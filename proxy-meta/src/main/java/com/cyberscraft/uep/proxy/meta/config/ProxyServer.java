package com.cyberscraft.uep.proxy.meta.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/12/11 7:31 下午
 */
@Component
@ConfigurationProperties(prefix = "proxy.server")
public class ProxyServer {
    private String username = "proxy_dg";
    private String password = "Digitalsee@2023";
    private String socksProxyHost = "127.0.0.1";
    private Integer socksProxyPort = 10809;
    private String socksNonProxyHosts = "127.0.0.1|localhost";


    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getSocksProxyHost() {
        return socksProxyHost;
    }

    public void setSocksProxyHost(String socksProxyHost) {
        this.socksProxyHost = socksProxyHost;
    }

    public Integer getSocksProxyPort() {
        return socksProxyPort;
    }

    public void setSocksProxyPort(Integer socksProxyPort) {
        this.socksProxyPort = socksProxyPort;
    }

    public String getSocksNonProxyHosts() {
        return socksNonProxyHosts;
    }

    public void setSocksNonProxyHosts(String socksNonProxyHosts) {
        this.socksNonProxyHosts = socksNonProxyHosts;
    }
}
