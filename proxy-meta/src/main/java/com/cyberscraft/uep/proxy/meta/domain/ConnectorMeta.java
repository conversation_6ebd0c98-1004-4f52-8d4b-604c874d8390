package com.cyberscraft.uep.proxy.meta.domain;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import java.io.Serializable;
import java.util.Set;

/**
 * <p>
 *  连通器元数据
 * </p>
 *
 * <AUTHOR>
 * @since 2023/12/4 5:06 下午
 */
@JsonNaming(PropertyNamingStrategy.class)
public class ConnectorMeta implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 连通器标识
     */
    private String connectorId;

    /**
     * 连通器密钥
     */
    private String clientSecret;

    /**
     * 版本
     */
    private String version;

    /**
     * 连接到的server ip
     */
    private String serverIp;

    /**
     * 连接到的server端口
     */
    private Integer serverPort;

    /**
     * 客户端ip
     */
    private String clientIp;

    /**
     * 连通器状态
     */
    private Integer status;

    /**
     * 所属代理组id列表
     */
    private Set<String> groupIds;

    /**
     * 建立连接的时间戳
     */
    private Long connectTime;

    /**
     * 断开连接的时间戳
     */
    private Long closeTime;


    public String getConnectorId() {
        return connectorId;
    }

    public void setConnectorId(String connectorId) {
        this.connectorId = connectorId;
    }

    public String getClientSecret() {
        return clientSecret;
    }

    public void setClientSecret(String clientSecret) {
        this.clientSecret = clientSecret;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getServerIp() {
        return serverIp;
    }

    public void setServerIp(String serverIp) {
        this.serverIp = serverIp;
    }

    public Integer getServerPort() {
        return serverPort;
    }

    public void setServerPort(Integer serverPort) {
        this.serverPort = serverPort;
    }

    public String getClientIp() {
        return clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Set<String> getGroupIds() {
        return groupIds;
    }

    public void setGroupIds(Set<String> groupIds) {
        this.groupIds = groupIds;
    }

    public Long getConnectTime() {
        return connectTime;
    }

    public void setConnectTime(Long connectTime) {
        this.connectTime = connectTime;
    }

    public Long getCloseTime() {
        return closeTime;
    }

    public void setCloseTime(Long closeTime) {
        this.closeTime = closeTime;
    }
}
