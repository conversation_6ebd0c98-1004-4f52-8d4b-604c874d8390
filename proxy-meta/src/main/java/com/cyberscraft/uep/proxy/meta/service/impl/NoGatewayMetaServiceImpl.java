package com.cyberscraft.uep.proxy.meta.service.impl;

import com.cyberscraft.uep.proxy.meta.domain.GatewayMeta;
import com.cyberscraft.uep.proxy.meta.service.IGatewayMetaService;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/11/29 6:14 下午
 */
public class NoGatewayMetaServiceImpl implements IGatewayMetaService {
    @Override
    public GatewayMeta getGatewayByDomain(String tenantId, String subdomain) {
        return null;
    }

    @Override
    public GatewayMeta getGatewayByClientId(String tenantId, String clientId) {
        return null;
    }

    @Override
    public boolean updateGatewayClient(GatewayMeta gatewayMeta) {
        return false;
    }

    @Override
    public boolean deleteGatewayClient(String tenantId, String clientId) {
        return false;
    }

    @Override
    public String generateSubdomain(String tenantId) {
        return null;
    }
}
