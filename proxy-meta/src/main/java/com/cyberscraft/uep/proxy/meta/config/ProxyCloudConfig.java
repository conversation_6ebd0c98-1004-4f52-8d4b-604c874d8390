package com.cyberscraft.uep.proxy.meta.config;

import com.cyberscraft.uep.proxy.meta.domain.ProxyMeta;
import com.cyberscraft.uep.proxy.meta.service.IGatewayMetaService;
import com.cyberscraft.uep.proxy.meta.service.IProxyMetaService;
import com.cyberscraft.uep.proxy.meta.service.impl.GatewayMetaServiceImpl;
import com.cyberscraft.uep.proxy.meta.service.impl.NoGatewayMetaServiceImpl;
import com.cyberscraft.uep.proxy.meta.service.impl.NoProxyMetaServiceImpl;
import com.cyberscraft.uep.proxy.meta.service.impl.ProxyMetaServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;

import java.io.IOException;
import java.net.*;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/12/11 10:40 上午
 */
@Configuration
@Order(value = Integer.MAX_VALUE)
public class ProxyCloudConfig {

    private static final Logger logger = LoggerFactory.getLogger(ProxyCloudConfig.class);

    @Autowired
    private ProxyServer proxyServer;

    @ConditionalOnProperty(name = "proxy.cloud.enable", havingValue = "true")
    @Bean(name = "proxyMetaService")
    public IProxyMetaService proxyMetaService() {
        logger.info("construct proxyMetaService...............................");
//        System.setProperty("socksNonProxyHosts", proxyServer.getSocksNonProxyHosts());
//        System.setProperty("ftp.nonProxyHosts", proxyServer.getSocksNonProxyHosts());
//        System.setProperty("http.nonProxyHosts", proxyServer.getSocksNonProxyHosts());
//        System.setProperty("socksProxyHost", proxyServer.getSocksProxyHost());
//        System.setProperty("socksProxyPort", proxyServer.getSocksProxyPort());
//        System.setProperty("proxySet", "true");

        IProxyMetaService proxyMetaService = new ProxyMetaServiceImpl();

        ProxySelector.setDefault(new ProxySelector() {
            private final Proxy proxy = new Proxy(Proxy.Type.SOCKS, new InetSocketAddress(proxyServer.getSocksProxyHost(), proxyServer.getSocksProxyPort()));

            @Override
            public List<Proxy> select(URI uri) {
                String host = uri.getHost();
                if (host != null) {
                    ProxyMeta proxyMeta = proxyMetaService.getProxyMeta(host);
                    if (proxyMeta != null) {
                        return Arrays.asList(proxy);
                    }
                }
                logger.debug("This host[{}] is not configured with a proxy", host);
                return Arrays.asList(Proxy.NO_PROXY);
            }

            @Override
            public void connectFailed(URI uri, SocketAddress sa, IOException ioe) {
                logger.warn("Proxy connection failed: {}", ioe.getMessage());
            }
        });

        Authenticator.setDefault(new Authenticator() {
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(proxyServer.getUsername(), proxyServer.getPassword().toCharArray());
            }
        });

        return proxyMetaService;
    }

    @ConditionalOnMissingBean(name="proxyMetaService")
    @Bean(name = "noProxyMetaService")
    public IProxyMetaService noProxyMetaService() {
        logger.info("construct noProxyMetaService..............................");
        IProxyMetaService proxyMetaService = new NoProxyMetaServiceImpl();
        return proxyMetaService;
    }

    @ConditionalOnProperty(name = "proxy.cloud.enable", havingValue = "true")
    @ConditionalOnMissingBean(name="gatewayMetaService")
    @Bean(name = "gatewayMetaService")
    public IGatewayMetaService gatewayMetaService() {
        logger.info("construct gatewayMetaService...............................");
        GatewayMetaServiceImpl gatewayMetaService = new GatewayMetaServiceImpl();
        return gatewayMetaService;
    }

    @ConditionalOnMissingBean(name="gatewayMetaService")
    @Bean(name = "noGatewayMetaService")
    public IGatewayMetaService noGatewayMetaService() {
        logger.info("construct noGatewayMetaService..............................");
        return new NoGatewayMetaServiceImpl();
    }


}
