package com.cyberscraft.uep.proxy.meta.domain;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import java.io.Serializable;

/**
 * <p>
 *  代理元数据
 * </p>
 *
 * <AUTHOR>
 * @since 2023/10/31 4:53 下午
 */
@JsonNaming(PropertyNamingStrategy.class)
public class ProxyMeta implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 代理组
     */
    private String group;

    /**
     * 请求的真实IP
     */
    private String realIp;

    /**
     * 分配的虚拟IP
     */
    private String virtualIp;

    public String getGroup() {
        return group;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    public String getRealIp() {
        return realIp;
    }

    public void setRealIp(String realIp) {
        this.realIp = realIp;
    }

    public String getVirtualIp() {
        return virtualIp;
    }

    public void setVirtualIp(String virtualIp) {
        this.virtualIp = virtualIp;
    }
}
