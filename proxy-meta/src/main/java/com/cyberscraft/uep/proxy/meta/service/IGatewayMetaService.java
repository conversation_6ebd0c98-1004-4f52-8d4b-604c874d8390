package com.cyberscraft.uep.proxy.meta.service;

import com.cyberscraft.uep.proxy.meta.domain.GatewayMeta;

import java.util.Set;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/11/18 3:31 下午
 */
public interface IGatewayMetaService {

    /**
     * 根据租户ID和子域名获取网关应用配置
     *
     * @param tenantId
     * @param subdomain
     * @return
     */
    GatewayMeta getGatewayByDomain(String tenantId, String subdomain);

    /**
     * 根据租户ID和clientId获取网关应用配置
     *
     * @param tenantId
     * @param clientId
     * @return
     */
    GatewayMeta getGatewayByClientId(String tenantId, String clientId);

    /**
     * 更新网关应用配置
     *
     * @param gatewayMeta
     * @return
     */
    boolean updateGatewayClient(GatewayMeta gatewayMeta);

    /**
     * 删除一个网关应用
     *
     * @param tenantId
     * @param clientId
     * @return
     */
    boolean deleteGatewayClient(String tenantId, String clientId);

    /**
     * 从租户级别获取一个唯一的子域名
     *
     * @param tenantId
     * @return
     */
    String generateSubdomain(String tenantId);

    /**
     * 检查域名是否合法
     * @param tenantId
     * @param clientId
     * @param domains
     * @return
     */
    default boolean checkValidDomain(String tenantId, String clientId, Set<String> domains) {
        return false;
    }
}
