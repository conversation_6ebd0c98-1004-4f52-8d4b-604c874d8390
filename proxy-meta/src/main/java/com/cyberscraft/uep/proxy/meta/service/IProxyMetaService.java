package com.cyberscraft.uep.proxy.meta.service;

import com.cyberscraft.uep.proxy.meta.domain.ConnectorMeta;
import com.cyberscraft.uep.proxy.meta.domain.ProxyMeta;

import java.util.List;
import java.util.Set;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/10/31 4:46 下午
 */
public interface IProxyMetaService {

    /**
     * 根据代理组和真实的请求ip(或域名) 来分配一个虚拟的ip
     * @param tenantId 租户编码
     * @param proxyGroup 代理组
     * @param realIp 代理组内真实ip
     * @return
     */
    String getVirtualIp(String tenantId, String proxyGroup, String realIp);

    /**
     * 根据虚拟ip获取proxy元数据
     * @param virtualIp
     * @return
     */
    ProxyMeta getProxyMeta(String virtualIp);

    /**
     * 删除一个虚拟IP
     * @param tenantId
     * @param proxyGroup
     * @param realIp
     */
    void deleteRealIp(String tenantId, String proxyGroup, String realIp);


    /**
     * 删除一个代理组
     * @param tenantId
     * @param proxyGroup
     */
    void deleteProxyGroup(String tenantId, String proxyGroup);

    /**
     * 删除一个租户
     * @param tenantId
     */
    void deleteTenant(String tenantId);

    /**
     * 根据代理组ID获取全部连通器ID
     * @param proxyGroupId
     * @return
     */
    Set<String> listConnectorIdsByGroup(String proxyGroupId);

    /**
     * 刷新连通器
     * @param connectorMeta
     */
    void refreshConnector(ConnectorMeta connectorMeta);

    /**
     * 删除连通器
     * @param connectorId
     * @return
     */
    ConnectorMeta deleteConnector(String connectorId);

    /**
     * 根据连通器ID获取连通器详情
     * @param connectorId
     * @return
     */
    ConnectorMeta getConnectorById(String connectorId);
}
