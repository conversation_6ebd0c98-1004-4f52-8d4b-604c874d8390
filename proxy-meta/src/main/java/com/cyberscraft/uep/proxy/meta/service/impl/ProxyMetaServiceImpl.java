package com.cyberscraft.uep.proxy.meta.service.impl;

import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.proxy.meta.domain.ConnectorMeta;
import com.cyberscraft.uep.proxy.meta.domain.ProxyMeta;
import com.cyberscraft.uep.proxy.meta.service.IProxyMetaService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RSet;
import org.redisson.client.protocol.ScoredEntry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/10/31 5:28 下午
 */
public class ProxyMetaServiceImpl implements IProxyMetaService {

    private static final Logger logger = LoggerFactory.getLogger(ProxyMetaServiceImpl.class);

    private final static String LOCK_TENANT = "lock:proxy:tenant";

    private final static String LOCK_GROUP = "lock:proxy:group";

    private final static String LOCK_HOST = "lock:proxy:host";

    /**
     * 租户级别的vlan，在缓存里存为MAP的形式，key为租户编码，value为网段数字，这个数字为ip地址的前2段
     */
    private final static String TENANT_VLAN_MAP_KEY = "proxy:tenant:vlan";

    /**
     * 某一租户下代理组级别的vlan，在缓存里存为MAP的形式，key为代理组id，value为网段数字
     */
    private final static String GROUP_VLAN_MAP_KEY = "proxy:group:vlan:%s";

    /**
     * 一个代理组下，各个realIp对应的virtualIp
     */
    private final static String REAL_IP_MAP_KEY = "proxy:virtual:ip:%s:%s";

    /**
     * 虚拟ip key， value为proxyMeta
     */
    private final static String VIRTUAL_IP_KEY = "proxy:virtual:ip:%s";

    /**
     * 一个代理组里的连通器MAP
     */
    private final static String CONNECTOR_IN_GROUP_KEY = "proxy:group:connectors:%s";

    /**
     * 一个连通器对应的key
     */
    private final static String CONNECTOR_KEY = "proxy:connector:%s";


    /**
     * 租户级别的网段最小值，后三位为一段，1000对应：1.0
     */
    private final static Integer MIN_TENANT_VLAN = 1000;

    /**
     * vlan段
     */
    private final static Integer MIN_SEGMENT_VALN = 1;

    @Resource
    @Qualifier("proxyRedissonClient")
    private Redisson proxyRedissonClient;

    /**
     * 根据租户生成租户级别的虚拟网段
     * @param tenantId
     * @return
     */
    private Integer getTenantVlan(String tenantId) {
        String key = TENANT_VLAN_MAP_KEY;
        RScoredSortedSet<Object> scoredSortedSet = proxyRedissonClient.getScoredSortedSet(key);
        Double score = scoredSortedSet.getScore(tenantId);
        if (score != null) {
            return score.intValue();
        }

        RLock lock = proxyRedissonClient.getLock(LOCK_TENANT);
        try {
            if (lock.tryLock(3000L, 2000L, TimeUnit.MILLISECONDS)) {
                try {
                    score = scoredSortedSet.getScore(tenantId);
                    if (score != null) {
                        return score.intValue();
                    }

                    Collection<ScoredEntry<Object>> scoredEntries = scoredSortedSet.entryRangeReversed(0, 0);
                    if (scoredEntries.isEmpty()) {
                        Integer vlan = newTenantVlan(null);
                        scoredSortedSet.add(vlan, tenantId);
                        return vlan;
                    } else {
                        Integer vlan = newTenantVlan(scoredEntries.toArray(new ScoredEntry[0])[0].getScore().intValue());
                        scoredSortedSet.add(vlan, tenantId);
                        return vlan;
                    }
                } finally {
                    lock.unlock();
                }
            }
        } catch (Exception e) {
            logger.error("get tenant vlan error", e);
            throw new RuntimeException(e.getMessage());
        }
        return null;
    }

    private Integer newTenantVlan(Integer currentMaxVlan) {
        if (currentMaxVlan == null || currentMaxVlan < MIN_TENANT_VLAN) {
            return MIN_TENANT_VLAN;
        } else {
            Integer newVlan = currentMaxVlan + 1;
            if (newVlan%1000 > 255) {
                newVlan += (1000-256);
            }
            return newVlan;
        }
    }

    private Integer newSegmentVlan(Integer currentMaxVlan) {
        if (currentMaxVlan == null || currentMaxVlan < MIN_SEGMENT_VALN) {
            return MIN_SEGMENT_VALN;
        } else {
            Integer newVlan = currentMaxVlan + 1;
            if (newVlan > 255) {
                throw new RuntimeException("proxy ip segment size is more than 255");
            }
            return newVlan;
        }
    }

    private String tenantVlan2Ip(Integer vlan) {
        Integer firstIp = vlan/1000;
        Integer secondIp = vlan%1000;
        return firstIp+"."+secondIp;
    }

    /**
     * 根据代理组生成代理组级别的网段
     * @param tenantId
     * @param proxyGroup
     * @return
     */
    private Integer getGroupVlan(String tenantId, String proxyGroup) {
        if (StringUtils.isBlank(proxyGroup)) {
            return 0;
        }
        if ("0".equals(proxyGroup)) {
            return 0;
        }

        String key = String.format(GROUP_VLAN_MAP_KEY, tenantId);
        RScoredSortedSet<Object> scoredSortedSet = proxyRedissonClient.getScoredSortedSet(key);
        Double score = scoredSortedSet.getScore(proxyGroup);
        if (score != null) {
            return score.intValue();
        }

        RLock lock = proxyRedissonClient.getLock(LOCK_GROUP);
        try {
            if (lock.tryLock(3000L, 2000L, TimeUnit.MILLISECONDS)) {
                try {
                    score = scoredSortedSet.getScore(proxyGroup);
                    if (score != null) {
                        return score.intValue();
                    }

                    Collection<ScoredEntry<Object>> scoredEntries = scoredSortedSet.entryRangeReversed(0, 0);
                    if (scoredEntries.isEmpty()) {
                        Integer vlan = newSegmentVlan(null);
                        scoredSortedSet.add(vlan, proxyGroup);
                        return vlan;
                    } else {
                        Integer vlan = newSegmentVlan(scoredEntries.toArray(new ScoredEntry[0])[0].getScore().intValue());
                        scoredSortedSet.add(vlan, proxyGroup);
                        return vlan;
                    }
                } finally {
                    lock.unlock();
                }
            }
        } catch (Exception e) {
            logger.error("get proxy group vlan error", e);
            throw new RuntimeException(e.getMessage());
        }
        return null;
    }

    @Override
    public String getVirtualIp(String tenantId, String proxyGroup, String realIp) {
        if (StringUtils.isBlank(proxyGroup) || proxyGroup.equalsIgnoreCase("null") || proxyGroup.equals("0")) {
            return null;
        }

        if (StringUtils.isBlank(proxyGroup) || proxyGroup.equalsIgnoreCase("null")) {
            proxyGroup = "0";
        }

        Integer tenantVlan = getTenantVlan(tenantId);
        Integer groupVlan = getGroupVlan(tenantId, proxyGroup);

        Integer lastVirtalIp = 1;

        String key = String.format(REAL_IP_MAP_KEY, tenantId, proxyGroup);
        RScoredSortedSet<Object> scoredSortedSet = proxyRedissonClient.getScoredSortedSet(key);
        Double score = scoredSortedSet.getScore(realIp);
        if (score != null) {
            lastVirtalIp = score.intValue();
        } else {
            RLock lock = proxyRedissonClient.getLock(LOCK_HOST);
            try {
                if (lock.tryLock(3000L, 2000L, TimeUnit.MILLISECONDS)) {
                    score = scoredSortedSet.getScore(realIp);
                    if (score != null) {
                        lastVirtalIp = score.intValue();
                    } else {
                        try {
                            Collection<ScoredEntry<Object>> scoredEntries = scoredSortedSet.entryRangeReversed(0, 0);
                            if (scoredEntries.isEmpty()) {
                                lastVirtalIp = newSegmentVlan(null);
                                scoredSortedSet.add(lastVirtalIp, realIp);
                            } else {
                                lastVirtalIp = newSegmentVlan(scoredEntries.toArray(new ScoredEntry[0])[0].getScore().intValue());
                                scoredSortedSet.add(lastVirtalIp, realIp);
                            }
                        } finally {
                            lock.unlock();
                        }
                    }
                }
            } catch (Exception e) {
                logger.error("get virtual ip error", e);
                throw new RuntimeException(e.getMessage());
            }
        }

        String virtualIp = tenantVlan2Ip(tenantVlan) + "." + groupVlan + "." + lastVirtalIp;

        ProxyMeta proxyMeta = new ProxyMeta();
        proxyMeta.setRealIp(realIp);
        proxyMeta.setVirtualIp(virtualIp);
        proxyMeta.setGroup(proxyGroup);

        saveVirtualIp(proxyMeta);

        return virtualIp;
    }

    private String checkVirtualIp(String tenantId, String proxyGroup, String realIp) {
        if (StringUtils.isBlank(proxyGroup)) {
            proxyGroup = "0";
        }

        String key = String.format(REAL_IP_MAP_KEY, tenantId, proxyGroup);
        RScoredSortedSet<Object> scoredSortedSet = proxyRedissonClient.getScoredSortedSet(key);
        Double score = scoredSortedSet.getScore(realIp);
        if (score != null) {
            Integer lastVirtalIp = score.intValue();

            Integer tenantVlan = getTenantVlan(tenantId);
            Integer groupVlan = getGroupVlan(tenantId, proxyGroup);

            String virtualIp = tenantVlan2Ip(tenantVlan) + "." + groupVlan + "." + lastVirtalIp;
            return virtualIp;
        }

        return null;
    }

    private void saveVirtualIp(ProxyMeta proxyMeta) {
        String virtualIp = proxyMeta.getVirtualIp();

        String key = String.format(VIRTUAL_IP_KEY, virtualIp);
        String strMeta = (String)proxyRedissonClient.getBucket(key).get();
        if (StringUtils.isBlank(strMeta)) {
            strMeta = JsonUtil.obj2Str(proxyMeta, JsonUtil.camelSerializeConfig);
            proxyRedissonClient.getBucket(key).set(strMeta);
        }
    }

    @Override
    public ProxyMeta getProxyMeta(String virtualIp) {

        String key = String.format(VIRTUAL_IP_KEY, virtualIp);
        String strMeta = (String)proxyRedissonClient.getBucket(key).get();

        return JsonUtil.str2Obj(strMeta, ProxyMeta.class, JsonUtil.camelParseConfig);
    }

    @Override
    public void deleteRealIp(String tenantId, String proxyGroup, String realIp) {
        String virtualIp = checkVirtualIp(tenantId, proxyGroup, realIp);
        if (virtualIp != null) {
            String key = String.format(REAL_IP_MAP_KEY, tenantId, proxyGroup);
            RScoredSortedSet<Object> scoredSortedSet = proxyRedissonClient.getScoredSortedSet(key);
            scoredSortedSet.remove(realIp);

            key = String.format(VIRTUAL_IP_KEY, virtualIp);
            proxyRedissonClient.getBucket(key).delete();
        }
    }

    @Override
    public void deleteProxyGroup(String tenantId, String proxyGroup) {
        if (StringUtils.isBlank(proxyGroup) || "0".equals(proxyGroup)) {
            return;
        }

        String groupKey = String.format(GROUP_VLAN_MAP_KEY, tenantId);
        RScoredSortedSet<Object> groupSet = proxyRedissonClient.getScoredSortedSet(groupKey);
        Double groupScore = groupSet.getScore(proxyGroup);
        if (groupScore != null) {
            String realIpkey = String.format(REAL_IP_MAP_KEY, tenantId, proxyGroup);
            RScoredSortedSet<Object> realIpSet = proxyRedissonClient.getScoredSortedSet(realIpkey);

            Collection<ScoredEntry<Object>> scoredEntries = realIpSet.entryRange(0, -1);
            if (!scoredEntries.isEmpty()) {
                Integer tenantVlan = getTenantVlan(tenantId);
                String ip12 = tenantVlan2Ip(tenantVlan);

                for (ScoredEntry<Object> scoredEntry : scoredEntries) {
                    Double entryScore = scoredEntry.getScore();

                    String virtualIp = ip12 + "." + groupScore.intValue() + "." + entryScore.intValue();
                    proxyRedissonClient.getBucket(String.format(VIRTUAL_IP_KEY, virtualIp)).delete();
                }
            }

            proxyRedissonClient.getBucket(realIpkey).delete();

            groupSet.remove(proxyGroup);
        }
    }

    @Override
    public void deleteTenant(String tenantId) {
        String groupKey = String.format(GROUP_VLAN_MAP_KEY, tenantId);
        RScoredSortedSet<Object> groupSet = proxyRedissonClient.getScoredSortedSet(groupKey);
        Collection<ScoredEntry<Object>> scoredEntries = groupSet.entryRange(0, -1);
        if (!scoredEntries.isEmpty()) {
            for (ScoredEntry<Object> scoredEntry : scoredEntries) {
                String proxyGroup = (String) scoredEntry.getValue();

                deleteProxyGroup(tenantId, proxyGroup);
            }
        }
        proxyRedissonClient.getBucket(groupKey).delete();

        String key = TENANT_VLAN_MAP_KEY;
        RScoredSortedSet<Object> tenantSet = proxyRedissonClient.getScoredSortedSet(key);
        tenantSet.remove(tenantId);
    }

    @Override
    public Set<String> listConnectorIdsByGroup(String proxyGroupId) {
        String key = String.format(CONNECTOR_IN_GROUP_KEY, proxyGroupId);
        RSet<String> connectorSet = proxyRedissonClient.getSet(key);
        return connectorSet;
    }

    @Override
    public void refreshConnector(ConnectorMeta connectorMeta) {
        String connectorId = connectorMeta.getConnectorId();
        Set<String> newGroupIds = connectorMeta.getGroupIds();

        ConnectorMeta oldMeta = getConnectorById(connectorId);
        if (oldMeta != null) {
            Set<String> oldGroupIds = oldMeta.getGroupIds();
            if (connectorMeta.getStatus() != 1) {
                for (String oldGroupId : oldGroupIds) {
                    String groupKey = String.format(CONNECTOR_IN_GROUP_KEY, oldGroupId);
                    RSet<String> connectorSet = proxyRedissonClient.getSet(groupKey);
                    connectorSet.remove(connectorId);
                }
            } else {
                Collection<String> oldRemove = CollectionUtils.subtract(oldGroupIds, newGroupIds);
                for (String oldGroupId : oldRemove) {
                    String groupKey = String.format(CONNECTOR_IN_GROUP_KEY, oldGroupId);
                    RSet<String> connectorSet = proxyRedissonClient.getSet(groupKey);
                    connectorSet.remove(connectorId);
                }

                Collection<String> newAdd = CollectionUtils.subtract(newGroupIds, oldGroupIds);
                for (String proxyGroupId : newAdd) {
                    String groupKey = String.format(CONNECTOR_IN_GROUP_KEY, proxyGroupId);
                    RSet<String> connectorSet = proxyRedissonClient.getSet(groupKey);
                    connectorSet.add(connectorId);
                }
            }
        } else {
            if (connectorMeta.getStatus() == 1) {
                for (String proxyGroupId : newGroupIds) {
                    String groupKey = String.format(CONNECTOR_IN_GROUP_KEY, proxyGroupId);
                    RSet<String> connectorSet = proxyRedissonClient.getSet(groupKey);
                    connectorSet.add(connectorId);
                }
            }
        }

        String key = String.format(CONNECTOR_KEY, connectorId);
        proxyRedissonClient.getBucket(key).set(JsonUtil.obj2Str(connectorMeta, JsonUtil.camelSerializeConfig));
    }

    @Override
    public ConnectorMeta deleteConnector(String connectorId) {
        String key = String.format(CONNECTOR_KEY, connectorId);
        String strMeta = (String)proxyRedissonClient.getBucket(key).getAndDelete();
        ConnectorMeta connectorMeta = JsonUtil.str2Obj(strMeta, ConnectorMeta.class, JsonUtil.camelParseConfig);

        if (connectorMeta != null) {
            Set<String> groupIds = connectorMeta.getGroupIds();
            for (String proxyGroupId : groupIds) {
                String groupKey = String.format(CONNECTOR_IN_GROUP_KEY, proxyGroupId);
                RSet<String> connectorSet = proxyRedissonClient.getSet(groupKey);
                connectorSet.remove(connectorMeta.getConnectorId());
            }
        }

        return connectorMeta;
    }

    @Override
    public ConnectorMeta getConnectorById(String connectorId) {
        String key = String.format(CONNECTOR_KEY, connectorId);
        String strMeta = (String)proxyRedissonClient.getBucket(key).get();
        return JsonUtil.str2Obj(strMeta, ConnectorMeta.class, JsonUtil.camelParseConfig);
    }
}
