package com.cyberscraft.uep.proxy.meta.service.impl;

import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.common.util.RandomUtil;
import com.cyberscraft.uep.proxy.meta.domain.GatewayMeta;
import com.cyberscraft.uep.proxy.meta.service.IGatewayMetaService;
import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RMap;
import org.springframework.beans.factory.annotation.Qualifier;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/11/18 3:52 下午
 */
public class GatewayMetaServiceImpl implements IGatewayMetaService {

    /**
     * 一个租户下子域名对应的clientId，在缓存里存为MAP的形式：key为subdomain，value为json
     */
    private final static String GW_TENANT_DOMAIN_MAP_KEY = "gateway:domain:%s";

    /**
     * 一个租户下网关应用的配置，在缓存里存为MAP的形式：key为client，value为json
     */
    private final static String GW_TENANT_CLIENT_MAP_KEY = "gateway:client:%s";

    @Resource
    @Qualifier("proxyRedissonClient")
    private Redisson proxyRedissonClient;

    @Override
    public GatewayMeta getGatewayByDomain(String tenantId, String subdomain) {
        String key = String.format(GW_TENANT_DOMAIN_MAP_KEY, tenantId);
        RMap<String, String> map = proxyRedissonClient.getMap(key);
        String clientId = map.get(subdomain);
        if (StringUtils.isNotBlank(clientId)) {
            GatewayMeta gatewayMeta = getGatewayByClientId(tenantId, clientId);
            return gatewayMeta;
        }
        return null;
    }

    @Override
    public GatewayMeta getGatewayByClientId(String tenantId, String clientId) {
        String key = String.format(GW_TENANT_CLIENT_MAP_KEY, tenantId);
        RMap<String, String> map = proxyRedissonClient.getMap(key);
        String gwConfig = map.get(clientId);
        if (StringUtils.isNotBlank(gwConfig)) {
            GatewayMeta gatewayMeta = JsonUtil.str2Obj(gwConfig, GatewayMeta.class);
            return gatewayMeta;
        }
        return null;
    }

    @Override
    public boolean updateGatewayClient(GatewayMeta gatewayMeta) {
        final String clientId = gatewayMeta.getClientId();
        final String tenantId = gatewayMeta.getTenantId();
        final String subdomain = gatewayMeta.getSubdomain();
        final Map<String, String> otherDomain = gatewayMeta.getOtherDomain();

        deleteGatewayClient(tenantId, clientId);

        String domainKey = String.format(GW_TENANT_DOMAIN_MAP_KEY, tenantId);
        final RMap<String, String> domainMap = proxyRedissonClient.getMap(domainKey);

        String usedClientId = domainMap.get(subdomain);
        if (usedClientId != null) {
            String errMsg = String.format("subdomain %s is used by %s", subdomain, usedClientId);
            throw new RuntimeException(errMsg);
        }
        if (otherDomain != null) {
            for (String domain : otherDomain.keySet()) {
                usedClientId = domainMap.get(domain);
                if (usedClientId != null) {
                    String errMsg = String.format("other domain %s is used by %s", subdomain, usedClientId);
                    throw new RuntimeException(errMsg);
                }
            }
        }

        String clientKey = String.format(GW_TENANT_CLIENT_MAP_KEY, tenantId);
        RMap<String, String> clientMap = proxyRedissonClient.getMap(clientKey);
        clientMap.put(clientId, JsonUtil.obj2Str(gatewayMeta));

        domainMap.put(subdomain, clientId);
        if (otherDomain != null) {
            for (String domain : otherDomain.keySet()) {
                domainMap.put(domain, clientId);
            }
        }


        return true;
    }

    @Override
    public boolean deleteGatewayClient(String tenantId, String clientId) {
        String key = String.format(GW_TENANT_CLIENT_MAP_KEY, tenantId);
        RMap<String, String> clientMap = proxyRedissonClient.getMap(key);
        String clientConfig = clientMap.remove(clientId);

        if (StringUtils.isNotBlank(clientConfig)) {
            final GatewayMeta gatewayMeta = JsonUtil.str2Obj(clientConfig, GatewayMeta.class);
            final String subdomain = gatewayMeta.getSubdomain();

            key = String.format(GW_TENANT_DOMAIN_MAP_KEY, tenantId);
            final RMap<Object, Object> domainMap = proxyRedissonClient.getMap(key);
            domainMap.remove(subdomain);

            final Map<String, String> otherDomain = gatewayMeta.getOtherDomain();
            if (otherDomain != null) {
                for (String domain : otherDomain.keySet()) {
                    domainMap.remove(domain);
                }
            }
        }
        return true;
    }

    /**
     * 从租户级别获取一个唯一的子域名
     * @param tenantId
     * @return
     */
    @Override
    public String generateSubdomain(String tenantId) {
        String subdomain = RandomUtil.getRandomString(6);
        String key = String.format(GW_TENANT_DOMAIN_MAP_KEY, tenantId);
        final RMap<String, String> domainMap = proxyRedissonClient.getMap(key);
        while (domainMap.get(subdomain) != null) {
            subdomain = RandomUtil.getRandomString(6);
        }
        return subdomain;
    }

    @Override
    public boolean checkValidDomain(String tenantId, String clientId, Set<String> domains) {
        String key = String.format(GW_TENANT_DOMAIN_MAP_KEY, tenantId);
        final RMap<String, String> domainMap = proxyRedissonClient.getMap(key);
        for (String domain : domains) {
            final String existClientId = domainMap.get(domain);
            if (existClientId != null && !existClientId.equals(clientId)) {
                return false;
            }
        }
        return true;
    }
}
