package com.cyberscraft.uep.proxy.meta.domain;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/11/18 3:34 下午
 */
@JsonNaming(PropertyNamingStrategy.class)
public class GatewayMeta implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 租户标识
     */
    private String tenantId;

    /**
     * 应用在租户下对应的网关子域名，例如：https://test17-bit.gw.digitalsee.cn, test17为bit租户下的网关子域名
     */
    private String subdomain;

    /**
     * 代理组
     */
    private String group;

    /**
     * 应用的原始http origin: schema://host:port
     */
    private String origin;

    /**
     * 网关应用clientId
     */
    private String clientId;

    /**
     * 应用公钥，jwk格式
     */
    private String publicKey;

    /**
     * 应用私钥，jwk格式
     */
    private String privateKey;

    /**
     * 其他域名，key为subdomain，value为origin
     */
    private Map<String, String> otherDomain;

    /**
     * 允许匿名访问的url（http开头的正则表达式）
     */
    private List<String> permitAllUrl;

    /**
     * 需重写的url
     */
    private Map<String,Boolean> rewriteUrl;

    /**
     * 请求的Header重写规则
     */
    private List<GwRewriteRule> rewriteReqHeader;

    /**
     * 请求的Query重写规则
     */
    private List<GwRewriteRule> rewriteReqQuery;

    /**
     * 应答的Header重写规则
     */
    private List<GwRewriteRule> rewriteResHeader;

    /**
     * 应答的Body重写规则
     */
    private List<GwRewriteRule> rewriteResBody;

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getSubdomain() {
        return subdomain;
    }

    public void setSubdomain(String subdomain) {
        this.subdomain = subdomain;
    }

    public String getGroup() {
        return group;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    public String getOrigin() {
        return origin;
    }

    public void setOrigin(String origin) {
        this.origin = origin;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getPublicKey() {
        return publicKey;
    }

    public void setPublicKey(String publicKey) {
        this.publicKey = publicKey;
    }

    public String getPrivateKey() {
        return privateKey;
    }

    public void setPrivateKey(String privateKey) {
        this.privateKey = privateKey;
    }

    public Map<String, String> getOtherDomain() {
        return otherDomain;
    }

    public void setOtherDomain(Map<String, String> otherDomain) {
        this.otherDomain = otherDomain;
    }

    public List<String> getPermitAllUrl() {
        return permitAllUrl;
    }

    public void setPermitAllUrl(List<String> permitAllUrl) {
        this.permitAllUrl = permitAllUrl;
    }

    public Map<String, Boolean> getRewriteUrl() {
        return rewriteUrl;
    }

    public void setRewriteUrl(Map<String, Boolean> rewriteUrl) {
        this.rewriteUrl = rewriteUrl;
    }

    public List<GwRewriteRule> getRewriteReqHeader() {
        return rewriteReqHeader;
    }

    public void setRewriteReqHeader(List<GwRewriteRule> rewriteReqHeader) {
        this.rewriteReqHeader = rewriteReqHeader;
    }

    public List<GwRewriteRule> getRewriteReqQuery() {
        return rewriteReqQuery;
    }

    public void setRewriteReqQuery(List<GwRewriteRule> rewriteReqQuery) {
        this.rewriteReqQuery = rewriteReqQuery;
    }

    public List<GwRewriteRule> getRewriteResHeader() {
        return rewriteResHeader;
    }

    public void setRewriteResHeader(List<GwRewriteRule> rewriteResHeader) {
        this.rewriteResHeader = rewriteResHeader;
    }

    public List<GwRewriteRule> getRewriteResBody() {
        return rewriteResBody;
    }

    public void setRewriteResBody(List<GwRewriteRule> rewriteResBody) {
        this.rewriteResBody = rewriteResBody;
    }
}
