package com.cyberscraft.uep.proxy.meta.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/12/4 6:35 下午
 */
@Component("proxyRedisPool")
@ConditionalOnProperty(name = "proxy.cloud.enable", havingValue = "true")
@ConfigurationProperties(prefix = "proxy.redis.pool")
public class ProxyRedisPoolConfig {
    private int maxIdle = 10;
    private int maxActive = 100;
    private int maxWait = 10000;
    private int maxThreads = 16;
    private int maxNettyThreads = 32;
    private boolean testOnBorrow = false;

    public int getMaxIdle() {
        return maxIdle;
    }

    public void setMaxIdle(int maxIdle) {
        this.maxIdle = maxIdle;
    }

    public int getMaxActive() {
        return maxActive;
    }

    public void setMaxActive(int maxActive) {
        this.maxActive = maxActive;
    }

    public int getMaxWait() {
        return maxWait;
    }

    public void setMaxWait(int maxWait) {
        this.maxWait = maxWait;
    }

    public boolean isTestOnBorrow() {
        return testOnBorrow;
    }

    public void setTestOnBorrow(boolean testOnBorrow) {
        this.testOnBorrow = testOnBorrow;
    }

    public int getMaxThreads() {
        return maxThreads;
    }

    public void setMaxThreads(int maxThreads) {
        this.maxThreads = maxThreads;
    }

    public int getMaxNettyThreads() {
        return maxNettyThreads;
    }

    public void setMaxNettyThreads(int maxNettyThreads) {
        this.maxNettyThreads = maxNettyThreads;
    }
}