package com.cyberscraft.uep.proxy.meta.service.impl;

import com.cyberscraft.uep.proxy.meta.domain.ConnectorMeta;
import com.cyberscraft.uep.proxy.meta.domain.ProxyMeta;
import com.cyberscraft.uep.proxy.meta.service.IProxyMetaService;

import java.util.List;
import java.util.Set;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/12/11 11:09 上午
 */
public class NoProxyMetaServiceImpl implements IProxyMetaService {

    @Override
    public String getVirtualIp(String tenantId, String proxyGroup, String realIp) {
        return null;
    }

    @Override
    public ProxyMeta getProxyMeta(String virtualIp) {
        return null;
    }

    @Override
    public void deleteRealIp(String tenantId, String proxyGroup, String realIp) {

    }

    @Override
    public void deleteProxyGroup(String tenantId, String proxyGroup) {

    }

    @Override
    public void deleteTenant(String tenantId) {

    }

    @Override
    public Set<String> listConnectorIdsByGroup(String proxyGroupId) {
        return null;
    }

    @Override
    public void refreshConnector(ConnectorMeta connectorMeta) {

    }

    @Override
    public ConnectorMeta deleteConnector(String connectorId) {
        return null;
    }

    @Override
    public ConnectorMeta getConnectorById(String connectorId) {
        return null;
    }

}
