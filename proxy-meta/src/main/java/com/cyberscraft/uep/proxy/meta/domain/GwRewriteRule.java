package com.cyberscraft.uep.proxy.meta.domain;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/12/18 3:50 下午
 */
@JsonNaming(PropertyNamingStrategy.class)
public class GwRewriteRule implements Serializable {
    /**
     * 重写操作，0、设置；1、简单替换；2、正则替换
     */
    private int op = 0;

    /**
     * 要被替换的key或内容
     */
    private String target;

    /**
     * 替换后的内容
     */
    private String replacement;

    public int getOp() {
        return op;
    }

    public void setOp(int op) {
        this.op = op;
    }

    public String getTarget() {
        return target;
    }

    public void setTarget(String target) {
        this.target = target;
    }

    public String getReplacement() {
        return replacement;
    }

    public void setReplacement(String replacement) {
        this.replacement = replacement;
    }
}
