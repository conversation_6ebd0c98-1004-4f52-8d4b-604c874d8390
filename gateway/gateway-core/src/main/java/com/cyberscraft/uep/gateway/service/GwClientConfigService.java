package com.cyberscraft.uep.gateway.service;

import com.cyberscraft.uep.proxy.meta.domain.GatewayMeta;

import javax.servlet.http.HttpServletRequest;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/11/18 5:18 下午
 */
public interface GwClientConfigService {

    /**
     * 根据请求的网关origin获取代理origin
     * @param httpOrigin 应用网关访问的http origin
     * @return
     */
    String getVirtualHttpOrigin(String httpOrigin);

    String getVirtualHttpOrigin(GatewayMeta gatewayMeta);

    /**
     * 根据请求的网关origin获取应用网关配置
     * @param httpOrigin 应用网关访问的http origin
     * @return
     */
    GatewayMeta getGatewayMeta(String httpOrigin);

    GatewayMeta getGatewayMeta(HttpServletRequest request);

    String getHost(String httpOrigin);

    String getHostAndPort(String httpOrigin);
}
