package com.cyberscraft.uep.gateway.service.impl;

import com.cyberscraft.uep.gateway.service.GwClientConfigService;
import com.cyberscraft.uep.gateway.service.HttpProxyService;
import com.cyberscraft.uep.proxy.meta.domain.GatewayMeta;
import com.cyberscraft.uep.proxy.meta.domain.GwRewriteRule;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/11/17 2:19 下午
 */
@Service
public class HttpProxyServiceImpl implements HttpProxyService {

    @Value("${proxy.origin}")
    private String proxyOrigin;

    @Autowired
    private GwClientConfigService gwClientConfigService;

    private boolean checkRewriteUri(String requestUri, GatewayMeta gatewayMeta) {
        final Map<String, Boolean> rewriteUrl = gatewayMeta.getRewriteUrl();
        if (rewriteUrl != null) {
            if (rewriteUrl.isEmpty()) {
                return true;
            }
            for (String s : rewriteUrl.keySet()) {
                if (requestUri.matches(s)) {
                    return rewriteUrl.get(s);
                }
            }
        } else {
            return true;
        }
        return false;
    }

    @Override
    public String decorateOriginQuery(String requestUri, String queryString, GatewayMeta gatewayMeta) {
        if (gatewayMeta == null) return queryString;
        if (!checkRewriteUri(requestUri, gatewayMeta)) {
            return queryString;
        }

        final List<GwRewriteRule> rewriteReqQuery = gatewayMeta.getRewriteReqQuery();
        if (rewriteReqQuery == null || rewriteReqQuery.isEmpty()) {
            return queryString;
        }

        if (StringUtils.isNotBlank(queryString)) {
            final String[] split = queryString.split("&");
            final Map<String, String> queryMap = new HashMap<>();
            for (int i = 0; i < split.length; i++) {
                if (StringUtils.isNotBlank(split[i])) {
                    final String[] split1 = split[i].split("=");
                    if (split1.length > 1) {
                        queryMap.put(split1[0], split1[1]);
                    } else {
                        queryMap.put(split1[0], "");
                    }
                }
            }

            for (GwRewriteRule rewriteRule : rewriteReqQuery) {
                final String target = rewriteRule.getTarget();
                final String replacement = rewriteRule.getReplacement();

                String replaceVal = target + "=" + replacement;

                if (queryMap.containsKey(target)) {
                    queryString = queryString.replace(target+"="+queryMap.get(target), replaceVal);
                } else {
                    queryString = queryString + "&" + replaceVal;
                }
            }
        }
        return queryString;
    }

    @Override
    public HttpHeaders decorateOriginHeaders(String requestUri, HttpHeaders originHeaders, GatewayMeta gatewayMeta) {
        HttpHeaders newHeaders = copyHttpHeaders(originHeaders);
        newHeaders.set("Host", gwClientConfigService.getHostAndPort(gatewayMeta.getOrigin()));
        List<String> connection = newHeaders.getConnection();
        if (!connection.isEmpty()) {
            connection = connection.stream().map(e->e.equalsIgnoreCase("close")?"keep-alive":e).collect(Collectors.toList());
            newHeaders.setConnection(connection);
        }

        if (gatewayMeta == null) return newHeaders;
        if (!checkRewriteUri(requestUri, gatewayMeta)) {
            return newHeaders;
        }

        final List<GwRewriteRule> rewriteHeader = gatewayMeta.getRewriteReqHeader();
        if (rewriteHeader == null || rewriteHeader.isEmpty()) {
            return newHeaders;
        }

        for (GwRewriteRule rewriteRule : rewriteHeader) {
            final String target = rewriteRule.getTarget();
            final String replacement = rewriteRule.getReplacement();

            newHeaders.set(target, replacement);
        }
        return newHeaders;
    }

    private HttpHeaders copyHttpHeaders(HttpHeaders originHeaders) {
        HttpHeaders newHeaders = new HttpHeaders();
        final Set<Map.Entry<String, List<String>>> entries = originHeaders.entrySet();
        for (Map.Entry<String, List<String>> entry : entries) {
            final String key = entry.getKey();
            final List<String> valList = entry.getValue();
            newHeaders.put(key, valList);
        }
        return newHeaders;
    }

    @Override
    public byte[] decorateOriginBody(String requestUri, byte[] originBody, MediaType requestMediaType, GatewayMeta gatewayMeta) {
        return originBody;
    }

    @Override
    public HttpHeaders decorateResponseHeaders(String requestUri, HttpHeaders responseHeaders, GatewayMeta gatewayMeta) {
        HttpHeaders httpHeaders = copyHttpHeaders(responseHeaders);
        if (!checkRewriteUri(requestUri, gatewayMeta)) {
            return httpHeaders;
        }

        Map<String, String> allDomains = new HashMap<>();

        final String subdomain = gatewayMeta.getSubdomain();
        final String origin = gatewayMeta.getOrigin();
        String mainOrigin = String.format(proxyOrigin, subdomain, gatewayMeta.getTenantId());

        allDomains.put(origin, mainOrigin);
        final Map<String, String> otherDomain = gatewayMeta.getOtherDomain();
        for (String s : otherDomain.keySet()) {
            allDomains.put(otherDomain.get(s), String.format(proxyOrigin, s, gatewayMeta.getTenantId()));
        }

        final List<GwRewriteRule> rewriteResHeader = gatewayMeta.getRewriteResHeader();
        HttpHeaders newHeaders = new HttpHeaders();

        final Set<Map.Entry<String, List<String>>> entries = responseHeaders.entrySet();
        for (Map.Entry<String, List<String>> entry : entries) {
            final String key = entry.getKey();
            final List<String> valList = entry.getValue();

            List<String> replacedValList = new ArrayList<>(valList.size());
            for (String s : valList) {
                if (StringUtils.isNotBlank(s)) {
                    for (String s1 : allDomains.keySet()) {
                        s = s.replaceAll(s1, allDomains.get(s1));
                    }

                    replacedValList.add(s);
                }
            }
            newHeaders.put(key, replacedValList);
        }

        if (rewriteResHeader != null && !rewriteResHeader.isEmpty()) {
            for (GwRewriteRule rewriteRule : rewriteResHeader) {
                final String target = rewriteRule.getTarget();
                final String replacement = rewriteRule.getReplacement();
                newHeaders.set(target, replacement);
            }
        }
        return newHeaders;
    }

    @Override
    public byte[] decorateResponseBody(String requestUri, byte[] responseBody, MediaType responseMediaType, GatewayMeta gatewayMeta) {
        if (responseBody == null) return null;
        if (!checkRewriteUri(requestUri, gatewayMeta)) {
            return responseBody;
        }

            //判断是否有装饰配置
        boolean isTextResponse = isTextResponse(responseMediaType);
        if (isTextResponse) {
            Map<String, String> allDomains = new HashMap<>();

            final String subdomain = gatewayMeta.getSubdomain();
            final String origin = gatewayMeta.getOrigin();
            String mainOrigin = String.format(proxyOrigin, subdomain, gatewayMeta.getTenantId());

            allDomains.put(origin, mainOrigin);
            final Map<String, String> otherDomain = gatewayMeta.getOtherDomain();
            for (String s : otherDomain.keySet()) {
                allDomains.put(otherDomain.get(s), String.format(proxyOrigin, s, gatewayMeta.getTenantId()));
            }

            final List<GwRewriteRule> rewriteResBody = gatewayMeta.getRewriteResBody();

            final String charset = getCharset(responseMediaType);
            String strBody;
            try {
                strBody = new String(responseBody, charset);

                for (String s : allDomains.keySet()) {
                    strBody = strBody.replaceAll(s, allDomains.get(s));
                }

                if (rewriteResBody != null && !rewriteResBody.isEmpty()) {
                    for (GwRewriteRule rewriteRule : rewriteResBody) {
                        int op = rewriteRule.getOp();
                        String target = rewriteRule.getTarget();
                        String replacement = rewriteRule.getReplacement();
                        if (replacement == null) replacement="";

                        if (op != 2) {
                            target = Pattern.quote(target);
                        }
                        strBody = strBody.replaceAll(target, replacement);
                    }
                }
                return strBody.getBytes(charset);
            } catch (UnsupportedEncodingException e) {
                strBody = new String(responseBody);
                for (String s : allDomains.keySet()) {
                    strBody = strBody.replaceAll(s, allDomains.get(s));
                }

                if (rewriteResBody != null && !rewriteResBody.isEmpty()) {
                    for (GwRewriteRule rewriteRule : rewriteResBody) {
                        int op = rewriteRule.getOp();
                        String target = rewriteRule.getTarget();
                        String replacement = rewriteRule.getReplacement();
                        if (replacement == null) replacement="";

                        if (op != 2) {
                            target = Pattern.quote(target);
                        }
                        strBody = strBody.replaceAll(target, replacement);
                    }
                }
                return strBody.getBytes();
            }
        } else {
            return responseBody;
        }
    }

    private String getCharset(MediaType responseMediaType) {
        if (responseMediaType == null) return "UTF-8";
        final String charset = responseMediaType.getParameter("charset");
        if (charset != null && charset.trim().length() > 0) {
            return charset;
        } else {
            return "UTF-8";
        }
    }

    private boolean isTextResponse(MediaType responseMediaType) {
        if (responseMediaType == null) return false;

        final String type = responseMediaType.getType();
        if ("text".equalsIgnoreCase(type)) {
            return true;
        } else if ("application".equalsIgnoreCase(type)) {
            final String subtype = responseMediaType.getSubtype().toLowerCase();
            switch (subtype) {
                case "json":
                case "xml":
                case "javascript":
                case "ecmascript":
                    return true;
            }
            return false;
        }
        return false;
    }
}
