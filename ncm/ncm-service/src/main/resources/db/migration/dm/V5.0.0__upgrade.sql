CREATE TABLE "ncm"."ncm_config"
(
"id" BIGINT NOT NULL,
"config_key" VARCHAR(256) NOT NULL,
"config_value" TEXT NOT NULL,
"config_type" TINYINT,
"create_time" TIMESTAMP(0),
"update_time" TIMESTAMP(0),
"create_by" VARCHAR(256),
"update_by" VARCHAR(256),
"tenant_id" VARCHAR(256) NOT NULL,
NOT CLUSTER PRIMARY KEY("id", "tenant_id"),
CONSTRAINT "config_key_index" UNIQUE("config_key", "tenant_id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "ncm"."ncm_config" IS 'NCM-配置表';
COMMENT ON COLUMN "ncm"."ncm_config"."config_key" IS '配置key';
COMMENT ON COLUMN "ncm"."ncm_config"."config_type" IS '配置类型，1：EMAIL，2：SMS';
COMMENT ON COLUMN "ncm"."ncm_config"."config_value" IS '配置value';
COMMENT ON COLUMN "ncm"."ncm_config"."create_by" IS '创建人';
COMMENT ON COLUMN "ncm"."ncm_config"."create_time" IS '创建时间';
COMMENT ON COLUMN "ncm"."ncm_config"."id" IS '配置ID';
COMMENT ON COLUMN "ncm"."ncm_config"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "ncm"."ncm_config"."update_by" IS '更新人';
COMMENT ON COLUMN "ncm"."ncm_config"."update_time" IS '更新时间';


CREATE TABLE "ncm"."ncm_media"
(
"id" BIGINT NOT NULL,
"type" TINYINT NOT NULL,
"title" VARCHAR(400),
"target_type" TINYINT,
"third_media_id" VARCHAR(200) NOT NULL,
"created_time" TIMESTAMP(0),
"created_by" BIGINT,
"use_times" INT,
"tenant_id" VARCHAR(256) NOT NULL,
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "ncm"."ncm_media" IS 'NCM-媒体文件信息表';
COMMENT ON COLUMN "ncm"."ncm_media"."created_by" IS '媒体文件的创建用户';
COMMENT ON COLUMN "ncm"."ncm_media"."created_time" IS '媒体文件创建时间';
COMMENT ON COLUMN "ncm"."ncm_media"."id" IS '主键';
COMMENT ON COLUMN "ncm"."ncm_media"."target_type" IS '媒体文件所在平台（0InSite,5DingDing,4Wework,参考ThirdPartyAccountType的定义）';
COMMENT ON COLUMN "ncm"."ncm_media"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "ncm"."ncm_media"."third_media_id" IS '媒体文件在第三主平台媒体ID';
COMMENT ON COLUMN "ncm"."ncm_media"."title" IS '媒体文件名称';
COMMENT ON COLUMN "ncm"."ncm_media"."type" IS '媒体文件类型（1图片，2音频，3视频）';
COMMENT ON COLUMN "ncm"."ncm_media"."use_times" IS '当前已经使用次数';


CREATE OR REPLACE  INDEX "ncm"."ncm_media_idx_1" ON "ncm"."ncm_media"("type" ASC,"tenant_id" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "ncm"."ncm_message"
(
"id" BIGINT NOT NULL,
"biz_type" TINYINT NOT NULL,
"type" TINYINT NOT NULL,
"title" VARCHAR(400),
"content" TEXT,
"status" TINYINT,
"sender" BIGINT,
"target_type" TINYINT,
"user_type" TINYINT,
"user_data_ids" TEXT,
"error_msg" TEXT,
"send_time" TIMESTAMP(0),
"next_send_time" TIMESTAMP(0),
"send_times" INT,
"config_id" TEXT,
"created_time" TIMESTAMP(0),
"tenant_id" VARCHAR(256) NOT NULL,
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "ncm"."ncm_message" IS 'NCM-待发送消息信息表';
COMMENT ON COLUMN "ncm"."ncm_message"."biz_type" IS '消息的业务类型';
COMMENT ON COLUMN "ncm"."ncm_message"."config_id" IS '目标平台配置信息ID';
COMMENT ON COLUMN "ncm"."ncm_message"."content" IS '消息内容,为一个json体，不同的消息存储不同的内容';
COMMENT ON COLUMN "ncm"."ncm_message"."created_time" IS '消息创建时间';
COMMENT ON COLUMN "ncm"."ncm_message"."error_msg" IS '最后一次发送结果的错误消息';
COMMENT ON COLUMN "ncm"."ncm_message"."id" IS '主键';
COMMENT ON COLUMN "ncm"."ncm_message"."next_send_time" IS '下次发送时间';
COMMENT ON COLUMN "ncm"."ncm_message"."send_time" IS '最后一次发送时间';
COMMENT ON COLUMN "ncm"."ncm_message"."send_times" IS '当前已经发送次数';
COMMENT ON COLUMN "ncm"."ncm_message"."sender" IS '消息的发送人';
COMMENT ON COLUMN "ncm"."ncm_message"."status" IS '消息状态（未发送，已发送）';
COMMENT ON COLUMN "ncm"."ncm_message"."target_type" IS '发送的目标平台列表对应的json字符串，如[InSite,DingDing]代表发送到站内消息及钉钉消息中';
COMMENT ON COLUMN "ncm"."ncm_message"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "ncm"."ncm_message"."title" IS '消息标题';
COMMENT ON COLUMN "ncm"."ncm_message"."type" IS '消息类型';
COMMENT ON COLUMN "ncm"."ncm_message"."user_data_ids" IS '用户ID或者组ID对应的json数组';
COMMENT ON COLUMN "ncm"."ncm_message"."user_type" IS '指定用户0，指定用户组1，全站用户2';


CREATE OR REPLACE  INDEX "ncm"."ncm_message_idx_1" ON "ncm"."ncm_message"("biz_type" ASC,"type" ASC,"target_type" ASC,"status" ASC,"send_time" ASC,"tenant_id" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;
CREATE OR REPLACE  INDEX "ncm"."ncm_message_idx_2" ON "ncm"."ncm_message"("next_send_time" ASC,"status" ASC,"tenant_id" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "ncm"."ncm_message_record"
(
"id" BIGINT NOT NULL,
"biz_type" TINYINT NOT NULL,
"type" TINYINT NOT NULL,
"title" VARCHAR(400),
"content" TEXT,
"status" TINYINT,
"sender" BIGINT,
"target_type" TINYINT,
"user_type" TINYINT,
"user_data_ids" TEXT,
"error_msg" TEXT,
"send_time" TIMESTAMP(0),
"send_times" INT,
"config_id" TEXT,
"created_time" TIMESTAMP(0),
"tenant_id" VARCHAR(256) NOT NULL,
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "ncm"."ncm_message_record" IS 'NCM-发送失败消息信息表';
COMMENT ON COLUMN "ncm"."ncm_message_record"."biz_type" IS '消息的业务类型';
COMMENT ON COLUMN "ncm"."ncm_message_record"."config_id" IS '目标平台配置信息ID';
COMMENT ON COLUMN "ncm"."ncm_message_record"."content" IS '消息内容,为一个json体，不同的消息存储不同的内容';
COMMENT ON COLUMN "ncm"."ncm_message_record"."created_time" IS '消息创建时间';
COMMENT ON COLUMN "ncm"."ncm_message_record"."error_msg" IS '最后一次发送结果的错误消息';
COMMENT ON COLUMN "ncm"."ncm_message_record"."id" IS '主键';
COMMENT ON COLUMN "ncm"."ncm_message_record"."send_time" IS '最后一次发送时间';
COMMENT ON COLUMN "ncm"."ncm_message_record"."send_times" IS '当前已经发送次数';
COMMENT ON COLUMN "ncm"."ncm_message_record"."sender" IS '消息的发送人';
COMMENT ON COLUMN "ncm"."ncm_message_record"."status" IS '消息状态（未发送，已发送）';
COMMENT ON COLUMN "ncm"."ncm_message_record"."target_type" IS '发送的目标平台列表对应的json字符串，如[InSite,DingDing]代表发送到站内消息及钉钉消息中';
COMMENT ON COLUMN "ncm"."ncm_message_record"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "ncm"."ncm_message_record"."title" IS '消息标题';
COMMENT ON COLUMN "ncm"."ncm_message_record"."type" IS '消息类型';
COMMENT ON COLUMN "ncm"."ncm_message_record"."user_data_ids" IS '用户ID或者组ID对应的json数组';
COMMENT ON COLUMN "ncm"."ncm_message_record"."user_type" IS '指定用户0，指定用户组1，全站用户2';


CREATE OR REPLACE  INDEX "ncm"."ncm_message_record_idx_1" ON "ncm"."ncm_message_record"("biz_type" ASC,"type" ASC,"target_type" ASC,"status" ASC,"send_time" ASC,"tenant_id" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "ncm"."ncm_message_record_user"
(
"id" BIGINT NOT NULL,
"msg_id" BIGINT NOT NULL,
"user_type" TINYINT NOT NULL,
"data_id" VARCHAR(256) NOT NULL,
"third_data_id" VARCHAR(256),
"task_id" VARCHAR(256),
"error_msg" TEXT,
"result" TINYINT,
"send_time" TIMESTAMP(0),
"finish_time" TIMESTAMP(0),
"send_times" INT,
"created_time" TIMESTAMP(0),
"tenant_id" VARCHAR(256) NOT NULL,
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "ncm"."ncm_message_record_user" IS 'NCM-消息目标用户表';
COMMENT ON COLUMN "ncm"."ncm_message_record_user"."created_time" IS '消息创建时间';
COMMENT ON COLUMN "ncm"."ncm_message_record_user"."data_id" IS 'IAM用户ID/组ID/工号等';
COMMENT ON COLUMN "ncm"."ncm_message_record_user"."error_msg" IS '最后失败的消息前150个字符(UTF-8*3)';
COMMENT ON COLUMN "ncm"."ncm_message_record_user"."finish_time" IS '最后一次发送结束时间';
COMMENT ON COLUMN "ncm"."ncm_message_record_user"."id" IS '主键';
COMMENT ON COLUMN "ncm"."ncm_message_record_user"."msg_id" IS 'IAM消息ID';
COMMENT ON COLUMN "ncm"."ncm_message_record_user"."result" IS '消息的结果（1、发送成功，2、无效用户，3、禁止发送，4、发送失败)';
COMMENT ON COLUMN "ncm"."ncm_message_record_user"."send_time" IS '最后一次发送时间';
COMMENT ON COLUMN "ncm"."ncm_message_record_user"."send_times" IS '当前已经发送次数';
COMMENT ON COLUMN "ncm"."ncm_message_record_user"."task_id" IS '当前发送的任务ID,如果是异步发送，用于查询进度，生成发送结果及重新发送处理逻辑';
COMMENT ON COLUMN "ncm"."ncm_message_record_user"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "ncm"."ncm_message_record_user"."third_data_id" IS '第三方平台对应的用户，或者组，ID';
COMMENT ON COLUMN "ncm"."ncm_message_record_user"."user_type" IS 'IAM用户类型';


CREATE OR REPLACE  INDEX "ncm"."ncm_message_record_user_idx_1" ON "ncm"."ncm_message_record_user"("msg_id" ASC,"result" ASC,"created_time" ASC,"tenant_id" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "ncm"."ncm_message_task"
(
"id" BIGINT NOT NULL,
"msg_id" BIGINT NOT NULL,
"send_times" INT,
"error_msg" VARCHAR(500),
"task_id" VARCHAR(256) NOT NULL,
"send_time" TIMESTAMP(0),
"finish_time" TIMESTAMP(0),
"task_check_times" INT,
"next_task_check_time" TIMESTAMP(0),
"status" TINYINT,
"tenant_id" VARCHAR(256) NOT NULL,
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "ncm"."ncm_message_task" IS 'NCM-消息发送任务表';
COMMENT ON COLUMN "ncm"."ncm_message_task"."error_msg" IS '最后一次发送结果的错误消息,前150个字符';
COMMENT ON COLUMN "ncm"."ncm_message_task"."finish_time" IS '任务发送结束时间';
COMMENT ON COLUMN "ncm"."ncm_message_task"."id" IS '主键';
COMMENT ON COLUMN "ncm"."ncm_message_task"."msg_id" IS 'IAM消息ID';
COMMENT ON COLUMN "ncm"."ncm_message_task"."next_task_check_time" IS '下次查询进度时间';
COMMENT ON COLUMN "ncm"."ncm_message_task"."send_time" IS '任务发送开始时间';
COMMENT ON COLUMN "ncm"."ncm_message_task"."send_times" IS '当前已经发送次数,将当前发送的批次号';
COMMENT ON COLUMN "ncm"."ncm_message_task"."status" IS '消息状态（发送中，发送完成）';
COMMENT ON COLUMN "ncm"."ncm_message_task"."task_check_times" IS '当前任务的检查次数';
COMMENT ON COLUMN "ncm"."ncm_message_task"."task_id" IS '当前发送的任务ID,如果是异步发送，用于查询进度，生成发送结果及重新发送处理逻辑';
COMMENT ON COLUMN "ncm"."ncm_message_task"."tenant_id" IS '租户ID';


CREATE OR REPLACE  INDEX "ncm"."ncm_message_task_idx_1" ON "ncm"."ncm_message_task"("msg_id" ASC,"status" ASC,"next_task_check_time" ASC,"task_check_times" ASC,"tenant_id" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

CREATE TABLE "ncm"."ncm_message_user"
(
"id" BIGINT NOT NULL,
"msg_id" BIGINT NOT NULL,
"user_type" TINYINT NOT NULL,
"data_id" VARCHAR(256) NOT NULL,
"third_data_id" VARCHAR(256),
"task_id" VARCHAR(256),
"error_msg" VARCHAR(500),
"result" TINYINT,
"status" TINYINT,
"send_time" TIMESTAMP(0),
"finish_time" TIMESTAMP(0),
"send_times" INT,
"created_time" TIMESTAMP(0),
"tenant_id" VARCHAR(256) NOT NULL,
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR) ;

COMMENT ON TABLE "ncm"."ncm_message_user" IS 'NCM-消息目标用户表';
COMMENT ON COLUMN "ncm"."ncm_message_user"."created_time" IS '消息创建时间';
COMMENT ON COLUMN "ncm"."ncm_message_user"."data_id" IS 'IAM用户ID/组ID/工号等';
COMMENT ON COLUMN "ncm"."ncm_message_user"."error_msg" IS '最后失败的消息前150个字符(UTF-8*3)';
COMMENT ON COLUMN "ncm"."ncm_message_user"."finish_time" IS '最后一次发送结束时间';
COMMENT ON COLUMN "ncm"."ncm_message_user"."id" IS '主键';
COMMENT ON COLUMN "ncm"."ncm_message_user"."msg_id" IS 'IAM消息ID';
COMMENT ON COLUMN "ncm"."ncm_message_user"."result" IS '消息的结果（1、发送成功，2、无效用户，3、禁止发送，4、发送失败)';
COMMENT ON COLUMN "ncm"."ncm_message_user"."send_time" IS '最后一次发送时间';
COMMENT ON COLUMN "ncm"."ncm_message_user"."send_times" IS '当前已经发送次数';
COMMENT ON COLUMN "ncm"."ncm_message_user"."status" IS '消息状态（发送中，发送完成）';
COMMENT ON COLUMN "ncm"."ncm_message_user"."task_id" IS '当前发送的任务ID,如果是异步发送，用于查询进度，生成发送结果及重新发送处理逻辑';
COMMENT ON COLUMN "ncm"."ncm_message_user"."tenant_id" IS '租户ID';
COMMENT ON COLUMN "ncm"."ncm_message_user"."third_data_id" IS '第三方平台对应的用户，或者组，ID';
COMMENT ON COLUMN "ncm"."ncm_message_user"."user_type" IS 'IAM用户类型';


CREATE OR REPLACE  INDEX "ncm"."ncm_message_user_idx_3" ON "ncm"."ncm_message_user"("status" ASC,"msg_id" ASC,"result" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;
CREATE OR REPLACE  INDEX "ncm"."ncm_message_user_idx_4" ON "ncm"."ncm_message_user"("task_id" ASC,"status" ASC,"created_time" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;
CREATE OR REPLACE  INDEX "ncm"."ncm_message_user_idx_1" ON "ncm"."ncm_message_user"("msg_id" ASC,"result" ASC,"created_time" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;
CREATE OR REPLACE  INDEX "ncm"."ncm_message_user_idx_2" ON "ncm"."ncm_message_user"("data_id" ASC,"msg_id" ASC,"status" ASC) STORAGE(ON "MAIN", CLUSTERBTR) ;

