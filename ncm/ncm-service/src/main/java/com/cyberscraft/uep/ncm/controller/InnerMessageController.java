package com.cyberscraft.uep.ncm.controller;

import com.cyberscraft.uep.common.bean.Result;
import com.cyberscraft.uep.common.enums.ExceptionCodeEnum;
import com.cyberscraft.uep.common.exception.BaseException;
import com.cyberscraft.uep.common.exception.ResponseResult;
import com.cyberscraft.uep.ncm.api.InnerMessageApi;
import com.cyberscraft.uep.ncm.core.common.exception.NcmException;
import com.cyberscraft.uep.ncm.core.service.IMessageService;
import com.cyberscraft.uep.ncm.dto.MessageDTO;
import com.cyberscraft.uep.ncm.enums.NcmErrorCode;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
public class InnerMessageController implements InnerMessageApi {

    @Resource
    private IMessageService messageService;

    @Override
    public Result<String> innerSendMessage(@Valid MessageDTO message) {
        String tcode = TenantHolder.getTenantCode();
        if (StringUtils.isBlank(tcode)) {
            throw new BaseException(ExceptionCodeEnum.TENANT_CODE_EMPTY);
        }
        if (!messageService.isValidMessageContent(message.getType(), message.getContent())) {
            throw new NcmException(NcmErrorCode.MESSAGE_BODY_INVALID);
        }
        if (!messageService.isValidMessageUserDataIds(message.getUserType(), message.getUserDataIds())) {
            throw new NcmException(NcmErrorCode.MESSAGE_USER_ID_INVALID);
        }
        Long id = messageService.send(message);
        return ResponseResult.success(String.valueOf(id));
    }
}
