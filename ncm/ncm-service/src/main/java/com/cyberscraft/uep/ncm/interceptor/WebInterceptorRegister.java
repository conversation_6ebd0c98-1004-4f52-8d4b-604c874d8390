package com.cyberscraft.uep.ncm.interceptor;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * @<PERSON>
 * @Date 2024/6/18 18:48
 * @Version 1.0
 * @Description 拦截器注册
 */
@Configuration
public class WebInterceptorRegister implements WebMvcConfigurer {

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new TraceInterceptor())
                .addPathPatterns("/**");
    }

}
