package com.cyberscraft.uep.ncm.core.service.sms.handler.bit;

import com.alibaba.fastjson.JSONObject;
import com.cyberscraft.uep.common.util.RestAPIUtil;
import com.cyberscraft.uep.ncm.client.domain.Sms;
import com.cyberscraft.uep.ncm.core.service.sms.handler.ISmsHandler;
import com.cyberscraft.uep.ncm.core.service.sms.handler.SmsConstant;
import com.cyberscraft.uep.ncm.core.service.sms.handler.SmsHandlerType;
import com.cyberscraft.uep.ncm.core.service.sms.handler.util.SmsUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 北京理工短信网关实现类
 * <AUTHOR>
 */
@Component
public class BitSmsHandler implements ISmsHandler{
    private final static Logger logger = LoggerFactory.getLogger(BitSmsHandler.class);
    private final static String URI_VARIABLES = "?mobile=%s&secure=%s&message=%s";
    @Override
    public Integer getType() {
        return SmsHandlerType.BIT.getValue();
    }

    @Override
    public JSONObject send(Sms sms, Map config) {
        String url = config.getOrDefault(SmsConstant.PROVIDER_BIT_URL, "").toString();
        String secure = config.getOrDefault(SmsConstant.PROVIDER_BIT_SECURE, "").toString();
        if (StringUtils.isEmpty(url)){
            String msg =String.format("tenant %s sms gateway config error: url is empty", sms.getTenantId());
            logger.warn(msg);
            return buildErrorStatus(msg);
        }

        if (StringUtils.isEmpty(secure)){
            String msg =String.format("tenant %s sms gateway config error: secure is empty", sms.getTenantId());
            logger.warn(msg);
            return buildErrorStatus(msg);
        }

        Map<String, Object> params = sms.getParams();
        String content = (String) params.getOrDefault(SmsConstant.SMS_TEMPLATE_CONTENT, "");
        if (!SmsUtils.isValidMobile(sms.getTarget())){
            String msg =String.format("tenant %s sms gateway config error: mobile is empty", sms.getTenantId());
            logger.warn(msg);
            return buildErrorStatus(msg);
        }

        if (!SmsUtils.isValidMessage(content)){
            String msg =String.format("tenant %s sms gateway config error: content is empty", sms.getTenantId());
            logger.warn(msg);
            return buildErrorStatus(msg);
        }
        return send(sms.getTarget(), content, url, secure);
    }

    private JSONObject send(String mobile, String message, String url, String secure) {

        Map<String, String> requestParams = new HashMap<>(3);
        requestParams.put("mobile", mobile);
        requestParams.put("message", message);
        requestParams.put("secure", secure);

        Map result;
        try{
            result = RestAPIUtil.getForEntity(SmsUtils.normalizeUrl(url + String.format(URI_VARIABLES, mobile, secure, message)), null, new Object());
            if (result == null){
                String msg =String.format("failed to send message %s to mobile %s", message, mobile);
                logger.warn(msg);
                return buildErrorStatus(msg);
            }
            if (result.containsKey("uuid")){
                logger.info("succeed to send message {} to mobile {}, result {}", message, mobile, result.get("uuid"));
                return buildSuccessStatus();
            }else{
                String msg =String.format("failed to send message %s to mobile %s, result %s", message, mobile, result);
                logger.warn(msg);
                return buildErrorStatus(msg);
            }
        }catch (Exception e){
            logger.warn("failed to send message {} to mobile {}", message, mobile, e);
            throw  e;
        }
    }
}
