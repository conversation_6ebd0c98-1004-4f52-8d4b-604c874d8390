package com.cyberscraft.uep.ncm.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serializable;

/**
 * <p>
 * NCM-消息发送任务表
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2020-08-03
 */
@TableName("ncm_message_task")
public class MessageTaskEntity implements Serializable {


    private static final long serialVersionUID = -7846725382492948876L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * IAM消息ID
     */
    private Long msgId;

    /**
     * 当前发送的任务ID,如果是异步发送，用于查询进度，生成发送结果及重新发送处理逻辑
     */
    private String taskId;

    /**
     * 当前已经发送次数(即当前任务所属的批次号)
     */
    private Integer sendTimes;

    /**
     * 任务发送开始时间
     */
    private LocalDateTime sendTime;

    /**
     * 任务发送结束时间
     */
    private LocalDateTime finishTime;


    /**
     * 任务对应的错误消息
     */
    private String errorMsg;

    /**
     * 当前任务的检查次数
     */
    private Integer taskCheckTimes;

    /**
     * 下次查询进度时间
     */
    private LocalDateTime nextTaskCheckTime;

    /**
     * 消息状态（发送中，发送完成）
     */
    private Integer status;

    /**
     * 租户ID
     */
    private String tenantId;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getMsgId() {
        return msgId;
    }

    public void setMsgId(Long msgId) {
        this.msgId = msgId;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public Integer getSendTimes() {
        return sendTimes;
    }

    public void setSendTimes(Integer sendTimes) {
        this.sendTimes = sendTimes;
    }

    public LocalDateTime getSendTime() {
        return sendTime;
    }

    public void setSendTime(LocalDateTime sendTime) {
        this.sendTime = sendTime;
    }

    public LocalDateTime getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(LocalDateTime finishTime) {
        this.finishTime = finishTime;
    }

    public Integer getTaskCheckTimes() {
        return taskCheckTimes;
    }

    public void setTaskCheckTimes(Integer taskCheckTimes) {
        this.taskCheckTimes = taskCheckTimes;
    }

    public LocalDateTime getNextTaskCheckTime() {
        return nextTaskCheckTime;
    }

    public void setNextTaskCheckTime(LocalDateTime nextTaskCheckTime) {
        this.nextTaskCheckTime = nextTaskCheckTime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    @Override
    public String toString() {
        return "MessageTaskEntity{" +
                "id=" + id +
                ", msgId=" + msgId +
                ", taskId=" + taskId +
                ", sendTimes=" + sendTimes +
                ", sendTime=" + sendTime +
                ", finishTime=" + finishTime +
                ", taskCheckTimes=" + taskCheckTimes +
                ", nextTaskCheckTime=" + nextTaskCheckTime +
                ", status=" + status +
                ", tenantId=" + tenantId +
                "}";
    }
}
