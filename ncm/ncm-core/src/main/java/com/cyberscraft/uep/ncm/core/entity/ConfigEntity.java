package com.cyberscraft.uep.ncm.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

/**
 * <p>
 * NCM-配置表
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2020-07-20
 */
@TableName("ncm_config")
public class ConfigEntity implements Serializable {

    private static final long serialVersionUID=1L;

      /**
     * 配置ID
     */
        @TableId(value = "id", type = IdType.ASSIGN_ID)
      private Long id;

      /**
     * 配置key
     */
      private String configKey;

      /**
     * 配置value
     */
      private String configValue;

      /**
     * 配置类型，1：EMAIL，2：SMS
     */
      private Integer configType;

      /**
     * 创建时间
     */
      private LocalDateTime createTime;

      /**
     * 更新时间
     */
      private LocalDateTime updateTime;

      /**
     * 创建人
     */
      private String createBy;

      /**
     * 更新人
     */
      private String updateBy;

      /**
     * 租户ID
     */
      private String tenantId;

    
    public Long getId() {
        return id;
    }

      public void setId(Long id) {
          this.id = id;
      }
    
    public String getConfigKey() {
        return configKey;
    }

      public void setConfigKey(String configKey) {
          this.configKey = configKey;
      }
    
    public String getConfigValue() {
        return configValue;
    }

      public void setConfigValue(String configValue) {
          this.configValue = configValue;
      }
    
    public Integer getConfigType() {
        return configType;
    }

      public void setConfigType(Integer configType) {
          this.configType = configType;
      }
    
    public LocalDateTime getCreateTime() {
        return createTime;
    }

      public void setCreateTime(LocalDateTime createTime) {
          this.createTime = createTime;
      }
    
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

      public void setUpdateTime(LocalDateTime updateTime) {
          this.updateTime = updateTime;
      }
    
    public String getCreateBy() {
        return createBy;
    }

      public void setCreateBy(String createBy) {
          this.createBy = createBy;
      }
    
    public String getUpdateBy() {
        return updateBy;
    }

      public void setUpdateBy(String updateBy) {
          this.updateBy = updateBy;
      }
    
    public String getTenantId() {
        return tenantId;
    }

      public void setTenantId(String tenantId) {
          this.tenantId = tenantId;
      }

    @Override
    public String toString() {
        return "ConfigEntity{" +
              "id=" + id +
                  ", configKey=" + configKey +
                  ", configValue=" + configValue +
                  ", configType=" + configType +
                  ", createTime=" + createTime +
                  ", updateTime=" + updateTime +
                  ", createBy=" + createBy +
                  ", updateBy=" + updateBy +
                  ", tenantId=" + tenantId +
              "}";
    }
}
