package com.cyberscraft.uep.ncm.core.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/***
 * 上传文件客户端相关配置
 * @date 2021/7/13
 * <AUTHOR>
 ***/
@Configuration
@RefreshScope
@ConfigurationProperties(prefix = "sys.upload.client")
public class UploadClientConfig {

    /***
     * 上传文件临时目录
     */
    private String tempFilePath="/tmp";

    public String getTempFilePath() {
        return tempFilePath;
    }

    public void setTempFilePath(String tempFilePath) {
        this.tempFilePath = tempFilePath;
    }
}
