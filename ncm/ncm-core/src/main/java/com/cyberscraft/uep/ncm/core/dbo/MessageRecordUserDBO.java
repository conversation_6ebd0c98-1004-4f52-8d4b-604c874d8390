package com.cyberscraft.uep.ncm.core.dbo;

import com.cyberscraft.uep.common.dto.PageView;
import com.cyberscraft.uep.common.dto.Pagination;
import com.cyberscraft.uep.ncm.core.common.domain.MessageUserQueryDTO;
import com.cyberscraft.uep.ncm.core.entity.MessageRecordUserEntity;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cyberscraft.uep.ncm.core.entity.MessageUserEntity;

/**
 * <p>
 * NCM-消息目标用户表 服务类
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2020-08-03
 */
public interface MessageRecordUserDBO extends IService<MessageRecordUserEntity> {

    /**
     * 根据消息id查询消息发送用户列表
     * @param id
     * @return
     */
    List<MessageRecordUserEntity> getListByMessageId(Long id);
    
    /**
     * 分页查询消息发送的用户/组列表
     * @return
     */
    PageView<MessageRecordUserEntity> getMesaageRecordUserPage(Pagination<MessageUserQueryDTO> queryPage);

    /**
     * 通过消息id和用户id查询消息发送用户记录
     * @param msgId
     * @param dataId
     * @return
     */
    MessageRecordUserEntity getByMessageAndUser(Long msgId, String dataId);

}
