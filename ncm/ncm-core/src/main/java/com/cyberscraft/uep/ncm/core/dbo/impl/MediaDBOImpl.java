package com.cyberscraft.uep.ncm.core.dbo.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cyberscraft.uep.common.dto.IQueryConditonApply;
import com.cyberscraft.uep.common.dto.PageView;
import com.cyberscraft.uep.common.dto.Pagination;
import com.cyberscraft.uep.common.util.PagingUtil;
import com.cyberscraft.uep.ncm.core.common.domain.MediaQueryDTO;
import com.cyberscraft.uep.ncm.core.entity.MediaEntity;
import com.cyberscraft.uep.ncm.core.dao.MediaDao;
import com.cyberscraft.uep.ncm.core.dbo.MediaDBO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * NCM-媒体文件信息表 服务实现类
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2020-07-09
 */
@Service
public class MediaDBOImpl extends ServiceImpl<MediaDao, MediaEntity> implements MediaDBO {

    @Resource
    private MediaDao mediaDao;

    /***
     * 进行条件转换处理
     */
    private IQueryConditonApply<MediaQueryDTO, MediaEntity> queryApply = (queryDto, queryWrapper) -> {

        if (queryDto.getType() != null) {
            queryWrapper.eq(MediaEntity::getType, queryDto.getType());
        }
        if (queryDto.getTargetType() != null) {
            queryWrapper.eq(MediaEntity::getTargetType, queryDto.getTargetType());
        }
        if (queryDto.getStartCreateTime() != null) {
            queryWrapper.ge(MediaEntity::getCreatedTime, queryDto.getStartCreateTime());
        }
        if (queryDto.getEndCreateTime() != null) {
            queryWrapper.lt(MediaEntity::getCreatedTime, queryDto.getEndCreateTime());
        }
        if (StringUtils.isNotBlank(queryDto.getQ())) {
            queryWrapper.like(MediaEntity::getTitle, queryDto.getQ());
        }
    };

    @Override
    public void addUseTimes(Long id, Integer step) {
        mediaDao.addUseTimes(id, step);
    }

    @Override
    public PageView<MediaEntity> page(Pagination<MediaQueryDTO> queryPage) {
        Page<MediaEntity> page = PagingUtil.toMybatisPage(queryPage);
        String[] selectStr = queryPage.getRequestAttrs() != null ? queryPage.getRequestAttrs().toArray(new String[0]) : new String[0];
        QueryWrapper<MediaEntity> queryWrapper = new QueryWrapper().select(selectStr);//.lambda();
        MediaQueryDTO queryDto = queryPage.getQueryDto();
        LambdaQueryWrapper<MediaEntity> lambdaQueryWrapper = queryWrapper.lambda();
        //设置排序条件
        if (page.getOrders() != null && page.getOrders().size() > 0) {
        } else {
            lambdaQueryWrapper.orderByDesc(MediaEntity::getId);
        }
        //应用对应的条件
        queryApply.apply(queryDto, lambdaQueryWrapper);

        IPage<MediaEntity> resultPage = baseMapper.selectPage(page, lambdaQueryWrapper);
        return PagingUtil.toPageView(resultPage);
    }
}
