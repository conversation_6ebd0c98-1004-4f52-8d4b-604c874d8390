package com.cyberscraft.uep.ncm.core.service.sms.handler.gxlt;

import com.alibaba.fastjson.JSONObject;
import com.cyberscraft.uep.common.util.MD5Util;
import com.cyberscraft.uep.common.util.RestAPIUtil;
import com.cyberscraft.uep.iam.constants.CommonConstants;
import com.cyberscraft.uep.iam.constants.HttpStatus;
import com.cyberscraft.uep.ncm.client.domain.Sms;
import com.cyberscraft.uep.ncm.core.service.sms.handler.ISmsHandler;
import com.cyberscraft.uep.ncm.core.service.sms.handler.SmsConstant;
import com.cyberscraft.uep.ncm.core.service.sms.handler.SmsHandlerType;
import com.cyberscraft.uep.ncm.core.service.sms.handler.util.SmsUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.client.utils.URLEncodedUtils;
import org.apache.http.message.BasicNameValuePair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * 诚立业短信网关实现类
 */
@Component
public class ClySmsHandler implements ISmsHandler {
    private final static Logger logger = LoggerFactory.getLogger(ClySmsHandler.class);

    private final SmsCodeTranslator translator = new SmsCodeTranslator();

    /**
     * 构建查询参数
     *
     * @param mobile
     * @param message
     * @return
     */
    private String buildQueryString(String mobile, String message, String userName, String passWord) {
        List<NameValuePair> params = new LinkedList<NameValuePair>();
        params.add(new BasicNameValuePair("username", userName));
        params.add(new BasicNameValuePair("password", MD5Util.md5(userName + MD5Util.md5(passWord))));
        params.add(new BasicNameValuePair("mobile", mobile));
        params.add(new BasicNameValuePair("msgid", String.valueOf(System.currentTimeMillis())));
        params.add(new BasicNameValuePair("content", message));

        return URLEncodedUtils.format(params, CommonConstants.UTF8);
    }

    /**
     * 向指定的手机号发送短信
     * <p/>
     * sms消息长度限制为140字节
     *
     * @param mobile  手机号码
     * @param message 短信内容
     * @return
     */
    private JSONObject send(String mobile, String message, String baseUrl, String username, String password, String signature) {
        StringBuilder sb = new StringBuilder(message);
        sb.append(signature);
        ResponseEntity responseEntity = RestAPIUtil.getForEntityNotJson(SmsUtils.normalizeUrl(baseUrl) + buildQueryString(mobile, sb.toString(), username, password));
        int statusCode = responseEntity.getStatusCode().value();
        if (statusCode == HttpStatus.SC_OK) {
            if (responseEntity.getBody() != null) {
                logger.info("Response content is: {}", responseEntity.getBody());
                String ret = translator.transIfPossible(responseEntity.getBody().toString());
                logger.info(ret);
                return buildSuccessStatus();
            } else {
                String msg = "Calling sms api success,but ret content is empty!";
                logger.warn(msg);
                return buildErrorStatus(msg);
            }
        } else {
            if (responseEntity.getBody() != null) {
                logger.error(responseEntity.getBody().toString());
                return buildErrorStatus(responseEntity.getBody().toString());
            } else {
                String msg =String.format("Calling sms api failed,ret http code : %s", statusCode);
                logger.warn(msg);
                return buildErrorStatus(msg);
            }
        }
    }

    @Override
    public Integer getType() {
        return SmsHandlerType.CLY.getValue();
    }

    @Override
    public JSONObject send(Sms sms, Map config) {
        String url = config.getOrDefault(SmsConstant.PROVIDER_GXLT_URL, "").toString();
        String username = config.getOrDefault(SmsConstant.PROVIDER_GXLT_USERNAME, "").toString();
        String password = config.getOrDefault(SmsConstant.PROVIDER_GXLT_PASSWORD, "").toString();

        if (StringUtils.isEmpty(url)){
            String msg =String.format("tenant %s sms gateway config error: url is empty", sms.getTenantId());
            logger.error(msg);
            return buildErrorStatus(msg);
        }

        if (StringUtils.isEmpty(username)){
            String msg =String.format("tenant %s sms gateway config error: username is empty", sms.getTenantId());
            logger.error(msg);
            return buildErrorStatus(msg);
        }

        if (StringUtils.isEmpty(password)){
            String msg =String.format("tenant %s sms gateway config error: password is empty", sms.getTenantId());
            logger.error(msg);
            return buildErrorStatus(msg);
        }

        if (!SmsUtils.isValidMobile(sms.getTarget())) {
            String msg =String.format("tenant %s, target %s is invalid", sms.getTenantId(), sms.getTarget());
            logger.error(msg);
            return buildErrorStatus(msg);
        }

        Map<String, Object> params = sms.getParams();
        boolean isOverseasMobile = SmsUtils.isOverseasMobile(sms.getTarget());

        String sign = (String) params.getOrDefault(SmsConstant.SMS_TEMPLATE_OVERSEAS_TITLE, "");
        String content = (String) params.getOrDefault(SmsConstant.SMS_TEMPLATE_CONTENT, "");

        if (isOverseasMobile && StringUtils.isNotBlank(sign)) {
            content = (String) params.getOrDefault(SmsConstant.SMS_TEMPLATE_OVERSEAS_CONTENT, "");
        } else {
            sign = (String) params.getOrDefault(SmsConstant.SMS_TEMPLATE_TITLE, "");
        }

        if (StringUtils.isEmpty(sign)) {
            String msg =String.format("tenant %s sms gateway config error: sign is empty", sms.getTenantId());
            logger.error(msg);
            return buildErrorStatus(msg);
        }

        if (!SmsUtils.isValidMobile(sms.getTarget())){
            String msg =String.format("tenant %s sms gateway config error: mobile is empty", sms.getTenantId());
            logger.error(msg);
            return buildErrorStatus(msg);
        }
        if (!SmsUtils.isValidMessage(content)) {
            String msg =String.format("tenant %s sms gateway config error: content is empty", sms.getTenantId());
            logger.error(msg);
            return buildErrorStatus(msg);
        }

        String signature = String.format("[%s]", sign);
        //TODO: 提取config信息，转化成短信网关配置信息
        return send(sms.getTarget(), content, url, username, password, signature);
    }

}
