package com.cyberscraft.uep.ncm.core.service;

import com.cyberscraft.uep.common.dto.PageView;
import com.cyberscraft.uep.common.dto.Pagination;
import com.cyberscraft.uep.ncm.core.common.domain.MessageLogQueryDTO;
import com.cyberscraft.uep.ncm.core.common.domain.MessageQueryDTO;
import com.cyberscraft.uep.ncm.core.common.domain.MessageTaskQueryDTO;
import com.cyberscraft.uep.ncm.core.common.domain.MessageUserQueryDTO;
import com.cyberscraft.uep.ncm.core.common.exception.NcmException;
import com.cyberscraft.uep.ncm.core.entity.MessageEntity;
import com.cyberscraft.uep.ncm.core.entity.MessageTaskEntity;
import com.cyberscraft.uep.ncm.core.entity.MessageUserEntity;
import com.cyberscraft.uep.ncm.dto.*;
import com.cyberscraft.uep.ncm.enums.MessageTypeEnum;
import com.cyberscraft.uep.ncm.enums.MessageUserTypeEnum;

import java.util.List;
import java.util.Map;

/***
 *
 * @date 2021/7/14
 * <AUTHOR>
 ***/
public interface IMessageService {

    /***
     * 发送消息
     * @param dto
     */
    Long send(MessageDTO dto);

    /***
     *
     * @param obj
     */
    void add(MessageEntity obj);

    /***
     *
     * @param obj
     */
    void modify(MessageEntity obj);

    /***
     *
     * @param id
     * @return
     */
    MessageEntity get(Long id);

    /***
     *
     * @param id
     */
    void remove(Long id);

    /***
     *
     * @param obj
     */
    void addMessageTask(MessageTaskEntity obj);

    /***
     *
     * @param obj
     */
    void modifyMessageTask(MessageTaskEntity obj);

    /****
     *
     * @param id
     * @param excludTaskId
     * @return
     */
    int getUnFinishedMessageTaskCount(Long id, Long excludTaskId);

    /***
     *
     * @param obj
     */
    void modifyMessageUser(MessageUserEntity obj);

    /***
     *
     * @param obj
     */
    void addMessageUser(MessageUserEntity obj);

    /****
     * 获取消息用户对像
     * @param id
     * @return
     */
    MessageUserEntity getMessageUser(Long id);

    /***
     *
     * @param ids
     * @return
     * @throws NcmException
     */
    List<MessageEntity> getMessages(List<Long> ids) throws NcmException;

    /***
     *  分页获取消息列表
     * @return
     */
    PageView<MessageEntity> page(Pagination<MessageQueryDTO> queryPage);

    /***
     * 分页查询消息任务信息
     * @param queryPage
     * @return
     */
    PageView<MessageTaskEntity> pageMessageTasks(Pagination<MessageTaskQueryDTO> queryPage);


    /***
     *
     * @param msgId
     * @param dataId
     * @return
     */
    MessageUserEntity getMessageUserByMessageAndUser(Long msgId, String dataId);

    /***
     * 查询消息对应的用户详情列表
     * @param msgId
     * @return
     */
    List<MessageUserEntity> getMessageUsersByMessage(Long msgId);


    /***
     *
     * @param msgId
     * @return
     */
    int getMessageUserCount(Long msgId);

    /***
     * 根据状态查询消息对应的用户详情列表
     * @param msgId
     * @param statuses
     * @return
     */
    List<MessageUserEntity> getMessageUsersByMessageAndStatus(Long msgId, List<Integer> statuses);

    /***
     * 根据状态查询消息对应的用户详情列表
     * @param taskId
     * @param statuses
     * @return
     */
    List<MessageUserEntity> getMessageUsersByTaskAndStatus(String taskId, List<Integer> statuses);


    /***
     * 根据发送结果查询消息对应的用户详情列表
     * @param msgId
     * @param results
     * @return
     */
    List<MessageUserEntity> getMessageUsersByMessageAndResult(Long msgId, List<Integer> results);

    /****
     * 获取消息发送进度
     * @param id
     * @return
     * @throws NcmException
     */
    MessageSendProgressVO getMessageSendProgress(Long id) throws NcmException;

    /**
     * 根据消息id获取消息详情
     *
     * @param id
     * @return
     * @throws NcmException
     */
    SendMessageVO getMessageDetail(Long id) throws NcmException;

    /**
     * 分页获取消息发送的用户/组列表
     *
     * @param queryPage
     * @return
     * @throws NcmException
     */
    PageView<SendMessageUserListVO> getMessageUserListPage(Pagination<MessageUserQueryDTO> queryPage) throws NcmException;

    /***
     *  分页获取消息列表
     * @return
     */
    PageView<SendMessageListVO> getMessageListPage(Pagination<MessageQueryDTO> queryPage);

    /**
     * 分页获取消息日志列表
     *
     * @param queryPage
     * @return
     * @throws NcmException
     */
    PageView<SendMessageLogListVO> getMessageLogListPage(Pagination<MessageLogQueryDTO> queryPage);

    /**
     * 根据消息id获取正在处理的消息详情
     *
     * @param id
     * @return
     * @throws NcmException
     */
    SendingMessageVO getSendingMessageDetail(Long id) throws NcmException;

    /***
     *  分页获取消息列表
     * @return
     */
    PageView<SendingMessageListVO> getSendingMessageListPage(Pagination<MessageQueryDTO> queryPage);

    /**
     * 校验发送消息时的消息内容是否符合特定的格式
     * @param messageType 消息类型
     * @param content 消息内容
     * @return
     */
    boolean isValidMessageContent(MessageTypeEnum messageType, Map<String, Object> content);

    /**
     * 校验发送消息时的userDataIds(组ID/用户ID)是否合法
     * @param userType
     * @param userDataIds
     * @return
     */
    boolean isValidMessageUserDataIds(MessageUserTypeEnum userType, List<String> userDataIds);
}
