package com.cyberscraft.uep.ncm.core.common.domain;

import com.cyberscraft.uep.common.dto.IQueryDto;
import com.cyberscraft.uep.ncm.enums.MessageSendStatusEnum;

/**
 * 查询消息发送的用户/组的查询对象
 * <AUTHOR>
 * @date Created：2020-08-06
 */
public class MessageUserQueryDTO implements IQueryDto {

    /**
     * 
     */
    private static final long serialVersionUID = 1L;
    
    /**
     * 消息ID
     */
    private Long messageId;
    
    /**
     * 消息发送状态
     */
    private MessageSendStatusEnum messageSendStatus;
    
    /**
     * 发送的用户类型
     * @see com.cyberscraft.uep.ncm.enums.MessageUserTypeEnum
     */
    private Integer userType;
    
    /**
     * 消息发送结果（1、发送成功，2、无效用户，3、禁止发送，4、发送失败)
     */
    private Integer result;

    public Long getMessageId() {
        return messageId;
    }

    public void setMessageId(Long messageId) {
        this.messageId = messageId;
    }

    public Integer getUserType() {
        return userType;
    }

    public void setUserType(Integer userType) {
        this.userType = userType;
    }

    public Integer getResult() {
        return result;
    }

    public void setResult(Integer result) {
        this.result = result;
    }

    public MessageSendStatusEnum getMessageSendStatus() {
        return messageSendStatus;
    }

    public void setMessageSendStatus(MessageSendStatusEnum messageSendStatus) {
        this.messageSendStatus = messageSendStatus;
    }

}
