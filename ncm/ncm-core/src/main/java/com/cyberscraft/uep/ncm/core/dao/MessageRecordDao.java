package com.cyberscraft.uep.ncm.core.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cyberscraft.uep.ncm.core.entity.MessageRecordEntity;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * <p>
 * NCM-发送失败消息信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2020-08-03
 */
public interface MessageRecordDao extends BaseMapper<MessageRecordEntity> {
    /**
     * 分页查询记录
     *
     * @param page
     * @param bizType
     * @param userName
     * @param userEmail
     * @param userPhone
     * @param templateCode
     * @param status
     * @param startTime
     * @param endTime
     * @return
     */
    IPage<Map<String, Object>> pageMessageLogRecord(Page<Map<String, Object>> page,
                                                    @Param("bizType") Integer bizType,
                                                    @Param("userName") String userName,
                                                    @Param("userEmail") String userEmail,
                                                    @Param("userPhone") String userPhone,
                                                    @Param("templateCode") String templateCode,
                                                    @Param("status") Integer status,
                                                    @Param("startTime") Long startTime,
                                                    @Param("endTime") Long endTime,
                                                    @Param("tenantId") String tenantId
    );

}
