package com.cyberscraft.uep.ncm.core.service.user.parser;

import com.cyberscraft.uep.iam.dto.response.OrgBasicInfoVO;
import com.cyberscraft.uep.ncm.core.common.domain.MessageUser;
import com.cyberscraft.uep.ncm.core.service.IMessageUserParser;
import com.cyberscraft.uep.ncm.dto.MessageUserOrgInfoVO;
import com.cyberscraft.uep.ncm.enums.MessageIdTypeEnum;
import com.cyberscraft.uep.ncm.enums.MessageUserTypeEnum;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/***
 * 按照组ID发送的用户解析器
 * @date 2021/8/4
 * <AUTHOR>
 ***/
@Component
public class MessageOrgIdParser extends AbstractMessageUserParser implements IMessageUserParser {

    @Override
    public Integer getSupportUserType() {
        return MessageUserTypeEnum.ORG.getValue();
    }

    @Override
    public List<MessageUser> parserUsers(List<String> userDataIds, Integer targetType, boolean isAuthBind) {
        if (userDataIds == null || userDataIds.size() == 0) {
            return null;
        }
        List<Long> orgIds = new ArrayList<>(userDataIds.size());
        for (String dataId : userDataIds) {
            try {
                Long id = Long.parseLong(dataId);
                orgIds.add(id);
            } catch (Exception e) {
            }
        }
        Map<Long, String> orgDataMap = orgIds.size() > 0 ? iamUserClientService.getSnsGroupCodesByOrgIds(orgIds, targetType, isAuthBind) : null;
        Map<String, String> targetDataIdMap = new HashMap<>(userDataIds.size());
        if (orgDataMap != null) {
            for (Map.Entry<Long, String> entry : orgDataMap.entrySet()) {
                targetDataIdMap.put(String.valueOf(entry.getKey()), entry.getValue());
            }
        }
        return parserUsers(userDataIds, targetDataIdMap, MessageUserTypeEnum.ORG);
    }

    @Override
    public List<MessageUser> startParserUsers(List<String> userDataIds, MessageIdTypeEnum idType, Integer targetType, boolean isAuthBind) {
        if (userDataIds == null || userDataIds.isEmpty()) {
            return null;
        }

        if (MessageIdTypeEnum.DINGDING == idType) {
            // 钉钉 userId 和 iam_user id 对应关系的集合
            Map<String, Long> userDataIdsMap = iamUserClientService.getOrgIdsMapByThirdPartyGroupCodes(userDataIds, targetType, isAuthBind);
            Map<String, String> targetDataIdMap = new HashMap<>(userDataIds.size());
            if (userDataIdsMap != null) {
                for (Map.Entry<String, Long> entry : userDataIdsMap.entrySet()) {
                    targetDataIdMap.put(String.valueOf(entry.getValue()), entry.getKey());
                }
            }
            userDataIds.clear();
            userDataIds.addAll(targetDataIdMap.keySet());
            //  iam_user id  钉钉 user_id 用户
            return parserUsers(userDataIds, targetDataIdMap, MessageUserTypeEnum.ORG);
        }
        List<Long> orgIds = new ArrayList<>(userDataIds.size());
        for (String dataId : userDataIds) {
            try {
                Long id = Long.parseLong(dataId);
                orgIds.add(id);
            } catch (Exception e) {
            }
        }
        Map<Long, String> orgDataMap = orgIds.size() > 0 ? iamUserClientService.getSnsGroupCodesByOrgIds(orgIds, targetType, isAuthBind) : null;
        Map<String, String> targetDataIdMap = new HashMap<>(userDataIds.size());
        if (orgDataMap != null) {
            for (Map.Entry<Long, String> entry : orgDataMap.entrySet()) {
                targetDataIdMap.put(String.valueOf(entry.getKey()), entry.getValue());
            }
        }
        return parserUsers(userDataIds, targetDataIdMap, MessageUserTypeEnum.ORG);
    }

    @Override
    public Map<String, MessageUserOrgInfoVO> parserUserOrgInfo(List<String> userDataIds) {
        if (userDataIds == null || userDataIds.size() == 0) {
            return Collections.emptyMap();
        }
        List<Long> orgIds = userDataIds.stream().map(Long::valueOf).collect(Collectors.toList());
        List<OrgBasicInfoVO> orgBasicInfoVOList = iamUserClientService.getOrgBasicInfosByOrgIds(orgIds);
        int size;
        if (orgBasicInfoVOList != null && (size = orgBasicInfoVOList.size()) > 0) {
            Map<String, MessageUserOrgInfoVO> messageUserOrgInfoMap = new HashMap<>(size);
            orgBasicInfoVOList.forEach(orgBasicInfo -> {
                MessageUserOrgInfoVO vo = messageUserTransfer.orgBasic2UserOrgInfoVO(orgBasicInfo);
                vo.setOrgNames(orgBasicInfo.getParentOrgName());
                messageUserOrgInfoMap.put(vo.getId(), vo);
            });
            return messageUserOrgInfoMap;
        }
        return Collections.emptyMap();
    }

    @Override
    public boolean isValidUserDataIds(List<String> userDataIds) {
        if (userDataIds == null || userDataIds.size() == 0 || userDataIds.size() > 20) {
            return false;
        }
        for (String dataId : userDataIds) {
            if (dataId != null && dataId.startsWith("-")) {
                dataId = dataId.substring(1, dataId.length());
            }
            if (!StringUtils.isNumeric(dataId)) {
                return false;
            }
        }
        return true;
    }
}
