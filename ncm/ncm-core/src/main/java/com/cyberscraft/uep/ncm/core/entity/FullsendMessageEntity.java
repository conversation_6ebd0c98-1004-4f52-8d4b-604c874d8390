package com.cyberscraft.uep.ncm.core.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serializable;

/**
 * <p>
 * NCM全站发送成功记录表
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2020-07-09
 */
@TableName("ncm_fullsend_message")
public class FullsendMessageEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 消息类型
     */
    private Integer type;

    /**
     * 消息标题
     */
    private String title;

    /**
     * 消息内容,为一个json体，不同的消息存储不同的内容
     */
    private String content;

    /**
     * 消息的发送人
     */
    private Long sender;

    /**
     * 发送的目标平台0InSite,5DingDing,4Wework,参考ThirdPartyAccountType的定义
     */
    private Integer targetType;

    /**
     * 消息发送时间
     */
    private LocalDateTime sendTime;

    /**
     * 消息发送完成时间
     */
    private LocalDateTime sendFinishedTime;

    /**
     * 消息的创建时间，即业务代码中的发送时间
     */
    private LocalDateTime createdTime;
    /**
     * 当前已经发送次数
     */
    private Integer sendTimes;

    /**
     * 租户ID
     */
    private String tenantId;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Long getSender() {
        return sender;
    }

    public void setSender(Long sender) {
        this.sender = sender;
    }

    public Integer getTargetType() {
        return targetType;
    }

    public void setTargetType(Integer targetType) {
        this.targetType = targetType;
    }

    public LocalDateTime getSendTime() {
        return sendTime;
    }

    public void setSendTime(LocalDateTime sendTime) {
        this.sendTime = sendTime;
    }

    public LocalDateTime getSendFinishedTime() {
        return sendFinishedTime;
    }

    public void setSendFinishedTime(LocalDateTime sendFinishedTime) {
        this.sendFinishedTime = sendFinishedTime;
    }

    public Integer getSendTimes() {
        return sendTimes;
    }

    public void setSendTimes(Integer sendTimes) {
        this.sendTimes = sendTimes;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    @Override
    public String toString() {
        return "FullsendMessageEntity{" +
                "id=" + id +
                ", type=" + type +
                ", title=" + title +
                ", content=" + content +
                ", sender=" + sender +
                ", targetType=" + targetType +
                ", sendTime=" + sendTime +
                ", sendFinishedTime=" + sendFinishedTime +
                ", sendTimes=" + sendTimes +
                ", tenantId=" + tenantId +
                "}";
    }
}
