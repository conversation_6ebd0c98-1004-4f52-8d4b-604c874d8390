package com.cyberscraft.uep.ncm.core.service.handle.parser;

import com.cyberscraft.uep.common.domain.message.MessageText;
import com.cyberscraft.uep.common.domain.message.MessageTypeEnum;
import com.cyberscraft.uep.common.domain.message.ThirdMessageBody;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.ncm.client.domain.message.MessageBody;
import com.cyberscraft.uep.ncm.client.domain.message.MessageBodyText;
import com.cyberscraft.uep.ncm.core.service.handle.IMessageParser;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/***
 *
 * @date 2021/7/17
 * <AUTHOR>
 ***/
@Component
public class MessageTextParser implements IMessageParser {

    @Override
    public MessageTypeEnum getSupportedMessageType() {
        return MessageTypeEnum.TEXT;
    }

    @Override
    public ThirdMessageBody parse(String content) {
        if (StringUtils.isBlank(content)) {
            return null;
        }
        MessageBodyText body = JsonUtil.str2Obj(content, MessageBodyText.class);
        return parse(body);
    }

    @Override
    public ThirdMessageBody parse(MessageBody body) {
        if (body == null) {
            return null;
        }
        MessageText ret = new MessageText();
        MessageBodyText textBody = (MessageBodyText) body;
        ret.setText(textBody.getText());
        ret.setTitle(textBody.getTitle());
        ret.setMsgType(MessageTypeEnum.TEXT);
        return ret;
    }

    @Override
    public void addMediaUseTimes(MessageBody body) {

    }

    @Override
    public void addMediaUseTimes(String content) {

    }
}
