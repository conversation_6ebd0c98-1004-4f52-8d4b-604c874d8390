package com.cyberscraft.uep.ncm.core.service;

import com.cyberscraft.uep.ncm.client.domain.Message;

/***
 *
 * @date 2021/7/14
 * <AUTHOR>
 ***/
public interface INotificationMessageSendService {

    /***
     * 发送消息到目标平台对应的目标用户
     * @param message
     */
    void send(Message message);

    /***
     * 检查发送结果，需要同一时间内，保证只有一个服务在运行，如果性能不够时，可以改成分布式处理，即一个检查入队，多个检查服务
     */
    void checkSendResult();

    /***
     * 对失败的记录进行重发处理,及对于已经发送完成的进行检查，如果是超过指定的发送次数，则移至发送记录中
     */
    void resend();
}
