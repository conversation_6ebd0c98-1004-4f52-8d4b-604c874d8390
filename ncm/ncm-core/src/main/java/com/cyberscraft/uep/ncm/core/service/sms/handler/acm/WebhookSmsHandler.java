package com.cyberscraft.uep.ncm.core.service.sms.handler.acm;

import com.alibaba.fastjson.JSONObject;
import com.cyberscraft.uep.common.bean.RestApiResponse;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.common.util.RestAPIUtil;
import com.cyberscraft.uep.ncm.client.domain.Sms;
import com.cyberscraft.uep.ncm.core.service.sms.handler.ISmsHandler;
import com.cyberscraft.uep.ncm.core.service.sms.handler.SmsHandlerType;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class WebhookSmsHandler implements ISmsHandler {

    private static final Logger logger = LoggerFactory.getLogger(WebhookSmsHandler.class);

    @Override
    public Integer getType() {
        return SmsHandlerType.WEBHOOK.getValue();
    }

    @Override
    public JSONObject send(Sms sms, Map config) throws Exception {
        try {
            String endpoint = (String) config.get("endpoint");
            if (StringUtils.isEmpty(endpoint)) {
                String msg =String.format("tenant %s sms gateway config error: url is empty", sms.getTenantId());
                logger.error(msg);
                return buildErrorStatus(msg);
            }
            Map<String, Object> params = sms.getParams();
            params.put("mobile", sms.getTarget());

            RestApiResponse post = RestAPIUtil.modifyEntityForString(endpoint, "POST", params, new HashMap<>(), new HashMap<>(), null);
            logger.info("send sms result {}", JsonUtil.obj2Str(post));
            if (post.getHttpStatus() != 200) {
                logger.error("tenant {} sms gateway config error: url is empty", sms.getTenantId());
                throw new Exception("tenant {} sms gateway config error: url is empty", null);
            }
            Map<String, Object> map = JsonUtil.str2Map((String) post.getBody());
            if (map != null) {
                Map data = (Map) map.get("data");
                if (data != null) {
                    if (data.get("code") != null && 200 == (Integer) data.get("code") && data.get("value") != null && (Boolean) data.get("value")) {
                        logger.info("send sms success");
                    } else {
                        throw new Exception("send sms fail error :" + (String) post.getBody());
                    }
                }
            } else {
                throw new Exception("send sms fail error :" + (String) post.getBody());
            }
        } catch (Exception e) {
            throw new Exception("send sms fail error :" + e.getMessage(), e);
        }
        return buildSuccessStatus();
    }
}
