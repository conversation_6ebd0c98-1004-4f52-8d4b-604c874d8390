package com.cyberscraft.uep.ncm.core.service.impl;

import com.cyberscraft.uep.common.bean.Result;
import com.cyberscraft.uep.common.enums.ExceptionCodeEnum;
import com.cyberscraft.uep.iam.api.inner.SNSCorpInfoApi;
import com.cyberscraft.uep.iam.dto.response.configs.SnsConfigVO;
import com.cyberscraft.uep.ncm.core.common.exception.NcmException;
import com.cyberscraft.uep.ncm.core.service.ISnsConfigClientService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/***
 *
 * @date 2021/7/13
 * <AUTHOR>
 ***/
@Service
public class SnsConfigClientServiceImpl implements ISnsConfigClientService {

    /***
     *
     */
    @Resource
    private SNSCorpInfoApi snsCorpInfoApi;

    @Override
    public SnsConfigVO getSnsConfigWithDefault(Long id, Integer type) {
        Result<SnsConfigVO> rs = snsCorpInfoApi.getSnsConfig(id, type);
        SnsConfigVO obj = rs != null ? rs.getData() : null;
        if (obj == null) {
            if (StringUtils.isNotBlank(rs.getCode())) {
                throw new NcmException(rs.getCode(), rs.getMessage());
            } else {
                throw new NcmException(ExceptionCodeEnum.UNKNOW_ERROR);
            }
        }
        return obj;
    }
}
