package com.cyberscraft.uep.ncm.core.dbo;

import com.cyberscraft.uep.common.dto.PageView;
import com.cyberscraft.uep.common.dto.Pagination;
import com.cyberscraft.uep.ncm.core.common.domain.MediaQueryDTO;
import com.cyberscraft.uep.ncm.core.entity.MediaEntity;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 * NCM-媒体文件信息表 服务类
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2020-07-09
 */
public interface MediaDBO extends IService<MediaEntity> {

    /***
     *
     * @param id
     * @param step
     */
    void addUseTimes(Long id, Integer step);

    /***
     *
     * @param pagination
     * @return
     */
    PageView<MediaEntity> page(Pagination<MediaQueryDTO> pagination);
}
