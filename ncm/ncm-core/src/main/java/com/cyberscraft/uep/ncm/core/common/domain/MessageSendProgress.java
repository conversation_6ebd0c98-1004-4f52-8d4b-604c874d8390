package com.cyberscraft.uep.ncm.core.common.domain;

import java.io.Serializable;
import java.time.LocalDateTime;

/***
 *
 * @date 2021/8/10
 * <AUTHOR>
 ***/
public class MessageSendProgress implements Serializable {

    private static final long serialVersionUID = 358942976093892584L;
    /***
     *
     */
    private Long id;

    /***
     *
     */
    private Integer status;

    /***
     * 发送时间
     */
    private LocalDateTime sendTime;

    /***
     * 发送完成时间
     */
    private LocalDateTime finishTime;

    public MessageSendProgress() {
    }

    public MessageSendProgress(Long id, Integer status) {
        this.id = id;
        this.status = status;
        this.sendTime = LocalDateTime.now();
    }


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public LocalDateTime getSendTime() {
        return sendTime;
    }

    public void setSendTime(LocalDateTime sendTime) {
        this.sendTime = sendTime;
    }

    public LocalDateTime getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(LocalDateTime finishTime) {
        this.finishTime = finishTime;
    }
}
