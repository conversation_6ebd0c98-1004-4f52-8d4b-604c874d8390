package com.cyberscraft.uep.ncm.core.service.impl;

import com.cyberscraft.uep.common.util.UUIDUtil;
import com.cyberscraft.uep.ncm.core.config.UploadClientConfig;
import com.cyberscraft.uep.ncm.core.service.handle.IMediaHandler;
import com.cyberscraft.uep.ncm.core.service.IThirdPartyMediaManageService;
import com.cyberscraft.uep.ncm.enums.MediaTypeEnum;
import com.cyberscraft.uep.ncm.enums.TargetTypeEnum;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/***
 *
 * @date 2021/7/13
 * <AUTHOR>
 ***/
@Service
public class ThirdPartyMediaManageServiceImpl implements IThirdPartyMediaManageService {

    @Resource
    private UploadClientConfig uploadClientConfig;

    /***
     *
     */
    private List<IMediaHandler> handlers;

    /***
     *
     */
    private final static Map<Integer, IMediaHandler> TARGET_TYPE_HANDLER = new HashMap<>();

    private synchronized void initHandlerMap() {
        TARGET_TYPE_HANDLER.clear();
        if (this.handlers != null && this.handlers.size() > 0) {
            for (IMediaHandler handler : this.handlers) {
                Integer type = handler.getSupportedTargetType();
                TARGET_TYPE_HANDLER.put(type, handler);
            }
        }
    }

    public List<IMediaHandler> getHandlers() {
        return handlers;
    }

    @Autowired(required = false)
    public void setHandlers(List<IMediaHandler> handlers) {
        this.handlers = handlers;
        initHandlerMap();
    }

    /***
     *
     */
    private final static Logger LOG = LoggerFactory.getLogger(ThirdPartyMediaManageServiceImpl.class);

    @Override
    public String upload(String tenantId, TargetTypeEnum targetType, MediaTypeEnum type, String config, MultipartFile file) {
        String fileExt = file.getOriginalFilename().indexOf(".") > 0 ? file.getOriginalFilename().substring(file.getOriginalFilename().indexOf(".")) : "";
        String tempFileName = uploadClientConfig.getTempFilePath() + File.separator + UUIDUtil.getUUID() + fileExt;
        try {
            File mediaFile = new File(tempFileName);
            file.transferTo(mediaFile);
            return upload(tenantId, targetType, type, config, mediaFile);
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        } finally {
            if (StringUtils.isNotBlank(tempFileName)) {
                try {
                    FileUtils.forceDelete(new File(tempFileName));
                } catch (Exception e) {

                }
            }
        }
        return null;
    }

    @Override
    public String upload(String tenantId, TargetTypeEnum targetType, MediaTypeEnum type, String config, File file) {
        IMediaHandler handler = findHandler(targetType.getValue());
        if (handler == null) {
            LOG.info("can not find media deal handler, media type is :{}", type);
            return null;
        }
        return handler.upload(tenantId, type, config, file);
    }

    private IMediaHandler findHandler(Integer targetType) {
        return TARGET_TYPE_HANDLER.get(targetType);
    }
}
