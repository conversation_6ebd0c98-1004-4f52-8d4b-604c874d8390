package com.cyberscraft.uep.ncm.core.service.handle.parser;

import com.cyberscraft.uep.ncm.core.entity.MediaEntity;
import com.cyberscraft.uep.ncm.core.service.IMediaService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/***
 *
 * @date 2021/7/21
 * <AUTHOR>
 ***/
@Component
public abstract class AbstractMessageBodyParser {

    @Resource
    private IMediaService mediaService;

    /***
     *
     * @param mediaId
     * @return
     */
    protected String parseThirdMedia(String mediaId) {
        if (StringUtils.isBlank(mediaId)) {
            return null;
        }
        MediaEntity obj = mediaService.getMedia(mediaId);
//        if (obj != null) {
//            mediaService.addUseTimes(obj.getId(), 1);
//        }
        return obj != null ? obj.getThirdMediaId() : mediaId;
    }

    /***
     * 增加媒体文件的使用计数
     * @param mediaId
     */
    protected void addMediaFileUseTimes(String mediaId) {
        if (StringUtils.isBlank(mediaId)) {
            return;
        }
        MediaEntity obj = mediaService.getMedia(mediaId);
        if (obj != null) {
            mediaService.addUseTimes(obj.getId(), 1);
        }
    }
}
