package com.cyberscraft.uep.ncm.core.dbo.impl;

import com.cyberscraft.uep.ncm.core.entity.FullsendMessageEntity;
import com.cyberscraft.uep.ncm.core.dao.FullsendMessageDao;
import com.cyberscraft.uep.ncm.core.dbo.FullsendMessageDBO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 * NCM全站发送成功记录表 服务实现类
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2020-07-09
 */
@Service
public class FullsendMessageDBOImpl extends ServiceImpl<FullsendMessageDao, FullsendMessageEntity> implements FullsendMessageDBO {

}
