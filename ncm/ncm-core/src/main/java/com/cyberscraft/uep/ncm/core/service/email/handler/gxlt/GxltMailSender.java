package com.cyberscraft.uep.ncm.core.service.email.handler.gxlt;

import org.springframework.mail.javamail.JavaMailSender;

/**
 * mail发送类，封装了sendFrom，username等信息
 * <AUTHOR>
 */
public class GxltMailSender {
    private JavaMailSender javaMailSender;
    private String sendFrom;
    private String username;

    public JavaMailSender getJavaMailSender() {
        return javaMailSender;
    }

    public void setJavaMailSender(JavaMailSender javaMailSender) {
        this.javaMailSender = javaMailSender;
    }

    public String getSendFrom() {
        return sendFrom;
    }

    public void setSendFrom(String sendFrom) {
        this.sendFrom = sendFrom;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }
}
