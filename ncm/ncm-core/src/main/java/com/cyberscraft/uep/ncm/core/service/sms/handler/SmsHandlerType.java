package com.cyberscraft.uep.ncm.core.service.sms.handler;

import com.cyberscraft.uep.ncm.enums.IBaseEnum;

/**
 * 目前支持的短信网关类型
 * <AUTHOR>
 */
public enum SmsHandlerType implements IBaseEnum {
    /***
     * 诚立业短信网关
     */
    CLY(0,"诚立业短信"),
    /***
     * 北理工短信网关
     */
    BIT(1,"北理工短信"),
    /***
     * 国家认可委短信网关
     */
    CNAS(2,"国家认可委短信"),
    /***
     * 安徽交控短信网关
     */
    AHJK(3,"安徽交控短信"),
    /***
     * 阿里云短信网关
     */
    ALI(4,"阿里云短信"),
    /**
     * 通过ACM 连接流方式发送短信
     */
    WEBHOOK(5,"连接流发送短信"),
    ;

    private int value;
    /**
     * 描述
     */
    private String desc;

    SmsHandlerType(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    @Override
    public final int getValue() {
        return this.value;
    }

    public String getDesc() {
        return desc;
    }

    @Override
    public String toString() {
        return this.name();
    }

    public static SmsHandlerType getByName(String name) {
        for (SmsHandlerType type : SmsHandlerType.values()) {
            if (type.name().equals(name)) {
                return type;
            }
        }
        return null;
    }

}
