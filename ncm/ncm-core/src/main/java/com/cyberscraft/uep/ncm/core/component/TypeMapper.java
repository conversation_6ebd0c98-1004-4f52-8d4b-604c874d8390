package com.cyberscraft.uep.ncm.core.component;

import com.google.common.base.Strings;
import com.cyberscraft.uep.common.constant.SplitStrConstant;
import com.cyberscraft.uep.ncm.enums.BooleanEnum;
import com.cyberscraft.uep.ncm.enums.IBaseEnum;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.TargetType;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Stream;

import static java.util.regex.Pattern.compile;

/**
 * <p>
 * 枚举转换类
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2020-04-20 11:00
 */
@Component
public class TypeMapper {
    public static Long asLong(Date date) {
        if (date != null) {
            return TimeUnit.MILLISECONDS.toSeconds(date.getTime());
        }
        return null;
    }

    public static Integer asInt(Boolean bool) {
        if (bool == null) {
            return null;
        }
        if (bool) {
            return BooleanEnum.TRUE.getValue();
        } else {
            return BooleanEnum.FALSE.getValue();
        }
    }

    public static Boolean asBoolean(Integer intValue) {
        if (intValue == null) {
            return false;
        }
        if (intValue.intValue() == BooleanEnum.TRUE.getValue()) {
            return true;
        } else if (intValue.intValue() == BooleanEnum.FALSE.getValue()) {
            return false;
        }

        throw new IllegalArgumentException(String.format("failed to convert %d to boolean", intValue.intValue()));
    }

    public static Integer asInt(IBaseEnum iBaseEnum) {
        if (iBaseEnum == null) {
            return null;
        }
        return iBaseEnum.getValue();
    }

    /**
     * 整形值映射为枚举
     *
     * @param intEnumValue 枚举值
     * @param enumClass    枚举类
     * @return <E> 对应枚举
     */
    public static <E extends Enum<?>> E asEnum(Integer intEnumValue, @TargetType Class<E> enumClass) {
        if (intEnumValue == null) {
            return null;
        }
        E[] es = enumClass.getEnumConstants();
        for (E e : es) {
            IBaseEnum iBaseEnum = (IBaseEnum) e;
            if (iBaseEnum.getValue() == intEnumValue.intValue()) {
                return e;
            }
        }
        throw new IllegalArgumentException(String.format("failed to convert %s to enum class type %s", intEnumValue, enumClass.getName()));
    }

    public static Long date2long(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        return localDateTime.toInstant(ZoneOffset.of("+8")).toEpochMilli() / 1000;
    }

    public static LocalDateTime long2date(Long dateTime) {
        if (dateTime == null) {
            return null;
        }
        Pattern pattern = compile("^([0-9]{13})$");
        Matcher matcher = pattern.matcher(dateTime + "");
        if (matcher.matches()) {
            return Instant.ofEpochSecond(dateTime).atOffset(ZoneOffset.of("+08:00")).toLocalDateTime();
        } else {
            throw new RuntimeException();
        }
    }

    public static Set<String> string2set(String validFactorsString) {
        if (StringUtils.isBlank(validFactorsString)) {
            return null;
        }
        String[] validFactorsArray = validFactorsString.split(SplitStrConstant.COMMA);
        Set<String> factorsList;
        if (validFactorsArray.length == 0) {
            return null;
        }
        factorsList = new HashSet<>();
        Stream.of(validFactorsArray).forEach(factor -> {
            if (!Strings.isNullOrEmpty(factor)) {
                factorsList.add(factor);
            }
        });
        return factorsList;
    }

    public static String set2string(Set<String> validFactorList) {
        if (null == validFactorList) {
            return null;
        }

        if (validFactorList.size() == 0) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        validFactorList.forEach(fac -> {
            if (!Strings.isNullOrEmpty(fac)) {
                sb.append(fac).append(SplitStrConstant.COMMA);
            }
        });
        if (sb.length() == 0) {
            return "";
        }
        sb.delete(sb.length() - SplitStrConstant.COMMA.length(), sb.length());
        return sb.toString();
    }
}
