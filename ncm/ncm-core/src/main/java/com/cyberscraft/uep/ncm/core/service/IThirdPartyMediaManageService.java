package com.cyberscraft.uep.ncm.core.service;

import com.cyberscraft.uep.ncm.enums.MediaTypeEnum;
import com.cyberscraft.uep.ncm.enums.TargetTypeEnum;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;

/***
 * 第三方平台媒体文件管理接口
 * @date 2021/7/13
 * <AUTHOR>
 ***/
public interface IThirdPartyMediaManageService {

    /***
     *
     * @param tenantId
     * @param targetType
     * @param type
     * @param config
     * @param file
     * @return
     */
    String upload(String tenantId, TargetTypeEnum targetType, MediaTypeEnum type, String config, MultipartFile file);

    /***
     *
     * @param tenantId
     * @param targetType
     * @param type
     * @param config
     * @param file
     * @return
     */
    String upload(String tenantId, TargetTypeEnum targetType, MediaTypeEnum type, String config, File file);
}
