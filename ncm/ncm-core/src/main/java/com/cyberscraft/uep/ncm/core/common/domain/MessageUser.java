package com.cyberscraft.uep.ncm.core.common.domain;

import com.cyberscraft.uep.ncm.enums.MessageSendResultEnum;
import com.cyberscraft.uep.ncm.enums.MessageUserTypeEnum;

/***
 *
 * @date 2021/8/4
 * <AUTHOR>
 ***/
public class MessageUser {
    /***
     * 用户类型
     */
    private MessageUserTypeEnum userType;
    /***
     * 原始数据ID
     */
    private String dataId;
    /***
     * 第三方平台，目标平台用户类型
     */
    private String thirdDataId;
    /***
     * 发送结果
     */
    private MessageSendResultEnum result;
    /***
     * 发送结果对应的任务ID
     */
    private String taskId;
    /**
     * 当前已经发送次数
     */
    private Integer sendTimes;

    public MessageUserTypeEnum getUserType() {
        return userType;
    }

    public void setUserType(MessageUserTypeEnum userType) {
        this.userType = userType;
    }

    public String getDataId() {
        return dataId;
    }

    public void setDataId(String dataId) {
        this.dataId = dataId;
    }

    public String getThirdDataId() {
        return thirdDataId;
    }

    public void setThirdDataId(String thirdDataId) {
        this.thirdDataId = thirdDataId;
    }

    public MessageSendResultEnum getResult() {
        return result;
    }

    public void setResult(MessageSendResultEnum result) {
        this.result = result;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public Integer getSendTimes() {
        return sendTimes;
    }

    public void setSendTimes(Integer sendTimes) {
        this.sendTimes = sendTimes;
    }
}
