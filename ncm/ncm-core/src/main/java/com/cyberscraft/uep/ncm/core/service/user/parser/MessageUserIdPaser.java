package com.cyberscraft.uep.ncm.core.service.user.parser;

import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.iam.dto.response.UserBasicInfoVO;
import com.cyberscraft.uep.ncm.core.common.domain.MessageUser;
import com.cyberscraft.uep.ncm.core.service.IMessageUserParser;
import com.cyberscraft.uep.ncm.dto.MessageUserOrgInfoVO;
import com.cyberscraft.uep.ncm.enums.MessageIdTypeEnum;
import com.cyberscraft.uep.ncm.enums.MessageUserTypeEnum;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/***
 *
 * @date 2021/8/4
 * <AUTHOR>
 ***/
@Component
public class MessageUserIdPaser extends AbstractMessageUserParser implements IMessageUserParser {

    private static final Logger logger = LoggerFactory.getLogger(MessageUserIdPaser.class);

    @Override
    public Integer getSupportUserType() {
        return MessageUserTypeEnum.USER.getValue();
    }

    @Override
    public List<MessageUser> parserUsers(List<String> userDataIds, Integer targetType, boolean isAuthBind) {
        if (userDataIds == null || userDataIds.isEmpty()) {
            return null;
        }
        List<Long> userIds = new ArrayList<>(userDataIds.size());
        for (String dataId : userDataIds) {
            try {
                Long id = Long.parseLong(dataId);
                userIds.add(id);
            } catch (Exception e) {
            }
        }
        // iam_user id 和钉钉 userId 对应关系的集合
        Map<Long, String> userDataIdsMap = !userIds.isEmpty() ? iamUserClientService.getSnsUserIdsByUserIds(userIds, targetType, isAuthBind) : null;
        Map<String, String> targetDataIdMap = new HashMap<>(userDataIds.size());
        if (userDataIdsMap != null) {
            for (Map.Entry<Long, String> entry : userDataIdsMap.entrySet()) {
                targetDataIdMap.put(String.valueOf(entry.getKey()), entry.getValue());
            }
        }
        //  iam_user id  钉钉 user_id 用户
        return parserUsers(userDataIds, targetDataIdMap, MessageUserTypeEnum.USER);
    }

    @Override
    public List<MessageUser> startParserUsers(List<String> userDataIds, MessageIdTypeEnum idType, Integer targetType, boolean isAuthBind) {
        if (userDataIds == null || userDataIds.isEmpty()) {
            return null;
        }
        if (MessageIdTypeEnum.DINGDING == idType) {
            Map<String, Long> userDataIdsMap = iamUserClientService.getUserIdsMapBySnsUserIds(userDataIds, targetType, isAuthBind);
            Map<String, String> targetDataIdMap = new HashMap<>(userDataIds.size());
            if (userDataIdsMap != null) {
                for (Map.Entry<String, Long> entry : userDataIdsMap.entrySet()) {
                    targetDataIdMap.put(String.valueOf(entry.getValue()), entry.getKey());
                }
            }
            userDataIds.clear();
            userDataIds.addAll(targetDataIdMap.keySet());
            return parserUsers(userDataIds, targetDataIdMap, MessageUserTypeEnum.USER);
        }
        List<Long> userIds = new ArrayList<>(userDataIds.size());
        for (String dataId : userDataIds) {
            try {
                Long id = Long.parseLong(dataId);
                userIds.add(id);
            } catch (Exception e) {
            }
        }
        Map<Long, String> userDataIdsMap = !userIds.isEmpty() ? iamUserClientService.getSnsUserIdsByUserIds(userIds, targetType, isAuthBind) : null;

        Map<String, String> targetDataIdMap = new HashMap<>(userDataIds.size());
        if (userDataIdsMap != null) {
            for (Map.Entry<Long, String> entry : userDataIdsMap.entrySet()) {
                targetDataIdMap.put(String.valueOf(entry.getKey()), entry.getValue());
            }
        }
        return parserUsers(userDataIds, targetDataIdMap, MessageUserTypeEnum.USER);
    }

    @Override
    public List<MessageUser> startParserUsers(List<String> userDataIds, String attr) {
        if (userDataIds == null || userDataIds.isEmpty()) {
            return null;
        }

        List<Long> userIds = new ArrayList<>(userDataIds.size());
        for (String dataId : userDataIds) {
            try {
                Long id = Long.parseLong(dataId);
                userIds.add(id);
            } catch (Exception e) {
            }
        }
        Map<Long, String> userDataIdsMap = !userIds.isEmpty() ? iamUserClientService.getUserThirdIds(userIds, attr) : null;

        Map<String, String> targetDataIdMap = new HashMap<>(userDataIds.size());
        if (userDataIdsMap != null) {
            for (Map.Entry<Long, String> entry : userDataIdsMap.entrySet()) {
                targetDataIdMap.put(String.valueOf(entry.getKey()), entry.getValue());
            }
        }
        return parserUsers(userDataIds, targetDataIdMap, MessageUserTypeEnum.USER);
    }

    @Override
    public Map<String, MessageUserOrgInfoVO> parserUserOrgInfo(List<String> userDataIds) {
        if (userDataIds == null || userDataIds.size() == 0) {
            return Collections.emptyMap();
        }
        List<Long> userIds = userDataIds.stream().map(Long::valueOf).collect(Collectors.toList());
        List<UserBasicInfoVO> userBasicInfoVOList = iamUserClientService.getUserBasicInfosByUserIds(userIds);
        if (userBasicInfoVOList != null && userBasicInfoVOList.size() > 0) {
            if (logger.isDebugEnabled()) {
                logger.debug("userBasicInfoVOList=" + JsonUtil.obj2Str(userBasicInfoVOList));
            }
            Map<String, MessageUserOrgInfoVO> messageUserOrgInfoMap = new HashMap<>(userBasicInfoVOList.size());
            userBasicInfoVOList.forEach(userBasicInfo -> {
                MessageUserOrgInfoVO vo = messageUserTransfer.userBasic2UserOrgInfoVO(userBasicInfo);
                messageUserOrgInfoMap.put(vo.getId(), vo);
            });
            return messageUserOrgInfoMap;
        }
        return Collections.emptyMap();
    }

    @Override
    public boolean isValidUserDataIds(List<String> userDataIds) {
        if (userDataIds == null || userDataIds.size() == 0 || userDataIds.size() > 100) {
            return false;
        }
        for (String dataId : userDataIds) {
            if (!StringUtils.isNumeric(dataId)) {
                return false;
            }
        }
        return true;
    }
}
