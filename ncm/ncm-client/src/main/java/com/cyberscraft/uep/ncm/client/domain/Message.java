package com.cyberscraft.uep.ncm.client.domain;

import com.cyberscraft.uep.ncm.client.domain.message.MessageBody;
import com.cyberscraft.uep.ncm.enums.*;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/***
 * 要发送的通知对像
 * @date 2021/7/8
 * <AUTHOR>
 ***/
public class Message implements Serializable {
    /***
     * 消息业务类型(0,通知类消息）
     */
    private MessageBizTypeEnum bizType;
    /***
     * 消息类型，参考消息类型常量定义
     */
    private MessageTypeEnum type;
//    /***
//     * 消息内容（json体）
//     */
//    private String content;

    /***
     * 消息体
     */
    private MessageBody body;

    /***
     * 发送用户ID
     */
    private String sender;
    /***
     * 消息的接收平台
     */
    private TargetTypeEnum targetType;
    /***
     * 用户类型
     */
    private MessageUserTypeEnum userType;
    /**
     * 接收用户ID 或用户ID集合的类型： 0 表示 iam 的userId  1 表示钉钉的userId
     */
    private MessageIdTypeEnum idType;
    /***
     * 接收的用户ID,或者组ID列表,或用户扩展字段如用户编号
     */
    private List<String> userDataIds;

    private String attr;

    /***
     * 对应的企业配置ID
     */
    private Set<Long> configIds;

    /***
     * 当前对应的租户ID
     */
    private String tenantId;

    /***
     * 当前的发送次数
     */
    private Integer sendTimes = 0;

    /***
     * 用于直接返回对应的任务ID
     */
    private Long id;


    public MessageBizTypeEnum getBizType() {
        return bizType;
    }

    public void setBizType(MessageBizTypeEnum bizType) {
        this.bizType = bizType;
    }

    public MessageTypeEnum getType() {
        return type;
    }

    public void setType(MessageTypeEnum type) {
        this.type = type;
    }
//
//    public String getContent() {
//        return content;
//    }
//
//    public void setContent(String content) {
//        this.content = content;
//    }


    public String getSender() {
        return sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public TargetTypeEnum getTargetType() {
        return targetType;
    }

    public void setTargetType(TargetTypeEnum targetType) {
        this.targetType = targetType;
    }

    public MessageUserTypeEnum getUserType() {
        return userType;
    }

    public void setUserType(MessageUserTypeEnum userType) {
        this.userType = userType;
    }

    public MessageIdTypeEnum getIdType() {
        return idType;
    }

    public void setIdType(MessageIdTypeEnum idType) {
        this.idType = idType;
    }

    public List<String> getUserDataIds() {
        return userDataIds;
    }

    public void setUserDataIds(List<String> userDataIds) {
        this.userDataIds = userDataIds;
    }

    public String getAttr() {
        return attr;
    }

    public void setAttr(String attr) {
        this.attr = attr;
    }

    public Set<Long> getConfigIds() {
        return configIds;
    }

    public void setConfigIds(Set<Long> configIds) {
        this.configIds = configIds;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public MessageBody getBody() {
        return body;
    }

    public void setBody(MessageBody body) {
        this.body = body;
    }

    public Integer getSendTimes() {
        return sendTimes;
    }

    public void setSendTimes(Integer sendTimes) {
        this.sendTimes = sendTimes;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Override
    public String toString() {
        return "Message{" +
                "bizType=" + bizType +
                ", type=" + type +
                ", body=" + body +
                ", sender='" + sender + '\'' +
                ", targetType=" + targetType +
                ", userType=" + userType +
                ", idType=" + idType +
                ", userDataIds=" + userDataIds +
                ", configIds=" + configIds +
                ", tenantId='" + tenantId + '\'' +
                ", sendTimes=" + sendTimes +
                ", id=" + id +
                '}';
    }
}
