package com.cyberscraft.uep.ncm.api;

import com.cyberscraft.uep.common.bean.Result;
import com.cyberscraft.uep.ncm.constants.UrlConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/***
 * NCM基本工具API
 * @date 2021/8/21
 * <AUTHOR>
 ***/
@RestController
@Api(description = "相关工具接口", tags = "Tools")
public interface ToolApi {

    /***
     * 用AES进行加密处理
     * @return
     */
    @PostMapping(value = UrlConstant.TOOL + "/aesEncrypt", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.TEXT_PLAIN_VALUE)
    @ApiOperation(response = String.class, nickname = "aesEncrypt", value = "用AES/CBC模式对请求的body进行加密处理", notes = "请求的body进行加密处理")
    Result<Map<String, String>> aesEncrpty(
            @ApiParam(required = true, name = "appKey", value = "应用AppKey")
            @RequestHeader(value = "appKey", required = true) String appKey,
            @ApiParam(required = true, name = "appSecret", value = "应用appSecret")
            @RequestHeader(value = "appSecret", required = true) String appSecret,
            @ApiParam(required = true, name = "aesKey", value = "AES加密key")
            @RequestHeader(value = "aesKey", required = true) String aesKey,
            @ApiParam(required = true, name = "token", value = "签名所用Token")
            @RequestHeader(value = "token", required = true) String token,

            @ApiParam(required = false, name = "nonce", value = "nonce值")
            @RequestParam(value = "nonce", required = false) String nonce,
            @ApiParam(required = false, name = "timestamp", value = "timestamp值")
            @RequestParam(value = "timestamp", required = false) String timestamp,
            @RequestBody String message
    );


    /***
     * 对传入的业务参数按照appkey,appsecret进行业务签名处理
     * @return
     */
    @PostMapping(value = UrlConstant.TOOL + "/sign", produces = MediaType.APPLICATION_JSON_VALUE, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(response = String.class, nickname = "signParam", value = "MD5对请求的参数进行签名处理", notes = "返回所有的提交到需要签名接口的参数列表健值对")
    Result<Map<String, String>> signParam(
            @ApiParam(required = true, name = "appKey", value = "应用AppKey")
            @RequestHeader(value = "appKey", required = true) String appKey,
            @ApiParam(required = true, name = "appSecret", value = "应用appSecret")
            @RequestHeader(value = "appSecret", required = true) String appSecret,
            @ApiParam(required = false, name = "nonce", value = "nonce值")
            @RequestParam(value = "nonce", required = false) String nonce,
            @ApiParam(required = false, name = "timestamp", value = "timestamp值")
            @RequestParam(value = "timestamp", required = false) String timestamp,
            @RequestBody Map<String, Object> bizParameter
    );
}
