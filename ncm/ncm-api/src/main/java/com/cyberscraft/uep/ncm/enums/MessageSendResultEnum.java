package com.cyberscraft.uep.ncm.enums;

/***
 *
 * @date 2021/8/3
 * <AUTHOR>
 ***/
public enum MessageSendResultEnum implements IBaseEnum {
    /***
     * 发送中
     */
    SENDING(0,"发送中"),
    /***
     * 已成功
     */
    SUCCESS(1,"成功"),
    /***
     * 无效用户
     */
    INVALID(2,"无效用户"),
    /***
     * 禁止发送
     */
    FORBIDDEN(3,"禁止发送"),
    /***
     * 失败
     */
    FAILURE(4,"失败");

    private int value;
    /**
     * 描述
     */
    private String desc;

    MessageSendResultEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getDesc() {
        return desc;
    }

    public static MessageSendResultEnum of(String enumName) {
        for (MessageSendResultEnum result : values()) {
            if (result.name().equals(enumName)) {
                return result;
            }
        }
        return null;
    }

    public static MessageSendResultEnum valueOfStatus(Integer status) {
        for (MessageSendResultEnum result : values()) {
            if (result.getValue() == status) {
                return result;
            }
        }
        return null;
    }

    public static MessageSendResultEnum valueOfSendStatus(MessageSendStatusEnum sendStatusEnum) {
        switch (sendStatusEnum){
            case UNSTART:
            case SENDING:
                return SENDING;
            case FINISHED:
                return SUCCESS;
            case FAILURED:
                return FAILURE;
        }
        return FAILURE;
    }

}
