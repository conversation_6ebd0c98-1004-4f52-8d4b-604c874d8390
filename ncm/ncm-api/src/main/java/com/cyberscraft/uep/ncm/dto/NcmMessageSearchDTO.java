package com.cyberscraft.uep.ncm.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * Created by Intellij IDEA.
 *
 * @Author: linyuxb
 * @Date: 2025/3/28 18:47
 * @Desc: 获取消息发送日志列表 请求对象
 */
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class NcmMessageSearchDTO implements Serializable {

    private static final long serialVersionUID = -1L;

    @ApiModelProperty(value = "消息类型", required = true)
    private Integer bizType;
    @ApiModelProperty(value = "模糊搜索用户名称")
    private String userName;
    @ApiModelProperty(value = "全值匹配邮箱 ")
    private String userEmail;
    @ApiModelProperty(value = "全值匹配手机号")
    private String userPhone;

    @ApiModelProperty(value = "模版类型")
    private String templateCode;
    @ApiModelProperty(value = "开始时间")
    private  Long startTime;
    @ApiModelProperty(value = "结束日期")
    private Long endTime;
    /**
     * 发送状态：0发送中、1已成功、2无效用户、3禁止发送、4失败
     */
    @ApiModelProperty(value = "发送状态")
    private Integer sendStatus;

    @ApiModelProperty(value = "要获取的结果页号")
    private Integer page;
    @ApiModelProperty(value = "获取的每一页的记录数")
    private Integer size;

    public Integer getBizType() {
        return bizType;
    }

    public void setBizType(Integer bizType) {
        this.bizType = bizType;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail;
    }

    public String getUserPhone() {
        return userPhone;
    }

    public void setUserPhone(String userPhone) {
        this.userPhone = userPhone;
    }

    public String getTemplateCode() {
        return templateCode;
    }

    public void setTemplateCode(String templateCode) {
        this.templateCode = templateCode;
    }

    public Long getStartTime() {
        return startTime;
    }

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public Long getEndTime() {
        return endTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    public Integer getSendStatus() {
        return sendStatus;
    }

    public void setSendStatus(Integer sendStatus) {
        this.sendStatus = sendStatus;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }
}
