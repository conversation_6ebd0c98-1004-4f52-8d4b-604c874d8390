package com.cyberscraft.uep.ncm.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import java.io.Serializable;
import java.time.LocalDateTime;

/***
 * 消息进度视图对像，用于查询消息进度，如果结果为FINISHED，则代表消息发送完成，消息发送完成后，可能通过查询消息详情，获取发送结果信息。
 * @date 2021/8/10
 * <AUTHOR>
 ***/
@JsonNaming(value = PropertyNamingStrategy.class)
public class MessageSendProgressVO implements Serializable {

    private static final long serialVersionUID = -837680056420157744L;
    /***
     * 消息发送状态
     */
    private Integer status;

    /***
     * 消息ID
     */
    private String id;


    /***
     * 发送时间
     */
    private LocalDateTime sendTime;

    /***
     * 发送完成时间
     */
    private LocalDateTime finishTime;

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public MessageSendProgressVO() {
    }

    public MessageSendProgressVO(String id, Integer status) {
        this.status = status;
        this.id = id;
    }

    public LocalDateTime getSendTime() {
        return sendTime;
    }

    public void setSendTime(LocalDateTime sendTime) {
        this.sendTime = sendTime;
    }

    public LocalDateTime getFinishTime() {
        return finishTime;
    }

    public void setFinishTime(LocalDateTime finishTime) {
        this.finishTime = finishTime;
    }
}
