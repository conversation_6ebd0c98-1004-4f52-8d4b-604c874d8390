package com.cyberscraft.uep.ncm.dto;

import java.io.Serializable;
import java.time.LocalDateTime;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

/***
 * 已发送通知消息详情视图对像
 * @date 2021/7/8
 * <AUTHOR>
 ***/
@JsonNaming(value = PropertyNamingStrategy.class)
public class SendingMessageVO extends SendingMessageListVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 发送失败错误信息
     */
    private String errorMsg;
    /**
     * 下次的发送时间
     */
    private LocalDateTime nextSendTime;

    /**
     * 消息关联的媒体文件
     */
    private MediaVO media;

    private Long sender;

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public LocalDateTime getNextSendTime() {
        return nextSendTime;
    }

    public void setNextSendTime(LocalDateTime nextSendTime) {
        this.nextSendTime = nextSendTime;
    }

    public MediaVO getMedia() {
        return media;
    }

    public void setMedia(MediaVO media) {
        this.media = media;
    }

    @Override
    public Long getSender() {
        return sender;
    }

    @Override
    public void setSender(Long sender) {
        this.sender = sender;
    }

    @Override
    public String toString() {
        return "SendingMessageVO{" +
                "errorMsg='" + errorMsg + '\'' +
                ", nextSendTime=" + nextSendTime +
                ", media=" + media +
                ", sender=" + sender +
                '}';
    }
}
