package com.cyberscraft.uep.ncm.enums;

/***
 * 短信发送目标定义
 * @date 2021/7/14
 * <AUTHOR>
 ***/
public enum SmsTargetTypeEnum implements IBaseEnum {
    /***
     * 发送对象为手机号
     */
    MOBILE(0),
    /***
     * 发送对象为用户id
     */
    USERID(1),
    ;

    private int value;

    SmsTargetTypeEnum(int value) {
        this.value = value;
    }

    @Override
    public final int getValue() {
        return this.value;
    }

    @Override
    public String toString() {
        return this.name();
    }
}
