<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8"/>
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=2.0, user-scalable=no"/>
    <meta name="format-detection" content="telephone=no"/>
    <link rel="shortcut icon" href="/login/images/favicon.ico" type="image/x-icon">
    <title id="platform_title" data-th-text="${uc_name}"></title>
    <style>
        body {
            width:100%;
            height:100%;
        }
        body * {
            outline: none;
            font-family: "Microsoft Yahei","微软雅黑",Tahoma,Arial,Helvetica,STHeiti;
        }
        .navbar-top {
            width: 100%;
            height: 60px;
            line-height: 60px;
            background: #1f293d;
            color: #FFFFFF;
            font-size: 20px;
            position: fixed;
            top: 0;
            z-index: 100;
        }
        .error-description {
            font-size: 15px;
            width: 300px;
            word-wrap: break-word;
            margin: 0 auto;
        }
        .iconDefault  {
            width: 24px;
            height: 24px;
            display: inline-block;
        }
        .pathNodeIcon {
            position: relative;
            top: 5px;
            margin-right: 5px;
        }
        .icon-favicon-white {
            background: url("/login/images/icon-favicon-white.png") no-repeat;
        }
        .error-icon {
            position: fixed;
            top: 50%;
            width: 800px;
            height: 200px;
            margin-left: -400px;
            margin-top: -100px;
            text-align: center;
            color: rgba(0, 0, 0, 0.65);
        }
        .left{
            position: fixed;
            left: 25%;
            top: 40%;
        }
        .right{
            position: fixed;
            left: 55%;
            top: 40%;
        }
        .error-msg {
            font-size: 26px;
            margin-top: 20px;
        }
        .loginTitle {
            width: 100%;
            text-align: center;
            margin-bottom: 1em;
        }
        .btn {
            border-radius: 5px;
            background: #1890ff;
            color: #fff;
            background-color: #1890ff;
            border-color: #1890ff;
            text-shadow: 0 -1px 0 rgba(0,0,0,.12);
            box-shadow: 0 2px 0 rgba(0,0,0,.045);
            width: 100px;
            height: 30px;
        }
        .title {
            margin-bottom: 16px;
            color: rgba(0,0,0,.45);
            font-size: 20px;
            line-height: 28px;
        }
    </style>
</head>
<body style="margin: 0;background: #f5f5f5;">
<header class="navbar-top">
        <span>
            <i class="iconDefault pathNodeIcon icon-favicon-white"></i>
            <span id="uc_console_name" data-th-utext="${uc_name}?: _">
            </span>
        </span>
</header>
<div class="error-icon">
    <div class="left">
        <img src="/uc/images/50x.svg"/>
    </div>
    <div class="right">
        <div class="error-msg loginTitle">
            <h1>500</h1>
            <div class="title">抱歉，服务器出错了</div>
        </div>

        <div class="error-description">
            <div id="error_message" data-th-text="${message}"></div>
            <a href="/">
                <button class="btn">
                    <span>回到首页</span>
                </button>
            </a>
        </div>
    </div>
</div>
</body>
</html>