#!/bin/bash
set -e
#===============================================================================
#
#          FILE: install.sh
#
#
#   DESCRIPTION:  install script will put all uemx service config file 
#                 into consul kv.
#                 Consul command: consul kv put {config path} @{file path}
#                 e.x:
#                 consul kv put config/mtenant-service/data @config/mtenant-service/data
#
#       OPTIONS:  ---
#  REQUIREMENTS:  ---
#          BUGS:  ---
#         NOTES:  ---
#        AUTHOR:  Liu Yang
#       VERSION:  0.1
#       CREATED:  6/28/2019 04:57:21 PM CST
#      REVISION:  ---
#===============================================================================#
SCRIPT=$(readlink -f $0)
SCRIPTPATH=$(dirname ${SCRIPT})
CONFIG_TPL_PATH=${SCRIPTPATH}/config
CONFIGPATH=${SCRIPTPATH}/config_value

VERSION_CONFIG_KEY="config/version"
VERSION_CHECK_RTN=${SCRIPTPATH}/latest_result_num
VERSION_FILE=${SCRIPTPATH}/config/version

function usage() {
    echo "USAGE: ${SCRIPT} [-h]"
    echo -e "\t -h to show usage"
    #echo -e "\t -p Specify the consul-kv profile"
    exit 0
}

function version_check() {
    consul kv get -keys "${VERSION_CONFIG_KEY}" > ${VERSION_CHECK_RTN}
    version_key=$(cat ${VERSION_CHECK_RTN})
    if [ -n "${version_key}" ]; then
        consul kv get "${VERSION_CONFIG_KEY}" > ${VERSION_CHECK_RTN}
    fi
    version_as=$(cat ${VERSION_CHECK_RTN})
    version_to=$(cat ${VERSION_FILE})
    version_to=$((version_to))
    version_as=$((${version_as:-0}))

    echo "as-is: ${version_as} to-be: ${version_to}"
    if [[ ${version_to} -gt ${version_as} ]]; then
        prepare_config_files
    else
        echo "Do not need to upgrade consul-kv.Exit"
        exit 0
    fi
}

function prepare_config_files() {
  if [ -d "${CONFIGPATH}" ]; then
    [ "${CLEAN_OLD_CONFIG}" == "true" ] && rm -rf "${CONFIGPATH}"
  fi
  if [ ! -d "${CONFIGPATH}" ]; then
    echo "Generate config_value from EVN"
    if [ -d "${CONFIG_TPL_PATH}" ]; then
      for tpl in `ls ${CONFIG_TPL_PATH}`; do
        if [ -d ${CONFIG_TPL_PATH}/${tpl} ]; then
          echo "handle ${tpl}"
          mkdir -p ${CONFIGPATH}/${tpl}
          touch ${CONFIGPATH}/${tpl}/data
          dockerize --template ${CONFIG_TPL_PATH}/${tpl}/data.tpl:${CONFIGPATH}/${tpl}/data
        fi
      done
    fi
  fi
}

function put_configs() {
    if [ -d "${CONFIGPATH}" ]; then
        for service in `ls ${CONFIGPATH}`; do 
            if [ -d ${CONFIGPATH}/${service} ]; then
                consul kv put "config/${service}/data" "@${CONFIGPATH}/${service}/data" 
            fi
        done
    else
        echo "ignore update consul-kv"
        exit 1
    fi
    #consul kv put "config/application-$PROFILE/data"
    #consul kv put "config/backend-service-$PROFILE/data"
    consul kv put "config/version" "@${CONFIG_TPL_PATH}/version"

}

function main() {
    sleep $((${WAIT_SECOND:-0}))
    version_check
    put_configs
}

#arg parser.
CLEAN_OLD_CONFIG=false
while getopts 'hcs:' OPT; do
  case $OPT in
    h)
      usage
      ;;
    c)
      CLEAN_OLD_CONFIG=true
      ;;
    s)
      WAIT_SECOND="${OPTARG}"
      ;;
#    p)
#      PROFILE="${OPTARG}"
#      ;;
    ?)
      usage
  esac
done

shift $(($OPTIND - 1))

main "$@"
