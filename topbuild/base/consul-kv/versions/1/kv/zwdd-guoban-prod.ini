[MYSQL]
DB_ADDR = rm-vy26s4s489v055rk2.mysql.rds.cloud-inner.zj.gov.cn
DB_PORT = 3306
DB_PRIMARY = mdm
DB_USERNAME = mdm
DB_PWD = 14tQmivGLzBIT0yR

[REDIS]
REDIS_IP = r-vy28d3c9b446b8c4.redis.rds.cloud-inner.zj.gov.cn
REDIS_PORT = 6379
REDIS_DB = 2
REDIS_PWD = ProRedis1234

[MQ]
MQ_IP = ************
MQ_PORT = 8080
MQ_ACK = jLQ9HNVkbZJpJzKl
MQ_SCK = JtnO66OWy31BS14tQmivGLzBIT0xyR
MQ_CONSUME_NUMS = 20
MQ_INSTANCE_NAME = MQ_inst_cluster1

[MOZI-Platform]
DING_DOMAIN_NAME = openplatform-gbd.ding.zj.gov.cn
DING_ACCESS_KEY = DingGuard_xiong-p2Do102tUfaaYb
DING_SECRET_KEY = meNJ0y3K9Ex4a3FdHH72p3YzW67m0jSsRY0x4882
DING_EVENT_CALLBACK = http://dingguard-slb/mdm/basic/event

[OSS]
OSS_ENDPOINT = oss-cn-hangzhou-zjzwy01-d01-a.cloud-inner.zj.gov.cn/gbdd-oss-1t
OSS_BUCKET_URL = https://work-gbd.ding.zj.gov.cn
OSS_BUCKET_NAME = gbdd-oss-1t
OSS_ACK = b94I25ReLZZ8DYOt
OSS_SCK = vEk0eC8U5YRC7MYXkpD0bMjgYIcAwW

[FILE-SERVER]
FILE_SERVER_URL = https://short-gbd.ding.zj.gov.cn
FILE_SERVER_URL_OUTTER = https://short-gbd.ding.zj.gov.cn
FILE_SERVER_BIZ_TYPE = ZWDD

[UEMX]
UEM_IP = guard-gbd.ding.zj.gov.cn