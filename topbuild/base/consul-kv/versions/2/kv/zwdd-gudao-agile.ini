[MYSQL]
DB_ADDR = rm-0vnjm4p0trk604w87.mysql.rds.ops.env45.com
DB_PORT = 3306
DB_PRIMARY = uemx
DB_USERNAME = zwdd_rds_at_1
DB_PWD = ZWDDzwdd666

[REDIS]
REDIS_IP = r-0vn23a23a60d52b4.redis.rds.ops.env45.com
REDIS_PORT = 6379
REDIS_DB = 2
REDIS_PWD = ZWDDredis666

[MQ]
MQ_IP = http://mq.namesrv.cloud.env45.com
MQ_PORT = 9876
MQ_ACK = 2Qek5naiGw95Xe8d
MQ_SCK = lCx673ge0cQmSkxPHMWrVyNMScmGf5
MQ_CONSUME_NUMS = 20
MQ_INSTANCE_NAME = zwddTest

[MOZI-Platform]
DING_DOMAIN_NAME = openplatform-svc
DING_ACCESS_KEY = DingGuard_xiong-90YiTodzuy2yMf
DING_SECRET_KEY = 6e2oqEkHKs54dS85Y3E7mbiPK51Xf50B5Rfo2lz3
DING_EVENT_CALLBACK = http://dingguard-slb.zwdd.svc.cluster.local/mdm/basic/event
DING_APP = DingGuard_xiong

[OSS]
OSS_ENDPOINT = oss-cn-neimeng-env45-d01-a.cloud.env45.com/zwdd-test1
OSS_BUCKET_URL = https://zwdd-test1.oss-cn-neimeng-env45-d01-a.cloud.env45.com
OSS_BUCKET_NAME = zwdd-test1
OSS_ACK = 2Qek5naiGw95Xe8d
OSS_SCK = lCx673ge0cQmSkxPHMWrVyNMScmGf5
OSS_BUCKET_ISCONTAIN_BUCKETNAME = 0

[FILE-SERVER]
FILE_SERVER_URL = http://file.zwdd.svc
FILE_SERVER_URL_OUTTER = https://zwding-daily-file.alibaba-inc.com
FILE_SERVER_BIZ_TYPE = ZWDD

[SCEP]
SCEP_ENABLED = true
SCEP_PAYLOAD_IDENTIFIER = com.govdd.profile.scep1
SCEP_CLIENT_CERT_ORG = GOVDD
SCEP_CLIENT_CERT_CN = clientCert
SCEP_CLIENT_CERT_ISSUER_CN = GOVDD_ISSUER

[MDM_PROFILE]
MDM_PROFILE_PAYLOAD_DISPLAY_NAME = GOVDD MDM Profile
MDM_PROFILE_PAYLOAD_ORG = GOVDD
MDM_PROFILE_PAYLOAD_DESC = MDM Profile Issued By GOVDD
MDM_PROFILE_PAYLOAD_IDENTIFIER = com.govdd.mdm.profile
MDM_PROFILE_TOPIC = com.apple.mgmt.External.f0960e8b-c23f-4f89-926b-31075243187b

[UEMX]
UEM_IP = zwding-guard.alibaba-inc.com

[ADM]
ADM_DOWNLOAD_URL = https://zwding-guard.alibaba-inc.com/d

[MDM]
ASYNC_DEVICE_ACTIVATE_ENABLED=true

[MDM]
ASYNC_DEVICE_ACTIVATE_ENABLED=true