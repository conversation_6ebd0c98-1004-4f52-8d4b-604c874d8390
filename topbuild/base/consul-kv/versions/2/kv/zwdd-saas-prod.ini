[MYSQL]
DB_ADDR = rm-9dp0256by8cb8g10g.mysql.rds.aliyuncs.com
DB_PORT = 3306
DB_PRIMARY = uemx
DB_USERNAME = uemx
DB_PWD = #GzeCLCCrNXR&nKj

[REDIS]
REDIS_IP = r-9dpqvwj6e6fulh8oqo.redis.rds.aliyuncs.com
REDIS_PORT = 6379
REDIS_DB = 3
REDIS_PWD = cwBG2NJZgRWjtTxx

[MQ]
MQ_IP = http://MQ_INST_1415171625838692_Bb5znlZs.cn-north-2-gov-1.mq-internal.aliyuncs.com
MQ_PORT = 8080
MQ_ACK = LTAI4FhC3oeFRfpznpCLokbB
MQ_SCK = ******************************
MQ_CONSUME_NUMS = 20
MQ_INSTANCE_NAME = MQ_INST_1415171625838692_Bb5znlZs

[MOZI-Platform]
DING_APP = DingGuard
DING_DOMAIN_NAME = openplatform-svc
DING_ACCESS_KEY = DingGuard_xiong-w89SukyEymIv2i
DING_SECRET_KEY = FvRE88uZn7V2vw5TowEWUiU3mfjC1aDV46dGD1y7
DING_EVENT_CALLBACK = http://dingguard-slb-svc.zwdd-saas.svc.cluster.local/mdm/basic/event

[OSS]
OSS_ENDPOINT = oss-cn-north-2-gov-1-internal.aliyuncs.com
OSS_BUCKET_URL = https://zwdd-saas-dingguard.oss-cn-north-2-gov-1.aliyuncs.com
OSS_BUCKET_NAME = zwdd-saas-dingguard
OSS_ACK = LTAI4FhC3oeFRfpznpCLokbB
OSS_SCK = ******************************
OSS_BUCKET_ISCONTAIN_BUCKETNAME = 0

[FILE-SERVER]
FILE_SERVER_URL = http://file
FILE_SERVER_URL_OUTTER = http://short.gov.alibaba-inc.com
FILE_SERVER_BIZ_TYPE = ZWDD

[SCEP]
SCEP_ENABLED = true
SCEP_PAYLOAD_IDENTIFIER = com.govdd.profile.scep1
SCEP_CLIENT_CERT_ORG = GOVDD
SCEP_CLIENT_CERT_CN = clientCert
SCEP_CLIENT_CERT_ISSUER_CN = GOVDD_ISSUER

[MDM_PROFILE]
MDM_PROFILE_PAYLOAD_DISPLAY_NAME = GOVDD MDM Profile
MDM_PROFILE_PAYLOAD_ORG = GOVDD
MDM_PROFILE_PAYLOAD_DESC = MDM Profile Issued By GOVDD
MDM_PROFILE_PAYLOAD_IDENTIFIER = com.govdd.mdm.profile
MDM_PROFILE_TOPIC = com.apple.mgmt.External.f0960e8b-c23f-4f89-926b-31075243187b

[UEMX]
UEM_IP = dingguard.dg-work.cn
SAG_IP = dingguard.dg-work.cn

[ADM]
ADM_DOWNLOAD_URL = https://dingguard.dg-work.cn/d

[MDM]
ASYNC_DEVICE_ACTIVATE_ENABLED=true

[AHAS]
AHAS_DISABLE=true