[MYSQL]
DB_ADDR = **************
DB_PORT = 3306
DB_PRIMARY = uemx
DB_USERNAME = mdm
DB_PWD = Cyb2021@dgsee

[REDIS]
REDIS_IP = **************
REDIS_PORT = 6379
REDIS_DB = 2
REDIS_PWD =

[MQ]
MQ_IP = **************
MQ_PORT = 9876
MQ_ACK = admin
MQ_SCK = admin
MQ_CONSUME_NUMS = 20
MQ_INSTANCE_NAME = zwdd-test-MQ

[MOZI-Platform]
DING_DOMAIN_NAME = gudao-openplatform-daily
DING_ACCESS_KEY = DingGuard_xiong-90YiTodzuy2yMf
DING_SECRET_KEY = 6e2oqEkHKs54dS85Y3E7mbiPK51Xf50B5Rfo2lz3
DING_EVENT_CALLBACK = http://dingguard-slb.dingguard-test.svc.cluster.local/mdm/basic/event

[OSS]
OSS_ENDPOINT = oss-cn-neimeng-env10-d01-a.ops.env10.shuguang.com/bucket-381-test
OSS_BUCKET_URL = https://oss-cn-neimeng-env10-d01-a.inter.env10.shuguang.com
OSS_BUCKET_NAME = bucket-381-test
OSS_ACK = aqrjjVPvkzw3xuOi
OSS_SCK = RfvtTRAXHh0gTzhtv9RGXrgklcNBvd
OSS_BUCKET_ISCONTAIN_BUCKETNAME = 0

[FILE-SERVER]
FILE_SERVER_URL = http://file.im-v381.svc
FILE_SERVER_URL_OUTTER = https://zwding-daily-file.alibaba-inc.com
FILE_SERVER_BIZ_TYPE = ZWDD

[SCEP]
SCEP_ENABLED = true
SCEP_PAYLOAD_IDENTIFIER = com.govdd.profile.scep1
SCEP_CLIENT_CERT_ORG = GOVDD
SCEP_CLIENT_CERT_CN = clientCert
SCEP_CLIENT_CERT_ISSUER_CN = GOVDD_ISSUER

[MDM_PROFILE]
MDM_PROFILE_PAYLOAD_DISPLAY_NAME = GOVDD MDM Profile
MDM_PROFILE_PAYLOAD_ORG = GOVDD
MDM_PROFILE_PAYLOAD_DESC = MDM Profile Issued By GOVDD
MDM_PROFILE_PAYLOAD_IDENTIFIER = com.govdd.mdm.profile
MDM_PROFILE_TOPIC = com.apple.mgmt.External.4118dbc1-ea76-42cb-bd3f-d1a6b331c144

[UEMX]
UEM_IP = test-guard.digitalsee.cn

[ADM]
ADM_DOWNLOAD_URL = https://test-guard.digitalsee.cn/d

[MDM]
ASYNC_DEVICE_ACTIVATE_ENABLED=true

[AHAS]
AHAS_DISABLE=true
