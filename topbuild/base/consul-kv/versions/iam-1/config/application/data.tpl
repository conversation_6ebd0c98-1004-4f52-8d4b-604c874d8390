#service config
iam_service_name=iam-service
iam_notify_service_name=iam-notify-service
gateway_service_name=gateway-service
open_platform_service_name=open-platform-service
adm_service_name=adm-service
ncm_service_name=ncm-service

#服务发现时，使用的标签，默认为空
spring.cloud.consul.discovery.tags=

#swagger 相关配置
swagger.enable=true

#zipkin 是否开启
spring.zipkin.enabled=false
spring.zipkin.base-url=http://{{ .Env.ZIPKIN_ADDR }}:9001/
spring.zipkin.sender.type=web
spring.sleuth.sampler.probability=1

#是否开启监控中心
spring.boot.admin.client.enabled=false
spring.boot.admin.client.url=http://{{ .Env.ADMIN_ADDR }}:9002
#management.endpoints.web.exposure.include=*
#management.endpoint.health.show-details=always

#是否开启JMS相关监控
#management.endpoint.jolokia.enabled=false

更改feign.client配置
feign.client.default-config=default
feign.client.config.default.connect-timeout=3000
feign.client.config.default.read-timeout=10000
#如果是测试，可以设置成FULL
feign.client.config.default.logger-level=NONE

#dds config
dds.general.defaultDataBase=digitalsee
dds.general.multiTenant=true
dds.general.defaultSchema={{ .Env.DB_PRIMARY }}
dds.general.headerSeparator=.
dds.general.tenantHeaderName=tcode
dds.general.filterUrls=/dds-sample/actuator/health,/error,/actuator,/health
dds.general.filterUrlsSeparator=;
dds.general.activeDataBase=digitalsee
dds.general.enableGlobalInterceptor=false
dds.general.shardingDb=false

dds.database.digitalsee.jdbcUrl=jdbc:mysql://{{ .Env.DB_ADDR }}:3306
dds.database.digitalsee.username={{ .Env.DB_USERNAME }}
dds.database.digitalsee.password={{ .Env.DB_PWD }}
dds.database.digitalsee.driverClassName=com.mysql.cj.jdbc.Driver
dds.database.digitalsee.urlParams=useSSL=false&useUnicode=true&characterEncoding=utf-8&serverTimezone=CTT&allowMultiQueries=true

#指定连接维护的最小空闲连接数，当使用HikariCP时指定.
dds.database.digitalsee.minimum-idle=100
#spring最大的连接池数量
dds.database.digitalsee.maximum-pool-size=100

#Redis相关配置
system.redis.host={{ .Env.REDIS_IP }}
system.redis.port={{ .Env.REDIS_PORT }}
system.redis.password={{ .Env.REDIS_PWD }}
system.redis.database={{ .Env.REDIS_DB }}
redis.pool.maxIdle=100
redis.pool.maxActive=100
redis.pool.maxWait=10000
redis.pool.testOnBorrow=false

spring.redis.host=${system.redis.host}
spring.redis.port=${system.redis.port}
spring.redis.database=${system.redis.database}
#spring.redis.password=${system.redis.password}
spring.redis.jedis.pool.max-wait=${redis.pool.maxWait}
spring.redis.jedis.pool.max-active=${redis.pool.maxActive}
spring.redis.jedis.pool.max-idle=${redis.pool.maxIdle}
spring.redis.jedis.pool.min-idle=${redis.pool.maxIdle}
spring.redis.jedis.pool.test-on-borrow=${redis.pool.testOnBorrow}

sysconf.mq.type=rocketmq
#rocketmq config
sysconf.rocketmq.namesrv={{ .Env.MQ_IP }}:{{ .Env.MQ_PORT }}
sysconf.rocketmq.accesskey={{ .Env.MQ_ACK }}
sysconf.rocketmq.secretkey={{ .Env.MQ_SCK }}
sysconf.rocketmq.consumeNums={{ .Env.MQ_CONSUME_NUMS }}
sysconf.rocketmq.consumerId=GID_uep_
sysconf.rocketmq.producerId=GID_sec_producer
sysconf.rocketmq.instanceName={{ .Env.MQ_INSTANCE_NAME }}

sys.server.uep.outterUrl={{ .Env.UEP_OUTTER_URL }}
sys.server.uep.innerUrl={{ .Env.UEP_IP }}
sys.server.uep.openPlatformUri=/open/login/oauth2/code/iam
sys.server.uep.gatewayPrefix={{ Env.GW_PREFIX }}
sys.server.uep.gatewayUri=${sys.server.uep.gatewayPrefix}/login/oauth2/code/gateway
sys.server.uep.admUri=/adm
sys.server.uep.singleTenant=false
sys.server.uep.singleTenantId=