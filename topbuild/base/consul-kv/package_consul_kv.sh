#!/bin/bash
set -e

SCRIPT=$(readlink -f $0)
SCRIPTPATH=$(dirname ${SCRIPT})

BUILD_DIR="${SCRIPTPATH}/../target/consul_kv"
CONSUL_KV_BASE_DIR="${SCRIPTPATH}/versions"
KV_FILE_SUFFIX="ini"
BUILD_CONIG_DIR="${BUILD_DIR}/config"
BUILD_CONIG_VALUE_DIR="${BUILD_DIR}/config_value"

options=()
sections=()

function read_ini_file(){
  kv_file="${1}.${KV_FILE_SUFFIX}"
  echo "${kv_file}"
  all_sections=$(awk -F '[][]' '/\[.*]/{print $2}' ${kv_file})
  sections=(${all_sections// /})
  if [ ${#sections[@]} -gt 0 ]; then
    for sec in "${sections[@]}"; do
      echo "Section: ${sec}"
      section_opts=$(awk "/\[${sec}\]/{a=1}a==1" ${kv_file} | sed -e'1d' -e '/^$/d' -e 's/[ \t]*$//g' -e 's/^[ \t]*//g' -e 's/[ ]/@G@/g' -e '/\[/,$d')
      section_opts_array=(${section_opts})
      for opt in "${section_opts_array[@]}"; do
        if [ -n "${opt}" ] || [ "${opt}" opt != "@G@" ]; then
          options[${#options[@]}]=${opt//@G@/ }
          echo "${opt//@G@/ }"
        fi
      done
      echo "-------------"
    done
    OLD_IFS=$IFS
    IFS="="
    for _opt in "${options[@]}"; do
      kv_array=(${_opt})
      key=`echo ${kv_array[0]}|sed -e 's/[ \t]*$//g' -e 's/^[ \t]*//g'`
      value=`echo ${kv_array[1]}|sed -e 's/[ \t]*$//g' -e 's/^[ \t]*//g'`
      #echo "export ${key}=${value}"
      export "${key}"="${value}"
    done
    IFS=$OLD_IFS
  fi
}

function dockerize_consul_kv() {
  if [ -d "${BUILD_CONIG_DIR}" ]; then
    for tpl in `ls ${BUILD_CONIG_DIR}`; do
      if [ -d ${BUILD_CONIG_DIR}/${tpl} ]; then
        echo "handle ${tpl}"
        mkdir -p ${BUILD_CONIG_VALUE_DIR}/${tpl}
        touch ${BUILD_CONIG_VALUE_DIR}/${tpl}/data
        dockerize --template ${BUILD_CONIG_DIR}/${tpl}/data.tpl:${BUILD_CONIG_VALUE_DIR}/${tpl}/data
      fi
    done
  fi
}

function check_version_file() {
  version_dir=${1}
  if [ ! -d ${CONSUL_KV_BASE_DIR}/${version_dir}/config ];then
    echo "Specified consul-kv version not exist"
    exit 1
  fi
}

function check_consul_files() {
  version_dir=${1}
  kv_file=${2}
  if [ -d ${CONSUL_KV_BASE_DIR}/${version_dir}/config ]; then
    if [[ -n "${kv_file}" ]]; then
      if [ ! -f "${CONSUL_KV_BASE_DIR}/${version_dir}/kv/${kv_file}.${KV_FILE_SUFFIX}" ]; then
        echo "Specified kv file: ${CONSUL_KV_BASE_DIR}/${version_dir}/kv/${kv_file}."${KV_FILE_SUFFIX}" not exist!"
        exit 1
      fi
    fi
  fi
}

function set_env() {
  export DB_ADDR=${DB_ADDR:-127.0.0.1}
  export DB_PORT=${DB_PORT:-3306}
  export DB_PRIMARY=${DB_PRIMARY:-iam}
  export DB_USERNAME=${DB_USERNAME:-uep}
  export DB_PWD=${DB_PWD:-Cyb2021@dgsee}
  export REDIS_IP=${REDIS_IP:-127.0.0.1}
  export REDIS_PORT=${REDIS_PORT:-6379}
  export REDIS_DB=${REDIS_DB:-1}
  export REDIS_PWD=${REDIS_PWD}
  export MQ_IP=${MQ_IP:-127.0.0.1}
  export MQ_PORT=${MQ_PORT:-9876}
  export MQ_ACK=${MQ_ACK:-admin}
  export MQ_SCK=${MQ_SCK:-admin}
  export MQ_CONSUME_NUMS=${MQ_CONSUME_NUMS}
  export MQ_INSTANCE_NAME=${MQ_INSTANCE_NAME}
  export UEP_OUTTER_URL=${UEP_OUTTER_URL:-http://internal-uc.cyb-sky.net/dev}
  export OPEN_PLATFORM_OUTTER_URL=${OPEN_PLATFORM_OUTTER_URL:-http://127.0.0.1/open}
}

function main() {
  if [ -d ${BUILD_DIR} ]; then
      builder::clean
  fi
  mkdir -p "${BUILD_DIR}"

  # determine package consul_kv version
  version_num=$(package::max_kv_version "${VERSION}")
  [ "${KV_NAME}" == "-" ] && KV_NAME=""

  # check input parameters
  if [ -z "${KV_NAME}" ]; then
    # not using kv file
    check_version_file ${version_num}
  else
    check_consul_files ${version_num} ${KV_NAME}
  fi

  echo "prepare package consul_kv: ${CONSUL_KV_BASE_DIR}/${version_num}/config"
  cp -rf "${CONSUL_KV_BASE_DIR}/${version_num}/config" "${BUILD_DIR}"
  if [ -n "${KV_NAME}" ]; then
    echo "with kv: ${KV_NAME}"
    read_ini_file "${CONSUL_KV_BASE_DIR}/${version_dir}/kv/${kv_file}"
  else
    echo "with env variables"
    set_env
  fi
  dockerize_consul_kv

  cp -rf ${SCRIPTPATH}/install.sh ${BUILD_DIR}
  build::archive_source "consul-kv-${version_num}-${KV_NAME:-consul}"
}

function usage() {
    echo "USAGE: ${SCRIPT} [-h] [-b]"
    echo -e "\t -h to show usage"
    echo -e "\t -k to specify which kv file end with .ini should be used"
    echo -e "\t -v to specify consul-kv version. Not specify this option or specify -, should use latest verion"
    exit 0
}

KV_NAME=""
. "${SCRIPTPATH}/../../util/util.sh"
while getopts 'hk:v:-:' OPT; do
  case $OPT in
  h)
    usage
  ;;
  k)
    KV_NAME=${OPTARG}
  ;;
  v)
    VERSION=${OPTARG}
  ;;
  -)
    LONG_OPTARG=""
    LONG_OPTARG="${OPTARG#*=}"
    case $OPTARG in
      db_ip=?*)
      DB_ADDR=${LONG_OPTARG}
      ;;
      db_port=?*)
      DB_PORT=${LONG_OPTARG}
      ;;
      db_schema=?*)
      DB_PRIMARY=${LONG_OPTARG}
      ;;
      db_username=?*)
      DB_USERNAME=${LONG_OPTARG}
      ;;
      db_pwd=?*)
      DB_PWD=${LONG_OPTARG}
      ;;
      redis_ip=?*)
      REDIS_IP=${LONG_OPTARG}
      ;;
      redis_port=?*)
      REDIS_PORT=${LONG_OPTARG}
      ;;
      redis_db=?*)
      REDIS_DB=${LONG_OPTARG}
      ;;
      redis_pwd=?*)
      REDIS_PWD=${LONG_OPTARG}
      ;;
      mq_ip=?*)
      MQ_IP=${LONG_OPTARG}
      ;;
      mq_port=?*)
      MQ_PORT=${LONG_OPTARG}
      ;;
      mq_ack=?*)
      MQ_ACK=${LONG_OPTARG}
      ;;
      mq_sck=?*)
      MQ_SCK=${LONG_OPTARG}
      ;;
      mq_consume_nums=?*)
      MQ_CONSUME_NUMS=${LONG_OPTARG}
      ;;
      mq_instance=?*)
      MQ_INSTANCE_NAME=${LONG_OPTARG}
      ;;
      uep_outter_url=?*)
      UEP_OUTTER_URL=${LONG_OPTARG}
      ;;
      open_platform_outter_url=?*)
      OPEN_PLATFORM_OUTTER_URL=${LONG_OPTARG}
      ;;
      *)
        echo "Illegal option --${OPTARG}, exit." >&2;
        exit 1
      ;;
      esac
    ;;
    ?)
      usage
    ;;
  esac
done
main "$@"
