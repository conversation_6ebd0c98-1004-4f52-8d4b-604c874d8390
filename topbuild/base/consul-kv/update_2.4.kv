#记录2.4版本consul的配置变化
###########app#############
#是否开启开发调试模式
sys.server.uep.debug=false

#流程引擎配置
#应用授权申请
#process.engine.flows[0].key=APP_AUTH
#process.engine.flows[0].name=应用授权申请
#process.engine.flows[0].code=PROC-F4049AAA-2FAA-4EDA-9031-36D51FFAEDE1
#process.engine.flows[0].fields[0].name=应用ID
#process.engine.flows[0].fields[0].attr=client_id
#process.engine.flows[0].fields[0].attrExt=
#process.engine.flows[0].fields[1].name=应用名称
#process.engine.flows[0].fields[1].attr=client_name
#process.engine.flows[0].fields[1].attrExt=
#process.engine.flows[0].fields[2].name=申请理由
#process.engine.flows[0].fields[2].attr=reason
#process.engine.flows[0].fields[2].attrExt=
#process.engine.flows[0].fields[3].name=开始时间
#process.engine.flows[0].fields[3].attr=start_date
#process.engine.flows[0].fields[3].attrExt=
#process.engine.flows[0].fields[4].name=结束时间
#process.engine.flows[0].fields[4].attr=end_date
#process.engine.flows[0].fields[4].attrExt=

#用户导入授权申请
#process.engine.flows[1].key=USER_IMPORT
#process.engine.flows[1].name=用户导入授权申请
#process.engine.flows[1].code=PROC-676DF944-7B10-45CB-BDD2-1A775875B359
#process.engine.flows[1].fields[0].name=批次号
#process.engine.flows[1].fields[0].attr=batch_no
#process.engine.flows[1].fields[0].attrExt=

#发送工作通知配置文件
admin.message.url=dingtalk://dingtalkclient/action/openapp?corpid=%s&container_type=work_platform&app_id=0_%s&redirect_type=jump&redirect_url=
personal.message.markDown=\u60a8\u7684\u8d26\u53f7\u5c06\u4e8e\u0025\u0073\u5230\u671f\uff0c\u5230\u671f\u540e\u4e0d\u80fd\u518d\u767b\u5f55\uff0c\u70b9\u51fb\u81ea\u52a9\u7533\u8bf7\u7eed\u671f
admin.message.markDown=\u60a8\u7ba1\u7406\u7684\u90e8\u95e8\u4e0b\u6709\u0025\u0073\u4eba\u8d26\u53f7\u5373\u5c06\u5230\u671f\uff0c\u5df2\u901a\u77e5\u8be5\u4eba\u5458\u8fdb\u884c\u81ea\u52a9\u7533\u8bf7\u7eed\u671f\uff0c\u70b9\u51fb\u67e5\u770b\u5177\u4f53\u540d\u5355

#根据开始截至时间计算用户状态周期，并发送到期通知 每个小时执行一次
user.remind.cron=0 0 0/1 * * ?

###########iam#############

###########iamnotify#############


###########mdm#############


###########adm#############
