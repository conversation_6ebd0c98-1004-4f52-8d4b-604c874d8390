#!/bin/bash
set -e
#===============================================================================
#
#          FILE: package.sh
#
#
#   DESCRIPTION:  script to package uep services, put all together
#
#       OPTIONS:  ---
#  REQUIREMENTS:  ---
#          BUGS:  ---
#         NOTES:  ---
#        AUTHOR:  Liu Yang
#       VERSION:  1.0
#       CREATED:  06/19/2019 20:12:43 AM CST
#      REVISION:  ---
#===============================================================================#
SCRIPT=$(readlink -f $0)
SCRIPTPATH=$(dirname ${SCRIPT})

# WORKBASE is output dir
declare -r WORKBASE="${SCRIPTPATH}/target"
declare -a CANDIDATE=("iam" "iam-notify" "mdm" "adm" "ncm" "uam" "acm" "gateway")
declare -a CANDIDATE_PROJECT=(
"iam-service"
"iam-notify-service"
"mdm-service"
"adm-service"
"ncm-service"
"uam-service"
"acm-service"
"gateway-service"
)

declare -a SOURCEBASE="${SCRIPTPATH}/../../"
declare -r BINARYSUFFIX="-exec.jar"

# app config
CONSUL_HOST="127.0.0.1"
CONSUL_PORT="8500"
CONSUL_PROFILE="consul"

function package_app(){

    local app_name=$1
    local artifect_project_name=$2

    # app sources location dirs
    local app_source_dir="${SOURCEBASE}${app_name}"
    if [[ ${app_name} = "iam-notify" ]]; then
      app_source_dir="${SOURCEBASE}iam"
    fi
    local app_resource_dir="${app_source_dir}/${artifect_project_name}/src/main/resources"
    local app_binary_dir="${app_source_dir}/${artifect_project_name}/target"
    local app_binary_name="${app_name}${BINARYSUFFIX}"
    local binary="${app_binary_dir}/${app_binary_name}"

    # the location for output target
    local work_dir="${WORKBASE}/${app_name}"
    local conf_dir="${work_dir}"/conf
    mkdir -p "${work_dir}"

    #copy firewall sh
    cp -rf "${SCRIPTPATH}/../util/setup_firewall.sh" "${work_dir}"

    # copy app deploy directory layout here.
    local deploy_base="${SCRIPTPATH}/deploy/${app_name}"
    local layout_dirs=("app" "conf" "scripts")

    for dir in "${layout_dirs[@]}"; do
        echo "Copy dir:${dir}"
        cp -rf "${deploy_base}/${dir}" "${work_dir}"
    done

    cp -rf "${deploy_base}/install.sh" "${work_dir}"
    #add executable permission for start.sh
    [ -e ${work_dir}/app/bin/start.sh ] && chmod +x ${work_dir}/app/bin/start.sh
    [ -e ${work_dir}/install.sh ] && chmod +x ${work_dir}/install.sh

    cp -rf "${app_resource_dir}"/*.properties "${conf_dir}"
    cp -rf "${app_resource_dir}"/*.xml "${conf_dir}"

    echo `git rev-parse --short HEAD` > "${conf_dir}/version"

    if [[ ${app_name} != "gateway" ]]; then
      set_prop "${conf_dir}/application-consul.properties" "spring.cloud.consul.host" "${CONSUL_HOST}"
      set_prop "${conf_dir}/application-consul.properties" "spring.cloud.consul.port" "${CONSUL_PORT}"
      set_prop "${conf_dir}/application.properties" "spring.profiles.active" "${CONSUL_PROFILE}"
    fi

    # copy app binary
    if [[ -f "${binary}" ]]; then
        echo "Copy ${app_name}${BINARYSUFFIX}"
        cp "${binary}" "${work_dir}/app/${app_binary_name}"
    else
        echo "${binary} was not found. Please build project using maven. Quit."
        exit 1
    fi

    # special for license
    if [ ${app_name} = "license" ]; then
        echo "Copy license files"
        cp -rf "${app_source_dir}/${artifect_project_name}/files" "${work_dir}"
        cp -rf "${app_source_dir}/${artifect_project_name}/multiFiles" "${work_dir}"
        cp -rf "${app_source_dir}/${artifect_project_name}/feature" "${work_dir}"
    fi

    cd ${work_dir}
    local build_tar="${WORKBASE}/${app_name}.tar.gz"
    tar cvzfp ${build_tar} ./*
}

function package_all(){
    for key in "${!CANDIDATE[@]}"; do
        echo "Package ${CANDIDATE[key]} with artifect dir ${CANDIDATE_PROJECT[$key]}"
        clean_app_target ${CANDIDATE[key]}
        package_app "${CANDIDATE[key]}" ${CANDIDATE_PROJECT[$key]}
    done
}

#NOTE: this DOES NOT work when the value is multi-lines
function set_prop() {
  local prop_file="$1"
  local key="$2"
  local val="$3"
  #1.use | as delimiter here, as the new value might contain
  #2.ignore comments
  #3.replace with new value.

  grep -q '^[[:space:]]*'"${key}"'[[:space:]]*=[[:space:]]*.*$' "${prop_file}"

  # if found match, then replace, otherwise append
  if [[ $? -eq 0 ]]; then
      echo "set property ${key}=${val}"
      sed -i 's|^[[:space:]]*'"${key}"'[[:space:]]*=[[:space:]]*.*$|'"${key}=${val}"'|' "${prop_file}"
  else
      echo "Property not find: ${key}"
  fi
}

function clean_app_target() {
    local app_dir=${WORKBASE}/$1
    [[ -d "${app_dir}" ]] && rm -rf "${app_dir}" 2>/dev/null
    echo "Clean up path: ${app_dir}"
}

################################ MAIN ROUTINE ##########################################
function main(){
    if [ -n "${BUILD_APP}" ];then
        echo "Package module: ${BUILD_APP}"
        for key in "${!CANDIDATE[@]}"; do
         if test "${BUILD_APP}" = ${CANDIDATE[key]};then
            echo "Package app: ${CANDIDATE[key]}"
            clean_app_target ${CANDIDATE[key]}
            package_app "${CANDIDATE[key]}" ${CANDIDATE_PROJECT[$key]}
         fi
        done
        exit 0
    fi
    #package, put all together.
    echo "Package all"
    package_all
    echo "Success!"
}

###### MAIN ENTRY ####
while getopts 'up:h:f:b:' OPT; do
  case $OPT in
    u)
      usage
      ;;
    p)
      CONSUL_PORT=${OPTARG}
    ;;
    h)
      CONSUL_HOST=${OPTARG}
    ;;
    f)
      CONSUL_PROFILE=${OPTARG}
    ;;
    b)
      BUILD_APP=${OPTARG}
    ;;
    ?)
      usage
  esac
done

main "@"
