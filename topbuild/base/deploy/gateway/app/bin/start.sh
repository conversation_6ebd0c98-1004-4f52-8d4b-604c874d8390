#!/bin/bash
#set -x -e
#===============================================================================
#
#          FILE: start.sh
#
#
#   DESCRIPTION:  start script.
#
#       OPTIONS:  ---
#  REQUIREMENTS:  ---
#          BUGS:  ---
#         NOTES:  ---
#        AUTHOR:  Liu Yang
#       VERSION:  0.1
#       CREATED:  6/12/2019 09:43:36 AM CST
#      REVISION:  ---
#===============================================================================#
################################ MAIN ROUTINE ##########################################
function set_prop() {
  local prop_file="$1"
  local key="$2"
  local val="$3"
  #1.use | as delimiter here, as the new value might contain
  #2.ignore comments
  #3.replace with new value.

  grep -q '^[[:space:]]*'"${key}"'[[:space:]]*=[[:space:]]*.*$' "${prop_file}"

  # if found match, then replace, otherwise append
  if [[ $? -eq 0 ]]; then
      echo "set property ${key}=${val}"
      sed -i 's|^[[:space:]]*'"${key}"'[[:space:]]*=[[:space:]]*.*$|'"${key}=${val}"'|' "${prop_file}"
  else
      echo "Property not find: ${key}"
  fi
}

if [[ -z "${APP_HOME}" ]]; then
    APP_HOME="${1:-/usr/local/gateway}"
fi

cd "${APP_HOME}/app/"

if [[ -x /usr/local/bin/java-opts-in-docker.sh ]]; then
    export _JAVA_XMX_OPT=" ${JAVA_AGENT}
        $(/usr/local/bin/java-opts-in-docker.sh) \
        -XX:+UseG1GC \
        -XX:+UnlockExperimentalVMOptions \
        -XX:MaxGCPauseMillis=50 \
        -XX:G1MaxNewSizePercent=80 \
        -XX:+UseStringDeduplication"
else
    export _JAVA_XMX_OPT=" \
        -Xms1024M \
        -Xmx1024M \
        -XX:MetaspaceSize=256M \
        -XX:MaxMetaspaceSize=256M \
        -XX:MaxDirectMemorySize=64M \
        -XX:+UseG1GC \
        -XX:+UnlockExperimentalVMOptions \
        -XX:MaxGCPauseMillis=100 \
        -XX:G1MaxNewSizePercent=80 \
        -XX:+UseStringDeduplication"
fi
exec ./gateway-exec.jar
