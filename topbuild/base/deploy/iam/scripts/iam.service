#This file should be installed in /usr/lib/systemd/system
#After installation should run command:
#systemctl daemon-reload
#systemctl enable iam.service
[Unit]
Description=IAM Service
After=network.target

[Service]
Environment=JAVA_HOME=/usr/bin/java
Environment=APP_HOME=__INSTALL_TO__
Type=simple
User=uep
LimitNOFILE=65536
ExecStart=__INSTALL_TO__/app/bin/start.sh
Restart=on-abort

[Install]
WantedBy=multi-user.target