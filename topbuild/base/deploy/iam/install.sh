#!/bin/bash
set -e 
#===============================================================================
#
#          FILE: package.sh
#
#
#   DESCRIPTION:  script to package IAM, put all together
#
#       OPTIONS:  ---
#  REQUIREMENTS:  ---
#          BUGS:  ---
#         NOTES:  ---
#        AUTHOR:  Liu Yang
#       VERSION:  1.0
#       CREATED:  06/25/2019 12:04:56 AM CST
#      REVISION:  ---
#===============================================================================#
SCRIPT=$(readlink -f $0)
SCRIPTPATH=$(dirname ${SCRIPT})
INSTALL_TO="${SCRIPTPATH}"

# app name
APP_NAME="iam"
SERVICE_NAME="${APP_NAME}"
SERVICE_FILE="/usr/lib/systemd/system/${APP_NAME}.service"
PORT=8089
SVC_USER=uep
SVC_GROUP=uep

#create svc group and account
function create_svc_account_group(){
    if [[ $(getent group "${SVC_GROUP}") ]]; then
        echo "${SVC_GROUP} group is existed!"
    else
        groupadd -r "${SVC_GROUP}"
    fi

    if [[ $(getent passwd "${SVC_USER}") ]]; then
        echo "${SVC_USER} user is existed!"
    else
        useradd -r -g "${SVC_GROUP}" -s /sbin/nologin "${SVC_USER}"
    fi
}

#systemd script.
function install_as_service() {
    echo "Install ${SERVICE_NAME} as a service"
    sed -e "s:__INSTALL_TO__:${INSTALL_TO}:g" ${SCRIPTPATH}/scripts/${APP_NAME}.service > ${SERVICE_FILE}
    sudo systemctl daemon-reload
    sudo systemctl enable ${SERVICE_NAME}
    sudo systemctl start ${SERVICE_NAME}
}

function check_service() {
    if [[ -f ${SERVICE_FILE} ]]; then
        echo "Service \"${SERVICE_NAME}\" already existed."
        return 1
    fi
}

#create log dir if no
function create_log_dir() {
    local log_dir="${SCRIPTPATH}"/logs
    if [[ ! -d "${log_dir}" ]]; then
        mkdir "${log_dir}"
    fi
}


function main() {
    echo "installing ..."
    if check_service; then
        install_as_service
    else
        echo "${SERVICE_NAME} service already installed"
    fi

    create_svc_account_group
    create_log_dir

#    echo "------setup firewall for load balance-----"
#    sh ${SCRIPTPATH}/setup_firewall.sh --port=${PORT}

    echo "------setup install_to folder Owner rights-----"
    chown -R "${SVC_USER}":"${SVC_GROUP}" "${INSTALL_TO}"
}

main "@"