use mdm;
ALTER TABLE t_push_info MODIFY COLUMN `createtime` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间';

-- 1 所有表新增租户ID字段
ALTER TABLE t_accessweb_restriction       ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_admin_list_custom           ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_admin_list_default          ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_api_dict                    ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_api_token                   ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_app_category                ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_app_download_log            ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_app_push                    ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_app_push_device             ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_app_push_user               ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_app_push_user_group         ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_app_schedule                ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_app_store                   ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_app_store_appinfo           ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_app_store_category          ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_app_store_conf              ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_app_store_del               ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_app_store_device            ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_app_store_user              ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_app_store_user_group        ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_app_strategy                ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_app_strategy_device         ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_app_strategy_group          ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_app_strategy_irre_log       ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_app_strategy_label          ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_app_strategy_push           ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_app_strategy_store          ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_app_strategy_user           ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_app_time_group              ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_app_traffic                 ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_apple_dep_device            ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_apple_dep_profile           ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_apple_device_profile        ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_certificate                 ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
-- ALTER TABLE t_client                      ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_client_layout               ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_client_layout_group         ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_client_policy               ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_command_log                 ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_compliance_schedule         ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_config                      ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_connector                   ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_contact                     ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_contact_data                ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_contact_log                 ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_contact_version             ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_device                      ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_device_accessweb            ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_device_apn                  ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_device_apn_token            ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_device_app                  ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_device_app_action           ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_device_app_crash            ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_device_app_startup          ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_device_appusage             ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_device_area                 ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_device_browserhistory       ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_device_call                 ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_device_callover_log         ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_device_cert                 ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_device_check                ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_device_check_device         ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_device_cmd_send_state       ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_device_collect              ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_device_cryptomodule         ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_device_diagnose_log         ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_device_history              ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_device_model                ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_device_msg                  ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_device_network_log          ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_device_oper_log             ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_device_profile              ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_device_remotecontrol        ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_device_samsung              ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_device_securityinfo         ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_device_status               ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_device_sysevent_log         ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_device_tmp                  ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_device_traffic              ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_device_usercheckin          ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_device_using_log            ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_device_wechat               ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_device_wechat_linkman       ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_device_wechat_log           ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_device_wrapapp              ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_doc_group_version           ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_document                    ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_document_log                ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_document_oper_log           ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_document_statistics         ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_document_tag                ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_document_tag_group          ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_document_tag_link           ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_document_tag_user           ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_document_user_version       ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_dynamic_label_user          ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_emm_client_login            ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_emm_grant                   ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_emm_pincode                 ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_emm_token                   ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_entrance_guard              ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_entrance_guard_device       ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_entrance_guard_group        ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_entrance_guard_log          ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_entrance_guard_pu           ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_entrance_guard_pug          ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_entrance_guard_push         ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_exception                   ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_fence                       ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_fence_inout_log             ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_fence_trigger_log           ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_file                        ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_file_dist_device            ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_file_dist_group             ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_file_dist_user              ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_file_distribute             ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_file_user_open              ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_file_user_star              ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_folder                      ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_folder_dist_group           ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_folder_dist_user            ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_folder_distribute           ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_google_layout_page          ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_google_play_product         ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_google_user_product         ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_group                       ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_group_center                ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_group_map                   ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_illegal_smssend_log         ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_illegal_time                ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_import_server               ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_ind_strategy_max_set        ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_index_app_max_set           ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_index_app_set               ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_index_chart_def             ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_index_chart_set             ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_index_shortcut_def          ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_index_shortcut_set          ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_index_strategy_set          ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_ios_buildin_app             ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
-- ALTER TABLE t_ios_model_dict              ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_label                       ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_label_device                ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_last_location               ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_ldap_config                 ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_location_history            ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_malware_list                ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_mdm_log_device_violation    ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_mdm_policy                  ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_mdm_policy_global           ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_mem_task                    ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_notification                ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_notifications_logon         ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_perm_template               ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_perm_template_api           ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_perm_template_menu          ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_policy                      ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_policy_device               ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_policy_group                ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_policy_mdm                  ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_policy_push                 ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_policy_scep                 ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_policy_user                 ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_push_info                   ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_remote_desktop_log          ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_report                      ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_report_chats_content        ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_report_chats_file           ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_report_schedule             ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_report_subs_param           ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_report_subscription         ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_reset_password              ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_restrictions_samsung        ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_server                      ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_static_label_device         ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_static_label_user           ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_sys_config                  ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_sys_dictionary              ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_sys_dictionary_value        ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_sys_log                     ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_sys_menu                    ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_sys_role                    ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_sys_role_menu               ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_sys_user                    ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_sys_user_api                ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_sys_user_group              ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_sys_user_perm               ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_sys_user_role               ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_sysconf_setlicense          ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_tag                         ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_tag_group                   ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_tag_position                ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_tag_time                    ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_tag_user                    ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_temp_device_ios_ctrl        ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_time_fence_record           ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_time_unit                   ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_user                        ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_user_app_permission         ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_user_back_thread_log        ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_user_center                 ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_user_custom_field           ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_user_family_phone           ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_user_pin                    ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_user_signin_log             ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_user_vacation               ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_version                     ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_violate_dist_device         ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
-- ALTER TABLE t_violate_process_dict        ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_violation_admin_alarm       ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
-- ALTER TABLE t_violation_cond_dict         ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_violation_device            ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_violation_device_app        ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_violation_device_proc       ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_violation_device_proc_his   ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_violations                  ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_violations_device           ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_violations_group            ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_violations_user             ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_virusscan_history           ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_virusscan_latest            ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;
ALTER TABLE t_wechat_linkman_his          ADD COLUMN `tenantId` varchar(64) NOT NULL COMMENT '租户ID' ;


-- 2 去掉所有的外键关系
ALTER TABLE t_admin_list_custom           DROP FOREIGN KEY `t_admin_list_custom_ibfk_1`;
ALTER TABLE t_app_schedule                DROP FOREIGN KEY `t_app_schedule_ibfk_1`;
ALTER TABLE t_app_store_category          DROP FOREIGN KEY `t_app_store_category_ibfk_1`;
ALTER TABLE t_app_store_category          DROP FOREIGN KEY `t_app_store_category_ibfk_2`;
ALTER TABLE t_app_store_category          DROP FOREIGN KEY `t_app_store_category_ibfk_3`;
ALTER TABLE t_app_store_device            DROP FOREIGN KEY `t_app_store_device_ibfk_1`;
ALTER TABLE t_app_store_device            DROP FOREIGN KEY `t_app_store_device_ibfk_2`;
ALTER TABLE t_app_store_user              DROP FOREIGN KEY `t_app_store_user_ibfk_1`;
ALTER TABLE t_app_store_user              DROP FOREIGN KEY `t_app_store_user_ibfk_2`;
ALTER TABLE t_app_store_user_group        DROP FOREIGN KEY `t_app_store_user_group_ibfk_1`;
ALTER TABLE t_app_store_user_group        DROP FOREIGN KEY `t_app_store_user_group_ibfk_2`;
ALTER TABLE t_app_strategy_device         DROP FOREIGN KEY `t_app_strategy_device_ibfk_1`;
ALTER TABLE t_app_strategy_device         DROP FOREIGN KEY `t_app_strategy_device_ibfk_2`;
ALTER TABLE t_app_strategy_irre_log       DROP FOREIGN KEY `t_app_strategy_irre_log_ibfk_1`;
ALTER TABLE t_app_strategy_irre_log       DROP FOREIGN KEY `t_app_strategy_irre_log_ibfk_2`;
ALTER TABLE t_app_strategy_label          DROP FOREIGN KEY `t_app_strategy_label_ibfk_1`;
ALTER TABLE t_app_strategy_label          DROP FOREIGN KEY `t_app_strategy_label_ibfk_2`;
ALTER TABLE t_app_strategy_store          DROP FOREIGN KEY `t_app_strategy_store_ibfk_1`;
ALTER TABLE t_app_time_group              DROP FOREIGN KEY `t_app_time_group_ibfk_1`;
ALTER TABLE t_app_time_group              DROP FOREIGN KEY `t_app_time_group_ibfk_2`;
ALTER TABLE t_client_policy               DROP FOREIGN KEY `client_policy__client__clientId`;
ALTER TABLE t_contact_data                DROP FOREIGN KEY `t_contact_data_ibfk_1`;
ALTER TABLE t_device_apn                  DROP FOREIGN KEY `fk_t_device_apn_deviceid`;
ALTER TABLE t_device_apn_token            DROP FOREIGN KEY `fk_t_device_apn_token_deviceid`;
ALTER TABLE t_device_area                 DROP FOREIGN KEY `t_device_area_ibfk_1`;
ALTER TABLE t_device_cert                 DROP FOREIGN KEY `fk_t_device_cert_deviceid`;
ALTER TABLE t_device_check_device         DROP FOREIGN KEY `fk_device_check_device_checkid`;
ALTER TABLE t_device_check_device         DROP FOREIGN KEY `fk_device_check_device_deviceid`;
ALTER TABLE t_device_cryptomodule         DROP FOREIGN KEY `fk_t_device_cryptomodule_deviceid`;
ALTER TABLE t_device_diagnose_log         DROP FOREIGN KEY ` t_device_diagnose_log_ibfk_1`;
ALTER TABLE t_device_profile              DROP FOREIGN KEY `t_device_profile_ibfk_1`;
ALTER TABLE t_device_remotecontrol        DROP FOREIGN KEY `t_device_remotecontrol_ibfk_1`;
ALTER TABLE t_device_samsung              DROP FOREIGN KEY `t_device_samsung_ibfk_1`;
ALTER TABLE t_device_securityinfo         DROP FOREIGN KEY `t_device_securityinfo_ibfk_1`;
ALTER TABLE t_device_status               DROP FOREIGN KEY `t_device_status_ibfk_1`;
ALTER TABLE t_device_using_log            DROP FOREIGN KEY `t_device_using_log_ibfk_1`;
ALTER TABLE t_device_wrapapp              DROP FOREIGN KEY `t_device_wrapap_ibfk_1`;
ALTER TABLE t_document                    DROP FOREIGN KEY `t_document_ibfk_1`;
ALTER TABLE t_document_log                DROP FOREIGN KEY `t_document_log_ibfk_1`;
ALTER TABLE t_document_log                DROP FOREIGN KEY `t_document_log_ibfk_2`;
ALTER TABLE t_document_tag_group          DROP FOREIGN KEY `t_document_tag_group_ibfk_1`;
ALTER TABLE t_document_tag_group          DROP FOREIGN KEY `t_document_tag_group_ibfk_2`;
ALTER TABLE t_document_tag_link           DROP FOREIGN KEY `t_document_tag_link_ibfk_1`;
ALTER TABLE t_document_tag_link           DROP FOREIGN KEY `t_document_tag_link_ibfk_2`;
ALTER TABLE t_document_tag_user           DROP FOREIGN KEY `t_document_tag_user_ibfk_1`;
ALTER TABLE t_document_tag_user           DROP FOREIGN KEY `t_document_tag_user_ibfk_2`;
ALTER TABLE t_dynamic_label_user          DROP FOREIGN KEY `t_dynamic_label_user_idfk`;
ALTER TABLE t_emm_grant                   DROP FOREIGN KEY `pincode_fk`;
ALTER TABLE t_entrance_guard_device       DROP FOREIGN KEY `tegd_ibfk_1`;
ALTER TABLE t_entrance_guard_device       DROP FOREIGN KEY `tegd_ibfk_2`;
ALTER TABLE t_entrance_guard_pu           DROP FOREIGN KEY `tegpu_ibfk_1`;
ALTER TABLE t_entrance_guard_pu           DROP FOREIGN KEY `tegpu_ibfk_2`;
ALTER TABLE t_entrance_guard_pug          DROP FOREIGN KEY `tegpug_ibfk_1`;
ALTER TABLE t_entrance_guard_pug          DROP FOREIGN KEY `tegpug_ibfk_2`;
ALTER TABLE t_file_dist_device            DROP FOREIGN KEY `t_file_dist_device_ibfk_1`;
ALTER TABLE t_file_dist_device            DROP FOREIGN KEY `t_file_dist_device_ibfk_2`;
ALTER TABLE t_file_dist_group             DROP FOREIGN KEY `t_file_distribute_group_ibfk_1`;
ALTER TABLE t_file_dist_group             DROP FOREIGN KEY `t_file_distribute_group_ibfk_2`;
ALTER TABLE t_file_dist_user              DROP FOREIGN KEY `t_file_distribute_user_ibfk_1`;
ALTER TABLE t_file_dist_user              DROP FOREIGN KEY `t_file_distribute_user_ibfk_2`;
ALTER TABLE t_file_distribute             DROP FOREIGN KEY `t_file_distribute_ibfk_1`;
ALTER TABLE t_file_distribute             DROP FOREIGN KEY `t_file_distribute_ibfk_2`;
ALTER TABLE t_file_user_open              DROP FOREIGN KEY `t_file_user_open_ibfk_1`;
ALTER TABLE t_file_user_open              DROP FOREIGN KEY `t_file_user_open_ibfk_2`;
ALTER TABLE t_file_user_star              DROP FOREIGN KEY `t_file_user_star_ibfk_1`;
ALTER TABLE t_file_user_star              DROP FOREIGN KEY `t_file_user_star_ibfk_2`;
ALTER TABLE t_folder_dist_group           DROP FOREIGN KEY `t_folder_distribute_group_ibfk_1`;
ALTER TABLE t_folder_dist_group           DROP FOREIGN KEY `t_folder_distribute_group_ibfk_2`;
ALTER TABLE t_folder_dist_user            DROP FOREIGN KEY `t_folder_distribute_user_ibfk_1`;
ALTER TABLE t_folder_dist_user            DROP FOREIGN KEY `t_folder_distribute_user_ibfk_2`;
ALTER TABLE t_folder_distribute           DROP FOREIGN KEY `t_folder_distribute_ibfk_1`;
ALTER TABLE t_folder_distribute           DROP FOREIGN KEY `t_folder_distribute_ibfk_2`;
ALTER TABLE t_last_location               DROP FOREIGN KEY `t_last_location_ibfk_1`;
ALTER TABLE t_perm_template               DROP FOREIGN KEY `t_perm_template_ibfk_1`;
ALTER TABLE t_perm_template_api           DROP FOREIGN KEY `t_perm_template_api_fk`;
ALTER TABLE t_perm_template_menu          DROP FOREIGN KEY `t_perm_template_menu_ibfk_1`;
ALTER TABLE t_perm_template_menu          DROP FOREIGN KEY `t_perm_template_menu_ibfk_2`;
ALTER TABLE t_policy                      DROP FOREIGN KEY `fk_t_policy_fenceid_t_fence`;
ALTER TABLE t_policy_device               DROP FOREIGN KEY `t_policy_device_ibfk_1`;
ALTER TABLE t_policy_device               DROP FOREIGN KEY `t_policy_device_ibfk_2`;
ALTER TABLE t_policy_group                DROP FOREIGN KEY `t_policy_group_ibfk_1`;
ALTER TABLE t_policy_group                DROP FOREIGN KEY `t_policy_group_ibfk_2`;
ALTER TABLE t_policy_mdm                  DROP FOREIGN KEY `t_policy_mdm_ibfk_1`;
ALTER TABLE t_policy_scep                 DROP FOREIGN KEY `t_policy_scep_ibfk_1`;
ALTER TABLE t_remote_desktop_log          DROP FOREIGN KEY `t_remote_desktop_log_ibfk_1`;
ALTER TABLE t_report_schedule             DROP FOREIGN KEY `t_report_schedule_ibfk_1`;
ALTER TABLE t_report_subs_param           DROP FOREIGN KEY `t_report_subs_param_ibfk_1`;
ALTER TABLE t_report_subscription         DROP FOREIGN KEY `t_report_subscription_ibfk_1`;
ALTER TABLE t_restrictions_samsung        DROP FOREIGN KEY `t_restrictions_samsung_ibfk_1`;
ALTER TABLE t_static_label_device         DROP FOREIGN KEY `t_static_label_device_ibfk_1`;
ALTER TABLE t_static_label_device         DROP FOREIGN KEY `t_static_label_device_ibfk_2`;
ALTER TABLE t_static_label_user           DROP FOREIGN KEY `t_static_label_user_ibfk_1`;
ALTER TABLE t_static_label_user           DROP FOREIGN KEY `t_static_label_user_ibfk_2`;
ALTER TABLE t_sys_dictionary_value        DROP FOREIGN KEY `t_sys_dictionary_value_ibfk_1`;
ALTER TABLE t_sys_role_menu               DROP FOREIGN KEY `t_sys_role_menu_ibfk_1`;
ALTER TABLE t_sys_role_menu               DROP FOREIGN KEY `t_sys_role_menu_ibfk_2`;
ALTER TABLE t_sys_user_api                DROP FOREIGN KEY `t_sys_user_api_fk`;
ALTER TABLE t_sys_user_group              DROP FOREIGN KEY `sys_user_group_ibfk_1`;
ALTER TABLE t_sys_user_group              DROP FOREIGN KEY `sys_user_group_ibfk_2`;
ALTER TABLE t_sys_user_perm               DROP FOREIGN KEY `t_sys_user_perm_ibfk_1`;
ALTER TABLE t_sys_user_perm               DROP FOREIGN KEY `t_sys_user_perm_ibfk_2`;
ALTER TABLE t_sys_user_role               DROP FOREIGN KEY `t_sys_user_role_ibfk_2`;
ALTER TABLE t_sys_user_role               DROP FOREIGN KEY `t_sys_user_role_ibfk_3`;
ALTER TABLE t_tag_group                   DROP FOREIGN KEY `t_tag_group_ibfk_1`;
ALTER TABLE t_tag_group                   DROP FOREIGN KEY `t_tag_group_ibfk_2`;
ALTER TABLE t_tag_position                DROP FOREIGN KEY `t_tag_position_ibfk_1`;
ALTER TABLE t_tag_time                    DROP FOREIGN KEY `t_tag_time_ibfk_1`;
ALTER TABLE t_tag_user                    DROP FOREIGN KEY `t_tag_user_ibfk_1`;
ALTER TABLE t_tag_user                    DROP FOREIGN KEY `t_tag_user_ibfk_2`;
ALTER TABLE t_time_unit                   DROP FOREIGN KEY `t_time_unit_ibfk_1`;
ALTER TABLE t_user_app_permission         DROP FOREIGN KEY `FK_user`;
ALTER TABLE t_user_pin                    DROP FOREIGN KEY `FK_user_pin`;
ALTER TABLE t_user_signin_log             DROP FOREIGN KEY `FK_user_signin_log`;
ALTER TABLE t_violate_dist_device         DROP FOREIGN KEY `t_violate_dist_device_ibfk_1`;
ALTER TABLE t_violate_dist_device         DROP FOREIGN KEY `t_violate_dist_device_ibfk_2`;
ALTER TABLE t_violation_device            DROP FOREIGN KEY `t_violation_device_ibfk_2`;
ALTER TABLE t_violation_device_proc       DROP FOREIGN KEY `t_violation_device_proc_ibfk_1`;
ALTER TABLE t_violation_device_proc       DROP FOREIGN KEY `t_violation_device_proc_ibfk_2`;
ALTER TABLE t_violation_device_proc_his   DROP FOREIGN KEY `t_violation_device_proc_his_ibfk_1`;
ALTER TABLE t_violation_device_proc_his   DROP FOREIGN KEY `t_violation_device_proc_his_ibfk_2`;
ALTER TABLE t_violations_device           DROP FOREIGN KEY `t_violations_device_ibfk_1`;
ALTER TABLE t_violations_device           DROP FOREIGN KEY `t_violations_device_ibfk_2`;
ALTER TABLE t_violations_group            DROP FOREIGN KEY `t_violations_group_ibfk_1`;
ALTER TABLE t_violations_group            DROP FOREIGN KEY `t_violations_group_ibfk_2`;
ALTER TABLE t_violations_user             DROP FOREIGN KEY `t_violations_user_ibfk_1`;
ALTER TABLE t_violations_user             DROP FOREIGN KEY `t_violations_user_ibfk_2`;
ALTER TABLE t_policy_user                 DROP FOREIGN KEY `t_policy_user_ibfk_1`;
ALTER TABLE t_policy_user                 DROP FOREIGN KEY `t_policy_user_ibfk_2`;

-- 3 索引及主键
ALTER TABLE t_user DROP INDEX `loginid_index`,ADD UNIQUE INDEX `loginid_index`(`loginid`) USING BTREE;
ALTER TABLE t_app_store DROP INDEX `index_unique_pkg_ver_plat`,DROP INDEX `index_unique_pkg_vercode_plat`,
ADD UNIQUE INDEX `index_unique_pkg_ver_plat`(`pkgname`, `version`, `platform`, `domaintype`, `tenantId`) USING BTREE,
ADD UNIQUE INDEX `index_unique_pkg_vercode_plat`(`pkgname`, `versioncode`, `platform`, `domaintype`, `tenantId`) USING BTREE;

ALTER TABLE t_config DROP INDEX `k`, ADD UNIQUE INDEX `k`(`k`, `tenantId`) USING BTREE;
ALTER TABLE t_device_model DROP INDEX `model`,ADD UNIQUE INDEX `model`(`model`, `tenantId`) USING BTREE;
ALTER TABLE t_sys_user DROP PRIMARY KEY, ADD PRIMARY KEY (`loginid`, `tenantId`) USING BTREE;
ALTER TABLE t_sys_config DROP PRIMARY KEY,ADD PRIMARY KEY (`id`, `tenantId`) USING BTREE;
ALTER TABLE t_sys_dictionary DROP PRIMARY KEY, ADD PRIMARY KEY (`id`, `tenantId`) USING BTREE;
ALTER TABLE t_sys_dictionary_value DROP PRIMARY KEY, ADD PRIMARY KEY (`id`, `tenantId`) USING BTREE;
ALTER TABLE t_user DROP INDEX `oid_unique`;

-- 4 其他
-- 租户信息表
DROP TABLE IF EXISTS `t_sys_tenant`;
CREATE TABLE `t_sys_tenant` (
                                `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
                                `enName` varchar(256) DEFAULT NULL COMMENT '租户英文标识',
                                `zhName` varchar(64) DEFAULT NULL COMMENT '租户名称',
                                `address` varchar(256) DEFAULT NULL COMMENT '租户地址',
                                `contacts` varchar(64) DEFAULT NULL COMMENT '联系人',
                                `phone` varchar(32) DEFAULT NULL COMMENT '租户电话',
                                `email` varchar(64) DEFAULT NULL COMMENT '租户email',
                                `createTime` datetime DEFAULT NULL COMMENT '创建时间',
                                `updateTime` datetime DEFAULT NULL COMMENT '更新时间',
                                `status` int(11) DEFAULT NULL COMMENT '租户状态',
                                `expiredTime` datetime DEFAULT NULL COMMENT '租户过期时间',
                                `tenantType` int(11) DEFAULT NULL COMMENT '客户类型。0-正式 1-试用',
                                PRIMARY KEY (`tenantId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='租户信息表';

DROP TABLE IF EXISTS `t_sys_user_file`;
CREATE TABLE `t_sys_user_file` (
  `id` bigint(20) NOT NULL,
  `deviceId` bigint(20) DEFAULT NULL COMMENT '设备ID',
  `userId` bigint(20) NOT NULL COMMENT '用户ID',
  `businessType` tinyint(3) NOT NULL COMMENT '业务类型，1：截屏日志',
  `filePath` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '文件路径',
  `fileName` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '文件名',
  `fileExt` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '文件扩展名',
  `dfsType` tinyint(3) NOT NULL COMMENT '文件系统类型，1：本地，2：OSS',
  `createTime` datetime DEFAULT NULL COMMENT '创建时间',
  `updateTime` datetime DEFAULT NULL COMMENT '更新时间',
  `tenantId` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '租户ID',
  `packageName` varchar(64) COLLATE utf8_bin DEFAULT NULL COMMENT '应用包名',
  `deviceName` varchar(64) COLLATE utf8_bin DEFAULT NULL COMMENT '设备名称',
  `userName` varchar(64) COLLATE utf8_bin DEFAULT NULL COMMENT '用户名称',
  `loginId` varchar(32) COLLATE utf8_bin DEFAULT NULL COMMENT '用户登录ID',
  `occTime` datetime DEFAULT NULL COMMENT '发生时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='用户文件表';

ALTER TABLE t_connector MODIFY COLUMN `config` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '配置信息，json格式' ;

-- alter table t_device add uemVersion varchar(50) comment 'UEM版本号';


-- VPP 应用商店表 --
DROP TABLE IF EXISTS `t_vpp_store`;
CREATE TABLE t_vpp_store (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `adamid` bigint(20) NOT NULL COMMENT '苹果应用商店应用id',
  `producttypename` varchar(50) COMMENT 'productTypeName',
  `producttypeid` int(11) COMMENT 'productTypeId',
  `assignedcount` int(11) COMMENT '已授权license个数',
  `availablecount` int(11) COMMENT '可用license个数',
  `pricingparam` varchar(30) COMMENT 'pricingParam',
  `retiredcount` int(11) COMMENT '已收回license个数',
  `totalcount` int(11) COMMENT 'license总数',
  `irrevocable` varchar(10) COMMENT 'license是否可回收',
  `deviceassignable` varchar(10) COMMENT '是否可直接授权给设备',
  `name` varchar(200) DEFAULT NULL COMMENT '名称',
  `pkgname` varchar(200) DEFAULT NULL COMMENT '应用id',
  `version` varchar(50) DEFAULT NULL COMMENT '版本',
  `icon` varchar(200) DEFAULT NULL COMMENT '图标',
  `price` varchar(50) DEFAULT NULL COMMENT '价格',
  `appsize` int(11) DEFAULT NULL COMMENT '大小',
  `brief` varchar(2000) DEFAULT NULL COMMENT '软件简介',
  `company` varchar(200) DEFAULT NULL COMMENT '开发商',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `updatetime` datetime DEFAULT NULL COMMENT '更新时间',
  `osversion` varchar(200) DEFAULT NULL COMMENT '要求系统版本',
  `description` varchar(2000) DEFAULT NULL COMMENT '描述',
  `b2bcustomapp` varchar(10) COMMENT '自定app',
  `source` varchar(1000) COMMENT '应用源地址',
  `modeltype` varchar(20) COMMENT '支持设备型号',
  `conflict` int(11) DEFAULT 0 COMMENT '与应用商店是否有冲突：0、无冲突，1、有冲突',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='VPP应用';


-- VPP 应用兑换码 --
DROP TABLE IF EXISTS `t_vpp_cdkey`;
CREATE TABLE t_vpp_cdkey (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `orderid` varchar(50) COMMENT '订单号',
  `loginid` varchar(50) COMMENT '用户longinid',
  `adamid` bigint(20) NOT NULL COMMENT '苹果应用商店应用id',
  `vppappid` bigint(20) NOT NULL COMMENT 'vppappid',
  `status` int(11) DEFAULT 0 COMMENT '0：未使用，1已使用',
  `cdkey` varchar(50) COMMENT '兑换码',
  `inviteurl` varchar(500) COMMENT 'inviteUrl',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  `updatetime` datetime NOT NULL COMMENT '更新时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `t_vpp_cdkey_idx_1` (`adamid`,`tenantId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='VPP应用兑换码';


-- 客户端应用信息表 --
DROP TABLE IF EXISTS `t_client_app`;
CREATE TABLE t_client_app (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `appName` varchar(200) COMMENT '应用名称',
  `pkgName` varchar(200) COMMENT '应用的包名',
  `publishTime` datetime NOT NULL COMMENT '应用的打包或者发布时间，一般会从apk包中解释出来',
  `platform` int(11) NOT NULL COMMENT '平台类型，参考系统平台类型定义',
  `version` varchar(50) COMMENT '版本信息',
  `size` int(11) DEFAULT 0 COMMENT '应用的包大小',
  `status` int(11) DEFAULT 0 COMMENT '1当前活动的有效的应用，0上传的历史版本应用或者未启用的版本',
  `filePath` varchar(500) COMMENT '文件路径（或者OSS中的Key)',
  `rootPath` varchar(500) COMMENT '文件根目录，或者OSS中的bucketName',
  `checksum` varchar(100) COMMENT '文件的MDM值',
  `createTime` datetime NOT NULL COMMENT '创建时间',
  `updateTime` datetime NOT NULL COMMENT '更新时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `t_client_app_idx_1` (`platform`,`pkgName`,`version`,`tenantId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='客户端应用信息表';

-- 5 存储过程
-- 更新所有表中租户ID字段的值并初始化租户数据
DELIMITER $$
DROP PROCEDURE IF EXISTS updateTenantId;
CREATE PROCEDURE updateTenantId(
    IN dbname VARCHAR (20),
    IN tcode VARCHAR (20)
)
BEGIN
DECLARE flag INT DEFAULT 0;
DECLARE tname VARCHAR(50);
-- 查询数据库mdm中含有tenantId列的表
DECLARE result CURSOR FOR SELECT TABLE_NAME FROM  INFORMATION_SCHEMA.Columns WHERE TABLE_SCHEMA = dbname AND  COLUMN_NAME = 'tenantId';

DECLARE CONTINUE HANDLER FOR SQLSTATE '02000' SET flag = 1;

OPEN result;
    WHILE flag <> 1 DO

    FETCH result INTO tname;
        -- 批量设置所有表的为tenantId字段为实际的租户ID
        IF tname != 't_sys_tenant' THEN
            SET @execSql = CONCAT('UPDATE ', tname, ' SET tenantId = \'',tcode,'\'');
            PREPARE stmt FROM @execSql;
            EXECUTE stmt;
        END IF;

    END WHILE;

    SET @execSql = CONCAT('INSERT INTO t_sys_tenant(\`tenantId\`,\`status\`) VALUES(\'',tcode,'\',1)');
        PREPARE stmt FROM @execSql;
        EXECUTE stmt;
END;
$$
DELIMITER ;
-- CALL updateTenantId('mdm_new','afe');

-- 6 其他数据
-- DELETE FROM t_mdm_policy_global WHERE id = 'fecbb9bb-827c-4d3e-8fa1-cb29e692650c';

