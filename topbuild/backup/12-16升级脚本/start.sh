#!/bin/bash
#set -x -e
#===============================================================================
#
#          FILE: start.sh
#
#
#   DESCRIPTION:  start script.
#
#       OPTIONS:  ---
#  REQUIREMENTS:  ---
#          BUGS:  ---
#         NOTES:  ---
#        AUTHOR:  Liu Yang
#       VERSION:  0.1
#       CREATED:  6/12/2019 09:43:36 AM CST
#      REVISION:  ---
#===============================================================================#
################################ MAIN ROUTINE ##########################################
if [[ -z "${APP_HOME}" ]]; then
    APP_HOME="${1:-/usr/local/mdm}"
fi

cd "${APP_HOME}/app/"

export _JAVA_XMX_OPT=" \
        -Xms4096M \
        -Xmx4096M \
        -XX:MetaspaceSize=512M \
        -XX:MaxMetaspaceSize=1024M \
        -XX:MaxDirectMemorySize=64M \
        -XX:+UseG1GC \
        -XX:+UnlockExperimentalVMOptions \
        -XX:MaxGCPauseMillis=50 \
        -XX:G1MaxNewSizePercent=80 \
        -XX:+UseStringDeduplication"

exec ./mdm-exec.jar
