server.port=8091
server.servlet.context-path=/
spring.aop.proxy-target-class=true

#dds.database.digitalsee.driverClassName=com.p6spy.engine.spy.P6SpyDriver
#dds.database.digitalsee.jdbcUrl=**************************************
#swagger.enable=true

#dds.database.digitalsee.driverClassName=com.p6spy.engine.spy.P6SpyDriver
#dds.database.digitalsee.jdbcUrl=****************************************************************************


#\u591A\u79DF\u6237license\u9A8C\u8BC1\u5F00\u5173 \u5F00\u53D1\u4F7F\u7528
sys.tenant.auth.use.license.server=false
#是否使用mtenant服务
sys.tenant.auth.use.mtenant.server=3
#如果是多个租户，则用，进行分隔
sys.tenant.auth.use.mtenant.tenantIds=mdm

# License Management \u5F00\u53D1\u4F7F\u7528
#原值为license.service.check
sys.license.service.check=true
#原值为enable.sag=true
sys.enable.sag=false
sys.license.type=standard

#sysconfig account
sys.account.loginId=sysconfig
sys.account.username=Configuration Management
sys.account.pass=5919d1ff704ad16d3ba44ebde8a9e549
sys.account.type=30
sys.account.email=
sys.account.telephone=

#sys push server 1 guoxin,2 hexin
sys.push.server.type=2

#900天
sys.oauth2.refreshTokenExpiresIn=********
#900天
sys.oauth2.acccessTokenExpiresIn=********
#900天
sys.oauth2.accessTokenExpiresIn=********
#tokenType=1为opaque,0为jwt
sys.oauth2.tokenType=1


#jwt 相关配置
sys.jwt.key=mdm.digitalsee.cn,www.digitalsee.cn,uemx.digitalsee.cn,ThiSisAsecUrity,dontEllanYonEelse.
#sys.jwt.key=ThiSisAsecUrity,dontEllanYonEelse.
sys.jwt.expire=28800
sys.jwt.iss=https://mdm.digitalsee.cn
sys.jwt.admin-iss=https://mdm.admin.digitalsee.cn
sys.jwt.aud=UEM

sys.jwt.sag-iss=com.cyb.sag
sys.jwt.sag-expire=300
sys.jwt.sag-aud=com.cyb.mdm
sys.jwt.sag-key=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA1cg4lQUKgQ4p59SDZ+BHfvuzMrUzpRMgC9rxkqFfaqKceyr60nkhqkPuHZXGHAcWzmzDFqBhv66yJVLsbTl3rmmzh978HCfcRjzGFh2kfWb8dVyqNl7f04PsBvFT6VuNFZS8CPtCkzhNWTBhWo5X1pJ8yQvSORrba6CKatUEjF4fGcX67jwxkZWThIgftQt4kDR2ogUDDTy/y6lhKVh7iZRC0Q4NVvW4oNoWP0AXpNWyJG8n5lRn/4VtC09pdFpeAXBqljR8T26wyX3UIVpcg/l0lFxfDtDYoBjOCEZhGPCu7NG+oiG2kM1YtQg7loenQQpDC9uUWaIXdklhfXBpqwIDAQAB

sys.jwt.iam-iss=https://{uem.ip}
sys.jwt.iam-expire=300
sys.jwt.iam-aud=uem_native_app
sys.jwt.iam-key=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAjP55KadDgT7Ax1eFjDI9E6FccdCkUE2jp+mXgQeyYaIY8EW8+RrslAQezMShHJep1Vp9bl80ScuommQtKqOS7zHQJiQ+vZTbLNmCOjTTCx8mfJCzWdlRGxO9nl5gOHI9YY8nDzXRTrAjNsobSR1GUUL45QC/s1I1qseCWlR8Qixf7hjubft4bzD829DWsiN/0wrX021C+HzdhnS0j6uE6UHkYedhqwNykaYbcbAWaTOKdq7fbl3Q1iRltggIi8SlgncO8/ADaEnDmvuM0LDyD9WLs9wiB7kQ2OAmbj8+Gs6Q8X29/RctcZorDGHj5U4tT0DqEjkeXPG8Lb9kpE9tqQIDAQAB

#rsa相关配置
sys.rsa.pubKey=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDnT734M3vPLiQTgyOew4VOclY25r5BDsNNhJKIOaoTHA7hwU416gXauuKmFZX525JSvm2r9C3wozy4dvbDESfghztzYK3x2nMHosuh1R3JPrbCfb3e5qJmV/Ai/hEYMWtSh+lP1OzFpZPyKaw6Wz0o//GEu13Y8+UhugTEtdCkmQIDAQAB
sys.rsa.privateKey=MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAOdPvfgze88uJBODI57DhU5yVjbmvkEOw02Ekog5qhMcDuHBTjXqBdq64qYVlfnbklK+bav0LfCjPLh29sMRJ+CHO3NgrfHacweiy6HVHck+tsJ9vd7momZX8CL+ERgxa1KH6U/U7MWlk/IprDpbPSj/8YS7Xdjz5SG6BMS10KSZAgMBAAECgYAVaOoaeRlymcC5FaBMZ6iFFEwqlLzzZJxrJmQxu7SkRCqUdsH4d4rHLhkgaksRb2NhyUJ5cgZ2iYVrRYedko7wKkebGYZWMgSzMF+iSnmR+41KJJCw/CWa8uLWRaupB/JZ+XKMibtWbFPYMcLze21Ph1Ke32EOjeNiVY52aSpmKQJBAPVzkhUz+vdGklj74ZVYpnPX6pyV62jcOz2u2Liinispg4BnvEj/bYN+P3SHCk5KaMPD7uiwB9FKBjCEoseSVb8CQQDxQJrwFvgjc9BQ3ecnuHSzxjW+bjHqYNYWCzjcxbjA/JmcCWJpPfHDn9jKc4wezwHgFgctwTudP7AMzQbaNIunAkEAttLZmb95DJxdP7iF44b/nMPT9cRZb2azHZPRy10dMQsf7xHVlg3j1ZDA1RwM2hnkhQS5PEp52DMp/xOcZ8ig4wJAAMfIuIYaf2LchaZyHPrHxd0aR2dr2eo+Rwv6PxyUoGswxARzwQtmQM+/j8gX5/Gbe/IWS9uYKcYO97uiDgqleQJBAM3jKodJlAkROx3kOEGmZBANJrtHJtl9WqW4l8vSrtV4QOqdjHrdnDB2OetPO39Pyx0KzWUzvYBfvjCn0zPGY4M=



#系统证书路径
sys.ssl.serverCert=/etc/pki/tls/certs/localhost.crt
sys.ssl.serverKey=/etc/pki/tls/private/localhost.key
sys.ssl.rootCA=/usr/local/openscep/etc/SCEPRootCA.pem

#iOS设备激活相关配置
ios.mdm.profile.templatePath=/data/uemx/sys/ios/profile/template
ios.mdm.profile.clientCertFile=/data/uemx/sys/ios/profile/clientcert/mdmclient.cert
ios.mdm.profile.tmpPath=/data/uemx/sys/ios/profile/tmp
ios.mdm.profile.scepUrl=https://{uem.ip}/cgi-bin/pkiclient.exe
ios.mdm.profile.activateUrl=https://{uem.ip}/mdm/app/ios/mdmprofile
ios.mdm.profile.checkinUrl=https://{uem.ip}/mdm/app/ios/checkin
ios.mdm.profile.serverUrl=https://{uem.ip}/mdm/app/ios/server

#短信网关配置
sms.provider=gxlt

#gxlt
sms.server.url=http://www.sms-cly.cn/smsSend.do
sms.server.user=gxlt
sms.server.password=maa7aw11
sms.template=\u5C0A\u656C\u7684\u5BA2\u6237\u60A8\u597D\uFF0C\u60A8\u7684\u9A8C\u8BC1\u7801\u4E3A{0},\u6709\u6548\u65F6\u95F4\u4E3A15\u5206\u949F\u3002\u5982\u975E\u672C\u4EBA\u64CD\u4F5C\uFF0C\u8BF7\u5FFD\u7565\u672C\u6D88\u606F\u3002\u3010\u542F\u8FEA\u56FD\u4FE1\u3011

#cmcc provider
sms.cmcc.url=http://mas.ecloud.10086.cn/app/sdk/login
sms.cmcc.user=digitalsee
sms.cmcc.password=Dgsee508
sms.cmcc.ecname=\u884C\u4E1A\u7EC8\u7AEF\u7BA1\u7406\u5E73\u53F0
sms.cmcc.sign=AEXe0WnX

#邮件模版文件保存的根目录
sys.conf.mail.templatePath=/data/uemx/sys/mail/template

#Whether to use PNServer service
sys.push.server.type=2
sys.conf.PNServer.server.use=true
sys.conf.PNServer.default.featureId=a5c77542-7892-4f8b-a6c3-d9f3bb9ecbd6
sys.conf.PNServer.apns.cer.default.featureId=c56d16b0-b8ac-4d03-b5ba-51d30e716171
sys.conf.PNServer.useSSL=true


#原sysconfig下面的配置信息，整体移过来未进行处理
#\u65F6\u949F\u670D\u52A1\u5668
sys.conf.time.timeServer=pool.ntp.org
sys.conf.app.inhouse.filePath=/data/app/
sys.conf.policy.cert.filePath=/data/app/
sys.conf.recommendapp.inhouse.filePath=/data/recommend/app/

#\u7F51\u7EDC\u8FDE\u63A5\u95F4\u9694\u65F6\u95F4(\u5206\u949F)
sys.conf.network.timeInterval=60
sys.conf.network.timeInterval.version=2012-06-07 01:02:12
sys.conf.network.timePeriod=7
sys.conf.network.validDay=1/2/3/4/5/6/7

sys.conf.ios.hostname=*************
sys.conf.fileserver.hostname=************

# MDM config heart time
sys.conf.mdm.heart.time=120
# MDM data upload period
sys.conf.mdm.dataUpload.period=10

#\u7EDB\u682B\u6690\u93C7\u5B58\u67CA\u93C3\u5815\u68FF\u95C2\u64AE\u6BA7(\u704F\u5FD4\u6902)
sys.conf.policy.update.timeInterval=8


# register & activate settings
sys.conf.base.removeData.activate.allow=1
sys.conf.mdm.info.license=
sys.conf.show.license.when.active=0
sys.conf.base.restriction.enableDictContactsWhitelist=0
sys.conf.base.restriction.enableContactsWhitelist=0
sys.conf.base.restriction.enableFamilyNumberContactsWhitelist=0


#定时任务相关
task.syncUser=false
#登录态
sys.deviceLoginPolicy=2

aliyun.oss.endpoint=https://oss-cn-hangzhou-zwynet-d01-a.internet.cloud-inner.zj.gov.cn/gbdd-oss-200g
aliyun.oss.accessKeyId=b94I25ReLZZ8DYOt
aliyun.oss.accessKeySecret=vEk0eC8U5YRC7MYXkpD0bMjgYIcAwW
aliyun.oss.bucketName=gbdd-oss-200g
aliyun.oss.bucketUrl=https://oss-cn-hangzhou-zwynet-d01-a.internet.cloud-inner.zj.gov.cn
#token过期时间：5分钟
aliyun.oss.tokenExpireTime=300000
dfs.type=OSS
dfs.uploadPath=mdm


dfs.appFileType=GOVDD