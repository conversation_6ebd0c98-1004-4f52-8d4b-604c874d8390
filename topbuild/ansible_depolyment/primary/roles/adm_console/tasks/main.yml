---
# 删除旧的安装路径
- name: Remove old install directory
  file:
    path: '{{ install_dir }}'
    state: absent

# 确保安装路径存在
- name: Ensure install directory exists
  file:
    path: '{{ install_dir }}'
    state: directory

# 拷贝安装包到目标机器的
- name: Copy adm-console package to {{ ansible_host }}
  unarchive:
    src: '{{ adm_console_pkg_url }}adm-console.tar.gz'
    dest: '{{ install_dir }}'
    remote_src: '{{ "yes" if (adm_console_pkg_url is not none) else  "no" }}'