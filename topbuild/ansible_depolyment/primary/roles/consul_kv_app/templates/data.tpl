#service config
iam_service_name=iam-service
iam_notify_service_name=iam-notify-service
gateway_service_name=gateway-service
open_platform_service_name=open-platform-service
adm_service_name=adm-service
ncm_service_name=ncm-service
mdm_service_name=mdm-service
uam_service_name=uam-service
acm_service_name=acm-service

# 短链接服务指定为ncm
shorturl_service_name=adm-service

#允许循坏依赖
spring.main.allow-circular-references=true

#服务发现时，使用的标签，默认为空
spring.cloud.consul.discovery.tags=

#swagger 相关配置
swagger.enable=false

#zipkin 是否开启
spring.zipkin.enabled=false
spring.zipkin.base-url=http://{{ zip_host }}:9001/
spring.zipkin.sender.type=web
spring.sleuth.sampler.probability=1

#是否开启监控中心
spring.boot.admin.client.enabled=false
spring.boot.admin.client.url=http://{{ admin_host }}:9002
#heapdump存在安全漏洞
#management.endpoints.web.exposure.exclude=heapdump
#management.endpoints.web.exposure.include=*
#management.endpoint.health.show-details=always
#是否开启JMS相关监控
#management.endpoint.jolokia.enabled=false

management.endpoints.enabled-by-default=false
#endpoints.enabled=false

更改feign.client配置
feign.client.default-config=default
feign.client.config.default.connect-timeout=3000
feign.client.config.default.read-timeout=10000
#如果是测试，可以设置成FULL
feign.client.config.default.logger-level=NONE

#dds config
dds.general.defaultDataBase=digitalsee
dds.general.multiTenant=true
dds.general.defaultSchema={{ db_name }}
dds.general.headerSeparator=.
dds.general.tenantHeaderName=tcode
dds.general.filterUrls=/dds-sample/actuator/health;/error;/actuator;/health;/uam/api/tenants;/uam/version
dds.general.filterUrlsSeparator=;
dds.general.activeDataBase=digitalsee
dds.general.enableGlobalInterceptor=false
dds.general.shardingDb=false

# 达梦 classpath:db/migration/dm mysql classpath:db/migration/prd,classpath:db/migration/dev
spring.flyway.locations=classpath:db/migration/prd,classpath:db/migration/dev


{% if database_type is defined and database_type == 'MYSQL' %}
dds.database.digitalsee.jdbcUrl=jdbc:mysql://{{ db_host }}:{{ db_port }}
dds.database.digitalsee.username={{ db_username }}
dds.database.digitalsee.password={{ db_pwd }}
dds.database.digitalsee.driverClassName=com.mysql.cj.jdbc.Driver
dds.database.digitalsee.urlParams=useSSL=false&useUnicode=true&characterEncoding=utf-8&serverTimezone=CTT&allowMultiQueries=true
{% endif %}


{% if database_type is defined and database_type == 'DM' %}
dds.database.digitalsee.jdbcUrl=jdbc:dm://{{ db_host }}:{{ db_port }}
dds.database.digitalsee.username={{ db_username }}
dds.database.digitalsee.password={{ db_pwd }}
dds.database.digitalsee.driverClassName=dm.jdbc.driver.DmDriver
dds.database.digitalsee.urlParams=schema=iam&compatibleMode=mysql&characterEncoding=UTF-8&useUnicode=true&useSSL=false&tinyInt1isBit=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai&columnNameCase=lower
{% endif %}


#指定连接维护的最小空闲连接数，当使用HikariCP时指定.
dds.database.digitalsee.minimum-idle=5
#spring最大的连接池数量
dds.database.digitalsee.maximum-pool-size=100

#mongodb相关配置
spring.data.mongodb.uri=mongodb://*************:27017/zsb

#Redis相关配置
system.redis.host={{ redis_host }}
system.redis.port={{ redis_port }}
system.redis.password={{ redis_pwd }}
system.redis.database={{ redis_db }}
redis.pool.maxIdle=10
redis.pool.maxActive=100
redis.pool.maxWait=10000
redis.pool.maxThreads=32
redis.pool.maxNettyThreads=64
redis.pool.testOnBorrow=false

spring.redis.host=${system.redis.host}
spring.redis.port=${system.redis.port}
spring.redis.database=${system.redis.database}
#spring.redis.password=${system.redis.password}
{% if redis_pwd is defined and redis_pwd != '' %}
spring.redis.password=${system.redis.password}
{% endif %}
spring.redis.jedis.pool.max-wait=${redis.pool.maxWait}
spring.redis.jedis.pool.max-active=${redis.pool.maxActive}
spring.redis.jedis.pool.max-idle=${redis.pool.maxIdle}
spring.redis.jedis.pool.min-idle=${redis.pool.maxIdle}
spring.redis.jedis.pool.test-on-borrow=${redis.pool.testOnBorrow}


#连接中心缓存配置
data.connect.redis.host=${system.redis.host}
data.connect.redis.port=${system.redis.port}
data.connect.redis.database=2
{% if redis_pwd is defined and redis_pwd != '' %}
data.connect.redis.password=${system.redis.password}
{% endif %}
data.connect.redis.pool.maxIdle=10
data.connect.redis.pool.maxActive=100
data.connect.redis.pool.maxWait=10000
data.connect.redis.pool.maxThreads=32
data.connect.redis.pool.maxNettyThreads=64
data.connect.redis.pool.testOnBorrow=false

#代理网关元数据
#网关组件认证用户名
proxy.server.username=proxy_dg
#网关组件认证密码
proxy.server.password=Digitalsee@2023
#网关组件server地址
proxy.server.socksProxyHost=*************
#网关组件server socks端口
proxy.server.socksProxyPort=10809
#不走socks代理的ip地址
proxy.server.socksNonProxyHosts=*************|127.0.0.1|localhost|*-service|*.com|*.cn

proxy.redis.host=${system.redis.host}
proxy.redis.port=${system.redis.port}
proxy.redis.database=3
{% if redis_pwd is defined and redis_pwd != '' %}
proxy.redis.password=${system.redis.password}
{% endif %}
proxy.redis.pool.maxIdle=10
proxy.redis.pool.maxActive=100
proxy.redis.pool.maxWait=10000
proxy.redis.pool.maxThreads=32
proxy.redis.pool.maxNettyThreads=64
proxy.redis.pool.testOnBorrow=false

sysconf.mq.type=rocketmq
#rocketmq config
sysconf.rocketmq.namesrv={{ rocketmq_host }}:{{ rocketmq_port }}
sysconf.rocketmq.accesskey={{ rocketmq_ack }}
sysconf.rocketmq.secretkey={{ rocketmq_sck }}
sysconf.rocketmq.consumeNums={{ rocketmq_consume_nums }}
sysconf.rocketmq.consumerId=GID_uep_
sysconf.rocketmq.producerId=GID_sec_producer
sysconf.rocketmq.instanceName={{ rocketmq_instance_name }}

sys.server.uep.outterUrl={{ uep_outter_url }}
sys.server.uep.outterTenantUrl={{ uep_outter_tenant_url }}
sys.server.uep.outterShortUrl={{ uep_outter_short_url }}
sys.server.uep.innerUrl={{ uep_inner_url }}
sys.server.uep.openPlatformUri=/open/login/oauth2/code/iam
sys.server.uep.gatewayPrefix={{ gateway_prefix }}
sys.server.uep.gatewayDomain=digitalsee.cn
sys.server.uep.gatewayUri=${sys.server.uep.gatewayPrefix}/login/oauth2/code/gateway
sys.server.uep.admUri=/adm/login/oauth2/code/iam
sys.server.uep.singleTenant=true
sys.server.uep.singleTenantId={{ default_tcode }}
sys.server.uep.debug=false

sys.server.uep.ldapOutterHost={{ ldap_outter_host }}
sys.server.uep.ldapInnerHost={{ ldap_inner_host }}
sys.server.uep.ldapOutterPort={{ ldap_outter_port }}
sys.server.uep.ldapsOutterPort={{ ldaps_outter_port }}
sys.server.uep.ldapInnerPort={{ ldap_inner_port }}
sys.server.uep.ldapsInnerPort={{ ldaps_inner_port }}

#按黑白名单暴露租户id，多个用逗号分隔，黑白名单都为空时，暴露所有租户
#白名单，优先暴露白名单
sys.server.uep.whiteTenants=
#黑名单，白名单为空时，屏蔽黑名单
sys.server.uep.blackTenants=

sys.tenant.defaultTcode={{ default_tcode }}

#流程引擎配置
#应用授权申请
#process.engine.flows[0].key=APP_AUTH
#process.engine.flows[0].name=应用授权申请
#process.engine.flows[0].code=PROC-F4049AAA-2FAA-4EDA-9031-36D51FFAEDE1
#process.engine.flows[0].fields[0].name=应用ID
#process.engine.flows[0].fields[0].attr=client_id
#process.engine.flows[0].fields[0].attrExt=
#process.engine.flows[0].fields[1].name=应用名称
#process.engine.flows[0].fields[1].attr=client_name
#process.engine.flows[0].fields[1].attrExt=
#process.engine.flows[0].fields[2].name=申请理由
#process.engine.flows[0].fields[2].attr=reason
#process.engine.flows[0].fields[2].attrExt=
#process.engine.flows[0].fields[3].name=开始时间
#process.engine.flows[0].fields[3].attr=start_date
#process.engine.flows[0].fields[3].attrExt=
#process.engine.flows[0].fields[4].name=结束时间
#process.engine.flows[0].fields[4].attr=end_date
#process.engine.flows[0].fields[4].attrExt=

#用户导入授权申请
#process.engine.flows[1].key=USER_IMPORT
#process.engine.flows[1].name=用户导入授权申请
#process.engine.flows[1].code=PROC-676DF944-7B10-45CB-BDD2-1A775875B359
#process.engine.flows[1].fields[0].name=批次号
#process.engine.flows[1].fields[0].attr=batch_no
#process.engine.flows[1].fields[0].attrExt=

process.engine.app.flows=[{"name":"应用名称","attr":"client_name"},{"name":"应用简介","attr":"client_brief"},{"name":"申请理由","attr":"reason"},{"name":"开始时间","attr":"start_date"},{"name":"结束时间","attr":"end_date"}]
process.engine.account.flows=[{"name":"类型","attr":"apply_type"},{"name":"人员数量","attr":"user_number"},{"name":"申请说明","attr":"apply_explain"},{"name":"人员列表","attr":"detail_url"},{"name":"开始时间","attr":"start_date"},{"name":"结束时间","attr":"end_date"}]

#发送工作通知配置文件
admin.message.url=dingtalk://dingtalkclient/action/openapp?corpid=%s&container_type=work_platform&app_id=0_%s&redirect_type=jump&redirect_url=
personal.message.markDown=\u60a8\u7684\u8d26\u53f7\u5c06\u4e8e\u0025\u0073\u5230\u671f\uff0c\u5230\u671f\u540e\u4e0d\u80fd\u518d\u767b\u5f55\uff0c\u70b9\u51fb\u81ea\u52a9\u7533\u8bf7\u7eed\u671f
admin.message.markDown=\u60a8\u7ba1\u7406\u7684\u90e8\u95e8\u4e0b\u6709\u0025\u0073\u4eba\u8d26\u53f7\u5373\u5c06\u5230\u671f\uff0c\u5df2\u901a\u77e5\u8be5\u4eba\u5458\u8fdb\u884c\u81ea\u52a9\u7533\u8bf7\u7eed\u671f\uff0c\u70b9\u51fb\u67e5\u770b\u5177\u4f53\u540d\u5355

#根据开始截至时间计算用户状态周期，并发送到期通知 每个小时执行一次
user.remind.cron=0 0 0/1 * * ?

#密码到期检测表达式
user.passwd.expire.cron=0 0 0/1 * * ?