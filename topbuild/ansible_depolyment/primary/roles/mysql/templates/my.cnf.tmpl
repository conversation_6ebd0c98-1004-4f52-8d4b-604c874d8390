# For advice on how to change settings please see
# http://dev.mysql.com/doc/refman/5.6/en/server-configuration-defaults.html

[mysqld]

# Remove leading # and set to the amount of RAM for the most important data
# cache in MySQL. Start at 70% of total RAM for dedicated server, else 10%.
# innodb_buffer_pool_size = 128M

# Remove leading # to turn on a very important data integrity option: logging
# changes to the binary log between backups.
# log_bin

# These are commonly set, remove the # and set as required.
# basedir = .....
# datadir = .....
# port = .....
# server_id = .....
# socket = .....

# Remove leading # to set options mainly useful for reporting servers.
# The server defaults are faster for transactions and fast SELECTs.
# Adjust sizes as needed, experiment to find the optimal values.
# join_buffer_size = 128M
# sort_buffer_size = 2M
# read_rnd_buffer_size = 2M 

{% if db_repl_role is defined and db_repl_role != 'none' %}
server-id={{ db_server_id }}
log-bin=mysql-bin
{% if db_repl_role == 'master1' %}
auto_increment_offset=1
{% else %}
auto_increment_offset=2
{% endif %}
auto_increment_increment=2
expire_logs_days=20
max_binlog_size=1000M
sync_binlog=1
log_slave_updates=1
{% endif %}

default_authentication_plugin=mysql_native_password
log-error=/var/log/mysqld.log
sql_mode=NO_ENGINE_SUBSTITUTION,STRICT_TRANS_TABLES 
skip_ssl
#skip-grant-tables
#skip-networking  

port = 3306
socket = /var/lib/mysql/mysql.sock
character-set-server=utf8
max_connections=1500
wait_timeout=3600
innodb_flush_log_at_trx_commit = 2
innodb_buffer_pool_size = 1024M
user=mysql

[client]
port = 3306
socket = /var/lib/mysql/mysql.sock
default-character-set=utf8

[mysql]
no-auto-rehash
default-character-set=utf8
