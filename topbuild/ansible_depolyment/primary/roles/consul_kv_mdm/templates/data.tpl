server.port=8091
server.servlet.context-path=/
spring.aop.proxy-target-class=true

{% if database_type is defined and database_type == 'DM' %}
dds.database.digitalsee.urlParams=schema=uemx&compatibleMode=mysql&characterEncoding=UTF-8&useUnicode=true&useSSL=false&tinyInt1isBit=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai&columnNameCase=lower
{% endif %}

swagger.enable=false

dds.general.defaultSchema=uemx
sys.enable.asyncDeviceActivate=false
#推送服务是否开启，默认不开启，如果对应的模块需要启动则进行开启（true），关闭为（false）
sys.conf.push.server.enable=false
#如果要启用推送token验证服务，则指定为true,否则指定为false，代表不开启推送token验证
sys.conf.push.tokencheck.enable=false;

sys.conf.push.type=onaction
#MDM推送服务组件的类型，目前有两个可选项，amdm(苹果本身的推送)和npns(云推送),onaction
sys.conf.push.mdmType=onaction

#jwt 相关配置
sys.jwt.key=mdm.digitalsee.cn,www.digitalsee.cn,uemx.digitalsee.cn,ThiSisAsecUrity,dontEllanYonEelse.
sys.jwt.expire=28800
sys.jwt.aud=IAM

sys.jwt.iam-iss=${sys.server.uep.outterUrl}
sys.jwt.iam-expire=300
sys.jwt.iam-aud=uem_native_app
sys.jwt.iam-key=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAjP55KadDgT7Ax1eFjDI9E6FccdCkUE2jp+mXgQeyYaIY8EW8+RrslAQezMShHJep1Vp9bl80ScuommQtKqOS7zHQJiQ+vZTbLNmCOjTTCx8mfJCzWdlRGxO9nl5gOHI9YY8nDzXRTrAjNsobSR1GUUL45QC/s1I1qseCWlR8Qixf7hjubft4bzD829DWsiN/0wrX021C+HzdhnS0j6uE6UHkYedhqwNykaYbcbAWaTOKdq7fbl3Q1iRltggIi8SlgncO8/ADaEnDmvuM0LDyD9WLs9wiB7kQ2OAmbj8+Gs6Q8X29/RctcZorDGHj5U4tT0DqEjkeXPG8Lb9kpE9tqQIDAQAB

#oauth2相关配置
#900天
sys.oauth2.refreshTokenExpiresIn=7776000
#900天
sys.oauth2.accessTokenExpiresIn=7776000
#tokenType=1为opaque,0为jwt
sys.oauth2.tokenType=1
sys.oauth2.tokenPolicy=1

#iOS设备激活相关配置
ios.mdm.profile.activateUrl={{ uep_outter_url }}/mdm/app/ios/mdmprofile
ios.mdm.profile.checkinUrl={{ uep_outter_url }}/mdm/app/ios/checkin
ios.mdm.profile.serverUrl={{ uep_outter_url }}/mdm/app/ios/server
ios.mdm.profile.mdmTopic={{ mdm_profile_topic }}
ios.mdm.profile.payloadDisplayName={{ mdm_profile_payload_display_name }}
ios.mdm.profile.payloadOrganization={{ mdm_profile_payload_org }}
ios.mdm.profile.payloadDescription={{ mdm_profile_payload_desc }}
ios.mdm.profile.payloadIdentifier={{ mdm_profile_payload_identifier }}

#scep服务器配置相关
scep.enabled={{ scep_enabled }}
scep.payloadIdentifier={{ secp_payload_identifier }}
scep.url={{ uep_outter_url }}/mdm/scep
scep.clientCertOrganization={{ secp_client_cert_org }}
scep.clientCertCommonName={{ secp_client_cert_cn }}
scep.clientCertIssuerCommonName={{ secp_client_cert_issuer_cn }}

#Whether to use PNServer service
sys.conf.pn.use=false

#定时任务相关
task.syncUser=true
task.syncUser.cron=0 10 3 * * ?

#登录态策略
# 0：默认状态，同一用户,即同一设备唯一登录状态
# 1：同一用户,即同一类型(platform)设备唯一登录状态
# 2：同一用户,(PC或者移动设备)设备唯一登录状态
# 3：同一用户所有设备唯一登录状态
sys.deviceLoginPolicy=2


sys.oauth2.client.permitAllUrls=/mdm/app/**,/mdm/basic/**,/mdm/scep/**

aliyun.oss.isBucketUrlContainName=1
#应用相关的文件文件系统类型为政务钉钉
dfs.appFileType=GOVDD
#阿里云OSS配置
aliyun.oss.endpoint={{ aliyun_oss_endpoint }}
aliyun.oss.accessKeyId={{ aliyun_oss_accessKeyId }}
aliyun.oss.accessKeySecret={{ aliyun_oss_accessKeySecret }}
aliyun.oss.bucketName={{ aliyun_oss_bucketName }}
aliyun.oss.bucketUrl={{ aliyun_oss_bucketUrl }}
#token过期时间：5分钟
aliyun.oss.tokenExpireTime=300000
aliyun.oss.autoRenameFile=false
dfs.type=OSS
dfs.uploadPath=mdm
sys.conf.tempFilePath=/tmp

aliyun.ocr.endpoint={{ aliyun_ocr_endpoint }}
aliyun.ocr.accessKeyId={{ aliyun_ocr_accessKeyId }}
aliyun.ocr.accessKeySecret={{ aliyun_ocr_accessKeySecret }}
ocr.type={{ ocr_type }}
ocr.enabled=FALSE