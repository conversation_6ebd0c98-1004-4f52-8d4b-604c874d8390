---
# 确保安装路径存在
- name: Ensure install directory exists
  file:
    path: '{{ install_dir }}'
    state: directory

# 拷贝安装包到目标机器的
- name: Copy iam-notify package to {{ ansible_host }}
  unarchive:
    src: '{{ iam_notify_pkg_url }}iam-notify.tar.gz'
    dest: '{{ install_dir }}'
    remote_src: '{{ "yes" if (iam_notify_pkg_url is not none) else  "no" }}'

# 替换consul服务器的主机地址
- name: Replace consul host with {{ consul_server_host }}
  lineinfile:
    path: '{{ install_dir }}/conf/application.properties'
    regexp: '^spring\.cloud\.consul\.host=(.*)$'
    line: 'spring.cloud.consul.host={{ consul_server_host }}'
    backrefs: yes

# 运行安装脚本
- name: Execute install script
  shell: '{{ install_dir }}/install.sh'

# 确保iam-notify服务已经启动
- name: Start iam-notify service
  service:
    name: iam-notify
    state: restarted
  become: true
