server.port=8090

#任务开启标志
sysconf.task.enabled=true
#连接器相关任务开启标志
sysconf.task.connector.enabled=true
#数据推送相关任务开启标志
sysconf.task.datapush.enabled=true


data.push.enable=true
data.push.maxCount=5
data.push.intervalTime=120
data.push.mq.delay=50
data.push.orgIncludeField=id;parent_ref_id;name;description;readonly;status;org_path;create_time;update_time;
data.push.userIncludeField=id;address;birthdate;email;gender;locale;email_verified;name;nickname;phone_number;phone_number_verified;picture;preferred_username;profile;status;telephone_number;username;website;zoneinfo;last_login;pwd_expiration_time;pwd_changed_time;created_mode;create_time;update_time;create_by;update_by;
#数据推送消息消费核心线程数
data.push.threadpool.corePoolSize=5
#数据推送消息消费最大线程数
data.push.threadpool.MaxPoolSize=10
#数据推送补偿任务核心线程数
data.push.job.threadpool.corePoolSize=5
#数据推送补偿任务最大线程数
data.push.job.threadpool.MaxPoolSize=10

#连接器任务周期每隔1分钟运行一次检查
data.pull.connector.cron=0 0/1 * * * ?
#自定义用户数据推送周期
data.push.job.app.cron=0 0/10 * * * ?
#数据补偿任务周期
data.push.job.mq.cron=0 0/20 * * * ?
#钉钉文件审计日志同步周期 30 分钟同步一次
audit.pull.cron=0 0/30 * * * ?

#以下为新增加的第三方平台账户支持相关配置

#企业微信相关配置
#是否开启企业微信
wework.enabled=true

#钉钉相关配置
#是否开启钉钉
dingding.enabled=true

#是否启用代理网关，true为启用，false为禁用
proxy.cloud.enable=true
