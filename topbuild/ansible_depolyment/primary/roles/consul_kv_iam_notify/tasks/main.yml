---
# 将配置信息导出为host-fact类型的变量
- name: Export some information to a 'host-fact' type variable
  set_fact:
    remote_tmp_dir: /tmp

# 拷贝配置文件到目标机器
- name: Copy config file to remote host
  template:
    src: data.tpl
    dest: '{{ remote_tmp_dir }}/consul_kv_{{ service_name }}'

# 将配置推送值consul服务器
- name: Put {{ service_name }} config to consul server
  uri:
    url: http://{{ consul_server }}:{{ consul_port }}/v1/kv/config/{{ service_name }}/data
    method: PUT
    src: '{{ remote_tmp_dir }}/consul_kv_{{ service_name }}'
    remote_src: yes

# 删除临时文件
- name: Clean the temporary directory
  file:
    dest: '{{ remote_tmp_dir }}/consul_kv_{{ service_name }}'
    state: absent