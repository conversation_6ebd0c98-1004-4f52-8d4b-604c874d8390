---
# 导出为host-fact类型的变量
- name: Export some information to a 'host-fact' type variable
  set_fact:
    remote_tmp_dir: /tmp

# 判断jdk是否已经安装
- name: Check jdk is installed or not
  command: java -version
  register: isInstalled
  ignore_errors: yes

# 如果jdk没有安装，则拷贝安装包到目标机器的/tmp目录下
- name: Copy jdk package to {{ ansible_host }}
  copy:
    src: '{{ jdk_rpm }}'
    dest: '{{ remote_tmp_dir }}'
  when: isInstalled.rc != 0 and remote_file_server is none
  register: isCopied

# 如果jdk没有安装，则拷贝安装包到目标机器的/tmp目录下
- name: Get jdk package from url to {{ ansible_host }}
  get_url:
    url: '{{ remote_file_server }}{{ jdk_rpm }}'
    dest: '{{ remote_tmp_dir }}'
  when: isInstalled.rc != 0 and remote_file_server is not none
  register: isCopied

# 拷贝成功的话，运行安装命令
- name: Install jdk with rpm package
  shell: yum -y localinstall {{ remote_tmp_dir }}/{{ jdk_rpm }}
  when: isInstalled.rc != 0


# 删除临时文件
- name: Clean the temporary directory
  file:
    dest: '{{ remote_tmp_dir }}/{{ jdk_rpm }}'
    state: absent

