server.port=8089

#session cookie设置
#cookie失效设置成七天
server.servlet.session.cookie.max-age=604800
server.servlet.session.cookie.name=IAMSSO
server.servlet.session.cookie.http-only=true
server.servlet.session.cookie.secure=false

#是否开启console debug模式
ucconsoleui.debug=false

swagger.enable=false

data.push.enable=true
#以下是iam-notify-service的配置，notify和iam集成部署时，需要引入
data.push.maxCount=5
data.push.intervalTime=120
data.push.mq.delay=50
data.push.job.app.cron=0 0/10 * * * ?
data.push.job.mq.cron=0 0/20 * * * ?
data.push.orgIncludeField=id;parent_ref_id;name;description;readonly;status;org_path;create_time;update_time;
data.push.userIncludeField=id;address;birthdate;email;gender;locale;email_verified;name;nickname;phone_number;phone_number_verified;picture;preferred_username;profile;status;telephone_number;username;website;zoneinfo;last_login;pwd_expiration_time;pwd_changed_time;created_mode;create_time;update_time;create_by;update_by;
#数据推送消息消费核心线程数
data.push.threadpool.corePoolSize=5
#数据推送消息消费最大线程数
data.push.threadpool.MaxPoolSize=10
#数据推送补偿任务核心线程数
data.push.job.threadpool.corePoolSize=5
#数据推送补偿任务最大线程数
data.push.job.threadpool.MaxPoolSize=10
data.pull.connector.cron=0 0/5 * * * ?

#以下为新增加的第三方平台账户支持相关配置
#企业微信相关配置
#是否开启企业微信
wework.enabled=true

#钉钉相关配置
#是否开启钉钉
dingding.enabled=true
dingding.corpId=fasdfasdfasfasdf
dingding.ssoSecret=AYz8bE
dingding.suiteKey=suitfasdfasdf
dingding.suiteSecret=ijJRhWOfasd
dingding.suiteId=19518005
dingding.token=41234g
dingding.aesKey=iBnkNethZCXQ
dingding.proxyServer=app82711.eapps.dingtalkcloud.com

#是否开启bit etl消息推送
message.provider.bit.enabled=true
#如果是其他provider，则需要实现对应的provider
# message.provider.XXX.enabled=true

#开启AKSK的开关
sysconf.aksk.enabled=1

#第一个应用的appKey
sysconf.aksk.apps[0].appKey=ncmapp
#第一个应用的appSecret
sysconf.aksk.apps[0].appSecret=ncmapp
#第一个应用的aesKey
sysconf.aksk.apps[0].aesKey=0qXE4q3dI9Q8C5NkrVgsMvaiKOypT9EDV7SldR9Ry6o
#第一个应用的签名token
sysconf.aksk.apps[0].token=siwqgrbyma27hi9ckb044ypp

#URL地址可以采用antPath对应的表达式，
sysconf.aksk.filters.etl_test.url=/iam/api/open/etl_message/test
#当前URL中请求的方式，如果有多个用，进行分隔
sysconf.aksk.filters.etl_test.methods=POST
#如果是签名则为SIGN,如果为AES加密，则配置成AES
sysconf.aksk.filters.etl_test.type=AES

sysconf.aksk.filters.etl_event.url=/iam/api/open/etl_message
sysconf.aksk.filters.etl_event.methods=POST
sysconf.aksk.filters.etl_event.type=AES

#是否启用代理网关，true为启用，false为禁用
proxy.cloud.enable=true

