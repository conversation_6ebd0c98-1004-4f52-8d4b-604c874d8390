
# ansible登录目标服务器的用户名
ansible_ssh_user: root

# ansible登录目标服务器的密码
ansible_ssh_pass: '<PERSON><PERSON>@0!6'

# ansible通过ssh登录目标服务器的端口
ansible_ssh_port: 22

# 数据库服务器地址
db_server_host: **************

# redis服务器地址
redis_server_host: **************

# rocketmq服务器地址
rocketmq_server_host: **************
# 对应于: -Xms1g
rocketmq_xms: 1
# 对应于： -Xmx1g
rocketmq_xmx: 1
# 对应于：-Xmn1g
rocketmq_xmn: 1

# consul服务器地址
consul_server_host: **************

# Web服务器地址
web_server_host: **************

# iam服务器地址
iam_server_host: **************

# uep映射到外网URL，正式环境中，应该设置为外网的域名
uep_outter_url: https://**************
uep_inner_url: https://**************

# iam-notify服务器地址
iam_notify_server_host: **************

# 开放平台的目标安装地址
openplatform_host: **************

# 网关服务器地址
gateway_server_host: **************
gateway_client_name: gateway
gateway_client_id: gateway
gateway_client_secret: 123456

# 网关在iam注册的redirectUri的域名和端口，正式环境中，应该设置为外网的域名
gateway_client_redirect_uri: https://**************

# etl服务器地址
etl_server_host: **************

# etl源数据库地址
etl_src_db_host: **************

# etl源数据库地址
etl_src_db_port: 3306

# etl源数据库地址
etl_src_db_name: bit

# etl源数据库地址
etl_src_db_user: uep

# etl源数据库地址
etl_src_db_pwd: Cyb2021@dgsee