apiVersion: apps/v1beta1
kind: Deployment
metadata:
  name: dingguard-mdm
  namespace: dingguard-test
spec:
  replicas: 1
  template:
    metadata:
      labels:
        uep-app: dingguard-mdm
    spec:
      containers:
      - name: dingguard-mdm
        image: cr.registry.inter.env10.shuguang.com/zwdd_test/dingguard-mdm:test
        imagePullPolicy: Always
        resources:
          limits:
            cpu: '2'
            memory: 4Gi
          requests:
            cpu: 500m
            memory: 512Mi
#        volumeMounts:
#        - name: logs
#          mountPath: /home/<USER>/logs
        lifecycle:
          preStop:
            exec:
              command:
                - /home/<USER>/app/bin/deregister_consul_docker.sh
        livenessProbe:
          exec:
            command:
              - /bin/sh
              - app/bin/liveness.sh
          initialDelaySeconds: 60
          periodSeconds: 10
          timeoutSeconds: 5
        readinessProbe:
          exec:
            command:
              - /bin/sh
              - app/bin/readiness.sh
          initialDelaySeconds: 60
          periodSeconds: 10
          timeoutSeconds: 5
        ports:
        - containerPort: 8091
          protocol: TCP
        env:
        - name: POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        - name: CONSUL_HOST
          value: "dingguard-consul"
        - name: CONSUL_PORT
          value: "8500"
        - name: CONSUL_DOMAIN
          value: "dingguard-consul.dingguard-test.svc.cluster.local"
        - name: PROFILE
          value: "test"
        - name: JAVA_AGENT
          value: "-javaagent:/home/<USER>/ArmsAgent/arms-bootstrap-1.7.0-SNAPSHOT.jar -Darms.licenseKey=1888074413486392@3b37d6e78918c51 -Darms.appName=dingguard-mdm-prod"
      restartPolicy: Always
#        lifecycle:
#          postStart:
#            exec:
#              command:
#                - /bin/sh
#                - install.sh
#      initContainers:
#      - name: consul-check
#        image: registry.nscloud.local/busybox:latest
#        command: ['sh', '-c', 'until nslookup consul; do echo waiting for consul; sleep 2; done;']
#      volumes:
#      - name: logs
#        hostPath:
#          path: /home/<USER>/logs
---
apiVersion: v1
kind: Service
metadata:
  name: dingguard-mdm
  namespace: dingguard-test
  labels:
    app: dingguard-mdm
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 8091
    targetPort: 8091
  selector:
    uep-app: dingguard-mdm
