apiVersion: apps/v1beta1
kind: Deployment
metadata:
  name: dingguard-mdm-test
  namespace: default
spec:
  replicas: 1
  template:
    metadata:
      labels:
        uep-app: dingguard-mdm-test
    spec:
      containers:
        - name: dingguard-mdm-test
          image: registry.nscloud.local/dingding/dingguard-mdm:20200109113709_27
          imagePullPolicy: Always
          ports:
            - containerPort: 8091
              protocol: TCP
          env:
            - name: POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: CONSUL_HOST
              valueFrom:
                fieldRef:
                  fieldPath: status.hostIP
            - name: CONSUL_PORT
              value: "8500"
            - name: CONSUL_DOMAIN
              value: "dingguard-consul-test.default.svc.cluster.local"
            - name: PROFILE
              value: "test"
          resources:
            limits:
              cpu: '2'
              memory: 4Gi
            requests:
              cpu: 500m
              memory: 512Mi
          lifecycle:
            preStop:
             exec:
               command:
               - /home/<USER>/app/bin/deregister_consul_docker.sh
          livenessProbe:
            exec:
              command:
                - /bin/bash
                - app/bin/liveness.sh
            initialDelaySeconds: 60
            periodSeconds: 10
            timeoutSeconds: 5
          readinessProbe:
            exec:
              command:
                - /bin/bash
                - app/bin/readiness.sh
            initialDelaySeconds: 60
            periodSeconds: 10
            timeoutSeconds: 5
