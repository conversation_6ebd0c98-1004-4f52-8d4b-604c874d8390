apiVersion: extensions/v1beta1
kind: Deployment
metadata:
  name: dingguard-slb
spec:
  replicas: 1
  template:
    metadata:
      labels:
        uep-app: dingguard-slb
    spec:
      containers:
      - name: dingguard-slb
        image: cr.registry.inter.env10.shuguang.com/zwdd_test/dingguard-nginx:test
        imagePullPolicy: Always
        resources:
          limits:
            cpu: '1'
            memory: 2Gi
          requests:
            cpu: 500m
            memory: 512Mi
        readinessProbe:
          exec:
            command:
              - /bin/sh
              - readiness.sh
          initialDelaySeconds: 60
          periodSeconds: 300
          timeoutSeconds: 5
        ports:
        - containerPort: 80
          protocol: TCP
        - containerPort: 443
          protocol: TCP
        env:
        - name: HTTP_PORT
          value: "80"
        - name: HTTPS_PORT
          value: "443"
        - name: CONSUL_HOST
          value: "dingguard-consul"
        - name: CONSUL_PORT
          value: "8500"
        lifecycle:
          postStart:
            exec:
              command:
                - /bin/sh
                - install.sh
#      initContainers:
#      - name: dp-consul-check
#        image: registry.nscloud.local/busybox:latest
#        command: ['sh', '-c', 'until nslookup consul; do echo waiting for consul; sleep 2; done;']
#      volumes:
#      - name: www
#        hostPath:
#          path: /home/<USER>/www
---
apiVersion: v1
kind: Service
metadata:
  name: dingguard-slb
  annotations:
    service.beta.kubernetes.io/alicloud-loadbalancer-address-type: intranet
spec:
  type: LoadBalancer
  externalTrafficPolicy: Cluster
  ports:
  - name: http
    port: 80
    targetPort: 80
  - name: https
    port: 443
    targetPort: 443
  selector:
    uep-app: dingguard-slb
