apiVersion: apps/v1beta1
kind: StatefulSet
metadata:
  name: dingguard-consul-daily
  namespace: dingguard-test
spec:
  serviceName: dingguard-consul-daily
  replicas: 3
  template:
    metadata:
      labels:
        app: dingguard-consul-daily
    spec:
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchExpressions:
                  - key: app
                    operator: In
                    values:
                      - dingguard-consul-daily
              topologyKey: kubernetes.io/hostname
      terminationGracePeriodSeconds: 10
      containers:
      - name: dingguard-consul-daily
        image: cr.registry.inter.env10.shuguang.com/zwdd_test/dingguard-consul:201912271143_10
        imagePullPolicy: Always
        args:
             - "agent"
             - "-server"
             - "-bootstrap-expect=3"
             - "-retry-join=dingguard-consul-daily-0.dingguard-consul-daily.$(NAMESPACE).svc.cluster.local"
             - "-retry-join=dingguard-consul-daily-1.dingguard-consul-daily.$(NAMESPACE).svc.cluster.local"
             - "-retry-join=dingguard-consul-daily-2.dingguard-consul-daily.$(NAMESPACE).svc.cluster.local"
             - "-ui"
             - "-data-dir=/consul/data"
             - "-bind=0.0.0.0"
             - "-client=0.0.0.0"
             - "-datacenter=dc1"
             - "-advertise=$(PODIP)"
             - "-domain=cluster.local"
        livenessProbe:
          exec:
            command: ["consul", "info"]
          initialDelaySeconds: 20
          periodSeconds: 10
          timeoutSeconds: 5
        readinessProbe:
          exec:
            command: ["consul", "info"]
          initialDelaySeconds: 20
          periodSeconds: 10
          timeoutSeconds: 5
        volumeMounts:
            - name: data
              mountPath: /consul/data/
        lifecycle:
#          postStart:
#            exec:
#              command:
#                - /bin/sh
#                - install.sh
#                - -p test
#                - -s 5
          preStop:
            exec:
              command:
                - /bin/sh
                - -c
                - consul leave
        resources:
          limits:
            cpu: '1'
            memory: 2Gi
          requests:
            cpu: 500m
            memory: 512Mi
        env:
            - name: PODIP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: DB_ADDR
              value: "rm-tx93n0567dd18tyrw.mysql.rds.ops.env10.shuguang.com"
            - name: DB_PORT
              value: "3306"
            - name: DB_PRIMARY
              value: "mdm"
            - name: DB_USERNAME
              value: "mdm"
            - name: DB_PWD
              value: "IWjhM3JNmDnNZgJQ"
            - name: REDIS_IP
              value: "r-tx998899f4cb4a34.redis.rds.ops.env10.shuguang.com"
            - name: REDIS_PORT
              value: "6379"
            - name: REDIS_DB
              value: "2"
            - name: REDIS_PWD
              value: "ZwddTestRedis1"
            - name: MQ_IP
              value: "http://MQ_INST_1491971638880353_BbekyfcI.cn-neimeng-env10-d01.mq.namesrv.inter.env10.shuguang.com"
            - name: MQ_PORT
              value: "9876"
            - name: MQ_ACK
              value: "smb2dFPjiwaXsK25"
            - name: MQ_SCK
              value: "Z0QigRK3ZzyLPMlaPwPUhnAx32spLj"
            - name: MQ_CONSUME_NUMS
              value: "20"
            - name: MQ_INSTANCE_NAME
              value: "zwdd-test-MQ"
            - name: USE_GROUP_ID
              value: "0"
            - name: SAG_IP
              value: "zwding-guard.alibaba-inc.com"
            - name: UEM_IP
              value: "zwding-guard.alibaba-inc.com"
            - name: DING_DOMAIN_NAME
              value: "gudao-openplatform-daily"
            - name: DING_EVENT_CALLBACK
              value: "http://dingguard-slb.dingguard-test.svc.cluster.local/mdm/basic/event"
            - name: DING_APP
              value: "DingGuard_xiong"
            - name: DING_ACCESS_KEY
              value: "DingGuard_xiong-90YiTodzuy2yMf"
            - name: DING_SECRET_KEY
              value: "6e2oqEkHKs54dS85Y3E7mbiPK51Xf50B5Rfo2lz3"
            - name: OSS_ENDPOINT
              value: "oss-cn-neimeng-env10-d01-a.ops.env10.shuguang.com/bucket-381-test"
            - name: OSS_BUCKET_URL
              value: "https://oss-cn-neimeng-env10-d01-a.inter.env10.shuguang.com"
            - name: OSS_BUCKET_NAME
              value: "bucket-381-test"
            - name: OSS_BUCKET_ISCONTAIN_BUCKETNAME
              value: "0"
            - name: OSS_ACK
              value: "aqrjjVPvkzw3xuOi"
            - name: OSS_SCK
              value: "RfvtTRAXHh0gTzhtv9RGXrgklcNBvd"
            - name: FILE_SERVER_URL
              value: "http://file.im-v381.svc"
            - name: FILE_SERVER_URL_OUTTER
              value: "https://zwding-daily-file.alibaba-inc.com"
            - name: FILE_SERVER_BIZ_TYPE
              value: "ZWDD"
            - name: SCEP_ENABLED
              value: "false"
            - name: SCEP_PAYLOAD_IDENTIFIER
              value: "com.govdd.profile.scep1"
            - name: SCEP_CLIENT_CERT_ORG
              value: "GOVDD"
            - name: SCEP_CLIENT_CERT_CN
              value: "clientCert"
            - name: SCEP_CLIENT_CERT_ISSUER_CN
              value: "GOVDD_ISSUER"
            - name: MDM_PROFILE_PAYLOAD_DISPLAY_NAME
              value: "GOVDD MDM Profile"
            - name: MDM_PROFILE_PAYLOAD_ORG
              value: "GOVDD"
            - name: MDM_PROFILE_PAYLOAD_DESC
              value: "MDM Profile Issued By GOVDD"
            - name: MDM_PROFILE_PAYLOAD_IDENTIFIER
              value: "com.govdd.mdm.profile"
            - name: MDM_PROFILE_TOPIC
              value: "com.apple.mgmt.External.f0960e8b-c23f-4f89-926b-31075243187b"
        ports:
        - containerPort: 8300
          name: server
        - containerPort: 8301
          name: serflan
        - containerPort: 8302
          name: serfwan
        - containerPort: 8443
          name: https-port
        - containerPort: 8500
          name: ui-port
        - containerPort: 8501
          name: opt-https-port
        - containerPort: 8502
          name: grfc-port
        - containerPort: 8600
          name: consuldns
  volumeClaimTemplates:
    - metadata:
        name: data
      spec:
        accessModes:
          - ReadWriteMany
        resources:
          requests:
            storage: 1Gi
#        selector:
#          matchLabels:
#            app: dingguard-consul
---
apiVersion: v1
kind: Service
metadata: 
   name: dingguard-consul-daily
   namespace: dingguard-test
   labels:   
     name: dingguard-consul-daily
spec:
  clusterIP: None
  ports:
  - name: serflan-tcp
    protocol: "TCP"
    port: 8301
    targetPort: 8301
  - name: serflan-udp
    protocol: "UDP"
    port: 8301
    targetPort: 8301
  - name: serfwan-tcp
    protocol: "TCP"
    port: 8302
    targetPort: 8302
  - name: serfwan-udp
    protocol: "UDP"
    port: 8302
    targetPort: 8302
  - name: server
    port: 8300
    targetPort: 8300
  - name: https
    port: 8443
    targetPort: 8443
  - name: http
    port: 8500
    targetPort: 8500
  - name: consuldns
    port: 8600
    targetPort: 8600
   selector:   
    app: dingguard-consul-daily
---
apiVersion: v1
kind: Service
metadata:
  name: dingguard-consul-daily-dns
  namespace: default
  labels:
    name: dingguard-consul-dns
spec:
  type: ClusterIP
  ports:
    - name: dns-tcp
      protocol: "TCP"
      targetPort: dns-tcp
      port: 53
    - name: dns-udp
      protocol: "UDP"
      targetPort: dns-udp
      port: 53
  selector:
    app: dingguard-consul-daily
