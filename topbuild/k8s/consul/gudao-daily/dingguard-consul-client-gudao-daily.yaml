apiVersion: extensions/v1beta1
kind: DaemonSet
metadata:
  name: dingguard-consul-client
  namespace: default
spec:
  selector:
    matchLabels:
      name: dingguard-consul-client
  template:
    metadata:
      labels:
        name: dingguard-consul-client
        app: dingguard-consul-client
    spec:
      containers:
        - name: dingguard-consul-client
          image: harbor.myk8s.ops.env45.com:32080/zwdd/dingguard-consul:20200109111501_57
          args:
            - "agent"
            - "-node=$(NODENAME)"
            - "-data-dir=/consul/data"
            - "-advertise=$(PODIP)"
            - "-retry-join=dingguard-consul-test-0.dingguard-consul-test.$(NAMESPACE).svc"
            - "-retry-join=dingguard-consul-test-1.dingguard-consul-test.$(NAMESPACE).svc"
            - "-retry-join=dingguard-consul-test-2.dingguard-consul-test.$(NAMESPACE).svc"
            - "-bind=0.0.0.0"
            - "-client=0.0.0.0"
            - "-domain=dingguard.consul"
          resources:
            limits:
              cpu: '1'
              memory: 2Gi
            requests:
              cpu: 500m
              memory: 256Mi
          env:
            - name: PODIP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: NODENAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
            - name: NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
          ports:
            - containerPort: 8300
              name: server
              protocol: "TCP"
            - containerPort: 8301
              name: serflan-tcp
              protocol: "TCP"
            - containerPort: 8301
              name: serflan-udp
              protocol: "UDP"
            - containerPort: 8302
              name: serfwan
              protocol: "TCP"
            - containerPort: 8500
              hostPort: 8500
              name: ui-port
              protocol: "TCP"
            - containerPort: 8502
              hostPort: 8502
              name: grfc-port
              protocol: "TCP"
            - containerPort: 8600
              name: consuldns-udp
              protocol: "UDP"
            - containerPort: 8600
              name: consuldns
              protocol: "TCP"