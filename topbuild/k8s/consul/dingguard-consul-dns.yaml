apiVersion: v1
kind: Service
metadata:
  name: dingguard-consul-test-dns
  namespace: default
  labels:
    name: dingguard-consul-test
spec:
  type: ClusterIP
  ports:
    - name: dns-tcp
      protocol: "TCP"
      targetPort: dns-tcp
      port: 53
    - name: dns-udp
      protocol: "UDP"
      targetPort: dns-udp
      port: 53
  selector:
    app: dingguard-consul-test
---
apiVersion: batch/v1
kind: Job
metadata:
  name: dns
spec:
  template:
    spec:
      containers:
        - name: dns
          image: anubhavmishra/tiny-tools
          command: ["dig",  "consul.service.consul"]
      restartPolicy: Never
  backoffLimit: 4