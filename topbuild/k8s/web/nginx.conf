user nginx;
worker_processes auto;
worker_rlimit_nofile 65535;
error_log /var/log/nginx/error.log;
pid /run/nginx.pid;

# Load dynamic modules. See /usr/share/doc/nginx/README.dynamic.
include /usr/share/nginx/modules/*.conf;

events {
    use epoll;
    worker_connections  65535;
}

http {
    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile            on;
    tcp_nopush          on;
    tcp_nodelay         on;
    keepalive_timeout   65;
    types_hash_max_size 2048;

    include             /etc/nginx/mime.types;
    default_type        application/octet-stream;

    # If we receive X-Forwarded-Proto, pass it through; otherwise, pass along the
    # scheme used to connect to this server
    map $http_x_forwarded_proto $proxy_x_forwarded_proto {
      default $http_x_forwarded_proto;
      ''      $scheme;
    }
    # If we receive X-Forwarded-Port, pass it through; otherwise, pass along the
    # server port the client connected to
    map $http_x_forwarded_port $proxy_x_forwarded_port {
      default $http_x_forwarded_port;
      ''      $server_port;
    }
    # If we receive Upgrade, set Connection to "upgrade"; otherwise, delete any
    # Connection header that may have been passed to this server
    map $http_upgrade $proxy_connection {
      default upgrade;
      '' close;
    }
    # Set appropriate X-Forwarded-Ssl header
    map $scheme $proxy_x_forwarded_ssl {
      default off;
      https on;
    }
    gzip_types text/plain text/css application/javascript application/json application/x-javascript text/xml application/xml application/xml+rss text/javascript;
    gzip on;
    gzip_comp_level 5;
    gzip_min_length 256;
       # HTTP 1.1 support
        proxy_http_version 1.1;
        proxy_buffering on;
        proxy_buffer_size          16k;
        proxy_buffers              4 256k;
        proxy_busy_buffers_size    256k;
        proxy_set_header Host $http_host;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection $proxy_connection;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $proxy_x_forwarded_proto;
        proxy_set_header X-Forwarded-Ssl $proxy_x_forwarded_ssl;
        #proxy_set_header X-Forwarded-Port 31841;
        proxy_set_header X-Forwarded-Port $proxy_x_forwarded_port;
        # Mitigate httpoxy attack (see README for details)
        proxy_set_header Proxy "";
        proxy_send_timeout      90;
        proxy_read_timeout      90;
        client_max_body_size 100m;

       upstream digital-service {
       #  将k8s集群环境的所有节点的ip添加到这
         server ************** weight=5;
         server ************** weight=5;
         server ************** weight=5;
       }

        server {
            listen 80;
            server_name _;

            location / {
                proxy_pass http://digital-service$request_uri;
                proxy_set_header Host $host;
                proxy_set_header X-Real-IP $remote_addr;
                proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
                proxy_set_header X-Forwarded-Proto $scheme;
             }

        include /etc/nginx/default.d/http.conf;
    }

# Settings for a TLS enabled server.
#     server {
#         listen 443 ssl;
#
#         add_header Strict-Transport-Security "max-age=31536000";
#         ssl_certificate "/etc/pki/tls/certs/*************.crt";
#         ssl_certificate_key "/etc/pki/tls/certs/*************.key";
#         ssl_protocols TLSv1.2 TLSv1.3;
#         ssl_ciphers 'ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-AES128-SHA256:ECDHE-RSA-AES128-SHA256:ECDHE-ECDSA-AES128-SHA:ECDHE-RSA-AES256-SHA384:ECDHE-RSA-AES128-SHA:ECDHE-ECDSA-AES256-SHA384:ECDHE-ECDSA-AES256-SHA:ECDHE-RSA-AES256-SHA:DHE-RSA-AES128-SHA256:DHE-RSA-AES128-SHA:DHE-RSA-AES256-SHA256:DHE-RSA-AES256-SHA:ECDHE-ECDSA-DES-CBC3-SHA:ECDHE-RSA-DES-CBC3-SHA:EDH-RSA-DES-CBC3-SHA:AES128-GCM-SHA256:AES256-GCM-SHA384:AES128-SHA256:AES256-SHA256:AES128-SHA:AES256-SHA:DES-CBC3-SHA:!DSS';
#         ssl_prefer_server_ciphers on;
#         ssl_session_timeout 5m;
#         ssl_session_cache shared:SSL:50m;
#         ssl_session_tickets off;
#
#         include /etc/nginx/default.d/http_entry.conf;
#     }
}