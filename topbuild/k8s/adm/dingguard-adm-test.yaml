apiVersion: apps/v1beta1
kind: Deployment
metadata:
  name: dingguard-adm
  namespace: dingguard-test
spec:
  replicas: 1
  template:
    metadata:
      labels:
        uep-app: dingguard-adm
    spec:
      containers:
      - name: dingguard-adm
        image: cr.registry.inter.env10.shuguang.com/zwdd_test/dingguard-adm:test
        imagePullPolicy: Always
        resources:
          limits:
            cpu: '2'
            memory: 4Gi
          requests:
            cpu: 500m
            memory: 512Mi
#        volumeMounts:
#        - name: logs
#          mountPath: /home/<USER>/logs
        livenessProbe:
          exec:
            command:
              - /bin/sh
              - app/bin/liveness.sh
          initialDelaySeconds: 60
          periodSeconds: 10
          timeoutSeconds: 5
        readinessProbe:
          exec:
            command:
              - /bin/sh
              - app/bin/readiness.sh
          initialDelaySeconds: 60
          periodSeconds: 10
          timeoutSeconds: 5
        ports:
        - containerPort: 8092
          protocol: TCP
        env:
        - name: POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        - name: CONSUL_HOST
          value: "dingguard-consul"
        - name: CONSUL_PORT
          value: "8500"
        - name: CONSUL_DOMAIN
          value: "dingguard-consul.dingguard-test.svc.cluster.local"
        - name: PROFILE
          value: "test"
        - name: JAVA_AGENT
          value: "-javaagent:/home/<USER>/ArmsAgent/arms-bootstrap-1.7.0-SNAPSHOT.jar -Darms.licenseKey=1491971638880353@e6eecf8256245a1 -Darms.appName=dingguard-adm-test"
      restartPolicy: Always
#        lifecycle:
#          postStart:
#            exec:
#              command:
#                - /bin/sh
#                - install.sh
#      initContainers:
#      - name: consul-check
#        image: registry.nscloud.local/busybox:latest
#        command: ['sh', '-c', 'until nslookup consul; do echo waiting for consul; sleep 2; done;']
#      volumes:
#      - name: logs
#        hostPath:
#          path: /home/<USER>/logs
---
apiVersion: v1
kind: Service
metadata:
  name: dingguard-adm
  namespace: dingguard-test
  labels:
    app: dingguard-adm
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 8092
    targetPort: 8092
  selector:
    uep-app: dingguard-adm
