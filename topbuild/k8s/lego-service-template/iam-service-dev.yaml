apiVersion: apps/v1
kind: Deployment
metadata:
  name: iam
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: iam
  template:
    metadata:
      labels:
        app: iam
    spec:
      containers:
      - name: iam
        image: registry.cn-beijing.aliyuncs.com/lego-saas/lego-iam:feature-dtalk-dm-dev_20201105151036_
        imagePullPolicy: Always
        resources:
          limits:
            cpu: '2'
            memory: 2Gi
          requests:
            cpu: 500m
            memory: 512Mi
        ports:
        - containerPort: 8080
          protocol: TCP
        env:
        - name: POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        - name: SPRING_CLOUD_CONSUL_HOST
          value: consul-client
        - name: CONSUL_PORT
          value: "8500"
        - name: PROFILE
          value: ""
      restartPolicy: Always
      imagePullSecrets:
      - name: lego-registry-secret
---
apiVersion: v1
kind: Service
metadata:
  name: iam-service
  namespace: default
  labels:
    app: iam-service
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 8080
    targetPort: 8080
  selector:
    app: iam
