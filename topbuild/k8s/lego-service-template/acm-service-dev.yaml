apiVersion: apps/v1
kind: Deployment
metadata:
  name: acm
  namespace: default
spec:
  replicas: 1
  selector:
    matchLabels:
      app: acm
  template:
    metadata:
      labels:
        app: acm
    spec:
      containers:
        - name: acm
          image: **************/lego-saas/lego-acm:demon_20240319182036_37
          imagePullPolicy: Always
          resources:
            limits:
              cpu: '2'
              memory: 2Gi
            requests:
              cpu: 500m
              memory: 512Mi
          ports:
            - containerPort: 8080
              protocol: TCP
          env:
            - name: POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: SPRING_CLOUD_CONSUL_HOST
              value: consul-client
            - name: CONSUL_PORT
              value: "8500"
            - name: PROFILE
              value: ""
      restartPolicy: Always
      imagePullSecrets:
        - name: lego-registry-secret
---
apiVersion: v1
kind: Service
metadata:
  name: acm-service
  namespace: default
  labels:
    app: acm-service
spec:
  type: ClusterIP
  ports:
    - name: http
      port: 8092
      targetPort: 8092
  selector:
    app: acm