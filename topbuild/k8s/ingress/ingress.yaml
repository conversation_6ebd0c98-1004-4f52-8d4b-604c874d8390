apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: app-ingress
  namespace: default
  annotations:
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/use-port-in-redirects: "true"
spec:
  ingressClassName: nginx
  rules:
    - http:
        paths:
          - path: /uc
            pathType: Prefix
            backend:
              service:
                name: web-app
                port:
                  number: 80
          - path: /login
            pathType: Prefix
            backend:
              service:
                name: web-app
                port:
                  number: 80
          - path: /portal
            pathType: Prefix
            backend:
              service:
                name: web-app
                port:
                  number: 80
          - path: /iam/tc(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: web-app
                port:
                  number: 80
          - path: /adm/download(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: web-app
                port:
                  number: 80
          - path: /adm(?!/admin)(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: web-app
                port:
                  number: 80
          - path: /iam(?!/tc)(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: iam-service
                port:
                  number: 8089
          - path: /mdm
            pathType: Prefix
            backend:
              service:
                name: mdm-service
                port:
                  number: 8091
          - path: /adm/admin
            pathType: Prefix
            backend:
              service:
                name: adm-service
                port:
                  number: 8092
          - path: /ncm
            pathType: Prefix
            backend:
              service:
                name: ncm-service
                port:
                  number: 8093
          - path: /acm
            pathType: Prefix
            backend:
              service:
                name: acm-service
                port:
                  number: 8095
          - path: /uam
            pathType: Prefix
            backend:
              service:
                name: uam-service
                port:
                  number: 8094