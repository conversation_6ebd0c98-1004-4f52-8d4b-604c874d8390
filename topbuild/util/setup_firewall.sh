#!/bin/sh
#set -x -e
#===============================================================================
#
#          FILE:  config_firewall.sh
# 
# 
#   DESCRIPTION:  script to config firewall rules on centos 7.2.
# 
#       OPTIONS:  ---
#  REQUIREMENTS:  ---
#          BUGS:  ---
#         NOTES:  ---
#        AUTHOR:  Feng Xi 
#       VERSION:  1.0
#       CREATED:  08/19/2016 11:03:36 AM CST
#      REVISION:  ---
#===============================================================================#
split_char=","
function split()
{
    OLD_IFS="$IFS"
    IFS=$2
    array=($1)
    IFS="$OLD_IFS"
    for each in ${array[*]}
    do
        echo $each
    done
}

# add new rules
function setup_rich_rules() {
  local port_array
  local ip_array
  if [[ -z "$2" ]]; then
    # allow all ip address
    port_array=`split $1 ${split_char}`
    for port in ${port_array[@]}; do
        allow_port ${port}
    done
  else
    # allow only the specified ip address
    port_array=`split $1 ${split_char}`
    for port in ${port_array[@]}; do
        ip_array=`split $2 ${split_char}`
        for ip in ${ip_array[@]}; do
            allow_ip_port ${port} ${ip}
        done
    done
  fi
}

# allow all for specified port
function allow_port() {
    local port="$1"
    # echo "allow_port: port = ${port}"
    if [[ -n "${port}" ]]; then
        firewall-cmd --zone=public --add-port="${port}"/tcp --permanent
    fi
}

# allow all for specified port
function allow_ip_port() {
    local port=$1
    local ip=$2
    # echo "allow_ip_port: port = ${port}, ip = ${ip}"
    if [[ -n "${port}" && -n "${ip}" ]]; then
        local rule="rule family=ipv4 source address=${ip} port protocol=tcp port=${port} accept"
        sudo firewall-cmd --add-rich-rule="${rule}" --permanent
    fi
}

function print_current(){
  echo "######################################################################"
  echo "#####################Current Firewall Policies########################"
  echo "######################################################################"
  sudo firewall-cmd --list-all
}

################## MAIN ###################
zone="public"
ports=""
allow_ip=""

while getopts p:-: OPT; do
    case ${OPT} in
    p)
        ports=${OPTARG}
    ;;
    -)
      LONG_OPTARG=""
      LONG_OPTARG="${OPTARG#*=}"

      if [[ -z "${LONG_OPTARG}" ]]; then
        echo "Illegal option --${OPTARG}, exit."
        exit 1
      fi

      case $OPTARG in
        port=?*)
          ports=${LONG_OPTARG}
        ;;
        allow-ip=?*)
          allow_ip=${LONG_OPTARG}
        ;;
        *)
          echo "Illegal option --${OPTARG}, exit." >&2;
          exit 1
         ;;
      esac
      ;;

    \?)
        echo "Illegal option --${OPTARG}, exit." >&2;
        exit 1
    esac
done
shift $(($OPTIND - 1))

sudo systemctl status firewalld 2>&1 1>/dev/null
if [[ $? -ne 0 ]]; then
  echo "firewalld is not running, try to start."
  sudo systemctl start firewalld.service
  sleep 5s
fi

sudo systemctl enable firewalld.service

setup_rich_rules ${ports} ${allow_ip}

echo "applying/re-loading new firewall policies"
sudo firewall-cmd --reload

print_current
