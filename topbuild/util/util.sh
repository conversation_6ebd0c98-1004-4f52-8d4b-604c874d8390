#!/bin/bash
set -e

SCRIPT=$(readlink -f $0)
SCRIPTPATH=$(dirname ${SCRIPT})
PROJECT_ROOT_PATH=${SCRIPTPATH}/../../

VERSION_MARJOR="0.0.1"

# 4 registry record in REGISTRY_NAME, the related info storage in REGISTRY_INFO.
# The column in REGISTRY_INFO represent: push_url, pull_url, repo_name, username, password, java_agent_label
#REGISTRY_NAME=(
#        "lego")
#REGISTRY_INFO=(
#        "registry.cn-beijing.aliyuncs.com registry.cn-beijing.aliyuncs.com lego-saas liuyang@digitalsee Dgsee508"
#        )

REGISTRY_NAME=(
        "digitalsee")
REGISTRY_INFO=(
        "192-168-50-250-9ob512ba5hxctd.ztna-dingtalk.com 192-168-50-250-9ob512ba5hxctd.ztna-dingtalk.com digitalsee-saas admin Harbor12345"
        )

function die() {
    echo "$@"

    type die_hook >/dev/null 2>&1
    if [[ $? -eq 0 ]]; then
      die_hook
    fi

    exit 1
}

####### Package mvn Begins #########
#mvn build.
function mvn::mvn_build() {
    local pom_path="${1}"
    local clean="${2:-${MVN_CLEAN}}"

    if [[ "${clean}" -eq "false" ]]; then
        mvn -f "${pom_path}" install -DBUILD_NUMBER=${BUILD_NUMBER}
    else
        mvn -f "${pom_path}" clean install -DBUILD_NUMBER=${BUILD_NUMBER}
    fi
}

#mvn build skip tests and doc.
function mvn::mvn_build_skip_tests_doc() {
    local pom_path="${1}"
    local clean="${2:-${MVN_CLEAN}}"

    echo "clean flag: ${clean} pom_path: ${pom_path}"
    if [[ "${clean}" == "false" ]]; then
        echo "not clean"
        mvn -T 1C -f "${pom_path}" install -DBUILD_NUMBER=${BUILD_NUMBER} -Dmaven.compile.fork=true -Dmaven.test.skip=true -Dmaven.javadoc.skip=true
    else
        echo "mvn -T 1C "${pom_path}" clean install -DBUILD_NUMBER=${BUILD_NUMBER}"
        mvn -T 1C -f "${pom_path}" clean install -DBUILD_NUMBER=${BUILD_NUMBER} -Dmaven.compile.fork=true -Dmaven.test.skip=true -Dmaven.javadoc.skip=true
    fi
}

########################################
####### Package mvn Ends #########
REGISTRY=""
PUBLIC_REGISTRY=""
REGISTRY_USER=""
REGISTRY_PWD=""

#Relase Major Version
MAJOR_IMG_VERSION=1.0.1
BUILD_NUMBER="${BUILD_NUMBER:-dev}"

#complete image version
IMG_REPO=${IMG_REPO:-dingding}
IMG_VERSION="${MAJOR_IMG_VERSION}.${BUILD_NUMBER}"
IMG_REGISTRY=""
#To support multi-registry
function docker::use_registry() {
    echo "Using registry ${IMG_REGISTRY}"
    local reg_index=-1
    for key in "${!REGISTRY_NAME[@]}"; do
        if [ "${IMG_REGISTRY}" == ${REGISTRY_NAME[$key]} ]; then
            reg_exist_flag="true"
            reg_index=${key}
            break
        fi
    done

    if [ "${reg_index}" -eq -1 ]; then
        IMG_REGISTRY=""
        echo "Registry ${IMG_REGISTRY} not available, only build and package"
    else
        local registry_info=${REGISTRY_INFO["${key}"]}
        read -r REGISTRY PUBLIC_REGISTRY IMG_REPO REGISTRY_USER REGISTRY_PWD <<< ${registry_info}
        echo "Used registry info: ${REGISTRY} ${PUBLIC_REGISTRY} ${IMG_REPO} ${REGISTRY_USER} ${REGISTRY_PWD}"
    fi
}

#docker login remote internal registry
function docker::login() {
    echo "docker login "${REGISTRY}" -u "${REGISTRY_USER}" -p "${REGISTRY_PWD}""
    docker login "${PUBLIC_REGISTRY}" -u "${REGISTRY_USER}" -p "${REGISTRY_PWD}"
}

#normalize img name and tag
#1. img(REQUIRED): docker img name
#2. tag(OPTIONAL): docker img tag, default using latest
function docker::normalize() {
    local img="$1"
    local tag="${2}"

    if [[ -z "${tag}" ]]; then
        echo "${img}"
    else
        echo "${img}:${tag}"
    fi
}

function docker::build() {
    local img="$1"
    local tag="${2}"
    local img_tag="$(docker::normalize $1 $2)"
    local app=${3}
    local docker_file="${4:-Dockerfile}"

    #docker build
    echo "docker build -f ${docker_file} -t ${img_tag} \
    --build-arg APP_NAME=${app} \
    --build-arg BUILD_NUMBER=${BUILD_NUMBER} --build-arg BUILD_TIMESTAMP=${BUILD_TIMESTAMP} \
    $(dirname ${docker_file})"

    docker build -f "${docker_file}" -t "${img_tag}" \
    --build-arg APP_NAME=${app} \
    --build-arg BUILD_NUMBER=${BUILD_NUMBER} --build-arg BUILD_TIMESTAMP=${BUILD_TIMESTAMP} \
    $(dirname ${docker_file})

    #tag as latest
    docker tag "${img_tag}" "${img}:latest"
}

#docker push to remote internal registry, and tag as latest
#Params:
#1. img(REQUIRED): docker img name
#2. tag(OPTIONAL): docker img tag, default using latest
function docker::push() {
    local img="$1"
    local tag="${2}"

    local img_tag="$(docker::normalize $img $tag)"
    docker tag "${img_tag}" "${REGISTRY}"/"${img_tag}"
    #docker tag "${img_tag}" "${REGISTRY}"/"${img}:latest"
    #tag as latest
    if [ -n "${PUSH_LATEST_TAG}" ]; then
      echo "Tag: ${PUSH_LATEST_TAG} for registry: ${REGISTRY_NAME}"
      docker tag "${img_tag}" "${REGISTRY}"/"${img}:${PUSH_LATEST_TAG}"
    fi

    echo "current BUILD_NUMBER: ${BUILD_NUMBER}"
    #push to remote
    if [[ "${BUILD_NUMBER}" =~ "_dev"  ]]; then
        echo "WARNING: dev build, do not push to registry."
    else
        #login into remote registry
        docker::login
        docker push "${REGISTRY}"/"${img_tag}"
        if [ -n "${PUSH_LATEST_TAG}" ]; then
          echo "Push: ${PUSH_LATEST_TAG} for registry: ${REGISTRY_NAME}"
          docker push "${REGISTRY}"/"${img}:${PUSH_LATEST_TAG}"
        fi
    fi
}

#docker build then push to remote
function docker::build_then_push() {
    local img="$1"
    local tag="${2}"
    local app="${3}"

    docker::build "${img}" "${tag}" "${app}"
    docker::push "${img}" "${tag}" "${app}"
}

#docker pull from remote internal registry
#Params:
#1. img(REQUIRED): docker img name
#2. tag(OPTIONAL): docker img tag, default using latest
function docker::pull() {
    local img_tag="$(docker::normalize $1 $2)"

    #login into remote registry
    #docker::login

    docker pull "${PUBLIC_REGISTRY}"/"${img_tag}"

    #docker tag "${REGISTRY}"/"${img_tag}" "${img_tag}"
}

#remove docker images
function docker::rmi() {
    local img="$1"
    local tag="${2}"
    local img_tag="$(docker::normalize $img $tag)"

    docker rmi "${IMG_REPO}/${img_tag}"
    docker rmi "${REGISTRY}/${IMG_REPO}/${img_tag}"
}

#docker save all image(tag and latest) wrapper
function docker::save_all() {
    local dst="${1}"
    local img="$2"
    local tag="${3}"

    local img_tag="$(docker::normalize $img $tag)"

    #tagged as latest as well
    local img_latest_tag="$(docker::normalize $img latest)"

    echo "####### saving img of ${IMG_REPO}/${img_tag} to ${dst}/${img}.tar ######"
    docker save -o "${dst}"/"${img}.tar" "${IMG_REPO}/${img_tag}" "${IMG_REPO}/${img_latest_tag}"

    echo "Done."
}

#docker save image wrapper
function docker::save() {
    local dst="$1"
    local img="$2"
    local tag="${3:-${IMG_VERSION}}"

    local img_tag="$(docker::normalize $img $tag)"

    echo "####### saving img of ${img_tag} to ${dst}/${img}.tar ######"
    docker save -o "${dst}"/"${img}.tar" "${IMG_REPO}/${img_tag}"

    echo "Done."
}

function docker::build_then_save() {
    local img="${1}"
    local tag="${2}"
    local docker_file="${3}"
    local dst="${4}"
    local app="${5}"

    docker::build "${img}" "${tag}" "${app}" "${docker_file}"
    echo "image: ${img} tag:${tag} app: ${app}"
    docker::save "${dst}" "${img}" "${tag}"
}

#get all tags for a image on remote registry
#Params:
#1. img(REQUIRED): docker img name
#2. repo(OPTIONAL): docker registry repo name

function docker::get() {
    local img="$1"
    local repo="${2:-tae}"
    curl --insecure --user "${REGISTRY_USER}:${REGISTRY_PWD}" https://${PUBLIC_REGISTRY}/v2/${repo}/${img}/tags/list
}

########################################
####### Package docker Ends #########

####### Package builder Begins #########

#clean build
function builder::clean() {
    cd "${SCRIPTPATH}"
    if [[ -d "${BUILD_DIR}" ]]; then
        rm -rf "${BUILD_DIR}"
    fi
}

#docker build and push
function builder::build_then_push() {
    cd "${BUILD_DIR}"
    docker::build_then_push "${IMG_REPO}/${IMG_NAME}" "${IMG_VERSION}"

    #clean up work dir
#    cd "${SCRIPTPATH}"
#    builder::clean
}

function builder::build_then_save_push() {
    cd "${BUILD_DIR}"
    local dst=${1}
    local docker_file=${2}
    local app=${3}
    local tag="${4:-${IMG_VERSION}}"
    local save="${5:-false}"

    docker::use_registry

    if [ -z "${IMG_REGISTRY}" -a "${save}" == false ]; then
        exit 0
    fi

    echo "----${IMG_VERSION}----"
    echo "build image: ${IMG_REPO}/${IMG_NAME}:${tag} Dockerfile: ${docker_file} APP_NAME=${app}"
    docker::build "${IMG_REPO}/${IMG_NAME}" "${tag}" "${app}" "${docker_file}"

    echo "Save images flag: ${save}"
    if [ "${save}" == true ]; then
        docker::save "${dst}" "${IMG_NAME}" "${tag}"
    fi
    echo "Push images to registry: ${IMG_REGISTRY}"
    if [ ! -z "${IMG_REGISTRY}" ]; then
        docker::push "${IMG_REPO}/${IMG_NAME}" "${tag}"
        docker::rmi "${IMG_NAME}" "${tag}"
    fi

}

#check if image sanity
function builder::check() {
    echo
    echo "#### checking img sanity of ${IMG_REPO}/${IMG_NAME} ####"

    docker::get "${IMG_NAME}" "${IMG_REPO}"
}

#build machine docker-engine cleanup
function docker::cleanup(){
    #stop and remove all containers
    docker stop $(docker ps -qa) 2>/dev/null
    docker rm $(docker ps -qa) 2>/dev/null

    #remove the images with name <none>
    docker rmi $(docker images | grep '^<none>' | awk '{print $3}') 2>/dev/null
}

function docker::images() {
    local img="$1"
    local tag="${2:-${IMG_VERSION}}"
    local img_tag="$(docker::normalize $img $tag)"
    echo `docker images -q ${IMG_REPO}/${img_tag}`
}

function build::archive_source() {
    local pakage_name=${1}
    cd ${BUILD_DIR}
    rm -rf "${BUILD_DIR}/../${pakage_name}.tar.gz"
    local source_tar="${BUILD_DIR}/../${pakage_name}.tar.gz"
    tar cvzfp ${source_tar} ./*
    echo "archive ${pakage_name} into ${source_tar}"
}

function build::archive_image(){
    echo "archive images"
    cd ${BUILD_DIR}

    local image_tar="${BUILD_DIR}/../${IMG_NAME}-image.tar.gz"
    tar cvzfp ${image_tar} ./*.tar
    rm -rf ./*.tar
}

function package::max_kv_version() {
  use_version=$1
  if [[ -n "${use_version}" && "${use_version}" != "-" ]]; then
    if [ ! -d ${CONSUL_KV_BASE_DIR}/${use_version} ]; then
      echo "Can not find consul_kv version: ${use_version}"
      exit 1
    fi
    echo "${use_version}"
  else
    max=0
    for dir in ${CONSUL_KV_BASE_DIR}/*; do
      if [ -d "${dir}" ]; then
        dir_name=`basename "${dir}"`
        if [[ ${dir_name} -ge ${max} ]]; then
          max=${dir_name}
        fi
      fi
    done
    echo "${max}"
  fi
}

########################################
####### Package builder Eegins #########
