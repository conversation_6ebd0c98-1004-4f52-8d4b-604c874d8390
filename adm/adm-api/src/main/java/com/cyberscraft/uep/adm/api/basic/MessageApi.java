package com.cyberscraft.uep.adm.api.basic;

import com.cyberscraft.uep.adm.constants.UrlConstant;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/***
 *
 * @date 2021-11-03
 * <AUTHOR>
 ***/
@RestController
@RequestMapping(value = UrlConstant.BASIC_URL_PREFIX, produces = MediaType.APPLICATION_JSON_VALUE)
@Api(value = "Basic 数据相关接口", tags = "Basic 数据处理 ")
public interface MessageApi {

    /****
     * POST数据到mdm服务器
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/message", method = RequestMethod.POST, produces = MediaType.TEXT_HTML_VALUE, consumes = {MediaType.ALL_VALUE})
    @ResponseBody
    @ApiOperation(response = String.class, nickname = "用于处理通用数据请求",
            value = "用于处理通用数据请求",
            notes = "用于处理通用数据请求")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "tenantId", value = "租户ID", dataType = "String", required = true, paramType = "form"),
            @ApiImplicitParam(name = "api", value = "请求的API地址", dataType = "String", required = true, paramType = "form"),
            @ApiImplicitParam(name = "method", value = "请求的API方式", dataType = "String", required = true, paramType = "form"),
            @ApiImplicitParam(name = "data", value = "请求的数据", dataType = "String", required = false, paramType = "form")
    })
    String reciveData(
            @RequestParam("tenantId") String tenantId,
            @RequestParam("api") String api,
            @RequestParam("method") String method,
            @RequestParam(value = "data", required = false) String data);

}
