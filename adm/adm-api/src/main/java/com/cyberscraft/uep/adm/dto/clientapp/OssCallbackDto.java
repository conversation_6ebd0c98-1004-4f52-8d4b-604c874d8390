package com.cyberscraft.uep.adm.dto.clientapp;


/**
 * <p>
 *     文件上传回调对象类
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2019-11-25 15:23
 */
public class OssCallbackDto implements java.io.Serializable {
    private static final long serialVersionUID = -1L;
    private String bucket;
    private String object;
    private String mimeType;
    private String size;
    private String fileId;
    private String tenantId;
    private String businessType;

    public String getBucket() {
        return bucket;
    }

    public void setBucket(String bucket) {
        this.bucket = bucket;
    }

    public String getObject() {
        return object;
    }

    public void setObject(String object) {
        this.object = object;
    }

    public String getMimeType() {
        return mimeType;
    }

    public void setMimeType(String mimeType) {
        this.mimeType = mimeType;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public String getFileId() {
        return fileId;
    }

    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    @Override
    public String toString() {
        return "OssCallbackDto{" +
                "bucket='" + bucket + '\'' +
                ", object='" + object + '\'' +
                ", mimeType='" + mimeType + '\'' +
                ", size='" + size + '\'' +
                ", fileId='" + fileId + '\'' +
                ", tenantId='" + tenantId + '\'' +
                ", businessType='" + businessType + '\'' +
                '}';
    }
}
