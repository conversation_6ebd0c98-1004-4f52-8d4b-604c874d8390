package com.cyberscraft.uep.adm.dto.sysuser;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * Date: 2020/6/10
 * description: 系统管理员对象
 *
 * <AUTHOR>
 */
public class AdminInfoDto {
    /**
     * 用户名
     */
    @NotBlank
    @Pattern(regexp = "admin", message = "loginId must be admin")
    private String loginId;

    /**
     * 真实姓名
     */
    @NotBlank
    private String name;

    /**
     * 电话
     */
    private String telephone;

    /**
     * 电子信箱
     */
    private String email;

    @NotBlank(message = "password is required")
    private String password;

    public String getLoginId() {
        return loginId;
    }

    public void setLoginId(String loginId) {
        this.loginId = loginId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }
}
