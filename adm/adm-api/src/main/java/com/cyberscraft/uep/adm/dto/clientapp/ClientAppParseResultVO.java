package com.cyberscraft.uep.adm.dto.clientapp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/***
 *
 * @date 2021-12-12
 * <AUTHOR>
 ***/
@ApiModel(value = "ClientAppParseResultVO", description = "客户端应用解释结果")
public class ClientAppParseResultVO implements Serializable {

    /***
     * 客户端解释结果，0未解释，1解释中，2解释完成
     */
    @ApiModelProperty(value = "客户端解释结果，0未解释，1解释中，2解释完成 参考:com.cyberscraft.uep.mdm.api.constant.ProgressStatus ", example = "0")
    private Integer result;

    /***
     * 解释进度消息
     */
    @ApiModelProperty(value = "解释进度消息 ")
    private String msg;

    /***
     * 应用信息，只有在result为2时有效
     */
    @ApiModelProperty(value = "解释完成的应用信息，只有在result为2时有效 ")
    private ClientAppInfoVO app;

    public Integer getResult() {
        return result;
    }

    public void setResult(Integer result) {
        this.result = result;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public ClientAppInfoVO getApp() {
        return app;
    }

    public void setApp(ClientAppInfoVO app) {
        this.app = app;
    }
}
