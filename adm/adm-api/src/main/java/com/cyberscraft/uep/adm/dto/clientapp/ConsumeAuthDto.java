package com.cyberscraft.uep.adm.dto.clientapp;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * Date: 2020/7/10
 * description:
 *
 * <AUTHOR>
 */
public class ConsumeAuthDto {

    @NotBlank
    private String tCode;

    @NotNull
    private Integer platform;

    @NotNull
    private Integer count;

    public String gettCode() {
        return tCode;
    }

    public void settCode(String tCode) {
        this.tCode = tCode;
    }

    public Integer getPlatform() {
        return platform;
    }

    public void setPlatform(Integer platform) {
        this.platform = platform;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }
}
