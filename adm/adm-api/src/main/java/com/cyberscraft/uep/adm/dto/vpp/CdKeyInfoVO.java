package com.cyberscraft.uep.adm.dto.vpp;

import io.swagger.annotations.ApiModelProperty;

/**
 * Created  by  <PERSON>evin.
 * Date: 2019/12/12 15:44
 * description:
 *
 * <AUTHOR>
 */
public class CdKeyInfoVO {

    @ApiModelProperty(value = "订单号，如：MTV2MHJKD8")
    private String orderId;

    @ApiModelProperty(value = "导入的当前客户端的兑换码总数", example = "0")
    private long totalNum;

    @ApiModelProperty(value = "当前客户端的兑换码已分配的数量", example = "0")
    private long assignedNum;

    @ApiModelProperty(value="最后同步时间")
    private Long lastUploadTime;

    public CdKeyInfoVO() {
    }

    public CdKeyInfoVO(String orderId, int totalNum, int assignedNum) {
        this.orderId = orderId;
        this.totalNum = totalNum;
        this.assignedNum = assignedNum;
    }

    public CdKeyInfoVO(String orderId, long totalNum, long assignedNum, Long lastUploadTime) {
        this.orderId = orderId;
        this.totalNum = totalNum;
        this.assignedNum = assignedNum;
        this.lastUploadTime = lastUploadTime;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public long getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(long totalNum) {
        this.totalNum = totalNum;
    }

    public long getAssignedNum() {
        return assignedNum;
    }

    public void setAssignedNum(long assignedNum) {
        this.assignedNum = assignedNum;
    }

    public Long getLastUploadTime() {
        return lastUploadTime;
    }

    public void setLastUploadTime(Long lastUploadTime) {
        this.lastUploadTime = lastUploadTime;
    }
}
