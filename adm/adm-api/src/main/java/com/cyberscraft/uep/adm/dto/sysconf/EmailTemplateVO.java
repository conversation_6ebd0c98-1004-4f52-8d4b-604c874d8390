package com.cyberscraft.uep.adm.dto.sysconf;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@ApiModel(value = "email_template", description = "response object for get, email templates operations")
public class EmailTemplateVO extends TemplateVO {
    @ApiModelProperty(value = "消息模板的主题，长度是[2-50]字符", required = true, example="hello")
    @NotNull(message = "message template title must not be null")
    @Size(min = 2, max=50, message = "message template title size shoud be [2, 50]")
    private String title;

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
}
