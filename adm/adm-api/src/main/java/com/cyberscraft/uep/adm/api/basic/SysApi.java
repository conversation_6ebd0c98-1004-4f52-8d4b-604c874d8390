package com.cyberscraft.uep.adm.api.basic;

import com.cyberscraft.uep.adm.constants.UrlConstant;
import io.swagger.annotations.Api;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.Map;

@RestController
@RequestMapping(value = UrlConstant.DISTRIBUTE_URL_PREFIX, produces = MediaType.APPLICATION_JSON_VALUE)
@Api(value = "系统相关配置", tags = "System info")
public interface SysApi {

    /**
     * 获取server端build版本
     * @return
     */
    @GetMapping(value = "/version")
    Map version();
}
