package com.cyberscraft.uep.adm.dto.user;

import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

public class LdapVO {
    /**
     * 配置id
     */
    private Long id;
    /**
     * ldap类型 ad or ldap
     */
    private String type;

    /**
     * ldap名称(作为ldap的根组)
     */
    private String rootName;

    /**
     * ldap服务器地址
     */
    private String url;

    /**
     * ldap入口BaseDN
     */
    private String base;

    /**
     * 用户匹配模式
     */
    private String userFilter;

    /**
     * 用户组匹配模式
     */
    private String ouFilter;

    /**
     * ldap管理员账号
     */
    private String userName;

    /**
     * ldap管理员密码
     */
    private String password;

    /**
     * 用户帐号
     */
    private String loginId;

    /**
     * 用户姓名
     */
    private String name;

    /**
     * 用户邮箱
     */
    private String mail;

    /**
     * 用户手机号
     */
    private String mobile;

    /**
     * 组名称
     */
    private String groupName = "ou";


    /**
     * 上次同步开始时间
     */
    private LocalDateTime lastSyncStartTime;

    /**
     * 上次同步结束时间
     */
    private LocalDateTime lastSyncTime;

    /**
     * 导入是否完成 0-完成 1-进行中
     */
    private Integer syncFlag = 0;

    /**
     * 上次同步用户\导入用户数
     */
    private Integer lastSyncUsers;

    /**
     * 上次同步用户\导入用户组数
     */
    private Integer lastSyncGroups;

    private String domain;
    private String loginFilter;


    /**
     * 冲突处理方式 0-不导入 1-直接覆盖
     */
    private Integer conflictHandle = 1;

    /**
     * 是否采用ldap认证 0-否 1-是
     */
    @NotNull
    @Range(min = 0, max = 1)
    private Integer ldapVerify = 1;

    /**
     * 自动同步周期（小时） 0-不自动同步
     */
    private Integer autoSyncInterval = 0;

    /**
     * 导入到的目标组id
     */
    private Long parentGroupId = -1L;

    /**
     * 导入到的目标组名称
     */
    private String parentGroupName;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getRootName() {
        return rootName;
    }

    public void setRootName(String rootName) {
        this.rootName = rootName;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getBase() {
        return base;
    }

    public void setBase(String base) {
        this.base = base;
    }

    public String getUserFilter() {
        return userFilter;
    }

    public void setUserFilter(String userFilter) {
        this.userFilter = userFilter;
    }

    public String getOuFilter() {
        return ouFilter;
    }

    public void setOuFilter(String ouFilter) {
        this.ouFilter = ouFilter;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getLoginId() {
        return loginId;
    }

    public void setLoginId(String loginId) {
        this.loginId = loginId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMail() {
        return mail;
    }

    public void setMail(String mail) {
        this.mail = mail;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public LocalDateTime getLastSyncStartTime() {
        return lastSyncStartTime;
    }

    public void setLastSyncStartTime(LocalDateTime lastSyncStartTime) {
        this.lastSyncStartTime = lastSyncStartTime;
    }

    public LocalDateTime getLastSyncTime() {
        return lastSyncTime;
    }

    public void setLastSyncTime(LocalDateTime lastSyncTime) {
        this.lastSyncTime = lastSyncTime;
    }

    public Integer getSyncFlag() {
        return syncFlag;
    }

    public void setSyncFlag(Integer syncFlag) {
        this.syncFlag = syncFlag;
    }

    public Integer getLastSyncUsers() {
        return lastSyncUsers;
    }

    public void setLastSyncUsers(Integer lastSyncUsers) {
        this.lastSyncUsers = lastSyncUsers;
    }

    public Integer getLastSyncGroups() {
        return lastSyncGroups;
    }

    public void setLastSyncGroups(Integer lastSyncGroups) {
        this.lastSyncGroups = lastSyncGroups;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public String getLoginFilter() {
        return loginFilter;
    }

    public void setLoginFilter(String loginFilter) {
        this.loginFilter = loginFilter;
    }

    public Integer getConflictHandle() {
        return conflictHandle;
    }

    public void setConflictHandle(Integer conflictHandle) {
        this.conflictHandle = conflictHandle;
    }

    public Integer getLdapVerify() {
        return ldapVerify;
    }

    public void setLdapVerify(Integer ldapVerify) {
        this.ldapVerify = ldapVerify;
    }

    public Integer getAutoSyncInterval() {
        return autoSyncInterval;
    }

    public void setAutoSyncInterval(Integer autoSyncInterval) {
        this.autoSyncInterval = autoSyncInterval;
    }

    public Long getParentGroupId() {
        return parentGroupId;
    }

    public void setParentGroupId(Long parentGroupId) {
        this.parentGroupId = parentGroupId;
    }

    public String getParentGroupName() {
        return parentGroupName;
    }

    public void setParentGroupName(String parentGroupName) {
        this.parentGroupName = parentGroupName;
    }
}
