package com.cyberscraft.uep.adm.dto.clientapp;

import com.cyberscraft.uep.common.enums.Platform;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/***
 *
 * @date 2021-12-12
 * <AUTHOR>
 ***/
@ApiModel(value = "ClientAppDetailDto", description = "客户端应用信息详情请求对像")
public class ClientAppDetailDto implements Serializable {

    /***
     * 应用ID
     */
    @ApiModelProperty(value = "应用ID")
    private String id;

    /**
     * 应用名称
     */
    @ApiModelProperty(value = "应用名称")
    private String appName;

    /**
     * 应用的包名
     */
    @ApiModelProperty(value = "应用的包名")
    private String pkgName;


    /**
     * 应用对应的平台类型，参考平台类型定义
     */
    @ApiModelProperty(value = "应用的平台类型")
    private Platform platform;

    /**
     * 应用状态,1启用，0禁用
     */
    @ApiModelProperty(value = "应用状态,1启用，0禁用")
    private Integer status;

    /**
     * 应用的说明，应用描述信息
     */
    @ApiModelProperty(value = "应用的说明，应用描述信息")
    private String comment;

    /**
     * 供应商
     */
    @ApiModelProperty(value = "供应商")
    private String company;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getPkgName() {
        return pkgName;
    }

    public void setPkgName(String pkgName) {
        this.pkgName = pkgName;
    }

    public Platform getPlatform() {
        return platform;
    }

    public void setPlatform(Platform platform) {
        this.platform = platform;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }


}
