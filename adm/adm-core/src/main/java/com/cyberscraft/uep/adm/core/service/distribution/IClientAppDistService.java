package com.cyberscraft.uep.adm.core.service.distribution;

import com.cyberscraft.uep.account.client.domain.*;
import com.cyberscraft.uep.adm.core.exception.AdmException;

public interface IClientAppDistService {

    public void sendSmsCode(DownloadSmsCodeRequest request) throws AdmException;

    /**
     * 短信验证
     * @param request
     * @return
     * @throws AdmException
     */
    public String verifySmsCode(DownloadSmsCodeRequest request) throws AdmException;

    /**
     * 帐号密码验证
     * @param request
     * @return
     * @throws AdmException
     */
    public String verifyUserPassword(UserPasswordRequest request) throws AdmException;

    /**
     * 提取码验证
     * @param request
     * @return
     * @throws AdmException
     */
    public String verifyExtractedCode(ExtractedCodeRequest request) throws AdmException;

    /**
     * 无校验
     * @param request
     * @return
     * @throws AdmException
     */
    public String verifyNone(NoneVerifyRequest request) throws AdmException;

    /**
     * 验证ticket
     * @param ticket
     * @return
     * @throws AdmException
     */
    public ThirdPartyTicket verifyTicket(String ticket) throws AdmException;
}
