package com.cyberscraft.uep.adm.core.dbo;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cyberscraft.uep.adm.core.entity.GroupEntity;
import com.cyberscraft.uep.adm.core.exception.AdmException;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 设备用户组表 服务类
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2019-08-28
 */
public interface GroupDBO extends IService<GroupEntity> {

    /***
     * 返回组ID及名称对应的Map
     * @param groupIds
     * @return
     * @throws AdmException
     */
    Map<Long, String> getGroupNamesMap(List<Long> groupIds, boolean containInvalid) throws AdmException;

    /***
     * 返回组ID及路径对应的Map
     * @param groupIds
     * @return
     * @throws AdmException
     */
    Map<Long, String> getGroupPathsMap(List<Long> groupIds) throws AdmException;

    /***
     * 根据组ID获取组及子组ID列表
     * @param groupIds
     * @return
     * @throws AdmException
     */
    List<Long> getGroupAndSubGroupIdsById(List<Long> groupIds) throws AdmException;


    /***
     * 获取有效的组ID列表
     * @param groupIds
     * @return
     * @throws AdmException
     */
    List<Long> getValidGroupIds(List<String> groupIds) throws AdmException;


    /**
     * 查询某个节点下面的子节点信息
     *
     * @param parentId
     * @param type
     * @param isSyncGoogleEnterprise
     * @return
     * @throws AdmException
     */
    List<GroupEntity> getChildGroupByParentId(Long parentId, String type, Integer isSyncGoogleEnterprise) throws AdmException;

    /**
     * 非管理员用户的节点Id集合
     *
     * @param loginId
     * @return
     * @throws AdmException
     */
    List<Long> getSysUserGroupIdByLoginId(String loginId) throws AdmException;

    /**
     * 获取所有状态有效的组实体集合
     *
     * @return
     * @throws AdmException
     */
    List<GroupEntity> getValidStatusGroup() throws AdmException;

    /***
     * 查询当前组代码下面的所有的组代码列表
     * @param parentId
     * @return
     * @throws AdmException
     */
    List<String> getSubGroupCodesByParentId(Long parentId) throws AdmException;


    /***
     * 查询当前组代码下面的所有的组代码列表
     * @param parentId
     * @param type
     * @return
     * @throws AdmException
     */
    List<String> getSubGroupCodesByParentId(Long parentId, String type) throws AdmException;

    /***
     * 根据父组织代码，得到所有的子组ID列表
     * @param groupCode
     * @return
     * @throws AdmException
     */
    List<Long> getGroupAndSubGroupIdsByPrentCode(String groupCode) throws AdmException;

    /***
     *
     * @param parentId
     * @return
     * @throws AdmException
     */
    List<GroupEntity> getSubGroupsByParentId(Long parentId) throws AdmException;


    /***
     * 得到不等于当前批次的组列表
     * @param type
     * @param batchNo
     * @return
     * @throws AdmException
     */
    List<GroupEntity> getNotEqBatchNoGroupsByType(String type, Long batchNo) throws AdmException;

    /****
     *
     * @param thirdGroupIds
     * @return
     * @throws AdmException
     */
    List<GroupEntity> getBasicGroupsByThirdGroupIds(String thirdGroupIds) throws AdmException;

    List<GroupEntity> getLdapGroupsForDelete(Long ldapConfigId, Integer type, Long syncBactchNo);

    /****
     *
     * @return
     * @throws AdmException
     */
    List<String> getGroupsCode() throws AdmException;

    /***
     * 重置用户组的状态
     * @param tgroupId
     * @param status
     * @throws AdmException
     */
    void setStatusById(Long tgroupId, Integer status) throws AdmException;

    /**
     * 获取
     * @return
     * @throws AdmException
     */
    List<String> getOldSyncRootGroupCode(Long syncConnectorId) throws AdmException;

    /****
     * 根据第三方平台同步id和组id，获取组信息
     * @param syncConnectorId
     * @param thirdGroupId
     * @return
     */
    GroupEntity getGroupBySyncConnectorId(Long syncConnectorId, String thirdGroupId) throws AdmException;

}
