package com.cyberscraft.uep.adm.core.service.invitation;

import com.cyberscraft.uep.adm.dto.invitation.InvitationRecordVO;
import com.cyberscraft.uep.adm.dto.invitation.SendToTargetsDto;
import com.cyberscraft.uep.common.dto.QueryPage;
import com.cyberscraft.uep.common.util.query.QueryCondition;

public interface IInvitationService {
    /**
     * 发送邀请短信
     * @param dto
     * @return
     */
    void sendSMS(SendToTargetsDto dto);

    /**
     * 发送邀请邮件
     * @param dto
     * @return
     */
    void sendEmail(SendToTargetsDto dto);

    /**
     * 分页查询邀请记录
     * @param queryCondition
     * @return
     */
    QueryPage<InvitationRecordVO> searchInvitationRecord(QueryCondition queryCondition);
}
