package com.cyberscraft.uep.adm.core.service.vpp.transfer;

import com.cyberscraft.uep.adm.core.domain.AppSimpleInfo;
import com.cyberscraft.uep.adm.core.entity.VppStoreEntity;
import com.cyberscraft.uep.adm.dto.vpp.AppInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * vpp相关vo与entity的转换
 */
@Mapper(componentModel = "spring")
public interface IVppTransfer {

    IVppTransfer INSTANCE = Mappers.getMapper(IVppTransfer.class);

    /**
     * entityToAppInfoVO
     * @param entity
     * @return
     */
    AppInfoVO entityToAppInfoVO(VppStoreEntity entity);

    @Mappings({
            @Mapping(source = "adamId", target = "appid"),
            @Mapping(source = "appSize", target = "size"),
            @Mapping(source = "osVersion", target = "osversion"),
            @Mapping(source = "pkgName", target = "pkgname")
    })
    AppSimpleInfo entityToAppSimpleInfo(VppStoreEntity entity);
}


