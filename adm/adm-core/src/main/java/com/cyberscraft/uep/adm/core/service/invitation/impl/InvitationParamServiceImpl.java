package com.cyberscraft.uep.adm.core.service.invitation.impl;

import com.cyberscraft.uep.adm.constants.InvitationChannel;
import com.cyberscraft.uep.adm.core.domain.InvitationParam;
import com.cyberscraft.uep.adm.core.service.invitation.IInvitationParamHandlerService;
import com.cyberscraft.uep.adm.core.service.invitation.IInvitationParamService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;

@Service
public class InvitationParamServiceImpl implements IInvitationParamService {
    private final Logger logger = LoggerFactory.getLogger(getClass());
    @Resource
    private List<IInvitationParamHandlerService> handlers;

    private HashMap<String, IInvitationParamHandlerService> HANDLER_MAP = new HashMap<>();

    @PostConstruct
    private void buildSenderHandlerMap(){
        for (final IInvitationParamHandlerService handler: handlers) {
            InvitationChannel channel = handler.supportChannel();
            String key = channel.name();
            HANDLER_MAP.put(key, handler);
        }
        logger.info("current InvitationParamHandler size={}, list={}", handlers.size(), handlers);
    }

    /***
     * 根据渠道，查找响应到处理服务
     * @param channel
     * @return
     */
    private IInvitationParamHandlerService findHandler(InvitationChannel channel) {
        String key = channel.name();
        return HANDLER_MAP.get(key);
    }

    @Override
    public InvitationParam gen(InvitationChannel channel) {
        IInvitationParamHandlerService handlerService = findHandler(channel);
        if(handlerService != null) {
            return handlerService.gen();
        }else {
            logger.warn("Not found handler for channel {}", channel.name());
        }
        return null;
    }
}
