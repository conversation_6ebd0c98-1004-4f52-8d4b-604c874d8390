package com.cyberscraft.uep.adm.core.service.user.dingtalk.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
public class DingTalkUserList {
    @JsonProperty("userlist")
    private List<DingTalkUserInfo> userList;

    public List<DingTalkUserInfo> getUserList() {
        return userList;
    }

    public void setUserList(List<DingTalkUserInfo> userList) {
        this.userList = userList;
    }
}
