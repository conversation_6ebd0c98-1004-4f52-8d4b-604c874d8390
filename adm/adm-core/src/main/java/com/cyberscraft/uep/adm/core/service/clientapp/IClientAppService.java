package com.cyberscraft.uep.adm.core.service.clientapp;

import com.cyberscraft.uep.account.client.domain.ThirdPartyTicket;
import com.cyberscraft.uep.adm.constants.DistChannel;
import com.cyberscraft.uep.adm.core.entity.ClientAppEntity;
import com.cyberscraft.uep.adm.core.exception.AdmException;
import com.cyberscraft.uep.adm.dto.clientapp.*;
import com.cyberscraft.uep.common.enums.Platform;

import java.util.List;

/***
 * 客户端应用服务接口
 * @date 2021-12-12
 * <AUTHOR>
 ***/
public interface IClientAppService {

    /****
     *
     * @param platform
     * @param pkgName
     * @return
     * @throws AdmException
     */
    ClientAppEntity getClientAppByPlatformAndPkg(Integer platform, String pkgName) throws AdmException;

    /****
     * 得到平台当前活跃的，有效的客户端应用
     * @param platform
     * @return
     * @throws AdmException
     */
    ClientAppEntity getPlatformCurrentActivateClientApp(int platform) throws AdmException;

    /****
     * 得到平台当前活跃的，有效的客户端应用
     * @param platform
     * @return
     * @throws AdmException
     */
    ClientAppInfoVO getPlatformCurrentActivateClientAppInfo(int platform) throws AdmException;

    /****
     * 获取应用详情
     * @param id
     * @return
     * @throws AdmException
     */
    ClientAppInfoVO getAppDetail(Long id) throws AdmException;

    /****
     * 保存客户端应用信息
     * @param dto
     * @throws AdmException
     */
    Long save(ClientAppInfoDto dto) throws AdmException;

    /****
     * 保存客户端应用信息
     * @param dto
     * @throws AdmException
     */
    Long update(ClientAppDetailDto dto) throws AdmException;

    /***
     * 进行客户端应用的解析
     * @param dto
     * @return
     * @throws AdmException
     */
    Long parseClientApp(ClientAppParseDto dto) throws AdmException;


    /***
     * 获取客户端解析结果
     * @param resultId
     * @return
     * @throws AdmException
     */
    ClientAppParseResultVO getClientAppParseResultInfo(Long resultId) throws AdmException;

    /***
     * App文件上传
     * @param fileUploadDto
     * @return
     * @throws AdmException
     */
    String uploadApp(FileUploadDto fileUploadDto) throws AdmException;

    /**
     * 文件上传回调处理
     * @param ossCallbackDto
     * @return
     * @throws AdmException
     */
    boolean uploadAppCallback(OssCallbackDto ossCallbackDto) throws AdmException;

    /**
     * 获取主推应用的下载地址url
     * @return
     */
    String getMainAppDownloadUrl(Platform platform, DistChannel channel, String target, ThirdPartyTicket ticket);

    /**
     * 获取应用的详细信息
     * @return
     */
    ClientAppInfoVO getAppInfo(Long appId);

    List<ClientAppDetailVO> listAppInfo(Platform platform);
    List<ClientAppVersionDetailVO> listAppVersion(Long appId);
    boolean selectAppVersion(Long id,String status) ;
    boolean deleteAppVersion(Long id) ;

    /**
     * 获取当前已经发布的应用列表
     * @param platform
     * @param channel
     * @return
     */
    List<ClientSimpleInfoVO> getDistributedAppList(Platform platform, DistChannel channel, ThirdPartyTicket thirdPartyTicket);

    /**
     * 增加应用版本的下载计数
     * @param appVersionId
     * @param loginId
     * @return
     */
    void addDownloadCount(Long appVersionId, String loginId);

    /**
     * 消耗license
     * @param platform
     * @param count
     * @return
     */
    void consumeAuth(int platform, int count);
}
