package com.cyberscraft.uep.adm.core.service.basic.parser;

import com.cyberscraft.uep.adm.constants.Constant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/***
 *
 * @date 2021-12-14
 * <AUTHOR>
 ***/
public abstract class AbastractFileParser {

    /****
     *
     */
    protected final static Logger LOG = LoggerFactory.getLogger(AbastractFileParser.class);


    /**
     * 内置应用包名
     */
    protected static List<String> builtinPkgName;

    static {
        if (null == builtinPkgName) {
            builtinPkgName = new ArrayList<String>();
            //安全浏览器
            builtinPkgName.add(Constant.BUILTIN_SECUREBROWSER_APKAPP_PKGNAME);
            //安全浏览器-ios
            builtinPkgName.add(Constant.BUILTIN_SECUREBROWSER_IOSAPP_PKGNAME);
            //杀毒
            builtinPkgName.add(Constant.BUILTIN_ANTIVIRUS_APP_PKGNAME);
            //安全容器-ios
            builtinPkgName.add(Constant.BUILTIN_SECURECONTAINER_IOSAPP_PKGNAME);
            //安全容器-apk
            builtinPkgName.add(Constant.BUILTIN_SECURECONTAINER_APKAPP_PKGNAME);
            //安全桌面
            builtinPkgName.add(Constant.BUILTIN_SECUREDESKTOP_APP_PKGNAME);
            //远程控制
            builtinPkgName.add(Constant.BUILTIN_REMOTE_CTRL_PKGNAME);
            //MCM
            builtinPkgName.add(Constant.BUILTIN_MCM_APP_PKGNAME);
            //MCM
            builtinPkgName.add(Constant.BUILTIN_NEWMCM_APP_PKGNAME);
        }
    }

    /***
     *
     * @param file
     * @param filePath
     * @return
     * @throws Exception
     */
    protected Long getAppSize(File file, String filePath) {
        try {
            Long len = file.length();
            return len.longValue();
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
        return 0L;
    }
}
