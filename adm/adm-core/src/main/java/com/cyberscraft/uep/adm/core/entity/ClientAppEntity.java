package com.cyberscraft.uep.adm.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 客户端应用信息表
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2020-05-21
 */
@TableName("t_client_app")
public class ClientAppEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 应用名称
     */
    @TableField("appName")
    private String appName;

    /**
     * 应用的包名
     */
    @TableField("pkgName")
    private String pkgName;

    /**
     * 平台类型，参考系统平台类型定义
     */
    @TableField("platform")
    private Integer platform;

    /**
     * 1启用，0禁用
     */
    @TableField("status")
    private Integer status;

    /**
     * 是否主推应用,1:是，0：否
     */
    @TableField("mainApp")
    private Integer mainApp;

    /**
     * 使用自己资源文件路径,自定义下64位下载地址，macOs环境表示m系列芯片
     */
    @TableField("downloadUrl64")
    private String downloadUrl64;
    /**
     * 使用自己资源文件路径,自定义32位下载地址，macOs环境表示intel芯片
     */
    @TableField("downloadUrl32")
    private String downloadUrl32;

    /**
     * 应用说明描述
     */
    @TableField("remark")
    private String remark;

    /**
     * 供应商
     */
    @TableField("company")
    private String company;

    /**
     * 最近上传时间
     */
    @TableField("uploadTime")
    private LocalDateTime uploadTime;

    /**
     * 创建时间
     */
    @TableField("createTime")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("updateTime")
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    @TableField("createBy")
    private String createBy;

    /**
     * 更新人
     */
    @TableField("updateBy")
    private String updateBy;

    /**
     * 租户ID
     */
    @TableField("tenantId")
    private String tenantId;

    /**
     * ICON
     */
    @TableField("icon")
    private String icon;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getPkgName() {
        return pkgName;
    }

    public void setPkgName(String pkgName) {
        this.pkgName = pkgName;
    }

    public Integer getPlatform() {
        return platform;
    }

    public void setPlatform(Integer platform) {
        this.platform = platform;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getMainApp() {
        return mainApp;
    }

    public void setMainApp(Integer mainApp) {
        this.mainApp = mainApp;
    }

    public String getDownloadUrl64() {
        return downloadUrl64;
    }

    public void setDownloadUrl64(String downloadUrl64) {
        this.downloadUrl64 = downloadUrl64;
    }

    public String getDownloadUrl32() {
        return downloadUrl32;
    }

    public void setDownloadUrl32(String downloadUrl32) {
        this.downloadUrl32 = downloadUrl32;
    }

//    public String getComment() {
//        return comment;
//    }
//
//    public void setComment(String comment) {
//        this.comment = comment;
//    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public LocalDateTime getUploadTime() {
        return uploadTime;
    }

    public void setUploadTime(LocalDateTime uploadTime) {
        this.uploadTime = uploadTime;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    @Override
    public String toString() {
        return "ClientAppEntity{" +
                "id=" + id +
                ", appName='" + appName + '\'' +
                ", pkgName='" + pkgName + '\'' +
                ", platform=" + platform +
                ", status=" + status +
                ", mainApp=" + mainApp +
                ", downloadUrl64='" + downloadUrl64 + '\'' +
                ", downloadUrl32='" + downloadUrl32 + '\'' +
                ", remark='" + remark + '\'' +
                ", company='" + company + '\'' +
                ", uploadTime=" + uploadTime +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", createBy='" + createBy + '\'' +
                ", updateBy='" + updateBy + '\'' +
                ", tenantId='" + tenantId + '\'' +
                ", icon='" + icon + '\'' +
                '}';
    }
}
