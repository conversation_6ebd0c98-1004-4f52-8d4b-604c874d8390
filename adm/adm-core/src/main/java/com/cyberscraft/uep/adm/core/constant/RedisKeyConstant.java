package com.cyberscraft.uep.adm.core.constant;

public class RedisKeyConstant {
    public static final String CDKEY_QUEUE_PREFIX = "CDKEY_QUEUE";

    /**
     * cdkey队列，方便消费者消费
     * @param tenantId
     * @return
     */
    public static String getCdkeyQueueKey(String tenantId){
        return String.format("%s:%s", CDKEY_QUEUE_PREFIX, tenantId);
    }


    /**
     * 生产者生产时的同步锁，防止多个生产者重复生产
     */
    public static final String CDKEY_PRODUCER_LOCK_PREFIX = "CDKEY_PRODUCER_LOCK";
    public static String getCdkeyProducerLock(String tenantId){
        return String.format("%s:%s", CDKEY_PRODUCER_LOCK_PREFIX, tenantId);
    }

    /**
     * 记录当前生产者生产的位置
     */
    public static final String CDKEY_PRODUCER_POSITION_PREFIX = "CDKEY_PRODUCER_POSITION";
    public static String getCdkeyProducerPosition(String tenantId){
        return String.format("%s:%s", CDKEY_PRODUCER_POSITION_PREFIX, tenantId);
    }

    /**
     * 记录那些租户还可以生产兑换码
     */
    public static final String HAS_MORE_REDEEM_CODE_TENANT_LIST = "HAS_MORE_REDEEM_CODE_TENANT_LIST";

    /**
     * 租户值的占位符，防止实际的租户全被删除后HAS_MORE_REDEEM_CODE_TENANT_LIST彻底的被删除了
     */
    public static final String HAS_MORE_REDEEM_CODE_TENANT_LIST_PLACE_HOLDER = "HAS_MORE_REDEEM_CODE_TENANT_LIST_PLACE_HOLDER";
}
