package com.cyberscraft.uep.adm.core.service.vpp.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cyberscraft.uep.adm.constants.Constant;
import com.cyberscraft.uep.adm.constants.ProgressStatus;
import com.cyberscraft.uep.adm.core.constant.RedisKeyConstant;
import com.cyberscraft.uep.adm.core.constant.ThreadPoolNameConstant;
import com.cyberscraft.uep.adm.core.constant.VppConstant;
import com.cyberscraft.uep.adm.core.dbo.VppCdkeyDBO;
import com.cyberscraft.uep.adm.core.dbo.VppManageDBO;
import com.cyberscraft.uep.adm.core.dbo.VppStoreDBO;
import com.cyberscraft.uep.adm.core.domain.OperProgress;
import com.cyberscraft.uep.adm.core.entity.VppCdkeyEntity;
import com.cyberscraft.uep.adm.core.entity.VppManageEntity;
import com.cyberscraft.uep.adm.core.entity.VppStoreEntity;
import com.cyberscraft.uep.adm.core.enums.AdmErrorType;
import com.cyberscraft.uep.adm.constants.DownloadValidType;
import com.cyberscraft.uep.adm.core.exception.AdmException;
import com.cyberscraft.uep.adm.core.service.basic.IOperProgressService;
import com.cyberscraft.uep.adm.core.service.sysconf.IDownloadConfigService;
import com.cyberscraft.uep.adm.core.service.vpp.IAppleVppService;
import com.cyberscraft.uep.adm.core.service.vpp.IVppService;
import com.cyberscraft.uep.adm.core.service.vpp.transfer.ICdKeyTransfer;
import com.cyberscraft.uep.adm.core.utils.RedisUtil;
import com.cyberscraft.uep.adm.core.utils.RegexValidateUtil;
import com.cyberscraft.uep.adm.dto.clientapp.CdKeyFileParseResultVO;
import com.cyberscraft.uep.adm.dto.clientapp.CdKeyFileResultVO;
import com.cyberscraft.uep.adm.dto.clientapp.CdKeyItemVO;
import com.cyberscraft.uep.adm.dto.sysconf.AppDownloadConfigVO;
import com.cyberscraft.uep.adm.dto.vpp.CdKeyInfoVO;
import com.cyberscraft.uep.common.dto.QueryPage;
import com.cyberscraft.uep.common.enums.ExceptionCodeEnum;
import com.cyberscraft.uep.common.service.ISelectPage;
import com.cyberscraft.uep.common.util.*;
import com.cyberscraft.uep.common.util.query.QueryAccuracy;
import com.cyberscraft.uep.common.util.query.QueryCondition;
import com.cyberscraft.uep.file.DfsFactory;
import com.cyberscraft.uep.file.FileManager;
import com.cyberscraft.uep.file.config.DfsConfig;
import com.cyberscraft.uep.file.dto.FileDto;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.poifs.filesystem.POIFSFileSystem;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.*;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Created  by  Angevin.
 * Date: 2019/12/11 19:12
 * description:
 *
 * <AUTHOR>
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class VppServiceImpl implements IVppService {
    /***
     *
     */
    private final Logger LOG = LoggerFactory.getLogger(getClass());

    @Resource
    private VppCdkeyDBO vppCdkeyDBO;
    @Resource
    private VppManageDBO vppManageDBO;
    @Resource
    private VppStoreDBO vppStoreDBO;
    @Resource
    private IAppleVppService appleVppService;
    @Resource
    private FileManager fileManager;

    @Resource(name = ThreadPoolNameConstant.COMMON_POOL_NAME)
    private ExecutorService executorService;
    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Resource
    private DfsConfig dfsConfig;
    @Resource
    private IOperProgressService operProgressService;

    @Resource
    private IDownloadConfigService<AppDownloadConfigVO> downloadConfigService;

    @Resource
    ICdKeyTransfer cdKeyTransfer;

    /***
     *
     */
    private final static String CDKEY_MALLOCED_KEY_PREFIX = "MDM:CDKEY:MALLOC";

    private final static String REDEMPTION = "Code Redemption Link";
    private static final String FORMAT = ".png";


    /***
     * 获取流文件,并生成保存实体类
     * @param fileKey
     */
    @Override
    public Long parseCsvByVppAppCDKey(String fileKey) throws AdmException {
        LOG.info("parseCsvByVppAppCDKey 解析上传文件 fileKey ：{}", fileKey);

        String tenantId = TenantHolder.getTenantCode();

        OperProgress<CdKeyFileParseResultVO> progress = new OperProgress<>();
        progress.setId(SnowflakeIDUtil.getId());
        updateProgress(progress, ProgressStatus.UNSTART.getCode(), "未开始");

        executorService.submit(() -> {
            try {
                parseCsvByVappAppCDkeyThread(tenantId, fileKey, progress);
            } catch (Exception e) {
                LOG.error(e.getMessage(), e);
                updateProgress(progress, ProgressStatus.ERROR.getCode(), AdmErrorType.VPP_FORMAT_ERROR.getDesc());
            } finally {
                TenantHolder.remove();
            }
        });
        LOG.info("parseCsvByVppAppCDKey 解析上传文件 fileKey ：{},进度ID", fileKey, progress.getId());
        return progress.getId();
    }

    /***
     * 进行事件处理
     * @param fileKey
     * @param progress
     */
    private void parseCsvByVappAppCDkeyThread(String tenantId, String fileKey, OperProgress<CdKeyFileParseResultVO> progress) {
        updateProgress(progress, ProgressStatus.PROCESSING.getCode(), "解析进行中");

        TenantHolder.setTenantCode(tenantId);
        String orderId = null;
        long appId = 0L;
        long adamId = 0L;

        FileManager excelFileManager = DfsFactory.getFileManager(dfsConfig.getAppFileType());
        InputStream is = excelFileManager.downloadFile(fileKey);

        HSSFWorkbook wb = null;
        try {
            wb = new HSSFWorkbook(new POIFSFileSystem(is));
        } catch (IOException e) {
            LOG.warn("解析VPP cdKey 的文件获取 HSSFWorkbook 流错误！！");
            updateProgress(progress, ProgressStatus.ERROR.getCode(), ExceptionCodeEnum.FILE_INVALID.getMessage());
            return;
        }
        HSSFSheet sheet = wb.getSheetAt(0);
        if (null == sheet || sheet.getRow(0).getLastCellNum() < 11) {
            LOG.info("解析VPP cdKey 的文件第1行列数小于12");
            updateProgress(progress, ProgressStatus.ERROR.getCode(), AdmErrorType.VPP_FORMAT_ERROR.getDesc());
            return;
        } else {
            String adamIdStr = sheet.getRow(0).getCell(11).getStringCellValue();
            if (StringUtils.isBlank(adamIdStr)) {
                LOG.info("解析VPP cdKey 未找到adamId");
                updateProgress(progress, ProgressStatus.ERROR.getCode(), AdmErrorType.VPP_ADAMID_INVALID.getDesc());
                return;
            }

            adamId = NumberUtils.toLong(adamIdStr, 0);
            if (adamId < 1) {
                LOG.info("解析VPP cdKey 的adamId：{}转换失Long失败", adamId);
                updateProgress(progress, ProgressStatus.ERROR.getCode(), AdmErrorType.VPP_ADAMID_INVALID.getDesc());
                return;
            }
            VppStoreEntity vppStore = vppStoreDBO.getByAdamId(adamId);
            if (null == vppStore) {
                LOG.info("解析VPP cdKey 的根据adamId：{}未查询到应用", adamId);
                updateProgress(progress, ProgressStatus.ERROR.getCode(), AdmErrorType.VPP_STORE_ENEITY_INVALID.getDesc());
                return;
            } else {
                appId = vppStore.getId();
            }
        }
        if (sheet.getRow(1).getLastCellNum() < 2) {
            LOG.info("解析VPP cdKey 的文件第2行列数小于3");
            updateProgress(progress, ProgressStatus.ERROR.getCode(), AdmErrorType.VPP_FORMAT_ERROR.getDesc());
            return;
        } else {
            orderId = sheet.getRow(1).getCell(2).getStringCellValue();

            if (StringUtils.isBlank(orderId)) {
                LOG.info("解析VPP cdKey 未找到orderId");
                updateProgress(progress, ProgressStatus.ERROR.getCode(), AdmErrorType.VPP_ORDERID_INVALID.getDesc());
                return;
            }
        }
        //遍历行row
        boolean isCdKey = false;
        for (int rownum = 0; rownum <= sheet.getLastRowNum(); rownum++) {
            HSSFRow sheetRow = sheet.getRow(rownum);
            if (sheetRow == null) {
                LOG.info("解析VPP cdKey 第{}行爲空", rownum);
                continue;
            }
            //查找cdkey开始位置
            if (!isCdKey) {
                if (sheetRow.getLastCellNum() < 2) {
                    LOG.info("解析VPP cdKey 第{}行列数小于3", rownum);
                    continue;
                }
                if (null != sheetRow.getCell(2) && sheetRow.getCell(2).getStringCellValue().equals(REDEMPTION)) {
                    LOG.info("解析VPP cdKey 第{}行的下一行为cdkey列表", rownum);
                    isCdKey = true;
                }
                continue;
            }

            // 验证行格式
            if (sheetRow.getLastCellNum() < 3) {
                LOG.info("解析VPP cdKey 第{}行的cdkey列表列数错误，小于3列，终止解析", rownum);
                updateProgress(progress, ProgressStatus.ERROR.getCode(), AdmErrorType.VPP_FORMAT_ERROR.getDesc());
                return;
            }

            // cdKey
            String cdKey = sheetRow.getCell(0).getStringCellValue();
            // inviteUrl
            String inviteUrl = sheetRow.getCell(2).getStringCellValue();
            if (StringUtils.isBlank(cdKey)) {
                LOG.info("解析VPP cdKey 获取cdKey为空");
                updateProgress(progress, ProgressStatus.ERROR.getCode(), AdmErrorType.VPP_CDKEY_INVALID.getDesc());
                return;
            } else if (StringUtils.isBlank(inviteUrl)) {
                LOG.info("解析VPP inviteUrl 为空，cdkey：{}", cdKey);
                updateProgress(progress, ProgressStatus.ERROR.getCode(), AdmErrorType.VPP_INVITEURL_INVALID.getDesc());
                return;
            } else if (!inviteUrl.equalsIgnoreCase("redeemed") && !RegexValidateUtil.urlFormat(inviteUrl) && !inviteUrl.endsWith(cdKey)) {
                LOG.info("解析VPP inviteUrl 格式错误，inviteUrl：{}", inviteUrl);
                updateProgress(progress, ProgressStatus.ERROR.getCode(), AdmErrorType.VPP_INVITEURL_INVALID.getDesc());
                return;
            } else {

                VppCdkeyEntity entityByCdKey = vppCdkeyDBO.getByCdkey(cdKey);
                if (null != entityByCdKey) {
                    if (entityByCdKey.getStatus() == 0 && inviteUrl.equalsIgnoreCase("redeemed")) {
                        entityByCdKey.setLoginId("^unknow^");
                        entityByCdKey.setStatus(1);
                        entityByCdKey.setUpdateTime(LocalDateTime.now());
                        vppCdkeyDBO.updateById(entityByCdKey);
                    }
                } else {
                    LOG.info("解析VPP cdKey 保存兑换码信息inviteUrl：{} ", inviteUrl);

                    VppCdkeyEntity entity = new VppCdkeyEntity();
                    entity.setOrderId(orderId);
                    entity.setAdamId(adamId);
                    entity.setVppAppId(appId);
                    entity.setCdkey(cdKey);
                    entity.setInviteUrl(inviteUrl);
                    entity.setUpdateTime(LocalDateTime.now());
                    if (inviteUrl.equalsIgnoreCase("redeemed")) {
                        entity.setLoginId("^unknow^");
                        entity.setStatus(1);
                    } else {
                        entity.setStatus(0);
                        entity.setCreateTime(LocalDateTime.now());
                    }
                    vppCdkeyDBO.save(entity);
                }
            }
        }

        updateProgress(progress, ProgressStatus.FINISHED.getCode(), ExceptionCodeEnum.SUCCESS.getMessage());
        RedisUtil.addTenantToProducerList(stringRedisTemplate, TenantHolder.getTenantCode());
    }

    private void updateProgress(OperProgress<CdKeyFileParseResultVO> progress, int result, String msg) {
        progress.setMsg(msg);
        progress.setResult(result);
        operProgressService.save(progress);
    }

    private VppCdkeyEntity getCdkeyByLoginIdAndAdamid (String loginId, Long adamid) {
        DownloadValidType downloadValidType = downloadConfigService.getValidType();
        if (downloadValidType != null
                && (downloadValidType.equals(DownloadValidType.EXTRACTED_CODE) || downloadValidType.equals(DownloadValidType.NONE))
                && StringUtils.isEmpty(loginId)) {
            // 对于使用提取码或不验证的验证方式，且loginId为空时，直接返回null,
            // 表示该用户没有获取过任何兑换码
            return null;
        }
        if (StringUtils.isBlank(loginId)) {
            throw new AdmException(AdmErrorType.USER_LOGINID_INVALID);
        }
        // 查询该用户已经被获取过的兑换码
        VppCdkeyEntity cdkeyEntity  = vppCdkeyDBO.getByLoginId(loginId, adamid);
        return cdkeyEntity;
    }

    /***
     * 获取cdkey详情
     * @param
     */
    @Override
    public CdKeyInfoVO getCdkeySummary() throws AdmException {
        //需要获取当前客户端的adamId
        String tenantId = TenantHolder.getTenantCode();
        String orderId = "";
        long totalNum = 0L;
        long assignedNum = 0L;
        long lastUploadTime = 0L;
        List<Map<String,Object>> statusList;
        VppStoreEntity entity = appleVppService.getDefaultVppEntity(tenantId);
        if (null != entity) {
            statusList = vppCdkeyDBO.countCdkeyStatus(entity.getAdamId());
        } else {
            LOG.info("Fail to get Cdkey summary, tenantId: {}", tenantId);
            throw new AdmException(AdmErrorType.VPP_STORE_ENEITY_INVALID);
        }

        if(statusList != null){
            long unassignedNum = 0;
            for (Map<String, Object> map : statusList) {
                Integer status = (Integer)map.get("status");
                Long num = (Long)map.get("num");
                if (status != null) {
                    if(status == 0) {
                        unassignedNum = num;
                    }else if(status == 1){
                        assignedNum = num;
                    }
                }
            }
            totalNum = unassignedNum + assignedNum;

            LOG.info("OK to get Cdkey summary, tenantId: {}, adamid: {}，total: {}", tenantId, entity.getAdamId(), totalNum);

            Long cdKeyMaxId = vppCdkeyDBO.maxCdkeyId(entity.getAdamId());
            if(cdKeyMaxId != null) {
                VppCdkeyEntity cdKeyEntity = vppCdkeyDBO.getById(cdKeyMaxId);
                orderId = cdKeyEntity.getOrderId();
                lastUploadTime = cdKeyEntity.getCreateTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
            }
        }

        CdKeyInfoVO vo = new CdKeyInfoVO(orderId, totalNum, assignedNum,lastUploadTime);
        LOG.info("Cdkey summary: tenantId: {}, adamid: {}, orderId: {}, totalNum: {}, assignedNum: {}", tenantId, entity.getAdamId(), orderId, totalNum, assignedNum);

        return vo;
    }


    /***
     * 根据longid获取兑换码
     * @param
     */
    @Override
    public String getCdkey(String loginId) throws AdmException {
        String cdkey = null;
        long startTime = System.currentTimeMillis();
        try {
            String tenantId = TenantHolder.getTenantCode();

            VppStoreEntity storeEntity = appleVppService.getDefaultVppEntity(tenantId);
            if (null == storeEntity) {
                throw new AdmException(AdmErrorType.VPP_STORE_ENEITY_INVALID);
            }
            //admin 可以获取多个，用于批量导出
            VppCdkeyEntity cdkeyEntity = null;
            if ( !Constant.TENANT_ADMIN_ID.equals(loginId) ){
                cdkeyEntity =  getCdkeyByLoginIdAndAdamid(loginId, storeEntity.getAdamId());
            }
            if (null != cdkeyEntity) {
                LOG.info("cdkey {} already assigned to user {}, took {} ms", cdkeyEntity.getCdkey(), loginId,
                        System.currentTimeMillis() - startTime);
                return cdkeyEntity.getInviteUrl();
            }
            LOG.info("user {} no cdkey, took {} ms", loginId,
                    System.currentTimeMillis() - startTime);

            while (true) {
                cdkey = stringRedisTemplate.opsForList().rightPop(
                        RedisKeyConstant.getCdkeyQueueKey(TenantHolder.getTenantCode()),
                        5, TimeUnit.SECONDS);
                if (cdkey == null) {
                    LOG.warn("unable to assign cdkey to user {}, no more cdkey or wait for producer to produce", loginId);
                    throw new AdmException(AdmErrorType.VPP_CDKEY_ASSIGNED_ERROR);
                }

                cdkeyEntity = vppCdkeyDBO.getUnusedCdkey(cdkey);

                if (cdkeyEntity == null) {
                    LOG.warn("cdkey {} not found, can't assign to user {}, get next one", cdkey, loginId);
                    continue;
                }

                cdkeyEntity.setUpdateTime(LocalDateTime.now());
                if(StringUtils.isNotEmpty(loginId)) {
                    cdkeyEntity.setLoginId(loginId);
                }
                cdkeyEntity.setStatus(1);

                boolean success = vppCdkeyDBO.updateWithCondition(cdkeyEntity, 0, true);
                /**
                 * 本次兑换码已经被使用，尝试获取下一个兑换码
                 */
                if (!success) {
                    LOG.warn("failed to update cdkey {} status to 1,loginId to {}, get next one ", cdkey, loginId);
                    continue;
                }
                LOG.info("user {} getCdkey took {} ms", loginId, System.currentTimeMillis() - startTime);
                return cdkeyEntity.getInviteUrl();
            }
        } catch (AdmException e) {
            reEnqueue(cdkey);
            throw e;
        } catch (Exception e) {
            reEnqueue(cdkey);
            LOG.warn("getCdkey failed, user {}", loginId);
            LOG.error(e.getMessage(), e);
            throw new AdmException(AdmErrorType.VPP_CDKEY_ASSIGNED_ERROR);
        }
    }

    @Override
    public List<String> getCdKeyByNum(Integer num) throws AdmException {
        String tenantId = TenantHolder.getTenantCode();
        List<String> cdKeyList = new ArrayList<>();

        VppStoreEntity storeEntity = appleVppService.getDefaultVppEntity(tenantId);
        if (null == storeEntity) {
            throw new AdmException(AdmErrorType.VPP_STORE_ENEITY_INVALID);
        }

        while (true) {
            if ( cdKeyList.size() < num){
                String cdKey = this.getCdkey(Constant.TENANT_ADMIN_ID);
                cdKeyList.add(cdKey);
            }else {
                break;
            }
        }

        return cdKeyList;
    }
    /**
     * 如果cdkey成功获取到，但是并未被成功消费，需要重新入队列
     * @param cdkey
     */
    private void reEnqueue(String cdkey){
        if (StringUtils.isEmpty(cdkey)){
            return;
        }
        stringRedisTemplate.opsForList().leftPush(
                RedisKeyConstant.getCdkeyQueueKey(TenantHolder.getTenantCode()),cdkey);
    }
    /**
     * 批量导出指定数量未使用的兑换码，用兑换码生成二维码，并打包成zip包。
     * zip包上传到OSS，返回文件地址【5分钟失效】
     *
     * @param loginId 当前登录的账号
     * @param num
     */
    @Override
    public Long batchDownLoadCdKey(String loginId, int num) throws AdmException {
        LOG.info("批量导出兑换码二维码。loginId：{}，num：{}！！", loginId, num);

        OperProgress<CdKeyFileParseResultVO> progress = new OperProgress<>();
        progress.setId(SnowflakeIDUtil.getId());
        updateProgress(progress, ProgressStatus.UNSTART.getCode(), null);

        //需要获取当前客户端的adamId
        String tenantId = TenantHolder.getTenantCode();
        VppStoreEntity entity = appleVppService.getDefaultVppEntity(tenantId);
        if (null == entity) {
            LOG.info("批量导出兑换码二维码。根据tenantId：{}，查询默认客户端为空！！", tenantId);
            updateProgress(progress, ProgressStatus.ERROR.getCode(), AdmErrorType.VPP_STORE_ENEITY_INVALID.getDesc());
            return progress.getId();
        }
        List<VppCdkeyEntity> list = vppCdkeyDBO.getCdkeyByNum(entity.getAdamId(), num);
        if (null == list || num > list.size()) {
            LOG.info("批量导出兑换码二维码。导出数量：{}，超过可用数量，查询出的数量list：{}", num, list.size());
            updateProgress(progress, ProgressStatus.ERROR.getCode(), AdmErrorType.VPP_CDKEY_NOT_ENOUGH_ERROR.getDesc());
            return progress.getId();
        }

        //1.为每个cdkey生成一个激活码，
        // 都放在一个临时目录保存
        String tmpFile = UUID.randomUUID().toString();
        String cdKeyPath = "/tmp/cdkey/" + tmpFile;
        String zipFilePath = cdKeyPath + "/" + tmpFile + ".zip";
        String qrPath = cdKeyPath + "/" + tmpFile + "/cdkey_" + DateUtil.getStrDate(new Date()) + "/";
        File dfile = new File(cdKeyPath);
        //异步执行
        executorService.submit(() -> {
            LOG.info("批量导出兑换码二维码。进入异步执行 ，导出数量：{}，查询出的数量list：{}", num, list.size());
            updateProgress(progress, ProgressStatus.PROCESSING.getCode(), "开始导出二维码");

            //以下放到线程池里进行执行，执行结果放到redis
            //该接口返回空值，额外新增接口用于查询执行结果及路径
            File folder = new File(qrPath);
            if (!folder.exists()) {
                folder.mkdirs();
            }
            for (VppCdkeyEntity cdkeyEntity : list) {
                String fileName = UUID.randomUUID().toString() + FORMAT;

                try {
                    LOG.info("批量导出兑换码二维码。生成二维码file：{}，url：{}", qrPath + fileName, cdkeyEntity.getInviteUrl());
                    //生成二维码
                    QRCodeUtil.generateQRCodeFileByString(cdkeyEntity.getInviteUrl(), qrPath + fileName);
                } catch (Exception e) {
                    LOG.error("兑换码生成二维码失败", e);
                    //更新redis中导出状态为错误
                    FileUtil.delAllFile(dfile);
                    updateProgress(progress, ProgressStatus.ERROR.getCode(), AdmErrorType.VPP_CDKEY_GENERATE_QRCODE_ERROR.getDesc());
                    return;
                }
            }
            File file = new File(zipFilePath);
            if (null != file && file.exists()) {
                file.delete();// 清除文件
            }

            //压缩
            try {
                ZIPUtil.compress(qrPath, zipFilePath);
            } catch (Exception e) {
                LOG.error("批量导出兑换码二维码。压缩错误！！！", e);
                FileUtil.delAllFile(dfile);
                //更新redis中导出状态为错误
                updateProgress(progress, ProgressStatus.ERROR.getCode(), "批量导出兑换码二维码。压缩错误！！！");
                return;
            }
            File zipFile = new File(zipFilePath);
            if (null != zipFile && zipFile.exists()) {
                //上传至OSS
                int lastIndex = zipFilePath.lastIndexOf("/");
                String targetPath = dfsConfig.getUploadPath() + zipFilePath.substring(0, lastIndex);
                String fileName = zipFilePath.substring(lastIndex + 1);
                InputStream is = null;
                try {
                    is = new FileInputStream(new File(zipFilePath));
                } catch (FileNotFoundException e) {
                    LOG.error("批量导出兑换码二维码，未获取到压缩文件！！", e);
                    FileUtil.delAllFile(dfile);
                    //更新redis中导出状态为错误
                    updateProgress(progress, ProgressStatus.ERROR.getCode(), "批量导出兑换码二维码，未获取到压缩文件！！");
                    return;
                }
                //开始上传文件到OSS服务器
                FileDto fileDto = fileManager.upload(is, fileName, targetPath);
                FileUtil.delAllFile(dfile);

                if (null != fileDto) {

                    for (VppCdkeyEntity cdkeyEntity : list) {
                        cdkeyEntity.setUpdateTime(LocalDateTime.now());
                        cdkeyEntity.setLoginId(loginId);
                        cdkeyEntity.setStatus(1);
                    }
                    boolean up = vppCdkeyDBO.updateBatchById(list);
                    if (up) {
                        LOG.info("批量导出兑换码二维码，成功！！");
                        //通过真是路径，换取5分钟有效期的路径
                        String downUrl = fileManager.getFileUrl(fileDto.getFilePath());
                        //更新redis中导出状态为完成
                        updateProgress(progress, ProgressStatus.FINISHED.getCode(), downUrl);

                    } else {
                        LOG.info("批量导出兑换码二维码，更新到数据库失败！！");
                        updateProgress(progress, ProgressStatus.ERROR.getCode(), "批量导出兑换码二维码，更新到数据库失败！！");

                    }
                } else {
                    LOG.info("批量导出兑换码二维码，保存到OSS失败！！");
                    updateProgress(progress, ProgressStatus.ERROR.getCode(), "批量导出兑换码二维码，保存到OSS失败！！");
                }
            } else {
                LOG.info("批量导出兑换码二维码。压缩错误！！！");
                //更新redis中导出状态为错误
                updateProgress(progress, ProgressStatus.ERROR.getCode(), "批量导出兑换码二维码。压缩错误！！！");
            }

        });

        return progress.getId();
    }

    @Override
    public CdKeyFileParseResultVO getParseCdKeyStatus(Long resultId) throws AdmException {
        OperProgress<CdKeyFileParseResultVO> progress = operProgressService.getOperProgress(resultId);
        CdKeyFileParseResultVO ret = new CdKeyFileParseResultVO();
        ret.setResult(ProgressStatus.UNSTART.getCode());
        if (progress != null) {
            ret.setResult(progress.getResult());
            ret.setMsg(progress.getMsg());
        }
        return ret;
    }

    @Override
    public CdKeyFileParseResultVO getBatchDownLoadCdKeyStatus(Long resultId) throws AdmException {
        OperProgress<CdKeyFileParseResultVO> progress = operProgressService.getOperProgress(resultId);
        CdKeyFileParseResultVO ret = new CdKeyFileParseResultVO();
        if (progress != null) {
            ret.setResult(progress.getResult());
            if (ProgressStatus.FINISHED.getCode().equals(progress.getResult())) {
                ret.setFileUrl(progress.getMsg());
            } else {
                ret.setMsg(progress.getMsg());
            }
        }
        return ret;
    }

    @Override
    public CdKeyFileResultVO parseVppAppCdKeyXlsFile(String filePath) throws AdmException {

        CdKeyFileResultVO ret = new CdKeyFileResultVO();
        String lockKey = VppConstant.VPP_UPLOAD_CDKEY_LOCK_PREFIX + TenantHolder.getTenantCode();
        try {
            if (!stringRedisTemplate.opsForValue().setIfAbsent(lockKey, "1", 10, TimeUnit.MINUTES)) {
                LOG.warn("parseVppAppCdKeyXlsFile is running, please wait");
                throw new AdmException(AdmErrorType.VPP_UPLOAD_CDKEY_LOCK);
            }

            String orderId = null;
            long appId = 0L;
            long adamId = 0L;

            InputStream is = new FileInputStream(filePath);

            HSSFWorkbook wb = null;
            try {
                wb = new HSSFWorkbook(new POIFSFileSystem(is));
            } catch (IOException e) {
                LOG.warn("解析VPP cdKey 的文件获取 HSSFWorkbook 流错误！！");
                ret.setCode(ExceptionCodeEnum.FILE_INVALID.getCode());
                ret.setMsg(ExceptionCodeEnum.FILE_INVALID.getMessage());
                return ret;
            }
            HSSFSheet sheet = wb.getSheetAt(0);
            if (null == sheet || sheet.getRow(0).getLastCellNum() < 11) {
                LOG.info("解析VPP cdKey 的文件第1行列数小于12");
                ret.setCode(AdmErrorType.VPP_FORMAT_ERROR.getCode());
                ret.setMsg(AdmErrorType.VPP_FORMAT_ERROR.getDesc());
                return ret;
            } else {
                String adamIdStr = sheet.getRow(0).getCell(11).getStringCellValue();
                if (StringUtils.isBlank(adamIdStr)) {
                    LOG.info("解析VPP cdKey 未找到adamId");
                    ret.setCode(AdmErrorType.VPP_ADAMID_INVALID.getCode());
                    ret.setMsg(AdmErrorType.VPP_ADAMID_INVALID.getDesc());
                    return ret;
                }

                adamId = NumberUtils.toLong(adamIdStr, 0);
                if (adamId < 1) {
                    LOG.info("解析VPP cdKey 的adamId：{}转换失Long失败", adamId);
                    ret.setCode(AdmErrorType.VPP_ADAMID_INVALID.getCode());
                    ret.setMsg(AdmErrorType.VPP_ADAMID_INVALID.getDesc());
                    return ret;
                }
                VppStoreEntity vppStore = vppStoreDBO.getByAdamId(adamId);
                if (null == vppStore) {
                    LOG.info("解析VPP cdKey 的根据adamId：{}未查询到应用", adamId);
                    ret.setCode(AdmErrorType.VPP_STORE_ENEITY_INVALID.getCode());
                    ret.setMsg(AdmErrorType.VPP_STORE_ENEITY_INVALID.getDesc());
                    return ret;
                } else {
                    appId = vppStore.getId();
                }
            }
            if (sheet.getRow(1).getLastCellNum() < 2) {
                LOG.info("解析VPP cdKey 的文件第2行列数小于3");
                ret.setCode(AdmErrorType.VPP_FORMAT_ERROR.getCode());
                ret.setMsg(AdmErrorType.VPP_FORMAT_ERROR.getDesc());
                return ret;
            } else {
                orderId = sheet.getRow(1).getCell(2).getStringCellValue();

                if (StringUtils.isBlank(orderId)) {
                    LOG.info("解析VPP cdKey 未找到orderId");
                    ret.setCode(AdmErrorType.VPP_ORDERID_INVALID.getCode());
                    ret.setMsg(AdmErrorType.VPP_ORDERID_INVALID.getDesc());
                    return ret;
                }
            }
            //遍历行row
            boolean isCdKey = false;
            for (int rownum = 0; rownum <= sheet.getLastRowNum(); rownum++) {
                HSSFRow sheetRow = sheet.getRow(rownum);
                if (sheetRow == null) {
                    LOG.info("解析VPP cdKey 第{}行爲空", rownum);
                    continue;
                }
                //查找cdkey开始位置
                if (!isCdKey) {
                    if (sheetRow.getLastCellNum() < 2) {
                        LOG.info("解析VPP cdKey 第{}行列数小于3", rownum);
                        continue;
                    }
                    if (null != sheetRow.getCell(2) && sheetRow.getCell(2).getStringCellValue().equals(REDEMPTION)) {
                        LOG.info("解析VPP cdKey 第{}行的下一行为cdkey列表", rownum);
                        isCdKey = true;
                    }
                    continue;
                }

                // 验证行格式
                if (sheetRow.getLastCellNum() < 3) {
                    LOG.info("解析VPP cdKey 第{}行的cdkey列表列数错误，小于3列，终止解析", rownum);
                    ret.setCode(AdmErrorType.VPP_FORMAT_ERROR.getCode());
                    ret.setMsg(AdmErrorType.VPP_FORMAT_ERROR.getDesc());
                    return ret;
                }

                // cdKey
                String cdKey = sheetRow.getCell(0).getStringCellValue();
                // inviteUrl
                String inviteUrl = sheetRow.getCell(2).getStringCellValue();
                if (StringUtils.isBlank(cdKey)) {
                    LOG.info("解析VPP cdKey 获取cdKey为空");
                    ret.setCode(AdmErrorType.VPP_CDKEY_INVALID.getCode());
                    ret.setMsg(AdmErrorType.VPP_CDKEY_INVALID.getDesc());
                    return ret;
                } else if (StringUtils.isBlank(inviteUrl)) {
                    LOG.info("解析VPP inviteUrl 为空，cdkey：{}", cdKey);
                    ret.setCode(AdmErrorType.VPP_INVITEURL_INVALID.getCode());
                    ret.setMsg(AdmErrorType.VPP_INVITEURL_INVALID.getDesc());
                    return ret;
                } else if (!inviteUrl.equalsIgnoreCase("redeemed") && !RegexValidateUtil.urlFormat(inviteUrl) && !inviteUrl.endsWith(cdKey)) {
                    LOG.info("解析VPP inviteUrl 格式错误，inviteUrl：{}", inviteUrl);
                    ret.setCode(AdmErrorType.VPP_INVITEURL_INVALID.getCode());
                    ret.setMsg(AdmErrorType.VPP_INVITEURL_INVALID.getDesc());
                    return ret;
                } else {

                    VppCdkeyEntity entityByCdKey = vppCdkeyDBO.getByCdkey(cdKey);
                    if (null != entityByCdKey) {
                        if (entityByCdKey.getStatus() == 0 && inviteUrl.equalsIgnoreCase("redeemed")) {
                            entityByCdKey.setLoginId("^unknow^");
                            entityByCdKey.setStatus(1);
                            entityByCdKey.setUpdateTime(LocalDateTime.now());
                            vppCdkeyDBO.updateById(entityByCdKey);
                        }
                    } else {
                        LOG.info("解析VPP cdKey 保存兑换码信息inviteUrl：{} ", inviteUrl);

                        VppCdkeyEntity entity = new VppCdkeyEntity();
                        entity.setOrderId(orderId);
                        entity.setAdamId(adamId);
                        entity.setVppAppId(appId);
                        entity.setCdkey(cdKey);
                        entity.setInviteUrl(inviteUrl);
                        entity.setUpdateTime(LocalDateTime.now());
                        if (inviteUrl.equalsIgnoreCase("redeemed")) {
                            entity.setLoginId("^unknow^");
                            entity.setStatus(1);
                        } else {
                            entity.setStatus(0);
                            entity.setCreateTime(LocalDateTime.now());
                        }
                        vppCdkeyDBO.save(entity);
                    }
                }
            }
            RedisUtil.addTenantToProducerList(stringRedisTemplate, TenantHolder.getTenantCode());

            ret.setCode(ExceptionCodeEnum.SUCCESS.getCode());
            ret.setMsg(ExceptionCodeEnum.SUCCESS.getMessage());
            return ret;
        } catch (AdmException e){
            LOG.error(e.getMessage(), e);
            ret.setCode(e.getCode());
            ret.setMsg(e.getMessage());
            return ret;
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
            ret.setCode(AdmErrorType.VPP_FORMAT_ERROR.getCode());
            ret.setMsg(AdmErrorType.VPP_FORMAT_ERROR.getDesc());
            return ret;
        } finally {
            stringRedisTemplate.delete(lockKey);
        }
    }

    @Override
    public QueryPage<CdKeyItemVO> searchCdKey(QueryCondition queryCondition) {
        VppStoreEntity vppStoreEntity = appleVppService.getDefaultVppEntity(TenantHolder.getTenantCode());
        if(vppStoreEntity == null || vppStoreEntity.getAdamId() == null) {
            return new QueryPage<>();
        }
        String adamid = vppStoreEntity.getAdamId().toString();
        LOG.info("search cdkey list for adamid {}", adamid);
        queryCondition.setFilter(adamid);
        queryCondition.setAccuracy(QueryAccuracy.MATCH_ALL);

        List<String> sorts = queryCondition.getSorts();
        if(sorts == null || sorts.isEmpty()) {
            // 默认使用创建时间降序
            sorts = new ArrayList<>();
            sorts.add("updateTime,desc");
            queryCondition.setSorts(sorts);
        }

        List<SFunction<VppCdkeyEntity, ?>> columns = new ArrayList<>();
        columns.add(VppCdkeyEntity::getAdamId);

        QueryPage<VppCdkeyEntity> entityQueryPage = PagingUtil.page(queryCondition,
                new VppCdkeyEntity(), new ISelectPage<VppCdkeyEntity>() {
                    @Override
                    public <E extends IPage<VppCdkeyEntity>> E selectPage(Page<VppCdkeyEntity> page, QueryWrapper<VppCdkeyEntity> queryWrapper) {
                        return (E) vppCdkeyDBO.selectPage(page, queryWrapper);
                    }
                }, columns, null, null);

        return cdKeyTransfer.entityToPage(entityQueryPage);
    }

    /**
     * 管理式授权
     *
     * @param loginId
     * @return
     * @throws AdmException
     */
    @Override
    public String manageVpp(String loginId) throws AdmException {
        String tenantId = TenantHolder.getTenantCode();

        VppStoreEntity storeEntity = appleVppService.getDefaultVppEntity(tenantId);
        if (null == storeEntity) {
            throw new AdmException(AdmErrorType.VPP_STORE_ENEITY_INVALID);
        }

        VppManageEntity vppManageEntity = vppManageDBO.getByAdamIdAndLoginId(storeEntity.getAdamId(), loginId);
        if(vppManageEntity != null){
            return vppManageEntity.getInviteUrl();
        }else{
            String sToken = appleVppService.getOriginToken(tenantId);
            String inviteUrl = appleVppService.registerVppUser(sToken, loginId, loginId);
            String licenseIdStr = appleVppService.manageVPPByAdamIdAndUserId(sToken, storeEntity.getAdamId(), storeEntity.getPricingParam(), loginId);

            vppManageEntity = new VppManageEntity();
            vppManageEntity.setId(SnowflakeIDUtil.getId());
            vppManageEntity.setAdamId(storeEntity.getAdamId());
            vppManageEntity.setInviteUrl(inviteUrl);
            vppManageEntity.setLicenseId(licenseIdStr);
            vppManageEntity.setLoginId(loginId);
            vppManageEntity.setVppAppId(storeEntity.getId());
            vppManageEntity.setCreateTime(LocalDateTime.now());
            vppManageEntity.setUpdateTime(LocalDateTime.now());
            vppManageDBO.save(vppManageEntity);
            return inviteUrl;
        }
    }
}