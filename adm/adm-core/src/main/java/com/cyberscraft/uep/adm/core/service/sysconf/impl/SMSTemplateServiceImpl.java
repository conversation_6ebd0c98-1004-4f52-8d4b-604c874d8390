package com.cyberscraft.uep.adm.core.service.sysconf.impl;

import com.cyberscraft.uep.adm.core.constant.ConfigConstant;
import com.cyberscraft.uep.adm.core.domain.InvitationParam;
import com.cyberscraft.uep.adm.core.service.sysconf.ITemplateService;
import com.cyberscraft.uep.adm.dto.sysconf.SMSTemplateVO;
import org.springframework.stereotype.Service;

@Service
public class SMSTemplateServiceImpl extends AbstractTemplateService<SMSTemplateVO> implements ITemplateService<SMSTemplateVO> {

    @Override
    public String getConfigKey() {
        return ConfigConstant.CONFIG_KEY_SMS_TEMPLATE;
    }

    @Override
    public SMSTemplateVO getTemplate() {
        SMSTemplateVO templateVO = this.getTemplateInner();
        return templateVO;
    }

    @Override
    public void saveTemplate(SMSTemplateVO templateVO) {
        this.saveTemplateInner(templateVO);
    }

    @Override
    public String renderContent(InvitationParam param) {
        return renderContentInner(param);
    }

    @Override
    public String renderTitle(InvitationParam param) {
        return "";
    }
}
