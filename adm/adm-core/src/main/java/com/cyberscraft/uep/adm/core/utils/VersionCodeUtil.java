package com.cyberscraft.uep.adm.core.utils;

import org.apache.commons.lang3.StringUtils;


/**
 * Created  by  <PERSON><PERSON><PERSON>.
 * Date: 2019-08-28 14:26
 * description:
 *
 * <AUTHOR>
 */
public class VersionCodeUtil {

    /**
     * 输出标准化版本号，用于比较版本大小，
     * 返回结果为3+3+3+6位组合，
     *
     * @param version
     * @return
     */
    public static String parseVersion(String version) {
        if (StringUtils.isNotBlank(version)) {
            String[] vArr = version.split("\\.");
            if (null != vArr) {
                int index = 0;
                StringBuilder sd = new StringBuilder();

                if (vArr.length > 4) {
                    index = 4;
                } else {
                    index = vArr.length;
                }
                switch (index) {
                    case 1:
                        sd.append("100000000").append(redata(vArr[0], 6));
                        break;
                    case 2:
                        sd.append("100000").append(redata(vArr[0], 3)).append(redata(vArr[1], 6));
                        break;
                    case 3:
                        sd.append("100").append(redata(vArr[0], 3)).append(redata(vArr[1], 3)).append(redata(vArr[2], 6));
                        break;
                    case 4:
                        sd.append(redata1(vArr[0], 3)).append(redata(vArr[1], 3)).append(redata(vArr[2], 3)).append(redata(vArr[3], 6));
                        break;

                    default:
                        return null;
                }
                return sd.toString();

            }

        }
        return null;
    }

    private static String redata(String data, int length) {
        StringBuilder sd = new StringBuilder();
        String code = "000000" + data;
        sd.append(code.substring(code.length() - length));
        return sd.toString();
    }

    private static String redata1(String data, int length) {
        String code;
        if (0 == data.length()) {
            code = "100";
        } else if (1 == data.length()) {
            code = "10" + data;

        } else if (2 == data.length()) {
            code = "1" + data;

        } else {
            code = data;

        }
        return code;
    }

    public static void main(String args[]) {

        System.out.println(parseVersion("1.5.4.48.36"));
        System.out.println(parseVersion("1.5.4.4885"));
        System.out.println(parseVersion("45.9.488"));
        System.out.println(parseVersion("559.485"));
        System.out.println(parseVersion("8885"));
        System.out.println(parseVersion(""));
        System.out.println(parseVersion(null));
        System.out.println("ssss".startsWith("ssss"));
    }
}
