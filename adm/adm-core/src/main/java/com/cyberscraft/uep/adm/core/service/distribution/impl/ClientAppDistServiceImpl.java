package com.cyberscraft.uep.adm.core.service.distribution.impl;

import com.alibaba.fastjson.JSON;
import com.cyberscraft.uep.account.client.constant.ThirdPartyTokenType;
import com.cyberscraft.uep.account.client.domain.*;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountException;
import com.cyberscraft.uep.account.client.service.IThirdPartyDownloadSmsTicketService;
import com.cyberscraft.uep.adm.core.constant.ClientAppDistributeConstant;
import com.cyberscraft.uep.adm.core.enums.AdmErrorType;
import com.cyberscraft.uep.adm.core.exception.AdmException;
import com.cyberscraft.uep.adm.core.service.distribution.IClientAppDistService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Service
public class ClientAppDistServiceImpl implements IClientAppDistService {

    private static Logger log = LoggerFactory.getLogger(ClientAppDistServiceImpl.class);

    @Resource
    private IThirdPartyDownloadSmsTicketService thirdPartyDownloadSmsTicketService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    private String saveTicketToCache(ThirdPartyTicket thirdPartyTicket) {
        String ticketJson = JSON.toJSONString(thirdPartyTicket);
        stringRedisTemplate.opsForValue().set(ClientAppDistributeConstant.USER_TICKET_REDIS_PREFIX + thirdPartyTicket.getTicket(), ticketJson, 12, TimeUnit.HOURS);
        return thirdPartyTicket.getTicket();
    }

    @Override
    public void sendSmsCode(DownloadSmsCodeRequest request) throws AdmException {

        //对接NCM短信发送后，不需要在ADM中判断租户信息
        //检查sms是否开通
        //TenantConfig tenantConfig = sysConfigService.checkTenantConfig();
        //if(Constants.TENANT_SMS_DISABLE == tenantConfig.getSms()){
        //    throw new AdmException(AdmErrorType.TENANT_SMS_FORBIDDEN);
        //}

        try {
            log.info("sendSmsCode request:{}", JSON.toJSONString(request));

            //List list = userService.getUserByMobile(request.getPhone());
            //if (CollectionUtils.isEmpty(list)) {
            //    log.error("sendSmsCode mobile is inValid.");
            //    throw new AdmException(AdmErrorType.SEND_SMS_PHONE_INVALID_FAIL);
            //}
            thirdPartyDownloadSmsTicketService.sendDownloadSmsCode(request, ThirdPartyTokenType.SDP_IAM.getCode());
        } catch (Exception e) {
            log.error("sendSmsCode Exception. {}", e.getMessage());
            throw new AdmException(AdmErrorType.SEND_SMS_CODE_FAIL.getCode(), e.getMessage());
        }
    }

    /**
     * 短信验证
     *
     * @param request
     * @return
     * @throws AdmException
     */
    @Override
    public String verifySmsCode(DownloadSmsCodeRequest request) throws AdmException {
        try {
            log.info("verifySmsCode request:{}", JSON.toJSONString(request));
            ThirdPartyTicket thirdPartyTicket = thirdPartyDownloadSmsTicketService.verifyDownloadSmsCode(request, ThirdPartyTokenType.SDP_IAM.getCode());
            if (thirdPartyTicket != null) {
                return saveTicketToCache(thirdPartyTicket);
            } else {
                log.error("verifySmsCode return null.");
                throw new AdmException(AdmErrorType.SMS_CODE_VERIFY_FAIL);
            }
        } catch (ThirdPartyAccountException ex){
            log.error("verifySmsCode ThirdPartyAccountException.", ex);
            throw ex;
        } catch (Exception e) {
            log.error("verifySmsCode Exception.", e);
            throw new AdmException(AdmErrorType.SMS_CODE_VERIFY_FAIL.getCode(), e.getMessage());
        }
    }

    /**
     * 帐号密码验证
     *
     * @param request
     * @return
     * @throws AdmException
     */
    @Override
    public String verifyUserPassword(UserPasswordRequest request) throws AdmException {
        try {
            log.info("verifyUserPassword request:{}", JSON.toJSONString(request));
            ThirdPartyTicket thirdPartyTicket = thirdPartyDownloadSmsTicketService.verifyUserPassword(request, ThirdPartyTokenType.SDP_IAM.getCode());
            if (thirdPartyTicket != null) {
                return saveTicketToCache(thirdPartyTicket);
            } else {
                log.error("verifyUserPassword return null.");
                throw new AdmException(AdmErrorType.USER_AUTH_FAIL);
            }
        } catch (ThirdPartyAccountException ex){
            log.error("verifyUserPassword ThirdPartyAccountException.", ex);
            throw ex;
        } catch (Exception e) {
            log.error("verifyUserPassword Exception.", e);
            throw new AdmException(AdmErrorType.USER_AUTH_FAIL.getCode(), e.getMessage());
        }
    }

    @Override
    public String verifyExtractedCode(ExtractedCodeRequest request) throws AdmException {
        try {
            log.info("verifyExtractedCode request:{}", JSON.toJSONString(request));
            ThirdPartyTicket thirdPartyTicket = thirdPartyDownloadSmsTicketService.verifyExtractedCode(request, ThirdPartyTokenType.SDP_IAM.getCode());
            if(thirdPartyTicket != null) {
                return saveTicketToCache(thirdPartyTicket);
            } else {
                log.error("verifyExtractedCode return null.");
                throw new AdmException(AdmErrorType.EXTRACTED_CODE_AUTH_FAIL);
            }
        } catch (ThirdPartyAccountException ex){
            log.error("verifyExtractedCode ThirdPartyAccountException.", ex);
            throw ex;
        } catch (Exception e) {
            log.error("verifyExtractedCode Exception.", e);
            throw new AdmException(AdmErrorType.EXTRACTED_CODE_AUTH_FAIL.getCode(), e.getMessage());
        }
    }

    /**
     * 无校验
     *
     * @param request
     * @return
     * @throws AdmException
     */
    @Override
    public String verifyNone(NoneVerifyRequest request) throws AdmException {
        try {
            log.info("verifyNone request:{}", JSON.toJSONString(request));
            ThirdPartyTicket thirdPartyTicket = thirdPartyDownloadSmsTicketService.verifyNone(request, ThirdPartyTokenType.SDP_IAM.getCode());
            if(thirdPartyTicket != null) {
                return saveTicketToCache(thirdPartyTicket);
            } else {
                log.error("verifyNone return null.");
                throw new AdmException(AdmErrorType.USER_AUTH_FAIL);
            }
        } catch (ThirdPartyAccountException ex){
            log.error("verifyNone ThirdPartyAccountException.", ex);
            throw ex;
        } catch (Exception e) {
            log.error("verifyNone Exception.", e);
            throw new AdmException(AdmErrorType.USER_AUTH_FAIL.getCode(), e.getMessage());
        }
    }

    /**
     * 验证ticket
     *
     * @param ticket
     * @return
     * @throws AdmException
     */
    @Override
    public ThirdPartyTicket verifyTicket(String ticket) throws AdmException {
        if(StringUtils.isBlank(ticket)){
            log.error("ticket is bland");
            throw new AdmException(AdmErrorType.TICKET_VERIFY_FAIL);
        }
        String ticketJson = stringRedisTemplate.opsForValue().get(ClientAppDistributeConstant.USER_TICKET_REDIS_PREFIX+ticket);
        if(ticketJson != null){
            log.info("ticket json: {}", ticketJson);
            ThirdPartyTicket ticketPojo = JSON.parseObject(ticketJson, ThirdPartyTicket.class);
            return ticketPojo;
        }else{
            log.error("ticket json is empty");
            throw new AdmException(AdmErrorType.TICKET_VERIFY_FAIL);
        }
    }
}
