package com.cyberscraft.uep.adm.core.service.user.dingtalk;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.cyberscraft.uep.adm.core.constant.ConnectorExceptionConstant;
import com.cyberscraft.uep.adm.core.service.user.CallbackConnector;
import com.cyberscraft.uep.adm.core.service.user.ConnectorConfig;
import com.cyberscraft.uep.adm.core.service.user.UpdatingPolicy;
import com.cyberscraft.uep.adm.core.service.user.exception.ConnectorException;
import com.cyberscraft.uep.adm.core.service.user.util.UrlUtil;
import org.apache.commons.lang3.StringUtils;


public class DingTalkConfig implements ConnectorConfig {

    @JsonProperty(value = "interneturl", required = true)
    private String internetUrl;
    @JsonProperty(value = "intraneturl")
    private String intranetUrl;
    @JsonProperty(value = "appkey")
    private String appKey;
    @JsonProperty(value = "appsecret")
    private String appSecret;
    private String token;
    @JsonProperty(value = "corpid")
    private String corpId;
    @JsonProperty(value = "agentid")
    private String agentId;
    private String dataAESKey;
    @JsonProperty(value = "autocancel")
    private String autoCancel;

    @Override
    public CallbackConnector open(Long id, UpdatingPolicy updatingPolicy) throws ConnectorException{
        return new DingTalkConnector(id, updatingPolicy, this);
    }

    @Override
    public void handleConfigChange(Long syncConnectorId, ConnectorConfig newConfig) throws ConnectorException{


    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public String getToken() {
        return this.token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getInternetUrl() {
        return internetUrl;
    }

    public void setInternetUrl(String internetUrl) {
        this.internetUrl = internetUrl;
    }

    public String getIntranetUrl() {
        return intranetUrl;
    }

    public void setIntranetUrl(String intranetUrl) {
        this.intranetUrl = intranetUrl;
    }

    public String getCorpId() {
        return corpId;
    }

    public void setCorpId(String corpId) {
        this.corpId = corpId;
    }

    public String getAgentId() {
        return agentId;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public String getDataAESKey() {
        return dataAESKey;
    }

    public void setDataAESKey(String dataAESKey) {
        this.dataAESKey = dataAESKey;
    }

    public String getAutoCancel() {
        return autoCancel;
    }

    public void setAutoCancel(String autoCancel) {
        this.autoCancel = autoCancel;
    }

    public void validate() throws ConnectorException {
        if (StringUtils.isBlank(internetUrl)){
            throw new ConnectorException(ConnectorExceptionConstant.CONNECTOR_INVALID_PARAM, "interneturl required");
        }

        if (!UrlUtil.pingURL(internetUrl, 200)){
            throw new ConnectorException(ConnectorExceptionConstant.CONNECTOR_INVALID_PARAM, "interneturl not reachable");
        }

        if (StringUtils.isBlank(appKey)){
            throw new ConnectorException(ConnectorExceptionConstant.CONNECTOR_INVALID_PARAM, "appKey required");
        }

        if (StringUtils.isBlank(appSecret)){
            throw new ConnectorException(ConnectorExceptionConstant.CONNECTOR_INVALID_PARAM, "appSecret required");
        }
    }
}
