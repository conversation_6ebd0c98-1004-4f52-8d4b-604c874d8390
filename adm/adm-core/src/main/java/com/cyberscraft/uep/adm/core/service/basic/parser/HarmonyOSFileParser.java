package com.cyberscraft.uep.adm.core.service.basic.parser;

import com.cyberscraft.uep.adm.core.domain.AppInfo;
import com.cyberscraft.uep.adm.core.domain.AppParseRequest;
import com.cyberscraft.uep.adm.core.exception.AdmException;
import com.cyberscraft.uep.adm.core.service.basic.IAppParser;
import com.cyberscraft.uep.common.enums.Platform;
import org.springframework.stereotype.Service;

import java.io.File;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * @Description
 * <AUTHOR>
 * @Date 2025/1/22 16:47
 */
@Service
public class HarmonyOSFileParser extends AbastractFileParser implements IAppParser {

    private final static Set<Integer> SUPPORTED_PLATFORM = new HashSet<>();

    static {
        SUPPORTED_PLATFORM.add(Platform.HarmonyOS.getValue());
    }

    @Override
    public Set<Integer> getSupportedPlatform() {
        return SUPPORTED_PLATFORM;
    }

    @Override
    public boolean isSupported(Integer platform) {
        return SUPPORTED_PLATFORM.contains(platform);
    }

    @Override
    public AppInfo parse(AppParseRequest request) throws AdmException {
        AppInfo ret = new AppInfo();
        try {
            File lecFile = new File(request.getFilePath());
//            ret.setSize(getAppSize(lecFile, request.getFilePath()));
//            //根据srcFilePath,文件名称进行解析

            ret.setSize(0L);
            ret.setIconElemList(Arrays.asList(lecFile.getName(), lecFile.getName()));
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
        ret.setPublishTime(LocalDateTime.now());
        return ret;
    }
}
