package com.cyberscraft.uep.adm.core.enums;

/***
 *
 * @date 2021-10-21
 * <AUTHOR>
 ***/
public enum TenantStatusEnum {
    /***
     * 准备创建中
     */
    CREATING(0),
    /***
     * 正常状态
     */
    NORMAL(1),
    /***
     * 错误状态，即上次执行出错
     */
    ERROR(2),
    /***
     * 待重新创建
     */
    RETRYING(3),
    /***
     * 未知状态
     */
    UNKOWN(4),

    /***
     * 禁用状态
     */
    DISABLE(5);

    private int code;

    TenantStatusEnum(int code) {
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }
}
