package com.cyberscraft.uep.adm.core.service.vpp;

import com.cyberscraft.uep.adm.core.domain.AppSimpleInfo;
import com.cyberscraft.uep.adm.core.entity.VppStoreEntity;
import com.cyberscraft.uep.adm.core.exception.AdmException;
import com.cyberscraft.uep.adm.dto.vpp.AppInfoVO;
import com.cyberscraft.uep.adm.dto.vpp.TokenInfoVO;

import java.util.List;

/**
 * VPP应用同步相关服务
 */
public interface IAppleVppService {

    /**
     * 上传某一租户vpp token
     * @param tenantId 租户ID
     * @param sToken
     */
    public void uploadVppToken(String tenantId, String sToken);

    /**
     * 获取某一租户的原始token全字符串
     * @param tenantId
     * @return
     */
    public String getOriginToken(String tenantId) throws AdmException;

    /**
     * 解析某一租户的token
     * @param tenantId
     * @return
     */
    public TokenInfoVO parseToken(String tenantId) throws AdmException;

    /**
     * 同步vpp应用列表
     * @param tenantId
     * @param freshAllAppInfo 是否刷新全部应用详情
     * @throws AdmException
     */
    public void syncAssetsFromVpp(String tenantId, Boolean freshAllAppInfo) throws AdmException;

    /**
     * 获取默认vpp应用
     * @param tenantId
     * @return
     */
    public VppStoreEntity getDefaultVppEntity(String tenantId);

    /**
     * 获取默认客户端应用详情
     * @param tenantId
     * @param freshInfo 是否刷新应用详情
     * @return
     */
    public AppInfoVO getDefaultAppInfo(String tenantId, Boolean freshInfo);

    /**
     * 设置默认应用到redis缓存中
     * @param appId
     * @return
     */
    public AppInfoVO setDefaultApp(Long appId);

    /**
     * 从redis缓存中获取默认应用
     * @return
     */
    public AppSimpleInfo getDefaultApp(String tCode);


    /**
     * 根据appId获取应用详情
     * @param appId
     * @param tenantId
     * @param freshInfo 是否刷新该应用详情
     * @return
     */
    public AppInfoVO getAppInfoById(Long appId, String tenantId, Boolean freshInfo);

    /**
     * 获取应用列表
     * @param tenantId
     * @param freshAll 是否刷新应用列表和全部应用详情
     * @return
     */
    public List<AppInfoVO> getAppInfoList(String tenantId, Boolean freshAll);


    /**
     * 刷新vpp应用详情
     * @param tenantId
     * @param vppStore
     * @return
     * @throws AdmException
     */
    public boolean freshVpp(String tenantId, VppStoreEntity vppStore) throws AdmException;

    /**
     * vpp用户注册
     * @param tenantId
     * @param userId
     * @param manageId
     * @return inviteUrl
     */
    String registerVppUser(String tenantId, String userId, String manageId) throws AdmException;

    /**
     * 管理式授权
     * @param tenantId
     * @param adamId
     * @param pricingParam
     * @param userId
     * @return licenseIdStr
     */
    String manageVPPByAdamIdAndUserId(String tenantId, long adamId, String pricingParam, String userId) throws AdmException;

}
