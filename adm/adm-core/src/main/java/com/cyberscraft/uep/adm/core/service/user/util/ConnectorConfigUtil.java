package com.cyberscraft.uep.adm.core.service.user.util;

import com.cyberscraft.uep.adm.core.constant.ConnectorExceptionConstant;
import com.cyberscraft.uep.adm.core.constant.Constants;
import com.cyberscraft.uep.adm.core.service.user.CallbackConnector;
import com.cyberscraft.uep.adm.core.service.user.ConnectorConfig;
import com.cyberscraft.uep.adm.core.service.user.UpdatingPolicy;
import com.cyberscraft.uep.adm.core.service.user.dingtalk.DingTalkConfig;
import com.cyberscraft.uep.adm.core.service.user.exception.ConnectorException;
import com.cyberscraft.uep.adm.core.utils.JsonJacksonUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.concurrent.TimeUnit;

public class ConnectorConfigUtil {
    private static Logger logger = LoggerFactory.getLogger(ConnectorConfigUtil.class);

    public static ConnectorConfig toConnectorConfig(String type, Object config) throws ConnectorException {
        if (StringUtils.isBlank(type)) {
            logger.warn("empty connector type");
            throw new ConnectorException(101, "empty connector type");
        }

        try {
                DingTalkConfig dingTalkConfig = null;
                if (config instanceof String) {
                    dingTalkConfig = JsonJacksonUtil.str2Obj((String)config, DingTalkConfig.class);
                }else if (config instanceof Map) {
                    Map<String, Object> map = (Map) config;
                    dingTalkConfig = JsonJacksonUtil.convert(trim(map),DingTalkConfig.class);
                }else {
                    logger.warn("connector type not supported: {}", type);
                    throw new ConnectorException(ConnectorExceptionConstant.CONNECTOR_CONFIG_ERROR, "connector config can't be match");
                }
                return dingTalkConfig;
        } catch (IllegalArgumentException e) {
            logger.warn("illegal connector type: {}", type);
            return null;
        } catch (RuntimeException e) {
            logger.error("failed to convert connector config with type" + type, e);
            return null;
        }

    }

    private static Map<String, Object> trim(Map<String, Object> map) {
        for (String key: map.keySet()){
            Object value = map.get(key);
            if (value != null && value instanceof String){
                map.put(key, ((String)value).trim());
            }
        }
        return map;
    }

    public static CallbackConnector getCallbackConnector(Long id, String type, UpdatingPolicy updatingPolicy, Object config) throws ConnectorException {
        ConnectorConfig connectorConfig = toConnectorConfig(type, config);
        return connectorConfig.open(id, updatingPolicy);
    }

    /**
     * 检测是否有其他任务在执行如果存在则等待
     */
    public static void thridCheckSleep(){
        try {
            TimeUnit.SECONDS.sleep(2);
        }catch (Exception e){
            logger.error("Thread exception", e);
        }

    }

    /**
     * 根据tconnector 类型 判断用户的 type字段值
     * @param type
     * @return
     */
    public static int getConnectorUserType(String type ){
         return Constants.USER_TYPE_CONNECTOR_DINGTALK;
    }
}
