package com.cyberscraft.uep.adm.core.dbo;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cyberscraft.uep.adm.core.entity.SysConfigEntity;

import java.util.List;

/**
 * <p>
 * 系统配置表 服务类
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2020-05-21
 */
public interface SysConfigDBO extends IService<SysConfigEntity> {

    /**
     * 根据关键字获取配置
     * @param key
     * @return
     */
    SysConfigEntity getByKey(String key);

    /**
     * 获取租户列表
     * @return
     */
    List<SysConfigEntity> getTenantConfigList(String key) ;

    /**
     * 保存配置信息
     * @param configEntity
     * @return
     */
    boolean save(SysConfigEntity configEntity);

    /**
     * 更新配置信息
     * @param configEntity
     * @return
     */
    boolean updateById(SysConfigEntity configEntity);

}
