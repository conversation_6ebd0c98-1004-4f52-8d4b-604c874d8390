package com.cyberscraft.uep.adm.core.domain;

import java.io.Serializable;

/***
 * 进度对像
 * @date 2021-12-13
 * <AUTHOR>
 ***/
public class OperProgress<T> implements Serializable {

    /****
     * 进度的ID
     */
    private Long id;
    /***
     * 进度的状态，参考具体的业务中的进度状态定义
     */
    private Integer result;

    /****
     * 当前进度执行时，需要对外展示的消息,或者错误信息
     */
    private String msg;

    /***
     * 操作进度对应的数据信息
     */
    private T data;

    public OperProgress() {
    }

    public OperProgress(Long id, Integer result, String msg, T data) {
        this.id = id;
        this.result = result;
        this.msg = msg;
        this.data = data;
    }

    public OperProgress(Long id, Integer result) {
        this.id = id;
        this.result = result;
    }

    public OperProgress(Long id, Integer result, String msg) {
        this.id = id;
        this.result = result;
        this.msg = msg;
    }

    public OperProgress(Long id, Integer result, T data) {
        this.id = id;
        this.result = result;
        this.data = data;
    }

    public Integer getResult() {
        return result;
    }

    public void setResult(Integer result) {
        this.result = result;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
}
