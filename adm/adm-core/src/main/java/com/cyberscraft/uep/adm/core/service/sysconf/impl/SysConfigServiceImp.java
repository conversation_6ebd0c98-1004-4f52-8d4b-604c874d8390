package com.cyberscraft.uep.adm.core.service.sysconf.impl;

import com.cyberscraft.uep.adm.core.constant.ConfigConstant;
import com.cyberscraft.uep.adm.core.dbo.SysConfigDBO;
import com.cyberscraft.uep.adm.core.entity.SysConfigEntity;
import com.cyberscraft.uep.adm.core.enums.AdmErrorType;
import com.cyberscraft.uep.adm.core.exception.AdmException;
import com.cyberscraft.uep.adm.core.service.sysconf.ISysConfigService;
import com.cyberscraft.uep.adm.core.service.sysconf.transfer.ISysConfigTransfer;
import com.cyberscraft.uep.adm.dto.sysconf.AppDownloadConfigVO;
import com.cyberscraft.uep.adm.dto.sysconf.DownloadConfigVO;
import com.cyberscraft.uep.adm.dto.sysconf.SysConfigDto;
import com.cyberscraft.uep.adm.dto.sysconf.SysConfigVo;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.common.util.StringUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Date: 2020/7/1
 * description:
 *
 * <AUTHOR>
 */
@Service
public class SysConfigServiceImp implements ISysConfigService {
    @Resource
    private SysConfigDBO configDBO;
    @Resource
    private ISysConfigTransfer configTransfer;

    private String SECRET_FIELD = "extracted_code";

    @Override
    public boolean saveOrUpdateClientConfig(SysConfigDto dto) {
        SysConfigEntity configEntity = configDBO.getByKey(dto.getConfigKey());
        if(configEntity == null){
            configEntity = configTransfer.dtoToEntity(dto);
            configEntity.setCreateTime(LocalDateTime.now());
            configDBO.save(configEntity);
        }else{
            this.updateClientConfig(dto);
        }

        return true;
    }

    private boolean updateClientConfig(SysConfigDto dto) {
        SysConfigEntity configEntity = configDBO.getByKey(dto.getConfigKey());
        if(configEntity == null){
            throw  new AdmException(AdmErrorType.CONFIG_KEY_NOT_EXIST);
        }

        configEntity.setConfigValue(dto.getConfigValue());
        configEntity.setUpdateTime(LocalDateTime.now());
        configDBO.updateById(configEntity);
        return true;
    }

    @Override
    public SysConfigVo getByKey(String key, boolean hideSecretField) {
        SysConfigEntity configEntity = configDBO.getByKey(key);
        if(configEntity == null && ConfigConstant.CONFIG_KEY_APP_DOWNLOAD_CONFIG.equals(key)){
            configEntity = configDBO.getByKey(ConfigConstant.CONFIG_KEY_DOWNLOAD_CONFIG);

            if(configEntity != null){
                String configValue = configEntity.getConfigValue();
                DownloadConfigVO downloadConfigVO = JsonUtil.str2Obj(configValue, DownloadConfigVO.class);
                AppDownloadConfigVO appDownloadConfigVO = configTransfer.downloadConfig2AppDownloadConfig(downloadConfigVO);
                String appConfigValue = JsonUtil.obj2Str(appDownloadConfigVO);

                configEntity.setConfigValue(appConfigValue);
                configEntity.setConfigKey(ConfigConstant.CONFIG_KEY_APP_DOWNLOAD_CONFIG);

            //如果没有config这里给一个appdownload的默认config以避免下载页报错
            }else{
                configEntity = new SysConfigEntity();
                configEntity.setConfigKey(ConfigConstant.CONFIG_KEY_APP_DOWNLOAD_CONFIG);
                configEntity.setConfigValue("{\"verifyConf\":{\"type\":\"NONE\",\"extractConf\":{\"title\":\"欢迎使用数犀应用分发平台\",\"validationTip\":\"\",\"tip\":\"\",\"code\":\"\"}}}");
                configDBO.save(configEntity);
            }
        }


        SysConfigVo vo = configTransfer.entityToVo(configEntity);
        if(vo != null && hideSecretField) {
            String value = JsonUtil.hideSecretFieldInJson(SECRET_FIELD, vo.getConfigValue());
            vo.setConfigValue(value);
        }
        return vo;
    }

    @Override
    public Map<String, Object> getMap(String key) {
        Map<String, Object> objectMap = new HashMap<>();
        SysConfigVo downloadConfig = getByKey(key, false);
        if(downloadConfig != null && !StringUtil.isEmptyOrNull(downloadConfig.getConfigValue())) {
            objectMap = JsonUtil.str2Map(downloadConfig.getConfigValue());
        }
        return objectMap;
    }

    @Override
    public String getConfigValueByKey(String key, boolean hideSecretField) {
        SysConfigEntity configEntity = configDBO.getByKey(key);
        String configValue = configEntity.getConfigValue();
        if(hideSecretField) {
            configValue = JsonUtil.hideSecretFieldInJson(SECRET_FIELD, configValue);
        }
        return configValue;
    }
}
