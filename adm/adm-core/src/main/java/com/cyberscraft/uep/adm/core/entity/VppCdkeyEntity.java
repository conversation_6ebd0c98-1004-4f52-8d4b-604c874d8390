package com.cyberscraft.uep.adm.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;

/***
 *
 * @date 2021-12-11
 * <AUTHOR>
 ***/
@TableName("t_vpp_cdkey")
public class VppCdkeyEntity implements Serializable {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /****
     * 订单号
     */
    private String orderId;

    /****
     * 用户longinId
     */
    private String loginId;

    /****
     *苹果应用商店应用id
     */
    private Long adamId;


    /****
     * vppappid
     */
    private Long vppAppId;

    /***
     * 0：未使用，1已使用
     */
    private Integer status;

    /***
     * 兑换码
     */
    private String cdkey;

    /***
     * inviteUrl
     */
    private String inviteUrl;

    /****
     *
     */
    private LocalDateTime createTime;

    /***
     *
     */
    private LocalDateTime updateTime;

    /**
     * 租户ID
     */
    private String tenantId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getLoginId() {
        return loginId;
    }

    public void setLoginId(String loginId) {
        this.loginId = loginId;
    }

    public Long getAdamId() {
        return adamId;
    }

    public void setAdamId(Long adamId) {
        this.adamId = adamId;
    }

    public Long getVppAppId() {
        return vppAppId;
    }

    public void setVppAppId(Long vppAppId) {
        this.vppAppId = vppAppId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getCdkey() {
        return cdkey;
    }

    public void setCdkey(String cdkey) {
        this.cdkey = cdkey;
    }

    public String getInviteUrl() {
        return inviteUrl;
    }

    public void setInviteUrl(String inviteUrl) {
        this.inviteUrl = inviteUrl;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }
}
