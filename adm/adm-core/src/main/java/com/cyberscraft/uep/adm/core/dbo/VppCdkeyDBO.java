package com.cyberscraft.uep.adm.core.dbo;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cyberscraft.uep.adm.core.entity.VppCdkeyEntity;
import com.cyberscraft.uep.common.dto.QueryPage;
import com.cyberscraft.uep.common.util.query.QueryCondition;
import java.util.List;
import java.util.Map;

/***
 *
 * @date 2021-12-11
 * <AUTHOR>
 ***/
public interface VppCdkeyDBO extends IService<VppCdkeyEntity> {
    /**
     * 根据orderId获取对象
     * @param orderId
     * @return
     */
    VppCdkeyEntity getByOrderId(String orderId);

    /**
     * 根据cdkey获取对象
     * @param cdkey
     * @return
     */
    VppCdkeyEntity getByCdkey(String cdkey);

    VppCdkeyEntity getUnusedCdkey(String cdkey);

//    /**
//     * 获取指定应用的兑换码
//     * @param adamId
//     * @return
//     */
//    List<VppCdkeyEntity> getCdkeyByAdamin(Long adamId);
    /**
     * 获取指定应用的兑换码
     * 指定数量，未使用的兑换码
     * @param adamId
     * @param num
     * @return
     */
    List<VppCdkeyEntity> getCdkeyByNum(Long adamId, int num);

    /**
     * 一个未使用的cdkey对象
     * @param adamId
     * @return
     */
    VppCdkeyEntity getOneCdkey(Long adamId);
    /**
     * 根据loginid获取cdkey对象
     * @param loginId
     * @return
     */
    VppCdkeyEntity getByLoginId(String loginId, Long adamId);

    /**
     * 获取后续N条状态为0的记录
     * @return
     */
    List<VppCdkeyEntity> getNextAvailableRecords(Long adamId, Long startPosition, int limit);

    boolean updateWithCondition(VppCdkeyEntity cdkeyEntity, int status, boolean loginIdIsNull);

    /**
     * 分页查询兑换码
     * @param queryCondition
     * @return
     */
    QueryPage<VppCdkeyEntity> page(QueryCondition queryCondition);

    /**
     * 分页查询接口，配合通用分页查询工具类方法使用
     * @param page
     * @param queryWrapper
     * @return
     */
    IPage<VppCdkeyEntity> selectPage(Page<VppCdkeyEntity> page, QueryWrapper<VppCdkeyEntity> queryWrapper);

    /**
     * 统计兑换码使用状态
     * @param adamId
     * @return
     */
    List<Map<String, Object>> countCdkeyStatus(Long adamId);

    /**
     * 获取最新的兑换码ID
     * @param adamId
     * @return
     */
    Long maxCdkeyId(Long adamId);
}
