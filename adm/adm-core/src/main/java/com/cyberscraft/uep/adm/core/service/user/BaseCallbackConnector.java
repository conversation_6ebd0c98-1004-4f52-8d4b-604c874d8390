package com.cyberscraft.uep.adm.core.service.user;

import com.alibaba.fastjson.JSONArray;
import com.cyberscraft.uep.adm.core.constant.Constants;
import com.cyberscraft.uep.adm.core.constant.SyncConstant;
import com.cyberscraft.uep.adm.core.constant.ThreadPoolNameConstant;
import com.cyberscraft.uep.adm.core.dbo.ConnectorDBO;
import com.cyberscraft.uep.adm.core.dbo.GroupDBO;
import com.cyberscraft.uep.adm.core.dbo.UserDBO;
import com.cyberscraft.uep.adm.core.entity.ConnectorEntity;
import com.cyberscraft.uep.adm.core.entity.GroupEntity;
import com.cyberscraft.uep.adm.core.entity.UserEntity;
import com.cyberscraft.uep.adm.core.enums.AdmErrorType;
import com.cyberscraft.uep.adm.core.enums.ConnectorType;
import com.cyberscraft.uep.adm.core.service.user.exception.ConnectorException;
import com.cyberscraft.uep.adm.core.service.user.util.ConnectorConfigUtil;
import com.cyberscraft.uep.adm.core.service.user.util.DaoUtil;
import com.cyberscraft.uep.common.service.ServiceLocator;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.counting;


public abstract class BaseCallbackConnector implements CallbackConnector {
    protected Long id;
    protected UpdatingPolicy updatingPolicy;
    private static Logger logger = LoggerFactory.getLogger(BaseCallbackConnector.class);
    public static String SYS_USER_PARENT_ID = "admin";
    private GroupDBO groupDBO = ServiceLocator.getBean(GroupDBO.class);
    private UserDBO userDBO = ServiceLocator.getBean(UserDBO.class);
    private ConnectorDBO connectorDBO = ServiceLocator.getBean(ConnectorDBO.class);




    private String getSyncStatusKey(String tenantId){
        String SYNC_RUNNING_KEY = "TENANT:%s:CONNECTORS:TYPE:%s:SYNC_STATUS";
        return String.format(SYNC_RUNNING_KEY, tenantId, getConnectorType());
    }


    /**
     * sync connector users and grous
     * NOTE: since wework supports user in multiple groups,
     * we are doing a sync users per group logic.
     * So SyncStats is a rough stats, because user might be updated twice. or be insert first and update later
     *
     * @param tConnector
     * @return
     */
    private Void syncConnectorDataAsync(ConnectorEntity tConnector){
        logger.debug("sync.......");
        SyncStats syncStats = new SyncStats();
        //Mark sync as in progress, increase batch no
        try {
            if(!markSyncAsRunning(tConnector.getTenantId(),tConnector.getType().toLowerCase())){
                return null;
            }
            while (syncCallBackStillRunning(tConnector.getTenantId(),tConnector.getType().toLowerCase())){
                ConnectorConfigUtil.thridCheckSleep();
            }

            tConnector.setSyncBatchNo(tConnector.getSyncBatchNo() + 1);
            tConnector.setLastSyncStartTime(LocalDateTime.now());
            SyncGroupInfo rootGroup = null;
            try {
                rootGroup = getSyncGroupTree();

                HashMap<String, Long> syncedGroupMap = saveSyncGroup(
                        0L, getSyncRootGroupCode(tConnector), tConnector,
                        rootGroup, syncStats);
                List<CompletableFuture<SyncStats>> syncStatsFutures =
                        syncedGroupMap.keySet().stream()
                                .map(syncGroupId ->
                                        CompletableFuture.supplyAsync(
                                                () -> saveUsersByGroup(syncGroupId, syncedGroupMap.get(syncGroupId), tConnector)
                                                ,(ThreadPoolTaskExecutor)ServiceLocator.getBean(ThreadPoolNameConstant.COMMON_TASK_NAME)
                                        ))
                                .collect(Collectors.toList());

                List<SyncStats> listSyncStats = syncStatsFutures.stream()
                        .map(CompletableFuture::join)
                        .collect(Collectors.toList());
                syncStats.addAll(listSyncStats);

                cleanHaveDeletedUsersAndGroups(tConnector, syncStats);

                tConnector.setLastSyncStatus("SUCCESS");
                tConnector.setLastSyncEndTime(LocalDateTime.now());
                tConnector.setLastSyncStats(syncStats.toJson());
            } catch (ConnectorException e) {
                logger.error("sync failed", e);
                tConnector.setLastSyncStatus("FAILED");
            }
            connectorDBO.updateById(tConnector);
        }finally {
            markSyncAsFinished(tConnector.getTenantId(),tConnector.getType().toLowerCase());
        }
        if(ConnectorType.DINGTALK.name().toLowerCase().equals(tConnector.getType().toLowerCase())){
            logger.info("start call back task ");
            doSyncGroupWait(tConnector);
            doSyncUserWait(tConnector);
        }
        return null;
    }

    @Override
    public void sync(ConnectorEntity tConnector) throws ConnectorException {
        CompletableFuture.supplyAsync(() -> syncConnectorDataAsync(tConnector), (ThreadPoolTaskExecutor)ServiceLocator.getBean(ThreadPoolNameConstant.COMMON_TASK_NAME));
    }

    @Override
    public Map<String, Object> syncRegisterCancel(ConnectorEntity tConnector) {
        Map<String, Object> resultMap ;
        if (tConnector.getCallBackStatus().equals(1)) {
            resultMap = new HashMap<>();
            if (cancelCallBack()){
                tConnector.setCallBackStatus(0);
                connectorDBO.updateById(tConnector);
                resultMap.put("code","1");
                return resultMap;
            }else {
                resultMap.put("code","0");
                resultMap.put("admErrorType", AdmErrorType.SYNC_TYPE_INTERFACE_FAIL);
                return resultMap;
            }
        } else {
            resultMap = new HashMap<>();
            resultMap.put("errcode","0");
            resultMap.put("errmsg",AdmErrorType.SYNC_TYPE_INVALID_FAIL);
            return resultMap;
        }
    }

    @Override
    public Map<String, Object> syncRegister(ConnectorEntity tConnector){
        Map<String, Object> resultMap = new HashMap<>();
        if (tConnector.getCallBackStatus() == null || tConnector.getCallBackStatus().equals(0) ) {
            if (registerCallBack()) {
                tConnector.setCallBackStatus(1);
                connectorDBO.updateById(tConnector);
                resultMap.put("code","1");
                return resultMap;
            } else {
                resultMap.put("code","0");
                resultMap.put("admErrorType", AdmErrorType.SYNC_TYPE_INTERFACE_FAIL);
                return resultMap;
            }
        } else {
            if (updateCallBack()){
                resultMap.put("code","1");
                return resultMap;
            } else {
                resultMap.put("code","0");
                resultMap.put("admErrorType", AdmErrorType.SYNC_TYPE_INTERFACE_FAIL);
                return resultMap;
            }
        }
    }

    @Override
    public  void syncUser(JSONArray userIds, ConnectorEntity tConnector, String eventType) throws ConnectorException {
//
        if(SyncConstant.USER_MODIFY_ORG.equals(eventType)){
            String tgroupId = null ;
            for(Object thirdUserId : userIds){
                Map<String,SyncUserInfo> userInfoMap = getUsersByUserThirdUserId((String) thirdUserId);
                logger.info("user_modify_org"+(String) thirdUserId + userInfoMap.get(tgroupId));
                for(String key : userInfoMap.keySet()){
                    tgroupId = key;
                    break;
                }
                GroupEntity tGroup = DaoUtil.getGroupByThirdGroupId(tConnector.getId(),tgroupId);
                if (tGroup == null){
                    logger.warn("ThirdUserId not found group uem ");
                } else {
                    UserEntity user = DaoUtil.getUserByLoginId(userInfoMap.get(tgroupId).getLoginId());
                    if (user == null){
                        logger.warn(userInfoMap.get(tgroupId).getLoginId() +" not found in uem ");
                    } else {
                        saveOrUpdateUser(tGroup.getId(), tConnector, userInfoMap.get(tgroupId));
                    }

                }
            }
        }
//
        if (SyncConstant.USER_ADD_ORG.equals(eventType) ){
            String tgroupId = null ;
            for(Object thirdUserId : userIds){
                logger.info("user_add_org" + (String) thirdUserId);
                Map<String,SyncUserInfo> userInfoMap = getUsersByUserThirdUserId((String) thirdUserId);
                logger.debug("user_add_org"+(String) thirdUserId + userInfoMap.get(tgroupId));
                for(String key : userInfoMap.keySet()){
                    tgroupId = key;
                    break;
                }
                GroupEntity tGroup = DaoUtil.getGroupByThirdGroupId(tConnector.getId(),tgroupId);
                if (tGroup == null){
                    logger.warn("ThirdUserId not found group uem  ");
                    syncGroup(JSONArray.parseArray("["+tgroupId+"]"), tConnector, "org_dept_create");
                    tGroup = DaoUtil.getGroupByThirdGroupId(tConnector.getId(),tgroupId);
                    if (tGroup == null ){
                        logger.warn("ThirdUserId add group error  ");
                        continue;
                    }
                    saveOrUpdateUser(tGroup.getId(), tConnector, userInfoMap.get(tgroupId));
                } else {
                    saveOrUpdateUser(tGroup.getId(), tConnector, userInfoMap.get(tgroupId));
                }
            }
        }
//
        if(SyncConstant.USER_LEAVE_ORG.equals(eventType)){
            for(Object thirdUserId : userIds){
                UserEntity user = DaoUtil.getUserByLoginId((String) thirdUserId);
                if (user != null ){
                    // 状态1-启用自动删除用户会擦除用户下注册设备 0-不启用
                    if ("1".equals(getAutoCancel())){
                        logger.info("user id "+(String) thirdUserId +" uem "+user.getLoginId()+" will be delete ");
                        if (safeToDeleteUser((String) thirdUserId)){
                            DaoUtil.markSyncUserStatusAsDelete(user.getId());
                        }
                    }else {
                        DaoUtil.markSyncUserStatusAsInvalid(user.getId());
                    }
                }
            }
        }
    }

    @Override
    public void syncGroup(JSONArray GroupIds, ConnectorEntity tConnector, String eventType) throws ConnectorException {
        try {

        }finally {

        }
        if (SyncConstant.ORG_DEPT_REMOVE.equals(eventType)){
            for(Object thirdGroupId : GroupIds){
                GroupEntity tGroup = DaoUtil.getGroupByThirdGroupId(tConnector.getId(),thirdGroupId.toString());
                if (tGroup != null){
                    List<Long> deleteUserList = DaoUtil.getUserIdByGroupId(ConnectorConfigUtil.getConnectorUserType(tConnector.getType()),tGroup.getId(),tConnector.getId());
                   // 更新
                    for (Long id :deleteUserList){
                        DaoUtil.markSyncUserStatusAsInvalid(id);
                    }
                }

            }
        }
        // 新增部门时处理流程
        if (SyncConstant.ORG_DEPT_CREATE.equals(eventType) ){
            SyncStats syncStats = new SyncStats();
            for(Object thirdGroupId : GroupIds){
                GroupEntity tGroup = DaoUtil.getGroupByThirdGroupId(tConnector.getId(),thirdGroupId.toString());
                if (tGroup != null && tGroup.getId() > 0){
                    continue;
                }
                Map<String,SyncGroupInfo> groupInfo = getSyncGroupInfoById(thirdGroupId.toString());
                if (groupInfo != null && groupInfo.get(thirdGroupId.toString()) != null  ){
                    GroupEntity tGroupParentid = DaoUtil.getGroupByThirdGroupId(tConnector.getId(),thirdGroupId.toString());
                    if (tGroupParentid != null ){
                        saveSyncGroup(tGroupParentid.getId(), DaoUtil.getGroupCodeByParent(tGroupParentid.getId(),tGroupParentid.getGroupCode()), tConnector, groupInfo.get(thirdGroupId.toString()), syncStats);
                        continue;
                    }
                    // 如添加部门的父节点在uem不存在 ，则递增查询父节点直到在uem系统查到
                    syncSavaGroup(groupInfo.get(thirdGroupId.toString()),thirdGroupId.toString(),tConnector,syncStats);

                }
            }
        }

        // 部门变更类型处理
        if (SyncConstant.ORG_DEPT_MODIFY.equals(eventType)){
            for(Object thirdGroupId : GroupIds){
                GroupEntity tGroup = DaoUtil.getGroupByThirdGroupId(tConnector.getId(),thirdGroupId.toString());
                Map<String,SyncGroupInfo> groupInfo = getSyncGroupInfoById(thirdGroupId.toString());
                if (groupInfo != null && groupInfo.get(thirdGroupId.toString()) != null  ){
                    GroupEntity tGroupParentid = DaoUtil.getGroupByThirdGroupId(tConnector.getId(),groupInfo.get(thirdGroupId.toString()).getSyncParentid());
                    // 判断父节点的用户组在uem系统是否存在，存在则更新父节点id和 groupcode 不存在仅更新name
                    if (tGroupParentid != null ){
                        tGroup.setName(groupInfo.get(thirdGroupId.toString()).getGroupName());
                        String groupCode = DaoUtil.getGroupCodeByParent(tGroupParentid.getId(),tGroupParentid.getGroupCode());
                        if (StringUtils.isEmpty(groupCode)){
                            logger.warn(String.valueOf(tGroup.getId())+":"+tGroup.getName()+" can not make groupcode in uem , only update name ");
                        } else {
                            tGroup.setParentId(tGroupParentid.getId());
                            tGroup.setGroupCode(groupCode);
                        }
                        groupDBO.updateById(tGroup);
                    } else {
                        logger.info(String.valueOf(tGroup.getId())+":"+tGroup.getName()+" can not found Parentid in uem , only update name ");
                        tGroup.setName(groupInfo.get(thirdGroupId.toString()).getGroupName());
                        groupDBO.updateById(tGroup);
                    }

                }
            }
        }
    }

    /**
     * sync connector users and grous
     * NOTE: since wework supports user in multiple groups,
     * we are doing a sync users per group logic.
     * So SyncStats is a rough stats, because user might be updated twice. or be insert first and update later
     *
     * @param
     * @return
     */

    protected SyncStats saveUsersByGroup(String syncGroupId, Long tgroupId, ConnectorEntity tConnector){
        SyncStats stats = new SyncStats();
        logger.info("****"+syncGroupId+"****"+String.valueOf(tgroupId));

        long startTime = System.currentTimeMillis();

        List<? extends SyncUserInfo> userInfoList = null;
        try{
            userInfoList = getUsersByGroupId(syncGroupId);
        }catch(Exception e){
            logger.error("saveUsersByGroup: syncGroupId {} ",syncGroupId, e);
            return stats;
        }

        int count = 0;
        for (SyncUserInfo userInfo : userInfoList) {
            ++count;
            try{
                stats.add(saveOrUpdateUser(tgroupId, tConnector, userInfo));
            }catch(Exception e){
                logger.error("saveOrUpdateUser user: {}", userInfo.getLoginId(), e);
            }

            if (count == 100) {
                count = 0;
            }
        }
        // flush the rest users
        if (count>0 && count < 100){

        }

        long endTime = System.currentTimeMillis();
        logger.info("sync group {} took {} ms ", syncGroupId, endTime - startTime);

        return stats;
    }

    private void cleanHaveDeletedUsersAndGroups(ConnectorEntity tConnector, SyncStats syncStats) {
        syncStats.deletedGroupCount += cleanGroups(tConnector);
        syncStats.deletedUserCount += cleanUsers(tConnector);
        cleanSysUsers(tConnector);
    }

    private long cleanGroups(ConnectorEntity tConnector) {
//        // 本次可能需要删除的组
        List<GroupEntity> deleteGroupList = groupDBO.getNotEqBatchNoGroupsByType(String.valueOf(Constants.GROUP_TYPE_CONNECTOR),tConnector.getSyncBatchNo());
        if (deleteGroupList == null || deleteGroupList.size() ==0 ){
            return 0;
        }
        logger.debug(deleteGroupList.toString());
        List<CompletableFuture<Long>> futures = deleteGroupList.stream()
                .map(groupEntity ->
                        CompletableFuture.supplyAsync(
                                () -> deleteGroup(groupEntity.getThirdGroupId(), groupEntity.getName(), groupEntity.getId())
                                , (ThreadPoolTaskExecutor)ServiceLocator.getBean(ThreadPoolNameConstant.COMMON_TASK_NAME)
                        ))
                .collect(Collectors.toList());

        return futures.stream()
                .map(CompletableFuture::join)
                .filter(tgroupId -> tgroupId != null)
                .collect(counting());
    }

    private Long deleteGroup(String thirdGroupId, String groupName, Long tgroupId) {
        if (safeToDeleteGroup(thirdGroupId)) {
            logger.info("group {} {} does not exist, safe to delete it", groupName, thirdGroupId);
            groupDBO.setStatusById(tgroupId,0);
            return tgroupId;
        }
        return null;
    }

    private Long deleteUser(String thirdUserId, Long tuserId) {
        if (safeToDeleteUser(thirdUserId)) {
            logger.info("user {} does not exist, safe to delete it", thirdUserId);
            DaoUtil.markSyncUserStatusAsInvalid(tuserId);
            return tuserId;
        }else{
            logger.debug("user {} exist, can't delete", thirdUserId);
            return null;
        }
    }

    private int cleanUsers(ConnectorEntity tConnector) {
        List<UserEntity> deleteUserList = userDBO.getOidUser(String.valueOf(Constants.USER_TYPE_CONNECTOR_DINGTALK),tConnector.getSyncBatchNo(),
                tConnector.getId());
        if (deleteUserList == null || deleteUserList.size() ==0 ){
            return 0;
        }
        List<CompletableFuture<Long>> futures =
                deleteUserList.stream().map(userEntity ->
                        CompletableFuture.supplyAsync(
                                () -> deleteUser(userEntity.getOid(), userEntity.getId())
                                , (ThreadPoolTaskExecutor)ServiceLocator.getBean(ThreadPoolNameConstant.COMMON_TASK_NAME)
                        ))
                        .collect(Collectors.toList());
        List<Long> finalDeleteUserList = futures.stream()
                .map(CompletableFuture::join)
                .filter(tuserId -> tuserId != null)
                .collect(Collectors.toList());
        return finalDeleteUserList.size();
    }

    private void cleanSysUsers(ConnectorEntity tConnector){

    }

    /**
     * check again in connector, to see whether group is safe to delete
     * @param thirdGroupId
     * @return
     */
    protected abstract boolean safeToDeleteGroup(String thirdGroupId);

    /**
     * check again in connector, to see whether user is safe to delete
     * @param thirdUserId
     * @return
     */
    protected abstract boolean safeToDeleteUser(String thirdUserId);

    protected abstract SyncGroupInfo getSyncGroupTree() throws ConnectorException;
    protected abstract List<? extends SyncUserInfo> getUsersByGroupId(String id) throws ConnectorException;
    /**
     * 取消注册回调地址接口
     * @return
     */
    protected abstract boolean cancelCallBack();

    /**
     * 注册回调接口
     * @return
     */
    protected abstract boolean registerCallBack();

    /**
     * 更新 回调接口
     * @return
     */
    protected abstract boolean updateCallBack();

    /**
     * 根据三方的组id信息 获取该部门组的详细信息
     * @param thirdGroupId
     * @return
     * @throws ConnectorException
     */
    protected abstract Map<String,SyncGroupInfo> getSyncGroupInfoById(String thirdGroupId) throws ConnectorException;

    /**
     * 根据三方用户的id信息获取该用户的详细信息
     * @param thirdUserId
     * @return
     * @throws ConnectorException
     */
    protected abstract Map<String,SyncUserInfo> getUsersByUserThirdUserId(String thirdUserId) throws ConnectorException;

    /**
     * 获取回调是否自动删除
     * @return
     */
    protected abstract String getAutoCancel();

    private String getSyncRootGroupCode(ConnectorEntity tConnector) {

        String oldSyncRootGroupCode = getOldSyncRootGroupCode(tConnector);
        if (oldSyncRootGroupCode != null){
            return oldSyncRootGroupCode;
        }
        return generateNewSyncRootGroupCode();
    }

    private String getOldSyncRootGroupCode(ConnectorEntity tConnector){
        List<String> list = groupDBO.getOldSyncRootGroupCode(tConnector.getId());
        return (list != null && list.size() > 0) ? list.get(0) : null;
    }

    private String generateNewSyncRootGroupCode() {
        String pGroupCode = "";

        List<String> list = groupDBO.getGroupsCode();

        String groupCode = "";
        if (CollectionUtils.isEmpty(list)) {            //parentId下面还没有任何子组
            groupCode = pGroupCode + "0001";
        } else {
            for (int i = 1; i <= list.size(); i++) {
                String seqStr = list.get(i - 1);
                int seq = NumberUtils.toInt(StringUtils.substring(seqStr, seqStr.length() - 4));
                if (seq != i) {
                    groupCode = pGroupCode + StringUtils.leftPad(String.valueOf(i), 4, '0');
                    break;
                }

                if (i == list.size()) {                 //每个组最多拥有9999个直属下级组
                    if (seq < 9999) {
                        groupCode = pGroupCode + StringUtils.leftPad(String.valueOf(i + 1), 4, '0');
                    } else {
                        throw new RuntimeException("超出最大值");
                    }
                }
            }
        }
        return groupCode;
    }

    /**
     * Save Synced Group and its children into DB
     * NOTE: groupCode will be generated and update with every group
     *
     * @param parentId
     * @param groupCode
     * @param tConnector
     * @param syncGroupInfo
     * @param syncStats
     * @return HashMap, key is thirdGroupId, value is tgroupId
     * @throws ConnectorException
     */
    private HashMap<String, Long> saveSyncGroup(Long parentId, String groupCode, ConnectorEntity tConnector, SyncGroupInfo syncGroupInfo, SyncStats syncStats) throws ConnectorException {
        HashMap<String, Long> result = new LinkedHashMap<>();

        logger.debug("syncGroupInfo is null:{}", syncGroupInfo==null);
        logger.debug("syncGroupInfo groupId:{}", syncGroupInfo.getGroupId());
        logger.debug("groupDBO is null:{}", groupDBO==null);

        GroupEntity tGroup = groupDBO.getGroupBySyncConnectorId(tConnector.getId(), syncGroupInfo.getGroupId());
        boolean isNew = false;
        if (tGroup == null) {
            isNew = true;
            tGroup = new GroupEntity();
            tGroup.setType(Constants.GROUP_TYPE_CONNECTOR);
            tGroup.setThirdGroupId(syncGroupInfo.getGroupId());
            tGroup.setSyncConnectorId(tConnector.getId());
            tGroup.setCreatTime(LocalDateTime.now());
            DaoUtil.setGroupPolicy(parentId, tGroup);
        }

        tGroup.setUpdateTime(LocalDateTime.now());
        tGroup.setName(syncGroupInfo.getGroupName());
        tGroup.setGroupCode(groupCode);
        tGroup.setStatus(1);
        tGroup.setSyncBactchNo(tConnector.getSyncBatchNo());
        tGroup.setParentId(parentId);

        if (isNew){
            groupDBO.save(tGroup);
            syncStats.addedGroupCount++;
        }else{
            groupDBO.saveOrUpdate(tGroup);
            syncStats.updatedGroupCount++;
        }

        result.put(syncGroupInfo.getGroupId(), tGroup.getId());

        logger.info("sync group {} has {} subgroups", syncGroupInfo.getGroupName(), syncGroupInfo.getChildren().size());

        int child = 1;
        for (SyncGroupInfo group : syncGroupInfo.getChildren()) {
            String childGroupCode = getNextGroupCode(groupCode, child++);
            result.putAll(saveSyncGroup(tGroup.getId(), childGroupCode, tConnector, group, syncStats));
        }


        return result;
    }

    private SyncStats saveOrUpdateUser(Long groupId, ConnectorEntity tConnector, SyncUserInfo userInfo) {
        String loginId = userInfo.getLoginId();
        // 查询当前可用用户
        UserEntity user = userDBO.getByLoginId(loginId);

        SyncStats syncStats = new SyncStats();



        Boolean isSave = DaoUtil.saveOrUpdateUserDB(tConnector.getSyncBatchNo(), tConnector.getId(), groupId, user, userInfo, this.updatingPolicy);
        if (isSave == null){
            return null;
        }else if (isSave){
            syncStats.addedUserCount++;
        }else{
            syncStats.updatedUserCount++;
        }
        return syncStats;
    }

    private String getNextGroupCode(String parentCode, int childIndex) {
        return parentCode + StringUtils.leftPad("" + childIndex, 4, '0');
    }

    /**
     * 添加部门时父节点在uem不存在，递增查找上级节点并保存
     * @param syncGroupInfo
     * @param thirdGroupId
     * @param tConnector
     * @throws ConnectorException
     */
    protected void syncSavaGroup(SyncGroupInfo syncGroupInfo,String thirdGroupId,ConnectorEntity tConnector,SyncStats syncStats)throws ConnectorException{
        Map<String,SyncGroupInfo> syncedGroupMap = new HashMap<>();
        syncedGroupMap.put(thirdGroupId,syncGroupInfo);
        String fThirdGroupId = syncGroupInfo.getSyncParentid();
        String gThirdGroupId = "";
        String sThirdGroupId = thirdGroupId;
        List<String> noExistGroup = new ArrayList<>();
        noExistGroup.add(sThirdGroupId);
        Map<String,SyncGroupInfo> groupInfo = null;
        GroupEntity tGroupParentid = null ;
        while (tGroupParentid == null ){
            groupInfo = getSyncGroupInfoById(fThirdGroupId);
            gThirdGroupId = groupInfo.get(fThirdGroupId).getSyncParentid();
            tGroupParentid = DaoUtil.getGroupByThirdGroupId(tConnector.getId(),gThirdGroupId);
            if (tGroupParentid != null){
                saveSyncGroup(tGroupParentid.getId(), DaoUtil.getGroupCodeByParent(tGroupParentid.getId(),tGroupParentid.getGroupCode()), tConnector, groupInfo.get(fThirdGroupId), syncStats);
                for(int i = noExistGroup.size();i>=1;i--){
                    tGroupParentid = DaoUtil.getGroupByThirdGroupId(tConnector.getId(),syncedGroupMap.get(noExistGroup.get(i-1)).getSyncParentid());
                    saveSyncGroup(tGroupParentid.getId(), DaoUtil.getGroupCodeByParent(tGroupParentid.getId(),tGroupParentid.getGroupCode()), tConnector, syncedGroupMap.get(noExistGroup.get(i-1)), syncStats);
                }
                break;
            } else {
                syncedGroupMap.putAll(groupInfo);
                sThirdGroupId = fThirdGroupId;
                fThirdGroupId = gThirdGroupId;
                noExistGroup.add(sThirdGroupId);
                if("0".equals(gThirdGroupId)){
                    logger.error(" not root group in uem ");
                    break;
                }
            }
        }
    }

    /**
     * 获取redis暂存的待处理的部门回调信息，并对回调的信息做部门的变更处理
     * @param tConnector
     */
    protected void doSyncGroupWait(ConnectorEntity tConnector) {
        String tenantId = tConnector.getTenantId() != null? tConnector.getTenantId() : "mdm";//TODO: tenantId
        String SYNC_CALL_BACK_GROUP_KEY = "TENANT:%s:CONNECTORS:TYPE:%s:SYNC_GROUP_WAIT";
        SYNC_CALL_BACK_GROUP_KEY = String.format(SYNC_CALL_BACK_GROUP_KEY, tenantId, ConnectorType.DINGTALK.name().toLowerCase());
        if (DaoUtil.getStringRedisTemplate().hasKey(SYNC_CALL_BACK_GROUP_KEY)){
            try {
                String groupInfo = DaoUtil.getStringRedisTemplate().opsForValue().get(SYNC_CALL_BACK_GROUP_KEY);
                if (!StringUtils.isEmpty(groupInfo)){
                    String[] group =groupInfo.split("-");
                    for (String groupCallBack:group) {
                        if (StringUtils.isNotEmpty(groupCallBack)){
                            String[] groupIdInfo =  groupCallBack.split(" ");
                            try {
                                while (syncCallBackStillRunning(tConnector.getTenantId(),tConnector.getType().toLowerCase())){
                                    ConnectorConfigUtil.thridCheckSleep();
                                }
                                if(markSyncCallBackAsRunning(tConnector.getTenantId(),tConnector.getType().toLowerCase())){
                                    syncGroup(JSONArray.parseArray("["+groupIdInfo[1]+"]"),tConnector,groupIdInfo[0]);
                                }else{
                                    logger.error(groupIdInfo[1]+"***"+groupIdInfo[0]+" redis markSyncCallBackAsRunning failed ");
                                }

                            } catch (ConnectorException e) {
                                logger.error(groupIdInfo.toString()+" error in uem ");
                            }finally {
                                markSyncCallBackAsFinished(tConnector.getTenantId(),tConnector.getType().toLowerCase());
                            }
                        }
                    }
                }
            }finally {
                DaoUtil.getStringRedisTemplate().delete(SYNC_CALL_BACK_GROUP_KEY);
            }
        }
    }

    /**
     * 获取redis暂存的待处理的用户回调信息，并对回调的信息做用户的变更处理
     * @param tConnector
     */
    protected void doSyncUserWait(ConnectorEntity tConnector) {
        String tenantId = tConnector.getTenantId() != null? tConnector.getTenantId() : "mdm";//TODO: tenantId
        String SYNC_CALL_BACK_USER_KEY = "TENANT:%s:CONNECTORS:TYPE:%s:SYNC_GROUP_WAIT";
        SYNC_CALL_BACK_USER_KEY = String.format(SYNC_CALL_BACK_USER_KEY, tenantId, ConnectorType.DINGTALK.name().toLowerCase());
        if(DaoUtil.getStringRedisTemplate().hasKey(SYNC_CALL_BACK_USER_KEY)){
            String userInfo = DaoUtil.getStringRedisTemplate().opsForValue().get(SYNC_CALL_BACK_USER_KEY);
            logger.info(userInfo);
            try {
                if (!StringUtils.isEmpty(userInfo)){
                    String[] user =userInfo.split("-");
                    for (String userCallBack:user) {
                        if (StringUtils.isNotEmpty(userCallBack)){
                            String[] userIdInfo =  userCallBack.split(" ");
                            try {
                                while (syncCallBackStillRunning(tConnector.getTenantId(),tConnector.getType().toLowerCase())) {
                                    ConnectorConfigUtil.thridCheckSleep();
                                }
                                if(markSyncCallBackAsRunning(tConnector.getTenantId(),tConnector.getType().toLowerCase())){
                                    syncUser(JSONArray.parseArray("["+userIdInfo[1]+"]"),tConnector,userIdInfo[0]);
                                }else{
                                    logger.error(userIdInfo[1]+"****"+userIdInfo[0]+" redis markSyncCallBackAsRunning failed ");
                                }

                            } catch (ConnectorException e) {
                                logger.error(userIdInfo.toString()+" error in uem ");
                            }finally {
                                markSyncCallBackAsFinished(tConnector.getTenantId(),tConnector.getType().toLowerCase());
                            }
                        }
                    }
                }
            }finally {
                DaoUtil.getStringRedisTemplate().delete(SYNC_CALL_BACK_USER_KEY);
            }

        }
    }


    /**
     * 查看回调接口是否有任务正在执行
     * @param tenantId
     * @return
     */
    @Override
    public boolean syncCallBackStillRunning(String tenantId,String type){
        if (DaoUtil.getStringRedisTemplate().hasKey(getSyncCallBackStatusKey(tenantId,type))){
            return true;
        }
        return false;
    }

    /**
     * 获取钉钉回调对应租户和 tconnector 类型的，redis key
     * @param tenantId
     * @return
     */
    public String getSyncCallBackStatusKey(String tenantId,String type){
        String SYNC_RUNNING_KEY = "TENANT:%s:CONNECTORS_CALLBACK:TYPE:%s:SYNC_STATUS";
        return String.format(SYNC_RUNNING_KEY, tenantId, type);
    }

    /**
     * 标记回调接口执行任务
     * @param tenantId
     * @return
     */
    @Override
    public boolean markSyncCallBackAsRunning(String tenantId,String type) {
        if(StringUtils.isEmpty(tenantId)){
            tenantId = "mdm";//TODO: tenantId
        }
        return DaoUtil.getStringRedisTemplate().opsForValue().setIfAbsent(getSyncCallBackStatusKey(tenantId,type), "RUNNING", 60, TimeUnit.MINUTES);
    }

    /**
     * remove sync key, so that other UEM instance know it.
     */
    @Override
    public void markSyncCallBackAsFinished(String tenantId,String type) {
        if(StringUtils.isEmpty(tenantId)){
            tenantId = "mdm";//TODO: tenantId
        }
        DaoUtil.getStringRedisTemplate().delete(getSyncCallBackStatusKey(tenantId,type));
    }

    /**
     * set sync task as running(redis), so that other UEM instance know it.
     *
     * @return if failed, return false
     */
    public boolean markSyncAsRunning(String tenantId,String type) {
        String lockKey = "Sync_Connector_Key_"+tenantId+"Type"+type;
        return DaoUtil.getStringRedisTemplate().opsForValue().setIfAbsent(lockKey, "running", 120, TimeUnit.MINUTES);
    }

    /**
     * remove sync key, so that other UEM instance know it.
     */
    public void markSyncAsFinished(String tenantId,String type) {
        String lockKey = "Sync_Connector_Key_"+tenantId+"Type"+type;
        DaoUtil.getStringRedisTemplate().delete(lockKey);
    }
}
