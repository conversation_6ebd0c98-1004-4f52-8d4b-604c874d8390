package com.cyberscraft.uep.adm.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Date: 2021/4/28
 * description: vpp应用授权记录
 *
 * <AUTHOR>
 */
@TableName("t_vpp_manage")
public class VppManageEntity implements Serializable {
    private static final long serialVersionUID = -7810310656659376234L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /****
     * 用户longinId
     */
    private String loginId;

    /****
     *苹果应用商店应用id
     */
    private Long adamId;


    /****
     * vppappid
     */
    private Long vppAppId;

    /***
     * 授权码
     */
    private String licenseId;

    /***
     * inviteUrl
     */
    private String inviteUrl;

    /****
     *
     */
    private LocalDateTime createTime;

    /***
     *
     */
    private LocalDateTime updateTime;

    /**
     * 租户ID
     */
    private String tenantId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLoginId() {
        return loginId;
    }

    public void setLoginId(String loginId) {
        this.loginId = loginId;
    }

    public Long getAdamId() {
        return adamId;
    }

    public void setAdamId(Long adamId) {
        this.adamId = adamId;
    }

    public Long getVppAppId() {
        return vppAppId;
    }

    public void setVppAppId(Long vppAppId) {
        this.vppAppId = vppAppId;
    }

    public String getLicenseId() {
        return licenseId;
    }

    public void setLicenseId(String licenseId) {
        this.licenseId = licenseId;
    }

    public String getInviteUrl() {
        return inviteUrl;
    }

    public void setInviteUrl(String inviteUrl) {
        this.inviteUrl = inviteUrl;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }
}
