package com.cyberscraft.uep.adm.core.service.config;

import com.cyberscraft.uep.adm.core.exception.AdmException;

public interface ISmsParams {

    void setSmsServerUrl(String smsServerUrl);

    void setSmsServerUser(String smsServerUser);

    void setSmsServerPassword(String smsServerPassword);

    String getSmsProvider() throws AdmException;

    String getSmsServerUrl() throws AdmException;

    String getSmsServerUser() throws AdmException;

    String getSmsServerPassword() throws AdmException;

    String getSmsTemplate() throws AdmException;

    String getSmsCmccUrl() throws AdmException;

    String getSmsCmccUser() throws AdmException;

    String getSmsCmccPassword() throws AdmException;

    String getSmsCmccEcname() throws AdmException;

    String getSmsCmccSign() throws AdmException;
}
