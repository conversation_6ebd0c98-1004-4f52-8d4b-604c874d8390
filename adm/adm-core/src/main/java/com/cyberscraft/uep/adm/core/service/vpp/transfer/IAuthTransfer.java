package com.cyberscraft.uep.adm.core.service.vpp.transfer;

import com.cyberscraft.uep.adm.core.entity.SysUserEntity;
import com.cyberscraft.uep.adm.dto.auth.AdminInfoVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * auth相关vo与entity的转换
 */
@Mapper(componentModel = "spring")
public interface IAuthTransfer {
    IAuthTransfer INSTANCE = Mappers.getMapper(IAuthTransfer.class);

    AdminInfoVO entityToAdminInfoVO(SysUserEntity entity);
}
