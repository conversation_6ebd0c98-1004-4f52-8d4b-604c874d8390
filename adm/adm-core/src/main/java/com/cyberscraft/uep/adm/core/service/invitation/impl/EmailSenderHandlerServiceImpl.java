package com.cyberscraft.uep.adm.core.service.invitation.impl;

import com.cyberscraft.uep.adm.constants.InvitationChannel;
import com.cyberscraft.uep.adm.constants.InvitationStatus;
import com.cyberscraft.uep.adm.core.constant.ConfigConstant;
import com.cyberscraft.uep.adm.core.dbo.InvitationRecordDBO;
import com.cyberscraft.uep.adm.core.domain.InvitationParam;
import com.cyberscraft.uep.adm.core.entity.InvitationRecordEntity;
import com.cyberscraft.uep.adm.core.enums.AdmErrorType;
import com.cyberscraft.uep.adm.core.exception.AdmException;
import com.cyberscraft.uep.adm.core.service.invitation.IInvitationSenderHandlerService;
import com.cyberscraft.uep.adm.core.service.sysconf.ITemplateService;
import com.cyberscraft.uep.adm.dto.invitation.SendToTargetsDto;
import com.cyberscraft.uep.adm.dto.sysconf.EmailTemplateVO;
import com.cyberscraft.uep.common.util.StringUtil;
import com.cyberscraft.uep.ncm.client.domain.Email;
import com.cyberscraft.uep.ncm.client.service.INotificationClientService;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Service
public class EmailSenderHandlerServiceImpl implements IInvitationSenderHandlerService {
    private final Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private INotificationClientService notificationClientService;

    @Resource
    private ITemplateService<EmailTemplateVO> emailTemplateService;

    @Resource
    private InvitationRecordDBO invitationRecordDBO;


    @Override
    public boolean validateTarget(SendToTargetsDto dto) {
        List<String> targets = dto.getTargets();
        if(targets == null || targets.isEmpty()) {
            return false;
        }
        for(String target: targets) {
            if(StringUtils.isEmpty(target)
                || !StringUtil.isValidEmailAddress(target)){
                logger.warn("invalid email address: {}", target);
                return false;
            }
        }
        return true;
    }

    @Override
    public InvitationChannel supportChannel() {
        return InvitationChannel.EMAIL;
    }

    @Override
    public void send(SendToTargetsDto dto, InvitationParam param) {
        String tenantId = TenantHolder.getTenantCode();
        List<String> targetList = dto.getTargets();
        if(targetList == null || targetList.isEmpty()) {
            logger.warn("target email address is null");
            throw new AdmException(AdmErrorType.INVITATION_EMAIL_TARGET_NULL);
        }
        // 转为Set，排重
        Set<String> targetSet = new HashSet(targetList);
        EmailTemplateVO emailTemplateVO = emailTemplateService.getTemplate();
        if(emailTemplateVO == null) {
            logger.warn("email template is null");
            throw new AdmException(AdmErrorType.INVITATION_EMAIL_TEMPLATE_NULL);
        }
        String title = emailTemplateService.renderTitle(param);
        if(StringUtils.isEmpty(title)) {
            logger.warn("email title is null");
            throw new AdmException(AdmErrorType.INVITATION_EMAIL_TITLE_NULL);
        }

        String content = emailTemplateService.renderContent(param);
        if(StringUtils.isEmpty(content)) {
            logger.warn("email content is null");
            throw new AdmException(AdmErrorType.INVITATION_EMAIL_CONTENT_NULL);
        }
        String[] targets = targetSet.toArray(new String[targetSet.size()]);
        Email email = new Email(targets, title, content, tenantId, ConfigConstant.CONFIG_KEY_EMAIL_TEMPLATE);
        List<InvitationRecordEntity> invitationRecordEntities = new ArrayList<>();

        try {
            notificationClientService.send(email);
            targetSet.forEach(e-> {
                InvitationRecordEntity entity = new InvitationRecordEntity();
                entity.setReceiver(e);
                entity.setStatus(InvitationStatus.SUCCESS.getCode());
                entity.setChannel(InvitationChannel.EMAIL.name());
                invitationRecordEntities.add(entity);
            });

            invitationRecordDBO.saveBatch(invitationRecordEntities);

        }catch (Exception ex) {
            logger.warn("failed to send email: {}", ex.getMessage());
            targetSet.forEach(e-> {
                InvitationRecordEntity entity = new InvitationRecordEntity();
                entity.setReceiver(e);
                entity.setStatus(InvitationStatus.FAILED.getCode());
                entity.setChannel(InvitationChannel.EMAIL.name());
                invitationRecordEntities.add(entity);
            });

            invitationRecordDBO.saveBatch(invitationRecordEntities);
        }
    }
}
