package com.cyberscraft.uep.adm.core.service.invitation.impl;

import com.cyberscraft.uep.adm.core.enums.AdmErrorType;
import com.cyberscraft.uep.adm.constants.DownloadValidType;
import com.cyberscraft.uep.adm.core.exception.AdmException;
import com.cyberscraft.uep.adm.core.service.sysconf.IDownloadConfigService;
import com.cyberscraft.uep.adm.core.service.vpp.IAppleVppService;
import com.cyberscraft.uep.adm.dto.sysconf.AppDownloadConfigVO;
import com.cyberscraft.uep.adm.dto.vpp.AppInfoVO;
import com.cyberscraft.uep.common.config.ServerConfig;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;

public abstract class AbstractInvitationParamHandler {
    protected Logger logger = LoggerFactory.getLogger(getClass());

    @Resource
    private IAppleVppService appleVppService;

    @Resource
    private ServerConfig serverConfig;

    @Resource
    private IDownloadConfigService<AppDownloadConfigVO> downloadConfigService;

    public String getClientAppName() {
        AppInfoVO vo = appleVppService.getDefaultAppInfo(TenantHolder.getTenantCode(), false);
        if(vo == null) {
            logger.warn("can not found active client app");
            throw new AdmException(AdmErrorType.INVITATION_INIT_PARAM_ERROR);
        }
        return vo.getName();
    }

    public String getDownloadUrl() {
        String outterUrl = serverConfig.getOutterUrl();
        String tenantId = TenantHolder.getTenantCode();
        String ret = String.format("%s/adm/download/?tenantId=%s", outterUrl, tenantId);
        return ret;
    }

    public String getExtractedCode() {
        DownloadValidType downloadValidType = downloadConfigService.getValidType();
        if(downloadValidType.equals(DownloadValidType.EXTRACTED_CODE)) {
            return downloadConfigService.getExtractedCode();
        }
        return null;
    }
}
