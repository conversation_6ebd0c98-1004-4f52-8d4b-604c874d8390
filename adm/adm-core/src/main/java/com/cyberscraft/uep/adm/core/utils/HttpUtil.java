package com.cyberscraft.uep.adm.core.utils;

import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2020-05-25 10:07
 */
public class HttpUtil {


    public static String getPostBody(HttpServletRequest request, int contentLen) {
        if (contentLen > 0) {
            int readLen = 0;
            int readLengthThisTime = 0;
            byte[] message = new byte[contentLen];
            try {
                while (readLen != contentLen) {
                    readLengthThisTime = request.getInputStream().read(message, readLen, contentLen - readLen);
                    if (readLengthThisTime == -1) {
                        break;
                    }
                    readLen += readLengthThisTime;
                }
                return new String(message);
            } catch (IOException e) {
            }
        }
        return "";
    }

    /**
     * best effort to get http(s) request scheme
     */
    public static String getScheme(HttpServletRequest req) {
        return useHeaderValueOrDefault(req, "x-forwarded-proto", req.getScheme());
    }

    /**
     * best effort to get remote server FQDN
     */
    public static String getServerName(HttpServletRequest req) {
        return useHeaderValueOrDefault(req, "x-forwarded-host", req.getServerName()).split(":", 2)[0];
    }

    /**
     * best effort to get server port
     */
    public static int getServerPort(HttpServletRequest req) {
        /**
         * debug: dump request headers

         logger.info("req.getServerPort(): {}", req.getServerPort());
         logger.info("x-forwarded-port: {}",  req.getHeader("x-forwarded-port"));
         Enumeration<String> names = req.getHeaderNames();
         for(String n =names.nextElement(); names.hasMoreElements(); n = names.nextElement()) {
         logger.info("Header[{}]:{}", n, req.getHeader(n));
         }

         logger.info("req.getRequestURI():{}", req.getRequestURI());
         logger.info("req.getRequestURL():{}", req.getRequestURL().toString());
         */

        /**
         * try to get correct port in the order of:
         * 1. x-forwarded-port
         * 2. x-forwarded-host
         * 3. host Header
         */
        String portStr = useHeaderValueOrDefault(req,"x-forwarded-port", null);
        if(StringUtils.isEmpty(portStr)){
            String[] xHostArray = useHeaderValueOrDefault(req,"x-forwarded-host", "").split(":", 2);
            portStr = xHostArray.length>1 ? xHostArray[1] : "";
        }

        if (StringUtils.isEmpty(portStr)) {
            //get port from host header
            String[] hostArray = req.getHeader("host").split(":", 2);
            portStr = hostArray.length>1 ? hostArray[1] : "";
        }
        return Integer.valueOf(StringUtils.isEmpty(portStr)? "8089" : portStr);
    }

    /**
     * try to use the value of specified header, if case the header is null, use defaultValue.
     */
    private static String useHeaderValueOrDefault(HttpServletRequest req, String headerName, String defaultValue) {
        String headerValue = req.getHeader(headerName);
        //logger.debug("headerValue=>{},defalutValue=>{}",headerValue,defaultValue);
        return (headerValue != null) ? headerValue : defaultValue;
    }
}
