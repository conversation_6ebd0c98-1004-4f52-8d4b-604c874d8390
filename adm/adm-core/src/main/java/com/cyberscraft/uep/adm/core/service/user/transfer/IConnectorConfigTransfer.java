package com.cyberscraft.uep.adm.core.service.user.transfer;

import com.cyberscraft.uep.adm.core.entity.ConnectorEntity;
import com.cyberscraft.uep.adm.dto.user.SyncConfigDto;
import com.cyberscraft.uep.adm.dto.user.SyncConfigVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
public interface IConnectorConfigTransfer {

    IConnectorConfigTransfer INSTANCE = Mappers.getMapper(IConnectorConfigTransfer.class);

    SyncConfigVO entityToVO(ConnectorEntity tConnector);

//    ConnectorEntity dtoToEntity(SyncConfigDto syncConfigDto);
}
