package com.cyberscraft.uep.adm.core.dbo.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cyberscraft.uep.adm.core.dao.VppManageDao;
import com.cyberscraft.uep.adm.core.dbo.VppManageDBO;
import com.cyberscraft.uep.adm.core.entity.VppManageEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * Date: 2021/4/28
 * description:
 *
 * <AUTHOR>
 */
@Service
public class VppManageDBOImpl extends ServiceImpl<VppManageDao, VppManageEntity> implements VppManageDBO {

    @Resource
    private VppManageDao vppManageDao;

    @Override
    public VppManageEntity getByAdamIdAndLoginId(Long adamId, String loginId) {
        LambdaQueryWrapper<VppManageEntity> queryWrapper = new QueryWrapper().lambda();
        queryWrapper.eq(VppManageEntity::getAdamId, adamId);
        queryWrapper.eq(VppManageEntity::getLoginId, loginId);
        List<VppManageEntity> list = this.vppManageDao.selectList(queryWrapper);
        return list != null && list.size() > 0 ? list.get(0) : null;
    }
}
