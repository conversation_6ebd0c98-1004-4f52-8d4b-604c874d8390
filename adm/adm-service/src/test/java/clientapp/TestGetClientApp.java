package clientapp;

import com.cyberscraft.uep.starter.dds.TenantHolder;
import com.cyberscraft.uep.adm.AdmApplication;
import com.cyberscraft.uep.adm.core.entity.ClientAppEntity;
import com.cyberscraft.uep.adm.core.service.clientapp.IClientAppService;
import com.cyberscraft.uep.adm.dto.clientapp.ClientAppInfoDto;
import com.cyberscraft.uep.common.enums.Platform;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import javax.annotation.Resource;

/***
 *
 * @date 2021-12-12
 * <AUTHOR>
 ***/
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = AdmApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class TestGetClientApp {

    @Resource
    private IClientAppService clientAppService;

    @Test
    public void test() {
        TenantHolder.setTenantCode("57");
        ClientAppEntity obj = clientAppService.getClientAppByPlatformAndPkg(Platform.ANDROID.getValue(), "com.alibaba.taurus.guoban");

        System.out.println(obj);
    }

    @Test
    public void testSave() {
        TenantHolder.setTenantCode("57");
        ClientAppInfoDto clientAppInfoDto = new ClientAppInfoDto();
        clientAppInfoDto.setPlatform(Platform.ANDROID);
        clientAppInfoDto.setPkgName("te9");
//        clientAppInfoDto.setVersion("1.1");
//        clientAppInfoDto.setTenantId("57");
//        clientAppInfoDto.setFilePath("aaa");


        this.clientAppService.save(clientAppInfoDto);

    }
}
