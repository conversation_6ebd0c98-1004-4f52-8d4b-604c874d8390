spring.cloud.kubernetes.enabled=false

#注册中心
spring.config.import=consul:
#spring.cloud.consul.discovery.ip-address=**********
spring.cloud.consul.discovery.prefer-ip-address=true
spring.cloud.consul.host=*************
spring.cloud.consul.port=8500
spring.cloud.consul.discovery.enabled=true
spring.cloud.consul.discovery.service-name=${spring.application.name}
spring.cloud.consul.discovery.instance-id=${spring.application.name}:${spring.cloud.client.ip-address}:${server.port}
spring.cloud.consul.discovery.health-check-path=/actuator
spring.cloud.consul.discovery.health-check-interval=10s
spring.cloud.consul.discovery.health-check-critical-timeout=30s
spring.cloud.consul.discovery.deregister=true
spring.application.admin.enabled=false
#\u914D\u7F6E\u4E2D\u5FC3
spring.cloud.consul.config.fail-fast=true
spring.cloud.consul.config.enabled=true
#\u7EDF\u4E00\u4F7F\u7528properties
spring.cloud.consul.config.format=properties
spring.cloud.consul.config.prefix=config
#\u9ED8\u8BA4\u7684\u6587\u4EF6\uFF0C\u53EF\u4EE5\u505A\u4E3A\u516C\u5171\u6587\u4EF6\u914D\u7F6E,\u4F46\u4E5F\u662F\u5728prefix\u6307\u5B9A\u7684\u76EE\u5F55\u4E0B
spring.cloud.consul.config.default-context=application
spring.cloud.consul.config.profile-separator=-
spring.cloud.consul.config.data-key=data
spring.cloud.consul.config.watch.delay=1000
