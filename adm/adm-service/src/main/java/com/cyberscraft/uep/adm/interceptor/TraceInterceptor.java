//package com.cyberscraft.uep.adm.interceptor;
//
//import com.alibaba.arms.tracing.Span;
//import com.alibaba.arms.tracing.Tracer;
//import com.cyberscraft.uep.common.constant.SysConstant;
//import com.cyberscraft.uep.common.util.SnowflakeIDUtil;
//import org.apache.commons.lang3.StringUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.slf4j.MDC;
//import org.springframework.web.servlet.HandlerInterceptor;
//import org.springframework.web.servlet.ModelAndView;
//
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpServletResponse;
//
///***
// *
// * @date 2021/5/12
// * <AUTHOR>
// ***/
//public class TraceInterceptor implements HandlerInterceptor {
//
//    private final static Logger LOG = LoggerFactory.getLogger(TraceInterceptor.class);
//
//    /****
//     * 请求开始时，通过工具类，获取对应的traceId,如果没有，则用udid自动生成一个新的
//     * @param request
//     * @param response
//     * @param handler
//     * @return
//     * @throws Exception
//     */
//    @Override
//    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
//        MDC.clear();
//        Span span = Tracer.builder().getSpan();
//        String traceId = span.getTraceId();
//        String rpcId = span.getRpcId();
//        LOG.info("current traceId:{},rpcId:{}", traceId, rpcId);
//        if (StringUtils.isBlank(traceId)) {
//            traceId = SnowflakeIDUtil.getIdStr();
//            LOG.info("current traceId is empty,rpcId:{}，new traceId is :{}", rpcId, traceId);
//        }
//        MDC.put(SysConstant.LOG_TRACE_ID, traceId);
//        MDC.put(SysConstant.LOG_RPC_ID, rpcId);
//        return true;
//    }
//
//    @Override
//    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
//
//    }
//
//    @Override
//    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
//
//    }
//}
