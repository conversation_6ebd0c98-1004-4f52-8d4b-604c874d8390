package com.cyberscraft.uep.adm.controller.inner;

import com.cyberscraft.uep.adm.api.inner.SystemApi;
import com.cyberscraft.uep.adm.controller.admin.BaseAdminCtroller;
import com.cyberscraft.uep.adm.core.service.vpp.IAuthService;
import com.cyberscraft.uep.adm.dto.sysconf.TenantSyncDto;
import com.cyberscraft.uep.adm.dto.sysuser.AdminInfoDto;
import com.cyberscraft.uep.common.bean.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * Date: 2020/6/10
 * description:
 *
 * <AUTHOR>
 */
@RestController
public class SystemController extends BaseAdminCtroller implements SystemApi {

    protected final static Logger log = LoggerFactory.getLogger(SystemController.class);

    @Resource
    private IAuthService authService;

    /**
     * 同步admin超级管理员
     * @param tenantSyncDto
     * @return
     */
    @Override
    public Result<Boolean> syncTenant(String tCode, TenantSyncDto tenantSyncDto) {
        log.info("syncAdmin tCode:{}",tCode);
        authService.syncTenant(tenantSyncDto);
        return successResult(Boolean.TRUE);
    }
}
