package com.cyberscraft.uep.adm.controller.admin.clientapp;

import com.aliyun.oss.common.utils.BinaryUtil;
import com.cyberscraft.uep.adm.api.admin.clientapp.ClientAppApi;
import com.cyberscraft.uep.adm.controller.admin.BaseAdminCtroller;
import com.cyberscraft.uep.adm.core.enums.AdmErrorType;
import com.cyberscraft.uep.adm.core.exception.AdmException;
import com.cyberscraft.uep.adm.core.service.clientapp.IClientAppService;
import com.cyberscraft.uep.adm.core.utils.HttpUtil;
import com.cyberscraft.uep.adm.dto.clientapp.*;
import com.cyberscraft.uep.common.bean.Result;
import com.cyberscraft.uep.common.enums.ExceptionCodeEnum;
import com.cyberscraft.uep.common.enums.Platform;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.common.util.RSAUtil;
import com.cyberscraft.uep.common.util.RestAPIUtil;
import com.cyberscraft.uep.common.util.ValidatorUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.util.List;

/***
 *
 * @date 2021-12-12
 * <AUTHOR>
 ***/

@RestController
public class ClientAppController extends BaseAdminCtroller implements ClientAppApi {
    private static final Logger logger = LoggerFactory.getLogger(ClientAppController.class);

    @Resource
    private IClientAppService clientAppService;

    @Override
    @PreAuthorize("#oauth2.hasPermission('create:client_app')")
    public Result<Long> saveClientApp(ClientAppInfoDto dto) {
        String errMsg = ValidatorUtil.validate(dto);
        if (StringUtils.isNotBlank(errMsg)) {
            throw new AdmException(ExceptionCodeEnum.PARAM_INVALID.getCode(), errMsg);
        }
        Long id = clientAppService.save(dto);
        return successResult(id);
    }

    @Override
    @PreAuthorize("#oauth2.hasPermission('update:client_app')")
    public Result<Long> updateClientApp(ClientAppDetailDto dto) {
        return successResult(clientAppService.update(dto));
    }

    @Override
    @PreAuthorize("#oauth2.hasPermission('read:client_app')")
    public Result<ClientAppInfoVO> getActivateClientApp(Platform platform) {
        if (platform == null) {
            throw new AdmException(AdmErrorType.PLATFORM_INVALID);
        }
        ClientAppInfoVO vo = clientAppService.getPlatformCurrentActivateClientAppInfo(platform.getValue());
        return successResult(vo);
    }

    @Override
    @PreAuthorize("#oauth2.hasPermission('read:client_app')")
    public Result<ClientAppInfoVO> getAppDetail(String id) {
        return successResult(clientAppService.getAppDetail(Long.valueOf(id)));
    }

    @Override
    @PreAuthorize("#oauth2.hasPermission('read:client_app')")
    public Result<String> parseClientApp(ClientAppParseDto dto) {
        String errMsg = ValidatorUtil.validate(dto);
        if (StringUtils.isNotBlank(errMsg)) {
            throw new AdmException(ExceptionCodeEnum.PARAM_INVALID.getCode(), errMsg);
        }
        Long id = clientAppService.parseClientApp(dto);
        return successResult(String.valueOf(id));
    }

    @Override
    @PreAuthorize("#oauth2.hasPermission('read:client_app')")
    public Result<ClientAppParseResultVO> getClientAppParseResult(String id) {
        if (id == null) {
            throw new AdmException(AdmErrorType.CLIENTAPP_PARSE_RESULT_ID_INVALID);
        }
        ClientAppParseResultVO vo = clientAppService.getClientAppParseResultInfo(Long.valueOf(id));
        return successResult(vo);
    }

    @Override
    @PreAuthorize("#oauth2.hasPermission('update:upload_client_app')")
    public Result<String> uploadApp(HttpServletRequest request,FileUploadDto fileUploadDto) {
        return successResult(clientAppService.uploadApp(fileUploadDto));
    }

    @Override
    public Result<Boolean> uploadAppCallback(HttpServletRequest request) throws IOException {
        OssCallbackDto ossCallbackDto = verifyOssSign(request);
        boolean result = clientAppService.uploadAppCallback(ossCallbackDto);
        return successResult(result);
    }

    @Override
    @PreAuthorize("#oauth2.hasPermission('list:client_app')")
    public Result<List<ClientAppDetailVO>> listAppInfo(Platform platform)  {
        return successResult(this.clientAppService.listAppInfo(platform));
    }

    @Override
    @PreAuthorize("#oauth2.hasPermission('list:client_app_version')")
    public Result<List<ClientAppVersionDetailVO>> listAppVersion(String id) {
        return successResult(this.clientAppService.listAppVersion(Long.valueOf(id)));
    }

    @Override
    @PreAuthorize("#oauth2.hasPermission('update:client_app_version')")
    public Result<Boolean> selectAppVersion(String id,String status) {
        return successResult(this.clientAppService.selectAppVersion(Long.valueOf(id),status));
    }

    @Override
    @PreAuthorize("#oauth2.hasPermission('delete:client_app_version')")
    public Result<Boolean> deleteAppVersion(String id) {
        return successResult(this.clientAppService.deleteAppVersion(Long.valueOf(id)));
    }


    private OssCallbackDto verifyOssSign(HttpServletRequest request) throws IOException {
        logger.info("verifyOssSign...");
        boolean result = false;
        int contentLen =  Integer.parseInt(request.getHeader("content-length"));
        String ossCallbackBody = HttpUtil.getPostBody(request,contentLen);
        logger.info("ossCallbackBody: {}", ossCallbackBody);
        String autorizationInput = request.getHeader("Authorization");
        String pubKeyInput = request.getHeader("x-oss-pub-key-url");
        byte[] authorization = BinaryUtil.fromBase64String(autorizationInput);
        byte[] pubKey = BinaryUtil.fromBase64String(pubKeyInput);
        String pubKeyAddr = new String(pubKey);
        if (!pubKeyAddr.startsWith("http://gosspublic.alicdn.com/") && !pubKeyAddr.startsWith("https://gosspublic.alicdn.com/")) {
            logger.warn("pub key addr must be oss addrss");
            throw new AdmException(AdmErrorType.PUBLIC_KEY_VERIFY_ERROR);
        }

        String publicKey = executeGet(pubKeyAddr);
        publicKey = publicKey.replace("-----BEGIN PUBLIC KEY-----", "");
        publicKey = publicKey.replace("-----END PUBLIC KEY-----", "");

        String queryString = request.getQueryString();
        String uri = request.getRequestURI();
        String decodeUri = java.net.URLDecoder.decode(uri, "UTF-8");
        String authStr = decodeUri;
        if (queryString != null && !queryString.equals("")) {
            authStr += "?" + queryString;
        }
        authStr += "\n" + ossCallbackBody;

        try {
            result = RSAUtil.verify(authStr.getBytes(),publicKey,authorization);
        } catch (Exception e) {
            e.printStackTrace();
        }

        logger.info("verify result:" + result);
        logger.info("OSS Callback Body:" + ossCallbackBody);

        return JsonUtil.str2Obj(ossCallbackBody,OssCallbackDto.class);
    }



    public String executeGet(String url) {
        ResponseEntity result =  RestAPIUtil.getForEntityNotJson(url);
        return String.valueOf(result.getBody());
    }

}
