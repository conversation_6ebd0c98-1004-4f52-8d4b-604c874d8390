package com.cyberscraft.uep.adm.config;

import com.cyberscraft.uep.adm.core.constant.ThreadPoolNameConstant;
import com.cyberscraft.uep.common.bean.NamedThreadFactory;
import com.cyberscraft.uep.common.util.MDCThreadWrapUtil;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/***
 *
 * @date 2021-09-16
 * <AUTHOR>
 ***/
@Configuration
public class ThreadPoolConfiguration {


    /***
     * 131072
     */
    private final static int COMMON_QUEUE_CAPACITY = 2 << 16;

    private final static int NCPU = Runtime.getRuntime().availableProcessors();


    /***
     * 10's
     */
    private final static int KEEPALIVE_SECOND = 10000;


    /***
     * 通用线程池定义
     * @return
     */
    @Bean(ThreadPoolNameConstant.COMMON_TASK_NAME)
    public ThreadPoolTaskExecutor commonTaskExecutor() {

        int nThreads = 20;
        MDCThreadWrapUtil.MdcThreadPoolTaskExecutorWrapper executor = new MDCThreadWrapUtil.MdcThreadPoolTaskExecutorWrapper();
        executor.setCorePoolSize(nThreads);
        executor.setMaxPoolSize(nThreads);
        executor.setQueueCapacity(COMMON_QUEUE_CAPACITY);
        executor.setKeepAliveSeconds(KEEPALIVE_SECOND);
        return executor;
    }


    /***
     * 通用调用线程池
     * @return
     */
    @Bean(ThreadPoolNameConstant.COMMON_POOL_NAME)
    @ConditionalOnMissingBean(name = ThreadPoolNameConstant.COMMON_POOL_NAME)
    public ExecutorService commonPoolExcutor() {
        int nThreads = Math.max(NCPU * 8, 16);
        ExecutorService executor = new MDCThreadWrapUtil.MdcThreadPoolExecutorWrapper(nThreads, nThreads,
                0L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<Runnable>(),
                new NamedThreadFactory("comm"));
        return executor;
    }

}
