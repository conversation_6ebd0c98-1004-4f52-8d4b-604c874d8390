package com.cyberscraft.uep.adm.config;

import com.cyberscraft.uep.adm.core.constant.CacheNameConstant;
import org.redisson.api.RedissonClient;
import org.redisson.spring.cache.RedissonSpringCacheManager;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

@Configuration
@EnableCaching
public class CacheManagerConfiguration {
    @Value("${caffeine.cache.client.expireAfterWrite:300}")
    private int clientCacheExpireAfterWrite;

    @Value("${caffeine.cache.client.initialCapacity:50}")
    private int clientCacheInitialCapacity;

    @Value("${caffeine.cache.client.maximumSize:10000}")
    private int clientCacheMaximumSize;

    @Bean(name = CacheNameConstant.ADM_DEFAULT_CACHE_MANAGER)
    @Primary
    CacheManager cacheManager(RedissonClient redissonClient) {
        // create "testMap" cache with ttl = 24 minutes and maxIdleTime = 12 minutes

        //Map<String, CacheConfig> config = new HashMap<String, CacheConfig>();
        //config.put("testMap", new CacheConfig(24*60*1000, 12*60*1000));
        //return new RedissonSpringCacheManager(redissonClient, config);
        return new RedissonSpringCacheManager(redissonClient);
    }

//    @Bean
//    @Primary
//    CacheManager caffeineCacheManager() {
//        CaffeineCacheManager cacheManager = new CaffeineCacheManager("tenant_list_cache", "default_app_cache");
//        cacheManager.setCaffeine(caffeineCacheBuilder());
//        return cacheManager;
//    }
//
//    Caffeine<Object, Object> caffeineCacheBuilder() {
//        return Caffeine.newBuilder()
//                .initialCapacity(clientCacheInitialCapacity)
//                .maximumSize(clientCacheMaximumSize)
//                .expireAfterWrite(clientCacheExpireAfterWrite, TimeUnit.SECONDS);
//    }
}
