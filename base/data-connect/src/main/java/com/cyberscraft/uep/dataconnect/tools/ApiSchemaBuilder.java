package com.cyberscraft.uep.dataconnect.tools;

import com.cyberscraft.uep.common.domain.auth.Account;
import com.cyberscraft.uep.dataconnect.domain.SuccessPredicate;
import com.cyberscraft.uep.dataconnect.domain.action.HttpAction;
import com.cyberscraft.uep.common.domain.HttpMethod;
import com.cyberscraft.uep.dataconnect.domain.http.HttpOutput;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 *  ApiSchema对象构造器
 * </p>
 *
 * <AUTHOR>
 * @since 2022/10/12 11:07 上午
 */
public class ApiSchemaBuilder {
    /**
     * http method,例如get,post,put,delete,patch等
     */
    private HttpMethod httpMethod;

    /**
     * api endpoint
     */
    private String endpoint;

    /**
     * http header: Content-Type
     */
    private String contentType;

    /**
     * Auth认证配置
     */
    private Account account;

    /**
     * API出参模型
     */
    private HttpOutput outputSchema;

    /**
     * 调用成功条件
     */
    private List<SuccessPredicate> successCondition = new ArrayList<>();

    private ApiSchemaBuilder(){
        super();
    }

    public static ApiSchemaBuilder create(){
        return new ApiSchemaBuilder();
    }

    public final ApiSchemaBuilder setHttpMethod(HttpMethod httpMethod) {
        this.httpMethod = httpMethod;
        return this;
    }

    public final ApiSchemaBuilder setEndpoint(String endpoint) {
        this.endpoint = endpoint;
        return this;
    }

    public final ApiSchemaBuilder setContentType(String contentType) {
        this.contentType = contentType;
        return this;
    }

    public final ApiSchemaBuilder setAccount(Account authParam) {
        this.account = authParam;
        return this;
    }

    public final ApiSchemaBuilder setOutputSchema(HttpOutput outputSchema) {
        this.outputSchema = outputSchema;
        return this;
    }

    public final ApiSchemaBuilder setSuccessCondition(List<SuccessPredicate> successCondition) {
        this.successCondition = successCondition;
        return this;
    }

    public final ApiSchemaBuilder addSuccessPredicate(SuccessPredicate successPredicate){
        this.successCondition.add(successPredicate);
        return this;
    }

    public HttpAction build(){
        HttpAction httpAction = new HttpAction();
        httpAction.setEndpoint(endpoint);
        httpAction.setHttpMethod(httpMethod);
        httpAction.setContentType(contentType);
        httpAction.setAccount(account);
        httpAction.setSuccessCondition(successCondition);

        return httpAction;
    }
}
