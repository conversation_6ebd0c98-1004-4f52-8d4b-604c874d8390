package com.cyberscraft.uep.dataconnect.domain.action;

import com.cyberscraft.uep.common.domain.HttpMethod;
import com.cyberscraft.uep.common.domain.Param;
import com.cyberscraft.uep.common.domain.ResponseEncoding;
import com.cyberscraft.uep.common.domain.ResponseType;
import com.cyberscraft.uep.common.domain.auth.Account;
import com.cyberscraft.uep.dataconnect.domain.SuccessPredicate;

import java.util.List;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2025/1/22 11:40
 */
public class PushUserAction implements Param {

    /**
     * 帐号ID，用于缓存
     */
    private String accountId;

    /**
     *
     */
    private String authProtocol;


    /**
     * 帐号配置
     */
    private Account account;

    /**
     * 虚拟代理IP
     */
    private String proxyIp;

    private String endpoint;

    private HttpMethod httpMethod;

    private HttpMethod createMethod;

    /**
     * 更新的方法
     */
    private HttpMethod updateMethod;

    /**
     * 创建用户或者部门的路径
     */
    private String createPath;

    /**
     * 更新用户或者部门的路径
     */
    private String updatePath;

    /**
     * http header: Content-Type
     */
    private String contentType;

    /**
     * 返回类型
     */
    private ResponseType responseType = ResponseType.JSON;

    /**
     * 应答编码
     */
    private ResponseEncoding encoding = ResponseEncoding.UTF8;

    /**
     * path参数
     */
    private Map<String, Object> path;

    /**
     * query参数
     */
    private Map<String, Object> query;

    /**
     * header参数
     */
    private Map<String, Object> header;

    private Object body;

    private Object updateBody;

    /**
     * 调用成功条件
     */
    private String successCondition;

    /**
     * 从响应结果解析出唯一ID表达式
     */
    private String idExpress;

    /**
     * 从响应结果解析出unionID表达式
     */
    private String unionIdExpress;

    /**
     * 从响应结果解析出openID表达式
     */
    private String openIdExpress;

    /**
     * 是否创建 的表达式
     */
    private String createExpress;

    /**
     * 是否更新的表达式
     */
    private String updateExpress;

    /**
     * 创建标记
     */
    private Map<String, Object> createFlag;

    /**
     * 更新标记
     */
    private Map<String, Object> updateFlag;

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public String getAuthProtocol() {
        return authProtocol;
    }

    public void setAuthProtocol(String authProtocol) {
        this.authProtocol = authProtocol;
    }

    public Account getAccount() {
        return account;
    }

    public void setAccount(Account account) {
        this.account = account;
    }

    public String getProxyIp() {
        return proxyIp;
    }

    public void setProxyIp(String proxyIp) {
        this.proxyIp = proxyIp;
    }

    public String getEndpoint() {
        return endpoint;
    }

    public void setEndpoint(String endpoint) {
        this.endpoint = endpoint;
    }

    public HttpMethod getHttpMethod() {
        return httpMethod;
    }

    public void setHttpMethod(HttpMethod httpMethod) {
        this.httpMethod = httpMethod;
    }

    public HttpMethod getCreateMethod() {
        return createMethod;
    }

    public void setCreateMethod(HttpMethod createMethod) {
        this.createMethod = createMethod;
    }

    public HttpMethod getUpdateMethod() {
        return updateMethod;
    }

    public void setUpdateMethod(HttpMethod updateMethod) {
        this.updateMethod = updateMethod;
    }

    public String getCreatePath() {
        return createPath;
    }

    public void setCreatePath(String createPath) {
        this.createPath = createPath;
    }

    public String getUpdatePath() {
        return updatePath;
    }

    public void setUpdatePath(String updatePath) {
        this.updatePath = updatePath;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public ResponseType getResponseType() {
        return responseType;
    }

    public void setResponseType(ResponseType responseType) {
        this.responseType = responseType;
    }

    public ResponseEncoding getEncoding() {
        return encoding;
    }

    public void setEncoding(ResponseEncoding encoding) {
        this.encoding = encoding;
    }

    public Map<String, Object> getPath() {
        return path;
    }

    public void setPath(Map<String, Object> path) {
        this.path = path;
    }

    public Map<String, Object> getQuery() {
        return query;
    }

    public void setQuery(Map<String, Object> query) {
        this.query = query;
    }

    public Map<String, Object> getHeader() {
        return header;
    }

    public void setHeader(Map<String, Object> header) {
        this.header = header;
    }

    public Object getBody() {
        return body;
    }

    public void setBody(Object body) {
        this.body = body;
    }

    public String getSuccessCondition() {
        return successCondition;
    }

    public void setSuccessCondition(String successCondition) {
        this.successCondition = successCondition;
    }

    public String getIdExpress() {
        return idExpress;
    }

    public void setIdExpress(String idExpress) {
        this.idExpress = idExpress;
    }

    public String getUnionIdExpress() {
        return unionIdExpress;
    }

    public void setUnionIdExpress(String unionIdExpress) {
        this.unionIdExpress = unionIdExpress;
    }

    public String getOpenIdExpress() {
        return openIdExpress;
    }

    public void setOpenIdExpress(String openIdExpress) {
        this.openIdExpress = openIdExpress;
    }

    public String getCreateExpress() {
        return createExpress;
    }

    public void setCreateExpress(String createExpress) {
        this.createExpress = createExpress;
    }

    public String getUpdateExpress() {
        return updateExpress;
    }

    public void setUpdateExpress(String updateExpress) {
        this.updateExpress = updateExpress;
    }

    public Object getUpdateBody() {
        return updateBody;
    }

    public void setUpdateBody(Object updateBody) {
        this.updateBody = updateBody;
    }

    public Map<String, Object> getCreateFlag() {
        return createFlag;
    }

    public void setCreateFlag(Map<String, Object> createFlag) {
        this.createFlag = createFlag;
    }

    public Map<String, Object> getUpdateFlag() {
        return updateFlag;
    }

    public void setUpdateFlag(Map<String, Object> updateFlag) {
        this.updateFlag = updateFlag;
    }
}
