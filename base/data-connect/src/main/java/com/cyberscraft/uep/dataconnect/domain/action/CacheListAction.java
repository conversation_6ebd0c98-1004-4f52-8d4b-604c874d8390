package com.cyberscraft.uep.dataconnect.domain.action;

import com.cyberscraft.uep.common.domain.Param;

import java.util.List;

/**
 * <p>
 *  列表型参数
 * </p>
 *
 * <AUTHOR>
 * @since 2024/3/26 11:22 上午
 */
public class CacheListAction implements Param {
    /**
     * 存入、读取
     */
    private String operation;

    /**
     * 缓存的key
     */
    private String key;

    /**
     * 缓存的值
     */
    private List<String> value;

    /**
     * 列表最大长度
     */
    private Integer maxSize = 16;

    /**
     * 过期时间（天）
     */
    private Integer expireTime;

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public List<String> getValue() {
        return value;
    }

    public void setValue(List<String> value) {
        this.value = value;
    }

    public Integer getMaxSize() {
        return maxSize;
    }

    public void setMaxSize(Integer maxSize) {
        this.maxSize = maxSize;
    }

    public Integer getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Integer expireTime) {
        this.expireTime = expireTime;
    }
}
