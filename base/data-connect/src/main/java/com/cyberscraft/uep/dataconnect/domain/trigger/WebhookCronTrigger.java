package com.cyberscraft.uep.dataconnect.domain.trigger;

import com.alibaba.fastjson.JSONObject;
import com.cyberscraft.uep.common.domain.Param;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/5 12:16 下午
 */
public class WebhookCronTrigger implements Param {

    /**
     * webhook url
     */
    private String hookUrl;

    /**
     * 连接流执行方式
     */
    private String type;

    /**
     * 参数值
     */
    private Object value;

    //定时参数

    /**
     * 开始日期
     */
    private String startDate;

    /**
     * 结束日期
     */
    private String endDate;

    /**
     * cron表达式
     */
    private String cycle;

    public String getHookUrl() {
        return hookUrl;
    }

    public void setHookUrl(String hookUrl) {
        this.hookUrl = hookUrl;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Object getValue() {
        return value;
    }

    public void setValue(Object value) {
        this.value = value;
    }

    public boolean isSync() {
        return "同步".equals(this.type);
    }

    public JSONObject toJson() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("hookUrl", this.hookUrl);
        jsonObject.put("type", this.type);
        jsonObject.put("value", this.value);
        jsonObject.put("startDate", this.startDate);
        jsonObject.put("endDate", this.endDate);
        jsonObject.put("cycle", this.cycle);

        return jsonObject;
    }
}
