package com.cyberscraft.uep.dataconnect.domain.node;

import com.cyberscraft.uep.common.domain.auth.AccountParam;
import com.cyberscraft.uep.dataconnect.domain.ProtocolType;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/4 11:50 上午
 */
public class StartNode extends Node {

    public StartNode(ProtocolType protocol, String flowId, String nodeId, String name, NodeParam input, AccountParam accountParam) {
        super(protocol, flowId, nodeId, name, input, accountParam);
    }

    @Override
    public NodeType getNodeType() {
        return NodeType.StartEvent;
    }
}
