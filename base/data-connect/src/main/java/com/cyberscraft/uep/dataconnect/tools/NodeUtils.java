package com.cyberscraft.uep.dataconnect.tools;

import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.dataconnect.constant.Constants;
import com.cyberscraft.uep.dataconnect.context.NodeRunContext;
import com.cyberscraft.uep.dataconnect.domain.ErrorExecEnum;
import com.cyberscraft.uep.dataconnect.domain.node.Node;
import com.cyberscraft.uep.dataconnect.domain.node.RunStatus;
import com.cyberscraft.uep.dataconnect.exception.FlowException;
import com.cyberscraft.uep.dataconnect.service.IFlowLogService;
import com.cyberscraft.uep.dataconnect.service.IFlowService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/10/23 9:27 下午
 */
public class NodeUtils {
    private final static Logger logger = LoggerFactory.getLogger(NodeUtils.class);

    public static RunStatus successExecChildren(String taskId, List<Node> children, Map<String, Object> context, IFlowService flowService, Map<String, NodeRunContext> nodeContext, Node parentNode) {
        //result优先记录FAILED、WARNING，也就是如果有节点WARNING了，后面节点即使SUCCESS，result还是WARNING
        RunStatus result = RunStatus.SUCCESS;
        Boolean onLine = (Boolean) context.get(Constants.IS_ONLINE_CHECK);
        if (children == null && onLine) {
            throw new FlowException("当前连接流存在循环节点的子节点为空，请完善之后再上线！");
        }
        if (children != null) {
            logger.debug("children context:{}", JsonUtil.obj2Str(context));
            for (Node child : children) {
                try {
                    flowService.doAction(taskId, child, context, nodeContext);
                    if (child.getRunStatus().equals(RunStatus.FAILED)) {
                        result = RunStatus.FAILED;
                        // 子节点终止，则父节点也终止，保持一致
                        parentNode.setEnd(child.getEnd());

                        parentNode.setRunStatus(RunStatus.FAILED);
                        if (child.getEnd()) {
                            parentNode.setErrorMsg("子节点返回失败");
                        } else {
                            parentNode.setErrorMsg(child.getErrorMsg());
                        }
                        break;
                    } else if (child.getRunStatus().equals(RunStatus.WARNING)) {
                        result = RunStatus.WARNING;
                    } else if (child.getEnd()) {
                        // 子节点终止，则父节点也终止，保持一致
                        parentNode.setEnd(child.getEnd());
                        break;
                    }
                } catch (Exception e) {
                    parentNode.setErrorMsg(e.getMessage());
                    result = RunStatus.FAILED;
                    break;
                }
            }
        }
        return result;
    }

    public static void debugForTrue(Node node, IFlowLogService flowLogService) {
        List<Node> children = node.getChildren();
        if (children != null) {
            for (Node child : children) {
                if (child != null) {
                    child.setRunStatus(RunStatus.IDLE);
                    child.setRunEndTime(LocalDateTime.now());
                    //记录child执行完
                    flowLogService.updateFlowNode("0", child);
                    debugForTrue(child, flowLogService);
                }
            }
        }
    }

    /**
     * 处理循环节点的返回状态
     *
     * @param parentNode  循环节点
     * @param childStatus 子节点的执行状态
     * @param errorExec   错误执行方式
     * @return 是否终止执行，true表示终止执行，false表示继续执行
     */
    public static boolean handleLoopStatus(Node parentNode, RunStatus childStatus, ErrorExecEnum errorExec) throws FlowException {
        switch (childStatus) {
            case FAILED:
                return handleFailedStatus(parentNode, errorExec);
            case WARNING:
                parentNode.setRunStatus(RunStatus.WARNING);
                return parentNode.getEnd();
            case SUCCESS:
                // 根据终止标记，决定流程 继续 or 终止
                return parentNode.getEnd();
            default:
                return false;
        }
    }

    private static boolean handleFailedStatus(Node node, ErrorExecEnum errorExec) throws FlowException {
        switch (errorExec) {
            case BREAK:
                node.setRunStatus(RunStatus.WARNING);
                return true;
            case CONTINUE:
                node.setRunStatus(RunStatus.WARNING);
                return node.getEnd();
            default:
                String errorMsg = node.getErrorMsg();
                if (StringUtils.isBlank(errorMsg)) {
                    errorMsg = "子节点执行失败导致流程终止";
                }
                node.setErrorMsg(errorMsg);
                node.setRunStatus(RunStatus.FAILED);
                return true;
        }
    }

    public static void main(String[] args) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("name", "yang");
        HashMap<String, Object> map1 = new HashMap<>();
        map1.put("map", map);


        Map<String,Object> clone = (Map<String,Object>)map1.clone();
        Map<String,Object> map2 = (Map<String,Object>)clone.get("map");
        map2.put("age", 40);

        System.out.println(map1);

    }
}
