package com.cyberscraft.uep.dataconnect.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * @Author：xhquan
 * @Project：uep-services
 * @since：2023/8/23 19:13
 */
@Component("dataConnectRedisPool")
@ConfigurationProperties(prefix = "data.connect.redis.pool")
@RefreshScope
public class DataConnectRedisPoolConfig {
    private int maxIdle = 30;
    private int maxActive = 30;
    private int maxWait = 10000;
    private int maxThreads = 16;
    private int maxNettyThreads = 32;
    private boolean testOnBorrow = false;

    public int getMaxIdle() {
        return maxIdle;
    }

    public void setMaxIdle(int maxIdle) {
        this.maxIdle = maxIdle;
    }

    public int getMaxActive() {
        return maxActive;
    }

    public void setMaxActive(int maxActive) {
        this.maxActive = maxActive;
    }

    public int getMaxWait() {
        return maxWait;
    }

    public void setMaxWait(int maxWait) {
        this.maxWait = maxWait;
    }

    public boolean isTestOnBorrow() {
        return testOnBorrow;
    }

    public void setTestOnBorrow(boolean testOnBorrow) {
        this.testOnBorrow = testOnBorrow;
    }

    public int getMaxThreads() {
        return maxThreads;
    }

    public void setMaxThreads(int maxThreads) {
        this.maxThreads = maxThreads;
    }

    public int getMaxNettyThreads() {
        return maxNettyThreads;
    }

    public void setMaxNettyThreads(int maxNettyThreads) {
        this.maxNettyThreads = maxNettyThreads;
    }
}