package com.cyberscraft.uep.dataconnect.handler;

import com.cyberscraft.uep.account.client.service.IAuthenticateService;
import com.cyberscraft.uep.common.config.FTPConfig;
import com.cyberscraft.uep.common.domain.Param;
import com.cyberscraft.uep.common.domain.ParamSchema;
import com.cyberscraft.uep.common.domain.auth.AccountParam;
import com.cyberscraft.uep.dataconnect.constant.Constants;
import com.cyberscraft.uep.dataconnect.context.NodeRunContext;
import com.cyberscraft.uep.dataconnect.domain.ProtocolType;
import com.cyberscraft.uep.dataconnect.domain.action.FTPAction;
import com.cyberscraft.uep.dataconnect.domain.node.Node;
import com.cyberscraft.uep.dataconnect.domain.node.NodeOutput;
import com.cyberscraft.uep.dataconnect.exception.FlowException;
import com.cyberscraft.uep.dataconnect.tools.ContextHolder;
import com.cyberscraft.uep.dataconnect.tools.NodeContextHolder;
import org.apache.commons.net.ftp.FTPClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.util.HashMap;
import java.util.Map;

/**
 * @description: FTP动作处理实现类
 * @author: seven
 * @date: 2024/3/13
 */
@Service
public class FTPActionHandler implements IActionHandler {
    private final static Logger logger = LoggerFactory.getLogger(FTPActionHandler.class);

    @Autowired
    private IAuthenticateService authenticateService;

    @Override
    public boolean isSupported(ProtocolType protocolType) {
        return ProtocolType.FTP == protocolType;
    }

    @Override
    public void run(String taskId, Node node) {
        NodeRunContext nodeRunContext = NodeContextHolder.get(node.getNodeId());
        Param inputParam = (Param) nodeRunContext.getInputValue();

        FTPAction ftpAction = (FTPAction) inputParam;
        //下载
        if ("DOWNLOAD".equals(ftpAction.getOpType())) {
            Boolean result = download(node, ftpAction);
            if (result) {
                node.setOutput(new NodeOutput(result));
            } else {
                throw new FlowException("FTP下载失败");
            }
            //上传 todo seven
        } else if ("UPLOAD".equals(ftpAction.getOpType())) {

        }
    }

    private Boolean download(Node node, FTPAction ftpAction) {
        Boolean result = false;

        AccountParam accountParam = node.getAccount();
        FTPConfig account = (FTPConfig) authenticateService.getAccount(accountParam, new HashMap<>(), new HashMap<>());

        String host = account.getHost();
        int port = account.getPort();
        String username = account.getUsername();
        String password = account.getPassword();
        String transferMode = account.getTransferMode();

        String remoteFilePath = ftpAction.getRemoteFilePath();

        File remoteFile = new File(remoteFilePath);
        String filename = remoteFile.getName();
        if (!filename.contains(".")) {
            throw new FlowException("文件路径中未包含文件名称！");
        }
        String localSavePath = "/tmp/access/" + ContextHolder.get(Constants.CURRENT_TENANT_ID) + "/" + filename;

//        String localSavePath = "C:\\tmp\\"+context.get("currentTenantId")+"\\"+ filename;
        File localFile = new File(localSavePath);
        // 获取父目录
        File parentDir = localFile.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            boolean dirCreated = parentDir.mkdirs();
            if (!dirCreated) {
                throw new FlowException("无法创建本地目录：" + parentDir.getAbsolutePath());
            }
        }


        FTPClient ftpClient = new FTPClient();
        try {
            ftpClient.connect(host, port); // 连接到指定端口的FTP服务器
            ftpClient.login(username, password); // 使用用户名和密码登录
            if ("binary".equals(transferMode)) {
                ftpClient.setFileType(2);
            } else if ("ASCII".equals(transferMode)) {
                ftpClient.setFileType(1);
            }
            ftpClient.enterLocalPassiveMode();

            try (OutputStream outputStream = new FileOutputStream(localSavePath)) {
                // 下载文件
                result = ftpClient.retrieveFile(remoteFilePath, outputStream);
                if (result) {
                    logger.info("文件下载成功: " + remoteFilePath);
                } else {
                    logger.error("文件下载失败: " + remoteFilePath);
                    throw new FlowException("文件下载失败");
                }
            }
            ftpClient.logout();
        } catch (IOException e) {
            throw new RuntimeException(e);
        } catch (Exception ex) {
            throw new FlowException("下载错误，错误详情：" + ex.toString());
        } finally {
            if (ftpClient.isConnected()) {
                try {
                    ftpClient.disconnect();
                } catch (IOException ex) {
                    logger.error("ftpClient断开异常，详情：" + ex);
                }
            }
        }
        return result;
    }

    private Boolean upload(Node node, MultipartFile file, Map<String, Object> context) throws Exception {
        Boolean result = false;
        AccountParam accountParam = node.getAccount();
        FTPConfig account = (FTPConfig) authenticateService.getAccount(accountParam, new HashMap<>(), new HashMap<>());

        String host = account.getHost();
        int port = account.getPort();
        String username = account.getUsername();
        String password = account.getPassword();
        String transferMode = account.getTransferMode();

        ParamSchema paramSchema = node.getAccount().getAccount();
        String remoteFilePath = paramSchema.getSubParamByName("remoteFilePath").getValue();

        FTPClient ftpClient = new FTPClient();

        try {
            if ("binary".equals(transferMode)) {
                ftpClient.setFileType(2);
            } else if ("ASCII".equals(transferMode)) {
                ftpClient.setFileType(1);
            }

            ftpClient.connect(host, port);
            ftpClient.login(username, password);

            // 使用MultipartFile的getInputStream方法获取文件的输入流
            try (InputStream inputStream = file.getInputStream()) {
                // 上传文件到远程FTP服务器
                result = ftpClient.storeFile(remoteFilePath, inputStream);
                if (result) {
                    logger.info("文件上传成功: " + remoteFilePath);
                } else {
                    logger.error("文件上传失败: " + remoteFilePath);
                }
            }
            ftpClient.logout();
        } catch (IOException e) {
            throw new RuntimeException(e);
        } catch (Exception ex) {
            throw new Exception("上传错误，错误详情：" + ex.toString());
        } finally {
            if (ftpClient.isConnected()) {
                try {
                    ftpClient.disconnect();
                } catch (IOException ex) {
                    logger.error("ftpClient断开异常，详情：" + ex);
                }
            }
        }
        return result;
    }


}
