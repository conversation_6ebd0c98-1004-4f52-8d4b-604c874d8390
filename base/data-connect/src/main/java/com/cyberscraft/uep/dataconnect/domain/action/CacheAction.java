package com.cyberscraft.uep.dataconnect.domain.action;

import com.cyberscraft.uep.common.domain.Param;

import java.util.Map;

/**
 * @Author：xhquan
 * @Project：uep-services
 * @since：2023/8/22 17:15
 */
public class CacheAction implements Param {

    /**
     * 操作 保存、读取、删除
     */
    private String operation;

    /**
     * 缓存的key
     */
    private String key;

    /**
     * 缓存的值
     */
    private String value;

    /**
     * 过期时间
     */
    private String expireTime;

    public String getOperation() {
        return operation;
    }

    public void setOperation(String operation) {
        this.operation = operation;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(String expireTime) {
        this.expireTime = expireTime;
    }
}
