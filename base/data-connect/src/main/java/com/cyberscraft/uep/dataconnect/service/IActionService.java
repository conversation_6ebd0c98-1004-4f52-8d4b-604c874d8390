package com.cyberscraft.uep.dataconnect.service;

import com.cyberscraft.uep.common.domain.Param;
import com.cyberscraft.uep.dataconnect.domain.node.Node;

/**
 * <p>
 * 执行动作service
 * </p>
 *
 * <AUTHOR>
 * @since 2022/10/8 4:03 下午
 */
public interface IActionService {

    /**
     * 执行动作
     *
     * @param taskId
     * @param node
     * @param inputParam
     * @return
     */
    boolean run(String taskId, Node node, Param inputParam);

}
