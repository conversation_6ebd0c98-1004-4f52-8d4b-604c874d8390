package com.cyberscraft.uep.dataconnect.domain.action;

import com.cyberscraft.uep.common.domain.Param;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/3/27 2:05 下午
 */
public class OpenAiAction implements Param {

    /**
     * 模型
     */
    private String model;

    /**
     * 角色设定
     */
    private String prompt;

    /**
     * 温度
     */
    private String temperature = "0.7";

    /**
     * 上下文长度
     */
    private Integer contextLength = 6;

    /**
     * 问题
     */
    private String question;

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getPrompt() {
        return prompt;
    }

    public void setPrompt(String prompt) {
        this.prompt = prompt;
    }

    public String getTemperature() {
        return temperature;
    }

    public void setTemperature(String temperature) {
        this.temperature = temperature;
    }

    public Integer getContextLength() {
        return contextLength;
    }

    public void setContextLength(Integer contextLength) {
        this.contextLength = contextLength;
    }

    public String getQuestion() {
        return question;
    }

    public void setQuestion(String question) {
        this.question = question;
    }
}
