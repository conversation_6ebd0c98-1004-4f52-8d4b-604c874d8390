package com.cyberscraft.uep.dataconnect.handler;

import com.cyberscraft.uep.common.domain.Param;
import com.cyberscraft.uep.common.util.JsUtil;
import com.cyberscraft.uep.dataconnect.context.NodeRunContext;
import com.cyberscraft.uep.dataconnect.domain.ProtocolType;
import com.cyberscraft.uep.dataconnect.domain.node.Node;
import com.cyberscraft.uep.dataconnect.domain.node.NodeOutput;
import com.cyberscraft.uep.dataconnect.domain.node.NodeParam;
import com.cyberscraft.uep.dataconnect.exception.FlowException;
import com.cyberscraft.uep.dataconnect.tools.ContextHolder;
import com.cyberscraft.uep.dataconnect.tools.NodeContextHolder;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 通用handler，输入当输出
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/12 4:15 下午
 */
@Service
public class CommonActionHandler implements IActionHandler {

    @Override
    public boolean isSupported(ProtocolType protocolType) {
        return ProtocolType.DEFINE_VAR == protocolType || ProtocolType.JS == protocolType
                || ProtocolType.WEBHOOK == protocolType || ProtocolType.CRONTAB == protocolType || ProtocolType.STREAM == protocolType;
    }

    @Override
    public void run(String taskId, Node node) {
        NodeRunContext nodeRunContext = NodeContextHolder.get(node.getNodeId());
        if (ProtocolType.DEFINE_VAR.equals(node.getProtocol())) {

            Map<String,Object> param = (Map) nodeRunContext.getInputValue();
            HashMap<String,Object> vars = (HashMap) param.get("vars");
            Set<String> keys = vars.keySet();
            if (keys.contains("")) {
                throw new FlowException("必填内容值为空");
            }

            for (String key : keys) {
                final Object varValue = vars.get(key);
                if (varValue instanceof String) {
                    String valExpression = varValue.toString();
                    Object value = JsUtil.runExpression(valExpression, ContextHolder.getContext());
                    vars.put(key, value);
                }
            }

        }

        Object value = nodeRunContext.getInputValue();
        Param input = node.getInput();
        if (input instanceof NodeParam) {
            node.setOutput(new NodeOutput(((NodeParam) input).getParam(), value));
        } else {
            node.setOutput(new NodeOutput(value));
        }
    }
}
