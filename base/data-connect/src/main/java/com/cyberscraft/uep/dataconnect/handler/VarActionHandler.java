package com.cyberscraft.uep.dataconnect.handler;

import com.cyberscraft.uep.common.domain.Param;
import com.cyberscraft.uep.common.util.MappingParser;
import com.cyberscraft.uep.dataconnect.constant.Constants;
import com.cyberscraft.uep.dataconnect.context.NodeRunContext;
import com.cyberscraft.uep.dataconnect.domain.ProtocolType;
import com.cyberscraft.uep.dataconnect.domain.action.VarAction;
import com.cyberscraft.uep.dataconnect.domain.node.Node;
import com.cyberscraft.uep.dataconnect.domain.node.NodeOutput;
import com.cyberscraft.uep.dataconnect.domain.var.UpdateVar;
import com.cyberscraft.uep.dataconnect.domain.var.VarOutput;
import com.cyberscraft.uep.dataconnect.exception.FlowException;
import com.cyberscraft.uep.dataconnect.tools.ContextHolder;
import com.cyberscraft.uep.dataconnect.tools.NodeContextHolder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/12 12:34 下午
 */
@Service
public class VarActionHandler implements IActionHandler {
    private final static Logger logger = LoggerFactory.getLogger(VarActionHandler.class);

    @Override
    public boolean isSupported(ProtocolType protocolType) {
        return ProtocolType.SET_VAR == protocolType;
    }

    @Override
    public void run(String taskId, Node node) {
        NodeRunContext nodeRunContext = NodeContextHolder.get(node.getNodeId());
        Param inputParam = (Param) nodeRunContext.getInputValue();

        try {
            VarAction varAction = (VarAction) inputParam;
            UpdateVar input = varAction.getInput();
            MappingParser.updateMapByPath(ContextHolder.getContext(), input.getVarPath(), input.getValue());
            VarOutput varOutput = new VarOutput(200, input.getValue(), Constants.SUCCESS_MSG);

            node.setOutput(new NodeOutput(varOutput));
        } catch (Exception e) {
            logger.error("set var value error.", e);
            VarOutput varOutput = new VarOutput(400, null, e.getMessage());
            node.setOutput(new NodeOutput(varOutput));
            throw new FlowException(e.getMessage());
        }
    }
}
