package com.cyberscraft.uep.dataconnect.domain.action;

import com.cyberscraft.uep.common.config.DataBaseConfig;
import com.cyberscraft.uep.common.config.EmailConfig;
import com.cyberscraft.uep.common.domain.Param;
import com.cyberscraft.uep.dataconnect.domain.db.SqlOutput;
import com.cyberscraft.uep.dataconnect.domain.email.EmailOutput;

/**
 * @description: 邮箱动作参数
 * @author:行家玮
 * @date:2024/1/23
 */
public class EmailAction implements Param {

    /**
     * 帐号ID
     */
    private String accountId;

    /**
     * 邮箱配置
     */
    private EmailConfig account;

    /**
     * 发件人
     */
    private String from;

    /**
     * 收件人
     */
    private String to;

    /**
     * 抄送人
     */
    private String cc;

    /**
     * 邮件主题
     */
    private String subject;

    /**
     * 邮件正文
     */
    private String body;

    /**
     * 邮件正文格式
     */
    private boolean bodyFormat;

    /**
     * 邮件附件url
     */
    private String attachmentUrl;

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public EmailConfig getAccount() {
        return account;
    }

    public void setAccount(EmailConfig account) {
        this.account = account;
    }

    public String getFrom() {
        return from;
    }

    public void setFrom(String from) {
        this.from = from;
    }

    public String getTo() {
        return to;
    }

    public void setTo(String to) {
        this.to = to;
    }

    public String getCc() {
        return cc;
    }

    public void setCc(String cc) {
        this.cc = cc;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getBody() {
        return body;
    }

    public void setBody(String body) {
        this.body = body;
    }

    public boolean isBodyFormat() {
        return bodyFormat;
    }

    public void setBodyFormat(boolean bodyFormat) {
        this.bodyFormat = bodyFormat;
    }

    public String getAttachmentUrl() {
        return attachmentUrl;
    }

    public void setAttachmentUrl(String attachmentUrl) {
        this.attachmentUrl = attachmentUrl;
    }

}
