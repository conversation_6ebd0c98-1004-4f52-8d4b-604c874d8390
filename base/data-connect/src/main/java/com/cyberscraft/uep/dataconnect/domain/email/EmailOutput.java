package com.cyberscraft.uep.dataconnect.domain.email;

import com.cyberscraft.uep.dataconnect.constant.Constants;

import java.util.HashMap;
import java.util.Map;

/**
 * @description: 邮箱返回输出
 * @author:行家玮
 * @date:2024/1/23
 */
public class EmailOutput {

    /**
     * 执行是否成功
     */
    private Boolean result;

    /**
     * 执行消息
     */
    private String msg;

    public EmailOutput(Boolean result, String msg) {
        this.result = result;
        this.msg = msg;
    }

    public Boolean getResult() {
        return result;
    }

    public void setResult(Boolean result) {
        this.result = result;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Map<String, Object> toMap() {
        Map<String, Object> params = new HashMap<>();
        params.put(Constants.RESULT_KEY, getResult());
        params.put(Constants.DATA_KEY, getMsg());
        return params;
    }

}
