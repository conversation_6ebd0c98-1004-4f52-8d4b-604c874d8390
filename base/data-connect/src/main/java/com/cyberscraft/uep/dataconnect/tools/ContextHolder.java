package com.cyberscraft.uep.dataconnect.tools;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/11/1 3:17 下午
 */
public class ContextHolder {
    private static final ThreadLocal<Map<String, Object>> contextLocal = new ThreadLocal<>();

    public static void setContext(Map<String, Object> context) {
        contextLocal.set(context);
    }

    public static void clean() {
        contextLocal.remove();
    }

    public static Map<String,Object> getContext() {
        return contextLocal.get();
    }

    public static void put(String contextName, Object value) {
        Map<String, Object> context = getContext();
        if (context == null) {
            context = new HashMap<>();
            setContext(context);
        }

        context.put(contextName, value);
    }

    public static Object get(String contextName) {
        Map<String, Object> context = getContext();
        if (context != null) {
            return context.get(contextName);
        }
        return null;
    }

    public static Object remove(String contextName) {
        Map<String, Object> context = getContext();
        if (context != null) {
            return context.remove(contextName);
        }
        return null;
    }
}
