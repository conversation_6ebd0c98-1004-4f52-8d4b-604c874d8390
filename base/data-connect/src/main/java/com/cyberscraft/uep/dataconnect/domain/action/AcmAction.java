package com.cyberscraft.uep.dataconnect.domain.action;

import com.cyberscraft.uep.common.domain.Param;

import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/10/12 17:50
 */
public class AcmAction implements Param {

    /**
     * 业务操作 的 Key
     */
    private String key;

    /**
     * 输入参数
     */
    private Object input;

    /**
     * 账号信息 业务操作需要的鉴权信息
     */
    private Object account;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public Object getInput() {
        return input;
    }

    public void setInput(Object input) {
        this.input = input;
    }

    public Object getAccount() {
        return account;
    }

    public void setAccount(Object account) {
        this.account = account;
    }
}
