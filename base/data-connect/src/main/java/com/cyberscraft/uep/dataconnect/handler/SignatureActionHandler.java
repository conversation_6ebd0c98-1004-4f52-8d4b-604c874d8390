package com.cyberscraft.uep.dataconnect.handler;

import com.cyberscraft.uep.account.client.service.IAuthenticateService;
import com.cyberscraft.uep.common.domain.Param;
import com.cyberscraft.uep.common.util.ByteUtil;
import com.cyberscraft.uep.common.util.HMACSignUtil;
import com.cyberscraft.uep.common.util.MD5Util;
import com.cyberscraft.uep.common.util.SHAUtil;
import com.cyberscraft.uep.dataconnect.context.NodeRunContext;
import com.cyberscraft.uep.dataconnect.domain.ProtocolType;
import com.cyberscraft.uep.dataconnect.domain.action.SignatureAction;
import com.cyberscraft.uep.dataconnect.domain.node.Node;
import com.cyberscraft.uep.dataconnect.domain.node.NodeOutput;
import com.cyberscraft.uep.dataconnect.exception.FlowException;
import com.cyberscraft.uep.dataconnect.service.IFlowLogService;
import com.cyberscraft.uep.dataconnect.tools.NodeContextHolder;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/4/10 15:30
 */
@Service
public class SignatureActionHandler implements IActionHandler {

    private final static Logger logger = LoggerFactory.getLogger(SignatureActionHandler.class);


    private IFlowLogService flowLogService;

    private IAuthenticateService authenticateService;

    @Autowired
    public void setFlowLogService(IFlowLogService flowLogService) {
        this.flowLogService = flowLogService;
    }

    @Autowired
    public void setAuthenticateService(IAuthenticateService authenticateService) {
        this.authenticateService = authenticateService;
    }

    @Override
    public boolean isSupported(ProtocolType protocolType) {
        return ProtocolType.SIGNATURE.equals(protocolType);
    }

    @Override
    public void run(String taskId, Node node) {
        NodeRunContext nodeRunContext = NodeContextHolder.get(node.getNodeId());
        Param inputParam = (Param) nodeRunContext.getInputValue();
        SignatureAction action = (SignatureAction) inputParam;
        try {
            byte[] bytes = null;
            byte[] content = null;
            if (StringUtils.isNotBlank(action.getContent())) {
                content = stringToByte(action.getContentEncode(), action.getContent());
            }

            byte[] key = null;
            if (StringUtils.isNotBlank(action.getSecret())) {
                key = stringToByte(action.getKeyEncode(), action.getSecret());
            }
            switch (action.getAlg()) {
                case "HmacMD5":
                case "HmacSHA1":
                case "HmacSHA224":
                case "HmacSHA256":
                case "HmacSHA384":
                case "HmacSHA512":
                    bytes = HMACSignUtil.hMacSign(action.getAlg(), content, key);
                    break;
                case "MD5":
                    bytes = MD5Util.md5(content);
                    break;
                case "SHA-1":
                case "SHA-224":
                case "SHA-256":
                case "SHA-384":
                case "SHA-512":
                    bytes = SHAUtil.getSHABytes(action.getAlg(), content);
                    break;
            }
            if (bytes == null) {
                throw new FlowException("sign fail");
            }
            String result = byteToString(action.getEncode(), bytes);
            node.setOutput(new NodeOutput(result));
        } catch (Exception e) {
            logger.warn("signature exception ", e);
            throw new FlowException(e.getMessage());
        }
    }

    private byte[] stringToByte(String encode, String data) {
        byte[] content = null;
        if (StringUtils.isNotBlank(encode)) {
            switch (encode) {
                case "Base64":
                case "base64":
                    content = Base64.decodeBase64(data);
                    break;
                case "Hex":
                case "hex":
                    content = ByteUtil.hex2Byte(data);
                    break;
                case "HEX":
                    content = ByteUtil.hex2Byte(data.toLowerCase());
                    break;
                case "PlainText":
                    content = data.getBytes(StandardCharsets.UTF_8);
                    break;
            }
        } else {
            content = data.getBytes(StandardCharsets.UTF_8);
        }
        return content;
    }

    private String byteToString(String encode, byte[] data) {
        String result = "";
        if (StringUtils.isNotBlank(encode)) {
            switch (encode) {
                case "Base64":
                case "base64":
                    result = Base64.encodeBase64String(data);
                    break;
                case "Hex":
                case "hex":
                    result = ByteUtil.byte2Hex(data);
                    break;
                case "HEX":
                    result = ByteUtil.byte2Hex(data).toUpperCase();
                    break;
                default:
                    result = new String(data);
            }
        } else {
            result = new String(data);
        }
        return result;
    }

    public static void main(String[] args) throws Exception {
        String s = Base64.encodeBase64String(SHAUtil.getSHABytes("SHA3-256", "1234", StandardCharsets.UTF_8));
        System.out.println(s);
    }
}
