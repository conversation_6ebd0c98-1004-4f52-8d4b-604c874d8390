package com.cyberscraft.uep.account.client.provider.etl.service.impl;

import com.cyberscraft.uep.account.client.domain.ThirdPartyAccount;
import com.cyberscraft.uep.account.client.domain.ThirdPartyGroup;
import com.cyberscraft.uep.account.client.domain.ThirdPartyGroupPosition;
import com.cyberscraft.uep.account.client.provider.etl.dbo.EtlOrgDBO;
import com.cyberscraft.uep.account.client.provider.etl.dbo.EtlUserDBO;
import com.cyberscraft.uep.account.client.provider.etl.dbo.EtlUserOrgDBO;
import com.cyberscraft.uep.account.client.provider.etl.entity.EtlOrgEntity;
import com.cyberscraft.uep.account.client.provider.etl.entity.EtlUserEntity;
import com.cyberscraft.uep.account.client.provider.etl.entity.EtlUserOrgEntity;
import com.cyberscraft.uep.account.client.provider.etl.service.IETLAccountService;
import com.cyberscraft.uep.account.client.provider.etl.transfer.EtlOrgTransfer;
import com.cyberscraft.uep.account.client.provider.etl.transfer.EtlUserOrgTransfer;
import com.cyberscraft.uep.account.client.provider.etl.transfer.EtlUserTransfer;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class ETLAccountServiceImpl implements IETLAccountService {

    @Resource
    private EtlOrgTransfer etlOrgTransfer;

    @Resource
    private EtlUserTransfer etlUserTransfer;

    @Resource
    private EtlUserOrgTransfer etlUserOrgTransfer;

    @Resource
    private EtlOrgDBO etlOrgDBO;

    @Resource
    private EtlUserOrgDBO etlUserOrgDBO;

    @Resource
    private EtlUserDBO etlUserDBO;

    @Override
    public ThirdPartyGroup getRootGroup(String datasourceId) {
        EtlOrgEntity etlOrgEntity = etlOrgDBO.getRootOrg(datasourceId);
        return etlOrgTransfer.entity2Vo(etlOrgEntity);
    }

    @Override
    public List<ThirdPartyGroup> getAllGroups(String datasourceId) {
        List<EtlOrgEntity> etlOrgEntities = etlOrgDBO.getAllOrgs(datasourceId);
        List<ThirdPartyGroup> thirdPartyGroups = etlOrgEntities.stream().map(e -> etlOrgTransfer.entity2Vo(e)).collect(Collectors.toList());
        return thirdPartyGroups;
    }

    @Override
    public ThirdPartyGroup getGroup(String datasourceId, String orgId) {
        EtlOrgEntity entity = etlOrgDBO.getOrg(datasourceId, orgId);
        return etlOrgTransfer.entity2Vo(entity);
    }

    @Override
    public List<ThirdPartyAccount> getAccountsByGroup(String datasourceId, String orgId) {
        List<ThirdPartyAccount> result = new ArrayList<>();
        // 查询该组下对应的用户
        List<EtlUserOrgEntity> etlUserOrgEntities = etlUserOrgDBO.getUserOrgByOrgId(datasourceId, orgId);
        List<String> userIds = etlUserOrgEntities.stream().map(e -> e.getUid()).collect(Collectors.toList());
        if(userIds == null || userIds.isEmpty()) {
            return result;
        }

        // 查询这些用户所在的组
        List<EtlUserOrgEntity> etlUserOrgEntities2 = etlUserOrgDBO.getUserOrgByUids(datasourceId, userIds);
        Map<String, List<ThirdPartyGroupPosition>> mapUserOrg = new HashMap<>();
        if(etlUserOrgEntities2 != null && etlUserOrgEntities2.size() > 0) {
            etlUserOrgEntities2.forEach( e -> {
                String key = e.getUid();
                List<ThirdPartyGroupPosition> groupPositionList = mapUserOrg.get(key);
                if (groupPositionList == null) {
                    groupPositionList = new ArrayList<>();
                }
                ThirdPartyGroupPosition groupPosition = etlUserOrgTransfer.entity2Vo(e);
                groupPositionList.add(groupPosition);
                mapUserOrg.put(key, groupPositionList);
            });
        }
        List<EtlUserEntity> etlUserEntities = etlUserDBO.getUsersByIds(datasourceId, userIds);
        if(etlUserEntities != null && etlUserEntities.size() > 0) {
            result = etlUserEntities.stream().map(e -> {
                ThirdPartyAccount account = etlUserTransfer.entity2Vo(e);
                account.setGroupPositions(mapUserOrg.get(e.getId()));
                return account;
            }).collect(Collectors.toList());
        }
        return result;
    }
}
