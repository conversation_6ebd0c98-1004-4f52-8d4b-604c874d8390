package com.cyberscraft.uep.account.client.provider.etl.transfer;

import com.cyberscraft.uep.account.client.domain.ThirdPartyAccount;
import com.cyberscraft.uep.account.client.provider.etl.entity.EtlUserEntity;
import com.cyberscraft.uep.common.util.JsonUtil;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;
import org.springframework.data.convert.TypeMapper;

import java.io.UnsupportedEncodingException;
import java.sql.Blob;
import java.sql.SQLException;

@Mapper(uses = {TypeMapper.class}, componentModel = "spring")
public interface EtlUserTransfer {
    EtlUserTransfer INSTANCE = Mappers.getMapper(EtlUserTransfer.class);


    @Mappings({
            @Mapping(source = "id", target = "userId"),
            @Mapping(source = "username", target = "accountId"),
            @Mapping(source = "username", target = "loginId"),
            @Mapping(source = "name", target = "name"),
            @Mapping(source = "nickname", target = "nickName"),
            @Mapping(source = "phoneNumber", target = "mobile"),
            @Mapping(source = "email", target = "mail"),
            @Mapping(source = "status", target = "status"),
            @Mapping(target = "picture", ignore = true)
//            @Mapping(target = "picture", expression = "java(com.cyberscraft.uep.account.client.provider.etl.transfer.EtlUserTransfer.blobToString(entity.getPicture()))"),
    })
    ThirdPartyAccount entity2Vo(EtlUserEntity entity);

    static String blobToString(Blob picture) {
        try {
            return new String(picture.getBytes(1, (int) picture.length()), "GBK");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        } catch (SQLException throwables) {
            throwables.printStackTrace();
        }
        return null;
    }
}
