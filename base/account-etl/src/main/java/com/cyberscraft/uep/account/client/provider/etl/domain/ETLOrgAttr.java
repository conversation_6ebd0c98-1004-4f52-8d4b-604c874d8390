package com.cyberscraft.uep.account.client.provider.etl.domain;

import com.cyberscraft.uep.account.client.constant.ExternalAttr;
import com.cyberscraft.uep.common.domain.DataTypeEnum;

import static com.cyberscraft.uep.account.client.constant.AccountConstant.APPORG_PROFILE_ROOTNAME;
import static com.cyberscraft.uep.account.client.constant.AccountConstant.IDPORG_PROFILE_ROOTNAME;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/26 7:50 下午
 */
public enum ETLOrgAttr implements ExternalAttr {
    name("name", DataTypeEnum.STRING, "部门名称"),
    code("code", DataTypeEnum.STRING, "部门编号"),
    parent_code("parent_code", DataTypeEnum.STRING, "父部门编号"),
    ;

    private String attrName;

    private DataTypeEnum dataType;

    private String displayName;

    private String description;

    private Boolean multiValued;

    ETLOrgAttr(String attrName, DataTypeEnum dataType, String displayName) {
        this(attrName, dataType, displayName, displayName, false);
    }

    ETLOrgAttr(String attrName, DataTypeEnum dataType, String displayName, Boolean multiValued) {
        this(attrName, dataType, displayName, displayName, multiValued);
    }

    ETLOrgAttr(String attrName, DataTypeEnum dataType, String displayName, String description, Boolean multiValued) {
        this.attrName = attrName;
        this.dataType = dataType;
        this.displayName = displayName;
        this.description = description;
        this.multiValued = multiValued;
    }

    public String getAttrName() {
        return attrName;
    }


    public DataTypeEnum getDataType() {
        return dataType;
    }


    public String getDisplayName() {
        return displayName;
    }


    public String getDescription() {
        return description;
    }


    public Boolean getMultiValued() {
        return multiValued;
    }


    public String getAppNamePath() {
        return APPORG_PROFILE_ROOTNAME + "." + getAttrName();
    }

    @Override
    public String getIdpNamePath() {
        return IDPORG_PROFILE_ROOTNAME + "." + getAttrName();
    }
}
