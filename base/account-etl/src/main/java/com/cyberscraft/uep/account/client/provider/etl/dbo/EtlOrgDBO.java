package com.cyberscraft.uep.account.client.provider.etl.dbo;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cyberscraft.uep.account.client.provider.etl.entity.EtlOrgEntity;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * IAM-组织结构表 服务类
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2019-07-08
 */
public interface EtlOrgDBO extends IService<EtlOrgEntity> {
    /**
     * 查询数据源下的根组信息
     * @param datasourceId
     * @return
     */
    EtlOrgEntity getRootOrg(String datasourceId);

    /**
     * 查询单个组信息
     * @param datasourceId
     * @param orgId
     * @return
     */
    EtlOrgEntity getOrg(String datasourceId, String orgId);

    /**
     * 查询数据源下的所有组信息
     * @param datasourceId
     * @return
     */
    List<EtlOrgEntity> getAllOrgs(String datasourceId);

    /**
     * 查询一级子组信息
     */
    List<EtlOrgEntity> getSubOrgs(String datasourceId, String parentOrgId);

    List<EtlOrgEntity> getOrgsByParentRefId(String datasourceId, String orgId);
}
