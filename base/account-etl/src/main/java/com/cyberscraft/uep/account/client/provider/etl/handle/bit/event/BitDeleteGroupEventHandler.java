package com.cyberscraft.uep.account.client.provider.etl.handle.bit.event;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.cyberscraft.uep.account.client.constant.ThirdPartyAccountType;
import com.cyberscraft.uep.account.client.constant.ThirdPartyEventTagConstant;
import com.cyberscraft.uep.account.client.domain.Connector;
import com.cyberscraft.uep.account.client.domain.GroupEventBody;
import com.cyberscraft.uep.account.client.domain.ThirdPartyEvent;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountErrorType;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountException;
import com.cyberscraft.uep.account.client.provider.etl.dbo.EtlOrgDBO;
import com.cyberscraft.uep.account.client.provider.etl.dbo.EtlUserOrgDBO;
import com.cyberscraft.uep.account.client.provider.etl.domain.ETLConnectorConfig;
import com.cyberscraft.uep.account.client.provider.etl.domain.bit.BitEventType;
import com.cyberscraft.uep.account.client.provider.etl.domain.bit.BitGroupEvent;
import com.cyberscraft.uep.account.client.provider.etl.entity.EtlOrgEntity;
import com.cyberscraft.uep.account.client.provider.etl.entity.EtlUserOrgEntity;
import com.cyberscraft.uep.account.client.service.IThirdPartyEventExecutorService;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.common.util.MD5Util;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

@Component
@ConditionalOnProperty(name = "message.provider.bit.enabled", havingValue = "true")
/**
 * BIT添加更新用户事件处理器
 */
public class BitDeleteGroupEventHandler implements IBitEventHandler {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private EtlOrgDBO etlOrgDBO;

    @Autowired
    private EtlUserOrgDBO etlUserOrgDBO;

    @Resource
    private IThirdPartyEventExecutorService thirdPartyEventExecutorService;

    @Override
    public Set<BitEventType> supportedEventTypes() {
        return Sets.newHashSet(BitEventType.DELETE_GROUP);
    }

    @Override
    public void handleEvent(String event, Connector connector) {
        BitGroupEvent bitGroupEvent = JsonUtil.str2Obj(event, BitGroupEvent.class);
        ETLConnectorConfig etlConnectorConfig = JsonUtil.str2Obj(connector.getConfig(), ETLConnectorConfig.class);
        if (etlConnectorConfig == null) {
            logger.error("DELETE_GROUP event: failed to convert {} to etl connector config", connector.getConfig());
            return;
        }

        if (bitGroupEvent == null) {
            logger.error("DELETE_GROUP event: failed to convert {} to bitGroupEvent", event);
            return;
        }

        if (StringUtils.isEmpty(bitGroupEvent.getOrgPath())) {
            logger.error("DELETE_GROUP event: org path is empty");
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.MESSAGE_BODY_INVALID, "orgPath不能为空");
        }

        /**
         * iam connnector batchNo+1
         */
        connector.setSyncBatchNo(connector.getSyncBatchNo()+1);

        deleteGroup(bitGroupEvent.getOrgPath(), etlConnectorConfig, connector);

    }

    /**
     * 返回需要的
     * @param requestOrgFullPath
     * @param etlConnectorConfig
     * @return
     */
    private void deleteGroup(String requestOrgFullPath, ETLConnectorConfig etlConnectorConfig, Connector connector) {
        StringBuilder sb = new StringBuilder();

        //增加北京理工大学的前缀
        sb.append("/北京理工大学");
        if (requestOrgFullPath.charAt(0) == '/') {
            sb.append(requestOrgFullPath);
        } else {
            sb.append("/").append(requestOrgFullPath);
        }

        String orgId = MD5Util.md5(sb.toString());

        EtlOrgEntity etlOrgEntity = etlOrgDBO.getOrg(etlConnectorConfig.getDatasourceId(), orgId);
        if (etlOrgEntity == null) {
            logger.error("DELETE_GROUP event: elt org {} {} datasourceId {} not found", orgId, sb.toString(), etlConnectorConfig.getDatasourceId());
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.GROUP_NOT_EXIST);
        }

        List<EtlOrgEntity> children = etlOrgDBO.getOrgsByParentRefId(etlConnectorConfig.getDatasourceId(), orgId);
        if (children != null && children.size() > 0){
            logger.error("DELETE_GROUP event: elt org {} {} datasourceId {} has {} children, not allowed to delete",
                    orgId, sb.toString(), etlConnectorConfig.getDatasourceId(), children.size());
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.GROUP_NOT_ALLOW_TO_DELETE);
        }

        List<EtlUserOrgEntity> userOrgEntities = etlUserOrgDBO.getUserOrgByOrgId(etlConnectorConfig.getDatasourceId(), orgId);
        if (userOrgEntities != null && userOrgEntities.size() > 0){
            logger.error("DELETE_GROUP event: elt org {} {} datasourceId {} has {} users, not allowed to delete",
                    orgId, sb.toString(), etlConnectorConfig.getDatasourceId(), userOrgEntities.size());
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.GROUP_NOT_ALLOW_TO_DELETE);
        }
        boolean succeed = etlOrgDBO.removeById(etlOrgEntity.getId());
        if (succeed){
            triggerThirdPartyGroupEvent(orgId, connector);
        }
    }

    private void triggerThirdPartyGroupEvent(String orgId, Connector connector) {
        ThirdPartyEvent<GroupEventBody> event = new ThirdPartyEvent<>();
        event.setAccountType(ThirdPartyAccountType.ETL.getCode());
        event.setEventTag(ThirdPartyEventTagConstant.GROUP_REMOVE);
        event.setEventTime(LocalDateTime.now());
        event.setTenantId(connector.getTenantId());

        GroupEventBody body = new GroupEventBody();
        body.setConnector(connector);
        body.setCodes(Lists.newArrayList(orgId));

        event.setData(body);
        thirdPartyEventExecutorService.onEvent(event);
    }
}