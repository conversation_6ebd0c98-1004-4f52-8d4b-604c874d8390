package com.cyberscraft.uep.account.client.provider.etl.handle.bit.event;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.cyberscraft.uep.account.client.constant.ThirdPartyAccountType;
import com.cyberscraft.uep.account.client.constant.ThirdPartyEventTagConstant;
import com.cyberscraft.uep.account.client.domain.Connector;
import com.cyberscraft.uep.account.client.domain.ThirdPartyEvent;
import com.cyberscraft.uep.account.client.domain.UserEventBody;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountErrorType;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountException;
import com.cyberscraft.uep.account.client.provider.etl.dbo.EtlUserDBO;
import com.cyberscraft.uep.account.client.provider.etl.dbo.EtlUserOrgDBO;
import com.cyberscraft.uep.account.client.provider.etl.domain.ETLConnectorConfig;
import com.cyberscraft.uep.account.client.provider.etl.domain.bit.BitEventType;
import com.cyberscraft.uep.account.client.provider.etl.domain.bit.BitUserEvent;
import com.cyberscraft.uep.account.client.service.IThirdPartyEventExecutorService;
import com.cyberscraft.uep.common.util.JsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Set;

@Component
@ConditionalOnProperty(name = "message.provider.bit.enabled", havingValue = "true")
/**
 * BIT删除用户事件处理器
 */
public class BitDeleteUserEventHandler implements IBitEventHandler{
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private EtlUserOrgDBO etlUserOrgDBO;

    @Autowired
    private EtlUserDBO etlUserDBO;

    @Resource
    private IThirdPartyEventExecutorService thirdPartyEventExecutorService;
    @Override
    public Set<BitEventType> supportedEventTypes() {
        return Sets.newHashSet(BitEventType.DELETE_USER);
    }

    @Override
    public void handleEvent(String event, Connector connector) {
        BitUserEvent bitUserEvent = JsonUtil.str2Obj(event, BitUserEvent.class);
        ETLConnectorConfig etlConnectorConfig = JsonUtil.str2Obj(connector.getConfig(), ETLConnectorConfig.class);
        if (etlConnectorConfig == null) {
            logger.error("failed to convert {} to etl connector config", connector.getConfig());
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.SERVER_RESPONSE_INVALID);
        }

        if (bitUserEvent == null) {
            logger.error("failed to convert {} to bitUserEvent", event);
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.MESSAGE_BODY_INVALID);
        }

        if (StringUtils.isEmpty(bitUserEvent.getIdentifier())) {
            logger.error("user identifier is empty");
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.MESSAGE_BODY_INVALID, "identifier不能为空");
        }

        deleteUser(etlConnectorConfig, bitUserEvent, connector);
    }

    private void deleteUser(ETLConnectorConfig etlConnectorConfig, BitUserEvent bitUserEvent, Connector connector) {
        boolean succeed = etlUserDBO.removeByUsername(etlConnectorConfig.getDatasourceId(), bitUserEvent.getIdentifier());
        etlUserOrgDBO.removeByUser(etlConnectorConfig.getDatasourceId(), bitUserEvent.getIdentifier());

        if (succeed){
            triggerThirdPartyUserEvent(connector, bitUserEvent);
        }else{
            logger.warn("user {} not found in datasource {}, connector {} {}",
                    bitUserEvent.getIdentifier(),
                    etlConnectorConfig.getDatasourceId(), connector.getId(), connector.getName());
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.USER_NOT_EXIST);
        }
    }

    private void triggerThirdPartyUserEvent(Connector connector, BitUserEvent bitUserEvent) {
        ThirdPartyEvent<UserEventBody> event = new ThirdPartyEvent<>();
        event.setAccountType(ThirdPartyAccountType.ETL.getCode());
        event.setEventTag(ThirdPartyEventTagConstant.USER_REMOVE);
        event.setEventTime(LocalDateTime.now());
        event.setTenantId(connector.getTenantId());

        UserEventBody body = new UserEventBody();
        body.setConnector(connector);
        body.setUserIds(Lists.newArrayList(bitUserEvent.getIdentifier()));

        event.setData(body);
        thirdPartyEventExecutorService.onEvent(event);
    }
}
