package com.cyberscraft.uep.config.client;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.*;

/***
 *
 * @date 2021-06-06
 * <AUTHOR>
 ***/
public class Bean2PropertyUtils {

    /***
     * 要过虑的字段列表，默认为字段名称的小写
     */
    private final static Set<String> IGNORE_PROPERTIES = new HashSet<>();

    /***
     *
     */
    private final static Logger LOG = LoggerFactory.getLogger(Bean2PropertyUtils.class);


    static {
        IGNORE_PROPERTIES.add("serialversionuid");
        IGNORE_PROPERTIES.add("class");
    }

    /***
     *
     * @param obj
     * @param <T>
     * @return
     * @throws Exception
     */
    public static <T extends PropertyBean> Map<String, String> toPropertyMap(T obj) throws Exception {

        PropertyDescriptor[] srcPds = BeanUtils.getPropertyDescriptors(obj.getClass());
        if (srcPds == null || srcPds.length == 0) {
            return null;
        }

        Map<String, String> ret = new HashMap<>(srcPds.length);

        Class<?> classT = obj.getClass();
        Map<String,Field> fieldsMap= getDeclaredFieldsMap(obj);
        for (PropertyDescriptor p : srcPds) {
            String name = p.getName();
            if (IGNORE_PROPERTIES.contains(name.toLowerCase())) {
                continue;
            }
            try {
                Field field = null;
                try {
                    field =fieldsMap.get(name);// classT.getDeclaredField(name);
                } catch (Exception e) {
                    //如果出错，则代表不是对应的字段,因为该PropertyDescriptor即包括字段也包括方法，跳过就行
                    if (LOG.isTraceEnabled()) {
                        LOG.trace("{}对应的属性，不是字段，进行跳过", name);
                    }
                }
                if (field != null) {
                    String keyName = name;
                    boolean flag = false;
                    String valueName = name;
                    PropertyName propertyAnnotation = field.getAnnotation(PropertyName.class);
                    if (propertyAnnotation != null && StringUtils.isNotBlank(propertyAnnotation.value())) {
                        keyName = propertyAnnotation.value();
                        flag = true;
                    }
                    if (!flag) {
                        Value valueAnnotation = field.getAnnotation(Value.class);
                        if (valueAnnotation != null && StringUtils.isNotBlank(valueAnnotation.value())) {
                            valueName = valueAnnotation.value();
                            keyName = valueName.replace("${", "").replace("}", "").trim();
                        }
                    }
                    String propertyValue = "";

                    Method readMethod = p.getReadMethod();
                    if (readMethod != null) {
                        try {
                            if (!Modifier.isPublic(readMethod.getDeclaringClass().getModifiers())) {
                                readMethod.setAccessible(true);
                            }
                            Object value = readMethod.invoke(obj);
                            if (value != null) {
                                propertyValue = String.valueOf(value);
                            }
                        } catch (Exception ex) {
                            LOG.warn("获取字段{}对应的属性出错,未设置对应的getter方法", name, ex);
                        }
                    }
                    //TODO 做一个特殊处理，因为如果对应的是Value属性，但是配置中心没有指定的情况下，值会为%{pName}
                    if (StringUtils.isNotBlank(propertyValue) && propertyValue.equalsIgnoreCase(valueName)) {
                        propertyValue = "";
                    }
                    ret.put(keyName, propertyValue);
                }
                //Annotation annotation=p.get
            } catch (Exception e) {
                LOG.warn("获取字段{}对应的属性出错", name, e);
            }
        }
        return ret;
    }

    /***
     * 首先
     * @param obj
     * @param <T>
     * @return
     * @throws Exception
     */
    public static <T extends PropertyBean> List<NameValuePair> toPropertyNameValuePairs(T obj) throws Exception {

        PropertyDescriptor[] srcPds = BeanUtils.getPropertyDescriptors(obj.getClass());
        if (srcPds == null || srcPds.length == 0) {
            return null;
        }

        List<NameValuePair> ret = new ArrayList<>(srcPds.length);

        //首先判断是否为采用ConfigurationProperties属性的,如果是则为前缀加上Field名称小写的格式
        //如果不是则为对应的Value annotion对应的属性名称
        String classPrefix = "";
        boolean isPrefix = false;
        ConfigurationProperties classAnnotion = obj.getClass().getDeclaredAnnotation(ConfigurationProperties.class);
        if (classAnnotion != null) {
            isPrefix = true;
            classPrefix = classAnnotion.prefix();
        }

        //Class<?> classT = obj.getClass();
        Map<String,Field> fieldsMap= getDeclaredFieldsMap(obj);
        for (PropertyDescriptor p : srcPds) {
            String name = p.getName();
            if (IGNORE_PROPERTIES.contains(name.toLowerCase())) {
                continue;
            }
            try {
                Field field = null;
                try {
                    field = fieldsMap.get(name);
                } catch (Exception e) {
                    //如果出错，则代表不是对应的字段,因为该PropertyDescriptor即包括字段也包括方法，跳过就行
                    if (LOG.isTraceEnabled()) {
                        LOG.trace("{}对应的属性，不是字段，进行跳过", name);
                    }
                }

                if (field != null) {
                    String keyName = name;
                    String valueName = name;
                    if (isPrefix) {
                        keyName = classPrefix + "." + field.getName().toLowerCase();
                    } else {
                        Value valueAnnotation = field.getAnnotation(Value.class);
                        if (valueAnnotation != null && StringUtils.isNotBlank(valueAnnotation.value())) {
                            valueName = valueAnnotation.value();
                            keyName = valueName.replace("${", "").replace("}", "").trim();
                        }
                    }
                    String propertyValue = "";
                    Method readMethod = p.getReadMethod();
                    if (readMethod != null) {
                        try {
                            if (!Modifier.isPublic(readMethod.getDeclaringClass().getModifiers())) {
                                readMethod.setAccessible(true);
                            }
                            Object value = readMethod.invoke(obj);
                            if (value != null) {
                                propertyValue = String.valueOf(value);
                            }
                        } catch (Exception ex) {
                            LOG.warn("获取字段{}对应的属性出错,未设置对应的getter方法", name, ex);
                        }
                    }
                    //TODO 做一个特殊处理，因为如果对应的是Value属性，但是配置中心没有指定的情况下，值会为%{pName}
                    if (StringUtils.isNotBlank(propertyValue) && propertyValue.equalsIgnoreCase(valueName)) {
                        propertyValue = "";
                    }
                    ret.add(new NameValuePair(keyName, propertyValue));
                }

                //Annotation annotation=p.get
            } catch (Exception e) {
                LOG.warn("获取字段{}对应的属性出错", name, e);
            }
        }
        return ret;
    }

    /***
     * 得到对像的所有字段，包括父对像的字段
     * @param obj
     * @param <T>
     * @return
     */
    private static <T> Map<String,Field> getDeclaredFieldsMap(T obj) {
        Map<String,Field> ret = new HashMap<>();
        Class<?> clazz = obj.getClass();
        while (clazz != null) {
            Field[] fields = clazz.getDeclaredFields();
            if (fields != null && fields.length > 0) {
                for (Field f: fields ) {
                    ret.put(f.getName(),f);
                }
            }
            clazz = clazz.getSuperclass();
        }
        return ret;
    }
}
