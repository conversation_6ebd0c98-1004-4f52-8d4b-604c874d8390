package com.cyberscraft.uep.config.client;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/***
 * 从原来的com.cyberscraft.emm.config.client.model.PN移值而来
 * @date 2021-06-06
 * <AUTHOR>
 ***/
@Component
@ConfigurationProperties(prefix = "sys.conf.pn")
public class PNConfiguration extends AbstractPropertyBean{
    //private static final String PN_COMPONENT_TYPE = "sys.conf.push.type";
    private static final String PNSERVER_STATUS = "sys.conf.PNServer.status";
    private static final String PNSERVER_IP = "sys.conf.PNServer.ip";
    private static final String PNSERVER_SERVERIP = "sys.conf.PNServer.serverIp";
    private static final String PNSERVER_UDPSTARTPORT = "sys.conf.PNServer.udpStartPort";
    private static final String PNSERVER_UDPPORTCOUNT = "sys.conf.PNServer.udpPortCount";
    private static final String PNSERVER_TCPPORT = "sys.conf.PNServer.tcpPort";
    private static final String PNSERVER_SERVERTCPPORT = "sys.conf.PNServer.serverTcpPort";
    private static final String PNSERVER_NAME = "sys.conf.PNServer.name";
    private static final String PNSERVER_PWD = "sys.conf.PNServer.pwd";
    private static final String PNSERVER_APPID = "sys.conf.PNServer.appid";
    private static final String PNSERVER_NCM = "sys.conf.PNServer.ncm";
    private static final String PNSERVER_APNS = "sys.conf.PNServer.apns";
    private static final String PNSERVER_FCM = "sys.conf.PNServer.fcm";
    private static final String PNSERVER_ANDPUSHTYPE = "sys.conf.PNServer.andriodPushType";
    private static final String PN_COM_CY_MDM = "com.cyb.mdm";
    private static final String PN_COM_SKY_MDM = "com.sky.mdm";
    private static final String PN_COM_DIGITALSEE_EMM = "com.cyberscraft.emm";
    private static final String PN_COM_CY_MCM = "com.cyb.mcm";
    private static final String PN_CUSTOM_BUNDLEID = "sys.conf.custom.bundleid";
    private static final String PN_CUSTOM_BUNDLEID_FEATUREID = "sys.conf.custom.bundleid.featureid";
    private static final String PN_CUSTOM_MCM_BUNDLEID = "sys.conf.custom.mcm.bundleid";
    private static final String PN_CUSTOM_ANDROID_APPID = "sys.conf.custom.android.appid";
    private static final String PN_CUSTOM_BUNDLEIDS = "sys.conf.custom.bundleids";
    private static final String PN_SELF_BUNDLEIDS = "sys.conf.self.bundleids";
    private static final String PN_IOS_CLIENT_TYPE = "sys.conf.ios.clent.type";
    private static final String PN_ANDROID_CLIENT_TYPE = "sys.conf.android.clent.type";
    private static final String PNSERVER_WINDOWS = "sys.conf.PNServer.windows";
    private static final String PNSERVER_MACOS = "sys.conf.PNServer.macOS";
    private static final String PNSERVER_USE_SSL = "sys.conf.PNServer.useSSL";
    
//    /***
//     *
//     */
//    //@Value("${"+PN_COMPONENT_TYPE+"}")
//    @PropertyName(PN_COMPONENT_TYPE)
//    private String pushComponentType;

    /***
     *
     */
    //@Value("${"+PNSERVER_STATUS+"}")
    @PropertyName(PNSERVER_STATUS)
    private String status;

    /***
     *
     */
    //@Value("${"+PNSERVER_IP+"}")
    @PropertyName(PNSERVER_IP)
    private String ip;

    /***
     *
     */
    //@Value("${"+PNSERVER_SERVERIP+"}")
    @PropertyName(PNSERVER_SERVERIP)
    private String serverIp;

    /***
     *
     */
    //@Value("${"+PNSERVER_UDPSTARTPORT+"}")
    @PropertyName(PNSERVER_UDPSTARTPORT)
    private String udpStartPort;

    /***
     *
     */
    //@Value("${"+PNSERVER_UDPPORTCOUNT+"}")
    @PropertyName(PNSERVER_UDPPORTCOUNT)
    private String udpPortCount;

    /***
     *
     */
    //@Value("${"+PNSERVER_TCPPORT+"}")
    @PropertyName(PNSERVER_TCPPORT)
    private String tcpPort;

    /***
     *
     */
    //@Value("${"+PNSERVER_SERVERTCPPORT+"}")
    @PropertyName(PNSERVER_SERVERTCPPORT)
    private String serverTcpPort;

    /***
     *
     */
    //@Value("${"+PNSERVER_NAME+"}")
    @PropertyName(PNSERVER_NAME)
    private String name;

    /***
     *
     */
    //@Value("${"+PNSERVER_PWD+"}")
    @PropertyName(PNSERVER_PWD)
    private String pwd;

    /***
     * token的过期时间45分钟
     */
    private Integer tokenExpire=45;

    /***
     *
     */
    //@Value("${"+PNSERVER_APPID+"}")
    @PropertyName(PNSERVER_APPID)
    private String appId;

    /***
     *
     */
    @PropertyName(PNSERVER_NCM)
    private String ncm;

    /***
     *
     */
    @PropertyName(PNSERVER_APNS)
    private String apns;

    /***
     *
     */
    @PropertyName(PN_COM_CY_MDM)
    private String comDgsMdm;

    /***
     *
     */
    @PropertyName(PN_COM_SKY_MDM)
    private String comSkyMdm;

    /***
     *
     */
    @PropertyName(PN_COM_DIGITALSEE_EMM)
    private String comDigitalSeeEmm;

    /***
     *
     */
    @PropertyName(PN_COM_CY_MCM)
    private String comDgsMcm;

    /***
     *
     */
    @PropertyName(PN_CUSTOM_BUNDLEID)
    private String customBundleId;

    /***
     *
     */
    @PropertyName(PN_CUSTOM_BUNDLEIDS)
    private String customBundleIds;


    /***
     *
     */
    @PropertyName(PN_CUSTOM_BUNDLEID_FEATUREID)
    private String customBundleidFeatureid;

    /***
     *
     */
    @PropertyName(PN_CUSTOM_MCM_BUNDLEID)
    private String customMcmBundleId;

    /***
     *
     */
    @PropertyName(PN_CUSTOM_ANDROID_APPID)
    private String customAndroidAppId;


    /***
     *
     */
    @PropertyName(PN_SELF_BUNDLEIDS)
    private String selfBundleIds;


    /***
     *
     */
    @PropertyName(PN_IOS_CLIENT_TYPE)
    private String iosClientType;

    /***
     *
     */
    @PropertyName(PN_ANDROID_CLIENT_TYPE)
    private String androidClientType;

    /***
     *
     */
    @PropertyName(PNSERVER_WINDOWS)
    private String windows;

    /***
     *
     */
    @PropertyName(PNSERVER_MACOS)
    private String macOS;

    /***
     *
     */
    @PropertyName(PNSERVER_FCM)
    private String fcm;

    /***
     *
     */
    @PropertyName(PNSERVER_ANDPUSHTYPE)
    private String andriodPushType;
    
    /***
     *
     */
    //@Value("${"+PNSERVER_USE_SSL+"}")
    @PropertyName(PNSERVER_USE_SSL)
    private String useSSL;

    /***
     * PN对应的登录地址，用于获取token
     */
    private String loginApiUrl="/cps/api/login";

    /***
     * 对应的推送地址
     */
    private String notificationApiUrl="/cps/api/notifications";

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getServerIp() {
        return serverIp;
    }

    public void setServerIp(String serverIp) {
        this.serverIp = serverIp;
    }

    public String getUdpStartPort() {
        return udpStartPort;
    }

    public void setUdpStartPort(String udpStartPort) {
        this.udpStartPort = udpStartPort;
    }

    public String getUdpPortCount() {
        return udpPortCount;
    }

    public void setUdpPortCount(String udpPortCount) {
        this.udpPortCount = udpPortCount;
    }

    public String getTcpPort() {
        return tcpPort;
    }

    public void setTcpPort(String tcpPort) {
        this.tcpPort = tcpPort;
    }

    public String getServerTcpPort() {
        return serverTcpPort;
    }

    public void setServerTcpPort(String serverTcpPort) {
        this.serverTcpPort = serverTcpPort;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPwd() {
        return pwd;
    }

    public void setPwd(String pwd) {
        this.pwd = pwd;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getNcm() {
        return ncm;
    }

    public void setNcm(String ncm) {
        this.ncm = ncm;
    }

    public String getApns() {
        return apns;
    }

    public void setApns(String apns) {
        this.apns = apns;
    }

    public String getComDgsMdm() {
        return comDgsMdm;
    }

    public void setComDgsMdm(String comDgsMdm) {
        this.comDgsMdm = comDgsMdm;
    }

    public String getComSkyMdm() {
        return comSkyMdm;
    }

    public void setComSkyMdm(String comSkyMdm) {
        this.comSkyMdm = comSkyMdm;
    }

    public String getComDigitalSeeEmm() {
        return comDigitalSeeEmm;
    }

    public void setComDigitalSeeEmm(String comDigitalSeeEmm) {
        this.comDigitalSeeEmm = comDigitalSeeEmm;
    }

    public String getComDgsMcm() {
        return comDgsMcm;
    }

    public void setComDgsMcm(String comDgsMcm) {
        this.comDgsMcm = comDgsMcm;
    }

    public String getCustomBundleId() {
        return customBundleId;
    }

    public void setCustomBundleId(String customBundleId) {
        this.customBundleId = customBundleId;
    }

    public String getCustomBundleIds() {
        return customBundleIds;
    }

    public void setCustomBundleIds(String customBundleIds) {
        this.customBundleIds = customBundleIds;
    }

    public String getCustomBundleidFeatureId() {
        return customBundleidFeatureid;
    }

    public void setCustomBundleidFeatureId(String customBundleidFeatureid) {
        this.customBundleidFeatureid = customBundleidFeatureid;
    }

    public String getCustomMcmBundleId() {
        return customMcmBundleId;
    }

    public void setCustomMcmBundleId(String customMcmBundleId) {
        this.customMcmBundleId = customMcmBundleId;
    }

    public String getCustomAndroidAppId() {
        return customAndroidAppId;
    }

    public void setCustomAndroidAppId(String customAndroidAppId) {
        this.customAndroidAppId = customAndroidAppId;
    }

    public String getSelfBundleIds() {
        return selfBundleIds;
    }

    public void setSelfBundleIds(String selfBundleIds) {
        this.selfBundleIds = selfBundleIds;
    }

    public String getIosClientType() {
        return iosClientType;
    }

    public void setIosClientType(String iosClientType) {
        this.iosClientType = iosClientType;
    }

    public String getAndroidClientType() {
        return androidClientType;
    }

    public void setAndroidClientType(String androidClientType) {
        this.androidClientType = androidClientType;
    }

    public String getWindows() {
        return windows;
    }

    public void setWindows(String windows) {
        this.windows = windows;
    }

    public String getMacOS() {
        return macOS;
    }

    public void setMacOS(String macOS) {
        this.macOS = macOS;
    }

    public String getFcm() {
        return fcm;
    }

    public void setFcm(String fcm) {
        this.fcm = fcm;
    }

    public String getAndriodPushType() {
        return andriodPushType;
    }

    public void setAndriodPushType(String andriodPushType) {
        this.andriodPushType = andriodPushType;
    }

    public String getUseSSL() {
        return useSSL;
    }

    public void setUseSSL(String useSSL) {
        this.useSSL = useSSL;
    }

    public String getCustomBundleidFeatureid() {
        return customBundleidFeatureid;
    }

    public void setCustomBundleidFeatureid(String customBundleidFeatureid) {
        this.customBundleidFeatureid = customBundleidFeatureid;
    }

    public String getLoginApiUrl() {
        return loginApiUrl;
    }

    public void setLoginApiUrl(String loginApiUrl) {
        this.loginApiUrl = loginApiUrl;
    }

    public String getNotificationApiUrl() {
        return notificationApiUrl;
    }

    public void setNotificationApiUrl(String notificationApiUrl) {
        this.notificationApiUrl = notificationApiUrl;
    }

    public Integer getTokenExpire() {
        return tokenExpire;
    }

    public void setTokenExpire(Integer tokenExpire) {
        this.tokenExpire = tokenExpire;
    }
}
