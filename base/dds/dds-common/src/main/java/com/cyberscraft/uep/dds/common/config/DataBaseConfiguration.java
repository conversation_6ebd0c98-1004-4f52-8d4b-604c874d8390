package com.cyberscraft.uep.dds.common.config;

import com.cyberscraft.uep.common.config.DataBaseConfig;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.dds.common.exception.DbConnectException;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import net.ucanaccess.jdbc.UcanaccessSQLException;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.SQLException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Author：xhquan
 * @Project：uep-services
 * @since：2023/4/12 18:21
 */
@Component
public class DataBaseConfiguration {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    // api数据源配置x
    private Map<String, DataSource> dataSourceHashMap = new ConcurrentHashMap<>();

    public Connection getConnection(String accountId, DataBaseConfig dataBaseConfig) {
        DataSource dataSource = dataSourceHashMap.get(accountId);
        if (dataSource == null) {
            dataSource = createDataSource(dataBaseConfig);
            dataSourceHashMap.put(accountId, dataSource);
        }
        try {
            Connection connection = dataSource.getConnection();
//            logger.info("get database connection success  : {} ", JsonUtil.obj2Str(dataBaseConfig));
            return connection;
        } catch (SQLException sqlException) {
            logger.error("data connect get datasource connection  log===>get connection error.", sqlException);
            throw new DbConnectException("DDS get connection fail");
        }
    }

    public void evictCache(String accountId) {
        DataSource dataSource = dataSourceHashMap.remove(accountId);
        if (dataSource != null) {
            ((HikariDataSource) dataSource).close();
        }
    }

    private String getJdbcDriver(DataBaseConfig dataBaseConfig) {
        String driverClassName = "";
        switch (dataBaseConfig.getType()) {
            case MYSQL:
                driverClassName = "com.mysql.cj.jdbc.Driver";
                break;
            case ORACLE:
                driverClassName = "oracle.jdbc.driver.OracleDriver";
                break;
            case SQLSERVER:
                driverClassName = "com.microsoft.sqlserver.jdbc.SQLServerDriver";
                break;
            case POSTGRESQL:
                driverClassName = "org.postgresql.Driver";
                break;
            case DB2:
                driverClassName = "com.ibm.db2.jcc.DB2Driver";
                break;
            case ACCESS:
                driverClassName = "net.ucanaccess.jdbc.UcanaccessDriver";
                break;
            case DM8:
                driverClassName = "dm.jdbc.driver.DmDriver";
                break;
        }
        return driverClassName;
    }

    // 创建数据源
    private DataSource createDataSource(DataBaseConfig dataBaseConfig) {
        String driverClassName = getJdbcDriver(dataBaseConfig);
        HikariConfig hikariConfig = new HikariConfig();
        hikariConfig.setAutoCommit(true);
        hikariConfig.setJdbcUrl(dataBaseConfig.getDataBaseUrl());
        hikariConfig.setDriverClassName(driverClassName);
        hikariConfig.setUsername(dataBaseConfig.getUsername());
        hikariConfig.setPassword(dataBaseConfig.getPassword());
        hikariConfig.setMinimumIdle(5);
        hikariConfig.setMaximumPoolSize(50);
        HikariDataSource hikariDataSource = new HikariDataSource(hikariConfig);
        return hikariDataSource;
    }

    public boolean testConnect(DataBaseConfig dataBaseConfig) {
        Connection connection = null;
        try {
            logger.info("test database connection: {} ", JsonUtil.obj2Str(dataBaseConfig));
            Class.forName(getJdbcDriver(dataBaseConfig));
            connection = DriverManager.getConnection(dataBaseConfig.getDataBaseUrl(), dataBaseConfig.getUsername(), dataBaseConfig.getPassword());
            return connection != null;
        } catch (UcanaccessSQLException exception) {
            throw new RuntimeException("文件不存在或者密码错误");
        } catch (Exception exception) {
            logger.error("test database connection error.", exception);
            String message = exception.getMessage();
            if (StringUtils.isNotBlank(message)) {
                message = message.replace("Failed to initialize pool: ", "");
            }
            throw new DbConnectException(message);
        } finally {
            try {
                if (connection != null && !connection.isClosed()) {
                    connection.close();
                }
            } catch (SQLException e) {
                logger.warn("close connection exception:{}", e.getMessage());
            }
        }
    }
}
