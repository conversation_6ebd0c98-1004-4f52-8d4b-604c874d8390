package com.cyberscraft.uep.starter.dds;

import com.cyberscraft.uep.starter.dds.bean.DDSHikariConfig;
import com.cyberscraft.uep.starter.dds.bean.GeneralAttributes;
import com.cyberscraft.uep.starter.dds.exceptions.DDSException;
import com.cyberscraft.uep.starter.dds.exceptions.DDSExceptionCodeEnum;
import com.zaxxer.hikari.HikariDataSource;
import com.zaxxer.hikari.pool.HikariPool;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jdbc.datasource.AbstractDataSource;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.sql.SQLFeatureNotSupportedException;
import java.sql.SQLSyntaxErrorException;
import java.util.HashMap;
import java.util.Map;

/**
 * @Auther: liuyang
 * @Date 2021-05-20 14:51
 * @Description:动态数据源管理
 */
@Component
public class DDSDataSourceManager extends AbstractDataSource {
    private Logger logger = LoggerFactory.getLogger(this.getClass());

    public static final String JDBC_URL_FORMAT = "%s/%s?%s";

    /**
     * 缓存数据源信息，key:租户ID，value:DataSource对象
     */
    private Map<String, DataSource> dataSourceMap = new HashMap<>();
    /**
     * DDS通用配置
     */
    private GeneralAttributes generalAttributes = new GeneralAttributes();
    /**
     * 数据库连接配置，key: db编号，value：数据库连接的配置信息（url,password,username等）
     */
    private Map<String, DDSHikariConfig> ddsHikariConfigHashMap = new HashMap<>();

    String getTenantCode(){
        if(generalAttributes.isShardingDb()){
            Thread thread = Thread.currentThread();
            String currentTenantCode = TenantHolder.getTenantCode();
            logger.debug("dds log===>request tenant code:{}",currentTenantCode);
            if(StringUtils.isBlank(currentTenantCode)) {
                if("main".equals(thread.getName()) ||
                        thread.getName().contains("RMI TCP Connection") ||
                        !generalAttributes.isMultiTenant() ||
                        thread.getName().contains("startStop")){
                    currentTenantCode = generalAttributes.getDefaultSchema()+generalAttributes.getHeaderSeparator()+generalAttributes.getDefaultDataBase();
                }else{
                    logger.error("dds log===>the value of tenant code name:["+generalAttributes.getTenantHeaderName()+"] is null");
                    throw new DDSException(DDSExceptionCodeEnum.UEP_TENANT_DATABASE_COULD_NOT_EMPTY);
                }

            }
            if (!currentTenantCode.contains(generalAttributes.getHeaderSeparator())) {
                //throw new RuntimeException("tenant code :"+currentTenantCode+" is incorrect,tenant code  must contain  '" +generalAttributes.getHeaderSeparator() + "'" );
                logger.debug("dds log===>tenant code do not contain separator 【{}】,use default dbhost 【{}】",generalAttributes.getHeaderSeparator(),generalAttributes.getDefaultDataBase());
                currentTenantCode = currentTenantCode + generalAttributes.getHeaderSeparator() + generalAttributes.getDefaultDataBase();
            }
            return currentTenantCode;
        }


        String currentTenantCode = generalAttributes.getDefaultSchema() + generalAttributes.getHeaderSeparator() + generalAttributes.getDefaultDataBase();
        return currentTenantCode;
    }


    @Override
    public Connection getConnection() throws SQLException {
        String currentTenantCode = getTenantCode();

        StringBuffer sb = new StringBuffer();
        sb.append("\\").append(generalAttributes.getHeaderSeparator());
        String currentTenantCodeArr[] = currentTenantCode.split(sb.toString());
        String currentDbNo = currentTenantCodeArr[1];
        String schema = currentTenantCodeArr[0];
        logger.debug("dds log===>real tenant code:{}, dbNo:{},schema:{}",currentTenantCode,currentDbNo,schema);

        //在配置中没有找到对应的数据源
        //String activeDbs = generalAttributes.getActiveDataBase();
        //if(activeDbs == null || "".equals(activeDbs)){
        //    throw new RuntimeException("there is no active datasource int the configuration");
        //}
        //
        //if(!ArrayUtils.contains(activeDbs.split(generalAttributes.getActiveDataBaseSeparator()),currentDbNo)) {
        //    throw new RuntimeException("there is no datasource int the configuration  with tenant id: " + currentTenantCode);
        //}

        DataSource ds = dataSourceMap.get(currentTenantCode);
        if(ds == null) {
            DDSHikariConfig ddsHikariConfig = ddsHikariConfigHashMap.get(currentDbNo);
            if(ddsHikariConfig == null){
                logger.error("dds log===>there is no datasource int the configuration  with tenant code: " + currentTenantCode);
                throw new DDSException(DDSExceptionCodeEnum.UEP_DATABASE_INSTANCE_NOT_FOUND);
            }
            synchronized(this) {
                ds = dataSourceMap.get(currentTenantCode);
                if(ds == null) {
                    try {
                        DDSHikariConfig ddsHikariConfigTemp = ddsHikariConfig.clone();
                        String realJdbcUrl = String.format(JDBC_URL_FORMAT, ddsHikariConfig.getJdbcUrl().trim(), schema.trim(), ddsHikariConfig.getUrlParams().trim());
                        ddsHikariConfigTemp.setJdbcUrl(realJdbcUrl);

                        ds =  new HikariDataSource(ddsHikariConfigTemp);
                        dataSourceMap.put(currentTenantCode, ds);
                        logger.debug("dds log===>database change success,realJdbcUrl:{}",realJdbcUrl);
                    } catch (CloneNotSupportedException e) {
                        e.printStackTrace();
                    } catch (Exception e){
                        if(e instanceof HikariPool.PoolInitializationException){
                            if(null != e.getCause() && e.getCause() instanceof SQLSyntaxErrorException) {
                                logger.error("dds log===>DDS connection pool initialize error: {}", e.getCause().getMessage());
                                throw new DDSException(DDSExceptionCodeEnum.UEP_TENANT_DATABASE_NOT_EXIST, e.getCause().getMessage());
                            }
                            logger.error("dds log===>DDS connection pool initialize error: {}", e.getMessage());
                        }
                        logger.error("dds log===>DDS occur error: {}", e.getMessage());
                        throw new DDSException(DDSExceptionCodeEnum.UEP_TENANT_DATABASE_NOT_EXIST);
                    }
                }
            }
        }
        try {
            return ds.getConnection();
        } catch (SQLException sqlException) {
            logger.error("dds log===>get connection error.", sqlException);
            throw new DDSException(DDSExceptionCodeEnum.DDS_GET_CONNECTION_FAIL);
        }
    }

    @Override
    public Connection getConnection(String username, String password) throws SQLException {
        throw new SQLFeatureNotSupportedException();
    }

    public void setGeneralAttributes(GeneralAttributes generalAttributes) {
        this.generalAttributes = generalAttributes;
    }

    public void setDdsHikariConfigHashMap(Map<String, DDSHikariConfig> ddsHikariConfigHashMap) {
        this.ddsHikariConfigHashMap = ddsHikariConfigHashMap;
    }
}