package com.cyberscraft.uep.starter.dds.properties;

import com.cyberscraft.uep.starter.dds.DDSDataSourceManager;
import com.cyberscraft.uep.starter.dds.interceptor.DDSInterceptor;
import com.cyberscraft.uep.starter.dds.interceptor.InterceptorRegister;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

/**
 * @Auther: liuyang
 * @Date 2021-05-20 14:32
 * @Description:动态切换数据源自动配置类
 */
@Configuration
@EnableConfigurationProperties(DDSConfigProperties.class)
public class DDSAutoConfiguration {
    @Autowired
    private DDSConfigProperties ddsConfigProperties;

    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnClass(DDSDataSourceManager.class)
    @RefreshScope
    DataSource dataSource (){
        DDSDataSourceManager ds = new DDSDataSourceManager();
        ds.setDdsHikariConfigHashMap(ddsConfigProperties.getDatabase());
        ds.setGeneralAttributes(ddsConfigProperties.getGeneral());
        return ds;
    }

    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnClass(DDSDataSourceManager.class)
    @RefreshScope
    DDSInterceptor ddsInterceptor() {
        DDSInterceptor interceptor = new DDSInterceptor();
        interceptor.setGeneralAttributes(ddsConfigProperties.getGeneral());
        return interceptor;
    }

    @Bean
    @ConditionalOnMissingBean
    @ConditionalOnClass(DDSDataSourceManager.class)
    InterceptorRegister interceptorRegister() {
        return new InterceptorRegister();
    }

}
