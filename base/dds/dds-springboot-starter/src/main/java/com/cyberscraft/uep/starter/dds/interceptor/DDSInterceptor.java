package com.cyberscraft.uep.starter.dds.interceptor;

import com.cyberscraft.uep.starter.dds.TenantHolder;
import com.cyberscraft.uep.starter.dds.bean.GeneralAttributes;
import com.cyberscraft.uep.starter.dds.exceptions.DDSException;
import com.cyberscraft.uep.starter.dds.exceptions.DDSExceptionCodeEnum;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpMethod;
import org.springframework.util.PatternMatchUtils;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @Auther: liuyang
 * @Date 2021-05-20 14:02
 * @Description:获得租户数据，并放入ThreadLocal
 */
public class DDSInterceptor implements HandlerInterceptor {
    private static final Logger logger = LoggerFactory.getLogger(HandlerInterceptor.class);
    private GeneralAttributes generalAttributes = new GeneralAttributes();

    public void setGeneralAttributes(GeneralAttributes generalAttributes) {
        this.generalAttributes = generalAttributes;
    }

    /**
     * 用户请求拦截，这里只校验基本的信息，数据源的校验由DataSource来做
     * @param httpServletRequest
     * @param httpServletResponse
     * @param o
     * @return
     * @throws Exception
     */
    @Override
    public boolean preHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o) {
        String tenantCodeInAttr = (String) httpServletRequest.getAttribute(generalAttributes.getTenantHeaderName());
        if (StringUtils.isNotEmpty(tenantCodeInAttr)){
            TenantHolder.setTenantCode(tenantCodeInAttr);
            return true;
        }

        if(HttpMethod.OPTIONS.name().equals(httpServletRequest.getMethod())){
            return true;
        }

        if(!generalAttributes.isEnableGlobalInterceptor()){
            String tenantCodeValue = httpServletRequest.getHeader(generalAttributes.getTenantHeaderName());
            if(StringUtils.isNotBlank(tenantCodeValue)){
                TenantHolder.setTenantCode(tenantCodeValue);
                logger.debug("dds log===>requestUri:{}，tenantCodeHeaderName:{}，tenantCodeValue:{}",
                        httpServletRequest.getRequestURI(),generalAttributes.getTenantHeaderName(),tenantCodeValue);
            }
            return true;
        }

        String requestUri = httpServletRequest.getRequestURI();

        if(requestUri == null || "".equals(requestUri)){
            return true;
        }

        String tenantCodeValue = httpServletRequest.getHeader(generalAttributes.getTenantHeaderName());

        if(tenantCodeValue == null || "".equals(tenantCodeValue)){
            tenantCodeValue = httpServletRequest.getParameter(generalAttributes.getTenantHeaderName());
        }

        if(StringUtils.isNotBlank(generalAttributes.getFilterUrls())){
//            List filterUrlsList= new ArrayList();
            //Collections.addAll(filterUrlsList,generalAttributes.getFilterUrls().split(generalAttributes.getFilterUrlsSeparator()));
            String filterUrls[] = generalAttributes.getFilterUrls().split(generalAttributes.getFilterUrlsSeparator());
            boolean isMatched = PatternMatchUtils.simpleMatch(filterUrls, requestUri);
            if(isMatched) return true;

        }

        logger.debug("dds log===>requestUri:{}，tenantCodeHeaderName:{}，tenantCodeValue:{}",
                requestUri,generalAttributes.getTenantHeaderName(),tenantCodeValue);

        if(StringUtils.isEmpty(tenantCodeValue)) {
            logger.warn("dds log===>the value of request header param: {} not exist", generalAttributes.getTenantHeaderName());
            throw new DDSException(DDSExceptionCodeEnum.UEP_TENANT_REQUEST_TCODE_NOT_EXIST);
        }
//        Assert.notNull(tenantCodeValue,"the value of request header param:"+generalAttributes.getTenantHeaderName()+" is null");
        //如果没有传，且没有默认值
//        if(StringUtils.isBlank(tenantCodeValue) && StringUtils.isBlank(generalAttributes.getDefaultDataBase() )){
//            logger.warn("dds login===>tenantCode is null,default config Database is null");
//            return true;
//        }

//        if(StringUtils.isBlank(tenantCodeValue) && StringUtils.isNotBlank(generalAttributes.getDefaultDataBase())){
//            logger.info("dds loging===>tenantCode is null,use default config");
//            tenantCodeValue = generalAttributes.getDefaultDataBase()+generalAttributes.getHeaderSeparator()+generalAttributes.getDefaultSchema();
//        }
        TenantHolder.setTenantCode(tenantCodeValue);
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, ModelAndView modelAndView) throws Exception {
        TenantHolder.remove();
    }
}