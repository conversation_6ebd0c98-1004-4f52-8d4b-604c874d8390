package com.cyberscraft.uep.account.client.provider.wework;

import com.cyberscraft.uep.common.enums.ExceptionCodeEnum;
import com.cyberscraft.uep.account.client.domain.ThirdPartyGroup;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountErrorType;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountException;
import com.cyberscraft.uep.common.constant.SysConstant;
import com.cyberscraft.uep.common.util.JsonUtil;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/***
 *
 * @date 2021/3/23
 * <AUTHOR>
 ***/
public class TestParseDepartmentResponse {


    /****
     *
     */
    private final static Logger LOG = LoggerFactory.getLogger(TestParseDepartmentResponse.class);

    @Test
    public void run() {
        String res1 = "{\n" +
                "   \"errcode\": 0,\n" +
                "   \"errmsg\": \"ok\",\n" +
                "   \"department\": [\n" +
                "       {\n" +
                "           \"id\": 2,\n" +
                "           \"name\": \"广州研发中心\",\n" +
                "           \"name_en\": \"RDGZ\",\n" +
                "           \"parentid\": 1,\n" +
                "           \"order\": 10\n" +
                "       },\n" +
                "       {\n" +
                "           \"id\": 3,\n" +
                "           \"name\": \"邮箱产品部\",\n" +
                "           \"name_en\": \"mail\",\n" +
                "           \"parentid\": 2,\n" +
                "           \"order\": 40\n" +
                "       }\n" +
                "   ]\n" +
                "}\n";


        String res2 = "{\n" +
                "   \"errcode\": 0,\n" +
                "   \"errmsg\": \"ok\",\n" +
                "   \"department\": [\n" +
                "       {\n" +
                "           \"id2\": 2,\n" +
                "           \"name\": \"广州研发中心\",\n" +
                "           \"name_en\": \"RDGZ\",\n" +
                "           \"parentid\": 1,\n" +
                "           \"order\": 10\n" +
                "       },\n" +
                "       {\n" +
                "           \"id2\": 3,\n" +
                "           \"name\": \"邮箱产品部\",\n" +
                "           \"name_en\": \"mail\",\n" +
                "           \"parentid\": 2,\n" +
                "           \"order\": 40\n" +
                "       }\n" +
                "   ]\n" +
                "}\n";

        String res3 = "{\n" +
                "   \"errcode\": 0,\n" +
                "   \"errmsg\": \"ok\",\n" +
                "   \"department\": [\n" +
                "       {\n" +
                "           \"id\": 2,\n" +
                "           \"name2\": \"广州研发中心\",\n" +
                "           \"name_en\": \"RDGZ\",\n" +
                "           \"parentid\": 1,\n" +
                "           \"order\": 10\n" +
                "       },\n" +
                "       {\n" +
                "           \"id\": 3,\n" +
                "           \"name2\": \"邮箱产品部\",\n" +
                "           \"name_en\": \"mail\",\n" +
                "           \"parentid2\": 2,\n" +
                "           \"order\": 40\n" +
                "       }\n" +
                "   ]\n" +
                "}\n";

        List<ThirdPartyGroup> list1 = parseDepartments(res1);

        List<ThirdPartyGroup> list2 = parseDepartments(res2);
        List<ThirdPartyGroup> list3 = parseDepartments(res3);

        Assert.assertTrue(list1.size() == 2);
        Assert.assertTrue(list2.size() == 0);
        Assert.assertTrue(list3.size() == 2);
        Assert.assertTrue(list3.get(0).getName() == null);
    }

    /****
     *
     * @param res
     * @return
     */
    @SuppressWarnings({"unchecked"})
    private List<ThirdPartyGroup> parseDepartments(String res) {
        Map<String, Object> json = JsonUtil.str2Map(res);
        String errorCode = String.valueOf(json.get("errcode"));
        String errorMsg = String.valueOf(json.get("errmsg"));
        if (!ExceptionCodeEnum.SUCCESS.getCode().equalsIgnoreCase(errorCode)) {
            LOG.warn("企业信息返回错误code:{},msg:{}", errorCode, errorMsg);
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.SERVER_RESPONSE_INVALID.getCode(), errorMsg);
        }
        List<Map<String, Object>> list = (List<Map<String, Object>>) json.get("department");
        if (list == null || list.size() == 0) {
            return null;
        }
        List<ThirdPartyGroup> ret = new ArrayList<>(list.size());
        for (Map<String, Object> data : list) {
            ThirdPartyGroup obj = parseDepartment(data);
            if (obj != null) {
                ret.add(obj);
            }
        }
        return ret;
    }

    /***
     *
     * @param data
     * @return
     */
    private ThirdPartyGroup parseDepartment(Map<String, Object> data) {
        ThirdPartyGroup obj = new ThirdPartyGroup();

        obj.setStatus(SysConstant.TRUE);
        Map<String, Object> extendAttributes = new HashMap<>(data);
        Object id = extendAttributes.remove("id");
        //code 不能这为空
        if (id == null) {
            return null;
        }
        obj.setCode(String.valueOf(id));
        Object name = extendAttributes.remove("name");
        if (name != null) {
            obj.setName(String.valueOf(name));
        }
        Object parentId = extendAttributes.remove("parentid");
        if (parentId != null) {
            obj.setParentCode(String.valueOf(parentId));
        }
//        extendAttributes.put("order",data.get("order"));
//        extendAttributes.put("name_en",data.get("name_en"));
        obj.setExtAttributes(extendAttributes);
        return obj;
    }
}
