package com.cyberscraft.uep.account.client.provider.wework.handle;

import com.cyberscraft.uep.account.client.constant.ThirdPartyAccountType;
import com.cyberscraft.uep.account.client.domain.ThirdPartyAccount;
import com.cyberscraft.uep.account.client.provider.IThirdPartyTokenHandler;

/***
 *  用户处理微信对应的token验证及测试登录等逻辑
 * @date 2021/3/13
 * <AUTHOR>
 ***/
public class WeWorkTokenHandler implements IThirdPartyTokenHandler {

    @Override
    public boolean isSupported(String tokenType) {
        return ThirdPartyAccountType.WEWORK.getCode().equalsIgnoreCase(tokenType);
    }

    /***
     * 因为企业微信，暂时没有此类使用场景，所以不进行实现
     * @param token
     * @return
     */
    @Override
    public ThirdPartyAccount validateToken(String token) {
        return null;
    }

    /***
     * 因为企业微信，暂时没有此类使用场景，所以不进行实现
     * @param token
     * @return
     */
    @Override
    public String generatorTicket(String token, String ticketType) {
        return null;
    }

    /***
     * 进行登录处理，需要进行实现
     * @param userName
     * @param password
     * @return
     */
    @Override
    public ThirdPartyAccount login(String userName, String password) {
        return null;
    }
}
