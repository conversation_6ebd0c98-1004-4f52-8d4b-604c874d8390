package com.cyberscraft.uep.account.client.provider.wework.constant;

/***
 *企业微信对应的消息类型定义，包括几类，事件消息，普通消息，参考微信的文档，暂时未定义全
 * @date 2021/3/5
 * <AUTHOR>
 ***/
public enum WeWorkMessageType {
    EVENT("event");
    private String code;

    WeWorkMessageType(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    @Override
    public String toString() {
        return this.code;
    }
}
