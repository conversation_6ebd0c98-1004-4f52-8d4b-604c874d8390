package com.cyberscraft.uep.account.client.provider.wework.handle;

import com.cyberscraft.uep.account.client.constant.ThirdPartyAccountType;
import com.cyberscraft.uep.account.client.domain.Connector;
import com.cyberscraft.uep.account.client.domain.ThirdPartyAccount;
import com.cyberscraft.uep.account.client.domain.ThirdPartyGroup;
import com.cyberscraft.uep.account.client.domain.ThirdPartyGroupPosition;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountErrorType;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountException;
import com.cyberscraft.uep.account.client.provider.IThirdPartyAccountHandler;
import com.cyberscraft.uep.account.client.provider.wework.config.WeWorkClientConfig;
import com.cyberscraft.uep.account.client.provider.wework.domain.WeWorkConfig;
import com.cyberscraft.uep.account.client.provider.wework.service.IWeWorkAccessTokenService;
import com.cyberscraft.uep.common.util.WebUrlUtil;
import com.cyberscraft.uep.common.constant.SysConstant;
import com.cyberscraft.uep.common.enums.ExceptionCodeEnum;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.common.util.RestAPIUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

/***
 * 企业微信对应的用户及组操作接口实现类
 * @date 2021/3/13
 * <AUTHOR>
 ***/
public class WeWorkAccountHandler implements IThirdPartyAccountHandler {

    /***
     * 微信访问Token接口
     */
    private IWeWorkAccessTokenService weWorkAccessTokenService;

    /***
     * 微信客户端配置
     */
    private WeWorkClientConfig weWorkClientConfig;

    /***
     * 得到部门列表
     */
    private String getDepartmentListApi = "/cgi-bin/department/list";

    /***
     * 获取部门成员详情
     */
    private String getUserListApi = "/cgi-bin/user/list";


    /***
     * 获取用户详情
     */
    private String getUserApi = "/cgi-bin/user/get";

    /***
     *
     */
    private final static Logger LOG = LoggerFactory.getLogger(WeWorkAccountHandler.class);


    @Override
    public boolean isSupported(String accountType) {
        return ThirdPartyAccountType.WEWORK.getCode().equals(accountType);
    }

    /****
     * 因为企业微信根组为1，所以这里写死？
     * @param tenantId
     * @param connector
     * @return
     * @throws ThirdPartyAccountException
     */
    @Override
    public ThirdPartyGroup getRootGroup(String tenantId, Connector connector) throws ThirdPartyAccountException {
        ThirdPartyGroup obj = new ThirdPartyGroup();
        obj.setCode(weWorkClientConfig.getRootGroupCode());
        obj.setParentCode(String.valueOf(SysConstant.DEFAULT_ROOT_GROUP_PARENTID));
        obj.setName(weWorkClientConfig.getRootGroupName());
        obj.setStatus(SysConstant.TRUE);
        return obj;
    }

    @Override
    public ThirdPartyGroup getGroupByCode(String tenantId, String groupCode, Connector connector) throws ThirdPartyAccountException {
        if (StringUtils.isBlank(groupCode)) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.GROUP_CODE_EMPTY);
        }
        if (connector == null) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.CONNECTOR_INVALID);
        }
        if (weWorkClientConfig.getRootGroupCode().equals(groupCode)) {
            return getRootGroup(tenantId, connector);
        }

        WeWorkConfig cfg = JsonUtil.str2Obj(connector.getConfig(), WeWorkConfig.class);

        String accessToken = getAccessToken(tenantId, cfg);

        return getGroupByCode(tenantId, groupCode, accessToken, cfg, connector);
    }

    /***
     *
     * @param tenantId
     * @param cfg
     * @return
     */
    private String getAccessToken(String tenantId, WeWorkConfig cfg) {
        //WeWorkConfig cfg = JsonUtil.str2Obj(connector.getConfig(), WeWorkConfig.class);

         String accessToken = weWorkAccessTokenService.getAccessToken(tenantId, cfg.getApiBaseUrl(), cfg.getCorpId(), cfg.getAppSecret());
        //因为企业微信会返回当前组及对应的子组列表（所有的子组）
        if (StringUtils.isBlank(accessToken)) {
            LOG.warn("获取企业微信对应的组织信息时，未获取到租户对应的连接器的accessToken,corpId:{},ApiBaseUrl:{}", tenantId, cfg.getCorpId(), cfg.getApiBaseUrl());
            return null;
        }
        return accessToken;
    }

    /****
     * 根据组织代码，获取对应的企业微信组织信息
     * @param tenantId
     * @param groupCode
     * @param accessToken
     * @param connector
     * @return
     * @throws ThirdPartyAccountException
     */
    private ThirdPartyGroup getGroupByCode(String tenantId, String groupCode, String accessToken, WeWorkConfig cfg, Connector connector) throws ThirdPartyAccountException {
        //进行代码转换
        List<ThirdPartyGroup> departments = getGroupsByCode(tenantId, groupCode, accessToken, cfg, connector);
        return departments != null && departments.size() > 0 ? departments.get(0) : null;
    }

    /****
     * 根据组织代码，获取对应的企业微信组织信息
     * @param tenantId
     * @param groupCode
     * @param accessToken
     * @param connector
     * @return
     * @throws ThirdPartyAccountException
     */
    private List<ThirdPartyGroup> getGroupsByCode(String tenantId, String groupCode, String accessToken, WeWorkConfig cfg, Connector connector) throws ThirdPartyAccountException {

        String api = WebUrlUtil.getWebServerUrl(cfg.getApiBaseUrl(), getDepartmentListApi);
        //因为企业微信会返回当前组及对应的子组列表（所有的子组）
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("access_token", accessToken);
        parameters.put("id", groupCode);
        //String parameterVal = WebUrlUtil.getParameterValueStr(parameters);
        String url = WebUrlUtil.getWebAccessServerUrl(api, parameters);
        LOG.info("当前请求的企业参数地址:{}", url);
        String res = RestAPIUtil.getStringForEntity(url);
        //进行代码转换
        List<ThirdPartyGroup> departments = parseDepartments(res);
        return departments;
    }

    /****
     *
     * @param res
     * @return
     */
    @SuppressWarnings({"unchecked"})
    private List<ThirdPartyGroup> parseDepartments(String res) {
        if (StringUtils.isBlank(res)) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.SERVER_RESPONSE_INVALID.getCode(), "服务器端返回空内容");
        }
        Map<String, Object> json = JsonUtil.str2Map(res);
        String errorCode = String.valueOf(json.get("errcode"));
        String errorMsg = String.valueOf(json.get("errmsg"));
        if (!ExceptionCodeEnum.SUCCESS.getCode().equalsIgnoreCase(errorCode)) {
            LOG.warn("企业信息返回错误code:{},msg:{}", errorCode, errorMsg);
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.SERVER_RESPONSE_INVALID.getCode(), errorMsg);
        }
        List<Map<String, Object>> list = (List<Map<String, Object>>) json.get("department");
        if (list == null || list.size() == 0) {
            return null;
        }
        List<ThirdPartyGroup> ret = new ArrayList<>(list.size());
        for (Map<String, Object> data : list) {
            ThirdPartyGroup obj = parseDepartment(data);
            if (obj != null) {
                ret.add(obj);
            }
        }
        return ret;
    }

    /***
     *
     * @param data
     * @return
     */
    private ThirdPartyGroup parseDepartment(Map<String, Object> data) {
        ThirdPartyGroup obj = new ThirdPartyGroup();

        obj.setStatus(SysConstant.TRUE);
        Map<String, Object> extendAttributes = new HashMap<>(data);
        Object id = extendAttributes.remove("id");
        //code 不能为空
        if (id == null) {
            return null;
        }
        obj.setCode(String.valueOf(id));
        Object name = extendAttributes.remove("name");
        if (name != null) {
            obj.setName(String.valueOf(name));
        }
        Object parentId = extendAttributes.remove("parentid");
        if (parentId != null) {
            obj.setParentCode(String.valueOf(parentId));
        }
        obj.setExtAttributes(extendAttributes);
        return obj;
    }


    @Override
    public List<ThirdPartyGroup> getGroupsByCodes(String tenantId, List<String> codes, Connector connector) throws ThirdPartyAccountException {
        if (codes == null || codes.size() == 0) {
            return null;
        }
        WeWorkConfig cfg = JsonUtil.str2Obj(connector.getConfig(), WeWorkConfig.class);
        String accessToken = getAccessToken(tenantId, cfg);
        //这里进行去重处理
        Set<String> queryCodes = new HashSet<>(codes);
        List<ThirdPartyGroup> ret = new ArrayList<>(codes.size());
        for (String code : queryCodes) {
            ThirdPartyGroup obj = null;
            if (weWorkClientConfig.getRootGroupCode().equals(code)) {
                obj = getRootGroup(tenantId, connector);
            } else {
                obj = getGroupByCode(tenantId, code, accessToken, cfg, connector);
            }
            if (obj != null) {
                ret.add(obj);
            }
        }
        return ret;
    }

    /***
     * 因为企业微信不支持分页，所以返回的就是全部
     * @param tenantId
     * @param parentGroup
     * @param connector
     * @param pageIndex
     * @param pageSize
     * @return
     * @throws ThirdPartyAccountException
     */
    @Override
    public List<ThirdPartyGroup> getSubGroups(String tenantId, String parentGroup, Connector connector, int pageIndex, int pageSize) throws ThirdPartyAccountException {
        return getSubGroups(tenantId, parentGroup, connector);
    }

    @Override
    public List<ThirdPartyGroup> getSubGroups(String tenantId, String parentGroup, Connector connector) throws ThirdPartyAccountException {
        if (StringUtils.isBlank(parentGroup)) {
            return null;
        }
        WeWorkConfig cfg = JsonUtil.str2Obj(connector.getConfig(), WeWorkConfig.class);
        String accessToken = getAccessToken(tenantId, cfg);
        return getGroupsByCode(tenantId, parentGroup, accessToken, cfg, connector);
    }

    @Override
    public List<ThirdPartyGroup> getAllSubGroups(String tenantId, String parentGroup, Connector connector) throws ThirdPartyAccountException {
        List<ThirdPartyGroup> subGroups = getSubGroups(tenantId, parentGroup, connector);
        return subGroups.stream().filter(e -> !e.getCode().equals(parentGroup)).collect(Collectors.toList());
    }

    @Override
    public List<ThirdPartyGroup> getAllGroups(String tenantId, Connector connector) throws ThirdPartyAccountException {
        return getSubGroups(tenantId, weWorkClientConfig.getRootGroupCode(), connector);
    }

    @Override
    public List<ThirdPartyAccount> getAccountsByGroup(String tenantId, String groupCode, Connector connector) throws ThirdPartyAccountException {
        if (StringUtils.isBlank(groupCode)) {
            return null;
        }
        WeWorkConfig cfg = JsonUtil.str2Obj(connector.getConfig(), WeWorkConfig.class);
        String accessToken = getAccessToken(tenantId, cfg);
        return getAccountsByGroup(tenantId, groupCode, accessToken, cfg, connector);
    }

    /****
     * 因为企业微信不支持分页获取，所以获取的都是对应部门的全部记录
     * @param tenantId
     * @param groupCode
     * @param connector
     * @param pageIndex
     * @param pageSize
     * @return
     * @throws ThirdPartyAccountException
     */
    @Override
    public List<ThirdPartyAccount> getAccountsByGroup(String tenantId, String groupCode, Connector connector, int pageIndex, int pageSize) throws ThirdPartyAccountException {
        return getAccountsByGroup(tenantId, groupCode, connector);
    }

    @Override
    public List<ThirdPartyAccount> getAccountsByUserIds(String tenantId, List<String> userIds, Connector connector) throws ThirdPartyAccountException {
        if (userIds == null || userIds.size() == 0) {
            return null;
        }
        WeWorkConfig cfg = JsonUtil.str2Obj(connector.getConfig(), WeWorkConfig.class);
        String accessToken = getAccessToken(tenantId, cfg);

        List<ThirdPartyAccount> ret = new ArrayList<>(userIds.size());
        for (String accountId : userIds) {
            Map<String, Object> data = getAccountByUserId(tenantId, accountId, accessToken, cfg, connector);
            if (data == null) {
                continue;
            }
            ThirdPartyAccount user = parseUser(tenantId, data);
            if (user != null) {
                ret.add(user);
            }
        }
        return ret;
    }

    @Override
    public List<ThirdPartyAccount> getAccountsByMobile(String tenantId, List<String> userIds, Connector connector) throws ThirdPartyAccountException {
        if (userIds == null || userIds.size() == 0) {
            return null;
        }
        WeWorkConfig cfg = JsonUtil.str2Obj(connector.getConfig(), WeWorkConfig.class);
        String accessToken = getAccessToken(tenantId, cfg);

        List<ThirdPartyAccount> ret = new ArrayList<>(userIds.size());
        for (String accountId : userIds) {
            Map<String, Object> data = getAccountByUserId(tenantId, accountId, accessToken, cfg, connector);
            if (data == null) {
                continue;
            }
            ThirdPartyAccount user = parseUser(tenantId, data);
            if (user != null) {
                ret.add(user);
            }
        }
        return ret;
    }

    /***
     * 获取用户详情列表
     * @param tenantId        请求租户id
     * @param thirdAccountIds 请求用户Id列表
     * @return
     * @throws ThirdPartyAccountException
     */
    @Override
    public List<Map<String, Object>> getTenantUserProfileByUserIds(String tenantId, List<String> thirdAccountIds, Connector connector) throws ThirdPartyAccountException {
        if (thirdAccountIds == null || thirdAccountIds.size() == 0) {
            return null;
        }
        WeWorkConfig cfg = JsonUtil.str2Obj(connector.getConfig(), WeWorkConfig.class);
        String accessToken = getAccessToken(tenantId, cfg);
        List<Map<String, Object>> ret = new ArrayList<>(thirdAccountIds.size());
        for (String accountId : thirdAccountIds) {
            Map<String, Object> user = getAccountByUserId(tenantId, accountId, accessToken, cfg, connector);
            if (user != null) {
                ret.add(user);
            }
        }
        return ret;
    }

    private Map<String, Object> getAccountByUserId(String tenantId, String userId, String accessToken, WeWorkConfig cfg, Connector connector) {
        String api = WebUrlUtil.getWebServerUrl(cfg.getApiBaseUrl(), getUserApi);
        //因为企业微信会返回当前组及对应的子组列表（所有的子组）
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("access_token", accessToken);
        parameters.put("userid", userId);
        parameters.put("avatar_addr", "1");
//        String parameterVal = WebUrlUtil.getParameterValueStr(parameters);
        String url = WebUrlUtil.getWebAccessServerUrl(api, parameters);
        LOG.info("当前请求的企业参数地址:{}", url);
        String res = RestAPIUtil.getStringForEntity(url);
        //进行代码转换
        Map<String, Object> users = parseUserData(tenantId, res);
        return users;
    }


    private Map<String, Object> parseUserData(String userId, String res) {
        if (StringUtils.isBlank(res)) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.SERVER_RESPONSE_INVALID.getCode(), "服务器端返回空内容,userId:" + userId);
        }
        Map<String, Object> json = JsonUtil.str2Map(res);
        String errorCode = String.valueOf(json.get("errcode"));
        String errorMsg = String.valueOf(json.get("errmsg"));
        if (!ExceptionCodeEnum.SUCCESS.getCode().equalsIgnoreCase(errorCode)) {
            LOG.warn("企业微信获取用户返回错误userId:{},code:{},msg:{}", userId, errorCode, errorMsg);
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.SERVER_RESPONSE_INVALID.getCode(), errorMsg);
        }
        return json;
    }

    /***
     *
     * @param tenantId
     * @param code
     * @param accessToken
     * @param connector
     * @return
     * @throws ThirdPartyAccountException
     */
    private List<ThirdPartyAccount> getAccountsByGroup(String tenantId, String code, String accessToken, WeWorkConfig cfg, Connector connector) throws ThirdPartyAccountException {
        String api = WebUrlUtil.getWebServerUrl(cfg.getApiBaseUrl(), getUserListApi);
        //因为企业微信会返回当前组及对应的子组列表（所有的子组）
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("access_token", accessToken);
        parameters.put("department_id", code);
        parameters.put("avatar_addr", "1");
//        String parameterVal = WebUrlUtil.getParameterValueStr(parameters);
        String url = WebUrlUtil.getWebAccessServerUrl(api, parameters);
        LOG.info("当前请求的企业参数地址:{}", url);
        String res = RestAPIUtil.getStringForEntity(url);
        //进行代码转换
        List<ThirdPartyAccount> users = parseUsers(tenantId, code, res);
        return users;
    }


    @SuppressWarnings({"unchecked"})
    private List<ThirdPartyAccount> parseUsers(String tenantId, String groupCode, String res) {
        if (StringUtils.isBlank(res)) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.SERVER_RESPONSE_INVALID.getCode(), "服务器端返回空内容,组织代码:" + groupCode);
        }
        Map<String, Object> json = JsonUtil.str2Map(res);
        String errorCode = String.valueOf(json.get("errcode"));
        String errorMsg = String.valueOf(json.get("errmsg"));
        if (!ExceptionCodeEnum.SUCCESS.getCode().equalsIgnoreCase(errorCode)) {
            LOG.warn("企业微信获取用户返回错误groupCode:{},code:{},msg:{}", groupCode, errorCode, errorMsg);
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.SERVER_RESPONSE_INVALID.getCode(), errorMsg);
        }
        List<Map<String, Object>> list = (List<Map<String, Object>>) json.get("userlist");
        if (list == null || list.size() == 0) {
            return null;
        }
        List<ThirdPartyAccount> ret = new ArrayList<>(list.size());
        for (Map<String, Object> data : list) {
            ThirdPartyAccount obj = parseUser(tenantId, data);
            if (obj != null) {
                ret.add(obj);
            }
        }
        return ret;
    }

    /***
     *
     * @param data
     * @return
     */
    private ThirdPartyAccount parseUser(String tenantId, Map<String, Object> data) {
        ThirdPartyAccount obj = new ThirdPartyAccount();

        obj.setUserId((String) data.get("userid"));
        obj.setName((String) data.get("name"));
        obj.setMobile((String) data.get("mobile"));
        obj.setGender((String) data.get("gender"));
        obj.setMail((String) data.get("email"));
        obj.setTel((String) data.get("telephone"));
        Object enable = data.get("enable");
        obj.setStatus(enable != null ? (Integer) enable : SysConstant.TRUE_VALUE);
//        obj.setNickName((String) data.get("english_name"));
        obj.setEnglish_name((String) data.get("english_name"));

        obj.setLoginId(obj.getUserId());
        obj.setAccountId(obj.getUserId());
        obj.setAccountType(ThirdPartyAccountType.WEWORK.getCode());
        obj.setTenantId(tenantId);

        Map<String, Object> extendAttributes = new HashMap<>();
//        extendAttributes.put("avatar", data.get("avatar"));
//        extendAttributes.put("address", data.get("address"));
//        extendAttributes.put("position", data.get("position"));
        extendAttributes.putAll(data);

        obj.setGroupPositions(getGroupPositions(data));
        obj.setExtAttributes(extendAttributes);
        return obj;
    }

    /***
     * 得到用户所在的组织列表
     * @param data
     * @return
     */
    @SuppressWarnings({"unchecked"})
    private List<ThirdPartyGroupPosition> getGroupPositions(Map<String, Object> data) {
        List<Integer> departs = (List<Integer>) data.get("department");
        if (departs == null || departs.size() == 0) { //!= isLeaderInDepts.length
            LOG.warn("微信用户信息中部门数据不正确，未能解析用户所在部门");
            return null;
        }
        List<ThirdPartyGroupPosition> ret = new ArrayList<>(departs.size());

        for (Integer depart : departs) {
            ThirdPartyGroupPosition position = new ThirdPartyGroupPosition();
            position.setIsMain(SysConstant.TRUE_VALUE);
            position.setCode(String.valueOf(depart));
            position.setPosition((String) data.get("position"));
            ret.add(position);
        }
        return ret;
    }

    public IWeWorkAccessTokenService getWeWorkAccessTokenService() {
        return weWorkAccessTokenService;
    }

    public void setWeWorkAccessTokenService(IWeWorkAccessTokenService weWorkAccessTokenService) {
        this.weWorkAccessTokenService = weWorkAccessTokenService;
    }

    public WeWorkClientConfig getWeWorkClientConfig() {
        return weWorkClientConfig;
    }

    public void setWeWorkClientConfig(WeWorkClientConfig weWorkClientConfig) {
        this.weWorkClientConfig = weWorkClientConfig;
    }

    public String getGetDepartmentListApi() {
        return getDepartmentListApi;
    }

    public void setGetDepartmentListApi(String getDepartmentListApi) {
        this.getDepartmentListApi = getDepartmentListApi;
    }

    public String getGetUserListApi() {
        return getUserListApi;
    }

    public void setGetUserListApi(String getUserListApi) {
        this.getUserListApi = getUserListApi;
    }

    public String getGetUserApi() {
        return getUserApi;
    }

    public void setGetUserApi(String getUserApi) {
        this.getUserApi = getUserApi;
    }
}
