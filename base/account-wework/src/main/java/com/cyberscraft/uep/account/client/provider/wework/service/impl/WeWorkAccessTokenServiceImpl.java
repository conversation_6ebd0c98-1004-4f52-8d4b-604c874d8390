package com.cyberscraft.uep.account.client.provider.wework.service.impl;

import com.cyberscraft.uep.common.enums.ExceptionCodeEnum;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountErrorType;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountException;
import com.cyberscraft.uep.account.client.provider.wework.config.WeWorkClientConfig;
import com.cyberscraft.uep.account.client.provider.wework.domain.WeWorkAccessToken;
import com.cyberscraft.uep.account.client.provider.wework.service.IWeWorkAccessTokenService;
import com.cyberscraft.uep.common.util.WebUrlUtil;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.common.util.RestAPIUtil;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/***
 * 企业微信访问token接口实现
 * @date 2021/3/23
 * <AUTHOR>
 ***/
@Service
public class WeWorkAccessTokenServiceImpl implements IWeWorkAccessTokenService {

    private final static Logger LOG = LoggerFactory.getLogger(WeWorkAccessTokenServiceImpl.class);

    /***
     * 获取企业微信accessToken API地址
     */
    private String getAcessTokenApi = "/cgi-bin/gettoken";

    @Resource
    private WeWorkClientConfig weWorkClientConfig;

    @Resource
    private RedissonClient redissonClient;


    /**
     * 企业微信accessToken缓存的key，占位符1为租户ID，占用符2为应用ID
     */
    private static final String WEWORK_TOKEN_REDIS_KEY = "WEWORK_ACCESS_TOKEN:%s:%s";

    /**
     * 请求token时，锁的前缀，占位符为accessToken缓存的key
     */
    private static final String WEWORK_TOKEN_LOCK_KEY = "lock:%s";
    /**
     * 请求token时，锁的等待时间，单位：毫秒
     */
    private static final Long WEWORK_TOKEN_LOCK_WAIT_TIME = 5000L;
    /**
     * 请求token时，锁的超时时间,单位：毫秒
     */
    private static final Long WEWORK_TOKEN_LOCK_TIME = 10000L;

    /***
     *
     */
    private final static Integer TOKEN_EXPIRE_SKEW_VALUE = 10;

    /***
     *
     * @param tenantId
     * @param corpId
     * @param corpSecret
     * @return
     * @throws ThirdPartyAccountException
     */
    @Override
    public String getAccessToken(String tenantId, String apiBase, String corpId, String corpSecret) throws ThirdPartyAccountException {

        String tokenCacheKey = String.format(WEWORK_TOKEN_REDIS_KEY, tenantId, corpId);
        String accessToken = getAccessTokenFromCache(tokenCacheKey);
        if (StringUtils.isNotBlank(accessToken)) {
            return accessToken;
        }
        String lockKey = String.format(WEWORK_TOKEN_LOCK_KEY, tokenCacheKey);
        RLock lock = redissonClient.getLock(lockKey);

        boolean holdLock;
        try {
            holdLock = lock.tryLock(WEWORK_TOKEN_LOCK_WAIT_TIME, WEWORK_TOKEN_LOCK_TIME, TimeUnit.MILLISECONDS);

            accessToken = getAccessTokenFromCache(tokenCacheKey);
            if (StringUtils.isNotBlank(accessToken)) {
                return accessToken;
            }
            if (!holdLock) {
                throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.GET_ACCESS_TOKEN_ERROR);
            }

            WeWorkAccessToken token = getAccessTokenFromWeWork(apiBase, corpId, corpSecret);
            if (token != null) {
                saveAccessTokenToCache(tokenCacheKey, token);
            }
            return token.getAccessToken();
        } catch (InterruptedException e) {
            //LOG.error(e.getMessage(), e);
            throw new ThirdPartyAccountException(ExceptionCodeEnum.INNER_ERROR.getCode(), e.getMessage());
        } catch (ThirdPartyAccountException e) {
            throw e;
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
        //throw new ThirdPartyAccountException(ExceptionCodeEnum.INNER_ERROR);
    }

    @Override
    public void removeAccessToken(String tenantId, String corpId) throws ThirdPartyAccountException {
        String tokenCacheKey = String.format(WEWORK_TOKEN_REDIS_KEY, tenantId, corpId);
        redissonClient.getBucket(tokenCacheKey).delete();
    }

    /***
     * 保存到rediis
     * @param tokenCacheKey
     * @param token
     */
    private void saveAccessTokenToCache(String tokenCacheKey, WeWorkAccessToken token) {
        redissonClient.getBucket(tokenCacheKey).set(token.getAccessToken(), token.getExpiresIn(), TimeUnit.SECONDS);
    }

    private String getAccessTokenFromCache(String tokenCacheKey) {
        RBucket<String> bucket = redissonClient.getBucket(tokenCacheKey);// .get();
        return bucket.get();
    }


    /***
     * 从服务器端获取访问Token
     * @param corpId
     * @param corpSecret
     * @return
     */
    private WeWorkAccessToken getAccessTokenFromWeWork(String apiBase, String corpId, String corpSecret) {
        if (StringUtils.isBlank(corpId)) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.CORPID_INVALID);
        }
        if (StringUtils.isBlank(corpSecret))
            LOG.info("get access token from wework,corpId:{},corpsecret:{}", corpId, corpSecret);
        String api = WebUrlUtil.getWebServerUrl(apiBase, getAcessTokenApi);
        //因为企业微信会返回当前组及对应的子组列表（所有的子组）
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("corpid", corpId);
        parameters.put("corpsecret", corpSecret);
        //String parameterVal = WebUrlUtil.getParameterValueStr(parameters);
        String url = WebUrlUtil.getWebAccessServerUrl(api, parameters);
        LOG.info("当前请求的企业参数地址:{}", url);
        String res = RestAPIUtil.getStringForEntity(url);
        //进行代码转换
        WeWorkAccessToken token = parseAccessToken(res);

        return token;
    }

    /***
     *
     * @param res
     * @return
     */
    WeWorkAccessToken parseAccessToken(String res) {
        if (StringUtils.isBlank(res)) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.SERVER_RESPONSE_INVALID.getCode(), "服务器端返回空内容:");
        }
        Map<String, Object> json = JsonUtil.str2Map(res);
        String errorCode = String.valueOf(json.get("errcode"));
        String errorMsg = String.valueOf(json.get("errmsg"));
        if (!ExceptionCodeEnum.SUCCESS.getCode().equalsIgnoreCase(errorCode)) {
            LOG.warn("企业微信获取token返回错误,code:{},msg:{}", errorCode, errorMsg);
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.SERVER_RESPONSE_INVALID.getCode(), errorMsg);
        }
        WeWorkAccessToken obj = new WeWorkAccessToken();
        obj.setReceiveAt(LocalDateTime.now());
        obj.setAccessToken((String) json.get("access_token"));
        obj.setExpiresIn((Integer) json.get("expires_in"));
        if (obj.getExpiresIn() > TOKEN_EXPIRE_SKEW_VALUE) {
            obj.setExpiresIn(obj.getExpiresIn() - TOKEN_EXPIRE_SKEW_VALUE);
        }
        return obj;
    }

    public WeWorkClientConfig getWeWorkClientConfig() {
        return weWorkClientConfig;
    }

    public void setWeWorkClientConfig(WeWorkClientConfig weWorkClientConfig) {
        this.weWorkClientConfig = weWorkClientConfig;
    }

    public RedissonClient getRedissonClient() {
        return redissonClient;
    }

    public void setRedissonClient(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }
}
