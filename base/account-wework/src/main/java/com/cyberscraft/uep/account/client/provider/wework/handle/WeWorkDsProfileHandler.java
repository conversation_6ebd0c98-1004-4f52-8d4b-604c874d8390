package com.cyberscraft.uep.account.client.provider.wework.handle;

import com.cyberscraft.uep.account.client.constant.ThirdPartyAccountFieldNames;
import com.cyberscraft.uep.account.client.constant.ThirdPartyAccountType;
import com.cyberscraft.uep.account.client.constant.ThirdPartyGroupFiledNames;
import com.cyberscraft.uep.account.client.domain.ConnectorDsProfile;
import com.cyberscraft.uep.account.client.domain.ConnectorPushDsProfile;
import com.cyberscraft.uep.account.client.provider.AbstractThirdPartyDsProfileHandler;
import com.cyberscraft.uep.account.client.provider.wework.config.WeWorkGroupFieldMappingConfig;
import com.cyberscraft.uep.account.client.provider.wework.config.WeWorkUserFieldMappingConfig;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/***
 *
 * @date 2021/4/7
 * <AUTHOR>
 ***/
public class WeWorkDsP<PERSON><PERSON>leHandler extends AbstractThirdPartyDsProfileHandler {

    /***
     *
     */
    private WeWorkUserFieldMappingConfig userFieldConfig;

    /***
     *
     */
    private WeWorkGroupFieldMappingConfig groupFieldConfig;

    @Override
    protected void setConnectorDsProperties(ConnectorDsProfile obj) {

    }

    @Override
    protected void setConnectorPushDsProperties(ConnectorPushDsProfile obj) {

    }

    @Override
    protected Map<String, String> getUserMapAttributes() {
        Map<String, String> fieldsMap = new HashMap<>(16);
        fieldsMap.put(userFieldConfig.getName(), ThirdPartyAccountFieldNames.NAME);
        fieldsMap.put(userFieldConfig.getLoginId(), ThirdPartyAccountFieldNames.LOGINID);
        fieldsMap.put(userFieldConfig.getNickName(), ThirdPartyAccountFieldNames.NICKNAME);
        fieldsMap.put(userFieldConfig.getEmail(), ThirdPartyAccountFieldNames.MAIL);
        fieldsMap.put(userFieldConfig.getGender(), ThirdPartyAccountFieldNames.GENDER);
        fieldsMap.put(userFieldConfig.getMobile(), ThirdPartyAccountFieldNames.MOBILE);
        fieldsMap.put(userFieldConfig.getTel(), ThirdPartyAccountFieldNames.TEL);

        //以下几个字段暂时进行屏蔽
//        fieldsMap.put(userFieldConfig.getStatus(), ThirdPartyAccountFieldNames.STATUS);
//        fieldsMap.put(userFieldConfig.getUserId(), ThirdPartyAccountFieldNames.USERID);
//        fieldsMap.put(userFieldConfig.getAccountId(), ThirdPartyAccountFieldNames.ACCOUNTID);
        return fieldsMap;
    }

    @Override
    protected Map<String, String> getOrgMapAttributes() {
        Map<String, String> fieldsMap = new HashMap<>(16);
        fieldsMap.put(groupFieldConfig.getName(), ThirdPartyGroupFiledNames.NAME);
        //fieldsMap.put(groupFieldConfig.getCode(), ThirdPartyGroupFiledNames.CODE);
        //fieldsMap.put(groupFieldConfig.getStatus(), ThirdPartyGroupFiledNames.STATUS);
        return fieldsMap;
    }

    @Override
    public String getSupportedAccountType() {
        return ThirdPartyAccountType.WEWORK.getCode();
    }

    public WeWorkUserFieldMappingConfig getUserFieldConfig() {
        return userFieldConfig;
    }

    public void setUserFieldConfig(WeWorkUserFieldMappingConfig userFieldConfig) {
        this.userFieldConfig = userFieldConfig;
    }

    public WeWorkGroupFieldMappingConfig getGroupFieldConfig() {
        return groupFieldConfig;
    }

    public void setGroupFieldConfig(WeWorkGroupFieldMappingConfig groupFieldConfig) {
        this.groupFieldConfig = groupFieldConfig;
    }

    @Override
    protected Map<String, String> getPushUserMapAttributes() {
        Map<String, String> fieldsMap = new HashMap<>(16);
        fieldsMap.put(ThirdPartyAccountFieldNames.NAME, userFieldConfig.getName());
        fieldsMap.put(ThirdPartyAccountFieldNames.LOGINID, userFieldConfig.getLoginId());
        return fieldsMap;
    }

    @Override
    protected Map<String, String> getPushOrgMapAttributes() {
        Map<String, String> fieldsMap = new HashMap<>(16);
        fieldsMap.put(ThirdPartyGroupFiledNames.NAME, groupFieldConfig.getName());
        return fieldsMap;
    }

    @Override
    protected List<String> getPushOrgFields() {
        return Arrays.asList(groupFieldConfig.getName());
    }

    @Override
    protected List<String> getPushUserFields() {
        return Arrays.asList(userFieldConfig.getLoginId(), userFieldConfig.getName());
    }
}
