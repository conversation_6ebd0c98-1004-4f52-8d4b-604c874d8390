package com.cyberscraft.uep.account.client.provider.wework.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class WeWorkBaseResponse {
    private int errcode;
    private String errmsg;

    public int getErrcode() {
        return errcode;
    }

    public void setErrcode(int errcode) {
        this.errcode = errcode;
    }

    public String getErrmsg() {
        return "vendor_error: "+errmsg;
    }

    public void setErrmsg(String errmsg) {
        this.errmsg = errmsg;
    }

    public boolean hasError() {
        return errcode > 0;
    }

    public boolean tokenInvalid() {
        return errcode == 40014;
    }

    public boolean userIdInvalid() {
        return errcode == 60111;
    }

    public boolean groupIdInvalid() {
        return errcode == 60123;
    }

//    public ConnectorException connectorException(){
//        if (tokenInvalid()){
//            return new InvalidTokenException(errcode, errmsg);
//        } else if (userIdInvalid()) {
//            return new InvalidUserIdException(errcode, errmsg);
//        } else if (groupIdInvalid()) {
//            return new InvalidGroupIdException(errcode, errmsg);
//        } else{
//            return new ConnectorException(errcode,errmsg);
//        }
//    }
}