package com.cyberscraft.uep.account.client.provider.wework.event;

import com.cyberscraft.uep.account.client.domain.EventBody;
import com.cyberscraft.uep.account.client.domain.ThirdPartyEvent;
import com.cyberscraft.uep.account.client.provider.wework.constant.WeWorkMessageType;
import com.cyberscraft.uep.account.client.provider.wework.domain.WeWorkCmdBody;
import com.cyberscraft.uep.account.client.provider.wework.domain.WeWorkMessage;
import com.cyberscraft.uep.account.client.provider.wework.service.IWeWorkCmdBodyParser;
import org.dom4j.Element;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/***
 * 微信事件消息请求对像解析实现类
 * @date 2021/3/24
 * <AUTHOR>
 ***/
@Component
public class WeWorkEventCmdBodyParser implements IWeWorkCmdBodyParser {

    /****
     *
     */
    private final static Map<String, IWeWorkEventParser> EVENT_PARSER_MAP = new ConcurrentHashMap<>();

    /***
     *
     */
    private final static Logger LOG = LoggerFactory.getLogger(WeWorkEventCmdBodyParser.class);

    /****
     *
     */
    private List<IWeWorkEventParser> parsers = new ArrayList<>();

    public List<IWeWorkEventParser> getParsers() {
        return parsers;
    }

    @Autowired
    public void setParsers(List<IWeWorkEventParser> parsers) {
        this.parsers = parsers;
        this.initEventParserMap();
    }

    /***
     *
     */
    private synchronized void initEventParserMap() {
        EVENT_PARSER_MAP.clear();
        if (this.parsers == null || this.parsers.size() == 0) {
            return;
        }
        for (IWeWorkEventParser parser : parsers) {
            Set<String> tags = parser.getSupportedTags();
            if (tags != null && tags.size() > 0) {
                for (String tag : tags) {
                    EVENT_PARSER_MAP.put(tag, parser);
                }
            }
        }
    }

    @Override
    public String getSupportedMsgType() {
        return WeWorkMessageType.EVENT.getCode();
    }

    @Override
    public WeWorkCmdBody parseCmdBody(WeWorkMessage msg, Element rootElement) {
        if (msg == null) {
            LOG.warn("处理微信事件消息时，微信事件消息对像为空");
            return null;
        }
        String tag = msg.getEvent() + "_" + msg.getChangeType();
        IWeWorkEventParser parser = EVENT_PARSER_MAP.get(tag);
        if (parser == null) {
            LOG.warn("处理微信事件消息时，未找到事件消息对应的解析器，事件类型：{}，EventType:{}", msg.getEvent(), msg.getChangeType());
            return null;
        }
        WeWorkCmdBody cmdBody = parser.parseCmdBody(msg, rootElement);
        if (cmdBody == null) {
            LOG.warn("处理微信事件消息时，将事件消息转换成第三方平台事件对像后为空，事件类型：{}，EventType:{}", msg.getEvent(), msg.getChangeType());
            return null;
        }
        return cmdBody;
    }
}
