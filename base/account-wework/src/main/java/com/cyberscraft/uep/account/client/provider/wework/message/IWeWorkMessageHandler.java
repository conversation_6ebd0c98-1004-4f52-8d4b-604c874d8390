package com.cyberscraft.uep.account.client.provider.wework.message;

import com.cyberscraft.uep.account.client.domain.Connector;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountException;
import com.cyberscraft.uep.account.client.provider.wework.domain.WeWorkConfig;
import com.cyberscraft.uep.account.client.provider.wework.domain.WeWorkMessage;

import java.util.Set;

/***
 * 微信消息处理类，根据不同的类型，转到不同的处理器
 * @date 2021/3/5
 * <AUTHOR>
 ***/
public interface IWeWorkMessageHandler {

    /****
     *
     * @return
     */
    Set<String> getSupportedMessageType();

    /***
     *
     * @param msg
     * @param <T>
     * @return
     * @throws ThirdPartyAccountException
     */
    <T> T handle(WeWorkMessage msg, WeWorkConfig config) throws ThirdPartyAccountException;
}
