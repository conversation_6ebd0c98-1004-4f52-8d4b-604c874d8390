package com.cyberscraft.uep.file.enums;

/**
 * <p>
 * 文件模块业务错误码定义
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2020-04-10 15:58
 */
public enum FileErrorCode {
    /**0-999 : 系统保留**/

    UPLOAD_PATH_NULL("1000", "upload path can not be empty"),
    UPLOAD_TO_OSS_ERROR("1001", "upload to oss error"),
    UPLOAD_TO_LOCAL_ERROR("1002", "upload to local error"),
    INPUTSTREAM_NULL("1003", "inputstream can not be empty");


    private String code;
    private String message;

    FileErrorCode(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public final String getCode() {
        return this.code;
    }

    public final String getMessage() {
        return this.message;
    }
}
