package com.cyberscraft.uep.file.condition;

import com.cyberscraft.uep.file.enums.DfsTypeEnum;
import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.env.Environment;
import org.springframework.core.type.AnnotatedTypeMetadata;

/***
 * 政务钉钉IM的文件客户端
 * @date 2021-12-14
 * <AUTHOR>
 ***/
public class GovDDFileCondition implements Condition {
    @Override
    public boolean matches(ConditionContext context, AnnotatedTypeMetadata annotatedTypeMetadata) {
        Environment environment = context.getEnvironment();
        //如果OSS,则代表OSS，否则代表使用本地文件系统
        //如果是使用本地文件系统则代表True，否则false
        if (DfsTypeEnum.GOVDD.getName().equalsIgnoreCase(environment.getProperty("dfs.type"))) {
            return true;
        }
        return false;
    }
}
