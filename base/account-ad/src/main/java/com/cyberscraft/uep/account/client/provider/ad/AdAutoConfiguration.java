package com.cyberscraft.uep.account.client.provider.ad;

import com.cyberscraft.uep.account.client.provider.IExternalConnectHandler;
import com.cyberscraft.uep.account.client.provider.IThirdPartyCallBackConfigHandler;
import com.cyberscraft.uep.account.client.provider.ad.handle.ad.AdCallBackConfigHandler;
import com.cyberscraft.uep.account.client.provider.ad.handle.ad.AdExternalConnectHandler;
import com.cyberscraft.uep.account.client.provider.ad.handle.ldap.LdapCallBackConfigHandler;
import com.cyberscraft.uep.account.client.provider.ad.handle.ldap.LdapExternalConnectHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;

/***
 *
 * @date 2021/4/7
 * <AUTHOR>
 ***/
@Configuration
@Order(value = Integer.MAX_VALUE)
//@ConditionalOnProperty(name = "ad.enabled", havingValue = "true")
public class AdAutoConfiguration {

    @Bean
    public IThirdPartyCallBackConfigHandler adCallBackConfigHandler() {
        AdCallBackConfigHandler service = new AdCallBackConfigHandler();
        return service;
    }

    @Bean
    public IThirdPartyCallBackConfigHandler LdapCallBackConfigHandler() {
        LdapCallBackConfigHandler service = new LdapCallBackConfigHandler();
        return service;
    }

    @Bean
    public IExternalConnectHandler adExternalConnectHandler() {
        AdExternalConnectHandler adExternalConnectHandler = new AdExternalConnectHandler();
        return adExternalConnectHandler;
    }

    @Bean
    public IExternalConnectHandler ldapExternalConnectHandler() {
        LdapExternalConnectHandler ldapExternalConnectHandler = new LdapExternalConnectHandler();
        return ldapExternalConnectHandler;
    }
}
