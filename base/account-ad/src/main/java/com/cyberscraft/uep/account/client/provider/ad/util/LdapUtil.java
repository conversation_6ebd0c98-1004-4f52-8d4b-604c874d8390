package com.cyberscraft.uep.account.client.provider.ad.util;

import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountException;
import com.unboundid.ldap.sdk.DN;
import com.unboundid.ldap.sdk.Filter;
import com.unboundid.ldap.sdk.LDAPException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/2/12 5:12 下午
 */
public class LdapUtil {
    private static final Logger logger = LoggerFactory.getLogger(LdapUtil.class);

    public static DN dn(String dn){
        try {
            return new DN(dn);
        } catch (LDAPException e) {
            logger.error("", e);
            throw new ThirdPartyAccountException(e);
        }
    }

    public static String andFilter(String... filters){
        List<Filter> filterList = Arrays.stream(filters).map(f -> {
            try {
                return Filter.create(f);
            } catch (LDAPException e) {
                throw new ThirdPartyAccountException(e);
            }
        }).collect(Collectors.toList());
        return Filter.createANDFilter(filterList).toNormalizedString();
    }

    public static String filter(String key, String value){
        return Filter.createEqualityFilter(key, value).toNormalizedString();
    }

    public static String filter(String key, byte[] value){
        return Filter.createEqualityFilter(key, value).toNormalizedString();
    }

    public static String createPresenceFilter(String ldapAttrObjectClass) {
        return Filter.createPresenceFilter(ldapAttrObjectClass).toNormalizedString();
    }
}
