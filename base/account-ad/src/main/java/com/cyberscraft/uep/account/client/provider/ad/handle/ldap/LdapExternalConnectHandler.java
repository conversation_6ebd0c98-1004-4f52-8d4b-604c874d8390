package com.cyberscraft.uep.account.client.provider.ad.handle.ldap;

import com.cyberscraft.uep.account.client.constant.AccountConstant;
import com.cyberscraft.uep.account.client.constant.ConnectorType;
import com.cyberscraft.uep.account.client.domain.*;
import com.cyberscraft.uep.account.client.exception.*;
import com.cyberscraft.uep.account.client.provider.IExternalConnectHandler;
import com.cyberscraft.uep.account.client.provider.ad.domain.LdapAndAdConfig;
import com.cyberscraft.uep.account.client.provider.ad.domain.LdapOrgAttr;
import com.cyberscraft.uep.account.client.provider.ad.domain.LdapUserAttr;
import com.cyberscraft.uep.account.client.provider.ad.util.LdapUtil;
import com.cyberscraft.uep.account.client.util.AccountUtil;
import com.cyberscraft.uep.common.domain.DataTypeEnum;
import com.cyberscraft.uep.common.domain.ParamSchema;
import com.cyberscraft.uep.common.exception.errorcode.MultiLanguageExceptionCodes;
import com.cyberscraft.uep.common.util.Base64Util;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.common.util.MD5Util;
import com.cyberscraft.uep.common.util.MappingParser;
import com.cyberscraft.uep.proxy.meta.service.IProxyMetaService;
import org.apache.commons.lang3.StringUtils;
import org.ldaptive.*;
import org.ldaptive.control.PagedResultsControl;
import org.ldaptive.control.ResponseControl;
import org.ldaptive.io.BooleanValueTranscoder;
import org.ldaptive.io.ObjectValueTranscoder;
import org.ldaptive.provider.ConnectionException;
import org.ldaptive.provider.unboundid.UnboundIDProvider;
import org.ldaptive.ssl.AllowAnyHostnameVerifier;
import org.ldaptive.ssl.AllowAnyTrustManager;
import org.ldaptive.ssl.SslConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.NumberFormat;
import java.text.ParseException;
import java.time.Duration;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/2/12 4:15 下午
 */
public class LdapExternalConnectHandler implements IExternalConnectHandler {

    protected final static Logger LOG = LoggerFactory.getLogger(LdapExternalConnectHandler.class);

    protected final static String ORG_CLASS = "organization";
    protected final static String PERSON_CLASS = "person";

    private final static String LDAP_ATTR_UUID = LdapOrgAttr.entryUUID.getAttrName();
    private final static String LDAP_ATTR_PARENT_UUID = LdapOrgAttr.parentEntryUUID.getAttrName();
    protected final static String LDAP_ATTR_OBJECT_CLASS = LdapOrgAttr.objectclass.getAttrName();

    private final static String LDAP_ATTR_USERNAME = LdapUserAttr.uid.getAttrName();

    public final static String USER_DEPARTMENT = LdapUserAttr.department_ids.getAttrName();

    private final ThreadLocal<Connection> connectionLocal = new ThreadLocal<>();

    @Autowired
    private IProxyMetaService proxyMetaService;

    private final ParamSchema orgBasicAttrSchema;
    private final ParamSchema userBasicAttrSchema;

    public LdapExternalConnectHandler() {
        orgBasicAttrSchema = new ParamSchema(AccountConstant.APPORG_PROFILE_ROOTNAME, DataTypeEnum.OBJECT, "部门profile")
                .addSubParam(LdapOrgAttr.objectclass.ps())
                .addSubParam(LdapOrgAttr.ou.ps())
                .addSubParam(LdapOrgAttr.dn.ps())
                .addSubParam(LdapOrgAttr.cn.ps())
                .addSubParam(LdapOrgAttr.entryUUID.ps())
                .addSubParam(LdapOrgAttr.parentEntryUUID.ps());

        userBasicAttrSchema = new ParamSchema(AccountConstant.APPUSER_PROFILE_ROOTNAME, DataTypeEnum.OBJECT, "用户profile")
                .addSubParam(LdapUserAttr.objectclass.ps())
                .addSubParam(LdapUserAttr.entryUUID.ps())
                .addSubParam(LdapUserAttr.uid.ps())
                .addSubParam(LdapUserAttr.userPassword.ps())
                .addSubParam(LdapUserAttr.dn.ps())
                .addSubParam(LdapUserAttr.cn.ps())
                .addSubParam(LdapUserAttr.telephoneNumber.ps())
                .addSubParam(LdapUserAttr.mail.ps())
                .addSubParam(LdapUserAttr.display_name.ps())
                .addSubParam(LdapUserAttr.department_ids.ps());
    }

    protected LdapAndAdConfig getLdapAndAdConfig(Connector connector) {
        LdapAndAdConfig ldapConfig = JsonUtil.str2Obj(connector.getConfig(), LdapAndAdConfig.class);
        String virtualIp = proxyMetaService.getVirtualIp(connector.getTenantId(), String.valueOf(connector.getProxyId()), ldapConfig.getHost());
        ldapConfig.setProxyIp(virtualIp);
        return ldapConfig;
    }

    protected ConnectionFactory getConnectionFactory(LdapAndAdConfig ldapConfig){
        ConnectionConfig c = toConnectionConfig(ldapConfig);
        c.setConnectionInitializer(new BindConnectionInitializer(
                ldapConfig.getAdminAccount(), new Credential(ldapConfig.getAdminPassword())));
        ConnectionFactory connectionFactory = new DefaultConnectionFactory(c, new UnboundIDProvider());
        return connectionFactory;
    }

    protected Connection getConnection(Connector connector){
        LdapAndAdConfig ldapConfig = getLdapAndAdConfig(connector);

        return getConnection(ldapConfig);
    }

    protected Connection getConnection(LdapAndAdConfig ldapConfig) {
        Connection connection =  connectionLocal.get();
        if (connection == null || !connection.isOpen()) {
            try {
                ldapConfig.setStartTls(!ldapConfig.getTls());
                ConnectionFactory connectionFactory = getConnectionFactory(ldapConfig);
                connection = connectionFactory.getConnection();
                connection.open();

                connectionLocal.set(connection);
            } catch (ConnectionException ex) {
                LOG.error("tls error:{}", ex.getMessage());

                int exCode = ex.getResultCode().value();

                if ((52 == exCode || 82 == exCode) && !ldapConfig.getTls()) {
                    LOG.warn("Set startTls to false and retry once");
                    try {
                        ldapConfig.setStartTls(ldapConfig.getTls());
                        ConnectionFactory connectionFactory = getConnectionFactory(ldapConfig);
                        connection = connectionFactory.getConnection();
                        connection.open();

                        connectionLocal.set(connection);
                    } catch (LdapException le) {
                        if (le.getResultCode() == ResultCode.INVALID_CREDENTIALS) {
                            throw new ThirdPartyAccountException(MultiLanguageExceptionCodes.AD_LOGIN_CREDENTIAL_INVALID);
                        }
                        throw new ThirdPartyAccountException(le);
                    } catch (Exception e) {
                        LOG.error("tls error:{}", e.getMessage());
                        throw new ThirdPartyAccountException(e);
                    }
                } else {
                    throw new ThirdPartyAccountException(MultiLanguageExceptionCodes.LDAP_CONNECT_FAIL);
                }
            } catch (LdapException le) {
                if (le.getResultCode() == ResultCode.INVALID_CREDENTIALS) {
                    throw new ThirdPartyAccountException(MultiLanguageExceptionCodes.AD_LOGIN_CREDENTIAL_INVALID);
                }
                throw new ThirdPartyAccountException(le);
            } catch (Exception e) {
                throw new ThirdPartyAccountException(e);
            }
        }
        return connection;
    }

    private ConnectionConfig toConnectionConfig(LdapAndAdConfig ldapConfig) {
        ConnectionConfig c = new ConnectionConfig();
        String proxyIp = ldapConfig.getProxyIp();
        String ldapUrl;
        if (ldapConfig.getTls()) {
            if (StringUtils.isNotBlank(proxyIp)) {
                ldapUrl = String.format("ldaps://%s:%d", proxyIp, ldapConfig.getPort());
            } else {
                ldapUrl = String.format("ldaps://%s:%d", ldapConfig.getHost(), ldapConfig.getPort());
            }
            //开启ldaps
            SslConfig sslConfig = new SslConfig(new AllowAnyTrustManager());
            sslConfig.setHostnameVerifier(new AllowAnyHostnameVerifier());
            c.setSslConfig(sslConfig);
        } else {
            if (StringUtils.isNotBlank(proxyIp)) {
                ldapUrl = String.format("ldap://%s:%d", proxyIp, ldapConfig.getPort());
            } else {
                ldapUrl = String.format("ldap://%s:%d", ldapConfig.getHost(), ldapConfig.getPort());
            }
            SslConfig sslConfig = new SslConfig(new AllowAnyTrustManager());
            sslConfig.setHostnameVerifier(new AllowAnyHostnameVerifier());
            c.setSslConfig(sslConfig);
        }
        c.setUseStartTLS(ldapConfig.getStartTls()); // reverse value from tls attr for startTls
        c.setLdapUrl(ldapUrl);
        //下面的超时时间可以放到前端配置
        c.setConnectTimeout(Duration.ofSeconds(60));
        c.setResponseTimeout(Duration.ofSeconds(60));
        return c;
    }

    protected String getLdapUuidAttributeName() {
        return LDAP_ATTR_UUID;
    }

    protected String getLdapUsernameAttributeName() {
        return LDAP_ATTR_USERNAME;
    }

    protected String getDefaultTestPath() {
        return LDAP_ATTR_USERNAME;
    }

    protected Function<LdapAttribute, String> getConnectorIdFunc() {
        return LdapAttribute::getStringValue;
    }

    protected String getLdapParentUuidAttrName() {
        return LDAP_ATTR_PARENT_UUID;
    }

    @Override
    public boolean isSupported(ConnectorType connectorType) {
        return ConnectorType.LDAP == connectorType;
    }

    protected String queryUuid(Connection connection, String dn) {
        try {
            LdapEntry entry = queryEntry(connection, dn, getLdapUuidAttributeName());
            if (entry != null) {
                return getConnectorIdFunc().apply(entry.getAttribute(getLdapUuidAttributeName()));
            } else {
                return null;
            }
        } catch (Exception e) {
            throw new ThirdPartyAccountException(e);
        }
    }

    protected LdapEntry queryEntry(Connection connection, String dn, final String... attrs) {
        SearchRequest searchRequest;
        if (attrs == null || attrs.length == 0) {
            searchRequest = new SearchRequest(
                    dn, LdapUtil.createPresenceFilter(LDAP_ATTR_OBJECT_CLASS));
        } else {
            searchRequest = new SearchRequest(
                    dn, LdapUtil.createPresenceFilter(LDAP_ATTR_OBJECT_CLASS), attrs);
        }
        try {
            searchRequest.setSearchScope(org.ldaptive.SearchScope.OBJECT);
            LdapEntry entry = querySingleEntry(connection, searchRequest);

            return entry;
        } catch (ThirdPartyAccountNotExistException ex) {
            return null;
        } catch (Exception e) {
            throw new ThirdPartyAccountException(e);
        }
    }

    protected LdapEntry queryEntryById(String baseDn, String entryId, Connection connection, String... attrs) {
        try {
            String filter = String.format("%s=%s", getLdapUuidAttributeName(), entryId);

            SearchRequest searchRequest = new SearchRequest(baseDn, filter, attrs);
            LdapEntry entry = querySingleEntry(connection, searchRequest);
            return entry;
        } catch (ThirdPartyAccountNotExistException ta) {
            throw ta;
        } catch (Exception e) {
            throw new ThirdPartyAccountException(e);
        }
    }

    protected LdapEntry queryEntryByAttr(String baseDn, String entryName, String entryValue, Connection connection, String... attrs) {
        try {
            String filter = String.format("%s=%s", entryName, entryValue);

            SearchRequest searchRequest = new SearchRequest(baseDn, filter, attrs);
            LdapEntry entry = querySingleEntry(connection, searchRequest);
            return entry;
        } catch (ThirdPartyAccountNotExistException ta) {
            throw ta;
        } catch (Exception e) {
            throw new ThirdPartyAccountException(e);
        }
    }

    protected String generateOrgDn(Connector connector, ConnectorOrg externalOrg) {
        String orgDn = (String)externalOrg.getValue(LdapOrgAttr.dn);
        if (StringUtils.isNotBlank(orgDn)) {
            return orgDn;
        }

        LdapAndAdConfig ldapConfig = getLdapAndAdConfig(connector);
        ConnectorOrgProfile externalOrgFullProfile = getExternalOrgFullProfile(connector);

        String parentDn = ldapConfig.getUserBaseDn();
        try {
            String parentIdValue = (String)externalOrg.getParentIdValue();
            if (StringUtils.isNotBlank(parentIdValue)) {
                try {
                    LdapEntry ldapEntry = queryEntryById(ldapConfig.getUserBaseDn(), parentIdValue, getConnection(ldapConfig));
                    parentDn = ldapEntry.getDn();
                } catch (ThirdPartyAccountNotExistException ex) {
                    LOG.warn("not find third party account");
                }
            }

            if (externalOrg.get(externalOrg.getNameKey()) == null) {
                throw new ThirdPartyAccountException("name property is null");
            }
            return externalOrgFullProfile.getNameName()+"="+externalOrg.getNameValue()+","+parentDn;
        } catch (Exception e) {
            throw new ThirdPartyAccountException(e);
        }
    }

    protected String generateUserDn(Connector connector, ConnectorUser externalUser) {
        String userDn = (String) externalUser.getValue(LdapUserAttr.dn);
        if (StringUtils.isNotBlank(userDn)) {
            return userDn;
        }

        LdapAndAdConfig ldapConfig = getLdapAndAdConfig(connector);
        String parentDn = ldapConfig.getUserBaseDn();
        try {
            String externalOrgId = null;
            Object externalOrgIds = externalUser.get(USER_DEPARTMENT);
            if (externalOrgIds instanceof List) {
                externalOrgId = ((List<?>) externalOrgIds).get(0).toString();
            } else {
                if (externalOrgIds != null) {
                    externalOrgId = externalOrgIds.toString();
                }
            }
            if (StringUtils.isNotBlank(externalOrgId)) {
                try {
                    LdapEntry ldapEntry = queryEntryById(ldapConfig.getUserBaseDn(), externalOrgId, getConnection(ldapConfig));
                    parentDn = ldapEntry.getDn();
                } catch (ThirdPartyAccountNotExistException ex) {
                    LOG.warn("not find third party account");
                }
            }

            String uidValue = (String)externalUser.getValue(LdapUserAttr.uid);
            if (StringUtils.isNotBlank(uidValue)) {
                return "uid="+uidValue+","+parentDn;
            } else {
                String cnValue = (String) externalUser.getValue(LdapUserAttr.cn);
                if (StringUtils.isBlank(cnValue)) {
                    throw new ThirdPartyAccountException("generate user dn fail, cn is blank");
                }
                return "cn="+cnValue+","+parentDn;
            }
        } catch (Exception e) {
            throw new ThirdPartyAccountException(e);
        }
    }

    protected LdapEntry querySingleEntry(Connection connection, SearchRequest searchRequest) throws LdapException {
        try {
            SearchOperation search = new SearchOperation(connection);
            Response<SearchResult> res = search.execute(searchRequest);

            if (res.getResult().getEntries() != null && res.getResult().getEntries().size()>1) {
                throw new ThirdPartyAccountException("查询条目不唯一");
            }
            LdapEntry entry = res.getResult().getEntry();
            if (entry == null) {
                throw new ThirdPartyAccountNotExistException("条目不存在");
            }
            return entry;
        } catch (LdapException e) {
            LOG.error("querySingleEntry error:{}", e.getMessage());
            if (e.getResultCode() == ResultCode.NO_SUCH_OBJECT) {
                throw new ThirdPartyAccountNotExistException(e.getMessage());
            }
            throw e;
        }
    }

    @Override
    public ParamSchema getExternalOrgBasicAttrSchema(Connector connector) {
        return orgBasicAttrSchema;
    }

    @Override
    public ParamSchema getExternalUserBasicAttrSchema(Connector connector) {
        return userBasicAttrSchema;
    }

    @Override
    public ConnectorOrgProfile getExternalOrgFullProfile(Connector connector) {
        ParamSchema orgAllProfile = buildExternalOrgFullSchema(connector);

        ConnectorOrgProfile connectorOrgProfile = new ConnectorOrgProfile();
        connectorOrgProfile.setIdName(LdapOrgAttr.entryUUID.getAttrName());
        connectorOrgProfile.setParentIdName(LdapOrgAttr.parentEntryUUID.getAttrName());
        connectorOrgProfile.setNameName(LdapOrgAttr.cn.getAttrName());

        Integer direction = connector.getDirection();
        ConnectorOrgMapping orgAttrMapping = connector.getOrgAttrMapping();

        ParamSchema orgMapSchema = orgAttrMapping.getToLocalMapping();
        if (direction == 0) {
            List<ParamSchema> subParams = orgMapSchema.getSubParams();
            for (ParamSchema subParam : subParams) {
                String name = subParam.getName();
                if ("name".equals(name)) {
                    String value = subParam.getValue();
                    if (StringUtils.isNotBlank(value)) {
                        String ouName = AccountConstant.APPORG_PROFILE_ROOTNAME + "." + LdapOrgAttr.ou.getAttrName();
                        String cnName = AccountConstant.APPORG_PROFILE_ROOTNAME + "." + LdapOrgAttr.cn.getAttrName();
                        if (value.equals(ouName)) {
                            connectorOrgProfile.setNameName(LdapOrgAttr.ou.getAttrName());
                        } else if (value.equals(cnName)) {
                            connectorOrgProfile.setNameName(LdapOrgAttr.cn.getAttrName());
                        } else {
                            connectorOrgProfile.setNameName(value);
                        }
                    }
                    break;
                }
            }
        } else if (direction == 1) {
            orgMapSchema = orgAttrMapping.getToExternalMapping();
            List<ParamSchema> subParams = orgMapSchema.getSubParams();
            for (ParamSchema subParam : subParams) {
                String name = subParam.getName();
                if (LdapOrgAttr.ou.getAttrName().equals(name)) {
                    String value = subParam.getValue();
                    if (StringUtils.isNotBlank(value)) {
                        connectorOrgProfile.setNameName(LdapOrgAttr.ou.getAttrName());
                        break;
                    }
                }
            }
        }

        connectorOrgProfile.setAttrSchema(orgAllProfile);

        return connectorOrgProfile;
    }

    @Override
    public ConnectorUserProfile getExternalUserFullProfile(Connector connector) {
        ParamSchema userAllProfile = buildExternalUserFullSchema(connector);

        ConnectorUserProfile connectorUserProfile = new ConnectorUserProfile();
        connectorUserProfile.setIdName(LdapUserAttr.entryUUID.getAttrName());
        connectorUserProfile.setUnionIdName(LdapUserAttr.entryUUID.getAttrName());
        connectorUserProfile.setMobileName(LdapUserAttr.telephoneNumber.getAttrName());
        connectorUserProfile.setAttrSchema(userAllProfile);

        return connectorUserProfile;
    }

    @Override
    public ConnectorOrg<String, Object> getRootExternalOrg(Connector connector) throws ThirdPartyAccountException {
        LdapAndAdConfig ldapConfig = getLdapAndAdConfig(connector);

        ConnectorOrgProfile externalOrgFullProfile = getExternalOrgFullProfile(connector);
        ParamSchema attrSchema = externalOrgFullProfile.getAttrSchema();

        try {
            //查询根节点
            SearchRequest searchRequest;
            Map<String, ParamSchema> profileMap = null;

            if (attrSchema != null) {
                List<ParamSchema> subParams = attrSchema.getSubParams();
                profileMap = subParams.stream().collect(Collectors.toMap(ParamSchema::getName, Function.identity()));
                searchRequest = new SearchRequest(
                        ldapConfig.getUserBaseDn(),
                        LdapUtil.createPresenceFilter(LDAP_ATTR_OBJECT_CLASS),
                        profileMap.keySet().toArray(new String[0]));
            } else {
                searchRequest = new SearchRequest(
                        ldapConfig.getUserBaseDn(),
                        LdapUtil.createPresenceFilter(LDAP_ATTR_OBJECT_CLASS));
            }
            searchRequest.setSearchScope(org.ldaptive.SearchScope.OBJECT);

            Connection connection = getConnection(ldapConfig);
            LdapEntry ldapEntry = querySingleEntry(connection, searchRequest);
            Map<String, Object> rootEntry = entryToExtralMapFunc(profileMap, ORG_CLASS).apply(ldapEntry);

            com.unboundid.ldap.sdk.DN dn = (com.unboundid.ldap.sdk.DN) rootEntry.get(LdapOrgAttr.dn.getAttrName());
            rootEntry.put(LdapOrgAttr.dn.getAttrName(), dn.toString());

            ConnectorOrg<String, Object> externalOrg = AccountUtil.to(rootEntry, externalOrgFullProfile);
            if (externalOrg.getNameValue() == null){
                externalOrg.setNameValue(dn.toString());
            }
            return externalOrg;
        } catch (LdapException e) {
            LOG.error("", e);
            throw new ThirdPartyAccountException(e);
        }
    }

    @Override
    public List<ConnectorOrg<String, Object>> getSubExternalOrgs(ConnectorOrg parentExternalOrg, Connector connector) throws ThirdPartyAccountException {
        return getSubExternalOrgs(parentExternalOrg, connector, SearchScope.ONELEVEL);
    }

    @Override
    public List<ConnectorOrg<String, Object>> getSubExternalOrgs(String parentOrgId, Connector connector) throws ThirdPartyAccountException {
        ConnectorOrg parentExternalOrg = getExternalOrgById(parentOrgId, connector);
        return getSubExternalOrgs(parentExternalOrg, connector);
    }

    @Override
    public List<ConnectorOrg<String, Object>> getExternalOrgByIds(List<String> externalOrgIds, Connector connector) throws ThirdPartyAccountException {
        return null;
    }

    @Override
    public List<ConnectorUser<String, Object>> getExternalUsers(List<String> externalUserIds, Connector connector) throws ThirdPartyAccountException {
        return null;
    }

    @Override
    public Boolean listExternalUsersByLimit(Connector connector) {
        return false;
    }

    @Override
    public List<ConnectorOrg<String, Object>> getAllSubExternalOrgs(ConnectorOrg parentExternalOrg,Connector connector) throws ThirdPartyAccountException {
        return getSubExternalOrgs(parentExternalOrg, connector, SearchScope.SUBTREE);
    }

    private List<ConnectorOrg<String, Object>> getSubExternalOrgs(ConnectorOrg parentExternalOrg,Connector connector, SearchScope scope) throws ThirdPartyAccountException {
        LdapAndAdConfig ldapConfig = getLdapAndAdConfig(connector);

        ConnectorOrgProfile externalOrgFullProfile = getExternalOrgFullProfile(connector);
        ParamSchema attrSchema = externalOrgFullProfile.getAttrSchema();

        try {
            String orgDn = (String) parentExternalOrg.get(LdapOrgAttr.dn.getAttrName());

            //查询所有子节点
            SearchRequest searchRequest;
            Map<String, ParamSchema> profileMap = null;

            if (attrSchema != null) {
                List<ParamSchema> subParams = attrSchema.getSubParams();
                profileMap = subParams.stream().collect(Collectors.toMap(ParamSchema::getName, Function.identity()));
                searchRequest = new SearchRequest(
                        orgDn,
                        ldapConfig.getOrgFilter(),
                        profileMap.keySet().toArray(new String[0]));
            } else {
                searchRequest = new SearchRequest(
                        orgDn,
                        ldapConfig.getOrgFilter());
            }
            searchRequest.setSearchScope(scope);

            Connection connection = getConnection(ldapConfig);

            List<Map<String, Object>> list = queryEntries(connection, searchRequest, entryToExtralMapFunc(profileMap, ORG_CLASS));

            list = list.stream().filter(e -> !orgDn.equalsIgnoreCase(e.get(LdapOrgAttr.dn.getAttrName()).toString())).collect(Collectors.toList());

            Map<Object, Map<String, Object>> dnMap = list.stream()
                    .collect(Collectors.toMap(e -> e.get(LdapOrgAttr.dn.getAttrName()), Function.identity(), (v1, v2) -> v1));

            //设置parentId
            list.forEach(e -> {
                com.unboundid.ldap.sdk.DN dn = (com.unboundid.ldap.sdk.DN) e.get(LdapOrgAttr.dn.getAttrName());
                Map<String, Object> parent = dnMap.get(dn.getParent());
                if (parent != null) {
                    e.put(getLdapParentUuidAttrName(), parent.get(getLdapUuidAttributeName()));
                } else {
                    e.put(getLdapParentUuidAttrName(), parentExternalOrg.get(getLdapUuidAttributeName()));
                }
            });

            List<ConnectorOrg<String, Object>> result = new ArrayList<>();

            list.forEach(e -> {
                com.unboundid.ldap.sdk.DN dn = (com.unboundid.ldap.sdk.DN) e.get(LdapOrgAttr.dn.getAttrName());
                e.put(LdapOrgAttr.dn.getAttrName(), dn.toString());

                result.add(AccountUtil.to(e, externalOrgFullProfile));
            });

            return result;
        } catch (LdapException e) {
            LOG.error("", e);
            throw new ThirdPartyAccountException(e);
        }
    }

    protected List<Map<String,Object>> queryEntries(Connection connection, SearchRequest searchRequest, Function<LdapEntry, Map<String,Object>> entryToMapConverter) throws LdapException {
        try {
            SearchOperation search = new SearchOperation(connection);
            Response<SearchResult> res = search.execute(searchRequest);
            return res.getResult().getEntries().stream().map(entryToMapConverter).collect(Collectors.toList());
        } catch (LdapException e) {
            LOG.error("", e);
            throw e;
        }
    }

    protected static void addEntry(Connection connection, AddRequest addRequest) throws LdapException{
        try {
            AddOperation addOperation = new AddOperation(connection);
            Response<Void> execute = addOperation.execute(addRequest);
        } catch (LdapException e) {
            LOG.error("addEntry error", e);
            throw e;
        }
    }

    protected static void updateEntry(Connection connection, ModifyRequest modifyRequest) throws LdapException{
        try {
            ModifyOperation modifyOperation = new ModifyOperation(connection);
            Response<Void> execute = modifyOperation.execute(modifyRequest);
        } catch (LdapException e) {
            LOG.error("updateEntry error", e);
            throw e;
        }
    }

    protected static void modifyDn(Connection connection, String oldDn, String newDn) throws LdapException{
        try {
            ModifyDnRequest modifyDnRequest = new ModifyDnRequest(oldDn, newDn);
            ModifyDnOperation modifyDnOperation = new ModifyDnOperation(connection);
            Response<Void> execute = modifyDnOperation.execute(modifyDnRequest);
        } catch (LdapException e) {
            LOG.error("modifyDn error", e);
            throw e;
        }
    }

    protected static void deleteEntry(Connection connection, String dn) throws LdapException{
        try {
            DeleteRequest deleteRequest = new DeleteRequest(dn);
            DeleteOperation deleteOperation = new DeleteOperation(connection);
            Response<Void> execute = deleteOperation.execute(deleteRequest);
        } catch (LdapException e) {
            LOG.error("deleteEntry error:{}", e.getMessage());
            if (e.getResultCode() == ResultCode.NO_SUCH_OBJECT) {
                throw new ThirdPartyAccountNotExistException(e.getMessage());
            }
            throw e;
        }
    }

    protected Function<LdapEntry, Map<String, Object>> entryToExtralMapFunc(Map<String, ParamSchema> profileMap, String objectClass) {
        return (LdapEntry e) -> {
            Map<String, Object> map = new HashMap<>();
            if (ORG_CLASS.equals(objectClass)) {
                map.put(LdapOrgAttr.dn.getAttrName(), LdapUtil.dn(e.getDn()));
            } else {
                map.put(LdapUserAttr.dn.getAttrName(), e.getDn());
            }

            for (LdapAttribute a : e.getAttributes()) {
                if (a.getName().equals(getLdapUuidAttributeName())) {
                    map.put(getLdapUuidAttributeName(), getConnectorIdFunc().apply(a));
                    continue;
                }

                if (map.containsKey(LdapOrgAttr.dn.getAttrName()) && a.getName().equals(LdapOrgAttr.dn.getAttrName())) {
                    continue;
                }
                if (map.containsKey(LdapUserAttr.dn.getAttrName()) && a.getName().equals(LdapUserAttr.dn.getAttrName())) {
                    continue;
                }

                String connectorLdapName = a.getName();
                ParamSchema paramSchema = null;
                if (profileMap != null) {
                    paramSchema = profileMap.get(connectorLdapName);
                    if (paramSchema == null) {
                        LOG.warn("unknown profile name for LDAP attribute: [{}]", connectorLdapName);
                        continue;
                    }
                }

                ldapAttrMap(map, a, paramSchema);
            }
            return map;
        };
    }

    private void ldapAttrMap(Map<String, Object> map, LdapAttribute a, ParamSchema paramProfile) {
        if (paramProfile == null) {
            map.put(a.getName(), a.getStringValue());
            return;
        }
        String name = paramProfile.getName();
        DataTypeEnum type = paramProfile.getType();
        switch (type) {
            case NUMBER:
                if (a.isBinary()) {
                    LOG.warn("datatype {} not supported while converting {} from ldap", type, name);
                    break;
                }
                try {
                    map.put(name, NumberFormat.getInstance().parse(a.getStringValue()));
                } catch (ParseException e) {
                    LOG.warn("datatype {} not supported while converting {} from ldap", type, name);
                }
                break;
            case BOOLEAN:
                if (a.isBinary()) {
                    LOG.warn("datatype {} not supported while converting {} from ldap", type, name);
                    break;
                }
                map.put(name, a.getValue(new BooleanValueTranscoder()));
                break;
            case STRING:
                if (a.isBinary()) {
                    map.put(name, a.getStringValue());
                } else {
                    map.put(name, a.getStringValue());
                }
                break;
            case OBJECT:
                if (a.isBinary()) {
                    LOG.warn("datatype {} not supported while converting {} from ldap", type, name);
                    break;
                }
                map.put(name, a.getValue(new ObjectValueTranscoder()));
                break;
            default:
                LOG.warn("datatype {} not supported while converting {} from ldap", type, name);
                break;
        }
    }

    protected List<LdapAttribute> toLdapAttributes(Map<String, Object> map) {
        List<LdapAttribute> result = new ArrayList<>();
        Set<String> keySet = map.keySet();
        for (String name : keySet) {
            if ("dn".equalsIgnoreCase(name)) {
                continue;
            }
            if (getLdapUuidAttributeName().equalsIgnoreCase(name)) {
                continue;
            }
            if (getLdapParentUuidAttrName().equalsIgnoreCase(name)) {
                continue;
            }
            if (USER_DEPARTMENT.equalsIgnoreCase(name)) {
                continue;
            }
            Object o = map.get(name);
            if (o != null) {
                if (o instanceof byte[]) {
                    result.add(new LdapAttribute(name, (byte[]) o));
                } else if (o instanceof List) {
                    result.add(new LdapAttribute(name, ((List<?>) o).toArray(new String[0])));
                } else if (o instanceof Number) {
                    result.add(new LdapAttribute(name, o.toString()));
                } else if (o instanceof Boolean) {
                    result.add(new LdapAttribute(name, o.toString()));
                } else if (o instanceof String) {
                    result.add(new LdapAttribute(name, o.toString()));
                } else {
                    result.add(new LdapAttribute(name, JsonUtil.obj2Str(o)));
                }
            }
        }
        return result;
    }

    @Override
    public boolean isSortedByOrgLevel() {
        return false;
    }

    @Override
    public ConnectorOrg getExternalOrgById(String externalOrgId, Connector connector) throws ThirdPartyAccountException {
        LdapAndAdConfig ldapConfig = getLdapAndAdConfig(connector);
        try {
            ConnectorOrgProfile externalOrgFullProfile = getExternalOrgFullProfile(connector);
            ParamSchema attrSchema = externalOrgFullProfile.getAttrSchema();

            Map<String, ParamSchema> profileMap = null;
            if (attrSchema != null) {
                List<ParamSchema> subParams = attrSchema.getSubParams();
                profileMap = subParams.stream().collect(Collectors.toMap(ParamSchema::getName, Function.identity()));
            }

            LdapEntry ldapEntry = queryEntryById(ldapConfig.getUserBaseDn(), externalOrgId, getConnection(ldapConfig), profileMap.keySet().toArray(new String[0]));

            Map<String, Object> entry = entryToExtralMapFunc(profileMap, ORG_CLASS).apply(ldapEntry);
            entry.put(LdapOrgAttr.dn.getAttrName(), entry.get(LdapOrgAttr.dn.getAttrName()).toString());
            return AccountUtil.to(entry, externalOrgFullProfile);
        } catch (ThirdPartyAccountNotExistException ta) {
            throw new ThirdPartyGroupNotExistException(ThirdPartyAccountErrorType.GROUP_NOT_EXIST.getMessage());
        } catch (Exception e) {
            LOG.error("", e);
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.GROUP_NOT_EXIST);
        }
    }

    @Override
    public List<ConnectorUser<String, Object>> getExternalUsersByOrg(ConnectorOrg externalOrg, Connector connector) throws ThirdPartyAccountException {
        LdapAndAdConfig ldapConfig = getLdapAndAdConfig(connector);

        ConnectorUserProfile externalUserFullProfile = getExternalUserFullProfile(connector);
        ParamSchema externalAttrSchema = externalUserFullProfile.getAttrSchema();

        String orgDn = (String) externalOrg.get(LdapOrgAttr.dn.getAttrName());

        try {
            //查询所有某个组织结构下的用户列表
            SearchRequest searchRequest;
            Map<String, ParamSchema> profileMap = null;

            if (externalAttrSchema != null) {
                List<ParamSchema> subParams = externalAttrSchema.getSubParams();
                profileMap = subParams.stream().collect(Collectors.toMap(ParamSchema::getName, Function.identity()));
                searchRequest = new SearchRequest(
                        orgDn,
                        ldapConfig.getUserFilter(),
                        profileMap.keySet().toArray(new String[0]));
            } else {
                searchRequest = new SearchRequest(
                        orgDn,
                        ldapConfig.getUserFilter());
            }
            searchRequest.setSearchScope(org.ldaptive.SearchScope.ONELEVEL);

            PagedResultsControl pagedResultsControl = new PagedResultsControl(500);
            searchRequest.setControls(pagedResultsControl);

            List<Map<String, Object>> results = new ArrayList<>();
            byte[] cookie = null;
            do {
                SearchOperation search = new SearchOperation(getConnection(ldapConfig));
                Response<SearchResult> response = search.execute(searchRequest);

                ResponseControl[] responseControls = response.getControls();
                for (ResponseControl control : responseControls) {
                    if (control instanceof PagedResultsControl) {
                        cookie = ((PagedResultsControl) control).getCookie();
                        searchRequest.setControls(new PagedResultsControl(500, cookie, false));
                        break;
                    }
                }

                List<Map<String, Object>> pageResult = response.getResult().getEntries().stream().map(entryToExtralMapFunc(profileMap, PERSON_CLASS)).collect(Collectors.toList());
                results.addAll(pageResult);

                // If cookie is null or empty, we've reached the end
                if (cookie == null || cookie.length == 0) {
                    break;
                }

            } while (cookie != null && cookie.length > 0);

            results.forEach(e->e.put(USER_DEPARTMENT, Arrays.asList(externalOrg.getIdValue())));
            return AccountUtil.to(results, externalUserFullProfile);
        } catch (Exception e) {
            throw new ThirdPartyAccountException(e);
        }
    }

    @Override
    public Boolean limitListExternalUsers(int page, int pageSize, Connector connector, List<ConnectorUser<String, Object>> output) throws ThirdPartyAccountException {
        return null;
    }

    @Override
    public ConnectorUser getExternalUserById(String externalAccountId, Connector connector) throws ThirdPartyAccountException {
        LdapAndAdConfig ldapConfig = getLdapAndAdConfig(connector);
        try {
            ConnectorUserProfile externalUserFullProfile = getExternalUserFullProfile(connector);
            ParamSchema externalAttrSchema = externalUserFullProfile.getAttrSchema();

            Map<String, ParamSchema> profileMap = null;
            if (externalAttrSchema != null) {
                List<ParamSchema> externalSubParams = externalAttrSchema.getSubParams();
                profileMap = externalSubParams.stream().collect(Collectors.toMap(ParamSchema::getName, Function.identity()));
            }

            LdapEntry ldapEntry = queryEntryById(ldapConfig.getUserBaseDn(), externalAccountId, getConnection(ldapConfig), profileMap.keySet().toArray(new String[0]));

            Map<String, Object> entry = entryToExtralMapFunc(profileMap, ORG_CLASS).apply(ldapEntry);
            entry.put(LdapOrgAttr.dn.getAttrName(), entry.get(LdapOrgAttr.dn.getAttrName()).toString());
            return AccountUtil.to(entry, externalUserFullProfile);
        } catch (ThirdPartyAccountNotExistException ta) {
            throw ta;
        } catch (Exception e) {
            LOG.error("", e);
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.USER_NOT_EXIST);
        }
    }

    private Map<String, Object> getExternalUser(String attrName, String attrValue, LdapAndAdConfig ldapConfig, Map<String, ParamSchema> profileMap) {
        try {
            String filter = LdapUtil.andFilter(ldapConfig.getUserFilter(), LdapUtil.filter(attrName, attrValue));
            SearchRequest searchRequest;
            if (profileMap != null) {
                searchRequest = new SearchRequest(ldapConfig.getUserBaseDn(), filter, profileMap.keySet().toArray(new String[0]));
            } else {
                searchRequest = new SearchRequest(ldapConfig.getUserBaseDn(), filter);
            }

            Connection connection = getConnection(ldapConfig);
            List<Map<String,Object>> list = queryEntries(connection, searchRequest, entryToExtralMapFunc(profileMap, PERSON_CLASS));
            if (list.size() > 0) {
                Map<String, Object> map = list.get(0);
                //此处本应获取用户对应的部门id，但因为AD/LDAP没有变动事件通知，所以这个业务走不到这儿来
//                String dn = (String)map.get(LdapUserAttr.dn.getAttrName());
//                String departmentDn = LdapUtil.dn(dn).getParent().toString();
//                String departUuid = queryUuid(connection, departmentDn);
//                map.put(USER_DEPARTMENT, departUuid);
                return map;
            }
            LOG.error("user not found, name:{}, value:{}", attrName, attrValue);
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.ACCOUNT_NOT_EXIST);
        } catch (LdapException e) {
            LOG.error("", e);
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.ACCOUNT_NOT_EXIST);
        }
    }

    @Override
    public int login(Connector connector, String username, String password) {
        try {
            Connection conn = getConnection(connector);
            BindOperation bind = new BindOperation(conn);
            bind.execute(new BindRequest(username, new Credential(password)));
        } catch (LdapException e) {
            LOG.info("User {} binding request fail {}", username, e.getResultCode());
            return 1;
        }
        return 0;
    }

    @Override
    public ConnectorUser test(Connector connector) {
        LdapAndAdConfig ldapConfig = getLdapAndAdConfig(connector);

        ConnectorUserProfile externalUserFullProfile = getExternalUserFullProfile(connector);
        ParamSchema externalAttrSchema = externalUserFullProfile.getAttrSchema();

        Map<String, ParamSchema> profileMap = null;
        if (externalAttrSchema != null) {
            List<ParamSchema> externalSubParams = externalAttrSchema.getSubParams();
            profileMap = externalSubParams.stream().collect(Collectors.toMap(ParamSchema::getName, Function.identity()));
        }

        String testAccount = connector.getTestAccount();
        String testPath = connector.getTestPath();
        String testKey;
        if (externalAttrSchema != null && StringUtils.isNotBlank(testPath)) {
            ParamSchema testParamSchema = MappingParser.getSubParamSchema(externalAttrSchema, testPath);
            testKey = testParamSchema.getName();
        } else {
            testPath = getDefaultTestPath();
            String[] testPaths = testPath.split("\\.");
            testKey = testPaths[testPaths.length - 1];
        }

        Map<String, Object> externalUser = getExternalUser(testKey, testAccount, ldapConfig, profileMap);
        return AccountUtil.to(externalUser, externalUserFullProfile);
    }

    @Override
    public void closeConnection(Connector connector) {
        Connection connection = connectionLocal.get();
        if (connection != null) {
            connectionLocal.remove();
            connection.close();
        }
    }

    @Override
    public Boolean fullyModify(Connector connector) {
        return true;
    }

    @Override
    public String createExternalOrg(ConnectorOrg externalOrg, Connector connector) throws ThirdPartyAccountException {
        try {
            String dn = generateOrgDn(connector, externalOrg);

            LdapAndAdConfig ldapConfig = getLdapAndAdConfig(connector);

            ConnectorOrgProfile externalOrgFullProfile = getExternalOrgFullProfile(connector);
            ParamSchema attrSchema = externalOrgFullProfile.getAttrSchema();
            List<ParamSchema> subParams = attrSchema.getSubParams();
            Map<String, ParamSchema> profileMap = subParams.stream().collect(Collectors.toMap(ParamSchema::getName, Function.identity()));

            Connection connection = getConnection(ldapConfig);
            LdapEntry entry = queryEntry(connection, dn, profileMap.keySet().toArray(new String[0]));

            String uuid;
            if (entry == null) {
                createExternalOrg(connection, dn, externalOrg);
                uuid = queryUuid(connection, dn);
            } else {
                uuid = getConnectorIdFunc().apply(entry.getAttribute(getLdapUuidAttributeName()));
                throw new ThirdPartyGroupExistException(ResultCode.ENTRY_ALREADY_EXISTS.name(), "ldap entry is exist", uuid);
//                modifyExternalOrg(connector, connection, entry, externalOrg);
            }

            if (StringUtils.isBlank(uuid)) {
                throw new ThirdPartyAccountException("create external org failed");
            }
            externalOrg.setIdValue(uuid);
            return uuid;
        } catch (ThirdPartyGroupExistException e) {
            throw e;
        } catch (Exception ex) {
            LOG.error("", ex);
            throw new ThirdPartyAccountException(ex.getMessage());
        }
    }

    protected void createExternalOrg(Connection connection, String dn, ConnectorOrg externalOrg) {
        try {
            List<LdapAttribute> attrs = toLdapAttributes(externalOrg);
            if (!externalOrg.containsKey(LDAP_ATTR_OBJECT_CLASS)) {
                attrs.add(new LdapAttribute(LDAP_ATTR_OBJECT_CLASS, getOrgObjectClass()));
            }

            AddRequest addRequest = new AddRequest(dn, attrs);
            addEntry(connection, addRequest);
        } catch (LdapException ex) {
            if (ex.getResultCode() == ResultCode.ENTRY_ALREADY_EXISTS) {
                String uuid = queryUuid(connection, dn);
                throw new ThirdPartyGroupExistException(ResultCode.ENTRY_ALREADY_EXISTS.name(), ex.getMessage(), uuid);
            } else {
                throw new ThirdPartyAccountException(ex.getMessage());
            }
        }
    }

    @Override
    public String modifyExternalOrg(String externalOrgId, ConnectorOrg externalOrg, Connector connector) throws ThirdPartyAccountException {
        externalOrg.setIdValue(externalOrgId);
        LdapAndAdConfig ldapConfig = getLdapAndAdConfig(connector);

        ConnectorOrgProfile externalOrgFullProfile = getExternalOrgFullProfile(connector);
        ParamSchema attrSchema = externalOrgFullProfile.getAttrSchema();

        try {
            List<ParamSchema> subParams = attrSchema.getSubParams();
            Map<String, ParamSchema> profileMap = subParams.stream().collect(Collectors.toMap(ParamSchema::getName, Function.identity()));

            Connection connection = getConnection(ldapConfig);
            LdapEntry ldapEntry = queryEntryById(ldapConfig.getUserBaseDn(), externalOrgId, connection, profileMap.keySet().toArray(new String[0]));

            modifyExternalOrg(connector, connection, ldapEntry, externalOrg);
            return externalOrgId;
        } catch (ThirdPartyAccountNotExistException ta) {
            throw new ThirdPartyGroupNotExistException(ThirdPartyAccountErrorType.GROUP_NOT_EXIST.getMessage());
        } catch (Exception ex) {
            LOG.error("", ex);
            throw new ThirdPartyAccountException(ex.getMessage());
        }
    }

    private void deleteSameItem(Map<String,Object> oldMap, Map<String,Object> newMap) {

        Set<String> keySet = oldMap.keySet();
        for (String key : keySet) {
            Object o = oldMap.get(key);
            if (newMap.containsKey(key) && Objects.equals(o, newMap.get(key))) {
                newMap.remove(key);
            }
        }
    }
    protected void adjustModifyOrg(Map<String, Object> clone) {
        clone.remove(LdapOrgAttr.ou.getAttrName());
        clone.remove(LdapUserAttr.dn.getAttrName());
    }

    protected void modifyExternalOrg(Connector connector, Connection connection, LdapEntry ldapEntry, ConnectorOrg externalOrg) {
        try {
            String oldDn = ldapEntry.getDn();
            String newDn = generateOrgDn(connector, externalOrg);
            if (!oldDn.equalsIgnoreCase(newDn)) {
                modifyDn(connection, oldDn, newDn);
            }

            ConnectorOrgProfile externalOrgFullProfile = getExternalOrgFullProfile(connector);
            ParamSchema attrSchema = externalOrgFullProfile.getAttrSchema();

            Map<String, ParamSchema> profileMap = null;
            if (attrSchema != null) {
                List<ParamSchema> subParams = attrSchema.getSubParams();
                profileMap = subParams.stream().collect(Collectors.toMap(ParamSchema::getName, Function.identity()));
            }

            Map<String, Object> entry = entryToExtralMapFunc(profileMap, ORG_CLASS).apply(ldapEntry);
            ConnectorOrg<String, Object> oldExternalOrg = AccountUtil.to(entry, externalOrgFullProfile);

            Map<String, Object> clone = (Map<String, Object>) externalOrg.clone();
            deleteSameItem(oldExternalOrg, clone);
            adjustModifyOrg(clone);

            List<LdapAttribute> attrs = toLdapAttributes(clone);
            if (attrs.isEmpty()) {
                return;
            }
            final List<AttributeModification> resultModifications = new ArrayList<>(attrs.size());
            for (LdapAttribute attr : attrs) {
                AttributeModification attributeModification = new AttributeModification(AttributeModificationType.REPLACE, attr);
                resultModifications.add(attributeModification);
            }

            ModifyRequest modifyRequest = new ModifyRequest(newDn, resultModifications.toArray(new AttributeModification[0]));
            updateEntry(connection, modifyRequest);
        } catch (LdapException ex) {
            LOG.error("", ex);
            throw new ThirdPartyAccountException(ex.getMessage());
        }
    }

    @Override
    public void deleteExternalOrg(String externalOrgId, Connector connector) throws ThirdPartyAccountException {
        LdapAndAdConfig ldapConfig = getLdapAndAdConfig(connector);
        try {
            Connection connection = getConnection(ldapConfig);
            LdapEntry ldapEntry = queryEntryById(ldapConfig.getUserBaseDn(), externalOrgId, connection);
            String dn = ldapEntry.getDn();

            deleteEntry(connection, dn);
        } catch (ThirdPartyAccountNotExistException tx) {
            LOG.info("external org is not exist: {}", externalOrgId);
        } catch (Exception ex) {
            LOG.error("", ex);
            throw new ThirdPartyAccountException(ex.getMessage());
        }
    }

    @Override
    public void deleteExternalOrgs(List<String> externalOrgIds, Connector connector) throws ThirdPartyAccountException {
        for (String externalOrgId : externalOrgIds) {
            deleteExternalOrg(externalOrgId, connector);
        }
    }

    protected String[] getOrgObjectClass() {
        return new String[]{"top", "organizationalUnit"};
    }

    protected String[] getUserObjectClass() {
        return new String[]{"top", "organizationalPerson"};
    }

    @Override
    public String createExternalUser(ConnectorUser externalUser, Connector connector) throws ThirdPartyAccountException {
        try {
            String dn = generateUserDn(connector, externalUser);

            LdapAndAdConfig ldapConfig = getLdapAndAdConfig(connector);
            ConnectorUserProfile externalUserFullProfile = getExternalUserFullProfile(connector);
            ParamSchema attrSchema = externalUserFullProfile.getAttrSchema();
            List<ParamSchema> subParams = attrSchema.getSubParams();
            Map<String, ParamSchema> profileMap = subParams.stream().collect(Collectors.toMap(ParamSchema::getName, Function.identity()));

            Connection connection = getConnection(ldapConfig);
            LdapEntry entry = queryEntry(connection, dn, profileMap.keySet().toArray(new String[0]));
            String uuid;

            if (entry == null) {
                createExternalUser(connection, ldapConfig.getUserBaseDn(), dn, externalUser);
                uuid = queryUuid(connection, dn);
            } else {
                uuid = getConnectorIdFunc().apply(entry.getAttribute(getLdapUuidAttributeName()));
//                modifyExternalUser(connector, connection, entry, externalUser);
                throw new ThirdPartyAccountExistException(uuid, "ldap entry is exist");
            }

            if (StringUtils.isBlank(uuid)) {
                throw new ThirdPartyAccountException("create external user failed");
            }
            externalUser.setIdValue(uuid);
            return uuid;
        } catch (ThirdPartyAccountExistException e) {
            throw e;
        } catch (Exception ex) {
            LOG.error("", ex);
            throw new ThirdPartyAccountException(ex.getMessage());
        }
    }

    protected void createExternalUser(Connection connection, String baseDn, String dn, ConnectorUser externalUser) {
        try {
            List<LdapAttribute> attrs = toLdapAttributes(externalUser);
            if (!externalUser.containsKey(LDAP_ATTR_OBJECT_CLASS)) {
                attrs.add(new LdapAttribute(LDAP_ATTR_OBJECT_CLASS, getUserObjectClass()));
            }

            AddRequest addRequest = new AddRequest(dn, attrs);
            addEntry(connection, addRequest);
        } catch (LdapException ex) {
            if (ex.getResultCode() == ResultCode.ENTRY_ALREADY_EXISTS) {
                LdapEntry entry;
                try {
                    entry = queryEntryByAttr(baseDn, getLdapUsernameAttributeName(), (String) externalUser.get(getLdapUsernameAttributeName()), connection);
                } catch (ThirdPartyAccountNotExistException e) {
                    throw new ThirdPartyAccountException("同步范围外存在相同条目");
                }

                if (entry != null) {
                    String uuid = getConnectorIdFunc().apply(entry.getAttribute(getLdapUuidAttributeName()));
                    if (StringUtils.isNotBlank(uuid)) {
                        throw new ThirdPartyAccountExistException(uuid, ex.getMessage());
                    } else {
                        throw new ThirdPartyAccountException(ex.getMessage());
                    }
                }

            } else {
                throw new ThirdPartyAccountException(ex.getMessage());
            }
        }
    }

    @Override
    public String modifyExternalUser(String externalUserId, ConnectorUser externalUser, Connector connector) throws ThirdPartyAccountException {
        externalUser.setIdValue(externalUserId);
        LdapAndAdConfig ldapConfig = getLdapAndAdConfig(connector);
        try {
            ConnectorUserProfile externalUserFullProfile = getExternalUserFullProfile(connector);
            ParamSchema attrSchema = externalUserFullProfile.getAttrSchema();
            List<ParamSchema> subParams = attrSchema.getSubParams();
            Map<String, ParamSchema> profileMap = subParams.stream().collect(Collectors.toMap(ParamSchema::getName, Function.identity()));

            Connection connection = getConnection(ldapConfig);
            LdapEntry ldapEntry = queryEntryById(ldapConfig.getUserBaseDn(), externalUserId, connection, profileMap.keySet().toArray(new String[0]));

            modifyExternalUser(connector, connection, ldapEntry, externalUser);
            return externalUserId;
        } catch (ThirdPartyAccountNotExistException te) {
            throw te;
        } catch (Exception ex) {
            LOG.error("", ex);
            throw new ThirdPartyAccountException(ex.getMessage());
        }
    }

    protected void adjustModifyUser(Map<String, Object> clone) {
        clone.remove(LdapUserAttr.dn.getAttrName());
    }

    protected void modifyExternalUser(Connector connector, Connection connection, LdapEntry ldapEntry, ConnectorUser externalUser) {
        try {
            String oldDn = ldapEntry.getDn();
            String newDn = generateUserDn(connector, externalUser);
            if (!oldDn.equalsIgnoreCase(newDn)) {
                modifyDn(connection, oldDn, newDn);
            }

            ConnectorUserProfile externalUserFullProfile = getExternalUserFullProfile(connector);
            ParamSchema attrSchema = externalUserFullProfile.getAttrSchema();

            Map<String, ParamSchema> profileMap = null;
            if (attrSchema != null) {
                List<ParamSchema> subParams = attrSchema.getSubParams();
                profileMap = subParams.stream().collect(Collectors.toMap(ParamSchema::getName, Function.identity()));
            }

            Map<String, Object> entry = entryToExtralMapFunc(profileMap, PERSON_CLASS).apply(ldapEntry);
            ConnectorUser<String, Object> oldExternalUser = AccountUtil.to(entry, externalUserFullProfile);

            Map<String, Object> clone = (Map<String, Object>) externalUser.clone();
            deleteSameItem(oldExternalUser, clone);
            adjustModifyUser(clone);

            List<LdapAttribute> attrs = toLdapAttributes(clone);
            if (attrs.isEmpty()) {
                return;
            }
            final List<AttributeModification> resultModifications = new ArrayList<>(attrs.size());
            for (LdapAttribute attr : attrs) {
                AttributeModification attributeModification = new AttributeModification(AttributeModificationType.REPLACE, attr);
                resultModifications.add(attributeModification);
            }

            ModifyRequest modifyRequest = new ModifyRequest(newDn, resultModifications.toArray(new AttributeModification[0]));
            updateEntry(connection, modifyRequest);
        } catch (LdapException ex) {
            LOG.error("", ex);
            throw new ThirdPartyAccountException(ex.getMessage());
        }
    }

    @Override
    public void deleteExternalUser(String externalUserId, Connector connector) throws ThirdPartyAccountException {
        LdapAndAdConfig ldapConfig = getLdapAndAdConfig(connector);
        try {
            Connection connection = getConnection(ldapConfig);
            LdapEntry ldapEntry = queryEntryById(ldapConfig.getUserBaseDn(), externalUserId, connection);

            String dn = ldapEntry.getDn();

            deleteEntry(connection, dn);
        } catch (ThirdPartyAccountNotExistException tx) {
            LOG.info("external user is not exist: {}", externalUserId);
        } catch (Exception ex) {
            LOG.error("", ex);
            throw new ThirdPartyAccountException(ex.getMessage());
        }
    }

    @Override
    public void deleteExternalUsers(List<String> externalUserIds, Connector connector) throws ThirdPartyAccountException {
        for (String externalUserId : externalUserIds) {
            deleteExternalUser(externalUserId, connector);
        }
    }

    /**
     * ldap密码做MD5加密
     * @param pwd
     * @return
     */
    public static String LdapEncoderByMd5(String pwd) {
        byte[] digest = MD5Util.digest(pwd, "utf-8");
        String md5Pwd = Base64Util.encodeToString(digest);
        return "{MD5}"+ md5Pwd;
    }
}
