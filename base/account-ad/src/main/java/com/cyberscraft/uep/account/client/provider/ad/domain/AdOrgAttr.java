package com.cyberscraft.uep.account.client.provider.ad.domain;

import com.cyberscraft.uep.account.client.constant.ExternalAttr;
import com.cyberscraft.uep.common.domain.DataTypeEnum;
import com.cyberscraft.uep.common.domain.MutableEnum;

import static com.cyberscraft.uep.account.client.constant.AccountConstant.APPORG_PROFILE_ROOTNAME;
import static com.cyberscraft.uep.account.client.constant.AccountConstant.IDPORG_PROFILE_ROOTNAME;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/26 6:29 下午
 */
public enum AdOrgAttr implements ExternalAttr {
    ou("ou", DataTypeEnum.STRING, "ou"),
    dn("dn", DataTypeEnum.STRING, "dn"),
    objectGUID("objectGUID", DataTypeEnum.STRING, "objectGUID", MutableEnum.readonly),
    parentObjectGUID("parentObjectGUID", DataTypeEnum.STRING, "parentObjectGUID", MutableEnum.readonly),
    objectclass("objectclass", DataTypeEnum.STRING, "objectclass", true),
    ;

    private String attrName;

    private DataTypeEnum dataType;

    private String displayName;

    private String description;

    private Boolean multiValued;

    private MutableEnum mutability;

    AdOrgAttr(String attrName, DataTypeEnum dataType, String displayName) {
        this(attrName, dataType, displayName, displayName, false, MutableEnum.readWrite);
    }

    AdOrgAttr(String attrName, DataTypeEnum dataType, String displayName, Boolean multiValued) {
        this(attrName, dataType, displayName, displayName, multiValued, MutableEnum.readWrite);
    }


    AdOrgAttr(String attrName, DataTypeEnum dataType, String displayName, MutableEnum mutability) {
        this(attrName, dataType, displayName, displayName, false, mutability);
    }

    AdOrgAttr(String attrName, DataTypeEnum dataType, String displayName, String description, Boolean multiValued, MutableEnum mutability) {
        this.attrName = attrName;
        this.dataType = dataType;
        this.displayName = displayName;
        this.description = description;
        this.multiValued = multiValued;
        this.mutability = mutability;
    }

    public String getAttrName() {
        return attrName;
    }

    public DataTypeEnum getDataType() {
        return dataType;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getDescription() {
        return description;
    }

    public Boolean getMultiValued() {
        return multiValued;
    }

    @Override
    public MutableEnum getMutability() {
        return mutability;
    }

    public String getAppNamePath() {
        return APPORG_PROFILE_ROOTNAME + "." + getAttrName();
    }

    @Override
    public String getIdpNamePath() {
        return IDPORG_PROFILE_ROOTNAME + "." + getAttrName();
    }

}
