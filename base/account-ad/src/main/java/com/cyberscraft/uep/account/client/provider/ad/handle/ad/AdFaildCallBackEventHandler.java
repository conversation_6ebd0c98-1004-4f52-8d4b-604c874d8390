package com.cyberscraft.uep.account.client.provider.ad.handle.ad;

import com.cyberscraft.uep.account.client.constant.ThirdPartyAccountType;
import com.cyberscraft.uep.account.client.domain.Connector;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountException;
import com.cyberscraft.uep.account.client.provider.IThirdPartyFailedCallBackEventHandler;

/***
 *
 * @date 2021/4/7
 * <AUTHOR>
 ***/
public class AdFaildCallBackEventHandler implements IThirdPartyFailedCallBackEventHandler {

    @Override
    public String getSupportedAccountType() {
        return ThirdPartyAccountType.AD.getCode();
    }

    @Override
    public void redoFailedEvent(String tenantId, Connector connector) throws ThirdPartyAccountException {

    }
}
