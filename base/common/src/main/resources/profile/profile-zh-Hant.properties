# 系統角色映射
TC.TENANT_ADMIN=平臺租戶管理員
TC.APP_ADMIN=平臺應用管理員
TC.LINK_ADMIN=平臺連接流管理員
USERCENTER.SUPER_ADMIN=系統管理員
USERCENTER.APP_ADMIN=應用管理員
USERCENTER.USER_ADMIN=帳號管理員
USERCENTER.ROLE_APP=CLI應用角色
USERCENTER.ROLE_USER=普通用戶角色
TC.TENANT_ADMIN.DESCRIPTION=平臺租戶管理員，可對租戶進行管理
TC.APP_ADMIN.DESCRIPTION=平臺應用管理員，可對應用進行管理
TC.LINK_ADMIN.DESCRIPTION=平臺連接流管理員，可管理連接器、執行動作和連接流模板
USERCENTER.SUPER_ADMIN.DESCRIPTION=系統管理員，具有最高權限，可以進行一切操作
USERCENTER.APP_ADMI.DESCRIPTION=系統管理員，應用管理員，可以進行應用相關的操作，包括應用管理，應用權限角色管理，應用分配，應用角色分配。，可以進行一切操作
USERCENTER.USER_ADMIN.DESCRIPTION=帳號管理員，可以進行用戶帳號相關的操作，包括用戶帳號管理，組織結構管理，擴展屬性管理。
USERCENTER.ROLE_APP.DESCRIPTION=CLI應用角色，即用戶中心創建的CLI應用，默認具有的權限。包括用戶，組織結構，擴展屬性，審計日誌，鏈接器的權限。
USERCENTER.ROLE_USER.DESCRIPTION=普通用戶角色，可以進行自服務等操作。用戶中心中的用戶，默認具有該角色，無需額外分配。
# ====================== 本地用戶屬性 ======================
local.user.sub=用戶ID
local.user.sub.description=系統生成的用戶id
local.user.username=用戶名
local.user.username.description=用戶名
local.user.phone_number=手機號碼
local.user.phone_number.description=手機號碼
local.user.email=電子郵箱
local.user.email.description=電子郵箱
local.user.user_job_number=工號
local.user.user_job_number.description=工號
local.user.name=姓名
local.user.name.description=姓名
local.user.title=職位
local.user.title.description=職位
local.user.manager=直屬主管
local.user.manager.description=直屬主管
local.user.connector_manager=外部直屬主管
local.user.connector_manager.description=外部直屬主管
local.user.group_positions=部門職位
local.user.group_positions.description=部門職位
local.user.connector_group_positions=外部部門職位
local.user.connector_group_positions.description=外部部門職位
local.user.telephone_number=電話
local.user.telephone_number.description=電話
local.user.preferred_username=常用名
local.user.preferred_username.description=常用名
local.user.nickname=暱稱
local.user.nickname.description=暱稱
local.user.picture=頭像
local.user.picture.description=頭像
local.user.address=辦公地點
local.user.address.description=辦公地點
local.user.hired_date=入職日期
local.user.hired_date.description=入職日期
local.user.birthdate=生日
local.user.birthdate.description=生日
local.user.cert=個人證書
local.user.cert.description=個人證書
local.user.gender=性別
local.user.gender.description=性別
local.user.email_verified=郵箱是否已驗證
local.user.email_verified.description=郵箱是否已驗證
local.user.come_from=來源
local.user.come_from.description=來源
local.user.last_login=最後一次登錄時間
local.user.last_login.description=最後一次登錄時間
local.user.locale=URL of user's profile
local.user.locale.description=URL of user's profile
local.user.password=登錄密碼
local.user.password.description=登錄密碼
local.user.tag=用戶標籤
local.user.tag.description=用戶標籤
local.user.password_status=密碼狀態
local.user.password_status.description=密碼狀態
local.user.phone_number_verified=手機號是否已驗證
local.user.phone_number_verified.description=手機號是否已驗證
local.user.profile=URL of user's profile
local.user.profile.description=URL of user's profile
local.user.pwd_changed_time=密碼修改時間
local.user.pwd_changed_time.description=密碼修改時間
local.user.pwd_expired_time=密碼過期時間
local.user.pwd_expired_time.description=密碼過期時間
local.user.type=用戶類別
local.user.type.description=用戶類別
local.user.website=個人站點
local.user.website.description=個人站點
local.user.zoneinfo=時區
local.user.zoneinfo.description=時區
local.user.org_ids=部門ID
local.user.org_ids.description=部門ID
local.user.connector_org_ids=外部部門ID
local.user.connector_org_ids.description=外部部門ID
local.user.connector_user_id=外部用戶ID
local.user.connector_user_id.description=外部用戶ID
local.user.dept_path=部門路徑
local.user.dept_path.description=部門路徑
local.user.user_extension=擴展屬性
local.user.user_extension.description=擴展屬性
local.user.start_date=生效日期
local.user.start_date.description=生效日期
local.user.end_date=失效日期
local.user.end_date.description=失效日期
local.user.status=狀態
local.user.status.description=狀態
local.user.connector_id=來源系統
local.user.connector_id.description=來源系統
local.user.create_by=被誰創建
local.user.create_by.description=被誰創建
local.user.create_time=創建時間
local.user.create_time.description=創建時間
local.user.created_mode=創建方式
local.user.created_mode.description=創建方式
local.user.update_by=被誰修改
local.user.update_by.description=被誰修改
local.user.update_time=修改時間
local.user.update_time.description=修改時間
local.user.checksum=校驗碼
local.user.checksum.description=校驗碼
local.user.tenant_id=租戶編碼
local.user.tenant_id.description=租戶編碼
local.user.role_positions=角色信息
local.user.role_positions.description=角色信息
local.user.connector_role_positions=外部角色信息
local.user.connector_role_positions.description=外部角色信息
local.user.id_card=身份證
local.user.id_card.description=身份證
local.user.org_positions=部門職位
local.user.org_positions.description=部門職位
local.user.org_job_number=部門工號
local.user.org_job_number.description=部門工號
local.user.main_org=主部門
local.user.main_org.description=主部門

# ====================== 本地部門屬性 ======================
local.org.id=系統生成的id
local.org.id.description=系統生成的id
local.org.name=名稱
local.org.name.description=名稱
local.org.readonly=只讀
local.org.readonly.description=只讀
local.org.org_desc=部門描述
local.org.org_desc.description=部門描述
local.org.seq=同級別內排序
local.org.seq.description=同級別內排序
local.org.org_path=部門路徑
local.org.org_path.description=部門路徑
local.org.parent_ref_id=父ID
local.org.parent_ref_id.description=父ID
local.org.manager=部門主管
local.org.manager.description=部門主管
local.org.org_extension=擴展屬性
local.org.org_extension.description=擴展屬性
local.org.connector_org_id=外部部門ID
local.org.connector_org_id.description=外部部門ID
local.org.connector_org_name=外部部門名稱
local.org.connector_org_name.description=外部部門名稱
local.org.connector_parent_org_id=外部父部門ID
local.org.connector_parent_org_id.description=外部父部門ID
local.org.connector_manager=外部主管
local.org.connector_manager.description=外部主管
local.org.status=狀態
local.org.status.description=狀態
local.org.connector_id=來源
local.org.connector_id.description=來源
local.org.create_by=被誰創建
local.org.create_by.description=被誰創建
local.org.create_time=創建時間
local.org.create_time.description=創建時間
local.org.created_mode=創建方式
local.org.created_mode.description=創建方式
local.org.update_by=被誰修改
local.org.update_by.description=被誰修改
local.org.update_time=修改時間
local.org.update_time.description=修改時間
local.org.checksum=信息是否發生變更的校驗碼
local.org.checksum.description=信息是否發生變更的校驗碼
local.org.tenant_id=租戶編碼
local.org.tenant_id.description=租戶編碼

# ====================== 本地角色屬性 ======================
local.role.id=角色/角色組ID
local.role.id.description=角色/角色組ID
local.role.name=角色名稱
local.role.name.description=角色名稱
local.role.group_name=角色組名稱
local.role.group_name.description=角色組名稱
local.role.groupId=角色ID
local.role.groupId.description=角色ID
local.role.connector_role_id=外部角色ID
local.role.connector_role_id.description=外部角色ID
local.role.connector_role_group_id=外部角色組ID
local.role.connector_role_group_id.description=外部角色組ID
local.role.user_ids=角色所屬用戶
local.role.user_ids.description=角色所屬用戶
local.role.role_groups=角色組
local.role.role_groups.description=角色組
local.role.connector_role_groups=角色組
local.role.connector_role_groups.description=角色組

# ====================== 短信网关属性 ======================
local.sms.gateway.DGSEE=數犀短信
local.sms.gateway.CLY=誠立業短信
local.sms.gateway.BIT=北理工短信
local.sms.gateway.CNAS=國家認可委短信
local.sms.gateway.AHJK=安徽交控短信
local.sms.gateway.ALI=阿里雲短信
local.sms.gateway.WEBHOOK=連接流發送短信
# ====================== 邮件服务属性 ======================
local.mail.service.DGSEE=系統內寘服务
local.mail.service.ALI=阿里雲郵件推送
local.mail.service.CUSTOM=自定義郵件推送
# ====================== 企业消息服务属性 ======================
local.enterprise.message.service.INSITE=站內消息
local.enterprise.message.service.DINGDING=釘釘工作通知
local.enterprise.message.service.WEWORK=企業微信消息
local.enterprise.message.service.FEISHU=飛書消息
# ====================== 企业消息模版属性 ======================
local.enterprise.message.template.update_pwd_msg=密碼修改通知
local.enterprise.message.template.reset_pwd_msg=密碼重置通知
local.enterprise.message.template.admin_reset_pwd_msg=管理員重置密碼通知
local.enterprise.message.template.expire_pwd_msg=密碼過期提醒

# ====================== AD用戶屬性 ======================
ad.user.objectGUID=objectGUID
ad.user.objectGUID.description=objectGUID
ad.user.objectclass=objectclass
ad.user.objectclass.description=objectclass
ad.user.dn=dn
ad.user.dn.description=dn
ad.user.sAMAccountName=sAMAccountName
ad.user.sAMAccountName.description=sAMAccountName
ad.user.cn=cn
ad.user.cn.description=cn
ad.user.userAccountControl=用戶啟用標識，512-啟用，2-禁用
ad.user.userAccountControl.description=用戶啟用標識，512-啟用，2-禁用
ad.user.name=name
ad.user.name.description=name
ad.user.unicodePwd=unicodePwd
ad.user.unicodePwd.description=unicodePwd
ad.user.userPassword=userPassword
ad.user.userPassword.description=userPassword
ad.user.telephoneNumber=telephoneNumber
ad.user.telephoneNumber.description=telephoneNumber
ad.user.mail=mail
ad.user.mail.description=mail
ad.user.display_name=displayName
ad.user.display_name.description=displayName
ad.user.userPrincipalName=userPrincipalName
ad.user.userPrincipalName.description=userPrincipalName
ad.user.department_ids=department_ids
ad.user.department_ids.description=department_ids

# ====================== AD部門屬性 ======================
ad.org.ou=ou
ad.org.ou.description=ou
ad.org.dn=dn
ad.org.dn.description=dn
ad.org.objectGUID=objectGUID
ad.org.objectGUID.description=objectGUID
ad.org.parentObjectGUID=parentObjectGUID
ad.org.parentObjectGUID.description=parentObjectGUID
ad.org.objectclass=objectclass
ad.org.objectclass.description=objectclass

# ====================== LDAP用戶屬性 ======================
ldap.user.entryUUID=entryUUID
ldap.user.entryUUID.description=entryUUID
ldap.user.objectclass=objectclass
ldap.user.objectclass.description=objectclass
ldap.user.dn=dn
ldap.user.dn.description=dn
ldap.user.uid=uid
ldap.user.uid.description=uid
ldap.user.cn=cn
ldap.user.cn.description=cn
ldap.user.userPassword=userPassword
ldap.user.userPassword.description=userPassword
ldap.user.telephoneNumber=telephoneNumber
ldap.user.telephoneNumber.description=telephoneNumber
ldap.user.mail=mail
ldap.user.mail.description=mail
ldap.user.display_name=displayName
ldap.user.display_name.description=displayName
ldap.user.department_ids=department_ids
ldap.user.department_ids.description=department_ids

# ====================== LDAP部門屬性 ======================
ldap.org.ou=ou
ldap.org.ou.description=ou
ldap.org.cn=cn
ldap.org.cn.description=cn
ldap.org.entryUUID=entryUUID
ldap.org.entryUUID.description=entryUUID
ldap.org.parentEntryUUID=parentEntryUUID
ldap.org.parentEntryUUID.description=parentEntryUUID
ldap.org.objectclass=objectclass
ldap.org.objectclass.description=objectclass
ldap.org.dn=dn
ldap.org.dn.description=dn


# ====================== 釘釘部門屬性 ======================
dingding.org.name=部門名稱
dingding.org.name.description=部門的標準名稱
dingding.org.dept_id=部門ID
dingding.org.dept_id.description=系統生成的唯一標識符
dingding.org.parent_id=父部門ID
dingding.org.parent_id.description=上級部門標識符
dingding.org.hide_dept=是否隱藏本部門
dingding.org.hide_dept.description=控制本部門在通訊錄中的可見性
dingding.org.dept_permits=可見部門列表
dingding.org.dept_permits.description=指定可查看本部門的其他部門ID列表
dingding.org.user_permits=可見人員列表
dingding.org.user_permits.description=指定可查看本部門的人員userId列表
dingding.org.outer_dept=限制通訊錄可見性
dingding.org.outer_dept.description=是否限制本部門成員查看全部通訊錄
dingding.org.outer_dept_only_self=僅顯示本部門及下級
dingding.org.outer_dept_only_self.description=限制成員只能看到所在部門及下級部門
dingding.org.outer_permit_users=允許查看的用戶
dingding.org.outer_permit_users.description=指定可查看通訊錄的用戶ID列表
dingding.org.outer_permit_depts=允許查看的部門
dingding.org.outer_permit_depts.description=指定可查看通訊錄的部門ID列表
dingding.org.create_dept_group=創建企業群
dingding.org.create_dept_group.description=是否創建關聯部門的企業群
dingding.org.auto_approve_apply=自動審批加入
dingding.org.auto_approve_apply.description=是否默認同意加入部門申請
dingding.org.order=排序值
dingding.org.order.description=在父部門中的顯示順序
dingding.org.source_identifier=部門標識字段
dingding.org.source_identifier.description=部門唯一標識符字段
dingding.org.language=通訊錄語言
dingding.org.language.description=設置通訊錄顯示語言
dingding.org.auto_add_user=自動入群
dingding.org.auto_add_user.description=新人加入時自動加入部門群
dingding.org.dept_manager_userid_list=部門主管
dingding.org.dept_manager_userid_list.description=部門主管用戶ID列表
dingding.org.group_contain_sub_dept=包含子部門
dingding.org.group_contain_sub_dept.description=部門群是否包含子部門成員
dingding.org.group_contain_outer_dept=包含外包部門
dingding.org.group_contain_outer_dept.description=部門群是否包含外包部門成員
dingding.org.group_contain_hidden_dept=包含隱藏部門
dingding.org.group_contain_hidden_dept.description=部門群是否包含隱藏部門成員
dingding.org.org_dept_owner=群主
dingding.org.org_dept_owner.description=企業群群主的用戶ID
dingding.org.force_update_fields=強制更新字段
dingding.org.force_update_fields.description=指定需要強制更新的字段列表

# ====================== 釘釘用戶屬性 ======================
dingding.user.userid=用戶ID
dingding.user.userid.description=員工的唯一標識符
dingding.user.unionid=統一ID
dingding.user.unionid.description=企業賬號範圍內的唯一標識
dingding.user.name=姓名
dingding.user.name.description=員工的中文姓名
dingding.user.avatar=頭像
dingding.user.avatar.description=用戶頭像URL地址
dingding.user.state_code=國際區號
dingding.user.state_code.description=電話號碼國際區號
dingding.user.manager_userid=直屬主管
dingding.user.manager_userid.description=直接上級的用戶ID
dingding.user.mobile=手機號
dingding.user.mobile.description=員工手機號碼
dingding.user.hide_mobile=隱藏手機號
dingding.user.hide_mobile.description=是否隱藏手機號碼
dingding.user.telephone=分機號
dingding.user.telephone.description=辦公分機號碼
dingding.user.job_number=工號
dingding.user.job_number.description=員工工號
dingding.user.title=職位
dingding.user.title.description=員工職位名稱
dingding.user.email=郵箱
dingding.user.email.description=工作郵箱地址
dingding.user.work_place=辦公地點
dingding.user.work_place.description=員工辦公地點
dingding.user.remark=備註
dingding.user.remark.description=附加說明信息
dingding.user.exclusive_account=專屬賬號
dingding.user.exclusive_account.description=是否為本組織專屬賬號
dingding.user.exclusive_account_type=專屬賬號類型
dingding.user.exclusive_account_type.description=專屬賬號的認證類型
dingding.user.exclusive_mobile=專屬手機號
dingding.user.exclusive_mobile.description=專屬賬號綁定的手機號
dingding.user.avatarMediaId=頭像媒體ID
dingding.user.avatarMediaId.description=創建專屬賬號時指定的頭像媒體ID
dingding.user.nickname=暱稱
dingding.user.nickname.description=專屬賬號的顯示暱稱
dingding.user.login_id=登錄ID
dingding.user.login_id.description=專屬賬號登錄用戶名
dingding.user.loginId=登錄賬號
dingding.user.loginId.description=釘釘專屬賬號登錄名
dingding.user.init_password=初始密碼
dingding.user.init_password.description=專屬賬號的初始密碼
dingding.user.org_email=企業郵箱
dingding.user.org_email.description=員工的企業郵箱地址
dingding.user.org_email_type=郵箱類型
dingding.user.org_email_type.description=企業郵箱的類型標識
dingding.user.dept_id_list=所屬部門
dingding.user.dept_id_list.description=員工所屬部門ID列表
dingding.user.dept_order_list=部門排序
dingding.user.dept_order_list.description=在各部門中的排序信息
dingding.user.dept_title_list=部門職位
dingding.user.dept_title_list.description=在各部門中的職位信息
dingding.user.cust_position_list=職位排序
dingding.user.cust_position_list.description=職位和排序的組合信息
dingding.user.extension=擴展屬性
dingding.user.extension.description=自定義擴展屬性
dingding.user.hired_date=入職時間
dingding.user.hired_date.description=Unix時間戳格式的入職時間
dingding.user.active=激活狀態
dingding.user.active.description=賬號是否已激活
dingding.user.real_authed=實名認證
dingding.user.real_authed.description=是否完成實名認證
dingding.user.senior=高管標識
dingding.user.senior.description=是否為企業高管
dingding.user.senior_mode=高管模式
dingding.user.senior_mode.description=是否啟用高管模式
dingding.user.admin=管理員
dingding.user.admin.description=是否為企業管理員
dingding.user.boss=企業主
dingding.user.boss.description=是否為企業所有者
dingding.user.leader_in_dept=部門領導
dingding.user.leader_in_dept.description=在部門中的領導身份標識
dingding.user.language=語言偏好
dingding.user.language.description=用戶的通訊錄語言設置
dingding.user.login_email=登錄郵箱
dingding.user.login_email.description=用於登錄的郵箱地址
dingding.user.role_list=角色列表
dingding.user.role_list.description=用戶擁有的角色列表
dingding.user.union_emp_ext=關聯信息
dingding.user.union_emp_ext.description=來自關聯組織的擴展信息
dingding.user.force_update_fields=強制更新字段
dingding.user.force_update_fields.description=需要強制更新的字段列表

# ====================== 釘釘角色屬性 ======================
dingding.role.name=角色名稱
dingding.role.name.description=角色顯示名稱
dingding.role.groupName=角色組名稱
dingding.role.groupName.description=角色所屬分組的名稱
dingding.role.roleId=角色ID
dingding.role.roleId.description=角色的唯一標識符
dingding.role.roleName=角色名稱
dingding.role.roleName.description=角色的標準名稱
dingding.role.groupId=角色組ID
dingding.role.groupId.description=角色組的唯一標識符
dingding.role.roleIds=角色ID列表
dingding.role.roleIds.description=多個角色ID的集合
dingding.role.roles=角色列表
dingding.role.roles.description=角色信息的集合
dingding.role.userIds=用戶ID列表
dingding.role.userIds.description=關聯的用戶ID集合
dingding.role.role_groups=角色組列表
dingding.role.role_groups.description=角色分組信息的集合

# 釘釘第三方用戶屬性
dingthird.user.userid=員工的userId
dingthird.user.unionid=員工在當前開發者企業賬號範圍內的唯一標識
dingthird.user.name=員工姓名
dingthird.user.avatar=頭像
dingthird.user.job_number=員工工號
dingthird.user.title=職位

# 釘釘第三方用戶屬性描述
dingthird.user.userid.description=員工的userId
dingthird.user.unionid.description=員工在當前開發者企業賬號範圍內的唯一標識
dingthird.user.name.description=員工姓名
dingthird.user.avatar.description=頭像
dingthird.user.job_number.description=員工工號
dingthird.user.title.description=職位

# ====================== 企業微信用戶屬性 ======================
wework.user.userid=成員UserID
wework.user.userid.description=成員UserID
wework.user.gender=性別
wework.user.gender.description=性別
wework.user.avatar=頭像
wework.user.avatar.description=頭像
wework.user.qr_code=員工個人二維碼
wework.user.qr_code.description=員工個人二維碼
wework.user.mobile=手機號碼
wework.user.mobile.description=手機號碼
wework.user.email=郵箱
wework.user.email.description=郵箱
wework.user.biz_mail=企業郵箱
wework.user.biz_mail.description=企業郵箱
wework.user.address=地址
wework.user.address.description=地址
wework.user.name=成員名稱
wework.user.name.description=成員名稱
wework.user.alias=成員別名
wework.user.alias.description=成員別名
wework.user.department=成員所屬部門id列表
wework.user.department.description=成員所屬部門id列表
wework.user.order=部門內的排序值，默認為0，成員次序以創建時間從小到大排列。個數必須和參數department的個數一致，數值越大排序越前面
wework.user.order.description=部門內的排序值，默認為0，成員次序以創建時間從小到大排列。個數必須和參數department的個數一致，數值越大排序越前面
wework.user.position=職務信息
wework.user.position.description=職務信息
wework.user.is_leader_in_dept=個數必須和參數department的個數一致，表示在所在的部門內是否為部門負責人。1表示為部門負責人，0表示非部門負責人
wework.user.is_leader_in_dept.description=個數必須和參數department的個數一致，表示在所在的部門內是否為部門負責人。1表示為部門負責人，0表示非部門負責人
wework.user.direct_leader=直屬上級UserID
wework.user.direct_leader.description=直屬上級UserID
wework.user.avatar_mediaid=成員頭像的mediaid
wework.user.avatar_mediaid.description=成員頭像的mediaid
wework.user.enable=啟用/禁用成員。1表示啟用成員，0表示禁用成員
wework.user.enable.description=啟用/禁用成員。1表示啟用成員，0表示禁用成員
wework.user.extattr=自定義字段
wework.user.extattr.description=自定義字段
wework.user.to_invite=是否邀請該成員使用企業微信（將通過微信服務通知或短信或郵件下發邀請，每天自動下發一次，最多持續3個工作日），默認值為true。
wework.user.to_invite.description=是否邀請該成員使用企業微信（將通過微信服務通知或短信或郵件下發邀請，每天自動下發一次，最多持續3個工作日），默認值為true。
wework.user.external_profile=成員對外屬性
wework.user.external_profile.description=成員對外屬性
wework.user.external_position=對外職務
wework.user.external_position.description=對外職務
wework.user.nickname=視頻號名字
wework.user.nickname.description=視頻號名字
wework.user.main_department=主部門id
wework.user.main_department.description=主部門id
wework.user.telephone=座機
wework.user.telephone.description=座機

# ====================== 企業微信部門屬性 ======================
wework.org.id=部門id，若不填該參數，將自動生成id
wework.org.id.description=部門id，若不填該參數，將自動生成id
wework.org.name=部門名稱
wework.org.name.description=部門名稱
wework.org.name_en=部門英文名稱
wework.org.name_en.description=部門英文名稱
wework.org.parentid=父部門id
wework.org.parentid.description=父部門id
wework.org.order=在父部門中的次序值。order值大的排序靠前
wework.org.order.description=在父部門中的次序值。order值大的排序靠前

# ====================== JWT用戶屬性 ======================
jwt.user.sub=用戶CN
jwt.user.sub.description=用戶CN
jwt.user.name=姓名
jwt.user.name.description=姓名
jwt.user.mobile=手機號
jwt.user.mobile.description=手機號
jwt.user.email=電子郵箱
jwt.user.email.description=電子郵箱
jwt.user.userId=企微userid
jwt.user.userId.description=企微userid

# ====================== ETL用戶屬性 ======================
etl.user.user_id=userId
etl.user.user_id.description=userId
etl.user.account_id=accountId
etl.user.account_id.description=accountId
etl.user.login_id=login_id
etl.user.login_id.description=login_id
etl.user.name=姓名
etl.user.name.description=姓名
etl.user.mobile=手機號
etl.user.mobile.description=手機號
etl.user.mail=郵箱
etl.user.mail.description=郵箱
etl.user.tel=座機號
etl.user.tel.description=座機號
etl.user.nick_name=暱稱
etl.user.nick_name.description=暱稱
etl.user.gender=性別
etl.user.gender.description=性別
etl.user.group_positions=部門職位
etl.user.group_positions.description=部門職位
etl.user.status=狀態
etl.user.status.description=狀態
etl.user.user_job_number=工號
etl.user.user_job_number.description=工號

# ====================== ETL部門屬性 ======================
etl.org.name=部門名稱
etl.org.name.description=部門名稱
etl.org.code=部門編號
etl.org.code.description=部門編號
etl.org.parent_code=父部門編號
etl.org.parent_code.description=父部門編號

# ====================== 飛書用戶屬性 ======================
feishu.user.user_id=userId
feishu.user.user_id.description=userId
feishu.user.union_id=union_id
feishu.user.union_id.description=union_id
feishu.user.name=姓名
feishu.user.name.description=姓名
feishu.user.nickname=暱稱
feishu.user.nickname.description=暱稱
feishu.user.mobile=手機號
feishu.user.mobile.description=手機號
feishu.user.email=郵箱
feishu.user.email.description=郵箱
feishu.user.enterprise_email=企業郵箱
feishu.user.enterprise_email.description=企業郵箱
feishu.user.avatar=頭像
feishu.user.avatar.description=頭像
feishu.user.gender=性別
feishu.user.gender.description=性別
feishu.user.job_title=職務
feishu.user.job_title.description=職務
feishu.user.department_ids=部門ID
feishu.user.department_ids.description=部門ID
feishu.user.status=狀態
feishu.user.status.description=狀態
feishu.user.employee_no=工號
feishu.user.employee_no.description=工號
feishu.user.employee_type=員工類型
feishu.user.employee_type.description=員工類型
feishu.user.is_tenant_manager=是否是租戶超級管理員
feishu.user.is_tenant_manager.description=是否是租戶超級管理員
feishu.user.orders=用戶排序信息，主部門的部門排序order必須最大
feishu.user.orders.description=用戶排序信息，主部門的部門排序order必須最大
feishu.user.en_name=英文名
feishu.user.en_name.description=英文名
feishu.user.mobile_visible=手機號碼的可見性
feishu.user.mobile_visible.description=手機號碼的可見性
feishu.user.avatar_key=頭像的文件Key
feishu.user.avatar_key.description=頭像的文件Key
feishu.user.leader_user_id=用戶的直接主管的用戶ID
feishu.user.leader_user_id.description=用戶的直接主管的用戶ID
feishu.user.assign_info=用戶席位列表
feishu.user.assign_info.description=用戶席位列表
feishu.user.city=工作城市
feishu.user.city.description=工作城市
feishu.user.country=國家
feishu.user.country.description=國家
feishu.user.work_station=工位
feishu.user.work_station.description=工位
feishu.user.join_time=入職時間，時間戳格式
feishu.user.join_time.description=入職時間，時間戳格式
feishu.user.geo=數據駐留地
feishu.user.geo.description=數據駐留地
feishu.user.job_level_id=職級ID
feishu.user.job_level_id.description=職級ID
feishu.user.job_family_id=序列ID
feishu.user.job_family_id.description=序列ID
feishu.user.subscription_ids=分配給用戶的席位ID列表，需開通“分配用戶席位”權限。
feishu.user.subscription_ids.description=分配給用戶的席位ID列表，需開通“分配用戶席位”權限。
feishu.user.department_path=部門路徑
feishu.user.department_path.description=部門路徑
feishu.user.dotted_line_leader_user_ids=虛線上級ID
feishu.user.dotted_line_leader_user_ids.description=虛線上級ID
feishu.user.custom_attrs=自定義字段
feishu.user.custom_attrs.description=自定義字段

# ====================== 飛書部門屬性 ======================
feishu.org.name=部門名稱
feishu.org.name.description=部門名稱
feishu.org.department_id=部門ID
feishu.org.department_id.description=部門ID
feishu.org.parent_department_id=父部門ID
feishu.org.parent_department_id.description=父部門ID
feishu.org.open_department_id=部門的open_id
feishu.org.open_department_id.description=部門的open_id
feishu.org.chat_id=部門群ID
feishu.org.chat_id.description=部門群ID
feishu.org.member_count=當前部門及其下屬部門下用戶（包含部門負責人）的個數
feishu.org.member_count.description=當前部門及其下屬部門下用戶（包含部門負責人）的個數
feishu.org.i18n_name=國際化的部門名稱
feishu.org.i18n_name.description=國際化的部門名稱
feishu.org.leader_user_id=部門主管用戶ID
feishu.org.leader_user_id.description=部門主管用戶ID
feishu.org.order=部門的排序，即部門在其同級部門的展示順序
feishu.org.order.description=部門的排序，即部門在其同級部門的展示順序
feishu.org.unit_ids=部門單位自定義ID列表，當前只支持一個
feishu.org.unit_ids.description=部門單位自定義ID列表，當前只支持一個
feishu.org.create_group_chat=是否創建部門群，默認不創建。創建部門群時，默認群名為部門名，默認群主為部門主負責人
feishu.org.create_group_chat.description=是否創建部門群，默認不創建。創建部門群時，默認群名為部門名，默認群主為部門主負責人
feishu.org.leaders=部門負責人
feishu.org.leaders.description=部門負責人
feishu.org.group_chat_employee_types=部門群僱員類型限制,不傳該字段時，則部門成員類型是所有類型。
feishu.org.group_chat_employee_types.description=部門群僱員類型限制,不傳該字段時，則部門成員類型是所有類型。
feishu.org.department_hrbps=部門HRBP
feishu.org.department_hrbps.description=部門HRBP
feishu.org.primary_member_count=當前部門及其下屬部門的主屬成員（即成員的主部門為當前部門）的數量
feishu.org.primary_member_count.description=當前部門及其下屬部門的主屬成員（即成員的主部門為當前部門）的數量

# ====================== SCIM用戶屬性 ======================
scim.user.external_id=外部用戶ID
scim.user.external_id.description=外部用戶ID
scim.user.user_name=用戶名
scim.user.user_name.description=用戶名
scim.user.display_name=姓名
scim.user.display_name.description=姓名
scim.user.nick_name=暱稱
scim.user.nick_name.description=暱稱
scim.user.password=密碼
scim.user.password.description=密碼
scim.user.phone_number=手機號
scim.user.phone_number.description=手機號
scim.user.user_extension=用戶擴展屬性
scim.user.user_extension.description=用戶擴展屬性
scim.user.email=郵箱
scim.user.email.description=郵箱

# ====================== AZUREAD用戶屬性 ======================
azuread.user.external_id=外部用戶ID
azuread.user.external_id.description=外部用戶ID
azuread.user.user_name=用戶名
azuread.user.user_name.description=用戶名
azuread.user.display_name=姓名
azuread.user.display_name.description=姓名
azuread.user.nick_name=暱稱
scazureadim.user.nick_name.description=暱稱
azuread.user.password=密碼
azuread.user.password.description=密碼
azuread.user.phone_number=手機號
azuread.user.phone_number.description=手機號
azuread.user.user_extension=用戶擴展屬性
azuread.user.user_extension.description=用戶擴展屬性
azuread.user.email=郵箱
azuread.user.email.description=郵箱

# ====================== OAuth2用戶屬性 ======================
oauth2.user.sub=主題
oauth2.user.sub.description=主題
oauth2.user.username=用戶名
oauth2.user.username.description=用戶名
oauth2.user.phone_number=手機號
oauth2.user.phone_number.description=手機號
oauth2.user.email=郵箱
oauth2.user.email.description=郵箱

# ====================== OIDC用戶屬性 ======================
oidc.user.sub=主題
oidc.user.sub.description=主題
oidc.user.username=用戶名
oidc.user.username.description=用戶名
oidc.user.phone_number=手機號
oidc.user.phone_number.description=手機號
oidc.user.email=郵箱
oidc.user.email.description=郵箱

# ====================== CAS用戶屬性 ======================
cas.user.sub=user標識
cas.user.sub.description=user標識

# ====================== EHR用戶屬性 ======================
ehr.user.PK_DEPT=部門ID
ehr.user.PK_DEPT.description=部門ID
ehr.user.CERTIFICATE=證件號碼
ehr.user.CERTIFICATE.description=證件號碼
ehr.user.POST_CHANNEL=崗位序列
ehr.user.POST_CHANNEL.description=崗位序列
ehr.user.POS_LEVEL_PK=POS_LEVEL_PK
ehr.user.POS_LEVEL_PK.description=POS_LEVEL_PK
ehr.user.POSITION_CODE=崗位編碼
ehr.user.POSITION_CODE.description=崗位編碼
ehr.user.IS_CONVERSION_YSH=是否轉正
ehr.user.IS_CONVERSION_YSH.description=是否轉正
ehr.user.SEX=性別
ehr.user.SEX.description=性別
ehr.user.DIRECT_SUPERIOR_NAME=直接上級姓名
ehr.user.DIRECT_SUPERIOR_NAME.description=直接上級姓名
ehr.user.PHONE=手機號
ehr.user.PHONE.description=手機號
ehr.user.ENTER_AGILE_DATE=入職日期
ehr.user.ENTER_AGILE_DATE.description=入職日期
ehr.user.LOGIN_ACCOUNT=LOGIN_ACCOUNT
ehr.user.LOGIN_ACCOUNT.description=LOGIN_ACCOUNT
ehr.user.INIT_PASSWORD=初始化密碼
ehr.user.INIT_PASSWORD.description=初始化密碼
ehr.user.IS_CONVERT=是否轉檔
ehr.user.IS_CONVERT.description=是否轉檔
ehr.user.EMAIL=郵箱
ehr.user.EMAIL.description=郵箱
ehr.user.DIRECT_SUPERIOR_JOB_NUMBER=直接上級工號
ehr.user.DIRECT_SUPERIOR_JOB_NUMBER.description=直接上級工號
ehr.user.OWNER_GROUP=所屬組織
ehr.user.OWNER_GROUP.description=所屬組織
ehr.user.POSITION_RANK=職層
ehr.user.POSITION_RANK.description=職層
ehr.user.PK_ORG=PK_ORG
ehr.user.PK_ORG.description=PK_ORG
ehr.user.POSITION=崗位名稱
ehr.user.POSITION.description=崗位名稱
ehr.user.PK_PSNDOC=人員ID
ehr.user.PK_PSNDOC.description=人員ID
ehr.user.BIRTH_DATE=生日
ehr.user.BIRTH_DATE.description=生日
ehr.user.FINANCIAL_EXPENSE_DIVISION_COMPANY=財務費用劃分公司
ehr.user.FINANCIAL_EXPENSE_DIVISION_COMPANY.description=財務費用劃分公司
ehr.user.STANDARD_POST=標準職銜
ehr.user.STANDARD_POST.description=標準職銜
ehr.user.LOGIN_USERNAME=登錄名稱
ehr.user.LOGIN_USERNAME.description=登錄名稱
ehr.user.RESIGN_DATE=離職時間
ehr.user.RESIGN_DATE.description=離職時間
ehr.user.ORGANIZATION_ENTRY_DATE=ORGANIZATION_ENTRY_DATE
ehr.user.ORGANIZATION_ENTRY_DATE.description=ORGANIZATION_ENTRY_DATE
ehr.user.PK_GROUP=PK_GROUP
ehr.user.PK_GROUP.description=PK_GROUP
ehr.user.OWNING_SYSTEM=所屬系統
ehr.user.OWNING_SYSTEM.description=所屬系統
ehr.user.JOB_NUMBER=工號
ehr.user.JOB_NUMBER.description=工號
ehr.user.PK_POST=PK_POST
ehr.user.PK_POST.description=PK_POST
ehr.user.DEPARTMENT=DEPARTMENT
ehr.user.DEPARTMENT.description=DEPARTMENT
ehr.user.ENTER_GROUP_DATE=ENTER_GROUP_DATE
ehr.user.ENTER_GROUP_DATE.description=ENTER_GROUP_DATE
ehr.user.DEPARTMENT_CODE=DEPARTMENT_CODE
ehr.user.DEPARTMENT_CODE.description=DEPARTMENT_CODE
ehr.user.NAME=姓名
ehr.user.NAME.description=姓名
ehr.user.AD_ACCOUNT=AD賬號
ehr.user.AD_ACCOUNT.description=AD賬號
ehr.user.IS_RESIGN=在職狀態
ehr.user.IS_RESIGN.description=在職狀態
ehr.user.MODIFIEDTIME=MODIFIEDTIME
ehr.user.MODIFIEDTIME.description=MODIFIEDTIME
ehr.user.PERSONNEL_TYPE=人員類別
ehr.user.PERSONNEL_TYPE.description=人員類別
ehr.user.CONVERSION_DATE_YSH=轉正日期
ehr.user.CONVERSION_DATE_YSH.description=轉正日期
ehr.user.TS=TS
ehr.user.TS.description=TS
ehr.user.CERTIFICATE_TYPE=證件類型
ehr.user.CERTIFICATE_TYPE.description=證件類型
ehr.user.DIRECT_SUPERIOR_ID=上級主管ID
ehr.user.DIRECT_SUPERIOR_ID.description=上級主管ID

# ====================== EHR部門屬性 ======================
ehr.org.PK_ORG=父級組織ID
ehr.org.PK_ORG.description=父級組織ID
ehr.org.PK_DEPT1=組織ID
ehr.org.PK_DEPT1.description=組織ID
ehr.org.DEPTNAME1=組織名稱
ehr.org.DEPTNAME1.description=組織名稱
ehr.org.DEPTCODE1=組織編碼
ehr.org.DEPTCODE1.description=組織編碼
ehr.org.DEPTLEADER_PSNCODE=DEPTLEADER_PSNCODE
ehr.org.DEPTLEADER_PSNCODE.description=DEPTLEADER_PSNCODE
ehr.org.STATUS=狀態
ehr.org.STATUS.description=狀態
ehr.org.DEPT_LEVEL=部門級別
ehr.org.DEPT_LEVEL.description=部門級別
ehr.org.IS_DEPT_ARCHIVE=是否啟用
ehr.org.IS_DEPT_ARCHIVE.description=是否啟用
ehr.org.PK_PSNDOC=組織負責人ID
ehr.org.PK_PSNDOC.description=組織負責人ID
ehr.org.UNITCODE=UNITCODE
ehr.org.UNITCODE.description=UNITCODE
ehr.org.DEPTLEADER_PSNNAME=DEPTLEADER_PSNNAME
ehr.org.DEPTLEADER_PSNNAME.description=DEPTLEADER_PSNNAME
ehr.org.TS=時間戳
ehr.org.TS.description=時間戳
ehr.org.ORGLEVEL=組織級別
ehr.org.ORGLEVEL.description=組織級別
ehr.org.PK_GROUP=父級組織ID
ehr.org.PK_GROUP.description=父級組織ID
ehr.org.UNITCODE1=組織編碼
ehr.org.UNITCODE1.description=組織編碼
ehr.org.IS_ORG_ENABLE=是否啟用
ehr.org.IS_ORG_ENABLE.description=是否啟用
ehr.org.PK_ORG1=組織ID
ehr.org.PK_ORG1.description=組織ID
ehr.org.UNITNAME1=組織名稱
ehr.org.UNITNAME1.description=組織名稱

# ====================== RRS用戶屬性 ======================
rrs.user.empId=用戶ID
rrs.user.empId.description=用戶ID
rrs.user.effectDate=生效日期
rrs.user.effectDate.description=生效日期
rrs.user.mainDepartment=主部門
rrs.user.mainDepartment.description=主部門
rrs.user.mainDepartment_source=主部門信息
rrs.user.mainDepartment_source.description=主部門信息
rrs.user.ptjDepartment_source=兼職部門信息
rrs.user.ptjDepartment_source.description=兼職部門信息
rrs.user.department=部門
rrs.user.department.description=部門
rrs.user.jobDuty=職務
rrs.user.jobDuty.description=職務
rrs.user.jobSequence=序列
rrs.user.jobSequence.description=序列
rrs.user.jobTitle=職位
rrs.user.jobTitle.description=職位
rrs.user.jobLevel=職級
rrs.user.jobLevel.description=職級
rrs.user.jobGrade=職等
rrs.user.jobGrade.description=職等
rrs.user.manager=直屬主管
rrs.user.manager.description=直屬主管
rrs.user.employeeStatus=員工狀態
rrs.user.employeeStatus.description=員工狀態
rrs.user.employeeType=員工類型
rrs.user.employeeType.description=員工類型
rrs.user.onBoardingDate=入職日期
rrs.user.onBoardingDate.description=入職日期
rrs.user.probationDate=轉正日期
rrs.user.probationDate.description=轉正日期
rrs.user.lastDayOfWork=離職日期
rrs.user.lastDayOfWork.description=離職日期
rrs.user.reasonForOffBoarding=離職原因
rrs.user.reasonForOffBoarding.description=離職原因
rrs.user.employeeNumber=工號
rrs.user.employeeNumber.description=工號
rrs.user.name=姓名
rrs.user.name.description=姓名
rrs.user.gender=性別
rrs.user.gender.description=性別
rrs.user.idType=證件類型
rrs.user.idType.description=證件類型
rrs.user.idNumber=證件號碼
rrs.user.idNumber.description=證件號碼
rrs.user.dateOfBirth=出生日期
rrs.user.dateOfBirth.description=出生日期
rrs.user.mobileNumber=手機號碼
rrs.user.mobileNumber.description=手機號碼
rrs.user.personalEmail=個人郵箱
rrs.user.personalEmail.description=個人郵箱
rrs.user.email=郵箱
rrs.user.email.description=郵箱
rrs.user.syncDing=是否同步釘釘通訊錄
rrs.user.syncDing.description=是否同步釘釘通訊錄
rrs.user.extEmpId=來源人員ID
rrs.user.extEmpId.description=來源人員ID
rrs.user.extTel=分機號
rrs.user.extTel.description=分機號
rrs.user.extEntEmail=員工的企業郵箱
rrs.user.extEntEmail.description=員工的企業郵箱
rrs.user.extAccountName=賬戶名稱
rrs.user.extAccountName.description=賬戶名稱
rrs.user.extAdAccount=AD賬號
rrs.user.extAdAccount.description=AD賬號
rrs.user.extIsDingAdmin=是否為企業的管理員（釘釘）
rrs.user.extIsDingAdmin.description=是否為企業的管理員（釘釘）
rrs.user.extIsActiveDing=是否激活了釘釘
rrs.user.extIsActiveDing.description=是否激活了釘釘
rrs.user.extInSys=所屬系統
rrs.user.extInSys.description=所屬系統
rrs.user.extFromSys=系統來源
rrs.user.extFromSys.description=系統來源
rrs.user.extDeptId=來源部門ID
rrs.user.extDeptId.description=來源部門ID
rrs.user.extRrsEmpId=來源部門睿人事ID
rrs.user.extRrsEmpId.description=來源部門睿人事ID
rrs.user.extJobLevel=來源職層
rrs.user.extJobLevel.description=來源職層
rrs.user.extJobName=來源崗位名稱
rrs.user.extJobName.description=來源崗位名稱
rrs.user.extJobCode=來源崗位編碼
rrs.user.extJobCode.description=來源崗位編碼
rrs.user.extJobSequence=來源崗位序列
rrs.user.extJobSequence.description=來源崗位序列
rrs.user.extJobTitle=來源標準職銜
rrs.user.extJobTitle.description=來源標準職銜
rrs.user.extManager=來源直接上級人員ID
rrs.user.extManager.description=來源直接上級人員ID
rrs.user.extRrsManager=來源直接上級人員睿人事ID
rrs.user.extRrsManager.description=來源直接上級人員睿人事ID
rrs.user.extFinCompany=財務費用劃分公司
rrs.user.extFinCompany.description=財務費用劃分公司
rrs.user.extIsConvert=是否轉檔
rrs.user.extIsConvert.description=是否轉檔
rrs.user.extPostStatus=在職狀態
rrs.user.extPostStatus.description=在職狀態
rrs.user.extPsnType=人員類別
rrs.user.extPsnType.description=人員類別
rrs.user.extEntryDate=來源入職日期
rrs.user.extEntryDate.description=來源入職日期
rrs.user.extIsRegular=是否轉正
rrs.user.extIsRegular.description=是否轉正
rrs.user.extPsnSys=任職信息系統來源
rrs.user.extPsnSys.description=任職信息系統來源
rrs.user.managerSource=直屬主管信息
rrs.user.managerSource.description=直屬主管信息

# ====================== RRS部門屬性 ======================
rrs.org.orgId=組織ID
rrs.org.orgId.description=組織ID
rrs.org.effectDate=生效日期
rrs.org.effectDate.description=生效日期
rrs.org.name=組織名稱
rrs.org.name.description=組織名稱
rrs.org.code=組織編碼
rrs.org.code.description=組織編碼
rrs.org.type=組織類型
rrs.org.type.description=組織類型
rrs.org.managerEmpId=組織負責人ID
rrs.org.managerEmpId.description=組織負責人ID
rrs.org.parentOrgId=上級組織ID
rrs.org.parentOrgId.description=上級組織ID
rrs.org.order=組織序號
rrs.org.order.description=組織序號
rrs.org.status=是否啟用
rrs.org.status.description=是否啟用
rrs.org.syncStatus=是否同步釘釘通訊錄
rrs.org.syncStatus.description=是否同步釘釘通訊錄
rrs.org.extOrgId=來源組織ID
rrs.org.extOrgId.description=來源組織ID
rrs.org.extName=來源組織名稱
rrs.org.extName.description=來源組織名稱
rrs.org.extCode=來源組織編碼
rrs.org.extCode.description=來源組織編碼
rrs.org.extManagerEmpId=來源組織負責人ID
rrs.org.extManagerEmpId.description=來源組織負責人ID
rrs.org.extRrsManagerEmpId=來源組織負責人睿人事ID
rrs.org.extRrsManagerEmpId.description=來源組織負責人睿人事ID
rrs.org.extParentOrgId=來源上級組織ID
rrs.org.extParentOrgId.description=來源上級組織ID
rrs.org.extRrsParentOrgId=來源上級組織睿人事ID
rrs.org.extRrsParentOrgId.description=來源上級組織睿人事ID
rrs.org.extOrder=來源組織序號
rrs.org.extOrder.description=來源組織序號
rrs.org.extStatus=來源是否啟用
rrs.org.extStatus.description=來源是否啟用
rrs.org.extIsDing=是否釘釘自建組織
rrs.org.extIsDing.description=是否釘釘自建組織
rrs.org.extType=釘釘組織類型
rrs.org.extType.description=釘釘組織類型
rrs.org.extRemark=備註
rrs.org.extRemark.description=備註
rrs.org.extFromSys=系統來源
rrs.org.extFromSys.description=系統來源

# ====================== EDOC用戶屬性 ======================
edoc.user.id=id
edoc.user.id.description=id
edoc.user.account=用戶名
edoc.user.account.description=用戶名
edoc.user.email=Email
edoc.user.email.description=Email
edoc.user.name=用戶姓名
edoc.user.name.description=用戶姓名
edoc.user.gender=性別
edoc.user.gender.description=性別
edoc.user.birthday=生日
edoc.user.birthday.description=生日
edoc.user.telephone=電話
edoc.user.telephone.description=電話
edoc.user.mobile=手機
edoc.user.mobile.description=手機
edoc.user.status=用戶狀態
edoc.user.status.description=用戶狀態
edoc.user.fax=傳真
edoc.user.fax.description=傳真
edoc.user.mainPositionId=主職位ID
edoc.user.mainPositionId.description=主職位ID
edoc.user.remark=備註
edoc.user.remark.description=備註
edoc.user.MainDepartmentId=主部門ID
edoc.user.MainDepartmentId.description=主部門ID
edoc.user.MainDepartmentName=主部門名稱
edoc.user.MainDepartmentName.description=主部門名稱
edoc.user.TopPersonalFolderId=個人內容庫ID
edoc.user.TopPersonalFolderId.description=個人內容庫ID
edoc.user.groupIdList=用戶組列表
edoc.user.groupIdList.description=用戶組列表
edoc.user.mainPositionIdentityId=職位自增ID
edoc.user.mainPositionIdentityId.description=主職位自增ID
edoc.user.mainPositionName=主職位
edoc.user.mainPositionName.description=主職位名稱
edoc.user.role=Role
edoc.user.role.description=Role
edoc.user.enableTime=啟用時間
edoc.user.enableTime.description=啟用時間
edoc.user.token=token
edoc.user.token.description=token
edoc.user.code=用戶編號
edoc.user.code.description=用戶編號
edoc.user.thirdPartId=第三方ID
edoc.user.thirdPartId.description=第三方ID
edoc.user.userId=userId
edoc.user.userId.description=userId
edoc.user.identityId=自增長ID
edoc.user.identityId.description=自增長ID
edoc.user.expirationDate=過期時間
edoc.user.expirationDate.description=過期時間
edoc.user.PasswordLastChangeTime=修改密碼時間
edoc.user.PasswordLastChangeTime.description=修改密碼時間
edoc.user.CreatorId=創建人
edoc.user.CreatorId.description=創建人
edoc.user.CreateTime=創建時間
edoc.user.CreateTime.description=創建時間
edoc.user.LastChangeTime=修改時間
edoc.user.LastChangeTime.description=修改時間
edoc.user.Signed=是否登錄過
edoc.user.Signed.description=是否登錄過
edoc.user.PasswordWrongCount=密碼連續錯誤次數
edoc.user.PasswordWrongCount.description=密碼連續錯誤次數
edoc.user.MainDepartmentIdentityId=主部門自增ID
edoc.user.MainDepartmentIdentityId.description=主部門自增ID
edoc.user.IsAuthority=IsAuthority
edoc.user.IsAuthority.description=IsAuthority
edoc.user.positionList=職位列表
edoc.user.positionList.description=職位列表

# ====================== EDOC部門屬性 ======================
edoc.org.id=id
edoc.org.id.description=id
edoc.org.name=部門名稱
edoc.org.name.description=部門名稱
edoc.org.parentId=上級部門ID
edoc.org.parentId.description=上級部門ID
edoc.org.ParentName=上級部門名稱
edoc.org.ParentName.description=上級部門名稱
edoc.org.code=部門編號
edoc.org.code.description=部門編號
edoc.org.managerPositionId=部門主管職位
edoc.org.managerPositionId.description=部門主管職位
edoc.org.ManagerPositionIdentityId=部門主管職位自增ID
edoc.org.ManagerPositionIdentityId.description=部門主管職位自增ID
edoc.org.sort=自定義排序
edoc.org.sort.description=自定義排序
edoc.org.remark=備註
edoc.org.remark.description=備註
edoc.org.IdentityId=自增長ID
edoc.org.IdentityId.description=自增長ID
edoc.org.thirdPartId=第三方ID
edoc.org.thirdPartId.description=第三方ID
edoc.org.ParentIdentityId=上級部門自增ID
edoc.org.ParentIdentityId.description=上級部門自增ID
edoc.org.HaveChildren=是否有子部門
edoc.org.HaveChildren.description=是否有子部門
edoc.org.CreateTime=創建時間
edoc.org.CreateTime.description=創建時間
edoc.org.EnableTime=啟用時間
edoc.org.EnableTime.description=啟用時間
edoc.org.ExpirationTime=過期時間
edoc.org.ExpirationTime.description=過期時間
edoc.org.deptId=部門ID
edoc.org.deptId.description=部門ID
edoc.org.managerPositionName=主管職位名稱
edoc.org.managerPositionName.description=主管職位名稱
edoc.org.positionIdentytiId=職位編號自增ID
edoc.org.positionIdentytiId.description=職位編號自增ID

# ====================== SEYON用戶屬性 ======================
seeyon.user.id=Id，唯一標識人員
seeyon.user.id.description=Id，唯一標識人員
seeyon.user.name=姓名
seeyon.user.name.description=姓名
seeyon.user.loginName=登錄名
seeyon.user.loginName.description=登錄名
seeyon.user.code=人員編碼
seeyon.user.code.description=人員編碼
seeyon.user.createTime=創建時間
seeyon.user.createTime.description=創建時間
seeyon.user.updateTime=更新時間
seeyon.user.updateTime.description=更新時間
seeyon.user.sortId=排序號
seeyon.user.sortId.description=排序號
seeyon.user.desc=說明
seeyon.user.desc.description=說明
seeyon.user.orgAccountId=人員所屬單位Id
seeyon.user.orgAccountId.description=人員所屬單位Id
seeyon.user.orgAccountName=人員所屬單位名稱
seeyon.user.orgAccountName.description=人員所屬單位名稱
seeyon.user.orgLevelId=人員職務級別Id
seeyon.user.orgLevelId.description=人員職務級別Id
seeyon.user.orgLevelName=人員職務級別名稱
seeyon.user.orgLevelName.description=人員職務級別名稱
seeyon.user.orgPostId=人員崗位Id
seeyon.user.orgPostId.description=人員崗位Id
seeyon.user.orgPostName=人員崗位名稱
seeyon.user.orgPostName.description=人員崗位名稱
seeyon.user.orgDepartmentId=人員所屬部門Id
seeyon.user.orgDepartmentId.description=人員所屬部門Id
seeyon.user.second_post=副崗
seeyon.user.second_post.description=副崗
seeyon.user.concurrent_post=兼職
seeyon.user.concurrent_post.description=兼職
seeyon.user.enabled=賬戶狀態
seeyon.user.enabled.description=賬戶狀態
seeyon.user.isDeleted=刪除標記
seeyon.user.isDeleted.description=刪除標記
seeyon.user.isInternal=是否內部人員
seeyon.user.isInternal.description=是否內部人員
seeyon.user.isLoginable=是否可登錄
seeyon.user.isLoginable.description=是否可登錄
seeyon.user.isAssigned=是否已分配人員
seeyon.user.isAssigned.description=是否已分配人員
seeyon.user.isAdmin=是否管理員
seeyon.user.isAdmin.description=是否管理員
seeyon.user.isValid=是否可用
seeyon.user.isValid.description=是否可用
seeyon.user.state=人員狀態：1為在職，2 為離職
seeyon.user.state.description=人員狀態：1為在職，2 為離職
seeyon.user.status=實體狀態
seeyon.user.status.description=實體狀態
seeyon.user.telNumber=手機號碼
seeyon.user.telNumber.description=手機號碼
seeyon.user.birthday=出生日期
seeyon.user.birthday.description=出生日期
seeyon.user.officeNum=辦公電話
seeyon.user.officeNum.description=辦公電話
seeyon.user.emailAddress=電子郵件
seeyon.user.emailAddress.description=電子郵件
seeyon.user.gender=性別：-1為未指定、1為男、2為女
seeyon.user.gender.description=性別：-1為未指定、1為男、2為女
seeyon.user.location=工作地
seeyon.user.location.description=工作地
seeyon.user.reporter=彙報人
seeyon.user.reporter.description=彙報人
seeyon.user.hiredate=入職時間
seeyon.user.hiredate.description=入職時間

# ====================== SEYON部門屬性 ======================
seeyon.org.id=ID
seeyon.org.id.description=ID
seeyon.org.orgAccountId=所屬單位ID
seeyon.org.orgAccountId.description=所屬單位ID
seeyon.org.name=部門名稱
seeyon.org.name.description=部門名稱
seeyon.org.superiorName=上級名稱
seeyon.org.superiorName.description=上級名稱
seeyon.org.code=部門代碼
seeyon.org.code.description=部門代碼
seeyon.org.sortId=排序號
seeyon.org.sortId.description=排序號
seeyon.org.shortName=單位簡稱
seeyon.org.shortName.description=單位簡稱
seeyon.org.createTime=創建時間
seeyon.org.createTime.description=創建時間
seeyon.org.updateTime=修改時間
seeyon.org.updateTime.description=修改時間
seeyon.org.isDeleted=是否刪除
seeyon.org.isDeleted.description=是否刪除
seeyon.org.enabled=是否啟用
seeyon.org.enabled.description=是否啟用
seeyon.org.status=狀態
seeyon.org.status.description=狀態
seeyon.org.desc=描述
seeyon.org.desc.description=描述
seeyon.org.path=路徑
seeyon.org.path.description=路徑
seeyon.org.secondName=外文名稱
seeyon.org.secondName.description=外文名稱
seeyon.org.isInternal=是否是內部機構
seeyon.org.isInternal.description=是否是內部機構
seeyon.org.isGroup=是否是集團
seeyon.org.isGroup.description=是否是集團
seeyon.org.type=類型
seeyon.org.type.description=類型
seeyon.org.levelScope=只對type=account有效
seeyon.org.levelScope.description=只對type=account有效
seeyon.org.properties=屬性
seeyon.org.properties.description=屬性
seeyon.org.fax=傳真
seeyon.org.fax.description=傳真
seeyon.org.unitMail=郵件
seeyon.org.unitMail.description=郵件
seeyon.org.address=地址
seeyon.org.address.description=地址
seeyon.org.chiefLeader=負責人
seeyon.org.chiefLeader.description=負責人
seeyon.org.zipCode=郵編
seeyon.org.zipCode.description=郵編
seeyon.org.telephone=電話
seeyon.org.telephone.description=電話
seeyon.org.superior=上級部門Id，一級部門則為所屬單位Id
seeyon.org.superior.description=上級部門Id，一級部門則為所屬單位Id
seeyon.org.sortIdType=sortIdType
seeyon.org.sortIdType.description=sortIdType
seeyon.org.isCanAccess=是否允許訪問
seeyon.org.isCanAccess.description=是否允許訪問
seeyon.org.entityType=實體類型
seeyon.org.entityType.description=實體類型
seeyon.org.valid=是否合法
seeyon.org.valid.description=是否合法
seeyon.org.parentPath=父部門的路徑
seeyon.org.parentPath.description=父部門的路徑

# ====================== BEISEN用戶屬性 ======================
beisen.user.originalId=外部ID標識(非北森系統)
beisen.user.originalId.description=外部ID標識(非北森系統)
beisen.user.employeeInfo=查詢員工信息數據結果
beisen.user.employeeInfo.description=查詢員工信息數據結果
beisen.user.recordInfo=查詢員工任職記錄數據結果
beisen.user.recordInfo.description=查詢員工任職記錄數據結果
beisen.user.userID=員工UserID
beisen.user.userID.description=員工UserID
beisen.user.name=姓名
beisen.user.name.description=姓名
beisen.user._Name=姓名(拼音)
beisen.user._Name.description=姓名(拼音)
beisen.user.phoneticOfXing=姓拼音
beisen.user.phoneticOfXing.description=姓拼音
beisen.user.phoneticOfMing=名拼音
beisen.user.phoneticOfMing.description=名拼音
beisen.user.gender=性別
beisen.user.gender.description=性別
beisen.user.email=電子郵件
beisen.user.email.description=電子郵件
beisen.user.iDType=證件類型
beisen.user.iDType.description=證件類型
beisen.user.iDNumber=證件號碼
beisen.user.iDNumber.description=證件號碼
beisen.user.isLongTermCertificate=是否長期證件
beisen.user.isLongTermCertificate.description=是否長期證件
beisen.user.certificateStartDate=證件開始日期
beisen.user.certificateStartDate.description=證件開始日期
beisen.user.certificateValidityTerm=證件結束日期
beisen.user.certificateValidityTerm.description=證件結束日期
beisen.user.birthday=出生日期
beisen.user.birthday.description=出生日期
beisen.user.workDate=參加工作時間
beisen.user.workDate.description=參加工作時間
beisen.user.homeAddress=聯繫地址
beisen.user.homeAddress.description=聯繫地址
beisen.user.mobilePhone=手機號碼
beisen.user.mobilePhone.description=手機號碼
beisen.user.weiXin=微信
beisen.user.weiXin.description=微信
beisen.user.iDPhoto=照片，DFS附件路徑
beisen.user.iDPhoto.description=照片，DFS附件路徑
beisen.user.smallIDPhoto=照片縮略圖
beisen.user.smallIDPhoto.description=照片縮略圖
beisen.user.iDPortraitSide=身份證人面像
beisen.user.iDPortraitSide.description=身份證人面像
beisen.user.iDCountryEmblemSide=身份證國徽面
beisen.user.iDCountryEmblemSide.description=身份證國徽面
beisen.user.allowToLoginIn=允許登錄系統
beisen.user.allowToLoginIn.description=允許登錄系統
beisen.user.personalHomepage=個人主頁
beisen.user.personalHomepage.description=個人主頁
beisen.user.speciality=特長
beisen.user.speciality.description=特長
beisen.user.major=專業
beisen.user.major.description=專業
beisen.user.postalCode=郵政編碼
beisen.user.postalCode.description=郵政編碼
beisen.user.passportNumber=護照號碼
beisen.user.passportNumber.description=護照號碼
beisen.user.constellation=星座
beisen.user.constellation.description=星座
beisen.user.bloodType=血型
beisen.user.bloodType.description=血型
beisen.user.residenceAddress=戶籍地址
beisen.user.residenceAddress.description=戶籍地址
beisen.user.joinPartyDate=入黨/團日期
beisen.user.joinPartyDate.description=入黨/團日期
beisen.user.domicileType=戶口類別
beisen.user.domicileType.description=戶口類別
beisen.user.emergencyContact=緊急聯繫人
beisen.user.emergencyContact.description=緊急聯繫人
beisen.user.emergencyContactRelationship=緊急聯繫人與本人關係
beisen.user.emergencyContactRelationship.description=緊急聯繫人與本人關係
beisen.user.emergencyContactPhone=聯繫電話
beisen.user.emergencyContactPhone.description=聯繫電話
beisen.user.qQ=QQ
beisen.user.qQ.description=QQ
beisen.user.aboutMe=關於我
beisen.user.aboutMe.description=關於我
beisen.user.homePhone=家庭電話
beisen.user.homePhone.description=家庭電話
beisen.user.graduateDate=畢業時間
beisen.user.graduateDate.description=畢業時間
beisen.user.marryCategory=婚姻狀況
beisen.user.marryCategory.description=婚姻狀況
beisen.user.politicalStatus=政治面貌
beisen.user.politicalStatus.description=政治面貌
beisen.user.nationality=國家（地區）
beisen.user.nationality.description=國家（地區）
beisen.user.nation=民族
beisen.user.nation.description=民族
beisen.user.birthplace=戶籍所在地
beisen.user.birthplace.description=戶籍所在地
beisen.user.registAddress=籍貫
beisen.user.registAddress.description=籍貫
beisen.user.educationLevel=最高學歷
beisen.user.educationLevel.description=最高學歷
beisen.user.lastSchool=畢業學校
beisen.user.lastSchool.description=畢業學校
beisen.user.engName=其他語言姓名
beisen.user.engName.description=其他語言姓名
beisen.user.firstname=其他語言姓名_名
beisen.user.firstname.description=其他語言姓名_名
beisen.user.lastname=其他語言姓名_姓
beisen.user.lastname.description=其他語言姓名_姓
beisen.user.officeTel=辦公電話
beisen.user.officeTel.description=辦公電話
beisen.user.businessAddress=辦公地址
beisen.user.businessAddress.description=辦公地址
beisen.user.backupMail=個人郵箱
beisen.user.backupMail.description=個人郵箱
beisen.user.applicantId=應聘者id
beisen.user.applicantId.description=應聘者id
beisen.user.applyIdV6=招聘申請id
beisen.user.applyIdV6.description=招聘申請id
beisen.user.applicantIdV6=招聘申請id
beisen.user.applicantIdV6.description=招聘申請id
beisen.user.age=年齡
beisen.user.age.description=年齡
beisen.user.businessModifiedBy=業務修改人
beisen.user.businessModifiedBy.description=業務修改人
beisen.user.businessModifiedTime=業務修改時間
beisen.user.businessModifiedTime.description=業務修改時間
beisen.user.sourceType=類型
beisen.user.sourceType.description=類型
beisen.user.firstEntryDate=首次進入公司日期
beisen.user.firstEntryDate.description=首次進入公司日期
beisen.user.latestEntryDate=最新進入公司日期
beisen.user.latestEntryDate.description=最新進入公司日期
beisen.user.tutorNew=導師UserId
beisen.user.tutorNew.description=導師UserId
beisen.user.iDFront=證件正面
beisen.user.iDFront.description=證件正面
beisen.user.iDBehind=證件反面
beisen.user.iDBehind.description=證件反面
beisen.user.preRetireDate=預計退休日期
beisen.user.preRetireDate.description=預計退休日期
beisen.user.actualRetireDate=實際退休日期
beisen.user.actualRetireDate.description=實際退休日期
beisen.user.isConfirmRetireDate=預計退休日期已核准
beisen.user.isConfirmRetireDate.description=預計退休日期已核准
beisen.user.activationState=賬號激活狀態
beisen.user.activationState.description=賬號激活狀態
beisen.user.issuingAuthority=簽發機關
beisen.user.issuingAuthority.description=簽發機關
beisen.user.isDisabled=是否殘疾人
beisen.user.isDisabled.description=是否殘疾人
beisen.user.disabledNumber=殘疾人號
beisen.user.disabledNumber.description=殘疾人號
beisen.user.objectId=業務對象實體主鍵GUID
beisen.user.objectId.description=業務對象實體主鍵GUID
beisen.user.createdBy=創建人員UserId
beisen.user.createdBy.description=創建人員UserId
beisen.user.createdTime=創建時間
beisen.user.createdTime.description=創建時間
beisen.user.modifiedBy=修改人員UserId
beisen.user.modifiedBy.description=修改人員UserId
beisen.user.modifiedTime=修改時間
beisen.user.modifiedTime.description=修改時間
beisen.user.stdIsDeleted=是否刪除
beisen.user.stdIsDeleted.description=是否刪除
beisen.user.userID1=任職記錄父對象員工信息業務實體UserID
beisen.user.userID1.description=任職記錄父對象員工信息業務實體UserID
beisen.user.pObjectDataID=父對象數據員工實體主鍵GUID
beisen.user.pObjectDataID.description=父對象數據員工實體主鍵GUID
beisen.user.oIdDepartment=任職部門（如技術部），對應於組織單元業務實體關聯的部門OId
beisen.user.oIdDepartment.description=任職部門（如技術部），對應於組織單元業務實體關聯的部門OId
beisen.user.startDate=任職記錄的生效時間，用於任職記錄的時間軸
beisen.user.startDate.description=任職記錄的生效時間，用於任職記錄的時間軸
beisen.user.stopDate=任職記錄的失效時間，用於任職記錄的時間軸
beisen.user.stopDate.description=任職記錄的失效時間，用於任職記錄的時間軸
beisen.user.jobNumber=工號
beisen.user.jobNumber.description=工號
beisen.user.entryDate=入職操作時，任職記錄的入職日期，用於任職記錄的時間軸
beisen.user.entryDate.description=入職操作時，任職記錄的入職日期，用於任職記錄的時間軸
beisen.user.lastWorkDate=離職操作時，任職記錄的最後工作日的日期，用於任職記錄的時間軸
beisen.user.lastWorkDate.description=離職操作時，任職記錄的最後工作日的日期，用於任職記錄的時間軸
beisen.user.regularizationDate=試用期轉正操作時，任職記錄的轉正日期
beisen.user.regularizationDate.description=試用期轉正操作時，任職記錄的轉正日期
beisen.user.probation=試用期
beisen.user.probation.description=試用期
beisen.user.order=排序號碼
beisen.user.order.description=排序號碼
beisen.user.employType=僱傭關係
beisen.user.employType.description=僱傭關係
beisen.user.serviceType=任職類型
beisen.user.serviceType.description=任職類型
beisen.user.serviceStatus=任職狀態
beisen.user.serviceStatus.description=任職狀態
beisen.user.approvalStatus=審批狀態
beisen.user.approvalStatus.description=審批狀態
beisen.user.employmentSource=人員來源實體對象
beisen.user.employmentSource.description=人員來源實體對象
beisen.user.employmentForm=用工形式實體對象
beisen.user.employmentForm.description=用工形式實體對象
beisen.user.isCharge=是否部門負責人
beisen.user.isCharge.description=是否部門負責人
beisen.user.oIdJobPost=職務實體
beisen.user.oIdJobPost.description=職務實體
beisen.user.oIdJobSequence=職務序列實體
beisen.user.oIdJobSequence.description=職務序列實體
beisen.user.oIdProfessionalLine=專業條線實體
beisen.user.oIdProfessionalLine.description=專業條線實體
beisen.user.oIdJobPosition=職位實體
beisen.user.oIdJobPosition.description=職位實體
beisen.user.oIdJobLevel=職級實體
beisen.user.oIdJobLevel.description=職級實體
beisen.user.oidJobGrade=職等
beisen.user.oidJobGrade.description=職等
beisen.user.place=工作地點
beisen.user.place.description=工作地點
beisen.user.employeeStatus=人員狀態
beisen.user.employeeStatus.description=人員狀態
beisen.user.employmentType=人員類別實體對象
beisen.user.employmentType.description=人員類別實體對象
beisen.user.employmentChangeID=任職變更記錄實體
beisen.user.employmentChangeID.description=任職變更記錄實體
beisen.user.changedStatus=變動後狀態
beisen.user.changedStatus.description=變動後狀態
beisen.user.pOIdEmpAdmin=直線經理UserID
beisen.user.pOIdEmpAdmin.description=直線經理UserID
beisen.user.pOIdEmpReserve2=虛線經理UserID
beisen.user.pOIdEmpReserve2.description=虛線經理UserID
beisen.user.businessTypeOID=變動業務類型OID
beisen.user.businessTypeOID.description=變動業務類型OID
beisen.user.changeTypeOID=變動類型ID
beisen.user.changeTypeOID.description=變動類型ID
beisen.user.entryStatus=入職狀態
beisen.user.entryStatus.description=入職狀態
beisen.user.isCurrentRecord=是否當前生效
beisen.user.isCurrentRecord.description=是否當前生效
beisen.user.lUOffer=LU_Offer 業務數據GUID
beisen.user.lUOffer.description=LU_Offer 業務數據GUID
beisen.user.workYearBefore=入職前累計工齡（年）
beisen.user.workYearBefore.description=入職前累計工齡（年）
beisen.user.workYearGroupBefore=入職前集團工齡（年）
beisen.user.workYearGroupBefore.description=入職前集團工齡（年）
beisen.user.workYearCompanyBefore=入職前司齡（年）
beisen.user.workYearCompanyBefore.description=入職前司齡（年）
beisen.user.workYearTotal=累計工齡（年）
beisen.user.workYearTotal.description=累計工齡（年）
beisen.user.workYearGroupTotal=累計集團工齡（年）
beisen.user.workYearGroupTotal.description=累計集團工齡（年）
beisen.user.workYearCompanyTotal=累計司齡
beisen.user.workYearCompanyTotal.description=累計司齡
beisen.user.oIdOrganization=任職機構（如子公司），對應於組織單元業務實體關聯的機構OId
beisen.user.oIdOrganization.description=任職機構（如子公司），對應於組織單元業務實體關聯的機構OId
beisen.user.whereabouts=離職後去向
beisen.user.whereabouts.description=離職後去向
beisen.user.blackStaffDesc=加黑說明
beisen.user.blackStaffDesc.description=加黑說明
beisen.user.blackListAddReason=加黑原因
beisen.user.blackListAddReason.description=加黑原因
beisen.user.transitionTypeOID=異動類型ID
beisen.user.transitionTypeOID.description=異動類型ID
beisen.user.changeReason=變動原因
beisen.user.changeReason.description=變動原因
beisen.user.probationResult=試用結果
beisen.user.probationResult.description=試用結果
beisen.user.probationActualStopDate=試用期實際結束日期
beisen.user.probationActualStopDate.description=試用期實際結束日期
beisen.user.probationStartDate=試用開始日期
beisen.user.probationStartDate.description=試用開始日期
beisen.user.probationStopDate=預計試用結束日期
beisen.user.probationStopDate.description=預計試用結束日期
beisen.user.isHaveProbation=是否有試用期
beisen.user.isHaveProbation.description=是否有試用期
beisen.user.remarks=備註
beisen.user.remarks.description=備註
beisen.user.addOrNotBlackList=是否加入黑名單
beisen.user.addOrNotBlackList.description=是否加入黑名單
beisen.user.businessModifiedBy1=任職機構
beisen.user.businessModifiedBy1.description=任職機構
beisen.user.businessModifiedTime1=任職機構
beisen.user.businessModifiedTime1.description=任職機構
beisen.user.traineeStartDate=實習開始日期
beisen.user.traineeStartDate.description=實習開始日期
beisen.user.objectId1=任職機構
beisen.user.objectId1.description=任職機構

# ====================== BEISEN部門屬性 ======================
beisen.org.costCenterOId=薪酬成本中心ID
beisen.org.costCenterOId.description=薪酬成本中心ID
beisen.org.name=部門機構名稱
beisen.org.name.description=部門機構名稱
beisen.org.name_en_US=部門機構名稱_英文
beisen.org.name_en_US.description=部門機構名稱_英文
beisen.org.name_zh_TW=部門機構名稱_繁體
beisen.org.name_zh_TW.description=部門機構名稱_繁體
beisen.org.shortName=部門機構簡稱
beisen.org.shortName.description=部門機構簡稱
beisen.org.code=組織單元編碼
beisen.org.code.description=組織單元編碼
beisen.org.oId=組織單元OId
beisen.org.oId.description=組織單元OId
beisen.org.level=組織層級實體對象
beisen.org.level.description=組織層級實體對象
beisen.org.status=狀態
beisen.org.status.description=狀態
beisen.org.establishDate=設立日期
beisen.org.establishDate.description=設立日期
beisen.org.startDate=生效日期
beisen.org.startDate.description=生效日期
beisen.org.stopDate=失效日期
beisen.org.stopDate.description=失效日期
beisen.org.changeDate=變更日期
beisen.org.changeDate.description=變更日期
beisen.org.pOIdOrgAdmin=行政維度上級組織OId
beisen.org.pOIdOrgAdmin.description=行政維度上級組織OId
beisen.org.pOIdOrgReserve2=業務維度上級組織OId
beisen.org.pOIdOrgReserve2.description=業務維度上級組織OId
beisen.org.pOIdOrgReserve3=產品維度組織OId
beisen.org.pOIdOrgReserve3.description=產品維度組織OId
beisen.org.isCurrentRecord=是否當前生效
beisen.org.isCurrentRecord.description=是否當前生效
beisen.org.personInCharge=部門負責人員工UserID
beisen.org.personInCharge.description=部門負責人員工UserID
beisen.org.hRBP=HRBP員工UserID
beisen.org.hRBP.description=HRBP員工UserID
beisen.org.shopOwner=店長員工
beisen.org.administrativeAssistant=行政助理,員工UserID
beisen.org.administrativeAssistant.description=行政助理的員工UserID
beisen.org.personInChargeDeputy=負責人（副職）,多員工UserID集合
beisen.org.personInChargeDeputy.description=負責人的副職員工UserID集合
beisen.org.businessModifiedBy=業務修改人
beisen.org.businessModifiedBy.description=業務修改人
beisen.org.businessModifiedTime=業務修改時間
beisen.org.businessModifiedTime.description=業務修改時間
beisen.org.legalMan=法人代表
beisen.org.legalMan.description=法人代表
beisen.org.address=地址
beisen.org.address.description=組織地址
beisen.org.fax=Fax
beisen.org.fax.description=組織傳真號碼
beisen.org.postcode=郵編
beisen.org.postcode.description=組織郵編
beisen.org.phone=電話
beisen.org.phone.description=組織電話號碼
beisen.org.url=網址
beisen.org.url.description=組織網址
beisen.org.description=簡介
beisen.org.description.description=組織簡介
beisen.org.number=文號
beisen.org.number.description=組織文號
beisen.org.broadType=組織大類字典鍵
beisen.org.broadType.description=組織大類字典鍵
beisen.org.economicType=經濟類型字典鍵
beisen.org.economicType.description=經濟類型字典鍵
beisen.org.industry=所屬行業字典鍵
beisen.org.industry.description=所屬行業字典鍵
beisen.org.place=所在地點字典鍵
beisen.org.place.description=所在地點字典鍵
beisen.org.orderAdmin=行政維度順序號
beisen.org.orderAdmin.description=行政維度順序號
beisen.org.orderReserve2=業務維度順序號
beisen.org.orderReserve2.description=業務維度順序號
beisen.org.orderReserve3=產品維度順序號
beisen.org.orderReserve3.description=產品維度順序號
beisen.org.comment=備註
beisen.org.comment.description=組織備註
beisen.org.oIdOrganizationType=組織類型實體對象（實體編碼：TenantBase.OrganizationType）業務數據GUID
beisen.org.oIdOrganizationType.description=組織類型實體對象（實體編碼：TenantBase.OrganizationType）業務數據GUID
beisen.org.pOIdOrgAdmin_TreePath=行政維度_路徑
beisen.org.pOIdOrgAdmin_TreePath.description=行政維度路徑
beisen.org.pOIdOrgAdmin_TreeLevel=行政維度_層級
beisen.org.pOIdOrgAdmin_TreeLevel.description=行政維度層級
beisen.org.pOIdOrgReserve2_TreePath=業務維度_路徑
beisen.org.pOIdOrgReserve2_TreePath.description=業務維度路徑
beisen.org.pOIdOrgReserve2_TreeLevel=業務維度_層級
beisen.org.pOIdOrgReserve2_TreeLevel.description=業務維度層級
beisen.org.firstLevelOrganization=一級組織OId
beisen.org.firstLevelOrganization.description=一級組織OId
beisen.org.secondLevelOrganization=二級組織OId
beisen.org.secondLevelOrganization.description=二級組織OId
beisen.org.thirdLevelOrganization=三級組織OId
beisen.org.thirdLevelOrganization.description=三級組織OId
beisen.org.fourthLevelOrganization=四級組織OId
beisen.org.fourthLevelOrganization.description=四級組織OId
beisen.org.fifthLevelOrganization=五級組織OId
beisen.org.fifthLevelOrganization.description=五級組織OId
beisen.org.sixthLevelOrganization=六級組織OId
beisen.org.sixthLevelOrganization.description=六級組織OId
beisen.org.seventhLevelOrganization=七級組織OId
beisen.org.seventhLevelOrganization.description=七級組織OId
beisen.org.eighthLevelOrganization=八級組織OId
beisen.org.eighthLevelOrganization.description=八級組織OId
beisen.org.ninthLevelOrganization=九級組織OId
beisen.org.ninthLevelOrganization.description=九級組織OId
beisen.org.tenthLevelOrganization=十級組織OId
beisen.org.tenthLevelOrganization.description=十級組織OId
beisen.org.orderCode=排序編碼
beisen.org.orderCode.description=組織排序編碼
beisen.org.pOIdOrgAdminNameTreePath=組織全稱
beisen.org.pOIdOrgAdminNameTreePath.description=組織全稱
beisen.org.isVirtualOrg=是否虛擬組織
beisen.org.isVirtualOrg.description=是否虛擬組織
beisen.org.leaderWithSpecificDuty=分管領導員工UserID
beisen.org.leaderWithSpecificDuty.description=分管領導的員工UserID
beisen.org.objectId=業務對象實體主鍵GUID
beisen.org.objectId.description=業務對象實體主鍵GUID

# SAML用戶屬性
saml.user.sub=sub

# SAML用戶屬性描述
saml.user.sub.description=用戶主題

# GOOGLE_SAML用戶屬性
google_saml.user.sub=sub

# GOOGLE_SAML用戶屬性描述
google_saml.user.sub.description=用戶主題

# ADFS_SAML用戶屬性
adfs_saml.user.sub=sub

# ADFS_SAML用戶屬性描述
adfs_saml.user.sub.description=用戶主題

# K3CLOUD用戶屬性
k3cloud.user.FID=FID
k3cloud.user.FName=FName
k3cloud.user.FStaffNumber=FStaffNumber
k3cloud.user.FMobile=FMobile
k3cloud.user.FTel=FTel
k3cloud.user.FEmail=FEmail
k3cloud.user.FDescription=FDescription
k3cloud.user.FAddress=FAddress
k3cloud.user.FUseOrgId=FUseOrgId
k3cloud.user.FCreateOrgId=FCreateOrgId
k3cloud.user.FBranchID=FBranchID
k3cloud.user.FCreateSaler=FCreateSaler
k3cloud.user.FCreateUser=FCreateUser
k3cloud.user.FUserRole=FUserRole
k3cloud.user.FCreateCashier=FCreateCashier
k3cloud.user.FCashierGrp=FCashierGrp
k3cloud.user.FSalerId=FSalerId
k3cloud.user.FCashierId=FCashierId
k3cloud.user.FUserId=FUserId
k3cloud.user.FPostId=FPostId
k3cloud.user.FJoinDate=FJoinDate
k3cloud.user.FUniportalNo=FUniportalNo
k3cloud.user.FSHRMapEntity=FSHRMapEntity
k3cloud.user.FPostEntity=FPostEntity
k3cloud.user.FBankInfo=FBankInfo
k3cloud.user.IsAutoSubmitAndAudit=IsAutoSubmitAndAudit
k3cloud.user.Id=Id
k3cloud.user.Name=Name
k3cloud.user.Number=Number

# K3CLOUD用戶屬性描述
k3cloud.user.FID.description=FID
k3cloud.user.FName.description=員工姓名
k3cloud.user.FStaffNumber.description=員工編號
k3cloud.user.FMobile.description=移動電話
k3cloud.user.FTel.description=固定電話
k3cloud.user.FEmail.description=電子郵箱
k3cloud.user.FDescription.description=描述
k3cloud.user.FAddress.description=聯繫地址
k3cloud.user.FUseOrgId.description=使用組織
k3cloud.user.FCreateOrgId.description=創建組織
k3cloud.user.FBranchID.description=所屬門店
k3cloud.user.FCreateSaler.description=創建銷售員
k3cloud.user.FCreateUser.description=創建Cloud用戶
k3cloud.user.FUserRole.description=用戶角色
k3cloud.user.FCreateCashier.description=創建POS收銀員
k3cloud.user.FCashierGrp.description=收銀員權限組
k3cloud.user.FSalerId.description=銷售員ID
k3cloud.user.FCashierId.description=收銀員ID
k3cloud.user.FUserId.description=用戶ID
k3cloud.user.FPostId.description=所屬崗位
k3cloud.user.FJoinDate.description=進店日期
k3cloud.user.FUniportalNo.description=統一賬號
k3cloud.user.FSHRMapEntity.description=FSHRMapEntity
k3cloud.user.FPostEntity.description=部門崗位信息
k3cloud.user.FBankInfo.description=銀行信息
k3cloud.user.IsAutoSubmitAndAudit.description=創建用戶自動提交
k3cloud.user.Id.description=用戶ID
k3cloud.user.Name.description=用戶名稱
k3cloud.user.Number.description=用戶編碼

# K3CLOUD部門屬性
k3cloud.org.FDEPTID=FDEPTID
k3cloud.org.FCreateOrgId=FCreateOrgId
k3cloud.org.FNumber=FNumber
k3cloud.org.FUseOrgId=FUseOrgId
k3cloud.org.FName=FName
k3cloud.org.FHelpCode=FHelpCode
k3cloud.org.FParentID=FParentID
k3cloud.org.FFullName=FFullName
k3cloud.org.FEffectDate=FEffectDate
k3cloud.org.FLapseDate=FLapseDate
k3cloud.org.FDeptProperty=FDeptProperty
k3cloud.org.FDescription=FDescription
k3cloud.org.FGroup=FGroup
k3cloud.org.FIsCopyFlush=FIsCopyFlush
k3cloud.org.FFinishQtyDepend=FFinishQtyDepend
k3cloud.org.FIsDetailDpt=FIsDetailDpt
k3cloud.org.FSHRMapEntity=FSHRMapEntity
k3cloud.org.IsAutoSubmitAndAudit=IsAutoSubmitAndAudit
k3cloud.org.Id=Id
k3cloud.org.Name=Name
k3cloud.org.Number=Number
k3cloud.org.ParentID=ParentID
k3cloud.org.Description=Description

# K3CLOUD部門屬性描述
k3cloud.org.FDEPTID.description=FDEPTID
k3cloud.org.FCreateOrgId.description=創建組織
k3cloud.org.FNumber.description=編碼
k3cloud.org.FUseOrgId.description=使用組織
k3cloud.org.FName.description=名稱
k3cloud.org.FHelpCode.description=助記碼
k3cloud.org.FParentID.description=上級部門
k3cloud.org.FFullName.description=部門全稱
k3cloud.org.FEffectDate.description=生效日期
k3cloud.org.FLapseDate.description=失效日期
k3cloud.org.FDeptProperty.description=部門屬性
k3cloud.org.FDescription.description=描述
k3cloud.org.FGroup.description=部門分組
k3cloud.org.FIsCopyFlush.description=副產品倒衝
k3cloud.org.FFinishQtyDepend.description=更新已排
k3cloud.org.FIsDetailDpt.description=是否明細部門
k3cloud.org.FSHRMapEntity.description=FSHRMapEntity
k3cloud.org.IsAutoSubmitAndAudit.description=創建部門是否自動提交
k3cloud.org.Id.description=部門ID
k3cloud.org.Name.description=部門名稱
k3cloud.org.Number.description=部門編碼
k3cloud.org.ParentID.description=父部門
k3cloud.org.Description.description=描述

# YYU8C用戶屬性
yyu8c.user.accopendate=開戶日期
yyu8c.user.account=賬號
yyu8c.user.accountcode=賬戶編碼
yyu8c.user.accountname=賬戶名稱
yyu8c.user.bankarea=開戶地區
yyu8c.user.city=城市
yyu8c.user.combineaccnum=聯行號
yyu8c.user.contactpsn=聯繫人
yyu8c.user.custcode=客戶編碼
yyu8c.user.groupid=集團號
yyu8c.user.memo=備註
yyu8c.user.netqueryflag=網銀開通狀態
yyu8c.user.orgnumber=機構號
yyu8c.user.pk_bankdoc=開戶銀行
yyu8c.user.pk_banktype=銀行類別
yyu8c.user.pk_currtype=幣種
yyu8c.user.pk_netbankinftp=網銀接口類別
yyu8c.user.province=省份
yyu8c.user.remcode=助記碼
yyu8c.user.signflag=是否簽約
yyu8c.user.tel=聯繫電話
yyu8c.user.unitname=單位名稱
yyu8c.user.isreimburse=默認報銷卡
yyu8c.user.pk_psnaccbank=個人銀行賬戶主鍵
yyu8c.user.currentcorp=公司主屬性
yyu8c.user.addr=家庭地址
yyu8c.user.birthdate=出生日期
yyu8c.user.bp=呼機
yyu8c.user.email=電子郵件
yyu8c.user.homephone=家庭電話
yyu8c.user.id=身份證號
yyu8c.user.isassociated=是否關聯其它公司人員(y/n)（3.6及之後支持）
yyu8c.user.joinworkdate=參加工作日期
yyu8c.user.mobile=手機
yyu8c.user.officephone=辦公電話
yyu8c.user.pk_corp=歸屬公司
yyu8c.user.pk_psnbasdoc=人員基本檔案主鍵
yyu8c.user.postalcode=郵政編碼
yyu8c.user.psnname=姓名
yyu8c.user.sex=性別
yyu8c.user.ssnum=社會保障號
yyu8c.user.usedname=曾用名
yyu8c.user.vdef1=自定義項1
yyu8c.user.vdef10=自定義項10
yyu8c.user.vdef11=自定義項11
yyu8c.user.vdef12=自定義項12
yyu8c.user.vdef13=自定義項13
yyu8c.user.vdef14=自定義項14
yyu8c.user.vdef15=自定義項15
yyu8c.user.vdef16=自定義項16
yyu8c.user.vdef17=自定義項17
yyu8c.user.vdef18=自定義項18
yyu8c.user.vdef19=自定義項19
yyu8c.user.vdef2=自定義項2
yyu8c.user.vdef20=自定義項20
yyu8c.user.vdef3=自定義項3
yyu8c.user.vdef4=自定義項4
yyu8c.user.vdef5=自定義項5
yyu8c.user.vdef6=自定義項6
yyu8c.user.vdef7=自定義項7
yyu8c.user.vdef8=自定義項8
yyu8c.user.vdef9=自定義項9
yyu8c.user.amcode=助記碼
yyu8c.user.clerkcode=業務員編號
yyu8c.user.clerkflag=業務員標誌（y/n）
yyu8c.user.def1=自定義項1
yyu8c.user.def10=自定義項10
yyu8c.user.def11=自定義項11
yyu8c.user.def12=自定義項12
yyu8c.user.def13=自定義項13
yyu8c.user.def14=自定義項14
yyu8c.user.def15=自定義項15
yyu8c.user.def16=自定義項16
yyu8c.user.def17=自定義項17
yyu8c.user.def18=自定義項18
yyu8c.user.def19=自定義項19
yyu8c.user.def2=自定義項2
yyu8c.user.def20=自定義項20
yyu8c.user.def3=自定義項3
yyu8c.user.def4=自定義項4
yyu8c.user.def5=自定義項5
yyu8c.user.def6=自定義項6
yyu8c.user.def7=自定義項7
yyu8c.user.def8=自定義項8
yyu8c.user.def9=自定義項9
yyu8c.user.indutydate=到職日期
yyu8c.user.outdutydate=離職日期
yyu8c.user.pk_deptdoc=部門
yyu8c.user.pk_psncl=人員類別,01在職員工
yyu8c.user.psncode=編碼

# YYU8C用戶屬性描述
yyu8c.user.accopendate.description=開戶日期
yyu8c.user.account.description=賬號
yyu8c.user.accountcode.description=賬戶編碼
yyu8c.user.accountname.description=賬戶名稱
yyu8c.user.bankarea.description=開戶地區
yyu8c.user.city.description=城市
yyu8c.user.combineaccnum.description=聯行號
yyu8c.user.contactpsn.description=聯繫人
yyu8c.user.custcode.description=客戶編碼
yyu8c.user.groupid.description=集團號
yyu8c.user.memo.description=備註
yyu8c.user.netqueryflag.description=網銀開通狀態
yyu8c.user.orgnumber.description=機構號
yyu8c.user.pk_bankdoc.description=開戶銀行
yyu8c.user.pk_banktype.description=銀行類別
yyu8c.user.pk_currtype.description=幣種
yyu8c.user.pk_netbankinftp.description=網銀接口類別
yyu8c.user.province.description=省份
yyu8c.user.remcode.description=助記碼
yyu8c.user.signflag.description=是否簽約
yyu8c.user.tel.description=聯繫電話
yyu8c.user.unitname.description=單位名稱
yyu8c.user.isreimburse.description=默認報銷卡
yyu8c.user.pk_psnaccbank.description=個人銀行賬戶主鍵
yyu8c.user.currentcorp.description=公司主屬性
yyu8c.user.addr.description=家庭地址
yyu8c.user.birthdate.description=出生日期
yyu8c.user.bp.description=呼機
yyu8c.user.email.description=電子郵件
yyu8c.user.homephone.description=家庭電話
yyu8c.user.id.description=身份證號
yyu8c.user.isassociated.description=是否關聯其它公司人員(y/n)（3.6及之後支持）
yyu8c.user.joinworkdate.description=參加工作日期
yyu8c.user.mobile.description=手機
yyu8c.user.officephone.description=辦公電話
yyu8c.user.pk_corp.description=歸屬公司
yyu8c.user.pk_psnbasdoc.description=人員基本檔案主鍵
yyu8c.user.postalcode.description=郵政編碼
yyu8c.user.psnname.description=姓名
yyu8c.user.sex.description=性別
yyu8c.user.ssnum.description=社會保障號
yyu8c.user.usedname.description=曾用名
yyu8c.user.vdef1.description=自定義項1
yyu8c.user.vdef10.description=自定義項10
yyu8c.user.vdef11.description=自定義項11
yyu8c.user.vdef12.description=自定義項12
yyu8c.user.vdef13.description=自定義項13
yyu8c.user.vdef14.description=自定義項14
yyu8c.user.vdef15.description=自定義項15
yyu8c.user.vdef16.description=自定義項16
yyu8c.user.vdef17.description=自定義項17
yyu8c.user.vdef18.description=自定義項18
yyu8c.user.vdef19.description=自定義項19
yyu8c.user.vdef2.description=自定義項2
yyu8c.user.vdef20.description=自定義項20
yyu8c.user.vdef3.description=自定義項3
yyu8c.user.vdef4.description=自定義項4
yyu8c.user.vdef5.description=自定義項5
yyu8c.user.vdef6.description=自定義項6
yyu8c.user.vdef7.description=自定義項7
yyu8c.user.vdef8.description=自定義項8
yyu8c.user.vdef9.description=自定義項9
yyu8c.user.amcode.description=助記碼
yyu8c.user.clerkcode.description=業務員編號
yyu8c.user.clerkflag.description=業務員標誌（y/n）
yyu8c.user.def1.description=自定義項1
yyu8c.user.def10.description=自定義項10
yyu8c.user.def11.description=自定義項11
yyu8c.user.def12.description=自定義項12
yyu8c.user.def13.description=自定義項13
yyu8c.user.def14.description=自定義項14
yyu8c.user.def15.description=自定義項15
yyu8c.user.def16.description=自定義項16
yyu8c.user.def17.description=自定義項17
yyu8c.user.def18.description=自定義項18
yyu8c.user.def19.description=自定義項19
yyu8c.user.def2.description=自定義項2
yyu8c.user.def20.description=自定義項20
yyu8c.user.def3.description=自定義項3
yyu8c.user.def4.description=自定義項4
yyu8c.user.def5.description=自定義項5
yyu8c.user.def6.description=自定義項6
yyu8c.user.def7.description=自定義項7
yyu8c.user.def8.description=自定義項8
yyu8c.user.def9.description=自定義項9
yyu8c.user.indutydate.description=到職日期
yyu8c.user.outdutydate.description=離職日期
yyu8c.user.pk_deptdoc.description=部門
yyu8c.user.pk_psncl.description=人員類別,01在職員工
yyu8c.user.psncode.description=編碼

# YYU8C部門屬性
yyu8c.org.addr=部門地址
yyu8c.org.createDate=創建時間
yyu8c.org.deptattr=部門屬性（其他部門1，採購2，銷售3，採購銷售4）
yyu8c.org.pk_corp=公司主鍵
yyu8c.org.unitname=公司名稱
yyu8c.org.unitcode=公司編碼
yyu8c.org.pk_deptdoc=部門檔案主鍵
yyu8c.org.pk_fathedept=上級編碼
yyu8c.org.deptcode=部門編碼
yyu8c.org.deptname=部門名稱
yyu8c.org.deptshortname=簡稱
yyu8c.org.depttype=部門類型
yyu8c.org.isuseretail=是否用於零售
yyu8c.org.def1=自定義1
yyu8c.org.def10=自定義10
yyu8c.org.def11=自定義11
yyu8c.org.def12=自定義12
yyu8c.org.def13=自定義13
yyu8c.org.def14=自定義14
yyu8c.org.def15=自定義15
yyu8c.org.def16=自定義16
yyu8c.org.def17=自定義17
yyu8c.org.def18=自定義18
yyu8c.org.def19=自定義19
yyu8c.org.def2=自定義2
yyu8c.org.def20=自定義20
yyu8c.org.def3=自定義3
yyu8c.org.def4=自定義4
yyu8c.org.def5=自定義5
yyu8c.org.def6=自定義6
yyu8c.org.def7=自定義7
yyu8c.org.def8=自定義8
yyu8c.org.def9=自定義9
yyu8c.org.deptduty=部門職責
yyu8c.org.deptlevel=部門級別
yyu8c.org.memo=備註
yyu8c.org.phone=部門電話
yyu8c.org.pk_calbody=test
yyu8c.org.pk_psndoc=負責人編碼
yyu8c.org.pk_psndoc2=負責人2
yyu8c.org.pk_psndoc3=負責人3
yyu8c.org.remcode=助記碼

# YYU8C部門屬性描述
yyu8c.org.addr.description=部門地址
yyu8c.org.createDate.description=創建時間
yyu8c.org.deptattr.description=部門屬性（其他部門1，採購2，銷售3，採購銷售4）
yyu8c.org.pk_corp.description=公司主鍵
yyu8c.org.unitname.description=公司名稱
yyu8c.org.unitcode.description=公司編碼
yyu8c.org.pk_deptdoc.description=部門檔案主鍵
yyu8c.org.pk_fathedept.description=上級編碼
yyu8c.org.deptcode.description=部門編碼
yyu8c.org.deptname.description=部門名稱
yyu8c.org.deptshortname.description=簡稱
yyu8c.org.depttype.description=部門類型
yyu8c.org.isuseretail.description=是否用於零售
yyu8c.org.def1.description=自定義1
yyu8c.org.def10.description=自定義10
yyu8c.org.def11.description=自定義11
yyu8c.org.def12.description=自定義12
yyu8c.org.def13.description=自定義13
yyu8c.org.def14.description=自定義14
yyu8c.org.def15.description=自定義15
yyu8c.org.def16.description=自定義16
yyu8c.org.def17.description=自定義17
yyu8c.org.def18.description=自定義18
yyu8c.org.def19.description=自定義19
yyu8c.org.def2.description=自定義2
yyu8c.org.def20.description=自定義20
yyu8c.org.def3.description=自定義3
yyu8c.org.def4.description=自定義4
yyu8c.org.def5.description=自定義5
yyu8c.org.def6.description=自定義6
yyu8c.org.def7.description=自定義7
yyu8c.org.def8.description=自定義8
yyu8c.org.def9.description=自定義9
yyu8c.org.deptduty.description=部門職責
yyu8c.org.deptlevel.description=部門級別
yyu8c.org.memo.description=備註
yyu8c.org.phone.description=部門電話
yyu8c.org.pk_calbody.description=test
yyu8c.org.pk_psndoc.description=負責人編碼
yyu8c.org.pk_psndoc2.description=負責人2
yyu8c.org.pk_psndoc3.description=負責人3
yyu8c.org.remcode.description=助記碼

# FBT用戶屬性
fbt.user.third_id=三方系統人員ID，不超過50字符確保唯一性
fbt.user.name=姓名
fbt.user.phone=手機號碼
fbt.user.role=默認角色枚舉值：2：普通管理員 3：普通人員(若不填則默認為管理後臺的普通人員權限角色
fbt.user.third_roles=三方系統角色列表
fbt.user.third_dept_id=三方系統部門ID
fbt.user.code=人員工號
fbt.user.email=人員郵箱,如果填寫了郵箱，人員會收到分貝通賬戶開通郵件
fbt.user.gender=性別：-1為未指定、1為男、2為女
fbt.user.birthday=出生日期
fbt.user.country_code=國籍編碼
fbt.user.cert_name=證件姓名
fbt.user.cert_last_name=證件英文姓
fbt.user.cert_first_name=證件英文名
fbt.user.type=證件類型枚舉值1：身份證2：護照3：回鄉證4：臺胞證5：往來港澳通行證6： 大陸居民往來臺灣通行證7：港澳居民居住證8：臺灣居民居住證9：外國人永久居留證999：其他
fbt.user.number=證件號碼
fbt.user.validity_type=有效期類型：0：有效期，1：長期有效
fbt.user.expire_date=有效期，格式為YYYY-MM-DD
fbt.user.third_rank_id=三方系統職級ID，職級ID與名稱同時傳入信息不匹配時報錯
fbt.user.rank_name=職級名稱
fbt.user.template_id=分貝通消費權限模版ID,為空時人員默認綁定分貝通
fbt.user.template_name=分貝通消費權限模版名稱，id和name都傳時使用id進行同步,為空時人員默認綁定分貝通
fbt.user.third_entity_id=三方系統法人實體ID
fbt.user.third_senior_id=三方系統直屬上級ID
fbt.user.city_id=城市ID
fbt.user.state=人員狀態人員新增該字段傳入無效，默認為啟用狀態，狀態枚舉值：1：啟用2：禁用，禁用後企業中商務消費功能不可使用
fbt.user.group_positions=職位信息

# FBT用戶屬性描述
fbt.user.third_id.description=三方系統人員ID，不超過50字符確保唯一性
fbt.user.name.description=姓名
fbt.user.phone.description=手機號碼
fbt.user.role.description=默認角色枚舉值：2：普通管理員 3：普通人員(若不填則默認為管理後臺的普通人員權限角色
fbt.user.third_roles.description=三方系統角色列表
fbt.user.third_dept_id.description=三方系統部門ID
fbt.user.code.description=人員工號
fbt.user.email.description=人員郵箱,如果填寫了郵箱，人員會收到分貝通賬戶開通郵件
fbt.user.gender.description=性別：-1為未指定、1為男、2為女
fbt.user.birthday.description=出生日期
fbt.user.country_code.description=國籍編碼
fbt.user.cert_name.description=證件姓名
fbt.user.cert_last_name.description=證件英文姓
fbt.user.cert_first_name.description=證件英文名
fbt.user.type.description=證件類型枚舉值1：身份證2：護照3：回鄉證4：臺胞證5：往來港澳通行證6： 大陸居民往來臺灣通行證7：港澳居民居住證8：臺灣居民居住證9：外國人永久居留證999：其他
fbt.user.number.description=證件號碼
fbt.user.validity_type.description=有效期類型：0：有效期，1：長期有效
fbt.user.expire_date.description=有效期，格式為YYYY-MM-DD
fbt.user.third_rank_id.description=三方系統職級ID，職級ID與名稱同時傳入信息不匹配時報錯
fbt.user.rank_name.description=職級名稱
fbt.user.template_id.description=分貝通消費權限模版ID,為空時人員默認綁定分貝通
fbt.user.template_name.description=分貝通消費權限模版名稱，id和name都傳時使用id進行同步,為空時人員默認綁定分貝通
fbt.user.third_entity_id.description=三方系統法人實體ID
fbt.user.third_senior_id.description=三方系統直屬上級ID
fbt.user.city_id.description=城市ID
fbt.user.state.description=人員狀態人員新增該字段傳入無效，默認為啟用狀態，狀態枚舉值：1：啟用2：禁用，禁用後企業中商務消費功能不可使用
fbt.user.group_positions.description=職位信息

# FBT部門屬性
fbt.org.third_id=部門ID，唯一標識
fbt.org.name=部門名稱
fbt.org.third_parent_id=三方系統父級部門ID
fbt.org.code=部門代碼
fbt.org.manager=部門主管
fbt.org.state=是否啟用

# FBT部門屬性描述
fbt.org.third_id.description=部門ID，唯一標識
fbt.org.name.description=部門名稱
fbt.org.third_parent_id.description=三方系統父級部門ID
fbt.org.code.description=部門代碼
fbt.org.manager.description=部門主管
fbt.org.state.description=是否啟用

# HLY用戶屬性
hly.user.custDeptNumber=部門編碼
hly.user.fullName=姓名
hly.user.employeeID=員工工號，租戶內不能重複
hly.user.companyOID=公司OID，集團登陸賬戶必填,公司登陸賬戶可不填,必填時，companyOID和companyCode二選一即可，都填時，以companyOID為準
hly.user.companyCode=公司編碼，集團登陸賬戶必填,公司登陸賬戶可不填,必填時，companyOID和companyCode二選一即可，都填時，以companyOID為準
hly.user.email=郵箱，長度:1到255個字符,內部員工和外部員工租戶內不能重複
hly.user.entryTime=入職時間，格式:yyyy-MM-dd
hly.user.mobile=手機號，內部員工和外部員工租戶內不能重複
hly.user.countryCode=國家編碼，不填默認為中國，不同國家手機號碼長度不同
hly.user.password=密碼,密碼為空，則默認密碼為手機號碼，如果手機號碼也為空，則密碼為工號(不足6位，工號後補0)
hly.user.title=職位,不傳默認創建職位為"員工"的崗位
hly.user.activated=是否激活,true表示創建後自動激活，false表示創建後需要員工自己手動激活
hly.user.birthday=生日,格式:yyyy-MM-dd
hly.user.genderCode=性別編碼,0--男;1--女;2--未知
hly.user.employeeTypeCode=人員類型編碼，如果需要人員組按照人員類型匹配，則需要傳人員類型編碼
hly.user.dutyCode=職務編碼，如果需要人員組按照職務匹配，則需要傳職務編碼
hly.user.rankCode=級別編碼，如果需要用級別值列表做差標管控和人員組按照級別匹配，則需要傳級別編碼
hly.user.directManagerOID=主崗直屬領導OID
hly.user.directManagerEmpoyeeId=主崗直屬領導工號
hly.user.language=語言編碼,zh_CN-中文簡體,zh_TW-中文繁體,en-英語,ms-馬來語,ja-日語,vi_VN-越南語,es_ES-西班牙,pt_PT-葡萄牙,it_IT-意大利語,ko_KR-韓語
hly.user.coverModeForUserJobs=多崗是否為覆蓋模式，默認為false,設置為true時，userJobsDtos裡面的isDeleted參數不能為true
hly.user.approvalLevel=審批層級，不超過8位的正整數
hly.user.mutiJobDirectManagerMode=是否按多崗更新主崗上級崗位編碼，默認為false
hly.user.contactBankAccountDTOs=銀行賬戶信息
hly.user.contactCardDTOs=證件集合
hly.user.customFormValues=用戶屬性擴展字段
hly.user.subAccountName=用戶攜程供應商實體，子賬戶，值列表項中無論是否存在，都保存或者更新
hly.user.subAccountCode=用戶攜程供應商實體，子賬戶編碼，需要校驗是否存在於值列表項中，不存在報錯，存在保存或者更新
hly.user.confirmUserOID=用戶攜程供應商實體，授權人OID
hly.user.confirmUserEmployeeID=用戶攜程供應商實體，授權人工號
hly.user.confirmCCUserOID=用戶攜程供應商實體，抄送授權人
hly.user.confirmCCUserEmployeeID=用戶攜程供應商實體，抄送授權人工號
hly.user.confirm2UserOID=用戶攜程供應商實體，二次授權人
hly.user.confirm2UserEmployeeID=用戶攜程供應商實體，二次授權人工號
hly.user.confirm2CCUserOID=用戶攜程供應商實體，抄送二次授權人
hly.user.confirm2CCUserEmployeeID=用戶攜程供應商實體，抄送二次授權人工號
hly.user.rescheduledAuthUserOID=用戶攜程供應商實體，改簽授權人OID
hly.user.rescheduledAuthUserEmployeeID=用戶攜程供應商實體，改簽授權人工號
hly.user.confirmPassword=用戶攜程供應商實體，攜程授權碼
hly.user.helpOthersBookCode=用戶攜程供應商實體，代訂類型,B:可以;C:不可以
hly.user.bookTheScopeCode=用戶攜程供應商實體，代訂範圍,C:整個公司;F:員工所在攜程主賬戶;S:員工所在攜程子賬戶;O:指定具體代訂名單
hly.user.agencyEmployeeIds=用戶攜程供應商實體，代訂員工集合,bookTheScopeCode為O時生效
hly.user.positionShield=用戶攜程供應商實體，標籤倉位屏蔽,Y:屏蔽;N:不屏蔽;默認不屏蔽
hly.user.changeTicketAuthType=用戶攜程供應商實體，改簽授權類型,Needless:不需要;CompanyAccountOnly:僅公司賬戶;PersonalPayment:僅個人支付;Both:混付（公司賬戶+個人支付）;FlightAuthNAlterAuto:改簽自動授權
hly.user.changeTicketNeedAuth=用戶攜程供應商實體，國內改簽免授權金額控制,T:需要,F:不需要
hly.user.changeTicketAuthAmount=用戶攜程供應商實體，國內改簽免授權金額,國內改簽免授權金額控制為T時必填
hly.user.kgSecondAuthEmployeeID=空港嘉華實體，二次授權人工號
hly.user.kgenabled=空港嘉華實體，是否啟用,默認為true,啟用
hly.user.supplierDTOs=通用二次授權人集合
hly.user.userJobsDtos=用戶崗位列表

# HLY用戶屬性描述
hly.user.custDeptNumber.description=部門編碼
hly.user.fullName.description=姓名
hly.user.employeeID.description=員工工號，租戶內不能重複
hly.user.companyOID.description=公司OID，集團登陸賬戶必填,公司登陸賬戶可不填,必填時，companyOID和companyCode二選一即可，都填時，以companyOID為準
hly.user.companyCode.description=公司編碼，集團登陸賬戶必填,公司登陸賬戶可不填,必填時，companyOID和companyCode二選一即可，都填時，以companyOID為準
hly.user.email.description=郵箱，長度:1到255個字符,內部員工和外部員工租戶內不能重複
hly.user.entryTime.description=入職時間，格式:yyyy-MM-dd
hly.user.mobile.description=手機號，內部員工和外部員工租戶內不能重複
hly.user.countryCode.description=國家編碼，不填默認為中國，不同國家手機號碼長度不同
hly.user.password.description=密碼,密碼為空，則默認密碼為手機號碼，如果手機號碼也為空，則密碼為工號(不足6位，工號後補0)
hly.user.title.description=職位,不傳默認創建職位為"員工"的崗位
hly.user.activated.description=是否激活,true表示創建後自動激活，false表示創建後需要員工自己手動激活
hly.user.birthday.description=生日,格式:yyyy-MM-dd
hly.user.genderCode.description=性別編碼,0--男;1--女;2--未知
hly.user.employeeTypeCode.description=人員類型編碼，如果需要人員組按照人員類型匹配，則需要傳人員類型編碼
hly.user.dutyCode.description=職務編碼，如果需要人員組按照職務匹配，則需要傳職務編碼
hly.user.rankCode.description=級別編碼，如果需要用級別值列表做差標管控和人員組按照級別匹配，則需要傳級別編碼
hly.user.directManagerOID.description=主崗直屬領導OID
hly.user.directManagerEmpoyeeId.description=主崗直屬領導工號
hly.user.language.description=語言編碼,zh_CN-中文簡體,zh_TW-中文繁體,en-英語,ms-馬來語,ja-日語,vi_VN-越南語,es_ES-西班牙,pt_PT-葡萄牙,it_IT-意大利語,ko_KR-韓語
hly.user.coverModeForUserJobs.description=多崗是否為覆蓋模式，默認為false,設置為true時，userJobsDtos裡面的isDeleted參數不能為true
hly.user.approvalLevel.description=審批層級，不超過8位的正整數
hly.user.mutiJobDirectManagerMode.description=是否按多崗更新主崗上級崗位編碼，默認為false
hly.user.contactBankAccountDTOs.description=銀行賬戶信息
hly.user.contactCardDTOs.description=證件集合
hly.user.customFormValues.description=用戶屬性擴展字段
hly.user.subAccountName.description=用戶攜程供應商實體，子賬戶，值列表項中無論是否存在，都保存或者更新
hly.user.subAccountCode.description=用戶攜程供應商實體，子賬戶編碼，需要校驗是否存在於值列表項中，不存在報錯，存在保存或者更新
hly.user.confirmUserOID.description=用戶攜程供應商實體，授權人OID
hly.user.confirmUserEmployeeID.description=用戶攜程供應商實體，授權人工號
hly.user.confirmCCUserOID.description=用戶攜程供應商實體，抄送授權人
hly.user.confirmCCUserEmployeeID.description=用戶攜程供應商實體，抄送授權人工號
hly.user.confirm2UserOID.description=用戶攜程供應商實體，二次授權人
hly.user.confirm2UserEmployeeID.description=用戶攜程供應商實體，二次授權人工號
hly.user.confirm2CCUserOID.description=用戶攜程供應商實體，抄送二次授權人
hly.user.confirm2CCUserEmployeeID.description=用戶攜程供應商實體，抄送二次授權人工號
hly.user.rescheduledAuthUserOID.description=用戶攜程供應商實體，改簽授權人OID
hly.user.rescheduledAuthUserEmployeeID.description=用戶攜程供應商實體，改簽授權人工號
hly.user.confirmPassword.description=用戶攜程供應商實體，攜程授權碼
hly.user.helpOthersBookCode.description=用戶攜程供應商實體，代訂類型,B:可以;C:不可以
hly.user.bookTheScopeCode.description=用戶攜程供應商實體，代訂範圍,C:整個公司;F:員工所在攜程主賬戶;S:員工所在攜程子賬戶;O:指定具體代訂名單
hly.user.agencyEmployeeIds.description=用戶攜程供應商實體，代訂員工集合,bookTheScopeCode為O時生效
hly.user.positionShield.description=用戶攜程供應商實體，標籤倉位屏蔽,Y:屏蔽;N:不屏蔽;默認不屏蔽
hly.user.changeTicketAuthType.description=用戶攜程供應商實體，改簽授權類型,Needless:不需要;CompanyAccountOnly:僅公司賬戶;PersonalPayment:僅個人支付;Both:混付（公司賬戶+個人支付）;FlightAuthNAlterAuto:改簽自動授權
hly.user.changeTicketNeedAuth.description=用戶攜程供應商實體，國內改簽免授權金額控制,T:需要,F:不需要
hly.user.changeTicketAuthAmount.description=用戶攜程供應商實體，國內改簽免授權金額,國內改簽免授權金額控制為T時必填
hly.user.kgSecondAuthEmployeeID.description=空港嘉華實體，二次授權人工號
hly.user.kgenabled.description=空港嘉華實體，是否啟用,默認為true,啟用
hly.user.supplierDTOs.description=通用二次授權人集合
hly.user.userJobsDtos.description=用戶崗位列表

# HLY部門屬性
hly.org.departmentName=部門名稱
hly.org.custDeptNumber=部門編碼
hly.org.managerEmployeeId=部門經理工號
hly.org.parentCustDeptNumber=上級部門編碼
hly.org.companyCode=公司編碼,當部門關聯公司開關開啟時，必填，開啟關聯後部門不可更改關聯公司;未開啟時，忽略此字段
hly.org.sequenceNumber=序號，取值1-999，可重複
hly.org.approvalLevel=審批層級，不超過8位的正整數
hly.org.departmentType=部門屬性可以為空、"ENTITY"、"VIRTUAL" ,為空默認為"ENTITY"
hly.org.departmentPositionList=部門角色集合
hly.org.customFormValues=擴展集合
hly.org.departmentOID=集成使用，創建的部門OID
hly.org.companyOID=集成使用，公司OID
hly.org.managerOID=集成使用，主管經理OID
hly.org.managerName=集成使用，主管經理姓名
hly.org.status=集成使用，狀態，101--啟用;102--禁用;103--刪除
hly.org.path=集成使用，部門路徑
hly.org.departmentParentOID=集成使用，父部門OID
hly.org.hasChildrenDepartments=集成使用，是否有子部門
hly.org.hasUsers=集成使用，是否有用戶
hly.org.positionName=集成使用，部門角色名稱
hly.org.userName=集成使用，員工名稱
hly.org.employeeId1=集成使用，員工工號
hly.org.positionCode1=集成使用，部門角色編碼

# HLY部門屬性描述
hly.org.departmentName.description=部門名稱
hly.org.custDeptNumber.description=部門編碼
hly.org.managerEmployeeId.description=部門經理工號
hly.org.parentCustDeptNumber.description=上級部門編碼
hly.org.companyCode.description=公司編碼,當部門關聯公司開關開啟時，必填，開啟關聯後部門不可更改關聯公司;未開啟時，忽略此字段
hly.org.sequenceNumber.description=序號，取值1-999，可重複
hly.org.approvalLevel.description=審批層級，不超過8位的正整數
hly.org.departmentType.description=部門屬性可以為空、"ENTITY"、"VIRTUAL" ,為空默認為"ENTITY"
hly.org.departmentPositionList.description=部門角色集合
hly.org.customFormValues.description=擴展集合
hly.org.departmentOID.description=集成使用，創建的部門OID
hly.org.companyOID.description=集成使用，公司OID
hly.org.managerOID.description=集成使用，主管經理OID
hly.org.managerName.description=集成使用，主管經理姓名
hly.org.status.description=集成使用，狀態，101--啟用;102--禁用;103--刪除
hly.org.path.description=集成使用，部門路徑
hly.org.departmentParentOID.description=集成使用，父部門OID
hly.org.hasChildrenDepartments.description=集成使用，是否有子部門
hly.org.hasUsers.description=集成使用，是否有用戶
hly.org.positionName.description=集成使用，部門角色名稱
hly.org.userName.description=集成使用，員工名稱
hly.org.employeeId1.description=集成使用，員工工號
hly.org.positionCode1.description=集成使用，部門角色編碼

# QQT用戶屬性
qqt.user.id=唯一標識
qqt.user.subAccount=登錄賬號
qqt.user.realname=姓名
qqt.user.email=郵箱
qqt.user.workNo=工號
qqt.user.phone=手機號
qqt.user.telephone=電話號
qqt.user.accountValidityDate=賬號過期時間
qqt.user.status=狀態(1：正常 2：凍結 ）
qqt.user.orgCode=組織編碼，多個使用','隔開
qqt.user.selectedroles=角色id，必填

# QQT用戶屬性描述
qqt.user.id.description=唯一標識
qqt.user.subAccount.description=登錄賬號
qqt.user.realname.description=姓名
qqt.user.email.description=郵箱
qqt.user.workNo.description=工號
qqt.user.phone.description=手機號
qqt.user.telephone.description=電話號
qqt.user.accountValidityDate.description=賬號過期時間
qqt.user.status.description=狀態(1：正常 2：凍結 ）
qqt.user.orgCode.description=組織編碼，多個使用','隔開
qqt.user.selectedroles.description=角色id，必填

# QQT部門屬性
qqt.org.orgName=組織名稱
qqt.org.orgAbbreviation=組織簡稱
qqt.org.orgCode=組織編碼
qqt.org.orgCategoryCode=組織類型
qqt.org.orgNature=組織性質 0：業務組織：1：行政組織
qqt.org.status=狀態 1：正常；2：凍結
qqt.org.dataSource=數據來源 1：外部系統；2：手工創建
qqt.org.id=組織id
qqt.org.orgCategoryDesc=組織類型描述
qqt.org.superExecutiveId=父級行政組織id
qqt.org.executiveLevel=行政組織層級 1：一級；2:二級；3：三級
qqt.org.executiveRegion=行政區域
qqt.org.executiveOrder=
qqt.org.superBusinessId=父級業務組織id
qqt.org.businessLevel=業務組織層級 1：一級；2:二級；3：三級
qqt.org.businessPerson=業務組織負責人
qqt.org.businessRegion=業務組織區域
qqt.org.status_dictText=
qqt.org.orgDesc=組織描述
qqt.org.relateBusinessId=
qqt.org.creditCode=信用代碼
qqt.org.taxNumber=稅號
qqt.org.injectionEls=
qqt.org.chargeCurrency=貨幣
qqt.org.createById=創建人id
qqt.org.createBy=創建人
qqt.org.createTime=創建時間 格式 2023-03-02
qqt.org.updateById=修改人id
qqt.org.updateBy=修改人
qqt.org.updateTime=修改時間 格式 2023-03-02

# QQT部門屬性描述
qqt.org.orgName.description=組織名稱
qqt.org.orgAbbreviation.description=組織簡稱
qqt.org.orgCode.description=組織編碼
qqt.org.orgCategoryCode.description=組織類型
qqt.org.orgNature.description=組織性質 0：業務組織：1：行政組織
qqt.org.status.description=狀態 1：正常；2：凍結
qqt.org.dataSource.description=數據來源 1：外部系統；2：手工創建
qqt.org.id.description=組織id
qqt.org.orgCategoryDesc.description=組織類型描述
qqt.org.superExecutiveId.description=父級行政組織id
qqt.org.executiveLevel.description=行政組織層級 1：一級；2:二級；3：三級
qqt.org.executiveRegion.description=行政區域
qqt.org.executiveOrder.description=
qqt.org.superBusinessId.description=父級業務組織id
qqt.org.businessLevel.description=業務組織層級 1：一級；2:二級；3：三級
qqt.org.businessPerson.description=業務組織負責人
qqt.org.businessRegion.description=業務組織區域
qqt.org.status_dictText.description=
qqt.org.orgDesc.description=組織描述
qqt.org.relateBusinessId.description=
qqt.org.creditCode.description=信用代碼
qqt.org.taxNumber.description=稅號
qqt.org.injectionEls.description=
qqt.org.chargeCurrency.description=貨幣
qqt.org.createById.description=創建人id
qqt.org.createBy.description=創建人
qqt.org.createTime.description=創建時間 格式 2023-03-02
qqt.org.updateById.description=修改人id
qqt.org.updateBy.description=修改人
qqt.org.updateTime.description=修改時間 格式 2023-03-02

# MEICLOUD用戶屬性
meicloud.user.empId=用戶id
meicloud.user.uid=用戶名
meicloud.user.sourceId=數據源id
meicloud.user.name=姓名
meicloud.user.nameEn=姓名-英文
meicloud.user.headPhoto=頭像（圖標訪問全路徑）
meicloud.user.gender=性別
meicloud.user.employeeNumber=員工編號
meicloud.user.extras=擴展屬性JSON

# MEICLOUD用戶屬性描述
meicloud.user.empId.description=用戶id
meicloud.user.uid.description=用戶名
meicloud.user.sourceId.description=數據源id
meicloud.user.name.description=姓名
meicloud.user.nameEn.description=姓名-英文
meicloud.user.headPhoto.description=頭像（圖標訪問全路徑）
meicloud.user.gender.description=性別
meicloud.user.employeeNumber.description=員工編號
meicloud.user.extras.description=擴展屬性JSON

# WEAVER 用戶屬性
weaver.user.id=Id
weaver.user.workcode=工號，唯一標識
weaver.user.lastname=人員名稱
weaver.user.department=部門編碼
weaver.user.subcompany=分部編碼
weaver.user.joblevel=職級
weaver.user.jobcall=職稱
weaver.user.jobactivityid=職務
weaver.user.jobgroupid=職務類型
weaver.user.jobtitle=崗位id
weaver.user.status=狀態: 0 試用 1 正式 2 臨時 3 試用延期 4 解聘 5 離職 6 退休 7 無效
weaver.user.locationid=辦公地點，關聯數據表：HRMLOCATIONS
weaver.user.workroom=辦公室
weaver.user.person_custom_data=個人自定義數據
weaver.user.base_custom_data=基本信息自定義數據
weaver.user.work_custom_data=自定義數據
weaver.user.language=系統語言；枚舉：簡體中文 、 繁體中文 、 English
weaver.user.startdate=合同開始日期
weaver.user.enddate=合同結束日期
weaver.user.subcompanyid1=分部id
weaver.user.subcompanyname=分部名稱
weaver.user.departmentid=部門id
weaver.user.departmentname=部門名稱
weaver.user.managerid=上級人員id
weaver.user.assistantid=助理人員id
weaver.user.accounttype=主次賬號標誌：1 次賬號 , 其他 主賬號
weaver.user.accountname=工資賬號戶名
weaver.user.bankid1=工資銀行
weaver.user.companystartdate=入職日期; 格式：yyyy-MM-dd
weaver.user.workstartdate=參加工作日期; 格式：yyyy-MM-dd
weaver.user.belongto=主賬號id （當accounttype 為 1 有效）
weaver.user.isadaccount=是否ad賬號; 範圍： 1是ad賬號；其他非ad賬號
weaver.user.islabouunion=工會會員
weaver.user.tempresidentnumber=暫住號碼
weaver.user.jobactivitydesc=職責描述
weaver.user.loginid=登錄名
weaver.user.password=密碼
weaver.user.certificatenum=身份證
weaver.user.mobilecall=其他電話
weaver.user.sex=性別
weaver.user.residentplace=現居住地
weaver.user.regresidentplace=戶口
weaver.user.maritalstatus=婚姻狀況
weaver.user.nativeplace=籍貫
weaver.user.folk=民族
weaver.user.birthday=生日
weaver.user.telephone=辦公電話
weaver.user.mobile=手機號
weaver.user.fax=傳真
weaver.user.email=郵箱
weaver.user.homeaddress=家庭聯繫方式
weaver.user.height=身高
weaver.user.weight=體重
weaver.user.healthinfo=健康狀況
weaver.user.policy=政治面貌
weaver.user.bememberdate=入團時間
weaver.user.bepartydate=入黨時間
weaver.user.educationlevel=學歷
weaver.user.degree=學位
weaver.user.seclevel=安全級別
weaver.user.createdate=創建日期
weaver.user.created=創建日期
weaver.user.modified=修改時間
weaver.user.lastmoddate=最後修改日期
weaver.user.dsporder=排序

# WEAVER 用戶屬性描述
weaver.user.id.description=Id
weaver.user.workcode.description=工號，唯一標識
weaver.user.lastname.description=人員名稱
weaver.user.department.description=部門編碼
weaver.user.subcompany.description=分部編碼
weaver.user.joblevel.description=職級
weaver.user.jobcall.description=職稱
weaver.user.jobactivityid.description=職務
weaver.user.jobgroupid.description=職務類型
weaver.user.jobtitle.description=崗位id
weaver.user.status.description=狀態: 0 試用 1 正式 2 臨時 3 試用延期 4 解聘 5 離職 6 退休 7 無效
weaver.user.locationid.description=辦公地點，關聯數據表：HRMLOCATIONS
weaver.user.workroom.description=辦公室
weaver.user.person_custom_data.description=個人自定義數據
weaver.user.base_custom_data.description=基本信息自定義數據
weaver.user.work_custom_data.description=自定義數據
weaver.user.language.description=系統語言；枚舉：簡體中文 、 繁體中文 、 English
weaver.user.startdate.description=合同開始日期
weaver.user.enddate.description=合同結束日期
weaver.user.subcompanyid1.description=分部id
weaver.user.subcompanyname.description=分部名稱
weaver.user.departmentid.description=部門id
weaver.user.departmentname.description=部門名稱
weaver.user.managerid.description=上級人員id
weaver.user.assistantid.description=助理人員id
weaver.user.accounttype.description=主次賬號標誌：1 次賬號 , 其他 主賬號
weaver.user.accountname.description=工資賬號戶名
weaver.user.bankid1.description=工資銀行
weaver.user.companystartdate.description=入職日期; 格式：yyyy-MM-dd
weaver.user.workstartdate.description=參加工作日期; 格式：yyyy-MM-dd
weaver.user.belongto.description=主賬號id （當accounttype 為 1 有效）
weaver.user.isadaccount.description=是否ad賬號; 範圍： 1是ad賬號；其他非ad賬號
weaver.user.islabouunion.description=工會會員
weaver.user.tempresidentnumber.description=暫住號碼
weaver.user.jobactivitydesc.description=職責描述
weaver.user.loginid.description=登錄名
weaver.user.password.description=密碼
weaver.user.certificatenum.description=身份證
weaver.user.mobilecall.description=其他電話
weaver.user.sex.description=性別
weaver.user.residentplace.description=現居住地
weaver.user.regresidentplace.description=戶口
weaver.user.maritalstatus.description=婚姻狀況
weaver.user.nativeplace.description=籍貫
weaver.user.folk.description=民族
weaver.user.birthday.description=生日
weaver.user.telephone.description=辦公電話
weaver.user.mobile.description=手機號
weaver.user.fax.description=傳真
weaver.user.email.description=郵箱
weaver.user.homeaddress.description=家庭聯繫方式
weaver.user.height.description=身高
weaver.user.weight.description=體重
weaver.user.healthinfo.description=健康狀況
weaver.user.policy.description=政治面貌
weaver.user.bememberdate.description=入團時間
weaver.user.bepartydate.description=入黨時間
weaver.user.educationlevel.description=學歷
weaver.user.degree.description=學位
weaver.user.seclevel.description=安全級別
weaver.user.createdate.description=創建日期
weaver.user.created.description=創建日期
weaver.user.modified.description=修改時間
weaver.user.lastmoddate.description=最後修改日期
weaver.user.dsporder.description=排序

# WEAVER 部門屬性
weaver.org.id=泛微oa的ID
weaver.org.subcompanyid1=分部id
weaver.org.subcompanycode=分部編碼
weaver.org.org_code=分部編號
weaver.org.supdepid=上級部門id
weaver.org.parent_code=上級部門編號
weaver.org.code=部門編碼，自定義
weaver.org.shortname=部門簡稱
weaver.org.fullname=部門全稱
weaver.org.canceled=封存標誌；默認查詢非封存數據。1:封存。2：獲取全部數據
weaver.org.custom_data=值內容是指定獲取OA自定義字段的列表（具體看【組織權限中心】-【自定義設置】-【部門字段定義】）
weaver.org.created=創建時間戳
weaver.org.modified=修改時間戳
weaver.org.showorder=自定義數據，具體參考 【組織權限中心-自定義設置-部門字段定義】

# WEAVER 部門屬性描述
weaver.org.id.description=泛微oa的ID
weaver.org.subcompanyid1.description=分部id
weaver.org.subcompanycode.description=分部編碼
weaver.org.org_code.description=分部編號
weaver.org.supdepid.description=上級部門id
weaver.org.parent_code.description=上級部門編號
weaver.org.code.description=部門編碼，自定義
weaver.org.shortname.description=部門簡稱
weaver.org.fullname.description=部門全稱
weaver.org.canceled.description=封存標誌；默認查詢非封存數據。1:封存。2：獲取全部數據
weaver.org.custom_data.description=值內容是指定獲取OA自定義字段的列表（具體看【組織權限中心】-【自定義設置】-【部門字段定義】）
weaver.org.created.description=創建時間戳
weaver.org.modified.description=修改時間戳
weaver.org.showorder.description=自定義數據，具體參考 【組織權限中心-自定義設置-部門字段定義】

# WECHAT 用戶屬性
wechat.user.openid=openid
wechat.user.unionid=用戶統一標識。針對一個微信開放平臺賬號下的應用，同一用戶的unionid是唯一的
wechat.user.headimgurl=用戶頭像，最後一個數值代表正方形頭像大小（有0、46、64、96、132數值可選，0代表640*640正方形頭像），用戶沒有頭像時該項為空

# WECHAT 用戶屬性描述
wechat.user.openid.description=openid
wechat.user.unionid.description=用戶統一標識。針對一個微信開放平臺賬號下的應用，同一用戶的unionid是唯一的
wechat.user.headimgurl.description=用戶頭像，最後一個數值代表正方形頭像大小（有0、46、64、96、132數值可選，0代表640*640正方形頭像），用戶沒有頭像時該項為空

# WECHATOFFICIAL 用戶屬性
wechatofficial.user.openid=openid
wechatofficial.user.headimgurl=用戶頭像，最後一個數值代表正方形頭像大小（有0、46、64、96、132數值可選，0代表640*640正方形頭像），用戶沒有頭像時該項為空

# WECHATOFFICIAL 用戶屬性描述
wechatofficial.user.openid.description=openid
wechatofficial.user.headimgurl.description=用戶頭像，最後一個數值代表正方形頭像大小（有0、46、64、96、132數值可選，0代表640*64

# YONBIP 用戶屬性
yonbip.user.billtype=單據類型
yonbip.user.isexchange=Y
yonbip.user.replace=Y
yonbip.user.sender=發送方編碼
yonbip.user.birthdate=出生日期
yonbip.user.id=Id
yonbip.user.code=人員編碼（唯一屬性）
yonbip.user.email=電子郵件
yonbip.user.enablestate=啟用狀態
yonbip.user.firstname=名
yonbip.user.homephone=家庭電話
yonbip.user.billId=證件號
yonbip.user.idtype=證件類型
yonbip.user.joinworkdate=參加工作日期
yonbip.user.lastname=姓
yonbip.user.mnecode=助記碼
yonbip.user.mobile=手機
yonbip.user.name=姓名
yonbip.user.nickname=暱稱
yonbip.user.officephone=辦公電話
yonbip.user.pk_group=所屬集團
yonbip.user.pk_org=所屬業務單元
yonbip.user.sex=性別
yonbip.user.usedname=曾用名
yonbip.user.def1=自定義項1
yonbip.user.def2=自定義項2
yonbip.user.def3=自定義項3
yonbip.user.def4=自定義項4
yonbip.user.def5=自定義項5
yonbip.user.def6=自定義項6
yonbip.user.def7=自定義項7
yonbip.user.def8=自定義項8
yonbip.user.def9=自定義項9
yonbip.user.def10=自定義項10
yonbip.user.def11=自定義項11
yonbip.user.def12=自定義項12
yonbip.user.def13=自定義項13
yonbip.user.def14=自定義項14
yonbip.user.def15=自定義項15
yonbip.user.def16=自定義項16
yonbip.user.def17=自定義項17
yonbip.user.def18=自定義項18
yonbip.user.def19=自定義項19
yonbip.user.def20=自定義項20
yonbip.user.city=城市
yonbip.user.addressCode=家庭地址編碼
yonbip.user.country=國家
yonbip.user.detailinfo=地址詳情
yonbip.user.postcode=郵政編碼
yonbip.user.province=省份
yonbip.user.vsection=縣區
yonbip.user.psnjobs=工作信息
yonbip.user.addr=家庭住址主鍵
yonbip.user.account=帳套
yonbip.user.groupcode=集團編碼
yonbip.user.user_password=用戶密碼
yonbip.user.pwdlevelcode=密碼安全級別
yonbip.user.pwdparam=密碼參數
yonbip.user.user_note=備註
yonbip.user.abledate=生效日期
yonbip.user.disabledate=失效日期
yonbip.user.islocked=是否鎖定
yonbip.user.base_doc_type=身份類型
yonbip.user.user_type=用戶類型
yonbip.user.pk_base_doc=身份
yonbip.user.identityverifycode=認證類型
yonbip.user.format=數據格式
yonbip.user.isca=CA用戶
yonbip.user.contentlang=語言信息
yonbip.user.user_code_q=查詢編碼
yonbip.user.pk_usergroupforcreate=所屬用戶組
yonbip.user.filename=文件名
yonbip.user.roottag=跟標籤

# YONBIP 用戶屬性描述
yonbip.user.billtype.description=單據類型
yonbip.user.isexchange.description=Y
yonbip.user.replace.description=Y
yonbip.user.sender.description=發送方編碼
yonbip.user.birthdate.description=出生日期
yonbip.user.id.description=Id
yonbip.user.code.description=人員編碼（唯一屬性確認之後就不能修改）
yonbip.user.email.description=電子郵件
yonbip.user.enablestate.description=啟用狀態
yonbip.user.firstname.description=名
yonbip.user.homephone.description=家庭電話
yonbip.user.billId.description=證件號
yonbip.user.idtype.description=證件類型
yonbip.user.joinworkdate.description=參加工作日期
yonbip.user.lastname.description=姓
yonbip.user.mnecode.description=助記碼
yonbip.user.mobile.description=手機
yonbip.user.name.description=姓名
yonbip.user.nickname.description=暱稱
yonbip.user.officephone.description=辦公電話
yonbip.user.pk_group.description=所屬集團
yonbip.user.pk_org.description=所屬業務單元
yonbip.user.sex.description=性別
yonbip.user.usedname.description=曾用名
yonbip.user.def1.description=自定義項1
yonbip.user.def2.description=自定義項2
yonbip.user.def3.description=自定義項3
yonbip.user.def4.description=自定義項4
yonbip.user.def5.description=自定義項5
yonbip.user.def6.description=自定義項6
yonbip.user.def7.description=自定義項7
yonbip.user.def8.description=自定義項8
yonbip.user.def9.description=自定義項9
yonbip.user.def10.description=自定義項10
yonbip.user.def11.description=自定義項11
yonbip.user.def12.description=自定義項12
yonbip.user.def13.description=自定義項13
yonbip.user.def14.description=自定義項14
yonbip.user.def15.description=自定義項15
yonbip.user.def16.description=自定義項16
yonbip.user.def17.description=自定義項17
yonbip.user.def18.description=自定義項18
yonbip.user.def19.description=自定義項19
yonbip.user.def20.description=自定義項20
yonbip.user.city.description=城市
yonbip.user.addressCode.description=家庭地址編碼
yonbip.user.country.description=國家
yonbip.user.detailinfo.description=地址詳情
yonbip.user.postcode.description=郵政編碼
yonbip.user.province.description=省份
yonbip.user.vsection.description=縣區
yonbip.user.psnjobs.description=工作信息
yonbip.user.addr.description=家庭住址主鍵
yonbip.user.account.description=帳套
yonbip.user.groupcode.description=集團編碼
yonbip.user.user_password.description=用戶密碼
yonbip.user.pwdlevelcode.description=密碼安全級別
yonbip.user.pwdparam.description=密碼參數
yonbip.user.user_note.description=備註
yonbip.user.abledate.description=生效日期
yonbip.user.disabledate.description=失效日期
yonbip.user.islocked.description=是否鎖定
yonbip.user.base_doc_type.description=身份類型
yonbip.user.user_type.description=用戶類型
yonbip.user.pk_base_doc.description=身份
yonbip.user.identityverifycode.description=認證類型
yonbip.user.format.description=數據格式
yonbip.user.isca.description=CA用戶
yonbip.user.contentlang.description=語言信息
yonbip.user.user_code_q.description=查詢編碼
yonbip.user.pk_usergroupforcreate.description=所屬用戶組
yonbip.user.filename.description=文件名
yonbip.user.roottag.description=跟標籤

# YONBIP 部門屬性
yonbip.org.account=賬套編碼
yonbip.org.billtype=單據類型
yonbip.org.groupcode=集團編碼
yonbip.org.isexchange=是否使用NC翻譯
yonbip.org.replace=是否允許更新
yonbip.org.roottag=跟標籤
yonbip.org.sender=發送方編碼
yonbip.org.orgcode=組織編碼
yonbip.org.id=id
yonbip.org.pk_dept=部門主鍵
yonbip.org.code=部門編碼
yonbip.org.name=部門名稱多語
yonbip.org.pk_fatherorg=上級部門主鍵
yonbip.org.pk_group=集團主鍵
yonbip.org.pk_org=業務單元主鍵
yonbip.org.mnecode=助記碼
yonbip.org.hrcanceled=HR撤銷標誌
yonbip.org.creator=創建人
yonbip.org.enablestate=啟用狀態
yonbip.org.displayorder=顯示順序
yonbip.org.depttype=部門類別
yonbip.org.orgtype13=報表
yonbip.org.orgtype17=預算
yonbip.org.principal=部門負責人
yonbip.org.city=城市
yonbip.org.code1=編碼
yonbip.org.country=國家
yonbip.org.detailinfo=詳細地址
yonbip.org.postcode=郵政編碼
yonbip.org.province=省份
yonbip.org.vsection=縣區

# YONBIP 部門屬性描述
yonbip.org.account.description=賬套編碼
yonbip.org.billtype.description=單據類型
yonbip.org.groupcode.description=集團編碼
yonbip.org.isexchange.description=是否使用NC翻譯
yonbip.org.replace.description=是否允許更新
yonbip.org.roottag.description=跟標籤
yonbip.org.sender.description=發送方編碼
yonbip.org.orgcode.description=組織編碼
yonbip.org.id.description=id
yonbip.org.pk_dept.description=部門主鍵
yonbip.org.code.description=部門編碼
yonbip.org.name.description=部門名稱多語
yonbip.org.pk_fatherorg.description=上級部門主鍵
yonbip.org.pk_group.description=集團主鍵
yonbip.org.pk_org.description=業務單元主鍵
yonbip.org.mnecode.description=助記碼
yonbip.org.hrcanceled.description=HR撤銷標誌
yonbip.org.creator.description=創建人
yonbip.org.enablestate.description=啟用狀態
yonbip.org.displayorder.description=顯示順序
yonbip.org.depttype.description=部門類別
yonbip.org.orgtype13.description=報表
yonbip.org.orgtype17.description=預算
yonbip.org.principal.description=部門負責人
yonbip.org.city.description=城市
yonbip.org.code1.description=編碼
yonbip.org.country.description=國家
yonbip.org.detailinfo.description=詳細地址
yonbip.org.postcode.description=郵政編碼
yonbip.org.province.description=省份
yonbip.org.vsection.description=縣區

# MOKA 用戶屬性
moka.user.userId=用戶id
moka.user.email=郵箱
moka.user.name=姓名
moka.user.phone=電話
moka.user.number=工號
moka.user.role=角色。0：內推人，5：前臺，10：面試官，20：用人經理，25：高級用人經理，30：HR，40：管理員，50：超管
moka.user.roleId=自定義角色id
moka.user.superiorEmail=上級郵箱
moka.user.deactivated=是否被禁用。可選值：0:可用，1:禁用
moka.user.isPending=功能性字段，用作單點登錄，沒有傳空字符串
moka.user.thirdPartyId=功能性字段，用作單點登錄，沒有返回空字符串
moka.user.locale=用戶語言。可選值：zh-CN:為中文，en-US:英文。默認為中文
moka.user.department=用戶所屬/負責部門
moka.user.departmentCode=三方部門id
moka.user.departmentId=moka部門id
moka.user.departmentName=部門名稱

# MOKA 用戶屬性描述
moka.user.userId.description=用戶id
moka.user.email.description=郵箱
moka.user.name.description=姓名
moka.user.phone.description=電話
moka.user.number.description=工號
moka.user.role.description=角色。0：內推人，5：前臺，10：面試官，20：用人經理，25：高級用人經理，30：HR，40：管理員，50：超管
moka.user.roleId.description=自定義角色id
moka.user.superiorEmail.description=上級郵箱
moka.user.deactivated.description=是否被禁用。可選值：0:可用，1:禁用
moka.user.isPending.description=功能性字段，用作單點登錄，沒有傳空字符串
moka.user.thirdPartyId.description=功能性字段，用作單點登錄，沒有返回空字符串
moka.user.locale.description=用戶語言。可選值：zh-CN:為中文，en-US:英文。默認為中文
moka.user.department.description=用戶所屬/負責部門
moka.user.departmentCode.description=三方部門id
moka.user.departmentId.description=moka部門id
moka.user.departmentName.description=部門名稱


# MOKA 部門屬性
moka.org.name=部門名稱
moka.org.departmentId=MOKA系統的部門id
moka.org.departmentCode=客戶系統的部門id
moka.org.parentCode=部門的上級部門的唯一 id, 如為 null 則為一級部門
moka.org.deletedByApi=是否刪除
moka.org.type=部門類型
moka.org.parentId=MOKA系統的父部門id

# MOKA 部門屬性描述
moka.org.name.description=部門名稱
moka.org.departmentId.description=MOKA系統的部門id
moka.org.departmentCode.description=客戶系統的部門id
moka.org.parentCode.description=部門的上級部門的唯一 id, 如為 null 則為一級部門
moka.org.deletedByApi.description=是否刪除
moka.org.type.description=部門類型
moka.org.parentId.description=MOKA系統的父部門id

# SIMPLEHR 用戶屬性
simplehr.user.accountId=賬號Id
simplehr.user.workCode=工號
simplehr.user.name=姓名
simplehr.user.phone=電話
simplehr.user.department=用戶所屬部門

# SIMPLEHR 用戶屬性描述
simplehr.user.accountId.description=賬號Id
simplehr.user.workCode.description=工號
simplehr.user.name.description=姓名
simplehr.user.phone.description=電話
simplehr.user.department.description=用戶所屬部門

# SIMPLEHR 部門屬性
simplehr.org.name=部門名稱
simplehr.org.deptId=部門id
simplehr.org.deptCode=客戶系統的部門code
simplehr.org.parentCode=部門的上級部門的唯一code

# SIMPLEHR 部門屬性描述
simplehr.org.name.description=部門名稱
simplehr.org.deptId.description=部門id
simplehr.org.deptCode.description=客戶系統的部門code
simplehr.org.parentCode.description=部門的上級部門的唯一code

# HAP 用戶屬性
hap.user.accountId=明道雲賬號Id
hap.user.corpUserId=第三方用戶Id, 必填
hap.user.name=用戶名, 必填
hap.user.email=郵箱，與 mobilePhone 不能同時為空, 必填
hap.user.mobilePhone=手機號，與 email 不能同時為空, 必填
hap.user.contactPhone=座機號, 非必填
hap.user.jobNumber=工號, 非必填
hap.user.departmentIds=第三方部門Id集合, 非必填
hap.user.positions=職位集合, 非必填
hap.user.emptyCover=當非必填字段為空時，是否覆蓋原來的值，默認：true（覆蓋）
hap.user.avatar=頭像，用於集成

# HAP 用戶屬性描述
hap.user.accountId.description=明道雲賬號Id
hap.user.corpUserId.description=第三方用戶Id, 必填
hap.user.name.description=用戶名, 必填
hap.user.email.description=郵箱，與 mobilePhone 不能同時為空, 必填
hap.user.mobilePhone.description=手機號，與 email 不能同時為空, 必填
hap.user.contactPhone.description=座機號, 非必填
hap.user.jobNumber.description=工號, 非必填
hap.user.departmentIds.description=第三方部門Id集合, 非必填
hap.user.positions.description=職位集合, 非必填
hap.user.emptyCover.description=當非必填字段為空時，是否覆蓋原來的值，默認：true（覆蓋）
hap.user.avatar.description=頭像，用於集成

# HAP 部門屬性
hap.org.departmentId=明道雲部門Id
hap.org.corpDepartmentId=第三方部門Id, 必填
hap.org.name=部門名稱, 必填
hap.org.parentId=父部門Id, 非必填
hap.org.order=排序值, 非必填
hap.org.isNumber=部門負責人id是否是數值類型（如果是基於 企業微信、釘釘、Welink 集成，則需要傳 true）, 非必填
hap.org.chargeCorpUserIds=部門負責人，第三方用戶Id列表, 非必填

# HAP 部門屬性描述
hap.org.departmentId.description=明道雲部門Id
hap.org.corpDepartmentId.description=第三方部門Id, 必填
hap.org.name.description=部門名稱, 必填
hap.org.parentId.description=父部門Id, 非必填
hap.org.order.description=排序值, 非必填
hap.org.isNumber.description=部門負責人id是否是數值類型（如果是基於 企業微信、釘釘、Welink 集成，則需要傳 true）, 非必填
hap.org.chargeCorpUserIds.description=部門負責人，第三方用戶Id列表, 非必填




















