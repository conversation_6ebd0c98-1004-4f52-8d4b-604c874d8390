# 系统角色映射
TC.TENANT_ADMIN=平台租户管理员
TC.APP_ADMIN=平台应用管理员
TC.LINK_ADMIN=平台连接流管理员
USERCENTER.SUPER_ADMIN=系统管理员
USERCENTER.APP_ADMIN=应用管理员
USERCENTER.USER_ADMIN=帐号管理员
USERCENTER.ROLE_APP=CLI应用角色
USERCENTER.ROLE_USER=普通用户角色
TC.TENANT_ADMIN.DESCRIPTION=平台租户管理员，可对租户进行管理
TC.APP_ADMIN.DESCRIPTION=平台应用管理员，可对应用进行管理
TC.LINK_ADMIN.DESCRIPTION=平台连接流管理员，可管理连接器、执行动作和连接流模板
USERCENTER.SUPER_ADMIN.DESCRIPTION=系统管理员，具有最高权限，可以进行一切操作
USERCENTER.APP_ADMI.DESCRIPTION=系统管理员，应用管理员，可以进行应用相关的操作，包括应用管理，应用权限角色管理，应用分配，应用角色分配。，可以进行一切操作
USERCENTER.USER_ADMIN.DESCRIPTION=帐号管理员，可以进行用户帐号相关的操作，包括用户帐号管理，组织结构管理，扩展属性管理。
USERCENTER.ROLE_APP.DESCRIPTION=CLI应用角色，即用户中心创建的CLI应用，默认具有的权限。包括用户，组织结构，扩展属性，审计日志，链接器的权限。
USERCENTER.ROLE_USER.DESCRIPTION=普通用户角色，可以进行自服务等操作。用户中心中的用户，默认具有该角色，无需额外分配。
# ====================== 本地用户属性 ======================
local.user.sub=用户ID
local.user.sub.description=系统生成的用户id
local.user.username=用户名
local.user.username.description=用户名
local.user.phone_number=手机号码
local.user.phone_number.description=手机号码
local.user.email=电子邮箱
local.user.email.description=电子邮箱
local.user.user_job_number=工号
local.user.user_job_number.description=工号
local.user.name=姓名
local.user.name.description=姓名
local.user.title=职位
local.user.title.description=职位
local.user.manager=直属主管
local.user.manager.description=直属主管
local.user.connector_manager=外部直属主管
local.user.connector_manager.description=外部直属主管
local.user.group_positions=部门职位
local.user.group_positions.description=部门职位
local.user.connector_group_positions=外部部门职位
local.user.connector_group_positions.description=外部部门职位
local.user.telephone_number=电话
local.user.telephone_number.description=电话
local.user.preferred_username=常用名
local.user.preferred_username.description=常用名
local.user.nickname=昵称
local.user.nickname.description=昵称
local.user.picture=头像
local.user.picture.description=头像
local.user.address=办公地点
local.user.address.description=办公地点
local.user.hired_date=入职日期
local.user.hired_date.description=入职日期
local.user.birthdate=生日
local.user.birthdate.description=生日
local.user.cert=个人证书
local.user.cert.description=个人证书
local.user.gender=性别
local.user.gender.description=性别
local.user.email_verified=邮箱是否已验证
local.user.email_verified.description=邮箱是否已验证
local.user.come_from=来源
local.user.come_from.description=来源
local.user.last_login=最后一次登录时间
local.user.last_login.description=最后一次登录时间
local.user.locale=URL of user's profile
local.user.locale.description=URL of user's profile
local.user.password=登录密码
local.user.password.description=登录密码
local.user.tag=用户标签
local.user.tag.description=用户标签
local.user.password_status=密码状态
local.user.password_status.description=密码状态
local.user.phone_number_verified=手机号是否已验证
local.user.phone_number_verified.description=手机号是否已验证
local.user.profile=URL of user's profile
local.user.profile.description=URL of user's profile
local.user.pwd_changed_time=密码修改时间
local.user.pwd_changed_time.description=密码修改时间
local.user.pwd_expired_time=密码过期时间
local.user.pwd_expired_time.description=密码过期时间
local.user.type=用户类别
local.user.type.description=用户类别
local.user.website=个人站点
local.user.website.description=个人站点
local.user.zoneinfo=时区
local.user.zoneinfo.description=时区
local.user.org_ids=部门ID
local.user.org_ids.description=部门ID
local.user.connector_org_ids=外部部门ID
local.user.connector_org_ids.description=外部部门ID
local.user.connector_user_id=外部用户ID
local.user.connector_user_id.description=外部用户ID
local.user.dept_path=部门路径
local.user.dept_path.description=部门路径
local.user.user_extension=扩展属性
local.user.user_extension.description=扩展属性
local.user.start_date=生效日期
local.user.start_date.description=生效日期
local.user.end_date=失效日期
local.user.end_date.description=失效日期
local.user.status=状态
local.user.status.description=状态
local.user.connector_id=来源系统
local.user.connector_id.description=来源系统
local.user.create_by=被谁创建
local.user.create_by.description=被谁创建
local.user.create_time=创建时间
local.user.create_time.description=创建时间
local.user.created_mode=创建方式
local.user.created_mode.description=创建方式
local.user.update_by=被谁修改
local.user.update_by.description=被谁修改
local.user.update_time=修改时间
local.user.update_time.description=修改时间
local.user.checksum=校验码
local.user.checksum.description=校验码
local.user.tenant_id=租户编码
local.user.tenant_id.description=租户编码
local.user.role_positions=角色信息
local.user.role_positions.description=角色信息
local.user.connector_role_positions=外部角色信息
local.user.connector_role_positions.description=外部角色信息
local.user.id_card=身份证
local.user.id_card.description=身份证
local.user.org_positions=部门职位
local.user.org_positions.description=部门职位
local.user.org_job_number=部门工号
local.user.org_job_number.description=部门工号
local.user.main_org=主部门
local.user.main_org.description=主部门

# ====================== 本地部门属性 ======================
local.org.id=系统生成的id
local.org.id.description=系统生成的id
local.org.name=名称
local.org.name.description=名称
local.org.readonly=只读
local.org.readonly.description=只读
local.org.org_desc=部门描述
local.org.org_desc.description=部门描述
local.org.seq=同级别内排序
local.org.seq.description=同级别内排序
local.org.org_path=部门路径
local.org.org_path.description=部门路径
local.org.parent_ref_id=父ID
local.org.parent_ref_id.description=父ID
local.org.manager=部门主管
local.org.manager.description=部门主管
local.org.org_extension=扩展属性
local.org.org_extension.description=扩展属性
local.org.connector_org_id=外部部门ID
local.org.connector_org_id.description=外部部门ID
local.org.connector_org_name=外部部门名称
local.org.connector_org_name.description=外部部门名称
local.org.connector_parent_org_id=外部父部门ID
local.org.connector_parent_org_id.description=外部父部门ID
local.org.connector_manager=外部主管
local.org.connector_manager.description=外部主管
local.org.status=状态
local.org.status.description=状态
local.org.connector_id=来源
local.org.connector_id.description=来源
local.org.create_by=被谁创建
local.org.create_by.description=被谁创建
local.org.create_time=创建时间
local.org.create_time.description=创建时间
local.org.created_mode=创建方式
local.org.created_mode.description=创建方式
local.org.update_by=被谁修改
local.org.update_by.description=被谁修改
local.org.update_time=修改时间
local.org.update_time.description=修改时间
local.org.checksum=信息是否发生变更的校验码
local.org.checksum.description=信息是否发生变更的校验码
local.org.tenant_id=租户编码
local.org.tenant_id.description=租户编码

# ====================== 本地角色属性 ======================
local.role.id=角色/角色组ID
local.role.id.description=角色/角色组ID
local.role.name=角色名称
local.role.name.description=角色名称
local.role.group_name=角色组名称
local.role.group_name.description=角色组名称
local.role.groupId=角色ID
local.role.groupId.description=角色ID
local.role.connector_role_id=外部角色ID
local.role.connector_role_id.description=外部角色ID
local.role.connector_role_group_id=外部角色组ID
local.role.connector_role_group_id.description=外部角色组ID
local.role.user_ids=角色所属用户
local.role.user_ids.description=角色所属用户
local.role.role_groups=角色组
local.role.role_groups.description=角色组
local.role.connector_role_groups=角色组
local.role.connector_role_groups.description=角色组

# ====================== 短信网关属性 ======================
local.sms.gateway.DGSEE=数犀短信
local.sms.gateway.CLY=诚立业短信
local.sms.gateway.BIT=北理工短信
local.sms.gateway.CNAS=国家认可委短信
local.sms.gateway.AHJK=安徽交控短信
local.sms.gateway.ALI=阿里云短信
local.sms.gateway.WEBHOOK=连接流发送短信
# ====================== 邮件服务属性 ======================
local.mail.service.DGSEE=系统内置服务
local.mail.service.ALI=阿里云邮件推送
local.mail.service.CUSTOM=自定义邮件推送
# ====================== 企业消息服务属性 ======================
local.enterprise.message.service.INSITE=站内消息
local.enterprise.message.service.DINGDING=钉钉工作通知
local.enterprise.message.service.WEWORK=企业微信消息
local.enterprise.message.service.FEISHU=飞书消息
# ====================== 企业消息模版属性 ======================
local.enterprise.message.template.update_pwd_msg=密码修改通知
local.enterprise.message.template.reset_pwd_msg=密码重置通知
local.enterprise.message.template.admin_reset_pwd_msg=管理员重置密码通知
local.enterprise.message.template.expire_pwd_msg=密码过期提醒

# ====================== AD用户属性 ======================
ad.user.objectGUID=objectGUID
ad.user.objectGUID.description=objectGUID
ad.user.objectclass=objectclass
ad.user.objectclass.description=objectclass
ad.user.dn=dn
ad.user.dn.description=dn
ad.user.sAMAccountName=sAMAccountName
ad.user.sAMAccountName.description=sAMAccountName
ad.user.cn=cn
ad.user.cn.description=cn
ad.user.userAccountControl=用户启用标识，512-启用，2-禁用
ad.user.userAccountControl.description=用户启用标识，512-启用，2-禁用
ad.user.name=name
ad.user.name.description=name
ad.user.unicodePwd=unicodePwd
ad.user.unicodePwd.description=unicodePwd
ad.user.userPassword=userPassword
ad.user.userPassword.description=userPassword
ad.user.telephoneNumber=telephoneNumber
ad.user.telephoneNumber.description=telephoneNumber
ad.user.mail=mail
ad.user.mail.description=mail
ad.user.display_name=displayName
ad.user.display_name.description=displayName
ad.user.userPrincipalName=userPrincipalName
ad.user.userPrincipalName.description=userPrincipalName
ad.user.department_ids=department_ids
ad.user.department_ids.description=department_ids

# ====================== AD部门属性 ======================
ad.org.ou=ou
ad.org.ou.description=ou
ad.org.dn=dn
ad.org.dn.description=dn
ad.org.objectGUID=objectGUID
ad.org.objectGUID.description=objectGUID
ad.org.parentObjectGUID=parentObjectGUID
ad.org.parentObjectGUID.description=parentObjectGUID
ad.org.objectclass=objectclass
ad.org.objectclass.description=objectclass

# ====================== LDAP用户属性 ======================
ldap.user.entryUUID=entryUUID
ldap.user.entryUUID.description=entryUUID
ldap.user.objectclass=objectclass
ldap.user.objectclass.description=objectclass
ldap.user.dn=dn
ldap.user.dn.description=dn
ldap.user.uid=uid
ldap.user.uid.description=uid
ldap.user.cn=cn
ldap.user.cn.description=cn
ldap.user.userPassword=userPassword
ldap.user.userPassword.description=userPassword
ldap.user.telephoneNumber=telephoneNumber
ldap.user.telephoneNumber.description=telephoneNumber
ldap.user.mail=mail
ldap.user.mail.description=mail
ldap.user.display_name=displayName
ldap.user.display_name.description=displayName
ldap.user.department_ids=department_ids
ldap.user.department_ids.description=department_ids

# ====================== LDAP部门属性 ======================
ldap.org.ou=ou
ldap.org.ou.description=ou
ldap.org.cn=cn
ldap.org.cn.description=cn
ldap.org.entryUUID=entryUUID
ldap.org.entryUUID.description=entryUUID
ldap.org.parentEntryUUID=parentEntryUUID
ldap.org.parentEntryUUID.description=parentEntryUUID
ldap.org.objectclass=objectclass
ldap.org.objectclass.description=objectclass
ldap.org.dn=dn
ldap.org.dn.description=dn


# ====================== 钉钉部门属性 ======================
dingding.org.name=部门名称
dingding.org.name.description=部门的标准名称
dingding.org.dept_id=部门ID
dingding.org.dept_id.description=系统生成的唯一标识符
dingding.org.parent_id=父部门ID
dingding.org.parent_id.description=上级部门标识符
dingding.org.hide_dept=是否隐藏本部门
dingding.org.hide_dept.description=控制本部门在通讯录中的可见性
dingding.org.dept_permits=可见部门列表
dingding.org.dept_permits.description=指定可查看本部门的其他部门ID列表
dingding.org.user_permits=可见人员列表
dingding.org.user_permits.description=指定可查看本部门的人员userId列表
dingding.org.outer_dept=限制通讯录可见性
dingding.org.outer_dept.description=是否限制本部门成员查看全部通讯录
dingding.org.outer_dept_only_self=仅显示本部门及下级
dingding.org.outer_dept_only_self.description=限制成员只能看到所在部门及下级部门
dingding.org.outer_permit_users=允许查看的用户
dingding.org.outer_permit_users.description=指定可查看通讯录的用户ID列表
dingding.org.outer_permit_depts=允许查看的部门
dingding.org.outer_permit_depts.description=指定可查看通讯录的部门ID列表
dingding.org.create_dept_group=创建企业群
dingding.org.create_dept_group.description=是否创建关联部门的企业群
dingding.org.auto_approve_apply=自动审批加入
dingding.org.auto_approve_apply.description=是否默认同意加入部门申请
dingding.org.order=排序值
dingding.org.order.description=在父部门中的显示顺序
dingding.org.source_identifier=部门标识字段
dingding.org.source_identifier.description=部门唯一标识符字段
dingding.org.language=通讯录语言
dingding.org.language.description=设置通讯录显示语言
dingding.org.auto_add_user=自动入群
dingding.org.auto_add_user.description=新人加入时自动加入部门群
dingding.org.dept_manager_userid_list=部门主管
dingding.org.dept_manager_userid_list.description=部门主管用户ID列表
dingding.org.group_contain_sub_dept=包含子部门
dingding.org.group_contain_sub_dept.description=部门群是否包含子部门成员
dingding.org.group_contain_outer_dept=包含外包部门
dingding.org.group_contain_outer_dept.description=部门群是否包含外包部门成员
dingding.org.group_contain_hidden_dept=包含隐藏部门
dingding.org.group_contain_hidden_dept.description=部门群是否包含隐藏部门成员
dingding.org.org_dept_owner=群主
dingding.org.org_dept_owner.description=企业群群主的用户ID
dingding.org.force_update_fields=强制更新字段
dingding.org.force_update_fields.description=指定需要强制更新的字段列表

# ====================== 钉钉用户属性 ======================
dingding.user.userid=用户ID
dingding.user.userid.description=员工的唯一标识符
dingding.user.unionid=统一ID
dingding.user.unionid.description=企业账号范围内的唯一标识
dingding.user.name=姓名
dingding.user.name.description=员工的中文姓名
dingding.user.avatar=头像
dingding.user.avatar.description=用户头像URL地址
dingding.user.state_code=国际区号
dingding.user.state_code.description=电话号码国际区号
dingding.user.manager_userid=直属主管
dingding.user.manager_userid.description=直接上级的用户ID
dingding.user.mobile=手机号
dingding.user.mobile.description=员工手机号码
dingding.user.hide_mobile=隐藏手机号
dingding.user.hide_mobile.description=是否隐藏手机号码
dingding.user.telephone=分机号
dingding.user.telephone.description=办公分机号码
dingding.user.job_number=工号
dingding.user.job_number.description=员工工号
dingding.user.title=职位
dingding.user.title.description=员工职位名称
dingding.user.email=邮箱
dingding.user.email.description=工作邮箱地址
dingding.user.work_place=办公地点
dingding.user.work_place.description=员工办公地点
dingding.user.remark=备注
dingding.user.remark.description=附加说明信息
dingding.user.exclusive_account=专属账号
dingding.user.exclusive_account.description=是否为本组织专属账号
dingding.user.exclusive_account_type=专属账号类型
dingding.user.exclusive_account_type.description=专属账号的认证类型
dingding.user.exclusive_mobile=专属手机号
dingding.user.exclusive_mobile.description=专属账号绑定的手机号
dingding.user.avatarMediaId=头像媒体ID
dingding.user.avatarMediaId.description=创建专属账号时指定的头像媒体ID
dingding.user.nickname=昵称
dingding.user.nickname.description=专属账号的显示昵称
dingding.user.login_id=登录ID
dingding.user.login_id.description=专属账号登录用户名
dingding.user.loginId=登录账号
dingding.user.loginId.description=钉钉专属账号登录名
dingding.user.init_password=初始密码
dingding.user.init_password.description=专属账号的初始密码
dingding.user.org_email=企业邮箱
dingding.user.org_email.description=员工的企业邮箱地址
dingding.user.org_email_type=邮箱类型
dingding.user.org_email_type.description=企业邮箱的类型标识
dingding.user.dept_id_list=所属部门
dingding.user.dept_id_list.description=员工所属部门ID列表
dingding.user.dept_order_list=部门排序
dingding.user.dept_order_list.description=在各部门中的排序信息
dingding.user.dept_title_list=部门职位
dingding.user.dept_title_list.description=在各部门中的职位信息
dingding.user.cust_position_list=职位排序
dingding.user.cust_position_list.description=职位和排序的组合信息
dingding.user.extension=扩展属性
dingding.user.extension.description=自定义扩展属性
dingding.user.hired_date=入职时间
dingding.user.hired_date.description=Unix时间戳格式的入职时间
dingding.user.active=激活状态
dingding.user.active.description=账号是否已激活
dingding.user.real_authed=实名认证
dingding.user.real_authed.description=是否完成实名认证
dingding.user.senior=高管标识
dingding.user.senior.description=是否为企业高管
dingding.user.senior_mode=高管模式
dingding.user.senior_mode.description=是否启用高管模式
dingding.user.admin=管理员
dingding.user.admin.description=是否为企业管理员
dingding.user.boss=企业主
dingding.user.boss.description=是否为企业所有者
dingding.user.leader_in_dept=部门领导
dingding.user.leader_in_dept.description=在部门中的领导身份标识
dingding.user.language=语言偏好
dingding.user.language.description=用户的通讯录语言设置
dingding.user.login_email=登录邮箱
dingding.user.login_email.description=用于登录的邮箱地址
dingding.user.role_list=角色列表
dingding.user.role_list.description=用户拥有的角色列表
dingding.user.union_emp_ext=关联信息
dingding.user.union_emp_ext.description=来自关联组织的扩展信息
dingding.user.force_update_fields=强制更新字段
dingding.user.force_update_fields.description=需要强制更新的字段列表

# ====================== 钉钉角色属性 ======================
dingding.role.name=角色名称
dingding.role.name.description=角色显示名称
dingding.role.groupName=角色组名称
dingding.role.groupName.description=角色所属分组的名称
dingding.role.roleId=角色ID
dingding.role.roleId.description=角色的唯一标识符
dingding.role.roleName=角色名称
dingding.role.roleName.description=角色的标准名称
dingding.role.groupId=角色组ID
dingding.role.groupId.description=角色组的唯一标识符
dingding.role.roleIds=角色ID列表
dingding.role.roleIds.description=多个角色ID的集合
dingding.role.roles=角色列表
dingding.role.roles.description=角色信息的集合
dingding.role.userIds=用户ID列表
dingding.role.userIds.description=关联的用户ID集合
dingding.role.role_groups=角色组列表
dingding.role.role_groups.description=角色分组信息的集合

# 钉钉第三方用户属性
dingthird.user.userid=员工的userId
dingthird.user.unionid=员工在当前开发者企业账号范围内的唯一标识
dingthird.user.name=员工姓名
dingthird.user.avatar=头像
dingthird.user.job_number=员工工号
dingthird.user.title=职位

# 钉钉第三方用户属性描述
dingthird.user.userid.description=员工的userId
dingthird.user.unionid.description=员工在当前开发者企业账号范围内的唯一标识
dingthird.user.name.description=员工姓名
dingthird.user.avatar.description=头像
dingthird.user.job_number.description=员工工号
dingthird.user.title.description=职位

# ====================== 企业微信用户属性 ======================
wework.user.userid=成员UserID
wework.user.userid.description=成员UserID
wework.user.gender=性别
wework.user.gender.description=性别
wework.user.avatar=头像
wework.user.avatar.description=头像
wework.user.qr_code=员工个人二维码
wework.user.qr_code.description=员工个人二维码
wework.user.mobile=手机号码
wework.user.mobile.description=手机号码
wework.user.email=邮箱
wework.user.email.description=邮箱
wework.user.biz_mail=企业邮箱
wework.user.biz_mail.description=企业邮箱
wework.user.address=地址
wework.user.address.description=地址
wework.user.name=成员名称
wework.user.name.description=成员名称
wework.user.alias=成员别名
wework.user.alias.description=成员别名
wework.user.department=成员所属部门id列表
wework.user.department.description=成员所属部门id列表
wework.user.order=部门内的排序值，默认为0，成员次序以创建时间从小到大排列。个数必须和参数department的个数一致，数值越大排序越前面
wework.user.order.description=部门内的排序值，默认为0，成员次序以创建时间从小到大排列。个数必须和参数department的个数一致，数值越大排序越前面
wework.user.position=职务信息
wework.user.position.description=职务信息
wework.user.is_leader_in_dept=个数必须和参数department的个数一致，表示在所在的部门内是否为部门负责人。1表示为部门负责人，0表示非部门负责人
wework.user.is_leader_in_dept.description=个数必须和参数department的个数一致，表示在所在的部门内是否为部门负责人。1表示为部门负责人，0表示非部门负责人
wework.user.direct_leader=直属上级UserID
wework.user.direct_leader.description=直属上级UserID
wework.user.avatar_mediaid=成员头像的mediaid
wework.user.avatar_mediaid.description=成员头像的mediaid
wework.user.enable=启用/禁用成员。1表示启用成员，0表示禁用成员
wework.user.enable.description=启用/禁用成员。1表示启用成员，0表示禁用成员
wework.user.extattr=自定义字段
wework.user.extattr.description=自定义字段
wework.user.to_invite=是否邀请该成员使用企业微信（将通过微信服务通知或短信或邮件下发邀请，每天自动下发一次，最多持续3个工作日），默认值为true。
wework.user.to_invite.description=是否邀请该成员使用企业微信（将通过微信服务通知或短信或邮件下发邀请，每天自动下发一次，最多持续3个工作日），默认值为true。
wework.user.external_profile=成员对外属性
wework.user.external_profile.description=成员对外属性
wework.user.external_position=对外职务
wework.user.external_position.description=对外职务
wework.user.nickname=视频号名字
wework.user.nickname.description=视频号名字
wework.user.main_department=主部门id
wework.user.main_department.description=主部门id
wework.user.telephone=座机
wework.user.telephone.description=座机

# ====================== 企业微信部门属性 ======================
wework.org.id=部门id，若不填该参数，将自动生成id
wework.org.id.description=部门id，若不填该参数，将自动生成id
wework.org.name=部门名称
wework.org.name.description=部门名称
wework.org.name_en=部门英文名称
wework.org.name_en.description=部门英文名称
wework.org.parentid=父部门id
wework.org.parentid.description=父部门id
wework.org.order=在父部门中的次序值。order值大的排序靠前
wework.org.order.description=在父部门中的次序值。order值大的排序靠前

# ====================== JWT用户属性 ======================
jwt.user.sub=用户CN
jwt.user.sub.description=用户CN
jwt.user.name=姓名
jwt.user.name.description=姓名
jwt.user.mobile=手机号
jwt.user.mobile.description=手机号
jwt.user.email=电子邮箱
jwt.user.email.description=电子邮箱
jwt.user.userId=企微userid
jwt.user.userId.description=企微userid

# ====================== ETL用户属性 ======================
etl.user.user_id=userId
etl.user.user_id.description=userId
etl.user.account_id=accountId
etl.user.account_id.description=accountId
etl.user.login_id=login_id
etl.user.login_id.description=login_id
etl.user.name=姓名
etl.user.name.description=姓名
etl.user.mobile=手机号
etl.user.mobile.description=手机号
etl.user.mail=邮箱
etl.user.mail.description=邮箱
etl.user.tel=座机号
etl.user.tel.description=座机号
etl.user.nick_name=昵称
etl.user.nick_name.description=昵称
etl.user.gender=性别
etl.user.gender.description=性别
etl.user.group_positions=部门职位
etl.user.group_positions.description=部门职位
etl.user.status=状态
etl.user.status.description=状态
etl.user.user_job_number=工号
etl.user.user_job_number.description=工号

# ====================== ETL部门属性 ======================
etl.org.name=部门名称
etl.org.name.description=部门名称
etl.org.code=部门编号
etl.org.code.description=部门编号
etl.org.parent_code=父部门编号
etl.org.parent_code.description=父部门编号

# ====================== 飞书用户属性 ======================
feishu.user.user_id=userId
feishu.user.user_id.description=userId
feishu.user.union_id=union_id
feishu.user.union_id.description=union_id
feishu.user.name=姓名
feishu.user.name.description=姓名
feishu.user.nickname=昵称
feishu.user.nickname.description=昵称
feishu.user.mobile=手机号
feishu.user.mobile.description=手机号
feishu.user.email=邮箱
feishu.user.email.description=邮箱
feishu.user.enterprise_email=企业邮箱
feishu.user.enterprise_email.description=企业邮箱
feishu.user.avatar=头像
feishu.user.avatar.description=头像
feishu.user.gender=性别
feishu.user.gender.description=性别
feishu.user.job_title=职务
feishu.user.job_title.description=职务
feishu.user.department_ids=部门ID
feishu.user.department_ids.description=部门ID
feishu.user.status=状态
feishu.user.status.description=状态
feishu.user.employee_no=工号
feishu.user.employee_no.description=工号
feishu.user.employee_type=员工类型
feishu.user.employee_type.description=员工类型
feishu.user.is_tenant_manager=是否是租户超级管理员
feishu.user.is_tenant_manager.description=是否是租户超级管理员
feishu.user.orders=用户排序信息，主部门的部门排序order必须最大
feishu.user.orders.description=用户排序信息，主部门的部门排序order必须最大
feishu.user.en_name=英文名
feishu.user.en_name.description=英文名
feishu.user.mobile_visible=手机号码的可见性
feishu.user.mobile_visible.description=手机号码的可见性
feishu.user.avatar_key=头像的文件Key
feishu.user.avatar_key.description=头像的文件Key
feishu.user.leader_user_id=用户的直接主管的用户ID
feishu.user.leader_user_id.description=用户的直接主管的用户ID
feishu.user.assign_info=用户席位列表
feishu.user.assign_info.description=用户席位列表
feishu.user.city=工作城市
feishu.user.city.description=工作城市
feishu.user.country=国家
feishu.user.country.description=国家
feishu.user.work_station=工位
feishu.user.work_station.description=工位
feishu.user.join_time=入职时间，时间戳格式
feishu.user.join_time.description=入职时间，时间戳格式
feishu.user.geo=数据驻留地
feishu.user.geo.description=数据驻留地
feishu.user.job_level_id=职级ID
feishu.user.job_level_id.description=职级ID
feishu.user.job_family_id=序列ID
feishu.user.job_family_id.description=序列ID
feishu.user.subscription_ids=分配给用户的席位ID列表，需开通“分配用户席位”权限。
feishu.user.subscription_ids.description=分配给用户的席位ID列表，需开通“分配用户席位”权限。
feishu.user.department_path=部门路径
feishu.user.department_path.description=部门路径
feishu.user.dotted_line_leader_user_ids=虚线上级ID
feishu.user.dotted_line_leader_user_ids.description=虚线上级ID
feishu.user.custom_attrs=自定义字段
feishu.user.custom_attrs.description=自定义字段

# ====================== 飞书部门属性 ======================
feishu.org.name=部门名称
feishu.org.name.description=部门名称
feishu.org.department_id=部门ID
feishu.org.department_id.description=部门ID
feishu.org.parent_department_id=父部门ID
feishu.org.parent_department_id.description=父部门ID
feishu.org.open_department_id=部门的open_id
feishu.org.open_department_id.description=部门的open_id
feishu.org.chat_id=部门群ID
feishu.org.chat_id.description=部门群ID
feishu.org.member_count=当前部门及其下属部门下用户（包含部门负责人）的个数
feishu.org.member_count.description=当前部门及其下属部门下用户（包含部门负责人）的个数
feishu.org.i18n_name=国际化的部门名称
feishu.org.i18n_name.description=国际化的部门名称
feishu.org.leader_user_id=部门主管用户ID
feishu.org.leader_user_id.description=部门主管用户ID
feishu.org.order=部门的排序，即部门在其同级部门的展示顺序
feishu.org.order.description=部门的排序，即部门在其同级部门的展示顺序
feishu.org.unit_ids=部门单位自定义ID列表，当前只支持一个
feishu.org.unit_ids.description=部门单位自定义ID列表，当前只支持一个
feishu.org.create_group_chat=是否创建部门群，默认不创建。创建部门群时，默认群名为部门名，默认群主为部门主负责人
feishu.org.create_group_chat.description=是否创建部门群，默认不创建。创建部门群时，默认群名为部门名，默认群主为部门主负责人
feishu.org.leaders=部门负责人
feishu.org.leaders.description=部门负责人
feishu.org.group_chat_employee_types=部门群雇员类型限制,不传该字段时，则部门成员类型是所有类型。
feishu.org.group_chat_employee_types.description=部门群雇员类型限制,不传该字段时，则部门成员类型是所有类型。
feishu.org.department_hrbps=部门HRBP
feishu.org.department_hrbps.description=部门HRBP
feishu.org.primary_member_count=当前部门及其下属部门的主属成员（即成员的主部门为当前部门）的数量
feishu.org.primary_member_count.description=当前部门及其下属部门的主属成员（即成员的主部门为当前部门）的数量

# ====================== SCIM用户属性 ======================
scim.user.external_id=外部用户ID
scim.user.external_id.description=外部用户ID
scim.user.user_name=用户名
scim.user.user_name.description=用户名
scim.user.display_name=姓名
scim.user.display_name.description=姓名
scim.user.nick_name=昵称
scim.user.nick_name.description=昵称
scim.user.password=密码
scim.user.password.description=密码
scim.user.phone_number=手机号
scim.user.phone_number.description=手机号
scim.user.user_extension=用户扩展属性
scim.user.user_extension.description=用户扩展属性
scim.user.email=邮箱
scim.user.email.description=邮箱

# ====================== AZUREAD用户属性 ======================
azuread.user.external_id=外部用户ID
azuread.user.external_id.description=外部用户ID
azuread.user.user_name=用户名
azuread.user.user_name.description=用户名
azuread.user.display_name=姓名
azuread.user.display_name.description=姓名
azuread.user.nick_name=昵称
scazureadim.user.nick_name.description=昵称
azuread.user.password=密码
azuread.user.password.description=密码
azuread.user.phone_number=手机号
azuread.user.phone_number.description=手机号
azuread.user.user_extension=用户扩展属性
azuread.user.user_extension.description=用户扩展属性
azuread.user.email=邮箱
azuread.user.email.description=邮箱

# ====================== OAuth2用户属性 ======================
oauth2.user.sub=主题
oauth2.user.sub.description=主题
oauth2.user.username=用户名
oauth2.user.username.description=用户名
oauth2.user.phone_number=手机号
oauth2.user.phone_number.description=手机号
oauth2.user.email=邮箱
oauth2.user.email.description=邮箱

# ====================== OIDC用户属性 ======================
oidc.user.sub=主题
oidc.user.sub.description=主题
oidc.user.username=用户名
oidc.user.username.description=用户名
oidc.user.phone_number=手机号
oidc.user.phone_number.description=手机号
oidc.user.email=邮箱
oidc.user.email.description=邮箱

# ====================== CAS用户属性 ======================
cas.user.sub=user标识
cas.user.sub.description=user标识

# ====================== EHR用户属性 ======================
ehr.user.PK_DEPT=部门ID
ehr.user.PK_DEPT.description=部门ID
ehr.user.CERTIFICATE=证件号码
ehr.user.CERTIFICATE.description=证件号码
ehr.user.POST_CHANNEL=岗位序列
ehr.user.POST_CHANNEL.description=岗位序列
ehr.user.POS_LEVEL_PK=POS_LEVEL_PK
ehr.user.POS_LEVEL_PK.description=POS_LEVEL_PK
ehr.user.POSITION_CODE=岗位编码
ehr.user.POSITION_CODE.description=岗位编码
ehr.user.IS_CONVERSION_YSH=是否转正
ehr.user.IS_CONVERSION_YSH.description=是否转正
ehr.user.SEX=性别
ehr.user.SEX.description=性别
ehr.user.DIRECT_SUPERIOR_NAME=直接上级姓名
ehr.user.DIRECT_SUPERIOR_NAME.description=直接上级姓名
ehr.user.PHONE=手机号
ehr.user.PHONE.description=手机号
ehr.user.ENTER_AGILE_DATE=入职日期
ehr.user.ENTER_AGILE_DATE.description=入职日期
ehr.user.LOGIN_ACCOUNT=LOGIN_ACCOUNT
ehr.user.LOGIN_ACCOUNT.description=LOGIN_ACCOUNT
ehr.user.INIT_PASSWORD=初始化密码
ehr.user.INIT_PASSWORD.description=初始化密码
ehr.user.IS_CONVERT=是否转档
ehr.user.IS_CONVERT.description=是否转档
ehr.user.EMAIL=邮箱
ehr.user.EMAIL.description=邮箱
ehr.user.DIRECT_SUPERIOR_JOB_NUMBER=直接上级工号
ehr.user.DIRECT_SUPERIOR_JOB_NUMBER.description=直接上级工号
ehr.user.OWNER_GROUP=所属组织
ehr.user.OWNER_GROUP.description=所属组织
ehr.user.POSITION_RANK=职层
ehr.user.POSITION_RANK.description=职层
ehr.user.PK_ORG=PK_ORG
ehr.user.PK_ORG.description=PK_ORG
ehr.user.POSITION=岗位名称
ehr.user.POSITION.description=岗位名称
ehr.user.PK_PSNDOC=人员ID
ehr.user.PK_PSNDOC.description=人员ID
ehr.user.BIRTH_DATE=生日
ehr.user.BIRTH_DATE.description=生日
ehr.user.FINANCIAL_EXPENSE_DIVISION_COMPANY=财务费用划分公司
ehr.user.FINANCIAL_EXPENSE_DIVISION_COMPANY.description=财务费用划分公司
ehr.user.STANDARD_POST=标准职衔
ehr.user.STANDARD_POST.description=标准职衔
ehr.user.LOGIN_USERNAME=登录名称
ehr.user.LOGIN_USERNAME.description=登录名称
ehr.user.RESIGN_DATE=离职时间
ehr.user.RESIGN_DATE.description=离职时间
ehr.user.ORGANIZATION_ENTRY_DATE=ORGANIZATION_ENTRY_DATE
ehr.user.ORGANIZATION_ENTRY_DATE.description=ORGANIZATION_ENTRY_DATE
ehr.user.PK_GROUP=PK_GROUP
ehr.user.PK_GROUP.description=PK_GROUP
ehr.user.OWNING_SYSTEM=所属系统
ehr.user.OWNING_SYSTEM.description=所属系统
ehr.user.JOB_NUMBER=工号
ehr.user.JOB_NUMBER.description=工号
ehr.user.PK_POST=PK_POST
ehr.user.PK_POST.description=PK_POST
ehr.user.DEPARTMENT=DEPARTMENT
ehr.user.DEPARTMENT.description=DEPARTMENT
ehr.user.ENTER_GROUP_DATE=ENTER_GROUP_DATE
ehr.user.ENTER_GROUP_DATE.description=ENTER_GROUP_DATE
ehr.user.DEPARTMENT_CODE=DEPARTMENT_CODE
ehr.user.DEPARTMENT_CODE.description=DEPARTMENT_CODE
ehr.user.NAME=姓名
ehr.user.NAME.description=姓名
ehr.user.AD_ACCOUNT=AD账号
ehr.user.AD_ACCOUNT.description=AD账号
ehr.user.IS_RESIGN=在职状态
ehr.user.IS_RESIGN.description=在职状态
ehr.user.MODIFIEDTIME=MODIFIEDTIME
ehr.user.MODIFIEDTIME.description=MODIFIEDTIME
ehr.user.PERSONNEL_TYPE=人员类别
ehr.user.PERSONNEL_TYPE.description=人员类别
ehr.user.CONVERSION_DATE_YSH=转正日期
ehr.user.CONVERSION_DATE_YSH.description=转正日期
ehr.user.TS=TS
ehr.user.TS.description=TS
ehr.user.CERTIFICATE_TYPE=证件类型
ehr.user.CERTIFICATE_TYPE.description=证件类型
ehr.user.DIRECT_SUPERIOR_ID=上级主管ID
ehr.user.DIRECT_SUPERIOR_ID.description=上级主管ID

# ====================== EHR部门属性 ======================
ehr.org.PK_ORG=父级组织ID
ehr.org.PK_ORG.description=父级组织ID
ehr.org.PK_DEPT1=组织ID
ehr.org.PK_DEPT1.description=组织ID
ehr.org.DEPTNAME1=组织名称
ehr.org.DEPTNAME1.description=组织名称
ehr.org.DEPTCODE1=组织编码
ehr.org.DEPTCODE1.description=组织编码
ehr.org.DEPTLEADER_PSNCODE=DEPTLEADER_PSNCODE
ehr.org.DEPTLEADER_PSNCODE.description=DEPTLEADER_PSNCODE
ehr.org.STATUS=状态
ehr.org.STATUS.description=状态
ehr.org.DEPT_LEVEL=部门级别
ehr.org.DEPT_LEVEL.description=部门级别
ehr.org.IS_DEPT_ARCHIVE=是否启用
ehr.org.IS_DEPT_ARCHIVE.description=是否启用
ehr.org.PK_PSNDOC=组织负责人ID
ehr.org.PK_PSNDOC.description=组织负责人ID
ehr.org.UNITCODE=UNITCODE
ehr.org.UNITCODE.description=UNITCODE
ehr.org.DEPTLEADER_PSNNAME=DEPTLEADER_PSNNAME
ehr.org.DEPTLEADER_PSNNAME.description=DEPTLEADER_PSNNAME
ehr.org.TS=时间戳
ehr.org.TS.description=时间戳
ehr.org.ORGLEVEL=组织级别
ehr.org.ORGLEVEL.description=组织级别
ehr.org.PK_GROUP=父级组织ID
ehr.org.PK_GROUP.description=父级组织ID
ehr.org.UNITCODE1=组织编码
ehr.org.UNITCODE1.description=组织编码
ehr.org.IS_ORG_ENABLE=是否启用
ehr.org.IS_ORG_ENABLE.description=是否启用
ehr.org.PK_ORG1=组织ID
ehr.org.PK_ORG1.description=组织ID
ehr.org.UNITNAME1=组织名称
ehr.org.UNITNAME1.description=组织名称

# ====================== RRS用户属性 ======================
rrs.user.empId=用户ID
rrs.user.empId.description=用户ID
rrs.user.effectDate=生效日期
rrs.user.effectDate.description=生效日期
rrs.user.mainDepartment=主部门
rrs.user.mainDepartment.description=主部门
rrs.user.mainDepartment_source=主部门信息
rrs.user.mainDepartment_source.description=主部门信息
rrs.user.ptjDepartment_source=兼职部门信息
rrs.user.ptjDepartment_source.description=兼职部门信息
rrs.user.department=部门
rrs.user.department.description=部门
rrs.user.jobDuty=职务
rrs.user.jobDuty.description=职务
rrs.user.jobSequence=序列
rrs.user.jobSequence.description=序列
rrs.user.jobTitle=职位
rrs.user.jobTitle.description=职位
rrs.user.jobLevel=职级
rrs.user.jobLevel.description=职级
rrs.user.jobGrade=职等
rrs.user.jobGrade.description=职等
rrs.user.manager=直属主管
rrs.user.manager.description=直属主管
rrs.user.employeeStatus=员工状态
rrs.user.employeeStatus.description=员工状态
rrs.user.employeeType=员工类型
rrs.user.employeeType.description=员工类型
rrs.user.onBoardingDate=入职日期
rrs.user.onBoardingDate.description=入职日期
rrs.user.probationDate=转正日期
rrs.user.probationDate.description=转正日期
rrs.user.lastDayOfWork=离职日期
rrs.user.lastDayOfWork.description=离职日期
rrs.user.reasonForOffBoarding=离职原因
rrs.user.reasonForOffBoarding.description=离职原因
rrs.user.employeeNumber=工号
rrs.user.employeeNumber.description=工号
rrs.user.name=姓名
rrs.user.name.description=姓名
rrs.user.gender=性别
rrs.user.gender.description=性别
rrs.user.idType=证件类型
rrs.user.idType.description=证件类型
rrs.user.idNumber=证件号码
rrs.user.idNumber.description=证件号码
rrs.user.dateOfBirth=出生日期
rrs.user.dateOfBirth.description=出生日期
rrs.user.mobileNumber=手机号码
rrs.user.mobileNumber.description=手机号码
rrs.user.personalEmail=个人邮箱
rrs.user.personalEmail.description=个人邮箱
rrs.user.email=邮箱
rrs.user.email.description=邮箱
rrs.user.syncDing=是否同步钉钉通讯录
rrs.user.syncDing.description=是否同步钉钉通讯录
rrs.user.extEmpId=来源人员ID
rrs.user.extEmpId.description=来源人员ID
rrs.user.extTel=分机号
rrs.user.extTel.description=分机号
rrs.user.extEntEmail=员工的企业邮箱
rrs.user.extEntEmail.description=员工的企业邮箱
rrs.user.extAccountName=账户名称
rrs.user.extAccountName.description=账户名称
rrs.user.extAdAccount=AD账号
rrs.user.extAdAccount.description=AD账号
rrs.user.extIsDingAdmin=是否为企业的管理员（钉钉）
rrs.user.extIsDingAdmin.description=是否为企业的管理员（钉钉）
rrs.user.extIsActiveDing=是否激活了钉钉
rrs.user.extIsActiveDing.description=是否激活了钉钉
rrs.user.extInSys=所属系统
rrs.user.extInSys.description=所属系统
rrs.user.extFromSys=系统来源
rrs.user.extFromSys.description=系统来源
rrs.user.extDeptId=来源部门ID
rrs.user.extDeptId.description=来源部门ID
rrs.user.extRrsEmpId=来源部门睿人事ID
rrs.user.extRrsEmpId.description=来源部门睿人事ID
rrs.user.extJobLevel=来源职层
rrs.user.extJobLevel.description=来源职层
rrs.user.extJobName=来源岗位名称
rrs.user.extJobName.description=来源岗位名称
rrs.user.extJobCode=来源岗位编码
rrs.user.extJobCode.description=来源岗位编码
rrs.user.extJobSequence=来源岗位序列
rrs.user.extJobSequence.description=来源岗位序列
rrs.user.extJobTitle=来源标准职衔
rrs.user.extJobTitle.description=来源标准职衔
rrs.user.extManager=来源直接上级人员ID
rrs.user.extManager.description=来源直接上级人员ID
rrs.user.extRrsManager=来源直接上级人员睿人事ID
rrs.user.extRrsManager.description=来源直接上级人员睿人事ID
rrs.user.extFinCompany=财务费用划分公司
rrs.user.extFinCompany.description=财务费用划分公司
rrs.user.extIsConvert=是否转档
rrs.user.extIsConvert.description=是否转档
rrs.user.extPostStatus=在职状态
rrs.user.extPostStatus.description=在职状态
rrs.user.extPsnType=人员类别
rrs.user.extPsnType.description=人员类别
rrs.user.extEntryDate=来源入职日期
rrs.user.extEntryDate.description=来源入职日期
rrs.user.extIsRegular=是否转正
rrs.user.extIsRegular.description=是否转正
rrs.user.extPsnSys=任职信息系统来源
rrs.user.extPsnSys.description=任职信息系统来源
rrs.user.managerSource=直属主管信息
rrs.user.managerSource.description=直属主管信息

# ====================== RRS部门属性 ======================
rrs.org.orgId=组织ID
rrs.org.orgId.description=组织ID
rrs.org.effectDate=生效日期
rrs.org.effectDate.description=生效日期
rrs.org.name=组织名称
rrs.org.name.description=组织名称
rrs.org.code=组织编码
rrs.org.code.description=组织编码
rrs.org.type=组织类型
rrs.org.type.description=组织类型
rrs.org.managerEmpId=组织负责人ID
rrs.org.managerEmpId.description=组织负责人ID
rrs.org.parentOrgId=上级组织ID
rrs.org.parentOrgId.description=上级组织ID
rrs.org.order=组织序号
rrs.org.order.description=组织序号
rrs.org.status=是否启用
rrs.org.status.description=是否启用
rrs.org.syncStatus=是否同步钉钉通讯录
rrs.org.syncStatus.description=是否同步钉钉通讯录
rrs.org.extOrgId=来源组织ID
rrs.org.extOrgId.description=来源组织ID
rrs.org.extName=来源组织名称
rrs.org.extName.description=来源组织名称
rrs.org.extCode=来源组织编码
rrs.org.extCode.description=来源组织编码
rrs.org.extManagerEmpId=来源组织负责人ID
rrs.org.extManagerEmpId.description=来源组织负责人ID
rrs.org.extRrsManagerEmpId=来源组织负责人睿人事ID
rrs.org.extRrsManagerEmpId.description=来源组织负责人睿人事ID
rrs.org.extParentOrgId=来源上级组织ID
rrs.org.extParentOrgId.description=来源上级组织ID
rrs.org.extRrsParentOrgId=来源上级组织睿人事ID
rrs.org.extRrsParentOrgId.description=来源上级组织睿人事ID
rrs.org.extOrder=来源组织序号
rrs.org.extOrder.description=来源组织序号
rrs.org.extStatus=来源是否启用
rrs.org.extStatus.description=来源是否启用
rrs.org.extIsDing=是否钉钉自建组织
rrs.org.extIsDing.description=是否钉钉自建组织
rrs.org.extType=钉钉组织类型
rrs.org.extType.description=钉钉组织类型
rrs.org.extRemark=备注
rrs.org.extRemark.description=备注
rrs.org.extFromSys=系统来源
rrs.org.extFromSys.description=系统来源

# ====================== EDOC用户属性 ======================
edoc.user.id=id
edoc.user.id.description=id
edoc.user.account=用户名
edoc.user.account.description=用户名
edoc.user.email=Email
edoc.user.email.description=Email
edoc.user.name=用户姓名
edoc.user.name.description=用户姓名
edoc.user.gender=性别
edoc.user.gender.description=性别
edoc.user.birthday=生日
edoc.user.birthday.description=生日
edoc.user.telephone=电话
edoc.user.telephone.description=电话
edoc.user.mobile=手机
edoc.user.mobile.description=手机
edoc.user.status=用户状态
edoc.user.status.description=用户状态
edoc.user.fax=传真
edoc.user.fax.description=传真
edoc.user.mainPositionId=主职位ID
edoc.user.mainPositionId.description=主职位ID
edoc.user.remark=备注
edoc.user.remark.description=备注
edoc.user.MainDepartmentId=主部门ID
edoc.user.MainDepartmentId.description=主部门ID
edoc.user.MainDepartmentName=主部门名称
edoc.user.MainDepartmentName.description=主部门名称
edoc.user.TopPersonalFolderId=个人内容库ID
edoc.user.TopPersonalFolderId.description=个人内容库ID
edoc.user.groupIdList=用户组列表
edoc.user.groupIdList.description=用户组列表
edoc.user.mainPositionIdentityId=职位自增ID
edoc.user.mainPositionIdentityId.description=主职位自增ID
edoc.user.mainPositionName=主职位
edoc.user.mainPositionName.description=主职位名称
edoc.user.role=Role
edoc.user.role.description=Role
edoc.user.enableTime=启用时间
edoc.user.enableTime.description=启用时间
edoc.user.token=token
edoc.user.token.description=token
edoc.user.code=用户编号
edoc.user.code.description=用户编号
edoc.user.thirdPartId=第三方ID
edoc.user.thirdPartId.description=第三方ID
edoc.user.userId=userId
edoc.user.userId.description=userId
edoc.user.identityId=自增长ID
edoc.user.identityId.description=自增长ID
edoc.user.expirationDate=过期时间
edoc.user.expirationDate.description=过期时间
edoc.user.PasswordLastChangeTime=修改密码时间
edoc.user.PasswordLastChangeTime.description=修改密码时间
edoc.user.CreatorId=创建人
edoc.user.CreatorId.description=创建人
edoc.user.CreateTime=创建时间
edoc.user.CreateTime.description=创建时间
edoc.user.LastChangeTime=修改时间
edoc.user.LastChangeTime.description=修改时间
edoc.user.Signed=是否登录过
edoc.user.Signed.description=是否登录过
edoc.user.PasswordWrongCount=密码连续错误次数
edoc.user.PasswordWrongCount.description=密码连续错误次数
edoc.user.MainDepartmentIdentityId=主部门自增ID
edoc.user.MainDepartmentIdentityId.description=主部门自增ID
edoc.user.IsAuthority=IsAuthority
edoc.user.IsAuthority.description=IsAuthority
edoc.user.positionList=职位列表
edoc.user.positionList.description=职位列表

# ====================== EDOC部门属性 ======================
edoc.org.id=id
edoc.org.id.description=id
edoc.org.name=部门名称
edoc.org.name.description=部门名称
edoc.org.parentId=上级部门ID
edoc.org.parentId.description=上级部门ID
edoc.org.ParentName=上级部门名称
edoc.org.ParentName.description=上级部门名称
edoc.org.code=部门编号
edoc.org.code.description=部门编号
edoc.org.managerPositionId=部门主管职位
edoc.org.managerPositionId.description=部门主管职位
edoc.org.ManagerPositionIdentityId=部门主管职位自增ID
edoc.org.ManagerPositionIdentityId.description=部门主管职位自增ID
edoc.org.sort=自定义排序
edoc.org.sort.description=自定义排序
edoc.org.remark=备注
edoc.org.remark.description=备注
edoc.org.IdentityId=自增长ID
edoc.org.IdentityId.description=自增长ID
edoc.org.thirdPartId=第三方ID
edoc.org.thirdPartId.description=第三方ID
edoc.org.ParentIdentityId=上级部门自增ID
edoc.org.ParentIdentityId.description=上级部门自增ID
edoc.org.HaveChildren=是否有子部门
edoc.org.HaveChildren.description=是否有子部门
edoc.org.CreateTime=创建时间
edoc.org.CreateTime.description=创建时间
edoc.org.EnableTime=启用时间
edoc.org.EnableTime.description=启用时间
edoc.org.ExpirationTime=过期时间
edoc.org.ExpirationTime.description=过期时间
edoc.org.deptId=部门ID
edoc.org.deptId.description=部门ID
edoc.org.managerPositionName=主管职位名称
edoc.org.managerPositionName.description=主管职位名称
edoc.org.positionIdentytiId=职位编号自增ID
edoc.org.positionIdentytiId.description=职位编号自增ID

# ====================== SEYON用户属性 ======================
seeyon.user.id=Id，唯一标识人员
seeyon.user.id.description=Id，唯一标识人员
seeyon.user.name=姓名
seeyon.user.name.description=姓名
seeyon.user.loginName=登录名
seeyon.user.loginName.description=登录名
seeyon.user.code=人员编码
seeyon.user.code.description=人员编码
seeyon.user.createTime=创建时间
seeyon.user.createTime.description=创建时间
seeyon.user.updateTime=更新时间
seeyon.user.updateTime.description=更新时间
seeyon.user.sortId=排序号
seeyon.user.sortId.description=排序号
seeyon.user.desc=说明
seeyon.user.desc.description=说明
seeyon.user.orgAccountId=人员所属单位Id
seeyon.user.orgAccountId.description=人员所属单位Id
seeyon.user.orgAccountName=人员所属单位名称
seeyon.user.orgAccountName.description=人员所属单位名称
seeyon.user.orgLevelId=人员职务级别Id
seeyon.user.orgLevelId.description=人员职务级别Id
seeyon.user.orgLevelName=人员职务级别名称
seeyon.user.orgLevelName.description=人员职务级别名称
seeyon.user.orgPostId=人员岗位Id
seeyon.user.orgPostId.description=人员岗位Id
seeyon.user.orgPostName=人员岗位名称
seeyon.user.orgPostName.description=人员岗位名称
seeyon.user.orgDepartmentId=人员所属部门Id
seeyon.user.orgDepartmentId.description=人员所属部门Id
seeyon.user.second_post=副岗
seeyon.user.second_post.description=副岗
seeyon.user.concurrent_post=兼职
seeyon.user.concurrent_post.description=兼职
seeyon.user.enabled=账户状态
seeyon.user.enabled.description=账户状态
seeyon.user.isDeleted=删除标记
seeyon.user.isDeleted.description=删除标记
seeyon.user.isInternal=是否内部人员
seeyon.user.isInternal.description=是否内部人员
seeyon.user.isLoginable=是否可登录
seeyon.user.isLoginable.description=是否可登录
seeyon.user.isAssigned=是否已分配人员
seeyon.user.isAssigned.description=是否已分配人员
seeyon.user.isAdmin=是否管理员
seeyon.user.isAdmin.description=是否管理员
seeyon.user.isValid=是否可用
seeyon.user.isValid.description=是否可用
seeyon.user.state=人员状态：1为在职，2 为离职
seeyon.user.state.description=人员状态：1为在职，2 为离职
seeyon.user.status=实体状态
seeyon.user.status.description=实体状态
seeyon.user.telNumber=手机号码
seeyon.user.telNumber.description=手机号码
seeyon.user.birthday=出生日期
seeyon.user.birthday.description=出生日期
seeyon.user.officeNum=办公电话
seeyon.user.officeNum.description=办公电话
seeyon.user.emailAddress=电子邮件
seeyon.user.emailAddress.description=电子邮件
seeyon.user.gender=性别：-1为未指定、1为男、2为女
seeyon.user.gender.description=性别：-1为未指定、1为男、2为女
seeyon.user.location=工作地
seeyon.user.location.description=工作地
seeyon.user.reporter=汇报人
seeyon.user.reporter.description=汇报人
seeyon.user.hiredate=入职时间
seeyon.user.hiredate.description=入职时间

# ====================== SEYON部门属性 ======================
seeyon.org.id=ID
seeyon.org.id.description=ID
seeyon.org.orgAccountId=所属单位ID
seeyon.org.orgAccountId.description=所属单位ID
seeyon.org.name=部门名称
seeyon.org.name.description=部门名称
seeyon.org.superiorName=上级名称
seeyon.org.superiorName.description=上级名称
seeyon.org.code=部门代码
seeyon.org.code.description=部门代码
seeyon.org.sortId=排序号
seeyon.org.sortId.description=排序号
seeyon.org.shortName=单位简称
seeyon.org.shortName.description=单位简称
seeyon.org.createTime=创建时间
seeyon.org.createTime.description=创建时间
seeyon.org.updateTime=修改时间
seeyon.org.updateTime.description=修改时间
seeyon.org.isDeleted=是否删除
seeyon.org.isDeleted.description=是否删除
seeyon.org.enabled=是否启用
seeyon.org.enabled.description=是否启用
seeyon.org.status=状态
seeyon.org.status.description=状态
seeyon.org.desc=描述
seeyon.org.desc.description=描述
seeyon.org.path=路径
seeyon.org.path.description=路径
seeyon.org.secondName=外文名称
seeyon.org.secondName.description=外文名称
seeyon.org.isInternal=是否是内部机构
seeyon.org.isInternal.description=是否是内部机构
seeyon.org.isGroup=是否是集团
seeyon.org.isGroup.description=是否是集团
seeyon.org.type=类型
seeyon.org.type.description=类型
seeyon.org.levelScope=只对type=account有效
seeyon.org.levelScope.description=只对type=account有效
seeyon.org.properties=属性
seeyon.org.properties.description=属性
seeyon.org.fax=传真
seeyon.org.fax.description=传真
seeyon.org.unitMail=邮件
seeyon.org.unitMail.description=邮件
seeyon.org.address=地址
seeyon.org.address.description=地址
seeyon.org.chiefLeader=负责人
seeyon.org.chiefLeader.description=负责人
seeyon.org.zipCode=邮编
seeyon.org.zipCode.description=邮编
seeyon.org.telephone=电话
seeyon.org.telephone.description=电话
seeyon.org.superior=上级部门Id，一级部门则为所属单位Id
seeyon.org.superior.description=上级部门Id，一级部门则为所属单位Id
seeyon.org.sortIdType=sortIdType
seeyon.org.sortIdType.description=sortIdType
seeyon.org.isCanAccess=是否允许访问
seeyon.org.isCanAccess.description=是否允许访问
seeyon.org.entityType=实体类型
seeyon.org.entityType.description=实体类型
seeyon.org.valid=是否合法
seeyon.org.valid.description=是否合法
seeyon.org.parentPath=父部门的路径
seeyon.org.parentPath.description=父部门的路径

# ====================== BEISEN用户属性 ======================
beisen.user.originalId=外部ID标识(非北森系统)
beisen.user.originalId.description=外部ID标识(非北森系统)
beisen.user.employeeInfo=查询员工信息数据结果
beisen.user.employeeInfo.description=查询员工信息数据结果
beisen.user.recordInfo=查询员工任职记录数据结果
beisen.user.recordInfo.description=查询员工任职记录数据结果
beisen.user.userID=员工UserID
beisen.user.userID.description=员工UserID
beisen.user.name=姓名
beisen.user.name.description=姓名
beisen.user._Name=姓名(拼音)
beisen.user._Name.description=姓名(拼音)
beisen.user.phoneticOfXing=姓拼音
beisen.user.phoneticOfXing.description=姓拼音
beisen.user.phoneticOfMing=名拼音
beisen.user.phoneticOfMing.description=名拼音
beisen.user.gender=性别
beisen.user.gender.description=性别
beisen.user.email=电子邮件
beisen.user.email.description=电子邮件
beisen.user.iDType=证件类型
beisen.user.iDType.description=证件类型
beisen.user.iDNumber=证件号码
beisen.user.iDNumber.description=证件号码
beisen.user.isLongTermCertificate=是否长期证件
beisen.user.isLongTermCertificate.description=是否长期证件
beisen.user.certificateStartDate=证件开始日期
beisen.user.certificateStartDate.description=证件开始日期
beisen.user.certificateValidityTerm=证件结束日期
beisen.user.certificateValidityTerm.description=证件结束日期
beisen.user.birthday=出生日期
beisen.user.birthday.description=出生日期
beisen.user.workDate=参加工作时间
beisen.user.workDate.description=参加工作时间
beisen.user.homeAddress=联系地址
beisen.user.homeAddress.description=联系地址
beisen.user.mobilePhone=手机号码
beisen.user.mobilePhone.description=手机号码
beisen.user.weiXin=微信
beisen.user.weiXin.description=微信
beisen.user.iDPhoto=照片，DFS附件路径
beisen.user.iDPhoto.description=照片，DFS附件路径
beisen.user.smallIDPhoto=照片缩略图
beisen.user.smallIDPhoto.description=照片缩略图
beisen.user.iDPortraitSide=身份证人面像
beisen.user.iDPortraitSide.description=身份证人面像
beisen.user.iDCountryEmblemSide=身份证国徽面
beisen.user.iDCountryEmblemSide.description=身份证国徽面
beisen.user.allowToLoginIn=允许登录系统
beisen.user.allowToLoginIn.description=允许登录系统
beisen.user.personalHomepage=个人主页
beisen.user.personalHomepage.description=个人主页
beisen.user.speciality=特长
beisen.user.speciality.description=特长
beisen.user.major=专业
beisen.user.major.description=专业
beisen.user.postalCode=邮政编码
beisen.user.postalCode.description=邮政编码
beisen.user.passportNumber=护照号码
beisen.user.passportNumber.description=护照号码
beisen.user.constellation=星座
beisen.user.constellation.description=星座
beisen.user.bloodType=血型
beisen.user.bloodType.description=血型
beisen.user.residenceAddress=户籍地址
beisen.user.residenceAddress.description=户籍地址
beisen.user.joinPartyDate=入党/团日期
beisen.user.joinPartyDate.description=入党/团日期
beisen.user.domicileType=户口类别
beisen.user.domicileType.description=户口类别
beisen.user.emergencyContact=紧急联系人
beisen.user.emergencyContact.description=紧急联系人
beisen.user.emergencyContactRelationship=紧急联系人与本人关系
beisen.user.emergencyContactRelationship.description=紧急联系人与本人关系
beisen.user.emergencyContactPhone=联系电话
beisen.user.emergencyContactPhone.description=联系电话
beisen.user.qQ=QQ
beisen.user.qQ.description=QQ
beisen.user.aboutMe=关于我
beisen.user.aboutMe.description=关于我
beisen.user.homePhone=家庭电话
beisen.user.homePhone.description=家庭电话
beisen.user.graduateDate=毕业时间
beisen.user.graduateDate.description=毕业时间
beisen.user.marryCategory=婚姻状况
beisen.user.marryCategory.description=婚姻状况
beisen.user.politicalStatus=政治面貌
beisen.user.politicalStatus.description=政治面貌
beisen.user.nationality=国家（地区）
beisen.user.nationality.description=国家（地区）
beisen.user.nation=民族
beisen.user.nation.description=民族
beisen.user.birthplace=户籍所在地
beisen.user.birthplace.description=户籍所在地
beisen.user.registAddress=籍贯
beisen.user.registAddress.description=籍贯
beisen.user.educationLevel=最高学历
beisen.user.educationLevel.description=最高学历
beisen.user.lastSchool=毕业学校
beisen.user.lastSchool.description=毕业学校
beisen.user.engName=其他语言姓名
beisen.user.engName.description=其他语言姓名
beisen.user.firstname=其他语言姓名_名
beisen.user.firstname.description=其他语言姓名_名
beisen.user.lastname=其他语言姓名_姓
beisen.user.lastname.description=其他语言姓名_姓
beisen.user.officeTel=办公电话
beisen.user.officeTel.description=办公电话
beisen.user.businessAddress=办公地址
beisen.user.businessAddress.description=办公地址
beisen.user.backupMail=个人邮箱
beisen.user.backupMail.description=个人邮箱
beisen.user.applicantId=应聘者id
beisen.user.applicantId.description=应聘者id
beisen.user.applyIdV6=招聘申请id
beisen.user.applyIdV6.description=招聘申请id
beisen.user.applicantIdV6=招聘申请id
beisen.user.applicantIdV6.description=招聘申请id
beisen.user.age=年龄
beisen.user.age.description=年龄
beisen.user.businessModifiedBy=业务修改人
beisen.user.businessModifiedBy.description=业务修改人
beisen.user.businessModifiedTime=业务修改时间
beisen.user.businessModifiedTime.description=业务修改时间
beisen.user.sourceType=类型
beisen.user.sourceType.description=类型
beisen.user.firstEntryDate=首次进入公司日期
beisen.user.firstEntryDate.description=首次进入公司日期
beisen.user.latestEntryDate=最新进入公司日期
beisen.user.latestEntryDate.description=最新进入公司日期
beisen.user.tutorNew=导师UserId
beisen.user.tutorNew.description=导师UserId
beisen.user.iDFront=证件正面
beisen.user.iDFront.description=证件正面
beisen.user.iDBehind=证件反面
beisen.user.iDBehind.description=证件反面
beisen.user.preRetireDate=预计退休日期
beisen.user.preRetireDate.description=预计退休日期
beisen.user.actualRetireDate=实际退休日期
beisen.user.actualRetireDate.description=实际退休日期
beisen.user.isConfirmRetireDate=预计退休日期已核准
beisen.user.isConfirmRetireDate.description=预计退休日期已核准
beisen.user.activationState=账号激活状态
beisen.user.activationState.description=账号激活状态
beisen.user.issuingAuthority=签发机关
beisen.user.issuingAuthority.description=签发机关
beisen.user.isDisabled=是否残疾人
beisen.user.isDisabled.description=是否残疾人
beisen.user.disabledNumber=残疾人号
beisen.user.disabledNumber.description=残疾人号
beisen.user.objectId=业务对象实体主键GUID
beisen.user.objectId.description=业务对象实体主键GUID
beisen.user.createdBy=创建人员UserId
beisen.user.createdBy.description=创建人员UserId
beisen.user.createdTime=创建时间
beisen.user.createdTime.description=创建时间
beisen.user.modifiedBy=修改人员UserId
beisen.user.modifiedBy.description=修改人员UserId
beisen.user.modifiedTime=修改时间
beisen.user.modifiedTime.description=修改时间
beisen.user.stdIsDeleted=是否删除
beisen.user.stdIsDeleted.description=是否删除
beisen.user.userID1=任职记录父对象员工信息业务实体UserID
beisen.user.userID1.description=任职记录父对象员工信息业务实体UserID
beisen.user.pObjectDataID=父对象数据员工实体主键GUID
beisen.user.pObjectDataID.description=父对象数据员工实体主键GUID
beisen.user.oIdDepartment=任职部门（如技术部），对应于组织单元业务实体关联的部门OId
beisen.user.oIdDepartment.description=任职部门（如技术部），对应于组织单元业务实体关联的部门OId
beisen.user.startDate=任职记录的生效时间，用于任职记录的时间轴
beisen.user.startDate.description=任职记录的生效时间，用于任职记录的时间轴
beisen.user.stopDate=任职记录的失效时间，用于任职记录的时间轴
beisen.user.stopDate.description=任职记录的失效时间，用于任职记录的时间轴
beisen.user.jobNumber=工号
beisen.user.jobNumber.description=工号
beisen.user.entryDate=入职操作时，任职记录的入职日期，用于任职记录的时间轴
beisen.user.entryDate.description=入职操作时，任职记录的入职日期，用于任职记录的时间轴
beisen.user.lastWorkDate=离职操作时，任职记录的最后工作日的日期，用于任职记录的时间轴
beisen.user.lastWorkDate.description=离职操作时，任职记录的最后工作日的日期，用于任职记录的时间轴
beisen.user.regularizationDate=试用期转正操作时，任职记录的转正日期
beisen.user.regularizationDate.description=试用期转正操作时，任职记录的转正日期
beisen.user.probation=试用期
beisen.user.probation.description=试用期
beisen.user.order=排序号码
beisen.user.order.description=排序号码
beisen.user.employType=雇佣关系
beisen.user.employType.description=雇佣关系
beisen.user.serviceType=任职类型
beisen.user.serviceType.description=任职类型
beisen.user.serviceStatus=任职状态
beisen.user.serviceStatus.description=任职状态
beisen.user.approvalStatus=审批状态
beisen.user.approvalStatus.description=审批状态
beisen.user.employmentSource=人员来源实体对象
beisen.user.employmentSource.description=人员来源实体对象
beisen.user.employmentForm=用工形式实体对象
beisen.user.employmentForm.description=用工形式实体对象
beisen.user.isCharge=是否部门负责人
beisen.user.isCharge.description=是否部门负责人
beisen.user.oIdJobPost=职务实体
beisen.user.oIdJobPost.description=职务实体
beisen.user.oIdJobSequence=职务序列实体
beisen.user.oIdJobSequence.description=职务序列实体
beisen.user.oIdProfessionalLine=专业条线实体
beisen.user.oIdProfessionalLine.description=专业条线实体
beisen.user.oIdJobPosition=职位实体
beisen.user.oIdJobPosition.description=职位实体
beisen.user.oIdJobLevel=职级实体
beisen.user.oIdJobLevel.description=职级实体
beisen.user.oidJobGrade=职等
beisen.user.oidJobGrade.description=职等
beisen.user.place=工作地点
beisen.user.place.description=工作地点
beisen.user.employeeStatus=人员状态
beisen.user.employeeStatus.description=人员状态
beisen.user.employmentType=人员类别实体对象
beisen.user.employmentType.description=人员类别实体对象
beisen.user.employmentChangeID=任职变更记录实体
beisen.user.employmentChangeID.description=任职变更记录实体
beisen.user.changedStatus=变动后状态
beisen.user.changedStatus.description=变动后状态
beisen.user.pOIdEmpAdmin=直线经理UserID
beisen.user.pOIdEmpAdmin.description=直线经理UserID
beisen.user.pOIdEmpReserve2=虚线经理UserID
beisen.user.pOIdEmpReserve2.description=虚线经理UserID
beisen.user.businessTypeOID=变动业务类型OID
beisen.user.businessTypeOID.description=变动业务类型OID
beisen.user.changeTypeOID=变动类型ID
beisen.user.changeTypeOID.description=变动类型ID
beisen.user.entryStatus=入职状态
beisen.user.entryStatus.description=入职状态
beisen.user.isCurrentRecord=是否当前生效
beisen.user.isCurrentRecord.description=是否当前生效
beisen.user.lUOffer=LU_Offer 业务数据GUID
beisen.user.lUOffer.description=LU_Offer 业务数据GUID
beisen.user.workYearBefore=入职前累计工龄（年）
beisen.user.workYearBefore.description=入职前累计工龄（年）
beisen.user.workYearGroupBefore=入职前集团工龄（年）
beisen.user.workYearGroupBefore.description=入职前集团工龄（年）
beisen.user.workYearCompanyBefore=入职前司龄（年）
beisen.user.workYearCompanyBefore.description=入职前司龄（年）
beisen.user.workYearTotal=累计工龄（年）
beisen.user.workYearTotal.description=累计工龄（年）
beisen.user.workYearGroupTotal=累计集团工龄（年）
beisen.user.workYearGroupTotal.description=累计集团工龄（年）
beisen.user.workYearCompanyTotal=累计司龄
beisen.user.workYearCompanyTotal.description=累计司龄
beisen.user.oIdOrganization=任职机构（如子公司），对应于组织单元业务实体关联的机构OId
beisen.user.oIdOrganization.description=任职机构（如子公司），对应于组织单元业务实体关联的机构OId
beisen.user.whereabouts=离职后去向
beisen.user.whereabouts.description=离职后去向
beisen.user.blackStaffDesc=加黑说明
beisen.user.blackStaffDesc.description=加黑说明
beisen.user.blackListAddReason=加黑原因
beisen.user.blackListAddReason.description=加黑原因
beisen.user.transitionTypeOID=异动类型ID
beisen.user.transitionTypeOID.description=异动类型ID
beisen.user.changeReason=变动原因
beisen.user.changeReason.description=变动原因
beisen.user.probationResult=试用结果
beisen.user.probationResult.description=试用结果
beisen.user.probationActualStopDate=试用期实际结束日期
beisen.user.probationActualStopDate.description=试用期实际结束日期
beisen.user.probationStartDate=试用开始日期
beisen.user.probationStartDate.description=试用开始日期
beisen.user.probationStopDate=预计试用结束日期
beisen.user.probationStopDate.description=预计试用结束日期
beisen.user.isHaveProbation=是否有试用期
beisen.user.isHaveProbation.description=是否有试用期
beisen.user.remarks=备注
beisen.user.remarks.description=备注
beisen.user.addOrNotBlackList=是否加入黑名单
beisen.user.addOrNotBlackList.description=是否加入黑名单
beisen.user.businessModifiedBy1=任职机构
beisen.user.businessModifiedBy1.description=任职机构
beisen.user.businessModifiedTime1=任职机构
beisen.user.businessModifiedTime1.description=任职机构
beisen.user.traineeStartDate=实习开始日期
beisen.user.traineeStartDate.description=实习开始日期
beisen.user.objectId1=任职机构
beisen.user.objectId1.description=任职机构

# ====================== BEISEN部门属性 ======================
beisen.org.costCenterOId=薪酬成本中心ID
beisen.org.costCenterOId.description=薪酬成本中心ID
beisen.org.name=部门机构名称
beisen.org.name.description=部门机构名称
beisen.org.name_en_US=部门机构名称_英文
beisen.org.name_en_US.description=部门机构名称_英文
beisen.org.name_zh_TW=部门机构名称_繁体
beisen.org.name_zh_TW.description=部门机构名称_繁体
beisen.org.shortName=部门机构简称
beisen.org.shortName.description=部门机构简称
beisen.org.code=组织单元编码
beisen.org.code.description=组织单元编码
beisen.org.oId=组织单元OId
beisen.org.oId.description=组织单元OId
beisen.org.level=组织层级实体对象
beisen.org.level.description=组织层级实体对象
beisen.org.status=状态
beisen.org.status.description=状态
beisen.org.establishDate=设立日期
beisen.org.establishDate.description=设立日期
beisen.org.startDate=生效日期
beisen.org.startDate.description=生效日期
beisen.org.stopDate=失效日期
beisen.org.stopDate.description=失效日期
beisen.org.changeDate=变更日期
beisen.org.changeDate.description=变更日期
beisen.org.pOIdOrgAdmin=行政维度上级组织OId
beisen.org.pOIdOrgAdmin.description=行政维度上级组织OId
beisen.org.pOIdOrgReserve2=业务维度上级组织OId
beisen.org.pOIdOrgReserve2.description=业务维度上级组织OId
beisen.org.pOIdOrgReserve3=产品维度组织OId
beisen.org.pOIdOrgReserve3.description=产品维度组织OId
beisen.org.isCurrentRecord=是否当前生效
beisen.org.isCurrentRecord.description=是否当前生效
beisen.org.personInCharge=部门负责人员工UserID
beisen.org.personInCharge.description=部门负责人员工UserID
beisen.org.hRBP=HRBP员工UserID
beisen.org.hRBP.description=HRBP员工UserID
beisen.org.shopOwner=店长员工
beisen.org.administrativeAssistant=行政助理,员工UserID
beisen.org.administrativeAssistant.description=行政助理的员工UserID
beisen.org.personInChargeDeputy=负责人（副职）,多员工UserID集合
beisen.org.personInChargeDeputy.description=负责人的副职员工UserID集合
beisen.org.businessModifiedBy=业务修改人
beisen.org.businessModifiedBy.description=业务修改人
beisen.org.businessModifiedTime=业务修改时间
beisen.org.businessModifiedTime.description=业务修改时间
beisen.org.legalMan=法人代表
beisen.org.legalMan.description=法人代表
beisen.org.address=地址
beisen.org.address.description=组织地址
beisen.org.fax=Fax
beisen.org.fax.description=组织传真号码
beisen.org.postcode=邮编
beisen.org.postcode.description=组织邮编
beisen.org.phone=电话
beisen.org.phone.description=组织电话号码
beisen.org.url=网址
beisen.org.url.description=组织网址
beisen.org.description=简介
beisen.org.description.description=组织简介
beisen.org.number=文号
beisen.org.number.description=组织文号
beisen.org.broadType=组织大类字典键
beisen.org.broadType.description=组织大类字典键
beisen.org.economicType=经济类型字典键
beisen.org.economicType.description=经济类型字典键
beisen.org.industry=所属行业字典键
beisen.org.industry.description=所属行业字典键
beisen.org.place=所在地点字典键
beisen.org.place.description=所在地点字典键
beisen.org.orderAdmin=行政维度顺序号
beisen.org.orderAdmin.description=行政维度顺序号
beisen.org.orderReserve2=业务维度顺序号
beisen.org.orderReserve2.description=业务维度顺序号
beisen.org.orderReserve3=产品维度顺序号
beisen.org.orderReserve3.description=产品维度顺序号
beisen.org.comment=备注
beisen.org.comment.description=组织备注
beisen.org.oIdOrganizationType=组织类型实体对象（实体编码：TenantBase.OrganizationType）业务数据GUID
beisen.org.oIdOrganizationType.description=组织类型实体对象（实体编码：TenantBase.OrganizationType）业务数据GUID
beisen.org.pOIdOrgAdmin_TreePath=行政维度_路径
beisen.org.pOIdOrgAdmin_TreePath.description=行政维度路径
beisen.org.pOIdOrgAdmin_TreeLevel=行政维度_层级
beisen.org.pOIdOrgAdmin_TreeLevel.description=行政维度层级
beisen.org.pOIdOrgReserve2_TreePath=业务维度_路径
beisen.org.pOIdOrgReserve2_TreePath.description=业务维度路径
beisen.org.pOIdOrgReserve2_TreeLevel=业务维度_层级
beisen.org.pOIdOrgReserve2_TreeLevel.description=业务维度层级
beisen.org.firstLevelOrganization=一级组织OId
beisen.org.firstLevelOrganization.description=一级组织OId
beisen.org.secondLevelOrganization=二级组织OId
beisen.org.secondLevelOrganization.description=二级组织OId
beisen.org.thirdLevelOrganization=三级组织OId
beisen.org.thirdLevelOrganization.description=三级组织OId
beisen.org.fourthLevelOrganization=四级组织OId
beisen.org.fourthLevelOrganization.description=四级组织OId
beisen.org.fifthLevelOrganization=五级组织OId
beisen.org.fifthLevelOrganization.description=五级组织OId
beisen.org.sixthLevelOrganization=六级组织OId
beisen.org.sixthLevelOrganization.description=六级组织OId
beisen.org.seventhLevelOrganization=七级组织OId
beisen.org.seventhLevelOrganization.description=七级组织OId
beisen.org.eighthLevelOrganization=八级组织OId
beisen.org.eighthLevelOrganization.description=八级组织OId
beisen.org.ninthLevelOrganization=九级组织OId
beisen.org.ninthLevelOrganization.description=九级组织OId
beisen.org.tenthLevelOrganization=十级组织OId
beisen.org.tenthLevelOrganization.description=十级组织OId
beisen.org.orderCode=排序编码
beisen.org.orderCode.description=组织排序编码
beisen.org.pOIdOrgAdminNameTreePath=组织全称
beisen.org.pOIdOrgAdminNameTreePath.description=组织全称
beisen.org.isVirtualOrg=是否虚拟组织
beisen.org.isVirtualOrg.description=是否虚拟组织
beisen.org.leaderWithSpecificDuty=分管领导员工UserID
beisen.org.leaderWithSpecificDuty.description=分管领导的员工UserID
beisen.org.objectId=业务对象实体主键GUID
beisen.org.objectId.description=业务对象实体主键GUID

# SAML用户属性
saml.user.sub=sub

# SAML用户属性描述
saml.user.sub.description=用户主题

# GOOGLE_SAML用户属性
google_saml.user.sub=sub

# GOOGLE_SAML用户属性描述
google_saml.user.sub.description=用户主题

# ADFS_SAML用户属性
adfs_saml.user.sub=sub

# ADFS_SAML用户属性描述
adfs_saml.user.sub.description=用户主题

# K3CLOUD用户属性
k3cloud.user.FID=FID
k3cloud.user.FName=FName
k3cloud.user.FStaffNumber=FStaffNumber
k3cloud.user.FMobile=FMobile
k3cloud.user.FTel=FTel
k3cloud.user.FEmail=FEmail
k3cloud.user.FDescription=FDescription
k3cloud.user.FAddress=FAddress
k3cloud.user.FUseOrgId=FUseOrgId
k3cloud.user.FCreateOrgId=FCreateOrgId
k3cloud.user.FBranchID=FBranchID
k3cloud.user.FCreateSaler=FCreateSaler
k3cloud.user.FCreateUser=FCreateUser
k3cloud.user.FUserRole=FUserRole
k3cloud.user.FCreateCashier=FCreateCashier
k3cloud.user.FCashierGrp=FCashierGrp
k3cloud.user.FSalerId=FSalerId
k3cloud.user.FCashierId=FCashierId
k3cloud.user.FUserId=FUserId
k3cloud.user.FPostId=FPostId
k3cloud.user.FJoinDate=FJoinDate
k3cloud.user.FUniportalNo=FUniportalNo
k3cloud.user.FSHRMapEntity=FSHRMapEntity
k3cloud.user.FPostEntity=FPostEntity
k3cloud.user.FBankInfo=FBankInfo
k3cloud.user.IsAutoSubmitAndAudit=IsAutoSubmitAndAudit
k3cloud.user.Id=Id
k3cloud.user.Name=Name
k3cloud.user.Number=Number

# K3CLOUD用户属性描述
k3cloud.user.FID.description=FID
k3cloud.user.FName.description=员工姓名
k3cloud.user.FStaffNumber.description=员工编号
k3cloud.user.FMobile.description=移动电话
k3cloud.user.FTel.description=固定电话
k3cloud.user.FEmail.description=电子邮箱
k3cloud.user.FDescription.description=描述
k3cloud.user.FAddress.description=联系地址
k3cloud.user.FUseOrgId.description=使用组织
k3cloud.user.FCreateOrgId.description=创建组织
k3cloud.user.FBranchID.description=所属门店
k3cloud.user.FCreateSaler.description=创建销售员
k3cloud.user.FCreateUser.description=创建Cloud用户
k3cloud.user.FUserRole.description=用户角色
k3cloud.user.FCreateCashier.description=创建POS收银员
k3cloud.user.FCashierGrp.description=收银员权限组
k3cloud.user.FSalerId.description=销售员ID
k3cloud.user.FCashierId.description=收银员ID
k3cloud.user.FUserId.description=用户ID
k3cloud.user.FPostId.description=所属岗位
k3cloud.user.FJoinDate.description=进店日期
k3cloud.user.FUniportalNo.description=统一账号
k3cloud.user.FSHRMapEntity.description=FSHRMapEntity
k3cloud.user.FPostEntity.description=部门岗位信息
k3cloud.user.FBankInfo.description=银行信息
k3cloud.user.IsAutoSubmitAndAudit.description=创建用户自动提交
k3cloud.user.Id.description=用户ID
k3cloud.user.Name.description=用户名称
k3cloud.user.Number.description=用户编码

# K3CLOUD部门属性
k3cloud.org.FDEPTID=FDEPTID
k3cloud.org.FCreateOrgId=FCreateOrgId
k3cloud.org.FNumber=FNumber
k3cloud.org.FUseOrgId=FUseOrgId
k3cloud.org.FName=FName
k3cloud.org.FHelpCode=FHelpCode
k3cloud.org.FParentID=FParentID
k3cloud.org.FFullName=FFullName
k3cloud.org.FEffectDate=FEffectDate
k3cloud.org.FLapseDate=FLapseDate
k3cloud.org.FDeptProperty=FDeptProperty
k3cloud.org.FDescription=FDescription
k3cloud.org.FGroup=FGroup
k3cloud.org.FIsCopyFlush=FIsCopyFlush
k3cloud.org.FFinishQtyDepend=FFinishQtyDepend
k3cloud.org.FIsDetailDpt=FIsDetailDpt
k3cloud.org.FSHRMapEntity=FSHRMapEntity
k3cloud.org.IsAutoSubmitAndAudit=IsAutoSubmitAndAudit
k3cloud.org.Id=Id
k3cloud.org.Name=Name
k3cloud.org.Number=Number
k3cloud.org.ParentID=ParentID
k3cloud.org.Description=Description

# K3CLOUD部门属性描述
k3cloud.org.FDEPTID.description=FDEPTID
k3cloud.org.FCreateOrgId.description=创建组织
k3cloud.org.FNumber.description=编码
k3cloud.org.FUseOrgId.description=使用组织
k3cloud.org.FName.description=名称
k3cloud.org.FHelpCode.description=助记码
k3cloud.org.FParentID.description=上级部门
k3cloud.org.FFullName.description=部门全称
k3cloud.org.FEffectDate.description=生效日期
k3cloud.org.FLapseDate.description=失效日期
k3cloud.org.FDeptProperty.description=部门属性
k3cloud.org.FDescription.description=描述
k3cloud.org.FGroup.description=部门分组
k3cloud.org.FIsCopyFlush.description=副产品倒冲
k3cloud.org.FFinishQtyDepend.description=更新已排
k3cloud.org.FIsDetailDpt.description=是否明细部门
k3cloud.org.FSHRMapEntity.description=FSHRMapEntity
k3cloud.org.IsAutoSubmitAndAudit.description=创建部门是否自动提交
k3cloud.org.Id.description=部门ID
k3cloud.org.Name.description=部门名称
k3cloud.org.Number.description=部门编码
k3cloud.org.ParentID.description=父部门
k3cloud.org.Description.description=描述

# YYU8C用户属性
yyu8c.user.accopendate=开户日期
yyu8c.user.account=账号
yyu8c.user.accountcode=账户编码
yyu8c.user.accountname=账户名称
yyu8c.user.bankarea=开户地区
yyu8c.user.city=城市
yyu8c.user.combineaccnum=联行号
yyu8c.user.contactpsn=联系人
yyu8c.user.custcode=客户编码
yyu8c.user.groupid=集团号
yyu8c.user.memo=备注
yyu8c.user.netqueryflag=网银开通状态
yyu8c.user.orgnumber=机构号
yyu8c.user.pk_bankdoc=开户银行
yyu8c.user.pk_banktype=银行类别
yyu8c.user.pk_currtype=币种
yyu8c.user.pk_netbankinftp=网银接口类别
yyu8c.user.province=省份
yyu8c.user.remcode=助记码
yyu8c.user.signflag=是否签约
yyu8c.user.tel=联系电话
yyu8c.user.unitname=单位名称
yyu8c.user.isreimburse=默认报销卡
yyu8c.user.pk_psnaccbank=个人银行账户主键
yyu8c.user.currentcorp=公司主属性
yyu8c.user.addr=家庭地址
yyu8c.user.birthdate=出生日期
yyu8c.user.bp=呼机
yyu8c.user.email=电子邮件
yyu8c.user.homephone=家庭电话
yyu8c.user.id=身份证号
yyu8c.user.isassociated=是否关联其它公司人员(y/n)（3.6及之后支持）
yyu8c.user.joinworkdate=参加工作日期
yyu8c.user.mobile=手机
yyu8c.user.officephone=办公电话
yyu8c.user.pk_corp=归属公司
yyu8c.user.pk_psnbasdoc=人员基本档案主键
yyu8c.user.postalcode=邮政编码
yyu8c.user.psnname=姓名
yyu8c.user.sex=性别
yyu8c.user.ssnum=社会保障号
yyu8c.user.usedname=曾用名
yyu8c.user.vdef1=自定义项1
yyu8c.user.vdef10=自定义项10
yyu8c.user.vdef11=自定义项11
yyu8c.user.vdef12=自定义项12
yyu8c.user.vdef13=自定义项13
yyu8c.user.vdef14=自定义项14
yyu8c.user.vdef15=自定义项15
yyu8c.user.vdef16=自定义项16
yyu8c.user.vdef17=自定义项17
yyu8c.user.vdef18=自定义项18
yyu8c.user.vdef19=自定义项19
yyu8c.user.vdef2=自定义项2
yyu8c.user.vdef20=自定义项20
yyu8c.user.vdef3=自定义项3
yyu8c.user.vdef4=自定义项4
yyu8c.user.vdef5=自定义项5
yyu8c.user.vdef6=自定义项6
yyu8c.user.vdef7=自定义项7
yyu8c.user.vdef8=自定义项8
yyu8c.user.vdef9=自定义项9
yyu8c.user.amcode=助记码
yyu8c.user.clerkcode=业务员编号
yyu8c.user.clerkflag=业务员标志（y/n）
yyu8c.user.def1=自定义项1
yyu8c.user.def10=自定义项10
yyu8c.user.def11=自定义项11
yyu8c.user.def12=自定义项12
yyu8c.user.def13=自定义项13
yyu8c.user.def14=自定义项14
yyu8c.user.def15=自定义项15
yyu8c.user.def16=自定义项16
yyu8c.user.def17=自定义项17
yyu8c.user.def18=自定义项18
yyu8c.user.def19=自定义项19
yyu8c.user.def2=自定义项2
yyu8c.user.def20=自定义项20
yyu8c.user.def3=自定义项3
yyu8c.user.def4=自定义项4
yyu8c.user.def5=自定义项5
yyu8c.user.def6=自定义项6
yyu8c.user.def7=自定义项7
yyu8c.user.def8=自定义项8
yyu8c.user.def9=自定义项9
yyu8c.user.indutydate=到职日期
yyu8c.user.outdutydate=离职日期
yyu8c.user.pk_deptdoc=部门
yyu8c.user.pk_psncl=人员类别,01在职员工
yyu8c.user.psncode=编码

# YYU8C用户属性描述
yyu8c.user.accopendate.description=开户日期
yyu8c.user.account.description=账号
yyu8c.user.accountcode.description=账户编码
yyu8c.user.accountname.description=账户名称
yyu8c.user.bankarea.description=开户地区
yyu8c.user.city.description=城市
yyu8c.user.combineaccnum.description=联行号
yyu8c.user.contactpsn.description=联系人
yyu8c.user.custcode.description=客户编码
yyu8c.user.groupid.description=集团号
yyu8c.user.memo.description=备注
yyu8c.user.netqueryflag.description=网银开通状态
yyu8c.user.orgnumber.description=机构号
yyu8c.user.pk_bankdoc.description=开户银行
yyu8c.user.pk_banktype.description=银行类别
yyu8c.user.pk_currtype.description=币种
yyu8c.user.pk_netbankinftp.description=网银接口类别
yyu8c.user.province.description=省份
yyu8c.user.remcode.description=助记码
yyu8c.user.signflag.description=是否签约
yyu8c.user.tel.description=联系电话
yyu8c.user.unitname.description=单位名称
yyu8c.user.isreimburse.description=默认报销卡
yyu8c.user.pk_psnaccbank.description=个人银行账户主键
yyu8c.user.currentcorp.description=公司主属性
yyu8c.user.addr.description=家庭地址
yyu8c.user.birthdate.description=出生日期
yyu8c.user.bp.description=呼机
yyu8c.user.email.description=电子邮件
yyu8c.user.homephone.description=家庭电话
yyu8c.user.id.description=身份证号
yyu8c.user.isassociated.description=是否关联其它公司人员(y/n)（3.6及之后支持）
yyu8c.user.joinworkdate.description=参加工作日期
yyu8c.user.mobile.description=手机
yyu8c.user.officephone.description=办公电话
yyu8c.user.pk_corp.description=归属公司
yyu8c.user.pk_psnbasdoc.description=人员基本档案主键
yyu8c.user.postalcode.description=邮政编码
yyu8c.user.psnname.description=姓名
yyu8c.user.sex.description=性别
yyu8c.user.ssnum.description=社会保障号
yyu8c.user.usedname.description=曾用名
yyu8c.user.vdef1.description=自定义项1
yyu8c.user.vdef10.description=自定义项10
yyu8c.user.vdef11.description=自定义项11
yyu8c.user.vdef12.description=自定义项12
yyu8c.user.vdef13.description=自定义项13
yyu8c.user.vdef14.description=自定义项14
yyu8c.user.vdef15.description=自定义项15
yyu8c.user.vdef16.description=自定义项16
yyu8c.user.vdef17.description=自定义项17
yyu8c.user.vdef18.description=自定义项18
yyu8c.user.vdef19.description=自定义项19
yyu8c.user.vdef2.description=自定义项2
yyu8c.user.vdef20.description=自定义项20
yyu8c.user.vdef3.description=自定义项3
yyu8c.user.vdef4.description=自定义项4
yyu8c.user.vdef5.description=自定义项5
yyu8c.user.vdef6.description=自定义项6
yyu8c.user.vdef7.description=自定义项7
yyu8c.user.vdef8.description=自定义项8
yyu8c.user.vdef9.description=自定义项9
yyu8c.user.amcode.description=助记码
yyu8c.user.clerkcode.description=业务员编号
yyu8c.user.clerkflag.description=业务员标志（y/n）
yyu8c.user.def1.description=自定义项1
yyu8c.user.def10.description=自定义项10
yyu8c.user.def11.description=自定义项11
yyu8c.user.def12.description=自定义项12
yyu8c.user.def13.description=自定义项13
yyu8c.user.def14.description=自定义项14
yyu8c.user.def15.description=自定义项15
yyu8c.user.def16.description=自定义项16
yyu8c.user.def17.description=自定义项17
yyu8c.user.def18.description=自定义项18
yyu8c.user.def19.description=自定义项19
yyu8c.user.def2.description=自定义项2
yyu8c.user.def20.description=自定义项20
yyu8c.user.def3.description=自定义项3
yyu8c.user.def4.description=自定义项4
yyu8c.user.def5.description=自定义项5
yyu8c.user.def6.description=自定义项6
yyu8c.user.def7.description=自定义项7
yyu8c.user.def8.description=自定义项8
yyu8c.user.def9.description=自定义项9
yyu8c.user.indutydate.description=到职日期
yyu8c.user.outdutydate.description=离职日期
yyu8c.user.pk_deptdoc.description=部门
yyu8c.user.pk_psncl.description=人员类别,01在职员工
yyu8c.user.psncode.description=编码

# YYU8C部门属性
yyu8c.org.addr=部门地址
yyu8c.org.createDate=创建时间
yyu8c.org.deptattr=部门属性（其他部门1，采购2，销售3，采购销售4）
yyu8c.org.pk_corp=公司主键
yyu8c.org.unitname=公司名称
yyu8c.org.unitcode=公司编码
yyu8c.org.pk_deptdoc=部门档案主键
yyu8c.org.pk_fathedept=上级编码
yyu8c.org.deptcode=部门编码
yyu8c.org.deptname=部门名称
yyu8c.org.deptshortname=简称
yyu8c.org.depttype=部门类型
yyu8c.org.isuseretail=是否用于零售
yyu8c.org.def1=自定义1
yyu8c.org.def10=自定义10
yyu8c.org.def11=自定义11
yyu8c.org.def12=自定义12
yyu8c.org.def13=自定义13
yyu8c.org.def14=自定义14
yyu8c.org.def15=自定义15
yyu8c.org.def16=自定义16
yyu8c.org.def17=自定义17
yyu8c.org.def18=自定义18
yyu8c.org.def19=自定义19
yyu8c.org.def2=自定义2
yyu8c.org.def20=自定义20
yyu8c.org.def3=自定义3
yyu8c.org.def4=自定义4
yyu8c.org.def5=自定义5
yyu8c.org.def6=自定义6
yyu8c.org.def7=自定义7
yyu8c.org.def8=自定义8
yyu8c.org.def9=自定义9
yyu8c.org.deptduty=部门职责
yyu8c.org.deptlevel=部门级别
yyu8c.org.memo=备注
yyu8c.org.phone=部门电话
yyu8c.org.pk_calbody=test
yyu8c.org.pk_psndoc=负责人编码
yyu8c.org.pk_psndoc2=负责人2
yyu8c.org.pk_psndoc3=负责人3
yyu8c.org.remcode=助记码

# YYU8C部门属性描述
yyu8c.org.addr.description=部门地址
yyu8c.org.createDate.description=创建时间
yyu8c.org.deptattr.description=部门属性（其他部门1，采购2，销售3，采购销售4）
yyu8c.org.pk_corp.description=公司主键
yyu8c.org.unitname.description=公司名称
yyu8c.org.unitcode.description=公司编码
yyu8c.org.pk_deptdoc.description=部门档案主键
yyu8c.org.pk_fathedept.description=上级编码
yyu8c.org.deptcode.description=部门编码
yyu8c.org.deptname.description=部门名称
yyu8c.org.deptshortname.description=简称
yyu8c.org.depttype.description=部门类型
yyu8c.org.isuseretail.description=是否用于零售
yyu8c.org.def1.description=自定义1
yyu8c.org.def10.description=自定义10
yyu8c.org.def11.description=自定义11
yyu8c.org.def12.description=自定义12
yyu8c.org.def13.description=自定义13
yyu8c.org.def14.description=自定义14
yyu8c.org.def15.description=自定义15
yyu8c.org.def16.description=自定义16
yyu8c.org.def17.description=自定义17
yyu8c.org.def18.description=自定义18
yyu8c.org.def19.description=自定义19
yyu8c.org.def2.description=自定义2
yyu8c.org.def20.description=自定义20
yyu8c.org.def3.description=自定义3
yyu8c.org.def4.description=自定义4
yyu8c.org.def5.description=自定义5
yyu8c.org.def6.description=自定义6
yyu8c.org.def7.description=自定义7
yyu8c.org.def8.description=自定义8
yyu8c.org.def9.description=自定义9
yyu8c.org.deptduty.description=部门职责
yyu8c.org.deptlevel.description=部门级别
yyu8c.org.memo.description=备注
yyu8c.org.phone.description=部门电话
yyu8c.org.pk_calbody.description=test
yyu8c.org.pk_psndoc.description=负责人编码
yyu8c.org.pk_psndoc2.description=负责人2
yyu8c.org.pk_psndoc3.description=负责人3
yyu8c.org.remcode.description=助记码

# FBT用户属性
fbt.user.third_id=三方系统人员ID，不超过50字符确保唯一性
fbt.user.name=姓名
fbt.user.phone=手机号码
fbt.user.role=默认角色枚举值：2：普通管理员 3：普通人员(若不填则默认为管理后台的普通人员权限角色
fbt.user.third_roles=三方系统角色列表
fbt.user.third_dept_id=三方系统部门ID
fbt.user.code=人员工号
fbt.user.email=人员邮箱,如果填写了邮箱，人员会收到分贝通账户开通邮件
fbt.user.gender=性别：-1为未指定、1为男、2为女
fbt.user.birthday=出生日期
fbt.user.country_code=国籍编码
fbt.user.cert_name=证件姓名
fbt.user.cert_last_name=证件英文姓
fbt.user.cert_first_name=证件英文名
fbt.user.type=证件类型枚举值1：身份证2：护照3：回乡证4：台胞证5：往来港澳通行证6： 大陆居民往来台湾通行证7：港澳居民居住证8：台湾居民居住证9：外国人永久居留证999：其他
fbt.user.number=证件号码
fbt.user.validity_type=有效期类型：0：有效期，1：长期有效
fbt.user.expire_date=有效期，格式为YYYY-MM-DD
fbt.user.third_rank_id=三方系统职级ID，职级ID与名称同时传入信息不匹配时报错
fbt.user.rank_name=职级名称
fbt.user.template_id=分贝通消费权限模版ID,为空时人员默认绑定分贝通
fbt.user.template_name=分贝通消费权限模版名称，id和name都传时使用id进行同步,为空时人员默认绑定分贝通
fbt.user.third_entity_id=三方系统法人实体ID
fbt.user.third_senior_id=三方系统直属上级ID
fbt.user.city_id=城市ID
fbt.user.state=人员状态人员新增该字段传入无效，默认为启用状态，状态枚举值：1：启用2：禁用，禁用后企业中商务消费功能不可使用
fbt.user.group_positions=职位信息

# FBT用户属性描述
fbt.user.third_id.description=三方系统人员ID，不超过50字符确保唯一性
fbt.user.name.description=姓名
fbt.user.phone.description=手机号码
fbt.user.role.description=默认角色枚举值：2：普通管理员 3：普通人员(若不填则默认为管理后台的普通人员权限角色
fbt.user.third_roles.description=三方系统角色列表
fbt.user.third_dept_id.description=三方系统部门ID
fbt.user.code.description=人员工号
fbt.user.email.description=人员邮箱,如果填写了邮箱，人员会收到分贝通账户开通邮件
fbt.user.gender.description=性别：-1为未指定、1为男、2为女
fbt.user.birthday.description=出生日期
fbt.user.country_code.description=国籍编码
fbt.user.cert_name.description=证件姓名
fbt.user.cert_last_name.description=证件英文姓
fbt.user.cert_first_name.description=证件英文名
fbt.user.type.description=证件类型枚举值1：身份证2：护照3：回乡证4：台胞证5：往来港澳通行证6： 大陆居民往来台湾通行证7：港澳居民居住证8：台湾居民居住证9：外国人永久居留证999：其他
fbt.user.number.description=证件号码
fbt.user.validity_type.description=有效期类型：0：有效期，1：长期有效
fbt.user.expire_date.description=有效期，格式为YYYY-MM-DD
fbt.user.third_rank_id.description=三方系统职级ID，职级ID与名称同时传入信息不匹配时报错
fbt.user.rank_name.description=职级名称
fbt.user.template_id.description=分贝通消费权限模版ID,为空时人员默认绑定分贝通
fbt.user.template_name.description=分贝通消费权限模版名称，id和name都传时使用id进行同步,为空时人员默认绑定分贝通
fbt.user.third_entity_id.description=三方系统法人实体ID
fbt.user.third_senior_id.description=三方系统直属上级ID
fbt.user.city_id.description=城市ID
fbt.user.state.description=人员状态人员新增该字段传入无效，默认为启用状态，状态枚举值：1：启用2：禁用，禁用后企业中商务消费功能不可使用
fbt.user.group_positions.description=职位信息

# FBT部门属性
fbt.org.third_id=部门ID，唯一标识
fbt.org.name=部门名称
fbt.org.third_parent_id=三方系统父级部门ID
fbt.org.code=部门代码
fbt.org.manager=部门主管
fbt.org.state=是否启用

# FBT部门属性描述
fbt.org.third_id.description=部门ID，唯一标识
fbt.org.name.description=部门名称
fbt.org.third_parent_id.description=三方系统父级部门ID
fbt.org.code.description=部门代码
fbt.org.manager.description=部门主管
fbt.org.state.description=是否启用

# HLY用户属性
hly.user.custDeptNumber=部门编码
hly.user.fullName=姓名
hly.user.employeeID=员工工号，租户内不能重复
hly.user.companyOID=公司OID，集团登陆账户必填,公司登陆账户可不填,必填时，companyOID和companyCode二选一即可，都填时，以companyOID为准
hly.user.companyCode=公司编码，集团登陆账户必填,公司登陆账户可不填,必填时，companyOID和companyCode二选一即可，都填时，以companyOID为准
hly.user.email=邮箱，长度:1到255个字符,内部员工和外部员工租户内不能重复
hly.user.entryTime=入职时间，格式:yyyy-MM-dd
hly.user.mobile=手机号，内部员工和外部员工租户内不能重复
hly.user.countryCode=国家编码，不填默认为中国，不同国家手机号码长度不同
hly.user.password=密码,密码为空，则默认密码为手机号码，如果手机号码也为空，则密码为工号(不足6位，工号后补0)
hly.user.title=职位,不传默认创建职位为"员工"的岗位
hly.user.activated=是否激活,true表示创建后自动激活，false表示创建后需要员工自己手动激活
hly.user.birthday=生日,格式:yyyy-MM-dd
hly.user.genderCode=性别编码,0--男;1--女;2--未知
hly.user.employeeTypeCode=人员类型编码，如果需要人员组按照人员类型匹配，则需要传人员类型编码
hly.user.dutyCode=职务编码，如果需要人员组按照职务匹配，则需要传职务编码
hly.user.rankCode=级别编码，如果需要用级别值列表做差标管控和人员组按照级别匹配，则需要传级别编码
hly.user.directManagerOID=主岗直属领导OID
hly.user.directManagerEmpoyeeId=主岗直属领导工号
hly.user.language=语言编码,zh_CN-中文简体,zh_TW-中文繁体,en-英语,ms-马来语,ja-日语,vi_VN-越南语,es_ES-西班牙,pt_PT-葡萄牙,it_IT-意大利语,ko_KR-韩语
hly.user.coverModeForUserJobs=多岗是否为覆盖模式，默认为false,设置为true时，userJobsDtos里面的isDeleted参数不能为true
hly.user.approvalLevel=审批层级，不超过8位的正整数
hly.user.mutiJobDirectManagerMode=是否按多岗更新主岗上级岗位编码，默认为false
hly.user.contactBankAccountDTOs=银行账户信息
hly.user.contactCardDTOs=证件集合
hly.user.customFormValues=用户属性扩展字段
hly.user.subAccountName=用户携程供应商实体，子账户，值列表项中无论是否存在，都保存或者更新
hly.user.subAccountCode=用户携程供应商实体，子账户编码，需要校验是否存在于值列表项中，不存在报错，存在保存或者更新
hly.user.confirmUserOID=用户携程供应商实体，授权人OID
hly.user.confirmUserEmployeeID=用户携程供应商实体，授权人工号
hly.user.confirmCCUserOID=用户携程供应商实体，抄送授权人
hly.user.confirmCCUserEmployeeID=用户携程供应商实体，抄送授权人工号
hly.user.confirm2UserOID=用户携程供应商实体，二次授权人
hly.user.confirm2UserEmployeeID=用户携程供应商实体，二次授权人工号
hly.user.confirm2CCUserOID=用户携程供应商实体，抄送二次授权人
hly.user.confirm2CCUserEmployeeID=用户携程供应商实体，抄送二次授权人工号
hly.user.rescheduledAuthUserOID=用户携程供应商实体，改签授权人OID
hly.user.rescheduledAuthUserEmployeeID=用户携程供应商实体，改签授权人工号
hly.user.confirmPassword=用户携程供应商实体，携程授权码
hly.user.helpOthersBookCode=用户携程供应商实体，代订类型,B:可以;C:不可以
hly.user.bookTheScopeCode=用户携程供应商实体，代订范围,C:整个公司;F:员工所在携程主账户;S:员工所在携程子账户;O:指定具体代订名单
hly.user.agencyEmployeeIds=用户携程供应商实体，代订员工集合,bookTheScopeCode为O时生效
hly.user.positionShield=用户携程供应商实体，标签仓位屏蔽,Y:屏蔽;N:不屏蔽;默认不屏蔽
hly.user.changeTicketAuthType=用户携程供应商实体，改签授权类型,Needless:不需要;CompanyAccountOnly:仅公司账户;PersonalPayment:仅个人支付;Both:混付（公司账户+个人支付）;FlightAuthNAlterAuto:改签自动授权
hly.user.changeTicketNeedAuth=用户携程供应商实体，国内改签免授权金额控制,T:需要,F:不需要
hly.user.changeTicketAuthAmount=用户携程供应商实体，国内改签免授权金额,国内改签免授权金额控制为T时必填
hly.user.kgSecondAuthEmployeeID=空港嘉华实体，二次授权人工号
hly.user.kgenabled=空港嘉华实体，是否启用,默认为true,启用
hly.user.supplierDTOs=通用二次授权人集合
hly.user.userJobsDtos=用户岗位列表

# HLY用户属性描述
hly.user.custDeptNumber.description=部门编码
hly.user.fullName.description=姓名
hly.user.employeeID.description=员工工号，租户内不能重复
hly.user.companyOID.description=公司OID，集团登陆账户必填,公司登陆账户可不填,必填时，companyOID和companyCode二选一即可，都填时，以companyOID为准
hly.user.companyCode.description=公司编码，集团登陆账户必填,公司登陆账户可不填,必填时，companyOID和companyCode二选一即可，都填时，以companyOID为准
hly.user.email.description=邮箱，长度:1到255个字符,内部员工和外部员工租户内不能重复
hly.user.entryTime.description=入职时间，格式:yyyy-MM-dd
hly.user.mobile.description=手机号，内部员工和外部员工租户内不能重复
hly.user.countryCode.description=国家编码，不填默认为中国，不同国家手机号码长度不同
hly.user.password.description=密码,密码为空，则默认密码为手机号码，如果手机号码也为空，则密码为工号(不足6位，工号后补0)
hly.user.title.description=职位,不传默认创建职位为"员工"的岗位
hly.user.activated.description=是否激活,true表示创建后自动激活，false表示创建后需要员工自己手动激活
hly.user.birthday.description=生日,格式:yyyy-MM-dd
hly.user.genderCode.description=性别编码,0--男;1--女;2--未知
hly.user.employeeTypeCode.description=人员类型编码，如果需要人员组按照人员类型匹配，则需要传人员类型编码
hly.user.dutyCode.description=职务编码，如果需要人员组按照职务匹配，则需要传职务编码
hly.user.rankCode.description=级别编码，如果需要用级别值列表做差标管控和人员组按照级别匹配，则需要传级别编码
hly.user.directManagerOID.description=主岗直属领导OID
hly.user.directManagerEmpoyeeId.description=主岗直属领导工号
hly.user.language.description=语言编码,zh_CN-中文简体,zh_TW-中文繁体,en-英语,ms-马来语,ja-日语,vi_VN-越南语,es_ES-西班牙,pt_PT-葡萄牙,it_IT-意大利语,ko_KR-韩语
hly.user.coverModeForUserJobs.description=多岗是否为覆盖模式，默认为false,设置为true时，userJobsDtos里面的isDeleted参数不能为true
hly.user.approvalLevel.description=审批层级，不超过8位的正整数
hly.user.mutiJobDirectManagerMode.description=是否按多岗更新主岗上级岗位编码，默认为false
hly.user.contactBankAccountDTOs.description=银行账户信息
hly.user.contactCardDTOs.description=证件集合
hly.user.customFormValues.description=用户属性扩展字段
hly.user.subAccountName.description=用户携程供应商实体，子账户，值列表项中无论是否存在，都保存或者更新
hly.user.subAccountCode.description=用户携程供应商实体，子账户编码，需要校验是否存在于值列表项中，不存在报错，存在保存或者更新
hly.user.confirmUserOID.description=用户携程供应商实体，授权人OID
hly.user.confirmUserEmployeeID.description=用户携程供应商实体，授权人工号
hly.user.confirmCCUserOID.description=用户携程供应商实体，抄送授权人
hly.user.confirmCCUserEmployeeID.description=用户携程供应商实体，抄送授权人工号
hly.user.confirm2UserOID.description=用户携程供应商实体，二次授权人
hly.user.confirm2UserEmployeeID.description=用户携程供应商实体，二次授权人工号
hly.user.confirm2CCUserOID.description=用户携程供应商实体，抄送二次授权人
hly.user.confirm2CCUserEmployeeID.description=用户携程供应商实体，抄送二次授权人工号
hly.user.rescheduledAuthUserOID.description=用户携程供应商实体，改签授权人OID
hly.user.rescheduledAuthUserEmployeeID.description=用户携程供应商实体，改签授权人工号
hly.user.confirmPassword.description=用户携程供应商实体，携程授权码
hly.user.helpOthersBookCode.description=用户携程供应商实体，代订类型,B:可以;C:不可以
hly.user.bookTheScopeCode.description=用户携程供应商实体，代订范围,C:整个公司;F:员工所在携程主账户;S:员工所在携程子账户;O:指定具体代订名单
hly.user.agencyEmployeeIds.description=用户携程供应商实体，代订员工集合,bookTheScopeCode为O时生效
hly.user.positionShield.description=用户携程供应商实体，标签仓位屏蔽,Y:屏蔽;N:不屏蔽;默认不屏蔽
hly.user.changeTicketAuthType.description=用户携程供应商实体，改签授权类型,Needless:不需要;CompanyAccountOnly:仅公司账户;PersonalPayment:仅个人支付;Both:混付（公司账户+个人支付）;FlightAuthNAlterAuto:改签自动授权
hly.user.changeTicketNeedAuth.description=用户携程供应商实体，国内改签免授权金额控制,T:需要,F:不需要
hly.user.changeTicketAuthAmount.description=用户携程供应商实体，国内改签免授权金额,国内改签免授权金额控制为T时必填
hly.user.kgSecondAuthEmployeeID.description=空港嘉华实体，二次授权人工号
hly.user.kgenabled.description=空港嘉华实体，是否启用,默认为true,启用
hly.user.supplierDTOs.description=通用二次授权人集合
hly.user.userJobsDtos.description=用户岗位列表

# HLY部门属性
hly.org.departmentName=部门名称
hly.org.custDeptNumber=部门编码
hly.org.managerEmployeeId=部门经理工号
hly.org.parentCustDeptNumber=上级部门编码
hly.org.companyCode=公司编码,当部门关联公司开关开启时，必填，开启关联后部门不可更改关联公司;未开启时，忽略此字段
hly.org.sequenceNumber=序号，取值1-999，可重复
hly.org.approvalLevel=审批层级，不超过8位的正整数
hly.org.departmentType=部门属性可以为空、"ENTITY"、"VIRTUAL" ,为空默认为"ENTITY"
hly.org.departmentPositionList=部门角色集合
hly.org.customFormValues=扩展集合
hly.org.departmentOID=集成使用，创建的部门OID
hly.org.companyOID=集成使用，公司OID
hly.org.managerOID=集成使用，主管经理OID
hly.org.managerName=集成使用，主管经理姓名
hly.org.status=集成使用，状态，101--启用;102--禁用;103--删除
hly.org.path=集成使用，部门路径
hly.org.departmentParentOID=集成使用，父部门OID
hly.org.hasChildrenDepartments=集成使用，是否有子部门
hly.org.hasUsers=集成使用，是否有用户
hly.org.positionName=集成使用，部门角色名称
hly.org.userName=集成使用，员工名称
hly.org.employeeId1=集成使用，员工工号
hly.org.positionCode1=集成使用，部门角色编码

# HLY部门属性描述
hly.org.departmentName.description=部门名称
hly.org.custDeptNumber.description=部门编码
hly.org.managerEmployeeId.description=部门经理工号
hly.org.parentCustDeptNumber.description=上级部门编码
hly.org.companyCode.description=公司编码,当部门关联公司开关开启时，必填，开启关联后部门不可更改关联公司;未开启时，忽略此字段
hly.org.sequenceNumber.description=序号，取值1-999，可重复
hly.org.approvalLevel.description=审批层级，不超过8位的正整数
hly.org.departmentType.description=部门属性可以为空、"ENTITY"、"VIRTUAL" ,为空默认为"ENTITY"
hly.org.departmentPositionList.description=部门角色集合
hly.org.customFormValues.description=扩展集合
hly.org.departmentOID.description=集成使用，创建的部门OID
hly.org.companyOID.description=集成使用，公司OID
hly.org.managerOID.description=集成使用，主管经理OID
hly.org.managerName.description=集成使用，主管经理姓名
hly.org.status.description=集成使用，状态，101--启用;102--禁用;103--删除
hly.org.path.description=集成使用，部门路径
hly.org.departmentParentOID.description=集成使用，父部门OID
hly.org.hasChildrenDepartments.description=集成使用，是否有子部门
hly.org.hasUsers.description=集成使用，是否有用户
hly.org.positionName.description=集成使用，部门角色名称
hly.org.userName.description=集成使用，员工名称
hly.org.employeeId1.description=集成使用，员工工号
hly.org.positionCode1.description=集成使用，部门角色编码

# QQT用户属性
qqt.user.id=唯一标识
qqt.user.subAccount=登录账号
qqt.user.realname=姓名
qqt.user.email=邮箱
qqt.user.workNo=工号
qqt.user.phone=手机号
qqt.user.telephone=电话号
qqt.user.accountValidityDate=账号过期时间
qqt.user.status=状态(1：正常 2：冻结 ）
qqt.user.orgCode=组织编码，多个使用','隔开
qqt.user.selectedroles=角色id，必填

# QQT用户属性描述
qqt.user.id.description=唯一标识
qqt.user.subAccount.description=登录账号
qqt.user.realname.description=姓名
qqt.user.email.description=邮箱
qqt.user.workNo.description=工号
qqt.user.phone.description=手机号
qqt.user.telephone.description=电话号
qqt.user.accountValidityDate.description=账号过期时间
qqt.user.status.description=状态(1：正常 2：冻结 ）
qqt.user.orgCode.description=组织编码，多个使用','隔开
qqt.user.selectedroles.description=角色id，必填

# QQT部门属性
qqt.org.orgName=组织名称
qqt.org.orgAbbreviation=组织简称
qqt.org.orgCode=组织编码
qqt.org.orgCategoryCode=组织类型
qqt.org.orgNature=组织性质 0：业务组织：1：行政组织
qqt.org.status=状态 1：正常；2：冻结
qqt.org.dataSource=数据来源 1：外部系统；2：手工创建
qqt.org.id=组织id
qqt.org.orgCategoryDesc=组织类型描述
qqt.org.superExecutiveId=父级行政组织id
qqt.org.executiveLevel=行政组织层级 1：一级；2:二级；3：三级
qqt.org.executiveRegion=行政区域
qqt.org.executiveOrder=
qqt.org.superBusinessId=父级业务组织id
qqt.org.businessLevel=业务组织层级 1：一级；2:二级；3：三级
qqt.org.businessPerson=业务组织负责人
qqt.org.businessRegion=业务组织区域
qqt.org.status_dictText=
qqt.org.orgDesc=组织描述
qqt.org.relateBusinessId=
qqt.org.creditCode=信用代码
qqt.org.taxNumber=税号
qqt.org.injectionEls=
qqt.org.chargeCurrency=货币
qqt.org.createById=创建人id
qqt.org.createBy=创建人
qqt.org.createTime=创建时间 格式 2023-03-02
qqt.org.updateById=修改人id
qqt.org.updateBy=修改人
qqt.org.updateTime=修改时间 格式 2023-03-02

# QQT部门属性描述
qqt.org.orgName.description=组织名称
qqt.org.orgAbbreviation.description=组织简称
qqt.org.orgCode.description=组织编码
qqt.org.orgCategoryCode.description=组织类型
qqt.org.orgNature.description=组织性质 0：业务组织：1：行政组织
qqt.org.status.description=状态 1：正常；2：冻结
qqt.org.dataSource.description=数据来源 1：外部系统；2：手工创建
qqt.org.id.description=组织id
qqt.org.orgCategoryDesc.description=组织类型描述
qqt.org.superExecutiveId.description=父级行政组织id
qqt.org.executiveLevel.description=行政组织层级 1：一级；2:二级；3：三级
qqt.org.executiveRegion.description=行政区域
qqt.org.executiveOrder.description=
qqt.org.superBusinessId.description=父级业务组织id
qqt.org.businessLevel.description=业务组织层级 1：一级；2:二级；3：三级
qqt.org.businessPerson.description=业务组织负责人
qqt.org.businessRegion.description=业务组织区域
qqt.org.status_dictText.description=
qqt.org.orgDesc.description=组织描述
qqt.org.relateBusinessId.description=
qqt.org.creditCode.description=信用代码
qqt.org.taxNumber.description=税号
qqt.org.injectionEls.description=
qqt.org.chargeCurrency.description=货币
qqt.org.createById.description=创建人id
qqt.org.createBy.description=创建人
qqt.org.createTime.description=创建时间 格式 2023-03-02
qqt.org.updateById.description=修改人id
qqt.org.updateBy.description=修改人
qqt.org.updateTime.description=修改时间 格式 2023-03-02

# MEICLOUD用户属性
meicloud.user.empId=用户id
meicloud.user.uid=用户名
meicloud.user.sourceId=数据源id
meicloud.user.name=姓名
meicloud.user.nameEn=姓名-英文
meicloud.user.headPhoto=头像（图标访问全路径）
meicloud.user.gender=性别
meicloud.user.employeeNumber=员工编号
meicloud.user.extras=扩展属性JSON

# MEICLOUD用户属性描述
meicloud.user.empId.description=用户id
meicloud.user.uid.description=用户名
meicloud.user.sourceId.description=数据源id
meicloud.user.name.description=姓名
meicloud.user.nameEn.description=姓名-英文
meicloud.user.headPhoto.description=头像（图标访问全路径）
meicloud.user.gender.description=性别
meicloud.user.employeeNumber.description=员工编号
meicloud.user.extras.description=扩展属性JSON

# WEAVER 用户属性
weaver.user.id=Id
weaver.user.workcode=工号，唯一标识
weaver.user.lastname=人员名称
weaver.user.department=部门编码
weaver.user.subcompany=分部编码
weaver.user.joblevel=职级
weaver.user.jobcall=职称
weaver.user.jobactivityid=职务
weaver.user.jobgroupid=职务类型
weaver.user.jobtitle=岗位id
weaver.user.status=状态: 0 试用 1 正式 2 临时 3 试用延期 4 解聘 5 离职 6 退休 7 无效
weaver.user.locationid=办公地点，关联数据表：HRMLOCATIONS
weaver.user.workroom=办公室
weaver.user.person_custom_data=个人自定义数据
weaver.user.base_custom_data=基本信息自定义数据
weaver.user.work_custom_data=自定义数据
weaver.user.language=系统语言；枚举：简体中文 、 繁體中文 、 English
weaver.user.startdate=合同开始日期
weaver.user.enddate=合同结束日期
weaver.user.subcompanyid1=分部id
weaver.user.subcompanyname=分部名称
weaver.user.departmentid=部门id
weaver.user.departmentname=部门名称
weaver.user.managerid=上级人员id
weaver.user.assistantid=助理人员id
weaver.user.accounttype=主次账号标志：1 次账号 , 其他 主账号
weaver.user.accountname=工资账号户名
weaver.user.bankid1=工资银行
weaver.user.companystartdate=入职日期; 格式：yyyy-MM-dd
weaver.user.workstartdate=参加工作日期; 格式：yyyy-MM-dd
weaver.user.belongto=主账号id （当accounttype 为 1 有效）
weaver.user.isadaccount=是否ad账号; 范围： 1是ad账号；其他非ad账号
weaver.user.islabouunion=工会会员
weaver.user.tempresidentnumber=暂住号码
weaver.user.jobactivitydesc=职责描述
weaver.user.loginid=登录名
weaver.user.password=密码
weaver.user.certificatenum=身份证
weaver.user.mobilecall=其他电话
weaver.user.sex=性别
weaver.user.residentplace=现居住地
weaver.user.regresidentplace=户口
weaver.user.maritalstatus=婚姻状况
weaver.user.nativeplace=籍贯
weaver.user.folk=民族
weaver.user.birthday=生日
weaver.user.telephone=办公电话
weaver.user.mobile=手机号
weaver.user.fax=传真
weaver.user.email=邮箱
weaver.user.homeaddress=家庭联系方式
weaver.user.height=身高
weaver.user.weight=体重
weaver.user.healthinfo=健康状况
weaver.user.policy=政治面貌
weaver.user.bememberdate=入团时间
weaver.user.bepartydate=入党时间
weaver.user.educationlevel=学历
weaver.user.degree=学位
weaver.user.seclevel=安全级别
weaver.user.createdate=创建日期
weaver.user.created=创建日期
weaver.user.modified=修改时间
weaver.user.lastmoddate=最后修改日期
weaver.user.dsporder=排序

# WEAVER 用户属性描述
weaver.user.id.description=Id
weaver.user.workcode.description=工号，唯一标识
weaver.user.lastname.description=人员名称
weaver.user.department.description=部门编码
weaver.user.subcompany.description=分部编码
weaver.user.joblevel.description=职级
weaver.user.jobcall.description=职称
weaver.user.jobactivityid.description=职务
weaver.user.jobgroupid.description=职务类型
weaver.user.jobtitle.description=岗位id
weaver.user.status.description=状态: 0 试用 1 正式 2 临时 3 试用延期 4 解聘 5 离职 6 退休 7 无效
weaver.user.locationid.description=办公地点，关联数据表：HRMLOCATIONS
weaver.user.workroom.description=办公室
weaver.user.person_custom_data.description=个人自定义数据
weaver.user.base_custom_data.description=基本信息自定义数据
weaver.user.work_custom_data.description=自定义数据
weaver.user.language.description=系统语言；枚举：简体中文 、 繁體中文 、 English
weaver.user.startdate.description=合同开始日期
weaver.user.enddate.description=合同结束日期
weaver.user.subcompanyid1.description=分部id
weaver.user.subcompanyname.description=分部名称
weaver.user.departmentid.description=部门id
weaver.user.departmentname.description=部门名称
weaver.user.managerid.description=上级人员id
weaver.user.assistantid.description=助理人员id
weaver.user.accounttype.description=主次账号标志：1 次账号 , 其他 主账号
weaver.user.accountname.description=工资账号户名
weaver.user.bankid1.description=工资银行
weaver.user.companystartdate.description=入职日期; 格式：yyyy-MM-dd
weaver.user.workstartdate.description=参加工作日期; 格式：yyyy-MM-dd
weaver.user.belongto.description=主账号id （当accounttype 为 1 有效）
weaver.user.isadaccount.description=是否ad账号; 范围： 1是ad账号；其他非ad账号
weaver.user.islabouunion.description=工会会员
weaver.user.tempresidentnumber.description=暂住号码
weaver.user.jobactivitydesc.description=职责描述
weaver.user.loginid.description=登录名
weaver.user.password.description=密码
weaver.user.certificatenum.description=身份证
weaver.user.mobilecall.description=其他电话
weaver.user.sex.description=性别
weaver.user.residentplace.description=现居住地
weaver.user.regresidentplace.description=户口
weaver.user.maritalstatus.description=婚姻状况
weaver.user.nativeplace.description=籍贯
weaver.user.folk.description=民族
weaver.user.birthday.description=生日
weaver.user.telephone.description=办公电话
weaver.user.mobile.description=手机号
weaver.user.fax.description=传真
weaver.user.email.description=邮箱
weaver.user.homeaddress.description=家庭联系方式
weaver.user.height.description=身高
weaver.user.weight.description=体重
weaver.user.healthinfo.description=健康状况
weaver.user.policy.description=政治面貌
weaver.user.bememberdate.description=入团时间
weaver.user.bepartydate.description=入党时间
weaver.user.educationlevel.description=学历
weaver.user.degree.description=学位
weaver.user.seclevel.description=安全级别
weaver.user.createdate.description=创建日期
weaver.user.created.description=创建日期
weaver.user.modified.description=修改时间
weaver.user.lastmoddate.description=最后修改日期
weaver.user.dsporder.description=排序

# WEAVER 部门属性
weaver.org.id=泛微oa的ID
weaver.org.subcompanyid1=分部id
weaver.org.subcompanycode=分部编码
weaver.org.org_code=分部编号
weaver.org.supdepid=上级部门id
weaver.org.parent_code=上级部门编号
weaver.org.code=部门编码，自定义
weaver.org.shortname=部门简称
weaver.org.fullname=部门全称
weaver.org.canceled=封存标志；默认查询非封存数据。1:封存。2：获取全部数据
weaver.org.custom_data=值内容是指定获取OA自定义字段的列表（具体看【组织权限中心】-【自定义设置】-【部门字段定义】）
weaver.org.created=创建时间戳
weaver.org.modified=修改时间戳
weaver.org.showorder=自定义数据，具体参考 【组织权限中心-自定义设置-部门字段定义】

# WEAVER 部门属性描述
weaver.org.id.description=泛微oa的ID
weaver.org.subcompanyid1.description=分部id
weaver.org.subcompanycode.description=分部编码
weaver.org.org_code.description=分部编号
weaver.org.supdepid.description=上级部门id
weaver.org.parent_code.description=上级部门编号
weaver.org.code.description=部门编码，自定义
weaver.org.shortname.description=部门简称
weaver.org.fullname.description=部门全称
weaver.org.canceled.description=封存标志；默认查询非封存数据。1:封存。2：获取全部数据
weaver.org.custom_data.description=值内容是指定获取OA自定义字段的列表（具体看【组织权限中心】-【自定义设置】-【部门字段定义】）
weaver.org.created.description=创建时间戳
weaver.org.modified.description=修改时间戳
weaver.org.showorder.description=自定义数据，具体参考 【组织权限中心-自定义设置-部门字段定义】

# WECHAT 用户属性
wechat.user.openid=openid
wechat.user.unionid=用户统一标识。针对一个微信开放平台账号下的应用，同一用户的unionid是唯一的
wechat.user.headimgurl=用户头像，最后一个数值代表正方形头像大小（有0、46、64、96、132数值可选，0代表640*640正方形头像），用户没有头像时该项为空

# WECHAT 用户属性描述
wechat.user.openid.description=openid
wechat.user.unionid.description=用户统一标识。针对一个微信开放平台账号下的应用，同一用户的unionid是唯一的
wechat.user.headimgurl.description=用户头像，最后一个数值代表正方形头像大小（有0、46、64、96、132数值可选，0代表640*640正方形头像），用户没有头像时该项为空

# WECHATOFFICIAL 用户属性
wechatofficial.user.openid=openid
wechatofficial.user.headimgurl=用户头像，最后一个数值代表正方形头像大小（有0、46、64、96、132数值可选，0代表640*640正方形头像），用户没有头像时该项为空

# WECHATOFFICIAL 用户属性描述
wechatofficial.user.openid.description=openid
wechatofficial.user.headimgurl.description=用户头像，最后一个数值代表正方形头像大小（有0、46、64、96、132数值可选，0代表640*64

# YONBIP 用户属性
yonbip.user.billtype=单据类型
yonbip.user.isexchange=Y
yonbip.user.replace=Y
yonbip.user.sender=发送方编码
yonbip.user.birthdate=出生日期
yonbip.user.id=Id
yonbip.user.code=人员编码（唯一属性）
yonbip.user.email=电子邮件
yonbip.user.enablestate=启用状态
yonbip.user.firstname=名
yonbip.user.homephone=家庭电话
yonbip.user.billId=证件号
yonbip.user.idtype=证件类型
yonbip.user.joinworkdate=参加工作日期
yonbip.user.lastname=姓
yonbip.user.mnecode=助记码
yonbip.user.mobile=手机
yonbip.user.name=姓名
yonbip.user.nickname=昵称
yonbip.user.officephone=办公电话
yonbip.user.pk_group=所属集团
yonbip.user.pk_org=所属业务单元
yonbip.user.sex=性别
yonbip.user.usedname=曾用名
yonbip.user.def1=自定义项1
yonbip.user.def2=自定义项2
yonbip.user.def3=自定义项3
yonbip.user.def4=自定义项4
yonbip.user.def5=自定义项5
yonbip.user.def6=自定义项6
yonbip.user.def7=自定义项7
yonbip.user.def8=自定义项8
yonbip.user.def9=自定义项9
yonbip.user.def10=自定义项10
yonbip.user.def11=自定义项11
yonbip.user.def12=自定义项12
yonbip.user.def13=自定义项13
yonbip.user.def14=自定义项14
yonbip.user.def15=自定义项15
yonbip.user.def16=自定义项16
yonbip.user.def17=自定义项17
yonbip.user.def18=自定义项18
yonbip.user.def19=自定义项19
yonbip.user.def20=自定义项20
yonbip.user.city=城市
yonbip.user.addressCode=家庭地址编码
yonbip.user.country=国家
yonbip.user.detailinfo=地址详情
yonbip.user.postcode=邮政编码
yonbip.user.province=省份
yonbip.user.vsection=县区
yonbip.user.psnjobs=工作信息
yonbip.user.addr=家庭住址主键
yonbip.user.account=帐套
yonbip.user.groupcode=集团编码
yonbip.user.user_password=用户密码
yonbip.user.pwdlevelcode=密码安全级别
yonbip.user.pwdparam=密码参数
yonbip.user.user_note=备注
yonbip.user.abledate=生效日期
yonbip.user.disabledate=失效日期
yonbip.user.islocked=是否锁定
yonbip.user.base_doc_type=身份类型
yonbip.user.user_type=用户类型
yonbip.user.pk_base_doc=身份
yonbip.user.identityverifycode=认证类型
yonbip.user.format=数据格式
yonbip.user.isca=CA用户
yonbip.user.contentlang=语言信息
yonbip.user.user_code_q=查询编码
yonbip.user.pk_usergroupforcreate=所属用户组
yonbip.user.filename=文件名
yonbip.user.roottag=跟标签

# YONBIP 用户属性描述
yonbip.user.billtype.description=单据类型
yonbip.user.isexchange.description=Y
yonbip.user.replace.description=Y
yonbip.user.sender.description=发送方编码
yonbip.user.birthdate.description=出生日期
yonbip.user.id.description=Id
yonbip.user.code.description=人员编码（唯一属性确认之后就不能修改）
yonbip.user.email.description=电子邮件
yonbip.user.enablestate.description=启用状态
yonbip.user.firstname.description=名
yonbip.user.homephone.description=家庭电话
yonbip.user.billId.description=证件号
yonbip.user.idtype.description=证件类型
yonbip.user.joinworkdate.description=参加工作日期
yonbip.user.lastname.description=姓
yonbip.user.mnecode.description=助记码
yonbip.user.mobile.description=手机
yonbip.user.name.description=姓名
yonbip.user.nickname.description=昵称
yonbip.user.officephone.description=办公电话
yonbip.user.pk_group.description=所属集团
yonbip.user.pk_org.description=所属业务单元
yonbip.user.sex.description=性别
yonbip.user.usedname.description=曾用名
yonbip.user.def1.description=自定义项1
yonbip.user.def2.description=自定义项2
yonbip.user.def3.description=自定义项3
yonbip.user.def4.description=自定义项4
yonbip.user.def5.description=自定义项5
yonbip.user.def6.description=自定义项6
yonbip.user.def7.description=自定义项7
yonbip.user.def8.description=自定义项8
yonbip.user.def9.description=自定义项9
yonbip.user.def10.description=自定义项10
yonbip.user.def11.description=自定义项11
yonbip.user.def12.description=自定义项12
yonbip.user.def13.description=自定义项13
yonbip.user.def14.description=自定义项14
yonbip.user.def15.description=自定义项15
yonbip.user.def16.description=自定义项16
yonbip.user.def17.description=自定义项17
yonbip.user.def18.description=自定义项18
yonbip.user.def19.description=自定义项19
yonbip.user.def20.description=自定义项20
yonbip.user.city.description=城市
yonbip.user.addressCode.description=家庭地址编码
yonbip.user.country.description=国家
yonbip.user.detailinfo.description=地址详情
yonbip.user.postcode.description=邮政编码
yonbip.user.province.description=省份
yonbip.user.vsection.description=县区
yonbip.user.psnjobs.description=工作信息
yonbip.user.addr.description=家庭住址主键
yonbip.user.account.description=帐套
yonbip.user.groupcode.description=集团编码
yonbip.user.user_password.description=用户密码
yonbip.user.pwdlevelcode.description=密码安全级别
yonbip.user.pwdparam.description=密码参数
yonbip.user.user_note.description=备注
yonbip.user.abledate.description=生效日期
yonbip.user.disabledate.description=失效日期
yonbip.user.islocked.description=是否锁定
yonbip.user.base_doc_type.description=身份类型
yonbip.user.user_type.description=用户类型
yonbip.user.pk_base_doc.description=身份
yonbip.user.identityverifycode.description=认证类型
yonbip.user.format.description=数据格式
yonbip.user.isca.description=CA用户
yonbip.user.contentlang.description=语言信息
yonbip.user.user_code_q.description=查询编码
yonbip.user.pk_usergroupforcreate.description=所属用户组
yonbip.user.filename.description=文件名
yonbip.user.roottag.description=跟标签

# YONBIP 部门属性
yonbip.org.account=账套编码
yonbip.org.billtype=单据类型
yonbip.org.groupcode=集团编码
yonbip.org.isexchange=是否使用NC翻译
yonbip.org.replace=是否允许更新
yonbip.org.roottag=跟标签
yonbip.org.sender=发送方编码
yonbip.org.orgcode=组织编码
yonbip.org.id=id
yonbip.org.pk_dept=部门主键
yonbip.org.code=部门编码
yonbip.org.name=部门名称多语
yonbip.org.pk_fatherorg=上级部门主键
yonbip.org.pk_group=集团主键
yonbip.org.pk_org=业务单元主键
yonbip.org.mnecode=助记码
yonbip.org.hrcanceled=HR撤销标志
yonbip.org.creator=创建人
yonbip.org.enablestate=启用状态
yonbip.org.displayorder=显示顺序
yonbip.org.depttype=部门类别
yonbip.org.orgtype13=报表
yonbip.org.orgtype17=预算
yonbip.org.principal=部门负责人
yonbip.org.city=城市
yonbip.org.code1=编码
yonbip.org.country=国家
yonbip.org.detailinfo=详细地址
yonbip.org.postcode=邮政编码
yonbip.org.province=省份
yonbip.org.vsection=县区

# YONBIP 部门属性描述
yonbip.org.account.description=账套编码
yonbip.org.billtype.description=单据类型
yonbip.org.groupcode.description=集团编码
yonbip.org.isexchange.description=是否使用NC翻译
yonbip.org.replace.description=是否允许更新
yonbip.org.roottag.description=跟标签
yonbip.org.sender.description=发送方编码
yonbip.org.orgcode.description=组织编码
yonbip.org.id.description=id
yonbip.org.pk_dept.description=部门主键
yonbip.org.code.description=部门编码
yonbip.org.name.description=部门名称多语
yonbip.org.pk_fatherorg.description=上级部门主键
yonbip.org.pk_group.description=集团主键
yonbip.org.pk_org.description=业务单元主键
yonbip.org.mnecode.description=助记码
yonbip.org.hrcanceled.description=HR撤销标志
yonbip.org.creator.description=创建人
yonbip.org.enablestate.description=启用状态
yonbip.org.displayorder.description=显示顺序
yonbip.org.depttype.description=部门类别
yonbip.org.orgtype13.description=报表
yonbip.org.orgtype17.description=预算
yonbip.org.principal.description=部门负责人
yonbip.org.city.description=城市
yonbip.org.code1.description=编码
yonbip.org.country.description=国家
yonbip.org.detailinfo.description=详细地址
yonbip.org.postcode.description=邮政编码
yonbip.org.province.description=省份
yonbip.org.vsection.description=县区

# MOKA 用户属性
moka.user.userId=用户id
moka.user.email=邮箱
moka.user.name=姓名
moka.user.phone=电话
moka.user.number=工号
moka.user.role=角色。0：内推人，5：前台，10：面试官，20：用人经理，25：高级用人经理，30：HR，40：管理员，50：超管
moka.user.roleId=自定义角色id
moka.user.superiorEmail=上级邮箱
moka.user.deactivated=是否被禁用。可选值：0:可用，1:禁用
moka.user.isPending=功能性字段，用作单点登录，没有传空字符串
moka.user.thirdPartyId=功能性字段，用作单点登录，没有返回空字符串
moka.user.locale=用户语言。可选值：zh-CN:为中文，en-US:英文。默认为中文
moka.user.department=用户所属/负责部门
moka.user.departmentCode=三方部门id
moka.user.departmentId=moka部门id
moka.user.departmentName=部门名称

# MOKA 用户属性描述
moka.user.userId.description=用户id
moka.user.email.description=邮箱
moka.user.name.description=姓名
moka.user.phone.description=电话
moka.user.number.description=工号
moka.user.role.description=角色。0：内推人，5：前台，10：面试官，20：用人经理，25：高级用人经理，30：HR，40：管理员，50：超管
moka.user.roleId.description=自定义角色id
moka.user.superiorEmail.description=上级邮箱
moka.user.deactivated.description=是否被禁用。可选值：0:可用，1:禁用
moka.user.isPending.description=功能性字段，用作单点登录，没有传空字符串
moka.user.thirdPartyId.description=功能性字段，用作单点登录，没有返回空字符串
moka.user.locale.description=用户语言。可选值：zh-CN:为中文，en-US:英文。默认为中文
moka.user.department.description=用户所属/负责部门
moka.user.departmentCode.description=三方部门id
moka.user.departmentId.description=moka部门id
moka.user.departmentName.description=部门名称


# MOKA 部门属性
moka.org.name=部门名称
moka.org.departmentId=MOKA系统的部门id
moka.org.departmentCode=客户系统的部门id
moka.org.parentCode=部门的上级部门的唯一 id, 如为 null 则为一级部门
moka.org.deletedByApi=是否删除
moka.org.type=部门类型
moka.org.parentId=MOKA系统的父部门id

# MOKA 部门属性描述
moka.org.name.description=部门名称
moka.org.departmentId.description=MOKA系统的部门id
moka.org.departmentCode.description=客户系统的部门id
moka.org.parentCode.description=部门的上级部门的唯一 id, 如为 null 则为一级部门
moka.org.deletedByApi.description=是否删除
moka.org.type.description=部门类型
moka.org.parentId.description=MOKA系统的父部门id

# SIMPLEHR 用户属性
simplehr.user.accountId=账号Id
simplehr.user.workCode=工号
simplehr.user.name=姓名
simplehr.user.phone=电话
simplehr.user.department=用户所属部门

# SIMPLEHR 用户属性描述
simplehr.user.accountId.description=账号Id
simplehr.user.workCode.description=工号
simplehr.user.name.description=姓名
simplehr.user.phone.description=电话
simplehr.user.department.description=用户所属部门

# SIMPLEHR 部门属性
simplehr.org.name=部门名称
simplehr.org.deptId=部门id
simplehr.org.deptCode=客户系统的部门code
simplehr.org.parentCode=部门的上级部门的唯一code

# SIMPLEHR 部门属性描述
simplehr.org.name.description=部门名称
simplehr.org.deptId.description=部门id
simplehr.org.deptCode.description=客户系统的部门code
simplehr.org.parentCode.description=部门的上级部门的唯一code

# HAP 用户属性
hap.user.accountId=明道云账号Id
hap.user.corpUserId=第三方用户Id, 必填
hap.user.name=用户名, 必填
hap.user.email=邮箱，与 mobilePhone 不能同时为空, 必填
hap.user.mobilePhone=手机号，与 email 不能同时为空, 必填
hap.user.contactPhone=座机号, 非必填
hap.user.jobNumber=工号, 非必填
hap.user.departmentIds=第三方部门Id集合, 非必填
hap.user.positions=职位集合, 非必填
hap.user.emptyCover=当非必填字段为空时，是否覆盖原来的值，默认：true（覆盖）
hap.user.avatar=头像，用于集成

# HAP 用户属性描述
hap.user.accountId.description=明道云账号Id
hap.user.corpUserId.description=第三方用户Id, 必填
hap.user.name.description=用户名, 必填
hap.user.email.description=邮箱，与 mobilePhone 不能同时为空, 必填
hap.user.mobilePhone.description=手机号，与 email 不能同时为空, 必填
hap.user.contactPhone.description=座机号, 非必填
hap.user.jobNumber.description=工号, 非必填
hap.user.departmentIds.description=第三方部门Id集合, 非必填
hap.user.positions.description=职位集合, 非必填
hap.user.emptyCover.description=当非必填字段为空时，是否覆盖原来的值，默认：true（覆盖）
hap.user.avatar.description=头像，用于集成

# HAP 部门属性
hap.org.departmentId=明道云部门Id
hap.org.corpDepartmentId=第三方部门Id, 必填
hap.org.name=部门名称, 必填
hap.org.parentId=父部门Id, 非必填
hap.org.order=排序值, 非必填
hap.org.isNumber=部门负责人id是否是数值类型（如果是基于 企业微信、钉钉、Welink 集成，则需要传 true）, 非必填
hap.org.chargeCorpUserIds=部门负责人，第三方用户Id列表, 非必填

# HAP 部门属性描述
hap.org.departmentId.description=明道云部门Id
hap.org.corpDepartmentId.description=第三方部门Id, 必填
hap.org.name.description=部门名称, 必填
hap.org.parentId.description=父部门Id, 非必填
hap.org.order.description=排序值, 非必填
hap.org.isNumber.description=部门负责人id是否是数值类型（如果是基于 企业微信、钉钉、Welink 集成，则需要传 true）, 非必填
hap.org.chargeCorpUserIds.description=部门负责人，第三方用户Id列表, 非必填




















