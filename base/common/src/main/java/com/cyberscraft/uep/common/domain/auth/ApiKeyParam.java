package com.cyberscraft.uep.common.domain.auth;

import java.io.Serializable;
import java.util.Map;

/**
 * <p>
 *  ApiKey认证参数
 * </p>
 *
 * <AUTHOR>
 * @since 2022/10/19 5:57 下午
 */
public class ApiKeyParam extends Account implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * query参数
     */
    private Map<String, String> query;

    /**
     * header参数
     */
    private Map<String, String> header;

    public Map<String, String> getQuery() {
        return query;
    }

    public void setQuery(Map<String, String> query) {
        this.query = query;
    }

    public Map<String, String> getHeader() {
        return header;
    }

    public void setHeader(Map<String, String> header) {
        this.header = header;
    }

    @Override
    public String getRealIp() {
        return null;
    }
}
