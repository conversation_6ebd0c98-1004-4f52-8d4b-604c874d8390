package com.cyberscraft.uep.common.util;

import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;

/**
 * @Description HMAC签名工具类
 * <AUTHOR>
 * @Date 2024/4/10 14:11
 */
public class HMACSignUtil {

    private static final Logger logger = LoggerFactory.getLogger(HMACSignUtil.class);

    /**
     * HAMC 签名 结果base64返回
     *
     * @param content 待签名内容
     * @param secret  密钥
     * @return 签名结果
     */
    public static String hMacSignBase64(String alg, String content, String secret) throws Exception {
        byte[] bytes = hMacSign(alg, content.getBytes(StandardCharsets.UTF_8), secret.getBytes(StandardCharsets.UTF_8));
        return Base64.encodeBase64String(bytes);
    }

    public static byte[] hMacSign(String alg, byte[] content, byte[] secret) throws Exception {
        Mac instance = Mac.getInstance(alg);
        SecretKeySpec secretKey = new SecretKeySpec(secret, alg);
        instance.init(secretKey);
        return instance.doFinal(content);

    }

}
