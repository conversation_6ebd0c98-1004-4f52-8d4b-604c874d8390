package com.cyberscraft.uep.common.domain.message;


import java.io.Serializable;

/***
 * 钉钉消息发送进度对像
 * @date 2021/7/15
 * <AUTHOR>
 ***/
public class MessageSendProgress implements Serializable {
    private int progressInPercent;
    private MessageSendStatusEnum status;

    public int getProgressInPercent() {
        return progressInPercent;
    }

    public void setProgressInPercent(int progressInPercent) {
        this.progressInPercent = progressInPercent;
    }

    public MessageSendStatusEnum getStatus() {
        return status;
    }

    public void setStatus(MessageSendStatusEnum status) {
        this.status = status;
    }
}
