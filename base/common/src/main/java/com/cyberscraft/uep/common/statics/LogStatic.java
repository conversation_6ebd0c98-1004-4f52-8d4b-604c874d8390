package com.cyberscraft.uep.common.statics;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/***
 * 统计信息注解，
 * 在需要进行调用次数， 成功次数，异常(失败)次数，时长，成功失败比率的地方增加该注解，
 * @date 2021/7/28
 * <AUTHOR>
 ***/
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface LogStatic {
    /***
     * 默认按天统计
     */
    StaticsType staticsType = StaticsType.DAY;
}
