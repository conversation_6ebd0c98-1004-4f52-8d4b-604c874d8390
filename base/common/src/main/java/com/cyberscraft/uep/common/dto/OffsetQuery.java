package com.cyberscraft.uep.common.dto;

/**
 * <p>
 *  按偏移量分批查询
 * </p>
 *
 * <AUTHOR>
 * @since 2022/5/13 7:59 下午
 */
public class OffsetQuery<T> {

    public static final int DEFAULT_PAGE_SIZE = 50;
    public static final int MAX_PAGE_SIZE = 100;

    private Long offset;
    private Integer count;
    String sortColumn;
    boolean isDesc;

    public OffsetQuery(Long offset, Integer count, String sortColumn, boolean isDesc){
        if(offset == null){
            this.offset = 0L;
        }else if(offset < 0L){
            this.offset = 0L;
        }else{
            this.offset = offset;
        }

        if(count == null || count < 0){
            this.count = DEFAULT_PAGE_SIZE;
        }else if(count > MAX_PAGE_SIZE){
            this.count = MAX_PAGE_SIZE;
        }else{
            this.count = count;
        }

        this.sortColumn = sortColumn;
        this.isDesc = isDesc;
    }

    public Long getOffset() {
        return offset;
    }

    public Integer getCount() {
        return count;
    }

    public String getSortColumn() {
        return sortColumn;
    }

    public boolean getDesc() {
        return isDesc;
    }
}
