package com.cyberscraft.uep.common.util;

import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;


/**
 * CookieUtil 设置https安全 cookie
 *
 * <AUTHOR>
 * @data 2019年6月2日
 */
public class CookieUtil {

    public static Cookie getCookie(HttpServletRequest httpRequest, String cookieName) {
        if (StringUtils.isNotBlank(cookieName)) {
            Cookie[] cookies = httpRequest.getCookies();
            if (cookies != null && cookies.length > 0) {
                for (Cookie cookie : cookies) {
                    if (cookieName.toLowerCase().equals(cookie.getName().toLowerCase())) {
                        return cookie;
                    }
                }
            }
        }
        return null;
    }

    public static String getCookieValue(HttpServletRequest httpRequest, String cookieName) {
        Cookie cookie = getCookie(httpRequest, cookieName);
        if (cookie != null) {
            return cookie.getValue();
        }
        return null;
    }

    /**
     * @param response    响应请求
     * @param name        Cookie名称
     * @param value       Cookie值
     * @param path        path信息
     * @param maxAge      Cookie有效时长
     * @param secure      secure参数，https请求时开启
     * @param httpOnly    Cookie信息设置为httpOnly
     * @param isAddCookie Cookie以Add方式还是set方式添加
     * @title
     */
    public static void setCookie(final HttpServletResponse response, final String name, final String value,
                                 final String path, final int maxAge, final boolean secure, final boolean httpOnly,
                                 final boolean isAddCookie) {
        setCookie(response, name, value, path, maxAge, secure, null, null, httpOnly, isAddCookie);
    }

    /**
     * @param response    响应请求
     * @param name        Cookie名称
     * @param value       Cookie值
     * @param path        path信息
     * @param maxAge      Cookie有效时长
     * @param secure      secure参数，https情书时开启
     * @param comment
     * @param domain
     * @param httpOnly    Cookie信息设置为httpOnly
     * @param isAddCookie Cookie以Add方式还是set方式添加
     * @title
     */
    public static void setCookie(final HttpServletResponse response, final String name, final String value,
                                 final String path, final int maxAge, final boolean secure, final String comment, final String domain,
                                 final boolean httpOnly, final boolean isAddCookie) {
        String cookieName = StringUtils.isBlank(name) ? "none" : name;
        String cookieVal = StringUtils.isBlank(value) ? "cookieVal" : value;
        final Cookie cookie = new Cookie(cookieName, cookieVal);
        cookie.setPath(path);
        cookie.setComment(comment);
        cookie.setDomain(StringUtils.isBlank(domain) ? "" : domain);
        cookie.setMaxAge(maxAge);
        cookie.setHttpOnly(httpOnly);
        cookie.setSecure(secure);
        // cookie.setVersion(version);

        //cookie.setSecure(true);
        // response.addCookie(cookie);
        // response.setHeader("Set-Cookie", "Secure;HttpOnly;");
        // 设置cookie
        setCookie(cookie, response, httpOnly, isAddCookie);
    }

    /**
     * 调整Set-Cookie设置
     *
     * @param cookie
     * @param response
     * @title
     */
    public static void setCookie(final Cookie cookie, final HttpServletResponse response, final boolean httpOnly,
                                 final boolean isAddCookie) {
        /*
         * response.addCookie(cookie);
         */
        // 改造
        StringBuilder sb = new StringBuilder(200);
        boolean addName = false;
        if (!"none".equals(cookie.getName())) {
            addName = true;
            String cookieVal = StringUtils.isBlank(cookie.getValue()) ? "" : cookie.getValue();
            sb.append(cookie.getName()).append("=").append(cookieVal);
        }
        if (addName) {
            sb.append(addName ? "; " : "");
        }
        sb.append("Path=/");
        if (cookie.getDomain() != null && !"".equals(cookie.getDomain())) {
            sb.append("; Domain=").append(cookie.getDomain());
        }
        if (cookie.getMaxAge() > -1) {
            if (cookie.getMaxAge() > 0) {
                sb.append("; Max-Age=").append(cookie.getMaxAge());
            } else if (cookie.getMaxAge() == 0) {
                sb.append("; Max-Age=").append(cookie.getMaxAge());
            }
        }

//        if (cookie.getSecure() && isHttps()) {
//            sb.append("; Secure");
//        }
        if (cookie.getSecure()) {
            sb.append("; Secure");
        }

        if (httpOnly) {
            sb.append("; HttpOnly");
        }
        sb.append("; Expires=" + getExpireDate());

        response.setHeader("Set-Cookie", sb.toString());
    }

    static String getExpireDate() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.HOUR, 12);
        Date date = cal.getTime();
        Locale locale = Locale.CHINA;
        SimpleDateFormat sdf = new SimpleDateFormat("dd-MM-yyyy HH:mm:ss", locale);
        return sdf.format(date);
    }

    static boolean isHttps() {
        // String isHttps = PropertiesUtil.readGlobal("isHttps");
        return true;
    }

}
