package com.cyberscraft.uep.common.util;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * <AUTHOR>
 * @Date 2024/7/24 18:15
 * @Version 1.0
 * @Description 公共map工具类保存某些信息至内存中
 */
public class CommonConcurrentMapUtils {



    private static final ConcurrentHashMap<String, Object> commonHashMap = new ConcurrentHashMap<>();
    /**
     * 存入map
     *
     * @param key   键
     * @param value 值
     */
    public static void put(String key, Object value) {
        commonHashMap.put(key, value);
    }

    /**
     * 获取map中的值
     *
     * @param key 键
     * @return 值
     */
    public static Object get(String key) {
        return commonHashMap.get(key);
    }

    /**
     * 移除map中的值
     *
     * @param key 键
     */
    public static void remove(String key) {
        commonHashMap.remove(key);
    }

    /**
     * 获取map
     *
     * @return map
     */
    public static ConcurrentMap<String, Object> getTaskMap() {
        return commonHashMap;
    }
}
