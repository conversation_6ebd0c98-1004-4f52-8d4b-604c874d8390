package com.cyberscraft.uep.common.domain.auth;

import java.io.Serializable;

/**
 * @Description Exchange账号
 * <AUTHOR>
 * @Date 2024/10/12 20:15
 */
public class ExchangeParam extends Account implements Serializable {

    private static final long serialVersionUID = 1L;

    private String accountId;

    private String clientId;

    private String clientSecret;

    private String tenantId;

    private String impersonatedUser;

    private String ewsOnlineUrl;

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getClientSecret() {
        return clientSecret;
    }

    public void setClientSecret(String clientSecret) {
        this.clientSecret = clientSecret;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getImpersonatedUser() {
        return impersonatedUser;
    }

    public void setImpersonatedUser(String impersonatedUser) {
        this.impersonatedUser = impersonatedUser;
    }

    public String getEwsOnlineUrl() {
        return ewsOnlineUrl;
    }

    public void setEwsOnlineUrl(String ewsOnlineUrl) {
        this.ewsOnlineUrl = ewsOnlineUrl;
    }

    @Override
    public String getRealIp() {
        return "";
    }
}
