package com.cyberscraft.uep.common.exception.errorcode;

public class MTenantError {

    public static final String  OK =  "0";
    public static final String MESSAGE_SUCCESS = "SUCCESS";
    public static final String  SYSTEM_CODE =  "300";
    /**
     * Authn and authz
     */
    public static final String MTENANT_ACCESS_USER_DISABLE                                      = SYSTEM_CODE + "0100";
    public static final String MTENANT_ACCESS_UNAUTHENTICATED                                   = SYSTEM_CODE + "0101";
    public static final String MTENANT_ACCESS_PERMISSION_DENY                                   = SYSTEM_CODE + "0102";

    /**
     * 通用错误代码
     */
    public static final String MTENANT_COMMON_ERROR_INIT                                        = SYSTEM_CODE + "0009";  //参数错误
    public static final String MTENANT_COMMON_ERROR_INTERNAL                                    = SYSTEM_CODE + "0099";  //内部错误
    public static final String MTENANT_COMMON_ERROR_MAIL                                        = SYSTEM_CODE + "0089";  //邮件相关错误
    public static final String MTENANT_COMMON_ERROR_PARAMS_TYPE_ERROR                           = SYSTEM_CODE + "0012";  //参数类型错误
    public static final String MTENANT_COMMON_ERROR_PARAMS_ERROR                                = SYSTEM_CODE + "0013";  //参数错误
    public static final String TENANT_CREATE_SERVICE_ERROR                                      = SYSTEM_CODE + "0014"; //租户服务创建失败
    public static final String TENANT_CREATE_SERVICE_TIMEOUT_ERROR                              = SYSTEM_CODE + "0015"; //租户服务创建超时

    /**
     * 租户处理相关 错误码
     */

    public static final String TENANT_ADD_ERROR_PARAMETER_EMPTY                                     = SYSTEM_CODE + "0201";
    public static final String TENANT_ADD_ERROR_PARAMETER_INVALID_EMAIL                             = SYSTEM_CODE + "0202";
    public static final String TENANT_ADD_ERROR_PARAMETER_INVALID_DOMAIN                            = SYSTEM_CODE + "0203";
    public static final String TENANT_ADD_ERROR_PARAMETER_DOMAIN_DUPLICATION                        = SYSTEM_CODE + "0204";
    public static final String TENANT_ADD_ERROR_PARAMETER_CONTRACT_NUM_DUPLICATION                  = SYSTEM_CODE + "0205";
    public static final String TENANT_ADD_ERROR_SOURCE_DB_NONE                                      = SYSTEM_CODE + "0206";
    public static final String TENANT_ADD_ERROR_APK_BUILD                                           = SYSTEM_CODE + "0207";
    public static final String TENANT_ADD_ERROR_CREATE_SCHEMA                                       = SYSTEM_CODE + "0208";
    public static final String TENANT_ADD_ERROR_SEND_MAIL                                           = SYSTEM_CODE + "0209";
    public static final String TENANT_ADD_ERROR_ADD_LICENSE                                         = SYSTEM_CODE + "0210";
    public static final String TENANT_ADD_ERROR_ROLLBACK_DELETE_SCHEMA                              = SYSTEM_CODE + "0211";
    public static final String TENANT_ADD_ERROR_LICENSE_NOT_ENOUGH                                  = SYSTEM_CODE + "0212";
    public static final String TENANT_FETCH_ERROR_PARAMETER_EMPTY                                   = SYSTEM_CODE + "0214";
    public static final String TENANT_FETCH_ERROR_PARAMETER                                         = SYSTEM_CODE + "0215";
    public static final String TENANT_FETCH_ERROR_NOT_EXIST                                         = SYSTEM_CODE + "0216";
    public static final String MESSAGE_TENANT_NOT_FOUND                                             = "Tenant not found";
    public static final String TENANT_UPDATE_ERROR_PARAMETER_EMPTY                                  = SYSTEM_CODE + "0217";
    public static final String TENANT_UPDATE_ERROR_NOT_EXIST                                        = SYSTEM_CODE + "0218";
    public static final String TENANT_UPDATE_ERROR_CONTRACT_NUM_DUPLICATION                         = SYSTEM_CODE + "0219";
    public static final String TENANT_UPDATE_ERROR_BATCH_IDS_EMPTY                                  = SYSTEM_CODE + "0220";
    public static final String TENANT_DELETE_TENANT_NO_EXPIRY_DATE                                  = SYSTEM_CODE + "0221";
    public static final String TENANT_DELETE_TENANT_DELETE_DB                                       = SYSTEM_CODE + "0222";
    public static final String TENANT_ERROR_DB_OPERATION                                            = SYSTEM_CODE + "0223";
    public static final String TENANT_ERROR_TENANT_HAS_EXPIRED                                      = SYSTEM_CODE + "0224";
    public static final String TENANT_ERROR_LICENSE_REQUEST                                         = SYSTEM_CODE + "0230";
    public static final String TENANT_UPDATE_ADMIN_PWD                                              = SYSTEM_CODE + "0231";
    public static final String TENANT_MEAL_EMPTY                                                    = SYSTEM_CODE + "0232";
    public static final String TENANT_SERVICE_CATEGORY_NOT_FOUND                                    = SYSTEM_CODE + "0233";
    public static final String TENANT_SERVICE_CATEGORY_NOT_ENABLED                                  = SYSTEM_CODE + "0234";
    public static final String TENANT_SERVICE_CALLBACK_DOMAIN_NOT_MATCH                             = SYSTEM_CODE + "0235";
    public static final String TENANT_SERVICE_CALLBACK_DOMAIN_NOT_MATCH_MESSAGE                     = "tenant domain not match";
    public static final String TENANT_SERVICE_CALLBACK_SERVICE_NOT_MATCH                            = SYSTEM_CODE + "0236";
    public static final String TENANT_SERVICE_CALLBACK_SERVICE_NOT_MATCH_MESSAGE                    = "service name not match";
    public static final String TENANT_SERVICE_CALLBACK_SEQUENCE_NOT_MATCH                           = SYSTEM_CODE + "0237";
    public static final String TENANT_SERVICE_CALLBACK_SEQUENCE_NOT_MATCH_MESSAGE                   = "sequence not match";
    public static final String TENANT_SERVICE_INACTIVE                                              = SYSTEM_CODE + "0238";
    public static final String TENANT_SERVICE_INACTIVE_MESSAGE                                      = "tenant service inactive";
    public static final String TENANT_SERVICE_DUPLICATED                                            = SYSTEM_CODE + "0239";
    public static final String TENANT_SERVICE_DUPLICATED_MESSAGE                                    = "tenant service duplicated";
    public static final String TENANT_SERVICE_OPEN_ERROR                                            = SYSTEM_CODE + "0240";
    /**
     *
     *租户数据源相关操作 模块码
     */
    public static final String TENANT_SOURCE_DATABASE_ERROR_PARAMETER_EMPTY                         = SYSTEM_CODE + "0301";
    public static final String TENANT_SOURCE_DATABASE_ERROR_DB_OPERATION                            = SYSTEM_CODE + "0302";
    public static final String TENANT_SOURCE_DATABASE_ERROR_CONFIG_EMPTY                            = SYSTEM_CODE + "0303";

    /**
     *租户组相关操作 模块码
     */
    public static final String TENANT_GROUP_ERROR_DB_OPERATION                                      = SYSTEM_CODE + "0401";
    public static final String TENANT_GROUP_ADD_ERROR_PARAMETER_EMPTY                               = SYSTEM_CODE + "0402";
    //创建组时重名（是同级重名，不同级可以重名
    public static final String TENANT_GROUP_ADD_ERROR_GROUP_DUPLICATION_NAME                        = SYSTEM_CODE + "0403";
    //public static final String TENANT_GROUP_ADD_ERROR_INTERNAL                                      = SYSTEM_CODE + "04";


    public static final String TENANT_GROUP_DELETE_ERROR_PARAMETER_EMPTY                                = SYSTEM_CODE + "0405";
    public static final String TENANT_GROUP_DELETE_ERROR_NOT_EXIST                                      = SYSTEM_CODE + "0406";
    public static final String TENANT_GROUP_DELETE_ERROR_PERMISSION_DENY                                = SYSTEM_CODE + "0407";
    public static final String TENANT_GROUP_UPDATE_ERROR_PARAMETER_EMPTY                                = SYSTEM_CODE + "0408";
    public static final String TENANT_GROUP_UPDATE_ERROR_NOT_EXIST                                      = SYSTEM_CODE + "0409";
    public static final String TENANT_GROUP_UPDATE_ERROR_GROUP_DUPLICATION_NAME                         = SYSTEM_CODE + "0410";
    public static final String TENANT_GROUP_UPDATE_ERROR_CAN_NOT_MOVE_TO_CHILD                          = SYSTEM_CODE + "0411";

    public static final String TENANT_GROUP_FETCH_ERROR_PARAMETER_EMPTY                                 = SYSTEM_CODE + "0420";
    public static final String TENANT_GROUP_FETCH_ERROR_NOT_EXIST                                       = SYSTEM_CODE + "0421";
    /**
     *租户license关操作 模块码
     */
    public static final String TENANT_LICENSE_FETCH_INFO_ERROR                                          = SYSTEM_CODE + "0501";
    public static final String TENANT_LICENSE_FETCH_ACTIVE_INFO_ERROR                                   = SYSTEM_CODE + "0502";
    public static final String TENANT_LICENSE_FETCH_INFO_ERROR_PARAMETER_EMPTY                          = SYSTEM_CODE + "0503";
    public static final String TENANT_LICENSE_FETCH_INFO_ERROR_PARAMETER                                = SYSTEM_CODE + "0504";
    /**
     * 系统用户
     */
    //public static final String TENANT_SYS_USER_ERROR_DB_OPERATION                                       = MTENANT_MODULE_CODE_SYSTEM_USER + "01";
    public static final String TENANT_SYS_USER_FETCH_ERROR_PARAMETER                                    = SYSTEM_CODE + "0601";
    public static final String TENANT_SYS_USER_FETCH_ERROR_USER_IS_NOT_EXIST                            = SYSTEM_CODE + "0602";

    public static final String TENANT_SYS_USER_ADD_DUPLICATION_LOGIN_ID                                 = SYSTEM_CODE + "0603";
    public static final String TENANT_SYS_USER_ADD_GROUP_IS_NOT_EXIST                                   = SYSTEM_CODE + "0604";

    public static final String TENANT_SYS_USER_DELETE_PARAMETER                                         = SYSTEM_CODE + "0605";
    public static final String TENANT_SYS_USER_DELETE_PARAMETER_EMPTY                                   = SYSTEM_CODE + "0606";

    public static final String TENANT_SYS_USER_UPDATE_USER_IS_NOT_EXIST                                 = SYSTEM_CODE + "0607";
    public static final String TENANT_SYS_USER_UPDATE_GROUP_IS_NOT_EXIST                                = SYSTEM_CODE + "0608";
    /**
     * menu 相关错误
     */
    public static final String TENANT_SYS_USER_MENU_ERROR_DB_OPERATION                                   = SYSTEM_CODE + "0701";




}
