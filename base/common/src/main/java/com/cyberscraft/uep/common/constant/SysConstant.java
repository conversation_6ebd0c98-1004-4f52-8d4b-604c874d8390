package com.cyberscraft.uep.common.constant;

import java.time.format.DateTimeFormatter;

/***
 *
 * 定义相关常量，替换原来代码中的相关字符串
 * @date 2021-07-16
 * <AUTHOR>
 ***/
public class SysConstant {

    /***
     * 用于使用到是否类型的是常量值
     */
    public final static String TRUE = "1";

    /***
     * 用于使用到是否类型的否常量值
     */
    public final static String FALSE = "0";

    /***
     * 一般用于返回至客户端的空值定义
     */
    public static final String NONE = "None";


    /***
     * 整数型的TRUE值
     */
    public final static Integer TRUE_VALUE = 1;

    /***
     * 整数型的FALSE值
     */
    public final static Integer FALSE_VALUE = 0;


    /***
     * 用于表示json或者其它传入的数据中为空的字符串，主要是在json等场合使用
     */
    public final static String NULL_VALUE_STR = "null";

    /***
     * 系统中的默认租户ID,如果是多租户环境下，则代表系统用户操作
     */
    public static final String DEFAULT_TENANT_ID = "mdm";

    /***
     * 执行成功
     */
    public static final int OPT_REURN_FLAG_SUCCESS = 0;
    /***
     * 执行失败
     */
    public static final int OPT_REURN_FLAG_FAILURE = -1;

    /***
     *
     */
    public static final String RETURN_CODE = "returnCode";
    /***
     *
     */
    public static final String ERROR_CODE = "errorCode";
    /***
     *
     */
    public static final String ERROR_MSG = "errorMsg";

    /**
     * 默认日期时间格式yyyy-MM-dd HH:mm:ss.SSS
     */
    public static final String DEFAULT_UNDEAL_DATE_TIME_FORMAT_MS = "yyyy-MM-dd HH:mm:ss.SSS";

    /**
     * 默认日期时间格式
     */
    public static final String DEFAULT_DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    /**
     * 默认日期时间格式
     */
    public static final String DEFAULT_UNDEAL_DATE_TIME_FORMAT = "yyyy-MM-ddTHH:mm:ss";

    /***
     *
     */
    public static final DateTimeFormatter DEFAULT_LOCAL_DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    /**
     * 默认日期格式
     */
    public static final String DEFAULT_DATE_FORMAT = "yyyy-MM-dd";
    /**
     * 默认时间格式
     */
    public static final String DEFAULT_TIME_FORMAT = "HH:mm:ss";

    /**
     * 调用DingDing的接口超时时间，5秒
     */
    public static final int GOVDD_CALL_TIMEOUT = 5 * 1000;


    /***
     * 默认的组ID
     */
    public final static Long DEFAULT_GROUP_ID = -2L;

    /***
     * 默认的根组父组ID
     */
    public final static Long DEFAULT_ROOT_GROUP_PARENTID = 0L;

    /***
     * 默认请求Header中access_token的参数名称
     */
    public final static String DEFAULT_ACCESSTOKEN_HEADERNAME = "Authorization";

    /***
     * 默认请求Header中access_token的模式名称
     */
    public static final String DEFAULT_ACCESSTOKEN_SCHEME = "Bearer";


    /***
     *默认的请Header参数tenantid的参数名称
     */
    public final static String DEFAULT_TENANT_HEADEARNAME = "tcode";

    /***
     * 日志对应的TraceId参数
     */
    public final static String LOG_TRACE_ID="LOGTraceId";

    /***
     * 日志对应的RPCID参数
     */
    public final static String LOG_RPC_ID="LOGRpcId";

    /***
     * 日志对应的X-B3-TraceId参数
     */
    public final static String LOG_X_TRACE_ID = "X-B3-TraceId";

    /***
     * 日志对应的SpanId参数
     */
    public final static String LOG_X_SPAN_ID = "X-B3-SpanId";
}

