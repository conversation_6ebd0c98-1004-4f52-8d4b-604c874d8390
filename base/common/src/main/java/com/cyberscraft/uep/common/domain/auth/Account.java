package com.cyberscraft.uep.common.domain.auth;

/**
 * <p>
 *  鉴权帐号配置参数统一接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/10 7:24 下午
 */
public abstract class Account {

    private String proxyIp;

    /**
     * 获取真实IP
     * @return
     */
    public abstract String getRealIp();

    /**
     * 获取代理时的虚拟IP
     * @return
     */
    public String getProxyIp() {
        return proxyIp;
    }

    public void setProxyIp(String proxyIp) {
        this.proxyIp = proxyIp;
    }
}
