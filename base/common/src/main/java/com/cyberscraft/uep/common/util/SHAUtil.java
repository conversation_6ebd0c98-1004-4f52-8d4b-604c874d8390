package com.cyberscraft.uep.common.util;

import org.apache.commons.lang3.StringUtils;

import java.nio.charset.Charset;
import java.security.MessageDigest;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/11/30 4:40 下午
 */
public class SHAUtil {

    /**
     * SHA 算法加密返回字节数组
     *
     * @param algorithm 算法名
     * @param data      待加密内容
     * @param charset   字符集
     * @return 加密结果字节数组
     * @throws Exception 异常
     */
    public static byte[] getSHABytes(String algorithm, String data, Charset charset) throws Exception {
        // SHA签名生成
        if (StringUtils.isBlank(algorithm)) {
            algorithm = "SHA-1";
        }
        MessageDigest md = MessageDigest.getInstance(algorithm);
        md.update(data.getBytes(charset));
        return md.digest();
    }

    public static byte[] getSHABytes(String algorithm, byte[] data) throws Exception {
        // SHA签名生成
        if (StringUtils.isBlank(algorithm)) {
            algorithm = "SHA-1";
        }
        MessageDigest md = MessageDigest.getInstance(algorithm);
        md.update(data);
        return md.digest();
    }

    /**
     * 将解密结果hex返回
     *
     * @param algorithm 算法
     * @param data      待加密内容
     * @param charset   字符集
     * @return 加密结果
     * @throws Exception 异常信息
     */
    public static String getSHA(String algorithm, String data, Charset charset) throws Exception {
        byte[] digest = getSHABytes(algorithm, data, charset);
        StringBuilder hexStr = new StringBuilder();
        String shaHex = "";
        for (byte b : digest) {
            shaHex = Integer.toHexString(b & 0xFF);
            if (shaHex.length() < 2) {
                hexStr.append(0);
            }
            hexStr.append(shaHex);
        }
        return hexStr.toString();
    }
}