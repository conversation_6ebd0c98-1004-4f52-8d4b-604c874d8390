package com.cyberscraft.uep.common.util;

import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.type.filter.AnnotationTypeFilter;
import org.springframework.core.type.filter.TypeFilter;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Controller;
import org.springframework.stereotype.Repository;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.lang.annotation.Annotation;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/***
 * 类扫描工具类,用于自定义扫描对应包下面的类列表
 * @date 2021/7/29
 * <AUTHOR>
 ***/
public class ClassScanUtil {

    /***
     * 获取获默认的需要定义成Bean，即可以进行自动注入的Annotion列表
     * @return
     */
    public static List<Class<? extends Annotation>> getDefaultBeanDefinitionAnnotion(){
        return Arrays.asList(Service.class,
                Component.class,
                Repository.class,
                Controller.class,
                Configuration.class,
                RestControllerAdvice.class,
                RestController.class);
    }

    /***
     *
     * @param clzz
     * @return
     */
    public static String[] getBasePackages(Class<?> clzz) {
        String[] basePackages = null;
        String basePackage = null;
        try {
            basePackage = Class.forName(clzz.getName()).getPackage().getName();
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        }
        // 2. 把包名填充到basePackages中
        basePackages = new String[]{basePackage};
        return basePackages;
    }

    /***
     * 扫描包下面的具有指定Annotation的类列表
     * @param basePackageClazz
     * @return
     */
    public static Set<String> scan(Class<?> basePackageClazz) {
        String[] basePackages = getBasePackages(basePackageClazz);
        return scan(basePackages, getDefaultBeanDefinitionAnnotion());
    }


    /***
     * 扫描包下面的具有指定Annotation的类列表
     * @param basePackageClazz
     * @param annotations
     * @return
     */
    public static Set<String> scan(Class<?> basePackageClazz, List<Class<? extends Annotation>> annotations) {
        String[] basePackages = getBasePackages(basePackageClazz);
        return scan(basePackages, annotations);
    }

    /***
     * 扫描包下面的具有指定Annotation的类列表
     * @param basePackages
     * @param annotations
     * @return
     */
    public static Set<String> scan(String[] basePackages, List<Class<? extends Annotation>> annotations) {
        // 3.创建类路径扫描器 false 表示 不使用默认扫描规则
        ClassPathScanningCandidateComponentProvider scan = new ClassPathScanningCandidateComponentProvider(false);
        for (Class<? extends Annotation> annotation : annotations) {
            TypeFilter annotionTypeFilter = new AnnotationTypeFilter(annotation);
            scan.addIncludeFilter(annotionTypeFilter);
        }
        final Set<String> classes = new HashSet<>();
        for (String aPackage : basePackages) {
            Set<BeanDefinition> beanDefinitions = scan.findCandidateComponents(aPackage);
            for (BeanDefinition beanDefinition : beanDefinitions) {
                classes.add(beanDefinition.getBeanClassName());
            }
        }
        return classes;
    }
}
