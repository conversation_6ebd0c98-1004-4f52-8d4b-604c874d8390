package com.cyberscraft.uep.common.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.MappedByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class MD5Util {
    private static final Logger log = LoggerFactory.getLogger(MD5Util.class);

    protected static char[] hexDigits = {'0', '1', '2', '3', '4', '5', '6',
            '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};

    private static final ThreadLocal<MessageDigest> messageDigest = new ThreadLocal<MessageDigest>() {
        @Override
        protected MessageDigest initialValue() {
            try {
                MessageDigest messagedigest = MessageDigest.getInstance("MD5");
                return messagedigest;
            } catch (NoSuchAlgorithmException e) {
                log.error(e.getMessage(), e);
            }
            return null;
        }
    };

    /**
     * md5算法
     */
    public static String md5(String string) {
        byte[] bytes = md5(string, StandardCharsets.UTF_8.name());
        //最后结果缓冲区
        StringBuilder buf = new StringBuilder(32);
        for (byte b : bytes) {
            buf.append(String.format("%02x", b & 0xff));
        }
        return buf.toString();
    }

    /**
     * md5算法
     *
     * @param string  待加密的字符串
     * @param charset 字符集
     * @return 加密结果 byte数组
     */
    public static byte[] md5(String string, String charset) {
        try {
            byte[] digest = messageDigest.get().digest(string.getBytes(charset));
            return digest;
        } catch (Exception e) {
            return null;
        }
    }

    public static byte[] md5(byte[] content) {
        try {
            byte[] digest = messageDigest.get().digest(content);
            return digest;
        } catch (Exception e) {
            return null;
        }
    }

    public static String getFileMD5String(File file) throws IOException {
        FileInputStream in = new FileInputStream(file);
        FileChannel ch = in.getChannel();
        MappedByteBuffer byteBuffer = ch.map(FileChannel.MapMode.READ_ONLY, 0, file.length());
        messageDigest.get().update(byteBuffer);
        return bufferToHex(messageDigest.get().digest());
    }

    private static String bufferToHex(byte[] bytes) {
        return bufferToHex(bytes, 0, bytes.length);
    }

    private static String bufferToHex(byte[] bytes, int m, int n) {
        StringBuffer stringbuffer = new StringBuffer(2 * n);
        int k = m + n;
        for (int l = m; l < k; l++) {
            appendHexPair(bytes[l], stringbuffer);
        }
        return stringbuffer.toString();
    }

    private static void appendHexPair(byte bt, StringBuffer stringbuffer) {
        char c0 = hexDigits[(bt & 0xf0) >> 4];
        char c1 = hexDigits[bt & 0xf];
        stringbuffer.append(c0);
        stringbuffer.append(c1);
    }

    /**
     * 检查两个字节数组是否相同
     *
     * @param src
     * @param dest
     * @return
     */
    public static boolean checkBA(byte[] src, byte[] dest) {
        if (src.length != dest.length) {
            return false;
        }
        for (int i = 0; i < src.length; i++) {
            if (src[i] != dest[i]) {
                return false;
            }
        }
        return true;
    }

    /**
     * MD5加密
     */
    public static byte[] digest(String text, String charset) {
        try {
            byte[] b = MessageDigest.getInstance("md5").digest(text.getBytes(charset));
            return b;
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }


    public static void main(String[] args) {
        log.info("MD5Util(\"admin\"): " + md5("admin"));
        log.info("MD5Util(\"admin123!@#\"): " + md5("admin123!@#"));
        log.info("MD5Util(\"auditor123!@#\"): " + md5("auditor123!@#"));
        log.info("MD5Util(\"123456\"): " + md5("123456"));

        try {
            File big = new File("F:\\temp\\cert\\Certisign Autoridade Certificadora AC1S.cer");
            String md5 = getFileMD5String(big);
            log.info("********" + md5);
        } catch (Exception e) {

        }

    }


}