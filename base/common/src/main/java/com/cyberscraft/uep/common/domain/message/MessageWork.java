package com.cyberscraft.uep.common.domain.message;

import java.time.LocalDateTime;

/***
 *
 * @date 2021/7/16
 * <AUTHOR>
 ***/
public class MessageWork extends ThirdMessageBody {

    /***
     * APP端打开 url
     */
    private String appUrl;

    /***
     * PC端打开url
     */
    private String pcUrl;

    /**
     * 结束时间
     */
    private Long dueTime;

    /**
     * 创建者ID 需要钉钉的unionId
     */
    private String createId;

    /**
     * 待办优先级
     */
    private Integer priority;

    /**
     * 内容描述
     */
    private String description;

    /***
     * 待办的创建时间
     */
    private LocalDateTime createTime;

    public String getAppUrl() {
        return appUrl;
    }

    public void setAppUrl(String appUrl) {
        this.appUrl = appUrl;
    }

    public String getPcUrl() {
        return pcUrl;
    }

    public void setPcUrl(String pcUrl) {
        this.pcUrl = pcUrl;
    }

    public Long getDueTime() {
        return dueTime;
    }

    public void setDueTime(Long dueTime) {
        this.dueTime = dueTime;
    }

    public String getCreateId() {
        return createId;
    }

    public void setCreateId(String createId) {
        this.createId = createId;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return "DingMessageWork{" +
                "appUrl='" + appUrl + '\'' +
                ", pcUrl='" + pcUrl + '\'' +
                ", dueTime=" + dueTime +
                ", createId='" + createId + '\'' +
                ", priority=" + priority +
                ", description='" + description + '\'' +
                ", createTime=" + createTime +
                '}';
    }
}
