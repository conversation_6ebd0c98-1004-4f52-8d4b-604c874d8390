package com.cyberscraft.uep.common.service;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/***
 * 从原来的platform及backend中提取而来，用于一些定时任务中，获取对应的Bean对像
 * @date 2021-07-20
 * <AUTHOR>
 ***/
@Component("serviceLocator")
public class ServiceLocator implements ApplicationContextAware {
    protected final static Log logger = LogFactory.getLog(ServiceLocator.class);

    // Spring应用上下文环境
    private static ApplicationContext applicationContext;

    /**
     * 实现ApplicationContextAware接口的回调方法，设置上下文环境
     */
    @Override
    public void setApplicationContext(ApplicationContext applicationContext)
            throws BeansException {
        ServiceLocator.applicationContext = applicationContext;
    }

    public static ApplicationContext getApplicationContext() {
        return applicationContext;
    }

    /**
     * 获取对象 这里重写了bean方法，起主要作用
     * @param beanId(通常就是Bean的类名首字母小写)
     *            一个以所给名字注册的bean的实例
     * @throws BeansException
     */
    public static Object getBean(String beanId) throws BeansException {
        return applicationContext.getBean(beanId);
    }

    /****
     * 获取对应的Bean
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T>T getBean( Class<T> clazz) {
        T bean = null;
        try{
            bean = applicationContext.getBean(clazz);
        }catch (BeansException ex){
            logger.error(ex);
        }
        return bean;
    }

    /***
     *
     * @param name
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T>T getBean(String name, Class<T> clazz) {
        T bean = null;
        try{
            bean = applicationContext.getBean(name, clazz);
        }catch (BeansException ex){
            logger.error(ex);
        }
        return bean;
    }

    /***
     *
     * @param name
     * @return
     */
    public static boolean containsBean(String name) {
        return applicationContext.containsBean(name);
    }

    /***
     *
     * @param name
     * @return
     */
    public static boolean isSingleton(String name){
        return applicationContext.isSingleton(name);
    }


}
