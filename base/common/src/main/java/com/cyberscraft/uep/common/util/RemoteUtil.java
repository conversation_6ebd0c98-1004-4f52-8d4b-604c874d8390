package com.cyberscraft.uep.common.util;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.math.BigInteger;
import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.UnknownHostException;
import java.util.Enumeration;
import java.util.List;

/***
 * 主要用于获取IP地址等相关功能
 * @date 2021-07-30
 * <AUTHOR>
 ***/
public class RemoteUtil {

    private final static Logger LOG = LoggerFactory.getLogger(RemoteUtil.class);

    /***
     * 得到系统中对应的第一个非环回地址对应的IP地址
     * @return
     */
    public static String getIpAddress() {

        return findFirstNonLoopbackAddress().getHostAddress();
    }


    public static InetAddress findFirstNonLoopbackAddress() {
        InetAddress result = null;
        try {
            int lowest = Integer.MAX_VALUE;
            for (Enumeration<NetworkInterface> nics = NetworkInterface
                    .getNetworkInterfaces(); nics.hasMoreElements(); ) {
                NetworkInterface ifc = nics.nextElement();
                if (ifc.isUp()) {
                    LOG.trace("Testing interface: " + ifc.getDisplayName());
                    if (ifc.getIndex() < lowest || result == null) {
                        lowest = ifc.getIndex();
                    } else if (result != null) {
                        continue;
                    }

                    for (Enumeration<InetAddress> addrs = ifc
                            .getInetAddresses(); addrs.hasMoreElements(); ) {
                        InetAddress address = addrs.nextElement();
                        if (address instanceof Inet4Address
                                && !address.isLoopbackAddress()) {
                            LOG.trace("Found non-loopback interface: "
                                    + ifc.getDisplayName());
                            result = address;
                        }
                    }
                    // @formatter:on
                }
            }
        } catch (IOException ex) {
            LOG.error("Cannot get first non-loopback address", ex);
        }

        if (result != null) {
            return result;
        }

        try {
            return InetAddress.getLocalHost();
        } catch (UnknownHostException e) {
            LOG.warn("Unable to retrieve localhost");
        }

        return null;
    }


    // 获取客户端 ip
    public static String getRealIpAddr(HttpServletRequest request) {
        String ipAddress = request.getHeader("x-forwarded-for");
        LOG.debug("**************** x-forwarded-for : {}", ipAddress);
        String ip = "";
        if (StringUtils.isNotBlank(ipAddress) && !"unknown".equalsIgnoreCase(ipAddress)) {
            String[] ips = ipAddress.split(",");
            if (ips.length > 2) {
                ip = ipAddress.split(",")[ips.length - 2].trim();
            } else {
                ip = ips[0].trim();
            }
            LOG.debug("**************** x-forwarded-for client ip : {}", ip);
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("x-real-ip");
            LOG.debug("**************** X-Real-IP : {}", ip);
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
            LOG.debug("**************** Proxy-Client-IP : {}", ip);
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
            LOG.debug("**************** WL-Proxy-Client-IP : {}", ip);
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
            LOG.debug("**************** query.getRemoteAddr() : {}", ip);
        }

        return ip;
    }

    /**
     * 判断ip是否在ip列表中
     *
     * @param ipList ip列表 "[\"***********\", \"***********-***********0\", \"***********/24\"]"
     * @param ip ip
     * @return true:在列表中，false:不在列表中
     */
    public static boolean isIpInList(List<String> ipList, String ip) {
        for (String entry : ipList) {
            if (isIpInRange(ip, entry)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断ip是否在ip范围中
     * @param ip ip
     * @param ipRange ip地址或ip段或CRID格式
     * @return true:在范围内，false:不在范围内
     */
    private static boolean isIpInRange(String ip, String ipRange) {
        // 处理特殊情况 0.0.0.0 和 ::/0，任何IP地址都在这些范围内
        if (ipRange.equals("0.0.0.0") || ipRange.equals("::/0")) {
            return true;
        }
        // IP地址精确匹配
        if (ip.equals(ipRange)) {
            return true;
        }

        // 判断是 IPv4 还是 IPv6 地址
        boolean isIPv4 = ip.contains(".") && ipRange.contains(".");
        boolean isIPv6 = ip.contains(":") && ipRange.contains(":");

        if (isIPv4) {
            if (ipRange.contains("-")) {
                // 处理 IPv4 普通IP地址范围
                String[] parts = ipRange.split("-");
                String startIp = parts[0];
                String endIp = parts[1];
                long start = ipToLong(startIp);
                long end = ipToLong(endIp);
                long target = ipToLong(ip);
                return start <= target && target <= end;
            } else if (ipRange.contains("/")) {
                // 处理 IPv4 CIDR表示法
                String[] parts = ipRange.split("/");
                String ipPart = parts[0];
                int cidr = Integer.parseInt(parts[1]);
                long subnetMask = (0xFFFFFFFFL << (32 - cidr)) & 0xFFFFFFFFL;
                long networkAddress = ipToLong(ipPart) & subnetMask;
                long target = ipToLong(ip) & subnetMask;
                return networkAddress == target;
            }
        } else if (isIPv6) {
            if (ipRange.contains("-")) {
                // 处理 IPv6 普通IP地址范围
                String[] parts = ipRange.split("-");
                String startIp = parts[0];
                String endIp = parts[1];
                BigInteger start = ipToBigInteger(startIp);
                BigInteger end = ipToBigInteger(endIp);
                BigInteger target = ipToBigInteger(ip);
                return start.compareTo(target) <= 0 && target.compareTo(end) <= 0;
            } else if (ipRange.contains("/")) {
                // 处理 IPv6 CIDR表示法
                String[] parts = ipRange.split("/");
                String ipPart = parts[0];
                int cidr = Integer.parseInt(parts[1]);
                BigInteger subnetMask = new BigInteger("2").pow(128).subtract(new BigInteger("2").pow(128 - cidr));
                BigInteger networkAddress = ipToBigInteger(ipPart).and(subnetMask);
                BigInteger target = ipToBigInteger(ip).and(subnetMask);
                return networkAddress.equals(target);
            }
        }
        return false;
    }

    /**
     * 将 IPv4 地址转换为 long 类型
     * @param ipAddress IPv4 地址
     * @return long 类型的 IPv4 地址
     */
    private static long ipToLong(String ipAddress) {
        try {
            byte[] bytes = InetAddress.getByName(ipAddress).getAddress();
            long result = 0;
            for (byte b : bytes) {
                result = (result << 8) | (b & 0xFF);
            }
            return result;
        } catch (UnknownHostException e) {
            throw new IllegalArgumentException("Invalid IP address", e);
        }
    }

    /**
     * 将 IPv6 地址转换为 BigInteger 类型
     * @param ipAddress IPv6 地址
     * @return BigInteger 类型的 IPv6 地址
     */
    private static BigInteger ipToBigInteger(String ipAddress) {
        try {
            byte[] bytes = InetAddress.getByName(ipAddress).getAddress();
            BigInteger result = BigInteger.ZERO;
            for (byte b : bytes) {
                result = result.shiftLeft(8).add(BigInteger.valueOf((b & 0xFF)));
            }
            return result;
        } catch (UnknownHostException e) {
            throw new IllegalArgumentException("Invalid IP address", e);
        }
    }


    public static void main(String[] args) {
        String ips = "[\"***********\", \"***********-***********0\", \"***********/24\", \"fd00::1\", \"fd00::2-fd00::10\", \"fd00::/8\", \"2001:0db8:85a3:0000:0000:8a2e:0370:7334\", \"2001:db8::1\", \"2001:4860:4860::8888\"]";
        List<String> ipList = JsonUtil.str2List(ips, String.class);

        String ip1 = "***********";
        String ip2 = "***********";
        String ip3 = "*************";
        String ipv6_1 = "fd00::1";
        String ipv6_2 = "fd00::5";
        String ipv6_3 = "fd00::115";
        String ipv6_public_1 = "2001:0db8:85a3:0000:0000:8a2e:0370:7334";
        String ipv6_public_2 = "2001:db8::1";
        String ipv6_public_3 = "2001:4860:4860::8888";

        boolean result1 = CollectionUtils.isNotEmpty(ipList) && isIpInList(ipList, ip1);
        boolean result2 = CollectionUtils.isNotEmpty(ipList) && isIpInList(ipList, ip2);
        boolean result3 = CollectionUtils.isNotEmpty(ipList) && isIpInList(ipList, ip3);
        boolean result4 = CollectionUtils.isNotEmpty(ipList) && isIpInList(ipList, ipv6_1);
        boolean result5 = CollectionUtils.isNotEmpty(ipList) && isIpInList(ipList, ipv6_2);
        boolean result6 = CollectionUtils.isNotEmpty(ipList) && isIpInList(ipList, ipv6_3);
        boolean result7 = CollectionUtils.isNotEmpty(ipList) && isIpInList(ipList, ipv6_public_1);
        boolean result8 = CollectionUtils.isNotEmpty(ipList) && isIpInList(ipList, ipv6_public_2);
        boolean result9 = CollectionUtils.isNotEmpty(ipList) && isIpInList(ipList, ipv6_public_3);

        System.out.println(result1); // 输出 true
        System.out.println(result2); // 输出 true
        System.out.println(result3); // 输出 true
        System.out.println(result4); // 输出 true
        System.out.println(result5); // 输出 true
        System.out.println(result6); // 输出 true
        System.out.println(result7); // 输出 true
        System.out.println(result8); // 输出 true
        System.out.println(result9); // 输出 true
    }

}
