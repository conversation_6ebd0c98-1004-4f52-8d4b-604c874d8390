package com.cyberscraft.uep.common.domain.auth;

import com.cyberscraft.uep.common.util.WebUrlUtil;

import java.io.Serializable;

/**
 * 用友U8C连接器配置参数
 *
 * <AUTHOR>
 * @date 2023年11月23日
 */
public class Yyu8cParam extends Account implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 服务器地址
     */
    private String host;

    /**
     * 系统编码
     */
    private String system;

    /**
     * 用户名
     */
    private String usercode;

    /**
     * 密码
     */
    private String password;

    public String getSystem() {
        return system;
    }

    public void setSystem(String system) {
        this.system = system;
    }

    public String getUsercode() {
        return usercode;
    }

    public void setUsercode(String usercode) {
        this.usercode = usercode;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    @Override
    public String getRealIp() {
        return WebUrlUtil.getHost(host);
    }
}
