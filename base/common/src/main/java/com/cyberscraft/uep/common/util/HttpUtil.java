package com.cyberscraft.uep.common.util;

import nl.basjes.parse.useragent.UserAgent;
import nl.basjes.parse.useragent.UserAgentAnalyzer;
import org.apache.http.NameValuePair;
import org.apache.http.client.utils.URLEncodedUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.DispatcherType;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.Charset;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class HttpUtil {

    private static Logger logger = LoggerFactory.getLogger(HttpUtil.class);


    private static final String REQUEST_PREFIX = ">>";
    private static final String RESPONSE_PREFIX = "<<";
    private static final String X_REQUEST_ID = "Request-Id";
    private static final String X_RESPONSE_ID = "Response-Id";
    private static final String MESSAGE_LINE = "------------------------------------------------------------------------------------";

    private static final String OS_NAME_VERSION = "OperatingSystemNameVersion";
    private static final String UA_NAME_VERSION = "AgentNameVersion";

    private static UserAgentAnalyzer userAgentAnalyzer = UserAgentAnalyzer.newBuilder()
            .hideMatcherLoadStats()
            .withField(UserAgent.DEVICE_NAME)
            .withField(OS_NAME_VERSION)
            .withField(UA_NAME_VERSION)
            .withField(UserAgent.OPERATING_SYSTEM_CLASS)
            .withField(UserAgent.AGENT_CLASS)
            .build();
    //    private static UserAgentAnalyzer userAgentAnalyzer;
    public static class RemoteHostInfo {
        public String ip;
        public String deviceModel;
        public String userAgent;
    }

    public static boolean currentRequestIsAsyncDispatcher(HttpServletRequest httpServletRequest) {
        return httpServletRequest.getDispatcherType().equals(DispatcherType.ASYNC);
    }

    public static void logRequestHeader(String requestId, HttpServletRequest request) {

        try {

            logger.debug(MESSAGE_LINE);

            // log request Id
            logger.debug("{}: {}", X_REQUEST_ID, requestId);

            // log request path
            logger.debug("{} {} {}", REQUEST_PREFIX, request.getMethod(), request.getRequestURI());

            // log http headers
            String name;
            String value;
            Enumeration<String> names = request.getHeaderNames();
            while (names.hasMoreElements()) {
                name = names.nextElement();
                value = request.getHeader(name);
                logger.debug("{} {} : {}", REQUEST_PREFIX, name, value);
            }
        } catch (Exception e) {
            // ignore any exception for logging
        }
    }

    public static void logResponseHeader(String requestId, HttpServletRequest request, HttpServletResponse response) {

        try {

            logger.debug(MESSAGE_LINE);

            // log request Id
            logger.debug("{}: {}", X_RESPONSE_ID, requestId);

            // log http resonse status
            logger.debug("{} {} {}", RESPONSE_PREFIX, response.getStatus(), request.getRequestURI());

            // log http headers
            response.getHeaderNames().forEach(n -> {
                logger.debug("{} {} : {}", RESPONSE_PREFIX, n, response.getHeader(n));
            });

        } catch (Exception e) {
            // ignore any exception for logging
        }
    }

    public static void logRequestContent(String contentStr) {
        logger.debug("{} {}", REQUEST_PREFIX, contentStr);
        logger.debug(MESSAGE_LINE);
    }

    public static void logResponseContent(String contentStr) {
        logger.debug("{} {}", RESPONSE_PREFIX, contentStr);
        logger.debug(MESSAGE_LINE);
    }
    private static String getRemoteDeviceModel(HttpServletRequest request) {
        String userAgent = request.getHeader("user-agent");
        logger.debug("=========================================================");
        logger.debug("User-Agent: " + userAgent);
        long begin = System.currentTimeMillis();
        UserAgent agent = userAgentAnalyzer.parse(userAgent);
        long end = System.currentTimeMillis();
        logger.info("{} took {} ms", "UserAgentAnalyzer", (end - begin));

        for (String fieldName: agent.getAvailableFieldNamesSorted()) {
            logger.info(fieldName + " = " + agent.getValue(fieldName));
        }
        logger.debug("=========================================================");
        String deviceModel = agent.getValue(UserAgent.DEVICE_NAME) + " " + agent.getValue(UserAgent.OPERATING_SYSTEM_VERSION);

        return deviceModel;
    }
    public static RemoteHostInfo getRemoteHostInfoFromContext() {
        HttpServletRequest request = null;
        try {
            request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
        }catch (IllegalStateException e) {
            logger.info("getRemoteHostInfoFromContext: Fail to get current request attributes (IllegalStateException), ignore.");
        }
        catch (Exception e) {
            logger.info("getRemoteHostInfoFromContext: Fail to get current request attributes, ignore.");
        }

        if (request != null) {
            return getRemoteHostInfo(request);
        }
        return null;
    }
    public static RemoteHostInfo getRemoteHostInfo(HttpServletRequest request) {
        RemoteHostInfo remoteHostInfo = new RemoteHostInfo();
        remoteHostInfo.ip = getRemoteHost(request);

        String userAgent = request.getHeader("user-agent");
        logger.debug("getRemoteHostInfo: User-Agent: " + userAgent);
        long begin = System.currentTimeMillis();
        UserAgent agent = userAgentAnalyzer.parse(userAgent);
        long end = System.currentTimeMillis();
        logger.debug("getRemoteHostInfo: {} took {} ms", "UserAgentAnalyzer", (end - begin));

        String deviceModel = agent.getValue(UserAgent.DEVICE_NAME) + "/" + agent.getValue(OS_NAME_VERSION);
        remoteHostInfo.deviceModel = deviceModel;
        remoteHostInfo.userAgent = agent.getValue(UA_NAME_VERSION);

        return remoteHostInfo;
    }

    public static String getRemoteDeviceModel() {
        HttpServletRequest request = null;
        try {
            request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
        }catch (IllegalStateException e) {
            logger.info("getRemoteDeviceModel: Fail to get current request attributes (IllegalStateException), ignore.");
        }
        catch (Exception e) {
            logger.info("getRemoteDeviceModel: Fail to get current request attributes, ignore.");
        }
        if (request != null) {
            return getRemoteDeviceModel(request);
        }
        return "Unknown";
    }

    public static UserAgent getRemoteUserAgent() {
        HttpServletRequest request = null;
        try {
            request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
        }catch (IllegalStateException e) {
            logger.info("getRemoteDeviceModel: Fail to get current request attributes (IllegalStateException), ignore.");
        }
        catch (Exception e) {
            logger.info("getRemoteDeviceModel: Fail to get current request attributes, ignore.");
        }
        if (request != null) {
            return getRemoteUserAgent(request);
        }
        return null;
    }

    private static UserAgent getRemoteUserAgent(HttpServletRequest request) {
        String userAgent = request.getHeader("user-agent");
        logger.debug("=========================================================");
        logger.debug("User-Agent: " + userAgent);
        long begin = System.currentTimeMillis();
        UserAgent agent = userAgentAnalyzer.parse(userAgent);
        long end = System.currentTimeMillis();
        logger.info("{} took {} ms", "UserAgentAnalyzer", (end - begin));

        for (String fieldName: agent.getAvailableFieldNamesSorted()) {
            logger.info(fieldName + " = " + agent.getValue(fieldName));
        }
        logger.debug("=========================================================");
        return agent;
    }

    /**
     * best effort to get http(s) request scheme
     */
    public static String getScheme(HttpServletRequest req) {
        return useHeaderValueOrDefault(req, "x-forwarded-proto", req.getScheme());
    }

    /**
     * best effort to get remote server FQDN
     */
    public static String getServerName(HttpServletRequest req) {
        return useHeaderValueOrDefault(req, "x-forwarded-host", req.getServerName()).split(":", 2)[0];
    }

    public static String getOrigin(HttpServletRequest req) {
        String scheme = HttpUtil.getScheme(req);
        String serverName = HttpUtil.getServerName(req);
        int serverPort = HttpUtil.getServerPort(req);

        boolean withPort = true;
        if ("http".equalsIgnoreCase(scheme) && 80 == serverPort) {
            withPort = false;
        } else if ("https".equalsIgnoreCase(scheme) && 443 == serverPort) {
            withPort = false;
        }

        String origin = scheme.toLowerCase() + "://" + serverName;
        if (withPort) {
            origin = origin + ":" + serverPort;
        }
        return origin;
    }

    public static String getOriginNoSchema(HttpServletRequest req) {
        String scheme = HttpUtil.getScheme(req);
        String serverName = HttpUtil.getServerName(req);
        int serverPort = HttpUtil.getServerPort(req);

        boolean withPort = true;
        if ("http".equalsIgnoreCase(scheme) && 80 == serverPort) {
            withPort = false;
        } else if ("https".equalsIgnoreCase(scheme) && 443 == serverPort) {
            withPort = false;
        }

        String origin =  serverName;
        if (withPort) {
            origin = origin + ":" + serverPort;
        }
        return origin;
    }

    /**
     * best effort to get server port
     */
    public static int getServerPort(HttpServletRequest req) {
        /**
         * debug: dump request headers

         logger.info("req.getServerPort(): {}", req.getServerPort());
         logger.info("x-forwarded-port: {}",  req.getHeader("x-forwarded-port"));
         Enumeration<String> names = req.getHeaderNames();
         for(String n =names.nextElement(); names.hasMoreElements(); n = names.nextElement()) {
         logger.info("Header[{}]:{}", n, req.getHeader(n));
         }

         logger.info("req.getRequestURI():{}", req.getRequestURI());
         logger.info("req.getRequestURL():{}", req.getRequestURL().toString());
         */

        /**
         * try to get correct port in the order of:
         * 1. x-forwarded-port
         * 2. x-forwarded-host
         * 3. host Header
         */
        String portStr = useHeaderValueOrDefault(req,"x-forwarded-port", null);
        if(StringUtil.isEmptyOrNull(portStr)){
            String[] xHostArray = useHeaderValueOrDefault(req,"x-forwarded-host", "").split(":", 2);
            portStr = xHostArray.length>1 ? xHostArray[1] : "";
        }

        if (StringUtil.isEmptyOrNull(portStr)) {
            //get port from host header
            String[] hostArray = req.getHeader("host").split(":", 2);
            portStr = hostArray.length>1 ? hostArray[1] : "";
        }

        if(!StringUtil.isEmptyOrNull(portStr) && portStr.indexOf(",") > 0){
            // This case face to multi ports got from x-forwarded-port.
            // ex. 80,443.
            String[] portArray = portStr.split(",");
            if(portArray.length > 0) portStr = portArray[0];
        }

        if (StringUtil.isEmptyOrNull(portStr)) {
            return req.getServerPort();
        }
        return Integer.parseInt(portStr);
    }

    /**
     * try to use the value of specified header, if case the header is null, use defaultValue.
     */
    private static String useHeaderValueOrDefault(HttpServletRequest req, String headerName, String defaultValue) {
        String headerValue = req.getHeader(headerName);
        //logger.debug("headerValue=>{},defalutValue=>{}",headerValue,defaultValue);
        return (headerValue != null) ? headerValue : defaultValue;
    }

    public static ServerContext getServerContext(final HttpServletRequest request) {
        return ServerContext.of(request);
    }

    public static Map<String, String> getParamsMap(String urlString) {
        Map<String, String> paramsMap = new HashMap<>();
        String enc = "utf-8";
        List<NameValuePair> nameValuePairs = URLEncodedUtils.parse(urlString, Charset.forName(enc));
        for(NameValuePair nameValuePair: nameValuePairs) {
            paramsMap.put(nameValuePair.getName(), nameValuePair.getValue());
        }
        return paramsMap;
    }

    public static String getRemoteHost(HttpServletRequest request) {
        if(logger.isDebugEnabled()) {
            String scheme = request.getHeader("x-forwarded-proto");
            logger.debug("Header x-forwarded-proto: " + scheme);

            String forwardedHost = request.getHeader( "x-forwarded-host");
            logger.debug("Header x-forwarded-host: " + forwardedHost);

            String host = request.getHeader( "host");
            logger.debug("Header host: " + host);

            String port = request.getHeader( "x-forwarded-port");
            logger.debug("Header x-forwarded-port: " + port);

            String xRealIP = request.getHeader("X-Real-IP");
            logger.debug("Header X-Real-IP: " + xRealIP);

            String headerXForwordedFor = request.getHeader("X-Forwarded-For");
            logger.debug("Header X-Forwarded-For: " + headerXForwordedFor);

            String proxyClientIP = request.getHeader("Proxy-Client-IP");
            logger.debug("Header Proxy-Client-IP: " + proxyClientIP);
        }

        String remoteIP = request.getHeader("X-Real-IP");
        if(StringUtil.isEmptyOrNull(remoteIP) || "unknown".equalsIgnoreCase(remoteIP)){
            remoteIP = request.getHeader("Proxy-Client-IP");
        }

        if(StringUtil.isEmptyOrNull(remoteIP) || "unknown".equalsIgnoreCase(remoteIP)){
            remoteIP = request.getHeader("X-Forwarded-For");
        }

        if(StringUtil.isEmptyOrNull(remoteIP) || "unknown".equalsIgnoreCase(remoteIP)){
            remoteIP = request.getRemoteAddr();
        }

        remoteIP = remoteIP.split(",")[0].trim();

        return remoteIP.equals("0:0:0:0:0:0:0:1")?"127.0.0.1":remoteIP;
    }


}
