package com.cyberscraft.uep.common.domain.message;

import java.util.List;

public class MessageActionCard extends ThirdMessageBody {

    private static final long serialVersionUID = 8131771691419933656L;

    private String singleTitle;

    private String markDown;

    private String singleUrl;

    private List<BtnJsonList> btnJsonList;

    private String btnOrientation;

    public String getSingleTitle() {
        return singleTitle;
    }

    public void setSingleTitle(String singleTitle) {
        this.singleTitle = singleTitle;
    }

    public String getMarkDown() {
        return markDown;
    }

    public void setMarkDown(String markDown) {
        this.markDown = markDown;
    }

    public String getSingleUrl() {
        return singleUrl;
    }

    public void setSingleUrl(String singleUrl) {
        this.singleUrl = singleUrl;
    }

    public List<BtnJsonList> getBtnJsonList() {
        return btnJsonList;
    }

    public void setBtnJsonList(List<BtnJsonList> btnJsonList) {
        this.btnJsonList = btnJsonList;
    }

    public String getBtnOrientation() {
        return btnOrientation;
    }

    public void setBtnOrientation(String btnOrientation) {
        this.btnOrientation = btnOrientation;
    }

    public static class BtnJsonList {

        private static final long serialVersionUID = 8131771691219933656L;

        private String actionUrl;

        private String title;

        public String getActionUrl() {
            return actionUrl;
        }

        public void setActionUrl(String actionUrl) {
            this.actionUrl = actionUrl;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }
    }
}
