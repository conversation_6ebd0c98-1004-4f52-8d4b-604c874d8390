package com.cyberscraft.uep.common.dto;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

import static java.util.stream.Collectors.toList;

/**
 * 分页 QueryPage 对象接口
 *
 * <AUTHOR>
 * @since 2018-06-09
 */
public interface IQueryPage<T> extends Serializable {
    /**
     * 获取排序信息，排序的字段和正反序
     *
     * @return 排序信息
     */
    List<QueryOrderItem> orders();

    /**
     * KEY/VALUE 条件
     *
     * @return ignore
     */
    default Map<Object, Object> condition() {
        return null;
    }

    /**
     * 自动优化 COUNT SQL【 默认：true 】
     *
     * @return true 是 / false 否
     */
    default boolean optimizeCountSql() {
        return true;
    }

    /**
     * 进行 count 查询 【 默认: true 】
     *
     * @return true 是 / false 否
     */
    default boolean isSearchCount() {
        return true;
    }

    /**
     * 计算当前分页偏移量
     */
    default long offset() {
        return getPage() > 0 ? (getPage() - 1) * getSize() : 0;
    }

    /**
     * 当前分页总页数
     */
    default long getPages() {
        if (getSize() == 0) {
            return 0L;
        }
        long pages = getTotal() / getSize();
        if (getTotal() % getSize() != 0) {
            pages++;
        }
        return pages;
    }

    /**
     * 内部什么也不干
     * <p>只是为了 json 反序列化时不报错</p>
     */
    default IQueryPage<T> setPages(long pages) {
        // to do nothing
        return this;
    }

    /**
     * 分页记录列表
     *
     * @return 分页对象记录列表
     */
    List<T> getItems();

    /**
     * 设置分页记录列表
     */
    IQueryPage<T> setItems(List<T> items);

    /**
     * 当前满足条件总行数
     *
     * @return 总条数
     */
    long getTotal();

    /**
     * 设置当前满足条件总行数
     */
    IQueryPage<T> setTotal(long total);

    /**
     * 当前分页总页数
     *
     * @return 总页数
     */
    long getSize();

    /**
     * 设置当前分页总页数
     */
    IQueryPage<T> setSize(long size);

    /**
     * 当前页，默认 1
     *
     * @return 当前页
     */
    long getPage();

    /**
     * 设置当前页
     */
    IQueryPage<T> setPage(long page);

    /**
     * IPage 的泛型转换
     *
     * @param mapper 转换函数
     * @param <R>    转换后的泛型
     * @return 转换泛型后的 IPage
     */
    @SuppressWarnings("unchecked")
    default <R> IQueryPage<R> convert(Function<? super T, ? extends R> mapper) {
        List<R> collect = this.getItems().stream().map(mapper).collect(toList());
        return ((IQueryPage<R>) this).setItems(collect);
    }
}