package com.cyberscraft.uep.common.util;

import com.cyberscraft.uep.common.domain.DataBaseType;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.expression.CaseExpression;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.*;
import net.sf.jsqlparser.util.TablesNamesFinder;
import net.sf.jsqlparser.util.deparser.SelectDeParser;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/***
 * SQL相关的工具类
 * @date 2021-10-12
 * <AUTHOR>
 ***/
public class SqlUtil {

    private static final Pattern pattern = Pattern.compile("(?<!['\"])\\B(:\\w+)(?![^'\"]*['\"][^'\"]*(?:['\"][^'\"]*['\"][^'\"]*)*$)");

    private static final String START_STR = "select";
    private static final String END_STR = "from";
    private static final String WHERE = "\\bWHERE\\b";
    private static final String CHINESE_PATTERN = "[\\u4E00-\\u9FA5]+";

    /***
     *
     * @param ids
     * @return
     */
    public static String getInSqlValues(String ids) {
        String[] temp = ids.split(",");
        StringBuilder sb = new StringBuilder();
        for (String val : temp) {
            if (StringUtils.isBlank(val)) {
                continue;
            }
            val = val.replace("'", "").trim();
            if (StringUtils.isBlank(val)) {
                continue;
            }
            if (sb.length() > 0) {
                sb.append(",");
            }
            sb.append("'").append(val).append("'");
        }
        return sb.toString();
    }


    /****
     *
     * @param ids
     * @return
     */
    public static <T extends Serializable> String getInSqlValues(List<T> ids) {
        StringBuilder sb = new StringBuilder();
        for (T val : ids) {
            if (val == null) {
                continue;
            }
            String tVal = String.valueOf(val);
            if (sb.length() > 0) {
                sb.append(",");
            }
            sb.append("'").append(tVal).append("'");
        }
        return sb.toString();
    }

    /****
     *
     * @param ids
     * @return
     */
    public static <T extends Serializable> String getInSqlValues(Collection<T> ids) {
        StringBuilder sb = new StringBuilder();
        for (Serializable val : ids) {
            if (val == null) {
                continue;
            }
            String tVal = String.valueOf(val);
            if (sb.length() > 0) {
                sb.append(",");
            }
            sb.append("'").append(tVal).append("'");
        }
        return sb.toString();
    }

    /**
     * 解析sql 获取执行时需要的参数
     *
     * @param sql sql 语句
     * @return
     */
    public static Map<Integer, Object> parseSqlParams(String sql) {
        Matcher matcher = pattern.matcher(sql);
        int i = 0;
        Map<Integer, Object> result = new HashMap<>();
        while (matcher.find()) {
            i++;
            result.put(i, matcher.group(1));
        }
        return result;
    }

    /**
     * 检测 sql 是否需要解析
     *
     * @param sql sql 语句
     * @return
     */
    public static boolean needParser(String sql) {
        Matcher matcher = pattern.matcher(sql);
        while (matcher.find()) {
            return true;
        }
        return false;
    }

    /**
     * 解析sql中的参数
     *
     * @param sql 预编译sql
     * @return 参数集合
     */
    public static Map<String, List<String>> parseSqlParamsValues(String sql) {
        Matcher matcher = pattern.matcher(sql);
        List<String> collect = new ArrayList<>();
        Map<String, List<String>> result = new HashMap<>();
        while (matcher.find()) {
            collect.add(matcher.group(1));
        }
        sql = sql.toLowerCase();

        int startIndex = sql.indexOf(START_STR);
        int endIndex = sql.indexOf(END_STR);
        if (startIndex != -1 && endIndex != -1) {
            startIndex += START_STR.length();
            sql = sql.substring(startIndex, endIndex);
        }
        result.put("input_params", collect);
        result.put("output_params", Arrays.asList(sql.trim().split(",")));
        return result;
    }

    /**
     * @param sql    预编译的sql字符串
     * @param params 需要替换的参数
     * @param result 最终sql执行时需要的参数
     * @return 替换之后sql
     */
    public static String parseAndReplace(String sql, Map<String, Object> params, Map<Integer, Object> result) {
        Matcher matcher = pattern.matcher(sql);
        int index = 0;
        while (matcher.find()) {
            String key = matcher.group(1).substring(1);
            Object value = params.get(key);
            if (value instanceof Collection) {
                int size = ((List<?>) value).size();
                String charString = createReplaceStr(" ? ", size);
                sql = sql.replaceFirst(matcher.group(), charString);
                for (int j = 0; j < size; j++) {
                    index++;
                    result.put(index, ((List<?>) value).get(j));
                }
            } else {
                sql = sql.replaceFirst(matcher.group(), " ? ");
                index++;
                result.put(index, value);
            }
        }
        return sql;
    }

    /**
     * 生成制定字符和长度的字符串
     *
     * @param str    字符串包含的字符
     * @param length 长度
     * @return
     */
    private static String createReplaceStr(String str, int length) {
        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < length - 1; i++) {
            stringBuilder.append(str).append(",");
        }
        stringBuilder.append(str);
        return stringBuilder.toString();

    }

    public static List<String> getColumnList(String sql) throws JSQLParserException {
        List<String> columns = new ArrayList<>();

        Select selectStatement = (Select) CCJSqlParserUtil.parse(sql);

        selectStatement.getSelectBody().accept(new SelectDeParser() {
            @Override
            public void visit(SelectExpressionItem item) {
                columns.add(item.toString());
                super.visit(item);
            }
        });

        return columns;
    }

    /**
     * 检查sql 查询的属性的别名 是否包含中文
     *
     * @param sql sql语句
     * @return
     * @throws JSQLParserException
     */
    public static Boolean checkSqlForChineseCharacters(String sql) throws JSQLParserException {
        List<String> columnList = getColumnList(sql);
        for (String column : columnList) {
            String[] split = column.split(" AS ", 2);
            if (split.length == 2) {
                column = split[1];
            } else {
                column = split[0];
            }
            if (column.matches(".*" + CHINESE_PATTERN + ".*")) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断sql是否存在where
     *
     * @param sql
     * @return
     */
    public static boolean isExistWhere(String sql) {
        Pattern pattern = Pattern.compile(WHERE, Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(sql);
        return matcher.find();
    }

    /**
     * sql 语句是否包含 条件where 语句
     *
     * @param sql
     * @return
     */
    public static boolean hasWhereCondition(String sql) {
        try {
            Statement statement = CCJSqlParserUtil.parse(sql);
            if (statement instanceof Select) {
                Select selectStatement = (Select) statement;
                SelectBody selectBody = selectStatement.getSelectBody();

                return hasWhereCondition(selectBody);
            }
        } catch (JSQLParserException e) {
            e.printStackTrace();
        }
        return false;
    }

    private static boolean hasWhereCondition(SelectBody selectBody) {
        if (selectBody instanceof PlainSelect) {
            PlainSelect plainSelect = (PlainSelect) selectBody;
            if (plainSelect.getWhere() != null) {
                return true;
            }
        }
        return false;
    }

    /**
     * 移除 sql 语句中的 Limit
     *
     * @param sql
     * @return
     * @throws JSQLParserException
     */
    public static String removeLimit(String sql) throws JSQLParserException {
        Statement statement = CCJSqlParserUtil.parse(sql);
        if (statement instanceof Select) {
            Select selectStatement = (Select) statement;
            SelectBody selectBody = selectStatement.getSelectBody();
            if (selectBody instanceof PlainSelect) {
                PlainSelect plainSelect = (PlainSelect) selectBody;
                plainSelect.setLimit(null);
            }
        }
        return statement.toString();
    }


    /**
     * 将sql中的sql关键字转为大写
     *
     * @param sql sql语句
     * @return 转换结果
     */
    public static String convertSqlKeywordsToUpper(String sql) {
        String[] keywords = {
                "select", "as", "from", "where", "and", "or", "insert", "update", "delete",
                "create", "alter", "drop", "set", "join", "inner", "left", "right", "full",
                "on", "order", "group", "by", "having", "distinct", "limit", "between", "like"
        };

        String patternString = "\\b(" + String.join("|", keywords) + ")\\b";
        Pattern pattern = Pattern.compile(patternString, Pattern.CASE_INSENSITIVE);

        Matcher matcher = pattern.matcher(sql);
        StringBuffer buffer = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(buffer, matcher.group(1).toUpperCase());
        }
        matcher.appendTail(buffer);

        return buffer.toString();
    }

    /**
     * 将sql 语句去除所有的查询条件
     *
     * @param sql
     * @return
     * @throws JSQLParserException
     */
    public static String removeConditions(String sql) throws JSQLParserException {
        Statement statement = CCJSqlParserUtil.parse(sql);
        if (statement instanceof Select) {
            Select selectStatement = (Select) statement;
            SelectBody selectBody = selectStatement.getSelectBody();
            if (selectBody instanceof PlainSelect) {
                PlainSelect plainSelect = (PlainSelect) selectBody;
                // 移除WHERE子句
                plainSelect.setWhere(null);
                // 移除HAVING子句
                if (plainSelect.getHaving() != null) {
                    plainSelect.setHaving(null);
                }
            }
        }
        return statement.toString();
    }

    /**
     * 获取sql 语句中的查询的内容信息
     *
     * @param sql
     * @return
     * @throws JSQLParserException
     */
    public static List<ColumnInfo> extractColumnInfo(String sql, boolean columnTrs) throws JSQLParserException {
        Statement statement = CCJSqlParserUtil.parse(sql);
        Select selectStatement = (Select) statement;
        SelectBody selectBody = selectStatement.getSelectBody();

        List<ColumnInfo> columnInfoList = new ArrayList<>();
        selectBody.accept(new SelectVisitorAdapter() {
            @Override
            public void visit(PlainSelect plainSelect) {
                List<SelectItem> selectItems = plainSelect.getSelectItems();
                int index = 1;
                for (int i = 0; i < selectItems.size(); i++) {
                    SelectItem selectItem = selectItems.get(i);
                    int finalIndex = index;
                    selectItem.accept(new SelectItemVisitorAdapter() {
                        @Override
                        public void visit(SelectExpressionItem selectExpressionItem) {
                            Expression expression = selectExpressionItem.getExpression();
                            String alias = null;
                            if (selectExpressionItem.getAlias() != null) {
                                alias = selectExpressionItem.getAlias().getName();
                            }
                            ColumnInfo columnInfo = new ColumnInfo();
                            if (expression instanceof Column) {
                                Column column = (Column) expression;
                                ConstructColumnInfo(alias, columnInfo, column, columnTrs);
                            } else if (expression instanceof CaseExpression) {
                                CaseExpression caseExpression = (CaseExpression) expression;
                                if (caseExpression.getSwitchExpression() instanceof Column) {
                                    Column column = (Column) caseExpression.getSwitchExpression();
                                    ConstructColumnInfo(alias, columnInfo, column, columnTrs);
                                }
                            } else {
                                columnInfo.alias = alias;
                                if (null != alias) {
                                    columnInfo.field = alias;
                                }
                            }
                            columnInfo.order = finalIndex;
                            columnInfoList.add(columnInfo);
                        }
                    });
                    index++;
                }
            }
        });

        return columnInfoList;
    }

    public static List<Map<String, Object>> getResult(Connection conn, DataBaseType dataBaseType, String sql, String oldSql, Boolean isUser) throws SQLException, JSQLParserException {
        java.sql.Statement stmt = null;
        try {
            List<Map<String, Object>> results = new ArrayList<>();
            stmt = conn.createStatement();
            ResultSet resultSet = stmt.executeQuery(sql);
            ResultSetMetaData metaData = resultSet.getMetaData();
            List<SqlUtil.ColumnInfo> columnInfos = null;
            if (isUser) {
                columnInfos = SqlUtil.extractColumnInfo(oldSql, false);
            } else {
                columnInfos = SqlUtil.extractColumnInfo(oldSql, false);
            }
            int numberOfColumns = metaData.getColumnCount();
            if (isUser && (DataBaseType.ORACLE.equals(dataBaseType) || DataBaseType.DB2.equals(dataBaseType))) {
                // 这里-- 是因为oracle 类型的数据会多查询出有一个排序字段这个字段不需要 所以计数-1
                numberOfColumns--;
            }

            while (resultSet.next()) {
                Map<String, Object> item = new HashMap<>();
                // 每一行中的每一列
                for (int i = 1; i <= numberOfColumns; i++) {
                    SqlUtil.ColumnInfo columnInfo = columnInfos.get(i - 1);
                    if (StringUtils.isBlank(columnInfo.alias)) {
                        item.put(columnInfo.field, resultSet.getString(columnInfo.columnName));
                    } else {
                        item.put(columnInfo.alias, resultSet.getString(columnInfo.alias));
                    }
                }
                results.add(item);
            }
            return results;
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            if (stmt != null) {
                stmt.close();
            }
        }
    }

    private static void ConstructColumnInfo(String alias, ColumnInfo columnInfo, Column column, boolean columnTrs) {
        columnInfo.columnName = column.getColumnName();
        columnInfo.alias = alias;
        if (column.getTable() != null) {
            columnInfo.table = column.getTable().getName();
        }
        columnInfo.tabAndColumn = column.toString();
        if (alias == null) {
            if (columnTrs) {
                columnInfo.field = StringUtils.isNotBlank(columnInfo.table) ? columnInfo.table + "_" + columnInfo.columnName : columnInfo.columnName;
            } else {
                columnInfo.field = column.getColumnName();
            }
        } else {
            columnInfo.field = alias;

        }
    }

    /**
     * 获取sql语句里面的子查询
     *
     * @param sql
     * @return
     */
    public static List<String> getSubSelects(String sql) {
        List<String> subSelectList = new ArrayList<>();
        try {
            Statement statement = CCJSqlParserUtil.parse(sql);
            if (statement instanceof Select) {
                Select selectStatement = (Select) statement;
                SelectBody selectBody = selectStatement.getSelectBody();

                TablesNamesFinder tablesNamesFinder = new TablesNamesFinder() {
                    @Override
                    public void visit(SubSelect subSelect) {
                        System.out.println("Found subquery: " + subSelect);
                        subSelectList.add(subSelect.getSelectBody().toString());

                    }
                };
                selectBody.accept(tablesNamesFinder);
            }
            return subSelectList;
        } catch (JSQLParserException e) {
            e.printStackTrace();
        }
        return subSelectList;
    }

    /**
     * 更新sql语句
     *
     * @param sql
     * @return
     */
    public static String editSql(String sql, String updateSql) {
        try {
            // 解析原始SQL语句
            Statement statement = CCJSqlParserUtil.parse(sql);
            if (statement instanceof Select) {
                Select selectStatement = (Select) statement;
                SelectBody selectBody = selectStatement.getSelectBody();

                // 遍历查询的各个部分，寻找子查询
                selectBody.accept(new SelectVisitorAdapter() {
                    @Override
                    public void visit(PlainSelect plainSelect) {
                        FromItem fromItem = plainSelect.getFromItem();
                        processFromItem(fromItem);
                        // 处理JOIN子句中可能存在的子查询
                        if (plainSelect.getJoins() != null) {
                            for (Join join : plainSelect.getJoins()) {
                                processFromItem(join.getRightItem());
                            }
                        }
                    }

                    private void processFromItem(FromItem fromItem) {
                        if (fromItem instanceof SubSelect) {
                            SubSelect subSelect = (SubSelect) fromItem;
                            // 这里可以获取并修改子查询
                            try {
                                SelectBody newSelectBody = ((Select) CCJSqlParserUtil.parse(updateSql)).getSelectBody();
                                subSelect.setSelectBody(newSelectBody);
                            } catch (JSQLParserException e) {
                                e.printStackTrace();
                            }
                        }
                    }
                });
                return selectStatement.toString();
            }
        } catch (JSQLParserException e) {
            e.printStackTrace();
        }
        return null;
    }


    public static void main(String[] args) {
        String ss = "SELECT ID, USERNAME, NAME, PHONE, EMAIL, DEPT, IDENTIFY FROM  (SELECT ROW_NUMBER() OVER (ORDER BY ID) AS rownum, IAM.USER.* FROM IAM.USER )";
        getSubSelects(ss);

        editSql(ss, "select * from iam_user");
        hasWhereCondition(ss);
        boolean b = hasWhereCondition("SELECT ROW_NUMBER() OVER (ORDER BY ID) AS rownum, IAM.USER.* FROM IAM.USER WHERE a=3");
        System.out.println(b);
//        String complexSQL = "SELECT * FROM users u INNER JOIN orders o ON u.id = o.user_id WHERE u.age > 30 AND o.status = 'shipped' HAVING COUNT(o.id) > 5;";
        String complexSQL = "SELECT * FROM users u INNER JOIN orders o ON u.id = o.user_id ;";
        try {
//            String modifiedSQL = removeConditions(complexSQL);
//            System.out.println(modifiedSQL);


            String str = "select iu.id,CASE iu.gender WHEN 1 THEN '男' WHEN 0 THEN '女' END as gender1,iu.name,iu.username,iuo.org_ref_id from iam_user iu left join iam_user_org iuo on iu.id = iuo.uid where iuo.org_ref_id='1694300148713848833';";
            List<ColumnInfo> columnInfos = extractColumnInfo(str, true);
        } catch (JSQLParserException e) {
            e.printStackTrace();
        }
    }

    public static class ColumnInfo {
        public String columnName;
        public String alias;
        public String table;
        public String tabAndColumn;
        public String field;
        public int order;

        public ColumnInfo() {
        }

        public ColumnInfo(String columnName, String alias, String table, String tabAndColumn, String field, int order) {
            this.columnName = columnName;
            this.alias = alias;
            this.table = table;
            this.tabAndColumn = tabAndColumn;
            this.field = field;
            this.order = order;
        }

        @Override
        public String toString() {
            return "ColumnInfo{" +
                    "columnName='" + columnName + '\'' +
                    ", alias='" + alias + '\'' +
                    ", order=" + order +
                    '}';
        }
    }


}
