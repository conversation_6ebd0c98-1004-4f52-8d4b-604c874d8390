package com.cyberscraft.uep.common.domain.auth;

import com.cyberscraft.uep.common.util.WebUrlUtil;

import java.io.Serializable;

/**
 * @Author：xhquan
 * @Project：uep-services
 * @since：2023/11/1 17:22
 */
public class K3CloudParam extends Account implements Serializable {

    private static final long serialVersionUID = 1L;

    private String endpoint;

    private String acctID;

    private String lcid;

    private String username;

    private String password;

    public String getEndpoint() {
        return endpoint;
    }

    public void setEndpoint(String endpoint) {
        this.endpoint = endpoint;
    }

    public String getAcctID() {
        return acctID;
    }

    public void setAcctID(String acctID) {
        this.acctID = acctID;
    }

    public String getLcid() {
        return lcid;
    }

    public void setLcid(String lcid) {
        this.lcid = lcid;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    @Override
    public String getRealIp() {
        return WebUrlUtil.getHost(endpoint);
    }
}
