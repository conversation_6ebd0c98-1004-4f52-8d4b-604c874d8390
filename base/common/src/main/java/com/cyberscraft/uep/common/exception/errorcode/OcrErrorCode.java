package com.cyberscraft.uep.common.exception.errorcode;

/**
 * @Author：xhquan
 * @Project：uep-services
 * @since：2023/6/9 19:26
 */
public enum OcrErrorCode {

    OCR_CLIENT_NULL("1000", "ocr client is null");


    private String code;
    private String message;

    OcrErrorCode(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public final String getCode() {
        return this.code;
    }

    public final String getMessage() {
        return this.message;
    }

}
