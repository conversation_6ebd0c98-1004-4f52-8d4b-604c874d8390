package com.cyberscraft.uep.common.util;

import com.google.common.collect.ImmutableList;
import io.swagger.models.auth.In;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.FatalBeanException;
import org.springframework.util.Assert;
import org.springframework.util.ClassUtils;

import java.beans.PropertyDescriptor;
import java.lang.reflect.*;
import java.text.NumberFormat;
import java.util.*;

/***
 *
 * @date 2021-11-21
 * <AUTHOR>
 ***/
public class BeanUtil {

    /***
     * 要过虑的字段列表，默认为字段名称的小写
     */
    private final static Set<String> IGNORE_PROPERTIES = new HashSet<>();

    /***
     *
     */
    private final static Logger LOG = LoggerFactory.getLogger(BeanUtil.class);


    static {
        IGNORE_PROPERTIES.add("serialversionuid");
        IGNORE_PROPERTIES.add("class");
    }

    public static <T> void setBeanProperties(T obj, Map<String, Object> map, boolean ignoreNull) {
        Class<?> objClass = obj.getClass();
        Set<String> keys = map.keySet();
        for (String key : keys) {
            Object value = map.get(key);
            if (ignoreNull && value==null) {
                continue;
            }
            try {
                String methodName = methodName(key);
                Method getMethod = objClass.getMethod("get" + methodName);

                Class<?> feildTypeClass = getMethod.getReturnType();
                Method setMethod = objClass.getMethod("set" + methodName, feildTypeClass);
                String feildType = feildTypeClass.getSimpleName();

                if (value != null) {
                    String strValue = value.toString();

                    if ("int".equals(feildType) || "Integer".equals(feildType)) {
                        value = StringUtils.isBlank(strValue)?null:Integer.valueOf(strValue);
                    } else if ("long".equals(feildType) || "Long".equals(feildType)) {
                        value = StringUtils.isBlank(strValue)?null:Long.valueOf(strValue);
                    } else if ("double".equals(feildType) || "Double".equals(feildType)) {
                        value = StringUtils.isBlank(strValue)?null:Double.valueOf(strValue);
                    } else if ("float".equals(feildType) || "Float".equals(feildType)) {
                        value = StringUtils.isBlank(strValue)?null:Float.valueOf(strValue);
                    } else if ("String".equals(feildType)) {
                        value = strValue;
                    }
                }
                setMethod.invoke(obj, value);

            } catch (NoSuchMethodException e) {
                if (LOG.isTraceEnabled()) {
                    LOG.trace("{}对应的属性，不是字段，进行跳过", key);
                }
            } catch (Exception e) {
                LOG.warn("写入字段{}的值{}出错", key, value);
            }
        }
    }

    public static void copyProperties(Object source, Object target, Boolean ignoreNull) throws BeansException {
        Assert.notNull(source, "Source must not be null");
        Assert.notNull(target, "Target must not be null");

        Class<?> actualEditable = target.getClass();
        PropertyDescriptor[] targetPds = BeanUtils.getPropertyDescriptors(actualEditable);
        List<String> ignoreList = null;

        for (PropertyDescriptor targetPd : targetPds) {
            Method writeMethod = targetPd.getWriteMethod();
            if (writeMethod != null && (ignoreList == null || !ignoreList.contains(targetPd.getName()))) {
                PropertyDescriptor sourcePd = BeanUtils.getPropertyDescriptor(source.getClass(), targetPd.getName());
                if (sourcePd != null) {
                    Method readMethod = sourcePd.getReadMethod();
                    if (readMethod != null &&
                            ClassUtils.isAssignable(writeMethod.getParameterTypes()[0], readMethod.getReturnType())) {
                        try {
                            if (!Modifier.isPublic(readMethod.getDeclaringClass().getModifiers())) {
                                readMethod.setAccessible(true);
                            }
                            Object value = readMethod.invoke(source);
                            if (!Modifier.isPublic(writeMethod.getDeclaringClass().getModifiers())) {
                                writeMethod.setAccessible(true);
                            }
                            if (ignoreNull && value != null) {
                                writeMethod.invoke(target, value);
                            } else if (!ignoreNull) {
                                writeMethod.invoke(target, value);
                            }
                        }
                        catch (Throwable ex) {
                            throw new FatalBeanException(
                                    "Could not copy property '" + targetPd.getName() + "' from source to target", ex);
                        }
                    }
                }
            }
        }
    }

    public static Class<?> getPropertyClass(Object obj, String propertyName) throws Exception {
        String className = "";
        Field declaredField = obj.getClass().getDeclaredField(propertyName);
        Type genericType = declaredField.getGenericType();
        if (genericType instanceof ParameterizedType){
            ParameterizedType parameterizedType = (ParameterizedType) genericType;
            Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
            for (Type actualTypeArgument : actualTypeArguments) {
                className = actualTypeArgument.getTypeName();
                break;
            }
        } else {
            className = genericType.getTypeName();
        }
        return Class.forName(className);
    }

    public static <T> void setBeanProperty(T obj, String propertyName, Object value) {
        Class<?> objClass = obj.getClass();
        try {
            String methodName = methodName(propertyName);
            Method getMethod = objClass.getMethod("get" + methodName);

            Class<?> fieldTypeClass = getMethod.getReturnType();
            Method setMethod = objClass.getMethod("set" + methodName, fieldTypeClass);
            String fieldType = fieldTypeClass.getSimpleName();

            if (value != null) {
                String strValue = value.toString();

                if ("int".equals(fieldType) || "Integer".equals(fieldType)) {
                    value = StringUtils.isBlank(strValue)?null:Integer.valueOf(strValue);
                } else if ("long".equals(fieldType) || "Long".equals(fieldType)) {
                    value = StringUtils.isBlank(strValue)?null:Long.valueOf(strValue);
                } else if ("double".equals(fieldType) || "Double".equals(fieldType)) {
                    value = StringUtils.isBlank(strValue)?null:Double.valueOf(strValue);
                } else if ("float".equals(fieldType) || "Float".equals(fieldType)) {
                    value = StringUtils.isBlank(strValue) ? null : Float.valueOf(strValue);
                } else if ("boolean".equals(fieldType) || "Boolean".equals(fieldType)) {
                    value  = StringUtils.isBlank(strValue) ? null : Boolean.valueOf(strValue);
                } else if ("Number".equals(fieldType)) {
                    value = StringUtils.isBlank(strValue)?null: NumberFormat.getInstance().parse(strValue);
                } else if ("String".equals(fieldType)) {
                    value = strValue;
                }
            }
            setMethod.invoke(obj, value);

        } catch (NoSuchMethodException e) {
            if (LOG.isTraceEnabled()) {
                LOG.trace("{}对应的属性，不是字段，进行跳过", propertyName);
            }
        } catch (Exception e) {
            LOG.warn("写入字段{}的值{}出错", propertyName, value);
        }
    }

    public static String methodName(String fieldName){
        char[] chars = fieldName.toCharArray();
        //小写字母开头
        if (chars[0] > 96 && chars[0] < 123) {
            if (chars.length > 1) {
                //第二个字母大写
                if (chars[1] > 64 && chars[1] < 91) {
                    return fieldName;
                }
            }
            chars[0] -= 32;
            return String.valueOf(chars);
        }
        return fieldName;
    }

    /***
     * 设置Bean的属性
     * @param obj
     * @param properties
     * @param <T>
     */
    public static <T> void setBeanProperties(T obj, Map<String, String> properties) {
        Map<String, Field> fieldsMap = getDeclaredFieldsMap(obj.getClass());
        PropertyDescriptor[] srcPds = BeanUtils.getPropertyDescriptors(obj.getClass());
        if (srcPds == null || srcPds.length == 0) {
            return;
        }
        for (PropertyDescriptor p : srcPds) {
            String name = p.getName();
            if (IGNORE_PROPERTIES.contains(name.toLowerCase())) {
                continue;
            }
            Field field = null;
            try {
                field = fieldsMap.get(name);
            } catch (Exception e) {
                //如果出错，则代表不是对应的字段,因为该PropertyDescriptor即包括字段也包括方法，跳过就行
                if (LOG.isTraceEnabled()) {
                    LOG.trace("{}对应的属性，不是字段，进行跳过", name);
                }
            }
            Object value = null;
            try {
                Method writeMethod = p.getWriteMethod();
                if (!Modifier.isPublic(writeMethod.getDeclaringClass().getModifiers())) {
                    writeMethod.setAccessible(true);
                }
                String val = properties.get(name.toLowerCase());
                if(StringUtils.isBlank(val)){
                    continue;
                }
                String feildType = field.getType().getSimpleName();

                if ("int".equals(feildType) || "Integer".equals(feildType)) {
                    value = NumberUtils.toInt(val, 0);
                } else if ("long".equals(feildType) || "Long".equals(feildType)) {
                    value = NumberUtils.toLong(val, 0);
                } else if ("double".equals(feildType) || "Double".equals(feildType)) {
                    value = NumberUtils.toDouble(val, 0);
                } else if ("float".equals(feildType) || "Float".equals(feildType)) {
                    value = NumberUtils.toFloat(val, 0);
                } else {
                    value = val;
                }

                writeMethod.invoke(obj, value);
            } catch (Exception e) {
                LOG.warn("写入字段{}的值{}出错", name, value);
            }
        }

    }

    /***
     * 得到对像的所有字段，包括父对像的字段
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T> Map<String, Field> getDeclaredFieldsMap(Class<?> clazz) {
        Map<String, Field> ret = new HashMap<>();
        //Class<?> clazz = obj.getClass();
        while (clazz != null) {
            Field[] fields = clazz.getDeclaredFields();
            if (fields != null && fields.length > 0) {
                for (Field f : fields) {
                    ret.put(f.getName(), f);
                }
            }
            clazz = clazz.getSuperclass();
        }
        return ret;
    }


    /***
     * 得到对像的所有字段，包括父对像的字段,但是返回小写属性
     * @param clazz
     * @param <T>
     * @return
     */
    public static <T> Map<String, Field> getDeclaredFieldsMapWithLowerCase(Class<?> clazz) {
        Map<String, Field> ret = new HashMap<>();
        //Class<?> clazz = obj.getClass();
        while (clazz != null) {
            Field[] fields = clazz.getDeclaredFields();
            if (fields != null && fields.length > 0) {
                for (Field f : fields) {
                    ret.put(f.getName().toLowerCase(), f);
                }
            }
            clazz = clazz.getSuperclass();
        }
        return ret;
    }
}
