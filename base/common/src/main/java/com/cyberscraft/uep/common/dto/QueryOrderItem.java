package com.cyberscraft.uep.common.dto;

public class QueryOrderItem {
    /**
     * 需要进行排序的字段
     */
    private String column;

    /**
     * 是否正序排列，默认 true
     */
    private boolean asc = true;

    public String getColumn() {
        return column;
    }

    public void setColumn(String column) {
        this.column = column;
    }

    public boolean isAsc() {
        return asc;
    }

    public void setAsc(boolean asc) {
        this.asc = asc;
    }

    public static QueryOrderItem asc(String column) {
        return build(column, true);
    }

    public static QueryOrderItem desc(String column) {
        return build(column, false);
    }

    public static QueryOrderItem build(String column, boolean asc) {
        QueryOrderItem item = new QueryOrderItem();
        item.setColumn(column);
        item.setAsc(asc);
        return item;
    }
}
