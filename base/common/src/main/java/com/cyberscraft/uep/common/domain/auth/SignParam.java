package com.cyberscraft.uep.common.domain.auth;

import java.io.Serializable;

/**
 * @Description 签名 加密 解密的账号 用于存放密钥
 * <AUTHOR>
 * @Date 2024/4/10 16:33
 */
public class SignParam extends Account implements Serializable {

    private static final long serialVersionUID = 1L;

    private String secret;

    private Number secretLength;

    public String getSecret() {
        return secret;
    }

    public void setSecret(String secret) {
        this.secret = secret;
    }

    public Number getSecretLength() {
        return secretLength;
    }

    public void setSecretLength(Number secretLength) {
        this.secretLength = secretLength;
    }

    @Override
    public String getRealIp() {
        return null;
    }
}
