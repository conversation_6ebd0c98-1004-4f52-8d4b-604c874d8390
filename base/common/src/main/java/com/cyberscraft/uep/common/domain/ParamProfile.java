package com.cyberscraft.uep.common.domain;

import com.cyberscraft.uep.common.dto.PageControl;
import com.cyberscraft.uep.common.enums.ProfileConstant;
import com.cyberscraft.uep.common.util.LocalLanguageUtil;
import com.cyberscraft.uep.common.util.SnowflakeIDUtil;
import com.cyberscraft.uep.common.util.ThreadLocalUtil;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 参数模型
 * </p>
 *
 * <AUTHOR>
 * @since 2022/10/19 11:52 上午
 */
public class ParamProfile implements Serializable {


    /**
     * 前端UI ID，业务逻辑上不用
     */
    @ApiModelProperty(value = "前端UI的ID", dataType = "String")
    private String uiId;

    /**
     * 参数名
     */
    @ApiModelProperty(value = "参数名", required = true, dataType = "String")
    private String name;

    @ApiModelProperty(value = "参数别名", dataType = "List")
    private List<String> alias;

    /**
     * 数据类型
     */
    @ApiModelProperty(value = "数据类型", required = true)
    private DataTypeEnum type;

    /**
     * 参数描述
     */
    @ApiModelProperty(value = "参数描述", dataType = "String")
    private String description;

    /**
     * 参数描述
     */
    @ApiModelProperty(value = "显示名称", required = true, dataType = "String")
    private String displayName;

    /**
     * 是否多值，多值用数组表示
     */
    @ApiModelProperty(value = "是否多值，多值用数组表示", dataType = "boolean")
    private Boolean multiValued = false;

    /**
     * 子参数，当type为object时才有意义
     */
    @ApiModelProperty(value = "子参数，当type为object时才有意义", dataType = "domain")
    private List<ParamProfile> subParams;

    /**
     * 映射值,类型为valueType，支持三种：JSON_PATH、JS_EXP、FIX_VALUE
     */
    @ApiModelProperty(value = "参数值", dataType = "String")
    private String value;

    /**
     * create or update, false: create only, true: create and update
     */
    @ApiModelProperty(value = "是否更新，false表示：仅创建；true表示：创建并更新", dataType = "boolean")
    private Boolean update = true;

    @ApiModelProperty(value = "是否必填，false表示：可填；true表示：必填", dataType = "boolean")
    private Boolean required = false;

    @ApiModelProperty(value = "页面类型，HIDDEN：隐藏域；LABEL：标签显示；TEXT：文本框；TEXTAREA：文本域；DATE：日期框（年-月-日）；" +
            "TIME：时间框（时:分:秒）；DATETIME：日期和时间框（年-月-日 时:分:秒）；SELECT：选择框；FIXED_OBJ：" +
            "固定的对象，不可自定义子属性；EXTEND_OBJ：可自定义子属性；JSON：文本域，要求输入JSON数据格式", required = true)
    private PageControl  pageControl;

    @ApiModelProperty(value = "可选值", dataType = "List")
    private List<String> optionValues;

    /**
     * 映射类型
     */
    @ApiModelProperty(value = "输入值类型，FIX_VALUE：文本；JS_EXP：JS表达式；ADAPTOR：自动适应，如果包含变量，则按JS处理，否则按文本处理", required = true)
    private MappingType valueType = MappingType.JS_EXP;

    @ApiModelProperty(value = "可见性")
    private MutableEnum mutability = MutableEnum.readWrite;

    @ApiModelProperty(value = "属性所属属性集")
    private String propertyType;

    public ParamProfile(){

    }

    public ParamProfile(String name, DataTypeEnum dataType, String displayName){
        this.name = name;
        this.type = dataType;
        this.displayName = displayName;
    }

    public String getUiId() {
        if (uiId != null) {
            return uiId;
        } else {
            return uiId = String.valueOf(SnowflakeIDUtil.getId());
        }
    }

    public void setUiId(String uiId) {
        this.uiId = uiId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<String> getAlias() {
        return alias;
    }

    public void setAlias(List<String> alias) {
        this.alias = alias;
    }

    public DataTypeEnum getType() {
        return type;
    }

    public void setType(DataTypeEnum type) {
        this.type = type;
    }

    public String getDescription() {
        if (StringUtils.isNotBlank(this.propertyType)) {
            return getTranslate(this.name + ".description", description);
        }
        if (StringUtils.isBlank(description)) {
            return this.displayName;
        }
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Boolean getMultiValued() {
        return multiValued;
    }

    public void setMultiValued(Boolean multiValued) {
        this.multiValued = multiValued;
    }

    public List<ParamProfile> getSubParams() {
        return subParams;
    }

    public void setSubParams(List<ParamProfile> subParams) {
        this.subParams = subParams;
    }

    public ParamProfile getSubParamByName(String name) {
        for (ParamProfile subParam : subParams) {
            if (subParam.getName().equals(name)) {
                return subParam;
            }
        }
        return null;
    }

    public ParamProfile addSubParam(ParamProfile paramProfile) {
        if (paramProfile == null) {
            return this;
        }
        if (this.subParams == null) {
            this.subParams = new ArrayList<>();
        }
        if (!existSameNameParam(paramProfile)) {
            this.subParams.add(paramProfile);
        }
        return this;
    }

    private boolean existSameNameParam(ParamProfile paramProfile){
        for (ParamProfile subParam : subParams) {
            if (subParam.getName().equals(paramProfile.getName())) {
                return true;
            }
        }
        return false;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public MappingType getValueType() {
        return valueType;
    }

    public void setValueType(MappingType valueType) {
        this.valueType = valueType;
    }

    public String getDisplayName() {
        if (StringUtils.isNotBlank(this.propertyType)) {
            return getTranslate(this.name, displayName);
        }
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public Boolean getUpdate() {
        return update;
    }

    public void setUpdate(Boolean update) {
        this.update = update;
    }

    public MutableEnum getMutability() {
        return mutability;
    }

    public void setMutability(MutableEnum mutability) {
        this.mutability = mutability;
    }

    public Boolean getRequired() {
        return required;
    }

    public void setRequired(Boolean required) {
        this.required = required;
    }

    public PageControl getPageControl() {
        return pageControl;
    }

    public void setPageControl(PageControl pageControl) {
        this.pageControl = pageControl;
    }

    public List<String> getOptionValues() {
        return optionValues;
    }

    public void setOptionValues(List<String> optionValues) {
        this.optionValues = optionValues;
    }


    public String getPropertyType() {
        return propertyType;
    }

    public void setPropertyType(String propertyType) {
        this.propertyType = propertyType;
    }

    /**
     * 获取翻译后的参数名称
     *
     * @param propertyName 属性名称
     * @param original 原文
     * @return 翻译后的参数名称
     */
    private String getTranslate(String propertyName, String original) {
        Map<String, Object> context = ThreadLocalUtil.getContext();
        if (context == null) {
            return original;
        }
        Object appFlag = context.get("appFlag");
        if (appFlag != null) {
            if (ProfileConstant.local.name().equals(appFlag)) {
                propertyName = ProfileConstant.getPropertyNameByDesc(propertyName, original);
            }
            String key = appFlag + "." + this.propertyType + "." + propertyName;
            return LocalLanguageUtil.getProfileLanguage(key, original);
        }
        return original;
    }
}
