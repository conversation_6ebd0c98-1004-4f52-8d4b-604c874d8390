package com.cyberscraft.uep.common.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Date 2025/2/7 14:23
 */
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class PushFlag implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 本地用户或部门
     */
    private Object local;

    /**
     * IDM用户或部门ID
     */
    private String idmId;

    /**
     * 外部用户或部门ID
     */
    private String externalId;

    /**
     * 外部用户或部门unionId
     */
    private String unionId;

    /**
     * 外部用户或部门openId
     */
    private String openId;

    private String profile;


    /**
     * 处理标识：CREATE、UPDATE、DELETE、NOUPDATE、ERROR
     */
    private String flag = "ERROR";

    private Boolean isCreate = false;

    /**
     * 提示信息
     */
    private String message = "";

    public Object getLocal() {
        return local;
    }

    public void setLocal(Object local) {
        this.local = local;
    }

    public String getIdmId() {
        return idmId;
    }

    public void setIdmId(String idmId) {
        this.idmId = idmId;
    }

    public String getExternalId() {
        return externalId;
    }

    public void setExternalId(String externalId) {
        this.externalId = externalId;
    }

    public String getUnionId() {
        return unionId;
    }

    public void setUnionId(String unionId) {
        this.unionId = unionId;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getProfile() {
        return profile;
    }

    public void setProfile(String profile) {
        this.profile = profile;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }

    public Boolean getCreate() {
        return isCreate;
    }

    public void setCreate(Boolean create) {
        isCreate = create;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    @Override
    public String toString() {
        return "PushFlag{" +
                "local=" + local +
                ", idmId='" + idmId + '\'' +
                ", externalId='" + externalId + '\'' +
                ", unionId='" + unionId + '\'' +
                ", openId='" + openId + '\'' +
                ", profile='" + profile + '\'' +
                ", flag='" + flag + '\'' +
                ", message='" + message + '\'' +
                '}';
    }
}
