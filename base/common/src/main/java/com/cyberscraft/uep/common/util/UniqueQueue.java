package com.cyberscraft.uep.common.util;

import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.LinkedList;

/**
 * @Author：xhquan
 * @Project：uep-services
 * @since：2024/3/5 15:18
 */
public class UniqueQueue<E> {

    private final LinkedHashSet<E> set = new LinkedHashSet<>();

    private final LinkedList<E> queue = new LinkedList<>();

    public synchronized boolean add(E e) {
        // 只有当元素成功添加到set中时，才将其添加到queue中
        if (set.add(e)) {
            queue.add(e);
            return true;
        }
        return false;
    }

    public synchronized E poll() {
        E e = queue.poll();
        if (e != null) {
            set.remove(e);
        }
        return e;
    }

    public E peek() {
        return queue.peek();
    }

    public boolean contains(E e) {
        return set.contains(e);
    }

    public int size() {
        return queue.size();
    }

    public Iterator<E> iterator() {
        return queue.iterator();
    }

    public Boolean isEmpty() {
        return queue.size() == 0;
    }

}


