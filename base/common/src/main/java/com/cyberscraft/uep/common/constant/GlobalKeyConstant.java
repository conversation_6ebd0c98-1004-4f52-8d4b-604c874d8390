package com.cyberscraft.uep.common.constant;

/**
 * ClassName: GlobalKeyConstant
 * Function:  GlobalKeyConstant
 * Date:      2019-06-22 15:17
 * author     liwenyong
 * version    V1.0
 */
public final class GlobalKeyConstant {
    private GlobalKeyConstant(){}






    /**
     * 单租户，多租户标识。值是 MD5后的字符串
     */
    public final static String  SYS_MTENANT_USE  = "sys.mtenant.use";

    /**
     * UEP各个的服务标识
     */
    public final static String IAM_SERVICE_NAME = "iam";
    public final static String UEM_SERVICE_NAME = "uem";

    public final static String TENANT_OPT_CREATE = "TENANT_CREATE";
    public final static String TENANT_OPT_DELETE = "TENANT_DELETE";
    public final static String TENANT_OPT_UPDATE = "TENANT_UPDATE";
}
