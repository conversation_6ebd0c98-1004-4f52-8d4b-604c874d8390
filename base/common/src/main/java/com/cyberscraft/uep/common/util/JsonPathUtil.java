package com.cyberscraft.uep.common.util;

import com.jayway.jsonpath.Configuration;
import com.jayway.jsonpath.DocumentContext;
import com.jayway.jsonpath.JsonPath;
import com.jayway.jsonpath.Option;
import com.jayway.jsonpath.spi.json.JsonSmartJsonProvider;
import com.jayway.jsonpath.spi.mapper.JsonSmartMappingProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;

/**
 * <p>
 * json-path 工具类
 * </p>
 *
 * <AUTHOR>
 * @since 2022/12/8 1:36 下午
 */
public class JsonPathUtil {

    private final static Logger logger = LoggerFactory.getLogger(JsonPathUtil.class);

    private static Configuration configuration = null;

    private static Configuration getDefaultConfiguration() {
        if (configuration == null) {
            configuration = Configuration.builder()
                    .jsonProvider(new JsonSmartJsonProvider())
                    .mappingProvider(new JsonSmartMappingProvider())
                    .options(Option.DEFAULT_PATH_LEAF_TO_NULL)
                    .options(Option.SUPPRESS_EXCEPTIONS)
                    .build();
        }
        return configuration;
    }

    public static String updateJsonByPath(String srcJson, String path, Object value) {

        Configuration configuration = getDefaultConfiguration();

        logger.info("source json:{}", srcJson);
        logger.info("path:{}", path);
        logger.info("value:{}", value);
        DocumentContext parse = JsonPath.using(configuration).parse(srcJson);
        JsonPath compile = JsonPath.compile(path);
        parse.set(compile, value);
        String resultJson = parse.jsonString();
        logger.info("result json:{}", resultJson);
        return resultJson;
    }

    public static String deletedJsonByPath(String srcJson, String path, Object value) {

        Configuration configuration = getDefaultConfiguration();

        logger.info("source json:{}", srcJson);
        logger.info("path:{}", path);
        logger.info("value:{}", value);
        DocumentContext parse = JsonPath.using(configuration).parse(srcJson);
        JsonPath compile = JsonPath.compile(path);
        parse.delete(compile);
        String resultJson = parse.jsonString();
        logger.info("result json:{}", resultJson);
        return resultJson;
    }

    public static String addJsonByPath(String srcJson, String path, Object value) {

        Configuration configuration = getDefaultConfiguration();

        logger.info("source json:{}", srcJson);
        logger.info("path:{}", path);
        logger.info("value:{}", value);
        DocumentContext parse = JsonPath.using(configuration).parse(srcJson);
        JsonPath compile = JsonPath.compile(path);
        parse.add(compile, value);
        String resultJson = parse.jsonString();
        logger.info("result json:{}", resultJson);
        return resultJson;
    }

    public static Object read(Object param, String jsonPath) {

        Configuration configuration = getDefaultConfiguration();

        JsonPath compile = JsonPath.compile(jsonPath);
        Object read = compile.read(param, configuration);
        return read;
    }

    public static void main(String[] args) {

        HashMap<String, Object> param = new HashMap<>();
        param.put("age", 1);
        param.put("name", "liu");

        HashMap<String, Object> subParam = new HashMap<>();
        subParam.put("pop", "play");
        subParam.put("fav", "ball");

        param.put("sub", subParam);

        HashMap<String, Object> params = new HashMap<>();
        params.put("external", param);

        Object name = JsonPathUtil.read(params, "external.name");
        Object sub = JsonPathUtil.read(params, "external.sub");
        Object sub1 = JsonPathUtil.read(params, "external.sub1");

        System.out.println("name:"+name);
        System.out.println("sub:"+sub);
        System.out.println("sub1:"+sub1);
    }

    public static void main1(String[] args) {
        String srcJson = "{\"display_name\":\"张三\",\"id\":\"1605859722240466946\",\"meta\":{\"created\":\"2022-12-22T17:36:08\",\"last_modified\":\"2022-12-22T17:36:08\",\"location\":\"/iam/scim/Users/<USER>",\"resource_type\":\"User\"},\"schemas\":[\"urn:ietf:params:scim:schemas:core:2.0:User\",\"urn:ietf:params:scim:schemas:extension:enterprise:2.0:User\"],\"user_extension\":{\"department\":\"/组织机构/默认组\"},\"user_name\":\"zhansan\"}";
        String path = "name.givenName";
        String value = "三2";

        String s = JsonPathUtil.updateJsonByPath(srcJson, path, value);
        System.out.println(s);
    }

}
