package com.cyberscraft.uep.common.domain.auth;

import java.io.Serializable;

/**
 * 企企通SRM连接器配置参数类
 *
 * <AUTHOR>
 * @date 2023年12月28日
 */
public class QqtParam extends Account implements Serializable {

    /**
     * 服务器地址
     */
    private String endpoint = "https://api-sit.51qqt.com/els/openApi/invoke";

    /**
     * 应用key
     */
    private String appKey;

    /**
     * 应用密钥
     */
    private String appSecret;

    /**
     * els账号
     */
    private String account;

    /**
     * 开放平台组织查询接口
     */
    private String orgQueryOpenApi;

    /**
     * 开放平台组织创建接口
     */
    private String orgAddOpenApi;

    /**
     * 开放平台组织修改接口
     */
    private String orgEditOpenApi;

    /**
     * 开放平台组织删除接口
     */
    private String orgDelOpenApi;

    /**
     * 操作平台组织查询接口
     */
    private String orgQueryApi;

    /**
     * 操作平台组织创建接口
     */
    private String orgAddApi;

    /**
     * 操作平台组织修改接口
     */
    private String orgEditApi;

    /**
     * 操作平台组织删除接口
     */
    private String orgDelApi;

    /**
     * 开放平台用户查询接口
     */
    private String userQueryOpenApi;

    /**
     * 开放平台用户创建接口
     */
    private String userAddOpenApi;

    /**
     * 开放平台用户修改接口
     */
    private String userEditOpenApi;

    /**
     * 开放平台用户删除接口
     */
    private String userDelOpenApi;

    /**
     * 操作平台用户查询接口
     */
    private String userQueryApi;

    /**
     * 操作平台用户创建接口
     */
    private String userAddApi;

    /**
     * 操作平台用户修改接口
     */
    private String userEditApi;

    /**
     * 操作平台用户删除接口
     */
    private String userDelApi;

    public String getEndpoint() {
        return endpoint;
    }

    public void setEndpoint(String endpoint) {
        this.endpoint = endpoint;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getOrgQueryOpenApi() {
        return orgQueryOpenApi;
    }

    public void setOrgQueryOpenApi(String orgQueryOpenApi) {
        this.orgQueryOpenApi = orgQueryOpenApi;
    }

    public String getOrgAddOpenApi() {
        return orgAddOpenApi;
    }

    public void setOrgAddOpenApi(String orgAddOpenApi) {
        this.orgAddOpenApi = orgAddOpenApi;
    }

    public String getOrgEditOpenApi() {
        return orgEditOpenApi;
    }

    public void setOrgEditOpenApi(String orgEditOpenApi) {
        this.orgEditOpenApi = orgEditOpenApi;
    }

    public String getOrgDelOpenApi() {
        return orgDelOpenApi;
    }

    public void setOrgDelOpenApi(String orgDelOpenApi) {
        this.orgDelOpenApi = orgDelOpenApi;
    }

    public String getOrgQueryApi() {
        return orgQueryApi;
    }

    public void setOrgQueryApi(String orgQueryApi) {
        this.orgQueryApi = orgQueryApi;
    }

    public String getOrgAddApi() {
        return orgAddApi;
    }

    public void setOrgAddApi(String orgAddApi) {
        this.orgAddApi = orgAddApi;
    }

    public String getOrgEditApi() {
        return orgEditApi;
    }

    public void setOrgEditApi(String orgEditApi) {
        this.orgEditApi = orgEditApi;
    }

    public String getOrgDelApi() {
        return orgDelApi;
    }

    public void setOrgDelApi(String orgDelApi) {
        this.orgDelApi = orgDelApi;
    }

    public String getUserQueryOpenApi() {
        return userQueryOpenApi;
    }

    public void setUserQueryOpenApi(String userQueryOpenApi) {
        this.userQueryOpenApi = userQueryOpenApi;
    }

    public String getUserAddOpenApi() {
        return userAddOpenApi;
    }

    public void setUserAddOpenApi(String userAddOpenApi) {
        this.userAddOpenApi = userAddOpenApi;
    }

    public String getUserEditOpenApi() {
        return userEditOpenApi;
    }

    public void setUserEditOpenApi(String userEditOpenApi) {
        this.userEditOpenApi = userEditOpenApi;
    }

    public String getUserDelOpenApi() {
        return userDelOpenApi;
    }

    public void setUserDelOpenApi(String userDelOpenApi) {
        this.userDelOpenApi = userDelOpenApi;
    }

    public String getUserQueryApi() {
        return userQueryApi;
    }

    public void setUserQueryApi(String userQueryApi) {
        this.userQueryApi = userQueryApi;
    }

    public String getUserAddApi() {
        return userAddApi;
    }

    public void setUserAddApi(String userAddApi) {
        this.userAddApi = userAddApi;
    }

    public String getUserEditApi() {
        return userEditApi;
    }

    public void setUserEditApi(String userEditApi) {
        this.userEditApi = userEditApi;
    }

    public String getUserDelApi() {
        return userDelApi;
    }

    public void setUserDelApi(String userDelApi) {
        this.userDelApi = userDelApi;
    }

    @Override
    public String getRealIp() {
        return "api-sit.51qqt.com";
    }
}
