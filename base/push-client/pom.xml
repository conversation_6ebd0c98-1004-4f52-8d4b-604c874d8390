<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>base</artifactId>
        <groupId>com.cyberscraft.uep</groupId>
        <version>5.4.1</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>push-client</artifactId>

    <dependencies>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.platform.shared</groupId>
            <artifactId>xxpt.gateway.shared.client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cyberscraft.uep</groupId>
            <artifactId>config-client</artifactId>
            <version>${project.parent.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.cyberscraft.uep</groupId>
            <artifactId>message-client</artifactId>
            <version>${project.parent.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.cyberscraft.uep</groupId>
            <artifactId>account-client</artifactId>
            <version>${project.parent.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.cyberscraft.uep</groupId>
            <artifactId>account-govdd</artifactId>
            <version>${project.parent.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.cyberscraft.uep</groupId>
            <artifactId>common</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-tcnative-boringssl-static</artifactId>
        </dependency>
        <dependency>
            <groupId>com.turo</groupId>
            <artifactId>pushy</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <finalName>push-client</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>