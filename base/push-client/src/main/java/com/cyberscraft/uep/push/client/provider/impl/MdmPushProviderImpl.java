package com.cyberscraft.uep.push.client.provider.impl;

import com.cyberscraft.uep.starter.dds.TenantHolder;
import com.cyberscraft.uep.common.enums.LogTag;
import com.cyberscraft.uep.common.enums.Platform;
import com.cyberscraft.uep.common.logger.DigitalSeeLoggerFactory;
import com.cyberscraft.uep.push.client.constant.PushClientType;
import com.cyberscraft.uep.push.client.constant.PushMode;
import com.cyberscraft.uep.push.client.domain.PushInfo;
import com.cyberscraft.uep.push.client.domain.PushReceiver;
import com.cyberscraft.uep.push.client.errors.PushErrorType;
import com.cyberscraft.uep.push.client.exception.PushException;
import com.cyberscraft.uep.push.client.provider.IMdmPushClient;
import com.cyberscraft.uep.push.client.provider.IPushProvider;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/***
 * 设备级推送提供者，
 * @date 2021-12-31
 * <AUTHOR>
 ***/
@Component
public class MdmPushProviderImpl extends AbstractPushProvider implements IPushProvider {

    /***
     *
     */
    private final static Logger LOG = DigitalSeeLoggerFactory.getLogger(MdmPushProviderImpl.class, LogTag.PUSH_CLIENT);

    /***
     * 设备类型，设备级推送客户端列表
     */
    private ConcurrentHashMap<Integer, IMdmPushClient> PLATFORM_PUSH_CLIENT_MAP = new ConcurrentHashMap<>();

    /****
     * 推送类型，推送客户端列表
     */
    private ConcurrentHashMap<String, IMdmPushClient> PUSHTYPE_PUSH_CLIENT_MAP = new ConcurrentHashMap<>();


    /***
     * 设备级推送客户端列表
     */
    private List<IMdmPushClient> pushClients = new ArrayList<>();

    /***
     * 支持的模式列表
     */
    private final static Set<Integer> SUPPORTED_MODES = new HashSet<>();

    static {
        SUPPORTED_MODES.add(PushMode.MDM.getCode());
    }


    public List<IMdmPushClient> getPushClients() {
        return pushClients;
    }

    @Autowired
    public void setPushClients(List<IMdmPushClient> pushClients) {
        this.pushClients = pushClients;
        this.initMdmPushClientMap();
    }

    /***
     *
     */
    private synchronized void initMdmPushClientMap() {
        PUSHTYPE_PUSH_CLIENT_MAP.clear();
        if (this.pushClients != null && this.pushClients.size() > 0) {
            for (IMdmPushClient pushClient : this.pushClients) {
                PUSHTYPE_PUSH_CLIENT_MAP.put(pushClient.getPushClientType(), pushClient);
            }
        }
    }

    @Override
    public Set<Integer> getSupportedModes() {
        return SUPPORTED_MODES;
    }


    /****
     * 设备级推送，第一步根据推送的平台进行平类型接收者拆分
     * 第二步根据平台类型，得到对应的推送客户端，
     * 第三步调用具体的推送客户端进行推送。
     * @param push
     * @throws PushException
     */
    @Override
    public void push(PushInfo push) throws PushException {
        if (push == null) {
            throw new PushException(PushErrorType.PUSH_INVALID);
        }
        if (push.getReceivers() == null || push.getReceivers().size() == 0) {
            throw new PushException(PushErrorType.PUSH_RECEIVER_EMPTY);
        }
        TenantHolder.setTenantCode(push.getTenantId());

        //根据deviceType,receiver进行分组
        Map<Integer, List<PushReceiver>> platformReceivers = push.getReceivers().stream().filter(el -> el.getPlatform() != null).collect(Collectors.groupingBy(PushReceiver::getPlatform));
        for (Integer platform : platformReceivers.keySet()) {
            List<PushReceiver> receivers = platformReceivers.get(platform);
            if (receivers == null || receivers.size() == 0) {
                continue;
            }
            try {
                IMdmPushClient pushClient = findPlatfromMdmPushClient(platform);
                if (pushClient == null) {
                    //如果是找不到，则可能表示为android之类的设备调用了设备级推送，所以转到应用级推送进行处理
                    throw new PushException(PushErrorType.PUSH_TYPE_INVALID);
                }
                pushClient.push(push, receivers);
            } catch (Exception e) {
                //保存为一条新的失败记录
                saveFailurePushRecord(push, receivers, null);
                LOG.error("设备级推送错误:{}", e.getMessage());
            }
        }
        savePushSuccessRecord(push);
    }


    /***
     * 根据Platform查找对应的推送客户端
     * @param platformValue
     * @return
     */
    private IMdmPushClient findPlatfromMdmPushClient(Integer platformValue) {
        if (platformValue == null) {
            return null;
        }
        IMdmPushClient pushClient = this.PLATFORM_PUSH_CLIENT_MAP.get(platformValue);
        if (pushClient != null) {
            return pushClient;
        }
        //进行计算，并且获取对应的推送客户端，采用分离锁进行处理，这里也可以不用锁，只是导致重复运行而以
        synchronized (platformValue) {
            //二次判断，以免重复计算
            pushClient = PLATFORM_PUSH_CLIENT_MAP.get(platformValue);
            if (pushClient != null) {
                return pushClient;
            }
            String pushType = pushConfiguartion.getMdmType();
            //如果未指定默认的推送类型，则指定为npns推送
            if (StringUtils.isBlank(pushType)) {
                pushType = PushClientType.NPNS.getCode();
            }
            Platform platform = Platform.findByValue(platformValue);// DeviceTypeConstant.getPlatformByDeviceType(deviceType);
            if (platform != null) {
                Map<String, String> deviceMdmPushTypes = pushConfiguartion.getMdmPushTypes();
                String key = platform.name().toLowerCase();
                if (deviceMdmPushTypes != null && deviceMdmPushTypes.containsKey(key)) {
                    pushType = deviceMdmPushTypes.get(key);
                }
            }
            pushClient = PUSHTYPE_PUSH_CLIENT_MAP.get(pushType);
            if (pushClient == null) {
                throw new PushException(PushErrorType.PUSH_TYPE_INVALID);
            }
            PLATFORM_PUSH_CLIENT_MAP.put(platformValue, pushClient);
            return pushClient;
        }
    }

}
