package com.cyberscraft.uep.push.client.errors;

import com.cyberscraft.uep.common.enums.SysCodeEnum;

/***
 *
 * @date 2021-12-30
 * <AUTHOR>
 ***/
public enum PushErrorType {
    /***
     * 推送模式无效
     */
    PUSH_INVALID("1001", "推送无效"),
    /***
     * 推送模式无效
     */
    PUSH_MODE_INVALID("1002", "推送模式无效"),
    /***
     * 推送接收者为空
     */
    PUSH_RECEIVER_EMPTY("1003", "推送接收者为空"),
    /***
     * 推送接收者无效
     */
    PUSH_RECEIVER_INVALID("1004", "推送接收者无效"),
    /***
     * 推送接收者为空
     */
    PUSH_TOKEN_EMPTY("1005", "推送Token为空"),
    /***
     * 推送接收者无效
     */
    PUSH_TOKEN_INVALID("1006", "推送TOKEN无效"),
    /***
     * 推送模式无效
     */
    PUSH_CLIENT_ERROR("1007", "推送客户端出错"),
    /***
     * 推送记录ID无效
     */
    PUSH_RECORD_ID_INVALID("1008", "推送记录ID无效"),
    /***
     * 推送记录无效
     */
    PUSH_RECORD_INVALID("1009", "推送记录无效"),
    /***
     * 推送证书无效
     */
    PUSH_CREDENTIAL_INVALID("1010", "推送证书无效"),
    /***
     * 推送类型无效
     */
    PUSH_TYPE_INVALID("1011", "推送类型无效"),
    /***
     * 推送featureId无效
     */
    PUSH_FEATUREID_INVALID("1012", "推送featureId无效"),
    /***
     * 推送TOKEN已失效
     */
    PUSH_TOKEN_EXPIRED("1013", "推送TOKEN已失效"),
    /***
     * 推送TOKENID为空
     */
    PUSH_TOKEN_ID_EMPTY("1014", "推送TOKENID为空");

    private String code;
    private String desc;

    PushErrorType(String errorCode, String desc) {
        this.code = errorCode;
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }

    /***
     * 返回代码时，自动返回MDM前缀码对应的代码，加上业务本身的错误代码
     */
    public String getCode() {
        String prefix = SysCodeEnum.PUSH.getCode();
        return prefix + this.code;
    }
}
