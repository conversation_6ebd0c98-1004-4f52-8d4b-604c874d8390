package com.cyberscraft.uep.push.client.service;

import com.cyberscraft.uep.push.client.domain.ApnsToken;
import com.cyberscraft.uep.push.client.domain.PushInfo;
import com.cyberscraft.uep.push.client.domain.PushReceiver;
import com.cyberscraft.uep.push.client.domain.PushToken;
import com.cyberscraft.uep.push.client.exception.PushException;

/***
 * 该接口为用于业务端调用的通用的接口，因为业务端主要存在两个调用，
 * 一是进行推送消息，二是更新推送Token，
 * 如果以后要把推送升级成一个微服务时，只要在实现类中，调用推送服务API进行Token更新即可，其它都不需要进行调整
 * @date 2021-12-30
 * <AUTHOR>
 ***/
public interface IPushClientService {

    /***
     * 主要供业务端进行调用，用于将推送消息发到MQ
     * @param push
     * @throws PushException
     */
    void push(PushInfo push) throws PushException;

    /***
     * 保存用户对应的推送Token
     */
    void savePushToken(PushToken token) throws PushException;

    /***
     * 保存用户对应的NPNS推送时，使用的ApnsToken值
     * @param token
     * @throws PushException
     */
    void saveApnsToken(ApnsToken token) throws PushException;

    /**
     * 克隆push token
     * @param src
     * @param dist
     */
    void clonePushToken(PushReceiver src, PushReceiver dist);
}
