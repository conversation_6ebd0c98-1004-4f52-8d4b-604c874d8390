package com.cyberscraft.uep.push.client.service.impl;

import com.cyberscraft.uep.common.bean.NamedThreadFactory;
import com.cyberscraft.uep.common.constant.SysConstant;
import com.cyberscraft.uep.common.dto.PageView;
import com.cyberscraft.uep.common.dto.Pagination;
import com.cyberscraft.uep.common.dto.QueryOrderItem;
import com.cyberscraft.uep.common.enums.LogTag;
import com.cyberscraft.uep.common.logger.DigitalSeeLoggerFactory;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.push.client.configuration.PushConfiguartion;
import com.cyberscraft.uep.push.client.domain.PushInfo;
import com.cyberscraft.uep.push.client.domain.PushReceiver;
import com.cyberscraft.uep.push.client.dto.PushFailureRecordQueryDto;
import com.cyberscraft.uep.push.client.entity.PushFailureRecordEntity;
import com.cyberscraft.uep.push.client.service.IPushFailureRecordService;
import com.cyberscraft.uep.push.client.service.IPushProviderService;
import com.cyberscraft.uep.push.client.service.IRePushService;
import org.slf4j.Logger;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;

/***
 * 默认的重新推送服务实现
 * @date 2021-01-03
 * <AUTHOR>
 ***/
public class RePushServiceImpl implements IRePushService {

    /***
     *
     */
    private final static Logger LOG = DigitalSeeLoggerFactory.getLogger(RePushServiceImpl.class, LogTag.PUSH_CLIENT);

    /***
     * 最少间隔为1分钟
     */
    private final static Integer MIN_REPUSH_INTERVAL = 1;
    /***
     * 定时服务
     */
    private static ScheduledExecutorService service;


    private final static int NCPU = Runtime.getRuntime().availableProcessors();

    /***
     * 任务线程池
     */
    private final ExecutorService executor = Executors.newFixedThreadPool(Math.max(NCPU * 4, 8), new NamedThreadFactory("repush"));

    private PushConfiguartion pushConfiguartion;

    private IPushFailureRecordService pushFailureRecordService;

    private IPushProviderService pushProviderService;

    private StringRedisTemplate stringRedisTemplate;

    @Override
    public void start() {
        LOG.info("当前重新推送服务开始运行");
        if (service == null) {
            // 增加数据到待抓取队列
            service = Executors.newSingleThreadScheduledExecutor();
            service.scheduleWithFixedDelay(new RePushThread(), 2, pushConfiguartion.getRepushTaskInterval(), TimeUnit.MINUTES);
        }
    }

    @Override
    public void stop() {
        if (service != null) {
            try {
                service.shutdown();
            } catch (Exception e) {
                LOG.warn("停止服务时发生错误:{}", e.getMessage());
            }
        }
    }


    /***
     * 重新推送线程,因为可能会存在多个服务启动的情况，所以需要进行分布式的初步处理，暂时不加锁
     */
    private class RePushThread implements Runnable {

        @Override
        public void run() {
            try {
                int pageSize = 500;
                int pageIndex = 1;

                List<QueryOrderItem> orderItems = new ArrayList<>();
                orderItems.add(QueryOrderItem.asc("nextPushTime"));

                PushFailureRecordQueryDto query = new PushFailureRecordQueryDto();
                query.setAllMaxRepushTimes(pushConfiguartion.getMaxFailurePushTimes());
                query.setMaxNextRepushTime(LocalDateTime.now());
                int totalRecord = 0;
                while (true) {
                    Pagination<PushFailureRecordQueryDto> page = new Pagination<>();
                    page.setPage(1);
                    page.setSize(pageSize);
                    page.setOrders(orderItems);
                    page.setQueryDto(query);

                    PageView<PushFailureRecordEntity> pageView = pushFailureRecordService.getPushFailureRecords(page);
                    if (pageView == null || pageView.getItems() == null || pageView.getItems().size() == 0) {
                        LOG.info("当前获取第{}页的重推数据时，已经没有需要当前进行重推的数据，结束本次重试", pageIndex);
                        break;
                    }
                    totalRecord += pageView.getItems().size();
                    repush(pageView.getItems());

                    if (pageView.getItems().size() < pageSize) {
                        LOG.info("当前重推数据到{}页，每页{}条,当前总共{}条，小于{}，已经没有需要当前进行重推的数据，结束本次重试", pageIndex, pageSize, pageView.getItems().size(), pageSize);
                        break;
                    }
                    pageIndex++;
                }
                LOG.info("本次重新推结束，总共推送了{}条数据", totalRecord);
            } catch (Exception e) {
                LOG.warn("重新推送时发重送错误:{}", e.getMessage());
            }
        }
    }

    /***
     * 重新推送处理
     * @param list
     */
    private void repush(List<PushFailureRecordEntity> list) {
        try {
            CountDownLatch latch = new CountDownLatch(list.size());
            for (final PushFailureRecordEntity entity : list) {
                //进行每一条的推送，这里采用异步线程池
                executor.submit(() -> {
                    try {
                        if (!isCanPush(entity.getId())) {
                            LOG.info("当前推送正在处理，本次跳过,推送ID:{},接收用户:{}", entity.getId(), entity.getReceivers());
                            return;
                        }
                        PushInfo push = new PushInfo();
                        push.setSender(entity.getSender());
                        push.setPayload(entity.getPayload());
                        push.setPushMode(entity.getPushMode());
                        push.setTenantId(entity.getTenantId());
                        push.setId(entity.getId());
                        String receiverStr = entity.getReceivers();
                        List<PushReceiver> receivers = parseReceivers(receiverStr);
                        push.setReceivers(receivers);
                        //增加1年，为了防止发送时，重复调用，相当于移去队列，因为后面的发送为异步发送
                        pushFailureRecordService.updateNextPushTime(entity.getId(), LocalDateTime.now().plusYears(1));
                        pushProviderService.pushToClient(push);
                    } catch (Exception e) {
                        LOG.warn("重新推送{}对应的数据时发生错误:{}", entity != null ? entity.getPushReceivers() : "", e.getMessage());
                    } finally {
                        latch.countDown();
                    }
                });
            }
            latch.await();
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }

    /****
     * 这里没有使用分布式锁，进行简单的setnx处理即可,重推不允许1分钟内再次重推
     * @param id
     * @return
     */
    private boolean isCanPush(Long id) {
        String key = "RePush_" + id;
        if (stringRedisTemplate.opsForValue().setIfAbsent(key, SysConstant.TRUE, MIN_REPUSH_INTERVAL, TimeUnit.MINUTES)) {
            return true;
        }
        return false;
    }

    /***
     *
     * @param receiverStr
     * @return
     */
    private List<PushReceiver> parseReceivers(String receiverStr) {
        return JsonUtil.str2List(receiverStr, PushReceiver.class);
    }


    public PushConfiguartion getPushConfiguartion() {
        return pushConfiguartion;
    }

    public void setPushConfiguartion(PushConfiguartion pushConfiguartion) {
        this.pushConfiguartion = pushConfiguartion;
    }

    public IPushFailureRecordService getPushFailureRecordService() {
        return pushFailureRecordService;
    }

    public void setPushFailureRecordService(IPushFailureRecordService pushFailureRecordService) {
        this.pushFailureRecordService = pushFailureRecordService;
    }

    public IPushProviderService getPushProviderService() {
        return pushProviderService;
    }

    public void setPushProviderService(IPushProviderService pushProviderService) {
        this.pushProviderService = pushProviderService;
    }

    public StringRedisTemplate getStringRedisTemplate() {
        return stringRedisTemplate;
    }

    public void setStringRedisTemplate(StringRedisTemplate stringRedisTemplate) {
        this.stringRedisTemplate = stringRedisTemplate;
    }
}
