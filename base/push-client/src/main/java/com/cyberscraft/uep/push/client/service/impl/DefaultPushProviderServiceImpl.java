package com.cyberscraft.uep.push.client.service.impl;

import com.cyberscraft.uep.common.enums.ExceptionCodeEnum;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import com.cyberscraft.uep.common.enums.LogTag;
import com.cyberscraft.uep.common.logger.DigitalSeeLoggerFactory;
import com.cyberscraft.uep.push.client.domain.PushInfo;
import com.cyberscraft.uep.push.client.errors.PushErrorType;
import com.cyberscraft.uep.push.client.exception.PushException;
import com.cyberscraft.uep.push.client.provider.IPushProvider;
import com.cyberscraft.uep.push.client.service.IPushProviderService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/***
 *
 * @date 2021-12-31
 * <AUTHOR>
 ***/
@Service
public class DefaultPushProviderServiceImpl implements IPushProviderService {
    /***
     *
     */
    private final static Logger LOG = DigitalSeeLoggerFactory.getLogger(DefaultPushProviderServiceImpl.class, LogTag.PUSH_CLIENT);

    private List<IPushProvider> providers = new ArrayList<>();

    private ConcurrentHashMap<Integer, IPushProvider> MODE_PROVIDER_MAP = new ConcurrentHashMap<>();


    public List<IPushProvider> getProviders() {
        return providers;
    }

    @Autowired
    public void setProviders(List<IPushProvider> providers) {
        this.providers = providers;
        this.initPushProviders();
    }

    /***
     * 初始化推送提供者Mode Provider MAP列表
     */
    private synchronized void initPushProviders() {
        MODE_PROVIDER_MAP.clear();
        if (providers == null || providers.size() == 0) {
            return;
        }
        for (IPushProvider provider : providers) {
            Set<Integer> modes = provider.getSupportedModes();
            for (Integer mode : modes) {
                MODE_PROVIDER_MAP.put(mode, provider);
            }
        }
    }


    @Override
    public void pushToClient(PushInfo push) throws PushException {
        if (push == null) {
            throw new PushException(PushErrorType.PUSH_INVALID);
        }
        if (push.getPushMode() == null) {
            throw new PushException(PushErrorType.PUSH_MODE_INVALID);
        }
        if (StringUtils.isBlank(push.getTenantId())) {
            throw new PushException(ExceptionCodeEnum.TENANT_INVALID);
        }
        IPushProvider provider = findPushProvider(push.getPushMode());
        if (provider == null) {
            LOG.warn("未找到{}对应的推送提供者", push.getPushMode());
            return;
        }
        TenantHolder.setTenantCode(push.getTenantId());
        try {
            provider.push(push);
        } catch (Exception e) {
            LOG.warn("推送数据到终端时发生错误:{}", e.getMessage());
        }
    }

    /****
     *
     * @param mode
     * @return
     */
    private IPushProvider findPushProvider(Integer mode) {
        if (mode == null) {
            return null;
        }
        return MODE_PROVIDER_MAP.get(mode);
    }
}
