package com.cyberscraft.uep.push.client.domain;

import java.io.Serializable;

/***
 * 推送信息的接收对像
 * @date 2021-12-26
 * <AUTHOR>
 ***/
public class PushReceiver implements Serializable {

    /***
     * 对应的用户ID
     */
    private String loginId;

    /***
     * 对应的设备umid;
     */
    private String udid;

    /****
     * 当前对应的设备平台类型
     */
    private Integer platform;

    public String getLoginId() {
        return loginId;
    }

    public void setLoginId(String loginId) {
        this.loginId = loginId;
    }

    public String getUdid() {
        return udid;
    }

    public void setUdid(String udid) {
        this.udid = udid;
    }

    public Integer getPlatform() {
        return platform;
    }

    public void setPlatform(Integer platform) {
        this.platform = platform;
    }

    /***
     *
     */
    public PushReceiver() {
    }

    /****
     *
     * @param loginId
     * @param udid
     */
    public PushReceiver(String loginId, String udid) {
        this.loginId = loginId;
        this.udid = udid;
    }

    public PushReceiver(String loginId, String udid, Integer platform) {
        this.loginId = loginId;
        this.udid = udid;
        this.platform = platform;
    }

    @Override
    public String toString() {
        return "PushReceiver{" +
                "loginId='" + loginId + '\'' +
                ", udid='" + udid + '\'' +
                ", platform='" + platform + '\'' +
                '}';
    }
}
