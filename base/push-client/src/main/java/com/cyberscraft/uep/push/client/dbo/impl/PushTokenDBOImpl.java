package com.cyberscraft.uep.push.client.dbo.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cyberscraft.uep.common.enums.ExceptionCodeEnum;
import com.cyberscraft.uep.common.constant.SysConstant;
import com.cyberscraft.uep.common.dto.PageView;
import com.cyberscraft.uep.common.dto.Pagination;
import com.cyberscraft.uep.common.util.PagingUtil;
import com.cyberscraft.uep.push.client.dao.PushTokenDao;
import com.cyberscraft.uep.push.client.dbo.PushTokenDBO;
import com.cyberscraft.uep.push.client.dto.PushFailureRecordQueryDto;
import com.cyberscraft.uep.push.client.dto.PushTokenQueryDto;
import com.cyberscraft.uep.push.client.entity.PushFailureRecordEntity;
import com.cyberscraft.uep.push.client.entity.PushTokenEntity;
import com.cyberscraft.uep.push.client.errors.PushErrorType;
import com.cyberscraft.uep.push.client.exception.PushException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 推送token信息表 服务实现类
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2019-12-31
 */
@Service
public class PushTokenDBOImpl extends ServiceImpl<PushTokenDao, PushTokenEntity> implements PushTokenDBO {

    @Resource
    private PushTokenDao pushTokenDao;

    @Override
    public PushTokenEntity getByUserIdAndUdid(Long userId, String udid) throws PushException {
        if (StringUtils.isBlank(udid)) {
            throw new PushException(ExceptionCodeEnum.PARAM_INVALID);
        }
        if (userId == null) {
            throw new PushException(ExceptionCodeEnum.PARAM_INVALID);
        }
        LambdaQueryWrapper<PushTokenEntity> queryWrapper = new QueryWrapper().lambda();
        queryWrapper.eq(PushTokenEntity::getUserId, userId);
        queryWrapper.eq(PushTokenEntity::getUdid, udid);
        List<PushTokenEntity> list = this.pushTokenDao.selectList(queryWrapper);
        return (list != null && list.size() > 0) ? list.get(0) : null;
    }

    @Override
    public PushTokenEntity getByLoginIdAndUdid(String loginId, String udid) throws PushException {
        if (StringUtils.isBlank(udid)) {
            throw new PushException(ExceptionCodeEnum.PARAM_INVALID);
        }
        if (StringUtils.isBlank(loginId)) {
            throw new PushException(ExceptionCodeEnum.PARAM_INVALID);
        }
        LambdaQueryWrapper<PushTokenEntity> queryWrapper = new QueryWrapper().lambda();
        queryWrapper.eq(PushTokenEntity::getLoginId, loginId);
        queryWrapper.eq(PushTokenEntity::getUdid, udid);
        List<PushTokenEntity> list = this.pushTokenDao.selectList(queryWrapper);
        return (list != null && list.size() > 0) ? list.get(0) : null;
    }


    @Override
    public void setTokenExpired(Long id) throws PushException {
        if (id == null) {
            throw new PushException(PushErrorType.PUSH_RECORD_ID_INVALID);
        }
        LambdaUpdateWrapper<PushTokenEntity> updateWrapper = new UpdateWrapper<PushTokenEntity>()
                .lambda()
                .eq(PushTokenEntity::getId, id)
                .set(PushTokenEntity::getStatus, SysConstant.FALSE_VALUE)
                .set(PushTokenEntity::getExpireTime, LocalDateTime.now());
        this.update(updateWrapper);
    }

    @Override
    public void updateTokenCheckTime(Long id, LocalDateTime nextCheckTime) throws PushException {
        if (id == null) {
            throw new PushException(PushErrorType.PUSH_RECORD_ID_INVALID);
        }
        LambdaUpdateWrapper<PushTokenEntity> updateWrapper = new UpdateWrapper<PushTokenEntity>()
                .lambda()
                .eq(PushTokenEntity::getId, id)
                .set(PushTokenEntity::getNextCheckTime, nextCheckTime);
        this.update(updateWrapper);
    }

    @Override
    public PageView<PushTokenEntity> page(Pagination<PushTokenQueryDto> page) throws PushException {
        IPage<PushTokenEntity> myPage = PagingUtil.toMybatisPage(page);
        String[] selectStr = page.getRequestAttrs() != null ? page.getRequestAttrs().toArray(new String[0]) : new String[0];
        QueryWrapper<PushTokenEntity> queryWrapper = new QueryWrapper<PushTokenEntity>().select(selectStr);

        // 查询失败重试记录列表
        PushTokenQueryDto queryDto = page.getQueryDto();

        LambdaQueryWrapper<PushTokenEntity> lambdaQueryWrapper = queryWrapper.lambda();
        setQueryCondition(queryDto, lambdaQueryWrapper);

        IPage<PushTokenEntity> temp = this.page(myPage, lambdaQueryWrapper);
        return PagingUtil.toPageView(temp);
    }


    /***
     *
     * @param queryDto
     * @param queryWrapper
     */
    void setQueryCondition(PushTokenQueryDto queryDto, LambdaQueryWrapper<PushTokenEntity> queryWrapper) {
        if (queryDto != null) {
            //允许的最大重试次数
            if (queryDto.getStatus() != null) {
                queryWrapper.eq(PushTokenEntity::getStatus, queryDto.getStatus());
            }
            //当前查询的最小验证时间
            if (queryDto.getLessNextCheckTime() != null) {
                queryWrapper.gt(PushTokenEntity::getNextCheckTime, queryDto.getLessNextCheckTime());
            }
            //当前查询的最大验证时间
            if (queryDto.getMaxNextCheckTime() != null) {
                queryWrapper.lt(PushTokenEntity::getNextCheckTime, queryDto.getMaxNextCheckTime());
            }
        }
    }
}
