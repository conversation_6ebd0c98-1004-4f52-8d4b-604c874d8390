package com.cyberscraft.uep.push.client.provider.apns;

import com.cyberscraft.uep.common.enums.ExceptionCodeEnum;
import com.cyberscraft.uep.common.constant.SysConstant;
import com.cyberscraft.uep.push.client.constant.PushClientTypeConstant;
import com.cyberscraft.uep.push.client.domain.*;
import com.cyberscraft.uep.push.client.errors.PushErrorType;
import com.cyberscraft.uep.push.client.exception.PushException;
import com.cyberscraft.uep.push.client.provider.IMdmPushClient;
import com.cyberscraft.uep.push.client.provider.IMdmPushListener;
import com.cyberscraft.uep.push.client.provider.IMdmPushTokenCheckProvider;
import com.turo.pushy.apns.ApnsClient;
import com.turo.pushy.apns.PushNotificationResponse;
import com.turo.pushy.apns.util.ApnsPayloadBuilder;
import com.turo.pushy.apns.util.SimpleApnsPushNotification;
import io.netty.util.concurrent.Future;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/***
 * 苹果MDM推送客户端实现类，通过引入pushy进行苹果指令的推送
 * @date 2021-01-02
 * <AUTHOR>
 ***/
@Component("apnsMdmPushClient")
public class ApnsMdmPushClientImpl extends AbstractApnsPushClient implements IMdmPushClient, IMdmPushTokenCheckProvider {


    @Override
    public String getPushClientType() {
        return PushClientTypeConstant.AMDM;
    }

    /****
     * 因为失败的推送会进行存储到失败重试队列中，所以需要清除
     * @param push
     * @param receivers
     * @return
     * @throws PushException
     */
    @Override
    public boolean push(PushInfo push, List<PushReceiver> receivers) throws PushException {

        for (PushReceiver receiver : receivers) {
            try {
                PushToken token = pushTokenService.getPushToken(receiver.getLoginId(), receiver.getUdid());
                if (token == null) {
                    LOG.warn("token not found: {} {} {}",
                            receiver.getLoginId(), receiver.getUdid(), receiver.getPlatform());
                    throw new PushException(PushErrorType.PUSH_TOKEN_INVALID);
                }
                if (SysConstant.FALSE_VALUE.equals(token.getStatus())) {
                    LOG.warn("token status invalid: {} {} {}",
                            receiver.getLoginId(), receiver.getUdid(), receiver.getPlatform());
                    throw new PushException(PushErrorType.PUSH_TOKEN_EXPIRED);
                }
                push(push, receiver, token, (push1, receiver1, result1) -> {
                    if (result1.isSuccess()) {
                        //savePushSuccessRecord(push1, result1.getPushClientType());
                    } else {
                        //保证是新的记录，原来的记录已经删除
                        saveFailurePushRecord(null, push1, receiver1, result1.getMsg(), result1.getPushClientType());
                    }
                });
            } catch (Exception e) {
                saveFailurePushRecord(null, push, receiver, e.getMessage(), getPushClientType());
            }
        }
        //因为前面如果每一条失败都会重新加入到重发队列中，所以这里需要标识原来的已经发送成功。
        savePushSuccessRecord(push, getPushClientType());
        return true;
    }

    /***
     * 进行推送
     * @param push
     * @param receiver
     * @param token
     * @param pushListener
     * @throws PushException
     */
    protected void push(final PushInfo push, final PushReceiver receiver, final PushToken token, IMdmPushListener pushListener) throws PushException {
        try {
            ApnsClient apnsClient = getApnsClient();
            if (apnsClient == null) {
                throw new PushException(PushErrorType.PUSH_CLIENT_ERROR.getCode(), "未能正确初始化Apns客户端");
            }
            String sendPayload = ApnsPayloadBuilder.buildMdmPayload(token.getPushMagic());//. payloadBuilder.buildWithDefaultMaximumLength();
            final SimpleApnsPushNotification pushNotification = new SimpleApnsPushNotification(token.getToken(), apnsPushConfiguration.getTopic(), sendPayload);
            final Future sendNotificationFuture = apnsClient.sendNotification(pushNotification);
            sendNotificationFuture.addListener((future) -> {
                //进行回调处理
                PushResult result = new PushResult();
                result.setSuccess(true);
                result.setPushClientType(getPushClientType());
                try {
                    final PushNotificationResponse response = ((Future<PushNotificationResponse>) future).getNow();
                    String msg = "";
                    if (response == null) {
                        msg = future.cause() != null ? future.cause().getMessage() : "未知错误";
                    }
                    if (response != null && response.isAccepted()) {
                        LOG.info("Push notification accepted by APNs gateway.");
                    } else {
                        result.setMsg(response != null ? response.getRejectionReason() : msg);
                        result.setSuccess(false);
                        LOG.warn("当前苹果推送结果错误信息:{},推送用户为:{}", result.getMsg(), receiver != null ? receiver.toString() : null);
                    }
                } catch (Exception e) {
                    LOG.warn("当前苹果推送结果错误信息:{},推送用户为:{}", result.getMsg(), receiver != null ? receiver.toString() : null);
                    result.setSuccess(false);
                    result.setMsg(e.getMessage());
                }
                //
                pushListener.onPushComplete(push, receiver, result);
            });
        } catch (PushException e) {
            throw e;
        } catch (final Exception e) {
            LOG.warn("当前使用苹果设备级推送发生错误:{}", e.getMessage(), e.getMessage(), receiver != null ? receiver.toString() : null);
            throw new PushException(ExceptionCodeEnum.INNER_ERROR.getCode(), e.getMessage());
        }
    }


    @Override
    public void check(PushToken token) throws PushException {
        try {
            ApnsClient apnsClient = getApnsClient();
            if (apnsClient == null) {
                throw new PushException(PushErrorType.PUSH_CLIENT_ERROR.getCode(), "未能正确初始化Apns客户端");
            }
            String sendPayload = ApnsPayloadBuilder.buildMdmPayload(token.getPushMagic());//. payloadBuilder.buildWithDefaultMaximumLength();
            final SimpleApnsPushNotification pushNotification = new SimpleApnsPushNotification(token.getToken(), apnsPushConfiguration.getTopic(), sendPayload);
            final Future sendNotificationFuture = apnsClient.sendNotification(pushNotification);
            sendNotificationFuture.addListener((future) -> {
                //进行回调处理
                PushResult result = new PushResult();
                result.setSuccess(true);
                result.setPushClientType(getPushClientType());
                try {
                    final PushNotificationResponse response = ((Future<PushNotificationResponse>) future).getNow();
                    String msg = "";
                    if (response == null) {
                        msg = future.cause() != null ? future.cause().getMessage() : "未知错误";
                    }
                    if (response != null && response.isAccepted()) {
                        LOG.info("当前Token验证苹果推送结果成功,Token用户为:{},{}", token.getLoginId(), token.getUdid());
                    } else {
                        result.setMsg(response != null ? response.getRejectionReason() : msg);
                        result.setSuccess(false);
                        LOG.warn("当前Token验证苹果推送结果错误信息:{},推送用户为:{},{}", result.getMsg(), token.getLoginId(), token.getUdid());
                    }
                } catch (Exception e) {
                    LOG.warn("当前Token验证苹果推送结果错误信息:{},推送用户为:{},{}", result.getMsg(), token.getLoginId(), token.getUdid());
                    result.setSuccess(false);
                    result.setMsg(e.getMessage());
                }
                //只有推送失败，并且错误消息是BadDeviceToken进行处理
                if (!result.isSuccess() && "BadDeviceToken".equalsIgnoreCase(result.getMsg())) {
                    setPushTokenExpired(token);
                }
            });
        } catch (final Exception e) {
            LOG.warn("当前使用苹果推送验证推送token发生错误:{},当前用户:{},{}", e.getMessage(), e.getMessage(), token.getLoginId(), token.getUdid());
            //throw new PushException(ExceptionCodeEnum.INNER_ERROR.getCode(), e.getMessage());
        }
    }

    @Transactional
    public void setPushTokenExpired(PushToken pushToken) {
        pushTokenService.setPushTokenInvalid(pushToken);
    }
}
