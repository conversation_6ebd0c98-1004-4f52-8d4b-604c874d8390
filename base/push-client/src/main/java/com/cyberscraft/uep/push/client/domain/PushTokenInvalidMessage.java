package com.cyberscraft.uep.push.client.domain;

import java.io.Serializable;
import java.time.LocalDateTime;

/***
 * 推送Token过期消息
 * @date 2021/2/27
 * <AUTHOR>
 ***/
public class PushTokenInvalidMessage implements Serializable {

    private static final long serialVersionUID = -6759081828772169753L;
    /***
     *
     */
    private String loginId;
    /***
     *
     */
    private String udid;

    /***
     *
     */
    private LocalDateTime expireTime;

    /***
     * 租户ID
     */
    private String tenantId;

    public PushTokenInvalidMessage() {
    }

    public PushTokenInvalidMessage(String logidId, String udid, LocalDateTime expireTime, String tenantId) {
        this.loginId = logidId;
        this.udid = udid;
        this.expireTime = expireTime;
        this.tenantId = tenantId;
    }

    public String getLoginId() {
        return loginId;
    }

    public void setLoginId(String loginId) {
        this.loginId = loginId;
    }

    public String getUdid() {
        return udid;
    }

    public void setUdid(String udid) {
        this.udid = udid;
    }

    public LocalDateTime getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(LocalDateTime expireTime) {
        this.expireTime = expireTime;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    @Override
    public String toString() {
        return "PushTokenExpiredMessage{" +
                "loginId='" + loginId + '\'' +
                ", udid='" + udid + '\'' +
                ", expireTime=" + expireTime +
                ", tenantId=" + tenantId +
                '}';
    }
}
