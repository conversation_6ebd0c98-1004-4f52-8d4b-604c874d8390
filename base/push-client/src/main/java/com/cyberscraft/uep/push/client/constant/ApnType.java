package com.cyberscraft.uep.push.client.constant;

/***
 *
 * @date 2021/1/9
 * <AUTHOR>
 ***/
public enum ApnType {
    /***
     * MDM管理应用
     */
    MDM(0),
    /***
     * MCM应用
     */
    MCM(1);

    private int code;

    ApnType(int errorCode) {
        this.code = errorCode;
    }


    /***
     * 返回代码时，自动返回MDM前缀码对应的代码，加上业务本身的错误代码
     */
    public int getCode() {
        return this.code;
    }
}
