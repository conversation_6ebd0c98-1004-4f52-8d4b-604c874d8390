package com.cyberscraft.uep.push.client.provider.apns;

import com.cyberscraft.uep.common.constant.SysConstant;
import com.cyberscraft.uep.common.enums.LogTag;
import com.cyberscraft.uep.common.logger.DigitalSeeLoggerFactory;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.common.util.SnowflakeIDUtil;
import com.cyberscraft.uep.push.client.configuration.PushConfiguartion;
import com.cyberscraft.uep.push.client.domain.PushCredential;
import com.cyberscraft.uep.push.client.domain.PushCredentialConfig;
import com.cyberscraft.uep.push.client.domain.PushInfo;
import com.cyberscraft.uep.push.client.domain.PushReceiver;
import com.cyberscraft.uep.push.client.entity.PushFailureRecordEntity;
import com.cyberscraft.uep.push.client.errors.PushErrorType;
import com.cyberscraft.uep.push.client.exception.PushException;
import com.cyberscraft.uep.push.client.service.IPushCredentialService;
import com.cyberscraft.uep.push.client.service.IPushFailureRecordService;
import com.cyberscraft.uep.push.client.service.IPushTokenService;
import com.turo.pushy.apns.ApnsClient;
import com.turo.pushy.apns.ApnsClientBuilder;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/***
 * @date 2021/2/12
 * <AUTHOR>
 ***/
@Component
public abstract class AbstractApnsPushClient {


    @Resource
    protected IPushCredentialService pushCredentialService;

    /***
     * 苹果推送token接口
     */
    @Resource
    protected IPushTokenService pushTokenService;


    /***
     * 推送配置
     */
    @Resource
    protected PushConfiguartion pushConfiguartion;


    @Resource
    protected IPushFailureRecordService pushFailureRecordService;


    protected PushCredentialConfig apnsPushConfiguration;


    /***
     * 苹果推送
     */
    private ApnsClient apnsClient;

    /***
     * 是否已经初始化了ApnsClient客户端对像
     */
    private volatile boolean isInitedApnsClient = false;

    /***
     *
     */
    protected final static Logger LOG = DigitalSeeLoggerFactory.getLogger(AbstractApnsPushClient.class, LogTag.PUSH_CLIENT);

    /***
     *
     * @return
     */
    public abstract String getPushClientType();

    /***
     * 获取推送客户端
     * @return
     * @throws Exception
     */
    protected ApnsClient getApnsClient() throws Exception {
        if (isInitedApnsClient) {
            return apnsClient;
        }
        synchronized (this) {
            apnsClient = null;
            PushCredential credential = pushCredentialService.getPushCredentialByPushType(getPushClientType());
            if (credential == null) {
                throw new PushException(PushErrorType.PUSH_CREDENTIAL_INVALID);
            }
            apnsPushConfiguration = credential.getConfig();
            ApnsClient client = new ApnsClientBuilder()
                    .setApnsServer(apnsPushConfiguration.getServerUrl())
                    .setClientCredentials(credential.getInputStream(), apnsPushConfiguration.getPassword())
                    .build();
            apnsClient = client;
            isInitedApnsClient = true;
        }
        return apnsClient;
    }

    /***
     * 保存推送成功日志，需要将已经存在的失败日志删除，已免重复推送
     * @param push
     * @param pushClientType
     */
    protected void savePushSuccessRecord(PushInfo push, String pushClientType) {
        if (push.getId() != null) {
            //PushFailureRecordEntity recordEntity = pushFailureRecordService.get(push.getId());
            pushFailureRecordService.remove(push.getId());
        }
    }

    /****
     * 保存推送失败记录，用于进行下一次的推送处理
     * @param push
     * @param receiver
     * @param errorMsg
     */
    protected void saveFailurePushRecord(Long id, PushInfo push, PushReceiver receiver, String errorMsg, String pushClientType) {
        try {
            PushFailureRecordEntity recordEntity = null;
            if (id != null) {
                recordEntity = pushFailureRecordService.get(id);
            }
            boolean add = false;
            if (recordEntity == null) {
                recordEntity = new PushFailureRecordEntity();
                recordEntity.setId(SnowflakeIDUtil.getId());
                recordEntity.setFailureTimes(0);
                add = true;
            }
            if (recordEntity.getFailureTimes() == null) {
                recordEntity.setFailureTimes(0);
            }
            recordEntity.setErrorMsg(errorMsg);
            recordEntity.setFailureTimes(recordEntity.getFailureTimes() + 1);

            if (recordEntity.getFailureTimes() > pushConfiguartion.getMaxFailurePushTimes()) {
                pushFailureRecordService.remove(push.getId());
                return;
            }
            recordEntity.setIsBatch(SysConstant.FALSE_VALUE);
            recordEntity.setNextPushTime(LocalDateTime.now().plusMinutes(recordEntity.getFailureTimes() * recordEntity.getFailureTimes() * pushConfiguartion.getFailuredRepushInterval()));
            recordEntity.setPayload(push.getPayload());
            recordEntity.setPushMode(push.getPushMode());
            //recordEntity.setPushPayload(push.getPayload());
            //recordEntity.setPushReceivers(token != null ? token.getDudid() : null);
            //recordEntity.setReceivers(JsonUtil.obj2Str(receiver));
            List<PushReceiver> receiverList = new ArrayList<>();
            receiverList.add(receiver);
            String receivers = JsonUtil.obj2Str(receiverList);
            //recordEntity.setPushReceivers(receivers);
            recordEntity.setReceivers(receivers);
            //推送客户端类型
            recordEntity.setPushType(pushClientType);
            recordEntity.setPushTime(LocalDateTime.now());
            recordEntity.setResult(SysConstant.FALSE);
            recordEntity.setSender(push.getSender());
            recordEntity.setTenantId(push.getTenantId());
            if (add) {
                pushFailureRecordService.add(recordEntity);
            } else {
                pushFailureRecordService.modify(recordEntity);
            }
        } catch (Exception e) {
            LOG.warn("记录推送失败记录发生错误:{}", e.getMessage());
        }
    }

}
