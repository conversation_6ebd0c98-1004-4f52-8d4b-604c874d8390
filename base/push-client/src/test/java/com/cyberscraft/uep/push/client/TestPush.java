package com.cyberscraft.uep.push.client;

import com.cyberscraft.uep.common.enums.Platform;
import com.cyberscraft.uep.push.client.constant.PushMode;
import com.cyberscraft.uep.push.client.domain.PushInfo;
import com.cyberscraft.uep.push.client.domain.PushReceiver;
import com.cyberscraft.uep.push.client.service.IPushClientService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = MockPushClientApplication.class)
public class TestPush {

    @Resource
    private IPushClientService pushClientService;

    @Test
    public void run() throws Exception {
//        List<String> list = new ArrayList<>();
//        list.add("83");
//        list.add("274");
//        pushClient.push("admin","57",null, null, list, null,"payload");

        PushInfo pushInfo = new PushInfo();
        pushInfo.setTenantId("57");
        pushInfo.setPushMode(PushMode.APP.getCode());
        pushInfo.setPayload("payload");
        pushInfo.setSender("admin");
        List<PushReceiver> receiverList = new ArrayList<>();
        receiverList.add(new PushReceiver("83", "d1", Platform.ANDROID.getValue()));
        receiverList.add(new PushReceiver("274", "d2", Platform.IOS.getValue()));
        pushInfo.setReceivers(receiverList);
        pushClientService.push(pushInfo);
    }
}
