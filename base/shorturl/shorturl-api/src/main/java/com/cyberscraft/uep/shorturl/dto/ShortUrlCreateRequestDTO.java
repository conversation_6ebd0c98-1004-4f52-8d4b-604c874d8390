package com.cyberscraft.uep.shorturl.dto;

import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/***
 *
 * @date 2021/10/23
 * <AUTHOR>
 ***/

public class ShortUrlCreateRequestDTO implements Serializable {
    /***
     * 登录用户名
     */
    @ApiModelProperty(value = "当前要进行创建短连接的URL地址列表")
    @NotNull
    private List<String> urls;

    public List<String> getUrls() {
        return urls;
    }

    public void setUrls(List<String> urls) {
        this.urls = urls;
    }
}
