package com.cyberscraft.uep.shorturl.core.service.impl;

import com.cyberscraft.uep.shorturl.core.constant.CacheNameConstant;
import com.cyberscraft.uep.shorturl.core.service.IShortUrlCacheService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Service;

/***
 *
 * @date 2021/10/29
 * <AUTHOR>
 ***/
@Service
public class ShortUrlCacheServiceImpl implements IShortUrlCacheService {

    /**
     * spring 提供的CacheManager
     */
    private final CacheManager cacheManager;


    @Autowired
    public ShortUrlCacheServiceImpl(@Qualifier(value = CacheNameConstant.SHORTURL_CACHE_MANAGE_NAME) CacheManager cacheManager) {
        this.cacheManager = cacheManager;
    }

    @Override
    public String get(String base58Id) {
        Cache.ValueWrapper vw = cacheManager.getCache(CacheNameConstant.SHORTURL_CACHE_NAME).get(base58Id);
        return vw != null ? (String) vw.get() : null;
    }

    @Override
    public void save(String base58Id, String url) {
        cacheManager.getCache(CacheNameConstant.SHORTURL_CACHE_NAME).put(base58Id, url);
    }
}
