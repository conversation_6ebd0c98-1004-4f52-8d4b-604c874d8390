package com.cyberscraft.uep.shorturl.core.dbo;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cyberscraft.uep.shorturl.core.entity.ShortUrlEntity;

import java.util.List;

/***
 *
 * @date 2021/10/23
 * <AUTHOR>
 ***/
public interface ShortUrlDBO extends IService<ShortUrlEntity> {

    /***
     *
     * @param url
     * @return
     */
    ShortUrlEntity getByUrl(String url);

    /***
     *
     * @param urls
     * @return
     */
    List<ShortUrlEntity> getListByUrl(List<String> urls);

    /***
     *
     * @param urls
     * @return
     */
    List<ShortUrlEntity> getListByShortUrl(List<String> urls);
}
