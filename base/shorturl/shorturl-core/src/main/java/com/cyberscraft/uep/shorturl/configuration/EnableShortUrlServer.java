package com.cyberscraft.uep.shorturl.configuration;

import org.springframework.context.annotation.Import;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/***
 *
 * @date 2021/10/23
 * <AUTHOR>
 ***/
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Import({ShortUrlServerAutoConfiguration.class})
public @interface EnableShortUrlServer {

}
