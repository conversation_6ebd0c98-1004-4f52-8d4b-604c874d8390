package com.cyberscraft.uep.shorturl.core.service;

import com.cyberscraft.uep.shorturl.dto.ShortUrlVO;

import java.util.List;

/***
 *
 * @date 2021/10/23
 * <AUTHOR>
 ***/
public interface IShortUrlService {

    /***
     *
     * @param urls
     * @return
     */
    List<ShortUrlVO> create(List<String> urls);

    /***
     *
     * @param urls
     * @return
     */
    List<ShortUrlVO> getListByShortUrl(List<String> urls);

    /***
     *
     * @param urls
     * @return
     */
    List<ShortUrlVO> getListByUrl(List<String> urls);

    /***
     * 根据短连接标识获取对应的连接地址
     * @param key
     * @return
     */
    String getUrlByKey(String key);
}
