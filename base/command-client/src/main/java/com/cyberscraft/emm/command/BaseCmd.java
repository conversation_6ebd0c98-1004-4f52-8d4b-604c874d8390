package com.cyberscraft.emm.command;

import com.cyberscraft.emm.enums.CmdOper;
import com.cyberscraft.emm.enums.CmdType;
import com.cyberscraft.emm.enums.DomainType;
import com.cyberscraft.uep.common.enums.Platform;

import java.io.Serializable;
import java.util.UUID;

/**
 * Created by gaohongtao on 2016/7/29.
 * 基本指令
 */
public class BaseCmd implements Serializable {
    private static final long serialVersionUID = 8427377536966616699L;

    public String udid;                 // 设备唯一标识
    public String uuid;                 // 创建指令时生成的指令唯一标识(对于策略类型指令，代表策略ID)
    // -------------------------------------------------
    public CmdOper cmdOper;             // 指令操作类型
    // -------------------------------------------------
    public Platform platform;           // 设备平台类型
    public CmdType cmdType;             // 指令类型
    public String tenantId = "mdm";             // 租户id

    public Long createTimestamp;         // 创建指令时的时间戳

    public DomainType domainType;       // 0-个人域 1-企业域
    
    public String seriNum; // 日志流水号
    
    public BaseCmd() {
        this.uuid = UUID.randomUUID().toString();
        this.cmdOper = CmdOper.ADD;
        this.domainType = DomainType.PERSONAL;
        this.createTimestamp = System.nanoTime();
    }

    public String getUdid() {
        return udid;
    }

    public void setUdid(String udid) {
        this.udid = udid;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public CmdOper getCmdOper() {
        return cmdOper;
    }

    public void setCmdOper(CmdOper cmdOper) {
        this.cmdOper = cmdOper;
    }

    public Platform getPlatform() {
        return platform;
    }

    public void setPlatform(Platform platform) {
        this.platform = platform;
    }

    public CmdType getCmdType() {
        return cmdType;
    }

    public void setCmdType(CmdType cmdType) {
        this.cmdType = cmdType;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public DomainType getDomainType() {
        return domainType;
    }

    public void setDomainType(DomainType domainType) {
        this.domainType = domainType;
    }


    public Long getCreateTimestamp() {
        return createTimestamp;
    }

    public void setCreateTimestamp(Long createTimestamp) {
        this.createTimestamp = createTimestamp;
    }

	protected String getSeriNum() {
		return seriNum;
	}

	protected void setSeriNum(String seriNum) {
		this.seriNum = seriNum;
	}
	@Override
	public String toString() {
		return "BaseCmd [udid=" + udid + ", uuid=" + uuid + ", cmdOper=" + cmdOper + ", platform=" + platform
				+ ", cmdType=" + cmdType + ", tenantId=" + tenantId + ", createTimestamp=" + createTimestamp
				+ ", domainType=" + domainType + ", seriNum=" + seriNum + "]";
	}
}
