package com.cyberscraft.emm.command;

import java.io.Serializable;

/**
 * 如类型声明处，乃一NotificationCmd<br>
 * <p>
 * 扩展appId，types等多个属性
 * 
 * <AUTHOR>
 *
 */
public final class AppUpdateCmd extends NotificationCmd implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 2032312870705781058L;
	
	private String appId;
	/**
	 * 应用图标上的那个小圆点
	 */
	private int badge;
	private String types;

	public String getAppId() {
		return appId;
	}

	public void setAppId(String appId) {
		this.appId = appId;
	}

	public int getBadge() {
		return badge;
	}
	
	public void setBadge(int badge) {
		this.badge = badge;
	}
	
	public void setTypes(String types) {
		this.types = types;
	}
	
	public String getTypes() {
		return types;
	}
}
