package com.cyberscraft.emm.command;

/**
 * 用于iOS的APP和DOC的ADD及UPDATE通知指令.
 * ========
 * 
 * 参考：
 *   + [IOSCmdProfile#getAppUpdate](http://192.168.22.67:7990/projects/SERVER/repos/platformjava/browse/src/com/cyb/platform/profile/IOSCmdProfile.java?until=ca3837a1c6e292cfd489a407721ffcb4306ced8c#394)
 *   + [IOSCmdProfile#getAppAdd](http://192.168.22.67:7990/projects/SERVER/repos/platformjava/browse/src/com/cyb/platform/profile/IOSCmdProfile.java?until=ca3837a1c6e292cfd489a407721ffcb4306ced8c#401)
 *   + [IOSCmdProfile#getDocAdd](http://192.168.22.67:7990/projects/SERVER/repos/platformjava/browse/src/com/cyb/platform/profile/IOSCmdProfile.java?until=ca3837a1c6e292cfd489a407721ffcb4306ced8c#408)
 *   + [IOSCmdProfile#getDocUpdate](http://192.168.22.67:7990/projects/SERVER/repos/platformjava/browse/src/com/cyb/platform/profile/IOSCmdProfile.java?until=ca3837a1c6e292cfd489a407721ffcb4306ced8c#415)
 * 
 * <AUTHOR>
 *
 */
public final class IosNotifyCmd extends NotificationCmd {

	public String getAlert() {
		return alert;
	}
	public void setAlert(String alert) {
		this.alert = alert;
	}
	
	/**
	 * 
	 */
	private static final long serialVersionUID = -5481988530066861651L;

	private String alert;
}
