package com.cyberscraft.emm.command;

import java.io.Serializable;
import java.util.Set;

public class ProcessstrategyInfo implements Serializable {
    private String processactionid;
    private String processdelaytime;
    private String processvalue;
    private String lockPass;
    private Set<String> appPkgList;

    public String getProcessactionid() {
        return processactionid;
    }

    public void setProcessactionid(String processactionid) {
        this.processactionid = processactionid;
    }

    public String getProcessdelaytime() {
        return processdelaytime;
    }

    public void setProcessdelaytime(String processdelaytime) {
        this.processdelaytime = processdelaytime;
    }

    public String getProcessvalue() {
        return processvalue;
    }

    public void setProcessvalue(String processvalue) {
        this.processvalue = processvalue;
    }

    public String getLockPass() {
        return lockPass;
    }

    public void setLockPass(String lockPass) {
        this.lockPass = lockPass;
    }

    public Set<String> getAppPkgList() {
        return appPkgList;
    }

    public void setAppPkgList(Set<String> appPkgList) {
        this.appPkgList = appPkgList;
    }

}
