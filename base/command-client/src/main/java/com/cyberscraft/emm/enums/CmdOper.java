package com.cyberscraft.emm.enums;

/**
 * Created by Administrator on 2016/7/29.
 * 指令操作类型
 */
public enum CmdOper {
    ADD(1),                 // 添加
    RE_SEND(9);             // 重发

    private final int value;

    CmdOper(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static CmdOper findByValue(int value) {
        switch (value) {
            case 1:
                return ADD;
            case 9:
                return RE_SEND;
            default:
                return null;
        }
    }
}
