package com.cyberscraft.emm.command;

import java.io.Serializable;

public class LicenseCmdInfo implements Serializable{

	private static final long serialVersionUID = -2646783550127256933L;

	private int licenseType; // required
	private int oper; // required
	private String license; // required

	public int getLicenseType() {
		return licenseType;
	}
	public void setLicenseType(int licenseType) {
		this.licenseType = licenseType;
	}
	public int getOper() {
		return oper;
	}
	public void setOper(int oper) {
		this.oper = oper;
	}
	public String getLicense() {
		return license;
	}
	public void setLicense(String license) {
		this.license = license;
	}
	
}
