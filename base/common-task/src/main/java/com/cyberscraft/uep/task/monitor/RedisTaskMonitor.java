package com.cyberscraft.uep.task.monitor;

import com.cyberscraft.uep.task.Task;
import com.cyberscraft.uep.task.TaskHolder;
import com.cyberscraft.uep.task.TaskStatus;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 *  一个简单的分布式的、自管理的、去中心化的任务监控器
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/26 2:20 下午
 */
public abstract class RedisTaskMonitor {
    protected Logger logger = LoggerFactory.getLogger(getClass());
    private final static String TASK_MONITOR_LOCK_KEY = "TASK:MONITOR:%s";

    private final Map<String, Task> tasks = new ConcurrentHashMap<>();

    private RedissonClient redissonClient;
    private int breakTimes = 0;
    private String monitorName;

    public RedisTaskMonitor(String monitorName) {
        this.monitorName = monitorName;
    }

    public RedissonClient getRedissonClient() {
        return redissonClient;
    }

    public void setRedissonClient(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }

    public String getMonitorName() {
        return monitorName;
    }

    /**
     * 启动监控
     */
    public void run() {
        //开启状态通报线程
        new Thread(() -> {
            while (true) {
                try {
                    Thread.sleep(5000);
                    notifyStatus();
                } catch (Exception e) {
                    logger.warn("notify task status failed", e);
                }
            }
        }).start();

        //开启监控线程
        new Thread(()->{
            while (true) {
                try {
                    Thread.sleep(20000);
                    monitorAllTasks();
                } catch (Exception e) {
                    logger.warn("monitor all task failed", e);
                }
            }
        }).start();
    }

    /**
     * 登记task
     * @param task
     */
    public void registerTask(Task task) {
        if (task.getId().equals("0")) return;

        if (getOwnTask(task.getId()) != null) {
            logger.warn("own task is exist, can not register, id: {}", task.getId());
            throw new RuntimeException("own task is exist, can not register");
        }
        if (getTask(task.getId()) != null) {
            logger.warn("global task is exist, can not register, id: {}", task.getId());
            throw new RuntimeException("global task is exist, can not register");
        }

        task.setStatus(TaskStatus.RUNNING);
        tasks.put(task.getId(), task);
        logger.info("{} register task: {}, tasks size is: {}", monitorName, task.getId(), tasks.size());
        saveTask(task);
        TaskHolder.setTask(task);
    }

    /**
     * 根据id获取task
     * @param taskId
     * @return
     */
    public Task getTask(String taskId) {
//        Task ownTask = getOwnTask(taskId);
//        if (ownTask != null) {
//            return ownTask;
//        }

        RMap<String, Task> map = redissonClient.getMap(monitorName);
        return map.get(taskId);
    }

    public Task getOwnTask(String taskId) {
        return tasks.get(taskId);
    }

    /**
     * 获取任务管理器运行环境中的全部task
     * @return
     */
    public List<Task> getOwnTasks() {
        return new ArrayList<>(tasks.values());
    }

    /**
     * 往redis里记录进行中的任务  saveTask
     * 如果连续3次未成功记录到redis，则终止已登记的全部任务
     */
    public void notifyStatus() {
        /**
         * 每隔5秒往redis里记录一下进行中的任务  saveTask
         * 如果连续3次未成功发送心跳，则终止已登记的全部任务
         */
        List<Task> ownTasks = getOwnTasks();
        for (Task ownTask : ownTasks) {
            if (ownTask.getStatus() != TaskStatus.RUNNING) {
                try {
                    tasks.remove(ownTask.getId());
                    logger.info("{} remove task: {}, tasks size is: {}", monitorName, ownTask.getId(), tasks.size());
                    //同时从redis里删除
                    RMap<String, Task> map = redissonClient.getMap(monitorName);
                    map.remove(ownTask.getId());
                } catch (Exception e) {
                    logger.warn("save task to redis failed", e);
                }
            } else {
                saveTask(ownTask);
                if (breakTimes > 3) {
                    cancelTask(ownTask.getId());
//                } else if (System.currentTimeMillis() - ownTask.getCreateTime() > 20 * 3600 * 1000L) {
//                    //如果登记时间超过20小时，则killTask
//                    killTask(ownTask.getId());
                }
            }
        }
    }

    /**
     * 正常完成任务
     * @param taskId
     */
    public void completeTask(String taskId) {
        if ("0".equals(taskId)) return;
        try {
            Task ownTask = getOwnTask(taskId);
            if (ownTask == null) {
                return;
            }
            if (ownTask.getStatus() == TaskStatus.RUNNING) {
                ownTask.setProgress(100);
                ownTask.setStatus(TaskStatus.COMPLETED);
            }

            tasks.remove(ownTask.getId());
            TaskHolder.remove();
            logger.info("{} complete task: {}, tasks size is: {}", monitorName, ownTask.getId(), tasks.size());

            //同时从redis里删除
            RMap<String, Task> map = redissonClient.getMap(monitorName);
            map.remove(ownTask.getId());
        } catch (Exception e) {
            logger.warn("complete task error", e);
        }
    }

    /**
     * 因为执行过程中出现了异常而终止任务，需做修复处理
     * @param task
     */
    public void terminateTask(Task task, Exception exception){
        try {
            logger.warn("terminate task error", exception);
            String taskId = task.getId();

            if (taskId.equals("0")) return;

            tasks.remove(taskId);

            //同时从redis里删除
            RMap<String, Task> map = redissonClient.getMap(monitorName);
            map.remove(taskId);

            resumeTask(task, false, exception.getMessage());
        } catch (Exception e) {
            logger.warn("invalid task error", e);
            throw new RuntimeException(exception);
        }
    }

    /**
     * 在获取锁时，考虑tasks里正在运行的任务个数，任务个数越多，在获取锁之前sleep越长时间，降低它获取锁的机会，用来达到负载均衡的目的
     * @param lock
     * @param waitTime
     * @param leaseTime
     * @param unit
     * @return
     */
    public boolean tryLock(RLock lock, long waitTime, long leaseTime, TimeUnit unit) {
        int size = tasks.size();
        long sleepTime = size * 30L;
        if (sleepTime > 3000L) {
            sleepTime = 3000L;
        }

        boolean lockResult = false;

        try {
            if (sleepTime > 0) {
                Thread.sleep(sleepTime);
            }

            if (unit == null) {
                lockResult = lock.tryLock();
            } else {
                lockResult = lock.tryLock(waitTime, leaseTime, unit);
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        return lockResult;
    }

    /**
     * 从redis里检测全部进行中的任务，心跳时间超过1分钟的任务认为是死掉的任务，则重新激活任务 activeTask
     */
    public void monitorAllTasks() {
        String lockKey = String.format(TASK_MONITOR_LOCK_KEY, monitorName);
        try {
            RLock lock = redissonClient.getLock(lockKey);
            if (!lock.tryLock(3L, 60L, TimeUnit.SECONDS)) {
                logger.info("cannot get task monitor lock for {}", monitorName);
                return;
            }

            try {
                RMap<String, Task> map = redissonClient.getMap(monitorName);
                logger.info("{} global tasks size is: {}, local tasks size is: {}", monitorName, map.size(), tasks.size());
                Iterator<Map.Entry<String, Task>> iterator = map.entrySet().iterator();
                while (iterator.hasNext()) {
                    Map.Entry<String, Task> entry = iterator.next();
                    Task task = entry.getValue();
                    TaskStatus status = task.getStatus();
                    Long liveTime = task.getLiveTime();
                    logger.info("monitor task: {}", task.getId());

                    if (status == TaskStatus.RUNNING && System.currentTimeMillis() - liveTime > 60 * 1000) {

                        //过滤租户
                        String tenant = getTenant(task);
                        if (StringUtils.isNotBlank(tenant)) {
                            final List<String> tenantBlackList = getTenantBlackList();
                            final List<String> tenantWhiteList = getTenantWhiteList();

                            if (tenantBlackList.contains(tenant)) {
                                continue;
                            } else if (!tenantWhiteList.isEmpty() && !tenantWhiteList.contains(tenant)) {
                                continue;
                            }

                            logger.info("task is dead, id: {}", task.getId());
                            iterator.remove();
                            resumeTask(task, true, "服务进程中断，等待系统自动重试");
                        }

                    } else if (status != TaskStatus.RUNNING) {
                        logger.info("漏网之鱼，现在删掉:{}，status:{}", task.getId(), status);
                        iterator.remove();
                    }
                }
            } finally {
                lock.unlock();
            }
        } catch (Exception e) {
            logger.warn("monitorAllTasks error", e);
        }
    }

    /**
     * 修复任务
     * 将原task状态置为失败，设置task重新激活的条件
     * @param task
     * @param isDead 是否因为进程死掉导致
     * @param msg 错误信息
     */
    public abstract void resumeTask(Task task, Boolean isDead, String msg);

    public abstract String getTenant(Task task);

    /**
     * 获取租户白名单
     * @return
     */
    public List<String> getTenantWhiteList() {
        return Collections.EMPTY_LIST;
    }

    public List<String> getTenantBlackList() {
        return Collections.EMPTY_LIST;
    }

    /**
     * 取消任务
     * @param taskId
     */
    public void cancelTask(String taskId) {
        Task task = getOwnTask(taskId);
        if (task != null) {
            task.setStatus(TaskStatus.CANCELED);
        }
        logger.info("cancel task: {}", taskId);
    }

    /**
     * 持久化任务
     * @param task
     */
    public boolean saveTask(Task task) {
        try {
            logger.info("save task to redis: {}", task);
            task.setLiveTime(System.currentTimeMillis());

            RMap<String, Task> map = redissonClient.getMap(monitorName);
            map.put(task.getId(), task);

            breakTimes = 0;
            return true;
        } catch (Exception e) {
            breakTimes++;
            logger.warn("save task to redis failed", e);
        }
        return false;
    }
}
