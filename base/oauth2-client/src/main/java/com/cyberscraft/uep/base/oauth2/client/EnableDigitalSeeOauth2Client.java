package com.cyberscraft.uep.base.oauth2.client;

import com.cyberscraft.uep.base.oauth2.client.common.properties.Oauth2ClientConfig;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Import;
import org.springframework.core.Ordered;

import java.lang.annotation.*;

/**
 * <p>
 * oath2 client自动配置
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2020-07-10 17:46
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Import(Oauth2ConfigurationSelector.class)
@EnableConfigurationProperties({Oauth2ClientConfig.class})
public @interface EnableDigitalSeeOauth2Client {
    int order() default Ordered.LOWEST_PRECEDENCE;
}
