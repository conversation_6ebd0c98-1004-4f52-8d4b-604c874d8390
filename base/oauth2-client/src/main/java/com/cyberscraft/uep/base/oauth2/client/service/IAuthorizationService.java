package com.cyberscraft.uep.base.oauth2.client.service;

import com.cyberscraft.uep.starter.dds.properties.DDSConfigProperties;
import com.cyberscraft.uep.base.oauth2.client.common.domain.BaseOauth2AuthenticationToken;
import com.cyberscraft.uep.base.oauth2.client.common.domain.UsernamePasswordDTO;
import com.cyberscraft.uep.base.oauth2.client.common.exceptions.BaseOauth2ClientException;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.core.authority.mapping.GrantedAuthoritiesMapper;
import org.springframework.security.oauth2.client.endpoint.OAuth2AccessTokenResponseClient;
import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository;
import org.springframework.security.oauth2.client.userinfo.OAuth2UserRequest;
import org.springframework.security.oauth2.client.userinfo.OAuth2UserService;
import org.springframework.security.oauth2.core.user.OAuth2User;

/***
 *
 * 授权服务
 * @date 2021/4/26
 * <AUTHOR>
 ***/
public interface IAuthorizationService extends AuthenticationProvider {

    /***
     * 根据用户名及密码，获取当前的访问Token,主要为从IAM中获取访问Token,IDToken，然后生成开放平台的token
     * @param loginDto
     * @return
     * @throws BaseOauth2ClientException
     */
    BaseOauth2AuthenticationToken authorize(UsernamePasswordDTO loginDto) throws BaseOauth2ClientException;

    /***
     *
     * @param openTokenStore
     */
    void setOpenTokenStore(IBaseTokenStore openTokenStore);

    /****
     *
     * @param client
     */
    void setAccessTokenResponseClient(OAuth2AccessTokenResponseClient client);

    /***
     *
     * @param userService
     */
    void setUserService(OAuth2UserService<OAuth2UserRequest, OAuth2User> userService);

    /***
     *
     * @param authoritiesMapper
     */
    void setAuthoritiesMapper(GrantedAuthoritiesMapper authoritiesMapper);

    /****
     *
     * @param clientRegistrationRepository
     */
    void setClientRegistrationRepository(ClientRegistrationRepository clientRegistrationRepository);

    /***
     *
     * @param ddsConfigProperties
     */
    void setDdsConfigProperties(DDSConfigProperties ddsConfigProperties);
}
