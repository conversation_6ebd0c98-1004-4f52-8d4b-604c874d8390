package com.cyberscraft.uep.base.oauth2.client.config;

import com.cyberscraft.uep.base.oauth2.client.common.domain.BaseOAuth2AuthorizationRequestResolver;
import com.cyberscraft.uep.base.oauth2.client.common.properties.Oauth2ClientConfig;
import com.cyberscraft.uep.base.oauth2.client.service.IAuthorizationService;
import com.cyberscraft.uep.base.oauth2.client.service.IBaseTokenStore;
import com.cyberscraft.uep.base.oauth2.client.service.impl.BaseAuthorizationServiceImpl;
import com.cyberscraft.uep.base.oauth2.client.service.impl.RedisBaseTokenStoreImpl;
import com.cyberscraft.uep.common.config.ServerConfig;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.security.core.authority.mapping.GrantedAuthoritiesMapper;
import org.springframework.security.core.authority.mapping.NullAuthoritiesMapper;
import org.springframework.security.oauth2.client.registration.ClientRegistration;
import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository;
import org.springframework.security.oauth2.client.registration.InMemoryClientRegistrationRepository;
import org.springframework.security.oauth2.client.web.OAuth2AuthorizationRequestRedirectFilter;
import org.springframework.security.oauth2.client.web.OAuth2AuthorizationRequestResolver;
import org.springframework.security.oauth2.core.AuthorizationGrantType;
import org.springframework.security.oauth2.core.ClientAuthenticationMethod;

import javax.annotation.Resource;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2020-07-28 15:49
 */
@Configuration
public class BeanConfig {

    @Resource
    private Oauth2ClientConfig oauth2ClientConfig;

    @Resource
    private ServerConfig serverConfig;

    @Resource
    private RedisConnectionFactory connectionFactory;

    @Bean
    public IBaseTokenStore baseTokenStore() {
        return new RedisBaseTokenStoreImpl(connectionFactory, oauth2ClientConfig.getTokenPrefixKey());
    }

    @Bean
    @ConditionalOnMissingBean
    public IAuthorizationService getOauth2ClientAuthorizationProvider() {
        BaseAuthorizationServiceImpl authorizationService = new BaseAuthorizationServiceImpl();
        authorizationService.setOpenTokenStore(baseTokenStore());
        authorizationService.setAuthoritiesMapper(getAuthoritiesMapper());
        authorizationService.setClientRegistrationRepository(clientRegistrationRepository());
        authorizationService.setEnableLocalToken(oauth2ClientConfig.isEnableLocalToken());
        //authorizationService.setDdsConfigProperties(ddsConfigProperties);
        return authorizationService;
    }


    @Bean
    public ClientRegistrationRepository clientRegistrationRepository() {
        return new InMemoryClientRegistrationRepository(this.iamClientRegistration(), iamPasswordClientRegistration());
    }

    @Bean
    public OAuth2AuthorizationRequestResolver oAuth2AuthorizationRequestResolver(){
        return new BaseOAuth2AuthorizationRequestResolver(clientRegistrationRepository(),oauth2ClientConfig.getBaseUrl() + OAuth2AuthorizationRequestRedirectFilter.DEFAULT_AUTHORIZATION_REQUEST_BASE_URI);
    }

    /***
     *
     * @return
     */
    public ClientRegistration iamClientRegistration() {
        if (serverConfig.getSingleTenant()) {
            return ClientRegistration.withRegistrationId(oauth2ClientConfig.getRegistrationId())
                    .clientId(oauth2ClientConfig.getClientId())
                    .clientSecret(oauth2ClientConfig.getClientSecret())
                    .clientAuthenticationMethod(ClientAuthenticationMethod.POST)
                    .redirectUriTemplate(oauth2ClientConfig.getRedirectUriTemplate())
                    .authorizationGrantType(AuthorizationGrantType.AUTHORIZATION_CODE)
                    .scope(StringUtils.split(oauth2ClientConfig.getScope(), ","))
                    .authorizationUri(oauth2ClientConfig.getAuthorizationUri())
                    .tokenUri(oauth2ClientConfig.getTokenUri())
                    .userInfoUri(oauth2ClientConfig.getUserInfoUri())
                    .userNameAttributeName(oauth2ClientConfig.getUserNameAttributeName())
                    //.jwkSetUri("https://www.googleapis.com/oauth2/v3/certs")
                    //.clientName("Google")
                    .build();
        }else {
            return ClientRegistration.withRegistrationId(oauth2ClientConfig.getRegistrationId())
                    .clientId(oauth2ClientConfig.getClientId())
                    .clientSecret(oauth2ClientConfig.getClientSecret())
                    .clientAuthenticationMethod(ClientAuthenticationMethod.POST)
                    .redirectUriTemplate(oauth2ClientConfig.getRedirectUriTemplate())
                    .authorizationGrantType(AuthorizationGrantType.AUTHORIZATION_CODE)
                    .scope(StringUtils.split(oauth2ClientConfig.getScope(), ","))
                    .authorizationUri(oauth2ClientConfig.getTenantAuthorizationUri())
                    .tokenUri(oauth2ClientConfig.getTokenUri())
                    .userInfoUri(oauth2ClientConfig.getUserInfoUri())
                    .userNameAttributeName(oauth2ClientConfig.getUserNameAttributeName())
                    //.jwkSetUri("https://www.googleapis.com/oauth2/v3/certs")
                    //.clientName("Google")
                    .build();
        }
    }

    /***
     *
     * @return
     */
    public ClientRegistration iamPasswordClientRegistration() {
        return ClientRegistration.withRegistrationId(oauth2ClientConfig.getPwdRegistrationId())
                .clientId(oauth2ClientConfig.getClientId())
                .clientSecret(oauth2ClientConfig.getClientSecret())
                .clientAuthenticationMethod(ClientAuthenticationMethod.POST)
                .redirectUriTemplate(oauth2ClientConfig.getRedirectUriTemplate())
                .authorizationGrantType(AuthorizationGrantType.PASSWORD)
                .scope(StringUtils.split(oauth2ClientConfig.getScope(), ","))
                .authorizationUri(oauth2ClientConfig.getAuthorizationUri())
                .tokenUri(oauth2ClientConfig.getTokenUri())
                .userInfoUri(oauth2ClientConfig.getUserInfoUri())
                .userNameAttributeName(oauth2ClientConfig.getUserNameAttributeName())
                //.jwkSetUri("https://www.googleapis.com/oauth2/v3/certs")
                //.clientName("Google")
                .build();
    }

    @Bean
    public GrantedAuthoritiesMapper getAuthoritiesMapper() {
        return new NullAuthoritiesMapper();
    }

}
