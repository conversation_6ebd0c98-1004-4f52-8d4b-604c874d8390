package com.cyberscraft.uep.base.oauth2.client.common.domain;

import org.springframework.security.oauth2.client.endpoint.OAuth2PasswordGrantRequest;
import org.springframework.security.oauth2.client.registration.ClientRegistration;

/***
 *
 * @date 2021/5/7
 * <AUTHOR>
 ***/
public class BaseOauth2PasswordGrantRequest extends OAuth2PasswordGrantRequest {

    public BaseOauth2PasswordGrantRequest(ClientRegistration clientRegistration, String username, String password, String tenantId) {
        super(clientRegistration, username, password);
        this.tenantId = tenantId;
    }

    private String tenantId;

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }
}
