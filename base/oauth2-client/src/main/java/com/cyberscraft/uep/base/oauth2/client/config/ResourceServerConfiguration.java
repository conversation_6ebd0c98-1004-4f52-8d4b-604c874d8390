package com.cyberscraft.uep.base.oauth2.client.config;

import com.cyberscraft.uep.base.oauth2.client.service.IBaseTokenStore;
import com.cyberscraft.uep.base.oauth2.client.service.express.BaseOAuth2MethodSecurityExpressionHandler;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.access.expression.method.MethodSecurityExpressionHandler;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.method.configuration.GlobalMethodSecurityConfiguration;

import javax.annotation.Resource;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2020-07-27 16:41
 */
@Configuration
@EnableGlobalMethodSecurity(prePostEnabled = true)
public class ResourceServerConfiguration extends GlobalMethodSecurityConfiguration {

    @Resource
    private IBaseTokenStore baseTokenStore;

    @Override
    protected MethodSecurityExpressionHandler createExpressionHandler() {
        return new BaseOAuth2MethodSecurityExpressionHandler(baseTokenStore);
    }
}