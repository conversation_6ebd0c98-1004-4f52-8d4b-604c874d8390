package com.cyberscraft.uep.base.oauth2.client.service.impl;

import com.cyberscraft.uep.base.oauth2.client.common.domain.BaseLoginUrlAuthenticationEndPoint;
import com.cyberscraft.uep.base.oauth2.client.service.IAuthorizationService;
import com.cyberscraft.uep.base.oauth2.client.service.IBaseTokenStore;
import com.cyberscraft.uep.starter.dds.properties.DDSConfigProperties;
import org.springframework.beans.factory.BeanFactoryUtils;
import org.springframework.beans.factory.NoUniqueBeanDefinitionException;
import org.springframework.context.ApplicationContext;
import org.springframework.core.ResolvableType;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.web.HttpSecurityBuilder;
import org.springframework.security.config.annotation.web.configurers.AbstractAuthenticationFilterConfigurer;
import org.springframework.security.config.annotation.web.configurers.oauth2.client.OAuth2LoginConfigurer;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.authority.mapping.GrantedAuthoritiesMapper;
import org.springframework.security.oauth2.client.InMemoryOAuth2AuthorizedClientService;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientService;
import org.springframework.security.oauth2.client.authentication.OAuth2LoginAuthenticationProvider;
import org.springframework.security.oauth2.client.authentication.OAuth2LoginAuthenticationToken;
import org.springframework.security.oauth2.client.endpoint.DefaultAuthorizationCodeTokenResponseClient;
import org.springframework.security.oauth2.client.endpoint.OAuth2AccessTokenResponseClient;
import org.springframework.security.oauth2.client.endpoint.OAuth2AuthorizationCodeGrantRequest;
import org.springframework.security.oauth2.client.oidc.authentication.OidcAuthorizationCodeAuthenticationProvider;
import org.springframework.security.oauth2.client.oidc.userinfo.OidcUserRequest;
import org.springframework.security.oauth2.client.oidc.userinfo.OidcUserService;
import org.springframework.security.oauth2.client.registration.ClientRegistration;
import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository;
import org.springframework.security.oauth2.client.userinfo.*;
import org.springframework.security.oauth2.client.web.*;
import org.springframework.security.oauth2.core.AuthorizationGrantType;
import org.springframework.security.oauth2.core.OAuth2AuthenticationException;
import org.springframework.security.oauth2.core.OAuth2Error;
import org.springframework.security.oauth2.core.endpoint.OAuth2AuthorizationRequest;
import org.springframework.security.oauth2.core.oidc.OidcScopes;
import org.springframework.security.oauth2.core.oidc.user.OidcUser;
import org.springframework.security.oauth2.core.user.OAuth2User;
import org.springframework.security.oauth2.jwt.JwtDecoderFactory;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.security.web.authentication.DelegatingAuthenticationEntryPoint;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.authentication.ui.DefaultLoginPageGeneratingFilter;
import org.springframework.security.web.savedrequest.RequestCache;
import org.springframework.security.web.util.matcher.*;
import org.springframework.util.Assert;
import org.springframework.util.ClassUtils;
import org.springframework.util.StringUtils;

import java.util.*;

/***
 * 因为open平台需要自己颁发业务token,所以需要自定义进行配置处理
 * @date 2021/4/27
 * <AUTHOR>
 ***/
public class BaseOauth2LoginSecurityConfig<B extends HttpSecurityBuilder<B>> extends
        AbstractAuthenticationFilterConfigurer<B, BaseOauth2LoginSecurityConfig<B>, BaseOauth2LoginAuthenticationFilter> {

    private IAuthorizationService authorizationService;

    private final AuthorizationEndpointConfig authorizationEndpointConfig = new AuthorizationEndpointConfig();
    private final TokenEndpointConfig tokenEndpointConfig = new TokenEndpointConfig();
    private final RedirectionEndpointConfig redirectionEndpointConfig = new RedirectionEndpointConfig();
    private final UserInfoEndpointConfig userInfoEndpointConfig = new UserInfoEndpointConfig();
    /**
     * 认证对应的过滤器
     */
    private BaseOauth2LoginAuthenticationFilter authenticationFilter;

    private IBaseTokenStore tokenStore;

    /***
     * 数据源配置类
     */
    private DDSConfigProperties ddsConfigProperties;

    /***
     * 登录页面，转换Oauth2对应的授权地址页，可以不指定，直接用ClientRegistration中的AuthorizationUri进行指定
     */
    private String loginPage;

    /***
     * Oauth2.0回调处理面面，接收Oauth2.0的code,并且需要根据token换取token信息，及获取用户信息后生成本地的token
     */
    private String loginProcessingUrl = OAuth2LoginAuthenticationFilter.DEFAULT_FILTER_PROCESSES_URI;


    @Override
    public BaseOauth2LoginSecurityConfig<B> loginProcessingUrl(String loginProcessingUrl) {
        Assert.hasText(loginProcessingUrl, "loginProcessingUrl cannot be empty");
        this.loginProcessingUrl = loginProcessingUrl;
        return this;
    }

    @Override
    public BaseOauth2LoginSecurityConfig<B> loginPage(String loginPage) {
        Assert.hasText(loginPage, "loginPage cannot be empty");
        this.loginPage = loginPage;
        return this;
    }

    /**
     * Returns the {@link BaseOauth2LoginSecurityConfig.AuthorizationEndpointConfig} for configuring the Authorization Server's Authorization Endpoint.
     *
     * @return the {@link BaseOauth2LoginSecurityConfig.AuthorizationEndpointConfig}
     */
    public AuthorizationEndpointConfig authorizationEndpoint() {
        return this.authorizationEndpointConfig;
    }

    /**
     * Configures the Authorization Server's Authorization Endpoint.
     *
     * @param authorizationEndpointCustomizer the {@link Customizer} to provide more options for
     *                                        the {@link BaseOauth2LoginSecurityConfig.AuthorizationEndpointConfig}
     * @return the {@link BaseOauth2LoginSecurityConfig} for further customizations
     */
    public BaseOauth2LoginSecurityConfig<B> authorizationEndpoint(Customizer<AuthorizationEndpointConfig> authorizationEndpointCustomizer) {
        authorizationEndpointCustomizer.customize(this.authorizationEndpointConfig);
        return this;
    }

    /**
     * Returns the {@link BaseOauth2LoginSecurityConfig.TokenEndpointConfig} for configuring the Authorization Server's Token Endpoint.
     *
     * @return the {@link BaseOauth2LoginSecurityConfig.TokenEndpointConfig}
     */
    public TokenEndpointConfig tokenEndpoint() {
        return this.tokenEndpointConfig;
    }

    /**
     * Configures the Authorization Server's Token Endpoint.
     *
     * @param tokenEndpointCustomizer the {@link Customizer} to provide more options for
     *                                the {@link BaseOauth2LoginSecurityConfig.TokenEndpointConfig}
     * @return the {@link BaseOauth2LoginSecurityConfig} for further customizations
     * @throws Exception
     */
    public BaseOauth2LoginSecurityConfig<B> tokenEndpoint(Customizer<TokenEndpointConfig> tokenEndpointCustomizer) {
        tokenEndpointCustomizer.customize(this.tokenEndpointConfig);
        return this;
    }

    /**
     * Returns the {@link  BaseOauth2LoginSecurityConfig.RedirectionEndpointConfig} for configuring the Client's Redirection Endpoint.
     *
     * @return the {@link BaseOauth2LoginSecurityConfig.RedirectionEndpointConfig}
     */
    public RedirectionEndpointConfig redirectionEndpoint() {
        return this.redirectionEndpointConfig;
    }

    /**
     * Configures the Client's Redirection Endpoint.
     *
     * @param redirectionEndpointCustomizer the {@link Customizer} to provide more options for
     *                                      the {@link  BaseOauth2LoginSecurityConfig.RedirectionEndpointConfig}
     * @return the {@link BaseOauth2LoginSecurityConfig} for further customizations
     */
    public BaseOauth2LoginSecurityConfig<B> redirectionEndpoint(Customizer<RedirectionEndpointConfig> redirectionEndpointCustomizer) {
        redirectionEndpointCustomizer.customize(this.redirectionEndpointConfig);
        return this;
    }

    /**
     * Returns the {@link BaseOauth2LoginSecurityConfig.UserInfoEndpointConfig} for configuring the Authorization Server's UserInfo Endpoint.
     *
     * @return the {@link BaseOauth2LoginSecurityConfig.UserInfoEndpointConfig}
     */
    public UserInfoEndpointConfig userInfoEndpoint() {
        return this.userInfoEndpointConfig;
    }

    /**
     * Configures the Authorization Server's UserInfo Endpoint.
     *
     * @param userInfoEndpointCustomizer the {@link Customizer} to provide more options for
     *                                   the {@link BaseOauth2LoginSecurityConfig.UserInfoEndpointConfig}
     * @return the {@link BaseOauth2LoginSecurityConfig} for further customizations
     */
    public BaseOauth2LoginSecurityConfig<B> userInfoEndpoint(Customizer<UserInfoEndpointConfig> userInfoEndpointCustomizer) {
        userInfoEndpointCustomizer.customize(this.userInfoEndpointConfig);
        return this;
    }

    /**
     * Sets the repository of client registrations.
     *
     * @param clientRegistrationRepository the repository of client registrations
     * @return the {@link OAuth2LoginConfigurer} for further configuration
     */
    public BaseOauth2LoginSecurityConfig<B> clientRegistrationRepository(ClientRegistrationRepository clientRegistrationRepository) {
        Assert.notNull(clientRegistrationRepository, "clientRegistrationRepository cannot be null");
        this.getBuilder().setSharedObject(ClientRegistrationRepository.class, clientRegistrationRepository);
        return this;
    }


    /**
     * Sets the repository for authorized client(s).
     *
     * @param authorizedClientRepository the authorized client repository
     * @return the {@link OAuth2LoginConfigurer} for further configuration
     * @since 5.1
     */
    public BaseOauth2LoginSecurityConfig<B> authorizedClientRepository(OAuth2AuthorizedClientRepository authorizedClientRepository) {
        Assert.notNull(authorizedClientRepository, "authorizedClientRepository cannot be null");
        this.getBuilder().setSharedObject(OAuth2AuthorizedClientRepository.class, authorizedClientRepository);
        return this;
    }

    /**
     * Sets the service for authorized client(s).
     *
     * @param authorizedClientService the authorized client service
     * @return the {@link OAuth2LoginConfigurer} for further configuration
     */
    public BaseOauth2LoginSecurityConfig<B> authorizedClientService(OAuth2AuthorizedClientService authorizedClientService) {
        Assert.notNull(authorizedClientService, "authorizedClientService cannot be null");
        this.authorizedClientRepository(new AuthenticatedPrincipalOAuth2AuthorizedClientRepository(authorizedClientService));
        return this;
    }

    /***
     *
     * @param mapper
     * @return
     */
    public BaseOauth2LoginSecurityConfig<B> grantedAuthoritiesMapper(GrantedAuthoritiesMapper mapper) {
        this.getBuilder().setSharedObject(GrantedAuthoritiesMapper.class, mapper);
        return this;
    }

    /***
     *
     * @param properties
     * @return
     */
    public BaseOauth2LoginSecurityConfig<B> ddsConfigProperties(DDSConfigProperties properties) {
        this.ddsConfigProperties = properties;
        this.getBuilder().setSharedObject(DDSConfigProperties.class, properties);
        return this;
    }


    @Override
    public void init(B http) throws Exception {
        BaseOauth2LoginAuthenticationFilter authenticationFilter =
                new BaseOauth2LoginAuthenticationFilter(
                        getClientRegistrationRepository(),
                        getAuthorizedClientRepository(),
                        this.loginProcessingUrl);
        this.authenticationFilter = authenticationFilter;
        this.setAuthenticationFilter(authenticationFilter);
        super.loginProcessingUrl(this.loginProcessingUrl);
//        if (this.loginPage != null) {
//            // Set custom login page
//            super.loginPage(this.loginPage);
//            super.init(http);
//        } else {
            Map<String, String> loginUrlToClientName = this.getLoginLinks();
            if (loginUrlToClientName.size() == 1) {
                // Setup auto-redirect to provider login page
                // when only 1 client is configured
                this.updateAuthenticationDefaults();
                this.updateAccessDefaults(http);
                String providerLoginPage = loginUrlToClientName.keySet().iterator().next();
                this.registerAuthenticationEntryPoint(http, this.getLoginEntryPoint(http, providerLoginPage));
            } else {
                if (this.loginPage != null) {
        //            // Set custom login page
                    super.loginPage(this.loginPage);
        //            super.init(http);
                }
                super.init(http);
            }
//        }


        OAuth2AccessTokenResponseClient<OAuth2AuthorizationCodeGrantRequest> accessTokenResponseClient =
                this.tokenEndpointConfig.accessTokenResponseClient;
        if (accessTokenResponseClient == null) {
            accessTokenResponseClient = new DefaultAuthorizationCodeTokenResponseClient();
        }

        //Oauth2认证实现类
        OAuth2UserService<OAuth2UserRequest, OAuth2User> oauth2UserService = getOAuth2UserService();
        OAuth2LoginAuthenticationProvider oauth2LoginAuthenticationProvider =
                new OAuth2LoginAuthenticationProvider(accessTokenResponseClient, oauth2UserService);
//        GrantedAuthoritiesMapper userAuthoritiesMapper = this.getGrantedAuthoritiesMapper();
//        if (userAuthoritiesMapper != null) {
//            oauth2LoginAuthenticationProvider.setAuthoritiesMapper(userAuthoritiesMapper);
//        }

        GrantedAuthoritiesMapper userAuthoritiesMapper = this.getGrantedAuthoritiesMapper();

        IBaseTokenStore tokenStore = getTokenStore(http);
        if (this.authorizationService == null) {
            authorizationService = new BaseAuthorizationServiceImpl();
        }
        this.authorizationService.setAccessTokenResponseClient(accessTokenResponseClient);
        this.authorizationService.setUserService(oauth2UserService);
        this.authorizationService.setAuthoritiesMapper(userAuthoritiesMapper);
        this.authorizationService.setClientRegistrationRepository(getClientRegistrationRepository());
        this.authorizationService.setOpenTokenStore(tokenStore);
        this.authorizationService.setDdsConfigProperties(ddsConfigProperties);


        this.authenticationFilter.setOpenTokenStore(tokenStore);

        http.authenticationProvider(this.postProcess(this.authorizationService));

        //http.authenticationProvider(this.postProcess(oauth2LoginAuthenticationProvider));

        boolean oidcAuthenticationProviderEnabled = ClassUtils.isPresent(
                "org.springframework.security.oauth2.jwt.JwtDecoder", this.getClass().getClassLoader());

        if (oidcAuthenticationProviderEnabled) {
            OAuth2UserService<OidcUserRequest, OidcUser> oidcUserService = getOidcUserService();
            OidcAuthorizationCodeAuthenticationProvider oidcAuthorizationCodeAuthenticationProvider =
                    new OidcAuthorizationCodeAuthenticationProvider(accessTokenResponseClient, oidcUserService);
            JwtDecoderFactory<ClientRegistration> jwtDecoderFactory = this.getJwtDecoderFactoryBean();
            if (jwtDecoderFactory != null) {
                oidcAuthorizationCodeAuthenticationProvider.setJwtDecoderFactory(jwtDecoderFactory);
            }
            if (userAuthoritiesMapper != null) {
                oidcAuthorizationCodeAuthenticationProvider.setAuthoritiesMapper(userAuthoritiesMapper);
            }
            http.authenticationProvider(this.postProcess(oidcAuthorizationCodeAuthenticationProvider));
        } else {
            http.authenticationProvider(new OidcAuthenticationRequestChecker());
        }

        this.initDefaultLoginFilter(http);
    }


    @Override
    public void configure(B http) throws Exception {

        OAuth2AuthorizationRequestRedirectFilter authorizationRequestFilter;
        http.addFilterAfter(getAuthenticationFilter(), UsernamePasswordAuthenticationFilter.class);

        if (this.authorizationEndpointConfig.authorizationRequestResolver != null) {
            authorizationRequestFilter = new OAuth2AuthorizationRequestRedirectFilter(
                    this.authorizationEndpointConfig.authorizationRequestResolver);
        } else {
            String authorizationRequestBaseUri = this.authorizationEndpointConfig.authorizationRequestBaseUri;
            if (authorizationRequestBaseUri == null) {
                authorizationRequestBaseUri = OAuth2AuthorizationRequestRedirectFilter.DEFAULT_AUTHORIZATION_REQUEST_BASE_URI;
            }
            authorizationRequestFilter = new OAuth2AuthorizationRequestRedirectFilter(
                    getClientRegistrationRepository(), authorizationRequestBaseUri);
        }

        if (this.authorizationEndpointConfig.authorizationRequestRepository != null) {
            authorizationRequestFilter.setAuthorizationRequestRepository(
                    this.authorizationEndpointConfig.authorizationRequestRepository);
        }
        RequestCache requestCache = http.getSharedObject(RequestCache.class);
        if (requestCache != null) {
            authorizationRequestFilter.setRequestCache(requestCache);
        }

        http.addFilter(this.postProcess(authorizationRequestFilter));

        BaseOauth2LoginAuthenticationFilter authenticationFilter = this.getAuthenticationFilter();
        if (this.redirectionEndpointConfig.authorizationResponseBaseUri != null) {
            authenticationFilter.setFilterProcessesUrl(this.redirectionEndpointConfig.authorizationResponseBaseUri);
        }
        if (this.authorizationEndpointConfig.authorizationRequestRepository != null) {
            authenticationFilter.setAuthorizationRequestRepository(
                    this.authorizationEndpointConfig.authorizationRequestRepository);
        }
        super.configure(http);


    }


    @Override
    protected RequestMatcher createLoginProcessingUrlMatcher(String loginProcessingUrl) {
        return new AntPathRequestMatcher(loginProcessingUrl);
    }

    @SuppressWarnings("unchecked")
    private Map<String, String> getLoginLinks() {
        Iterable<ClientRegistration> clientRegistrations = null;
        ClientRegistrationRepository clientRegistrationRepository = getClientRegistrationRepository();
        ResolvableType type = ResolvableType.forInstance(clientRegistrationRepository).as(Iterable.class);
        if (type != ResolvableType.NONE && ClientRegistration.class.isAssignableFrom(type.resolveGenerics()[0])) {
            clientRegistrations = (Iterable<ClientRegistration>) clientRegistrationRepository;
        }
        if (clientRegistrations == null) {
            return Collections.emptyMap();
        }

        String authorizationRequestBaseUri = this.authorizationEndpointConfig.authorizationRequestBaseUri != null ?
                this.authorizationEndpointConfig.authorizationRequestBaseUri :
                OAuth2AuthorizationRequestRedirectFilter.DEFAULT_AUTHORIZATION_REQUEST_BASE_URI;
        Map<String, String> loginUrlToClientName = new HashMap<>();
//        clientRegistrations.forEach(registration ->
//                loginUrlToClientName.put(
//                authorizationRequestBaseUri + "/" + registration.getRegistrationId(),
//                registration.getClientName()));
        for (ClientRegistration registration : clientRegistrations) {
            if (registration.getAuthorizationGrantType().equals(AuthorizationGrantType.AUTHORIZATION_CODE)) {
                loginUrlToClientName.put(
                        authorizationRequestBaseUri + "/" + registration.getRegistrationId(),
                        registration.getClientName());
            }
        }

        return loginUrlToClientName;
    }

    /***
     *
     * @param http
     * @param providerLoginPage
     * @return
     */
    private AuthenticationEntryPoint getLoginEntryPoint(B http, String providerLoginPage) {
        RequestMatcher loginPageMatcher = new AntPathRequestMatcher(this.getLoginPage());
        RequestMatcher faviconMatcher = new AntPathRequestMatcher("/favicon.ico");
        RequestMatcher defaultEntryPointMatcher = this.getAuthenticationEntryPointMatcher(http);
        RequestMatcher defaultLoginPageMatcher = new AndRequestMatcher(
                new OrRequestMatcher(loginPageMatcher, faviconMatcher), defaultEntryPointMatcher);

        RequestMatcher notXRequestedWith = new NegatedRequestMatcher(
                new RequestHeaderRequestMatcher("X-Requested-With", "XMLHttpRequest"));

        LinkedHashMap<RequestMatcher, AuthenticationEntryPoint> entryPoints = new LinkedHashMap<>();
        entryPoints.put(new AndRequestMatcher(notXRequestedWith, new NegatedRequestMatcher(defaultLoginPageMatcher)),
                new BaseLoginUrlAuthenticationEndPoint(providerLoginPage));

        DelegatingAuthenticationEntryPoint loginEntryPoint = new DelegatingAuthenticationEntryPoint(entryPoints);
        loginEntryPoint.setDefaultEntryPoint(this.getAuthenticationEntryPoint());

        return loginEntryPoint;
    }

    /***
     *
     * @return
     */
    @SuppressWarnings("unchecked")
    private JwtDecoderFactory<ClientRegistration> getJwtDecoderFactoryBean() {
        ResolvableType type = ResolvableType.forClassWithGenerics(JwtDecoderFactory.class, ClientRegistration.class);
        String[] names = this.getBuilder().getSharedObject(ApplicationContext.class).getBeanNamesForType(type);
        if (names.length > 1) {
            throw new NoUniqueBeanDefinitionException(type, names);
        }
        if (names.length == 1) {
            return (JwtDecoderFactory<ClientRegistration>) this.getBuilder().getSharedObject(ApplicationContext.class).getBean(names[0]);
        }
        return null;
    }


    /***
     *
     * @return
     */
    private GrantedAuthoritiesMapper getGrantedAuthoritiesMapper() {
        GrantedAuthoritiesMapper grantedAuthoritiesMapper =
                this.getBuilder().getSharedObject(GrantedAuthoritiesMapper.class);
        if (grantedAuthoritiesMapper == null) {
            grantedAuthoritiesMapper = this.getGrantedAuthoritiesMapperBean();
            if (grantedAuthoritiesMapper != null) {
                this.getBuilder().setSharedObject(GrantedAuthoritiesMapper.class, grantedAuthoritiesMapper);
            }
        }
        return grantedAuthoritiesMapper;
    }

    /***
     *
     * @return
     */
    private GrantedAuthoritiesMapper getGrantedAuthoritiesMapperBean() {
        Map<String, GrantedAuthoritiesMapper> grantedAuthoritiesMapperMap =
                BeanFactoryUtils.beansOfTypeIncludingAncestors(
                        this.getBuilder().getSharedObject(ApplicationContext.class),
                        GrantedAuthoritiesMapper.class);
        return (!grantedAuthoritiesMapperMap.isEmpty() ? grantedAuthoritiesMapperMap.values().iterator().next() : null);
    }

    /***
     *
     * @return
     */
    private OAuth2UserService<OidcUserRequest, OidcUser> getOidcUserService() {
        if (this.userInfoEndpointConfig.oidcUserService != null) {
            return this.userInfoEndpointConfig.oidcUserService;
        }
        ResolvableType type = ResolvableType.forClassWithGenerics(OAuth2UserService.class, OidcUserRequest.class, OidcUser.class);
        OAuth2UserService<OidcUserRequest, OidcUser> bean = getBeanOrNull(type);
        if (bean == null) {
            return new OidcUserService();
        }

        return bean;
    }

    /****
     *
     * @return
     */
    private OAuth2UserService<OAuth2UserRequest, OAuth2User> getOAuth2UserService() {
        if (this.userInfoEndpointConfig.userService != null) {
            return this.userInfoEndpointConfig.userService;
        }
        ResolvableType type = ResolvableType.forClassWithGenerics(OAuth2UserService.class, OAuth2UserRequest.class, OAuth2User.class);
        OAuth2UserService<OAuth2UserRequest, OAuth2User> bean = getBeanOrNull(type);
        if (bean == null) {
            if (!this.userInfoEndpointConfig.customUserTypes.isEmpty()) {
                List<OAuth2UserService<OAuth2UserRequest, OAuth2User>> userServices = new ArrayList<>();
                userServices.add(new CustomUserTypesOAuth2UserService(this.userInfoEndpointConfig.customUserTypes));
                userServices.add(new DefaultOAuth2UserService());
                return new DelegatingOAuth2UserService<>(userServices);
            } else {
                return new DefaultOAuth2UserService();
            }
        }

        return bean;
    }

    /***
     *
     * @param http
     */
    private void initDefaultLoginFilter(B http) {
        DefaultLoginPageGeneratingFilter loginPageGeneratingFilter = http.getSharedObject(DefaultLoginPageGeneratingFilter.class);
        if (loginPageGeneratingFilter == null || this.isCustomLoginPage()) {
            return;
        }

        loginPageGeneratingFilter.setOauth2LoginEnabled(true);
        loginPageGeneratingFilter.setOauth2AuthenticationUrlToClientName(this.getLoginLinks());
        loginPageGeneratingFilter.setLoginPageUrl(this.getLoginPage());
        loginPageGeneratingFilter.setFailureUrl(this.getFailureUrl());
    }

    /***
     *
     * @param type
     * @param <T>
     * @return
     */
    private <T> T getBeanOrNull(ResolvableType type) {
        ApplicationContext context = getBuilder().getSharedObject(ApplicationContext.class);
        if (context == null) {
            return null;
        }
        String[] names = context.getBeanNamesForType(type);
        if (names.length == 1) {
            return (T) context.getBean(names[0]);
        }
        return null;
    }

    /****
     * 得到tokenStore对像
     * @param http
     * @return
     */
    private IBaseTokenStore getTokenStore(B http) {
        if (this.tokenStore != null) {
            return this.tokenStore;
        }
        this.tokenStore = http.getSharedObject(IBaseTokenStore.class);
        return this.tokenStore;
    }

    /****
     *
     * @return
     */
    private ClientRegistrationRepository getClientRegistrationRepository() {
        ClientRegistrationRepository clientRegistrationRepository = this.getBuilder().getSharedObject(ClientRegistrationRepository.class);
        if (clientRegistrationRepository == null) {
            clientRegistrationRepository = getClientRegistrationRepositoryBean();
            getBuilder().setSharedObject(ClientRegistrationRepository.class, clientRegistrationRepository);
        }
        return clientRegistrationRepository;
    }

    /***
     *
     * @return
     */
    private ClientRegistrationRepository getClientRegistrationRepositoryBean() {
        return this.getBuilder().getSharedObject(ApplicationContext.class).getBean(ClientRegistrationRepository.class);
    }


    /****
     *
     * @return
     */
    private OAuth2AuthorizedClientRepository getAuthorizedClientRepository() {
        OAuth2AuthorizedClientRepository authorizedClientRepository = getBuilder().getSharedObject(OAuth2AuthorizedClientRepository.class);
        if (authorizedClientRepository == null) {
            authorizedClientRepository = getAuthorizedClientRepositoryBean();
            if (authorizedClientRepository == null) {
                authorizedClientRepository = new AuthenticatedPrincipalOAuth2AuthorizedClientRepository(
                        getAuthorizedClientService());
            }
            getBuilder().setSharedObject(OAuth2AuthorizedClientRepository.class, authorizedClientRepository);
        }
        return authorizedClientRepository;
    }

    /***
     *
     * @return
     */
    private OAuth2AuthorizedClientRepository getAuthorizedClientRepositoryBean() {
        return this.getBuilder().getSharedObject(ApplicationContext.class).getBean(OAuth2AuthorizedClientRepository.class);
    }

    /***
     *
     * @return
     */
    private OAuth2AuthorizedClientService getAuthorizedClientService() {
        OAuth2AuthorizedClientService authorizedClientService = getAuthorizedClientServiceBean();
        if (authorizedClientService == null) {
            authorizedClientService = new InMemoryOAuth2AuthorizedClientService(getClientRegistrationRepository());
        }
        return authorizedClientService;
    }

    /***
     *
     * @return
     */
    private OAuth2AuthorizedClientService getAuthorizedClientServiceBean() {
        Map<String, OAuth2AuthorizedClientService> authorizedClientServiceMap = BeanFactoryUtils.beansOfTypeIncludingAncestors(
                getBuilder().getSharedObject(ApplicationContext.class), OAuth2AuthorizedClientService.class);
        if (authorizedClientServiceMap.size() > 1) {
            throw new NoUniqueBeanDefinitionException(OAuth2AuthorizedClientService.class, authorizedClientServiceMap.size(),
                    "Expected single matching bean of type '" + OAuth2AuthorizedClientService.class.getName() + "' but found " +
                            authorizedClientServiceMap.size() + ": " + StringUtils.collectionToCommaDelimitedString(authorizedClientServiceMap.keySet()));
        }
        return (!authorizedClientServiceMap.isEmpty() ? authorizedClientServiceMap.values().iterator().next() : null);
    }

    /***
     * 认证服务提供者
     * @param provider
     * @return
     */
    public BaseOauth2LoginSecurityConfig<B> authorizationProvider(IAuthorizationService provider) {
        this.authorizationService = provider;
        return this;
    }

    /***
     *  自定义认证服务提供者
     * @param tokenStore
     * @return
     */
    public BaseOauth2LoginSecurityConfig<B> tokenStore(IBaseTokenStore tokenStore) {
        this.tokenStore = tokenStore;
        return this;
    }



    /**
     * Configuration options for the Authorization Server's Authorization Endpoint.
     */
    public class AuthorizationEndpointConfig {
        private String authorizationRequestBaseUri;
        private OAuth2AuthorizationRequestResolver authorizationRequestResolver;
        private AuthorizationRequestRepository<OAuth2AuthorizationRequest> authorizationRequestRepository;

        private AuthorizationEndpointConfig() {
        }

        /**
         * Sets the base {@code URI} used for authorization requests.
         *
         * @param authorizationRequestBaseUri the base {@code URI} used for authorization requests
         * @return the {@link OAuth2LoginConfigurer.AuthorizationEndpointConfig} for further configuration
         */
        public BaseOauth2LoginSecurityConfig.AuthorizationEndpointConfig baseUri(String authorizationRequestBaseUri) {
            Assert.hasText(authorizationRequestBaseUri, "authorizationRequestBaseUri cannot be empty");
            this.authorizationRequestBaseUri = authorizationRequestBaseUri;
            return this;
        }

        /**
         * Sets the resolver used for resolving {@link OAuth2AuthorizationRequest}'s.
         *
         * @param authorizationRequestResolver the resolver used for resolving {@link OAuth2AuthorizationRequest}'s
         * @return the {@link OAuth2LoginConfigurer.AuthorizationEndpointConfig} for further configuration
         * @since 5.1
         */
        public BaseOauth2LoginSecurityConfig.AuthorizationEndpointConfig authorizationRequestResolver(OAuth2AuthorizationRequestResolver authorizationRequestResolver) {
            Assert.notNull(authorizationRequestResolver, "authorizationRequestResolver cannot be null");
            this.authorizationRequestResolver = authorizationRequestResolver;
            return this;
        }

        /**
         * Sets the repository used for storing {@link OAuth2AuthorizationRequest}'s.
         *
         * @param authorizationRequestRepository the repository used for storing {@link OAuth2AuthorizationRequest}'s
         * @return the {@link OAuth2LoginConfigurer.AuthorizationEndpointConfig} for further configuration
         */
        public BaseOauth2LoginSecurityConfig.AuthorizationEndpointConfig authorizationRequestRepository(AuthorizationRequestRepository<OAuth2AuthorizationRequest> authorizationRequestRepository) {
            Assert.notNull(authorizationRequestRepository, "authorizationRequestRepository cannot be null");
            this.authorizationRequestRepository = authorizationRequestRepository;
            return this;
        }

        /**
         * Returns the {@link OAuth2LoginConfigurer} for further configuration.
         *
         * @return the {@link OAuth2LoginConfigurer}
         */
        public BaseOauth2LoginSecurityConfig<B> and() {
            return BaseOauth2LoginSecurityConfig.this;
        }
    }

    /**
     * Configuration options for the Authorization Server's Token Endpoint.
     */
    public class TokenEndpointConfig {
        private OAuth2AccessTokenResponseClient<OAuth2AuthorizationCodeGrantRequest> accessTokenResponseClient;

        private TokenEndpointConfig() {
        }

        /**
         * Sets the client used for requesting the access token credential from the Token Endpoint.
         *
         * @param accessTokenResponseClient the client used for requesting the access token credential from the Token Endpoint
         * @return the {@link OAuth2LoginConfigurer.TokenEndpointConfig} for further configuration
         */
        public BaseOauth2LoginSecurityConfig.TokenEndpointConfig accessTokenResponseClient(
                OAuth2AccessTokenResponseClient<OAuth2AuthorizationCodeGrantRequest> accessTokenResponseClient) {

            Assert.notNull(accessTokenResponseClient, "accessTokenResponseClient cannot be null");
            this.accessTokenResponseClient = accessTokenResponseClient;
            return this;
        }

        /**
         * Returns the {@link OAuth2LoginConfigurer} for further configuration.
         *
         * @return the {@link OAuth2LoginConfigurer}
         */
        public BaseOauth2LoginSecurityConfig<B> and() {
            return BaseOauth2LoginSecurityConfig.this;
        }
    }

    /**
     * Configuration options for the Client's Redirection Endpoint.
     */
    public class RedirectionEndpointConfig {
        private String authorizationResponseBaseUri;

        private RedirectionEndpointConfig() {
        }

        /**
         * Sets the {@code URI} where the authorization response will be processed.
         *
         * @param authorizationResponseBaseUri the {@code URI} where the authorization response will be processed
         * @return the {@link OAuth2LoginConfigurer.RedirectionEndpointConfig} for further configuration
         */
        public BaseOauth2LoginSecurityConfig.RedirectionEndpointConfig baseUri(String authorizationResponseBaseUri) {
            Assert.hasText(authorizationResponseBaseUri, "authorizationResponseBaseUri cannot be empty");
            this.authorizationResponseBaseUri = authorizationResponseBaseUri;
            return this;
        }

        /**
         * Returns the {@link OAuth2LoginConfigurer} for further configuration.
         *
         * @return the {@link OAuth2LoginConfigurer}
         */
        public BaseOauth2LoginSecurityConfig<B> and() {
            return BaseOauth2LoginSecurityConfig.this;
        }
    }

    /**

     * Configuration options for the Authorization Server's UserInfo Endpoint.
     */
    public class UserInfoEndpointConfig {
        private OAuth2UserService<OAuth2UserRequest, OAuth2User> userService;
        private OAuth2UserService<OidcUserRequest, OidcUser> oidcUserService;
        private Map<String, Class<? extends OAuth2User>> customUserTypes = new HashMap<>();

        private UserInfoEndpointConfig() {
        }

        /**
         * Sets the OAuth 2.0 service used for obtaining the user attributes of the End-User from the UserInfo Endpoint.
         *
         * @param userService the OAuth 2.0 service used for obtaining the user attributes of the End-User from the UserInfo Endpoint
         * @return the {@link OAuth2LoginConfigurer.UserInfoEndpointConfig} for further configuration
         */
        public BaseOauth2LoginSecurityConfig.UserInfoEndpointConfig userService(OAuth2UserService<OAuth2UserRequest, OAuth2User> userService) {
            Assert.notNull(userService, "userService cannot be null");
            this.userService = userService;
            return this;
        }

        /**
         * Sets the OpenID Connect 1.0 service used for obtaining the user attributes of the End-User from the UserInfo Endpoint.
         *
         * @param oidcUserService the OpenID Connect 1.0 service used for obtaining the user attributes of the End-User from the UserInfo Endpoint
         * @return the {@link OAuth2LoginConfigurer.UserInfoEndpointConfig} for further configuration
         */
        public BaseOauth2LoginSecurityConfig.UserInfoEndpointConfig oidcUserService(OAuth2UserService<OidcUserRequest, OidcUser> oidcUserService) {
            Assert.notNull(oidcUserService, "oidcUserService cannot be null");
            this.oidcUserService = oidcUserService;
            return this;
        }

        /**
         * Sets a custom {@link OAuth2User} type and associates it to the provided
         * client {@link ClientRegistration#getRegistrationId() registration identifier}.
         *
         * @param customUserType       a custom {@link OAuth2User} type
         * @param clientRegistrationId the client registration identifier
         * @return the {@link OAuth2LoginConfigurer.UserInfoEndpointConfig} for further configuration
         */
        public BaseOauth2LoginSecurityConfig.UserInfoEndpointConfig customUserType(Class<? extends OAuth2User> customUserType, String clientRegistrationId) {
            Assert.notNull(customUserType, "customUserType cannot be null");
            Assert.hasText(clientRegistrationId, "clientRegistrationId cannot be empty");
            this.customUserTypes.put(clientRegistrationId, customUserType);
            return this;
        }

        /**
         * Sets the {@link GrantedAuthoritiesMapper} used for mapping {@link OAuth2User#getAuthorities()}.
         *
         * @param userAuthoritiesMapper the {@link GrantedAuthoritiesMapper} used for mapping the user's authorities
         * @return the {@link OAuth2LoginConfigurer.UserInfoEndpointConfig} for further configuration
         */
        public BaseOauth2LoginSecurityConfig.UserInfoEndpointConfig userAuthoritiesMapper(GrantedAuthoritiesMapper userAuthoritiesMapper) {
            Assert.notNull(userAuthoritiesMapper, "userAuthoritiesMapper cannot be null");
            BaseOauth2LoginSecurityConfig.this.getBuilder().setSharedObject(GrantedAuthoritiesMapper.class, userAuthoritiesMapper);
            return this;
        }

        /**
         * Returns the {@link OAuth2LoginConfigurer} for further configuration.
         *
         * @return the {@link OAuth2LoginConfigurer}
         */
        public BaseOauth2LoginSecurityConfig<B> and() {
            return BaseOauth2LoginSecurityConfig.this;
        }
    }

    /***
     *
     */
    private static class OidcAuthenticationRequestChecker implements AuthenticationProvider {

        @Override
        public Authentication authenticate(Authentication authentication) throws AuthenticationException {
            OAuth2LoginAuthenticationToken authorizationCodeAuthentication =
                    (OAuth2LoginAuthenticationToken) authentication;

            // Section 3.1.2.1 Authentication Request - https://openid.net/specs/openid-connect-core-1_0.html#AuthRequest
            // scope
            // 		REQUIRED. OpenID Connect requests MUST contain the "openid" scope value.
            if (authorizationCodeAuthentication.getAuthorizationExchange()
                    .getAuthorizationRequest().getScopes().contains(OidcScopes.OPENID)) {

                OAuth2Error oauth2Error = new OAuth2Error(
                        "oidc_provider_not_configured",
                        "An OpenID Connect Authentication Provider has not been configured. " +
                                "Check to ensure you include the dependency 'spring-security-oauth2-jose'.",
                        null);
                throw new OAuth2AuthenticationException(oauth2Error, oauth2Error.toString());
            }

            return null;
        }

        @Override
        public boolean supports(Class<?> authentication) {
            return OAuth2LoginAuthenticationToken.class.isAssignableFrom(authentication);
        }
    }

}
