package com.cyberscraft.uep.account.client.service.impl;

import com.cyberscraft.uep.account.client.constant.ConnectorType;
import com.cyberscraft.uep.account.client.domain.*;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountException;
import com.cyberscraft.uep.account.client.provider.IExternalConnectHandler;
import com.cyberscraft.uep.account.client.service.IExternalConnectService;
import com.cyberscraft.uep.common.domain.ParamSchema;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/2/8 2:32 下午
 */
@Service
public class ExternalConnectServiceImpl implements IExternalConnectService {

    @Autowired(required = false)
    private List<IExternalConnectHandler> accountHandlers = new ArrayList<>();

    private ConcurrentHashMap<ConnectorType, IExternalConnectHandler> ACCOUNT_TYPE_HANDLER_MAP = new ConcurrentHashMap<>();

    private final static Logger LOG = LoggerFactory.getLogger(ExternalConnectServiceImpl.class);

    private IExternalConnectHandler findAcountHandler(ConnectorType connectorType) {
        IExternalConnectHandler ret = ACCOUNT_TYPE_HANDLER_MAP.get(connectorType);
        if (ret != null) {
            return ret;
        }
        synchronized (connectorType) {
            ret = ACCOUNT_TYPE_HANDLER_MAP.get(connectorType);
            if (ret != null) {
                return ret;
            }

            for (IExternalConnectHandler handler : accountHandlers) {
                if (handler.isSupported(connectorType)) {
                    ret = handler;
                    break;
                }
            }

            //todo ret==null时的处理
            ACCOUNT_TYPE_HANDLER_MAP.put(connectorType, ret);
        }
        return ret;
    }

    private IExternalConnectHandler getAccountHandler(Connector connector) {
        ConnectorType connectorType = ConnectorType.getObj(connector.getType());
        IExternalConnectHandler handler = findAcountHandler(connectorType);
        if (handler == null) {
            LOG.warn("未找到{}对应的处理器", connectorType);
            return null;
        }
        return handler;
    }

    @Override
    public ParamSchema getExternalOrgBasicAttrSchema(Connector connector) {
        IExternalConnectHandler handler = getAccountHandler(connector);
        if (handler == null) return null;
        return handler.getExternalOrgBasicAttrSchema(connector);
    }

    @Override
    public ParamSchema getExternalUserBasicAttrSchema(Connector connector) {
        IExternalConnectHandler handler = getAccountHandler(connector);
        if (handler == null) return null;
        return handler.getExternalUserBasicAttrSchema(connector);
    }

    @Override
    public ParamSchema getExternalRoleBasicAttrSchema(Connector connector) {
        IExternalConnectHandler handler = getAccountHandler(connector);
        if (handler == null) return null;
        return handler.getExternalRoleBasicAttrSchema(connector);
    }

    @Override
    public ConnectorOrgProfile getExternalOrgFullProfile(Connector connector) {
        IExternalConnectHandler handler = getAccountHandler(connector);
        if (handler == null) return null;
        return handler.getExternalOrgFullProfile(connector);
    }

    @Override
    public ConnectorUserProfile getExternalUserFullProfile(Connector connector) {
        IExternalConnectHandler handler = getAccountHandler(connector);
        if (handler == null) return null;
        return handler.getExternalUserFullProfile(connector);
    }

    @Override
    public Map<String, Object> toLocalOrg(ConnectorOrg<String, Object> externalOrg, Map<String, Object> currLocalOrg, Connector connector, Boolean isUpdateMapping) {
        IExternalConnectHandler handler = getAccountHandler(connector);
        if (handler == null) return null;
        return handler.toLocalOrg(externalOrg, currLocalOrg, connector, isUpdateMapping);
    }

    @Override
    public ConnectorOrg<String, Object> toConnectorOrg(LocalOrg localOrg, ConnectorOrg currConnectorOrg, Connector connector, Boolean isUpdateMapping) {
        IExternalConnectHandler handler = getAccountHandler(connector);
        if (handler == null) return null;
        return handler.toConnectorOrg(localOrg, currConnectorOrg, connector, isUpdateMapping);
    }

    @Override
    public Map<String, Object> toLocalUser(ConnectorUser<String, Object> externalUser, Map<String, Object> currLocalUser, Connector connector, Boolean isUpdateMapping) {
        IExternalConnectHandler handler = getAccountHandler(connector);
        if (handler == null) return null;
        return handler.toLocalUser(externalUser, currLocalUser, connector, isUpdateMapping);
    }

    @Override
    public Map<String, Object> toLocalUser(Map<String, Object> externalUser, Connector connector) {
        IExternalConnectHandler handler = getAccountHandler(connector);
        if (handler == null) return null;
        return handler.toLocalUser(externalUser, connector);
    }

    @Override
    public Map<String, Object> toLocalOrg(Map<String, Object> externalOrg, Connector connector) {
        IExternalConnectHandler handler = getAccountHandler(connector);
        if (handler == null) return null;
        return handler.toLocalOrg(externalOrg, connector);
    }

    @Override
    public ConnectorUser<String, Object> toConnectorUser(Map<String, Object> externalUser, Connector connector) {
        IExternalConnectHandler handler = getAccountHandler(connector);
        if (handler == null) return null;
        return handler.toConnectorUser(externalUser, connector);
    }

    @Override
    public ConnectorOrg<String, Object> toConnectorOrg(Map<String, Object> externalOrg, Connector connector) {
        IExternalConnectHandler handler = getAccountHandler(connector);
        if (handler == null) return null;
        return handler.toConnectorOrg(externalOrg, connector);
    }

    @Override
    public ConnectorUser<String, Object> toConnectorUser(LocalUser localUser, ConnectorUser currConnectorUser, Connector connector, Boolean isUpdateMapping) {
        IExternalConnectHandler handler = getAccountHandler(connector);
        if (handler == null) return null;
        return handler.toConnectorUser(localUser, currConnectorUser, connector, isUpdateMapping);
    }

    @Override
    public ConnectorOrg<String, Object> getRootExternalOrg(Connector connector) throws ThirdPartyAccountException {
        ConnectorType connectorType = ConnectorType.getObj(connector.getType());
        IExternalConnectHandler handler = findAcountHandler(connectorType);
        if (handler == null) {
            LOG.warn("未找到{}对应的处理器", connectorType);
            return null;
        }
        ConnectorOrg<String, Object> rootExternalOrg = handler.getRootExternalOrg(connector);
        if (rootExternalOrg == null) {
            throw new ThirdPartyAccountException("获取根组织失败");
        }
        //防止parentIdValue为null
        if (rootExternalOrg.getParentIdValue() == null) {
            rootExternalOrg.setParentIdValue(Strings.EMPTY);
        }
        return rootExternalOrg;
    }

    @Override
    public List<ConnectorOrg<String, Object>> getAllSubExternalOrgs(ConnectorOrg parentExternalOrg, Connector connector) throws ThirdPartyAccountException {
        ConnectorType connectorType = ConnectorType.getObj(connector.getType());
        IExternalConnectHandler handler = findAcountHandler(connectorType);
        if (handler == null) {
            LOG.warn("未找到{}对应的处理器", connectorType);
            return null;
        }
        return handler.getAllSubExternalOrgs(parentExternalOrg, connector);
    }

    @Override
    public List<ConnectorOrg<String, Object>> getSubExternalOrgs(String parentOrgId, Connector connector) throws ThirdPartyAccountException {
        IExternalConnectHandler handler = getAccountHandler(connector);
        if (handler == null) return null;
        return handler.getSubExternalOrgs(parentOrgId, connector);
    }

    @Override
    public List<ConnectorOrg<String, Object>> getSubExternalOrgs(ConnectorOrg parentExternalOrg, Connector connector) throws ThirdPartyAccountException {
        IExternalConnectHandler handler = getAccountHandler(connector);
        if (handler == null) return null;
        return handler.getSubExternalOrgs(parentExternalOrg, connector);
    }

    @Override
    public List<ConnectorOrg<String, Object>> getExternalOrgByIds(List<String> externalOrgIds, Connector connector) throws ThirdPartyAccountException {
        IExternalConnectHandler handler = getAccountHandler(connector);
        if (handler == null) return null;
        return handler.getExternalOrgByIds(externalOrgIds, connector);
    }

    @Override
    public ConnectorOrg getExternalOrgById(String externalOrgId, Connector connector) throws ThirdPartyAccountException {
        ConnectorType connectorType = ConnectorType.getObj(connector.getType());
        IExternalConnectHandler handler = findAcountHandler(connectorType);
        if (handler == null) {
            LOG.warn("未找到{}对应的处理器", connectorType);
            return null;
        }
        return handler.getExternalOrgById(externalOrgId, connector);
    }

    @Override
    public List<ConnectorUser<String, Object>> getExternalUsersByOrg(ConnectorOrg externalOrg, Connector connector) throws ThirdPartyAccountException {
        ConnectorType connectorType = ConnectorType.getObj(connector.getType());
        IExternalConnectHandler handler = findAcountHandler(connectorType);
        if (handler == null) {
            LOG.warn("未找到{}对应的处理器", connectorType);
            return null;
        }
        return handler.getExternalUsersByOrg(externalOrg, connector);
    }

    @Override
    public Boolean listExternalUsersByLimit(Connector connector) {
        IExternalConnectHandler handler = getAccountHandler(connector);
        if (handler == null) return false;
        return handler.listExternalUsersByLimit(connector);
    }

    @Override
    public Boolean fullyModify(Connector connector) {
        IExternalConnectHandler handler = getAccountHandler(connector);
        if (handler == null) return true;
        return handler.fullyModify(connector);
    }

    @Override
    public Boolean limitListExternalUsers(int page, int pageSize, Connector connector, List<ConnectorUser<String, Object>> output) throws ThirdPartyAccountException {
        ConnectorType connectorType = ConnectorType.getObj(connector.getType());
        IExternalConnectHandler handler = findAcountHandler(connectorType);
        if (handler == null) {
            LOG.warn("未找到{}对应的处理器", connectorType);
            return null;
        }
        return handler.limitListExternalUsers(page, pageSize, connector, output);
    }

    @Override
    public Integer limitStartPage(Connector connector) {
        IExternalConnectHandler handler = getAccountHandler(connector);
        if (handler == null) return 0;
        return handler.limitStartPage(connector);
    }

    @Override
    public Integer limitPageSize(Connector connector) {
        IExternalConnectHandler handler = getAccountHandler(connector);
        if (handler == null) return 100;
        return handler.limitPageSize(connector);
    }

    @Override
    public ConnectorUser getExternalUserById(String externalAccountId, Connector connector) throws ThirdPartyAccountException {
        ConnectorType connectorType = ConnectorType.getObj(connector.getType());
        IExternalConnectHandler handler = findAcountHandler(connectorType);
        if (handler == null) {
            LOG.warn("未找到{}对应的处理器", connectorType);
            return null;
        }
        return handler.getExternalUserById(externalAccountId, connector);
    }

    @Override
    public List<ConnectorUser<String, Object>> getExternalUsers(List<String> externalAccountIds, Connector connector) throws ThirdPartyAccountException {
        IExternalConnectHandler handler = getAccountHandler(connector);
        if (handler == null) return null;
        return handler.getExternalUsers(externalAccountIds, connector);
    }

    @Override
    public boolean isSortedByOrgLevel(Connector connector) {
        ConnectorType connectorType = ConnectorType.getObj(connector.getType());
        IExternalConnectHandler handler = findAcountHandler(connectorType);
        if (handler == null) {
            LOG.warn("未找到{}对应的处理器", connectorType);
            return false;
        }
        return handler.isSortedByOrgLevel();
    }

    @Override
    public int login(Connector connector, String username, String password) {
        ConnectorType connectorType = ConnectorType.getObj(connector.getType());
        IExternalConnectHandler handler = findAcountHandler(connectorType);
        if (handler == null) {
            LOG.warn("未找到{}对应的处理器", connectorType);
            return 1;
        }
        return handler.login(connector, username, password);
    }

    @Override
    public ConnectorUser test(Connector connector) {
        ConnectorType connectorType = ConnectorType.getObj(connector.getType());
        IExternalConnectHandler handler = findAcountHandler(connectorType);
        if (handler == null) {
            LOG.warn("未找到{}对应的处理器", connectorType);
            return null;
        }
        return handler.test(connector);
    }

    @Override
    public List<Map> getRoles(Connector connector) {
        ConnectorType connectorType = ConnectorType.getObj(connector.getType());
        IExternalConnectHandler handler = findAcountHandler(connectorType);
        if (handler == null) {
            LOG.warn("未找到{}对应的处理器", connectorType);
            return null;
        }
        return handler.getRoleList(connector);
    }

    @Override
    public List<String> getCurrentRoleIds(Connector connector) {
        ConnectorType connectorType = ConnectorType.getObj(connector.getType());
        IExternalConnectHandler handler = findAcountHandler(connectorType);
        if (handler == null) {
            LOG.warn("未找到{}对应的处理器", connectorType);
            return null;
        }
        return handler.getCurrentRoleIds(connector);
    }

    @Override
    public void closeConnection(Connector connector) {
        ConnectorType connectorType = ConnectorType.getObj(connector.getType());
        IExternalConnectHandler handler = findAcountHandler(connectorType);
        if (handler == null) {
            LOG.warn("未找到{}对应的处理器", connector);
            return;
        }
        handler.closeConnection(connector);
    }

    @Override
    public List<String> getAllExternalParentOrgIds(Connector connector, String orgId) {
        ConnectorType connectorType = ConnectorType.getObj(connector.getType());
        IExternalConnectHandler handler = findAcountHandler(connectorType);
        if (handler == null) {
            LOG.warn("未找到{}对应的处理器", connector);
            return null;
        }
        return handler.getAllExternalParentOrgIds(connector, orgId);
    }

    @Override
    public String createExternalOrg(ConnectorOrg externalOrg, Connector connector) throws ThirdPartyAccountException {
        LOG.info("createExternalOrg, externalOrg:{}", externalOrg);
        IExternalConnectHandler handler = getAccountHandler(connector);
        if (handler == null) return null;
        return handler.createExternalOrg(externalOrg, connector);
    }

    @Override
    public String modifyExternalOrg(String externalOrgId, ConnectorOrg externalOrg, Connector connector) throws ThirdPartyAccountException {
        LOG.info("modifyExternalOrg, externalOrgId:{}, externalOrg:{}", externalOrgId, externalOrg);
        IExternalConnectHandler handler = getAccountHandler(connector);
        if (handler == null) return null;
        return handler.modifyExternalOrg(externalOrgId, externalOrg, connector);
    }

    @Override
    public void deleteExternalOrg(String externalOrgId, Connector connector) throws ThirdPartyAccountException {
        LOG.info("deleteExternalOrg, externalOrgId:{}", externalOrgId);
        IExternalConnectHandler handler = getAccountHandler(connector);
        if (handler == null) return;
        handler.deleteExternalOrg(externalOrgId, connector);
    }

    @Override
    public void deleteExternalOrgs(List<String> externalOrgIds, Connector connector) throws ThirdPartyAccountException {
        LOG.info("deleteExternalOrgs, externalOrgIds:{}", externalOrgIds);
        IExternalConnectHandler handler = getAccountHandler(connector);
        if (handler == null) return;
        handler.deleteExternalOrgs(externalOrgIds, connector);
    }

    @Override
    public String createExternalUser(ConnectorUser externalUser, Connector connector) throws ThirdPartyAccountException {
        LOG.info("createExternalUser, externalUser:{}", externalUser);
        IExternalConnectHandler handler = getAccountHandler(connector);
        if (handler == null) return null;
        return handler.createExternalUser(externalUser, connector);
    }

    @Override
    public String modifyExternalUser(String externalUserId, ConnectorUser externalUser, Connector connector) throws ThirdPartyAccountException {
        LOG.info("modifyExternalUser, externalUserId:{}, externalUser:{}", externalUserId, externalUser);
        IExternalConnectHandler handler = getAccountHandler(connector);
        if (handler == null) return null;
        return handler.modifyExternalUser(externalUserId, externalUser, connector);
    }

    @Override
    public void deleteExternalUser(String externalUserId, Connector connector) throws ThirdPartyAccountException {
        LOG.info("deleteExternalUser, externalUserId:{}", externalUserId);
        IExternalConnectHandler handler = getAccountHandler(connector);
        if (handler == null) return;
        handler.deleteExternalUser(externalUserId, connector);
    }

    @Override
    public void deleteExternalUser(ConnectorUser externalUser, Connector connector) throws ThirdPartyAccountException {
        IExternalConnectHandler handler = getAccountHandler(connector);
        if (handler == null) return;
        handler.deleteExternalUser(externalUser, connector);
    }

    @Override
    public void deleteExternalUsers(List<String> externalUserIds, Connector connector) throws ThirdPartyAccountException {
        LOG.info("deleteExternalUsers, externalUserIds:{}", externalUserIds);
        IExternalConnectHandler handler = getAccountHandler(connector);
        if (handler == null) return;
        handler.deleteExternalUsers(externalUserIds, connector);
    }


    @Override
    public ConnectorRole<String, Object> toConnectorRole(LocalRole localRole, ConnectorRole currConnectorRole, Connector connector, Boolean isUpdateMapping) {
        IExternalConnectHandler handler = getAccountHandler(connector);
        if (handler == null) return null;
        return handler.toConnectorRole(localRole, currConnectorRole, connector, isUpdateMapping);
    }

    @Override
    public Map<String, Object> toLocalRole(LocalRole localRole, ConnectorRole currConnectorRole, Connector connector, Boolean isUpdateMapping) {
        IExternalConnectHandler handler = getAccountHandler(connector);
        if (handler == null) return null;
        return handler.toLocalRole(localRole, currConnectorRole, connector, isUpdateMapping);
    }


    @Override
    public Map<String, List<ConnectorRole>> getFullRolesInfoToSync(Connector connector) throws ThirdPartyAccountException {
        LOG.info("getExternalRoleByGroups, externalRoleGroups:{}", connector.getRoles());
        IExternalConnectHandler handler = getAccountHandler(connector);
        if (handler == null) return null;
        return handler.getFullRolesInfoToSync(connector);
    }

    @Override
    public ConnectorRole getExternalRoleById(String externalRoleId, Connector connector) throws ThirdPartyAccountException {
        LOG.info("getExternalRoleById, externalRole:{}", externalRoleId);
        IExternalConnectHandler handler = getAccountHandler(connector);
        if (handler == null) return null;
        return handler.getExternalRoleGroup(connector, externalRoleId);
    }

    @Override
    public ConnectorRole getExternalRoleUsers(String externalRoleId, Connector connector) throws ThirdPartyAccountException {
        LOG.info("getExternalRoleUsers, externalRole:{}", externalRoleId);
        IExternalConnectHandler handler = getAccountHandler(connector);
        if (handler == null) return null;
        return handler.getExternalRoleUsers(connector, externalRoleId);
    }

    @Override
    public String createExternalGroup(ConnectorRole externalRole, Connector connector) throws ThirdPartyAccountException {
        LOG.info("createExternalGroup, externalRoleGroup:{}", externalRole);
        IExternalConnectHandler handler = getAccountHandler(connector);
        if (handler == null) return null;
        return handler.createExternalRoleGroup(connector, externalRole);
    }

    @Override
    public String createExternalRole(ConnectorRole externalRole, Connector connector) throws ThirdPartyAccountException {
        LOG.info("createExternalRole, externalRole:{}", externalRole);
        IExternalConnectHandler handler = getAccountHandler(connector);
        if (handler == null) return null;
        return handler.createExternalRole(connector, externalRole);
    }

    @Override
    public String modifyExternalRole(String externalRoleId, ConnectorRole externalRole, Connector connector, boolean roleGroup) throws ThirdPartyAccountException {
        LOG.info("modifyExternalRole, externalRoleId:{}, externalRole:{}", externalRoleId, externalRole);
        IExternalConnectHandler handler = getAccountHandler(connector);
        if (handler == null) return null;
        return handler.modifyExternalRole(connector, externalRoleId, externalRole, roleGroup);
    }

    @Override
    public void deleteExternalRole(String externalRoleId, Connector connector) throws ThirdPartyAccountException {
        LOG.info("deleteExternalRole, externalRoleId:{}", externalRoleId);
        IExternalConnectHandler handler = getAccountHandler(connector);
        if (handler == null) return;
        handler.deleteExternalRole(connector, externalRoleId);
    }


    @Override
    public List<Map> getSimplelist(Connector connector, String roleId) throws ThirdPartyAccountException {
        LOG.info("getSimplelist, externalRoleId:{}", roleId);
        IExternalConnectHandler handler = getAccountHandler(connector);
        if (handler == null) return null;
        return handler.getSimplelist(connector, roleId);
    }

    @Override
    public void deleteExternalRoles(List<String> externalRoleIds, Connector connector) throws ThirdPartyAccountException {
        LOG.info("deleteExternalRoles, externalRole:{}", externalRoleIds);
        IExternalConnectHandler handler = getAccountHandler(connector);
        if (handler == null) return;
        handler.deleteExternalRoles(connector, externalRoleIds);
    }

    @Override
    public void addUserToRole(Connector connector, String roleId, List<String> userIds) throws ThirdPartyAccountException {
        LOG.info("addUserToRole, addUserToRole:{}", roleId);
        IExternalConnectHandler handler = getAccountHandler(connector);
        if (handler == null) return;
        handler.addUserToRole(connector, roleId, userIds);
    }

    @Override
    public void removeUserFromRole(Connector connector, String roleId, List<String> userIds) throws ThirdPartyAccountException {
        LOG.info("removeUserFromRole, removeUserFromRole:{}", roleId);
        IExternalConnectHandler handler = getAccountHandler(connector);
        if (handler == null) return;
        handler.removeUserFromRole(connector, roleId, userIds);
    }

    @Override
    public String generatePushProfile(ConnectorUser externalUser, Connector connector) {
        LOG.info("generatePushProfile, externalUserId:{}", externalUser.getIdValue());
        IExternalConnectHandler handler = getAccountHandler(connector);
        if (handler == null) return null;
        return handler.generatePushProfile(externalUser, connector);
    }

    @Override
    public void mergeUsers(ConnectorUser newExternalUser, ConnectorUser oldExternalUser, Connector connector) {
        LOG.info("mergeUsers, externalUserId:{}", newExternalUser.getIdValue());
        IExternalConnectHandler handler = getAccountHandler(connector);
        if (handler == null) return;
        handler.mergeUsers(newExternalUser, oldExternalUser, connector);
    }

    @Override
    public LocalDateTime getUserMappingUpdateTime(ConnectorType connectorType) {
        IExternalConnectHandler handler = findAcountHandler(connectorType);
        if (handler == null) return null;
        return handler.getUserMappingUpdateTime();
    }
}
