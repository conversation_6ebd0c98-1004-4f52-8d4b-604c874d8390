package com.cyberscraft.uep.account.client.auth;

import com.cyberscraft.uep.account.client.domain.account.MokaAccount;
import com.cyberscraft.uep.common.bean.RestApiResponse;
import com.cyberscraft.uep.common.domain.auth.Account;
import com.cyberscraft.uep.common.domain.auth.AccountParam;
import com.cyberscraft.uep.common.domain.auth.AuthorizeCredential;
import com.cyberscraft.uep.common.exception.ApiConnectException;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.common.util.MappingParser;
import com.cyberscraft.uep.common.util.RestAPIUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * moka认证
 * <AUTHOR>
 * @since 2024/5/22
 */
@Service
public class MokaAuthenticateHandler implements IAuthenticateHandler {

    private final static Logger logger = LoggerFactory.getLogger(MokaAuthenticateHandler.class);
    private Map<String, String> cacheMap = new ConcurrentHashMap<>();

    private final static String protocolType = "MOKA";

    @Override
    public boolean isSupported(String type) {
        return protocolType.equals(type);
    }

    @Override
    public AuthorizeCredential auth(Account account, String cacheKey, Boolean refresh) {
        AuthorizeCredential authorizeCredential = new AuthorizeCredential();
        MokaAccount mokaAccount = (MokaAccount) account;
        String fullCacheKey = IAuthenticateHandler.CACHE_KEY_PREFIX + cacheKey;
        if (!refresh) {
            String cacheValue = cacheMap.get(fullCacheKey);
            if (cacheValue != null) {
                String token = cacheValue;
                HashMap<String, String> headMap = new HashMap<>();
                headMap.put("Authorization", token);
                authorizeCredential.setHead(headMap);
                return authorizeCredential;
            }
        }
        RestApiResponse response;
        String url = "https://api.mokahr.com/api-platform/v1/auth/oauth2/getToken";
        Map<String, Object> postParam = new HashMap<>();
        postParam.put("clientID", mokaAccount.getClientID());
        postParam.put("clientSecret", mokaAccount.getClientSecret());
        postParam.put("grantType","client_credentials");
        response = RestAPIUtil.modifyEntityForString(url, "POST", postParam, new HashMap<>(), new HashMap<>(), new HashMap());
        Map map;
        if (response.getBody() instanceof String) {
            map = JsonUtil.str2Map((String) response.getBody());
        } else {
            map = (Map) response.getBody();
        }
        checkResponse(map);
        Map<String,Object> tokenInfo = (Map) map.get("data");
        String token = "Bearer "+ tokenInfo.get("accessToken").toString();
        HashMap<String, String> headMap = new HashMap<>();
        headMap.put("Authorization", token);
        authorizeCredential.setHead(headMap);
        cacheMap.put(fullCacheKey, token);
        return authorizeCredential;

    }

    @Override
    public void evictCache(String cacheKey) {
        String fullCacheKey = IAuthenticateHandler.CACHE_KEY_PREFIX + cacheKey;
        cacheMap.remove(fullCacheKey);
    }

    @Override
    public Account getAccount(AccountParam accountParam, Map<String, Object> queryParam, Map<String, Object> headerParam) {
        return MappingParser.schema2Obj(new HashMap<>(), accountParam.getAccount(), MokaAccount.class);
    }

    void checkResponse(Map<String, Object> body) {
        String code = body.get("code").toString();
        if (StringUtils.isEmpty(code) || !"0".equals(code)) {
            logger.error("checkResponse is fail");
            throw new ApiConnectException("access-token fail");
        }
    }
}
