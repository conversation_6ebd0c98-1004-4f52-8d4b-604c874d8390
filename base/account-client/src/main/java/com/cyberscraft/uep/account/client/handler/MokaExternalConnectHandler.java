package com.cyberscraft.uep.account.client.handler;

import com.cyberscraft.uep.account.client.constant.AccountConstant;
import com.cyberscraft.uep.account.client.constant.ConnectorType;
import com.cyberscraft.uep.account.client.constant.MokaOrgAttr;
import com.cyberscraft.uep.account.client.constant.MokaUserAttr;
import com.cyberscraft.uep.account.client.domain.*;
import com.cyberscraft.uep.account.client.domain.account.MokaAccount;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccessTokenInvalidException;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountErrorType;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountException;
import com.cyberscraft.uep.account.client.provider.IExternalConnectHandler;
import com.cyberscraft.uep.account.client.service.IAuthenticateService;
import com.cyberscraft.uep.account.client.util.AccountUtil;
import com.cyberscraft.uep.common.bean.RestApiResponse;
import com.cyberscraft.uep.common.domain.DataTypeEnum;
import com.cyberscraft.uep.common.domain.ParamSchema;
import com.cyberscraft.uep.common.domain.auth.AuthProtocol;
import com.cyberscraft.uep.common.domain.auth.AuthorizeCredential;
import com.cyberscraft.uep.common.enums.SysCodeEnum;
import com.cyberscraft.uep.common.util.CollectionUtils;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.common.util.RestAPIUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @Author：seven
 * @Project：uep-services
 * @since：2024/5/29
 */
@Service
public class MokaExternalConnectHandler implements IExternalConnectHandler {

    private final static Logger logger = LoggerFactory.getLogger(MokaExternalConnectHandler.class);

    /**
     * 获取全量组织架构
     */
    private static final String getAllOrg = "https://api.mokahr.com/api-platform/v1/departments";
    /**
     * 获取组织下的员工信息
     */
    private static final String getUsers = "https://api.mokahr.com/api-platform/v1/users/list";
    /**
     * 获取角色信息
     */
    private static final String getUserRole = "https://api.mokahr.com/api-platform/v1/users/roles?type=all";


    @Autowired
    private IAuthenticateService authenticateService;

    private final ParamSchema userBasicAttrSchema;

    private final ParamSchema orgBasicAttrSchema;

    public MokaExternalConnectHandler() {
        orgBasicAttrSchema = new ParamSchema(AccountConstant.APPORG_PROFILE_ROOTNAME, DataTypeEnum.OBJECT, "部门profile");
        for (MokaOrgAttr value : MokaOrgAttr.values()) {
            orgBasicAttrSchema.addSubParam(value.ps());
        }

        userBasicAttrSchema = new ParamSchema(AccountConstant.APPUSER_PROFILE_ROOTNAME, DataTypeEnum.OBJECT, "用户profile");
        for (MokaUserAttr value : MokaUserAttr.values()) {
            userBasicAttrSchema.addSubParam(value.ps());
        }

    }

    @Override
    public AuthorizeCredential getAuthCredential(Connector connector, Boolean refresh) {
        try {
            MokaAccount account = JsonUtil.str2Obj(connector.getConfig(), MokaAccount.class);
            AuthorizeCredential credential = authenticateService.auth(AuthProtocol.MOKA.name(), account, String.valueOf(connector.getId()), refresh);
            return credential;
        } catch (Exception e) {
            throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), e.getMessage());
        }
    }

    @Override
    public boolean isSupported(ConnectorType connectorType) {
        return ConnectorType.MOKA == connectorType;
    }

    @Override
    public ParamSchema getExternalOrgBasicAttrSchema(Connector connector) {
        return orgBasicAttrSchema;
    }

    @Override
    public ParamSchema getExternalUserBasicAttrSchema(Connector connector) {
        return userBasicAttrSchema;
    }

    @Override
    public ConnectorOrgProfile getExternalOrgFullProfile(Connector connector) {
        ParamSchema orgAllProfile = buildExternalOrgFullSchema(connector);

        ConnectorOrgProfile connectorOrgProfile = new ConnectorOrgProfile();
        connectorOrgProfile.setIdName(MokaOrgAttr.departmentId.getAttrName());
        connectorOrgProfile.setParentIdName(MokaOrgAttr.parentId.getAttrName());
        connectorOrgProfile.setNameName(MokaOrgAttr.name.getAttrName());
        connectorOrgProfile.setAttrSchema(orgAllProfile);

        return connectorOrgProfile;
    }

    @Override
    public ConnectorUserProfile getExternalUserFullProfile(Connector connector) {
        ParamSchema userAllProfile = buildExternalUserFullSchema(connector);

        ConnectorUserProfile connectorUserProfile = new ConnectorUserProfile();
        connectorUserProfile.setIdName(MokaUserAttr.userId.getAttrName());
        connectorUserProfile.setDeptName(MokaUserAttr.departmentId.getAttrName());
        connectorUserProfile.setAttrSchema(userAllProfile);
        return connectorUserProfile;
    }

    @Override
    public ConnectorOrg<String, Object> getRootExternalOrg(Connector connector) throws ThirdPartyAccountException {
        String rootCode = connector.getRootCode();
        if (StringUtils.isBlank(rootCode)) {
            throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), "rootCode is NUll");
        }
        //moka没有对应方法，只能通过获取全部部门取一级部门
        List<ConnectorOrg<String, Object>> allExternalOrgs = getAllSubExternalOrgs(null,connector);
        for(ConnectorOrg<String, Object> org:allExternalOrgs){
            if(null==org.getParentIdValue()){
                return org;
            }
        }
        return null;
    }

    @Override
    public List<ConnectorOrg<String, Object>> getAllSubExternalOrgs(ConnectorOrg parentExternalOrg, Connector connector) throws ThirdPartyAccountException {
        Map<String, String> header = new HashMap<>();
        RestApiResponse response = null;
        AuthorizeCredential authCredential = getAuthCredential(connector, false);
        try {
            header.putAll(authCredential.getHead());
            response = RestAPIUtil.modifyEntityForString(getAllOrg, "GET", null, header, null, new HashMap());
            checkResponse1(response, getAllOrg);
        } catch (ThirdPartyAccessTokenInvalidException e) {
            authCredential = getAuthCredential(connector, true);
            header.putAll(authCredential.getHead());
            response = RestAPIUtil.modifyEntityForString(getAllOrg, "GET", null, header, null, new HashMap());
            checkResponse1(response, getAllOrg);
        } catch (Exception e) {
            throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), e.getMessage());
        }
        Map<String, Object> resp = JsonUtil.str2Map(response.getBody().toString());
        List<Map<String, Object>> allExternalOrgs = (List<Map<String, Object>>) resp.get("departments");
        List<ConnectorOrg<String, Object>> ret = new ArrayList<>();
        ret.addAll(AccountUtil.to(allExternalOrgs, getExternalOrgFullProfile(connector)));
        logger.info("get moka department size is {} , department is {}", allExternalOrgs.size(), parentExternalOrg);
        return ret;
    }

    @Override
    public List<ConnectorOrg<String, Object>> getSubExternalOrgs(String parentOrgId, Connector connector) throws ThirdPartyAccountException {
        return null;
    }

    @Override
    public List<ConnectorOrg<String, Object>> getExternalOrgByIds(List<String> externalOrgIds, Connector connector) throws ThirdPartyAccountException {
        return null;
    }

    @Override
    public ConnectorOrg getExternalOrgById(String externalOrgId, Connector connector) throws ThirdPartyAccountException {
        return null;
    }

    @Override
    public boolean isSortedByOrgLevel() {
        return false;
    }

    @Override
    public List<ConnectorUser<String, Object>> getExternalUsersByOrg(ConnectorOrg externalOrg, Connector connector) throws ThirdPartyAccountException {
        Map<String, Object> body = new HashMap<>();
        body.put("departmentId", externalOrg.getIdValue());
        Map<String, String> header = new HashMap<>();
        RestApiResponse response = null;
        AuthorizeCredential authCredential = getAuthCredential(connector, false);
        List<Map<String, Object>> res = new ArrayList<>();
        Map<String,Object> queryParams = new HashMap<>();
        do {
            try {
                header.putAll(authCredential.getHead());
                response = RestAPIUtil.modifyEntityForString(getUsers, "POST", body, header, queryParams, new HashMap());
                checkResponse2(response, getUsers);
                Map<String, Object> resp = JsonUtil.str2Map(response.getBody().toString());
                List<Map<String, Object>> ExternalUsers = (List<Map<String, Object>>) resp.get("data");
                for(Map<String, Object> map:ExternalUsers){
                    map.put("departmentId",externalOrg.getIdValue().toString());
                }
                res.addAll(ExternalUsers);
                //分页处理
                if(null==resp.get("next")){
                    break;
                }else{
                    queryParams.put("next",resp.get("next"));
                }

            } catch (ThirdPartyAccessTokenInvalidException e) {
                header.putAll(authCredential.getHead());
                response = RestAPIUtil.modifyEntityForString(getUsers, "POST", body, header, null, new HashMap());
                checkResponse2(response, getUsers);
            } catch (Exception e) {
                throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), e.getMessage());
            }
        } while (true);

        return AccountUtil.to(res, getExternalUserFullProfile(connector));
    }

    @Override
    public List<ConnectorUser<String, Object>> getExternalUsers(List<String> externalUserIds, Connector connector) throws ThirdPartyAccountException {
        return null;
    }

    @Override
    public Boolean limitListExternalUsers(int page, int pageSize, Connector connector, List<ConnectorUser<String, Object>> output) throws ThirdPartyAccountException {
        return null;
    }

    @Override
    public ConnectorUser getExternalUserById(String externalAccountId, Connector connector) throws ThirdPartyAccountException {
        List<String> userIds = Arrays.asList(externalAccountId);
        List<ConnectorUser<String, Object>> externalUsers = getExternalUsers(userIds, connector);
        if (CollectionUtils.isNotEmpty(externalUsers)) {
            return externalUsers.get(0);
        }
        return null;
    }

    @Override
    public int login(Connector connector, String username, String password) {
        return 0;
    }

    @Override
    public ConnectorUser test(Connector connector) {
        if (connector == null) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.CONNECTOR_INVALID);
        }
        String testAccount = connector.getTestAccount();
        if (testAccount == null) {
            return null;
        }
        Map<String, Object> body = new HashMap<>();
        body.put("phone",testAccount);
        AuthorizeCredential authCredential = getAuthCredential(connector, true);
        RestApiResponse response = null;
        try {
            response = RestAPIUtil.modifyEntityForString(getUsers, "POST", body, authCredential.getHead(), null, new HashMap());
            checkResponse2(response, getUsers);
        } catch (ThirdPartyAccessTokenInvalidException e) {
            authCredential = getAuthCredential(connector, true);
            response = RestAPIUtil.modifyEntityForString(getUsers, "POST", body, authCredential.getHead(), null, new HashMap());
            checkResponse2(response, getUsers);
        } catch (Exception e) {
            throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), e.getMessage());
        }
        Map<String, Object> resp = JsonUtil.str2Map(response.getBody().toString());
        List<Map<String, Object>> users = (List<Map<String, Object>>) resp.get("data");
        return AccountUtil.to(users.get(0), getExternalUserFullProfile(connector));
    }

    @Override
    public void closeConnection(Connector connector) {}

    @Override
    public String createExternalOrg(ConnectorOrg externalOrg, Connector connector) throws ThirdPartyAccountException {
        return null;
    }

    @Override
    public String modifyExternalOrg(String externalOrgId, ConnectorOrg externalOrg, Connector connector) throws ThirdPartyAccountException {
        return null;
    }

    @Override
    public void deleteExternalOrg(String externalOrgId, Connector connector) throws ThirdPartyAccountException {

    }

    @Override
    public void deleteExternalOrgs(List<String> externalOrgIds, Connector connector) throws ThirdPartyAccountException {

    }

    @Override
    public String createExternalUser(ConnectorUser externalUser, Connector connector) throws ThirdPartyAccountException {
        return null;
    }

    @Override
    public String modifyExternalUser(String externalUserId, ConnectorUser externalUser, Connector connector) throws ThirdPartyAccountException {
        return null;
    }

    @Override
    public void deleteExternalUser(String externalUserId, Connector connector) throws ThirdPartyAccountException {

    }

    @Override
    public void deleteExternalUsers(List<String> externalUserIds, Connector connector) throws ThirdPartyAccountException {

    }

    /**
     * 获取组织结果检查
     * @param response
     * @param api
     */
    protected void checkResponse1(RestApiResponse response, String api) {
        if (response.getHttpStatus() != 200) {
            logger.info("moka error {}", api);
            throw new ThirdPartyAccessTokenInvalidException(String.valueOf(response.getHttpStatus()), "网络错误，详情："+response.toParamMap());
        }
        Map<String, Object> resp = JsonUtil.str2Map(response.getBody().toString());
        Boolean success = (boolean) resp.get("success");
        if(!success){
            logger.info("moka error {}", api);
            throw new ThirdPartyAccessTokenInvalidException(String.valueOf(response.getHttpStatus()), "moka请求获取组织信息错误，详情："+response.toParamMap());
        }
    }

    /**
     * 获取人员结果检查
     * @param response
     * @param api
     */
    protected void checkResponse2(RestApiResponse response, String api) {
        if (response.getHttpStatus() != 200) {
            logger.info("moka error {}", api);
            throw new ThirdPartyAccessTokenInvalidException(String.valueOf(response.getHttpStatus()), "网络错误，详情："+response.toParamMap());
        }
        Map<String, Object> resp = JsonUtil.str2Map(response.getBody().toString());
        String message = resp.get("message").toString();
        if(!"success".equals(message)){
            logger.info("moka error {}", api);
            throw new ThirdPartyAccessTokenInvalidException(String.valueOf(response.getHttpStatus()), "moka请求获取人员信息错误，详情："+response.toParamMap());
        }
    }


}
