package com.cyberscraft.uep.account.client.exception;

import com.cyberscraft.uep.common.enums.ExceptionCodeEnum;
import com.cyberscraft.uep.common.enums.SysCodeEnum;
import com.cyberscraft.uep.common.exception.InternationalMessage;
import com.cyberscraft.uep.common.exception.errorcode.MultiLanguageExceptionCodes;

/***
 *
 * @date 2021-10-30
 * <AUTHOR>
 ***/
public class ThirdPartyAccountException extends RuntimeException {

    private String code;
    private String appendMsg;

    private MultiLanguageExceptionCodes multiLanguageExceptionCodes;

    private InternationalMessage internationalMessage;

    public ThirdPartyAccountException(String code, String message) {
        super(message);
        this.code = SysCodeEnum.THIRDPARTYACCOUNT.getCode() + code;
    }

    /***
     *
     * @param code
     * @param message
     * @param e
     */
    public ThirdPartyAccountException(String code, String message, Throwable e) {
        super(message, e);
        this.code = SysCodeEnum.THIRDPARTYACCOUNT.getCode() + code;
    }

    public ThirdPartyAccountException(MultiLanguageExceptionCodes multiLanguageExceptionCodes) {
        super(multiLanguageExceptionCodes.getDesc());
        this.code = SysCodeEnum.THIRDPARTYACCOUNT.getCode() + multiLanguageExceptionCodes.getCode();
        this.multiLanguageExceptionCodes = multiLanguageExceptionCodes;
    }

    public ThirdPartyAccountException(String message, MultiLanguageExceptionCodes multiLanguageExceptionCodes) {
        super(message);
        this.code = SysCodeEnum.THIRDPARTYACCOUNT.getCode() + multiLanguageExceptionCodes.getCode();
        this.multiLanguageExceptionCodes = multiLanguageExceptionCodes;
    }



    /***
     * @param multiLanguageExceptionCodes
      */
    public ThirdPartyAccountException(String message, MultiLanguageExceptionCodes multiLanguageExceptionCodes, String appendMsg) {
        super(message);
        this.code = SysCodeEnum.THIRDPARTYACCOUNT.getCode() + multiLanguageExceptionCodes.getCode();
        this.multiLanguageExceptionCodes = multiLanguageExceptionCodes;
        this.appendMsg = appendMsg;
    }

    /***
     *
     * @param message
     * @param e
     */
    public ThirdPartyAccountException(String message, Throwable e) {
        super(message, e);
        this.code = SysCodeEnum.THIRDPARTYACCOUNT.getCode() + ExceptionCodeEnum.INNER_ERROR.getCode();
    }

    public ThirdPartyAccountException(Throwable e){
        super(e);
        this.code = SysCodeEnum.THIRDPARTYACCOUNT.getCode() + ExceptionCodeEnum.INNER_ERROR.getCode();
    }

    public ThirdPartyAccountException(String message) {
        super(message);
        this.code = SysCodeEnum.THIRDPARTYACCOUNT.getCode() + ExceptionCodeEnum.INNER_ERROR.getCode();
    }

    public ThirdPartyAccountException(ExceptionCodeEnum error) {
        super(error.getMessage());
        this.code = SysCodeEnum.THIRDPARTYACCOUNT.getCode() + error.getCode();
    }

    public ThirdPartyAccountException(ThirdPartyAccountErrorType error) {
        super(error.getMessage());
        this.code = error.getCode();
    }

    public ThirdPartyAccountException(ThirdPartyAccountErrorType error, String errMsg) {
        super(errMsg);
        this.code = error.getCode();
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public MultiLanguageExceptionCodes getMultiLanguageExceptionCodes() {
        return multiLanguageExceptionCodes;
    }

    public void setMultiLanguageExceptionCodes(MultiLanguageExceptionCodes multiLanguageExceptionCodes) {
        this.multiLanguageExceptionCodes = multiLanguageExceptionCodes;
    }

    public InternationalMessage getInternationalMessage() {
        return internationalMessage;
    }

    public void setInternationalMessage(InternationalMessage internationalMessage) {
        this.internationalMessage = internationalMessage;
    }

    public String getAppendMsg() {
        return appendMsg;
    }

    public void setAppendMsg(String appendMsg) {
        this.appendMsg = appendMsg;
    }
}
