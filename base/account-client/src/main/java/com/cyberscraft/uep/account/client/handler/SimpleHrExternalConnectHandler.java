package com.cyberscraft.uep.account.client.handler;

import com.cyberscraft.uep.account.client.constant.AccountConstant;
import com.cyberscraft.uep.account.client.constant.ConnectorType;
import com.cyberscraft.uep.account.client.constant.SimpleHrOrgAttr;
import com.cyberscraft.uep.account.client.constant.SimpleHrUserAttr;
import com.cyberscraft.uep.account.client.domain.*;
import com.cyberscraft.uep.account.client.domain.account.SimpleHrAccount;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccessTokenInvalidException;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountException;
import com.cyberscraft.uep.account.client.provider.IExternalConnectHandler;
import com.cyberscraft.uep.account.client.util.AccountUtil;
import com.cyberscraft.uep.common.bean.RestApiResponse;
import com.cyberscraft.uep.common.domain.DataTypeEnum;
import com.cyberscraft.uep.common.domain.ParamSchema;
import com.cyberscraft.uep.common.enums.SysCodeEnum;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.common.util.RestAPIUtil;
import com.cyberscraft.uep.common.util.WebUrlUtil;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.ResourceAccessException;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.cyberscraft.uep.account.client.exception.ThirdPartyAccountErrorType.CONNECTOR_INVALID;
import static com.cyberscraft.uep.common.enums.SysCodeConstant.THIRDPARTYACCOUNT;

@Service
public class SimpleHrExternalConnectHandler implements IExternalConnectHandler {
    private final static Logger logger = LoggerFactory.getLogger(SimpleHrExternalConnectHandler.class.getName());

    /**
     * 获取全量组织架构
     */
    private static final String orgApi = "/api/ulYcRBiE/dhr/getOrganization";
    /**
     * 获取全量员工信息
     */
    private static final String userApi = "/api/ulYcRBiE/dhr/getAccount";

    private final ParamSchema userBasicAttrSchema;

    private final ParamSchema orgBasicAttrSchema;

    public SimpleHrExternalConnectHandler() {
        orgBasicAttrSchema = new ParamSchema(AccountConstant.APPORG_PROFILE_ROOTNAME, DataTypeEnum.OBJECT, "部门profile");
        for (SimpleHrOrgAttr value : SimpleHrOrgAttr.values()) {
            orgBasicAttrSchema.addSubParam(value.ps());
        }
        userBasicAttrSchema = new ParamSchema(AccountConstant.APPUSER_PROFILE_ROOTNAME, DataTypeEnum.OBJECT, "用户profile");
        for (SimpleHrUserAttr value : SimpleHrUserAttr.values()) {
            userBasicAttrSchema.addSubParam(value.ps());
        }
    }

    @Override
    public boolean isSupported(ConnectorType connectorType) {
        return ConnectorType.SIMPLEHR == connectorType;
    }

    @Override
    public ParamSchema getExternalOrgBasicAttrSchema(Connector connector) {
        return orgBasicAttrSchema;
    }

    @Override
    public ParamSchema getExternalUserBasicAttrSchema(Connector connector) {
        return userBasicAttrSchema;
    }

    @Override
    public ConnectorOrgProfile getExternalOrgFullProfile(Connector connector) {
        ParamSchema orgAllProfile = buildExternalOrgFullSchema(connector);
        ConnectorOrgProfile connectorOrgProfile = new ConnectorOrgProfile();
        connectorOrgProfile.setIdName(SimpleHrOrgAttr.deptCode.getAttrName());
        connectorOrgProfile.setParentIdName(SimpleHrOrgAttr.parentCode.getAttrName());
        connectorOrgProfile.setNameName(SimpleHrOrgAttr.name.getAttrName());
        connectorOrgProfile.setAttrSchema(orgAllProfile);

        return connectorOrgProfile;
    }

    @Override
    public ConnectorUserProfile getExternalUserFullProfile(Connector connector) {
        ParamSchema userAllProfile = buildExternalUserFullSchema(connector);
        ConnectorUserProfile connectorUserProfile = new ConnectorUserProfile();
        connectorUserProfile.setIdName(SimpleHrUserAttr.accountId.getAttrName());
        connectorUserProfile.setDeptName(SimpleHrUserAttr.department.getAttrName());
        connectorUserProfile.setAttrSchema(userAllProfile);
        return connectorUserProfile;
    }

    @Override
    public ConnectorOrg<String, Object> getRootExternalOrg(Connector connector) throws ThirdPartyAccountException {
        String rootCode = connector.getRootCode();
        if (StringUtils.isBlank(rootCode)) {
            throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), "rootCode is NUll");
        }
        List<ConnectorOrg<String, Object>> allExternalOrgs = getAllSubExternalOrgs(null, connector);
        for (ConnectorOrg<String, Object> org : allExternalOrgs) {
            //指定根节点
            if (rootCode.equals(org.getIdValue())) {
                return org;
            }
        }
        throw new ThirdPartyAccountException(THIRDPARTYACCOUNT, "当前指定根节点不存在");
    }

    @Override
    public List<ConnectorOrg<String, Object>> getAllSubExternalOrgs(ConnectorOrg parentExternalOrg, Connector connector) throws ThirdPartyAccountException {
        List<ConnectorOrg<String, Object>> ret = new ArrayList<>();
        ret.addAll(AccountUtil.to(getExternalDatas(connector, "org"), getExternalOrgFullProfile(connector)));

        if (parentExternalOrg != null) {
            List<ConnectorOrg<String, Object>> subExternalOrgs = new ArrayList<>();
            for (ConnectorOrg org : ret) {
                if (String.valueOf(org.getIdValue()).startsWith(parentExternalOrg.getIdValue().toString())) {
                    subExternalOrgs.add(org);
                }
            }
            ret.clear();
            ret.addAll(subExternalOrgs);
        }
        return ret;
    }

    private List<Map<String, Object>> getExternalDatas(Connector connector, String orgOrUser) throws ThirdPartyAccountException {
        RestApiResponse response = getRestApiResponse(connector, orgOrUser);
        Map<String, Object> resp = JsonUtil.str2Map(response.getBody().toString());
        return (List<Map<String, Object>>) resp.get("data");
    }

    @NotNull
    private RestApiResponse getRestApiResponse(Connector connector, String orgOrUser) throws ThirdPartyAccountException {
        logger.info("SimpleHr begin getExternalDatas ,orgOrUser: {}", orgOrUser);
        SimpleHrAccount simpleHrConfig = JsonUtil.str2Obj(connector.getConfig(), SimpleHrAccount.class);
        Map<String, String> header = new HashMap<>();
        header.put("access_token", simpleHrConfig.getAccessToken());
        Map<String, String> queryParams = new HashMap<>();
        queryParams.put("ts", simpleHrConfig.getTs());
        String api = orgApi;
        if (orgOrUser.equals("user")) {
            api = userApi;
        }
        RestApiResponse response;
        try {
            response = RestAPIUtil.modifyEntityForString(WebUrlUtil.getWebServerUrl(simpleHrConfig.getUrl(), api), "GET", null, header, queryParams, new HashMap());
            checkResponse(response, api);
        } catch (HttpStatusCodeException e) {
            throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), e.getMessage());
        }
        return response;
    }

    protected void checkResponse(RestApiResponse response, String api) {
        if (response.getHttpStatus() != 200) {
            logger.error("SimpleHr {} interface is status: {}", api, response.getHttpStatus());
            throw new ThirdPartyAccessTokenInvalidException(String.valueOf(response.getHttpStatus()), "无法访问HR服务，请检查参数配置，并重试");
        }
    }

    @Override
    public List<ConnectorOrg<String, Object>> getSubExternalOrgs(String parentOrgId, Connector connector) throws ThirdPartyAccountException {
        return null;
    }

    @Override
    public List<ConnectorOrg<String, Object>> getExternalOrgByIds(List<String> externalOrgIds, Connector connector) throws ThirdPartyAccountException {
        return null;
    }

    @Override
    public ConnectorOrg getExternalOrgById(String externalOrgId, Connector connector) throws ThirdPartyAccountException {
        return null;
    }

    @Override
    public boolean isSortedByOrgLevel() {
        return false;
    }

    @Override
    public List<ConnectorUser<String, Object>> getExternalUsersByOrg(ConnectorOrg externalOrg, Connector connector) throws ThirdPartyAccountException {
        return getConnectorUsers(connector);
    }

    @Override
    public List<ConnectorUser<String, Object>> getExternalUsers(List<String> externalUserIds, Connector connector) throws ThirdPartyAccountException {
        return getConnectorUsers(connector);
    }

    @NotNull
    private List<ConnectorUser<String, Object>> getConnectorUsers(Connector connector) throws ThirdPartyAccountException {
        List<ConnectorUser<String, Object>> ret = new ArrayList<>();
        ret.addAll(AccountUtil.to(getExternalDatas(connector, "user"), getExternalUserFullProfile(connector)));
        return ret;
    }

    @Override
    public Boolean listExternalUsersByLimit(Connector connector) {
        //接口现状没有分页，但又要一次性读取，所以返回true
        return true;
    }

    @Override
    public Boolean limitListExternalUsers(int page, int pageSize, Connector connector, List<ConnectorUser<String, Object>> output) throws ThirdPartyAccountException {
        output.addAll(getConnectorUsers(connector));
        if (connector.getRootCode() != null) {
            List<ConnectorUser<String, Object>> deptStartUsers = new ArrayList<>();
            for (ConnectorUser user : output) {
                if (String.valueOf(user.getDeptValue()).startsWith(connector.getRootCode())) {
                    deptStartUsers.add(user);
                }
            }
            output.clear();
            output.addAll(deptStartUsers);
        }
        return false;
    }

    @Override
    public ConnectorUser getExternalUserById(String externalAccountId, Connector connector) throws ThirdPartyAccountException {
        return null;
    }

    @Override
    public int login(Connector connector, String username, String password) {
        return 0;
    }

    @Override
    public ConnectorUser test(Connector connector) {
        ConnectorUser ccu = null;
        try {
            if (getConnectorUsers(connector).size() > 0) {
                ccu = getConnectorUsers(connector).get(0);
            }
            this.getRootExternalOrg(connector);
        } catch (ThirdPartyAccountException tae) {
            throw new ThirdPartyAccountException(CONNECTOR_INVALID, "指定上游根组织没有对应节点，请检查根节点是否存在或最新修改时间参数设定是否过短");
        } catch (ResourceAccessException rae) {
            throw new ThirdPartyAccountException(CONNECTOR_INVALID, "HR服务地址错误，请检查参数配置，并重试");
        }
        if (ccu == null) {
            throw new ThirdPartyAccountException(CONNECTOR_INVALID, "HR用户接口数据为空,请检查最新修改时间参数设定是否过短");
        }
        return ccu;
    }

    @Override
    public void closeConnection(Connector connector) {

    }

    @Override
    public String createExternalOrg(ConnectorOrg externalOrg, Connector connector) throws ThirdPartyAccountException {
        return null;
    }

    @Override
    public String modifyExternalOrg(String externalOrgId, ConnectorOrg externalOrg, Connector connector) throws ThirdPartyAccountException {
        return null;
    }

    @Override
    public void deleteExternalOrg(String externalOrgId, Connector connector) throws ThirdPartyAccountException {

    }

    @Override
    public void deleteExternalOrgs(List<String> externalOrgIds, Connector connector) throws ThirdPartyAccountException {

    }

    @Override
    public String createExternalUser(ConnectorUser externalUser, Connector connector) throws ThirdPartyAccountException {
        return null;
    }

    @Override
    public String modifyExternalUser(String externalUserId, ConnectorUser externalUser, Connector connector) throws ThirdPartyAccountException {
        return null;
    }

    @Override
    public void deleteExternalUser(String externalUserId, Connector connector) throws ThirdPartyAccountException {

    }

    @Override
    public void deleteExternalUsers(List<String> externalUserIds, Connector connector) throws ThirdPartyAccountException {

    }
}
