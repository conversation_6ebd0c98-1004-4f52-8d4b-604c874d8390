package com.cyberscraft.uep.account.client.provider;

import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountException;

/***
 * UEM平台及第三方平台租户ID转换类
 * @date 2021-10-31
 * <AUTHOR>
 ***/
public interface IThirdPartyTenantTransfer {

    /***
     *
     * @param accountType
     * @return
     * @throws ThirdPartyAccountException
     */
    boolean isSupported(String accountType) throws ThirdPartyAccountException;

    /****
     * UEM平台对应的租户ID转换成第三方平台租户ID
     * @param tenantId
     * @return
     */
    String tenant2ThridPartyTenant(String tenantId) throws ThirdPartyAccountException;

    /****
     * 第三方平台租户ID转换成UEM平台对应的租户ID
     * @param tenantId
     * @return
     */
    String thridPartyTenant2Tenant(String tenantId) throws ThirdPartyAccountException;

}
