package com.cyberscraft.uep.account.client.handler;

import com.cyberscraft.uep.account.client.constant.AccountConstant;
import com.cyberscraft.uep.account.client.constant.DingThirdUserAttr;
import com.cyberscraft.uep.account.client.constant.JitConnectType;
import com.cyberscraft.uep.account.client.domain.ConnectorUserProfile;
import com.cyberscraft.uep.account.client.provider.IJitConnectHandler;
import com.cyberscraft.uep.common.domain.DataTypeEnum;
import com.cyberscraft.uep.common.domain.ParamSchema;
import org.springframework.stereotype.Service;

/**
 * @description:钉钉第三方企业应用认证jit实现类
 * @author:liusen
 * @date:2024/4/7
 */
@Service
public class DingThirdJitConnectHandler implements IJitConnectHandler {

    private final ParamSchema userBasicAttrSchema;

    public DingThirdJitConnectHandler() {
        userBasicAttrSchema = new ParamSchema(AccountConstant.IDPUSER_PROFILE_ROOTNAME, DataTypeEnum.OBJECT, "用户profile")
                .addSubParam(DingThirdUserAttr.userid.ps())
                .addSubParam(DingThirdUserAttr.unionid.ps())
                .addSubParam(DingThirdUserAttr.avatar.ps())
                .addSubParam(DingThirdUserAttr.title.ps())
                .addSubParam(DingThirdUserAttr.name.ps())
                .addSubParam(DingThirdUserAttr.mobile.ps())
                .addSubParam(DingThirdUserAttr.email.ps())
                .addSubParam(DingThirdUserAttr.job_number.ps());
    }

    @Override
    public boolean isSupported(JitConnectType jitConnectType) {
        return JitConnectType.DINGTHIRD == jitConnectType;
    }

    @Override
    public ParamSchema getJitUserBasicAttrSchema(JitConnectType jitConnectType) {
        return userBasicAttrSchema;
    }

    @Override
    public ConnectorUserProfile getJitUserFullProfile(ParamSchema jitExtensionSchema, JitConnectType jitConnectType) {
        ParamSchema userAllProfile = buildJitUserFullSchema(jitExtensionSchema, jitConnectType);

        ConnectorUserProfile connectorUserProfile = new ConnectorUserProfile();
        connectorUserProfile.setIdName(DingThirdUserAttr.userid.getAttrName());
        connectorUserProfile.setAttrSchema(userAllProfile);

        return connectorUserProfile;
    }
}
