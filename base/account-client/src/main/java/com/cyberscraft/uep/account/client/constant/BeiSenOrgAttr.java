package com.cyberscraft.uep.account.client.constant;

import com.cyberscraft.uep.common.domain.DataTypeEnum;
import com.cyberscraft.uep.common.domain.MutableEnum;

import static com.cyberscraft.uep.account.client.constant.AccountConstant.APPORG_PROFILE_ROOTNAME;
import static com.cyberscraft.uep.account.client.constant.AccountConstant.IDPORG_PROFILE_ROOTNAME;

/**
 * @Author：xhquan
 * @Project：uep-services
 * @since：2023/8/28 16:14
 */
public enum BeiSenOrgAttr implements ExternalAttr {
    costCenterOId("costCenterOId", DataTypeEnum.NUMBER, "薪酬成本中心ID"),
    name("name", DataTypeEnum.STRING, "部门机构名称"),
    name_en_US("name_en_US", DataTypeEnum.STRING, "部门机构名称_英文"),
    name_zh_TW("name_zh_TW", DataTypeEnum.STRING, "部门机构名称_繁体"),
    shortName("shortName", DataTypeEnum.STRING, "部门机构简称"),
    code("code", DataTypeEnum.STRING, "组织单元编码"),
    oId("oId", DataTypeEnum.NUMBER, "组织单元OId"),
    level("level", DataTypeEnum.STRING, "组织层级实体对象"),
    status("status", DataTypeEnum.NUMBER, "状态"),
    establishDate("establishDate", DataTypeEnum.STRING, "设立日期"),
    startDate("startDate", DataTypeEnum.STRING, "生效日期"),
    stopDate("stopDate", DataTypeEnum.STRING, "失效日期"),
    changeDate("changeDate", DataTypeEnum.STRING, "变更日期"),
    pOIdOrgAdmin("pOIdOrgAdmin", DataTypeEnum.NUMBER, "行政维度上级组织OId"),
    pOIdOrgReserve2("pOIdOrgReserve2", DataTypeEnum.NUMBER, "业务维度上级组织OId"),
    pOIdOrgReserve3("pOIdOrgReserve3", DataTypeEnum.NUMBER, "产品维度组织OId"),
    isCurrentRecord("isCurrentRecord", DataTypeEnum.BOOLEAN, "是否当前生效"),
    personInCharge("personInCharge", DataTypeEnum.NUMBER, "部门负责人员工UserID"),
    hRBP("hRBP", DataTypeEnum.NUMBER, "HRBP员工UserID"),
    shopOwner("shopOwner", DataTypeEnum.NUMBER, "店长员工UserID"),
    administrativeAssistant("administrativeAssistant", DataTypeEnum.NUMBER, "行政助理,员工UserID"),
    personInChargeDeputy("personInChargeDeputy", DataTypeEnum.STRING, "负责人（副职）,多员工UserID集合"),
    businessModifiedBy("businessModifiedBy", DataTypeEnum.NUMBER, "业务修改人"),
    businessModifiedTime("businessModifiedTime", DataTypeEnum.STRING, "业务修改时间"),
    legalMan("legalMan", DataTypeEnum.STRING, "法人代表"),
    address("address", DataTypeEnum.STRING, "地址"),
    fax("fax", DataTypeEnum.STRING, "Fax"),
    postcode("postcode", DataTypeEnum.STRING, "邮编"),
    phone("phone", DataTypeEnum.STRING, "电话"),
    url("url", DataTypeEnum.STRING, "网址"),
    description("description", DataTypeEnum.STRING, "简介"),
    number("number", DataTypeEnum.STRING, "文号"),
    broadType("broadType", DataTypeEnum.STRING, "组织大类字典键"),
    economicType("economicType", DataTypeEnum.STRING, "经济类型字典键"),
    industry("industry", DataTypeEnum.STRING, "所属行业字典键"),
    place("place", DataTypeEnum.STRING, "所在地点字典键"),

    orderAdmin("orderAdmin", DataTypeEnum.NUMBER, "行政维度顺序号"),
    orderReserve2("orderReserve2", DataTypeEnum.NUMBER, "业务维度顺序号"),
    orderReserve3("orderReserve3", DataTypeEnum.NUMBER, "产品维度顺序号"),
    comment("comment", DataTypeEnum.STRING, "备注"),
    oIdOrganizationType("oIdOrganizationType", DataTypeEnum.STRING, "组织类型实体对象（实体编码：TenantBase.OrganizationType）业务数据GUID"),
    pOIdOrgAdmin_TreePath("pOIdOrgAdmin_TreePath", DataTypeEnum.STRING, "行政维度_路径"),
    pOIdOrgAdmin_TreeLevel("pOIdOrgAdmin_TreeLevel", DataTypeEnum.NUMBER, "行政维度_层级"),
    pOIdOrgReserve2_TreePath("pOIdOrgReserve2_TreePath", DataTypeEnum.STRING, "业务维度_路径"),
    pOIdOrgReserve2_TreeLevel("pOIdOrgReserve2_TreeLevel", DataTypeEnum.NUMBER, "业务维度_层级"),
    firstLevelOrganization("firstLevelOrganization", DataTypeEnum.NUMBER, "一级组织OId"),
    secondLevelOrganization("secondLevelOrganization", DataTypeEnum.NUMBER, "二级组织OId"),
    thirdLevelOrganization("thirdLevelOrganization", DataTypeEnum.NUMBER, "三级组织OId"),
    fourthLevelOrganization("fourthLevelOrganization", DataTypeEnum.NUMBER, "四级组织OId"),
    fifthLevelOrganization("fifthLevelOrganization", DataTypeEnum.NUMBER, "五级组织OId"),
    sixthLevelOrganization("sixthLevelOrganization", DataTypeEnum.NUMBER, "六级组织OId"),
    seventhLevelOrganization("seventhLevelOrganization", DataTypeEnum.NUMBER, "七级组织OId"),
    eighthLevelOrganization("eighthLevelOrganization", DataTypeEnum.NUMBER, "八级组织OId"),
    ninthLevelOrganization("ninthLevelOrganization", DataTypeEnum.NUMBER, "九级组织OId"),
    tenthLevelOrganization("tenthLevelOrganization", DataTypeEnum.NUMBER, "十级组织OId"),
    orderCode("orderCode", DataTypeEnum.NUMBER, "排序编码"),
    pOIdOrgAdminNameTreePath("pOIdOrgAdminNameTreePath", DataTypeEnum.STRING, "组织全称"),
    isVirtualOrg("isVirtualOrg", DataTypeEnum.BOOLEAN, "是否虚拟组织"),
    leaderWithSpecificDuty("leaderWithSpecificDuty", DataTypeEnum.NUMBER, "分管领导员工UserID"),
    objectId("objectId", DataTypeEnum.STRING, "业务对象实体主键GUID"),
    ;

    BeiSenOrgAttr(String attrName, DataTypeEnum dataType, String displayName) {
        this(attrName, dataType, displayName, displayName, false);
    }

    BeiSenOrgAttr(String attrName, DataTypeEnum dataType, String displayName, MutableEnum mutability) {
        this(attrName, dataType, displayName, displayName, false, mutability);
    }

    BeiSenOrgAttr(String attrName, DataTypeEnum dataType, String displayName, String description, Boolean multiValued) {
        this(attrName, dataType, displayName, description, multiValued, MutableEnum.readWrite);
    }

    BeiSenOrgAttr(String attrName, DataTypeEnum dataType, String displayName, String description, Boolean multiValued, MutableEnum mutability) {
        this.attrName = attrName;
        this.dataType = dataType;
        this.displayName = displayName;
        this.desc = description;
        this.multiValued = multiValued;
        this.mutability = mutability;
    }

    private String attrName;

    private DataTypeEnum dataType;

    private String displayName;

    private String desc;

    private Boolean multiValued;

    private MutableEnum mutability;

    @Override
    public String getAttrName() {
        return this.attrName;
    }

    @Override
    public DataTypeEnum getDataType() {
        return this.dataType;
    }

    @Override
    public String getDisplayName() {
        return this.displayName;
    }

    @Override
    public String getDescription() {
        return desc;
    }

    @Override
    public Boolean getMultiValued() {
        return this.multiValued;
    }

    @Override
    public String getAppNamePath() {
        return APPORG_PROFILE_ROOTNAME + "." + getAttrName();
    }

    @Override
    public String getIdpNamePath() {
        return IDPORG_PROFILE_ROOTNAME + "." + getAttrName();
    }
}
