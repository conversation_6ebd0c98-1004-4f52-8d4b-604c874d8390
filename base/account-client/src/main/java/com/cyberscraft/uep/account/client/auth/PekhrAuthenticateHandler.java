package com.cyberscraft.uep.account.client.auth;

import com.cyberscraft.uep.common.bean.RestApiResponse;
import com.cyberscraft.uep.common.domain.auth.*;
import com.cyberscraft.uep.common.util.*;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Author：xhquan
 * @Project：uep-services
 * @since：2023/8/9 15:28
 */
@Service
public class PekhrAuthenticateHandler implements IAuthenticateHandler {

    private Map<String, String> cacheMap = new ConcurrentHashMap<>();

    private final String TOKEN = "token";
    private final String DATA = "data";

    @Override
    public boolean isSupported(String type) {
        return AuthProtocol.PEKHR.name().equals(type);
    }

    @Override
    public AuthorizeCredential auth(Account account, String cacheKey, Boolean refresh) {
        AuthorizeCredential authorizeCredential = new AuthorizeCredential();

        String fullCacheKey = IAuthenticateHandler.CACHE_KEY_PREFIX + cacheKey;
        Map<String, String> queryParam = new HashMap<>();
        if (!refresh) {
            String cacheValue = cacheMap.get(fullCacheKey);
            if (cacheValue != null) {
                queryParam.put(TOKEN, cacheValue);
                authorizeCredential.setQuery(queryParam);
                return authorizeCredential;
            }
        }

        PekHrParam rljParam = (PekHrParam) account;
        String endpoint = rljParam.getEndpoint();
        long timestamp = System.currentTimeMillis();

        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(rljParam.getCropId());
        stringBuilder.append(rljParam.getAccessKey());
        stringBuilder.append(rljParam.getAccessSecret());
        stringBuilder.append(timestamp);
        String signature = MD5Util.md5(stringBuilder.toString());

        Map<String, Object> query = new HashMap<>();
        query.put("corpId", rljParam.getCropId());
        query.put("signature", signature);
        query.put("timestamp", timestamp);

        String proxyIp = account.getProxyIp();
        RestApiResponse apiResponse = RestAPIUtil.getForEntityString(proxyIp, endpoint, null, query, new HashMap<>());
        Map map = null;
        if (apiResponse.getBody() instanceof String) {
            map = JsonUtil.str2Map((String) apiResponse.getBody());
        } else {
            map = (Map) apiResponse.getBody();
        }

        if (map.size() > 0) {
            if (map.get(DATA) != null) {
                String data = (String) map.get(DATA);
                queryParam.put(TOKEN, data);
            }
        }

        authorizeCredential.setQuery(queryParam);
        return authorizeCredential;
    }

    @Override
    public void evictCache(String cacheKey) {
        String fullCacheKey = IAuthenticateHandler.CACHE_KEY_PREFIX + cacheKey;
        cacheMap.remove(fullCacheKey);
    }

    @Override
    public Account getAccount(AccountParam accountParam, Map<String, Object> queryParam, Map<String, Object> headerParam) {
        return MappingParser.schema2Obj(new HashMap<>(), accountParam.getAccount(), PekHrParam.class);
    }
}
