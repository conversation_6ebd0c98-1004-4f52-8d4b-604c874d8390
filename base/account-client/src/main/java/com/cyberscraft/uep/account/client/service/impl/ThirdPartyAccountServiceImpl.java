package com.cyberscraft.uep.account.client.service.impl;

import com.cyberscraft.uep.account.client.domain.Connector;
import com.cyberscraft.uep.account.client.domain.ThirdPartyAccount;
import com.cyberscraft.uep.account.client.domain.ThirdPartyGroup;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountException;
import com.cyberscraft.uep.account.client.provider.IThirdPartyAccountHandler;
import com.cyberscraft.uep.account.client.provider.IThirdPartyAccountManageHandler;
import com.cyberscraft.uep.account.client.provider.IThirdPartyTenantTransfer;
import com.cyberscraft.uep.account.client.provider.noauth.NoAuthThirdPartyAccountHandler;
import com.cyberscraft.uep.account.client.provider.noauth.NoAuthThirdPartyAccountManageHandler;
import com.cyberscraft.uep.account.client.service.IThirdPartyAccountService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/***
 *
 * @date 2021-10-31
 * <AUTHOR>
 ***/
@Service
public class ThirdPartyAccountServiceImpl implements IThirdPartyAccountService {

    @Autowired(required = false)
    private List<IThirdPartyAccountHandler> accountHandlers = new ArrayList<>();

    private ConcurrentHashMap<String, IThirdPartyAccountHandler> ACCOUNT_TYPE_HANDLER_MAP = new ConcurrentHashMap<>();

    @Autowired(required = false)
    private List<IThirdPartyTenantTransfer> tenantTransfers = new ArrayList<>();

    private ConcurrentHashMap<String, IThirdPartyTenantTransfer> ACCOUNT_TYPE_TENANTTRANSFER_MAP = new ConcurrentHashMap<>();

    /**
     *
     */
    private ConcurrentHashMap<String, IThirdPartyAccountManageHandler> ACCOUNT_TYPE_MANAGE_MAP = new ConcurrentHashMap<>();

    /***
     *
     */
    private List<IThirdPartyAccountManageHandler> accountManageHandlers;

    /***
     *
     */
    private final static Logger LOG = LoggerFactory.getLogger(ThirdPartyAccountServiceImpl.class);

    /***
     *
     */
    private final IThirdPartyAccountHandler EMPTY_ACCOUNT_HANDLER = new NoAuthThirdPartyAccountHandler();

    /***
     *
     */
    private final IThirdPartyAccountManageHandler EMPTY_ACCOUNT_MANAGE_HANDLER = new NoAuthThirdPartyAccountManageHandler();


    public List<IThirdPartyAccountManageHandler> getAccountManageHandlers() {
        return accountManageHandlers;
    }

    @Autowired(required = false)
    public void setAccountManageHandlers(List<IThirdPartyAccountManageHandler> accountManageHandlers) {
        this.accountManageHandlers = accountManageHandlers;
        this.initAccountManageHandlers();
    }

    private synchronized void initAccountManageHandlers() {
        this.ACCOUNT_TYPE_MANAGE_MAP.clear();
        if (this.accountManageHandlers != null && this.accountManageHandlers.size() > 0) {
            for (IThirdPartyAccountManageHandler handler : this.accountManageHandlers) {
                String accountType = handler.getSupportedAccountType();
                if (StringUtils.isNotBlank(accountType)) {
                    ACCOUNT_TYPE_MANAGE_MAP.putIfAbsent(accountType, handler);
                }
            }
        }
    }

//    @Override
//    public ThirdPartyGroup getRootGroup(String tenantId, String accountType) throws ThirdPartyAccountException {
//        IThirdPartyAccountHandler handler = findAcountHandler(accountType);
//        if (handler == null) {
//            LOG.warn("未找到{}对应的账户处理器", accountType);
//            return null;
//        }
//        return handler.getRootGroup(tenantId);
//    }
//
//    @Override
//    public ThirdPartyGroup getGroup(String tenantId, String groupCode, String accountType) throws ThirdPartyAccountException {
//        return getGroupByCode(tenantId, accountType, groupCode, null);
//    }

    @Override
    public List<ThirdPartyGroup> getSubGroups(String tenantId, String accountType, String parentGroup, Connector connector, int pageIndex, int pageSize) throws ThirdPartyAccountException {
        IThirdPartyAccountHandler handler = findAcountHandler(accountType);
        if (handler == null) {
            LOG.warn("未找到{}对应的账户处理器", accountType);
            return null;
        }
        return handler.getSubGroups(tenantId, parentGroup, connector, pageIndex, pageSize);
    }

    @Override
    public List<ThirdPartyGroup> getSubGroups(String tenantId, String accountType, String parentGroup, Connector connector) throws ThirdPartyAccountException {
        IThirdPartyAccountHandler handler = findAcountHandler(accountType);
        if (handler == null) {
            LOG.warn("未找到{}对应的账户处理器", accountType);
            return null;
        }
        return handler.getSubGroups(tenantId, parentGroup, connector);
    }

    @Override
    public List<ThirdPartyAccount> getAccountsByGroup(String tenantId, String accountType, String groupCode, Connector connector, int pageIndex, int pageSize) throws ThirdPartyAccountException {
        IThirdPartyAccountHandler handler = findAcountHandler(accountType);
        if (handler == null) {
            LOG.warn("未找到{}对应的账户处理器", accountType);
            return null;
        }
        return handler.getAccountsByGroup(tenantId, groupCode, connector, pageIndex, pageSize);
    }

//    @Override
//    public List<ThirdPartyAccount> getAccountsByUserIds(String tenantId, String accountType, List<String> userIds) throws ThirdPartyAccountException {
//        return getAccountsByUserIds(tenantId, accountType, null);
//    }

    @Override
    public List<Map<String, Object>> getUserProfileByUserIds(String tenantId, String accountType, List<String> thirdUserIds, Connector connector) throws ThirdPartyAccountException {
        IThirdPartyAccountHandler handler = findAcountHandler(accountType);
        if (handler == null) {
            LOG.warn("未找到{}对应的账户处理器", accountType);
            return null;
        }
        return handler.getTenantUserProfileByUserIds(tenantId, thirdUserIds, connector);
    }

    @Override
    public String tenant2ThridPartyTenant(String tenantId, String accountType) throws ThirdPartyAccountException {
        IThirdPartyTenantTransfer handler = findTenantTransferHandler(accountType);
        if (handler == null) {
            LOG.warn("未找到{}对应的账户处理器", accountType);
            return null;
        }
        return handler.tenant2ThridPartyTenant(tenantId);
    }

    @Override
    public String thridPartyTenant2Tenant(String tenantId, String accountType) throws ThirdPartyAccountException {
        IThirdPartyTenantTransfer handler = findTenantTransferHandler(accountType);
        if (handler == null) {
            LOG.warn("未找到{}对应的账户处理器", accountType);
            return null;
        }
        return handler.thridPartyTenant2Tenant(tenantId);
    }

    @Override
    public List<ThirdPartyAccount> getAccountsByMobile(String tenantId, String accountType, List<String> userIds, Connector connector) throws ThirdPartyAccountException {
        IThirdPartyAccountHandler handler = findAcountHandler(accountType);
        if (handler == null) {
            LOG.warn("未找到{}对应的账户处理器", accountType);
            return null;
        }
        return handler.getAccountsByMobile(tenantId, userIds, connector);
    }

    @Override
    public List<ThirdPartyAccount> getAccountsByUserIds(String tenantId, String accountType, List<String> userIds, Connector connector) throws ThirdPartyAccountException {
        IThirdPartyAccountHandler handler = findAcountHandler(accountType);
        if (handler == null) {
            LOG.warn("未找到{}对应的账户处理器", accountType);
            return null;
        }
        return handler.getAccountsByUserIds(tenantId, userIds, connector);
    }

    @Override
    public ThirdPartyAccount getAccountByUserId(String tenantId, String accountType, String userId, Connector connector) throws ThirdPartyAccountException {
        List<ThirdPartyAccount> list = getAccountsByUserIds(tenantId, accountType, Arrays.asList(userId), connector);
        return list != null && list.size() > 0 ? list.get(0) : null;
    }

    @Override
    public ThirdPartyGroup getRootGroup(String tenantId, String accountType, Connector connector) throws ThirdPartyAccountException {
        IThirdPartyAccountHandler handler = findAcountHandler(accountType);
        if (handler == null) {
            LOG.warn("未找到{}对应的账户处理器", accountType);
            return null;
        }
        return handler.getRootGroup(tenantId, connector);
    }

    @Override
    public ThirdPartyGroup getGroupByCode(String tenantId, String accountType, String code, Connector connector) throws ThirdPartyAccountException {
        IThirdPartyAccountHandler handler = findAcountHandler(accountType);
        if (handler == null) {
            LOG.warn("未找到{}对应的账户处理器", accountType);
            return null;
        }
        return handler.getGroupByCode(tenantId, code, connector);
    }

    @Override
    public List<ThirdPartyGroup> getGroupsByCodes(String tenantId, String accountType, List<String> codes, Connector connector) throws ThirdPartyAccountException {
        IThirdPartyAccountHandler handler = findAcountHandler(accountType);
        if (handler == null) {
            LOG.warn("未找到{}对应的账户处理器", accountType);
            return null;
        }
        return handler.getGroupsByCodes(tenantId, codes, connector);
    }

    @Override
    public List<ThirdPartyGroup> getAllGroups(String tenantId, String accountType, Connector connector) throws ThirdPartyAccountException {
        IThirdPartyAccountHandler handler = findAcountHandler(accountType);
        if (handler == null) {
            LOG.warn("未找到{}对应的账户处理器", accountType);
            return null;
        }
        return handler.getAllGroups(tenantId, connector);
    }

    @Override
    public List<ThirdPartyAccount> getAccountsByGroup(String tenantId, String accountType, String groupCode, Connector connector) throws ThirdPartyAccountException {
        IThirdPartyAccountHandler handler = findAcountHandler(accountType);
        if (handler == null) {
            LOG.warn("未找到{}对应的账户处理器", accountType);
            return null;
        }
        return handler.getAccountsByGroup(tenantId, groupCode, connector);
    }

    @Override
    public String createGroup(String tenantId, String accountType, ThirdPartyGroup group, Connector connector) throws ThirdPartyAccountException {
        IThirdPartyAccountManageHandler handler = findAcountManageHandler(accountType);
        if (handler == null) {
            LOG.warn("未找到{}对应的账户处理器", accountType);
            return null;
        }
        return handler.createGroup(tenantId, group, connector);
    }

    @Override
    public String modifyGroup(String tenantId, String accountType, ThirdPartyGroup group, Connector connector) throws ThirdPartyAccountException {
        IThirdPartyAccountManageHandler handler = findAcountManageHandler(accountType);
        if (handler == null) {
            LOG.warn("未找到{}对应的账户处理器", accountType);
            return null;
        }
        return handler.modifyGroup(tenantId, group, connector);
    }

    @Override
    public void removeGroup(String tenantId, String accountType, List<String> codes, Connector connector) throws ThirdPartyAccountException {
        IThirdPartyAccountManageHandler handler = findAcountManageHandler(accountType);
        if (handler == null) {
            LOG.warn("未找到{}对应的账户处理器", accountType);
            return;
        }
        handler.removeGroup(tenantId, codes, connector);
    }

    @Override
    public String createAccount(String tenantId, String accountType, ThirdPartyAccount account, Connector connector) throws ThirdPartyAccountException {
        IThirdPartyAccountManageHandler handler = findAcountManageHandler(accountType);
        if (handler == null) {
            LOG.warn("未找到{}对应的账户处理器", accountType);
            return null;
        }
        return handler.createAccount(tenantId, account, connector);
    }

    @Override
    public String modifyAccount(String tenantId, String accountType, ThirdPartyAccount account, Connector connector) throws ThirdPartyAccountException {
        IThirdPartyAccountManageHandler handler = findAcountManageHandler(accountType);
        if (handler == null) {
            LOG.warn("未找到{}对应的账户处理器", accountType);
            return null;
        }
        return handler.modifyAccount(tenantId, account, connector);
    }

    @Override
    public void removeAccount(String tenantId, String accountType, List<String> accountIds, Connector connector) throws ThirdPartyAccountException {
        IThirdPartyAccountManageHandler handler = findAcountManageHandler(accountType);
        if (handler == null) {
            LOG.warn("未找到{}对应的账户处理器", accountType);
            return;
        }
        handler.removeAccount(tenantId, accountIds, connector);
    }

    /****
     *
     * @param accountType
     * @return
     */
    private IThirdPartyAccountManageHandler findAcountManageHandler(String accountType) {
        IThirdPartyAccountManageHandler ret = ACCOUNT_TYPE_MANAGE_MAP.getOrDefault(accountType, EMPTY_ACCOUNT_MANAGE_HANDLER);
        if (ret != null) {
            return ret;
        }
        return EMPTY_ACCOUNT_MANAGE_HANDLER;
    }


    /****
     *
     * @param accountType
     * @return
     */
    private IThirdPartyAccountHandler findAcountHandler(String accountType) {
        IThirdPartyAccountHandler ret = ACCOUNT_TYPE_HANDLER_MAP.get(accountType);
        if (ret != null) {
            return ret;
        }
        synchronized (accountType) {
            ret = ACCOUNT_TYPE_HANDLER_MAP.get(accountType);
            if (ret != null) {
                return ret;
            }
            for (IThirdPartyAccountHandler handler : accountHandlers) {
                if (handler.isSupported(accountType)) {
                    ret = handler;
                    break;
                }
            }
            if (ret == null) {
                ret = EMPTY_ACCOUNT_HANDLER;
            }
            ACCOUNT_TYPE_HANDLER_MAP.put(accountType, ret);
        }
        return ret;
    }


    /****
     *
     * @param accountType
     * @return
     */
    private IThirdPartyTenantTransfer findTenantTransferHandler(String accountType) {
        IThirdPartyTenantTransfer ret = ACCOUNT_TYPE_TENANTTRANSFER_MAP.get(accountType);
        if (ret != null) {
            return ret;
        }
        synchronized (accountType) {
            ret = ACCOUNT_TYPE_TENANTTRANSFER_MAP.get(accountType);
            if (ret != null) {
                return ret;
            }
            for (IThirdPartyTenantTransfer handler : tenantTransfers) {
                if (handler.isSupported(accountType)) {
                    ret = handler;
                    break;
                }
            }
            if (ret != null) {
                ACCOUNT_TYPE_TENANTTRANSFER_MAP.put(accountType, ret);
            }
        }
        return ret;
    }
}