package com.cyberscraft.uep.account.client.exception;

import com.cyberscraft.uep.common.exception.errorcode.MultiLanguageExceptionCodes;

/***
 *
 * @date 2021/7/13
 * <AUTHOR>
 ***/
public class ThirdPartyAccountNotExistException extends RuntimeException {

    private String code;

    private MultiLanguageExceptionCodes  multiLanguageExceptionCodes;
    private String  appendMsg;

    public ThirdPartyAccountNotExistException(MultiLanguageExceptionCodes multiLanguageExceptionCodes) {
        super(multiLanguageExceptionCodes.getDesc());
        this.code = ThirdPartyAccountErrorType.ACCOUNT_NOT_EXIST.getCode() + multiLanguageExceptionCodes.getCode();
        this.multiLanguageExceptionCodes = multiLanguageExceptionCodes;
    }

    public ThirdPartyAccountNotExistException(MultiLanguageExceptionCodes multiLanguageExceptionCodes, String appendMsg) {
        super(multiLanguageExceptionCodes.getDesc());
        this.code = ThirdPartyAccountErrorType.ACCOUNT_NOT_EXIST.getCode() + multiLanguageExceptionCodes.getCode();
        this.multiLanguageExceptionCodes = multiLanguageExceptionCodes;
        this.appendMsg = appendMsg;
    }

    public ThirdPartyAccountNotExistException(String message) {
        super(message);
        this.code = ThirdPartyAccountErrorType.ACCOUNT_NOT_EXIST.getCode();
    }

    public ThirdPartyAccountNotExistException(String code, String message) {
        super(message);
        this.code = code;
    }

    /***
     *
     * @param code
     * @param message
     * @param e
     */
    public ThirdPartyAccountNotExistException(String code, String message, Throwable e) {
        super(message, e);
        this.code = code;
    }


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public MultiLanguageExceptionCodes getMultiLanguageExceptionCodes() {
        return multiLanguageExceptionCodes;
    }

    public void setMultiLanguageExceptionCodes(MultiLanguageExceptionCodes multiLanguageExceptionCodes) {
        this.multiLanguageExceptionCodes = multiLanguageExceptionCodes;
    }

    public String getAppendMsg() {
        return appendMsg;
    }

    public void setAppendMsg(String appendMsg) {
        this.appendMsg = appendMsg;
    }
}
