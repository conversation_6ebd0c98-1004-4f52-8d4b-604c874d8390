package com.cyberscraft.uep.account.client.domain;

import com.cyberscraft.uep.account.client.constant.AccountConstant;
import com.cyberscraft.uep.common.domain.ParamSchema;
import com.cyberscraft.uep.common.util.MappingParser;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/11 16:43
 */
public class ConnectorRoleProfile {

    /**
     * 外部角色id属性名称
     */
    private String idName;

    /**
     * groupIdName 属性名称，可以为空
     */
    private String groupIdName;

    /**
     * groupNameName 角色组名称，可以为空
     */
    private String groupNameName;

    /**
     * 外部角色/角色组的名称属性名称
     */
    private String nameName;

    /**
     * 外部角色/角色组的属性schema可以为null
     */
    private ParamSchema attrSchema;

    public String getRootName() {
        if (attrSchema != null) {
            return attrSchema.getName();
        } else {
            return AccountConstant.APPUSER_PROFILE_ROOTNAME;
        }
    }

    public ParamSchema getAttr(String attrPath) {
        ParamSchema paramSchema = MappingParser.getSubParamSchema(attrSchema, attrPath);
        return paramSchema;
    }

    public String getIdName() {
        return idName;
    }

    public void setIdName(String idName) {
        this.idName = idName;
    }


    public String getGroupIdName() {
        return groupIdName;
    }

    public void setGroupIdName(String groupIdName) {
        this.groupIdName = groupIdName;
    }

    public String getGroupNameName() {
        return groupNameName;
    }

    public void setGroupNameName(String groupNameName) {
        this.groupNameName = groupNameName;
    }

    public String getNameName() {
        return nameName;
    }

    public void setNameName(String nameName) {
        this.nameName = nameName;
    }

    public ParamSchema getAttrSchema() {
        return attrSchema;
    }

    public void setAttrSchema(ParamSchema attrSchema) {
        this.attrSchema = attrSchema;
    }
}
