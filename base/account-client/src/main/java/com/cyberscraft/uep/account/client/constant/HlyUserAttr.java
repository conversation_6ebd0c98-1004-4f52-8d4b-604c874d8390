package com.cyberscraft.uep.account.client.constant;

import com.cyberscraft.uep.common.domain.DataTypeEnum;
import com.cyberscraft.uep.common.domain.MutableEnum;

import static com.cyberscraft.uep.account.client.constant.AccountConstant.APPUSER_PROFILE_ROOTNAME;
import static com.cyberscraft.uep.account.client.constant.AccountConstant.IDPUSER_PROFILE_ROOTNAME;

/**
 * 汇联易用户参数
 *
 * <AUTHOR>
 * @date 2024年01月02日
 */
public enum HlyUserAttr implements ExternalAttr {

    custDeptNumber("custDeptNumber", DataTypeEnum.STRING, "部门编码"),
    fullName("fullName", DataTypeEnum.STRING, "姓名"),
    employeeID("employeeID", DataTypeEnum.STRING, "员工工号，租户内不能重复"),
    companyOID("companyOID", DataTypeEnum.STRING, "公司OID，集团登陆账户必填,公司登陆账户可不填,必填时，companyOID和companyCode二选一即可，都填时，以companyOID为准"),
    companyCode("companyCode", DataTypeEnum.STRING, "公司编码，集团登陆账户必填,公司登陆账户可不填,必填时，companyOID和companyCode二选一即可，都填时，以companyOID为准"),
    email("email", DataTypeEnum.STRING, "邮箱，长度:1到255个字符,内部员工和外部员工租户内不能重复"),
    entryTime("entryTime", DataTypeEnum.STRING, "入职时间，格式:yyyy-MM-dd"),
    mobile("mobile", DataTypeEnum.STRING, "手机号，内部员工和外部员工租户内不能重复"),
    countryCode("countryCode", DataTypeEnum.STRING, "国家编码，不填默认为中国，不同国家手机号码长度不同"),
    password("password", DataTypeEnum.STRING, "密码,密码为空，则默认密码为手机号码，如果手机号码也为空，则密码为工号(不足6位，工号后补0)"),
    title("title", DataTypeEnum.STRING, "职位,不传默认创建职位为\"员工\"的岗位"),
    activated("activated", DataTypeEnum.BOOLEAN, "是否激活,true表示创建后自动激活，false表示创建后需要员工自己手动激活"),
    birthday("birthday", DataTypeEnum.STRING, "生日,格式:yyyy-MM-dd"),
    genderCode("genderCode", DataTypeEnum.STRING, "性别编码,0--男;1--女;2--未知"),
    employeeTypeCode("employeeTypeCode", DataTypeEnum.STRING, "人员类型编码，如果需要人员组按照人员类型匹配，则需要传人员类型编码"),
    dutyCode("dutyCode", DataTypeEnum.STRING, "职务编码，如果需要人员组按照职务匹配，则需要传职务编码"),
    rankCode("rankCode", DataTypeEnum.STRING, "级别编码，如果需要用级别值列表做差标管控和人员组按照级别匹配，则需要传级别编码"),
    directManagerOID("directManagerOID", DataTypeEnum.STRING, "主岗直属领导OID"),
    directManagerEmpoyeeId("directManagerEmpoyeeId", DataTypeEnum.STRING, "主岗直属领导工号"),
    language("language", DataTypeEnum.STRING, "语言编码,zh_CN-中文简体,zh_TW-中文繁体,en-英语,ms-马来语,ja-日语,vi_VN-越南语,es_ES-西班牙,pt_PT-葡萄牙,it_IT-意大利语,ko_KR-韩语"),
    coverModeForUserJobs("coverModeForUserJobs", DataTypeEnum.BOOLEAN, "多岗是否为覆盖模式，默认为false,设置为true时，userJobsDtos里面的isDeleted参数不能为true"),
    approvalLevel("approvalLevel", DataTypeEnum.STRING, "审批层级，不超过8位的正整数"),
    mutiJobDirectManagerMode("mutiJobDirectManagerMode", DataTypeEnum.BOOLEAN, "是否按多岗更新主岗上级岗位编码，默认为false"),
    // 银行卡
    contactBankAccountDTOs("contactBankAccountDTOs", DataTypeEnum.OBJECT, "银行账户信息"),
    // 证件
    contactCardDTOs("contactCardDTOs", DataTypeEnum.STRING, "证件集合"),
    // 扩展字段
    customFormValues("customFormValues", DataTypeEnum.OBJECT, "用户属性扩展字段"),
    // 用户携程供应商实体
    subAccountName("contactCtrip.subAccountName", DataTypeEnum.STRING, "用户携程供应商实体，子账户，值列表项中无论是否存在，都保存或者更新"),
    subAccountCode	("contactCtrip.subAccountCode", DataTypeEnum.STRING, "用户携程供应商实体，子账户编码，需要校验是否存在于值列表项中，不存在报错，存在保存或者更新"),
    confirmUserOID	("contactCtrip.confirmUserOID", DataTypeEnum.STRING, "用户携程供应商实体，授权人OID"),
    confirmUserEmployeeID	("contactCtrip.confirmUserEmployeeID", DataTypeEnum.STRING, "用户携程供应商实体，授权人工号"),
    confirmCCUserOID	("contactCtrip.confirmCCUserOID", DataTypeEnum.STRING, "用户携程供应商实体，抄送授权人"),
    confirmCCUserEmployeeID	("contactCtrip.confirmCCUserEmployeeID", DataTypeEnum.STRING, "用户携程供应商实体，抄送授权人工号"),
    confirm2UserOID	("contactCtrip.confirm2UserOID", DataTypeEnum.STRING, "用户携程供应商实体，二次授权人"),
    confirm2UserEmployeeID	("contactCtrip.confirm2UserEmployeeID", DataTypeEnum.STRING, "用户携程供应商实体，二次授权人工号"),
    confirm2CCUserOID	("contactCtrip.confirm2CCUserOID", DataTypeEnum.STRING, "用户携程供应商实体，抄送二次授权人"),
    confirm2CCUserEmployeeID	("contactCtrip.confirm2CCUserEmployeeID", DataTypeEnum.STRING, "用户携程供应商实体，抄送二次授权人工号"),
    rescheduledAuthUserOID	("contactCtrip.rescheduledAuthUserOID", DataTypeEnum.STRING, "用户携程供应商实体，改签授权人OID"),
    rescheduledAuthUserEmployeeID	("contactCtrip.rescheduledAuthUserEmployeeID", DataTypeEnum.STRING, "用户携程供应商实体，改签授权人工号"),
    confirmPassword	("contactCtrip.confirmPassword", DataTypeEnum.STRING, "用户携程供应商实体，携程授权码"),
    helpOthersBookCode("contactCtrip.helpOthersBookCode", DataTypeEnum.STRING, "用户携程供应商实体，代订类型,B:可以;C:不可以"),
    bookTheScopeCode("contactCtrip.bookTheScopeCode", DataTypeEnum.STRING, "用户携程供应商实体，代订范围,C:整个公司;F:员工所在携程主账户;S:员工所在携程子账户;O:指定具体代订名单"),
    agencyEmployeeIds("contactCtrip.agencyEmployeeIds", DataTypeEnum.OBJECT, "用户携程供应商实体，代订员工集合,bookTheScopeCode为O时生效"),
    positionShield("contactCtrip.positionShield", DataTypeEnum.STRING, "用户携程供应商实体，标签仓位屏蔽,Y:屏蔽;N:不屏蔽;默认不屏蔽"),
    changeTicketAuthType("contactCtrip.changeTicketAuthType", DataTypeEnum.STRING, "用户携程供应商实体，改签授权类型,Needless:不需要;CompanyAccountOnly:仅公司账户;PersonalPayment:仅个人支付;Both:混付（公司账户+个人支付）;FlightAuthNAlterAuto:改签自动授权"),
    changeTicketNeedAuth("contactCtrip.changeTicketNeedAuth", DataTypeEnum.STRING, "用户携程供应商实体，国内改签免授权金额控制,T:需要,F:不需要"),
    changeTicketAuthAmount("contactCtrip.changeTicketAuthAmount", DataTypeEnum.STRING, "用户携程供应商实体，国内改签免授权金额,国内改签免授权金额控制为T时必填"),
    // 空港嘉华实体
    kgSecondAuthEmployeeID("supplierKgjhDTO.secondAuthEmployeeID", DataTypeEnum.STRING, "空港嘉华实体，二次授权人工号"),
    kgenabled("supplierKgjhDTO.enabled", DataTypeEnum.BOOLEAN, "空港嘉华实体，是否启用,默认为true,启用"),
    // 通用二次授权人集合
    supplierDTOs("supplierDTOs", DataTypeEnum.OBJECT, "通用二次授权人集合"),
    // 用户岗位列表
    userJobsDtos("userJobsDtos", DataTypeEnum.OBJECT, "用户岗位列表"),
    ;

    private String attrName;

    private DataTypeEnum dataType;

    private String displayName;

    private String description;

    private Boolean multiValued;

    private MutableEnum mutability;

    HlyUserAttr(String attrName, DataTypeEnum dataType, String displayName) {
        this(attrName, dataType, displayName, displayName, false, MutableEnum.readWrite);
    }

    HlyUserAttr(String attrName, DataTypeEnum dataType, String displayName, String description, Boolean multiValued, MutableEnum mutability) {
        this.attrName = attrName;
        this.dataType = dataType;
        this.displayName = displayName;
        this.description = description;
        this.multiValued = multiValued;
        this.mutability = mutability;
    }

    @Override
    public String getAttrName() {
        return attrName;
    }

    @Override
    public DataTypeEnum getDataType() {
        return dataType;
    }

    @Override
    public String getDisplayName() {
        return displayName;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public Boolean getMultiValued() {
        return multiValued;
    }

    @Override
    public MutableEnum getMutability() {
        return mutability;
    }

    @Override
    public String getAppNamePath() {
        return APPUSER_PROFILE_ROOTNAME + "." + getAttrName();
    }

    @Override
    public String getIdpNamePath() {
        return IDPUSER_PROFILE_ROOTNAME + "." + getAttrName();
    }
}