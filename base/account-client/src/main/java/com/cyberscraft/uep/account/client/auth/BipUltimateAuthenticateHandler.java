package com.cyberscraft.uep.account.client.auth;

import com.cyberscraft.uep.common.bean.RestApiResponse;
import com.cyberscraft.uep.common.domain.auth.*;
import com.cyberscraft.uep.common.exception.ApiConnectException;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.common.util.MappingParser;
import com.cyberscraft.uep.common.util.RestAPIUtil;
import com.cyberscraft.uep.common.util.bip.SignHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @Date 2024/6/12 12:01
 * @Version 1.0
 * @Description bip旗舰版授权类
 */
@Service
public class BipUltimateAuthenticateHandler implements IAuthenticateHandler {

    private final static Logger logger = LoggerFactory.getLogger(BipUltimateAuthenticateHandler.class);

    private Map<String, String> cacheMap = new ConcurrentHashMap<>();

    @Override
    public boolean isSupported(String type) {
        return AuthProtocol.YONBIPUlTIMATE.name().equals(type);
    }

    @Override
    public AuthorizeCredential auth(Account account, String cacheKey, Boolean refresh) {
        BipUltimateParam bipParam = (BipUltimateParam) account;

        AuthorizeCredential authorizeCredential = new AuthorizeCredential();
        Map<String, Object> queryParam = new HashMap<>();
        // 除签名外的其他参数
        queryParam.put("appKey", bipParam.getAppKey());
        queryParam.put("timestamp", System.currentTimeMillis());
        // 计算签名
        try {
            String signature = SignHelper.sign(queryParam, bipParam.getAppSecret());
            queryParam.put("signature", signature);
        } catch (Exception e) {
            throw new ApiConnectException("bip旗舰版加签失败失败-详细信息[" + e.getMessage() + "]");
        }

        // 请求
        RestApiResponse apiResponse = RestAPIUtil.getForEntityString(account.getProxyIp(), bipParam.getEndpoint(), new HashMap<>(), queryParam, new HashMap<>());
        Map response;
        if (apiResponse.getBody() instanceof String) {
            response = JsonUtil.str2Map((String) apiResponse.getBody());
        } else {
            response = (Map) apiResponse.getBody();
        }

        checkResponse(response);
        Object data = response.get("data");
        Map resp = (Map) data;
        String accessToken = resp.get("access_token").toString();
        HashMap<String, String> queryMap = new HashMap<>();
        queryMap.put("access_token", accessToken);
        authorizeCredential.setQuery(queryMap);
        return authorizeCredential;

    }

    @Override
    public Account getAccount(AccountParam accountParam, Map<String, Object> queryParam, Map<String, Object> headerParam) {
        return MappingParser.schema2Obj(new HashMap<>(), accountParam.getAccount(), BipUltimateParam.class);
    }

    @Override
    public void evictCache(String cacheKey) {
        String fullCacheKey = IAuthenticateHandler.CACHE_KEY_PREFIX + cacheKey;
        cacheMap.remove(fullCacheKey);
    }


    private void checkResponse(Map<String, Object> body) {
        try {
            // 减少访问频率，此处只能减少单个线程的频率，对于多线程无效
            Thread.sleep(100);
        } catch (Exception e) {

        }
        String code = (String) body.get("code");
        String msg = (String) body.get("message");
        if (!"00000".equals(code) && !"200".equals(code)) {
            logger.error("checkResponse is fail, errcode:{}, errmsg:{}", code, msg);
            throw new ApiConnectException("请求bip旗舰版获取token失败-详细信息[" + msg + "]");
        }

    }
}
