package com.cyberscraft.uep.account.client.domain;

import com.cyberscraft.uep.account.client.constant.ThirdPartyTicketType;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/***
 *
 * @date 2021-12-15
 * <AUTHOR>
 ***/
public class DownloadSmsCodeRequest implements Serializable {
    /**
     * 租户标识
     */
    private String tenantId;

    /***
     * 地区码
     */
    private String areaCode = "86";

    /***
     * 手机号
     */
    private String phone;

    /***
     * 姓名
     */
    private String name;

    /****
     *
     */
    private String namespace = "local";

    /****
     *
     */
    private String smsCode;

    /****
     *
     */
    private String ticketType = ThirdPartyTicketType.REPEATABLE.getCode();


    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getAreaCode() {
        return areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNamespace() {
        return namespace;
    }

    public void setNamespace(String namespace) {
        this.namespace = namespace;
    }

    public String getSmsCode() {
        return smsCode;
    }

    public void setSmsCode(String smsCode) {
        this.smsCode = smsCode;
    }

    public String getTicketType() {
        return ticketType;
    }

    public void setTicketType(String ticketType) {
        this.ticketType = ticketType;
    }
}
