package com.cyberscraft.uep.account.client.constant;

import com.cyberscraft.uep.common.domain.DataTypeEnum;
import com.cyberscraft.uep.common.domain.MutableEnum;

import static com.cyberscraft.uep.account.client.constant.AccountConstant.APPORG_PROFILE_ROOTNAME;
import static com.cyberscraft.uep.account.client.constant.AccountConstant.IDPORG_PROFILE_ROOTNAME;

/**
 * 用友U8C部门参数
 *
 * <AUTHOR>
 * @date 2024年11月17日
 */
public enum Yyu8cOrgAttr implements ExternalAttr {
    addr("addr", DataTypeEnum.STRING, "部门地址"),
    createDate("createDate", new String[]{"createdate"}, DataTypeEnum.STRING, "创建时间"),
    deptattr("deptattr", DataTypeEnum.STRING, "部门属性（其他部门1，采购2，销售3，采购销售4）"),
    pk_corp("pk_corp", DataTypeEnum.STRING, "公司主键"),
    unitname("unitname", DataTypeEnum.STRING, "公司名称"),
    unitcode("unitcode", DataTypeEnum.STRING, "公司编码"),
    pk_deptdoc("pk_deptdoc", DataTypeEnum.STRING, "部门档案主键"),
    pk_fathedept("pk_fathedept", DataTypeEnum.STRING, "上级编码"),
    deptcode("deptcode", DataTypeEnum.STRING, "部门编码"),
    deptname("deptname", DataTypeEnum.STRING, "部门名称"),
    deptshortname("deptshortname", DataTypeEnum.STRING, "简称"),
    depttype("depttype", DataTypeEnum.NUMBER, "部门类型"),
    isuseretail("isuseretail", DataTypeEnum.BOOLEAN, "是否用于零售"),
    def1("def1", DataTypeEnum.STRING, "自定义1"),
    def10("def10", DataTypeEnum.STRING, "自定义10"),
    def11("def11", DataTypeEnum.STRING, "自定义11"),
    def12("def12", DataTypeEnum.STRING, "自定义12"),
    def13("def13", DataTypeEnum.STRING, "自定义13"),
    def14("def14", DataTypeEnum.STRING, "自定义14"),
    def15("def15", DataTypeEnum.STRING, "自定义15"),
    def16("def16", DataTypeEnum.STRING, "自定义16"),
    def17("def17", DataTypeEnum.STRING, "自定义17"),
    def18("def18", DataTypeEnum.STRING, "自定义18"),
    def19("def19", DataTypeEnum.STRING, "自定义19"),
    def2("def2", DataTypeEnum.STRING, "自定义2"),
    def20("def20", DataTypeEnum.STRING, "自定义20"),
    def3("def3", DataTypeEnum.STRING, "自定义3"),
    def4("def4", DataTypeEnum.STRING, "自定义4"),
    def5("def5", DataTypeEnum.STRING, "自定义5"),
    def6("def6", DataTypeEnum.STRING, "自定义6"),
    def7("def7", DataTypeEnum.STRING, "自定义7"),
    def8("def8", DataTypeEnum.STRING, "自定义8"),
    def9("def9", DataTypeEnum.STRING, "自定义9"),
    deptduty("deptduty", DataTypeEnum.STRING, "部门职责"),
    deptlevel("deptlevel", DataTypeEnum.STRING, "部门级别"),
    memo("memo", DataTypeEnum.STRING, "备注"),
    phone("phone", DataTypeEnum.STRING, "部门电话"),
    pk_calbody("pk_calbody", DataTypeEnum.STRING, "test"),
    pk_psndoc("pk_psndoc", DataTypeEnum.STRING, "负责人编码"),
    pk_psndoc2("pk_psndoc2", DataTypeEnum.STRING, "负责人2"),
    pk_psndoc3("pk_psndoc3", DataTypeEnum.STRING, "负责人3"),
    remcode("remcode", DataTypeEnum.STRING, "助记码"),
    ;

    private String attrName;

    private String[] alias;

    private DataTypeEnum dataType;

    private String displayName;

    private String description;

    private Boolean multiValued;

    private MutableEnum mutability;

    Yyu8cOrgAttr(String attrName, String[] alias, DataTypeEnum dataType, String displayName) {
        this(attrName, alias, dataType, displayName, false);
    }

    Yyu8cOrgAttr(String attrName, DataTypeEnum dataType, String displayName) {
        this(attrName, dataType, displayName, displayName, false);
    }

    Yyu8cOrgAttr(String attrName, String[] alias, DataTypeEnum dataType, String displayName, Boolean multiValued) {
        this.attrName = attrName;
        this.alias = alias;
        this.dataType = dataType;
        this.displayName = displayName;
        this.multiValued = multiValued;
    }

    Yyu8cOrgAttr(String attrName, DataTypeEnum dataType, String displayName, String description, Boolean multiValued) {
        this.attrName = attrName;
        this.dataType = dataType;
        this.displayName = displayName;
        this.description = description;
        this.multiValued = multiValued;
    }

    @Override
    public String getAttrName() {
        return attrName;
    }

    @Override
    public DataTypeEnum getDataType() {
        return dataType;
    }

    @Override
    public String getDisplayName() {
        return displayName;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public Boolean getMultiValued() {
        return multiValued;
    }

    @Override
    public MutableEnum getMutability() {
        return mutability;
    }

    @Override
    public String getAppNamePath() {
        return APPORG_PROFILE_ROOTNAME + "." + getAttrName();
    }

    @Override
    public String getIdpNamePath() {
        return IDPORG_PROFILE_ROOTNAME + "." + getAttrName();
    }
}