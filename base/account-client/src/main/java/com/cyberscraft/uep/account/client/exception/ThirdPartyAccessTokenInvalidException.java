package com.cyberscraft.uep.account.client.exception;

import com.cyberscraft.uep.common.enums.ExceptionCodeEnum;
import com.cyberscraft.uep.common.enums.SysCodeEnum;
import com.cyberscraft.uep.common.exception.errorcode.MultiLanguageExceptionCodes;

/***
 * 定义三方应用accessToken异常
 * @date 2021-10-30
 * <AUTHOR>
 ***/
public class ThirdPartyAccessTokenInvalidException extends RuntimeException {

    private String code;

    private String appendMsg;

    private MultiLanguageExceptionCodes multiLanguageExceptionCodes;

    /***
     * @param multiLanguageExceptionCodes
     */
    public ThirdPartyAccessTokenInvalidException(MultiLanguageExceptionCodes multiLanguageExceptionCodes) {
        super(multiLanguageExceptionCodes.getDesc());
        this.code = SysCodeEnum.THIRDPARTYACCOUNT.getCode() + multiLanguageExceptionCodes.getCode();
        this.multiLanguageExceptionCodes = multiLanguageExceptionCodes;
    }

    /**
     * 三方应用token异常
     *
     * @param code
     * @param message
     */
    public ThirdPartyAccessTokenInvalidException(String code, String message) {
        super(message);
        this.code = code;
    }

    /**
     * 三方应用token异常
     *
     * @param code
     * @param message
     * @param e
     */
    public ThirdPartyAccessTokenInvalidException(String code, String message, Throwable e) {
        super(message, e);
        this.code = code;
    }

    /**
     * 三方应用token异常
     *
     * @param message
     * @param e
     */
    public ThirdPartyAccessTokenInvalidException(String message, Throwable e) {
        super(message, e);
        this.code = ExceptionCodeEnum.INNER_ERROR.getCode();
    }

    /**
     * 三方应用token异常
     *
     * @param error
     */
    public ThirdPartyAccessTokenInvalidException(ExceptionCodeEnum error) {
        super(error.getMessage());
        this.code = error.getCode();
    }

    public ThirdPartyAccessTokenInvalidException(ThirdPartyAccountErrorType error) {
        super(error.getMessage());
        this.code = error.getCode();
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public MultiLanguageExceptionCodes getMultiLanguageExceptionCodes() {
        return multiLanguageExceptionCodes;
    }

    public void setMultiLanguageExceptionCodes(MultiLanguageExceptionCodes multiLanguageExceptionCodes) {
        this.multiLanguageExceptionCodes = multiLanguageExceptionCodes;
    }

    public String getAppendMsg() {
        return appendMsg;
    }

    public void setAppendMsg(String appendMsg) {
        this.appendMsg = appendMsg;
    }
}
