package com.cyberscraft.uep.account.client.constant;

import com.cyberscraft.uep.common.domain.DataTypeEnum;
import com.cyberscraft.uep.common.domain.MutableEnum;

import static com.cyberscraft.uep.account.client.constant.AccountConstant.APPUSER_PROFILE_ROOTNAME;
import static com.cyberscraft.uep.account.client.constant.AccountConstant.IDPUSER_PROFILE_ROOTNAME;

/**
 * <AUTHOR>
 * @Date 2024/9/19 14:05
 * @Version 1.0
 * @Description 明道云人员信息
 */
public enum HapUserAttr implements ExternalAttr {

    accountId("accountId", DataTypeEnum.STRING, "明道云账号Id", MutableEnum.readonly),
    corpUserId("corpUserId", DataTypeEnum.STRING, "第三方用户Id, 必填"),
    name("name", new String[]{"fullname"}, DataTypeEnum.STRING,"用户名, 必填"),

    email("email", DataTypeEnum.STRING, "邮箱，与 mobilePhone 不能同时为空, 必填"),
    mobilePhone("mobilePhone", DataTypeEnum.STRING, "手机号，与 email 不能同时为空, 必填"),
    contactPhone("contactPhone", DataTypeEnum.STRING, "座机号, 非必填"),
    jobNumber("jobNumber", DataTypeEnum.STRING, "工号, 非必填"),
    departmentIds("departmentIds", DataTypeEnum.OBJECT, "第三方部门Id集合, 非必填"),
    positions("positions", DataTypeEnum.OBJECT, "职位集合, 非必填"),
    emptyCover("emptyCover", DataTypeEnum.BOOLEAN, "当非必填字段为空时，是否覆盖原来的值，默认：true（覆盖）"),
    avatar("avatar", DataTypeEnum.STRING, "头像，用于集成"),
    ;

    private String attrName;

    private String[] alias;

    private DataTypeEnum dataType;

    private String displayName;

    private String description;

    private Boolean multiValued;

    private MutableEnum mutability;

    HapUserAttr(String attrName, DataTypeEnum dataType, String displayName) {
        this(attrName, dataType, displayName, displayName, false);
    }

    HapUserAttr(String attrName, DataTypeEnum dataType, String displayName, MutableEnum mutability) {
        this(attrName, null, dataType, displayName, displayName, false, mutability);
    }

    HapUserAttr(String attrName, String[] alias, DataTypeEnum dataType, String displayName) {
        this(attrName, alias, dataType, displayName, displayName, false);
    }

    HapUserAttr(String attrName, DataTypeEnum dataType, String displayName, Boolean multiValued) {
        this(attrName, dataType, displayName, displayName, multiValued);
    }

    HapUserAttr(String attrName, DataTypeEnum dataType, String displayName, String description, Boolean multiValued) {
        this(attrName, null, dataType, displayName, description, multiValued);
    }

    HapUserAttr(String attrName, String[] alias, DataTypeEnum dataType, String displayName, String description, Boolean multiValued) {
        this(attrName, alias, dataType, displayName, description, multiValued, MutableEnum.readWrite);
    }

    HapUserAttr(String attrName, String[] alias, DataTypeEnum dataType, String displayName, String description, Boolean multiValued, MutableEnum mutability) {
        this.attrName = attrName;
        this.alias = alias;
        this.dataType = dataType;
        this.displayName = displayName;
        this.description = description;
        this.multiValued = multiValued;
        this.mutability = mutability;
    }



    @Override
    public String getAttrName() {
        return attrName;
    }

    @Override
    public DataTypeEnum getDataType() {
        return dataType;
    }

    @Override
    public String getDisplayName() {
        return displayName;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public Boolean getMultiValued() {
        return multiValued;
    }

    @Override
    public MutableEnum getMutability() {
        return mutability;
    }

    @Override
    public String getAppNamePath() {
        return APPUSER_PROFILE_ROOTNAME + "." + getAttrName();
    }

    @Override
    public String getIdpNamePath() {
        return IDPUSER_PROFILE_ROOTNAME + "." + getAttrName();
    }
}