package com.cyberscraft.uep.account.client.constant;

import com.cyberscraft.uep.common.domain.DataTypeEnum;
import com.cyberscraft.uep.common.domain.MutableEnum;

import static com.cyberscraft.uep.account.client.constant.AccountConstant.*;

/**
 * @Author：xhquan
 * @Project：uep-services
 * @since：2023/11/3 16:37
 */
public enum K3CloudUserAttr implements ExternalAttr {
    FID("FID", DataTypeEnum.NUMBER, ""),
    FName("FName", DataTypeEnum.STRING, "员工姓名"),
    FStaffNumber("FStaffNumber", DataTypeEnum.STRING, "员工编号"),
    FMobile("FMobile", DataTypeEnum.STRING, "移动电话"),
    FTel("FTel", DataTypeEnum.STRING, "固定电话"),
    FEmail("FEmail", DataTypeEnum.STRING, "电子邮箱"),
    FDescription("FDescription", DataTypeEnum.STRING, "描述"),
    FAddress("FAddress", DataTypeEnum.STRING, "联系地址"),
    FUseOrgId("FUseOrgId", DataTypeEnum.OBJECT, "使用组织"),
    FCreateOrgId("FCreateOrgId", DataTypeEnum.OBJECT, "创建组织"),
    FBranchID("FBranchID", DataTypeEnum.OBJECT, "所属门店"),
    FCreateSaler("FCreateSaler", DataTypeEnum.BOOLEAN, "创建销售员"),
    FCreateUser("FCreateUser", DataTypeEnum.BOOLEAN, "创建Cloud用户"),
    FUserRole("FUserRole", DataTypeEnum.STRING, "用户角色", true),
    FCreateCashier("FCreateCashier", DataTypeEnum.BOOLEAN, "创建POS收银员"),
    FCashierGrp("FCashierGrp", DataTypeEnum.OBJECT, "收银员权限组"),
    FSalerId("FSalerId", DataTypeEnum.OBJECT, "销售员ID"),
    FCashierId("FCashierId", DataTypeEnum.OBJECT, "收银员ID"),
    FUserId("FUserId", DataTypeEnum.OBJECT, "用户ID"),
    FPostId("FPostId", DataTypeEnum.OBJECT, "所属岗位"),
    FJoinDate("FJoinDate", DataTypeEnum.STRING, "进店日期"),
    FUniportalNo("FUniportalNo", DataTypeEnum.STRING, "统一账号"),
    FSHRMapEntity("FSHRMapEntity", DataTypeEnum.OBJECT, ""),
    FPostEntity("FPostEntity", DataTypeEnum.OBJECT, "部门岗位信息", true),
    FBankInfo("FBankInfo", DataTypeEnum.OBJECT, "银行信息", true),
    IsAutoSubmitAndAudit("IsAutoSubmitAndAudit", DataTypeEnum.BOOLEAN, "创建用户自动提交", false),

    Id("Id", DataTypeEnum.NUMBER, "用户ID"),
    Name("Name", DataTypeEnum.STRING, "用户名称"),
    Number("Number", DataTypeEnum.STRING, "用户编码"),
    ;

    private String attrName;

    private DataTypeEnum dataType;

    private String displayName;

    private String desc;

    private Boolean multiValued;

    private MutableEnum mutability;


    K3CloudUserAttr(String attrName, DataTypeEnum dataType, String displayName) {
        this.attrName = attrName;
        this.dataType = dataType;
        this.displayName = displayName;
        this.multiValued = false;
    }

    K3CloudUserAttr(String attrName, DataTypeEnum dataType, String displayName, Boolean multiValued) {
        this.attrName = attrName;
        this.dataType = dataType;
        this.displayName = displayName;
        this.multiValued = multiValued;
    }

    @Override
    public String getAttrName() {
        return this.attrName;
    }

    @Override
    public DataTypeEnum getDataType() {
        return this.dataType;
    }

    @Override
    public String getDisplayName() {
        return this.displayName;
    }

    @Override
    public String getDescription() {
        return this.desc;
    }

    @Override
    public Boolean getMultiValued() {
        return this.multiValued;
    }

    @Override
    public String getAppNamePath() {
        return APPUSER_PROFILE_ROOTNAME + "." + getAttrName();
    }

    @Override
    public String getIdpNamePath() {
        return IDPUSER_PROFILE_ROOTNAME + "." + getAttrName();
    }
}
