package com.cyberscraft.uep.account.client.exception;

/***
 *
 * @date 2021/7/13
 * <AUTHOR>
 ***/
public class ThirdPartyAccountExistException extends RuntimeException {

    private String code;


    public ThirdPartyAccountExistException(String message) {
        super(message);
        this.code = ThirdPartyAccountErrorType.ACCOUNT_MOBILE_EXIST.getCode();
    }

    public ThirdPartyAccountExistException(String code, String message) {
        super(message);
        this.code = code;
    }

    /***
     *
     * @param code
     * @param message
     * @param e
     */
    public ThirdPartyAccountExistException(String code, String message, Throwable e) {
        super(message, e);
        this.code = code;
    }


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

}
