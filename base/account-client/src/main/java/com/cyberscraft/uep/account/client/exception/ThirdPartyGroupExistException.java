package com.cyberscraft.uep.account.client.exception;

/***
 *
 * @date 2021/10/12
 * <AUTHOR>
 ***/
public class ThirdPartyGroupExistException extends RuntimeException {

    private String code;

    /***
     * 用户在第三方平台中的用户ID
     */
    private String groupCode;

    public ThirdPartyGroupExistException(String message) {
        super(message);
        this.code = ThirdPartyAccountErrorType.GROUP_NAME_EXIST.getCode();
    }

    public ThirdPartyGroupExistException(String code, String message) {
        super(message);
        this.code = code;
    }

    public ThirdPartyGroupExistException(String code, String message, String groupCode) {
        super(message);
        this.code = code;
        this.groupCode = groupCode;
    }

    /***
     *
     * @param code
     * @param message
     * @param e
     */
    public ThirdPartyGroupExistException(String code, String message, Throwable e) {
        super(message, e);
        this.code = code;
    }

    /***
     *
     * @param code
     * @param message
     * @param e
     */
    public ThirdPartyGroupExistException(String code, String message, String userId, Throwable e) {
        super(message, e);
        this.code = code;
        this.groupCode = userId;
    }


    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getGroupCode() {
        return groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }
}
