package com.cyberscraft.uep.account.client.service.impl;

import com.cyberscraft.uep.account.client.domain.Connector;
import com.cyberscraft.uep.account.client.domain.SnsConfig;
import com.cyberscraft.uep.account.client.domain.ThirdPartyCallBackMessage;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountErrorType;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountException;
import com.cyberscraft.uep.account.client.provider.IThirdPartyMessageHandler;
import com.cyberscraft.uep.account.client.service.IThirdPartyMessageService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/***
 *
 * @date 2021/3/4
 * <AUTHOR>
 ***/
@Service
public class ThirdPartyMessageServiceImpl implements IThirdPartyMessageService {


    @Autowired(required = false)
    private List<IThirdPartyMessageHandler> providers = Collections.emptyList();

    /***
     *
     */
    private final static ConcurrentHashMap<String, IThirdPartyMessageHandler> PROVIDER_MAP = new ConcurrentHashMap<>(16);

    /****
     *
     */
    private final static Logger LOG = LoggerFactory.getLogger(ThirdPartyMessageServiceImpl.class);

    @PostConstruct
    private void initConnectorProviderMap() {
        PROVIDER_MAP.clear();
        if (this.providers != null && this.providers.size() > 0) {
            for (IThirdPartyMessageHandler provider : providers) {
                PROVIDER_MAP.put(provider.getSupportedAccountType(), provider);
            }
        }
    }

//    @Override
//    public String decryptMsg(ThirdPartyCallBackMessage msg) throws UserCenterException {
//        if (msg == null) {
//            throw new UserCenterException(TransactionErrorType.DS_CALLBACK_MESSAGE_INVALID_ERROR);
//        }
//        if (msg.getConnectorId() == null) {
//            throw new UserCenterException(TransactionErrorType.DS_ID_INVALID_ERROR);
//        }
//        TenantHolder.setTenantCode(msg.getTenantId());
//        ConnectorEntity connector = connectorService.searchDsById(msg.getConnectorId());
//
//        if (connector == null) {
//            LOG.error("处理微信消息时，连接器无效，租户:{},连接器:{}", msg.getTenantId(), msg.getConnectorId());
//            throw new UserCenterException(TransactionErrorType.DS_ID_INVALID_ERROR);
//        }
//        return decryptMsg(msg, connector);
//    }

    private final IThirdPartyMessageHandler findProvider(String accountType) throws ThirdPartyAccountException {
        if (accountType == null) {
            return null;
        }
        return PROVIDER_MAP.get(accountType);
    }


    @Override
    public String decryptMsg(ThirdPartyCallBackMessage msg, Connector connector) throws ThirdPartyAccountException {
        LOG.info("decryptMsg, type:{},tenant:{}", msg.getAccountType(), msg.getTenantId());
        IThirdPartyMessageHandler provider = findProvider(msg.getAccountType());
        if (provider == null) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.MESSAGE_PROVIDER_INVALID);
        }
        String plainMsg = provider.decryptMsg(msg, connector);
        LOG.info("decryptMsg finished, type:{},tenant:{}", msg.getAccountType(), msg.getTenantId());
        return plainMsg;
    }

    @Override
    public void dealRobotMsg(Map<String, Object> msg, String robotCode, String accountType) throws ThirdPartyAccountException {
        IThirdPartyMessageHandler provider = findProvider(accountType);
        if (provider == null) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.MESSAGE_PROVIDER_INVALID);
        }
        provider.dealRobotMessage(msg, robotCode);
    }

    @Override
    public <T> T deal(ThirdPartyCallBackMessage msg, SnsConfig snsConfig) throws ThirdPartyAccountException {
        IThirdPartyMessageHandler provider = findProvider(msg.getAccountType());
        if (provider == null) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.MESSAGE_PROVIDER_INVALID);
        }
        T rs = provider.deal(msg, snsConfig);
        LOG.info("process message finished, type:{},tenant:{},result:{}", msg.getAccountType(), msg.getTenantId(), rs);
        return rs;
    }

    @Override
    public <T> T deal(ThirdPartyCallBackMessage msg, Connector connector) throws ThirdPartyAccountException {
        IThirdPartyMessageHandler provider = findProvider(msg.getAccountType());
        if (provider == null) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.MESSAGE_PROVIDER_INVALID);
        }
        T rs = provider.deal(msg, connector);
        LOG.info("process message finished, type:{},tenant:{},result:{}", msg.getAccountType(), msg.getTenantId(), rs);
        return rs;
    }
}
