package com.cyberscraft.uep.account.client.domain;

import java.io.Serializable;

/***
 * 第三方平台用户所在组织职位信息
 * @date 2021/3/5
 * <AUTHOR>
 ***/
public class ThirdPartyGroupPosition implements Serializable {
    /****
     * 部门或者所在组织代码。code字段和path字段都可以作为部门的唯一标识，以code优先
     */
    private String code;

    /**
     * 部门路径
     */
    private String path;

    /****
     * 部门或者所在组织名称
     */
    private String name;

    /***
     * 职位
     */
    private String position;

    /**
     * 对应此部门中的直属主管
     */
    private String manager;

    /***
     * 是否为主部门：1、是，0、否
     */
    private Integer isMain;

    /**
     * 用户在该组下的编号，如学号、工号等
     */
    private String userCode;

    /**
     * 部门内排序
     */
    private Long order;

    /**
     * 是否为部门主管：1、是，0、否
     */
    private Integer isManager;

    /**
     * 员工在部门中的状况，作为一个标记字段，不做代码层面的业务处理
     */
    private String state;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getManager() {
        return manager;
    }

    public void setManager(String manager) {
        this.manager = manager;
    }

    public Integer getIsMain() {
        return isMain;
    }

    public void setIsMain(Integer isMain) {
        this.isMain = isMain;
    }

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public Long getOrder() {
        return order;
    }

    public void setOrder(Long order) {
        this.order = order;
    }

    public Integer getIsManager() {
        return isManager;
    }

    public void setIsManager(Integer isManager) {
        this.isManager = isManager;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }
}
