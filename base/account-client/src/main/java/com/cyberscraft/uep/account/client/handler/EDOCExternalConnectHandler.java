package com.cyberscraft.uep.account.client.handler;

import com.cyberscraft.uep.account.client.constant.*;
import com.cyberscraft.uep.account.client.domain.*;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccessTokenInvalidException;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountErrorType;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountException;
import com.cyberscraft.uep.account.client.provider.IExternalConnectHandler;
import com.cyberscraft.uep.account.client.util.AccountUtil;
import com.cyberscraft.uep.common.util.WebUrlUtil;
import com.cyberscraft.uep.common.enums.SysCodeEnum;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.common.util.RestAPIUtil;
import com.cyberscraft.uep.common.domain.DataTypeEnum;
import com.cyberscraft.uep.common.domain.ParamSchema;
import com.cyberscraft.uep.proxy.meta.service.IProxyMetaService;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @Author：xhquan
 * @Project：uep-services
 * @since：2023/7/15 13:52
 */
@Service
public class EDOCExternalConnectHandler implements IExternalConnectHandler {

    private final static Logger logger = LoggerFactory.getLogger(EDOCExternalConnectHandler.class);

    /**
     * 获取子部门列表
     */
    private String getChildDepartmentApi = "/api/services/OrgV2Department/GetChildDepartmentList";

    /***
     * 增加部门
     */
    private String createDepartmentApi = "/api/services/OrgV2Department/CreateDepartment";

    /***
     * 修改部门
     */
    private String modifyDepartmentApi = "/api/services/OrgV2Department/EditDepartment";


    /***
     * 修改（删除）部门
     */
    private String removeDepartmentApi = "/api/services/OrgDepartment/DeleteDepartmentById";

    /**
     * 获取部门下的用户
     */
    private String getUsersApi = "/api/services/OrgV2User/GetUsers";

    /***
     * 用户创建API
     */
    private String createUserApi = "/api/services/OrgV2User/CreateUser";

    /***
     * 用户修改API
     */
    private String modifyUserApi = "/api/services/OrgV2User/EditUser";

    /***
     * 用户修改（删除）API
     */
    private String removeUserApi = "/api/services/OrgUser/Logoff";

    /**
     * 通过account获取账户
     */
    private String getUserByAccount = "/api/services/OrgV2User/GetUserInfoByAccount";

    /**
     * 登录获取token的URL
     */
    private static final String tokenUrl = "/api/services/Org/UserLogin";

    /**
     * 通过token获取用户信息
     */
    private static final String getUserByToken = "/api/services/OrgV2User/GetUserInfoByToken";

    /**
     * 根绝ID获取用户信息
     */
    private static final String getUserByUserId = "/api/services/OrgV2User/GetUserInfoByUserId";

    /***
     * 获取部门详情
     */
    private String getDepartmentApi = "/api/services/OrgV2Department/GetDepartmentInfoById";

    Map<String, String> tokenMap = new HashMap<>();

    @Autowired
    private IProxyMetaService proxyMetaService;

    /**
     * 发送edoc请求需要的字段
     */
    private static final String TOKEN = "token";
    private static final String DATA = "data";
    private static final String USER_NAME = "username";
    private static final String PASS_WORD = "password";
    private static final String ERROR_CODE = "errorCode";
    private static final String ERROR_MSG = "errorMsg";
    private static final String TOKEN_ERROR_CODE = "4";
    private static final String Id = "Id";
    private static final String RESULT = "result";
    private static final String MESSAGE = "message";

    private static final String USER_ID_LIST = "userIdList";
    private static final String OPERATION_USER_ID = "operationUserId";

    private final ParamSchema userBasicAttrSchema;

    private final ParamSchema orgBasicAttrSchema;

    public EDOCExternalConnectHandler() {
        orgBasicAttrSchema = new ParamSchema(AccountConstant.APPORG_PROFILE_ROOTNAME, DataTypeEnum.OBJECT, "部门profile");
        for (EDOCOrgAttr value : EDOCOrgAttr.values()) {
            orgBasicAttrSchema.addSubParam(value.ps());
        }

        userBasicAttrSchema = new ParamSchema(AccountConstant.APPUSER_PROFILE_ROOTNAME, DataTypeEnum.OBJECT, "用户profile");
        for (EDOCUserAttr value : EDOCUserAttr.values()) {
            userBasicAttrSchema.addSubParam(value.ps());
        }

    }

    @Override
    public boolean isSupported(ConnectorType connectorType) {
        return ConnectorType.EDOC == connectorType;
    }

    @Override
    public ParamSchema getExternalOrgBasicAttrSchema(Connector connector) {
        return orgBasicAttrSchema;
    }

    @Override
    public ParamSchema getExternalUserBasicAttrSchema(Connector connector) {
        return userBasicAttrSchema;
    }

    @Override
    public ConnectorOrgProfile getExternalOrgFullProfile(Connector connector) {
        ParamSchema orgAllProfile = buildExternalOrgFullSchema(connector);

        ConnectorOrgProfile connectorOrgProfile = new ConnectorOrgProfile();
        connectorOrgProfile.setIdName(EDOCOrgAttr.id.getAttrName());
        connectorOrgProfile.setParentIdName(EDOCOrgAttr.parentId.getAttrName());
        connectorOrgProfile.setNameName(EDOCOrgAttr.name.getAttrName());
        connectorOrgProfile.setAttrSchema(orgAllProfile);
        return connectorOrgProfile;
    }

    @Override
    public ConnectorUserProfile getExternalUserFullProfile(Connector connector) {
        ParamSchema userAllProfile = buildExternalUserFullSchema(connector);

        ConnectorUserProfile connectorUserProfile = new ConnectorUserProfile();
        connectorUserProfile.setIdName(EDOCUserAttr.id.getAttrName());
        connectorUserProfile.setDeptName(EDOCUserAttr.MainDepartmentId.getAttrName());
        connectorUserProfile.setAttrSchema(userAllProfile);
        return connectorUserProfile;
    }

    @Override
    public ConnectorOrg<String, Object> getRootExternalOrg(Connector connector) throws ThirdPartyAccountException {
        String rootCode = connector.getRootCode();
        if (StringUtils.isBlank(rootCode)) {
            throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), "rootCode is NUll");
        }

        return getExternalOrgById(rootCode, connector);
    }

    @Override
    public List<ConnectorOrg<String, Object>> getAllSubExternalOrgs(ConnectorOrg parentExternalOrg, Connector connector) throws ThirdPartyAccountException {
        EDOCConfig edocConfig = JsonUtil.str2Obj(connector.getConfig(), EDOCConfig.class);
        Map<String, Object> params = new HashMap<>();
        params.put(TOKEN, tokenMap.get(TOKEN));
        params.put("recursive", true);
        params.put("pageSize", 100);
        params.put("departmentId", parentExternalOrg.getIdValue());
        Integer index = 1;
        List<ConnectorOrg<String, Object>> result = new ArrayList<>();
        while (true) {
            params.put("pageIndex", index);
            String url = WebUrlUtil.getWebAccessServerUrl(edocConfig.getHost(), getChildDepartmentApi, params);
            Map<String, Object> response = null;
            String virtualIp = getVirtualIp(edocConfig, String.valueOf(connector.getProxyId()));
            try {
                response = RestAPIUtil.getForEntity(virtualIp, url, null);
                checkResponse(response, getChildDepartmentApi);
            } catch (ThirdPartyAccessTokenInvalidException e) {
                // 如果token异常则重新申请token
                logger.info("get edoc group token exception group id is  : {}", parentExternalOrg.getIdValue());
                tokenMap.clear();
                String accessToken = getAccessToken(edocConfig, String.valueOf(connector.getProxyId()));
                params.put(TOKEN, accessToken);
                url = WebUrlUtil.getWebAccessServerUrl(edocConfig.getHost(), getChildDepartmentApi, params);
                response = RestAPIUtil.getForEntity(virtualIp, url, null);
                checkResponse(response, getChildDepartmentApi);
            } catch (Exception e) {
                logger.info("get edoc group info error group id is  : {}", parentExternalOrg.getIdValue());
                logger.error(e.getMessage());
                throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), e.getMessage());
            }
            Map data = (Map) response.get(DATA);
            List datas = (List) data.get("Datas");
            result.addAll(AccountUtil.to(datas, getExternalOrgFullProfile(connector)));

            Integer total = (Integer) data.get("Total");
            int maxPage = total == 0 ? 0 : (total % 100 == 0 ? total / 100 : total / 100 + 1);

            if (total == 0 || index == maxPage) {
                break;
            }
            index++;
        }
        return result;
    }

    @Override
    public List<ConnectorOrg<String, Object>> getSubExternalOrgs(String parentOrgId, Connector connector) throws ThirdPartyAccountException {
        return null;
    }

    @Override
    public List<ConnectorOrg<String, Object>> getExternalOrgByIds(List<String> externalOrgIds, Connector connector) throws ThirdPartyAccountException {
        return null;
    }

    @Override
    public ConnectorOrg getExternalOrgById(String externalOrgId, Connector connector) throws ThirdPartyAccountException {
        EDOCConfig edocConfig = JsonUtil.str2Obj(connector.getConfig(), EDOCConfig.class);
        Map<String, Object> params = new HashMap<>();
        params.put(TOKEN, tokenMap.get(TOKEN));
        params.put(EDOCOrgAttr.deptId.getAttrName(), externalOrgId);

        String url = WebUrlUtil.getWebAccessServerUrl(edocConfig.getHost(), getDepartmentApi, params);
        Map<String, Object> response = null;
        String virtualIp = getVirtualIp(edocConfig, String.valueOf(connector.getProxyId()));
        try {
            response = RestAPIUtil.getForEntity(virtualIp, url, null);
            checkResponse(response, getDepartmentApi);
        } catch (ThirdPartyAccessTokenInvalidException e) {
            // 如果token异常则重新申请token
            logger.info("get edoc group token exception group id is  : {}", externalOrgId);
            tokenMap.clear();
            String accessToken = getAccessToken(edocConfig,String.valueOf(connector.getProxyId()));
            params.put(TOKEN, accessToken);
            url = WebUrlUtil.getWebAccessServerUrl(edocConfig.getHost(), getDepartmentApi, params);
            response = RestAPIUtil.getForEntity(virtualIp, url, null);
            checkResponse(response, getDepartmentApi);

        } catch (Exception e) {
            logger.info("get edoc group info error group id is  : {}", externalOrgId);
            logger.error(e.getMessage());
            throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), e.getMessage());
        }

        return AccountUtil.to(response.get(DATA), getExternalOrgFullProfile(connector));
    }

    @Override
    public boolean isSortedByOrgLevel() {
        return false;
    }

    @Override
    public List<ConnectorUser<String, Object>> getExternalUsersByOrg(ConnectorOrg externalOrg, Connector connector) throws ThirdPartyAccountException {
        EDOCConfig edocConfig = JsonUtil.str2Obj(connector.getConfig(), EDOCConfig.class);
        Map<String, Object> params = new HashMap<>();
        params.put(TOKEN, tokenMap.get(TOKEN));
        params.put("orgType", edocConfig.getOrgType());
        params.put("pageSize", 100);
        params.put("orgId", externalOrg.getIdValue());
        params.put("status", 0);
        params.put("recursive", false);

        Integer index = 1;
        List<ConnectorUser<String, Object>> result = new ArrayList<>();
        while (true) {
            params.put("pageIndex", index);
            String url = WebUrlUtil.getWebAccessServerUrl(edocConfig.getHost(), getUsersApi, params);
            Map<String, Object> response = null;
            String virtualIp = getVirtualIp(edocConfig, String.valueOf(connector.getProxyId()));
            try {
                response = RestAPIUtil.getForEntity(virtualIp, url, null);
                checkResponse(response, getUsersApi);
            } catch (ThirdPartyAccessTokenInvalidException e) {
                // 如果token异常则重新申请token
                logger.info("get edoc user token exception group id is  : {}", externalOrg.getIdValue());
                tokenMap.clear();
                String accessToken = getAccessToken(edocConfig,String.valueOf(connector.getProxyId()));
                params.put(TOKEN, accessToken);
                url = WebUrlUtil.getWebAccessServerUrl(edocConfig.getHost(), getUsersApi, params);
                response = RestAPIUtil.getForEntity(virtualIp, url, null);
                checkResponse(response, getUsersApi);
            } catch (Exception e) {
                logger.info("get edoc user info error group id is  : {}", externalOrg.getIdValue());
                logger.error(e.getMessage());
                throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), e.getMessage());
            }
            Map data = (Map) response.get(DATA);
            List datas = (List) data.get("Datas");
            result.addAll(AccountUtil.to(datas, getExternalUserFullProfile(connector)));

            Integer total = (Integer) data.get("Total");
            int maxPage = total == 0 ? 0 : (total % 100 == 0 ? total / 100 : total / 100 + 1);

            if (total == 0 || index == maxPage) {
                break;
            }
            index++;
        }
        return result;
    }

    @Override
    public List<ConnectorUser<String, Object>> getExternalUsers(List<String> externalUserIds, Connector connector) throws ThirdPartyAccountException {
        return null;
    }

    @Override
    public Boolean limitListExternalUsers(int page, int pageSize, Connector connector, List<ConnectorUser<String, Object>> output) throws ThirdPartyAccountException {
        return null;
    }

    @Override
    public ConnectorUser getExternalUserById(String externalAccountId, Connector connector) throws ThirdPartyAccountException {
        EDOCConfig edocConfig = JsonUtil.str2Obj(connector.getConfig(), EDOCConfig.class);
        Map<String, Object> params = new HashMap<>();
        params.put(TOKEN, tokenMap.get(TOKEN));
        params.put(EDOCUserAttr.userId.getAttrName(), externalAccountId);

        String url = WebUrlUtil.getWebAccessServerUrl(edocConfig.getHost(), getUserByUserId, params);
        Map<String, Object> response = null;
        String virtualIp = getVirtualIp(edocConfig, String.valueOf(connector.getProxyId()));
        try {
            response = RestAPIUtil.getForEntity(virtualIp, url, null);
            checkResponse(response, getUserByUserId);
        } catch (ThirdPartyAccessTokenInvalidException e) {
            // 如果token异常则重新申请token
            logger.info("get edoc user token exception user id is  : {}", externalAccountId);
            tokenMap.clear();
            String accessToken = getAccessToken(edocConfig,String.valueOf(connector.getProxyId()));
            params.put(TOKEN, accessToken);
            url = WebUrlUtil.getWebAccessServerUrl(edocConfig.getHost(), getUserByUserId, params);
            response = RestAPIUtil.getForEntity(virtualIp, url, null);
            checkResponse(response, getUserByUserId);

        } catch (Exception e) {
            logger.info("get edoc user info error user id is  : {}", externalAccountId);
            logger.error(e.getMessage());
            throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), e.getMessage());
        }

        return AccountUtil.to(response.get(DATA), getExternalUserFullProfile(connector));
    }

    @Override
    public int login(Connector connector, String username, String password) {
        return 0;
    }

    @Override
    public ConnectorUser test(Connector connector) {
        if (connector == null) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.CONNECTOR_INVALID);
        }
        String testAccount = connector.getTestAccount();
        if (testAccount == null) {
            return null;
        }
        Map<String, Object> account = getEDOCUserByAccount(connector, testAccount, getUserByAccount);
        ConnectorUser res = null;
        if (account.size() > 0) {
            res = AccountUtil.to(account, getExternalUserFullProfile(connector));
        } else {
            res = getExternalUserById(testAccount, connector);
        }
        return res;
    }


    @Override
    public void closeConnection(Connector connector) {

    }

    @Override
    public String createExternalOrg(ConnectorOrg externalOrg, Connector connector) throws ThirdPartyAccountException {
        EDOCConfig edocConfig = JsonUtil.str2Obj(connector.getConfig(), EDOCConfig.class);
        String url = WebUrlUtil.getWebServerUrl(edocConfig.getHost(), createDepartmentApi);

        Map<String, Object> params = new HashMap<>();
        externalOrg.forEach((k, v) -> {
            if (v != null) {
                params.put(k.toString(), v);
            }
        });
        params.put(TOKEN, tokenMap.get(TOKEN));
        String virtualIp = getVirtualIp(edocConfig, String.valueOf(connector.getProxyId()));
        Map<String, Object> response = null;
        try {
            response = RestAPIUtil.postForEntity(virtualIp, url, params, new Object());
            checkResponse(response, createDepartmentApi);
        } catch (ThirdPartyAccessTokenInvalidException e) {
            // 如果token异常则重新申请token
            logger.info("create group  token exception group info  is : {}", JsonUtil.obj2Str(params));
            tokenMap.clear();
            String accessToken = getAccessToken(edocConfig,String.valueOf(connector.getProxyId()));
            params.put(TOKEN, accessToken);
            response = RestAPIUtil.postForEntity(virtualIp, url, params, new Object());
            checkResponse(response, createDepartmentApi);
        } catch (Exception e) {
            logger.info("create group  exception group info  is : {}", JsonUtil.obj2Str(params));
            logger.error(e.getMessage());
            throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), e.getMessage());
        }

        Map<String, Object> result = JsonUtil.str2Map((String) response.get(DATA));
        return (String) result.get(Id);
    }

    @Override
    public String modifyExternalOrg(String externalOrgId, ConnectorOrg externalOrg, Connector connector) throws ThirdPartyAccountException {
        externalOrg.setIdValue(externalOrgId);

        EDOCConfig edocConfig = JsonUtil.str2Obj(connector.getConfig(), EDOCConfig.class);
        String url = WebUrlUtil.getWebServerUrl(edocConfig.getHost(), modifyDepartmentApi);

        Map<String, Object> response = null;
        Map<String, Object> params = new HashMap<>();
        externalOrg.forEach((k, v) -> {
            if (v != null) {
                params.put(k.toString(), v);
            }
        });
        params.put(TOKEN, tokenMap.get(TOKEN));
        params.put(EDOCOrgAttr.id.getAttrName(), externalOrgId);
        String virtualIp = getVirtualIp(edocConfig, String.valueOf(connector.getProxyId()));
        try {
            response = RestAPIUtil.postForEntity(virtualIp, url, params, new Object());
            checkResponse(response, modifyDepartmentApi);
        } catch (ThirdPartyAccessTokenInvalidException e) {
            // 如果token异常则重新申请token
            logger.info("modify group  token exception group info  is : {}", JsonUtil.obj2Str(params));
            tokenMap.clear();
            String accessToken = getAccessToken(edocConfig,String.valueOf(connector.getProxyId()));
            params.put(TOKEN, accessToken);
            response = RestAPIUtil.postForEntity(virtualIp, url, params, new Object());
            checkResponse(response, modifyDepartmentApi);
        } catch (Exception e) {
            logger.info("modify group  exception group info  is : {}", JsonUtil.obj2Str(params));
            logger.error(e.getMessage());
            throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), e.getMessage());
        }
        return externalOrgId;
    }

    @Override
    public void deleteExternalOrg(String externalOrgId, Connector connector) throws ThirdPartyAccountException {
        EDOCConfig edocConfig = JsonUtil.str2Obj(connector.getConfig(), EDOCConfig.class);
        String url = WebUrlUtil.getWebServerUrl(edocConfig.getHost(), removeDepartmentApi);

        Map<String, Object> response = null;
        Map<String, Object> params = new HashMap<>();
        params.put(TOKEN, tokenMap.get(TOKEN));
        params.put(OPERATION_USER_ID, getEDOCUserByToken(connector, tokenMap.get(TOKEN)));
        params.put(EDOCOrgAttr.id.getAttrName(), externalOrgId);
        String virtualIp = getVirtualIp(edocConfig, String.valueOf(connector.getProxyId()));
        try {
            response = RestAPIUtil.postForEntity(virtualIp, url, params);
            checkResponse(response, removeDepartmentApi);
        } catch (ThirdPartyAccessTokenInvalidException e) {
            logger.info("remove group  token exception group info  is : {}", externalOrgId);
            tokenMap.clear();
            String accessToken = getAccessToken(edocConfig,String.valueOf(connector.getProxyId()));
            params.put(TOKEN, accessToken);
            response = RestAPIUtil.postForEntity(virtualIp, url, params);
            checkResponse(response, removeDepartmentApi);
        } catch (Exception e) {
            logger.info("remove group  exception group info  is : {}", externalOrgId);
            logger.error(e.getMessage());
            throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), e.getMessage());
        }
    }

    @Override
    public void deleteExternalOrgs(List<String> externalOrgIds, Connector connector) throws ThirdPartyAccountException {
        for (String externalOrgId : externalOrgIds) {
            deleteExternalOrg(externalOrgId, connector);
        }
    }

    @Override
    public String createExternalUser(ConnectorUser externalUser, Connector connector) throws ThirdPartyAccountException {
        EDOCConfig edocConfig = JsonUtil.str2Obj(connector.getConfig(), EDOCConfig.class);
        String url = WebUrlUtil.getWebServerUrl(edocConfig.getHost(), createUserApi);

        Map<String, Object> response = null;
        Map<String, Object> params = new HashMap<>();
        externalUser.forEach((k, v) -> {
            if (v != null) {
                params.put(k.toString(), v);
            }
        });
        params.put(TOKEN, tokenMap.get(TOKEN));

        // 处理主部门主职位逻辑
        Object positionListObj = externalUser.getValue(EDOCUserAttr.positionList);

        if (!(positionListObj instanceof List)) {
            logger.info("position is not list");
            throw new ThirdPartyAccountException("Expected a List for positionList attribute");
        }

        List<Object> positionsList = (List<Object>) positionListObj;
        boolean includeMainPosition = false;
        List<Map<String, Object>> positions = new ArrayList<>();

        for (Object position : positionsList) {
            if (position == null) continue; // 跳过null值

            try {
                if (position instanceof ThirdPartyGroupPosition) {
                    ThirdPartyGroupPosition groupPosition = (ThirdPartyGroupPosition) position;
                    includeMainPosition = isIncludeMainPosition(
                            connector, groupPosition.getCode(), positions, groupPosition.getIsMain()
                    );
                } else if (position instanceof Map) {
                    Map<?, ?> positionMap = (Map<?, ?>) position;
                    String orgId = Objects.toString(positionMap.get("code"), null);
                    String isMainStr = Objects.toString(positionMap.get("is_main"), null);
                    Integer mainGroup = isMainStr != null ? Integer.parseInt(isMainStr) : null;
                    includeMainPosition = isIncludeMainPosition(connector, orgId, positions, mainGroup);
                }
            } catch (NumberFormatException e) {
                // 日志或处理转换错误
                logger.info("数据转换异常 {}", JsonUtil.obj2Str(positionListObj));
                logger.info("", e);
            }
        }

        if (!includeMainPosition && !positions.isEmpty()) {
            positions.get(0).put("isMain", true);
        }

        params.put(EDOCUserAttr.positionList.getAttrName(), positions);
        String virtualIp = getVirtualIp(edocConfig, String.valueOf(connector.getProxyId()));
        try {
            response = RestAPIUtil.postForEntity(virtualIp, url, params);
            checkResponse(response, createUserApi);
        } catch (ThirdPartyAccessTokenInvalidException e) {
            logger.info("create user account  token exception createUserInfo info  is : {}", JsonUtil.obj2Str(params));
            tokenMap.clear();
            String accessToken = getAccessToken(edocConfig,String.valueOf(connector.getProxyId()));
            params.put(TOKEN, accessToken);
            response = RestAPIUtil.postForEntity(virtualIp, url, params);
            checkResponse(response, createUserApi);
        } catch (ThirdPartyAccountException e) {
            logger.info("create user account exception createUserInfo is : {}", e.getMessage());
            logger.info("create user account exception createUserInfo is : {}", JsonUtil.obj2Str(params));
            Map<String, Object> user = getEDOCUserByAccount(connector, (String) externalUser.getValue(EDOCUserAttr.account), getUserByAccount);
            if (user != null && !user.isEmpty()) {
                String id = (String) user.get(Arrays.asList(EDOCUserAttr.id.getAlias()).get(0));
                ConnectorUser<String, Object> oldInfo = AccountUtil.to(user, getExternalUserFullProfile(connector));
                oldInfo.putAll(externalUser);
                oldInfo.setIdValue(id);
                oldInfo.put(EDOCUserAttr.id.getAttrName(), id);
                externalUser.setIdValue(id);
                return modifyExternalUser(id, oldInfo, connector);
            }
        } catch (Exception e) {
            logger.info("create User error createUserInfo is : {}", JsonUtil.obj2Str(params));
            logger.error(e.getMessage());
            throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), e.getMessage());
        }

        if (response != null) {
            Map<String, Object> result = JsonUtil.str2Map((String) response.get(DATA));
            externalUser.setIdValue(result.get(Id));
            return (String) result.get(Id);
        } else {
            logger.info("create User  error group info  is : {}", JsonUtil.obj2Str(params));
            return null;
        }
    }

    private boolean isIncludeMainPosition(Connector connector, String orgId, List<Map<String, Object>> positions, Integer mainGroup) {
        boolean isMain = mainGroup != null && 1 == mainGroup;
        Map<String, Object> position = new HashMap<>();
        ConnectorOrg externalOrgById = getExternalOrgById(orgId, connector);
        String positionId = (String) externalOrgById.get(EDOCOrgAttr.managerPositionId.getAttrName());

        position.put("positionId", positionId);
        position.put("isMain", isMain);
        positions.add(position);

        return isMain;
    }

    @Override
    public String modifyExternalUser(String externalUserId, ConnectorUser externalUser, Connector connector) throws ThirdPartyAccountException {
        externalUser.setIdValue(externalUserId);
        EDOCConfig edocConfig = JsonUtil.str2Obj(connector.getConfig(), EDOCConfig.class);
        String url = WebUrlUtil.getWebServerUrl(edocConfig.getHost(), modifyUserApi);

        Map<String, Object> response = null;
        Map<String, Object> params = new HashMap<>();
        externalUser.forEach((k, v) -> {
            if (v != null) {
                params.put(k.toString(), v);
            }
        });
        params.put(TOKEN, tokenMap.get(TOKEN));
        params.put(EDOCUserAttr.id.getAttrName(), externalUserId);

        // 处理主部门主职位逻辑
        Object positionListObj = externalUser.getValue(EDOCUserAttr.positionList);

        if (!(positionListObj instanceof List)) {
            logger.info("position is not list");
            throw new ThirdPartyAccountException("Expected a List for positionList attribute");
        }

        List<Object> positionsList = (List<Object>) positionListObj;
        boolean includeMainPosition = false;
        List<Map<String, Object>> positions = new ArrayList<>();

        for (Object position : positionsList) {
            if (position == null) continue; // 跳过null值

            try {
                if (position instanceof ThirdPartyGroupPosition) {
                    ThirdPartyGroupPosition groupPosition = (ThirdPartyGroupPosition) position;
                    includeMainPosition = isIncludeMainPosition(
                            connector, groupPosition.getCode(), positions, groupPosition.getIsMain()
                    );
                } else if (position instanceof Map) {
                    Map<?, ?> positionMap = (Map<?, ?>) position;
                    String orgId = Objects.toString(positionMap.get("code"), null);
                    String isMainStr = Objects.toString(positionMap.get("is_main"), null);
                    Integer mainGroup = isMainStr != null ? Integer.parseInt(isMainStr) : null;
                    includeMainPosition = isIncludeMainPosition(connector, orgId, positions, mainGroup);
                }
            } catch (NumberFormatException e) {
                // 日志或处理转换错误
                logger.info("数据转换异常 {}", JsonUtil.obj2Str(positionListObj));
                logger.info("", e);
            }
        }

        String mainPosition = (String) externalUser.get(Arrays.asList(EDOCUserAttr.mainPositionId.getAlias()).get(0));
        if (!includeMainPosition && StringUtils.isNotBlank(mainPosition)) {
            boolean foundMainPosition = false;
            for (Map<String, Object> position : positions) {
                if (mainPosition.equals((String) position.get("positionId"))) {
                    position.put("isMain", true);
                    foundMainPosition = true;
                    break;
                }
            }
            if (!foundMainPosition) {
                Map<String, Object> newPosition = new HashMap<>();
                newPosition.put("positionId", mainPosition);
                newPosition.put("isMain", true);
                positions.add(newPosition);
            }
        }

        params.put(EDOCUserAttr.positionList.getAttrName(), positions);
        String virtualIp = getVirtualIp(edocConfig, String.valueOf(connector.getProxyId()));
        try {
            response = RestAPIUtil.postForEntity(virtualIp, url, params);
            checkResponse(response, modifyUserApi);
        } catch (ThirdPartyAccessTokenInvalidException e) {
            logger.info("modify user account  token exception modifyUserInfo info  is : {}", JsonUtil.obj2Str(params));
            tokenMap.clear();
            String accessToken = getAccessToken(edocConfig,String.valueOf(connector.getProxyId()));
            params.put(TOKEN, accessToken);
            response = RestAPIUtil.postForEntity(virtualIp, url, params);
            checkResponse(response, modifyUserApi);
        } catch (Exception e) {
            logger.info("modify User error modifyUserInfo is : {}", JsonUtil.obj2Str(params));
            logger.error(e.getMessage());
            throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), e.getMessage());
        }
        return externalUserId;
    }

    @Override
    public void deleteExternalUser(String externalUserId, Connector connector) throws ThirdPartyAccountException {
        EDOCConfig edocConfig = JsonUtil.str2Obj(connector.getConfig(), EDOCConfig.class);
        String url = WebUrlUtil.getWebServerUrl(edocConfig.getHost(), removeUserApi);

        Map<String, Object> response = null;
        Map<String, Object> params = new HashMap<>();
        params.put(TOKEN, tokenMap.get(TOKEN));
        params.put(OPERATION_USER_ID, getEDOCUserByToken(connector, tokenMap.get(TOKEN)));
        params.put(USER_ID_LIST, Arrays.asList(externalUserId));
        String virtualIp = getVirtualIp(edocConfig, String.valueOf(connector.getProxyId()));
        try {
            logger.info("remove user UserInfo is :  {}", externalUserId);
            response = RestAPIUtil.postForEntity(virtualIp, url, params);
            checkResponse(response, removeUserApi);
        } catch (ThirdPartyAccessTokenInvalidException e) {
            logger.info("remove user token exception  UserInfo is :  {}", externalUserId);
            tokenMap.clear();
            String accessToken = getAccessToken(edocConfig,String.valueOf(connector.getProxyId()));
            params.put(TOKEN, accessToken);
            params.put(OPERATION_USER_ID, getEDOCUserByToken(connector, tokenMap.get(TOKEN)));
            response = RestAPIUtil.postForEntity(virtualIp, url, params);
            checkResponse(response, removeUserApi);
        } catch (Exception e) {
            logger.info("remove user exception  UserInfo is :  {}", externalUserId);
            logger.error(e.getMessage());
            throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), e.getMessage());
        }
    }

    @Override
    public void deleteExternalUsers(List<String> externalUserIds, Connector connector) throws ThirdPartyAccountException {
        EDOCConfig edocConfig = JsonUtil.str2Obj(connector.getConfig(), EDOCConfig.class);
        String url = WebUrlUtil.getWebServerUrl(edocConfig.getHost(), removeUserApi);
        Map<String, Object> response = null;
        Map<String, Object> params = new HashMap<>();
        params.put(TOKEN, tokenMap.get(TOKEN));
        params.put(USER_ID_LIST, externalUserIds);
        String virtualIp = getVirtualIp(edocConfig, String.valueOf(connector.getProxyId()));
        try {
            logger.info("remove user UserInfo is :  {}", externalUserIds);
            response = RestAPIUtil.postForEntity(virtualIp, url, params);
            checkResponse(response, removeUserApi);
        } catch (ThirdPartyAccessTokenInvalidException e) {
            logger.info("remove user token exception  UserInfo is :  {}", externalUserIds);
            tokenMap.clear();
            String accessToken = getAccessToken(edocConfig,String.valueOf(connector.getProxyId()));
            params.put(TOKEN, accessToken);
            response = RestAPIUtil.postForEntity(virtualIp, url, params);
            checkResponse(response, removeUserApi);
        } catch (Exception e) {
            logger.info("remove user exception  UserInfo is :  {}", externalUserIds);
            logger.error(e.getMessage());
            throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), e.getMessage());
        }
    }

    private Map<String, Object> getEDOCUserByAccount(Connector connector, String account, String getUserByUserId) {
        EDOCConfig edocConfig = JsonUtil.str2Obj(connector.getConfig(), EDOCConfig.class);

        Map<String, Object> params = new HashMap<>();
        params.put(TOKEN, tokenMap.get(TOKEN));
        params.put(Arrays.asList(EDOCUserAttr.account.getAlias()).get(0), account);
        String url = WebUrlUtil.getWebAccessServerUrl(edocConfig.getHost(), getUserByUserId, params);
        Map<String, Object> response = null;
        String virtualIp = getVirtualIp(edocConfig, String.valueOf(connector.getProxyId()));
        try {
            response = RestAPIUtil.getForEntity(virtualIp, url, null);
            checkResponse(response, getUserByAccount);
        } catch (ThirdPartyAccessTokenInvalidException e) {
            // 如果token异常则重新申请token
            tokenMap.clear();
            String accessToken = getAccessToken(edocConfig,String.valueOf(connector.getProxyId()));
            params.put(TOKEN, accessToken);
            url = WebUrlUtil.getWebAccessServerUrl(edocConfig.getHost(), getUserByUserId, params);
            response = RestAPIUtil.getForEntity(virtualIp, url, null);
            checkResponse(response, getUserByAccount);
        } catch (Exception e) {
            logger.info("exception info is {}", e.getMessage());
            throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), e.getMessage());
        }

        return (Map<String, Object>) response.get(DATA);
    }

    private String getEDOCUserByToken(Connector connector, String token) {
        EDOCConfig edocConfig = JsonUtil.str2Obj(connector.getConfig(), EDOCConfig.class);

        Map<String, Object> params = new HashMap<>();
        params.put(TOKEN, token);

        String url = WebUrlUtil.getWebAccessServerUrl(edocConfig.getHost(), getUserByToken, params);
        String virtualIp = getVirtualIp(edocConfig, String.valueOf(connector.getProxyId()));
        Map<String, Object> response = null;
        try {
            response = RestAPIUtil.getForEntity(virtualIp, url, null);
            checkResponse(response, getUserByToken);
        } catch (ThirdPartyAccessTokenInvalidException e) {
            // 如果token异常则重新申请token
            tokenMap.clear();
            String accessToken = getAccessToken(edocConfig,String.valueOf(connector.getProxyId()));
            params.put(TOKEN, accessToken);
            url = WebUrlUtil.getWebAccessServerUrl(edocConfig.getHost(), getUserByToken, params);
            response = RestAPIUtil.getForEntity(virtualIp, url, null);
            checkResponse(response, getUserByToken);
        } catch (Exception e) {
            logger.info("exception info is {}", e.getMessage());
            throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), e.getMessage());
        }
        Map<String, Object> user = (Map<String, Object>) response.get(DATA);
        return (String) user.get(Arrays.asList(EDOCUserAttr.id.getAlias()).get(0));
    }

    /**
     * 发送请求获取token
     *
     * @param edocConfig
     * @return
     */
    private String getAccessToken(EDOCConfig edocConfig,String porxyId) {
        String host = edocConfig.getHost();
        String account = edocConfig.getUsername();
        String password = edocConfig.getPassword();
        Map<String, Object> body = new HashMap<>();
        body.put(USER_NAME, account);
        body.put(PASS_WORD, password);
        String virtualIp = getVirtualIp(edocConfig, porxyId);

        Map map = RestAPIUtil.postForEntity(virtualIp, WebUrlUtil.getWebServerUrl(host, tokenUrl), body);
        if (map.size() > 0) {
            String token = (String) map.get(DATA);
            if (StringUtils.isNotBlank(token)) {
                tokenMap.put(TOKEN, token);
                return token;
            } else {
                throw new RuntimeException("获取token异常");
            }
        } else {
            throw new RuntimeException("获取token异常");
        }
    }

    /**
     * 请求结果校验
     *
     * @param response
     */
    protected void checkResponse(Map response, String api) {
        if (response.containsKey(ERROR_CODE) && TOKEN_ERROR_CODE.equals(response.get(ERROR_CODE))) {
            // token异常
            throw new ThirdPartyAccessTokenInvalidException((String) response.get(ERROR_CODE), (String) response.get(ERROR_MSG));
        } else if ((Integer) response.get(RESULT) != 0) {
            logger.info("error code is {} error message is {}", response.get(RESULT), response.get(MESSAGE));
            throw new ThirdPartyAccountException(String.valueOf((Integer) response.get(RESULT)), (String) response.get(MESSAGE));
        }
    }

    private String getVirtualIp(EDOCConfig config, String proxyId) {
        return proxyMetaService.getVirtualIp(TenantHolder.getTenantCode(), proxyId, WebUrlUtil.getHost(config.getHost()));
    }
}
