package com.cyberscraft.uep.account.client;

import com.cyberscraft.uep.common.util.ClassScanUtil;
import org.springframework.context.annotation.ImportSelector;
import org.springframework.core.type.AnnotationMetadata;

import java.util.Set;

/***
 *
 * @date 2021/7/29
 * <AUTHOR>
 ***/
public class AccountClientImportSelector implements ImportSelector {

    @Override
    public String[] selectImports(AnnotationMetadata importingClassMetadata) {
        final Set<String> classes = ClassScanUtil.scan(getClass());
        return classes.toArray(new String[0]);
    }
}
