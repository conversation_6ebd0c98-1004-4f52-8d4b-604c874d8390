package com.cyberscraft.uep.account.client.domain;

import java.util.List;

/**
 * <p>
 *  钉钉应用市场企业授权 事件
 * </p>
 *
 * <AUTHOR>
 * @since 2022/9/19 5:47 下午
 */
public class DingOrgSuiteAuthEventBody extends EventBody {

    private String syncAction;

    private String corpId;

    private String corpName;

    private List<String> adminList;

    public String getSyncAction() {
        return syncAction;
    }

    public void setSyncAction(String syncAction) {
        this.syncAction = syncAction;
    }

    public String getCorpId() {
        return corpId;
    }

    public void setCorpId(String corpId) {
        this.corpId = corpId;
    }

    public String getCorpName() {
        return corpName;
    }

    public void setCorpName(String corpName) {
        this.corpName = corpName;
    }

    public List<String> getAdminList() {
        return adminList;
    }

    public void setAdminList(List<String> adminList) {
        this.adminList = adminList;
    }
}
