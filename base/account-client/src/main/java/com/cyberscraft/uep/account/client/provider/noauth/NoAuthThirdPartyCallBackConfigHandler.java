package com.cyberscraft.uep.account.client.provider.noauth;

import com.cyberscraft.uep.account.client.constant.ThirdPartyAccountType;
import com.cyberscraft.uep.account.client.domain.Connector;
import com.cyberscraft.uep.account.client.domain.SnsConfig;
import com.cyberscraft.uep.account.client.provider.IThirdPartyCallBackConfigHandler;
import com.cyberscraft.uep.account.client.provider.IThirdPartyTenantTransfer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class NoAuthThirdPartyCallBackConfigHandler implements IThirdPartyCallBackConfigHandler {
    protected final static Logger LOG = LoggerFactory.getLogger(NoAuthThirdPartyCallBackConfigHandler.class);

    private IThirdPartyTenantTransfer thirdPartyTenantTransfer;

    public IThirdPartyTenantTransfer getThirdPartyTenantTransfer() {
        return thirdPartyTenantTransfer;
    }

    public void setThirdPartyTenantTransfer(IThirdPartyTenantTransfer thirdPartyTenantTransfer) {
        this.thirdPartyTenantTransfer = thirdPartyTenantTransfer;
    }

    @Override
    public boolean isSupported(String accountType) {
        return ThirdPartyAccountType.NO_AUTH.getCode().equalsIgnoreCase(accountType) || ThirdPartyAccountType.FEISHU.getCode().equalsIgnoreCase(accountType);
    }

    @Override
    public void improveSnsConfig(String tenantId, SnsConfig snsConfig) {

    }

    @Override
    public void registerEventCallBack(String tenantId, SnsConfig snsConfig) {

    }

    @Override
    public void checkAndRegisterEventCallBack(String tenantId, SnsConfig snsConfig) {

    }

    @Override
    public void updateEventCallBack(String tenantId, SnsConfig oldSnsConfig, SnsConfig newSnsConfig) {

    }

    @Override
    public void cleanFiluredCallBackEvent(String tenantId, SnsConfig snsConfig) {

    }

    @Override
    public void removeEventCallBack(String tenantId, SnsConfig snsConfig) {

    }

    @Override
    public void removeEventCallBack(String tenantId, String id, SnsConfig snsConfig) {

    }

    @Override
    public <T> T getEventCallBacks(String tenantId, SnsConfig snsConfig) {
        return null;
    }
}
