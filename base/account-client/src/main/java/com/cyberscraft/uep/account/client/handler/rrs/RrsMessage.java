package com.cyberscraft.uep.account.client.handler.rrs;

import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/9/3 11:36
 * @Version 1.0
 * @Description 睿人事消息
 */
public class RrsMessage implements Serializable {

    private static final long serialVersionUID = -6864072752331912309L;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 企业id
     */
    private String corpId;

    /**
     * 事件类型
     */
    private String eventType;

    /**
     * 事件对应的数据对像，用map存储，一般为通过Json转换而来的对像
     */
    private Map<String, Object> msgInfo;


    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getCorpId() {
        return corpId;
    }

    public void setCorpId(String corpId) {
        this.corpId = corpId;
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public Map<String, Object> getMsgInfo() {
        return msgInfo;
    }

    public void setMsgInfo(Map<String, Object> msgInfo) {
        this.msgInfo = msgInfo;
    }
}
