package com.cyberscraft.uep.account.client.constant;

import com.cyberscraft.uep.common.domain.DataTypeEnum;

import static com.cyberscraft.uep.account.client.constant.AccountConstant.APPUSER_PROFILE_ROOTNAME;
import static com.cyberscraft.uep.account.client.constant.AccountConstant.IDPUSER_PROFILE_ROOTNAME;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/27 3:43 下午
 */
public enum OidcUserAttr implements ExternalAttr{
    sub("sub", DataTypeEnum.STRING, "主题"),
    username("username", DataTypeEnum.STRING, "用户名"),
    phone_number("phone_number", DataTypeEnum.STRING, "手机号"),
    email("email", DataTypeEnum.STRING, "邮箱"),
    ;

    private String attrName;

    private DataTypeEnum dataType;

    private String displayName;

    private String description;

    private Boolean multiValued;

    OidcUserAttr(String attrName, DataTypeEnum dataType, String displayName) {
        this(attrName, dataType, displayName, displayName, false);
    }

    OidcUserAttr(String attrName, DataTypeEnum dataType, String displayName, String description) {
        this(attrName, dataType, displayName, description, false);
    }

    OidcUserAttr(String attrName, DataTypeEnum dataType, String displayName, Boolean multiValued) {
        this(attrName, dataType, displayName, displayName, multiValued);
    }

    OidcUserAttr(String attrName, DataTypeEnum dataType, String displayName, String description, Boolean multiValued) {
        this.attrName = attrName;
        this.dataType = dataType;
        this.displayName = displayName;
        this.description = description;
        this.multiValued = multiValued;
    }

    public String getAttrName() {
        return attrName;
    }

    public DataTypeEnum getDataType() {
        return dataType;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getDescription() {
        return description;
    }

    public Boolean getMultiValued() {
        return multiValued;
    }

    public String getAppNamePath() {
        return APPUSER_PROFILE_ROOTNAME + "." + getAttrName();
    }

    @Override
    public String getIdpNamePath() {
        return IDPUSER_PROFILE_ROOTNAME + "." + getAttrName();
    }
}
