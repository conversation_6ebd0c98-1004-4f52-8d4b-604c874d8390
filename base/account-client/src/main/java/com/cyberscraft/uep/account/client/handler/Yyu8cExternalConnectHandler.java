package com.cyberscraft.uep.account.client.handler;

import com.cyberscraft.uep.account.client.constant.AccountConstant;
import com.cyberscraft.uep.account.client.constant.ConnectorType;
import com.cyberscraft.uep.account.client.constant.Yyu8cOrgAttr;
import com.cyberscraft.uep.account.client.constant.Yyu8cUserAttr;
import com.cyberscraft.uep.account.client.domain.*;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccessTokenInvalidException;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountErrorType;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountException;
import com.cyberscraft.uep.account.client.provider.IExternalConnectHandler;
import com.cyberscraft.uep.account.client.util.AccountUtil;
import com.cyberscraft.uep.common.domain.DataTypeEnum;
import com.cyberscraft.uep.common.domain.ParamSchema;
import com.cyberscraft.uep.common.domain.auth.AuthProtocol;
import com.cyberscraft.uep.common.domain.auth.AuthorizeCredential;
import com.cyberscraft.uep.common.domain.auth.Yyu8cParam;
import com.cyberscraft.uep.common.enums.SysCodeEnum;
import com.cyberscraft.uep.account.client.service.IAuthenticateService;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.common.util.MappingParser;
import com.cyberscraft.uep.common.util.RestAPIUtil;
import com.cyberscraft.uep.common.util.WebUrlUtil;
import com.cyberscraft.uep.proxy.meta.service.IProxyMetaService;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用友U8C连接器实现类
 *
 * <AUTHOR>
 * @date 2023年11月23日
 */
@Service
public class Yyu8cExternalConnectHandler implements IExternalConnectHandler {

    private final static Logger logger = LoggerFactory.getLogger(Yyu8cExternalConnectHandler.class);

    /**
     * 查询组织
     */
    private static final String queryOrgApi = "/u8cloud/api/uapbd/bddept/query";

    /**
     * 新建组织
     */
    private static final String svaeOrgApi = "/u8cloud/api/uapbd/bddept/save";

    /**
     * 修改组织
     */
    private static final String updateOrgApi = "/u8cloud/api/uapbd/bddept/update";

    /**
     * 删除组织
     */
    private static final String deleteOrgApi = "/u8cloud/api/uapbd/bddept/delete";

    /**
     * 查询用户
     */
    private static final String queryUserApi = "/u8cloud/api/uapbd/bdpsnman/query";

    /**
     * 保存、更新 用户
     */
    private static final String saveOrUpdateUserApi = "/u8cloud/api/uapbd/bdpsn/save";

    /**
     * 删除用户
     */
    private static final String deleteUserApi = "/u8cloud/api/uapbd/bdpsn/delete";

    /**
     * 用友U8C请求需要的key
     */
    private static final String TRAN_TYPE_CODE = "code";
    private static final String TRAN_TYPE_PK = "pk";

    @Autowired
    private IAuthenticateService authenticateService;

    @Autowired
    private IProxyMetaService proxyMetaService;

    private final ParamSchema userBasicAttrSchema;

    private final ParamSchema orgBasicAttrSchema;

    public Yyu8cExternalConnectHandler() {
        orgBasicAttrSchema = new ParamSchema(AccountConstant.APPORG_PROFILE_ROOTNAME, DataTypeEnum.OBJECT, "部门profile")
                .addSubParam(Yyu8cOrgAttr.addr.ps())
                .addSubParam(Yyu8cOrgAttr.createDate.ps())
                .addSubParam(Yyu8cOrgAttr.deptattr.ps())
                .addSubParam(Yyu8cOrgAttr.pk_deptdoc.ps())
                .addSubParam(Yyu8cOrgAttr.pk_fathedept.ps())
                .addSubParam(Yyu8cOrgAttr.deptcode.ps())
                .addSubParam(Yyu8cOrgAttr.deptname.ps())
                .addSubParam(Yyu8cOrgAttr.deptshortname.ps())
                .addSubParam(Yyu8cOrgAttr.depttype.ps())
                .addSubParam(Yyu8cOrgAttr.isuseretail.ps())
                .addSubParam(Yyu8cOrgAttr.pk_corp.ps())
                .addSubParam(Yyu8cOrgAttr.unitname.ps())
                .addSubParam(Yyu8cOrgAttr.unitcode.ps())
                .addSubParam(Yyu8cOrgAttr.def1.ps())
                .addSubParam(Yyu8cOrgAttr.def10.ps())
                .addSubParam(Yyu8cOrgAttr.def11.ps())
                .addSubParam(Yyu8cOrgAttr.def12.ps())
                .addSubParam(Yyu8cOrgAttr.def13.ps())
                .addSubParam(Yyu8cOrgAttr.def14.ps())
                .addSubParam(Yyu8cOrgAttr.def15.ps())
                .addSubParam(Yyu8cOrgAttr.def16.ps())
                .addSubParam(Yyu8cOrgAttr.def17.ps())
                .addSubParam(Yyu8cOrgAttr.def18.ps())
                .addSubParam(Yyu8cOrgAttr.def19.ps())
                .addSubParam(Yyu8cOrgAttr.def2.ps())
                .addSubParam(Yyu8cOrgAttr.def20.ps())
                .addSubParam(Yyu8cOrgAttr.def3.ps())
                .addSubParam(Yyu8cOrgAttr.def4.ps())
                .addSubParam(Yyu8cOrgAttr.def5.ps())
                .addSubParam(Yyu8cOrgAttr.def6.ps())
                .addSubParam(Yyu8cOrgAttr.def7.ps())
                .addSubParam(Yyu8cOrgAttr.def8.ps())
                .addSubParam(Yyu8cOrgAttr.def9.ps())
                .addSubParam(Yyu8cOrgAttr.deptduty.ps())
                .addSubParam(Yyu8cOrgAttr.deptlevel.ps())
                .addSubParam(Yyu8cOrgAttr.memo.ps())
                .addSubParam(Yyu8cOrgAttr.phone.ps())
                .addSubParam(Yyu8cOrgAttr.pk_calbody.ps())
                .addSubParam(Yyu8cOrgAttr.pk_psndoc.ps())
                .addSubParam(Yyu8cOrgAttr.pk_psndoc2.ps())
                .addSubParam(Yyu8cOrgAttr.pk_psndoc3.ps())
                .addSubParam(Yyu8cOrgAttr.remcode.ps())
        ;

        userBasicAttrSchema = new ParamSchema(AccountConstant.APPUSER_PROFILE_ROOTNAME, DataTypeEnum.OBJECT, "用户profile")
                .addSubParam(Yyu8cUserAttr.accopendate.ps())
                .addSubParam(Yyu8cUserAttr.account.ps())
                .addSubParam(Yyu8cUserAttr.accountcode.ps())
                .addSubParam(Yyu8cUserAttr.accountname.ps())
                .addSubParam(Yyu8cUserAttr.bankarea.ps())
                .addSubParam(Yyu8cUserAttr.city.ps())
                .addSubParam(Yyu8cUserAttr.combineaccnum.ps())
                .addSubParam(Yyu8cUserAttr.contactpsn.ps())
                .addSubParam(Yyu8cUserAttr.custcode.ps())
                .addSubParam(Yyu8cUserAttr.groupid.ps())
                .addSubParam(Yyu8cUserAttr.memo.ps())
                .addSubParam(Yyu8cUserAttr.netqueryflag.ps())
                .addSubParam(Yyu8cUserAttr.orgnumber.ps())
                .addSubParam(Yyu8cUserAttr.pk_bankdoc.ps())
                .addSubParam(Yyu8cUserAttr.pk_banktype.ps())
                .addSubParam(Yyu8cUserAttr.pk_currtype.ps())
                .addSubParam(Yyu8cUserAttr.pk_netbankinftp.ps())
                .addSubParam(Yyu8cUserAttr.province.ps())
                .addSubParam(Yyu8cUserAttr.remcode.ps())
                .addSubParam(Yyu8cUserAttr.signflag.ps())
                .addSubParam(Yyu8cUserAttr.tel.ps())
                .addSubParam(Yyu8cUserAttr.unitname.ps())
                .addSubParam(Yyu8cUserAttr.isreimburse.ps())
                .addSubParam(Yyu8cUserAttr.pk_psnaccbank.ps())
                .addSubParam(Yyu8cUserAttr.currentcorp.ps())
                .addSubParam(Yyu8cUserAttr.addr.ps())
                .addSubParam(Yyu8cUserAttr.birthdate.ps())
                .addSubParam(Yyu8cUserAttr.bp.ps())
                .addSubParam(Yyu8cUserAttr.email.ps())
                .addSubParam(Yyu8cUserAttr.homephone.ps())
                .addSubParam(Yyu8cUserAttr.id.ps())
                .addSubParam(Yyu8cUserAttr.isassociated.ps())
                .addSubParam(Yyu8cUserAttr.joinworkdate.ps())
                .addSubParam(Yyu8cUserAttr.mobile.ps())
                .addSubParam(Yyu8cUserAttr.officephone.ps())
                .addSubParam(Yyu8cUserAttr.pk_corp.ps())
                .addSubParam(Yyu8cUserAttr.pk_psnbasdoc.ps())
                .addSubParam(Yyu8cUserAttr.postalcode.ps())
                .addSubParam(Yyu8cUserAttr.psnname.ps())
                .addSubParam(Yyu8cUserAttr.sex.ps())
                .addSubParam(Yyu8cUserAttr.ssnum.ps())
                .addSubParam(Yyu8cUserAttr.usedname.ps())
                .addSubParam(Yyu8cUserAttr.vdef1.ps())
                .addSubParam(Yyu8cUserAttr.vdef10.ps())
                .addSubParam(Yyu8cUserAttr.vdef11.ps())
                .addSubParam(Yyu8cUserAttr.vdef12.ps())
                .addSubParam(Yyu8cUserAttr.vdef13.ps())
                .addSubParam(Yyu8cUserAttr.vdef14.ps())
                .addSubParam(Yyu8cUserAttr.vdef15.ps())
                .addSubParam(Yyu8cUserAttr.vdef16.ps())
                .addSubParam(Yyu8cUserAttr.vdef17.ps())
                .addSubParam(Yyu8cUserAttr.vdef18.ps())
                .addSubParam(Yyu8cUserAttr.vdef19.ps())
                .addSubParam(Yyu8cUserAttr.vdef2.ps())
                .addSubParam(Yyu8cUserAttr.vdef20.ps())
                .addSubParam(Yyu8cUserAttr.vdef3.ps())
                .addSubParam(Yyu8cUserAttr.vdef4.ps())
                .addSubParam(Yyu8cUserAttr.vdef5.ps())
                .addSubParam(Yyu8cUserAttr.vdef6.ps())
                .addSubParam(Yyu8cUserAttr.vdef7.ps())
                .addSubParam(Yyu8cUserAttr.vdef8.ps())
                .addSubParam(Yyu8cUserAttr.vdef9.ps())
                .addSubParam(Yyu8cUserAttr.amcode.ps())
                .addSubParam(Yyu8cUserAttr.clerkcode.ps())
                .addSubParam(Yyu8cUserAttr.clerkflag.ps())
                .addSubParam(Yyu8cUserAttr.def1.ps())
                .addSubParam(Yyu8cUserAttr.def10.ps())
                .addSubParam(Yyu8cUserAttr.def11.ps())
                .addSubParam(Yyu8cUserAttr.def12.ps())
                .addSubParam(Yyu8cUserAttr.def13.ps())
                .addSubParam(Yyu8cUserAttr.def14.ps())
                .addSubParam(Yyu8cUserAttr.def15.ps())
                .addSubParam(Yyu8cUserAttr.def16.ps())
                .addSubParam(Yyu8cUserAttr.def17.ps())
                .addSubParam(Yyu8cUserAttr.def18.ps())
                .addSubParam(Yyu8cUserAttr.def19.ps())
                .addSubParam(Yyu8cUserAttr.def2.ps())
                .addSubParam(Yyu8cUserAttr.def20.ps())
                .addSubParam(Yyu8cUserAttr.def3.ps())
                .addSubParam(Yyu8cUserAttr.def4.ps())
                .addSubParam(Yyu8cUserAttr.def5.ps())
                .addSubParam(Yyu8cUserAttr.def6.ps())
                .addSubParam(Yyu8cUserAttr.def7.ps())
                .addSubParam(Yyu8cUserAttr.def8.ps())
                .addSubParam(Yyu8cUserAttr.def9.ps())
                .addSubParam(Yyu8cUserAttr.indutydate.ps())
                .addSubParam(Yyu8cUserAttr.outdutydate.ps())
                .addSubParam(Yyu8cUserAttr.pk_deptdoc.ps())
                .addSubParam(Yyu8cUserAttr.pk_psncl.ps())
                .addSubParam(Yyu8cUserAttr.psncode.ps())
        ;
    }

    @Override
    public AuthorizeCredential getAuthCredential(Connector connector, Boolean refresh) {
        try {
            Yyu8cParam account = convertAccount(connector);
            return authenticateService.auth(AuthProtocol.YYU8C.name(), account, String.valueOf(connector.getId()), refresh);
        } catch (Exception e) {
            throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), e.getMessage());
        }
    }

    @Override
    public boolean isSupported(ConnectorType connectorType) {
        return ConnectorType.YYU8C == connectorType;
    }

    @Override
    public ParamSchema getExternalOrgBasicAttrSchema(Connector connector) {
        return orgBasicAttrSchema;
    }

    @Override
    public ParamSchema getExternalUserBasicAttrSchema(Connector connector) {
        return userBasicAttrSchema;
    }

    @Override
    public ConnectorOrgProfile getExternalOrgFullProfile(Connector connector) {
        ParamSchema orgAllProfile = buildExternalOrgFullSchema(connector);

        ConnectorOrgProfile connectorOrgProfile = new ConnectorOrgProfile();
        connectorOrgProfile.setIdName(Yyu8cOrgAttr.pk_deptdoc.getAttrName());
        connectorOrgProfile.setParentIdName(Yyu8cOrgAttr.pk_fathedept.getAttrName());
        connectorOrgProfile.setNameName(Yyu8cOrgAttr.deptname.getAttrName());
        connectorOrgProfile.setAttrSchema(orgAllProfile);

        return connectorOrgProfile;
    }

    @Override
    public ConnectorUserProfile getExternalUserFullProfile(Connector connector) {
        ParamSchema userAllProfile = buildExternalUserFullSchema(connector);

        ConnectorUserProfile connectorUserProfile = new ConnectorUserProfile();
        connectorUserProfile.setIdName(Yyu8cUserAttr.psncode.getAttrName());
        connectorUserProfile.setMobileName(Yyu8cUserAttr.mobile.getAttrName());
        connectorUserProfile.setAttrSchema(userAllProfile);

        return connectorUserProfile;
    }

    @Override
    public ConnectorOrg<String, Object> getRootExternalOrg(Connector connector) throws ThirdPartyAccountException {
        Map<String, Object> reqBody = new HashMap<>();
        // 部门档案主键
        reqBody.put("unitcode", connector.getRootCode());
        reqBody.put("page_now", "1");
        reqBody.put("page_size", "1");
        Map<String, Object> response = sendPostRequest(reqBody, queryOrgApi, connector, TRAN_TYPE_PK);
        List<Map<String, Object>> dataList = getDataList(response);
        ConnectorOrg<String, Object> data = dataList == null ? null : AccountUtil.to(dataList.get(0), getExternalOrgFullProfile(connector));
        // 将公司属性转为部门
        Map<String, Object> result = new HashMap<>();
        result.put(Yyu8cOrgAttr.pk_deptdoc.getAttrName(), data.getValue(Yyu8cOrgAttr.pk_corp));
        result.put(Yyu8cOrgAttr.deptname.getAttrName(), data.getValue(Yyu8cOrgAttr.unitname));
        return AccountUtil.to(result, getExternalOrgFullProfile(connector));
    }

    @Override
    public List<ConnectorOrg<String, Object>> getAllSubExternalOrgs(ConnectorOrg parentExternalOrg, Connector connector) throws ThirdPartyAccountException {
        return null;
    }

    @Override
    public List<ConnectorOrg<String, Object>> getSubExternalOrgs(String parentOrgId, Connector connector) throws ThirdPartyAccountException {
        return null;
    }

    @Override
    public List<ConnectorOrg<String, Object>> getExternalOrgByIds(List<String> externalOrgIds, Connector connector) throws ThirdPartyAccountException {
        return null;
    }

    @Override
    public ConnectorOrg getExternalOrgById(String externalOrgId, Connector connector) throws ThirdPartyAccountException {
        Map<String, Object> reqBody = new HashMap<>();
        // 部门档案主键
        reqBody.put("id", externalOrgId);
        reqBody.put("unitcode", connector.getRootCode());
        reqBody.put("page_now", "1");
        reqBody.put("page_size", "10");
        Map<String, Object> response = sendPostRequest(reqBody, queryOrgApi, connector, TRAN_TYPE_PK);
        List<Map<String, Object>> dataList = getDataList(response);
        return dataList == null ? null : AccountUtil.to(dataList.get(0), getExternalOrgFullProfile(connector));
    }

    @Override
    public boolean isSortedByOrgLevel() {
        return false;
    }

    @Override
    public List<ConnectorUser<String, Object>> getExternalUsersByOrg(ConnectorOrg externalOrg, Connector connector) throws ThirdPartyAccountException {
        return null;
    }

    @Override
    public List<ConnectorUser<String, Object>> getExternalUsers(List<String> externalUserIds, Connector connector) throws ThirdPartyAccountException {
        return null;
    }

    @Override
    public Boolean limitListExternalUsers(int page, int pageSize, Connector connector, List<ConnectorUser<String, Object>> output) throws ThirdPartyAccountException {
        return null;
    }

    @Override
    public ConnectorUser getExternalUserById(String externalAccountId, Connector connector) throws ThirdPartyAccountException {
        Map<String, Object> reqBody = new HashMap<>();
        // 人员编码
        reqBody.put("psncode", externalAccountId);
        reqBody.put("corpcode", connector.getRootCode());
        reqBody.put("page_now", "1");
        reqBody.put("page_size", "10");
        Map<String, Object> response = sendPostRequest(reqBody, queryUserApi, connector, TRAN_TYPE_PK);
        List<Map<String, Object>> dataList = getDataList(response);
        if (dataList == null) {
            return null;
        }

        Map<String, Object> resp = dataList.get(0);
        // 处理响应参数格式与本地一致
        Map<String, Object> parentvo = new HashMap<>();
        parentvo.put("psnbasvo", resp.get("psnbasvo"));
        parentvo.put("psnmanvo", resp.get("psnmanvo"));
        parentvo.put("currentcorp", resp.get("currentcorp"));

        Map<String, Object> psn = new HashMap<>();
        psn.put("parentvo", parentvo);
        psn.put("childrenvo", resp.get("childrenvo"));
        return AccountUtil.to(psn, getExternalUserFullProfile(connector));
    }

    @Override
    public int login(Connector connector, String username, String password) {
        return 0;
    }

    @Override
    public ConnectorUser test(Connector connector) {
        return null;
    }

    @Override
    public void closeConnection(Connector connector) {

    }

    @Override
    public String createExternalOrg(ConnectorOrg externalOrg, Connector connector) throws ThirdPartyAccountException {
        // 判断父组代码是否存在
        Object parentIdValue = externalOrg.getParentIdValue();
        if (StringUtils.isBlank(parentIdValue == null ? null : parentIdValue.toString())) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.PARENT_GROUP_CODE_INVALID);
        }
        String rootCode = connector.getRootCode();
        // 父部门为公司编码则删除父部门
        if (externalOrg.getValue(Yyu8cOrgAttr.pk_fathedept).equals(rootCode)) {
            externalOrg.remove(Yyu8cOrgAttr.pk_fathedept.getAttrName());
        }

        // 判断公司编码是否为空，为空填写配置中的值
        if (ObjectUtils.isEmpty(externalOrg.getValue(Yyu8cOrgAttr.pk_corp))) {
            externalOrg.put(Yyu8cOrgAttr.pk_corp.getAttrName(), rootCode);
        }

        // 拼装请求参数
        Map<String, Object> reqBody = new HashMap<>();
        List<Map<String, Object>> deptdoc = new ArrayList<>();
        deptdoc.add(externalOrg);
        reqBody.put("deptdoc", deptdoc);

        Map<String, Object> response = sendPostRequest(reqBody, svaeOrgApi, connector, TRAN_TYPE_PK);
        Map<String, Object> resp = getDataMap(response);
        return resp.get(Yyu8cOrgAttr.pk_deptdoc.getAttrName()).toString();
    }

    @Override
    public String modifyExternalOrg(String externalOrgId, ConnectorOrg externalOrg, Connector connector) throws ThirdPartyAccountException {
        // 父部门为公司编码则删除父部门
        if (externalOrg.getValue(Yyu8cOrgAttr.pk_corp).equals(externalOrg.getParentIdValue())) {
            externalOrg.remove(Yyu8cOrgAttr.pk_fathedept.getAttrName());
        }
        // 拼装请求参数 依据主键修改
        Map<String, Object> reqBody = new HashMap<>();
        List<Map<String, Object>> deptdoc = new ArrayList<>();
        deptdoc.add(externalOrg);
        reqBody.put("deptdoc", deptdoc);
        sendPostRequest(reqBody, updateOrgApi, connector, TRAN_TYPE_PK);
        return externalOrgId;
    }

    @Override
    public void deleteExternalOrg(String externalOrgId, Connector connector) throws ThirdPartyAccountException {
        ConnectorOrg connectorOrg = getExternalOrgById(externalOrgId, connector);
        if (connectorOrg == null) {
            return;
        }
        Map<String, Object> reqBody = new HashMap<>();
        // 删除依据部门档案主键和部门编码
        reqBody.put("id", externalOrgId);
        reqBody.put("code", connectorOrg.get("deptcode"));
        reqBody.put(Yyu8cOrgAttr.unitcode.getAttrName(), connector.getRootCode());
        sendPostRequest(reqBody, deleteOrgApi, connector, TRAN_TYPE_PK);
    }

    @Override
    public void deleteExternalOrgs(List<String> externalOrgIds, Connector connector) throws ThirdPartyAccountException {
        for (String externalOrgId : externalOrgIds) {
            deleteExternalOrg(externalOrgId, connector);
        }
    }

    @Override
    public String createExternalUser(ConnectorUser externalUser, Connector connector) throws ThirdPartyAccountException {
        // 检验公司编码是否为空
        Object currentcorp = MappingParser.getValue(Yyu8cUserAttr.currentcorp.getAttrName(), externalUser);
        if (currentcorp == null) {
            MappingParser.putValue(externalUser, Yyu8cUserAttr.currentcorp.getAttrName(), connector.getRootCode());

        }
        Map<String, Object> reqBody = buildSaveOrUpdateUserRequestBody(externalUser);
        sendPostRequest(reqBody, saveOrUpdateUserApi, connector, TRAN_TYPE_PK);
        String psncode = MappingParser.getValue(Yyu8cUserAttr.psncode.getAttrName(), externalUser).toString();
        externalUser.setIdValue(psncode);
        return psncode;
    }


    @Override
    public String modifyExternalUser(String externalUserId, ConnectorUser externalUser, Connector connector) throws ThirdPartyAccountException {
        // pk_psnbasdoc=null 新增； pk_psnbasdoc!=null 修改
        Map<String, Object> reqBody = buildSaveOrUpdateUserRequestBody(externalUser);
        sendPostRequest(reqBody, saveOrUpdateUserApi, connector, TRAN_TYPE_PK);
        return externalUserId;
    }

    @Override
    public void deleteExternalUser(String externalUserId, Connector connector) throws ThirdPartyAccountException {
        String unitcode = connector.getRootCode();
        Map<String, Object> reqBody = new HashMap<>();
        reqBody.put("psncode", externalUserId);
        reqBody.put("corpcode", unitcode);
        sendPostRequest(reqBody, deleteUserApi, connector, TRAN_TYPE_CODE);
    }

    @Override
    public void deleteExternalUsers(List<String> externalUserIds, Connector connector) throws ThirdPartyAccountException {
        for (String externalUserId : externalUserIds) {
            deleteExternalUser(externalUserId, connector);
        }
    }

    /**
     * 组装新建或修改用户的请求体
     *
     * @param externalUser 外部用户参数
     * @return 新建或修改用户的请求体
     */
    private Map<String, Object> buildSaveOrUpdateUserRequestBody(ConnectorUser externalUser) {
        List<Map<String, Object>> psnList = new ArrayList<>();
        psnList.add(externalUser);
        Map<String, Object> reqBody = new HashMap<>();
        reqBody.put("psn", psnList);
        return reqBody;
    }


    /**
     * 发送post请求
     *
     * @param reqBody   外部信息
     * @param api       接口
     * @param connector 连接器
     * @param type      判断类型 通过此字段告知接口，档案的名称/编码/主键，如果是主键请录入PK，是名称请录入name，是编码请录入code
     * @return 请求结果
     */
    private Map<String, Object> sendPostRequest(Map<String, Object> reqBody, String api, Connector connector, String type) {
        // 鉴权
        AuthorizeCredential authCredential = getAuthCredential(connector, false);
        // 拼接url
        Yyu8cParam account = convertAccount(connector);
        String url = WebUrlUtil.getWebServerUrl(account.getHost(), api);
        // 组装请求头
        Map<String, String> head = authCredential.getHead();
        // 外系统可以传入名称/编码/主键，可以为空，为空，接口自动取【系统信息设置】节点下的【档案翻译方式】字段，不为空，通过此字段告知接口，你传入的是档案的名称/编码/主键，如果是主键请录入PK，是名称请录入name，是编码请录入code。
        head.put("trantype", type);
        Map<String, Object> response;
        try {
            response = RestAPIUtil.postForEntity(account.getProxyIp(), url, reqBody, head);
            checkResponse(response, url);
        } catch (ThirdPartyAccessTokenInvalidException e) {
            response = RestAPIUtil.postForEntity(account.getProxyIp(), url, reqBody, head);
            checkResponse(response, url);
        } catch (Exception e) {
            logger.error("YYU8C send post request err api {} msg {}", api, e.getMessage());
            throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), e.getMessage());
        }
        return response;
    }

    /**
     * 校验请求结果
     *
     * @param response 响应参数
     * @param url      请求地址
     */
    private void checkResponse(Map<String, Object> response, String url) {
        try {
            // 用友U8C接口限制1秒不得超过5次
            Thread.sleep(200L);
        } catch (Exception e) {
            logger.error("sleep error", e);
        }

        String status = String.valueOf(response.get("status"));
        if (!"success".equalsIgnoreCase(status)) {
            String errorCode = String.valueOf(response.get("errorcode"));
            String errorMsg = String.valueOf(response.get("errormsg"));
            logger.error("YYU8C response error:{}, {}, {}, url: {}", errorCode, errorMsg, response, url);
            if (StringUtils.isNotBlank(errorCode)) {
                throw new ThirdPartyAccountException(errorCode, errorMsg);
            } else {
                throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.SERVER_RESPONSE_INVALID.getCode(), "调用-用友U8C接口错误，服务器未正确返回结果");
            }
        }

        if (logger.isDebugEnabled()) {
            logger.debug("YYU8C url:{}, body:{}", url, response);
        }
    }

    /**
     * 获取业务参数
     *
     * @param response 响应参数
     * @return 业务参数
     */
    private Map getDataMap(Map<String, Object> response) {
        Object dataObj = response.get("data");
        if (ObjectUtils.isEmpty(dataObj)) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.SERVER_RESPONSE_INVALID.getCode(), response.get("taskNumber") + "返回的data为空");
        }
        String respStr = dataObj.toString();
        List<Map> respList = JsonUtil.str2List(respStr, Map.class);
        return respList.get(0);
    }

    /**
     * 获取参数集合
     *
     * @param response 响应参数
     * @return 业务参数集合
     */
    private List<Map<String, Object>> getDataList(Map<String, Object> response) {
        Object dataObj = response.get("data");
        Map<String, Object> data = JsonUtil.str2Map(dataObj.toString());

        if (ObjectUtils.isEmpty(data)) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.SERVER_RESPONSE_INVALID.getCode(), response.get("taskNumber") + "返回的data为空");
        }
        Object allCount = data.get("allcount");
        if (allCount == null) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.SERVER_RESPONSE_INVALID.getCode(), response.get("taskNumber") + "返回的allcount为空");
        }
        int count = Integer.parseInt(allCount.toString());
        if (count == 0) {
            return null;
        }

        Object datas = data.get("datas");
        if (ObjectUtils.isEmpty(datas)) {
            return null;
        }
        return (List<Map<String, Object>>) datas;
    }

    private Yyu8cParam convertAccount(Connector connector) {
        Yyu8cParam account = JsonUtil.str2Obj(connector.getConfig(), Yyu8cParam.class);
        String virtualIp = proxyMetaService.getVirtualIp(TenantHolder.getTenantCode(), String.valueOf(connector.getProxyId()), account.getRealIp());
        account.setProxyIp(virtualIp);
        return account;
    }

}
