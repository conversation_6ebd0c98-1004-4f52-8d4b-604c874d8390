package com.cyberscraft.uep.account.client.handler.rrs;

import com.cyberscraft.uep.account.client.constant.ThirdPartyAccountType;
import com.cyberscraft.uep.account.client.constant.ThirdPartyEventTagConstant;
import com.cyberscraft.uep.account.client.domain.Connector;
import com.cyberscraft.uep.account.client.domain.EventBody;
import com.cyberscraft.uep.account.client.domain.ThirdPartyEvent;
import com.cyberscraft.uep.account.client.domain.UserEventBody;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2024/9/3 11:36
 * @Version 1.0
 * @Description 睿人事消息
 */
@Component
public class RrsUserAddUpdateEventParser implements IRrsEventParser {

    @Override
    public Set<String> getSupportedMessageType() {
        return new HashSet<>(Arrays.asList("emp_onboarding", "emp_personal_info_modify", "emp_work_info_add", "emp_work_info_modify", "emp_work_info_delete", "ptj_add", "ptj_modify", "ptj_delete", "ptj_end", "emp_leave"));
    }

    @Override
    public ThirdPartyEvent<? extends EventBody> parse(RrsMessage msg, String tenantId, Connector connector) {
        ThirdPartyEvent<UserEventBody> event = new ThirdPartyEvent<>();
        event.setEventTag(ThirdPartyEventTagConstant.USER_ADD_UPDATE);
        event.setTenantId(tenantId);
        event.setAccountType(ThirdPartyAccountType.RRS.getCode());

        UserEventBody eventBody = new UserEventBody();
        Map<String, Object> data = msg.getMsgInfo();
        eventBody.setUserIds(Arrays.asList((String) data.get("empId")));
        eventBody.setConnector(connector);
        event.setData(eventBody);
        return event;
    }
}
