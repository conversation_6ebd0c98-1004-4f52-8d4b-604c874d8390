package com.cyberscraft.uep.account.client.domain;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/***
 *  系统中指定的连接器信息
 * @date 2021/3/5
 * <AUTHOR>
 ***/
public class Connector implements Serializable {

    /***
     * 连接器的ID
     */
    private Long id;

    /**
     * 同步的事件ID
     */
    private Long taskId;

    /**
     * 连接方向：0-SYNC，1-PUSH
     */
    private Integer direction;

    /**
     * 连接器类型
     */
    private Integer type;

    /**
     * 数据源名称
     */
    private String name;

    /**
     * 导入用户冲突解决策略
     */
    private Integer conflictPolicy;

    /**
     * 用户同步周期,  单位：小时
     */
    private Integer importPeriod;

    /**
     * 是否强制写入
     */
    private Integer forceWrite;

    /**
     * 外部根组织编码
     */
    private String rootCode;

    /**
     * 用户名转换策略
     */
    private Integer usernameFormat;


    /**
     * 是否同步用户组
     */
    private Integer importOrgs;
    /**
     * 是否推送用户组
     */
    private Integer pushOrgs;

    /**
     * 集成角色组类型，0不集成，1按角色组集成，2按角色集成
     */
    private Integer importRolesType;

    /**
     * 集成外部角色/角色组
     */
    private List<String> roles;

    /**
     * 连接器状态
     */
    private Integer status;

    /**
     * 上次同步的批次号
     */
    private Integer syncBatchNo;

    /**
     * 连接器认证相关配置信息，json格式存储
     * 适用于存储AD/LDAP连接器目标数据源的配置信息
     */
    private String config;

    /**
     * 扩展的config
     */
    private String extraConfig;

    /**
     * 一次同步时删除用户数量限制
     */
    private Integer deleteLimit;

    /**
     * 删除用户方式：常规删除 0，用户停用 1
     */
    private Integer deleteMethod = 0;

    /**
     * 计划开始集成时间
     */
    private LocalDateTime plannedStartTime;

    /**
     * 企业配置信息主键ID
     * 适用于存储企业微信/钉钉等sns数据源配置信息的关联库表ID
     */
    private Long configId;

    /**
     * 测试帐号对应的属性path
     */
    private String testPath;

    /**
     * 测试账号
     */
    private String testAccount;

    /**
     * 是否创建同名连接器组
     */
    private Integer createConnectorOrg;
    /**
     * 同名连接器组ID
     */
    private Long connectorOrgRefId;

    /***
     * 用户属性对应的属性Map
     */
    private ConnectorUserMapping userAttrMapping;

    /****
     * 组织结构对应的属性Map
     */
    private ConnectorOrgMapping orgAttrMapping;
    /**
     * 角色对应的属性Map
     */
    private ConnectorRoleMapping roleAttrMapping;

    /**
     * 用户属性映射更新时间
     */
    private LocalDateTime mappingUpdateTime;

    /**
     * 同步连接器用户属性映射表达式是否包含appuser参数
     */
    private boolean containAppUserArg = false;

    /**
     * 同步连接器部门属性映射表达式是否包含apporg参数
     */
    private boolean containAppOrgArg = false;

    /**
     * 帐号关联
     */
    private List<AccountLink> accountLink;

    /**
     * 是否强制同步
     */
    private boolean isFullSync = false;

    /**
     * connector对象初始化时的时间
     */
    private LocalDateTime initTime;

    /**
     * 网关代理ID
     */
    private Long proxyId;

    /**
     * 筛选配置json格式，用于筛选符合条件的对象
     */
    private String filterConfig;

    /**
     * 是否开启过滤
     */
    private Integer enableFilter = 0;

    /**
     * 是否按追加的方式更新部门
     */
    private Boolean appendOrgs = true;

    /**
     * 是否推送角色
     */
    private boolean isPushRole = false;

    private boolean flow = false;


    /**
     * 是否开启身份属性验证 0:关闭, 1:开启
     */
    private boolean trustEnable;

    /**
     * 待验证身份属性配置
     */
    private List<String> trustConfig;

    /**
     * 租户ID
     */
    private String tenantId;

    public Connector() {
        this.initTime = LocalDateTime.now();
    }

    public LocalDateTime getInitTime() {
        return initTime;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public Integer getDirection() {
        return direction;
    }

    public void setDirection(Integer direction) {
        this.direction = direction;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getConflictPolicy() {
        return conflictPolicy;
    }

    public void setConflictPolicy(Integer conflictPolicy) {
        this.conflictPolicy = conflictPolicy;
    }

    public Integer getImportPeriod() {
        return importPeriod;
    }

    public void setImportPeriod(Integer importPeriod) {
        this.importPeriod = importPeriod;
    }

    public Integer getForceWrite() {
        return forceWrite;
    }

    public void setForceWrite(Integer forceWrite) {
        this.forceWrite = forceWrite;
    }

    public Integer getUsernameFormat() {
        return usernameFormat;
    }

    public void setUsernameFormat(Integer usernameFormat) {
        this.usernameFormat = usernameFormat;
    }


    public Integer getImportOrgs() {
        return importOrgs;
    }

    public void setImportOrgs(Integer importOrgs) {
        this.importOrgs = importOrgs;
    }

    public Integer getPushOrgs() {
        return pushOrgs;
    }

    public void setPushOrgs(Integer pushOrgs) {
        this.pushOrgs = pushOrgs;
    }

    public Integer getImportRolesType() {
        return importRolesType;
    }

    public void setImportRolesType(Integer importRolesType) {
        this.importRolesType = importRolesType;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getSyncBatchNo() {
        return syncBatchNo;
    }

    public void setSyncBatchNo(Integer syncBatchNo) {
        this.syncBatchNo = syncBatchNo;
    }

    public LocalDateTime getPlannedStartTime() {
        return plannedStartTime;
    }

    public void setPlannedStartTime(LocalDateTime plannedStartTime) {
        this.plannedStartTime = plannedStartTime;
    }

    public String getConfig() {
        return config;
    }

    public Long getConfigId() {
        return configId;
    }

    public void setConfigId(Long configId) {
        this.configId = configId;
    }

    public void setConfig(String config) {
        this.config = config;
    }

    public String getExtraConfig() {
        return extraConfig;
    }

    public void setExtraConfig(String extraConfig) {
        this.extraConfig = extraConfig;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getTestAccount() {
        return testAccount;
    }

    public void setTestAccount(String testAccount) {
        this.testAccount = testAccount;
    }

    public String getTestPath() {
        return testPath;
    }

    public void setTestPath(String testPath) {
        this.testPath = testPath;
    }

    public Long getConnectorOrgRefId() {
        return connectorOrgRefId;
    }

    public void setConnectorOrgRefId(Long connectorOrgRefId) {
        this.connectorOrgRefId = connectorOrgRefId;
    }

    public String getRootCode() {
        return rootCode;
    }

    public void setRootCode(String rootCode) {
        this.rootCode = rootCode;
    }

    public List<String> getRoles() {
        return roles;
    }

    public void setRoles(List<String> roles) {
        this.roles = roles;
    }

    public ConnectorUserMapping getUserAttrMapping() {
        return userAttrMapping;
    }

    public void setUserAttrMapping(ConnectorUserMapping userAttrMapping) {
        this.userAttrMapping = userAttrMapping;
    }

    public ConnectorOrgMapping getOrgAttrMapping() {
        return orgAttrMapping;
    }

    public void setOrgAttrMapping(ConnectorOrgMapping orgAttrMapping) {
        this.orgAttrMapping = orgAttrMapping;
    }

    public ConnectorRoleMapping getRoleAttrMapping() {
        return roleAttrMapping;
    }

    public void setRoleAttrMapping(ConnectorRoleMapping roleAttrMapping) {
        this.roleAttrMapping = roleAttrMapping;
    }

    public LocalDateTime getMappingUpdateTime() {
        return mappingUpdateTime;
    }

    public void setMappingUpdateTime(LocalDateTime mappingUpdateTime) {
        this.mappingUpdateTime = mappingUpdateTime;
    }

    public boolean isContainAppUserArg() {
        return containAppUserArg;
    }

    public void setContainAppUserArg(boolean containAppUserArg) {
        this.containAppUserArg = containAppUserArg;
    }

    public boolean isContainAppOrgArg() {
        return containAppOrgArg;
    }

    public void setContainAppOrgArg(boolean containAppOrgArg) {
        this.containAppOrgArg = containAppOrgArg;
    }

    public Integer getCreateConnectorOrg() {
        return createConnectorOrg;
    }

    public void setCreateConnectorOrg(Integer createConnectorOrg) {
        this.createConnectorOrg = createConnectorOrg;
    }

    public Integer getDeleteLimit() {
        return deleteLimit;
    }

    public void setDeleteLimit(Integer deleteLimit) {
        this.deleteLimit = deleteLimit;
    }

    public Integer getDeleteMethod() {
        return deleteMethod;
    }

    public void setDeleteMethod(Integer deleteMethod) {
        this.deleteMethod = deleteMethod;
    }

    public List<AccountLink> getAccountLink() {
        return accountLink;
    }

    public void setAccountLink(List<AccountLink> accountLink) {
        this.accountLink = accountLink;
    }

    public boolean isFullSync() {
        return isFullSync;
    }

    public void setFullSync(boolean fullSync) {
        isFullSync = fullSync;
    }

    public Long getProxyId() {
        return proxyId;
    }

    public void setProxyId(Long proxyId) {
        this.proxyId = proxyId;
    }

    public String getFilterConfig() {
        return filterConfig;
    }

    public void setFilterConfig(String filterConfig) {
        this.filterConfig = filterConfig;
    }

    public Integer getEnableFilter() {
        return enableFilter;
    }

    public void setEnableFilter(Integer enableFilter) {
        this.enableFilter = enableFilter;
    }

    public Boolean getAppendOrgs() {
        return appendOrgs;
    }

    public boolean isPushRole() {
        return isPushRole;
    }

    public void setPushRole(boolean pushRole) {
        isPushRole = pushRole;
    }

    public boolean isFlow() {
        return flow;
    }

    public void setFlow(boolean flow) {
        this.flow = flow;
    }

//    public void setAppendOrgs(Boolean appendOrgs) {
//        this.appendOrgs = appendOrgs;
//    }


    public boolean isTrustEnable() {
        return trustEnable;
    }

    public void setTrustEnable(boolean trustEnable) {
        this.trustEnable = trustEnable;
    }

    public List<String> getTrustConfig() {
        return trustConfig;
    }

    public void setTrustConfig(List<String> trustConfig) {
        this.trustConfig = trustConfig;
    }

    @Override
    public String toString() {
        return "Connector{" +
                "id=" + id +
                ", type=" + type +
                ", name='" + name + '\'' +
                ", conflictPolicy=" + conflictPolicy +
                ", importPeriod=" + importPeriod +
                ", usernameFormat=" + usernameFormat +
                ", importOrgs=" + importOrgs +
                ", status=" + status +
                ", syncBatchNo=" + syncBatchNo +
                ", config='" + config + '\'' +
                ", extraConfig='" + extraConfig + '\'' +
                ", deleteLimit=" + deleteLimit +
                ", deleteMethod=" + deleteMethod +
                ", configId=" + configId +
                ", testPath='" + testPath + '\'' +
                ", testAccount='" + testAccount + '\'' +
                ", createConnectorOrg=" + createConnectorOrg +
                ", connectorOrgRefId=" + connectorOrgRefId +
                ", userAttrMapping=" + userAttrMapping +
                ", orgAttrMapping=" + orgAttrMapping +
                ", accountLink=" + accountLink +
                ", isFullSync=" + isFullSync +
                ", initTime=" + initTime +
                ", tenantId='" + tenantId + '\'' +
                '}';
    }
}
