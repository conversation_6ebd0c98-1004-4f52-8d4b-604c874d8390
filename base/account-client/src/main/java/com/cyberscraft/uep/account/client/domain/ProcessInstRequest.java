package com.cyberscraft.uep.account.client.domain;

import com.cyberscraft.uep.account.client.constant.ProcessFlowType;

import java.util.Map;

/**
 * <p>
 *  创建审批流程实例请求对象
 * </p>
 *
 * <AUTHOR>
 * @since 2022/6/16 4:42 下午
 */
public class ProcessInstRequest {

    /**
     * 审批流类型
     */
    private ProcessFlowType flowType;

    /**
     * 审批实例发起人的userid
     */
    private String userId;

    /**
     * 发起人所在部门id
     */
    private String deptId;

    /**
     * 审批表单数据
     */
    private Map<String,FormFieldValue> formData;

    public ProcessFlowType getFlowType() {
        return flowType;
    }

    public void setFlowType(ProcessFlowType flowType) {
        this.flowType = flowType;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getDeptId() {
        return deptId;
    }

    public void setDeptId(String deptId) {
        this.deptId = deptId;
    }

    public Map<String, FormFieldValue> getFormData() {
        return formData;
    }

    public void setFormData(Map<String, FormFieldValue> formData) {
        this.formData = formData;
    }
}
