package com.cyberscraft.uep.account.client.constant;

import com.cyberscraft.uep.common.domain.DataTypeEnum;
import com.cyberscraft.uep.common.domain.MutableEnum;

import static com.cyberscraft.uep.account.client.constant.AccountConstant.APPUSER_PROFILE_ROOTNAME;
import static com.cyberscraft.uep.account.client.constant.AccountConstant.IDPUSER_PROFILE_ROOTNAME;

/**
 * @Author：xhquan
 * @Project：uep-services
 * @since：2023/12/19 11:04
 */
public enum YonBipUserAttr implements ExternalAttr {

    billtype("billtype", DataTypeEnum.STRING, "单据类型", "单据类型"),
    isexchange("isexchange", DataTypeEnum.STRING, "Y", "Y"),
    replace("replace", DataTypeEnum.STRING, "Y", "Y"),
    sender("sender", DataTypeEnum.STRING, "发送方编码", "发送方编码"),
    birthdate("bill.billhead.birthdate", DataTypeEnum.STRING, "出生日期", "出生日期"),
    id("bill.id", DataTypeEnum.STRING, "Id", "Id"),
    code("bill.billhead.code",new String[]{"user.user_code"}, DataTypeEnum.STRING, "人员编码（唯一属性）", "人员编码（唯一属性确认之后就不能修改）"),
    email("bill.billhead.email", DataTypeEnum.STRING, "电子邮件", "电子邮件"),
    enablestate("bill.billhead.enablestate", DataTypeEnum.STRING, "启用状态", "启用状态"),
    firstname("bill.billhead.firstname", DataTypeEnum.STRING, "名", "名"),
    homephone("bill.billhead.homephone", DataTypeEnum.STRING, "家庭电话", "家庭电话"),
    billId("bill.billhead.id", DataTypeEnum.STRING, "证件号", "证件号"),
    idtype("bill.billhead.idtype", DataTypeEnum.STRING, "证件类型", "证件类型"),
    joinworkdate("bill.billhead.joinworkdate", DataTypeEnum.STRING, "参加工作日期", "参加工作日期"),
    lastname("bill.billhead.lastname", DataTypeEnum.STRING, "姓", "姓"),
    mnecode("bill.billhead.mnecode", DataTypeEnum.STRING, "助记码", "助记码"),
    mobile("bill.billhead.mobile", DataTypeEnum.STRING, "手机", "手机"),
    name("bill.billhead.name",new String[]{"user.user_name"}, DataTypeEnum.STRING, "姓名", "姓名"),
    nickname("bill.billhead.nickname", DataTypeEnum.STRING, "昵称", "昵称"),
    officephone("bill.billhead.officephone", DataTypeEnum.STRING, "办公电话", "办公电话"),
    pk_group("bill.billhead.pk_group", DataTypeEnum.STRING, "所属集团", "所属集团"),
    pk_org("bill.billhead.pk_org", DataTypeEnum.STRING, "所属业务单元", "所属业务单元"),
    sex("bill.billhead.sex", DataTypeEnum.STRING, "性别", "性别"),
    usedname("bill.billhead.usedname", DataTypeEnum.STRING, "曾用名", "曾用名"),
    def1("bill.billhead.def1", DataTypeEnum.STRING, "自定义项1", "自定义项1"),
    def2("bill.billhead.def2", DataTypeEnum.STRING, "自定义项2", "自定义项2"),
    def3("bill.billhead.def3", DataTypeEnum.STRING, "自定义项3", "自定义项3"),
    def4("bill.billhead.def4", DataTypeEnum.STRING, "自定义项4", "自定义项4"),
    def5("bill.billhead.def5", DataTypeEnum.STRING, "自定义项5", "自定义项5"),
    def6("bill.billhead.def6", DataTypeEnum.STRING, "自定义项6", "自定义项6"),
    def7("bill.billhead.def7", DataTypeEnum.STRING, "自定义项7", "自定义项7"),
    def8("bill.billhead.def8", DataTypeEnum.STRING, "自定义项8", "自定义项8"),
    def9("bill.billhead.def9", DataTypeEnum.STRING, "自定义项9", "自定义项9"),
    def10("bill.billhead.def10", DataTypeEnum.STRING, "自定义项10", "自定义项10"),
    def11("bill.billhead.def11", DataTypeEnum.STRING, "自定义项11", "自定义项11"),
    def12("bill.billhead.def12", DataTypeEnum.STRING, "自定义项12", "自定义项12"),
    def13("bill.billhead.def13", DataTypeEnum.STRING, "自定义项13", "自定义项13"),
    def14("bill.billhead.def14", DataTypeEnum.STRING, "自定义项14", "自定义项14"),
    def15("bill.billhead.def15", DataTypeEnum.STRING, "自定义项15", "自定义项15"),
    def16("bill.billhead.def16", DataTypeEnum.STRING, "自定义项16", "自定义项16"),
    def17("bill.billhead.def17", DataTypeEnum.STRING, "自定义项17", "自定义项17"),
    def18("bill.billhead.def18", DataTypeEnum.STRING, "自定义项18", "自定义项18"),
    def19("bill.billhead.def19", DataTypeEnum.STRING, "自定义项19", "自定义项19"),
    def20("bill.billhead.def20", DataTypeEnum.STRING, "自定义项20", "自定义项20"),
    city("bill.billhead.addressvo.city", DataTypeEnum.STRING, "城市", "城市"),
    addressCode("bill.billhead.addressvo.code", DataTypeEnum.STRING, "家庭地址编码", "家庭地址编码"),
    country("bill.billhead.addressvo.country", DataTypeEnum.STRING, "国家", "国家"),
    detailinfo("bill.billhead.addressvo.detailinfo", DataTypeEnum.STRING, "地址详情", "地址详情"),
    postcode("bill.billhead.addressvo.postcode", DataTypeEnum.STRING, "邮政编码", "邮政编码"),
    province("bill.billhead.addressvo.province", DataTypeEnum.STRING, "省份", "省份"),
    vsection("bill.billhead.addressvo.vsection", DataTypeEnum.STRING, "县区", "县区"),
    psnjobs("bill.billhead.psnjobs.item", DataTypeEnum.OBJECT, "工作信息", "工作信息", true),
    //    indutydate("bill.billhead.psnjobs.indutydate", DataTypeEnum.STRING, "任职开始日期"),
//    ismainjob("bill.billhead.psnjobs.ismainjob", DataTypeEnum.BOOLEAN, "主职"),
//    jobname("bill.billhead.psnjobs.jobname", DataTypeEnum.STRING, "职务称谓"),
//    pk_dept("bill.billhead.psnjobs.pk_dept", DataTypeEnum.STRING, "所在部门"),
//    jobPkGroup("bill.billhead.psnjobs.pk_group", DataTypeEnum.STRING, "所属集团"),
//    pk_job("bill.billhead.psnjobs.pk_job", DataTypeEnum.STRING, "职务"),
//    jobPkOrg("bill.billhead.psnjobs.pk_org", DataTypeEnum.STRING, "任职业务单元"),
//    pk_post("bill.billhead.psnjobs.pk_post", DataTypeEnum.STRING, "岗位"),
//    pk_psncl("bill.billhead.psnjobs.pk_psncl", DataTypeEnum.STRING, "人员类别"),
//    psncode("bill.billhead.psnjobs.psncode", DataTypeEnum.STRING, "职务"),
//    showorder("bill.billhead.psnjobs.showorder", DataTypeEnum.STRING, "人员顺序"),
    addr("bill.billhead.addr", DataTypeEnum.STRING, "家庭住址主键", "家庭住址主键"),
    account("account", DataTypeEnum.STRING, "帐套", "帐套"),
    groupcode("groupcode", DataTypeEnum.STRING, "集团编码", "集团编码"),
    user_password("user.user_password", DataTypeEnum.STRING, "用户密码", "用户密码"),
    pwdlevelcode("user.pwdlevelcode", DataTypeEnum.STRING, "密码安全级别", "密码安全级别"),
    pwdparam("user.pwdparam", DataTypeEnum.STRING, "密码参数", "密码参数"),
    user_note("user.user_note", DataTypeEnum.STRING, "备注", "备注"),
    abledate("user.abledate", DataTypeEnum.STRING, "生效日期", "生效日期"),
    disabledate("user.disabledate", DataTypeEnum.STRING, "失效日期", "失效日期"),
    islocked("user.islocked", DataTypeEnum.STRING, "是否锁定", "用户密码"),
    base_doc_type("user.base_doc_type", DataTypeEnum.STRING, "身份类型", "身份类型"),
    user_type("user.user_type", DataTypeEnum.STRING, "用户类型", "用户类型"),
    pk_base_doc("user.pk_base_doc", DataTypeEnum.STRING, "身份", "身份"),
    identityverifycode("user.identityverifycode", DataTypeEnum.STRING, "认证类型", "认证类型"),
    format("user.format", DataTypeEnum.STRING, "数据格式", "数据格式"),
    isca("user.isca", DataTypeEnum.STRING, "CA用户", "CA用户"),
    contentlang("user.contentlang", DataTypeEnum.STRING, "语言信息", "语言信息"),
    user_code_q("user.user_code_q", DataTypeEnum.STRING, "查询编码", "查询编码"),
    pk_usergroupforcreate("user.pk_usergroupforcreate", DataTypeEnum.STRING, "所属用户组", "所属用户组"),
    filename("filename", DataTypeEnum.STRING, "文件名", "文件名"),
    roottag("roottag", DataTypeEnum.STRING, "跟标签", "跟标签");

    YonBipUserAttr(String attrName, DataTypeEnum dataType, String displayName, String desc) {
        this.attrName = attrName;
        this.dataType = dataType;
        this.displayName = displayName;
        this.desc = desc;
        this.multiValued = false;
    }

    YonBipUserAttr(String attrName, String[] alias, DataTypeEnum dataType, String displayName, String desc) {
        this.attrName = attrName;
        this.dataType = dataType;
        this.alias = alias;
        this.displayName = displayName;
        this.desc = desc;
        this.multiValued = false;
    }

    YonBipUserAttr(String attrName, DataTypeEnum dataType, String displayName, String desc, Boolean multiValued) {
        this.attrName = attrName;
        this.dataType = dataType;
        this.displayName = displayName;
        this.desc = desc;
        this.multiValued = multiValued;
    }

    private String attrName;

    private String[] alias;

    private DataTypeEnum dataType;

    private String displayName;

    private String desc;

    private Boolean multiValued;

    private MutableEnum mutability;

    @Override
    public String getAttrName() {
        return this.attrName;
    }

    @Override
    public String[] getAlias() {
        return this.alias;
    }


    @Override
    public DataTypeEnum getDataType() {
        return this.dataType;
    }

    @Override
    public String getDisplayName() {
        return this.displayName;
    }

    @Override
    public String getDescription() {
        return this.desc;
    }

    @Override
    public Boolean getMultiValued() {
        return this.multiValued;
    }

    @Override
    public String getAppNamePath() {
        return APPUSER_PROFILE_ROOTNAME + "." + getAttrName();
    }

    @Override
    public String getIdpNamePath() {
        return IDPUSER_PROFILE_ROOTNAME + "." + getAttrName();
    }
}
