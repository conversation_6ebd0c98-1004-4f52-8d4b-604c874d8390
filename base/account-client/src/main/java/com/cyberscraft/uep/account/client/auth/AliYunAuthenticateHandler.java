package com.cyberscraft.uep.account.client.auth;

import com.cyberscraft.uep.common.config.AliYunConfig;
import com.cyberscraft.uep.common.domain.auth.Account;
import com.cyberscraft.uep.common.domain.auth.AccountParam;
import com.cyberscraft.uep.common.domain.auth.AuthProtocol;
import com.cyberscraft.uep.common.domain.auth.AuthorizeCredential;
import com.cyberscraft.uep.common.exception.ApiConnectException;
import com.cyberscraft.uep.common.util.CollectionUtils;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.common.util.MappingParser;
import com.cyberscraft.uep.common.util.WebUrlUtil;
import jodd.net.HttpMethod;
import org.springframework.stereotype.Service;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.xml.bind.DatatypeConverter;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2024/10/30 11:37
 * @Version 1.0
 * @Description 阿里服务鉴权
 */
@Service
public class AliYunAuthenticateHandler implements IAuthenticateHandler {

    /**
     * 签名协议
     */
    private static final String ALGORITHM = "ACS3-HMAC-SHA256";

    /**
     * 日期格式化工具，用于将日期时间字符串格式化为"yyyy-MM-dd'T'HH:mm:ss'Z'"的格式。
     */
    private static final SimpleDateFormat SDF = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");

    @Override
    public boolean isSupported(String type) {
        return AuthProtocol.ALIYUN.name().equals(type);
    }

    @Override
    public AuthorizeCredential auth(Account account, String cacheKey, Boolean refresh) {
        return null;
    }


    @Override
    public Account getAccount(AccountParam accountParam, Map<String, Object> queryParam, Map<String, Object> headerParam) {
        return MappingParser.schema2Obj(new HashMap<>(), accountParam.getAccount(), AliYunConfig.class);
    }

    @Override
    public Object sign(Account account, Map<String, Object> queryParam, Map<String, Object> headerParam, Object body, Map<String, Object> context) {
        AliYunConfig param = (AliYunConfig) account;
        String endpoint = context.get("endpoint").toString();
        String httpMethod = context.get("httpMethod").toString().toUpperCase(Locale.ROOT);
        TreeMap<String, Object> headers = buildHeaders(endpoint, headerParam);
        TreeMap<String, Object> query = new TreeMap<>(queryParam);
        // RPC请求:/ ；ROA请求：全路径path
        String canonicalUri = WebUrlUtil.getPath(endpoint);
        if (!"/".equals(canonicalUri) && HttpMethod.GET.equalsName(httpMethod)) {
            queryParam.put("with_addon_resources", true);
        }
        Map<String, Object> bodyMap = (Map<String, Object>) body;
        String bodyStr = CollectionUtils.isEmpty(bodyMap) ? null : JsonUtil.obj2Str(bodyMap);
        getAuthorization(param, httpMethod, canonicalUri, headers, query, bodyStr);
        headerParam.putAll(headers);
        queryParam.putAll(query);
        return body;
    }

//
//    public static void main(String[] args) {
//        HashMap<String, Object> headers = new HashMap<>();
//        headers.put("x-acs-action", "SendSms");
//        headers.put("x-acs-version", "2017-05-25");
//        TreeMap<String, Object> headers1 = buildHeaders("https://dysmsapi.aliyuncs.com/", headers);
//        TreeMap<String, Object> query = new TreeMap<>();
//        query.put("PhoneNumbers", "13835389896");
//        query.put("SignName", "数犀科技");
//        query.put("TemplateCode", "SMS_472285100");
//        query.put("TemplateParam", "{\"code\":\"123456\"}");
//        AliYunConfig aliYunConfig = new AliYunConfig();
//        aliYunConfig.setKey("LTAI5tAoKD9whjyXo78Jj8ZE");
//        aliYunConfig.setSecret("******************************");
//        getAuthorization(aliYunConfig, "GET", "/", headers1, query, "");
//        RestApiResponse forEntityString = RestAPIUtil.getForEntityString(null, "https://dysmsapi.aliyuncs.com/", headers1, query, null);
//        System.out.println(forEntityString.getBody());
//    }


    private static TreeMap<String, Object> buildHeaders(String endpoint, Map<String, Object> headerParam) {
        TreeMap<String, Object> headers = new TreeMap<>();
        SDF.setTimeZone(new SimpleTimeZone(0, "GMT")); // 设置日期格式化时区为GMT
        headers.put("host", WebUrlUtil.getHost(endpoint));
        headers.put("x-acs-date", SDF.format(new Date()));
        headers.put("x-acs-signature-nonce", UUID.randomUUID().toString());
        headers.putAll(headerParam);
        return headers;
    }


    /**
     * 该方法用于根据传入的HTTP请求方法、规范化的URI、查询参数等，计算并生成授权信息。
     */
    private static void getAuthorization(AliYunConfig param, String httpMethod, String canonicalUri, TreeMap<String, Object> headers, TreeMap<String, Object> queryParam, String body) {
        try {
            // 步骤 1：拼接规范请求串
            // 请求参数，当请求的查询字符串为空时，使用空字符串作为规范化查询字符串
            StringBuilder canonicalQueryString = new StringBuilder();
            queryParam.entrySet().stream().map(entry -> percentCode(entry.getKey()) + "=" + percentCode(String.valueOf(entry.getValue()))).forEachOrdered(queryPart -> {
                // 如果canonicalQueryString已经不是空的，则在查询参数前添加"&"
                if (canonicalQueryString.length() > 0) {
                    canonicalQueryString.append("&");
                }
                canonicalQueryString.append(queryPart);
            });

            // 请求体，当请求正文为空时，比如GET请求，RequestPayload固定为空字符串
            String requestPayload = "";
            if (body != null) {
                requestPayload = body;
            }

            // 计算请求体的哈希值
            String hashedRequestPayload = sha256Hex(requestPayload);
            headers.put("x-acs-content-sha256", hashedRequestPayload);
            // 构造请求头，多个规范化消息头，按照消息头名称（小写）的字符代码顺序以升序排列后拼接在一起
            StringBuilder canonicalHeaders = new StringBuilder();
            // 已签名消息头列表，多个请求头名称（小写）按首字母升序排列并以英文分号（;）分隔
            StringBuilder signedHeadersSb = new StringBuilder();
            headers.entrySet().stream().filter(entry -> entry.getKey().toLowerCase().startsWith("x-acs-") || entry.getKey().equalsIgnoreCase("host") || entry.getKey().equalsIgnoreCase("content-type")).sorted(Map.Entry.comparingByKey()).forEach(entry -> {
                String lowerKey = entry.getKey().toLowerCase();
                String value = String.valueOf(entry.getValue()).trim();
                canonicalHeaders.append(lowerKey).append(":").append(value).append("\n");
                signedHeadersSb.append(lowerKey).append(";");
            });
            String signedHeaders = signedHeadersSb.substring(0, signedHeadersSb.length() - 1);
            String canonicalRequest = httpMethod + "\n" + canonicalUri + "\n" + canonicalQueryString + "\n" + canonicalHeaders + "\n" + signedHeaders + "\n" + hashedRequestPayload;

            // 步骤 2：拼接待签名字符串
            String hashedCanonicalRequest = sha256Hex(canonicalRequest);
            String stringToSign = ALGORITHM + "\n" + hashedCanonicalRequest;

            // 步骤 3：计算签名
            String signature = DatatypeConverter.printHexBinary(hmac256(param.getSecret().getBytes(StandardCharsets.UTF_8), stringToSign)).toLowerCase();

            // 步骤 4：拼接 Authorization
            String authorization = ALGORITHM + " " + "Credential=" + param.getKey() + ",SignedHeaders=" + signedHeaders + ",Signature=" + signature;
            headers.put("Authorization", authorization);
        } catch (Exception e) {
            // 异常处理
            throw new ApiConnectException("阿里云签名生成失败" + e.getMessage() + "]");
        }
    }

    /**
     * 使用HmacSHA256算法生成消息认证码（MAC）。
     *
     * @param key 密钥，用于生成MAC的密钥，必须保密。
     * @param str 需要进行MAC认证的消息。
     * @return 返回使用HmacSHA256算法计算出的消息认证码。
     * @throws Exception 如果初始化MAC或计算MAC过程中遇到错误，则抛出异常。
     */
    public static byte[] hmac256(byte[] key, String str) throws Exception {
        // 实例化HmacSHA256消息认证码生成器
        Mac mac = Mac.getInstance("HmacSHA256");
        // 创建密钥规范，用于初始化MAC生成器
        SecretKeySpec secretKeySpec = new SecretKeySpec(key, mac.getAlgorithm());
        // 初始化MAC生成器
        mac.init(secretKeySpec);
        // 计算消息认证码并返回
        return mac.doFinal(str.getBytes(StandardCharsets.UTF_8));
    }


    /**
     * 使用SHA-256算法计算字符串的哈希值并以十六进制字符串形式返回。
     *
     * @param str 需要进行SHA-256哈希计算的字符串。
     * @return 计算结果为小写十六进制字符串。
     * @throws Exception 如果在获取SHA-256消息摘要实例时发生错误。
     */
    private static String sha256Hex(String str) throws Exception {
        // 获取SHA-256消息摘要实例
        MessageDigest md = MessageDigest.getInstance("SHA-256");
        // 计算字符串s的SHA-256哈希值
        byte[] d = md.digest(str.getBytes(StandardCharsets.UTF_8));
        // 将哈希值转换为小写十六进制字符串并返回
        return DatatypeConverter.printHexBinary(d).toLowerCase();
    }

    /**
     * 对指定的字符串进行URL编码。
     * 使用UTF-8编码字符集对字符串进行编码，并对特定的字符进行替换，以符合URL编码规范。
     *
     * @param str 需要进行URL编码的字符串。
     * @return 编码后的字符串。其中，加号"+"被替换为"%20"，星号"*"被替换为"%2A"，波浪号"%7E"被替换为"~"。
     */
    private static String percentCode(String str) {
        if (str == null) {
            throw new IllegalArgumentException("输入字符串不可为null");
        }
        try {
            return URLEncoder.encode(str, "UTF-8").replace("+", "%20").replace("*", "%2A").replace("%7E", "~");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException("UTF-8编码不被支持", e);
        }
    }

}
