package com.cyberscraft.uep.account.client.auth;

import com.cyberscraft.uep.common.domain.auth.Account;
import com.cyberscraft.uep.common.domain.auth.AccountParam;
import com.cyberscraft.uep.common.domain.auth.AuthorizeCredential;
import com.cyberscraft.uep.common.domain.auth.OpenAiAccount;
import com.cyberscraft.uep.common.util.MappingParser;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/3/27 2:34 下午
 */
@Service
public class OpenAiAuthenticateHandler implements IAuthenticateHandler{
    @Override
    public boolean isSupported(String type) {
        return "OPENAI".equalsIgnoreCase(type);
    }

    @Override
    public AuthorizeCredential auth(Account account, String cacheKey, Boolean refresh) {
        return null;
    }

    @Override
    public Account getAccount(AccountParam accountParam, Map<String, Object> queryParam, Map<String, Object> headerParam) {
        return MappingParser.schema2Obj(new HashMap<>(), accountParam.getAccount(), OpenAiAccount.class);
    }
}
