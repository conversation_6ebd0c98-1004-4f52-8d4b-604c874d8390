package com.cyberscraft.uep.account.client.service;


import com.cyberscraft.uep.common.bean.RestApiResponse;
import com.cyberscraft.uep.common.domain.HttpMethod;
import com.cyberscraft.uep.common.domain.ResponseEncoding;
import com.cyberscraft.uep.common.domain.ResponseType;
import com.cyberscraft.uep.common.domain.auth.Account;
import com.cyberscraft.uep.common.domain.auth.AccountParam;
import com.cyberscraft.uep.common.domain.auth.AuthorizeCredential;
import com.cyberscraft.uep.common.exception.UnauthorizedException;

import java.util.Map;

/**
 * <p>
 * 认证service
 * </p>
 *
 * <AUTHOR>
 * @since 2022/9/13 3:08 下午
 */
public interface IAuthenticateService {

    /**
     * 认证
     *
     * @param protocol 认证方式
     * @param account  账号信息
     * @param cacheKey 缓存key
     * @param refresh  强制刷新
     * @return
     */
    AuthorizeCredential auth(String protocol, Account account, String cacheKey, Boolean refresh);

    /**
     * 清除缓存
     *
     * @param protocol
     * @param cacheKey
     */
    void evictCache(String protocol, String cacheKey);

    /**
     * 将accountParam转成Account
     *
     * @param accountParam
     * @return
     */
    Account getAccount(AccountParam accountParam, Map<String, Object> query, Map<String, Object> header);

    /**
     * 解析accountParam中的host ip
     * @param accountParam
     * @return
     */
    String parseHost(AccountParam accountParam);

    /**
     * 对参数进行签名或加密
     *
     * @param protocol
     * @param account
     * @param queryParam
     * @param headerParam
     * @param body
     * @param context
     * @return
     */
    Object sign(String protocol, Account account, Map<String, Object> queryParam, Map<String, Object> headerParam, Object body, Map<String, Object> context);

    /**
     * 对获取的凭据进行检查，对非法的凭据，要求抛出401异常
     *
     * @param protocol
     * @param response
     * @return
     */
    RestApiResponse checkCredential(String protocol, RestApiResponse response) throws UnauthorizedException;

    /**
     * 通用请求
     * @param endpoint
     * @param httpMethod
     * @param accountParam
     * @param queryParam
     * @param headerParam
     * @param params
     * @return
     */
    RestApiResponse request(String endpoint, HttpMethod httpMethod, AccountParam accountParam, Map<String, Object> queryParam, Map<String, Object> headerParam, Object params);

    /**
     * 通用请求
     * @param encoding
     * @param proxyIp
     * @param endpoint
     * @param httpMethod
     * @param accountParam
     * @param responseType
     * @param queryParam
     * @param headerParam
     * @param params
     * @param pathParam
     * @return
     */
    RestApiResponse request(ResponseEncoding encoding, String proxyIp, String endpoint, HttpMethod httpMethod, AccountParam accountParam, ResponseType responseType, Map<String, Object> queryParam, Map<String, Object> headerParam, Object params, Map<String, Object> pathParam);
}
