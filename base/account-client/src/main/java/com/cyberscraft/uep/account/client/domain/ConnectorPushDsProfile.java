package com.cyberscraft.uep.account.client.domain;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/***
 * 链接源数据画像对像
 * @date 2021/4/7
 * <AUTHOR>
 ***/
public class ConnectorPushDsProfile implements Serializable {

    /***
     * 对应的账户类型
     */
    private String accountType;

    /***
     * 是否导入用户组
     */
    private Boolean pushOrgs;

    /***
     * 连接器周期执行时间间隔
     */
    private Integer pushPeriod = 0;

    /***
     * 映射字段
     */
    private Map<String, String> userAttrs;

    /***
     * 组织结构映射字段
     */
    private Map<String, String> orgAttrs;

    /***
     * 第三方平台必填字段
     */
    private List<String> orgFields;

    /***
     * 第三方平台必填字段
     */
    private List<String> userFields;

    public String getAccountType() {
        return accountType;
    }

    public void setAccountType(String accountType) {
        this.accountType = accountType;
    }

    public Boolean getPushOrgs() {
        return pushOrgs;
    }

    public void setPushOrgs(Boolean pushOrgs) {
        this.pushOrgs = pushOrgs;
    }

    public Integer getPushPeriod() {
        return pushPeriod;
    }

    public void setPushPeriod(Integer pushPeriod) {
        this.pushPeriod = pushPeriod;
    }

    public Map<String, String> getUserAttrs() {
        return userAttrs;
    }

    public void setUserAttrs(Map<String, String> userAttrs) {
        this.userAttrs = userAttrs;
    }

    public Map<String, String> getOrgAttrs() {
        return orgAttrs;
    }

    public void setOrgAttrs(Map<String, String> orgAttrs) {
        this.orgAttrs = orgAttrs;
    }

    public List<String> getOrgFields() {
        return orgFields;
    }

    public void setOrgFields(List<String> orgFields) {
        this.orgFields = orgFields;
    }

    public List<String> getUserFields() {
        return userFields;
    }

    public void setUserFields(List<String> userFields) {
        this.userFields = userFields;
    }
}
