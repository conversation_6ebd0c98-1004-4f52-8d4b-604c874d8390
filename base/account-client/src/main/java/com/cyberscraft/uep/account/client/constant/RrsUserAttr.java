package com.cyberscraft.uep.account.client.constant;

import com.cyberscraft.uep.common.domain.DataTypeEnum;
import com.cyberscraft.uep.common.domain.MutableEnum;

import static com.cyberscraft.uep.account.client.constant.AccountConstant.*;

/**
 * <p>
 * 睿人事用户属性枚举
 * </p>
 *
 * <AUTHOR>
 * @since 2023/6/21 1:29 下午
 */
public enum RrsUserAttr implements ExternalAttr {
    empId("empId", DataTypeEnum.STRING, "用户ID", MutableEnum.readonly),
    effectDate("effectDate", DataTypeEnum.STRING, "生效日期", MutableEnum.readonly),

    mainDepartment("mainDepartment", DataTypeEnum.STRING, "主部门"),
    mainDepartment_source("mainDepartment_source", DataTypeEnum.OBJECT, "主部门信息", true),
    ptjDepartment_source("ptjDepartment_source", DataTypeEnum.OBJECT, "兼职部门信息"),
    department("department", DataTypeEnum.STRING, "部门", true),
    jobDuty("jobDuty", DataTypeEnum.STRING, "职务"),
    jobSequence("jobSequence", DataTypeEnum.STRING, "序列"),
    jobTitle("jobTitle", DataTypeEnum.STRING, "职位"),
    jobLevel("jobLevel", DataTypeEnum.STRING, "职级"),
    jobGrade("jobGrade", DataTypeEnum.STRING, "职等"),
    manager("manager", DataTypeEnum.STRING, "直属主管"),
    employeeStatus("employeeStatus", DataTypeEnum.STRING, "员工状态"),

    employeeType("employeeType", DataTypeEnum.STRING, "员工类型"),
    onBoardingDate("onBoardingDate", DataTypeEnum.STRING, "入职日期"),
    probationDate("probationDate", DataTypeEnum.STRING, "转正日期"),
    lastDayOfWork("lastDayOfWork", DataTypeEnum.STRING, "离职日期"),
    reasonForOffBoarding("reasonForOffBoarding", DataTypeEnum.STRING, "离职原因"),

    employeeNumber("employeeNumber", DataTypeEnum.STRING, "工号"),
    name("name", DataTypeEnum.STRING, "姓名"),
    gender("gender", DataTypeEnum.STRING, "性别"),
    idType("idType", DataTypeEnum.STRING, "证件类型"),
    idNumber("idNumber", DataTypeEnum.STRING, "证件号码"),
    dateOfBirth("dateOfBirth", DataTypeEnum.STRING, "出生日期"),
    mobileNumber("mobileNumber", DataTypeEnum.STRING, "手机号码"),
    personalEmail("personalEmail", DataTypeEnum.STRING, "个人邮箱"),

    email("email", DataTypeEnum.STRING, "邮箱"),
    syncDing("syncDing", DataTypeEnum.BOOLEAN, "是否同步钉钉通讯录"),

    extEmpId("field_6dcc86489c67437ab9b37bbb28b8ea9a", DataTypeEnum.STRING, "来源人员ID"),
    extTel("field_415e1b1797a34b7ab39a3b20c6d1f764", DataTypeEnum.NUMBER, "分机号"),
    extEntEmail("field_dc06053bb16149d9aa347e4264a2922b", DataTypeEnum.STRING, "员工的企业邮箱"),
    extAccountName("field_342c2294fad54b8cb6f0fc8866747c1c", DataTypeEnum.STRING, "账户名称"),
    extAdAccount("field_7551a9ba83864605981e7e06373b4fe2", DataTypeEnum.STRING, "AD账号"),
    extIsDingAdmin("field_c980ae11a0ac4fe18b3b20597bf754d1", DataTypeEnum.BOOLEAN, "是否为企业的管理员（钉钉）"),

    extIsActiveDing("field_57290fe67995465d992e66da0e093f00", DataTypeEnum.STRING, "是否激活了钉钉"),
    extInSys("field_768dac5191db4fb397c66100853a621d", DataTypeEnum.STRING, "所属系统"),
    extFromSys("field_0a36bdeedb9b4e988b743f4c6e32bf94", DataTypeEnum.STRING, "系统来源"),

    extDeptId("field_2c93724d8783478a9456353759e42924", DataTypeEnum.STRING, "来源部门ID", true),
    extRrsEmpId("field_539b3bb7f1664d43b6fbdf113bf96552", DataTypeEnum.STRING, "来源部门睿人事ID"),

    extJobLevel("field_f0673dbf4ea24d3dba2234079736fb07", DataTypeEnum.STRING, "来源职层"),
    extJobName("field_85c6bd475c34416b8a199239d7d33f73", DataTypeEnum.STRING, "来源岗位名称"),
    extJobCode("field_2f55f8ecbfaa4b5da2a12c9870b719b4", DataTypeEnum.STRING, "来源岗位编码"),

    extJobSequence("field_a53abbfd181e41519ceafc202bbb4c47", DataTypeEnum.STRING, "来源岗位序列"),
    extJobTitle("field_da046e3a744845c19f0b591d003f31e8", DataTypeEnum.STRING, "来源标准职衔"),
    extManager("field_f640561f50a3497f9beba086e7764479", DataTypeEnum.STRING, "来源直接上级人员ID"),
    extRrsManager("field_ab5fe509bb3a4866a35d160837acb735", DataTypeEnum.STRING, "来源直接上级人员睿人事ID"),

    extFinCompany("field_8ffe1c46609e420c9da8a32fa99129c6", DataTypeEnum.STRING, "财务费用划分公司"),
    extIsConvert("field_6aa691a1585442f591b9967e66fe7b9e", DataTypeEnum.STRING, "是否转档"),

    extPostStatus("field_3e9b9451a84a4c90bbe6bb30d2d5a5be", DataTypeEnum.STRING, "在职状态"),
    extPsnType("field_bf4ac369795b4db397e6c5276ac993e0", DataTypeEnum.STRING, "人员类别"),

    extEntryDate("field_84e74b3b1b314636923ceaf6ab76d8bd", DataTypeEnum.STRING, "来源入职日期"),
    extIsRegular("field_bdd193b60a8e44b9a15c2726001fc89d", DataTypeEnum.STRING, "是否转正"),
    extPsnSys("field_2597471cff42400da187cabff591d60d", DataTypeEnum.STRING, "任职信息系统来源"),
    managerSource("manager_source", DataTypeEnum.OBJECT, "直属主管信息", true),
    ;

    private String attrName;

    private DataTypeEnum dataType;

    private String displayName;

    private String description;

    private Boolean multiValued;

    private MutableEnum mutability;

    RrsUserAttr(String attrName, DataTypeEnum dataType, String displayName) {
        this(attrName, dataType, displayName, displayName, false);
    }

    RrsUserAttr(String attrName, DataTypeEnum dataType, String displayName, Boolean multiValued) {
        this(attrName, dataType, displayName, displayName, multiValued);
    }

    RrsUserAttr(String attrName, DataTypeEnum dataType, String displayName, MutableEnum mutability) {
        this(attrName, dataType, displayName, displayName, false, mutability);
    }

    RrsUserAttr(String attrName, DataTypeEnum dataType, String displayName, String description, Boolean multiValued) {
        this(attrName, dataType, displayName, description, multiValued, MutableEnum.readWrite);
    }

    RrsUserAttr(String attrName, DataTypeEnum dataType, String displayName, String description, Boolean multiValued, MutableEnum mutability) {
        this.attrName = attrName;
        this.dataType = dataType;
        this.displayName = displayName;
        this.description = description;
        this.multiValued = multiValued;
        this.mutability = mutability;
    }

    @Override
    public String getAttrName() {
        return attrName;
    }

    @Override
    public DataTypeEnum getDataType() {
        return dataType;
    }

    @Override
    public String getDisplayName() {
        return displayName;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public Boolean getMultiValued() {
        return multiValued;
    }

    @Override
    public MutableEnum getMutability() {
        return mutability;
    }

    @Override
    public String getAppNamePath() {
        return APPUSER_PROFILE_ROOTNAME + "." + getAttrName();
    }

    @Override
    public String getIdpNamePath() {
        return IDPUSER_PROFILE_ROOTNAME + "." + getAttrName();
    }
}
