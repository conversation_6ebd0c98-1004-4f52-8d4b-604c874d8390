package com.cyberscraft.uep.account.client.handler;

import com.cyberscraft.uep.account.client.constant.AccountConstant;
import com.cyberscraft.uep.account.client.constant.JitConnectType;
import com.cyberscraft.uep.account.client.constant.SamlUserAttr;
import com.cyberscraft.uep.account.client.domain.ConnectorUserProfile;
import com.cyberscraft.uep.account.client.provider.IJitConnectHandler;
import com.cyberscraft.uep.common.domain.DataTypeEnum;
import com.cyberscraft.uep.common.domain.ParamSchema;
import org.springframework.stereotype.Service;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/9/11 11:28 上午
 */
@Service
public class SamlJitConnectHandler implements IJitConnectHandler {

    private final ParamSchema userBasicAttrSchema;

    public SamlJitConnectHandler() {
        userBasicAttrSchema = new ParamSchema(AccountConstant.IDPUSER_PROFILE_ROOTNAME, DataTypeEnum.OBJECT, "用户profile")
                .addSubParam(SamlUserAttr.sub.ps());
    }


    @Override
    public boolean isSupported(JitConnectType jitConnectType) {
        return JitConnectType.SAML == jitConnectType
                || JitConnectType.GOOGLE_SAML == jitConnectType
                || JitConnectType.ADFS_SAML == jitConnectType;
    }

    @Override
    public ParamSchema getJitUserBasicAttrSchema(JitConnectType jitConnectType) {
        return userBasicAttrSchema;
    }

    @Override
    public ConnectorUserProfile getJitUserFullProfile(ParamSchema jitExtensionSchema, JitConnectType jitConnectType) {
        ParamSchema userAllProfile = buildJitUserFullSchema(jitExtensionSchema, jitConnectType);

        ConnectorUserProfile connectorUserProfile = new ConnectorUserProfile();
        connectorUserProfile.setIdName(SamlUserAttr.sub.getAttrName());
        connectorUserProfile.setAttrSchema(userAllProfile);

        return connectorUserProfile;
    }
}
