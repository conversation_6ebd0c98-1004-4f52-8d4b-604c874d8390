package com.cyberscraft.uep.account.client.constant;

import com.cyberscraft.uep.common.domain.DataTypeEnum;

import static com.cyberscraft.uep.account.client.constant.AccountConstant.APPUSER_PROFILE_ROOTNAME;
import static com.cyberscraft.uep.account.client.constant.AccountConstant.IDPUSER_PROFILE_ROOTNAME;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/26 9:43 下午
 */
public enum ScimUserAttr implements ExternalAttr{
    external_id("external_id", DataTypeEnum.STRING, "外部用户ID"),
    user_name("user_name", DataTypeEnum.STRING, "用户名"),
    display_name("display_name", DataTypeEnum.STRING, "姓名"),
    nick_name("nick_name", DataTypeEnum.STRING, "昵称"),
    password("password", DataTypeEnum.STRING, "密码"),
    phone_number("phone_number", DataTypeEnum.STRING, "手机号"),
    user_extension("user_extension", DataTypeEnum.OBJECT, "用户扩展属性"),
    email("email", DataTypeEnum.STRING, "邮箱"),
    ;

    private String attrName;

    private DataTypeEnum dataType;

    private String displayName;

    private String description;

    private Boolean multiValued;

    ScimUserAttr(String attrName, DataTypeEnum dataType, String displayName) {
        this(attrName, dataType, displayName, displayName, false);
    }

    ScimUserAttr(String attrName, DataTypeEnum dataType, String displayName, String description) {
        this(attrName, dataType, displayName, description, false);
    }

    ScimUserAttr(String attrName, DataTypeEnum dataType, String displayName, Boolean multiValued) {
        this(attrName, dataType, displayName, displayName, multiValued);
    }

    ScimUserAttr(String attrName, DataTypeEnum dataType, String displayName, String description, Boolean multiValued) {
        this.attrName = attrName;
        this.dataType = dataType;
        this.displayName = displayName;
        this.description = description;
        this.multiValued = multiValued;
    }

    public String getAttrName() {
        return attrName;
    }

    public DataTypeEnum getDataType() {
        return dataType;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getDescription() {
        return description;
    }

    public Boolean getMultiValued() {
        return multiValued;
    }

    public String getAppNamePath() {
        return APPUSER_PROFILE_ROOTNAME + "." + getAttrName();
    }

    @Override
    public String getIdpNamePath() {
        return IDPUSER_PROFILE_ROOTNAME + "." + getAttrName();
    }
}
