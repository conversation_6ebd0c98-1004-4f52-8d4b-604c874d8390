package com.cyberscraft.uep.account.client.domain.account;

import com.cyberscraft.uep.common.domain.auth.Account;

import java.io.Serializable;

/**
 * @description: 微信公众号认证账号
 * @author:liusen
 * @date:2024/1/26
 */
public class WechatOfficialAccount extends Account implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 回调地址
     */
    private String callBackUrl;
    /**
     * 应用唯一标识，在微信开放平台提交应用审核通过后获得
     */
    private String appId;
    /**
     * 应用密钥AppSecret
     */
    private String AppSecret;


    private String canRegistor;

    /**
     * 公众号校验文件名称
     */
    private String verifyFileName;

    /**
     * 公众号校验文件内容
     */
    private String verifyFileContent;


    public String getCallBackUrl() {
        return callBackUrl;
    }

    public void setCallBackUrl(String callBackUrl) {
        this.callBackUrl = callBackUrl;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getAppSecret() {
        return AppSecret;
    }

    public void setAppSecret(String appSecret) {
        AppSecret = appSecret;
    }

    public String getCanRegistor() {
        return canRegistor;
    }

    public void setCanRegistor(String canRegistor) {
        this.canRegistor = canRegistor;
    }

    public String getVerifyFileName() {
        return verifyFileName;
    }

    public void setVerifyFileName(String verifyFileName) {
        this.verifyFileName = verifyFileName;
    }

    public String getVerifyFileContent() {
        return verifyFileContent;
    }

    public void setVerifyFileContent(String verifyFileContent) {
        this.verifyFileContent = verifyFileContent;
    }

    @Override
    public String getRealIp() {
        return null;
    }
}
