package com.cyberscraft.uep.account.client.provider.noauth;

import com.cyberscraft.uep.account.client.constant.ThirdPartyAccountType;
import com.cyberscraft.uep.account.client.domain.Connector;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountException;
import com.cyberscraft.uep.account.client.provider.IThirdPartyFailedCallBackEventHandler;
import org.springframework.stereotype.Component;

/***
 *
 * @date 2021/3/26
 * <AUTHOR>
 ***/
@Component
public class NoAuthFailedCallBackEventHandler implements IThirdPartyFailedCallBackEventHandler {

    @Override
    public String getSupportedAccountType() {
        return ThirdPartyAccountType.NO_AUTH.getCode();
    }

    @Override
    public void redoFailedEvent(String tenantId, Connector connector) throws ThirdPartyAccountException {

    }
}
