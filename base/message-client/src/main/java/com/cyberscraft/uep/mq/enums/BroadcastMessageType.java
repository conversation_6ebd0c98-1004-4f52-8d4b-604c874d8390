package com.cyberscraft.uep.mq.enums;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/7/18 5:33 下午
 */
public enum BroadcastMessageType {

    APP_UPDATE("APP", "UPDATE"),
    APP_DELETE("APP", "DELETE"),
    LOGIN_POLICY_UPDATE("LOGIN_POLICY", "UPDATE"),
    LOGIN_POLICY_DELETE("LOGIN_POLICY", "DELETE"),
    PROFILE_UPDATE("PROFILE", "UPDATE"),
    PROFILE_DELETE("PROFILE", "DELETE"),
    ROLE_UPDATE("ROLE", "UPDATE"),
    R<PERSON><PERSON>_DELETE("ROLE", "DELETE"),
    AUTH_FACTOR_UPDATE("AUTH_FACTOR", "UPDATE"),
    AUTH_FACTOR_DELETE("AUTH_FACTOR", "DELETE"),
    APP_FLOW_UPDATE("FLOW", "UPDATE"),
    APP_FLOW_DELETE("FLOW", "DELETE"),
    FLOW_ACCOUNT_EVICT("ACCOUNT", "DELETE"),
    AGGREGATE_TASK_STOP("AGGREGATE_TASK", "STOP"),
    PUSH_TASK_STOP("PUSH_TASK", "STOP"),
    ;


    private String resourceType;
    private String action;

    BroadcastMessageType(String resourceType, String action){
        this.resourceType = resourceType;
        this.action = action;
    }

    public String getResourceType() {
        return resourceType;
    }

    public Boolean equals(String resourceType, String action) {
        return this.resourceType.equals(resourceType) && this.action.equals(action);
    }

    public String getAction() {
        return action;
    }

    public String getSupportedType() {
        return this.resourceType + ":" + this.action;
    }
}
