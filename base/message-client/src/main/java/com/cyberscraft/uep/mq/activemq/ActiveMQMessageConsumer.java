package com.cyberscraft.uep.mq.activemq;

import com.cyberscraft.uep.mq.service.AbstractMessageConsumer;
import com.cyberscraft.uep.mq.vo.MessageEntry;
import org.springframework.jms.listener.DefaultMessageListenerContainer;
import org.springframework.jms.listener.MessageListenerContainer;
import org.springframework.jms.support.converter.MessageConverter;

import javax.jms.ConnectionFactory;
import javax.jms.MessageListener;
import java.util.concurrent.ExecutionException;

/***
 *
 * @date 2021-08-09
 * <AUTHOR>
 ***/
public class ActiveMQMessageConsumer extends AbstractMessageConsumer {


    /****
     *
     */
    private volatile MessageListenerContainer jmsContainer;

    /***
     *
     */
    private MessageConverter messageConverter;

    /****
     *
     * @param connectionFactory
     */
    public ActiveMQMessageConsumer(ConnectionFactory connectionFactory, MessageConverter messageConverter){
        this.connectionFactory = connectionFactory;
        this.messageConverter = messageConverter;
    }

    @Override
    public void start() {
        if(jmsContainer==null){
            synchronized (this){
                DefaultMessageListenerContainer container = new DefaultMessageListenerContainer();
                container.setConcurrentConsumers(this.concurrentConsumer);
                container.setMaxConcurrentConsumers(this.maxConcurrentConsumer);
                MessageListener messageListener = (message)->{
                    try {
                        //TODO 进行消息处理因为ActiveMQ已经做了线程分发，
                        // 所以直接调用licenser进行分发处理即可，而且ActiveMQ也不需要进行消息确认处理
                        //将消息转发成MessageEntry进行业务处理
                        //Object el = messageConverter.fromMessage(message);
                        //MessageEntry entry = new MessageEntry();
                        //entry.setMsg(el);
                        MessageEntry entry =(MessageEntry)messageConverter.fromMessage(message);//message;
                        if (listeners != null && listeners.size() > 0) {
                            listeners.forEach(l -> l.onMessage(entry));
                        }
                    }
                    catch (Exception e){
                        LOG.error(e.getMessage(),e);
                    }
                };
                container.setMessageListener(messageListener);
                //container.setMessageConverter(this.messageConverter);
                container.setConnectionFactory(this.connectionFactory);
                String destinationName= null;
                //只取第一个
                if(this.subscription!=null){
                    destinationName = this.subscription.getTopic();
                }
                container.setDestinationName(destinationName);
                try {
                    // Actually register all listeners
                    container.afterPropertiesSet();
                }
                catch (Exception e){
                    LOG.error(e.getMessage(),e);
                }
                container.start();
                this.jmsContainer = container;
            }
        }
    }

    @Override
    public void stop() {
        if(this.jmsContainer!=null){
            this.jmsContainer.stop();
        }
    }
}
