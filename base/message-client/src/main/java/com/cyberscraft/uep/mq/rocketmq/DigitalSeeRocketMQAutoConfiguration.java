package com.cyberscraft.uep.mq.rocketmq;

import com.aliyun.openservices.ons.api.bean.ProducerBean;
import com.cyberscraft.uep.mq.condition.RocketMQCondition;
import com.cyberscraft.uep.mq.enums.MQCodec;
import com.cyberscraft.uep.mq.serialization.ISerialization;
import com.cyberscraft.uep.mq.service.IMessageConsumerFactory;
import com.cyberscraft.uep.mq.service.IMessageSendClient;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.actuate.autoconfigure.jms.JmsHealthContributorAutoConfiguration;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.jms.JmsAutoConfiguration;
import org.springframework.boot.autoconfigure.jms.activemq.ActiveMQAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/***
 *
 * @date 2021-07-23
 * <AUTHOR>
 ***/
@Configuration
@Conditional(RocketMQCondition.class)
@EnableConfigurationProperties({RocketMQConfig.class})
@EnableAutoConfiguration(exclude = {
        ActiveMQAutoConfiguration.class,
        JmsAutoConfiguration.class,
        JmsHealthContributorAutoConfiguration.class
})
public class DigitalSeeRocketMQAutoConfiguration {

    @Resource
    public RocketMQConfig rocketMQConfig;

    /**
     * mq 生产者， 是一个单例类
     *
     * @param
     * @return
     */
    @Conditional(RocketMQCondition.class)
    @ConditionalOnMissingBean
    @Bean(initMethod = "start", destroyMethod = "shutdown")
    public ProducerBean producerBean() {
        ProducerBean producerBean = new ProducerBean();
        producerBean.setProperties(rocketMQConfig.getRocketMqProp());
        return producerBean;
    }

    @Conditional(RocketMQCondition.class)
    @ConditionalOnMissingBean
    @Bean
    public RocketMQMessageConverter converterBean() {

        ISerialization serialization = serializationBean();
        final String consumerId = rocketMQConfig.getConsumerId();
        return new RocketMQMessageConverter(serialization, consumerId);
    }


    /****
     * 这里应该可以配置+SPI的方式，自动加载对应的序列化类，为了简化处理，暂时采用枚举来处理
     * @return
     */
    @Conditional(RocketMQCondition.class)
    @ConditionalOnMissingBean
    @Bean
    public ISerialization serializationBean() {
        String code = rocketMQConfig.getCodec();
        if (StringUtils.isNotBlank(code)) {
            MQCodec mqCodec = MQCodec.of(code);
            if (mqCodec != null) {
                return mqCodec.getSerialization();
            }
        }
        return MQCodec.FST.getSerialization();
    }


    @Bean(destroyMethod = "shutdown")
    @Conditional(RocketMQCondition.class)
    IMessageSendClient rocketMQMessageSendClient() {
        return new RocketMQMessageSendClient(converterBean(), producerBean(), rocketMQConfig.getRocketMqProp(), rocketMQConfig.getTopics());
    }


    @Bean
    @Conditional(RocketMQCondition.class)
    IMessageConsumerFactory rocketMQMessageConsumerFactory() {
        return new RocketMQMessageConsumerFactory(rocketMQConfig.getRocketConsumerProp(), rocketMQConfig.getTopics(), converterBean());
    }

}
