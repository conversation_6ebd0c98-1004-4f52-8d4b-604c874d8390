package com.cyberscraft.uep.mq.rocketmq;

import com.aliyun.openservices.ons.api.PropertyKeyConst;
import com.cyberscraft.uep.mq.condition.RocketMQCondition;
import com.cyberscraft.uep.mq.constant.MessageConsumerHasInstanceConstant;
import com.cyberscraft.uep.mq.constant.MessageConvertConstant;
import com.cyberscraft.uep.mq.enums.MQCodec;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Properties;

@ConfigurationProperties(
        prefix = "sysconf.rocketmq"
)
@Component
@RefreshScope
@Conditional(RocketMQCondition.class)
public class RocketMQConfig {

    /***
     * 对应的消费者ID
     */
    private String consumerId = "";

    /***
     * 对应的生产者ID
     */
    private String producerId = "";

    private String accessKey = "";

    private String secretKey = "";

    private String nameSrv = "";

    /***
     * 同时可以进行消费的消费线程数
     */
    private String consumeNums = "10";

    /***
     *
     */
    private String instanceName;

    /****
     * 是否有instanceName
     */
    private String hasInstanceName = MessageConsumerHasInstanceConstant.TRUE;


    /***
     * 指定的编码方式
     */
    private String codec = MQCodec.FST.getCode();


    /***
     * 是否进行消息转换
     */
    private String isRawMsg = MessageConvertConstant.FALSE;

    /***
     *
     */
    private Map<String, TopicItemCfg> topics;

    public String getAccessKey() {
        return accessKey;
    }

    public void setAccessKey(String accessKey) {
        this.accessKey = accessKey;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public String getNameSrv() {
        return nameSrv;
    }

    public void setNameSrv(String nameSrv) {
        this.nameSrv = nameSrv;
    }

    public String getCodec() {
        return codec;
    }

    public void setCodec(String codec) {
        this.codec = codec;
    }

    public String getConsumeNums() {
        return consumeNums;
    }

    public void setConsumeNums(String consumeNums) {
        this.consumeNums = consumeNums;
    }

    public String getConsumerId() {
        return consumerId;
    }

    public void setConsumerId(String consumerId) {
        this.consumerId = consumerId;
    }

    public String getProducerId() {
        return producerId;
    }

    public void setProducerId(String producerId) {
        this.producerId = producerId;
    }

    public String getInstanceName() {
        return instanceName;
    }

    public void setInstanceName(String instanceName) {
        this.instanceName = instanceName;
    }

    public Map<String, TopicItemCfg> getTopics() {
        return topics;
    }

    public void setTopics(Map<String, TopicItemCfg> topics) {
        this.topics = topics;
    }

    /***
     * 得到发送端的配置列表
     */
    public Properties getRocketMqProp() {
        Properties properties = new Properties();
//        if (USE_GROUP_ID.equalsIgnoreCase(useGroupId)) {
//            properties.setProperty(PropertyKeyConst.GROUP_ID, groupId);
//        } else {
        properties.setProperty(PropertyKeyConst.GROUP_ID, getProducerId());
//        }
//        properties.setProperty(USE_GROUP_PROPERTY_KEY, useGroupId);
        properties.setProperty(PropertyKeyConst.AccessKey, getAccessKey());
        properties.setProperty(PropertyKeyConst.SecretKey, getSecretKey());
        properties.setProperty(PropertyKeyConst.NAMESRV_ADDR, getNameSrv());
        properties.setProperty(PropertyKeyConst.InstanceName, instanceName);
        return properties;
    }

    /****
     * 得到消费端线可以消费的配置列表
     * @return
     */
    public Properties getRocketConsumerProp() {
        Properties properties = new Properties();
        properties.setProperty(PropertyKeyConst.GROUP_ID, consumerId);
        properties.setProperty(MessageConvertConstant.IS_RAW_MESSAGE_PROPERTY_KEY, isRawMsg);
        properties.setProperty(PropertyKeyConst.AccessKey, getAccessKey());
        properties.setProperty(PropertyKeyConst.SecretKey, getSecretKey());
        properties.setProperty(PropertyKeyConst.NAMESRV_ADDR, getNameSrv());
        properties.setProperty(PropertyKeyConst.ConsumeThreadNums, getConsumeNums());
        properties.setProperty(PropertyKeyConst.InstanceName, instanceName);
        properties.setProperty(MessageConsumerHasInstanceConstant.HAS_INSTANCENAME_PROPERTY_KEY, hasInstanceName);
        return properties;
    }

    public String getHasInstanceName() {
        return hasInstanceName;
    }

    public void setHasInstanceName(String hasInstanceName) {
        this.hasInstanceName = hasInstanceName;
    }
}
