package com.cyberscraft.uep.mq.serialization;

import com.esotericsoftware.kryo.Kryo;
import com.esotericsoftware.kryo.io.Input;
import com.esotericsoftware.kryo.io.Output;

import java.io.ByteArrayOutputStream;

/***
 * 采用kryo框架进行序列化
 * @date 2021-08-09
 * <AUTHOR>
 ***/
public class KryoSerialization implements ISerialization {


    private final Kryo kryo;

    public KryoSerialization() {
        kryo = new Kryo();
        kryo.setRegistrationRequired(false);
        kryo.setReferences(true);
    }

    @Override
    public <T> T deserialize(byte[] data, Class<T> clazz) {

        try (Input input = new Input(data)) {
            return (T) kryo.readClassAndObject(input);
        } catch (Exception e) {
        }
        return null;
    }

    @Override
    public <T> byte[] serialize(T obj) {
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        try (Output output = new Output(os)) {
            kryo.writeClassAndObject(output, obj);
            output.flush();
        } catch (Exception e) {
            //LOG.warn(e.getMessage());
        }
        return os.toByteArray();
    }
}
