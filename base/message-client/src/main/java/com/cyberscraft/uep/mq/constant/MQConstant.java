package com.cyberscraft.uep.mq.constant;

/***
 * MQ相关常量
 * @date 2021-10-21
 * <AUTHOR>
 ***/
public class MQConstant {

    /***
     * 租户创建消息常量-MDM自身创建
     */
    public final static String MDM_TENANT_CREATE_MESSAGE_TOPIC = "MDMTenantCreate";

    /***
     * 钉钉租户创建消息常量
     */
    public final static String DING_TENANT_CREATE_MESSAGE_TOPIC = "MOZI_TENANT_INITALIZATION";

    /***
     * 钉钉租户创建topic tag
     */
    public final static String DING_TENANT_CREATE_MESSAGE_TAG = "tenant_create";

    /**
     * 事件Topic常量
     */
    public static String MDM_CMD_TOPIC = "BaseCmdQueue";//"MdmEvent";


    /**
     * 事件Topic常量
     */
    public static String BASE_CMD_TOPIC = "BaseCmdQueue";

    /****
     * 推送队列Topic常量
     */
    public static String PUSH_TOPIC = "PushQueue";

    /****
     * 推送Token无效Topic常量
     */
    public static String PUSH_TOKEN_INVALID_TOPIC = "PushTokenInvalidQueue";

    /***
     * 开放平台鉴权应用TOPIC
     */
    public static String OPEN_AUTH_APP_TOPIC = "OpenAuthApp";

    /****
     * 开放平台应用TOPIC
     */
    public static String OPEN_APP_TOPIC = "OpenApp";

    /***
     * 推送连接器
     */
    public static String PUSH_CONNECTOR_CHANGED_TOPIC = "PushConnector";

    /***
     * 字段更改事件
     */
    public static String FIELD_DICT_CHANAGED_TOPIC = "FieldDict";

    /***
     * 短信发送
     */
    public static String NCM_SMS_TOPIC = "NcmSMS";

    /***
     * 邮件发送
     */
    public static String NCM_EMAIL_TOPIC = "NcmEmail";

    /***
     * 通知中心消息队列
     */
    public final static String NOTIFICATION_TOPIC = "NotificationQueue";

    /**
     * IAM推送数据到第三方应用的队列
     */
    public static final String QUEUE_PUSH_DATA = "PushData";

    /**
     * 推送连接器同步事件，北向连接器触发同步消息
     */
    public static final String QUEUE_PUSH_CONNECTOR_SYNC = "PushConnectorSync";
    /**
     * 拉取连接器同步事件，南向连接器触发聚合消息
     */
    public static final String QUEUE_PULL_CONNECTOR_SYNC = "PullConnectorSync";

    /**
     * 广播消息的topic
     */
    public static final String BROADCAST_TOPIC = "Broadcast";

    /**
     * 推送到notify的广播消息
     */
    public static final String PUSH_BROADCAST_TOPIC = "PushBroadcast";

    public static final String CONSUME_EXPRESSION_TAG = "TAG";
}
