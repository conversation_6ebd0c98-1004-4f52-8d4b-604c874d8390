package com.cyberscraft.uep.mq.condition;

import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.env.Environment;
import org.springframework.core.type.AnnotatedTypeMetadata;

/***
 *
 * @date 2021-07-23
 * <AUTHOR>
 ***/
public class ActiveMQCondition implements Condition {

    @Override
    public boolean matches(ConditionContext context, AnnotatedTypeMetadata annotatedTypeMetadata) {

        Environment environment = context.getEnvironment();
        //如果不是RocketMQ，则代表使用ActiveMQ
        if (!MqConstant.MT_ROCKET_MQ.equalsIgnoreCase(environment.getProperty("sysconf.mq.type"))) {
            return true;
        }
        return false;
    }
}
