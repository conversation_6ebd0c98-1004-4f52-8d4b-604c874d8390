package com.cyberscraft.uep.mq.activemq;

import com.cyberscraft.uep.mq.service.IMessageConsumer;
import com.cyberscraft.uep.mq.service.IMessageConsumerFactory;
import com.cyberscraft.uep.mq.vo.MessageConsumerEntry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jms.support.converter.MessageConverter;

import javax.jms.ConnectionFactory;
import java.util.List;

/***
 *  ActiveMQ对应的消息消费客户端
 * @date 2021-08-12
 * <AUTHOR>
 ***/
public class ActiveMQMessageConsumerFactory implements IMessageConsumerFactory {

    /***
     *
     */
    private MessageConverter messageConverter;

    /***
     *
     */
    private ConnectionFactory connectionFactory;
    /***
     *
     */
    private final static Logger LOG = LoggerFactory.getLogger(ActiveMQMessageConsumerFactory.class);

    /***
     *
     * @param connectionFactory
     * @param messageConverter
     */
    public ActiveMQMessageConsumerFactory(ConnectionFactory connectionFactory, MessageConverter messageConverter) {
        LOG.info("===============初始化Active MQ消息接收工厂===============");

        this.connectionFactory = connectionFactory;
        this.messageConverter = messageConverter;
    }

    @Override
    public IMessageConsumer build(MessageConsumerEntry entry) {
        ActiveMQMessageConsumer consumer = new ActiveMQMessageConsumer(connectionFactory, messageConverter);
        consumer.setConcurrentConsumer(entry.getConcurrentConsumerNum());
        consumer.setMaxConcurrentConsumer(entry.getMaxConcurrentConsumerNum());
        return consumer;
    }

    @Override
    public List<IMessageConsumer> build(List<MessageConsumerEntry> entries) {
        return null;
    }
}
