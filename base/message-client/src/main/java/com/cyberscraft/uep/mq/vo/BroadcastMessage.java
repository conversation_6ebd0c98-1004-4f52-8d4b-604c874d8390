package com.cyberscraft.uep.mq.vo;

import com.cyberscraft.uep.mq.enums.BroadcastMessageType;

import java.io.Serializable;

/**
 * 广播事件的domain对象
 * <AUTHOR>
 */
public class BroadcastMessage implements Serializable {
    /**
     * 资源对象，例如用户的id，应用的clientId，角色的名称等
     */
    private String target;

    /**
     * 资源类型，例如ROLE，USER，ORG，APP
     */
    private String resourceType;
    /**
     * 资源变更对应的动作，例如UPDATE操作，NEW操作，DELETE操作
     */
    private String action;

    /**
     * 租户id
     */
    private String tenantId;

    public String getTarget() {
        return target;
    }

    public void setTarget(String target) {
        this.target = target;
    }

    public String getResourceType() {
        return resourceType;
    }

    public void setResourceType(String resourceType) {
        this.resourceType = resourceType;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public BroadcastMessage(BroadcastMessageType messageType, String target, String tenantId) {
        this.resourceType = messageType.getResourceType();
        this.action = messageType.getAction();
        this.target = target;
        this.tenantId = tenantId;
    }

    public String getMessageType() {
        return resourceType + ":" + action;
    }

    @Override
    public String toString() {
        return "BroadcastMessage{" +
                "target='" + target + '\'' +
                ", resourceType='" + resourceType + '\'' +
                ", action='" + action + '\'' +
                '}';
    }
}
