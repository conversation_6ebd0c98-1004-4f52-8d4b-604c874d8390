package com.cyberscraft.uep.mq.activemq;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/***
 * ActiveMQ对应的配置属性对像
 * @date 2021-07-23
 * <AUTHOR>
 ***/
@ConfigurationProperties(
        prefix = "sysconf.activemq"
)
@Component
@RefreshScope
public class ActiveMQConfig {

    //@Value("${sysconf.activemq.brokerurl}")
    private String brokerURL;
    //@Value("${sysconf.activemq.username}")
    private String userName;
    //@Value("${sysconf.activemq.password}")
    private String password;
//
//
//    @Value("${jms.settings.closeTimeout}")
    private int closeTimeout=60000;
//    @Value("${jms.settings.optimizedAckScheduledAckInterval}")
    private long optimizedAckScheduledAckInterval=10000;
//    @Value("${jms.settings.maxConnections}")
    private int maxConnections=200;

    public String getBrokerURL() {
        return brokerURL;
    }

    public void setBrokerURL(String brokerURL) {
        this.brokerURL = brokerURL;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public int getCloseTimeout() {
        return closeTimeout;
    }

    public void setCloseTimeout(int closeTimeout) {
        this.closeTimeout = closeTimeout;
    }

    public long getOptimizedAckScheduledAckInterval() {
        return optimizedAckScheduledAckInterval;
    }

    public void setOptimizedAckScheduledAckInterval(long optimizedAckScheduledAckInterval) {
        this.optimizedAckScheduledAckInterval = optimizedAckScheduledAckInterval;
    }

    public int getMaxConnections() {
        return maxConnections;
    }

    public void setMaxConnections(int maxConnections) {
        this.maxConnections = maxConnections;
    }
}
