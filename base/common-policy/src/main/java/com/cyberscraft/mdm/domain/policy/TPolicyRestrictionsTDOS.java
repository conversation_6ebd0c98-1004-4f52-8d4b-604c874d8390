package com.cyberscraft.mdm.domain.policy;

import java.util.List;

/**
 * Description:
 * User: lijie
 * Date: 2017-11-17
 * Time: 下午4:12
 */
public class TPolicyRestrictionsTDOS implements java.io.Serializable {
    private static final long serialVersionUID = 1L;
    private String id;

    private Byte allowWifi; //Wi-Fi控制 0：不允许 1：允许 2：仅Wi-Fi指纹扫描
    private Byte allowCellular; //移动数据网控制 0：不允许 1：允许 2：强制开启
    private Byte allowBluetooth; //蓝牙控制 0：不允许 1：允许
    private Byte allowNFC; //NFC控制 0：不允许 1：允许 2：强制开启
    private Byte allowAPN; // APN管理功能控制   0：不允许 1：允许
    private Byte allowFingerprint; // 指纹识别模块控制   0：不允许 1：允许
    private Byte allowMessage; // 短信功能控制   0：不允许 1：允许
    private Byte allowMicrophone; // 麦克风控制(录音)   0：不允许 1：允许
    private Byte allowCamera; // 摄像头控制   0：不允许 1：允许
    private Byte allowUSBStorage; //USB工作模式控制-存储 0：不允许 1：允许
    private Byte allowUSBDevice; // USB工作模式控制-外设 0：不允许 1：允许
    private Byte allowUSBDebug; //  USB工作模式控制-调试 0：不允许 1：允许
    private Byte allowUSBMtp; //    USB工作模式控制-MTP 0：不允许 1：允许
    private List<String> callWhiteList; // 通话白名单

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Byte getAllowWifi() {
        return allowWifi;
    }

    public void setAllowWifi(Byte allowWifi) {
        this.allowWifi = allowWifi;
    }

    public Byte getAllowCellular() {
        return allowCellular;
    }

    public void setAllowCellular(Byte allowCellular) {
        this.allowCellular = allowCellular;
    }

    public Byte getAllowBluetooth() {
        return allowBluetooth;
    }

    public void setAllowBluetooth(Byte allowBluetooth) {
        this.allowBluetooth = allowBluetooth;
    }

    public Byte getAllowNFC() {
        return allowNFC;
    }

    public void setAllowNFC(Byte allowNFC) {
        this.allowNFC = allowNFC;
    }

    public Byte getAllowAPN() {
        return allowAPN;
    }

    public void setAllowAPN(Byte allowAPN) {
        this.allowAPN = allowAPN;
    }

    public Byte getAllowFingerprint() {
        return allowFingerprint;
    }

    public void setAllowFingerprint(Byte allowFingerprint) {
        this.allowFingerprint = allowFingerprint;
    }

    public Byte getAllowMessage() {
        return allowMessage;
    }

    public void setAllowMessage(Byte allowMessage) {
        this.allowMessage = allowMessage;
    }

    public Byte getAllowMicrophone() {
        return allowMicrophone;
    }

    public void setAllowMicrophone(Byte allowMicrophone) {
        this.allowMicrophone = allowMicrophone;
    }

    public Byte getAllowCamera() {
        return allowCamera;
    }

    public void setAllowCamera(Byte allowCamera) {
        this.allowCamera = allowCamera;
    }

    public Byte getAllowUSBStorage() {
        return allowUSBStorage;
    }

    public void setAllowUSBStorage(Byte allowUSBStorage) {
        this.allowUSBStorage = allowUSBStorage;
    }

    public Byte getAllowUSBDevice() {
        return allowUSBDevice;
    }

    public void setAllowUSBDevice(Byte allowUSBDevice) {
        this.allowUSBDevice = allowUSBDevice;
    }

    public Byte getAllowUSBDebug() {
        return allowUSBDebug;
    }

    public void setAllowUSBDebug(Byte allowUSBDebug) {
        this.allowUSBDebug = allowUSBDebug;
    }

    public Byte getAllowUSBMtp() {
        return allowUSBMtp;
    }

    public void setAllowUSBMtp(Byte allowUSBMtp) {
        this.allowUSBMtp = allowUSBMtp;
    }

    public List<String> getCallWhiteList() {
        return callWhiteList;
    }

    public void setCallWhiteList(List<String> callWhiteList) {
        this.callWhiteList = callWhiteList;
    }
}
