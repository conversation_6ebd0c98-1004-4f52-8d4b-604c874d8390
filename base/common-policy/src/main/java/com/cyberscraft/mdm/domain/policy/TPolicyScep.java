package com.cyberscraft.mdm.domain.policy;

public class TPolicyScep implements java.io.Serializable {
    /**
     *
     */
    private static final long serialVersionUID = 1L;
    private String id;
    private String url;                      //服务器URL
    private String name = "";                //名称
    private String subject;                  //主题
    private String challenge = "";           //口令
    private String keyType = "RSA";          //Currently always "RSA".
    private Integer keyUsage = 0;            //用做数字签名(1),用于密匙加密(4)
    private Integer subjectAltNameType = 0;  //主题备用名称类型   0--无,1--RFC 822 Name,2--DNS 名称,3--统一资源标识符
    private String ntPrincipalName;          //NT 主题名称
    private String rfc822name;               //主题备用名称值(主题备用名称类型  为1--RFC 822 Name)
    private String dnsname;                  //主题备用名称值(主题备用名称类型 为2--DNS 名称)
    private String uniformResourceIdentifier;  //主题备用名称值(主题备用名称类型 为3--统一资源标识符)
    private String caFingerprint;              //指纹BASE64
    private Integer retries = 3;             //重试次数
    private Integer retryDelay = 10;         //RetryDelay,重试间隔时间(秒)
    private Integer keysize = 1024;          //密匙大小(位)

    private String payloadDescription = "Configures SCEP";
    private String payloadDisplayName = "SCEP (" + name + ")";
    private String payloadType = "com.apple.security.scep";
    private String payloadIdentifier = "{0}.scep1";                          //{0}--通用配置中描述符，{1}--序号

    private int signingFlag = 0;                    //用做数字签名 0--false;1--true
    private int encryptionFlag = 0;                //用于密匙加密0--false;4--true
    private String caFingerprintSha1;              //指纹SHA1

    public TPolicyScep() {
    }

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPayloadIdentifier() {
        return this.payloadIdentifier;
    }

    public void setPayloadIdentifier(String payloadIdentifier) {
        this.payloadIdentifier = payloadIdentifier;
    }

    public String getPayloadType() {
        return this.payloadType;
    }

    public void setPayloadType(String payloadType) {
        this.payloadType = payloadType;
    }

    public String getUrl() {
        return this.url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSubject() {
        return this.subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getChallenge() {
        return this.challenge;
    }

    public void setChallenge(String challenge) {
        this.challenge = challenge;
    }

    public String getKeyType() {
        return this.keyType;
    }

    public void setKeyType(String keyType) {
        this.keyType = keyType;
    }

    public Integer getKeyUsage() {
        return this.keyUsage;
    }

    public void setKeyUsage(Integer keyUsage) {
        this.keyUsage = keyUsage;
    }

    public Integer getSubjectAltNameType() {
        return this.subjectAltNameType;
    }

    public void setSubjectAltNameType(Integer subjectAltNameType) {
        this.subjectAltNameType = subjectAltNameType;
    }

    public String getNtPrincipalName() {
        return this.ntPrincipalName;
    }

    public void setNtPrincipalName(String ntPrincipalName) {
        this.ntPrincipalName = ntPrincipalName;
    }

    public String getRfc822name() {
        return this.rfc822name;
    }

    public void setRfc822name(String rfc822name) {
        this.rfc822name = rfc822name;
    }

    public String getDnsname() {
        return this.dnsname;
    }

    public void setDnsname(String dnsname) {
        this.dnsname = dnsname;
    }

    public String getUniformResourceIdentifier() {
        return this.uniformResourceIdentifier;
    }

    public void setUniformResourceIdentifier(String uniformResourceIdentifier) {
        this.uniformResourceIdentifier = uniformResourceIdentifier;
    }

    public String getCaFingerprint() {
        return this.caFingerprint;
    }

    public void setCaFingerprint(String caFingerprint) {
        this.caFingerprint = caFingerprint;
    }

    public Integer getRetries() {
        return this.retries;
    }

    public void setRetries(Integer retries) {
        this.retries = retries;
    }

    public Integer getRetryDelay() {
        return this.retryDelay;
    }

    public void setRetryDelay(Integer retryDelay) {
        this.retryDelay = retryDelay;
    }

    public Integer getKeysize() {
        return this.keysize;
    }

    public void setKeysize(Integer keysize) {
        this.keysize = keysize;
    }

    public String getPayloadDescription() {
        return payloadDescription;
    }

    public void setPayloadDescription(String payloadDescription) {
        this.payloadDescription = payloadDescription;
    }

    public String getPayloadDisplayName() {
        return payloadDisplayName;
    }

    public void setPayloadDisplayName(String payloadDisplayName) {
        this.payloadDisplayName = payloadDisplayName;
    }

    public int getSigningFlag() {
        return signingFlag;
    }

    public void setSigningFlag(int signingFlag) {
        this.signingFlag = signingFlag;
    }

    public int getEncryptionFlag() {
        return encryptionFlag;
    }

    public void setEncryptionFlag(int encryptionFlag) {
        this.encryptionFlag = encryptionFlag;
    }

    public String getCaFingerprintSha1() {
        return caFingerprintSha1;
    }

    public void setCaFingerprintSha1(String caFingerprintSha1) {
        this.caFingerprintSha1 = caFingerprintSha1;
    }

}
