package com.cyberscraft.mdm.domain.policy;

import java.util.HashSet;
import java.util.Set;

public class TXiaoMiConfApp {
    private Set<String> appWhiteList = new HashSet<String>();
    private int allowApp;

    public Set<String> getAppWhiteList() {
        return appWhiteList;
    }

    public void setAppWhiteList(Set<String> appWhiteList) {
        this.appWhiteList = appWhiteList;
    }

    public int getAllowApp() {
        return allowApp;
    }

    public void setAllowApp(int allowApp) {
        this.allowApp = allowApp;
    }


}
