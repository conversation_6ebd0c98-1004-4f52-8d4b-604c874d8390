package com.cyberscraft.mdm.domain.policy;

public class TPolicyEmail implements java.io.Serializable {
    /**
     *
     */
    private static final long serialVersionUID = 1L;
    private String id;
    private String emailAccountDescription;                     //账户描述
    private String emailAccountType;                            //账户类型(EmailTypeIMAP/EmailTypePOP)
    private String emailAccountName;                            //用户显示名称
    private String emailAddress;                                //电子邮件地址
    private String incomingMailServerIMAPPathPref;            //路径前缀(IMAP)
    private Byte preventMove = Byte.valueOf((byte) 0);           //允许移动 0--false;1--true
    private String incomingMailServerHostName;                  //接收邮件服务器
    private Integer incomingMailServerPortNumber;               //接收邮件服务器端口
    private String incomingMailServerUsername;                  //接收邮件服务器用户名
    private String incomingPassword;                            //接收邮件服务器密码
    private Byte incomingMailServerUseSsl = Byte.valueOf((byte) 0);     //接收邮件使用SSL,0--false;1--true
    private String incomingMailServerAuth;            //接收邮件鉴定类型无--EmailAuthNone;密码--EmailAuthPassword;MD5--EmailAuthCRAMMD5;NTLM--EmailAuthNTLM;HTTP MD5--EmailAuthHTTPMD5
    private String outgoingMailServerHostName;                  //发送邮件服务器
    private Integer outgoingMailServerPortNumber;               //发送邮件服务器端口
    private String outgoingMailServerUsername;                   //发送邮件服务器用户名
    private String outgoingPassword;                             //发送邮件服务器密码
    private Byte outgoingMailServerUseSsl = Byte.valueOf((byte) 0);   //发送邮件使用SSL,0--false;1--true
    private String outgoingMailServerAuth;            //发送邮件鉴定类型无--EmailAuthNone;密码--EmailAuthPassword;MD5--EmailAuthCRAMMD5;NTLM--EmailAuthNTLM;HTTP MD5--EmailAuthHTTPMD5
    private Byte outgoingPwdSameAsIncomingPwd = Byte.valueOf((byte) 0);  //外发密码与传入密码相同,0--false;1--true
    private Byte preventAppSheet = Byte.valueOf((byte) 0);      //仅在邮件使用,0--false;1--true
    private Byte smimeEnabled = Byte.valueOf((byte) 0);          //使用S/MIME,0--false;1--true

    private String payloadDescription = "Configures email account.";
    private String payloadType = "com.apple.mail.managed";
    private String payloadIdentifier = "{0}.email";                          //{0}--通用配置中描述符，{1}--组件序号
    private String payloadDisplayName = emailAccountDescription;

    private Byte allowMailDrop = Byte.valueOf((byte) 1);    // 设备邮件配置禁止Mail Drop功能


    public Byte getAllowMailDrop() {
        return allowMailDrop;
    }

    public void setAllowMailDrop(Byte allowMailDrop) {
        this.allowMailDrop = allowMailDrop;
    }

    public TPolicyEmail() {
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getEmailAccountDescription() {
        return this.emailAccountDescription;
    }

    public void setEmailAccountDescription(String emailAccountDescription) {
        this.emailAccountDescription = emailAccountDescription;
    }

    public String getEmailAccountType() {
        return this.emailAccountType;
    }

    public void setEmailAccountType(String emailAccountType) {
        this.emailAccountType = emailAccountType;
    }

    public Byte getPreventMove() {
        return this.preventMove;
    }

    public void setPreventMove(Byte preventMove) {
        this.preventMove = preventMove;
    }

    public String getIncomingMailServerHostName() {
        return this.incomingMailServerHostName;
    }

    public void setIncomingMailServerHostName(String incomingMailServerHostName) {
        this.incomingMailServerHostName = incomingMailServerHostName;
    }

    public Integer getIncomingMailServerPortNumber() {
        return this.incomingMailServerPortNumber;
    }

    public void setIncomingMailServerPortNumber(Integer incomingMailServerPortNumber) {
        this.incomingMailServerPortNumber = incomingMailServerPortNumber;
    }

    public Byte getIncomingMailServerUseSsl() {
        return this.incomingMailServerUseSsl;
    }

    public void setIncomingMailServerUseSsl(Byte incomingMailServerUseSsl) {
        this.incomingMailServerUseSsl = incomingMailServerUseSsl;
    }

    public String getOutgoingMailServerHostName() {
        return this.outgoingMailServerHostName;
    }

    public void setOutgoingMailServerHostName(String outgoingMailServerHostName) {
        this.outgoingMailServerHostName = outgoingMailServerHostName;
    }

    public Integer getOutgoingMailServerPortNumber() {
        return this.outgoingMailServerPortNumber;
    }

    public void setOutgoingMailServerPortNumber(Integer outgoingMailServerPortNumber) {
        this.outgoingMailServerPortNumber = outgoingMailServerPortNumber;
    }

    public Byte getOutgoingMailServerUseSsl() {
        return this.outgoingMailServerUseSsl;
    }

    public void setOutgoingMailServerUseSsl(Byte outgoingMailServerUseSsl) {
        this.outgoingMailServerUseSsl = outgoingMailServerUseSsl;
    }

    public Byte getPreventAppSheet() {
        return this.preventAppSheet;
    }

    public void setPreventAppSheet(Byte preventAppSheet) {
        this.preventAppSheet = preventAppSheet;
    }

    public Byte getSmimeEnabled() {
        return this.smimeEnabled;
    }

    public void setSmimeEnabled(Byte smimeEnabled) {
        this.smimeEnabled = smimeEnabled;
    }

    public String getPayloadDescription() {
        return payloadDescription;
    }

    public void setPayloadDescription(String payloadDescription) {
        this.payloadDescription = payloadDescription;
    }

    public String getPayloadType() {
        return payloadType;
    }

    public void setPayloadType(String payloadType) {
        this.payloadType = payloadType;
    }

    public String getPayloadIdentifier() {
        return payloadIdentifier;
    }

    public void setPayloadIdentifier(String payloadIdentifier) {
        this.payloadIdentifier = payloadIdentifier;
    }

    public String getPayloadDisplayName() {
        return payloadDisplayName;
    }

    public void setPayloadDisplayName(String payloadDisplayName) {
        this.payloadDisplayName = payloadDisplayName;
    }

    public String getEmailAccountName() {
        return emailAccountName;
    }

    public void setEmailAccountName(String emailAccountName) {
        this.emailAccountName = emailAccountName;
    }

    public String getEmailAddress() {
        return emailAddress;
    }

    public void setEmailAddress(String emailAddress) {
        this.emailAddress = emailAddress;
    }

    public String getIncomingMailServerUsername() {
        return incomingMailServerUsername;
    }

    public void setIncomingMailServerUsername(String incomingMailServerUsername) {
        this.incomingMailServerUsername = incomingMailServerUsername;
    }

    public String getIncomingPassword() {
        return incomingPassword;
    }

    public void setIncomingPassword(String incomingPassword) {
        this.incomingPassword = incomingPassword;
    }

    public String getOutgoingMailServerUsername() {
        return outgoingMailServerUsername;
    }

    public void setOutgoingMailServerUsername(String outgoingMailServerUsername) {
        this.outgoingMailServerUsername = outgoingMailServerUsername;
    }

    public String getOutgoingPassword() {
        return outgoingPassword;
    }

    public void setOutgoingPassword(String outgoingPassword) {
        this.outgoingPassword = outgoingPassword;
    }

    public String getIncomingMailServerIMAPPathPref() {
        return incomingMailServerIMAPPathPref;
    }

    public void setIncomingMailServerIMAPPathPref(String incomingMailServerIMAPPathPref) {
        this.incomingMailServerIMAPPathPref = incomingMailServerIMAPPathPref;
    }

    public String getIncomingMailServerAuth() {
        return incomingMailServerAuth;
    }

    public void setIncomingMailServerAuth(String incomingMailServerAuth) {
        this.incomingMailServerAuth = incomingMailServerAuth;
    }

    public String getOutgoingMailServerAuth() {
        return outgoingMailServerAuth;
    }

    public void setOutgoingMailServerAuth(String outgoingMailServerAuth) {
        this.outgoingMailServerAuth = outgoingMailServerAuth;
    }

    public Byte getOutgoingPwdSameAsIncomingPwd() {
        return outgoingPwdSameAsIncomingPwd;
    }

    public void setOutgoingPwdSameAsIncomingPwd(Byte outgoingPwdSameAsIncomingPwd) {
        this.outgoingPwdSameAsIncomingPwd = outgoingPwdSameAsIncomingPwd;
    }

}
