package com.cyberscraft.mdm.domain.policy;

/**
 * PolicyKnoxSamsung entity. <AUTHOR> Persistence Tools
 */

public class TPolicyKnoxSamsung implements java.io.Serializable {

    // Fields
    /**
     *
     */
    private static final long serialVersionUID = 1L;
    //private Byte allowKnox;
    private String enableShareList;
    private String enableEmail;
    private String enableCamera;
    private String enableSecureKeypad;
    private String enableBrowsercookies;
    private String enableAutoFill;
    private String enableJavascript;
    private String enablePopups;

    public String getEnableShareList() {
        return enableShareList;
    }

    public void setEnableShareList(String enableShareList) {
        this.enableShareList = enableShareList;
    }

    public String getEnableEmail() {
        return enableEmail;
    }

    public void setEnableEmail(String enableEmail) {
        this.enableEmail = enableEmail;
    }

    public String getEnableCamera() {
        return enableCamera;
    }

    public void setEnableCamera(String enableCamera) {
        this.enableCamera = enableCamera;
    }

    public String getEnableSecureKeypad() {
        return enableSecureKeypad;
    }

    public void setEnableSecureKeypad(String enableSecureKeypad) {
        this.enableSecureKeypad = enableSecureKeypad;
    }

    public String getEnableBrowsercookies() {
        return enableBrowsercookies;
    }

    public void setEnableBrowsercookies(String enableBrowsercookies) {
        this.enableBrowsercookies = enableBrowsercookies;
    }

    public String getEnableAutoFill() {
        return enableAutoFill;
    }

    public void setEnableAutoFill(String enableAutoFill) {
        this.enableAutoFill = enableAutoFill;
    }

    public String getEnableJavascript() {
        return enableJavascript;
    }

    public void setEnableJavascript(String enableJavascript) {
        this.enableJavascript = enableJavascript;
    }

    public String getEnablePopups() {
        return enablePopups;
    }

    public void setEnablePopups(String enablePopups) {
        this.enablePopups = enablePopups;
    }


}