package com.cyberscraft.mdm.domain.policy;

public class TimeBarrier {

    // 周期
    private String period;
    // 时间开始
    private String timeStart;
    // 时间结束
    private String timeEnd;

    // 域类型
    private String domainType;

    private transient String timeStartStr;
    private transient String timeEndStr;
    private transient String periodStr;

    public TimeBarrier() {
    }

    public TimeBarrier(String period, String timeStart, String timeEnd) {
        super();
        this.period = period;
        this.timeStart = timeStart;
        this.timeEnd = timeEnd;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public String getTimeStart() {
        return timeStart;
    }

    public void setTimeStart(String timeStart) {
        this.timeStart = timeStart;
    }

    public String getTimeEnd() {
        return timeEnd;
    }

    public void setTimeEnd(String timeEnd) {
        this.timeEnd = timeEnd;
    }

    public String getTimeStartStr() {
        return timeStartStr;
    }

    public void setTimeStartStr(String timeStartStr) {
        this.timeStartStr = timeStartStr;
    }

    public String getTimeEndStr() {
        return timeEndStr;
    }

    public void setTimeEndStr(String timeEndStr) {
        this.timeEndStr = timeEndStr;
    }

    public String getPeriodStr() {
        return periodStr;
    }

    public void setPeriodStr(String periodStr) {
        this.periodStr = periodStr;
    }

    public String getDomainType() {
        return domainType;
    }

    public void setDomainType(String domainType) {
        this.domainType = domainType;
    }
}
