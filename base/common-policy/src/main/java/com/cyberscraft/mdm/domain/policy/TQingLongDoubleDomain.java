package com.cyberscraft.mdm.domain.policy;

public class TQingLongDoubleDomain implements java.io.Serializable {
    /**
     *
     */
    private static final long serialVersionUID = -589985126854328653L;
    private Byte doubleDomainSwitch;// 0-围栏内，无法退出安全域，围栏外，可以自由切换 1-围栏内，无法退出个人域，围栏外，可以自由切换


    public Byte getDoubleDomainSwitch() {
        return doubleDomainSwitch;
    }

    public void setDoubleDomainSwitch(Byte doubleDomainSwitch) {
        this.doubleDomainSwitch = doubleDomainSwitch;
    }
}
