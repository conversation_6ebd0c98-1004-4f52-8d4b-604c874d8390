package com.cyberscraft.mdm.domain.policy;

import java.util.List;

/**
 * Created by liji<PERSON> on 16/10/25.
 */
public class TPolicyAppLimit implements java.io.Serializable {
    private static final long serialVersionUID = 1L;

    private List<String> blacklistedAppBundleIDs;
    private List<String> whitelistedAppBundleIDs;

    public List<String> getBlacklistedAppBundleIDs() {
        return blacklistedAppBundleIDs;
    }

    public void setBlacklistedAppBundleIDs(List<String> blacklistedAppBundleIDs) {
        this.blacklistedAppBundleIDs = blacklistedAppBundleIDs;
    }

    public List<String> getWhitelistedAppBundleIDs() {
        return whitelistedAppBundleIDs;
    }

    public void setWhitelistedAppBundleIDs(List<String> whitelistedAppBundleIDs) {
        this.whitelistedAppBundleIDs = whitelistedAppBundleIDs;
    }
}
