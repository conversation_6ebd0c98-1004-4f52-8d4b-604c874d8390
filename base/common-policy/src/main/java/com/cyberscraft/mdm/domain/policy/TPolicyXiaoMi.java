package com.cyberscraft.mdm.domain.policy;

/**
 * @创建人 liuchunlin
 * @创建时间 18:21
 * @描述
 */
public class TPolicyXiaoMi {
    private TXiaoMiRestrictions restriction;
    private TXiaoMiApps xiaoMiApps;
    private TXiaoMiConfig xiaoMiConf = new TXiaoMiConfig();

    public TXiaoMiRestrictions getRestriction() {
        return restriction;
    }

    public void setRestriction(TXiaoMiRestrictions restriction) {
        this.restriction = restriction;
    }

    public TXiaoMiApps getXiaoMiApps() {
        return xiaoMiApps;
    }

    public void setXiaoMiApps(TXiaoMiApps xiaoMiApps) {
        this.xiaoMiApps = xiaoMiApps;
    }

    public TXiaoMiConfig getXiaoMiConf() {
        return xiaoMiConf;
    }

    public void setXiaoMiConf(TXiaoMiConfig xiaoMiConf) {
        this.xiaoMiConf = xiaoMiConf;
    }
}
