package com.cyberscraft.mdm.domain.policy;

public class GeoFenceInfo {
    private Double longitude;//经度
    private Double latitude;//纬度
    //private String center;//经度、维度
    private Float radius;//距离范围
    private String unit;//距离单位 1-km 2- mile

    public Float getRadius() {
        return radius;
    }

    public void setRadius(Float radius) {
        this.radius = radius;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public Double getLongitude() {
        return longitude;
    }

    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }

    public Double getLatitude() {
        return latitude;
    }

    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }

}
