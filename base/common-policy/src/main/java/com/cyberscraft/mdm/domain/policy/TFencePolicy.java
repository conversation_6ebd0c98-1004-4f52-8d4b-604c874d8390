package com.cyberscraft.mdm.domain.policy;

import java.util.List;

public class TFencePolicy {
    private List<GeoFenceInfo> geoFence;
    private DeviceRestriction deviceRestriction;
    private PolicyWifi wifiConfig;
    private TFencePolicyApp mamRestriction;
    private TFencePolicyContent mcmRestriction;
    private TSecureBrowser secureBrowser;
    private TCustomDesktop customDesktop;
    private TDingQiaoDoubleDomain TDDual;
    private TQingLongDoubleDomain QLDual;
    private THuaWeiEMUIFence huaWeiEMUIFence;
    private TXiaoMiFence tXiaoMiFence;
    private TPolicySingleapp policySingleapp;
    private IosWrappingConfig iosWrappingConfig;
    private String policyId;
    private String name;

    public TSecureBrowser getSecureBrowser() {
        return secureBrowser;
    }

    public void setSecureBrowser(TSecureBrowser secureBrowser) {
        this.secureBrowser = secureBrowser;
    }

    public List<GeoFenceInfo> getGeoFence() {
        return geoFence;
    }

    public void setGeoFence(List<GeoFenceInfo> geoFence) {
        this.geoFence = geoFence;
    }

    public DeviceRestriction getDeviceRestriction() {
        return deviceRestriction;
    }

    public void setDeviceRestriction(DeviceRestriction deviceRestriction) {
        this.deviceRestriction = deviceRestriction;
    }

    public TFencePolicyApp getMamRestriction() {
        return mamRestriction;
    }

    public void setMamRestriction(TFencePolicyApp mamRestriction) {
        this.mamRestriction = mamRestriction;
    }

    public TFencePolicyContent getMcmRestriction() {
        return mcmRestriction;
    }

    public void setMcmRestriction(TFencePolicyContent mcmRestriction) {
        this.mcmRestriction = mcmRestriction;
    }

    public String getPolicyId() {
        return policyId;
    }

    public void setPolicyId(String policyId) {
        this.policyId = policyId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public PolicyWifi getWifiConfig() {
        return wifiConfig;
    }

    public void setWifiConfig(PolicyWifi wifiConfig) {
        this.wifiConfig = wifiConfig;
    }

    public TCustomDesktop getCustomDesktop() {
        return customDesktop;
    }

    public void setCustomDesktop(TCustomDesktop customDesktop) {
        this.customDesktop = customDesktop;
    }

    public TDingQiaoDoubleDomain getTDDual() {
        return TDDual;
    }

    public void setTDDual(TDingQiaoDoubleDomain tDDual) {
        TDDual = tDDual;
    }

    public THuaWeiEMUIFence getHuaWeiEMUIFence() {
        return huaWeiEMUIFence;
    }

    public void setHuaWeiEMUIFence(THuaWeiEMUIFence huaWeiEMUIFence) {
        this.huaWeiEMUIFence = huaWeiEMUIFence;
    }

    public TXiaoMiFence gettXiaoMiFence() {
        return tXiaoMiFence;
    }

    public void settXiaoMiFence(TXiaoMiFence tXiaoMiFence) {
        this.tXiaoMiFence = tXiaoMiFence;
    }

    public TPolicySingleapp getPolicySingleapp() {
        return policySingleapp;
    }

    public void setPolicySingleapp(TPolicySingleapp policySingleapp) {
        this.policySingleapp = policySingleapp;
    }

    public TQingLongDoubleDomain getQLDual() {
        return QLDual;
    }

    public void setQLDual(TQingLongDoubleDomain QLDual) {
        this.QLDual = QLDual;
    }

    public IosWrappingConfig getIosWrappingConfig() {
        return iosWrappingConfig;
    }

    public void setIosWrappingConfig(IosWrappingConfig iosWrappingConfig) {
        this.iosWrappingConfig = iosWrappingConfig;
    }
}
