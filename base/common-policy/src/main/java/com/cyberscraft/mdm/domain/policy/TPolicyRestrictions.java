package com.cyberscraft.mdm.domain.policy;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Set;

public class TPolicyRestrictions implements java.io.Serializable {
    private static final long serialVersionUID = 1L;
    private String id;
    private Byte allowAppInstallation = Byte.valueOf((byte) 1);                    //允许安装应用程序 0--false;1--true
    private Byte allowCamera = Byte.valueOf((byte) 1);                             //允许使用相机  0--false;1--true
    private Byte allowSdCard = Byte.valueOf((byte) 1);                        //SD卡数据写入 0-不启用；1-启用
    private Byte allowWifi = Byte.valueOf((byte) 1);                            //允许使用wifi 0-不启用；1-启用
    private Byte allowBluetooth = Byte.valueOf((byte) 1);                      //允许使用蓝牙 0-不启用；1-启用
    private Byte allowCellular = Byte.valueOf((byte) 1);                          //允许移动数据 0-不启用；1-启用
    private Byte allowLockScreen = Byte.valueOf((byte) 1);                       //允许锁屏 0-不启用；1-启用
    private String lockScreenPwd;												//锁屏密码
    private Byte allowWebcam = Byte.valueOf((byte) 1);                            //允许摄像头 0-不启用；1-启用
    private Byte allowMicrophone = Byte.valueOf((byte) 1);                            //允许麦克风 0-不启用；1-启用
    private Byte allowDataInterface = Byte.valueOf((byte) 1);                            //允许数据接口 0-不启用；1-启用
    private Byte allowVideoConferencing = Byte.valueOf((byte) 1);                  //允许FaceTime 0--false;1--true
    private Byte allowScreenShot = Byte.valueOf((byte) 1);                         //允许屏幕捕捉  0--false;1--true
    private Byte allowGlobalBackgroundFetchWhenRoaming = Byte.valueOf((byte) 1);    //允许在漫游时自动同步 0--false;1--true
    private Byte allowAssistant = Byte.valueOf((byte) 1);                     //允许Siri 0--false;1--true
    private Byte allowAssistantWhileLocked = Byte.valueOf((byte) 1);          //设备锁定是允许使用Siri 0--false;1--true
    private Byte allowVoiceDialing = Byte.valueOf((byte) 1);                  //允许语音拨号 0--false;1--true
    private Byte allowInAppPurchases = Byte.valueOf((byte) 1);                //允许应用程序内购买 0--false;1--true
    private Byte forceITunesStorePasswordEntry = Byte.valueOf((byte) 0);      //强制用户为所有购买项目输入iTunes Store密码 0--false;1--true
    private Byte allowMultiplayerGaming = Byte.valueOf((byte) 1);             //允许多人游戏 0--false;1--true
    private Byte allowAddingGameCenterFriends = Byte.valueOf((byte) 1);       //允许添加Game Center朋友 0--false;1--true
    private TPolicyAppLimit appLimit = null;                                  //应用限制:黑白名单

    private Byte allowYouTube = Byte.valueOf((byte) 1);                      //允许YouTube 0--false;1--true
    private Byte allowiTunes = Byte.valueOf((byte) 1);                       //允许使用iTunes Store 0--false;1--true
    private Byte allowSafari = Byte.valueOf((byte) 1);                       //允许使用Safari 0--false;1--true
    private Byte safariAllowAutoFill = Byte.valueOf((byte) 1);              //启用自动填充 0--false;1--true
    private Byte safariAllowJavaScript = Byte.valueOf((byte) 1);            //启用JavaScript 0--false;1--true
    private Byte safariAllowPopups = Byte.valueOf((byte) 1);                //阻止弹出式窗口 0--false;1--true
    private Byte safariForceFraudWarning = Byte.valueOf((byte) 0);          //强制发出欺诈警告 0--false;1--true
    private Integer safariAcceptCookies = 0;                               //接受Cookie,0--永不;1--从所访问的站点;2--总是
    private Byte allowCloudBackup = Byte.valueOf((byte) 1);                //允许备份 0--false;1--true
    private Byte allowCloudDocumentSync = Byte.valueOf((byte) 1);          //允许文档同步 0--false;1--true
    private Byte allowPhotoStream = Byte.valueOf((byte) 1);                //允许照片流 0--false;1--true
    private Byte allowCloudPhotoLibrary = Byte.valueOf((byte) 1);           //允许iCloud照片图库（iOS9.0+）0--false;1--true
    private Byte allowDiagnosticSubmission = Byte.valueOf((byte) 1);       //允许向Apple发送诊断数据 0--false;1--true
    private Byte allowUntrustedTlsprompt = Byte.valueOf((byte) 1);         //允许用户接受不被信任的TLS证书 0--false;1--true
    private Byte forceEncryptedBackup = Byte.valueOf((byte) 0);           //强制对备份进行加密 0--false;1--true
    private Byte allowSingleApp = (byte) 1;                               // 允许单应用  0--false;1--true
    private String ratingRegion = "us";                                  //评级地区，默认us-美国.au-澳大利亚 ca-加拿大 de-德国  fr-法国  ie-爱尔兰  jp-日本  nz-新西兰  gb-英国
    private Integer ratingMovies = 1000;                                 //1000--允许所有影片
    private Integer ratingTVShows = 1000;                                //1000--允许所有电视节目
    private Integer ratingApps = 1000;                                   //应用评级功能:0：全部不允许，100：4+，200：9+，300：12+，600：17+，1000：全部允许


    private Byte forceAssistantProfanityFilter = Byte.valueOf((byte) 0); // 强制siri脏话过滤器 默认不强制
    private Byte allowAssistantUserGeneratedContent = Byte.valueOf((byte) 1); // 在siri中显示用户生成的内容
    private Byte allowBookstore = Byte.valueOf((byte) 1); // 允许使用iBookStore
    private Byte allowAppRemoval = Byte.valueOf((byte) 1); // 允许移除应用程序
    private Byte allowOTAPKIUpdates = Byte.valueOf((byte) 1); // 允许空中PKI更新（iOS7+）
    private Byte allowUIConfigurationProfileInstallation = Byte.valueOf((byte) 1); // 允许安装配置描述文件
    private Byte allowAccountModification = Byte.valueOf((byte) 1); // 允许iCloud帐号修改
    private Byte allowFindMyFriendsModification = Byte.valueOf((byte) 1); // 允许修改“查找我的朋友”设置
    private Byte allowHostPairing = Byte.valueOf((byte) 1); // 允许与未安装Configurator的主机配对
    private Byte allowOpenFromUnmanagedToManaged = Byte.valueOf((byte) 1); // 允许在未管理的应用中打开管理中的文档
    private Byte allowOpenFromManagedToUnmanaged = Byte.valueOf((byte) 1); // 允许在管理中的应用打开未管理的文档

    private Byte forceAirPlayIncomingRequestsPairingPassword = Byte.valueOf((byte) 0); // 强制使用配对密码发送AirPlay请求 默认不强制
    private Byte allowAppCellularDataModification = Byte.valueOf((byte) 1); // 允许修改应用蜂窝移动数据
    private Set<String> autonomousSingAppModePermittedAppIDs = new HashSet<String>(); // 允许确认的App IDs自动进入单应用模式
    private Byte allowGameCenter = Byte.valueOf((byte) 1); // 允许使用Game Center
    private Byte allowExplicitContent = Byte.valueOf((byte) 1);           //允许不良音乐、Podcast与iTunes U 0--false;1--true
    private Byte allowBookstoreErotica = Byte.valueOf((byte) 1); // 允许iBookstore色情书籍
    private transient String autonomousSingAppModePermittedAppNames;
    private String payloadDescription = "";
    private String payloadDisplayName = "Restrictions";
    private String payloadIdentifier = "{0}.restrictions1";
    private String payloadType = "com.apple.applicationaccess";

    private Byte forceLimitAdTracking = Byte.valueOf((byte) 1); // 强制有限广告跟踪 默认不强制
    private Byte allowCloudKeychainSync = Byte.valueOf((byte) 1); // 允许云钥匙串
    private Byte allowSharedStream = Byte.valueOf((byte) 1); // 允许云照片共享
    private Byte allowFingerprintForUnlock = Byte.valueOf((byte) 1); // 允许使用Touch ID解锁设备。对于android来说，允许指纹解锁 0--false;1--true
    private Byte allowManagedAppsCloudSync = Byte.valueOf((byte) 1); // 允许被管理的应用程序将数据存储到iCloud
    private Byte allowLockScreenTodayView = Byte.valueOf((byte) 1); // 在锁定屏幕中显示今日视图
    private Byte allowLockScreenNotificationsView = Byte.valueOf((byte) 1); // 在锁定屏幕中显示通知中心
    private Byte allowLockScreenControlCenter = Byte.valueOf((byte) 1); // 在锁定屏幕中显示控制中心
    private Byte allowPassbookWhileLocked = Byte.valueOf((byte) 1); // 设备在锁定屏幕时显示Passbook消息
    private Byte allowMobileHotspot = Byte.valueOf((byte) 1); // 允许移动热点（Android4.0+）
    private Byte allowGps = Byte.valueOf((byte) 1); //开启GPS-1,关闭GPS-0   0--false;1--true
    private Byte allowSlot2 = Byte.valueOf((byte) 1);     //允许使用卡槽2 0--false;1--true
    private Byte allowSystemUpdate = Byte.valueOf((byte) 1);     //系统升级 0--false;1--true

    private Byte allowAirDrop;       //设备禁止Airdrop  0--false;1--true
    private Byte allowPasscodeModification;       //允许修改设备密码0--false;1--true
    private Byte forceAirPlayOutgoingRequestsPairingPassword;       //设备首次进行AirPlay配对时需要输入密码0--false;1--true
    private Byte allowKeyboardShortcuts;       //禁用输入法快捷方式0--false;1--true
    private Byte allowChat;       //设备允许使用iMessage0--false;1--true
    private Byte allowDeviceNameModification;       //允许修改设备名称   0--false;1--true
    private Byte allowWallpaperModification;       //允许修改设备壁纸0--false;1--true
    private Byte allowSpotlightInternetResults;       //设备允许Spotlight显示互联网搜索结果0--false;1--true
    private Byte allowAutomaticAppDownloads;       //禁用 Automatic App Downloads  0--false;1--true
    private Byte allowEnterpriseAppTrust;           //[Trust UI] ++
    private Byte allowFingerprintModification;        //[允许对Touch ID进行修改（iOS8.3+监管模式）
    private Byte forceAirDropUnmanaged;              //将AirDrop视为未被管理的目的位置
    private Byte allowCompanyApp;              //允许信任企业应用（iOS9.0+）
    private Byte allowPairedWatch;              //允许配对Apple Watch（iOS9+监管）
    private Byte allowActivityContinuation;              //允许使用handoff（iOS8.0+）
    private Byte forceWatchWristDetection;              // 强制进行Apple Watch手腕检测功能（iOS8.2+）
    private Byte allowPredictiveKeyboard;              // 允许键盘的预测功能（iOS8.1.3+监管模式）
    private Byte allowAutoCorrection;              //  允许键盘的自动改正功能（iOS8.1.3+监管模式）
    private Byte allowSpellCheck;              // 允许键盘的拼写检查功能（iOS8.1.3+监管模式）  0--false;1--true
    private Byte allowDefinitionLookup;              // 允许键盘的拼写检查功能（iOS8.1.3+监管模式）  0--false;1--true
    private Byte allowUIAppInstallation;              // 允许键盘的拼写检查功能（iOS8.1.3+监管模式）  0--false;1--true
    private Byte allowPodcasts;              //允许播客（iOS8+ 监管模式）  0--false;1--true
    private Byte allowEraseContentAndSettings;              //允许擦除所有内容和配置（iOS8+ 监管模式）   0--false;1--true
    private Byte allowEnablingRestrictions;              //允许配置限制（iOS8+ 监管模式）   0--false;1--true

    private Byte allowFactoryReset;
    private Byte allowAudioRecord;
    private Byte allowUsbFileTransfer;
    private Byte allowModifyDateAndTime;
    private List<String> forbitApp;
    private List<String> callWhiteList  =new ArrayList<String>();
    private Byte allowInstallUnknowSource = Byte.valueOf((byte) 1); // 允许安装未知来源的应用
    private Byte allowMobileSafeMode = Byte.valueOf((byte) 1); // 允许进入安全模式
    private Byte allowUsbDebugMode = Byte.valueOf((byte) 1); // 允许通过USB调试模式

    private Byte allowInstApp;//允许安装应用 0--false;1--true Android5.0及以上设备，且离线刷机设备
    private Byte allowUninstApp;//允许卸载应用 0--false;1--true Android5.0及以上设备，且离线刷机设备
    private Byte allowCall;//允许拨打电话 0--false;1--true Android5.0及以上设备，且离线刷机设备
    private Byte allowMessage;//允许收发短信 0--false;1--true Android5.0及以上设备，且离线刷机设备
    private Byte allowNFC; //允许使用NFC传输数据 0--false;1--true Android5.1及以上设备，且离线刷机设备
    private Byte allowBtShareContact;//允许通过蓝牙传输通讯录 0--false;1--true Android6.0及以上设备，且离线刷机设备
    private Byte enableLockScreen;//禁用/启用锁屏 0--false;1--true Android6.0及以上设备，且离线刷机设备
    private Byte allowPulldownMenu;//允许启用状态栏下拉菜单 0--false;1--true Android6.0及以上设备，且离线刷机设备
    private Byte allowRoaming;//允许移动数据网络漫游 0--false;1--true Android7.0及以上设备，且离线刷机设备
    private Byte allowChgAvatar;//允许修改用户图像 0--false;1--true Android7.0及以上设备，且离线刷机设备
    private Byte allowChgWallpaper;//允许修改墙纸 0--false;1--true Android7.0及以上设备，且离线刷机设备
    private List<String> forbidUninstApp;//禁止卸载应用 Android5.0及以上设备，且离线刷机设备
    private Byte allowAppBehaviorLog; //允许通过用户行为日志0--false;1--true
    private Byte allowBluetoothModification; //允许修改蓝牙设置（iOS10+监管模式）  0--false;1--true

    private List<String> dedicateDeviceAppList; //[EMM国际版]单用途设备应用列表

    public List<String> getForbitApp() {
        return forbitApp;
    }

    public void setForbitApp(List<String> forbitApp) {
        this.forbitApp = forbitApp;
    }

    public Byte getAllowEraseContentAndSettings() {
        return allowEraseContentAndSettings;
    }

    public void setAllowEraseContentAndSettings(Byte allowEraseContentAndSettings) {
        this.allowEraseContentAndSettings = allowEraseContentAndSettings;
    }

    public Byte getAllowEnablingRestrictions() {
        return allowEnablingRestrictions;
    }

    public void setAllowEnablingRestrictions(Byte allowEnablingRestrictions) {
        this.allowEnablingRestrictions = allowEnablingRestrictions;
    }

    public Byte getAllowPodcasts() {
        return allowPodcasts;
    }

    public void setAllowPodcasts(Byte allowPodcasts) {
        this.allowPodcasts = allowPodcasts;
    }

    public Byte getAllowUIAppInstallation() {
        return allowUIAppInstallation;
    }

    public void setAllowUIAppInstallation(Byte allowUIAppInstallation) {
        this.allowUIAppInstallation = allowUIAppInstallation;
    }

    public Byte getAllowDefinitionLookup() {
        return allowDefinitionLookup;
    }

    public void setAllowDefinitionLookup(Byte allowDefinitionLookup) {
        this.allowDefinitionLookup = allowDefinitionLookup;
    }

    public Byte getAllowSpellCheck() {
        return allowSpellCheck;
    }

    public void setAllowSpellCheck(Byte allowSpellCheck) {
        this.allowSpellCheck = allowSpellCheck;
    }

    public Byte getAllowAutoCorrection() {
        return allowAutoCorrection;
    }

    public void setAllowAutoCorrection(Byte allowAutoCorrection) {
        this.allowAutoCorrection = allowAutoCorrection;
    }

    public Byte getAllowPredictiveKeyboard() {
        return allowPredictiveKeyboard;
    }

    public void setAllowPredictiveKeyboard(Byte allowPredictiveKeyboard) {
        this.allowPredictiveKeyboard = allowPredictiveKeyboard;
    }

    public Byte getForceWatchWristDetection() {
        return forceWatchWristDetection;
    }

    public void setForceWatchWristDetection(Byte forceWatchWristDetection) {
        this.forceWatchWristDetection = forceWatchWristDetection;
    }

    public Byte getAllowActivityContinuation() {
        return allowActivityContinuation;
    }

    public void setAllowActivityContinuation(Byte allowActivityContinuation) {
        this.allowActivityContinuation = allowActivityContinuation;
    }

    public Byte getAllowPairedWatch() {
        return allowPairedWatch;
    }

    public void setAllowPairedWatch(Byte allowPairedWatch) {
        this.allowPairedWatch = allowPairedWatch;
    }

    public Byte getAllowCompanyApp() {
        return allowCompanyApp;
    }

    public void setAllowCompanyApp(Byte allowCompanyApp) {
        this.allowCompanyApp = allowCompanyApp;
    }

    public Byte getForceAirDropUnmanaged() {
        return forceAirDropUnmanaged;
    }

    public void setForceAirDropUnmanaged(Byte forceAirDropUnmanaged) {
        this.forceAirDropUnmanaged = forceAirDropUnmanaged;
    }

    public Byte getAllowFingerprintModification() {
        return allowFingerprintModification;
    }

    public void setAllowFingerprintModification(Byte allowFingerprintModification) {
        this.allowFingerprintModification = allowFingerprintModification;
    }

    public Byte getAllowPasscodeModification() {
        return allowPasscodeModification;
    }

    public void setAllowPasscodeModification(Byte allowPasscodeModification) {
        this.allowPasscodeModification = allowPasscodeModification;
    }

    public Byte getAllowKeyboardShortcuts() {
        return allowKeyboardShortcuts;
    }

    public void setAllowKeyboardShortcuts(Byte allowKeyboardShortcuts) {
        this.allowKeyboardShortcuts = allowKeyboardShortcuts;
    }

    public Byte getAllowDeviceNameModification() {
        return allowDeviceNameModification;
    }

    public void setAllowDeviceNameModification(Byte allowDeviceNameModification) {
        this.allowDeviceNameModification = allowDeviceNameModification;
    }

    public Byte getAllowWallpaperModification() {
        return allowWallpaperModification;
    }

    public void setAllowWallpaperModification(Byte allowWallpaperModification) {
        this.allowWallpaperModification = allowWallpaperModification;
    }

    public Byte getAllowSpotlightInternetResults() {
        return allowSpotlightInternetResults;
    }

    public void setAllowSpotlightInternetResults(Byte allowSpotlightInternetResults) {
        this.allowSpotlightInternetResults = allowSpotlightInternetResults;
    }

    public Byte getAllowAutomaticAppDownloads() {
        return allowAutomaticAppDownloads;
    }

    public void setAllowAutomaticAppDownloads(Byte allowAutomaticAppDownloads) {
        this.allowAutomaticAppDownloads = allowAutomaticAppDownloads;
    }

    public Byte getAllowEnterpriseAppTrust() {
        return allowEnterpriseAppTrust;
    }

    public void setAllowEnterpriseAppTrust(Byte allowEnterpriseAppTrust) {
        this.allowEnterpriseAppTrust = allowEnterpriseAppTrust;
    }

    public static long getSerialversionuid() {
        return serialVersionUID;
    }

    public Byte getAllowMobileHotspot() {
        return allowMobileHotspot;
    }

    public void setAllowMobileHotspot(Byte allowMobileHotspot) {
        this.allowMobileHotspot = allowMobileHotspot;
    }

    public Byte getAllowGps() {
        return allowGps;
    }

    public void setAllowGps(Byte allowGps) {
        this.allowGps = allowGps;
    }

    public Byte getAllowManagedAppsCloudSync() {
        return allowManagedAppsCloudSync;
    }

    public void setAllowManagedAppsCloudSync(Byte allowManagedAppsCloudSync) {
        this.allowManagedAppsCloudSync = allowManagedAppsCloudSync;
    }

    public Byte getAllowPassbookWhileLocked() {
        return allowPassbookWhileLocked;
    }

    public void setAllowPassbookWhileLocked(Byte allowPassbookWhileLocked) {
        this.allowPassbookWhileLocked = allowPassbookWhileLocked;
    }

    public TPolicyRestrictions() {
    }

    public Byte getAllowSlot2() {
        return allowSlot2;
    }

    public void setAllowSlot2(Byte allowSlot2) {
        this.allowSlot2 = allowSlot2;
    }

    public Byte getAllowSystemUpdate() {
        return allowSystemUpdate;
    }

    public void setAllowSystemUpdate(Byte allowSystemUpdate) {
        this.allowSystemUpdate = allowSystemUpdate;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Byte getAllowAppInstallation() {
        return this.allowAppInstallation;
    }

    public void setAllowAppInstallation(Byte allowAppInstallation) {
        this.allowAppInstallation = allowAppInstallation;
    }

    public Byte getAllowCamera() {
        return this.allowCamera;
    }

    public void setAllowCamera(Byte allowCamera) {
        this.allowCamera = allowCamera;
    }

    public Byte getAllowVideoConferencing() {
        return this.allowVideoConferencing;
    }

    public void setAllowVideoConferencing(Byte allowVideoConferencing) {
        this.allowVideoConferencing = allowVideoConferencing;
    }

    public Byte getAllowScreenShot() {
        return this.allowScreenShot;
    }

    public void setAllowScreenShot(Byte allowScreenShot) {
        this.allowScreenShot = allowScreenShot;
    }

    public Byte getAllowAssistant() {
        return this.allowAssistant;
    }

    public void setAllowAssistant(Byte allowAssistant) {
        this.allowAssistant = allowAssistant;
    }

    public Byte getAllowAssistantWhileLocked() {
        return this.allowAssistantWhileLocked;
    }

    public void setAllowAssistantWhileLocked(Byte allowAssistantWhileLocked) {
        this.allowAssistantWhileLocked = allowAssistantWhileLocked;
    }

    public Byte getAllowVoiceDialing() {
        return this.allowVoiceDialing;
    }

    public void setAllowVoiceDialing(Byte allowVoiceDialing) {
        this.allowVoiceDialing = allowVoiceDialing;
    }

    public Byte getAllowInAppPurchases() {
        return this.allowInAppPurchases;
    }

    public void setAllowInAppPurchases(Byte allowInAppPurchases) {
        this.allowInAppPurchases = allowInAppPurchases;
    }

    public Byte getForceITunesStorePasswordEntry() {
        return forceITunesStorePasswordEntry;
    }

    public void setForceITunesStorePasswordEntry(Byte forceITunesStorePasswordEntry) {
        this.forceITunesStorePasswordEntry = forceITunesStorePasswordEntry;
    }

    public Byte getAllowMultiplayerGaming() {
        return this.allowMultiplayerGaming;
    }

    public void setAllowMultiplayerGaming(Byte allowMultiplayerGaming) {
        this.allowMultiplayerGaming = allowMultiplayerGaming;
    }

    public Byte getAllowAddingGameCenterFriends() {
        return this.allowAddingGameCenterFriends;
    }

    public void setAllowAddingGameCenterFriends(Byte allowAddingGameCenterFriends) {
        this.allowAddingGameCenterFriends = allowAddingGameCenterFriends;
    }

    public Byte getAllowYouTube() {
        return this.allowYouTube;
    }

    public void setAllowYouTube(Byte allowYouTube) {
        this.allowYouTube = allowYouTube;
    }

    public Byte getAllowiTunes() {
        return this.allowiTunes;
    }

    public void setAllowiTunes(Byte allowiTunes) {
        this.allowiTunes = allowiTunes;
    }

    public Byte getAllowSafari() {
        return this.allowSafari;
    }

    public void setAllowSafari(Byte allowSafari) {
        this.allowSafari = allowSafari;
    }

    public Byte getSafariAllowAutoFill() {
        return this.safariAllowAutoFill;
    }

    public void setSafariAllowAutoFill(Byte safariAllowAutoFill) {
        this.safariAllowAutoFill = safariAllowAutoFill;
    }

    public Byte getSafariAllowJavaScript() {
        return this.safariAllowJavaScript;
    }

    public void setSafariAllowJavaScript(Byte safariAllowJavaScript) {
        this.safariAllowJavaScript = safariAllowJavaScript;
    }

    public Byte getSafariAllowPopups() {
        return this.safariAllowPopups;
    }

    public void setSafariAllowPopups(Byte safariAllowPopups) {
        this.safariAllowPopups = safariAllowPopups;
    }

    public Byte getSafariForceFraudWarning() {
        return this.safariForceFraudWarning;
    }

    public void setSafariForceFraudWarning(Byte safariForceFraudWarning) {
        this.safariForceFraudWarning = safariForceFraudWarning;
    }

    public Integer getSafariAcceptCookies() {
        return this.safariAcceptCookies;
    }

    public void setSafariAcceptCookies(Integer safariAcceptCookies) {
        this.safariAcceptCookies = safariAcceptCookies;
    }

    public Byte getAllowCloudBackup() {
        return this.allowCloudBackup;
    }

    public void setAllowCloudBackup(Byte allowCloudBackup) {
        this.allowCloudBackup = allowCloudBackup;
    }

    public Byte getAllowCloudDocumentSync() {
        return this.allowCloudDocumentSync;
    }

    public void setAllowCloudDocumentSync(Byte allowCloudDocumentSync) {
        this.allowCloudDocumentSync = allowCloudDocumentSync;
    }

    public Byte getAllowPhotoStream() {
        return this.allowPhotoStream;
    }

    public void setAllowPhotoStream(Byte allowPhotoStream) {
        this.allowPhotoStream = allowPhotoStream;
    }

    public Byte getAllowDiagnosticSubmission() {
        return this.allowDiagnosticSubmission;
    }

    public void setAllowDiagnosticSubmission(Byte allowDiagnosticSubmission) {
        this.allowDiagnosticSubmission = allowDiagnosticSubmission;
    }

    public Byte getAllowUntrustedTlsprompt() {
        return this.allowUntrustedTlsprompt;
    }

    public void setAllowUntrustedTlsprompt(Byte allowUntrustedTlsprompt) {
        this.allowUntrustedTlsprompt = allowUntrustedTlsprompt;
    }

    public Byte getForceEncryptedBackup() {
        return this.forceEncryptedBackup;
    }

    public void setForceEncryptedBackup(Byte forceEncryptedBackup) {
        this.forceEncryptedBackup = forceEncryptedBackup;
    }

    public Byte getAllowExplicitContent() {
        return this.allowExplicitContent;
    }

    public void setAllowExplicitContent(Byte allowExplicitContent) {
        this.allowExplicitContent = allowExplicitContent;
    }

    public String getRatingRegion() {
        return this.ratingRegion;
    }

    public void setRatingRegion(String ratingRegion) {
        this.ratingRegion = ratingRegion;
    }

    public Integer getRatingMovies() {
        return this.ratingMovies;
    }

    public void setRatingMovies(Integer ratingMovies) {
        this.ratingMovies = ratingMovies;
    }

    public Integer getRatingTVShows() {
        return ratingTVShows;
    }

    public void setRatingTVShows(Integer ratingTVShows) {
        this.ratingTVShows = ratingTVShows;
    }

    public Integer getRatingApps() {
        return this.ratingApps;
    }

    public void setRatingApps(Integer ratingApps) {
        this.ratingApps = ratingApps;
    }

    public String getPayloadDescription() {
        return payloadDescription;
    }

    public void setPayloadDescription(String payloadDescription) {
        this.payloadDescription = payloadDescription;
    }

    public String getPayloadDisplayName() {
        return payloadDisplayName;
    }

    public void setPayloadDisplayName(String payloadDisplayName) {
        this.payloadDisplayName = payloadDisplayName;
    }

    public String getPayloadIdentifier() {
        return payloadIdentifier;
    }

    public void setPayloadIdentifier(String payloadIdentifier) {
        this.payloadIdentifier = payloadIdentifier;
    }

    public String getPayloadType() {
        return payloadType;
    }

    public void setPayloadType(String payloadType) {
        this.payloadType = payloadType;
    }

    public Byte getAllowSdCard() {
        return allowSdCard;
    }

    public void setAllowSdCard(Byte allowSdCard) {
        this.allowSdCard = allowSdCard;
    }



    public Byte getAllowGlobalBackgroundFetchWhenRoaming() {
        return allowGlobalBackgroundFetchWhenRoaming;
    }

    public void setAllowGlobalBackgroundFetchWhenRoaming(
            Byte allowGlobalBackgroundFetchWhenRoaming) {
        this.allowGlobalBackgroundFetchWhenRoaming = allowGlobalBackgroundFetchWhenRoaming;
    }

    public Byte getAllowSingleApp() {
        return allowSingleApp;
    }

    public void setAllowSingleApp(Byte allowSingleApp) {
        this.allowSingleApp = allowSingleApp;
    }

    public Byte getAllowAirDrop() {
        return allowAirDrop;
    }

    public void setAllowAirDrop(Byte allowAirDrop) {
        this.allowAirDrop = allowAirDrop;
    }

    public Byte getAllowChat() {
        return allowChat;
    }

    public void setAllowChat(Byte allowChat) {
        this.allowChat = allowChat;
    }

    public Byte getForceAssistantProfanityFilter() {
        return forceAssistantProfanityFilter;
    }

    public void setForceAssistantProfanityFilter(Byte forceAssistantProfanityFilter) {
        this.forceAssistantProfanityFilter = forceAssistantProfanityFilter;
    }

    public Byte getAllowBookstore() {
        return allowBookstore;
    }

    public void setAllowBookstore(Byte allowBookstore) {
        this.allowBookstore = allowBookstore;
    }

    public Byte getAllowAppRemoval() {
        return allowAppRemoval;
    }

    public void setAllowAppRemoval(Byte allowAppRemoval) {
        this.allowAppRemoval = allowAppRemoval;
    }

    public Byte getAllowCloudKeychainSync() {
        return allowCloudKeychainSync;
    }

    public void setAllowCloudKeychainSync(Byte allowCloudKeychainSync) {
        this.allowCloudKeychainSync = allowCloudKeychainSync;
    }

    public Byte getAllowSharedStream() {
        return allowSharedStream;
    }

    public void setAllowSharedStream(Byte allowSharedStream) {
        this.allowSharedStream = allowSharedStream;
    }

    public Byte getForceLimitAdTracking() {
        return forceLimitAdTracking;
    }

    public void setForceLimitAdTracking(Byte forceLimitAdTracking) {
        this.forceLimitAdTracking = forceLimitAdTracking;
    }

    public Byte getAllowOTAPKIUpdates() {
        return allowOTAPKIUpdates;
    }

    public void setAllowOTAPKIUpdates(Byte allowOTAPKIUpdates) {
        this.allowOTAPKIUpdates = allowOTAPKIUpdates;
    }

    public Byte getAllowAccountModification() {
        return allowAccountModification;
    }

    public void setAllowAccountModification(Byte allowAccountModification) {
        this.allowAccountModification = allowAccountModification;
    }

    public Byte getAllowFindMyFriendsModification() {
        return allowFindMyFriendsModification;
    }

    public void setAllowFindMyFriendsModification(Byte allowFindMyFriendsModification) {
        this.allowFindMyFriendsModification = allowFindMyFriendsModification;
    }

    public Byte getAllowHostPairing() {
        return allowHostPairing;
    }

    public void setAllowHostPairing(Byte allowHostPairing) {
        this.allowHostPairing = allowHostPairing;
    }

    public Byte getAllowOpenFromUnmanagedToManaged() {
        return allowOpenFromUnmanagedToManaged;
    }

    public void setAllowOpenFromUnmanagedToManaged(Byte allowOpenFromUnmanagedToManaged) {
        this.allowOpenFromUnmanagedToManaged = allowOpenFromUnmanagedToManaged;
    }

    public Byte getAllowOpenFromManagedToUnmanaged() {
        return allowOpenFromManagedToUnmanaged;
    }

    public void setAllowOpenFromManagedToUnmanaged(Byte allowOpenFromManagedToUnmanaged) {
        this.allowOpenFromManagedToUnmanaged = allowOpenFromManagedToUnmanaged;
    }

    public Byte getAllowFingerprintForUnlock() {
        return allowFingerprintForUnlock;
    }

    public void setAllowFingerprintForUnlock(Byte allowFingerprintForUnlock) {
        this.allowFingerprintForUnlock = allowFingerprintForUnlock;
    }

    public Byte getAllowLockScreenTodayView() {
        return allowLockScreenTodayView;
    }

    public void setAllowLockScreenTodayView(Byte allowLockScreenTodayView) {
        this.allowLockScreenTodayView = allowLockScreenTodayView;
    }

    public Byte getAllowAppCellularDataModification() {
        return allowAppCellularDataModification;
    }

    public void setAllowAppCellularDataModification(Byte allowAppCellularDataModification) {
        this.allowAppCellularDataModification = allowAppCellularDataModification;
    }

    public Byte getAllowGameCenter() {
        return allowGameCenter;
    }

    public void setAllowGameCenter(Byte allowGameCenter) {
        this.allowGameCenter = allowGameCenter;
    }

    public Byte getAllowBookstoreErotica() {
        return allowBookstoreErotica;
    }

    public void setAllowBookstoreErotica(Byte allowBookstoreErotica) {
        this.allowBookstoreErotica = allowBookstoreErotica;
    }

    public Byte getAllowAssistantUserGeneratedContent() {
        return allowAssistantUserGeneratedContent;
    }

    public void setAllowAssistantUserGeneratedContent(Byte allowAssistantUserGeneratedContent) {
        this.allowAssistantUserGeneratedContent = allowAssistantUserGeneratedContent;
    }

    public Byte getAllowUIConfigurationProfileInstallation() {
        return allowUIConfigurationProfileInstallation;
    }

    public void setAllowUIConfigurationProfileInstallation(Byte allowUIConfigurationProfileInstallation) {
        this.allowUIConfigurationProfileInstallation = allowUIConfigurationProfileInstallation;
    }

    public Byte getForceAirPlayOutgoingRequestsPairingPassword() {
        return forceAirPlayOutgoingRequestsPairingPassword;
    }

    public void setForceAirPlayOutgoingRequestsPairingPassword(Byte forceAirPlayOutgoingRequestsPairingPassword) {
        this.forceAirPlayOutgoingRequestsPairingPassword = forceAirPlayOutgoingRequestsPairingPassword;
    }

    public Byte getForceAirPlayIncomingRequestsPairingPassword() {
        return forceAirPlayIncomingRequestsPairingPassword;
    }

    public void setForceAirPlayIncomingRequestsPairingPassword(Byte forceAirPlayIncomingRequestsPairingPassword) {
        this.forceAirPlayIncomingRequestsPairingPassword = forceAirPlayIncomingRequestsPairingPassword;
    }

    public Byte getAllowLockScreenControlCenter() {
        return allowLockScreenControlCenter;
    }

    public void setAllowLockScreenControlCenter(Byte allowLockScreenControlCenter) {
        this.allowLockScreenControlCenter = allowLockScreenControlCenter;
    }

    public Byte getAllowLockScreenNotificationsView() {
        return allowLockScreenNotificationsView;
    }

    public void setAllowLockScreenNotificationsView(Byte allowLockScreenNotificationsView) {
        this.allowLockScreenNotificationsView = allowLockScreenNotificationsView;
    }

    public Byte getAllowBluetoothModification() {
        return allowBluetoothModification;
    }

    public void setAllowBluetoothModification(Byte allowBluetoothModification) {
        this.allowBluetoothModification = allowBluetoothModification;
    }

    public Set<String> getAutonomousSingAppModePermittedAppIDs() {
        return autonomousSingAppModePermittedAppIDs;
    }

    public String getAutonomousSingAppModePermittedAppIDsStr() {
        if (autonomousSingAppModePermittedAppIDs == null) {
            return "";
        } else {
            StringBuilder sb = new StringBuilder();
            Iterator<String> it = autonomousSingAppModePermittedAppIDs.iterator();
            if (it.hasNext()) {
                sb.append(it.next());
            } else {
                return "";
            }

            while (it.hasNext()) {
                sb.append(",");
                sb.append(it.next());
            }

            return sb.toString();
        }
    }


    public void setAutonomousSingAppModePermittedAppIDs(Set<String> autonomousSingAppModePermittedAppIDs) {
        this.autonomousSingAppModePermittedAppIDs = autonomousSingAppModePermittedAppIDs;
    }

    public String getAutonomousSingAppModePermittedAppNames() {
        return autonomousSingAppModePermittedAppNames;
    }

    public void setAutonomousSingAppModePermittedAppNames(String autonomousSingAppModePermittedAppNames) {
        this.autonomousSingAppModePermittedAppNames = autonomousSingAppModePermittedAppNames;
    }

    public Byte getAllowWifi() {
        return allowWifi;
    }

    public void setAllowWifi(Byte allowWifi) {
        this.allowWifi = allowWifi;
    }

    public Byte getAllowBluetooth() {
        return allowBluetooth;
    }

    public void setAllowBluetooth(Byte allowBluetooth) {
        this.allowBluetooth = allowBluetooth;
    }

    public Byte getAllowCellular() {
        return allowCellular;
    }

    public void setAllowCellular(Byte allowCellular) {
        this.allowCellular = allowCellular;
    }

    public Byte getAllowLockScreen() {
        return allowLockScreen;
    }

    public void setAllowLockScreen(Byte allowLockScreen) {
        this.allowLockScreen = allowLockScreen;
    }

    public String getLockScreenPwd() {
        return lockScreenPwd;
    }

    public void setLockScreenPwd(String lockScreenPwd) {
        this.lockScreenPwd = lockScreenPwd;
    }

    public Byte getAllowWebcam() {
        return allowWebcam;
    }

    public void setAllowWebcam(Byte allowWebcam) {
        this.allowWebcam = allowWebcam;
    }

    public Byte getAllowMicrophone() {
        return allowMicrophone;
    }

    public void setAllowMicrophone(Byte allowMicrophone) {
        this.allowMicrophone = allowMicrophone;
    }

    public Byte getAllowDataInterface() {
        return allowDataInterface;
    }

    public void setAllowDataInterface(Byte allowDataInterface) {
        this.allowDataInterface = allowDataInterface;
    }

    public Byte getAllowFactoryReset() {
        return allowFactoryReset;
    }

    public void setAllowFactoryReset(Byte allowFactoryReset) {
        this.allowFactoryReset = allowFactoryReset;
    }

    public Byte getAllowAudioRecord() {
        return allowAudioRecord;
    }

    public void setAllowAudioRecord(Byte allowAudioRecord) {
        this.allowAudioRecord = allowAudioRecord;
    }

    public Byte getAllowUsbFileTransfer() {
        return allowUsbFileTransfer;
    }

    public void setAllowUsbFileTransfer(Byte allowUsbFileTransfer) {
        this.allowUsbFileTransfer = allowUsbFileTransfer;
    }

    public Byte getAllowModifyDateAndTime() {
        return allowModifyDateAndTime;
    }

    public void setAllowModifyDateAndTime(Byte allowModifyDateAndTime) {
        this.allowModifyDateAndTime = allowModifyDateAndTime;
    }

    public Byte getAllowCloudPhotoLibrary() {
        return allowCloudPhotoLibrary;
    }

    public void setAllowCloudPhotoLibrary(Byte allowCloudPhotoLibrary) {
        this.allowCloudPhotoLibrary = allowCloudPhotoLibrary;
    }

    public TPolicyAppLimit getAppLimit() {
        return appLimit;
    }

    public void setAppLimit(TPolicyAppLimit appLimit) {
        this.appLimit = appLimit;
    }

    public List<String> getCallWhiteList() {
        return callWhiteList;
    }

    public void setCallWhiteList(List<String> callWhiteList) {
        this.callWhiteList = callWhiteList;
    }

    public Byte getAllowInstallUnknowSource() {
        return allowInstallUnknowSource;
    }

    public void setAllowInstallUnknowSource(Byte allowInstallUnknowSource) {
        this.allowInstallUnknowSource = allowInstallUnknowSource;
    }

    public Byte getAllowMobileSafeMode() {
        return allowMobileSafeMode;
    }

    public void setAllowMobileSafeMode(Byte allowMobileSafeMode) {
        this.allowMobileSafeMode = allowMobileSafeMode;
    }

    public Byte getAllowUsbDebugMode() {
        return allowUsbDebugMode;
    }

    public void setAllowUsbDebugMode(Byte allowUsbDebugMode) {
        this.allowUsbDebugMode = allowUsbDebugMode;
    }

    public Byte getAllowInstApp() {
        return allowInstApp;
    }

    public void setAllowInstApp(Byte allowInstApp) {
        this.allowInstApp = allowInstApp;
    }

    public Byte getAllowUninstApp() {
        return allowUninstApp;
    }

    public void setAllowUninstApp(Byte allowUninstApp) {
        this.allowUninstApp = allowUninstApp;
    }

    public Byte getAllowCall() {
        return allowCall;
    }

    public void setAllowCall(Byte allowCall) {
        this.allowCall = allowCall;
    }

    public Byte getAllowNFC() {
        return allowNFC;
    }

    public void setAllowNFC(Byte allowNFC) {
        this.allowNFC = allowNFC;
    }

    public Byte getAllowBtShareContact() {
        return allowBtShareContact;
    }

    public void setAllowBtShareContact(Byte allowBtShareContact) {
        this.allowBtShareContact = allowBtShareContact;
    }

    public Byte getEnableLockScreen() {
        return enableLockScreen;
    }

    public void setEnableLockScreen(Byte enableLockScreen) {
        this.enableLockScreen = enableLockScreen;
    }

    public Byte getAllowRoaming() {
        return allowRoaming;
    }

    public void setAllowRoaming(Byte allowRoaming) {
        this.allowRoaming = allowRoaming;
    }

    public Byte getAllowChgAvatar() {
        return allowChgAvatar;
    }

    public void setAllowChgAvatar(Byte allowChgAvatar) {
        this.allowChgAvatar = allowChgAvatar;
    }

    public Byte getAllowChgWallpaper() {
        return allowChgWallpaper;
    }

    public void setAllowChgWallpaper(Byte allowChgWallpaper) {
        this.allowChgWallpaper = allowChgWallpaper;
    }

    public List<String> getForbidUninstApp() {
        return forbidUninstApp;
    }

    public void setForbidUninstApp(List<String> forbidUninstApp) {
        this.forbidUninstApp = forbidUninstApp;
    }

    public Byte getAllowMessage() {
        return allowMessage;
    }

    public void setAllowMessage(Byte allowMessage) {
        this.allowMessage = allowMessage;
    }

    public Byte getAllowPulldownMenu() {
        return allowPulldownMenu;
    }

    public void setAllowPulldownMenu(Byte allowPulldownMenu) {
        this.allowPulldownMenu = allowPulldownMenu;
    }

    public Byte getAllowAppBehaviorLog() {
        return allowAppBehaviorLog;
    }

    public void setAllowAppBehaviorLog(Byte allowAppBehaviorLog) {
        this.allowAppBehaviorLog = allowAppBehaviorLog;
    }

    public List<String> getDedicateDeviceAppList() {
        return dedicateDeviceAppList;
    }

    public void setDedicateDeviceAppList(List<String> dedicateDeviceAppList) {
        this.dedicateDeviceAppList = dedicateDeviceAppList;
    }
}
