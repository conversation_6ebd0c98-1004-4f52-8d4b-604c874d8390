package com.cyberscraft.mdm.domain.policy;

import com.alibaba.fastjson.annotation.JSONField;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Set;

/**
 * Description:
 * User: lijie
 * Date: 2017-12-04
 * Time: 下午8:16
 */
public class TTDOSAppLimit {
    private Set<String> installBlackList;
    private Set<String> installWhiteList;
    private Set<String> uninstallBlackList;
    private Set<String> uninstallWhiteList;
    private Set<String> runningBlackList;
    private Set<String> runningWhiteList;

    @JSONField(serialize = false)
    public Boolean isEmpty() {
        return CollectionUtils.isEmpty(installBlackList)
                && CollectionUtils.isEmpty(installWhiteList)
                && CollectionUtils.isEmpty(uninstallBlackList)
                && CollectionUtils.isEmpty(uninstallWhiteList)
                && CollectionUtils.isEmpty(runningBlackList)
                && CollectionUtils.isEmpty(runningWhiteList);
    }

    public Set<String> getInstallBlackList() {
        return installBlackList;
    }

    public void setInstallBlackList(Set<String> installBlackList) {
        this.installBlackList = installBlackList;
    }

    public Set<String> getInstallWhiteList() {
        return installWhiteList;
    }

    public void setInstallWhiteList(Set<String> installWhiteList) {
        this.installWhiteList = installWhiteList;
    }

    public Set<String> getUninstallBlackList() {
        return uninstallBlackList;
    }

    public void setUninstallBlackList(Set<String> uninstallBlackList) {
        this.uninstallBlackList = uninstallBlackList;
    }

    public Set<String> getUninstallWhiteList() {
        return uninstallWhiteList;
    }

    public void setUninstallWhiteList(Set<String> uninstallWhiteList) {
        this.uninstallWhiteList = uninstallWhiteList;
    }

    public Set<String> getRunningBlackList() {
        return runningBlackList;
    }

    public void setRunningBlackList(Set<String> runningBlackList) {
        this.runningBlackList = runningBlackList;
    }

    public Set<String> getRunningWhiteList() {
        return runningWhiteList;
    }

    public void setRunningWhiteList(Set<String> runningWhiteList) {
        this.runningWhiteList = runningWhiteList;
    }
}
