package com.cyberscraft.mdm.domain.policy;

/**
 * 企业域策略--证书
 * <p>
 * Created by lumeng on 2014/11/25.
 */
public class DomainCertification {
    private String id;
    private String payloadCertificateFileName;
    private String originalFileName;
    private String url;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getPayloadCertificateFileName() {
        return payloadCertificateFileName;
    }

    public void setPayloadCertificateFileName(String payloadCertificateFileName) {
        this.payloadCertificateFileName = payloadCertificateFileName;
    }

    public String getOriginalFileName() {
        return originalFileName;
    }

    public void setOriginalFileName(String originalFileName) {
        this.originalFileName = originalFileName;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;

        DomainCertification that = (DomainCertification) o;

        if (url != null ? !url.equals(that.url) : that.url != null)
            return false;

        return true;
    }

    @Override
    public int hashCode() {
        return url != null ? url.hashCode() : 0;
    }
}
