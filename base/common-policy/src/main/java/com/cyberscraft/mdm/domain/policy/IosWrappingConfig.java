package com.cyberscraft.mdm.domain.policy;

import java.util.Set;

public class IosWrappingConfig {

    private Set<PackageInfo> allowpackages;//允许
    private Set<PackageInfo> forbiddenPackages;//禁止

    public Set<PackageInfo> getAllowpackages() {
        return allowpackages;
    }

    public void setAllowpackages(Set<PackageInfo> allowpackages) {
        this.allowpackages = allowpackages;
    }

    public Set<PackageInfo> getForbiddenPackages() {
        return forbiddenPackages;
    }

    public void setForbiddenPackages(Set<PackageInfo> forbiddenPackages) {
        this.forbiddenPackages = forbiddenPackages;
    }

}
