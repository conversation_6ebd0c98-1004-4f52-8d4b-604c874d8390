package com.cyberscraft.uep.account.client.provider.dingding.constant;

import com.cyberscraft.uep.account.client.constant.ExternalAttr;
import com.cyberscraft.uep.common.domain.DataTypeEnum;
import com.cyberscraft.uep.common.domain.MutableEnum;

import static com.cyberscraft.uep.account.client.constant.AccountConstant.APPUSER_PROFILE_ROOTNAME;
import static com.cyberscraft.uep.account.client.constant.AccountConstant.IDPUSER_PROFILE_ROOTNAME;

/**
 * <p>
 *  钉钉用户属性枚举
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/31 8:11 下午
 */
public enum CommonDingUserAttr implements ExternalAttr {
    userid("userid", DataTypeEnum.STRING, "员工的userId"),
    unionid("unionid", DataTypeEnum.STRING, "员工在当前开发者企业账号范围内的唯一标识", MutableEnum.readonly),
    name("name", DataTypeEnum.STRING, "员工姓名"),
    avatar("avatar", DataTypeEnum.STRING, "头像"),
    state_code("state_code", DataTypeEnum.STRING, "国际电话区号"),
    manager_userid("manager_userid", DataTypeEnum.STRING, "员工的直属主管"),
    mobile("mobile", DataTypeEnum.STRING, "手机号码"),
    hide_mobile("hide_mobile", DataTypeEnum.BOOLEAN, "是否号码隐藏"),
    telephone("telephone", DataTypeEnum.STRING, "分机号"),
    job_number("job_number", DataTypeEnum.STRING, "员工工号"),
    title("title", DataTypeEnum.STRING, "职位"),
    email("email", DataTypeEnum.STRING, "员工邮箱"),
    work_place("work_place", DataTypeEnum.STRING, "办公地点"),
    remark("remark", DataTypeEnum.STRING, "备注"),
    exclusive_account("exclusive_account", DataTypeEnum.BOOLEAN, "是否为专属帐号"),
    exclusive_account_type("exclusive_account_type", DataTypeEnum.STRING, "专属帐号类型"),
    exclusive_mobile("exclusive_mobile", DataTypeEnum.STRING, "专属帐号手机号"),
    avatarMediaId("avatarMediaId",DataTypeEnum.STRING, "创建本组织专属帐号时可指定头像MediaId"),
    nickname("nickname", DataTypeEnum.STRING, "创建本组织专属帐号时可指定昵称"),

    login_id("login_id", DataTypeEnum.STRING, "钉钉自建专属帐号的登录名"),
    loginId("loginId", DataTypeEnum.STRING, "钉钉专属帐号的登录名"),
    init_password("init_password", DataTypeEnum.STRING, "钉钉自建专属帐号的初始密码", MutableEnum.writeOnly),

    org_email("org_email", DataTypeEnum.STRING, "员工的企业邮箱"),
    org_email_type("org_email_type", DataTypeEnum.STRING, "员工的企业邮箱类型"),
    dept_id_list("dept_id_list", DataTypeEnum.STRING, "所属部门id列表"),
    dept_order_list("dept_order_list", DataTypeEnum.OBJECT, "员工在对应的部门中的排序",true),
    dept_title_list("dept_title_list", DataTypeEnum.OBJECT, "员工在对应的部门中的职位",true),
    cust_position_list("cust_position_list", DataTypeEnum.OBJECT, "员工在部门中的职位和排序",true),
    extension("extension", DataTypeEnum.OBJECT, "扩展属性"),
    hired_date("hired_date", DataTypeEnum.NUMBER, "入职时间，Unix时间戳，单位毫秒"),
    active("active", DataTypeEnum.BOOLEAN, "是否激活了钉钉"),
    real_authed("real_authed", DataTypeEnum.BOOLEAN, "是否完成了实名认证"),
    senior("senior", DataTypeEnum.BOOLEAN, "是否为企业的高管"),
    senior_mode("senior_mode", DataTypeEnum.BOOLEAN, "是否开启高管模式"),
    admin("admin", DataTypeEnum.BOOLEAN, "是否为企业的管理员"),
    boss("boss", DataTypeEnum.BOOLEAN, "是否为企业的老板"),
    leader_in_dept("leader_in_dept", DataTypeEnum.OBJECT, "员工所在部门信息及是否是领导", true),
    language("language", DataTypeEnum.STRING, "通讯录语言"),
    login_email("login_email", DataTypeEnum.STRING, "登录邮箱"),
    role_list("role_list", DataTypeEnum.OBJECT, "角色列表", true),
    union_emp_ext("union_emp_ext", DataTypeEnum.OBJECT, "当用户来自于关联组织时的关联信息"),
    force_update_fields("force_update_fields", DataTypeEnum.STRING, "强制更新的字段，支持清空指定的字段，多个字段之间使用英文逗号分隔"),

    idp_userId("userId", new String[]{"userid"}, DataTypeEnum.STRING, "员工的userId"),
    idp_unionId("unionId", new String[]{"unionid"}, DataTypeEnum.STRING, "员工在当前开发者企业账号范围内的唯一标识"),
    idp_nick("nick", DataTypeEnum.STRING, "昵称"),
    ;

    private String attrName;

    private String[] alias;

    private DataTypeEnum dataType;

    private String displayName;

    private String description;

    private Boolean multiValued;

    private MutableEnum mutability;

    CommonDingUserAttr(String attrName, DataTypeEnum dataType, String displayName) {
        this(attrName, dataType, displayName, displayName, false);
    }

    CommonDingUserAttr(String attrName, DataTypeEnum dataType, String displayName, MutableEnum mutability) {
        this(attrName, null, dataType, displayName, displayName, false, mutability);
    }


    CommonDingUserAttr(String attrName, String[] alias, DataTypeEnum dataType, String displayName) {
        this(attrName, alias, dataType, displayName, displayName, false);
    }

    CommonDingUserAttr(String attrName, DataTypeEnum dataType, String displayName, Boolean multiValued) {
        this(attrName, dataType, displayName, displayName, multiValued);
    }

    CommonDingUserAttr(String attrName, DataTypeEnum dataType, String displayName, String description, Boolean multiValued) {
        this(attrName, null, dataType, displayName, description, multiValued);
    }

    CommonDingUserAttr(String attrName, String[] alias, DataTypeEnum dataType, String displayName, String description, Boolean multiValued) {
        this(attrName, alias, dataType, displayName, description, multiValued, MutableEnum.readWrite);
    }

    CommonDingUserAttr(String attrName, String[] alias, DataTypeEnum dataType, String displayName, String description, Boolean multiValued, MutableEnum mutability) {
        this.attrName = attrName;
        this.alias = alias;
        this.dataType = dataType;
        this.displayName = displayName;
        this.description = description;
        this.multiValued = multiValued;
        this.mutability = mutability;
    }

    public String getAttrName() {
        return attrName;
    }

    @Override
    public String[] getAlias() {
        return alias;
    }

    public DataTypeEnum getDataType() {
        return dataType;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getDescription() {
        return description;
    }

    public Boolean getMultiValued() {
        return multiValued;
    }

    public MutableEnum getMutability() {
        return mutability;
    }

    public String getAppNamePath() {
        return APPUSER_PROFILE_ROOTNAME + "." + getAttrName();
    }

    @Override
    public String getIdpNamePath() {
        return IDPUSER_PROFILE_ROOTNAME + "." + getAttrName();
    }
}
