package com.cyberscraft.uep.account.client.provider.dingding.event;

import com.cyberscraft.uep.account.client.domain.SnsConfig;
import com.dingtalk.api.response.OapiCallBackGetCallBackFailedResultResponse;
import com.cyberscraft.uep.account.client.domain.Connector;
import com.cyberscraft.uep.account.client.domain.EventBody;
import com.cyberscraft.uep.account.client.domain.ThirdPartyEvent;
import com.cyberscraft.uep.account.client.provider.dingding.domain.DingTalkConfig;
import com.cyberscraft.uep.account.client.provider.dingding.domain.DingTalkMessage;

import java.util.List;
import java.util.Set;

/***
 * 钉钉消息处理器，用于处理钉钉消息
 * @date 2021/3/25
 * <AUTHOR>
 ***/
public interface IDingTalkEventParser {

    /****
     * 得到支持的事件消息类型
     * @return
     */
    Set<String> getSupportedMessageType();

    /***
     * 进行钉钉回调消息处理
     * @param msg
     *
     */
    List<ThirdPartyEvent<? extends EventBody>> parse(DingTalkMessage msg, SnsConfig snsConfig);


    /***
     * 进行失败的钉钉回调消息处理
     * @param msg
     * @param connector
     */
    ThirdPartyEvent<? extends EventBody> parse(OapiCallBackGetCallBackFailedResultResponse.Failed msg, String tenantId, Connector connector);
}
