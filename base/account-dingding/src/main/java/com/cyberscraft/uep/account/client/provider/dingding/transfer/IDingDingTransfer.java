package com.cyberscraft.uep.account.client.provider.dingding.transfer;

import com.cyberscraft.uep.account.client.domain.FormFieldValue;
import com.cyberscraft.uep.account.client.domain.ProcessInstRequest;
import com.cyberscraft.uep.account.client.provider.dingding.domain.DingProcessFormField;
import com.cyberscraft.uep.account.client.provider.dingding.domain.DingProcessInstance;
import com.cyberscraft.uep.account.client.provider.dingding.domain.DingTalkConfig;
import com.cyberscraft.uep.account.client.provider.dingding.domain.DingTalkProcessFlowConfig;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 将其他对象与钉钉对象做转换
 * </p>
 *
 * <AUTHOR>
 * @since 2022/6/16 5:07 下午
 */
@Mapper(componentModel = "spring")
public interface IDingDingTransfer {

    IDingDingTransfer INSTANCE = Mappers.getMapper(IDingDingTransfer.class);

    default DingProcessInstance processRequestToInstance(ProcessInstRequest request, DingTalkConfig cfg, DingTalkProcessFlowConfig processFlowConfig, String adminUrl) {
        DingProcessInstance dingProcessInstance = new DingProcessInstance();
        dingProcessInstance.setProcessCode(processFlowConfig.getCode());
        dingProcessInstance.setUserId(request.getUserId());
        dingProcessInstance.setDeptId(Long.valueOf(request.getDeptId()));


        List<DingProcessFormField> processFormFields = new ArrayList<>();
        // 表单
        Map<String, FormFieldValue> formData = request.getFormData();
        for (String key : formData.keySet()) {
            FormFieldValue formFieldValue = formData.get(key);
            if (formFieldValue == null) {
                continue;
            }
            if (formFieldValue.isUrl()) {
                String urlValue = null;
                try {
                    urlValue = URLEncoder.encode(formFieldValue.getValue(), "UTF-8");
                } catch (UnsupportedEncodingException e) {
                    throw new RuntimeException(e.getMessage());

                }
                formFieldValue.setValue(String.format(adminUrl, cfg.getCorpId(), cfg.getAgentId()) + urlValue);
            }

            DingProcessFormField dingProcessFormField = new DingProcessFormField(key, formFieldValue.getValue(), formFieldValue.getExtValue());
            processFormFields.add(dingProcessFormField);

        }
        dingProcessInstance.setProcessFormFields(processFormFields);

        return dingProcessInstance;
    }
}
