package com.cyberscraft.uep.account.client.provider.dingding.constant;

import com.cyberscraft.uep.account.client.constant.ExternalAttr;
import com.cyberscraft.uep.common.domain.DataTypeEnum;
import com.cyberscraft.uep.common.domain.MutableEnum;

import static com.cyberscraft.uep.account.client.constant.AccountConstant.APPROLE_PROFILE_ROOTNAME;

/**
 * @Description 钉钉角色属性枚举
 * <AUTHOR>
 * @Date 2024/7/10 18:35
 */
public enum CommonDingRoleAttr implements ExternalAttr {

    name("name", DataTypeEnum.STRING, "角色名称", "角色名称"),
    groupName("groupName", DataTypeEnum.STRING, "角色组名称", "角色组名称"),
    roleId("roleId", new String[]{"role_id"}, DataTypeEnum.STRING, "角色ID", "角色ID"),
    roleName("roleName", DataTypeEnum.STRING, "角色名称", "角色名称"),
    groupId("groupId", DataTypeEnum.STRING, "角色组ID", "角色组ID"),
    roleIds("roleIds", DataTypeEnum.STRING, "角色roleId列表", "角色roleId列表", true),
    roles("roles", DataTypeEnum.STRING, "角色列表", "角色列表", true),
    userIds("userIds", DataTypeEnum.STRING, "员工的userId", "员工的userId", true),
    role_groups("role_groups", DataTypeEnum.OBJECT, "角色组", "角色组", true);


    CommonDingRoleAttr(String attrName, DataTypeEnum dataType, String displayName, String description) {
        this(attrName, null, dataType, displayName, description, false, MutableEnum.readWrite);
    }

    CommonDingRoleAttr(String attrName, DataTypeEnum dataType, String displayName, String description, boolean multiValued) {
        this(attrName, null, dataType, displayName, description, multiValued, MutableEnum.readWrite);
    }

    CommonDingRoleAttr(String attrName, String[] alias, DataTypeEnum dataType, String displayName, String description) {
        this(attrName, alias, dataType, displayName, description, false, MutableEnum.readWrite);
    }

    CommonDingRoleAttr(String attrName, String[] alias, DataTypeEnum dataType, String displayName, String description, Boolean multiValued, MutableEnum mutability) {
        this.attrName = attrName;
        this.alias = alias;
        this.dataType = dataType;
        this.displayName = displayName;
        this.description = description;
        this.multiValued = multiValued;
        this.mutability = mutability;
    }

    private String attrName;

    private String[] alias;

    private DataTypeEnum dataType;

    private String displayName;

    private String description;

    private Boolean multiValued;

    private MutableEnum mutability;

    public String getAttrName() {
        return attrName;
    }

    @Override
    public String[] getAlias() {
        return alias;
    }

    public DataTypeEnum getDataType() {
        return dataType;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getDescription() {
        return description;
    }

    public Boolean getMultiValued() {
        return multiValued;
    }

    public MutableEnum getMutability() {
        return mutability;
    }

    public String getAppNamePath() {
        return APPROLE_PROFILE_ROOTNAME + "." + getAttrName();
    }

    @Override
    public String getIdpNamePath() {
        return APPROLE_PROFILE_ROOTNAME + "." + getAttrName();
    }
}
