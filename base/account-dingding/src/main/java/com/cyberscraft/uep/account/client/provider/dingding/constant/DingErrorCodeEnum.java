package com.cyberscraft.uep.account.client.provider.dingding.constant;

/**
 * <p>
 *     钉钉接口全局错误码
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2019-10-18 17:26
 */
public enum DingErrorCodeEnum {
    SUCCESS(0),
    SYSTEM_BUSY(-1),
    CALL_BACK_URL_NOT_EXIST(71006),
    CALL_BACK_URL_NOT_EXIST_2(71007),
    ;

    DingErrorCodeEnum(int value) {
        this.value = value;
    }

    private int value;

    public int getValue() {
        return value;
    }

    public static boolean isSuccess(String errorCode){
        try {
            int intErrorCode = Integer.valueOf(errorCode);
            return DingErrorCodeEnum.SUCCESS.value == intErrorCode;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    public static boolean isCallBackUrlNotExist(String errorCode){
        try {
            int intErrorCode = Integer.valueOf(errorCode);
            if(DingErrorCodeEnum.CALL_BACK_URL_NOT_EXIST.value == intErrorCode ||
                    DingErrorCodeEnum.CALL_BACK_URL_NOT_EXIST_2.value == intErrorCode) {
                return true;
            }
            return false;
        } catch (NumberFormatException e) {
            return false;
        }
    }
}
