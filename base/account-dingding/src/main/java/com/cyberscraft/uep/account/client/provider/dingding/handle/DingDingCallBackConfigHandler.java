package com.cyberscraft.uep.account.client.provider.dingding.handle;

import com.cyberscraft.uep.account.client.domain.SnsConfig;
import com.cyberscraft.uep.account.client.provider.dingding.constant.DingTalkEventConstant;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiCallBackDeleteCallBackRequest;
import com.dingtalk.api.request.OapiCallBackGetCallBackFailedResultRequest;
import com.dingtalk.api.request.OapiCallBackGetCallBackRequest;
import com.dingtalk.api.request.OapiCallBackRegisterCallBackRequest;
import com.dingtalk.api.response.OapiCallBackDeleteCallBackResponse;
import com.dingtalk.api.response.OapiCallBackGetCallBackFailedResultResponse;
import com.dingtalk.api.response.OapiCallBackGetCallBackResponse;
import com.dingtalk.api.response.OapiCallBackRegisterCallBackResponse;
import com.cyberscraft.uep.common.exception.BaseException;
import com.cyberscraft.uep.common.enums.ExceptionCodeEnum;
import com.cyberscraft.uep.account.client.constant.ThirdPartyAccountType;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountErrorType;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountException;
import com.cyberscraft.uep.account.client.provider.IThirdPartyCallBackConfigHandler;
import com.cyberscraft.uep.account.client.provider.dingding.constant.DingErrorCodeEnum;
import com.cyberscraft.uep.account.client.provider.dingding.domain.DingTalkConfig;
import com.cyberscraft.uep.common.util.WebUrlUtil;
import com.cyberscraft.uep.common.config.ServerConfig;
import com.cyberscraft.uep.common.util.AESUtil;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.common.util.RandomUtil;
import com.taobao.api.ApiException;
import com.taobao.api.TaobaoResponse;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpMethod;

import java.util.ArrayList;
import java.util.List;

/***
 *  钉钉回调配置实现接口
 * @date 2021/3/26
 * <AUTHOR>
 ***/
public class DingDingCallBackConfigHandler extends AbstractDingHandler implements IThirdPartyCallBackConfigHandler {


    private ServerConfig serverConfig;

    /***
     *
     */
    private final static Logger LOG = LoggerFactory.getLogger(DingDingCallBackConfigHandler.class);


    /***
     * 注册事件回调
     */
    private String registerCallBackApi = "/call_back/register_call_back";

    /***
     * 删除事件回调注册
     */
    private String removeCallBackApi = "/call_back/delete_call_back";

    /***
     * 查询事件回调接口
     */
    private String getCallBackApi = "/call_back/get_call_back";

    /***
     * 得到已经回调的失败结果
     */
    private String getFailedCallBackEventApi = "/call_back/get_call_back_failed_result";

    @Override
    public boolean isSupported(String accountType) {
        Boolean result = ThirdPartyAccountType.DINGDING.getCode().equalsIgnoreCase(accountType)||ThirdPartyAccountType.DINGTHIRD.getCode().equalsIgnoreCase(accountType);
        return result;
    }

    @Override
    public void  improveSnsConfig(String tenantId, SnsConfig snsConfig) {
        if (snsConfig == null) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.SNSCONFIG_INVALID);
        }

        snsConfig.setTenantId(tenantId);

        if (StringUtils.isNotBlank(snsConfig.getConfig())) {
            DingTalkConfig dingTalkConfig = JsonUtil.str2Obj(snsConfig.getConfig(), DingTalkConfig.class);
            String aesKey = dingTalkConfig.getAesKey();
            String token = dingTalkConfig.getToken();
            if (StringUtils.isBlank(aesKey)) {
                aesKey = AESUtil.initkeyEncodeBase64();
                aesKey = StringUtils.substringBeforeLast(aesKey, "=");
                dingTalkConfig.setAesKey(aesKey);
            }
            if (StringUtils.isBlank(token)) {
                token = RandomUtil.getRandomString(clientConfig.getTokenLen());
                dingTalkConfig.setToken(token);
            }

            String connectorPath = "/" + tenantId + "/" + snsConfig.getId() + "/";
            String url = WebUrlUtil.getWebServerUrl(serverConfig.getOutterUrl(), clientConfig.getCallBackUrl());
            dingTalkConfig.setRegisterBaseUrl(WebUrlUtil.getWebServerUrl(url, connectorPath));

            snsConfig.setConfig(JsonUtil.obj2Str(dingTalkConfig));
        }
    }

    /***
     * 注册对应的事件回调
     * @param tenantId
     * @param snsConfig
     */
    @Override
    public void registerEventCallBack(String tenantId, SnsConfig snsConfig) {
        if (tenantId == null) {
            throw new BaseException(ExceptionCodeEnum.TENANT_INVALID);
        }
        if (snsConfig == null) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.CONNECTOR_INVALID);
        }
        DingTalkConfig cfg = JsonUtil.str2Obj(snsConfig.getConfig(), DingTalkConfig.class);
        cfg.setHasProcess(snsConfig.getHasProcess());
        cfg.setHasConnector(snsConfig.getHasConnector());
        registerEventCallBack(tenantId, cfg);
    }

    /***
     *
     * @param tenantId
     * @param cfg
     */
    private void registerEventCallBack(String tenantId, DingTalkConfig cfg) {

        String accessToken = getAccessToken(tenantId, cfg);
        if (StringUtils.isBlank(accessToken)) {
            throw new ThirdPartyAccountException(ExceptionCodeEnum.ACCESSTOKEN_INVALID);
        }
        try {
            if (isRegisted(tenantId, accessToken, cfg)) {
                removeEventCallBack(tenantId, accessToken, cfg);
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }

        this.registerEventCallBack(tenantId, accessToken, cfg);

    }

    /**
     * 处理注册事件回调
     *
     * @param response
     * @param api
     * @param <T>
     * @return
     */
    private <T extends TaobaoResponse> T handerRegisterCallBackResponse(String tenantId, String accessToken, DingTalkConfig cfg, T response, String api) {
        LOG.info("dingtalk api:{}, response errorCode:{},message:{}, body:{},api:{}", api, response.getErrorCode(), response.getMessage(), response.getBody(), api);
        //回调地址已经存在时，先删除
        if (DingErrorCodeEnum.isCallBackUrlNotExist(response.getErrorCode())) {
            this.removeEventCallBack(tenantId, accessToken, cfg);
            this.registerEventCallBack(tenantId, accessToken, cfg);
        } else if (!DingErrorCodeEnum.isSuccess(response.getErrorCode())) {
            LOG.error("dingtalk response error,errorCode:{},api:{}", response.getErrorCode(), api);
            throw new ThirdPartyAccountException(response.getErrorCode(), response.getMsg());
        }
        return response;
    }

    @Override
    public void checkAndRegisterEventCallBack(String tenantId, SnsConfig snsConfig) {
        try {
            if (tenantId == null) {
                throw new BaseException(ExceptionCodeEnum.TENANT_INVALID);
            }
            if (snsConfig == null) {
                throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.CONNECTOR_INVALID);
            }
            DingTalkConfig cfg = JsonUtil.str2Obj(snsConfig.getConfig(), DingTalkConfig.class);

            String accessToken = getAccessToken(tenantId, cfg);
            if (StringUtils.isBlank(accessToken)) {
                throw new ThirdPartyAccountException(ExceptionCodeEnum.ACCESSTOKEN_INVALID);
            }
            //如果已经注册，则进行删除处理
            if (isRegisted(tenantId, accessToken, cfg)) {
                //removeEventCallBack(tenantId, accessToken, cfg);
                LOG.info("当前事件回调已经存在，本次注册回调事件跳过,租户:{}", tenantId);
                return;
            }
            registerEventCallBack(tenantId, accessToken, cfg);
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }

    /***
     * 进行事件回调注册
     * @param tenantId
     * @param accessToken
     * @param cfg
     */
    private void registerEventCallBack(String tenantId, String accessToken, DingTalkConfig cfg) {

        if (!cfg.getHasProcess() && !cfg.getHasConnector()) {
            return;
        }

        DingTalkClient client = getDingTalkClient(cfg.getApiBaseUrl(), registerCallBackApi);
        OapiCallBackRegisterCallBackRequest request = new OapiCallBackRegisterCallBackRequest();
        //采用路径的方式进行处理

        String callBackUrl = cfg.getRegisterBaseUrl();//
        request.setUrl(callBackUrl);

        request.setAesKey(cfg.getAesKey());
        request.setToken(cfg.getToken());

        List<String> callBackTag = new ArrayList();
        if (cfg.getHasConnector()) {
            callBackTag.addAll(DingTalkEventConstant.USERS_EVENT_LIST);
        }
        if (cfg.getHasProcess()) {
            callBackTag.addAll(DingTalkEventConstant.BPMS_EVENT_LIST);
        }
        request.setCallBackTag(callBackTag);


        LOG.info("当前回调注册相关信息为url:{},tags:{}", callBackUrl, JsonUtil.obj2Str(request.getCallBackTag()));
        try {
            OapiCallBackRegisterCallBackResponse response = client.execute(request, accessToken);
            checkResponse(response, registerCallBackApi);
            handerRegisterCallBackResponse(tenantId, accessToken, cfg, response, registerCallBackApi);
        } catch (ApiException e) {
            //LOG.error(e.getMessage(), e);
            throw new ThirdPartyAccountException(e.getErrCode(), e.getErrMsg());
        }
    }

    /***
     * 更新事件回调，因为采用的逻辑就是先删除再注册，所以直接调用register事件即可
     * @param tenantId
     * @param oldSnsConfig
     * @param newSnsConfig
     */
    @Override
    public void updateEventCallBack(String tenantId, SnsConfig oldSnsConfig, SnsConfig newSnsConfig) {
        DingTalkConfig newDingTalkConfig = JsonUtil.str2Obj(newSnsConfig.getConfig(), DingTalkConfig.class);
        newDingTalkConfig.setHasProcess(newSnsConfig.getHasProcess());
        newDingTalkConfig.setHasConnector(newSnsConfig.getHasConnector());
        try {
            removeEventCallBack(tenantId, oldSnsConfig);
        } catch (Exception e) {
            LOG.warn(e.getMessage() + ". 注册新回调地址");
        }
        registerEventCallBack(tenantId, newDingTalkConfig);
    }

    /***
     * 删除回调失败的事件列表
     * @param tenantId
     * @param snsConfig
     */
    @Override
    public void cleanFiluredCallBackEvent(String tenantId, SnsConfig snsConfig) {
        if (tenantId == null) {
            throw new BaseException(ExceptionCodeEnum.TENANT_INVALID);
        }
        if (snsConfig == null) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.CONNECTOR_INVALID);
        }
        DingTalkConfig cfg = JsonUtil.str2Obj(snsConfig.getConfig(), DingTalkConfig.class);
        String accessToken = getAccessToken(tenantId, cfg);
        if (StringUtils.isBlank(accessToken)) {
            throw new ThirdPartyAccountException(ExceptionCodeEnum.ACCESSTOKEN_INVALID);
        }
        DingTalkClient client = getDingTalkClient(cfg.getApiBaseUrl(), getFailedCallBackEventApi);
        OapiCallBackGetCallBackFailedResultRequest request = new OapiCallBackGetCallBackFailedResultRequest();
        request.setHttpMethod(HttpMethod.GET.name());
        try {
            OapiCallBackGetCallBackFailedResultResponse response = client.execute(request, accessToken);
            checkResponse(response, getFailedCallBackEventApi);
        } catch (ApiException e) {
            throw new ThirdPartyAccountException(e.getErrCode(), e.getErrMsg());
        }
    }

    /***
     * 删除事件回调
     * @param tenantId
     * @param snsConfig
     */
    @Override
    public void removeEventCallBack(String tenantId, SnsConfig snsConfig) {
        try {
            if (tenantId == null) {
                throw new BaseException(ExceptionCodeEnum.TENANT_INVALID);
            }
            if (snsConfig == null) {
                throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.CONNECTOR_INVALID);
            }
            DingTalkConfig cfg = JsonUtil.str2Obj(snsConfig.getConfig(), DingTalkConfig.class);
            String accessToken = getAccessToken(tenantId, cfg);
            if (StringUtils.isBlank(accessToken)) {
                throw new ThirdPartyAccountException(ExceptionCodeEnum.ACCESSTOKEN_INVALID);
            }
            removeEventCallBack(tenantId, accessToken, cfg);
        } catch (Exception e) {
            LOG.warn("removeEventCallBack error:{}", e.getMessage());
        }
    }

    /***
     * 删除对应的事件回调
     * @param tenantId
     * @param accessToken
     * @param cfg
     */
    private void removeEventCallBack(String tenantId, String accessToken, DingTalkConfig cfg) {
        DingTalkClient client = getDingTalkClient(cfg.getApiBaseUrl(), removeCallBackApi);
        OapiCallBackDeleteCallBackRequest request = new OapiCallBackDeleteCallBackRequest();
        request.setHttpMethod(HttpMethod.GET.name());
        try {
            OapiCallBackDeleteCallBackResponse response = client.execute(request, accessToken);
            checkResponse(response, removeCallBackApi);
        } catch (ApiException e) {
            throw new ThirdPartyAccountException(e.getErrCode(), e.getErrMsg());
        }
    }

    /***
     * 因为钉钉暂时不支持多个事件回调
     * @param tenantId
     * @param id
     * @param snsConfig
     */
    @Override
    public void removeEventCallBack(String tenantId, String id, SnsConfig snsConfig) {
        removeEventCallBack(tenantId, snsConfig);
    }

    @Override
    public <T> T getEventCallBacks(String tenantId, SnsConfig snsConfig) {
        if (tenantId == null) {
            throw new BaseException(ExceptionCodeEnum.TENANT_INVALID);
        }
        if (snsConfig == null) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.CONNECTOR_INVALID);
        }
        DingTalkConfig cfg = JsonUtil.str2Obj(snsConfig.getConfig(), DingTalkConfig.class);
        String accessToken = getAccessToken(tenantId, cfg);
        if (StringUtils.isBlank(accessToken)) {
            throw new ThirdPartyAccountException(ExceptionCodeEnum.ACCESSTOKEN_INVALID);
        }
        return (T) getEventCallBacks(tenantId, accessToken, cfg);
    }

    /***
     * 判断事件回调是否已经注册
     * @param accessToken
     * @param cfg
     * @return
     */
    private boolean isRegisted(String tenantId, String accessToken, DingTalkConfig cfg) {
        OapiCallBackGetCallBackResponse response = getEventCallBacks(tenantId, accessToken, cfg);
        return response != null && StringUtils.isNotBlank(response.getUrl());
    }

    /***
     * 查询已经注册的事件回调
     * @param tenantId
     * @param accessToken
     * @param cfg
     * @return
     */
    private OapiCallBackGetCallBackResponse getEventCallBacks(String tenantId, String accessToken, DingTalkConfig cfg) {
        try {
            DingTalkClient client = getDingTalkClient(cfg.getApiBaseUrl(), getCallBackApi);
            OapiCallBackGetCallBackRequest request = new OapiCallBackGetCallBackRequest();
            request.setHttpMethod(HttpMethod.GET.name());

            OapiCallBackGetCallBackResponse response = client.execute(request, accessToken);
            checkResponse(response, getCallBackApi);
            return response;
        } catch (ApiException e) {
            LOG.error("获取钉钉回调事件出错：code:{},msg:{}", e.getErrCode(), e.getErrMsg());
            //throw new ThirdPartyAccountException(e.getErrCode(), e.getErrMsg());
        } catch (Exception e) {
            LOG.error("获取钉钉回调事件出错：{}", e.getMessage());
        }
        return null;
    }

    public ServerConfig getServerConfig() {
        return serverConfig;
    }

    public void setServerConfig(ServerConfig serverConfig) {
        this.serverConfig = serverConfig;
    }
}
