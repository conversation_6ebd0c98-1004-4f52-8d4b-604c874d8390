package com.cyberscraft.uep.account.client.provider.dingding.service;

import com.cyberscraft.uep.account.client.provider.dingding.domain.DingProcessInstance;
import com.cyberscraft.uep.account.client.provider.dingding.domain.DingTalkConfig;

public interface IDingTalkProcessInstanceService {

    /**
     * 发起钉钉OA审批
     *
     * @param dingProcessInstance 钉钉审批实例
     * @param dingTalkConfig            钉钉配置信息
     * @param tenantId                  租户ID
     * @return 流程实例ID
     */
    String createProcessInstance(DingProcessInstance dingProcessInstance, DingTalkConfig dingTalkConfig, String tenantId);
}
