
package com.cyberscraft.uep.account.client.provider.dingding.handle;

import com.cyberscraft.uep.account.client.constant.ThirdPartyAccountType;
import com.cyberscraft.uep.account.client.domain.Connector;
import com.cyberscraft.uep.account.client.domain.ThirdPartyAccount;
import com.cyberscraft.uep.account.client.domain.ThirdPartyGroup;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccessTokenInvalidException;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountErrorType;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountException;
import com.cyberscraft.uep.account.client.provider.IThirdPartyAccountHandler;
import com.cyberscraft.uep.account.client.provider.dingding.config.DingTalkClientConfig;
import com.cyberscraft.uep.account.client.provider.dingding.domain.DingTalkConfig;
import com.cyberscraft.uep.account.client.provider.dingding.service.IDingTalkAccessTokenService;
import com.cyberscraft.uep.account.client.provider.dingding.transfer.IOapiResponseTransfer;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.*;
import com.dingtalk.api.response.*;
import com.taobao.api.ApiException;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/***
 * 钉钉对应的账户实现类，主要功能为获取用户，获取组织等相关业务接口
 * @date 2021/3/25
 * <AUTHOR>
 ***/
public class DingDingAccountHandler extends AbstractDingHandler implements IThirdPartyAccountHandler {


    private IOapiResponseTransfer oapiResponseTransfer;


    /***
     * 获取部门列表
     */
    protected String getDepartmentListApi = "/topapi/v2/department/listsub";

    /***
     * 获取部门详情
     */
    private String getDepartmentApi = "/topapi/v2/department/get";

    /***
     * 获取部门用户对应的详情
     */
    private String getUserListByDepartmentApi = "/topapi/v2/user/list";

    /***
     * 根据userid获取用户对应的详情
     */
    private String getUserApi = "/topapi/v2/user/get";


    private String getUserApiByMobile = "/topapi/v2/user/getbymobile";

    /***
     *
     */
    private final static Logger LOG = LoggerFactory.getLogger(DingDingAccountHandler.class);


    @Override
    public boolean isSupported(String accountType) {
        return ThirdPartyAccountType.DINGDING.getCode().equalsIgnoreCase(accountType);
    }

    /***
     * 因为钉钉的根部门ID都为1
     */
    @Override
    public ThirdPartyGroup getRootGroup(String tenantId, Connector connector) throws ThirdPartyAccountException {
//        ThirdPartyGroup obj = new ThirdPartyGroup();
//        obj.setCode(clientConfig.getRootGroupCode());
//        obj.setParentCode(String.valueOf(SysConstant.DEFAULT_ROOT_GROUP_PARENTID));
//        //obj.setName(clientConfig.getRootGroupName());
//        obj.setStatus(SysConstant.TRUE);
//        return obj;
        return getGroupByCode(tenantId, clientConfig.getRootGroupCode(), connector);
    }

    /***
     *
     * @param tenantId
     * @param groupCode
     * @param connector
     * @return
     * @throws ThirdPartyAccountException
     */
    @Override
    public ThirdPartyGroup getGroupByCode(String tenantId, String groupCode, Connector connector) throws ThirdPartyAccountException {
        if (StringUtils.isBlank(groupCode)) {
            return null;
        }
//        if (clientConfig.getRootGroupCode().equalsIgnoreCase(groupCode)) {
//            return getRootGroup(tenantId, connector);
//        }
        if (connector == null) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.CONNECTOR_INVALID);
        }
        DingTalkConfig cfg = JsonUtil.str2Obj(connector.getConfig(), DingTalkConfig.class);
        DingTalkClient client = getDingTalkClient(cfg.getApiBaseUrl(), getDepartmentApi);
        String accessToken = getAccessToken(tenantId, cfg);
        try {
            return getGroupsByCode(client, accessToken, groupCode);
        } catch (ThirdPartyAccessTokenInvalidException e) {
            accessToken = reGetAccessToken(tenantId, cfg);
            return getGroupsByCode(client, accessToken, groupCode);
        } catch (Exception e) {
            throw e;
        }
    }

    /****
     *
     * @param client
     * @param accessToken
     * @param groupCode
     * @return
     */
    private ThirdPartyGroup getGroupsByCode(DingTalkClient client, String accessToken, String groupCode) {
        OapiV2DepartmentGetRequest request = new OapiV2DepartmentGetRequest();
        request.setDeptId(Long.parseLong(groupCode));
        try {
            OapiV2DepartmentGetResponse response = client.execute(request, accessToken);
            checkResponse(response, getDepartmentApi);
            //转换成第三方平台用户组
            return oapiResponseTransfer.responseToThirdPartyGroup(response);
        } catch (ThirdPartyAccessTokenInvalidException e) {
            throw e;
        } catch (ApiException e) {
            throw new ThirdPartyAccountException(e.getErrCode(), e.getErrMsg());
        }
    }

    /***
     *
     * @param tenantId
     * @param codes
     * @param connector
     * @return
     * @throws ThirdPartyAccountException
     */
    @Override
    public List<ThirdPartyGroup> getGroupsByCodes(String tenantId, List<String> codes, Connector connector) throws ThirdPartyAccountException {
        if (codes == null || codes.size() == 0) {
            return null;
        }
        if (connector == null) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.CONNECTOR_INVALID);
        }
        DingTalkConfig cfg = JsonUtil.str2Obj(connector.getConfig(), DingTalkConfig.class);
        DingTalkClient client = getDingTalkClient(cfg.getApiBaseUrl(), getDepartmentApi);
        String accessToken = getAccessToken(tenantId, cfg);

        List<ThirdPartyGroup> ret = new ArrayList<>(codes.size());
        for (String code : codes) {
            try {
                if (clientConfig.getRootGroupCode().equalsIgnoreCase(code)) {
                    ret.add(getRootGroup(tenantId, connector));
                } else {
                    ThirdPartyGroup obj = getGroupsByCode(client, accessToken, code);
                    if (obj != null) {
                        ret.add(obj);
                    }
                }
            } catch (Exception e) {
                LOG.error("获取钉钉组时，发生错误，{}", e.getMessage());
            }
        }
        return ret;
    }

    /***
     * 因为钉钉的不支持分页，所以直接调用返回所有的子组列表
     * @param tenantId
     * @param parentGroup
     * @param connector
     * @param pageIndex
     * @param pageSize
     * @return
     * @throws ThirdPartyAccountException
     */
    @Override
    public List<ThirdPartyGroup> getSubGroups(String tenantId, String parentGroup, Connector connector, int pageIndex, int pageSize) throws ThirdPartyAccountException {
        return getSubGroups(tenantId, parentGroup, connector);
    }

    @Override
    public List<ThirdPartyGroup> getSubGroups(String tenantId, String parentGroup, Connector connector) throws ThirdPartyAccountException {
        if (StringUtils.isBlank(parentGroup)) {
            return null;
        }
        if (connector == null) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.CONNECTOR_INVALID);
        }
        DingTalkConfig cfg = JsonUtil.str2Obj(connector.getConfig(), DingTalkConfig.class);
        DingTalkClient client = getDingTalkClient(cfg.getApiBaseUrl(), getDepartmentListApi);
        String accessToken = getAccessToken(tenantId, cfg);
        try {
            return getSubGroups(client, accessToken, parentGroup, false);
        } catch (ThirdPartyAccessTokenInvalidException e) {
            accessToken = reGetAccessToken(tenantId, cfg);
            return getSubGroups(client, accessToken, parentGroup, false);
        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public List<ThirdPartyGroup> getAllSubGroups(String tenantId, String parentGroup, Connector connector) throws ThirdPartyAccountException {
        if (StringUtils.isBlank(parentGroup)) {
            return null;
        }
        if (connector == null) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.CONNECTOR_INVALID);
        }
        DingTalkConfig cfg = JsonUtil.str2Obj(connector.getConfig(), DingTalkConfig.class);
        DingTalkClient client = getDingTalkClient(cfg.getApiBaseUrl(), getDepartmentListApi);
        String accessToken = getAccessToken(tenantId, cfg);
        try {
            return getSubGroups(client, accessToken, parentGroup, true);
        } catch (ThirdPartyAccessTokenInvalidException e) {
            accessToken = reGetAccessToken(tenantId, cfg);
            return getSubGroups(client, accessToken, parentGroup, true);
        } catch (Exception e) {
            throw e;
        }
    }

    /***
     * 因为钉钉对应的根组是一个虚拟的组，所以需要手动增加上
     * @param tenantId
     * @param connector
     * @return
     * @throws ThirdPartyAccountException
     */
    @Override
    public List<ThirdPartyGroup> getAllGroups(String tenantId, Connector connector) throws ThirdPartyAccountException {
        if (connector == null) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.CONNECTOR_INVALID);
        }
        DingTalkConfig cfg = JsonUtil.str2Obj(connector.getConfig(), DingTalkConfig.class);
        DingTalkClient client = getDingTalkClient(cfg.getApiBaseUrl(), getDepartmentListApi);
        String accessToken = getAccessToken(tenantId, cfg);
        List<ThirdPartyGroup> subGroups = getSubGroups(client, accessToken, clientConfig.getRootGroupCode(), true);
        List<ThirdPartyGroup> ret = new ArrayList<>();
        ret.add(getRootGroup(tenantId, connector));
        if (subGroups != null && subGroups.size() > 0) {
            ret.addAll(subGroups);
        }
        return ret;
    }

    /***
     * 查询出对应的全部部门~新版本只能查询下级的第一层级部门
     * @param client 钉钉客户端
     * @param accessToken 访问token
     * @param parentGroup 父节点ID
     * @param isFetchChild
     * @return
     */
    protected List<ThirdPartyGroup> getSubGroups(DingTalkClient client, String accessToken, String parentGroup, boolean isFetchChild) {
        ArrayList<ThirdPartyGroup> thirdPartyGroups = new ArrayList<>();
        if (!isFetchChild) {
            List<ThirdPartyGroup> subGroups = getSubGroups(client, accessToken, parentGroup);
            thirdPartyGroups.addAll(subGroups);
        } else {
            List<String> groupFindList = new ArrayList<>();
            groupFindList.add(parentGroup);

            while (groupFindList.size() > 0) {
                List<ThirdPartyGroup> groups = new ArrayList<>();
                for (String groupCode : groupFindList) {
                    List<ThirdPartyGroup> subGroups = getSubGroups(client, accessToken, groupCode);
                    groups.addAll(subGroups);
                    thirdPartyGroups.addAll(subGroups);
                }
                groupFindList.clear();
                for (ThirdPartyGroup subGroup : groups) {
                    groupFindList.add(subGroup.getCode());
                }

            }
        }
        return thirdPartyGroups;
    }

    private List<ThirdPartyGroup> getSubGroups(DingTalkClient client, String accessToken, String parentGroup) {
        OapiV2DepartmentListsubRequest request = new OapiV2DepartmentListsubRequest();
        request.setDeptId(Long.parseLong(parentGroup));
        try {
            OapiV2DepartmentListsubResponse response = client.execute(request, accessToken);
            checkResponse(response, getDepartmentListApi);
            //转换成第三方平台用户组
            return oapiResponseTransfer.responseToThirdPartyGroups(response);

        } catch (ThirdPartyAccessTokenInvalidException e) {
            throw e;
        } catch (ApiException e) {
            throw new ThirdPartyAccountException(e.getErrCode(), e.getErrMsg());
        }
    }

    @Override
    public List<ThirdPartyAccount> getAccountsByGroup(String tenantId, String groupCode, Connector connector) throws ThirdPartyAccountException {
        if (connector == null) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.CONNECTOR_INVALID);
        }
        if (StringUtils.isBlank(groupCode)) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.GROUP_CODE_INVALID);
        }
        DingTalkConfig cfg = JsonUtil.str2Obj(connector.getConfig(), DingTalkConfig.class);
        DingTalkClient client = getDingTalkClient(cfg.getApiBaseUrl(), getUserListByDepartmentApi);
        String accessToken = getAccessToken(tenantId, cfg);
        List<ThirdPartyAccount> ret = new ArrayList<>();
        int pageSize = clientConfig.getMaxPageSize();
        try {
            ret = getAccountsByGroup(client, tenantId, accessToken, groupCode, pageSize);
        } catch (ThirdPartyAccessTokenInvalidException e) {
            accessToken = reGetAccessToken(tenantId, cfg);
            ret = getAccountsByGroup(client, tenantId, accessToken, groupCode, pageSize);
        } catch (Exception e) {
            throw e;
        }
        return ret;
    }

    @Override
    public List<ThirdPartyAccount> getAccountsByGroup(String tenantId, String groupCode, Connector connector, int pageIndex, int pageSize) throws ThirdPartyAccountException {
//        if (connector == null) {
//            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.CONNECTOR_INVALID);
//        }
//        if (StringUtils.isBlank(groupCode)) {
//            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.GROUP_CODE_INVALID);
//        }
//        DingTalkConfig cfg = JsonUtil.str2Obj(connector.getConfig(), DingTalkConfig.class);
//        DingTalkClient client = getDingTalkClient(cfg.getApiBaseUrl(), getUserListByDepartmentApi);
//        String accessToken = getAccessToken(tenantId, cfg);
//        try {
//            return getAccountsByGroupPage(client, tenantId, accessToken, groupCode, pageIndex, pageSize);
//        } catch (ThirdPartyAccessTokenInvalidException e) {
//            accessToken = reGetAccessToken(tenantId, cfg);
//            return getAccountsByGroupPage(client, tenantId, accessToken, groupCode, pageIndex, pageSize);
//        } catch (Exception e) {
//            throw e;
//        }
        return Collections.emptyList();
    }

    /***
     * 根据组获取用户
     * @param client
     * @param accessToken
     * @param groupCode
     * @param pageSize
     * @return
     */
    private List<ThirdPartyAccount> getAccountsByGroup(DingTalkClient client, String tenantId, String accessToken, String groupCode, int pageSize) {
        OapiV2UserListRequest request = new OapiV2UserListRequest();
        Long nextCursor = 0L;
        List<ThirdPartyAccount> list = new ArrayList<>();
        if (pageSize > clientConfig.getMaxPageSize()) {
            pageSize = clientConfig.getMaxPageSize();
        }
        request.setDeptId(NumberUtils.toLong(groupCode, 1L));
        request.setSize(new Long(pageSize));
        request.setOrderField("entry_desc");
        request.setContainAccessLimit(false);
        while (true) {
            // 分页查询的游标，最开始传0，后续传返回参数中的next_cursor值
            request.setCursor(nextCursor);

            try {
                OapiV2UserListResponse response = client.execute(request, accessToken);
                checkResponse(response, getUserListByDepartmentApi);
                list.addAll(oapiResponseTransfer.responseToThirdPartyAccounts(tenantId, response));
                // has_more为false 表示没有更多的分页
                Boolean hasMore = response.getResult().getHasMore();
                // has_more为false 表示没有更多的分页数据 退出循环
                if (!hasMore) {
                    LOG.info("当前获取钉钉对应部门下面的用户，已经没有用户，结束数据获取,租户:{},部门:{},nextCursor:{},pageSize:{},当前记录数:{}", tenantId, groupCode, nextCursor, pageSize, list.size());
                    break;
                }
                // 如果has_more为false，表示没有更多的分页数据
                nextCursor = response.getResult().getNextCursor();
            } catch (ThirdPartyAccessTokenInvalidException e) {
                throw e;
            } catch (ApiException e) {
                throw new ThirdPartyAccountException(e.getErrCode(), e.getErrMsg());
            }
        }
        return list;
    }

    @Override
    public List<ThirdPartyAccount> getAccountsByUserIds(String tenantId, List<String> userIds, Connector connector) throws ThirdPartyAccountException {
        /**
         * 验证通过 接口
         */
        if (connector == null) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.CONNECTOR_INVALID);
        }
        if (userIds == null || userIds.size() == 0) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.ACCOUNT_ID_EMPTY);
        }

        DingTalkConfig cfg = JsonUtil.str2Obj(connector.getConfig(), DingTalkConfig.class);
        String accessToken = getAccessToken(tenantId, cfg);

        List<ThirdPartyAccount> ret = new ArrayList<>();
        for (String userId : userIds) {
            //  测试连接器时 使用电话查询对应的用户ID  查询到的话替换userID  否则使用原ID
            ThirdPartyAccount obj = null;
            try {
                obj = getAccountByUserId(tenantId, cfg, accessToken, userId);
            } catch (ThirdPartyAccessTokenInvalidException e) {
                accessToken = reGetAccessToken(tenantId, cfg);
                obj = getAccountByUserId(tenantId, cfg, accessToken, userId);
            } catch (Exception e) {
                throw e;
            }
            if (obj != null) {
                ret.add(obj);
            }
        }
        return ret;
    }

    @Override
    public List<ThirdPartyAccount> getAccountsByMobile(String tenantId, List<String> mobiles, Connector connector) throws ThirdPartyAccountException {
        /**
         * 验证通过 接口
         */
        if (connector == null) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.CONNECTOR_INVALID);
        }
        if (mobiles == null || mobiles.size() == 0) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.ACCOUNT_ID_EMPTY);
        }

        DingTalkConfig cfg = JsonUtil.str2Obj(connector.getConfig(), DingTalkConfig.class);
        String accessToken = getAccessToken(tenantId, cfg);

        List<ThirdPartyAccount> ret = new ArrayList<>();
        for (String mobile : mobiles) {
            //  测试连接器时 使用电话查询用户信息
            ThirdPartyAccount obj = null;
            try {
                obj = getAccountByMobile(tenantId, cfg, accessToken, mobile);
            } catch (ThirdPartyAccessTokenInvalidException e) {
                accessToken = reGetAccessToken(tenantId, cfg);
                obj = getAccountByMobile(tenantId, cfg, accessToken, mobile);
            } catch (Exception e) {
                throw e;
            }
            if (obj != null) {
                ret.add(obj);
            }
        }
        return ret;
    }

    private ThirdPartyAccount getAccountByUserId(String tenantId, DingTalkConfig cfg, String accessToken, String userId) {
        try {
            OapiV2UserGetResponse response = getAccount(cfg, accessToken, userId);
            if (response == null) {
                return null;
            }
            ThirdPartyAccount obj = oapiResponseTransfer.responseToThirdPartyAccount(response);
            if (obj != null) {
                obj.setTenantId(tenantId);
            }
            return obj;
        } catch (ThirdPartyAccessTokenInvalidException | ThirdPartyAccountException e) {
            throw e;
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.SERVER_RESPONSE_INVALID.getCode(), e.getMessage());
        }
    }

    /**
     * 通过你手机号测试连接器是否可行
     *
     * @param tenantId    租户ID
     * @param cfg
     * @param accessToken access_token
     * @param mobile      电话
     * @return 用户信息
     */
    private ThirdPartyAccount getAccountByMobile(String tenantId, DingTalkConfig cfg, String accessToken, String mobile) {
        try {
            OapiV2UserGetbymobileResponse response = getAccountByMobile(cfg, accessToken, mobile);
            if (response == null) {
                return null;
            }
            // 根据查询到的userid 再查询详细信息
            String userid = response.getResult().getUserid();

            ThirdPartyAccount obj = getAccountByUserId(tenantId, cfg, accessToken, userid);
            if (obj != null) {
                obj.setTenantId(tenantId);
            }
            return obj;
        } catch (ThirdPartyAccountException e) {
            // 手机号异常 换userId测试
            ThirdPartyAccount obj = getAccountByUserId(tenantId, cfg, accessToken, mobile);
            if (obj != null) {
                obj.setTenantId(tenantId);
            }
            return obj;
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.SERVER_RESPONSE_INVALID.getCode(), e.getMessage());
        }
    }


    @Override
    public List<Map<String, Object>> getTenantUserProfileByUserIds(String tenantId, List<String> userIds, Connector connector) throws ThirdPartyAccountException {
        if (connector == null) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.CONNECTOR_INVALID);
        }
        if (userIds == null || userIds.size() == 0) {
            return null;
        }

        DingTalkConfig cfg = JsonUtil.str2Obj(connector.getConfig(), DingTalkConfig.class);
        String accessToken = getAccessToken(tenantId, cfg);
        List<Map<String, Object>> ret = new ArrayList<>();
        for (String userId : userIds) {
            Map<String, Object> obj = null;
            try {
                obj = getAccountMapByUserId(cfg, accessToken, userId);
            } catch (ThirdPartyAccessTokenInvalidException e) {
                accessToken = reGetAccessToken(tenantId, cfg);
                obj = getAccountMapByUserId(cfg, accessToken, userId);
            } catch (Exception e) {
                throw e;
            }
            if (obj != null) {
                ret.add(obj);
            }
        }
        return ret;
    }

    /**
     * 获取转换为Map
     *
     * @param cfg
     * @param accessToken
     * @param userId
     * @return
     */
    private Map<String, Object> getAccountMapByUserId(DingTalkConfig cfg, String accessToken, String userId) {
        OapiV2UserGetResponse response = getAccount(cfg, accessToken, userId);
        Map<String, Object> obj = JsonUtil.obj2Map(response);
        return obj;
    }


    /***
     *
     * @param cfg
     * @param accessToken
     * @param userId
     * @return
     */
    protected OapiV2UserGetResponse getAccount(DingTalkConfig cfg, String accessToken, String userId) {
        //  通过UserId 连接器测试 用此方法
        DingTalkClient client = getDingTalkClient(cfg.getApiBaseUrl(), getUserApi);
        OapiV2UserGetRequest request = new OapiV2UserGetRequest();
        request.setUserid(userId);
        try {
            OapiV2UserGetResponse response = client.execute(request, accessToken);
            checkResponse(response, getUserApi);
            return response;
        } catch (ThirdPartyAccessTokenInvalidException e) {
            throw e;
        } catch (ApiException e) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.SERVER_RESPONSE_INVALID.getCode(), e.getErrMsg());
        } catch (Throwable e) {
            throw new ThirdPartyAccountException(e.getMessage(), e);
        }
    }

    /***
     *
     * @param cfg
     * @param accessToken
     * @param mobile
     * @return
     */
    protected OapiV2UserGetbymobileResponse getAccountByMobile(DingTalkConfig cfg, String accessToken, String mobile) {
        // 通过用户电话 连接器测试 用此方法
        DingTalkClient client = getDingTalkClient(cfg.getApiBaseUrl(), getUserApiByMobile);
        OapiV2UserGetbymobileRequest request = new OapiV2UserGetbymobileRequest();
        request.setMobile(mobile);
        try {
            OapiV2UserGetbymobileResponse response = client.execute(request, accessToken);
            checkResponse(response, getUserApiByMobile);
            return response;
        } catch (ThirdPartyAccessTokenInvalidException e) {
            throw e;
        } catch (ApiException e) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.SERVER_RESPONSE_INVALID.getCode(), e.getErrMsg());
        } catch (Throwable e) {
            throw new ThirdPartyAccountException(e.getMessage(), e);
        }
    }

    @Override
    public DingTalkClientConfig getClientConfig() {
        return clientConfig;
    }

    @Override
    public void setClientConfig(DingTalkClientConfig clientConfig) {
        this.clientConfig = clientConfig;
    }

    @Override
    public IDingTalkAccessTokenService getDingTalkAccessTokenService() {
        return dingTalkAccessTokenService;
    }

    @Override
    public void setDingTalkAccessTokenService(IDingTalkAccessTokenService dingTalkAccessTokenService) {
        this.dingTalkAccessTokenService = dingTalkAccessTokenService;
    }

    public IOapiResponseTransfer getOapiResponseTransfer() {
        return oapiResponseTransfer;
    }

    public void setOapiResponseTransfer(IOapiResponseTransfer oapiResponseTransfer) {
        this.oapiResponseTransfer = oapiResponseTransfer;
    }
}
