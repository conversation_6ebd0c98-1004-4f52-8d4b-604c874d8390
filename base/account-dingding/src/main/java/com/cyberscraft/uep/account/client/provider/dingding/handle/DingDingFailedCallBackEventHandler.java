package com.cyberscraft.uep.account.client.provider.dingding.handle;

import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiCallBackGetCallBackFailedResultRequest;
import com.dingtalk.api.response.OapiCallBackGetCallBackFailedResultResponse;
import com.cyberscraft.uep.common.exception.BaseException;
import com.cyberscraft.uep.common.enums.ExceptionCodeEnum;
import com.cyberscraft.uep.account.client.constant.ThirdPartyAccountType;
import com.cyberscraft.uep.account.client.constant.ThirdPartyThreadPoolNameConstant;
import com.cyberscraft.uep.account.client.domain.Connector;
import com.cyberscraft.uep.account.client.domain.ThirdPartyEvent;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountErrorType;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountException;
import com.cyberscraft.uep.account.client.provider.IThirdPartyFailedCallBackEventHandler;
import com.cyberscraft.uep.account.client.provider.dingding.domain.DingTalkConfig;
import com.cyberscraft.uep.account.client.provider.dingding.event.IDingTalkEventParser;
import com.cyberscraft.uep.account.client.service.IThirdPartyEventExecutorService;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.taobao.api.ApiException;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

/***
 *
 * @date 2021/3/26
 * <AUTHOR>
 ***/
public class DingDingFailedCallBackEventHandler extends AbstractDingHandler implements IThirdPartyFailedCallBackEventHandler {

    /***
     *
     */
    private final static Logger LOG = LoggerFactory.getLogger(DingDingFailedCallBackEventHandler.class);


    /***
     * 第三方平台事件执行器
     */
    private IThirdPartyEventExecutorService thirdPartyEventExecutorService;

    /***
     * 线程池配置接口
     */
    @Resource(name = ThirdPartyThreadPoolNameConstant.THIRDPARTY_MESSAGE_POOL_NAME)
    private ExecutorService messagePoolExcutor;


    private List<IDingTalkEventParser> parsers = new ArrayList<>();


    private final static ConcurrentHashMap<String, IDingTalkEventParser> PARSER_MAP = new ConcurrentHashMap<>();

    @Resource
    private RedissonClient redissonClient;

    private final static String DINGDING_REDO_LOCK_KEY = "DD_REDO_LOCK:%s";

    private final static Integer DINGDING_REDO_LOCK_EXPIRE = 10;

    /***
     * 得到已经回调的失败结果
     */
    private String getFailedCallBackEventApi = "/call_back/get_call_back_failed_result";


    public List<IDingTalkEventParser> getParsers() {
        return parsers;
    }

    @Autowired
    public void setParsers(List<IDingTalkEventParser> parsers) {
        this.parsers = parsers;
        this.initParserMap();
    }

    /***
     *
     */
    private synchronized void initParserMap() {
        PARSER_MAP.clear();
        if (this.parsers != null && this.parsers.size() > 0) {
            for (IDingTalkEventParser handler : this.parsers) {
                Set<String> msgTypes = handler.getSupportedMessageType();
                if (msgTypes == null) {
                    LOG.warn("{}对应的消息解释器未指定可以支持的消息类型，可能是一个错误", handler.getClass().getSimpleName());
                    continue;
                }
                for (String msgType : msgTypes) {
                    PARSER_MAP.put(msgType, handler);
                }
            }
        }
    }

    /***
     *
     * @param msgType
     * @return
     */
    private IDingTalkEventParser findHandler(String msgType) {
        return PARSER_MAP.get(msgType);
    }


    @Override
    public String getSupportedAccountType() {
        return ThirdPartyAccountType.DINGDING.getCode();
    }

    /***
     * 进行失败补偿逻辑处理，采用异步线程池，从服务器，获取失败回调事件列表，将失败回调事件，转换成钉钉事件，进行处理。
     * @param tenantId
     * @param connector
     * @throws ThirdPartyAccountException
     */
    @Override
    public void redoFailedEvent(String tenantId, Connector connector) throws ThirdPartyAccountException {
        messagePoolExcutor.execute(() -> {
            try {
                String key = String.format(DINGDING_REDO_LOCK_KEY, tenantId);
                //RBucket<String> bucket = redissonClient.getBucket(String.format(CONNECTOR_SYNC_KEY, TenantHolder.getTenantCode(), connector.getId()));
                RLock lock = redissonClient.getLock(key);
                if (!lock.tryLock(DINGDING_REDO_LOCK_EXPIRE, DINGDING_REDO_LOCK_EXPIRE, TimeUnit.SECONDS)) {
                    LOG.error("redo ding ding failed event, cannot get lock");
                    return;
                }
                try {
                    redoFailedEventInner(tenantId, connector);
                } catch (Exception e) {
                    throw e;
                } finally {
                    try {
                        lock.unlock();
                    } catch (Exception ex) {

                    }
                }
            } catch (Exception e) {
                LOG.error(e.getMessage(), e);
            }
        });
    }

    private void redoFailedEventInner(String tenantId, Connector connector) throws ThirdPartyAccountException {
        if (!clientConfig.getFailuredCallBackEventEnabled()) {
            LOG.warn("if you need dingding failured call back event,you must set dingding.failuredCallBackEventEnabled=true");
            return;
        }
        LOG.info("the ding ding failured call back event deal started, tenant is :{}", tenantId);
        while (true) {
            OapiCallBackGetCallBackFailedResultResponse response = getFiluredCallBackEvent(tenantId, connector);
            if (response.getFailedList() != null) {
                LOG.info("当前需要处理{}个钉钉失败事件", response.getFailedList() != null ? response.getFailedList().size() : 0);
                for (OapiCallBackGetCallBackFailedResultResponse.Failed failed : response.getFailedList()) {
                    try {
                        ThirdPartyEvent<?> event = parserThirdPartyEvent(failed, tenantId, connector);
                        if (event != null) {
                            try {
                                Thread.sleep(50);
                            } catch (InterruptedException e) {
                                Thread.currentThread().interrupt();
                            }
                            thirdPartyEventExecutorService.onEvent(event);
                        }
                    } catch (Exception e) {
                        LOG.error("当前处理钉钉回调失败重试事件出错:{}", e.getMessage());
                    } finally {
                        //TODO 需要删除对应的失败事件
                    }
                }
            }

            if (response.getHasMore() == null || !response.getHasMore()) {
                LOG.info("the ding ding failured call back event has no more data this will return");
                break;
            }
        }
        LOG.info("the ding ding failured call back event deal finished, tenant is :{}", tenantId);
    }

    /***
     * 将失败回调，转换成第三方平台事件
     * @param failed
     * @return
     */
    private ThirdPartyEvent<?> parserThirdPartyEvent(OapiCallBackGetCallBackFailedResultResponse.Failed failed, String tenantId, Connector connector) {

        IDingTalkEventParser parser = findHandler(failed.getCallBackTag());
        if (parser == null) {
            LOG.error("未找到callBackTag:{}对应的事件解析器,data:{}", failed.getCallBackTag(), failed.getData());
            return null;
        }
        return parser.parse(failed, tenantId, connector);
    }


    /***
     * 删除回调失败的事件列表
     * @param tenantId
     * @param connector
     */
    private OapiCallBackGetCallBackFailedResultResponse getFiluredCallBackEvent(String tenantId, Connector connector) {
        if (tenantId == null) {
            throw new BaseException(ExceptionCodeEnum.TENANT_INVALID);
        }
        if (connector == null) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.CONNECTOR_INVALID);
        }
        DingTalkConfig cfg = JsonUtil.str2Obj(connector.getConfig(), DingTalkConfig.class);
        String accessToken = getAccessToken(tenantId, cfg);
        if (StringUtils.isBlank(accessToken)) {
            throw new ThirdPartyAccountException(ExceptionCodeEnum.ACCESSTOKEN_INVALID);
        }
        DingTalkClient client = getDingTalkClient(cfg.getApiBaseUrl(), getFailedCallBackEventApi);
        OapiCallBackGetCallBackFailedResultRequest request = new OapiCallBackGetCallBackFailedResultRequest();
        request.setHttpMethod(HttpMethod.GET.name());
        try {
            OapiCallBackGetCallBackFailedResultResponse response = client.execute(request, accessToken);
            checkResponse(response, getFailedCallBackEventApi);
            return response;
        } catch (ApiException e) {
            throw new ThirdPartyAccountException(e.getErrCode(), e.getErrMsg());
        }
    }


    public IThirdPartyEventExecutorService getThirdPartyEventExecutorService() {
        return thirdPartyEventExecutorService;
    }

    public void setThirdPartyEventExecutorService(IThirdPartyEventExecutorService thirdPartyEventExecutorService) {
        this.thirdPartyEventExecutorService = thirdPartyEventExecutorService;
    }

    public ExecutorService getMessagePoolExcutor() {
        return messagePoolExcutor;
    }

    public void setMessagePoolExcutor(ExecutorService messagePoolExcutor) {
        this.messagePoolExcutor = messagePoolExcutor;
    }

    public RedissonClient getRedissonClient() {
        return redissonClient;
    }

    public void setRedissonClient(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }
}
