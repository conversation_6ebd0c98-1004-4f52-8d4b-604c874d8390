package com.cyberscraft.uep.aksk.server.constants;

/***
 *
 * @date 2021/8/10
 * <AUTHOR>
 ***/
public class AKSKConstant {

    /***
     * app Key参数
     */
    public final static String AK_PARAMETER_NAME = "appKey";

    /***
     * app secret参数
     */
    public final static String SK_PARAMETER_NAME = "appSecret";

    /***
     * nonce值
     */
    public final static String NONCE_PARAMETER_NAME = "nonce";

    /***
     * 时间截字段，如果是业务中没有指定签名字段，或者是加密处理时，用于进行签名处理
     */
    public final static String TIMESTAMP_PARAMETER_NAME = "timestamp";

    /***
     * 签名参数
     */
    public final static String SIGN_PARAMETER_NAME = "signature";
}
