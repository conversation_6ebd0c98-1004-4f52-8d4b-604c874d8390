package com.cyberscraft.uep.aksk.server.service;

import com.cyberscraft.uep.aksk.server.domain.AppItem;

import java.util.List;
import java.util.Map;

/***
 * AKSK 明细接口,需要实现成单例，因为还需要往该接口中设置数据
 * @date 2021/8/10
 * <AUTHOR>
 ***/
public interface IAKSKDetailService {

    /****
     *
     * @return
     */
    List<AppItem> getList();

    /****
     *
     * @return
     */
    Map<String, AppItem> getAKSKAppMaps();

    /***
     *
     * @param item
     */
    void addApp(AppItem item);

    /***
     *
     * @param appKey
     */
    void removeApp(String appKey);

    /***
     *
     * @param list
     */
    void setApps(List<AppItem> list);

    /***
     *
     * @param appKey
     * @return
     */
    AppItem get(String appKey);

    /***
     * 得到App Key对应的secret key
     * @param appKey
     * @return
     */
    String getSecretKey(String appKey);
}
