package com.cyberscraft.uep.aksk.server;

import com.cyberscraft.uep.common.bean.Result;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.aksk.server.constants.AKSKConstant;
import com.cyberscraft.uep.aksk.server.service.IAKSKDetailService;
import org.springframework.http.HttpStatus;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;

/***
 *
 * @date 2021/8/10
 * <AUTHOR>
 ***/
public class AKSKSignInterceptor implements HandlerInterceptor {


    /***
     * AKSK 信息键值数据获取接口，通过外部进行注入，如果有更改，则在外部监控更改，并且更改过滤器中的值，以便于应用最新的值
     */
    private IAKSKDetailService akskDataLoader;


    public IAKSKDetailService getAkskDataLoader() {
        return akskDataLoader;
    }

    public void setAkskDataLoader(IAKSKDetailService akskDataLoader) {
        this.akskDataLoader = akskDataLoader;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String url = request.getRequestURI();
        String queryStringUrl = request.getQueryString();
        String methodType = request.getMethod();
        //如果是不需要进行AKSK签名的页面，则直接跳过,签名参数
        if (!isInFilterUrl(url, methodType)) {
            return true;
        }
        signVierfy(url, request, response, handler);
        return true;
    }

    /****会进行验证
     * 进行解码及验证处理,如果是post，并且body有参数的时候，因为body属于流，所以需要进行封装处理
     * @param url
     * @param request
     */
    private void signVierfy(String url, HttpServletRequest request, HttpServletResponse response, Object handler) {
        if (url.equalsIgnoreCase("/message/send")) {
            error(HttpStatus.UNAUTHORIZED, response, null);
            return;
        }

        String appId = request.getParameter(AKSKConstant.AK_PARAMETER_NAME);
        String secret = this.akskDataLoader.getSecretKey(appId);
    }

    /***
     *
     * @param response
     * @param result
     * @param <T>
     * @return
     */
    public static <T> boolean error(HttpStatus status, HttpServletResponse response, Result<T> result) {
        try {
            response.setStatus(status.value());
            response.setContentType("application/json;charset=UTF-8");
            PrintWriter pw = response.getWriter();
            pw.append(JsonUtil.obj2Str(result));
            pw.close();
        } catch (IOException e) {
            return false;
        }
        return false;
    }

    /***
     *
     * @param url
     * @param methodType
     * @return
     */
    private boolean isInFilterUrl(String url, String methodType) {
        return true;
    }
}
