package com.cyberscraft.uep.aksk.server.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/***
 * 主要用于配置当前服务中的需要签名及需要加密的URL及参数名称
 * @date 2021/8/11
 * <AUTHOR>
 ***/
@ConfigurationProperties(
        prefix = "sysconf.aksk"
)
@Component
@RefreshScope
public class AKSKFilterConfig {
    /***
     * 名称及对应的过滤器信息配置项
     */
    private Map<String, AKSKConfigFilterItem> filters;

    /****
     * 从配置中加载对应的APP信息
     */
    private boolean loadAppFromConfig;

    /***
     * 配置的应用数据列表
     */
    private List<AKSKConfigAppItem> apps;

    /***
     * 默认的时间差不能相差2小时,否则报请求不合法
     */
    private Integer timestampInterval = 0;

    public Map<String, AKSKConfigFilterItem> getFilters() {
        return filters;
    }

    public void setFilters(Map<String, AKSKConfigFilterItem> filters) {
        this.filters = filters;
    }

    public List<AKSKConfigAppItem> getApps() {
        return apps;
    }

    public void setApps(List<AKSKConfigAppItem> apps) {
        this.apps = apps;
    }

    public boolean isLoadAppFromConfig() {
        return loadAppFromConfig;
    }

    public void setLoadAppFromConfig(boolean loadAppFromConfig) {
        this.loadAppFromConfig = loadAppFromConfig;
    }

    public Integer getTimestampInterval() {
        return timestampInterval;
    }

    public void setTimestampInterval(Integer timestampInterval) {
        this.timestampInterval = timestampInterval;
    }
}
