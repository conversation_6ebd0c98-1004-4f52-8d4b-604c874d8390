package com.cyberscraft.uep.aksk.server.service.impl;

import com.cyberscraft.uep.aksk.server.config.AKSKConfigAppItem;
import com.cyberscraft.uep.aksk.server.config.AKSKFilterConfig;
import com.cyberscraft.uep.aksk.server.domain.AppItem;
import com.cyberscraft.uep.aksk.server.service.IAKSKAppDataService;

import javax.annotation.PostConstruct;
import java.util.List;

/***
 *
 * @date 2021/8/12
 * <AUTHOR>
 ***/
public class ConfigAKSKAppDataServiceImpl implements IAKSKAppDataService {

    private AKSKFilterConfig akskFilterConfig;

    //private IAKSKDetailService akskDetailService;

    public ConfigAKSKAppDataServiceImpl(AKSKFilterConfig akskFilterConfig) {
        this.akskFilterConfig = akskFilterConfig;
        //this.akskDetailService = akskDetailService;
    }

    @PostConstruct
    @Override
    public void load() {
        List<AKSKConfigAppItem> apps = akskFilterConfig.getApps();
        AKSKDetailService akskDetailService= AKSKDetailService.getInstance();
        for (AKSKConfigAppItem item : apps) {
            AppItem app = new AppItem();
            app.setAesKey(item.getAesKey());
            app.setAppKey(item.getAppKey());
            app.setSecretKey(item.getAppSecret());
            app.setToken(item.getToken());
            akskDetailService.addApp(app);
        }
    }
}
