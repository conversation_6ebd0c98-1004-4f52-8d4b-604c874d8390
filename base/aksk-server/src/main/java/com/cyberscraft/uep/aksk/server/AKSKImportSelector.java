package com.cyberscraft.uep.aksk.server;

import com.cyberscraft.uep.aksk.server.exception.AKSKException;
import com.cyberscraft.uep.common.util.ClassScanUtil;
import org.springframework.context.annotation.ImportSelector;
import org.springframework.core.type.AnnotationMetadata;

import java.util.Set;

/***
 *
 * @date 2021/8/27
 * <AUTHOR>
 ***/
public class AKSKImportSelector implements ImportSelector {
    @Override
    public String[] selectImports(AnnotationMetadata importingClassMetadata) {
        final Set<String> classes = ClassScanUtil.scan(AKSKException.class);
        return classes.toArray(new String[0]);
    }
}
