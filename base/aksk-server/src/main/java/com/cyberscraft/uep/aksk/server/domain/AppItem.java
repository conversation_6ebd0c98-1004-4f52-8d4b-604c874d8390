package com.cyberscraft.uep.aksk.server.domain;

import java.io.Serializable;

/***
 *
 * @date 2021/8/10
 * <AUTHOR>
 ***/
public class AppItem implements Serializable {
    /***
     *
     */
    private String appKey;

    /***
     *
     */
    private String secretKey;

    /***
     * 加密所用密钥
     */
    private String token;

    /**
     * 加密所用的key
     */
    private String aesKey;

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getAesKey() {
        return aesKey;
    }

    public void setAesKey(String aesKey) {
        this.aesKey = aesKey;
    }
}
