package com.cyberscraft.uep.aksk.server.service.impl;

import com.cyberscraft.uep.aksk.server.domain.AppItem;
import com.cyberscraft.uep.aksk.server.service.IAKSKDetailService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/***
 * 直接代码中写成单例模式，以保证唯一性
 * @date 2021/8/11
 * <AUTHOR>
 ***/
public class AKSKDetailService {

    private final static Logger LOG = LoggerFactory.getLogger(AKSKDetailService.class);

    private ReadWriteLock lock = new ReentrantReadWriteLock();

    private AKSKDetailService() {

    }

    public static AKSKDetailService getInstance() {
        return AKSKDetailServiceInner.getInstance();
    }

    private static class AKSKDetailServiceInner {
        private static AKSKDetailService INSTANCE = new AKSKDetailService();

        static AKSKDetailService getInstance() {
            return INSTANCE;
        }
    }

    private final static Map<String, AppItem> APP_KEY_APP_MAP = new HashMap<>();



    //@Override
    public List<AppItem> getList() {
        try {
            lock.readLock().lockInterruptibly();
            try {
                return new ArrayList<>(APP_KEY_APP_MAP.values());
            } catch (Exception ex) {
                LOG.error(ex.getMessage(), ex);
            } finally {
                lock.readLock().unlock();
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
        return null;
    }


    //@Override
    public AppItem get(String appKey) {
        try {
            lock.readLock().lockInterruptibly();
            try {
                return APP_KEY_APP_MAP.get(appKey);
            } catch (Exception ex) {
                LOG.error(ex.getMessage(), ex);
            } finally {
                lock.readLock().unlock();
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
        return null;
    }

    //@Override
    public String getSecretKey(String appKey) {
        try {
            lock.readLock().lockInterruptibly();
            try {
                AppItem item = get(appKey);
                return item != null ? item.getSecretKey() : null;
            } catch (Exception ex) {
                LOG.error(ex.getMessage(), ex);
            } finally {
                lock.readLock().unlock();
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
        return null;
    }

    //@Override
    public Map<String, AppItem> getAKSKAppMaps() {
        try {
            lock.readLock().lockInterruptibly();
            try {
                return APP_KEY_APP_MAP;
            } catch (Exception ex) {
                LOG.error(ex.getMessage(), ex);
            } finally {
                lock.readLock().unlock();
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
        return null;
    }

    //@Override
    public void addApp(AppItem item) {
        try {
            lock.writeLock().lockInterruptibly();
            try {
                APP_KEY_APP_MAP.put(item.getAppKey(), item);
            } catch (Exception ex) {
                LOG.error(ex.getMessage(), ex);
            } finally {
                lock.writeLock().unlock();
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }

    //@Override
    public void removeApp(String appKey) {
        try {
            lock.writeLock().lockInterruptibly();
            try {
                APP_KEY_APP_MAP.remove(appKey);
            } catch (Exception ex) {
                LOG.error(ex.getMessage(), ex);
            } finally {
                lock.writeLock().unlock();
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }

    //@Override
    public void setApps(List<AppItem> list) {
        try {
            lock.writeLock().lockInterruptibly();
            try {
                APP_KEY_APP_MAP.clear();
                for (AppItem item : list) {
                    APP_KEY_APP_MAP.put(item.getAppKey(), item);
                }
            } catch (Exception ex) {
                LOG.error(ex.getMessage(), ex);
            } finally {
                lock.writeLock().unlock();
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }

}
