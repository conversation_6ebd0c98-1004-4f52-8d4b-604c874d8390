package com.cyberscraft.uep.account.client.provider.feishu.service.impl;

import com.cyberscraft.uep.account.client.constant.ConnectorType;
import com.cyberscraft.uep.account.client.domain.account.FeiShuAccount;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccessTokenInvalidException;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountErrorType;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountException;
import com.cyberscraft.uep.account.client.provider.feishu.domain.FeiShuConfig;
import com.cyberscraft.uep.account.client.provider.feishu.service.AbstractFeiShuRequest;
import com.cyberscraft.uep.account.client.provider.feishu.service.IFeishuMessageService;
import com.cyberscraft.uep.account.client.service.IAuthenticateService;
import com.cyberscraft.uep.common.bean.RestApiResponse;
import com.cyberscraft.uep.common.domain.HttpMethod;
import com.cyberscraft.uep.common.domain.auth.AuthorizeCredential;
import com.cyberscraft.uep.common.domain.message.*;
import com.cyberscraft.uep.common.enums.SysCodeEnum;
import com.cyberscraft.uep.common.exception.ApiConnectException;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.common.util.RestAPIUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/12/30 15:24
 */
@Service
public class FeiShuMessageServiceImpl extends AbstractFeiShuRequest implements IFeishuMessageService {

    private final static Logger LOG = LoggerFactory.getLogger(FeiShuMessageServiceImpl.class);

    private final static String SEND_MESSAGE_API = "https://open.feishu.cn/open-apis/im/v1/messages";

    private final static String BATCH_SEND_MESSAGE_API = "https://open.feishu.cn/open-apis/message/v4/batch_send/";

    private final static String MESSAGE_PROGRESS_API = "https://open.feishu.cn/open-apis/im/v1/batch_messages/%s/get_progress";

    private final static String MESSAGE_READ_API = "https://open.feishu.cn/open-apis/im/v1/messages/%s/read_users";

    @Autowired
    private IAuthenticateService authenticateService;

    public AuthorizeCredential getAuthCredential(FeiShuConfig config, Boolean refresh) {
        try {
            String strConfig = JsonUtil.obj2Str(config);
            FeiShuAccount account = JsonUtil.str2Obj(strConfig, FeiShuAccount.class);
            return authenticateService.auth(ConnectorType.FEISHU.name(), account, account.getAppKey(), refresh);
        } catch (ApiConnectException ae) {
            throw new ThirdPartyAccountException(ae.getMultiLanguageExceptionCodes());
        } catch (Exception e) {
            throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), e.getMessage());
        }
    }

    @Override
    public MessageSendResult send(ThirdMessageEntity message, FeiShuConfig config, String tenantId) {
        Map<String, Object> map = messageBody(message);
        Map<String, Object> query = new HashMap<>();
        query.put("receive_id_type", "user_id");
        RestApiResponse restApiResponse = sendHttp(map, query, config, SEND_MESSAGE_API);
        return res(restApiResponse);
    }


    private RestApiResponse sendHttp(Map<String, Object> params, Map<String, Object> query, FeiShuConfig config, String api) {
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json; charset=utf-8");
        try {
            AuthorizeCredential credential = getAuthCredential(config, false);
            headers.putAll(credential.getHead());
            RestApiResponse restApiResponse = RestAPIUtil.modifyEntityForString(api, HttpMethod.POST.name(), params, headers, query, new HashMap());
            checkRestResponse(restApiResponse, api);
            return restApiResponse;
        } catch (ThirdPartyAccessTokenInvalidException e) {
            AuthorizeCredential credential = getAuthCredential(config, true);
            headers.putAll(credential.getHead());
            RestApiResponse restApiResponse = RestAPIUtil.modifyEntityForString(api, HttpMethod.POST.name(), params, headers, query, new HashMap());
            checkRestResponse(restApiResponse, api);
            return restApiResponse;
        }
    }

    private MessageSendResult res(RestApiResponse restApiResponse) {
        MessageSendResult result = new MessageSendResult();
        Object responseBody = restApiResponse.getBody();
        if (responseBody instanceof String) {
            result.setStatus(MessageSendStatusEnum.FINISHED);
            Map<String, Object> res = JsonUtil.str2Map(responseBody.toString());
            Object data = res.get("data");
            if (data != null) {
                Map<String, Object> dataMap = (Map) data;
                Object messageId = dataMap.get("message_id");
                result.setTaskIds(Arrays.asList(String.valueOf(messageId)));
            }
        } else {
            Map<String, Object> res = (Map) restApiResponse.getBody();
            result.setStatus(MessageSendStatusEnum.FINISHED);
            result.setSuccessUserSize(1);
            Object data = res.get("data");
            if (data != null) {
                Map<String, Object> dataMap = (Map) data;
                Object messageId = dataMap.get("message_id");
                result.setTaskIds(Arrays.asList(String.valueOf(messageId)));
            }
        }
        return result;
    }


    private Map<String, Object> messageBody(ThirdMessageEntity message) {
        ThirdMessageBody body = message.getBody();
        if (body == null) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.MESSAGE_BODY_INVALID);
        }
        if (body.getMsgType() == null) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.MESSAGE_BODY_INVALID);
        }

        Map<String, Object> msg = new HashMap<>();
        msg.put("msg_type", body.getMsgType().getCode());
        msg.put("receive_id", message.getUserIdList().get(0));
        switch (body.getMsgType()) {
            case TEXT:
                MessageText textMsg = (MessageText) body;
                Map<String, Object> text = new HashMap<>();
                text.put("text", textMsg.getText());
                msg.put("content", JsonUtil.obj2Str(text));
                break;
            default:
                LOG.error("invalid dingTalk message type :{}", body.getMsgType());
                throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.MESSAGE_TYPE_INVALID);
        }

        return msg;
    }

    /**
     * 飞书没有发送消息进度的接口 默认发送完成
     *
     * @param taskId   任务ID
     * @param bizType  消息类型
     * @param config   配置信息
     * @param tenantId 租户ID
     * @return 飞书没有发送消息进度的接口 默认发送完成
     */
    @Override
    public MessageSendProgress getSendProgress(String taskId, MessageBizType bizType, FeiShuConfig config, String tenantId) {
        MessageSendProgress progress = new MessageSendProgress();
        progress.setStatus(MessageSendStatusEnum.FINISHED);
        progress.setProgressInPercent(100);
        return progress;
    }

    @Override
    public MessageSendResult getSendResult(String taskId, MessageBizType bizType, FeiShuConfig config, String tenantId) {
        String url = String.format(MESSAGE_READ_API, taskId);
        MessageSendResult result = new MessageSendResult();
        Map<String, Object> query = new HashMap<>();
        query.put("receive_id_type", "user_id");
        query.put("page_size", 20);
        RestApiResponse restApiResponse = null;
        try {
            restApiResponse = sendHttp(null, query, config, url);
            Object responseBody = restApiResponse.getBody();
            if (responseBody instanceof String) {
                result.setStatus(MessageSendStatusEnum.FINISHED);
                Map<String, Object> res = JsonUtil.str2Map(responseBody.toString());
                Object data = res.get("data");
                if (data != null) {
                    Map<String, Object> dataMap = (Map) data;
                    List<Object> items = (List)dataMap.get("items");
                    result.setReadUserSize(items.size());
                }
            } else {
                Map<String, Object> res = (Map) responseBody;
                result.setStatus(MessageSendStatusEnum.FINISHED);
                result.setSuccessUserSize(1);
                Object data = res.get("data");
                if (data != null) {
                    Map<String, Object> dataMap = (Map) data;
                    List<Object> items = (List)dataMap.get("items");
                    result.setReadUserSize(items.size());
                }
            }
        } catch (Exception e) {
            LOG.warn("get send result error:{}", e.getMessage());
        }
        result.setStatus(MessageSendStatusEnum.FINISHED);
        result.setTaskIds(Arrays.asList(taskId));

        return result;
    }

    private MessageSendResult sendResult(RestApiResponse restApiResponse) {
        MessageSendResult result = new MessageSendResult();
        Object responseBody = restApiResponse.getBody();
        if (responseBody instanceof String) {
            result.setStatus(MessageSendStatusEnum.FINISHED);
            result.setTaskIds(Arrays.asList(String.valueOf(JsonUtil.str2Map(responseBody.toString()).get("message_id").toString())));
        } else {
            Map<String, Object> res = (Map) restApiResponse.getBody();
            result.setStatus(MessageSendStatusEnum.FINISHED);
            result.setTaskIds(Arrays.asList(String.valueOf(res.get("message_id").toString())));
        }
        return result;
    }

}
