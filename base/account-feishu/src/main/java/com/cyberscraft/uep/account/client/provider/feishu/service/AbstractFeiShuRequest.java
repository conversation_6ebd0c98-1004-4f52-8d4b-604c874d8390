package com.cyberscraft.uep.account.client.provider.feishu.service;

import com.cyberscraft.uep.account.client.exception.ThirdPartyAccessTokenInvalidException;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountErrorType;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountException;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountNotExistException;
import com.cyberscraft.uep.account.client.provider.feishu.config.FeiShuClientConfig;
import com.cyberscraft.uep.account.client.provider.feishu.constant.FeiShuErrorCodeEnum;
import com.cyberscraft.uep.common.bean.RestApiResponse;
import com.cyberscraft.uep.common.util.JsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

/**
 * @Author：xhquan
 * @Project：uep-services
 * @since：2023/2/27 16:56
 */
public abstract class AbstractFeiShuRequest {

    protected final static Logger LOG = LoggerFactory.getLogger(AbstractFeiShuRequest.class);

    @Autowired
    protected FeiShuClientConfig clientConfig;

    public FeiShuClientConfig getClientConfig() {
        return clientConfig;
    }

    public void setClientConfig(FeiShuClientConfig clientConfig) {
        this.clientConfig = clientConfig;
    }

    protected void checkRestResponse(RestApiResponse apiResponse, String api) {
        Map<String, Object> response = null;
        if (apiResponse.getBody() instanceof String) {
            response = JsonUtil.str2Map((String) apiResponse.getBody());
        } else {
            response = JsonUtil.obj2Map(apiResponse.getBody());
        }

        try {
            Thread.sleep(clientConfig.getRequestInterval()); //减少访问频率，此处只能减少单个线程的频率，对于多线程无效
        } catch (Exception e) {

        }

        String code = String.valueOf(response.get("code"));
        String message = (String) response.get("msg");

        if ("99991663".equalsIgnoreCase(code)) { //因为Token不合法，所以进行Token的清除处理
            throw new ThirdPartyAccessTokenInvalidException("feiShu-99991663", message);
        }
        if ("40003".equalsIgnoreCase(code)) { //因为Token不合法，所以进行Token的清除处理
            throw new ThirdPartyAccessTokenInvalidException("feiShu-40003", message);
        }
        if ("********".equalsIgnoreCase(code)) { //因为Token过期，所以要进行Token的清除处理
            throw new ThirdPartyAccessTokenInvalidException("feiShu-********", message);
        }
        // 40001部门不存在 42006用户不存在
        if ("40001".equalsIgnoreCase(code) || "42006".equalsIgnoreCase(code)) {
            throw new ThirdPartyAccountNotExistException("feiShu-" + code, message);
        }

        if (!FeiShuErrorCodeEnum.isSuccess(code)) {
            LOG.error("feiShu response error,errorCode:{}:{}:{},api:{}", code, message, response, api);
            if (StringUtils.isNotBlank(code)) {
                throw new ThirdPartyAccountException("feiShu-" + code, "feiShu-" + message);
            } else {
                throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.SERVER_RESPONSE_INVALID.getCode(), "调用飞书服务错误，服务器未正确返回结果");
            }
        }
        if (LOG.isDebugEnabled()) {
            LOG.debug("feiShu api:{}, response errorCode:{},message:{}, body:{},api:{}", api, code, message, response, api);
        }
    }
}
