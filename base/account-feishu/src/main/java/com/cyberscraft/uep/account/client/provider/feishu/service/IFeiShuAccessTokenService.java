package com.cyberscraft.uep.account.client.provider.feishu.service;

import com.cyberscraft.uep.account.client.provider.feishu.domain.FeiShuConfig;

/**
 * @Author：xhquan
 * @Project：uep-services
 * @since：2023/2/27 17:07
 */
public interface IFeiShuAccessTokenService {

    /**
     * 获取租户ID对应的飞书配置的accessToken
     *
     * @param tenantId 租户ID
     * @param config   配置信息
     * @return
     */
    String getAccessToken(String tenantId, FeiShuConfig config);

    /**
     * 重新获取租户ID对应飞书配置的accessToken
     *
     * @param tenantId 租户ID
     * @param config   配置信息
     * @return
     */
    String reGetAccessToken(String tenantId, FeiShuConfig config);
}
