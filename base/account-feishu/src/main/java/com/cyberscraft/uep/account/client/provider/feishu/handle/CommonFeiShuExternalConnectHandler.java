package com.cyberscraft.uep.account.client.provider.feishu.handle;

import com.cyberscraft.uep.account.client.constant.AccountConstant;
import com.cyberscraft.uep.account.client.constant.ConnectorType;
import com.cyberscraft.uep.account.client.domain.*;
import com.cyberscraft.uep.account.client.domain.account.FeiShuAccount;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccessTokenInvalidException;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountErrorType;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountException;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountNotExistException;
import com.cyberscraft.uep.account.client.provider.IExternalConnectHandler;
import com.cyberscraft.uep.account.client.provider.feishu.constant.FeiShuOrgAttr;
import com.cyberscraft.uep.account.client.provider.feishu.constant.FeiShuUserAttr;
import com.cyberscraft.uep.account.client.provider.feishu.domain.FeiShuConfig;
import com.cyberscraft.uep.account.client.service.IAuthenticateService;
import com.cyberscraft.uep.account.client.util.AccountUtil;
import com.cyberscraft.uep.common.bean.RestApiResponse;
import com.cyberscraft.uep.common.domain.DataTypeEnum;
import com.cyberscraft.uep.common.domain.ParamSchema;
import com.cyberscraft.uep.common.domain.auth.AuthorizeCredential;
import com.cyberscraft.uep.common.enums.SysCodeEnum;
import com.cyberscraft.uep.common.exception.ApiConnectException;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.common.util.RestAPIUtil;
import com.cyberscraft.uep.common.util.WebUrlUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @Author：xhquan
 * @Project：uep-services
 * @since：2023/2/27 17:18
 */
@Service
public class CommonFeiShuExternalConnectHandler extends AbstractFeiShuHandler implements IExternalConnectHandler {

    private final static Logger LOG = LoggerFactory.getLogger(CommonFeiShuExternalConnectHandler.class);

    /***
     * 根据部门ID获取部门列表，仅跟部门
     */
    protected String getGetDepartmentChild = "/open-apis/contact/v3/departments/%s/children";

    /***
     * 获取部门详情
     */
    private String getDepartmentInfo = "/open-apis/contact/v3/departments/%s";

    /***
     * 获取部门下的用户列表
     */
    private String getDepartmentUserList = "/open-apis/contact/v3/users/find_by_department";

    /***
     * 根据userid获取用户对应的详情
     */
    private String getUserInfo = "/open-apis/contact/v3/users/%s";

    /**
     * 通过手机号或者邮箱获取user_id
     */
    private String getUserIdByMobileOrMail = "/open-apis/contact/v3/users/batch_get_id";

    /**
     * 创建部门
     */
    private String createDepartmentPath = "/open-apis/contact/v3/departments";

    /**
     * 删除部门
     */
    private String deleteDepartmentPath = "/open-apis/contact/v3/departments/%s";

    /**
     * 修改部门
     */
    private String modifyDepartmentPath = "/open-apis/contact/v3/departments/%s";

    /**
     * 创建用户
     */
    private String createUserPath = "/open-apis/contact/v3/users";

    /**
     * 删除用户
     */
    private String deleteUserPath = "/open-apis/contact/v3/users/%s";

    /**
     * 修改用户
     */
    private String modifyUserPath = "/open-apis/contact/v3/users/%s";

    /**
     * 飞书请求需要的key
     */
    private static final String HAS_MORE = "has_more";
    private static final String PAGE_TOKEN = "page_token";
    private static final String USER_ID_TYPE = "user_id_type";
    private static final String CLIENT_TOKEN = "client_token";
    private static final String DEPARTMENT_ID_TYPE = "department_id_type";
    private static final String FETCH_CHILD = "fetch_child";
    private static final String PAGE_SIZE = "page_size";
    private static final String ITEMS = "items";
    private static final String DATA = "data";
    private static final String DEPARTMENT_ID = "department_id";
    private static final String DEPARTMENT = "department";
    private static final String USER = "user";
    private static final String USER_ID = "user_id";
    private static final Integer PAGE = 50;

    @Autowired
    private IAuthenticateService authenticateService;

    private final ParamSchema orgBasicAttrSchema;

    private final ParamSchema userBasicAttrSchema;

    public CommonFeiShuExternalConnectHandler() {
        orgBasicAttrSchema = new ParamSchema(AccountConstant.APPORG_PROFILE_ROOTNAME, DataTypeEnum.OBJECT, "部门profile")
                .addSubParam(FeiShuOrgAttr.name.ps())
                .addSubParam(FeiShuOrgAttr.department_id.ps())
                .addSubParam(FeiShuOrgAttr.parent_department_id.ps())
                .addSubParam(FeiShuOrgAttr.open_department_id.ps())
                .addSubParam(FeiShuOrgAttr.chat_id.ps())
                .addSubParam(FeiShuOrgAttr.member_count.ps())
                .addSubParam(FeiShuOrgAttr.i18n_name.ps())
                .addSubParam(FeiShuOrgAttr.leader_user_id.ps())
                .addSubParam(FeiShuOrgAttr.order.ps())
                .addSubParam(FeiShuOrgAttr.unit_ids.ps())
                .addSubParam(FeiShuOrgAttr.create_group_chat.ps())
                .addSubParam(FeiShuOrgAttr.leaders.ps())
                .addSubParam(FeiShuOrgAttr.group_chat_employee_types.ps())
                .addSubParam(FeiShuOrgAttr.department_hrbps.ps())
                .addSubParam(FeiShuOrgAttr.primary_member_count.ps());

        userBasicAttrSchema = new ParamSchema(AccountConstant.APPUSER_PROFILE_ROOTNAME, DataTypeEnum.OBJECT, "用户profile")
                .addSubParam(FeiShuUserAttr.user_id.ps())
                .addSubParam(FeiShuUserAttr.union_id.ps())
                .addSubParam(FeiShuUserAttr.name.ps())
                .addSubParam(FeiShuUserAttr.mobile.ps())
                .addSubParam(FeiShuUserAttr.email.ps())
                .addSubParam(FeiShuUserAttr.nickname.ps())
                .addSubParam(FeiShuUserAttr.avatar.ps())
                .addSubParam(FeiShuUserAttr.enterprise_email.ps())
                .addSubParam(FeiShuUserAttr.gender.ps())
                .addSubParam(FeiShuUserAttr.job_title.ps())
                .addSubParam(FeiShuUserAttr.status.ps())
                .addSubParam(FeiShuUserAttr.employee_no.ps())
                .addSubParam(FeiShuUserAttr.department_ids.ps())
                .addSubParam(FeiShuUserAttr.employee_type.ps())
                .addSubParam(FeiShuUserAttr.is_tenant_manager.ps())
                .addSubParam(FeiShuUserAttr.orders.ps())
                .addSubParam(FeiShuUserAttr.en_name.ps())
                .addSubParam(FeiShuUserAttr.mobile_visible.ps())
                .addSubParam(FeiShuUserAttr.avatar_key.ps())
                .addSubParam(FeiShuUserAttr.leader_user_id.ps())
                .addSubParam(FeiShuUserAttr.assign_info.ps())
                .addSubParam(FeiShuUserAttr.city.ps())
                .addSubParam(FeiShuUserAttr.country.ps())
                .addSubParam(FeiShuUserAttr.work_station.ps())
                .addSubParam(FeiShuUserAttr.join_time.ps())
                .addSubParam(FeiShuUserAttr.geo.ps())
                .addSubParam(FeiShuUserAttr.job_level_id.ps())
                .addSubParam(FeiShuUserAttr.job_family_id.ps())
                .addSubParam(FeiShuUserAttr.subscription_ids.ps())
                .addSubParam(FeiShuUserAttr.department_path.ps())
                .addSubParam(FeiShuUserAttr.dotted_line_leader_user_ids.ps())
                .addSubParam(FeiShuUserAttr.custom_attrs.ps());
    }

    @Override
    public boolean isSupported(ConnectorType connectorType) {
        return ConnectorType.FEISHU == connectorType;
    }

    @Override
    public AuthorizeCredential getAuthCredential(Connector connector, Boolean refresh) {
        try {
            FeiShuAccount account = JsonUtil.str2Obj(connector.getConfig(), FeiShuAccount.class);
            AuthorizeCredential credential = authenticateService.auth(ConnectorType.FEISHU.name(), account, account.getAppKey(), refresh);
            return credential;
        } catch (ApiConnectException ae) {
            throw new ThirdPartyAccountException(ae.getMultiLanguageExceptionCodes());
        } catch (Exception e) {
            throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), e.getMessage());
        }
    }

    @Override
    public ParamSchema getExternalOrgBasicAttrSchema(Connector connector) {
        return orgBasicAttrSchema;
    }

    @Override
    public ParamSchema getExternalUserBasicAttrSchema(Connector connector) {
        return userBasicAttrSchema;
    }

    @Override
    public ConnectorOrgProfile getExternalOrgFullProfile(Connector connector) {
        ParamSchema orgAllProfile = buildExternalOrgFullSchema(connector);

        ConnectorOrgProfile connectorOrgProfile = new ConnectorOrgProfile();
        connectorOrgProfile.setIdName(FeiShuOrgAttr.department_id.getAttrName());
        connectorOrgProfile.setParentIdName(FeiShuOrgAttr.parent_department_id.getAttrName());
        connectorOrgProfile.setNameName(FeiShuOrgAttr.name.getAttrName());
        connectorOrgProfile.setAttrSchema(orgAllProfile);

        return connectorOrgProfile;
    }

    @Override
    public ConnectorUserProfile getExternalUserFullProfile(Connector connector) {
        ParamSchema userAllProfile = buildExternalUserFullSchema(connector);

        ConnectorUserProfile connectorUserProfile = new ConnectorUserProfile();
        connectorUserProfile.setIdName(FeiShuUserAttr.user_id.getAttrName());
        connectorUserProfile.setUnionIdName(FeiShuUserAttr.union_id.getAttrName());
        connectorUserProfile.setMobileName(FeiShuUserAttr.mobile.getAttrName());
        connectorUserProfile.setAttrSchema(userAllProfile);

        return connectorUserProfile;
    }

    @Override
    public ConnectorOrg<String, Object> getRootExternalOrg(Connector connector) throws ThirdPartyAccountException {
        String rootOrgId = connector.getRootCode();
        ConnectorOrg rootOrg = getExternalOrgById(rootOrgId, connector);
        //飞书获取根部门的时候没有部门名称，所以使用连接器名称设置跟部门名称
        rootOrg.put(FeiShuOrgAttr.name.getAttrName(), connector.getName());
        return rootOrg;
    }

    @Override
    public List<ConnectorOrg<String, Object>> getSubExternalOrgs(String parentOrgId, Connector connector) throws ThirdPartyAccountException {
        return null;
    }

    @Override
    public List<ConnectorOrg<String, Object>> getExternalOrgByIds(List<String> externalOrgIds, Connector connector) throws ThirdPartyAccountException {
        return null;
    }

    @Override
    public List<ConnectorUser<String, Object>> getExternalUsers(List<String> externalUserIds, Connector connector) throws ThirdPartyAccountException {
        return null;
    }

    @Override
    public Boolean listExternalUsersByLimit(Connector connector) {
        return false;
    }

    @Override
    public List<ConnectorOrg<String, Object>> getAllSubExternalOrgs(ConnectorOrg parentExternalOrg, Connector connector) throws ThirdPartyAccountException {
        LOG.info("start get fei shu department info ");
        if (connector == null) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.CONNECTOR_INVALID);
        }
        String tenantId = connector.getTenantId();
        String rootOrgId = (String) parentExternalOrg.getIdValue();
        String api = String.format(getGetDepartmentChild, rootOrgId);
        Map<String, Object> params = new HashMap<>();
        params.put(FETCH_CHILD, true);
        List<Map<String, Object>> allExternalOrgs = getPageData(connector, params, tenantId, api);

        List<ConnectorOrg<String, Object>> ret = new ArrayList<>();
        ret.addAll(AccountUtil.to(allExternalOrgs, getExternalOrgFullProfile(connector)));
        LOG.info("get fei shu department size is {} , department id is {}", allExternalOrgs.size(), rootOrgId);
        return ret;
    }

    @Override
    public boolean isSortedByOrgLevel() {
        return false;
    }

    @Override
    public ConnectorOrg getExternalOrgById(String externalOrgId, Connector connector) throws ThirdPartyAccountException {
        LOG.info("get fei shu org info by id , id is {}", externalOrgId);
        String apiPath = String.format(getDepartmentInfo, externalOrgId);
        Map<String, Object> data = getFeiShuOrgOrUserById(apiPath, connector);
        Map<String, Object> result = (Map<String, Object>) data.get(DEPARTMENT);
        LOG.info("get fei shu org info by id , info is {}", result.toString());
        return AccountUtil.to(result, getExternalOrgFullProfile(connector));
    }

    @Override
    public List<ConnectorUser<String, Object>> getExternalUsersByOrg(ConnectorOrg externalOrg, Connector connector) throws ThirdPartyAccountException {
        LOG.info("start get department user");
        String tenantId = connector.getTenantId();
        String departmentId = (String) externalOrg.get(DEPARTMENT_ID);
        Map<String, Object> params = new HashMap<>();
        params.put(DEPARTMENT_ID, departmentId);
        List<Map<String, Object>> externalUserByOrg = getPageData(connector, params, tenantId, getDepartmentUserList);
        LOG.info("get department user ,dept Id is {} user size is {}", departmentId, externalUserByOrg.size());
        return AccountUtil.to(externalUserByOrg, getExternalUserFullProfile(connector));
    }


    @Override
    public Boolean limitListExternalUsers(int page, int pageSize, Connector connector, List<ConnectorUser<String, Object>> output) throws ThirdPartyAccountException {
        return false;
    }

    @Override
    public ConnectorUser getExternalUserById(String externalAccountId, Connector connector) throws ThirdPartyAccountException {
        LOG.info("get fei shu user info by id , id is {}", externalAccountId);
        String apiPath = String.format(getUserInfo, externalAccountId);
        Map<String, Object> data = getFeiShuOrgOrUserById(apiPath, connector);
        Map<String, Object> result = (Map<String, Object>) data.get(USER);
        LOG.info("get fei shu user info by id , info is {}", result.toString());
        return AccountUtil.to(result, getExternalUserFullProfile(connector));
    }

    private Map<String, Object> getFeiShuOrgOrUserById(String apiPath, Connector connector) {
        if (StringUtils.isBlank(apiPath)) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.CONNECTOR_INVALID);
        }
        FeiShuConfig feiShuConfig = JsonUtil.str2Obj(connector.getConfig(), FeiShuConfig.class);
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put(USER_ID_TYPE, feiShuConfig.getUserIdType());
        queryParams.put(DEPARTMENT_ID_TYPE, feiShuConfig.getDepartmentIdType());
        return sendGetRequest(feiShuConfig, connector, queryParams, apiPath);
    }

    private Map<String, Object> sendGetRequest(FeiShuConfig cfg, Connector connector, Map<String, Object> params, String api) {
        Map<String, String> header = new HashMap<>();
        Map<String, Object> response = null;
        String url = WebUrlUtil.getWebAccessServerUrl(cfg.getApiBaseUrl(), api, params);

        AuthorizeCredential authCredential = getAuthCredential(connector, false);
        try {
            header.putAll(authCredential.getHead());
            RestApiResponse apiResponse = RestAPIUtil.modifyEntityForString(url, HttpMethod.GET.name(), null, header, params, null);
            checkRestResponse(apiResponse, api);
            response = JsonUtil.str2Map(apiResponse.getBody().toString());
        } catch (ThirdPartyAccessTokenInvalidException e) {
            authCredential = getAuthCredential(connector, true);
            header.putAll(authCredential.getHead());
            RestApiResponse apiResponse = RestAPIUtil.modifyEntityForString(url, HttpMethod.GET.name(), null, header, params, null);
            checkRestResponse(apiResponse, api);
            response = JsonUtil.str2Map(apiResponse.getBody().toString());
        } catch (Exception e) {
            throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), e.getMessage());
        }
        Map<String, Object> result = (Map<String, Object>) response.get(DATA);
        return result;
    }

    private List<Map<String, Object>> getPageData(Connector connector, Map<String, Object> params, String tenantId, String api) {
        List<Map<String, Object>> result = new ArrayList<>();
        Boolean hasMore = false;
        String pageToken = null;
        FeiShuConfig cfg = JsonUtil.str2Obj(connector.getConfig(), FeiShuConfig.class);
        do {
            buildQueryParams(params, cfg, pageToken);
            Map<String, Object> response = sendGetRequest(cfg, connector, params, api);
            if (response != null && response.get(ITEMS) != null) {
                result.addAll((List<Map<String, Object>>) response.get(ITEMS));
                hasMore = (Boolean) response.get(HAS_MORE);
                if (hasMore) {
                    pageToken = response.get(PAGE_TOKEN).toString();
                }
            }
        } while (hasMore);
        return result;
    }

    private void buildQueryParams(Map<String, Object> queryParams, FeiShuConfig feiShuConfig, String pageToken) {
        queryParams.put(USER_ID_TYPE, feiShuConfig.getUserIdType());
        queryParams.put(DEPARTMENT_ID_TYPE, feiShuConfig.getDepartmentIdType());
        queryParams.put(PAGE_SIZE, PAGE);
        if (StringUtils.isNotBlank(pageToken)) {
            queryParams.put(PAGE_TOKEN, pageToken);
        }
    }

    private List<String> getUserId(FeiShuConfig feiShuConfig, String account, Connector connector) {

        AuthorizeCredential authCredential = getAuthCredential(connector, false);

        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json; charset=utf-8");
        header.putAll(authCredential.getHead());
        Map<String, Object> params = new HashMap<>();
        params.put(USER_ID_TYPE, feiShuConfig.getUserIdType());
        List<String> accounts = new ArrayList<>();
        accounts.add(account);
        Map<String, Object> body = new HashMap<>();
        body.put("emails", accounts);
        body.put("mobiles", accounts);
        Map<String, Object> response = null;
        String url = WebUrlUtil.getWebAccessServerUrl(feiShuConfig.getApiBaseUrl(), getUserIdByMobileOrMail, params);
        try {
            RestApiResponse apiResponse = RestAPIUtil.modifyEntityForString(url, HttpMethod.POST.name(), body, header, null, null);
            checkRestResponse(apiResponse, getUserIdByMobileOrMail);
            response = JsonUtil.str2Map(apiResponse.getBody().toString());
        } catch (ThirdPartyAccessTokenInvalidException e) {
            authCredential = getAuthCredential(connector, true);
            header.putAll(authCredential.getHead());
            RestApiResponse apiResponse = RestAPIUtil.modifyEntityForString(url, HttpMethod.POST.name(), body, header, null, null);
            checkRestResponse(apiResponse, getUserIdByMobileOrMail);
            response = JsonUtil.str2Map(apiResponse.getBody().toString());
        } catch (Exception e) {
            throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), e.getMessage());
        }
        Map<String, Object> data = (Map<String, Object>) response.get(DATA);
        List<Map<String, Object>> userList = (List<Map<String, Object>>) data.get("user_list");
        List<String> result = new ArrayList<>();
        for (Map<String, Object> map : userList) {
            Object userId = map.get("user_id");
            if (userId != null) {
                result.add(userId.toString());
            }

        }
        return result;
    }


    @Override
    public int login(Connector connector, String username, String password) {
        return 0;
    }

    @Override
    public ConnectorUser test(Connector connector) {
        if (connector == null) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.CONNECTOR_INVALID);
        }
        String testAccount = connector.getTestAccount();
        LOG.info("test connector testAccount is {}", testAccount);
        if (testAccount == null) {
            return null;
        }
        ConnectorUser externalUserById = null;
        try {
            externalUserById = getExternalUserById(testAccount, connector);
        } catch (ThirdPartyAccountException e) {
            LOG.info("test connector error {}", e.getMessage());
            FeiShuConfig feiShuConfig = JsonUtil.str2Obj(connector.getConfig(), FeiShuConfig.class);
            List<String> userIds = getUserId(feiShuConfig, testAccount, connector);
            for (String userId : userIds) {
                LOG.info("test connector testAccount is {}", userId);
                externalUserById = getExternalUserById(userId, connector);
                if (externalUserById != null) {
                    break;
                }
            }
        }
        return externalUserById;
    }


    @Override
    public void closeConnection(Connector connector) {
    }


    @Override
    public String createExternalOrg(ConnectorOrg externalOrg, Connector connector) throws ThirdPartyAccountException {
        // 判断父组代码是否存在
        Object parentIdValue = externalOrg.getParentIdValue();
        if (StringUtils.isBlank(parentIdValue == null ? null : parentIdValue.toString())) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.PARENT_GROUP_CODE_INVALID);
        }

        // 请求头
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json; charset=utf-8");
        // 查询参数
        FeiShuConfig feiShuConfig = JsonUtil.str2Obj(connector.getConfig(), FeiShuConfig.class);
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put(USER_ID_TYPE, feiShuConfig.getUserIdType());
        queryParams.put(DEPARTMENT_ID_TYPE, feiShuConfig.getDepartmentIdType());
        // CLIENT_TOKEN 用于幂等判断是否为同一请求，避免重复创建。字符串类型，自行生成。
        queryParams.put(CLIENT_TOKEN, UUID.randomUUID().toString());

        // 发起请求
        Map<String, Object> response = sendModifyRequest("createExternalOrg", HttpMethod.POST.name(), feiShuConfig.getApiBaseUrl(), createDepartmentPath, queryParams, header, externalOrg, connector);
        // 处理结果，返回部门id
        Map<String, Object> data = extractDataFromResponse(response, DATA);
        Map<String, Object> department = extractDataFromResponse(data, DEPARTMENT);
        Object departmentIdObj = department.get(DEPARTMENT_ID);
        if (departmentIdObj != null) {
            return departmentIdObj.toString();
        }
        return null;
    }

    /**
     * 发送一个可以修改请求方式的请求，支持POST、PUT、PATCH、DELETE
     *
     * @param methodName    本次调用起于那个服务的那个方法，方便拆分日志
     * @param requestMethod 请求方式
     * @param apiBaseUrl    接口的基础url
     * @param path          接口的路径
     * @param queryParams   查询参数
     * @param header        请求头
     * @param reqBody       请求体
     * @param connector     飞书的连接器信息
     * @return 响应体中的data
     */
    private Map<String, Object> sendModifyRequest(String methodName, String requestMethod, String apiBaseUrl, String path, Map<String, Object> queryParams, Map<String, String> header, Map<String, Object> reqBody, Connector connector) {
        Map<String, Object> response;
        String url = WebUrlUtil.getWebAccessServerUrl(apiBaseUrl, path, queryParams);
        // 获取鉴权
        AuthorizeCredential authCredential = getAuthCredential(connector, false);

        try {
            header.putAll(authCredential.getHead());
            response = sendRequestAndCheckResponse(url, requestMethod, reqBody, header, path);
        } catch (ThirdPartyAccessTokenInvalidException e) {
            // 因第三方鉴权导致的异常，刷新鉴权后再次请求
            authCredential = getAuthCredential(connector, true);
            header.putAll(authCredential.getHead());
            response = sendRequestAndCheckResponse(url, requestMethod, reqBody, header, path);
        } catch (ThirdPartyAccountNotExistException notExist) {
            throw notExist;
        } catch (Exception e) {
            LOG.info("{} exception, info  is : {}", methodName, JsonUtil.obj2Str(reqBody));
            LOG.error(e.getMessage());
            throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), e.getMessage());
        }
        return response;
    }

    /**
     * 发送请求并且校验结果
     *
     * @param url           请求地址
     * @param requestMethod 请求方式
     * @param reqBody       请求体
     * @param header        请求头
     * @param api           接口地址
     * @return 请求结果
     */
    private Map<String, Object> sendRequestAndCheckResponse(String url, String requestMethod, Map<String, Object> reqBody, Map<String, String> header, String api) {
        // 该请求方法有对queryParams和uriVariables，进行非空判断，为了减少内存损耗传null而不是new hashMap()
        RestApiResponse apiResponse = RestAPIUtil.modifyEntityForString(url, requestMethod, reqBody, header, null, null);
        checkRestResponse(apiResponse, api);
        String respStr = apiResponse.getBody().toString();
        // 返回的是字符串，强转map会报类型转换错误，必须转string再转map
        return JsonUtil.str2Map(respStr);
    }

    /**
     * 处理响应中的业务数据并将其转为Map
     *
     * @param response 请求响应
     * @param key      业务数据map的key
     * @return 转换好的map
     */
    private Map<String, Object> extractDataFromResponse(Map<String, Object> response, String key) {
        Object dataObj = response.get(key);
        if (!(dataObj instanceof Map)) {
            throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), key + " is empty or type err in the response");
        }
        return (Map<String, Object>) dataObj;
    }

    @Override
    public String modifyExternalOrg(String externalOrgId, ConnectorOrg externalOrg, Connector connector) throws ThirdPartyAccountException {
        externalOrg.setIdValue(externalOrgId);
        // 请求头
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json; charset=utf-8");
        // 查询参数
        FeiShuConfig feiShuConfig = JsonUtil.str2Obj(connector.getConfig(), FeiShuConfig.class);
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put(USER_ID_TYPE, feiShuConfig.getUserIdType());
        queryParams.put(DEPARTMENT_ID_TYPE, feiShuConfig.getDepartmentIdType());

        // 拼接path
        String path = String.format(modifyDepartmentPath, externalOrgId);
        // 发起请求
        sendModifyRequest("modifyExternalOrg", HttpMethod.PATCH.name(), feiShuConfig.getApiBaseUrl(), path, queryParams, header, externalOrg, connector);
        return externalOrgId;
    }

    @Override
    public void deleteExternalOrg(String externalOrgId, Connector connector) throws ThirdPartyAccountException {
        // 请求头, 删除部门请求只需Authorization, 在方法sendModifyRequest()中拼接
        Map<String, String> header = new HashMap<>();
        // 查询参数
        FeiShuConfig feiShuConfig = JsonUtil.str2Obj(connector.getConfig(), FeiShuConfig.class);
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put(DEPARTMENT_ID_TYPE, feiShuConfig.getDepartmentIdType());
        // 请求体
        Map<String, Object> reqBody = new HashMap<>();
        // 拼接path
        String path = String.format(deleteDepartmentPath, externalOrgId);
        // 发起请求
        sendModifyRequest("deleteExternalOrg", HttpMethod.DELETE.name(), feiShuConfig.getApiBaseUrl(), path, queryParams, header, reqBody, connector);
    }

    @Override
    public void deleteExternalOrgs(List<String> externalOrgIds, Connector connector) throws ThirdPartyAccountException {
        for (String externalOrgId : externalOrgIds) {
            deleteExternalOrg(externalOrgId, connector);
        }
    }

    @Override
    public String createExternalUser(ConnectorUser externalUser, Connector connector) throws ThirdPartyAccountException {
        // 请求头
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json; charset=utf-8");
        // 查询参数
        FeiShuConfig feiShuConfig = JsonUtil.str2Obj(connector.getConfig(), FeiShuConfig.class);
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put(USER_ID_TYPE, feiShuConfig.getUserIdType());
        queryParams.put(DEPARTMENT_ID_TYPE, feiShuConfig.getDepartmentIdType());
        // CLIENT_TOKEN 用于幂等判断是否为同一请求，避免重复创建。字符串类型，自行生成。
        queryParams.put(CLIENT_TOKEN, UUID.randomUUID().toString());
        // 员工类型不能为空，为空默认1（1：正式员工 2：实习生 3：外包 4：劳务 5：顾问）
        Object employeeType = externalUser.getValue(FeiShuUserAttr.employee_type);
        if (ObjectUtils.isEmpty(employeeType)) {
            externalUser.put(FeiShuUserAttr.employee_type.getAttrName(), 1);
        }
        // 发起请求
        Map<String, Object> response = sendModifyRequest("createExternalUser", HttpMethod.POST.name(), feiShuConfig.getApiBaseUrl(), createUserPath, queryParams, header, externalUser, connector);
        // 处理结果，返回用户id
        Map<String, Object> data = extractDataFromResponse(response, DATA);
        Map<String, Object> user = extractDataFromResponse(data, USER);
        Object userIdObj = user.get(USER_ID);
        if (userIdObj != null) {
            return userIdObj.toString();
        }
        return null;
    }

    @Override
    public String modifyExternalUser(String externalUserId, ConnectorUser externalUser, Connector connector) throws ThirdPartyAccountException {
        externalUser.setIdValue(externalUserId);
        // 请求头
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json; charset=utf-8");
        // 查询参数
        FeiShuConfig feiShuConfig = JsonUtil.str2Obj(connector.getConfig(), FeiShuConfig.class);
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put(USER_ID_TYPE, feiShuConfig.getUserIdType());
        queryParams.put(DEPARTMENT_ID_TYPE, feiShuConfig.getDepartmentIdType());

        // 拼接path
        String path = String.format(modifyUserPath, externalUserId);
        // 发起请求
        sendModifyRequest("modifyExternalUser", HttpMethod.PATCH.name(), feiShuConfig.getApiBaseUrl(), path, queryParams, header, externalUser, connector);
        return externalUserId;
    }

    @Override
    public void deleteExternalUser(String externalUserId, Connector connector) throws ThirdPartyAccountException {
        // 请求头
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json; charset=utf-8");
        // 查询参数
        FeiShuConfig feiShuConfig = JsonUtil.str2Obj(connector.getConfig(), FeiShuConfig.class);
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put(USER_ID_TYPE, feiShuConfig.getUserIdType());
        // 请求体
        Map<String, Object> reqBody = new HashMap<>();
        // 拼接path
        String path = String.format(deleteUserPath, externalUserId);
        // 发起请求
        sendModifyRequest("deleteExternalUser", HttpMethod.DELETE.name(), feiShuConfig.getApiBaseUrl(), path, queryParams, header, reqBody, connector);
    }

    @Override
    public void deleteExternalUsers(List<String> externalUserIds, Connector connector) throws ThirdPartyAccountException {
        for (String externalUserId : externalUserIds) {
            deleteExternalUser(externalUserId, connector);
        }
    }
}
