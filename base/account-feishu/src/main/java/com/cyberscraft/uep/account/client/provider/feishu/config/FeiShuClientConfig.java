package com.cyberscraft.uep.account.client.provider.feishu.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * @Author：xhquan
 * @Project：uep-services
 * @since：2023/2/27 16:57
 */
@Component
@RefreshScope
@ConfigurationProperties(prefix = "feishu")
public class FeiShuClientConfig {

    /**
     * 飞书服务器地址，这个是全局通用的地址
     */
    private String serverUrl = "https://open.feishu.cn";

    /**
     * 根组ID,飞书默认所有的根组ID都是0
     */
    private String rootGroupCode = "0";

    /**
     * 根组名称
     */
    private String rootGroupName = "根部门";

    private String appKey;

    private String appSecret;

    /**
     * 飞书通讯录接口访问频控限制：1000 次/分钟、50 次/秒
     */
    private Integer requestInterval = 50;

    public String getServerUrl() {
        return serverUrl;
    }

    public void setServerUrl(String serverUrl) {
        this.serverUrl = serverUrl;
    }

    public String getRootGroupCode() {
        return rootGroupCode;
    }

    public void setRootGroupCode(String rootGroupCode) {
        this.rootGroupCode = rootGroupCode;
    }

    public String getRootGroupName() {
        return rootGroupName;
    }

    public void setRootGroupName(String rootGroupName) {
        this.rootGroupName = rootGroupName;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public Integer getRequestInterval() {
        return requestInterval;
    }

    public void setRequestInterval(Integer requestInterval) {
        this.requestInterval = requestInterval;
    }
}
