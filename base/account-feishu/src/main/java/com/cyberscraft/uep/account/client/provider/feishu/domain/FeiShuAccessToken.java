package com.cyberscraft.uep.account.client.provider.feishu.domain;

/**
 * @Author：xhquan
 * @Project：uep-services
 * @since：2023/2/27 19:27
 */
public class FeiShuAccessToken {

    /**
     * 飞书 token 的类型 tenant 、app 等
     */
    private String tokenType;

    /**
     * 飞书 token
     */
    private String accessToken;

    /**
     * token过期时间
     */
    private Integer expire;

    public String getTokenType() {
        return tokenType;
    }

    public void setTokenType(String tokenType) {
        this.tokenType = tokenType;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public Integer getExpire() {
        return expire;
    }

    public void setExpire(Integer expire) {
        this.expire = expire;
    }
}
