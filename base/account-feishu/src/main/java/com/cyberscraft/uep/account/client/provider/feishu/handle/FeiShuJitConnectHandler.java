package com.cyberscraft.uep.account.client.provider.feishu.handle;

import com.cyberscraft.uep.account.client.constant.AccountConstant;
import com.cyberscraft.uep.account.client.constant.JitConnectType;
import com.cyberscraft.uep.account.client.domain.ConnectorUserProfile;
import com.cyberscraft.uep.account.client.provider.IJitConnectHandler;
import com.cyberscraft.uep.account.client.provider.feishu.constant.FeiShuUserAttr;
import com.cyberscraft.uep.common.domain.DataTypeEnum;
import com.cyberscraft.uep.common.domain.ParamSchema;
import org.springframework.stereotype.Service;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/27 2:52 下午
 */
@Service
public class FeiShuJitConnectHandler implements IJitConnectHandler {

    private final ParamSchema userBasicAttrSchema;

    public FeiShuJitConnectHandler() {
        userBasicAttrSchema = new ParamSchema(AccountConstant.IDPUSER_PROFILE_ROOTNAME, DataTypeEnum.OBJECT, "用户profile")
                .addSubParam(FeiShuUserAttr.user_id.ps())
                .addSubParam(FeiShuUserAttr.union_id.ps())
                .addSubParam(FeiShuUserAttr.name.ps())
                .addSubParam(FeiShuUserAttr.mobile.ps())
                .addSubParam(FeiShuUserAttr.email.ps())
                .addSubParam(FeiShuUserAttr.employee_no.ps());
    }

    @Override
    public boolean isSupported(JitConnectType jitConnectType) {
        return JitConnectType.FEISHU == jitConnectType;
    }

    @Override
    public ParamSchema getJitUserBasicAttrSchema(JitConnectType jitConnectType) {
        return userBasicAttrSchema;
    }

    @Override
    public ConnectorUserProfile getJitUserFullProfile(ParamSchema jitExtensionSchema, JitConnectType jitConnectType) {
        ParamSchema userAllProfile = buildJitUserFullSchema(jitExtensionSchema, jitConnectType);

        ConnectorUserProfile connectorUserProfile = new ConnectorUserProfile();
        connectorUserProfile.setIdName(FeiShuUserAttr.user_id.getAttrName());
        connectorUserProfile.setUnionIdName(FeiShuUserAttr.union_id.getAttrName());
        connectorUserProfile.setMobileName(FeiShuUserAttr.mobile.getAttrName());
        connectorUserProfile.setAttrSchema(userAllProfile);

        return connectorUserProfile;
    }
}
