package com.cyberscraft.uep.account.client.provider.feishu.service;

import com.cyberscraft.uep.account.client.provider.feishu.domain.FeiShuConfig;
import com.cyberscraft.uep.common.domain.message.MessageBizType;
import com.cyberscraft.uep.common.domain.message.MessageSendProgress;
import com.cyberscraft.uep.common.domain.message.MessageSendResult;
import com.cyberscraft.uep.common.domain.message.ThirdMessageEntity;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/12/30 19:57
 */
public interface IFeishuMessageService {

    /***
     * 发送消息到飞书用户,返回飞书消息ID，用于进度查询
     * @param message
     * @param tenantId
     * @return
     */
    MessageSendResult send(ThirdMessageEntity message, FeiShuConfig config, String tenantId);

    /***
     * 查询钉钉消息发送结果
     * @param taskId
     * @param config
     * @param tenantId
     * @return
     */
    MessageSendProgress getSendProgress(String taskId, MessageBizType bizType, FeiShuConfig config, String tenantId);

    /***
     * 查询飞书消息发送结果
     * @param taskId
     * @param config
     * @param tenantId
     * @return
     */
    MessageSendResult getSendResult(String taskId, MessageBizType bizType, FeiShuConfig config, String tenantId);
}
