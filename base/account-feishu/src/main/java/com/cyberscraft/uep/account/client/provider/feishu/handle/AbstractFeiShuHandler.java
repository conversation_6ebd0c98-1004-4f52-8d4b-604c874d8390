package com.cyberscraft.uep.account.client.provider.feishu.handle;

import com.cyberscraft.uep.account.client.provider.feishu.domain.FeiShuConfig;
import com.cyberscraft.uep.account.client.provider.feishu.service.AbstractFeiShuRequest;
import com.cyberscraft.uep.account.client.provider.feishu.service.IFeiShuAccessTokenService;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Author：xhquan
 * @Project：uep-services
 * @since：2023/2/27 16:54
 */
public abstract class AbstractFeiShuHandler extends AbstractFeiShuRequest {

    @Autowired
    protected IFeiShuAccessTokenService feiShuAccessTokenService;

    /**
     * 获取访问Token
     *
     * @param tenantId
     * @param cfg
     * @return
     */
    protected String getAccessToken(String tenantId, FeiShuConfig cfg) {
        return feiShuAccessTokenService.getAccessToken(tenantId, cfg);
    }

    /**
     * 获取访问Token
     *
     * @param tenantId
     * @param cfg
     * @return
     */
    protected String reGetAccessToken(String tenantId, FeiShuConfig cfg) {
        try {
            return feiShuAccessTokenService.reGetAccessToken(tenantId, cfg);
        } catch (Exception e) {
            LOG.warn("re get access token from fei shu has error :{}", e.getMessage());
        }
        return null;
    }

    public IFeiShuAccessTokenService getFeiShuAccessTokenService() {
        return feiShuAccessTokenService;
    }

    public void setFeiShuAccessTokenService(IFeiShuAccessTokenService feiShuAccessTokenService) {
        this.feiShuAccessTokenService = feiShuAccessTokenService;
    }
}
