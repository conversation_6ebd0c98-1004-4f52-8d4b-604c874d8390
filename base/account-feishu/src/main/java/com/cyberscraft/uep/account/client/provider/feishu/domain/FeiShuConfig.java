package com.cyberscraft.uep.account.client.provider.feishu.domain;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import java.io.Serializable;

/**
 * @Author：xhquan
 * @Project：uep-services
 * @since：2023/2/27 19:13
 */
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class FeiShuConfig implements Serializable {

    private static final long serialVersionUID = 3557920914407901042L;

    /**
     * 应用的appKey
     */
    private String appKey;

    /**
     * 应用的appSecret
     */
    private String appSecret;

    /**
     * 飞书接口的前缀地址
     */
    private String apiBaseUrl = "https://open.feishu.cn";

    /**
     * 用户ID类型
     * open_id (默认值) 标识一个用户在某个应用中的身份。同一个用户在不同应用中的 Open ID 不同
     * union_id 标识一个用户在某个应用开发商下的身份。同一用户在同一开发商下的应用中的 Union ID 是相同的，在不同开发商下的应用中的 Union ID 是不同的。通过 Union ID，应用开发商可以把同个用户在多个应用中的身份关联起来
     * user_id 标识一个用户在某个租户内的身份。同一个用户在租户 A 和租户 B 内的 User ID 是不同的。在同一个租户内，一个用户的 User ID 在所有应用（包括商店应用）中都保持一致。User ID 主要用于在不同的应用间打通用户数据
     * user_id 字段需要特定权限
     */
    private String userIdType = "user_id";

    /**
     * 部门ID类型
     * department_id 用来标识租户内一个唯一的部门
     * open_department_id（默认值） 用来在具体某个应用中标识一个部门，同一个部门 在不同应用中的 open_department_id 不相同
     */
    private String departmentIdType = "department_id";
    

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public String getApiBaseUrl() {
        return apiBaseUrl;
    }

    public void setApiBaseUrl(String apiBaseUrl) {
        this.apiBaseUrl = apiBaseUrl;
    }

    public String getUserIdType() {
        return userIdType;
    }

    public void setUserIdType(String userIdType) {
        this.userIdType = userIdType;
    }

    public String getDepartmentIdType() {
        return departmentIdType;
    }

    public void setDepartmentIdType(String departmentIdType) {
        this.departmentIdType = departmentIdType;
    }


}
