package com.cyberscraft.uep.account.client.provider.feishu;

import com.cyberscraft.uep.account.client.provider.IExternalConnectHandler;
import com.cyberscraft.uep.account.client.provider.IThirdPartyCallBackConfigHandler;
import com.cyberscraft.uep.account.client.provider.feishu.config.FeiShuClientConfig;
import com.cyberscraft.uep.account.client.provider.feishu.handle.CommonFeiShuExternalConnectHandler;
import com.cyberscraft.uep.account.client.provider.feishu.handle.FeiShuCallBackConfigHandler;
import com.cyberscraft.uep.account.client.provider.feishu.service.IFeiShuAccessTokenService;
import com.cyberscraft.uep.common.config.ServerConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;

import javax.annotation.Resource;

/**
 * 飞书对应的客户端自动注入实现类，只有在系统中配置了允许飞书，相关的配置才会生效
 *
 * @Author：xhquan
 * @Project：uep-services
 * @since：2023/2/28 14:15
 */

@Configuration
@Order(value = Integer.MAX_VALUE)
public class FeiShuAccountConfiguration {

    @Resource
    private IFeiShuAccessTokenService feiShuAccessTokenService;

    @Resource
    private FeiShuClientConfig feiShuClientConfig;

    @Bean
    public IExternalConnectHandler feiShuExternalAccountHandler() {
        CommonFeiShuExternalConnectHandler handler = new CommonFeiShuExternalConnectHandler();
        handler.setFeiShuAccessTokenService(feiShuAccessTokenService);
        handler.setClientConfig(feiShuClientConfig);
        return handler;
    }

    @Bean
    public IThirdPartyCallBackConfigHandler feiShuCallBackConfigHandler(ServerConfig serverConfig) {
        FeiShuCallBackConfigHandler handler = new FeiShuCallBackConfigHandler();
        handler.setClientConfig(feiShuClientConfig);
        handler.setFeiShuAccessTokenService(feiShuAccessTokenService);
        handler.setServerConfig(serverConfig);
        return handler;
    }
}
