package com.cyberscraft.uep.account.client.provider.feishu.service.impl;

import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountErrorType;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountException;
import com.cyberscraft.uep.account.client.provider.feishu.constant.FeiShuTokenTypeEnum;
import com.cyberscraft.uep.account.client.provider.feishu.domain.FeiShuAccessToken;
import com.cyberscraft.uep.account.client.provider.feishu.domain.FeiShuConfig;
import com.cyberscraft.uep.account.client.provider.feishu.service.AbstractFeiShuRequest;
import com.cyberscraft.uep.account.client.provider.feishu.service.IFeiShuAccessTokenService;
import com.cyberscraft.uep.common.bean.RestApiResponse;
import com.cyberscraft.uep.common.util.WebUrlUtil;
import com.cyberscraft.uep.common.enums.ExceptionCodeEnum;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.common.util.RestAPIUtil;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @Author：xhquan
 * @Project：uep-services
 * @since：2023/2/27 17:31
 */
@Service
public class FeiShuAccessTokenServiceImpl extends AbstractFeiShuRequest implements IFeiShuAccessTokenService {

    private String ACCESS_TOKEN_URL = "/open-apis/auth/v3/tenant_access_token/internal";

    private final static Logger LOG = LoggerFactory.getLogger(FeiShuAccessTokenServiceImpl.class);

    /**
     * 飞书access_token redis key前缀
     */
    private static final String FEISHU_TOKEN_REDIS_KEY = "FEISHU_TOKEN_REDIS_KEY:%s:%s";

    /**
     * 请求token时，锁的前缀，占位符为accessToken缓存的key
     */
    private static final String FEISHU_TOKEN_LOCK_KEY = "lock:%s";

    /**
     * 请求token时，锁的等待时间，单位：毫秒
     */
    private static final Long FEISHU_TOKEN_LOCK_WAIT_TIME = 5000L;
    /**
     * 请求token时，锁的超时时间,单位：毫秒
     */
    private static final Long FEISHU_TOKEN_LOCK_TIME = 10000L;

    @Resource
    private RedissonClient redissonClient;

    @Override
    public String getAccessToken(String tenantId, FeiShuConfig config) {
        if (config == null) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.CONFIG_INVALID);
        }
        String tokenCacheKey = String.format(FEISHU_TOKEN_REDIS_KEY, tenantId, config.getAppKey());

        String accessToken = getValueFromCache(tokenCacheKey);
        if (StringUtils.isNotBlank(accessToken)) {
            return accessToken;
        }
        String lockKey = String.format(FEISHU_TOKEN_LOCK_KEY, tokenCacheKey);
        RLock lock = redissonClient.getLock(lockKey);

        boolean holdLock;
        try {
            holdLock = lock.tryLock(FEISHU_TOKEN_LOCK_WAIT_TIME, FEISHU_TOKEN_LOCK_TIME, TimeUnit.MILLISECONDS);

            accessToken = getValueFromCache(tokenCacheKey);
            if (StringUtils.isNotBlank(accessToken)) {
                return accessToken;
            }
            if (!holdLock) {
                throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.GET_ACCESS_TOKEN_ERROR);
            }

            FeiShuAccessToken token = getTenantAccessTokenFromServer(config);
            saveAccessTokenToCache(tokenCacheKey, token);
            return token.getAccessToken();
        } catch (InterruptedException e) {
            throw new ThirdPartyAccountException(ExceptionCodeEnum.INNER_ERROR.getCode(), e.getMessage());
        } catch (ThirdPartyAccountException e) {
            throw e;
        } finally {
            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    /**
     * 从redis 获取token
     *
     * @param cacheKey
     * @return
     */
    private String getValueFromCache(String cacheKey) {
        RBucket<String> bucket = redissonClient.getBucket(cacheKey);
        return bucket.get();
    }

    /**
     * token 保存到缓存
     *
     * @param tokenCacheKey
     * @param token
     */
    private void saveAccessTokenToCache(String tokenCacheKey, FeiShuAccessToken token) {
        setValueToCache(tokenCacheKey, token.getAccessToken(), token.getExpire(), TimeUnit.SECONDS);
    }

    private void setValueToCache(String cacheKey, String value, long timeToLive, TimeUnit timeUnit) {
        redissonClient.getBucket(cacheKey).set(value, timeToLive, timeUnit);
    }


    private FeiShuAccessToken getTenantAccessTokenFromServer(FeiShuConfig config) {
        String api = WebUrlUtil.getWebServerUrl(config.getApiBaseUrl(), ACCESS_TOKEN_URL);

        Map<String, String> header = new HashMap<>(1);
        header.put("Content-Type", "application/json; charset=utf-8");

        Map<String, String> body = new HashMap<>(2);
        body.put("app_id", config.getAppKey());
        body.put("app_secret", config.getAppSecret());
        RestApiResponse apiResponse = RestAPIUtil.modifyEntityForString(api, "POST", body, header, null, null);
        checkRestResponse(apiResponse, ACCESS_TOKEN_URL);
        String respStr = apiResponse.getBody().toString();
        Map<String, Object> map = JsonUtil.str2Map(respStr);
        LOG.info("get access token info is {}", JsonUtil.obj2Str(map));
        FeiShuAccessToken token = new FeiShuAccessToken();
        token.setAccessToken(map.get("tenant_access_token").toString());
        token.setTokenType(FeiShuTokenTypeEnum.TENANT.getCode());
        token.setExpire((Integer) map.get("expire") - 100);
        return token;
    }


    @Override
    public String reGetAccessToken(String tenantId, FeiShuConfig config) {
        if (config == null) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.CONFIG_INVALID);
        }
        String tokenCacheKey = String.format(FEISHU_TOKEN_LOCK_KEY, tenantId, config.getAppKey());
        clearCache(tokenCacheKey);
        return getAccessToken(tenantId, config);
    }

    /**
     * 清楚redis 缓存的key
     *
     * @param cacheKey
     */
    private void clearCache(String cacheKey) {
        redissonClient.getBucket(cacheKey).delete();
    }
}
