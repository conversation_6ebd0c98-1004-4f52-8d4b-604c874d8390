package com.cyberscraft.uep.account.client.provider.feishu.handle;

import com.cyberscraft.uep.account.client.constant.ThirdPartyAccountType;
import com.cyberscraft.uep.account.client.domain.SnsConfig;
import com.cyberscraft.uep.account.client.provider.IThirdPartyCallBackConfigHandler;
import com.cyberscraft.uep.common.config.ServerConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @Author：xhquan
 * @Project：uep-services
 * @since：2023/3/6 11:11
 */
public class FeiShuCallBackConfigHandler extends AbstractFeiShuHandler implements IThirdPartyCallBackConfigHandler {

    private final static Logger LOG = LoggerFactory.getLogger(FeiShuCallBackConfigHandler.class);

    private ServerConfig serverConfig;

    @Override
    public boolean isSupported(String accountType) {
        return ThirdPartyAccountType.FEISHU.getCode().equalsIgnoreCase(accountType);
    }

    @Override
    public void improveSnsConfig(String tenantId, SnsConfig snsConfig) {

    }

    @Override
    public void registerEventCallBack(String tenantId, SnsConfig snsConfig) {

    }

    @Override
    public void checkAndRegisterEventCallBack(String tenantId, SnsConfig snsConfig) {

    }

    @Override
    public void updateEventCallBack(String tenantId, SnsConfig oldSnsConfig, SnsConfig newSnsConfig) {

    }

    @Override
    public void cleanFiluredCallBackEvent(String tenantId, SnsConfig snsConfig) {

    }

    @Override
    public void removeEventCallBack(String tenantId, SnsConfig snsConfig) {

    }

    @Override
    public void removeEventCallBack(String tenantId, String id, SnsConfig snsConfig) {

    }

    @Override
    public <T> T getEventCallBacks(String tenantId, SnsConfig snsConfig) {
        return null;
    }

    public void setServerConfig(ServerConfig serverConfig) {
        this.serverConfig = serverConfig;
    }
}
