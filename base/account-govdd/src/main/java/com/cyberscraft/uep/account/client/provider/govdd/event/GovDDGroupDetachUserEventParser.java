package com.cyberscraft.uep.account.client.provider.govdd.event;

import com.cyberscraft.uep.account.client.constant.ThirdPartyEventTagConstant;
import com.cyberscraft.uep.account.client.domain.ThirdPartyEvent;
import com.cyberscraft.uep.account.client.domain.UserEventBody;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountException;
import com.cyberscraft.uep.account.client.provider.govdd.constant.GovDDEventContentTagConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/***
 *
 * @date 2021-11-09
 * <AUTHOR>
 ***/
@Component
public class GovDDGroupDetachUserEventParser implements IGovDDEventParser {


    /***
     *
     */
    private final static Logger LOG = LoggerFactory.getLogger(GovDDUserLeaveEventParser.class);

    @Override
    public String getSupportTag() {
        return GovDDEventContentTagConstant.ORGANIZATION_DETACH_EMPLOYEE;
    }

    @Override
    public boolean isSupportTags(String tag) throws ThirdPartyAccountException {
        return GovDDEventContentTagConstant.ORGANIZATION_DETACH_EMPLOYEE.equalsIgnoreCase(tag);
    }

    @Override
    public void parse(ThirdPartyEvent event, Map<String, Object> data) throws ThirdPartyAccountException {
        try {
            event.setEventTag(ThirdPartyEventTagConstant.USER_ADD_UPDATE);
            UserEventBody eventBody = new UserEventBody();
            eventBody.setUserIds((List<String>) data.get("employeeCodes"));
            event.setData(eventBody);
            //event.setUserIds((List<String>) data.get("employeeCodes"));
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }
}
