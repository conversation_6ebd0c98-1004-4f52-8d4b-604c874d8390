package com.cyberscraft.uep.account.client.provider.govdd;

import com.alibaba.xxpt.gateway.shared.client.http.ExecutableClient;
import com.cyberscraft.uep.account.client.constant.ThirdPartyThreadPoolNameConstant;
import com.cyberscraft.uep.account.client.provider.*;
import com.cyberscraft.uep.account.client.provider.govdd.condition.GovDDApplicationPostStartCondition;
import com.cyberscraft.uep.account.client.provider.govdd.condition.GovDDCondition;
import com.cyberscraft.uep.account.client.provider.govdd.config.GovDDConfig;
import com.cyberscraft.uep.account.client.provider.govdd.config.GovDDFilterConfig;
import com.cyberscraft.uep.account.client.provider.govdd.event.IGovDDEventParser;
import com.cyberscraft.uep.account.client.provider.govdd.handle.*;
import com.cyberscraft.uep.account.client.service.IThirdPartyEventExecutorService;
import com.cyberscraft.uep.common.service.IApplicationPostStartHandle;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.StringRedisTemplate;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.ExecutorService;

/***
 *
 * @date 2021-10-30
 * <AUTHOR>
 ***/
@Configuration
@Order(value = Integer.MAX_VALUE)
//@ConditionalOnProperty(name = "auth.provider.third.party.grant.type", havingValue = "govdd")
@Conditional(GovDDCondition.class)
public class GovDDAccountAutoConfiguration {

    /***
     *
     */
    private final static Logger LOG = LoggerFactory.getLogger(GovDDAccountAutoConfiguration.class);

    @Resource
    private GovDDConfig govDDConfiguration;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    ExecutableClient executableClient;

    @Resource
    private List<IGovDDEventParser> govDDEventParsers;

    @Resource(name = ThirdPartyThreadPoolNameConstant.THIRDPARTY_MESSAGE_POOL_NAME)
    private ExecutorService messagePoolExcutor;

    @Resource
    private IThirdPartyEventExecutorService thirdPartyEventExecutorService;

    //@Resource
    //private IThirdPartyTenantTransfer thirdPartyTenantTransfer;
    //
    //@Resource
    //private IThirdPartySimulateTokenService thridPartySimulateTokenService;

    @Resource
    private GovDDFilterConfig govDDFilterConfig;

    @ConditionalOnMissingBean
    @Bean
    public IThirdPartySimulateTokenService govDDDefaultSimulateTokenService() {
        IThirdPartySimulateTokenService service = new IThirdPartySimulateTokenService() {
            @Override
            public void simulateAdminToken(String tokenValue, String adminLoginId, int adminType) {
                LOG.info("使用了默认的Token处理器，系统不进行处理");
            }
        };
        return service;
    }

    //@ConditionalOnProperty(name = "auth.provider.third.party.grant.type", havingValue = ThirdPartyAuthTypeConstant.GOVDD)
    @Bean
    //@ConditionalOnMissingBean
    public IThirdPartyCallBackConfigHandler govDDEventHandler() {
        GovDDCallBackConfigHandler handler = new GovDDCallBackConfigHandler();
        handler.setExecutableClient(executableClient);
        handler.setGovDDFilterConfig(govDDFilterConfig);
        handler.setThirdPartyTenantTransfer(govDDTenantTransfer());
        return handler;
    }

    //@ConditionalOnProperty(name = "auth.provider.third.party.grant.type", havingValue = ThirdPartyAuthTypeConstant.GOVDD)
    @Bean
    //@ConditionalOnMissingBean
    public IThirdPartyMessageHandler govDDMessageHandler() {
        GovDDMessageHandler handler = new GovDDMessageHandler();
        handler.setMessagePoolExcutor(messagePoolExcutor);
        handler.setThirdPartyEventExecutorService(thirdPartyEventExecutorService);
        handler.setThirdPartyTenantTransfer(govDDTenantTransfer());
        handler.setParsers(govDDEventParsers);
        return handler;
    }


    //@ConditionalOnProperty(name = "auth.provider.third.party.grant.type", havingValue = ThirdPartyAuthTypeConstant.GOVDD)
    @Bean
    //@ConditionalOnMissingBean
    public IThirdPartyTenantTransfer govDDTenantTransfer() {
        GovDDTenantTransferImpl handler = new GovDDTenantTransferImpl();
        handler.setStringRedisTemplate(stringRedisTemplate);
        return handler;
    }

    @Bean
    //@ConditionalOnMissingBean
    //@ConditionalOnProperty(name = "auth.provider.third.party.grant.type", havingValue = ThirdPartyAuthTypeConstant.GOVDD)
    public IThirdPartyAccountHandler govDDAccountHandler() {
        GovDDAccountHandler handler = new GovDDAccountHandler();
        handler.setExecutableClient(executableClient);
        handler.setThridPartyTenantTransfer(govDDTenantTransfer());
        return handler;
    }


    @Bean
    //@ConditionalOnMissingBean
    //@ConditionalOnProperty(name = "auth.provider.third.party.grant.type", havingValue = ThirdPartyAuthTypeConstant.GOVDD)
    public IThirdPartyTokenHandler govDDTokenHandler() {
        GovDDTokenHandler handler = new GovDDTokenHandler();
        handler.setExecutableClient(executableClient);
        return handler;
    }

    @Bean
    //@ConditionalOnMissingBean
    //@ConditionalOnProperty(name = "auth.provider.third.party.grant.type", havingValue = ThirdPartyAuthTypeConstant.GOVDD)
    public IThirdPartyDownloadTicketHanndler govDDDownloadTicketHanndler() {
        GovDDDownloadTicketHandler handler = new GovDDDownloadTicketHandler();
        handler.setExecutableClient(executableClient);
        handler.setGovDDConfiguration(govDDConfiguration);
        return handler;
    }

    @Bean
    //@ConditionalOnMissingBean
    //@ConditionalOnProperty(name = "auth.provider.third.party.grant.type", havingValue = ThirdPartyAuthTypeConstant.GOVDD)
    public IDeviceEventHandler govDDDeviceEventHandler() {
        GovDDDeviceEventHandler handler = new GovDDDeviceEventHandler();
        handler.setExecutableClient(executableClient);
        handler.setGovDDConfiguration(govDDConfiguration);
        return handler;
    }

    @Bean
    @Conditional(GovDDApplicationPostStartCondition.class)
    //@ConditionalOnProperty(name = "auth.provider.third.party.grant.type", havingValue = ThirdPartyAuthTypeConstant.GOVDD)
    public IApplicationPostStartHandle govDDEventApplicationPostStartHandle() {
        GovDDEventApplicationPostStartHandle handler = new GovDDEventApplicationPostStartHandle();
        handler.setThirdPartyEventHandler(govDDEventHandler());
        return handler;
    }
    @Bean
    //@ConditionalOnMissingBean
    public IThirdPartyDsProfileHandler dingDingDsProfileHandler() {
        GovDDDsProfileHandler handler = new GovDDDsProfileHandler();
        return handler;
    }
}
