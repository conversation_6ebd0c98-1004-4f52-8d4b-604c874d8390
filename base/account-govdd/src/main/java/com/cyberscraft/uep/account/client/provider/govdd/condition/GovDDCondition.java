package com.cyberscraft.uep.account.client.provider.govdd.condition;

import com.cyberscraft.uep.common.constant.SysConstant;
import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.env.Environment;
import org.springframework.core.type.AnnotatedTypeMetadata;

/***
 *
 * @date 2021/9/17
 * <AUTHOR>
 ***/
public class GovDDCondition implements Condition {
    @Override
    public boolean matches(ConditionContext context, AnnotatedTypeMetadata annotatedTypeMetadata) {
        Environment environment = context.getEnvironment();
        //只有开启政务钉钉账户，并且开启政务钉钉应用启动后处理才有效
        if ("true".equalsIgnoreCase(environment.getProperty("govdd.enabled"))
               ||
                SysConstant.TRUE.equalsIgnoreCase(environment.getProperty("govdd.enabled"))) {
            return true;
        }
        return false;
    }
}
