package com.cyberscraft.uep.account.client.provider.govdd.event;

import com.cyberscraft.uep.account.client.constant.ThirdPartyEventTagConstant;
import com.cyberscraft.uep.account.client.domain.GroupEventBody;
import com.cyberscraft.uep.account.client.domain.ThirdPartyEvent;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountException;
import com.cyberscraft.uep.account.client.provider.govdd.constant.GovDDEventContentTagConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/***
 *
 * @date 2021-11-08
 * <AUTHOR>
 ***/
@Component
public class GovDDGroupAndUpdateEventParser implements IGovDDEventParser {

    /***
     *
     */
    private final static Logger LOG = LoggerFactory.getLogger(GovDDGroupAndUpdateEventParser.class);

    @Override
    public String getSupportTag() {
        return GovDDEventContentTagConstant.ORGANIZATION_ADD_UPDATE;
    }

    @Override
    public boolean isSupportTags(String tag) throws ThirdPartyAccountException {
        return GovDDEventContentTagConstant.ORGANIZATION_ADD_UPDATE.equalsIgnoreCase(tag);
    }

    @Override
    public void parse(ThirdPartyEvent event, Map<String, Object> data) throws ThirdPartyAccountException {
        try {
            event.setEventTag(ThirdPartyEventTagConstant.GROUP_ADD_UPDATE);
            GroupEventBody eventBody = new GroupEventBody();
            eventBody.setCodes((List<String>) data.get("organizationCodes"));
            event.setData(eventBody);
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }
}
