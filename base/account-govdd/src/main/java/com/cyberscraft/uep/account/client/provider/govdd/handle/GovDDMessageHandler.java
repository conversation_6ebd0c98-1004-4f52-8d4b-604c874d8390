package com.cyberscraft.uep.account.client.provider.govdd.handle;

import com.cyberscraft.uep.account.client.constant.ThirdPartyAccountType;
import com.cyberscraft.uep.account.client.domain.Connector;
import com.cyberscraft.uep.account.client.domain.SnsConfig;
import com.cyberscraft.uep.account.client.domain.ThirdPartyCallBackMessage;
import com.cyberscraft.uep.account.client.domain.ThirdPartyEvent;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountException;
import com.cyberscraft.uep.account.client.provider.IThirdPartyMessageHandler;
import com.cyberscraft.uep.account.client.provider.IThirdPartyTenantTransfer;
import com.cyberscraft.uep.account.client.provider.govdd.domain.EventResultVO;
import com.cyberscraft.uep.account.client.provider.govdd.event.IGovDDEventParser;
import com.cyberscraft.uep.account.client.service.IThirdPartyEventExecutorService;
import com.cyberscraft.uep.common.util.JsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;

/***
 *
 * @date 2021/3/13
 * <AUTHOR>
 ***/
public class GovDDMessageHandler implements IThirdPartyMessageHandler {

    /***
     *
     */
    private final static Logger LOG = LoggerFactory.getLogger(GovDDMessageHandler.class);

    /***
     * 租户转换接口
     */
    private IThirdPartyTenantTransfer thirdPartyTenantTransfer;

    private IThirdPartyEventExecutorService thirdPartyEventExecutorService;

    /***
     *
     */
    private List<IGovDDEventParser> parsers;

    /***
     *
     */
    private final static HashMap<String, IGovDDEventParser> TAG_PARSER_MAP = new HashMap<>();


    ExecutorService messagePoolExcutor;

    @Override
    public String getSupportedAccountType() {
        return ThirdPartyAccountType.GOV_DINGDING.getCode();
    }

    /***
     * 因为政务钉钉不需要进行加解密处理
     * @param msg
     * @param connector
     * @return
     * @throws ThirdPartyAccountException
     */
    @Override
    public String decryptMsg(ThirdPartyCallBackMessage msg, Connector connector) throws ThirdPartyAccountException {
        return msg != null ? msg.getMsgBody() : null;
    }

    @Override
    public <T> T deal(ThirdPartyCallBackMessage msg, SnsConfig snsConfig) throws ThirdPartyAccountException {
        if (msg == null || StringUtils.isBlank(msg.getMsgBody())) {
            LOG.warn("处理政务钉钉消息时，消息对像为空，本次跳过");
            return getSuccessEventResult();
        }
        messagePoolExcutor.execute(() -> {
            try {
                ThirdPartyEvent event = parseThirdPartyEvent(msg.getMsgBody());
                if (event == null) {
                    LOG.warn("解析事件出错，事件源为:{}", msg.getMsgBody());
                    return;
                }
                thirdPartyEventExecutorService.onEvent(event);
            } catch (Exception e) {
                LOG.error(e.getMessage(), e);
            }
        });
        //因为政务钉钉的回调事件都是用户及组相关异步通知事件，所以进行异步事件的转换并且处理
        return getSuccessEventResult();
    }

    @Override
    public <T> T deal(ThirdPartyCallBackMessage msg, Connector connector) throws ThirdPartyAccountException {
        return null;
    }


    private ThirdPartyEvent parseThirdPartyEvent(String data) {
        try {
            Map<String, Object> json = JsonUtil.str2Map(data);
            ThirdPartyEvent event = new ThirdPartyEvent();
            event.setEventTag((String) json.get("tag"));
            event.setEventTime(LocalDateTime.now());
            String tenantId = String.valueOf(json.get("tenantId"));
            event.setTenantId(thirdPartyTenantTransfer.thridPartyTenant2Tenant(tenantId));
            event.setAccountType(ThirdPartyAccountType.GOV_DINGDING.getCode());
            IGovDDEventParser parser = findParser(event.getEventTag());
            if (parser == null) {
                LOG.error("未找到{}对应的事件解释器", event.getEventTag());
                return null;
            }
            parser.parse(event, json);
            //event.set((List<String>)json.get("employeeCodes"));
            LOG.info("当前事件消息Tag->{},对应租户ID->{}", event.getEventTag(), event.getTenantId());
            return event;
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
        return null;
    }

    /***
     *
     * @param <T>
     * @return
     */
    private <T> T getSuccessEventResult() {
        return (T) EventResultVO.SUCCESS;
    }

    /****
     *
     */
    private synchronized void initTagParserMap() {
        TAG_PARSER_MAP.clear();
        for (IGovDDEventParser parser : this.parsers) {
            String tag = parser.getSupportTag();
            if (StringUtils.isNotBlank(tag)) {
                TAG_PARSER_MAP.put(tag, parser);
            }
        }
    }

    /***
     *
     * @param tag
     * @return
     */
    private IGovDDEventParser findParser(String tag) {
        if (StringUtils.isBlank(tag)) {
            return null;
        }
        IGovDDEventParser ret = TAG_PARSER_MAP.get(tag);
        if (ret != null) {
            return ret;
        }
        if (parsers != null && parsers.size() > 0) {
            for (IGovDDEventParser parser : parsers) {
                if (parser.isSupportTags(tag)) {
                    TAG_PARSER_MAP.put(tag, parser);
                    return parser;
                }
            }
        }
        return null;
    }


    public List<IGovDDEventParser> getParsers() {
        return parsers;
    }

    public void setParsers(List<IGovDDEventParser> parsers) {
        this.parsers = parsers;
        this.initTagParserMap();
    }

    public IThirdPartyTenantTransfer getThirdPartyTenantTransfer() {
        return thirdPartyTenantTransfer;
    }

    public void setThirdPartyTenantTransfer(IThirdPartyTenantTransfer thirdPartyTenantTransfer) {
        this.thirdPartyTenantTransfer = thirdPartyTenantTransfer;
    }

    public IThirdPartyEventExecutorService getThirdPartyEventExecutorService() {
        return thirdPartyEventExecutorService;
    }

    public void setThirdPartyEventExecutorService(IThirdPartyEventExecutorService thirdPartyEventExecutorService) {
        this.thirdPartyEventExecutorService = thirdPartyEventExecutorService;
    }

    public ExecutorService getMessagePoolExcutor() {
        return messagePoolExcutor;
    }

    public void setMessagePoolExcutor(ExecutorService messagePoolExcutor) {
        this.messagePoolExcutor = messagePoolExcutor;
    }
}
