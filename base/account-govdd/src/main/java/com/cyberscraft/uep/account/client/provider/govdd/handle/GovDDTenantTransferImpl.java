package com.cyberscraft.uep.account.client.provider.govdd.handle;

import com.cyberscraft.uep.account.client.constant.ThirdPartyAccountType;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountException;
import com.cyberscraft.uep.account.client.provider.IThirdPartyTenantTransfer;
import com.cyberscraft.uep.account.client.provider.govdd.config.GovDDFilterConfig;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/***
 * 政务钉钉租户ID转换类
 * @date 2021-10-31
 * <AUTHOR>
 ***/
//@Component("govDDTenantTransfer")
public class GovDDTenantTransferImpl implements IThirdPartyTenantTransfer {


    @Resource
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private GovDDFilterConfig govDDFilterConfig;

    /***
     * 是否已经加载
     */
    private volatile boolean isLoaded = false;

    /***
     *
     */
    private final static String GOVDD_TENANT_ID_MAP_KEY = "GOVDD_TENANT_ID_MAP";

    /****
     *
     */
    private Map<String, String> GOVDDTENANT_2_TENANT_MAP = new HashMap<>();


    /****
     *
     */
    private Map<String, String> TENANTID_2_GOVDDTENANT_MAP = new HashMap<>();

    @Override
    public boolean isSupported(String accountType) throws ThirdPartyAccountException {
        return ThirdPartyAccountType.GOV_DINGDING.getCode().equalsIgnoreCase(accountType);
    }

    /****
     *
     */
    private final static Logger LOG = LoggerFactory.getLogger(GovDDTenantTransferImpl.class);

    /***
     *
     */
    void loadTenantMap() {
        if (isLoaded) {
            return;
        }
        synchronized (this) {
            LOG.info("开始加载政务钉钉租户与uemx租户ID的对应关系数据");
            //REDIS中存储的是UEMTenant与DDTenant的对应关系的MAP
            Map<Object, Object> data = stringRedisTemplate.opsForHash().entries(GOVDD_TENANT_ID_MAP_KEY);
            for (Map.Entry<Object, Object> entry : data.entrySet()) {
                String dovDDTenantVal = (String) entry.getValue();
                String[] govDDTenants = dovDDTenantVal.split(",");
                for (String govDDTenant : govDDTenants) {
                    GOVDDTENANT_2_TENANT_MAP.put(govDDTenant, entry.getKey().toString());
                    TENANTID_2_GOVDDTENANT_MAP.put(entry.getKey().toString(), govDDTenant);

                    LOG.info("政务钉钉租户{}对应uemx租户ID为:{}", govDDTenant, entry.getKey().toString());

                }
            }
            LOG.info("加载政务钉钉租户与uemx租户ID的对应关系数据完成，本次加载完成后共有{}个租户的对应的关系", GOVDDTENANT_2_TENANT_MAP.size());

            if (GOVDDTENANT_2_TENANT_MAP.size() >= 0) {
                isLoaded = true;
            } else {
                isLoaded = false;
            }
        }
    }

    @Override
    public String tenant2ThridPartyTenant(String tenantId) throws ThirdPartyAccountException {
        if(govDDFilterConfig.isTenantTransfer()) {
            loadTenantMap();
            if (StringUtils.isBlank(tenantId)) {
                return tenantId;
            }
            String transferTenant = TENANTID_2_GOVDDTENANT_MAP.get(tenantId);
            if (StringUtils.isBlank(transferTenant)) {
                LOG.info("tenant2ThridPartyTenant tenantId:{} 不存在 ", tenantId);
                transferTenant = tenantId;
            }
            return transferTenant;
        }else{
            return tenantId;
        }
    }

    @Override
    public String thridPartyTenant2Tenant(String tenantId) throws ThirdPartyAccountException {
        if(govDDFilterConfig.isTenantTransfer()){
            loadTenantMap();
            if (StringUtils.isBlank(tenantId)) {
                return tenantId;
            }
            String transferTenant = GOVDDTENANT_2_TENANT_MAP.get(tenantId);
            if (StringUtils.isBlank(transferTenant)) {
                LOG.info("thridPartyTenant2Tenant tenantId:{} 不存在 ", tenantId);
                transferTenant=tenantId;
                //throw new ThirdPartyAccountException(ExceptionCodeEnum.TENANT_NOT_EXIST.getCode(), "tenantId:" + tenantId + " " + ExceptionCodeEnum.TENANT_NOT_EXIST.getMessage());
            }
            return transferTenant;
        }else{
            return tenantId;
        }

    }

    public StringRedisTemplate getStringRedisTemplate() {
        return stringRedisTemplate;
    }

    public void setStringRedisTemplate(StringRedisTemplate stringRedisTemplate) {
        this.stringRedisTemplate = stringRedisTemplate;
    }
}
