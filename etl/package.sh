#!/bin/bash
set -e
#===============================================================================
#
#          FILE: package.sh
#
#
#   DESCRIPTION:  script to package uep services, put all together
#
#       OPTIONS:  ---
#  REQUIREMENTS:  ---
#          BUGS:  ---
#         NOTES:  ---
#        AUTHOR:  Liu Yang
#       VERSION:  1.0
#       CREATED:  06/19/2019 20:12:43 AM CST
#      REVISION:  ---
#===============================================================================#
SCRIPT=$(readlink -f $0)
SCRIPTPATH=$(dirname ${SCRIPT})

declare -a SUB_DIR=("bit")
declare -r ETL_DIR="etl"

function package_etl() {
    rm -f ${ETL_DIR}.tar.gz
    rm -rf ${ETL_DIR}
    mkdir -p ${ETL_DIR}

    # 复制子目录
    for sub_dir in "${SUB_DIR[@]}"; do
      cp -r ${sub_dir} ${ETL_DIR}
    done

    tar -czvf ${ETL_DIR}.tar.gz ${ETL_DIR}
    rm -rf ${ETL_DIR}
}

################################ MAIN ROUTINE ##########################################
function main(){
    cd ${SCRIPTPATH}
    echo "Package etl"
    package_etl
    echo "Package successfully!"
}

main "@"