<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户注册 - 数字化企业管理平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .registration-container {
            max-width: 500px;
            width: 100%;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            padding: 40px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .page-title {
            font-size: 28px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 8px;
            text-align: center;
        }
        
        .page-subtitle {
            font-size: 14px;
            color: #8c8c8c;
            text-align: center;
            margin-bottom: 32px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            color: #262626;
            font-weight: 500;
        }
        
        .required {
            color: #ff4d4f;
        }
        
        .form-control {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #d9d9d9;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: white;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        .form-control.error {
            border-color: #ff4d4f;
            box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
        }
        
        .error-message {
            color: #ff4d4f;
            font-size: 12px;
            margin-top: 4px;
            display: none;
        }
        
        .password-strength {
            margin-top: 8px;
        }
        
        .strength-bar {
            height: 4px;
            background: #f0f0f0;
            border-radius: 2px;
            overflow: hidden;
            margin-bottom: 4px;
        }
        
        .strength-fill {
            height: 100%;
            width: 0%;
            transition: all 0.3s ease;
            border-radius: 2px;
        }
        
        .strength-weak { background: #ff4d4f; }
        .strength-medium { background: #faad14; }
        .strength-strong { background: #52c41a; }
        
        .strength-text {
            font-size: 12px;
            color: #8c8c8c;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
        }
        
        .checkbox-group {
            display: flex;
            align-items: flex-start;
            margin-bottom: 24px;
        }
        
        .checkbox-input {
            margin-right: 8px;
            margin-top: 2px;
        }
        
        .checkbox-label {
            font-size: 13px;
            color: #595959;
            line-height: 1.5;
        }
        
        .checkbox-label a {
            color: #1890ff;
            text-decoration: none;
        }
        
        .checkbox-label a:hover {
            text-decoration: underline;
        }
        
        .btn-register {
            width: 100%;
            padding: 14px;
            background: linear-gradient(135deg, #1890ff, #40a9ff);
            border: none;
            border-radius: 8px;
            color: white;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .btn-register::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }
        
        .btn-register:hover {
            background: linear-gradient(135deg, #40a9ff, #69c0ff);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(24, 144, 255, 0.4);
        }
        
        .btn-register:hover::before {
            left: 100%;
        }
        
        .btn-register:active {
            transform: translateY(0);
        }
        
        .btn-register:disabled {
            background: #f5f5f5;
            color: #bfbfbf;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .login-link {
            text-align: center;
            margin-top: 24px;
            padding-top: 24px;
            border-top: 1px solid #f0f0f0;
        }
        
        .login-link a {
            color: #1890ff;
            text-decoration: none;
            font-size: 14px;
        }
        
        .login-link a:hover {
            text-decoration: underline;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .registration-container {
                padding: 24px;
                margin: 10px;
            }
            
            .form-row {
                grid-template-columns: 1fr;
                gap: 0;
            }
            
            .page-title {
                font-size: 24px;
            }
        }
        
        @media (max-width: 480px) {
            body {
                padding: 10px;
            }
            
            .registration-container {
                padding: 20px;
            }
            
            .page-title {
                font-size: 22px;
            }
        }
    </style>
</head>

<body>
    <div class="registration-container">
        <h1 class="page-title">用户注册</h1>
        <p class="page-subtitle">创建您的企业管理平台账户</p>

        <form id="registrationForm" novalidate>
            <div class="form-row">
                <div class="form-group">
                    <label class="form-label">姓名 <span class="required">*</span></label>
                    <input type="text" class="form-control" id="fullName" name="fullName" required>
                    <div class="error-message" id="fullNameError">请输入您的姓名</div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">用户名 <span class="required">*</span></label>
                    <input type="text" class="form-control" id="username" name="username" required>
                    <div class="error-message" id="usernameError">用户名长度应为3-20个字符</div>
                </div>
            </div>

            <div class="form-group">
                <label class="form-label">邮箱地址 <span class="required">*</span></label>
                <input type="email" class="form-control" id="email" name="email" required>
                <div class="error-message" id="emailError">请输入有效的邮箱地址</div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label class="form-label">手机号码 <span class="required">*</span></label>
                    <input type="tel" class="form-control" id="phone" name="phone" required>
                    <div class="error-message" id="phoneError">请输入有效的手机号码</div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">部门</label>
                    <input type="text" class="form-control" id="department" name="department">
                </div>
            </div>

            <div class="form-group">
                <label class="form-label">密码 <span class="required">*</span></label>
                <input type="password" class="form-control" id="password" name="password" required>
                <div class="password-strength">
                    <div class="strength-bar">
                        <div class="strength-fill" id="strengthFill"></div>
                    </div>
                    <div class="strength-text" id="strengthText">密码强度：请输入密码</div>
                </div>
                <div class="error-message" id="passwordError">密码长度至少8位，包含字母和数字</div>
            </div>

            <div class="form-group">
                <label class="form-label">确认密码 <span class="required">*</span></label>
                <input type="password" class="form-control" id="confirmPassword" name="confirmPassword" required>
                <div class="error-message" id="confirmPasswordError">两次输入的密码不一致</div>
            </div>

            <div class="checkbox-group">
                <input type="checkbox" class="checkbox-input" id="agreeTerms" name="agreeTerms" required>
                <label class="checkbox-label" for="agreeTerms">
                    我已阅读并同意 <a href="#" target="_blank">用户协议</a> 和 <a href="#" target="_blank">隐私政策</a>
                </label>
            </div>

            <button type="submit" class="btn-register" id="registerBtn">
                立即注册
            </button>
        </form>

        <div class="login-link">
            已有账户？<a href="login.html">立即登录</a>
        </div>
    </div>

    <script>
        // 表单验证和交互逻辑
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('registrationForm');
            const inputs = form.querySelectorAll('.form-control');
            const registerBtn = document.getElementById('registerBtn');
            
            // 密码强度检测
            const passwordInput = document.getElementById('password');
            const strengthFill = document.getElementById('strengthFill');
            const strengthText = document.getElementById('strengthText');
            
            passwordInput.addEventListener('input', function() {
                const password = this.value;
                const strength = calculatePasswordStrength(password);
                updatePasswordStrength(strength);
            });
            
            function calculatePasswordStrength(password) {
                let score = 0;
                if (password.length >= 8) score += 25;
                if (password.match(/[a-z]/)) score += 25;
                if (password.match(/[A-Z]/)) score += 25;
                if (password.match(/[0-9]/)) score += 25;
                if (password.match(/[^a-zA-Z0-9]/)) score += 25;
                return Math.min(score, 100);
            }
            
            function updatePasswordStrength(strength) {
                strengthFill.style.width = strength + '%';
                
                if (strength < 50) {
                    strengthFill.className = 'strength-fill strength-weak';
                    strengthText.textContent = '密码强度：弱';
                } else if (strength < 75) {
                    strengthFill.className = 'strength-fill strength-medium';
                    strengthText.textContent = '密码强度：中等';
                } else {
                    strengthFill.className = 'strength-fill strength-strong';
                    strengthText.textContent = '密码强度：强';
                }
            }
            
            // 实时验证
            inputs.forEach(input => {
                input.addEventListener('blur', function() {
                    validateField(this);
                });
                
                input.addEventListener('input', function() {
                    if (this.classList.contains('error')) {
                        validateField(this);
                    }
                });
            });
            
            function validateField(field) {
                const fieldName = field.name;
                const value = field.value.trim();
                let isValid = true;
                let errorMessage = '';
                
                switch (fieldName) {
                    case 'fullName':
                        isValid = value.length >= 2;
                        errorMessage = '请输入您的姓名';
                        break;
                    case 'username':
                        isValid = value.length >= 3 && value.length <= 20;
                        errorMessage = '用户名长度应为3-20个字符';
                        break;
                    case 'email':
                        isValid = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
                        errorMessage = '请输入有效的邮箱地址';
                        break;
                    case 'phone':
                        isValid = /^1[3-9]\d{9}$/.test(value);
                        errorMessage = '请输入有效的手机号码';
                        break;
                    case 'password':
                        isValid = value.length >= 8 && /(?=.*[a-zA-Z])(?=.*\d)/.test(value);
                        errorMessage = '密码长度至少8位，包含字母和数字';
                        break;
                    case 'confirmPassword':
                        isValid = value === passwordInput.value;
                        errorMessage = '两次输入的密码不一致';
                        break;
                }
                
                const errorElement = document.getElementById(fieldName + 'Error');
                if (isValid) {
                    field.classList.remove('error');
                    errorElement.style.display = 'none';
                } else {
                    field.classList.add('error');
                    errorElement.textContent = errorMessage;
                    errorElement.style.display = 'block';
                }
                
                return isValid;
            }
            
            // 表单提交
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                let isFormValid = true;
                inputs.forEach(input => {
                    if (!validateField(input)) {
                        isFormValid = false;
                    }
                });
                
                const agreeTerms = document.getElementById('agreeTerms');
                if (!agreeTerms.checked) {
                    alert('请先同意用户协议和隐私政策');
                    isFormValid = false;
                }
                
                if (isFormValid) {
                    registerBtn.disabled = true;
                    registerBtn.textContent = '注册中...';

                    // 保存注册信息到localStorage
                    const formData = new FormData(form);
                    localStorage.setItem('registeredUsername', formData.get('username'));
                    localStorage.setItem('registeredEmail', formData.get('email'));

                    // 模拟注册请求
                    setTimeout(() => {
                        // 跳转到注册成功页面
                        window.location.href = 'registration-success.html?username=' +
                            encodeURIComponent(formData.get('username')) +
                            '&email=' + encodeURIComponent(formData.get('email'));
                    }, 2000);
                }
            });
        });
    </script>
</body>

</html>
