package com.cyberscraft.uep.iam.notify.push;

import com.cyberscraft.uep.starter.dds.TenantHolder;
import com.cyberscraft.uep.iam.IamNotifyApplication;
import com.cyberscraft.uep.iam.service.data.IRepushService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import javax.annotation.Resource;

/***
 *
 * @date 2021/7/1
 * <AUTHOR>
 ***/
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = IamNotifyApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class TestRepushFailureRecord {


    @Resource
    private IRepushService repushService;

    @Test
    public void testRepushFailureRecord() {
        TenantHolder.setTenantCode("uep1");
        repushService.rePushFailureRecord();
        try {
            Thread.sleep(1000000000);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
