package com.cyberscraft.uep.iam.notify.service.impl;

import com.cyberscraft.uep.common.dto.QueryPage;
import com.cyberscraft.uep.iam.IamNotifyApplication;
import com.cyberscraft.uep.iam.service.data.IDataPullService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.time.LocalDateTime;


/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2019-08-20 15:21
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = IamNotifyApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class DataPullServiceImplTest {
    @Autowired
    private IDataPullService dataPullService;

    @Test
    public void pullUsers() {
        LocalDateTime localDateTime = LocalDateTime.now().minusYears(1L);
        LocalDateTime localDateTime1 = LocalDateTime.now();
        QueryPage queryPage = dataPullService.pullUsers(localDateTime,localDateTime1,1,10);
        System.out.println(queryPage);
    }

    @Test
    public void pullOrgs() {
    }
}