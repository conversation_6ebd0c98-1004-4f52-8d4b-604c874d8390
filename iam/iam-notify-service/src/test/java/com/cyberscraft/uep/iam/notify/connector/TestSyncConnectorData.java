package com.cyberscraft.uep.iam.notify.connector;

import com.cyberscraft.uep.iam.notify.configuartion.UserTaskConfiguration;
import com.cyberscraft.uep.iam.service.user.IUserService;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import com.cyberscraft.uep.common.bean.NamedThreadFactory;
import com.cyberscraft.uep.iam.IamNotifyApplication;
import com.cyberscraft.uep.iam.common.exception.UserCenterException;
import com.cyberscraft.uep.iam.constants.RedisKeyConstants;
import com.cyberscraft.uep.iam.errors.TransactionErrorType;
import com.cyberscraft.uep.iam.service.connector.IConnectorService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/***
 *
 * @date 2021/7/7
 * <AUTHOR>
 ***/
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = IamNotifyApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class TestSyncConnectorData {

    @Resource
    private IConnectorService connectorService;

    @Resource
    private IUserService iUserService;

    @Resource
    private RedissonClient redissonClient;

    @Autowired
    private UserTaskConfiguration userTaskConfiguration;

    private ExecutorService executorService = Executors.newFixedThreadPool(4, new NamedThreadFactory("tes"));

    @Test
    public void task() {
        userTaskConfiguration.send();
    }

    @Test
    public void run() throws InterruptedException {
        TenantHolder.setTenantCode("bit");
        connectorService.syncData("mysqltest");
//        connectorService.syncData("测试数据库");

        Thread.sleep(1000000000);
    }


    @Test
    public void testDeleteUser() {
        TenantHolder.setTenantCode("bit");
        Set<String> set = new HashSet<>();

        set.add("zhangsan6");
        set.add("zhangsan5");
        iUserService.removeUserByUid(set);
        try {
            Thread.sleep(5000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    void testLock() {
        String key = String.format(RedisKeyConstants.CONNECTOR_SYNC_TENANT_LOCK_KEY, TenantHolder.getTenantCode());
        RLock lock = redissonClient.getLock(key);
        try {
            if (new Random().nextInt(2) == 1) {
                throw new Exception("出错了");
            }
            if (!lock.tryLock(1, TimeUnit.SECONDS)) {
                throw new UserCenterException(TransactionErrorType.DS_SYNC_CURRENT_JOB_PROCESSING_ERROR);
            }

            System.out.println(Thread.currentThread().getId() + "get lock finished");

            Thread.sleep(10000);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                System.out.println(Thread.currentThread().getId() + "clean lock");
                lock.unlock();
            } catch (Exception ex) {
            }
        }
    }
}
