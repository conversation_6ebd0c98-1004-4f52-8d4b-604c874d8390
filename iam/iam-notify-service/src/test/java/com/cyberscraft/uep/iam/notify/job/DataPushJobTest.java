package com.cyberscraft.uep.iam.notify.job;

import com.cyberscraft.uep.iam.IamNotifyApplication;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;


/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2019-08-20 18:37
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = IamNotifyApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class DataPushJobTest {
    //@Autowired
    //private DataPushMQJobXXL dataPushJob;

    @Test
    public void getTenantList() {
        //dataPushJob.retryDataPush();
        //try {
        //    Thread.sleep(5000);
        //} catch (InterruptedException e) {
        //    e.printStackTrace();
        //}
    }
}