package com.cyberscraft.uep.iam.notify.push;

import com.cyberscraft.uep.starter.dds.TenantHolder;
import com.cyberscraft.uep.iam.IamNotifyApplication;
import com.cyberscraft.uep.iam.entity.PushConnectorEntity;
import com.cyberscraft.uep.iam.service.data.IPushDataCacheService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import javax.annotation.Resource;
import java.util.List;

/***
 * 测试推送相关缓存
 * @date 2021/7/3
 * <AUTHOR>
 ***/
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = IamNotifyApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class TestPushCache {

    @Resource
    private IPushDataCacheService pushDataCacheService;

    @Test
    public void run() {
        TenantHolder.setTenantCode("iam");
        List<PushConnectorEntity> list1 = pushDataCacheService.getPushConnectors();
        System.out.println(list1 != null ? list1.size() : 0);


        List<PushConnectorEntity> list2 = pushDataCacheService.getPushConnectors();
        System.out.println(list2 != null ? list2.size() : 0);
        try {
            Thread.sleep(1000L);
        } catch (Exception e) {

        }
        List<PushConnectorEntity> list3 = pushDataCacheService.getPushConnectors();
        System.out.println(list3 != null ? list3.size() : 0);
    }
}
