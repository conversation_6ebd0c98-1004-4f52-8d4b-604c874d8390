package com.cyberscraft.uep.iam.notify.job;

import com.cyberscraft.uep.iam.dbo.SysTenantDBO;
import com.cyberscraft.uep.iam.service.data.IDataPushRetryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;
import org.springframework.stereotype.Component;

/**
 * <p>
 * 推送数据失败的补偿任务,针对在业务数据变化，发送到消息中间件失败的场景
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2019-08-20 18:24
 */
@Component
@Conditional(DataPushTaskCondition.class)
public class DataPushMQJob implements SchedulingConfigurer {
    private static final Logger LOGGER = LoggerFactory.getLogger(DataPushMQJob.class);
    @Autowired
    private IDataPushRetryService dataPushRetryService;
    @Autowired
    private Environment environment;
    @Autowired
    private SysTenantDBO sysTenantDBO;


    @Override
    public void configureTasks(ScheduledTaskRegistrar taskRegistrar) {
//        TODO 熊祥众进行屏蔽处理，因为暂时不用对应的逻辑
//        taskRegistrar.addTriggerTask(() -> {
//            handJob();
//        }, triggerContext -> {
//            CronTrigger trigger = new CronTrigger(environment.getProperty(ConfigKeyConstants.DATA_PUSH_JOB_MQ_CRON));
//            Date nextExecDate = trigger.nextExecutionTime(triggerContext);
//            return nextExecDate;
//        });
    }


    private void handJob() {
//        LOGGER.info("data push to mq job begin runing...");
//        List<SysTenantEntity> result = sysTenantDBO.list();
//        if (result == null) {
//            return ;
//        }
//        for (SysTenantEntity tenantEntity : result) {
//            if(StringUtils.isBlank(tenantEntity.getTenantId())){
//                continue;
//            }
//            dataPushRetryService.retryPushMQ(tenantEntity.getTenantId());
//        }
    }

}
