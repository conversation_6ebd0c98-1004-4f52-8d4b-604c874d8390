//package com.cyberscraft.uep.iam.notify.config.web;
//
//import org.springframework.beans.factory.InitializingBean;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.web.filter.GenericFilterBean;
//
//import javax.servlet.FilterChain;
//import javax.servlet.ServletException;
//import javax.servlet.ServletRequest;
//import javax.servlet.ServletResponse;
//import javax.servlet.http.HttpServletResponse;
//import java.io.IOException;
//
///**
// * CORSFilter
// *
// * <AUTHOR>
// * @date 2021/3/27
// */
//TODO 和iam-service集成部署时不需要此类
//@Configuration
//public class CORSFilter extends GenericFilterBean implements InitializingBean {
//
//    @Value("${dds.general.tenantHeaderName}")
//    private String tcode;
//
//    /**
//     * The <code>doFilter</code> method of the Filter is called by the container
//     * each time a request/response pair is passed through the chain due to a
//     * client request for a resource at the end of the chain. The FilterChain
//     * passed in to this method allows the Filter to pass on the request and
//     * response to the next entity in the chain.
//     * <p>
//     * A typical implementation of this method would follow the following
//     * pattern:- <br>
//     * 1. Examine the request<br>
//     * 2. Optionally wrap the request object with a custom implementation to
//     * filter content or headers for input filtering <br>
//     * 3. Optionally wrap the response object with a custom implementation to
//     * filter content or headers for output filtering <br>
//     * 4. a) <strong>Either</strong> invoke the next entity in the chain using
//     * the FilterChain object (<code>chain.doFilter()</code>), <br>
//     * 4. b) <strong>or</strong> not pass on the request/response pair to the
//     * next entity in the filter chain to block the request processing<br>
//     * 5. Directly set headers on the response after invocation of the next
//     * entity in the filter chain.
//     *
//     * @param request  The request to process
//     * @param servletResponse The response associated with the request
//     * @param chain    Provides access to the next filter in the chain for this
//     *                 filter to pass the request and response to for further
//     *                 processing
//     * @throws IOException      if an I/O error occurs during this filter's
//     *                          processing of the request
//     * @throws ServletException if the processing fails for any other reason
//     */
//    @Override
//    public void doFilter(ServletRequest request, ServletResponse servletResponse, FilterChain chain) throws IOException, ServletException {
//        HttpServletResponse response = (HttpServletResponse)servletResponse;
//        response.setHeader("Access-Control-Allow-Origin", "*");
//        response.setHeader("Access-Control-Allow-Methods", "HEAD,POST,GET,PUT,OPTIONS,PATCH,DELETE,TRACE");
//        response.setHeader("Access-Control-Max-Age", "3600");
//        response.setHeader("Access-Control-Allow-Headers", "Origin,X-Requested-With,Content-Type,Accept,UCSSO,SID,Authorization,Pragma,"+tcode);
//        response.setHeader("Access-Control-Allow-Credentials", "true");
//        chain.doFilter(request, servletResponse);
//
//    }
//}
