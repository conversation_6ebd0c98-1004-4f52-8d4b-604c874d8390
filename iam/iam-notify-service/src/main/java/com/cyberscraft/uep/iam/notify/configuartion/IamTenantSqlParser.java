/*
package com.cyberscraft.uep.iam.notify.configuartion;

import com.baomidou.mybatisplus.core.toolkit.ExceptionUtils;
import com.baomidou.mybatisplus.extension.plugins.tenant.TenantHandler;
import com.baomidou.mybatisplus.extension.plugins.tenant.TenantSqlParser;
import com.cyberscraft.uep.common.util.CollectionUtils;
import net.sf.jsqlparser.expression.operators.relational.ExpressionList;
import net.sf.jsqlparser.expression.operators.relational.ItemsList;
import net.sf.jsqlparser.expression.operators.relational.MultiExpressionList;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.statement.insert.Insert;
import net.sf.jsqlparser.statement.select.PlainSelect;


*/
/**
 * <p>
 *     当sql的select字段为空或者为*时，MP会报空指针异常，所以扩展此类解决该问题
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2020-03-11 17:32
 *//*

public class IamTenantSqlParser extends TenantSqlParser {
    private TenantHandler iamTenantHandler;

    @Override
    public void processInsert(Insert insert) {
        if (iamTenantHandler.doTableFilter(insert.getTable().getName())) {
            // 过滤退出执行
            return;
        }
        //针对insert into table select 的场景
        if(insert.getColumns() == null){
            if (insert.getSelect() != null) {
                PlainSelect plainSelect = (PlainSelect) insert.getSelect().getSelectBody();
                boolean addColumn = true;
                //原有MP，在select * from table 时，会拼成select *,tenantId from table的SQL，导致出错。
                if(!CollectionUtils.isEmpty(plainSelect.getSelectItems()) && "*".equals(String.valueOf(plainSelect.getSelectItems().get(0)))){
                    addColumn = false;
                }
                processPlainSelect(plainSelect, addColumn);
            } else if (insert.getItemsList() != null) {
                ItemsList itemsList = insert.getItemsList();
                if (itemsList instanceof MultiExpressionList) {
                    ((MultiExpressionList) itemsList).getExprList().forEach(el -> el.getExpressions().add(iamTenantHandler.getTenantId(false)));
                } else {
                    ((ExpressionList) insert.getItemsList()).getExpressions().add(iamTenantHandler.getTenantId(false));
                }
            } else {
                throw ExceptionUtils.mpe("Failed to process multiple-table update, please exclude the tableName or statementId");
            }
            return;
        }

        // 判断insert语句里面，是否包含有字段tenant_id
        boolean hasTenantId = false;
        for(int i = insert.getColumns().size() - 1; i >= 0; i--) {
            Column column = insert.getColumns().get(i);
            if(column.getColumnName().equalsIgnoreCase(iamTenantHandler.getTenantIdColumn())) {
                hasTenantId = true;
                break;
            }
        }

        if(hasTenantId) {
            // 如果已经包含tenant_id字段，直接返回
            return;
        }
        super.processInsert(insert);
    }


    public TenantHandler getIamTenantHandler() {
        return iamTenantHandler;
    }

    public void setIamTenantHandler(TenantHandler iamTenantHandler) {
        this.iamTenantHandler = iamTenantHandler;
        super.setTenantHandler(iamTenantHandler);
    }
}*/
