package com.cyberscraft.uep.iam.notify.initializer;

import com.cyberscraft.uep.iam.service.connector.task.AggregateTaskMonitor;
import com.cyberscraft.uep.iam.service.connector.task.IamSnowflakeChecker;
import com.cyberscraft.uep.iam.service.connector.task.PushTaskMonitor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/29 8:41 下午
 */
@Component
public class TaskInitializer implements ApplicationRunner {
    @Autowired
    private AggregateTaskMonitor aggregateTaskMonitor;

    @Autowired
    private PushTaskMonitor pushTaskMonitor;

    @Autowired
    private IamSnowflakeChecker iamSnowflakeChecker;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        aggregateTaskMonitor.run();

        pushTaskMonitor.run();

        iamSnowflakeChecker.run();
    }
}
