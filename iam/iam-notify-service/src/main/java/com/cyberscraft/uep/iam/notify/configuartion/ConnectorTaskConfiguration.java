package com.cyberscraft.uep.iam.notify.configuartion;

import com.cyberscraft.uep.iam.constants.ConfigKeyConstants;
import com.cyberscraft.uep.iam.dto.response.SysTenantVo;
import com.cyberscraft.uep.iam.service.ITenantService;
import com.cyberscraft.uep.iam.service.config.ISnsConfigService;
import com.cyberscraft.uep.iam.service.connector.IConnectorService;
import com.cyberscraft.uep.iam.service.data.IPushConnectorService;
import com.cyberscraft.uep.iam.service.data.IPushDataConsumerService;
import com.cyberscraft.uep.iam.service.data.IRepushService;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;
import org.springframework.scheduling.support.CronTrigger;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/***
 * IAM 任务相关配置
 * @date 2021/5/22
 * <AUTHOR>
 ***/
@Configuration
@EnableScheduling  // 1.开启定时任务
@EnableAsync
@Conditional(ConnectorTaskCondition.class)
public class ConnectorTaskConfiguration implements SchedulingConfigurer {

    @Resource
    private Environment environment;

    @Resource
    private ITenantService tenantService;

    @Resource
    private IConnectorService connectorService;

    @Resource
    private IPushConnectorService pushConnectorService;

    @Resource
    private IPushDataConsumerService pushDataConsumerService;

    @Resource
    private IRepushService repushService;

    @Resource
    private ISnsConfigService snsConfigService;

    /***
     *
     */
    private final static Logger LOG = LoggerFactory.getLogger(ConnectorTaskConfiguration.class);

    @Override
    public void configureTasks(ScheduledTaskRegistrar taskRegistrar) {
        taskRegistrar.addTriggerTask(() -> {
            handJob();
        }, triggerContext -> {
            CronTrigger trigger = new CronTrigger(environment.getProperty(ConfigKeyConstants.CONNECTOR_SYNC_CRON));
            Date nextExecDate = trigger.nextExecutionTime(triggerContext);
            return nextExecDate;
        });
    }

    void handJob() {
        //得到所有的租户
        List<SysTenantVo> list = tenantService.getTenantList();

        if (list == null || list.isEmpty()) {
            LOG.info("connector task running but tenant  list is null");
            return;
        }
        for (SysTenantVo tenantInfo : list) {
            TenantHolder.setTenantCode(tenantInfo.getTenantId());
            connectorService.sync(tenantInfo);

            TenantHolder.setTenantCode(tenantInfo.getTenantId());
            //进行当前租户下面的所有连接器的数据推送处理
            pushConnectorService.periodSync(false);

            TenantHolder.setTenantCode(tenantInfo.getTenantId());
            //进行已经存储到数据的事件，但是未发送到MQ的失败事件处理
            pushDataConsumerService.rePushUnPushedMessage();

//            TenantHolder.setTenantCode(tenantInfo.getTenantId());
//            //进行当前租户下面的所有北向连接器的推送失败重试处理
//            repushService.rePushFailureRecord();
        }
    }
}
