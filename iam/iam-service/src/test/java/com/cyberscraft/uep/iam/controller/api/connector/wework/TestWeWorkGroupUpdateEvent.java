package com.cyberscraft.uep.iam.controller.api.connector.wework;

import com.cyberscraft.uep.account.client.domain.SnsConfig;
import com.cyberscraft.uep.iam.entity.SnsConfigEntity;
import com.cyberscraft.uep.iam.service.config.ISnsConfigService;
import com.cyberscraft.uep.iam.service.config.transfer.SnsConfigTransfer;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import com.cyberscraft.uep.account.client.constant.ThirdPartyAccountType;
import com.cyberscraft.uep.account.client.domain.Connector;
import com.cyberscraft.uep.account.client.domain.ThirdPartyCallBackMessage;
import com.cyberscraft.uep.account.client.provider.wework.aes.SHA1;
import com.cyberscraft.uep.account.client.provider.wework.aes.WXBizMsgCrypt;
import com.cyberscraft.uep.account.client.provider.wework.domain.WeWorkConfig;
import com.cyberscraft.uep.account.client.service.IThirdPartyMessageService;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.iam.IamApplication;
import com.cyberscraft.uep.iam.service.connector.IConnectorService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import javax.annotation.Resource;

/***
 * 企业微信组修改事件
 * @date 2021/3/25
 * <AUTHOR>
 ***/
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = IamApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class TestWeWorkGroupUpdateEvent {

    String xml = "<xml>\n" +
            "    <ToUserName><![CDATA[wwfc30a793d411d5a4]]></ToUserName>\n" +
            "    <FromUserName><![CDATA[sys]]></FromUserName> \n" +
            "    <CreateTime>**********</CreateTime>\n" +
            "    <MsgType><![CDATA[event]]></MsgType>\n" +
            "    <Event><![CDATA[change_contact]]></Event>\n" +
            "    <ChangeType>update_party</ChangeType>\n" +
            "    <Id>101</Id>\n" +
            "    <Name><![CDATA[吹风部2]]></Name>\n" +
            "    <ParentId><![CDATA[1]]></ParentId>\n" +
            "    <Order>100</Order>\n" +
            "</xml>";

    @Resource
    private IConnectorService connectorService;

    @Resource
    private ISnsConfigService snsConfigService;

    @Resource
    private SnsConfigTransfer snsConfigTransfer;

    @Resource
    private IThirdPartyMessageService thirdPartyMessageService;

    @Test
    public void run() throws Exception {
        String tenantId = "1";
        TenantHolder.setTenantCode(tenantId);

        Connector connector = connectorService.getConnector(1245989233848492034L);

        SnsConfigEntity aDefault = snsConfigService.getDefault(Integer.valueOf(ThirdPartyAccountType.WEWORK.getCode()));
        SnsConfig snsConfig = snsConfigTransfer.entityToVo(aDefault);

        WeWorkConfig config = JsonUtil.str2Obj(connector.getConfig(), WeWorkConfig.class);

        WXBizMsgCrypt msgCrypt = new WXBizMsgCrypt(config.getToken(), config.getAesKey(), config.getCorpId());

        String timestamp = String.valueOf(System.currentTimeMillis());
        String nonce = String.valueOf(System.currentTimeMillis());


        String msgBody = msgCrypt.encrypt(msgCrypt.getRandomStr(), xml);

        String msgSignature = SHA1.getSHA1(config.getToken(), timestamp, nonce, msgBody);

        ThirdPartyCallBackMessage msg = new ThirdPartyCallBackMessage();
        //msg.setConnectorId(connectorId);
        msg.setTenantId(tenantId);
        msg.setMsgBody(msgBody);
        msg.setNonce(nonce);
        msg.setMsgSignature(msgSignature);
        msg.setTimestamp(timestamp);
        msg.setAccountType(ThirdPartyAccountType.WEWORK.getCode());
        String rs = thirdPartyMessageService.deal(msg, snsConfig);
        Thread.sleep(1000000);
        System.out.println(rs);
    }
}
