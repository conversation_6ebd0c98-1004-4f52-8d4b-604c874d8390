package com.cyberscraft.uep.iam.sysConfig;

import com.cyberscraft.uep.iam.IamApplication;
import com.cyberscraft.uep.iam.common.domain.configparams.CfgPoliciesAuditLog;
import com.cyberscraft.uep.iam.service.IAuditlogService;
import com.cyberscraft.uep.iam.service.IConfigService;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;


/**
 *
 * 系统配置
 * <AUTHOR>
 * @version uep
 * @since 2019-10-24 10:35
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = IamApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class SysConfigTest {
    @Autowired
    private IAuditlogService auditlogService;
    @Autowired
    IConfigService iConfigService;


    @Test
    public void deleteAuditlogTest() {
        TenantHolder.setTenantCode("bit");
        CfgPoliciesAuditLog auditLogPolicy = iConfigService.getAuditLogPolicyCfg();
        auditlogService.delExpiredLog(auditLogPolicy);
    }

}