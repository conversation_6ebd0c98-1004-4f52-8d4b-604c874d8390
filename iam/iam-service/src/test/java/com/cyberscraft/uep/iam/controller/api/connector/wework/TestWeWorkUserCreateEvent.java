package com.cyberscraft.uep.iam.controller.api.connector.wework;

import com.cyberscraft.uep.account.client.domain.SnsConfig;
import com.cyberscraft.uep.iam.entity.SnsConfigEntity;
import com.cyberscraft.uep.iam.service.config.ISnsConfigService;
import com.cyberscraft.uep.iam.service.config.transfer.SnsConfigTransfer;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import com.cyberscraft.uep.account.client.constant.ThirdPartyAccountType;
import com.cyberscraft.uep.account.client.domain.Connector;
import com.cyberscraft.uep.account.client.domain.ThirdPartyCallBackMessage;
import com.cyberscraft.uep.account.client.provider.wework.aes.SHA1;
import com.cyberscraft.uep.account.client.provider.wework.aes.WXBizMsgCrypt;
import com.cyberscraft.uep.account.client.provider.wework.domain.WeWorkConfig;
import com.cyberscraft.uep.account.client.service.IThirdPartyMessageService;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.iam.IamApplication;
import com.cyberscraft.uep.iam.service.connector.IConnectorService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import javax.annotation.Resource;

/***
 * 测试微信加创建更新用户事件
 * @date 2021/3/24
 * <AUTHOR>
 ***/
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = IamApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class TestWeWorkUserCreateEvent {

    String xml = "<xml>\n" +
            "    <ToUserName><![CDATA[wwfc30a793d411d5a4]]></ToUserName>\n" +
            "    <FromUserName><![CDATA[sys]]></FromUserName> \n" +
            "    <CreateTime>**********</CreateTime>\n" +
            "    <MsgType><![CDATA[event]]></MsgType>\n" +
            "    <Event><![CDATA[change_contact]]></Event>\n" +
            "    <ChangeType>create_user</ChangeType>\n" +
            "    <UserID><![CDATA[xiongxzh]]></UserID>\n" +
            "    <Name><![CDATA[熊祥众]]></Name>\n" +
            "    <Department><![CDATA[1]]></Department>\n" +
            "    <IsLeaderInDept><![CDATA[0]]></IsLeaderInDept>\n" +
            "    <Position><![CDATA[不知道]]></Position>\n" +
            "    <Mobile>13800000000</Mobile>\n" +
            "    <Gender>1</Gender>\n" +
            "    <Email><![CDATA[<EMAIL>]]></Email>\n" +
            "    <Status>1</Status>\n" +
            "    <Avatar><![CDATA[http://wx.qlogo.cn/mmopen/ajNVdqHZLLA3WJ6DSZUfiakYe37PKnQhBIeOQBO4czqrnZDS79FH5Wm5m4X69TBicnHFlhiafvDwklOpZeXYQQ2icg/0]]></Avatar>\n" +
            "    <Alias><![CDATA[xiongxzh]]></Alias>\n" +
            "    <Telephone><![CDATA[010-12345678]]></Telephone>\n" +
            "    <Address><![CDATA[北京市]]></Address>\n" +
            "    <ExtAttr>\n" +
            "        <Item>\n" +
            "        <Name><![CDATA[爱好]]></Name>\n" +
            "        <Type>0</Type>\n" +
            "        <Text>\n" +
            "            <Value><![CDATA[旅游]]></Value>\n" +
            "        </Text>\n" +
            "        </Item>\n" +
            "        <Item>\n" +
            "        <Name><![CDATA[卡号]]></Name>\n" +
            "        <Type>1</Type>\n" +
            "        <Web>\n" +
            "            <Title><![CDATA[企业微信]]></Title>\n" +
            "            <Url><![CDATA[https://work.weixin.qq.com]]></Url>\n" +
            "        </Web>\n" +
            "        </Item>\n" +
            "    </ExtAttr>\n" +
            "</xml>";

    String weworkCfgStr = "{\n" +
            "  \"aes_key\": \"HglsdDPtuqDzrdjM8CJ6MYR1fzoz5BX/aEc4XvfcutI\",\n" +
            "  \"agent_id\": \"111\",\n" +
            "  \"api_base_url\": \"https://cyberscraft-poc.gov.weixin.qq.com\",\n" +
            "  \"app_key\": \"dingbnj3iwjtu9ktk12b\",\n" +
            "  \"app_secret\": \"yVhOsoOKC-ATMSupa-A1dB1ALw5HdU0NuNW3ZqFwBIc\",\n" +
            "  \"connector_id\": 1237648779289694209,\n" +
            "  \"corp_id\": \"wwfc30a793d411d5a4\",\n" +
            "  \"register_base_url\": \"http://iam.vaiwan.com/iam/api/open/sns_callback\",\n" +
            "  \"register_url_params\": \"tcode=100&connectorId=1237648779289694209\",\n" +
            "  \"tcode\": \"100\",\n" +
            "  \"token\": \"083508\",\n" +
            "  \"auth_config\": {\n" +
            "    \"app_id\": \"1000008\",\n" +
            "    \"app_secret\": \"yVhOsoOKC-ATMSupa-A1dB1ALw5HdU0NuNW3ZqFwBIc\",\n" +
            "    \"sch\": \"wwauthfc30a793d411d5a4000008\"\n" +
            "  }\n" +
            "}";


    @Resource
    private IConnectorService connectorService;

    @Resource
    private ISnsConfigService snsConfigService;

    @Resource
    private SnsConfigTransfer snsConfigTransfer;

    @Resource
    private IThirdPartyMessageService thirdPartyMessageService;

    @Test
    public void run() throws Exception {
        String tenantId = "1";
        TenantHolder.setTenantCode(tenantId);

        Connector connector = connectorService.getConnector(1245989233848492034L);

        SnsConfigEntity aDefault = snsConfigService.getDefault(Integer.valueOf(ThirdPartyAccountType.WEWORK.getCode()));
        SnsConfig snsConfig = snsConfigTransfer.entityToVo(aDefault);

        WeWorkConfig config = JsonUtil.str2Obj(connector.getConfig(), WeWorkConfig.class);

        WXBizMsgCrypt msgCrypt = new WXBizMsgCrypt(config.getToken(), config.getAesKey(), config.getCorpId());

        String timestamp = String.valueOf(System.currentTimeMillis());
        String nonce = String.valueOf(System.currentTimeMillis());


        String msgBody = msgCrypt.encrypt(msgCrypt.getRandomStr(), xml);

        String msgSignature = SHA1.getSHA1(config.getToken(), timestamp, nonce, msgBody);

        ThirdPartyCallBackMessage msg = new ThirdPartyCallBackMessage();
        //msg.setConnectorId(connectorId);
        msg.setTenantId(tenantId);
        msg.setMsgBody(msgBody);
        msg.setNonce(nonce);
        msg.setMsgSignature(msgSignature);
        msg.setTimestamp(timestamp);
        msg.setAccountType(ThirdPartyAccountType.WEWORK.getCode());
        String rs = thirdPartyMessageService.deal(msg, snsConfig);
        Thread.sleep(1000000);
        System.out.println(rs);
    }
}
