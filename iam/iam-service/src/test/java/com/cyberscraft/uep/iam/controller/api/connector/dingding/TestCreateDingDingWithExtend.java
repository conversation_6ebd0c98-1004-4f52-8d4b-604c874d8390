package com.cyberscraft.uep.iam.controller.api.connector.dingding;

import com.cyberscraft.uep.account.client.domain.*;
import com.cyberscraft.uep.account.client.provider.dingding.config.DingTalkClientConfig;
import com.cyberscraft.uep.account.client.provider.dingding.domain.DingTalkConfig;
import com.cyberscraft.uep.account.client.service.IThirdPartyAccountService;
import com.cyberscraft.uep.common.constant.SysConstant;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.common.util.SnowflakeIDUtil;
import com.cyberscraft.uep.iam.IamApplication;
import com.cyberscraft.uep.iam.dto.enums.ConnectorTypeEnum;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/***
 *
 * @date 2021/6/22
 * <AUTHOR>
 ***/
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = IamApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class TestCreateDingDingWithExtend {

    private static Connector connector;
    private static String tenantId = "1";
    private static DingTalkConfig config = null;

    @Resource
    private DingTalkClientConfig dingTalkClientConfig;

    @Resource
    private IThirdPartyAccountService thirdPartyAccountService;

    @BeforeAll
    private static void init() {
        config = new DingTalkConfig();
        config.setCorpId("ding0cb5e33d8e2abd7a35c2f4657eb6378f");
        config.setAppKey("dingbnj3iwjtu9ktk12b");
        config.setAppSecret("3ToxHsrFjHDL4uv82MYCPzwPXqPWlhhyGaajZMXrXRcR63_pWqAN8gAnItlRZmip");
        config.setApiBaseUrl("https://oapi.dingtalk.com/");

        connector = new Connector();
        connector.setType(ConnectorTypeEnum.DINGDING.getValue());
        connector.setTenantId(tenantId);
        connector.setOrgAttrMapping(JsonUtil.str2Obj("{\"myextend\":\"myextend\",\"myorgextend\":\"myorgextend\"}", ConnectorOrgMapping.class));
        connector.setUserAttrMapping(JsonUtil.str2Obj("{{\"myuserextend\":\"myuserextend\"}}", ConnectorUserMapping.class));
        connector.setConfig(JsonUtil.obj2Str(config));
    }

    @Test
    public void testCreateGroup() {

        ThirdPartyGroup createGroup = new ThirdPartyGroup();
        createGroup.setName("熊祥众测试部门");
        //createGroup.setParentCode("*********");
        createGroup.setStatus(SysConstant.TRUE);
        createGroup.setParentCode(dingTalkClientConfig.getRootGroupCode());
        Map<String, Object> extendsMap = new HashMap<>();
        extendsMap.put("myextend", "第二个扩展");
        extendsMap.put("myorgextend", "第二个企业扩展");
        createGroup.setExtAttributes(extendsMap);
//
        String value = thirdPartyAccountService.createGroup(connector.getTenantId(), String.valueOf(connector.getType()), createGroup, connector);
        System.out.println(value);

        //thirdPartyAccountService.removeGroup(connector.getTenantId(), String.valueOf(connector.getType()), Arrays.asList(value), connector);

//        ThirdPartyGroup serverGroup = thirdPartyAccountService.getGroupByCode(connector.getTenantId(), String.valueOf(connector.getType()), "*********", connector);
//        System.out.println(serverGroup.getExtAttributes() != null ? serverGroup.getExtAttributes().size() : "空");

//        List<ThirdPartyGroup> list = thirdPartyAccountService.getSubGroups(connector.getTenantId(), String.valueOf(connector.getType()), "*********", connector);
//        System.out.println(list != null ? list.size() : 0);
    }


    @Test
    public void testCreateUser() {

        ThirdPartyAccount createUser = new ThirdPartyAccount();
        createUser.setLoginId("熊祥众测试用户名4");
        createUser.setAccountId(SnowflakeIDUtil.getIdStr());

        //createGroup.setParentCode(dingTalkClientConfig.getRootGroupCode());
        Map<String, Object> extendsMap = new HashMap<>();
        extendsMap.put("myextend", "第二个扩展");
        extendsMap.put("myorgextend", "第二个企业扩展");
        extendsMap.put("mobile", "***********");
        createUser.setExtAttributes(extendsMap);

        ThirdPartyGroupPosition position = new ThirdPartyGroupPosition();
        position.setName(null);
        position.setCode("*********");
        createUser.setGroupPositions(Arrays.asList(position));
////
        String value = thirdPartyAccountService.createAccount(connector.getTenantId(), String.valueOf(connector.getType()), createUser, connector);
        System.out.println(value);
//
        ThirdPartyAccount user = thirdPartyAccountService.getAccountByUserId(connector.getTenantId(), String.valueOf(connector.getType()), value, connector);
        System.out.println(user.getAccountId() + "----" + user.getUserId());
//
        List<ThirdPartyAccount> userList = thirdPartyAccountService.getAccountsByGroup(connector.getTenantId(), String.valueOf(connector.getType()), "*********", connector);
        System.out.println(userList.size());

        thirdPartyAccountService.removeAccount(connector.getTenantId(), String.valueOf(connector.getType()), Arrays.asList(value), connector);

        List<ThirdPartyAccount> userList2 = thirdPartyAccountService.getAccountsByGroup(connector.getTenantId(), String.valueOf(connector.getType()), "*********", connector);
        System.out.println(userList2.size());

//        ThirdPartyGroup serverGroup = thirdPartyAccountService.getGroupByCode(connector.getTenantId(), String.valueOf(connector.getType()), "*********", connector);
//        System.out.println(serverGroup.getExtAttributes() != null ? serverGroup.getExtAttributes().size() : "空");

//        List<ThirdPartyGroup> list = thirdPartyAccountService.getSubGroups(connector.getTenantId(), String.valueOf(connector.getType()), "*********", connector);
//        System.out.println(list != null ? list.size() : 0);
    }
}
