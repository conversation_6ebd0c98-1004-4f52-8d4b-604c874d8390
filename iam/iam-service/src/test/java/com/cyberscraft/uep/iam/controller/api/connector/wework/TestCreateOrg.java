package com.cyberscraft.uep.iam.controller.api.connector.wework;

import com.cyberscraft.uep.starter.dds.TenantHolder;
import com.cyberscraft.uep.account.client.domain.Connector;
import com.cyberscraft.uep.common.constant.SysConstant;
import com.cyberscraft.uep.common.util.SnowflakeIDUtil;
import com.cyberscraft.uep.iam.IamApplication;
import com.cyberscraft.uep.iam.entity.OrgEntity;
import com.cyberscraft.uep.iam.dbo.OrgDBO;
import com.cyberscraft.uep.iam.service.connector.IConnectorService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/***
 *
 * @date 2021/3/24
 * <AUTHOR>
 ***/
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = IamApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class TestCreateOrg {
//
//    private IOrganizationService organizationService;


    @Resource
    private IConnectorService connectorService;


    @Resource
    private OrgDBO orgDBO;

    @Test
    public void run() {
        TenantHolder.setTenantCode("100");
        Connector connector = connectorService.getConnector(1237648779289694210L);

        OrgEntity org = new OrgEntity();
        org.setId(SnowflakeIDUtil.getId());
        org.setParentRefId(SysConstant.DEFAULT_ROOT_GROUP_PARENTID);
        org.setOrgPath(String.valueOf(org.getParentRefId()));
        org.setCreateTime(LocalDateTime.now());
        org.setConnectorId(connector.getId());
        org.setName("微信测试");
        org.setStatus(SysConstant.TRUE_VALUE);
        org.setCreateBy("system");
        org.setUpdateTime(LocalDateTime.now());
        orgDBO.save(org);
    }
}
