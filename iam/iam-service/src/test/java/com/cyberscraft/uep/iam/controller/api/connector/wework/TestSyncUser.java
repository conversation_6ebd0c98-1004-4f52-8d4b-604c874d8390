package com.cyberscraft.uep.iam.controller.api.connector.wework;

import com.cyberscraft.uep.starter.dds.TenantHolder;
import com.cyberscraft.uep.iam.IamApplication;
import com.cyberscraft.uep.iam.service.connector.IConnectorService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import javax.annotation.Resource;

/***
 *
 * 测试用户同步
 * @date 2021/3/23
 * <AUTHOR>
 ***/
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = IamApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class TestSyncUser {

    @Resource
    private IConnectorService connectorService;

    @Test
    public void run() {
        TenantHolder.setTenantCode("bit");
        //Long connectorId = 1237648779289694210L;
        connectorService.syncData( "feishu");
    }
}
