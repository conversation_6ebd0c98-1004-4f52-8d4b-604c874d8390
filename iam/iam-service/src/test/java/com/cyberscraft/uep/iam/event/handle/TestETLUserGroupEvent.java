package com.cyberscraft.uep.iam.event.handle;

import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountErrorType;
import com.cyberscraft.uep.aksk.server.aes.AKSKEncryptor;
import com.cyberscraft.uep.aksk.server.aes.SHA1;
import com.cyberscraft.uep.aksk.server.constants.AKSKConstant;
import com.cyberscraft.uep.common.bean.Result;
import com.cyberscraft.uep.common.util.JsonUtil;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static com.cyberscraft.uep.account.client.exception.ThirdPartyAccountErrorType.*;

/***
 * 测试创建，删除，用户和组
 * @date 2021/8/13
 * <AUTHOR>
 ***/
public class TestETLUserGroupEvent extends BaseTestRequest {

    private static final String url = "http://192.168.50.10:8089/iam/api/open/etl_message";

    private static final String test_url = url+"/test";

    private static final String TENANT_ID = "testbit";

    private static final String USER_CODE = "*********";
    private static final String USER_CODE_2 = "*********";
    private static final String USER_CODE_OVERRIDE = "*********";
    private static final String NAME = "杨望星";
    private static final String NAME_2 = "杨望星";
    private static final String IDENTIFIER = "1111";
    private static final String IDENTIFIER2 = "2222";
    private static final String MOBILE = "***********";
    private static final String MOBILE_2 = "***********";
    private static final String POSITION = "学生";
    private static final String ORG_PATH = "/计算机工程学院/学生";
    private static final String POSITION2 = "教师";
    private static final String ORG_PATH2 = "/计算机工程学院/教师";
    private static final String TEST_DATA = "test_data";


    private static final String ADD_USER_REQUEST = "{\"userCode\":\"" + USER_CODE + "\"" +
            ", \"name\":\"" + NAME + "\"" +
            ",\"identifier\":\"" + IDENTIFIER + "\"" +
            ",\"mobile\":\"" + MOBILE + "\"" +
            ",\"position\":\"" + POSITION + "\"" +
            ",\"orgPath\":\"" + ORG_PATH + "\"" +
            ",\"eventType\":\"ADD_UPDATE_USER\"}";

    private static final String ADD_USER2_REQUEST = "{\"userCode\":\"" + USER_CODE_2 + "\"" +
            ", \"name\":\"" + NAME_2 + "\"" +
            ",\"identifier\":\"" + IDENTIFIER2 + "\"" +
            ",\"mobile\":\"" + MOBILE_2 + "\"" +
            ",\"position\":\"" + POSITION + "\"" +
            ",\"orgPath\":\"" + ORG_PATH + "\"" +
            ",\"eventType\":\"ADD_UPDATE_USER\"}";

    private static final String ADD_USER_REQUEST_NO_USER_CODE = "{" +
            " \"name\":\"" + NAME + "\"" +
            ",\"identifier\":\"" + IDENTIFIER + "\"" +
            ",\"mobile\":\"" + MOBILE + "\"" +
            ",\"position\":\"" + POSITION + "\"" +
            ",\"orgPath\":\"" + ORG_PATH + "\"" +
            ",\"eventType\":\"ADD_UPDATE_USER\"}";

    private static final String ADD_USER_REQUEST_NO_NAME = "{\"userCode\":\"" + USER_CODE + "\"" +
            ",\"identifier\":\"" + IDENTIFIER + "\"" +
            ",\"mobile\":\"" + MOBILE + "\"" +
            ",\"position\":\"" + POSITION + "\"" +
            ",\"orgPath\":\"" + ORG_PATH + "\"" +
            ",\"eventType\":\"ADD_UPDATE_USER\"}";

    private static final String ADD_USER_REQUEST_NO_IDENTIFIER = "{\"userCode\":\"" + USER_CODE + "\"" +
            ", \"name\":\"" + NAME + "\"" +
            ",\"mobile\":\"" + MOBILE + "\"" +
            ",\"position\":\"" + POSITION + "\"" +
            ",\"orgPath\":\"" + ORG_PATH + "\"" +
            ",\"eventType\":\"ADD_UPDATE_USER\"}";

    private static final String ADD_USER_REQUEST_NO_POSITION = "{\"userCode\":\"" + USER_CODE + "\"" +
            ", \"name\":\"" + NAME + "\"" +
            ",\"identifier\":\"" + IDENTIFIER + "\"" +
            ",\"mobile\":\"" + MOBILE + "\"" +
            ",\"orgPath\":\"" + ORG_PATH + "\"" +
            ",\"eventType\":\"ADD_UPDATE_USER\"}";

    private static final String ADD_USER_REQUEST_NO_MOBILE = "{\"userCode\":\"" + USER_CODE + "\"" +
            ", \"name\":\"" + NAME + "\"" +
            ",\"identifier\":\"" + IDENTIFIER + "\"" +
            ",\"position\":\"" + POSITION + "\"" +
            ",\"orgPath\":\"" + ORG_PATH + "\"" +
            ",\"eventType\":\"ADD_UPDATE_USER\"}";

    private static final String ADD_USER_REQUEST_NO_ORG_PATH = "{\"userCode\":\"" + USER_CODE + "\"" +
            ", \"name\":\"" + NAME + "\"" +
            ",\"identifier\":\"" + IDENTIFIER + "\"" +
            ",\"mobile\":\"" + MOBILE + "\"" +
            ",\"position\":\"" + POSITION + "\"" +
            ",\"eventType\":\"ADD_UPDATE_USER\"}";

    private static final String ADD_USER_REQUEST2 = "{\"userCode\":\"" + USER_CODE + "\"" +
            ", \"name\":\"" + NAME + "\"" +
            ",\"identifier\":\"" + IDENTIFIER + "\"" +
            ",\"mobile\":\"" + MOBILE + "\"" +
            ",\"position\":\"" + POSITION2 + "\"" +
            ",\"orgPath\":\"" + ORG_PATH2 + "\"" +
            ",\"eventType\":\"ADD_UPDATE_USER\"}";

    private static final String ADD_USER_REQUEST_OVERRIDE_USER_CODE = "{\"userCode\":\"" + USER_CODE_OVERRIDE + "\"" +
            ", \"name\":\"" + NAME + "\"" +
            ",\"identifier\":\"" + IDENTIFIER + "\"" +
            ",\"mobile\":\"" + MOBILE + "\"" +
            ",\"position\":\"" + POSITION + "\"" +
            ",\"orgPath\":\"" + ORG_PATH + "\"" +
            ",\"eventType\":\"ADD_UPDATE_USER\"}";

    private static final String ADD_USER_REQUEST_SAME_USER_CODE_DIFF_IDENTIFIER = "{\"userCode\":\"" + USER_CODE_2 + "\"" +
            ", \"name\":\"" + NAME + "\"" +
            ",\"identifier\":\"" + IDENTIFIER + "\"" +
            ",\"mobile\":\"" + MOBILE + "\"" +
            ",\"position\":\"" + POSITION + "\"" +
            ",\"orgPath\":\"" + ORG_PATH + "\"" +
            ",\"eventType\":\"ADD_UPDATE_USER\"}";

    private static final String ADD_USER_REQUEST_SAME_MOBILE_DIFF_IDENTIFIER = "{\"userCode\":\"" + USER_CODE_OVERRIDE + "\"" +
            ", \"name\":\"" + NAME + "\"" +
            ",\"identifier\":\"" + IDENTIFIER2 + "\"" +
            ",\"mobile\":\"" + MOBILE + "\"" +
            ",\"position\":\"" + POSITION + "\"" +
            ",\"orgPath\":\"" + ORG_PATH + "\"" +
            ",\"eventType\":\"ADD_UPDATE_USER\"}";

    private static final String ADD_GROUP_REQUEST = "{" +
            "\"orgPath\":\"" + ORG_PATH + "\"" +
            ",\"eventType\":\"ADD_GROUP\"}";
    private static final String ADD_GROUP_REQUEST2 = "{" +
            "\"orgPath\":\"" + ORG_PATH2 + "\"" +
            ",\"eventType\":\"ADD_GROUP\"}";

    private static final String DELETE_GROUP_REQUEST1 = "{" +
            "\"orgPath\":\"" + ORG_PATH + "\"" +
            ",\"eventType\":\"DELETE_GROUP\"}";

    private static final String DELETE_GROUP_REQUEST2 = "{" +
            "\"orgPath\":\"" + ORG_PATH2 + "\"" +
            ",\"eventType\":\"DELETE_GROUP\"}";

    private static final String DELETE_USER = "{" +
            "\"identifier\":\"" + IDENTIFIER + "\"" +
            ",\"eventType\":\"DELETE_USER\"}";

    private static final String DELETE_USER_2 = "{" +
            "\"identifier\":\"" + IDENTIFIER2 + "\"" +
            ",\"eventType\":\"DELETE_USER\"}";

    private static final String DELETE_USER_NO_IDENTIFIER = "{" +
            "\"eventType\":\"DELETE_USER\"}";

    private static final String ADD_GROUP_REQUEST_NO_ORG_PATH = "{" +
            "\"eventType\":\"ADD_GROUP\"}";

    private static final String DELETE_GROUP_REQUEST_NO_ORG_PATH = "{" +
            "\"eventType\":\"DELETE_GROUP\"}";


    private static final String USER_ADD_UPDATE = "{\n" +
            "    \"identifier\":\"4019523c74f7b44a32fbaee5aa36c38b\",\n" +
            "    \"eventType\":\"ADD_UPDATE_USER\",\n" +
            "    \"userCode\":\"2120130682\",\n" +
            "    \"name\":\"刘安\",\n" +
            "    \"mobile\":\"18601235048\",\n" +
            "    \"position\":\"老师\",\n" +
            "    \"orgPath\":\"/研究生/2020\"\n" +
            "}";

    private static final String USER_ADD_UPDATE2 = "{\n" +
            "    \"identifier\":\"4019523c74f7b44a32fbaee5aa36c38b\",\n" +
            "    \"eventType\":\"ADD_UPDATE_USER\",\n" +
            "    \"userCode\":\"2120130683\",\n" +
            "    \"name\":\"刘安2\",\n" +
            "    \"mobile\":\"18601235048\",\n" +
            "    \"position\":\"老师\",\n" +
            "    \"orgPath\":\"/研究生/2021\"\n" +
            "}";

    private static final String DELETE_USER3 = "{" +
            "\"identifier\":\"4019523c74f7b44a32fbaee5aa36c38b\"" +
            ",\"eventType\":\"DELETE_USER\"}";

    @Test
    public void cleanData(){
//        expectSuccess(DELETE_USER);
//        expectSuccess(DELETE_GROUP_REQUEST1);
    }

    @Test
    public void testOne(){
//        Result<String> result = getResult(TEST_DATA, test_url);
//        Assert.assertEquals(TEST_DATA, result.getData());

//        expectSuccess(USER_ADD_UPDATE);
        expectSuccess(DELETE_USER3);
    }

    @Test
    public void test() {

        /**
         * 测试接口
         */
        Result<String> result = getResult(TEST_DATA, test_url);
        Assertions.assertEquals(TEST_DATA, result.getData());

        /**
         * 两个组不存在
         */
        expectError(DELETE_GROUP_REQUEST1, GROUP_NOT_EXIST);
        expectError(DELETE_GROUP_REQUEST2, GROUP_NOT_EXIST);

        /**
         * 添加用户缺少必填项
         */
        expectError(ADD_USER_REQUEST_NO_IDENTIFIER, MESSAGE_BODY_INVALID);
        expectError(ADD_USER_REQUEST_NO_NAME, MESSAGE_BODY_INVALID);
        expectError(ADD_USER_REQUEST_NO_MOBILE, MESSAGE_BODY_INVALID);
        expectError(ADD_USER_REQUEST_NO_ORG_PATH, MESSAGE_BODY_INVALID);
        expectError(ADD_USER_REQUEST_NO_POSITION, MESSAGE_BODY_INVALID);
        expectError(ADD_USER_REQUEST_NO_USER_CODE, MESSAGE_BODY_INVALID);

        /**
         * 删除用户缺少必填项
         */
        expectError(DELETE_USER_NO_IDENTIFIER, MESSAGE_BODY_INVALID);

        /**
         * 添加组缺少必填项
         */
        expectError(ADD_GROUP_REQUEST_NO_ORG_PATH, MESSAGE_BODY_INVALID);

        /**
         * 删除组缺少必填项
         */
        expectError(DELETE_GROUP_REQUEST_NO_ORG_PATH, MESSAGE_BODY_INVALID);

        /**
         * Case：自动创建组和用户
         */
        expectSuccess(ADD_USER_REQUEST);
        expectSuccess(ADD_USER2_REQUEST);

        /**
         * 覆盖原用户的userCode
         */
        expectSuccess(ADD_USER_REQUEST_OVERRIDE_USER_CODE);

        /**
         * 相同userCode，传入不同的identifier
         */
        expectError(ADD_USER_REQUEST_SAME_USER_CODE_DIFF_IDENTIFIER, MESSAGE_BODY_INVALID);

        /**
         * 相同mobile，传入不同的identifier
         */
        expectError(ADD_USER_REQUEST_SAME_MOBILE_DIFF_IDENTIFIER, MESSAGE_BODY_INVALID);

        /**
         * Case：创建组
         */
        expectSuccess(ADD_GROUP_REQUEST2);
        /**
         * Case：只创建用户
         */
        expectSuccess(ADD_USER_REQUEST2);
        /**
         * Case：组已经存在，该操作直接返回success
         */
        expectSuccess(ADD_GROUP_REQUEST);
        /**
         * Case：组下有用户，无法删除
         */
        expectError(DELETE_GROUP_REQUEST1, GROUP_NOT_ALLOW_TO_DELETE);
        /**
         * Case：组下有用户，无法删除
         */
        expectError(DELETE_GROUP_REQUEST2, GROUP_NOT_ALLOW_TO_DELETE);
        /**
         * Case：删除用户，不删除组
         */
        expectSuccess(DELETE_USER);
        expectSuccess(DELETE_USER_2);
        /**
         * Case：删除组
         */
        expectSuccess(DELETE_GROUP_REQUEST1);
        expectSuccess(DELETE_GROUP_REQUEST2);
        /**
         * Case：无法删除用户
         */
        expectError(DELETE_USER, USER_NOT_EXIST);
        /**
         * Case：无法删除组
         */
        expectError(DELETE_GROUP_REQUEST1, GROUP_NOT_EXIST);
        expectError(DELETE_GROUP_REQUEST2, GROUP_NOT_EXIST);
    }

    private void expectError(String request, ThirdPartyAccountErrorType expect) {
        Result<String> result = getResult(request, url);
        expect(expect, result);
    }

    private void expectSuccess(String request) {
        Result<String> result = getResult(request, url);
        Assertions.assertEquals("OK", result.getData());
    }

    private Result<String> getResult(String request, String url) {
        //因为没有参数，所以初始化，nonce,timestamp参数，并且进行签名处理
        String nonce = String.valueOf(System.nanoTime());
        String timestamp = String.valueOf(System.currentTimeMillis());
        System.out.println("nonce=" + nonce);
        System.out.println("timestamp=" + timestamp);

        Map<String, String> headerParameter = new HashMap<>();
        headerParameter.put("Content-Type", "application/json");
        headerParameter.put("tcode", TENANT_ID);

        Map<String, String> parameters = new HashMap<>();
        parameters.put(AKSKConstant.AK_PARAMETER_NAME, appItem.getAppKey());
        parameters.put(AKSKConstant.NONCE_PARAMETER_NAME, nonce);
        parameters.put(AKSKConstant.TIMESTAMP_PARAMETER_NAME, timestamp);

        AKSKEncryptor encryptor = new AKSKEncryptor(appItem.getToken(), appItem.getAesKey(), appItem.getAppKey());
        String messageBody = request;
        String encryptMsg = encryptor.EncryptMsg(messageBody);
        String sign = SHA1.getSHA1(appItem.getToken(), timestamp, nonce, encryptMsg);
        parameters.put(AKSKConstant.SIGN_PARAMETER_NAME, sign);


        String res = post(url, parameters, headerParameter, encryptMsg, null);
        System.out.println(res);
        return JsonUtil.str2Obj(res, Result.class);
    }

    private void expect(ThirdPartyAccountErrorType thirdPartyAccountErrorType, Result<String> result) {
        Assertions.assertEquals(thirdPartyAccountErrorType.getCode(),
                result.getCode());
    }
}
