package com.cyberscraft.uep.iam.controller.api.connector.dingding;

import com.cyberscraft.uep.starter.dds.TenantHolder;
import com.cyberscraft.uep.account.client.provider.dingding.domain.DingTalkConfig;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.iam.IamApplication;
import com.cyberscraft.uep.iam.dto.enums.ConnectorImportStatus;
import com.cyberscraft.uep.iam.dto.enums.ConnectorStatusEnum;
import com.cyberscraft.uep.iam.dto.enums.ConnectorTypeEnum;
import com.cyberscraft.uep.iam.dto.request.configs.ConnectorDto;
import com.cyberscraft.uep.iam.service.connector.IConnectorService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import javax.annotation.Resource;

/***
 * 测试创建企业微信连接器
 * @date 2021/4/3
 * <AUTHOR>
 ***/
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = IamApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class TestCreateDingDingConnector {

    @Resource
    private IConnectorService connectorService;



    @Test
    public void run() {

        TenantHolder.setTenantCode("1");
        DingTalkConfig config = new DingTalkConfig();
        //config.setAesKey("HglsdDPtuqDzrdjM8CJ6MYR1fzoz5BX/aEc4XvfcutI");
        //config.setToken("083508");
        //config.setAgentId("111");
        config.setApiBaseUrl("https://oapi.dingtalk.com/");
        config.setAppSecret("niWH1DHO7PoRKlCIdKUJmCVHUL6VGyZ8Thp5sw7Yc5S7HASxqWQNaWjK0TZUHQlZ");
        config.setRegisterBaseUrl("http://xiongxzh.vaiwan.com/iam/api/open/sns_callback");
        config.setCorpId("ding0cb5e33d8e2abd7a35c2f4657eb6378f");
        //config.setRegisterBaseUrl("");

        ConnectorDto dto = new ConnectorDto();
        dto.setConfig(JsonUtil.obj2Map(config));
        dto.setDsType(ConnectorTypeEnum.DINGDING);
        dto.setImportOrgs(true);
        //dto.setAdminAccount("test");
        dto.setCreateDefaultOrg(true);
        dto.setImportStatus(ConnectorImportStatus.SUCCESS);
        dto.setStatus(ConnectorStatusEnum.ACTIVE);
        dto.setTestAccount("test");
        dto.setProfileName("钉钉测试1");
        dto.setConflictPolicy(true);

        connectorService.createDsConfig(dto);

        try {
            Thread.sleep(1000000);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
