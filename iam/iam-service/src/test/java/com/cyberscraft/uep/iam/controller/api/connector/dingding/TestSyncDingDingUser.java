package com.cyberscraft.uep.iam.controller.api.connector.dingding;

import com.cyberscraft.uep.starter.dds.TenantHolder;
import com.cyberscraft.uep.iam.IamApplication;
import com.cyberscraft.uep.iam.service.connector.IConnectorService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import javax.annotation.Resource;

/***
 * 测试同步钉钉用户
 * @date 2021/3/27
 * <AUTHOR>
 ***/
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = IamApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class TestSyncDingDingUser {
    @Resource
    private IConnectorService connectorService;

    @Test
    public void run() {
        TenantHolder.setTenantCode("100");
        //Long connectorId = 1239751012588339202L;
        connectorService.syncData("钉钉测试");
    }
}
