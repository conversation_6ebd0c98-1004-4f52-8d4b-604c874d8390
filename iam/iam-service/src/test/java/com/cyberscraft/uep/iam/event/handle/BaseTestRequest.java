package com.cyberscraft.uep.iam.event.handle;

import com.cyberscraft.uep.aksk.server.domain.AppItem;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.FileBody;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.util.Map;

/***
 *
 * @date 2021/8/13
 * <AUTHOR>
 ***/
public class BaseTestRequest {

    protected final static AppItem appItem;

    static {
        appItem = new AppItem();
        appItem.setAppKey("ncmapp");
        appItem.setSecretKey("ncmapp");
        appItem.setAesKey("0qXE4q3dI9Q8C5NkrVgsMvaiKOypT9EDV7SldR9Ry6o");
        appItem.setToken("siwqgrbyma27hi9ckb044ypp");
    }

    public String post(String url, Map<String, String> queryParameters, Map<String, String> headerParameter, String body, FileBody fileBody) {
        if (queryParameters != null && queryParameters.size() > 0) {
            StringBuilder sb = new StringBuilder();
            for (Map.Entry<String, String> param : queryParameters.entrySet()) {
                if (sb.length() > 0) {
                    sb.append("&");
                }
                sb.append(param.getKey()).append("=").append(param.getValue());
            }
            if (url.indexOf("?") > 0) {
                url = url + "&" + sb.toString();
            } else {
                url = url + "?" + sb.toString();
            }
        }
        return post(url, headerParameter, body, fileBody);
    }

    /***
     * 进行数据提交测试
     * @param url
     * @param cmdBody
     * @return
     */
    public String post(String url, Map<String, String> headerParameter, String cmdBody, FileBody fileBody) {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);
        RequestConfig requestConfig = RequestConfig.custom().
                setConnectTimeout(180 * 1000).setConnectionRequestTimeout(180 * 1000)
                .setSocketTimeout(180 * 1000).setRedirectsEnabled(true).build();
        httpPost.setConfig(requestConfig);

        for (Map.Entry<String, String> headerEntry : headerParameter.entrySet()) {
            httpPost.setHeader(headerEntry.getKey(), headerEntry.getValue());
        }
        try {
            //ByteArrayOutputStream os = new DefaultLegacyDecoder().encode(request);
            if(null != cmdBody){
                httpPost.setEntity(new ByteArrayEntity(cmdBody.getBytes()));
            }else if(null != fileBody){
                MultipartEntityBuilder multipartEntityBuilder = MultipartEntityBuilder.create();
                multipartEntityBuilder.addPart("file", fileBody);
                HttpEntity multiPartEntity  = multipartEntityBuilder.build();
                httpPost.setEntity(multiPartEntity);
            }
            //System.out.println("request parameters" + EntityUtils.toString(httpPost.getEntity()));
            System.out.println("httpPost:" + httpPost);
            HttpResponse response = httpClient.execute(httpPost);
            if (response != null) {
                //System.out.println("result:" + result);
                return EntityUtils.toString(response.getEntity());
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (null != httpClient) {
                try {
                    httpClient.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return null;
    }
}
