package com.cyberscraft.uep.iam.service.connector.impl;

import com.cyberscraft.uep.iam.IamApplication;
import com.cyberscraft.uep.iam.service.connector.IConnectorService;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

/**
 * @Author：xhquan
 * @Project：uep-services
 * @since：2023/3/7 18:21
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = IamApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class ConnectorServiceImplTest {

    @Autowired
    IConnectorService connectorService;

    @Test
    public void test() {
        TenantHolder.setTenantCode("bit");
//        connectorService.batchSyncData("1637768370617122818", true);

        connectorService.syncData("");


    }

    @Test
    public void testEhr() {
        TenantHolder.setTenantCode("bit");
        connectorService.syncData("beiSen");
    }


}