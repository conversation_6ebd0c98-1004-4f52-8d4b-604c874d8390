package com.cyberscraft.uep.iam.service.connector.impl;

import com.cyberscraft.uep.iam.IamApplication;
import com.cyberscraft.uep.iam.service.data.IPushConnectorService;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author：xhquan
 * @Project：uep-services
 * @since：2023/7/17 14:32
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = IamApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class PushConnectorServiceImplTest {

    @Autowired
    IPushConnectorService pushConnectorService;

    @Test
    public void testEdoc() {
        TenantHolder.setTenantCode("bit");
        List<Long> list = new ArrayList<>();
        list.add(1767431810454798338L);
        pushConnectorService.sync(list, true);
    }
}
