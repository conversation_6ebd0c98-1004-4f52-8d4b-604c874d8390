package com.cyberscraft.uep.iam.controller.api.connector.dingding;

import com.cyberscraft.uep.starter.dds.TenantHolder;
import com.cyberscraft.uep.account.client.domain.Connector;
import com.cyberscraft.uep.account.client.domain.ThirdPartyAccount;
import com.cyberscraft.uep.account.client.provider.dingding.domain.DingTalkConfig;
import com.cyberscraft.uep.account.client.provider.dingding.service.IDingTalkAccessTokenService;
import com.cyberscraft.uep.account.client.service.IThirdPartyAccountService;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.iam.IamApplication;
import com.cyberscraft.uep.iam.dto.enums.ConnectorTypeEnum;
import com.cyberscraft.uep.iam.service.connector.IConnectorService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/***
 *
 * @date 2021/6/9
 * <AUTHOR>
 ***/
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = IamApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class TestSendDingDingNotice {

    @Resource
    private IDingTalkAccessTokenService dingTalkAccessTokenService;

    @Resource
    private IConnectorService connectorService;

    @Resource
    private IThirdPartyAccountService thirdPartyAccountService;

    @Test
    public void run() {
        String userId = "015562492128916060";
        String tenantId = "1";
        Long agentId = 304064911L;

        TenantHolder.setTenantCode(tenantId);

        //Connector connector = connectorService.getConnector(1245997804518649857L);
        //DingTalkConfig config = JsonUtil.str2Obj(connector.getConfig(), DingTalkConfig.class);

        DingTalkConfig config = new DingTalkConfig();
        config.setCorpId("ding0cb5e33d8e2abd7a35c2f4657eb6378f");
        config.setAppKey("dingbnj3iwjtu9ktk12b");
        config.setAppSecret("3ToxHsrFjHDL4uv82MYCPzwPXqPWlhhyGaajZMXrXRcR63_pWqAN8gAnItlRZmip");
        config.setApiBaseUrl("https://oapi.dingtalk.com/");

        Connector connector = new Connector();
        connector.setType(ConnectorTypeEnum.DINGDING.getValue());
        connector.setTenantId(tenantId);
        connector.setOrgAttrMapping(null);
        connector.setUserAttrMapping(null);
        connector.setConfig(JsonUtil.obj2Str(config));

//        List<ThirdPartyGroup> groups = thirdPartyAccountService.getAllGroups(tenantId, String.valueOf(connector.getType()),  connector);
//        if(groups!=null && groups.size()>0){
//            for (ThirdPartyGroup group: groups) {
//                System.out.println("code:"+group.getCode()+",name="+group.getName());
//                List<ThirdPartyAccount> accounts = this.thirdPartyAccountService.getAccountsByGroup(tenantId,String.valueOf(connector.getType()),group.getCode(),connector,1,100);
//                if(accounts!=null && accounts.size()>0){
//                    for (ThirdPartyAccount account: accounts) {
//                        System.out.println("userId:"+account.getUserId()+",name="+account.getName()+",accountId="+account.getAccountId()+",nickName="+account.getNickName());
//                    }
//                }
//                //System.out.println(accounts != null ? accounts.size() : 0);
//
//            }
//
//        }
        List<ThirdPartyAccount> accounts = this.thirdPartyAccountService.getAccountsByUserIds(tenantId,String.valueOf(connector.getType()),Arrays.asList(userId),connector);
        System.out.println(accounts != null ? accounts.size() : 0);
//        String accessToken = dingTalkAccessTokenService.getAccessToken(tenantId, config);
//
//        System.out.println("access_token:" + accessToken);
//
//        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2");
//
//        OapiMessageCorpconversationAsyncsendV2Request request = new OapiMessageCorpconversationAsyncsendV2Request();
//        request.setUseridList(userId);
//        request.setAgentId(agentId);
//        request.setToAllUser(false);
//
//        OapiMessageCorpconversationAsyncsendV2Request.Msg msg = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
//        msg.setMsgtype("text");
//        msg.setText(new OapiMessageCorpconversationAsyncsendV2Request.Text());
//        msg.getText().setContent("工作消息，用于测试" + SnowflakeIDUtil.getShortId());
//        request.setMsg(msg);
//        try {
//            OapiMessageCorpconversationAsyncsendV2Response response = client.execute(request, accessToken);
//            System.out.println(response.getBody());
//            System.out.println(response.getTaskId());
//        } catch (Exception e) {
//            e.printStackTrace();
//        }


    }
}
