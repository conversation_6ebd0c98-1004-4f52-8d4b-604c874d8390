package com.cyberscraft.uep.iam.controller.api.connector.ldap;

import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.iam.IamApplication;
import com.cyberscraft.uep.iam.dto.enums.ConnectorImportStatus;
import com.cyberscraft.uep.iam.dto.enums.ConnectorStatusEnum;
import com.cyberscraft.uep.iam.dto.enums.ConnectorTypeEnum;
import com.cyberscraft.uep.iam.dto.request.configs.ConnectorDto;
import com.cyberscraft.uep.iam.service.connector.IConnectorService;
import com.cyberscraft.uep.iam.service.connector.ldap.LdapConfig;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2020-09-22 14:10
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = IamApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class TestCreateLDAPConnector {

    @Resource
    private IConnectorService connectorService;

    @Test
    public void run() {

        TenantHolder.setTenantCode("test1");
        LdapConfig config = new LdapConfig();
        config.setStartTls(true);
        config.setAdminAccount("cn=Directory Manager");
        config.setAdminPassword("Nsky@0!6");
        config.setHost("*************");
        config.setPort(636);
        config.setUserFilter("(objectclass=organizationalUnit)");
        config.setOrgFilter("(objectclass=organizationalPerson)");
        config.setUserBaseDn("ou=people,ou=Subjects,dc=cyberscraft,dc=com");
        config.setTls(true);


        Map userMap = new HashMap<>();
        userMap.put("username","uid");

        Map orgMap = new HashMap<>();
        orgMap.put("name","ou");

        ConnectorDto dto = new ConnectorDto();
        dto.setConfig(JsonUtil.obj2Map(config));
        dto.setDsType(ConnectorTypeEnum.LDAP);
        dto.setImportOrgs(true);
        dto.setCreateDefaultOrg(true);
        dto.setImportStatus(ConnectorImportStatus.SUCCESS);
        dto.setStatus(ConnectorStatusEnum.ACTIVE);
        dto.setTestAccount("cn=Directory Manager");
        dto.setProfileName("LDAP测试");
        dto.setConflictPolicy(true);

        connectorService.createDsConfig(dto);

    }
}
