package com.cyberscraft.uep.iam.controller.api.connector.ldap;

import com.cyberscraft.uep.iam.IamApplication;
import com.cyberscraft.uep.iam.service.connector.IConnectorService;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import javax.annotation.Resource;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2020-09-22 14:10
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = IamApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class TestDeleteLDAPConnector {

    @Resource
    private IConnectorService connectorService;

    @Test
    public void run() {

        TenantHolder.setTenantCode("test1");
        connectorService.deleteDsConfig("LDAP测试","DELETE");
    }
}
