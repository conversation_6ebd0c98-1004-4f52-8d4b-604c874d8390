package com.cyberscraft.uep.iam.jwt;

import com.cyberscraft.uep.iam.common.util.RandomStringGenerator;
import com.nimbusds.jose.JOSEException;
import com.nimbusds.jose.crypto.MACVerifier;
import com.nimbusds.jwt.SignedJWT;

import java.text.ParseException;
import java.util.Base64;

public class TestHS256 {
    public static void main(String[] args) throws ParseException, JOSEException {
        String idToken =
                "eyJraWQiOiJ1c2VyY2VudGVyIiwidHlwIjoiSldUIiwiYWxnIjoiSFMyNTYifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Yzn7C47K9odhOGS0hb7Z4q1_3cSLTs7ttrqhbQ5GqEA";
        String secretStr = "ROvv2FssLqPw+QvZatQSH6hZHZRyYbZOf5IVDI3ttzCyXIpf6ca/uqYDg3XERbdI8U+BhaZ7PevNkChuB6Edlp+esi/AGSo9KRLxDVxdaypLXBfkLNSE1xsxJE0/T9uKf4bwuMEKh0S+rqRfz/shwJphygiItH+kqI815czaxvE=";
        SignedJWT signedJWT = SignedJWT.parse(idToken);

        byte[] secret = Base64.getDecoder().decode(secretStr);
        MACVerifier macVerifier = new MACVerifier(secret);
        boolean result = signedJWT.verify(macVerifier);
        System.out.println(result);

        signedJWT = SignedJWT.parse(idToken);
        secret = RandomStringGenerator.getRandomBytes(128);
        macVerifier = new MACVerifier(secret);
        result = signedJWT.verify(macVerifier);
        System.out.println(result);
    }
}
