package com.cyberscraft.uep.iam.configuration;

import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.core.config.GlobalConfig;
import com.baomidou.mybatisplus.core.parser.ISqlParser;
import com.baomidou.mybatisplus.core.parser.SqlParserHelper;
import com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor;
import com.baomidou.mybatisplus.extension.plugins.tenant.TenantHandler;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.cyberscraft.uep.common.constant.PaginationConst;
import com.cyberscraft.uep.iam.common.exception.UserCenterException;
import com.cyberscraft.uep.iam.common.plugin.IamTenantSqlParser;
import com.cyberscraft.uep.iam.configuration.db.TenantUniqueInterceptor;
import com.cyberscraft.uep.iam.constants.TenantConstant;
import com.cyberscraft.uep.iam.errors.TransactionErrorType;
import com.cyberscraft.uep.iam.service.connector.task.IamSnowflakeChecker;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.StringValue;
import org.apache.commons.lang3.RandomUtils;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;


/**
 * mybatis配置
 * <AUTHOR>
 * @date 2021-07-05 10:36
 * @description
 */
@Configuration
@EnableCaching
@EnableTransactionManagement
@MapperScan("com.cyberscraft.uep.iam.dao")
public class DataSourceConfiguration {

    private static final Logger logger = LoggerFactory.getLogger(DataSourceConfiguration.class);

    private static final List<String> IGNORE_TENANT_TABLES = new ArrayList<>();
    private static final List<String> IGNORE_MAPPER_IDS = new ArrayList<>();

    @Autowired
    private IamSnowflakeChecker iamSnowflakeChecker;

    static {
        IGNORE_TENANT_TABLES.add("iam_sys_tenant");
        IGNORE_TENANT_TABLES.add("iam_app");
        IGNORE_TENANT_TABLES.add("iam_deleted_app");
        IGNORE_TENANT_TABLES.add("iam_app_permission_sets");
        IGNORE_TENANT_TABLES.add("iam_app_permission");
        IGNORE_TENANT_TABLES.add("iam_app_permission_sets_permission");
        IGNORE_TENANT_TABLES.add("iam_app_role");
        IGNORE_TENANT_TABLES.add("iam_app_role_permission");
        IGNORE_TENANT_TABLES.add("iam_app_tenant");
        IGNORE_TENANT_TABLES.add("iam_device_code");
        IGNORE_TENANT_TABLES.add("iam_etl_org");
        IGNORE_TENANT_TABLES.add("iam_etl_user");
        IGNORE_TENANT_TABLES.add("iam_etl_user_org");
        IGNORE_TENANT_TABLES.add("iam_etl_sync_history");
        IGNORE_TENANT_TABLES.add("iam_app_connector");
        IGNORE_TENANT_TABLES.add("iam_app_action");
        IGNORE_TENANT_TABLES.add("iam_app_account");
        IGNORE_TENANT_TABLES.add("iam_app_permission_bind");
        IGNORE_TENANT_TABLES.add("iam_holidays");
        IGNORE_TENANT_TABLES.add("iam_ip");
    }

    static {
        //IGNORE_MAPPER_IDS.add("com.cyberscraft.uep.adm.core.dao.TestMapper.selectById");
//        IGNORE_MAPPER_IDS.add("com.cyberscraft.uep.iam.dao.UserDao.getTenantIdByMobile");
//        IGNORE_MAPPER_IDS.add("com.cyberscraft.uep.iam.dao.UserDao.getTenantIdByEmail");
    }

    @Bean("sqlSessionFactory")
    @Primary
    public SqlSessionFactory sqlSessionFactory(@Autowired @Qualifier("dataSource") DataSource dataSource, TenantUniqueInterceptor tenantUniqueIntercepto) throws Exception {
        MybatisSqlSessionFactoryBean sqlSessionFactoryBean = new MybatisSqlSessionFactoryBean();
        sqlSessionFactoryBean.setDataSource(dataSource);
        sqlSessionFactoryBean.setTypeAliasesPackage("com.cyberscraft.uep.iam.common.entity");
        //sqlSessionFactoryBean.setTypeEnumsPackage("com.cyberscraft.uep.iam.dto.enums");
        if (isDmDatabase(dataSource)) {
            sqlSessionFactoryBean.setPlugins(new Interceptor[]{
                    paginationInterceptor(),
                    tenantUniqueIntercepto
            });
        } else {
            sqlSessionFactoryBean.setPlugins(new Interceptor[]{
                    paginationInterceptor()
            });
        }
        sqlSessionFactoryBean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:mappers/*.xml"));
        MybatisConfiguration configuration = new MybatisConfiguration();
        //查询结果为空的也返回
        configuration.setCallSettersOnNulls(true);

        configuration.setMapUnderscoreToCamelCase(true);
        sqlSessionFactoryBean.setConfiguration(configuration);

        setSnowFlakeKey(sqlSessionFactoryBean);

        return sqlSessionFactoryBean.getObject();
    }

    /**
     * 设置雪花ID
     * @param sqlSessionFactoryBean
     */
    private void setSnowFlakeKey(MybatisSqlSessionFactoryBean sqlSessionFactoryBean){
        long workerId, datacenterId;
        // 最大尝试次数为50次
        for (int i = 0; i<50; i++){
            workerId = RandomUtils.nextLong(0, 32);
            datacenterId = RandomUtils.nextLong(0, 32);
            boolean status = iamSnowflakeChecker.checkSnowflakeKey(workerId, datacenterId);
            if(status){
                GlobalConfig globalConfig = sqlSessionFactoryBean.getConfiguration().getGlobalConfig();
                globalConfig.setWorkerId(workerId);
                globalConfig.setDatacenterId(datacenterId);
                sqlSessionFactoryBean.setGlobalConfig(globalConfig);
                logger.info("iam setSnowFlakeKey i:{}, workerId:{},dataCenterId:{}",i, workerId, datacenterId);
                return;
            }
        }
        logger.error("iam setSnowFlakeKey fail，未能命中workerId和dataCenterId");
    }

    @ConditionalOnMissingBean
    @Bean
    public PlatformTransactionManager transactionManager(DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    /**
     * 分页插件
     *
     * @return
     */
    @Bean
    public PaginationInterceptor paginationInterceptor() {
        PaginationInterceptor paginationInterceptor = new PaginationInterceptor();
        paginationInterceptor.setLimit(PaginationConst.MAX_PAGE_SIZE);

        List<ISqlParser> sqlParserList = new ArrayList<>();
        IamTenantSqlParser tenantSqlParser = new IamTenantSqlParser();
        tenantSqlParser.setIamTenantHandler(new TenantHandler() {
            @Override
            public Expression getTenantId(boolean where) {
                if (TenantHolder.getTenantCode() == null) {
                    throw new UserCenterException(TransactionErrorType.UC_TENANT_NOT_FOUND_ERROR);
                }
                return new StringValue(TenantHolder.getTenantCode());
            }

            @Override
            public String getTenantIdColumn() {
                return TenantConstant.tenantDbFieldName;
            }

            @Override
            public boolean doTableFilter(String tableName) {
                return IGNORE_TENANT_TABLES.stream().anyMatch((e) -> e.equalsIgnoreCase(tableName));
            }
        });

        sqlParserList.add(tenantSqlParser);
        paginationInterceptor.setSqlParserList(sqlParserList);
        paginationInterceptor.setSqlParserFilter(metaObject -> {
            MappedStatement ms = SqlParserHelper.getMappedStatement(metaObject);
            return IGNORE_MAPPER_IDS.stream().anyMatch((e) -> e.equalsIgnoreCase(ms.getId()));
        });
        return paginationInterceptor;
    }

    @Bean
    public TenantUniqueInterceptor tenantUniqueInterceptor(JdbcTemplate jdbcTemplate) {
        TenantUniqueInterceptor interceptor = new TenantUniqueInterceptor();
        interceptor.setJdbcTemplate(jdbcTemplate);
        return interceptor;
    }

    private boolean isDmDatabase(DataSource dataSource) {
        Connection connection = null;
        try {
            connection = dataSource.getConnection();
            String url = connection.getMetaData().getURL().toLowerCase();
            return url.contains("dm");
        } catch (Exception e) {
            return false;
        } finally {
            if (connection != null) {
                try {
                    connection.close();
                } catch (SQLException e) {
                    logger.warn(e.getMessage());
                }
            }
        }
    }

//    @Bean
//    public IdentifierGenerator identifierGenerator() {
//        long workerId = RandomUtils.nextLong(0, 32);
//        long dataCenterId = RandomUtils.nextLong(0, 32);
//        logger.info("iam workerId:{},dataCenterId:{}", workerId, dataCenterId);
//        DefaultIdentifierGenerator defaultIdentifierGenerator = new DefaultIdentifierGenerator(workerId, dataCenterId);
//        IdWorker.setIdentifierGenerator(defaultIdentifierGenerator);
//        return defaultIdentifierGenerator;
//    }

    //    /**
    // * SQL执行效率插件
    // */
    //@Bean
    //@Profile({"dev","test"})// 设置 dev test 环境开启
    //public PerformanceInterceptor performanceInterceptor() {
    //    return new PerformanceInterceptor();
    //}
}
