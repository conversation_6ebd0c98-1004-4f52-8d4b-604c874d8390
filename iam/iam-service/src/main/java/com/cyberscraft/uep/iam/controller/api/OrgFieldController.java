package com.cyberscraft.uep.iam.controller.api;

import com.cyberscraft.uep.common.dto.QueryPage;
import com.cyberscraft.uep.common.dto.ReturnResultVO;
import com.cyberscraft.uep.common.util.StringUtil;
import com.cyberscraft.uep.iam.api.OrgFieldApi;
import com.cyberscraft.uep.iam.dto.enums.FieldMode;
import com.cyberscraft.uep.iam.service.auditlog.annotation.Auditable;
import com.cyberscraft.uep.iam.service.auditlog.constant.AuditEventSubtype;
import com.cyberscraft.uep.iam.service.auditlog.constant.AuditEventType;
import com.cyberscraft.uep.iam.dto.enums.CreatedMode;
import com.cyberscraft.uep.iam.dto.enums.FieldType;
import com.cyberscraft.uep.iam.dto.enums.OpConstraint;
import com.cyberscraft.uep.iam.dto.request.AttrBatchCreateInVO;
import com.cyberscraft.uep.iam.dto.request.AttributeUpdateInVO;
import com.cyberscraft.uep.iam.dto.response.AttrSchemaVO;
import com.cyberscraft.uep.iam.dto.response.FieldDictVO;
import com.cyberscraft.uep.iam.query.FieldDictQueryDto;
import com.cyberscraft.uep.iam.service.IFieldDictService;
import com.cyberscraft.uep.iam.service.transfer.TypeMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.cyberscraft.uep.iam.errors.TransactionErrorDescription.USER_SCHEMA_NOT_FOUND_ERROR_CODE;
import static com.cyberscraft.uep.iam.errors.TransactionErrorDescription.USER_SCHEMA_NOT_FOUND_ERROR_DESC;

@RestController
public class OrgFieldController implements OrgFieldApi {

    @Autowired
    IFieldDictService fieldDictService;

    @Override
    @PreAuthorize("#oauth2.hasPermission('read:user_attr_all')")
//    @ControllerLog(description = "Controller searchOrgFields")
    public ReturnResultVO<QueryPage<FieldDictVO>> searchOrgFields(
        String filter,
        Boolean basic,
        OpConstraint opConstraint,
        Boolean mandatory,
        Boolean asImport,
        Boolean searchable,
        Integer page,
        Integer size,
        List<String> sort) {
        FieldMode fieldMode = null;
        if (basic != null){
            if (basic){
                fieldMode = FieldMode.BASIC;
            }else{
                fieldMode = FieldMode.DYNAMIC;
            }
        }
        FieldDictQueryDto queryDto =
                FieldDictQueryDto
                        .build()
                        .filter(filter)
                        .createMod(TypeMapper.asInt(fieldMode))
                        .opConstraint(TypeMapper.asInt(opConstraint))
                        .mandatory(TypeMapper.asInt(mandatory))
                        .asImport(TypeMapper.asInt(asImport))
                        .searchable(TypeMapper.asInt(searchable))
                        .fieldType(FieldType.ORG);
        QueryPage<FieldDictVO> queryPageVO = new QueryPage(page, size, sort);

        return new ReturnResultVO<>(
                fieldDictService.search(queryDto, queryPageVO));
    }

    @Override
    @PreAuthorize("#oauth2.hasPermission('read:user_attrs')")
//    @ControllerLog(description = "Controller getUserInfoById")
    public ReturnResultVO<FieldDictVO> getById(Long id) {
        FieldDictVO out = fieldDictService.getById(id);
        return new ReturnResultVO<>(out);
    }

    @Override
    @Auditable(eventType = AuditEventType.ORG_EXT_ATTR, eventSubtype = AuditEventSubtype.NEW,
            fields = "targetName=#{#input.getExtAttrs().get(0).getDisplayName()};;" +
                    "parameters={\"ext_attrs\": #{T(com.cyberscraft.uep.iam.common.util.JacksonJsonUtil).beanToJson(#input)}}")
    @PreAuthorize("#oauth2.hasPermission('create:user_attrs')")
//    @ControllerLog(description = "controller createOrgSchema")
    public ReturnResultVO<Void> batchCreateOrgField(AttrBatchCreateInVO input) {
        return new ReturnResultVO<>(fieldDictService.batchCreateFieldDicts(FieldType.ORG, input.getExtAttrs()));
    }

    @Override
    @PreAuthorize("#oauth2.hasPermission('delete:user_attrs')")
    @Auditable(eventType = AuditEventType.ORG_EXT_ATTR, eventSubtype = AuditEventSubtype.DELETE,
            fields = "targetName=#{#domainName}")
    public ReturnResultVO<?> deleteFieldDict(String domainName) {
        return fieldDictService.deleteFieldByDomainName(domainName, FieldType.ORG);
    }

    @Override
    @PreAuthorize("#oauth2.hasPermission('update:user_attrs')")
//    @ControllerLog(description = "controller updateFieldDict")
    @Auditable(eventType = AuditEventType.ORG_EXT_ATTR, eventSubtype = AuditEventSubtype.UPDATE,
            fields = "targetId=#{#id};;" +
                    "targetName=#{#orgAttribute.getDisplayName()};;" +
                    "parameters=#{T(com.cyberscraft.uep.iam.common.util.JacksonJsonUtil).beanToJson(#orgAttribute)}")
    public ReturnResultVO<AttrSchemaVO> updateField(Long id,
            AttributeUpdateInVO orgAttribute) {
        return new ReturnResultVO<>(fieldDictService.updateField(id, orgAttribute, FieldType.ORG));
    }
}
