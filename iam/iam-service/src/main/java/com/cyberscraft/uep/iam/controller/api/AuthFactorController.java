package com.cyberscraft.uep.iam.controller.api;

import com.cyberscraft.uep.common.dto.ReturnResultVO;
import com.cyberscraft.uep.iam.api.AuthFactorApi;
import com.cyberscraft.uep.iam.dto.request.login.AuthMethod;
import com.cyberscraft.uep.iam.dto.request.login.factors.AuthFactorDTO;
import com.cyberscraft.uep.iam.dto.request.login.factors.AuthFactorStatusChangeDTO;
import com.cyberscraft.uep.iam.dto.response.login.AuthFactorVO;
import com.cyberscraft.uep.iam.service.IAuthFactorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
public class AuthFactorController implements AuthFactorApi {
    @Autowired
    private IAuthFactorService authFactorService;

    @PreAuthorize("#oauth2.hasPermission('update:auth_factors')")
    @Override
    public ReturnResultVO<Void> updateAuthFactor(@Valid AuthFactorDTO authFactorDTO) {
        return new ReturnResultVO<>(authFactorService.update(authFactorDTO));
    }

    @PreAuthorize("#oauth2.hasPermission('read:auth_factors')")
    @Override
    public ReturnResultVO<AuthFactorVO> getAuthFactor(AuthMethod authMethod) {
        return new ReturnResultVO<>(authFactorService.get(authMethod));
    }

    @PreAuthorize("#oauth2.hasPermission('search:auth_factors')")
    @Override
    public ReturnResultVO<List<AuthFactorVO>> list() {
        return new ReturnResultVO<>(authFactorService.list());
    }

    @PreAuthorize("#oauth2.hasPermission('update:auth_factors')")
    @Override
    public ReturnResultVO<Void> changeAuthFactorStatus(AuthMethod authMethod, @Valid AuthFactorStatusChangeDTO authFactorStatusChangeDTO) {
        return new ReturnResultVO<>(authFactorService.changeStatus(authMethod, authFactorStatusChangeDTO));
    }
}
