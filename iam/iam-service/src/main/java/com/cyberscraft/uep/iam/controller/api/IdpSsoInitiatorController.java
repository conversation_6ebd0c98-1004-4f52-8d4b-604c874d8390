package com.cyberscraft.uep.iam.controller.api;

import com.cyberscraft.uep.common.dto.ReturnResultVO;
import com.cyberscraft.uep.iam.api.IdpSsoInitiatorApi;
import com.cyberscraft.uep.iam.service.IdpSsoInitiatorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @Description 由idp发起的单点登录
 * <AUTHOR>
 * @Date 2024/9/10 17:52
 */
@RestController
public class IdpSsoInitiatorController implements IdpSsoInitiatorApi {

    @Autowired
    private IdpSsoInitiatorService idpInitiatedSso;

    @Override
    public ReturnResultVO<Void> initiateSsoRequest(HttpServletRequest request, HttpServletResponse response, String clientId) {
        idpInitiatedSso.initiateSsoRequest(request, response, clientId);
        return null;
    }

}
