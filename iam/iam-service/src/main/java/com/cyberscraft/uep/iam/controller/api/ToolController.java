package com.cyberscraft.uep.iam.controller.api;

import com.cyberscraft.uep.common.bean.Result;
import com.cyberscraft.uep.common.domain.ParamProfile;
import com.cyberscraft.uep.common.exception.ResponseResult;
import com.cyberscraft.uep.iam.api.ToolApi;
import com.cyberscraft.uep.iam.service.IToolService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
public class ToolController implements ToolApi {

    @Autowired
    private IToolService iToolService;

    @Override
    public Result<List<ParamProfile>> jsonToParamSchema(String rootName, String json) {
        return ResponseResult.success(iToolService.jsonToParamSchema(rootName, json));
    }
}
