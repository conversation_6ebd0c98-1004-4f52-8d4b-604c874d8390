package com.cyberscraft.uep.iam.controller.api.connector.controller;

import com.cyberscraft.uep.common.bean.Result;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import com.cyberscraft.uep.account.client.domain.Connector;
import com.cyberscraft.uep.account.client.domain.ThirdPartyAccount;
import com.cyberscraft.uep.account.client.domain.ThirdPartyGroup;
import com.cyberscraft.uep.account.client.service.IThirdPartyAccountService;
import com.cyberscraft.uep.account.client.service.IThirdPartyMessageService;
import com.cyberscraft.uep.iam.common.exception.UserCenterException;
import com.cyberscraft.uep.iam.common.constants.UrlConstant;
import com.cyberscraft.uep.iam.errors.TransactionErrorType;
import com.cyberscraft.uep.iam.service.connector.IConnectorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;

/***
 * 第三方平台账户相关的controller，主要用于方便部署后的系统进行调试使用
 * @date 2021/3/31
 * <AUTHOR>
 ***/
@RestController
public class ThirdPartyAccountController extends  BaseThirdPartyController{


    @Autowired
    private IConnectorService connectorService;

    @Resource
    private IThirdPartyMessageService thirdPartyMessageService;

    @Resource
    private IThirdPartyAccountService thirdPartyAccountService;


    /****
     *
     * @param tenantId
     * @param connectorId
     * @param request
     * @return
     */
    @RequestMapping(value = UrlConstant.IAM_THIRDPARTY_GROUP_LIST, method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public Result<List<ThirdPartyGroup>> getAllGroups(
            @PathVariable("tenantId") String tenantId,
            @PathVariable("connectorId") Long connectorId,
            HttpServletRequest request) {

        TenantHolder.setTenantCode(tenantId);
        Connector connector = connectorService.getConnector(connectorId);
        if (connector == null) {
            LOG.error("处理钉钉消息时，连接器无效，租户:{},连接器:{}", tenantId, connectorId);
            throw new UserCenterException(TransactionErrorType.DS_ID_INVALID_ERROR);
        }
        List<ThirdPartyGroup> list = thirdPartyAccountService.getAllGroups(tenantId, String.valueOf(connector.getType()), connector);
        return successResult(list);
    }

    /****
     *
     * @param tenantId
     * @param connectorId
     * @param request
     * @return
     */
    @RequestMapping(value = UrlConstant.IAM_THIRDPARTY_GROUP, method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public Result<ThirdPartyGroup> getAllGroups(
            @PathVariable("tenantId") String tenantId,
            @PathVariable("connectorId") Long connectorId,
            @PathVariable("groupCode") String groupCode,
            HttpServletRequest request) {

        TenantHolder.setTenantCode(tenantId);
        Connector connector = connectorService.getConnector(connectorId);
        if (connector == null) {
            LOG.error("处理钉钉消息时，连接器无效，租户:{},连接器:{}", tenantId, connectorId);
            throw new UserCenterException(TransactionErrorType.DS_ID_INVALID_ERROR);
        }
        ThirdPartyGroup list = thirdPartyAccountService.getGroupByCode(tenantId, String.valueOf(connector.getType()), groupCode, connector);
        return successResult(list);
    }

    /****
     *
     * @param tenantId
     * @param connectorId
     * @param groupCode
     * @param request
     * @return
     */
    @RequestMapping(value = UrlConstant.IAM_THIRDPARTY_ACCOUNT_LIST, method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public Result<List<ThirdPartyAccount>> getAccounts(
            @PathVariable("tenantId") String tenantId,
            @PathVariable("connectorId") Long connectorId,
            @RequestParam(value = "groupCode", required = true) String groupCode,
            HttpServletRequest request) {

        TenantHolder.setTenantCode(tenantId);
        Connector connector = connectorService.getConnector(connectorId);
        if (connector == null) {
            LOG.error("处理钉钉消息时，连接器无效，租户:{},连接器:{}", tenantId, connectorId);
            throw new UserCenterException(TransactionErrorType.DS_ID_INVALID_ERROR);
        }
        List<ThirdPartyAccount> list = thirdPartyAccountService.getAccountsByGroup(tenantId, String.valueOf(connector.getType()), groupCode, connector);
        return successResult(list);
    }

    /****
     *
     * @param tenantId
     * @param connectorId
     * @param request
     * @return
     */
    @RequestMapping(value = UrlConstant.IAM_THIRDPARTY_ACCOUNT, method = {RequestMethod.POST, RequestMethod.GET})
    @ResponseBody
    public Result<List<ThirdPartyAccount>> getAccount(
            @PathVariable("tenantId") String tenantId,
            @PathVariable("connectorId") Long connectorId,
            @PathVariable("userId") String userId,
            HttpServletRequest request) {

        TenantHolder.setTenantCode(tenantId);
        Connector connector = connectorService.getConnector(connectorId);
        if (connector == null) {
            LOG.error("处理钉钉消息时，连接器无效，租户:{},连接器:{}", tenantId, connectorId);
            throw new UserCenterException(TransactionErrorType.DS_ID_INVALID_ERROR);
        }
        List<ThirdPartyAccount> list = thirdPartyAccountService.getAccountsByUserIds(tenantId, String.valueOf(connector.getType()), Arrays.asList(userId), connector);
        return successResult(list);
    }

}
