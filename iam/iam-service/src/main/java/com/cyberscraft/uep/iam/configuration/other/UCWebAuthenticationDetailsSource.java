package com.cyberscraft.uep.iam.configuration.other;

import com.cyberscraft.uep.iam.service.oidc.domain.UCWebAuthenticationDetails;
import org.springframework.security.web.authentication.WebAuthenticationDetails;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

@Component("ucWebAuthenticationDetailSource")
public class UCWebAuthenticationDetailsSource extends WebAuthenticationDetailsSource {
    @Override
    public WebAuthenticationDetails buildDetails(HttpServletRequest context) {
        return new UCWebAuthenticationDetails(context);
    }
}
