package com.cyberscraft.uep.iam.controller.api;

import com.cyberscraft.uep.common.bean.Result;
import com.cyberscraft.uep.common.exception.ResponseResult;
import com.cyberscraft.uep.iam.constants.UrlConstants;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2021-07-03 17:24
 * @description
 */
@RestController
public class HealthCheckController {

    @GetMapping(value = UrlConstants.URL_PREFIX_IAM + "/health/")
    public Result getUser(String id){
        return ResponseResult.success("HELLO,THIS IS IAM-SERVICE");
    }
}
