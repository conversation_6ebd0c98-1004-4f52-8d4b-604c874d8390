package com.cyberscraft.uep.iam.configuration.security;

import com.cyberscraft.uep.common.util.HttpUtil;
import com.cyberscraft.uep.iam.common.util.IAMHttpUtil;
import com.cyberscraft.uep.iam.dbo.AppDBO;
import com.cyberscraft.uep.iam.dto.constraint.as.AuthServerConstants;
import com.cyberscraft.uep.iam.entity.AppEntity;
import com.cyberscraft.uep.iam.service.IAlertService;
import com.cyberscraft.uep.iam.service.IAuditlogService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.SimpleUrlAuthenticationSuccessHandler;
import org.springframework.stereotype.Service;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

import static com.cyberscraft.uep.iam.dto.constraint.as.AuthServerConstants.STR_CLIENT_ID;

/***
 *
 * @date 2021-08-07
 * <AUTHOR>
 ***/
@Service
public class IamAuthenticationSuccessHandler extends SimpleUrlAuthenticationSuccessHandler {

    /***
     *
     */
    private final static Logger LOG= LoggerFactory.getLogger(IamAuthenticationSuccessHandler.class);

    @Autowired
    private IAuditlogService auditlogService;

    @Autowired
    private IAlertService alertService;

    @Autowired
    private AppDBO appDBO;

    /**
     * Builds the target URL according to the logic defined in the main class Javadoc.
     *
     * @param request
     * @param response
     */
    @Override
    protected String determineTargetUrl(HttpServletRequest request, HttpServletResponse response) {
        String continueUrl = request.getParameter(AuthServerConstants.CONTINUE_URL);
        if(StringUtils.isNotBlank(continueUrl)){
            //登录成功，则必然有tcode，所以无需在url中再添加tcode
            //continueUrl = HttpUtil.addTenantCodeToContinueParam(continueUrl, request.getParameter(AuthServerConstants.STR_TENANT_CODE));
            LOG.debug("continueUrl is =>{}",continueUrl);
            return continueUrl;
        }
        return super.determineTargetUrl(request, response);
    }

    @Override
    public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication) throws IOException, ServletException {
        String continueUrl = request.getParameter(AuthServerConstants.CONTINUE_URL);
        String clientId = request.getParameter(AuthServerConstants.STR_CLIENT_ID);
        String username = request.getParameter(AuthServerConstants.LOGIN_USERNAME);

        if(StringUtils.isNotEmpty(continueUrl)) {
            String clientIdTmp = HttpUtil.getParamsMap(continueUrl).get(STR_CLIENT_ID);
            if(StringUtils.isNotEmpty(clientIdTmp)) {
                clientId = clientIdTmp;
            }
        }
        LOG.debug("client_id is: {}", clientId);

        super.onAuthenticationSuccess(request, response, authentication);

        IAMHttpUtil.RemoteHostInfo remoteHostInfo = IAMHttpUtil.getRemoteHostInfoFromContext();
        if(remoteHostInfo != null) {
            alertService.ansyncCheckCrossRegionLogin(username, remoteHostInfo.ip);
//        alertService.ansyncCheckCrossRegionLogin(username, "**************");
        }
    }
}
