package com.cyberscraft.uep.iam.configuration.server.filter;

import com.cyberscraft.uep.common.util.LocalLanguageUtil;
import com.ibm.icu.util.ULocale;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.filter.GenericFilterBean;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class SetLocalLanguageFilter extends GenericFilterBean {

    private final static Logger logger = LoggerFactory.getLogger(SetLocalLanguageFilter.class);

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, Filter<PERSON>hain chain) throws IOException, ServletException {
        String language = resolveLanguage((HttpServletRequest) request);
        logger.info("request language is:{}", language);
        if (StringUtils.isNotEmpty(language)) {
            LocalLanguageUtil.setLanguage(language);
        }
        try {
            chain.doFilter(request, response);
        } finally {
            LocalLanguageUtil.remove();
        }
    }

    private String resolveLanguage(HttpServletRequest request) {
        // 优先从cookie中获取语言信息
        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if ("DigitalSee_Language".equals(cookie.getName())) {
                    String langFromCookie = cookie.getValue();
                    if (StringUtils.isNotBlank(langFromCookie)) {
                        return langFromCookie;
                    }
                }
            }
        }

        // 如果cookie中没有语言信息，则从header中获取
        String lang = request.getHeader("Accept-Language");
        if (StringUtils.isBlank(lang)) {
            return "en-US"; // 默认英文
        }

        // 解析Accept-Language头，获取主要语言代码
        String primaryLang = lang.split(",")[0].trim().split(";")[0].trim();

        // 如果只有语言代码，没有地区代码，则添加最常用的地区代码
        if (!primaryLang.contains("-") && !primaryLang.contains("_")) {
            primaryLang = standardizeLanguageCode(primaryLang);
        }

        return primaryLang;
    }


    private String standardizeLanguageCode(String langCode) {
        try {
            // 获取该语言的默认区域
            ULocale defaultLocale = ULocale.addLikelySubtags(new ULocale(langCode));

            if (defaultLocale.getCountry() != null && !defaultLocale.getCountry().isEmpty()) {
                return defaultLocale.getLanguage() + "-" + defaultLocale.getCountry();
            }

            // 否则使用静态映射
            Map<String, String> languageToRegion = new HashMap<>();
            languageToRegion.put("zh", "zh-CN");  // 中文 -> 中国
            languageToRegion.put("en", "en-US");  // 英语 -> 美国
            languageToRegion.put("es", "es-ES");  // 西班牙语 -> 西班牙
            languageToRegion.put("fr", "fr-FR");  // 法语 -> 法国
            languageToRegion.put("de", "de-DE");  // 德语 -> 德国
            languageToRegion.put("it", "it-IT");  // 意大利语 -> 意大利
            languageToRegion.put("ja", "ja-JP");  // 日语 -> 日本
            languageToRegion.put("ko", "ko-KR");  // 韩语 -> 韩国
            languageToRegion.put("pt", "pt-BR");  // 葡萄牙语 -> 巴西
            languageToRegion.put("ru", "ru-RU");  // 俄语 -> 俄罗斯
            languageToRegion.put("ar", "ar-SA");  // 阿拉伯语 -> 沙特阿拉伯
            languageToRegion.put("th", "th-TH");  // 泰语 -> 泰国
            languageToRegion.put("vi", "vi-VN");  // 越南语 -> 越南

            return languageToRegion.getOrDefault(langCode, langCode);
        } catch (Exception e) {
            logger.warn("Failed to standardize language code: " + langCode, e);
            return langCode;
        }
    }

}
