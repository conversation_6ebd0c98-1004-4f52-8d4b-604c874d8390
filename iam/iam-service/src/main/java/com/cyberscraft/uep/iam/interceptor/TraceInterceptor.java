package com.cyberscraft.uep.iam.interceptor;//package com.cyberscraft.uep.adm.interceptor;

import com.cyberscraft.uep.common.util.MDCThreadWrapUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @Date 2024/6/18 18:48
 * @Version 1.0
 * @Description 线程traceId
 */
public class TraceInterceptor implements HandlerInterceptor {

    private final static Logger LOG = LoggerFactory.getLogger(TraceInterceptor.class);

    /****
     * 请求开始时，通过工具类，获取对应的traceId,如果没有，则用udid自动生成一个新的
     * @param request
     * @param response
     * @param handler
     * @return
     * @throws Exception
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        //增加日志traceID相关信息
        MDCThreadWrapUtil.setTraceId();
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {

    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        MDCThreadWrapUtil.clearTraceId();
    }
}
