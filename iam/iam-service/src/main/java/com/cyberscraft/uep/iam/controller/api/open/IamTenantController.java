package com.cyberscraft.uep.iam.controller.api.open;

import com.cyberscraft.uep.common.bean.Result;
import com.cyberscraft.uep.common.dto.QueryPage;
import com.cyberscraft.uep.common.dto.ReturnResultVO;
import com.cyberscraft.uep.common.exception.ResponseResult;
import com.cyberscraft.uep.iam.api.open.IamTenantApi;
import com.cyberscraft.uep.iam.common.exception.UserCenterException;
import com.cyberscraft.uep.iam.common.util.UserUtil;
import com.cyberscraft.uep.iam.dto.request.AppListVO;
import com.cyberscraft.uep.iam.dto.request.TcUserResetPwdVO;
import com.cyberscraft.uep.iam.dto.request.tenant.*;
import com.cyberscraft.uep.iam.dto.response.SysTenantVo;
import com.cyberscraft.uep.iam.dto.response.tenant.TenantLicenseVO;
import com.cyberscraft.uep.iam.errors.TransactionErrorType;
import com.cyberscraft.uep.iam.query.TenantQueryCondition;
import com.cyberscraft.uep.iam.service.ITenantService;
import com.cyberscraft.uep.iam.service.user.IUserService;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

@RestController
public class IamTenantController implements IamTenantApi {

    private static Logger logger = LoggerFactory.getLogger(IamTenantController.class);


    @Autowired
    private IUserService iUserService;
    @Autowired
    private ITenantService tenantService;

    @Override
    public ReturnResultVO<Boolean> resetAdminPwd(TcUserResetPwdVO tcUserResetPwdVO) {
        try {
            TenantHolder.setTenantCode(tcUserResetPwdVO.getTcode());
            ReturnResultVO<Boolean> returnResultVO = new ReturnResultVO<>();
            boolean updateResult = iUserService.resetAdminPwd(tcUserResetPwdVO);
            returnResultVO.setData(updateResult);
            return returnResultVO;
        } finally {
            TenantHolder.remove();
        }
    }

    @PreAuthorize("#oauth2.hasPermission('create:tenants')")
    @Override
    public Result<Boolean> createTenant(TenantCreateRequestDto tenantCreateRequestDto) {
        return tenantService.createTenant(tenantCreateRequestDto);
    }

    @Override
    public Result<Boolean> innerCreateTenant(String apiKey, TenantCreateRequestDto tenantCreateRequestDto) {
        String CREATE_TENANT_KEY = "aaXkILhx4wT5uRpj33NiQc6WcP7LifxM0QwAmFSvKMWECMj1k8PouYlTjOT5ckyKPI1CCcfGT9z0hWfAB9t7IfsQWDFhTq2JE6sQ12znC1cgEQrHnpiIWJvcN0Uh2";
        if (StringUtils.isBlank(apiKey) || !CREATE_TENANT_KEY.equals(apiKey)) {
            throw new UserCenterException(TransactionErrorType.INVALID_REQUEST_PARAM);
        }
        return tenantService.createTenant(tenantCreateRequestDto);
    }

    @PreAuthorize("#oauth2.hasPermission('update:tenants')")
    @Override
    public Result<Boolean> updateTenantPut(String tenantId, @Valid TenantUpdateRequestDto tenantUpdateRequestDto) {
        return tenantService.updateTenant(tenantId, tenantUpdateRequestDto);
    }

    @PreAuthorize("#oauth2.hasPermission('update:tenants')")
    @Override
    public Result<Boolean> updateTenantPost(String tenantId, TenantUpdateRequestDto tenantUpdateRequestDto) {
        return tenantService.updateTenant(tenantId, tenantUpdateRequestDto);
    }

    @PreAuthorize("#oauth2.hasPermission('delete:tenants')")
    @Override
    public Result<Boolean> deleteTenant(String tenantId) {
        return tenantService.deleteTenant(tenantId);
    }

    @PreAuthorize("#oauth2.hasPermission('read:tenants')")
    @Override
    public Result<SysTenantVo> getTenant(String tenantId) {
        return tenantService.getTenantWithAdminInfo(tenantId);
    }

    @PreAuthorize("#oauth2.hasPermission('search:tenants')")
    @Override
    public ReturnResultVO<QueryPage<SysTenantVo>> query(TenantSearchDto tenantSearchDto) {
        TenantQueryCondition queryCondition = new TenantQueryCondition();
        BeanUtils.copyProperties(tenantSearchDto, queryCondition);
        QueryPage<SysTenantVo> result = tenantService.query(queryCondition);
        return new ReturnResultVO<>(result);
    }

    @Override
    public Result<Boolean> isExistTenant(String tenantId) {
        return tenantService.isExistTenant(tenantId);
    }

    @PreAuthorize("#oauth2.hasPermission('update:tenants')")
    @Override
    public Result<Boolean> changeStatusPut(@Valid TenantChangeStatusDto dto) {
        Boolean ret = tenantService.changeStatus(dto);
        Result<Boolean> rr = ResponseResult.success(ret);
        return rr;
    }

    @PreAuthorize("#oauth2.hasPermission('update:tenants')")
    @Override
    public Result<Boolean> changeStatusPost(@Valid TenantChangeStatusDto dto) {
        Boolean ret = tenantService.changeStatus(dto);
        Result<Boolean> rr = ResponseResult.success(ret);
        return rr;
    }

    @PreAuthorize("#oauth2.hasPermission('read:tenants')")
    @Override
    public Result<AppListVO> getTenantAppList(String tenantId) {
        AppListVO searchResult = tenantService.searchAvailableApps(tenantId);
        return ResponseResult.success(searchResult);
    }

    @PreAuthorize("#oauth2.hasPermission('update:tenants')")
    @Override
    public Result<Boolean> setTenantAppListPut(String tenantId, @Valid TenantAppListDto dto) {
        Boolean ret = tenantService.setTenantApps(tenantId, dto);
        Result<Boolean> rr = ResponseResult.success(ret);
        return rr;
    }

    @PreAuthorize("#oauth2.hasPermission('update:tenants')")
    @Override
    public Result<Boolean> setTenantAppListPost(String tenantId, @Valid TenantAppListDto dto) {
        Boolean ret = tenantService.setTenantApps(tenantId, dto);
        Result<Boolean> rr = ResponseResult.success(ret);
        return rr;
    }

    /**
     * 获取当前租户的授权模块信息
     *
     * @return
     */
    @Override
    public Result<TenantLicenseVO> getTenantLicense() {
        String tenantCode = TenantHolder.getTenantCode();
        final String loginUserName = UserUtil.getLoginUserName();
        if (loginUserName == null) {
            throw new UserCenterException(TransactionErrorType.NO_PERMISSION_ERROR);
        }
        TenantLicenseVO tenantIdToTenantVo = tenantService.getTenantLicense(tenantCode);
        Result<TenantLicenseVO> voResult = ResponseResult.success(tenantIdToTenantVo);
        return voResult;
    }

    @PreAuthorize("#oauth2.hasPermission('read:self_application')")
    @Override
    public Result<Map<String, String>> getLoginSecret() {
        String tenantCode = TenantHolder.getTenantCode();
        String secret = tenantService.getLoginSecret(tenantCode);
        HashMap<String, String> resp = new HashMap<>();
        resp.put("secret_key", secret);
        return ResponseResult.success(resp);
    }
}
