package com.cyberscraft.uep.iam.configuration.filters;

import com.cyberscraft.uep.common.util.ServerContext;
import com.cyberscraft.uep.iam.common.correlation.RequestCorrelation;
import com.cyberscraft.uep.iam.common.domain.resource.ServerContextThreadLocal;
import com.cyberscraft.uep.iam.common.util.IAMHttpUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.web.util.ContentCachingResponseWrapper;

import javax.servlet.FilterConfig;
import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * To print the access log in debug level.
 */
public class AccessFilter implements Filter {

    public static final String URL_PATTERN = "/*";
    private Logger logger = LoggerFactory.getLogger(getClass());

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {

        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;

        String requestURL = httpRequest.getRequestURI();
        if (requestURL.startsWith("/actuator")) {
            logger.debug("requesting {} , will not set servercontext", requestURL);
        } else {
            logger.debug("requesting {} , will set servercontext", requestURL);
            // save the request context to ThreadLocal
            ServerContextThreadLocal.setContextInfo(ServerContext.of(httpRequest));
        }

        // only print request in the first round
        if (!IAMHttpUtil.currentRequestIsAsyncDispatcher(httpRequest)) {
            String requestId = IAMHttpUtil.getCorrelationIdFromHeader(httpRequest);
            if (requestId == null) {
                logger.error("header doesn't have correlation id");
            } else {
                if (logger.isDebugEnabled()) {
                    IAMHttpUtil.logRequestHeader(requestId, httpRequest);
                }
            }

            chain.doFilter(request, response);
            return;
        }
        
        if (!logger.isDebugEnabled()){
            chain.doFilter(request, response);
            return;
        }

        // print response in the second round
        ContentCachingResponseWrapper httpResponseWrapper = new ContentCachingResponseWrapper(httpResponse);
        String requestId = IAMHttpUtil.getCorrelationIdFromHeader(httpRequest);

        if (requestId == null) {
            logger.error("header doesn't have correlation id");
        } else {
            MDC.put(RequestCorrelation.CORRELATION_ID_HEADER, requestId);
        }

        try {
            chain.doFilter(httpRequest, httpResponseWrapper);
        } finally {
            // String responseContent = new String(httpResponseWrapper.getContentAsByteArray(), "UTF-8");

            // flush cached content to response and clear cache
            httpResponseWrapper.copyBodyToResponse();

            // log http response header part
            IAMHttpUtil.logResponseHeader(requestId, httpRequest, httpResponseWrapper);

            // By default, don't print response info, because it may include sensitive info
            // HttpUtil.logResponseContent(responseContent);
        }
    }

    @Override
    public void destroy() {
    }

}
