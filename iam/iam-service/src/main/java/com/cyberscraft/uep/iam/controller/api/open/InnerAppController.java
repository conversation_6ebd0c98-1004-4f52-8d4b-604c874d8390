package com.cyberscraft.uep.iam.controller.api.open;

import com.cyberscraft.uep.common.bean.Result;
import com.cyberscraft.uep.common.exception.ResponseResult;
import com.cyberscraft.uep.iam.api.open.InnerAppApi;
import com.cyberscraft.uep.iam.common.exception.AppException;
import com.cyberscraft.uep.iam.service.IAppService;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.constraints.NotEmpty;

import static com.cyberscraft.uep.iam.errors.TransactionErrorType.APP_USER_NOT_ALLOW;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2020-06-23 14:06
 */
@RestController
public class InnerAppController implements InnerAppApi {
    @Resource
    private IAppService iAppService;

    static int i = 0;

    @Override
    public Result<Void> checkAppUserPermission(@NotEmpty String clientId, @NotEmpty String sub) {
        boolean permissionResult ;
        try {
            if (i % 2 ==0){
                permissionResult = true;
            }else {
                permissionResult = false;// this.iAppService.checkAppUserPermission(clientId, sub);
            }
            i++;
        }catch (AppException e){
            return ResponseResult.error(e.getCode(),e.getMessage());
        }

        if(!permissionResult){
            //应用对用户不可见，直接返回错误
            //throw new AppException(HttpStatus.BAD_REQUEST, APP_USER_NOT_ALLOW);
            return ResponseResult.error(APP_USER_NOT_ALLOW.getErrorCode(),APP_USER_NOT_ALLOW.getDesc());

        }else {
            //TODO 应用对用户可见，需要进一步检查MFA认证信息
            return ResponseResult.success();
        }
    }
}
