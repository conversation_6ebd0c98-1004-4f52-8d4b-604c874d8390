package com.cyberscraft.uep.iam.controller.inner;

import com.cyberscraft.uep.common.dto.ReturnResultVO;
import com.cyberscraft.uep.iam.api.inner.EtlDataSyncApi;
import com.cyberscraft.uep.iam.common.constants.DaoConstants;
import com.cyberscraft.uep.iam.dto.response.SysTenantVo;
import com.cyberscraft.uep.iam.service.ITenantService;
import com.cyberscraft.uep.iam.service.connector.IConnectorService;
import com.cyberscraft.uep.iam.service.data.IPushConnectorMessageSend;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
public class EtlDataSyncController implements EtlDataSyncApi {
    private static final Logger logger = LoggerFactory.getLogger(EtlDataSyncController.class);

    @Resource
    private ITenantService tenantService;

    @Autowired
    private IConnectorService connectorService;

    @Autowired
    private IPushConnectorMessageSend pushConnectorMessageSend;

    @Override
    public ReturnResultVO<?> etlSync(String datasourceId) {
        logger.info("Current thread {} process sync datasource id {}", Thread.currentThread().getName(), datasourceId);

        //得到所有的租户
        List<SysTenantVo> tenantList = tenantService.getTenantList();
        for (SysTenantVo tenantInfo : tenantList) {
            logger.info("tenant: {}, {}", tenantInfo.getTenantId(), tenantInfo.getName());
            if(!tenantInfo.getTenantId().equals(DaoConstants.IAM_TENANT_ID)) {
                logger.info("Ready to send etl connector sync task to mq, tenant :{}", tenantInfo.getTenantId());
//                connectorService.syncDataByTenant(tenantInfo.getTenantId(), datasourceId, true);
                pushConnectorMessageSend.sendAggrMessage(tenantInfo.getTenantId(), datasourceId, true);
                logger.info("OK to send etl connector sync task to mq, tenant: {}}", tenantInfo.getTenantId());
            }

        }
        return new ReturnResultVO();
    }
}
