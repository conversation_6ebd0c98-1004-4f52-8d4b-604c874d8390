package com.cyberscraft.uep.iam.configuration.db;

import com.cyberscraft.uep.iam.errors.TransactionErrorType;

/**
 * <AUTHOR>
 * @Date 2025/1/7 18:29
 * @Version 1.0
 * @Description
 */
public class UniqueFieldConfig {
    private String tableName;
    private String fieldName;
    private TransactionErrorType errorType;

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public TransactionErrorType getErrorType() {
        return errorType;
    }

    public void setErrorType(TransactionErrorType errorType) {
        this.errorType = errorType;
    }

    public UniqueFieldConfig(String tableName, String fieldName, TransactionErrorType errorType) {
        this.tableName = tableName;
        this.fieldName = fieldName;
        this.errorType = errorType;
    }
}
