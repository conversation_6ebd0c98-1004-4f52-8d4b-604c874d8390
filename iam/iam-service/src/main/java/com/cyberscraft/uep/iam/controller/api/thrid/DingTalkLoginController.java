package com.cyberscraft.uep.iam.controller.api.thrid;

import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.request.OapiSnsGetuserinfoBycodeRequest;
import com.dingtalk.api.response.OapiSnsGetuserinfoBycodeResponse;
import com.taobao.api.ApiException;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;

/**
 * <p>
 *     //TODO 后续做WEB端扫码登录流程用到
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2020-03-02 14:13
 */
@Controller
public class DingTalkLoginController {

    @RequestMapping(value = "/iam/api/login/dingtalk")
    public String getCode(HttpServletRequest request) throws ApiException {
        String code = request.getParameter("code");
        System.out.println("接收到的code:"+code);

        //TODO 调用钉钉接口，获取用户信息
        DefaultDingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/sns/getuserinfo_bycode");
        OapiSnsGetuserinfoBycodeRequest req = new OapiSnsGetuserinfoBycodeRequest();
        req.setTmpAuthCode(code);
        OapiSnsGetuserinfoBycodeResponse response = client.execute(req,"dingoafwl0dvhuxe1fklca","LSC9o6LuEC5yaL75tkza5GRkazQHezzl-peRIgSJ-cWkr0ObO1bc4uTuprkKaryv");


        //TODO 和IAM进行账号绑定操作，如果已经绑定，则走内部的认证流程（参考一次性密码登录、手机验证码登录）
        return "redirect:/iam/index.html";
    }
}
