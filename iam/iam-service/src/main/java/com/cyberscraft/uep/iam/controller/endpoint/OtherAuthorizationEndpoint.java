package com.cyberscraft.uep.iam.controller.endpoint;

import com.cyberscraft.uep.iam.common.exception.UserCenterException;
import com.cyberscraft.uep.iam.common.exception.UserCenterViewException;
import com.cyberscraft.uep.iam.constants.UrlConstants;
import com.cyberscraft.uep.iam.dto.response.login.LoginNextActionVO;
import com.cyberscraft.uep.iam.errors.TransactionErrorType;
import com.cyberscraft.uep.iam.service.IAppService;
import com.cyberscraft.uep.iam.service.LoginService;
import com.cyberscraft.uep.iam.service.auditlog.annotation.Auditable;
import com.cyberscraft.uep.iam.service.auditlog.constant.AuditEventSubtype;
import com.cyberscraft.uep.iam.service.auditlog.constant.AuditEventType;
import com.cyberscraft.uep.iam.service.auditlog.constant.AuditType;
import com.cyberscraft.uep.iam.service.oidc.exceptions.MultiFactorAccessDeniedException;
import com.cyberscraft.uep.iam.service.oidc.provider.AppStoreAuthAuthenticator;
import com.cyberscraft.uep.iam.service.oidc.token.entity.MultiFactorAuthenticationToken;
import com.cyberscraft.uep.iam.service.oidc.util.SecureRequestCheck;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import io.swagger.annotations.Api;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.InsufficientAuthenticationException;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.security.Principal;

/**
 * <p>
 *  非标协议的认证
 * </p>
 *
 * <AUTHOR>
 * @since 2022/7/1 1:33 下午
 */
@RestController
@Api(value = "非标协议认证相关API", tags = "Other Protocol Auth")
public class OtherAuthorizationEndpoint {

    private static final Logger logger = LoggerFactory.getLogger(OtherAuthorizationEndpoint.class);

    @Autowired
    LoginService loginService;

    @Autowired
    IAppService appService;

    @Autowired
    AppStoreAuthAuthenticator appstoreAuthAuthenticator;

    /**
     * 应用后台认证，支持NCC之类的应用
     * @param client_id 应用ID
     */
    @RequestMapping(value = {UrlConstants.URL_PREFIX_IAM_OTHER + "/{clientId}/login"}, method = RequestMethod.GET)
    @ResponseBody
    @Auditable(auditType = AuditType.LOGIN, eventType = AuditEventType.LOGIN, eventSubtype = AuditEventSubtype.AUTHORIZE,
            fields = "targetId=#{#client_id};;targetName=#{#client_id};;clientId=#{#client_id}")
    public void appstoreLogin(@PathVariable("clientId") String client_id, Principal principal){
        logger.info("other client:{} login", client_id);
        HttpServletResponse response = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
        if (!(principal instanceof MultiFactorAuthenticationToken)) {
            logger.warn("InsufficientAuthenticationException");
            throw new InsufficientAuthenticationException(
                    "User must be authenticated with Spring Security before authorization can be completed.");
        }else {
            try {
                LoginNextActionVO loginNextActionVO = loginService.whereToGo(client_id, true);
                if (!loginNextActionVO.isContinue()) {
                    throw new MultiFactorAccessDeniedException(loginNextActionVO.getNext(),
                            "User must be authenticated before authorization can be completed.");
                }
            } catch (UserCenterException e) {
                throw new MultiFactorAccessDeniedException(LoginNextActionVO.PRIMARY,
                        "User must be authenticated before authorization can be completed.");
            }
            if (!appService.checkAppUserPermission(client_id, SecureRequestCheck.getSubject((Authentication) principal))) {
                String desc = TransactionErrorType.APP_ACCESS_DENIED_ERROR.getDesc(client_id);
                throw new UserCenterViewException(TransactionErrorType.APP_ACCESS_DENIED_ERROR, desc, TenantHolder.getTenantCode());
            }
        }

        String thirdPartyUrl = null;
        String tcode = TenantHolder.getTenantCode();
        try {
            logger.info("start decideAppStoreRedirectUrl");
            thirdPartyUrl = appstoreAuthAuthenticator.decideAppStoreRedirectUrl(tcode, client_id);
        }catch (Exception e){
            logger.error("decideAppStoreRedirectUrl error", e);
            throw new UserCenterViewException(e.getMessage(), tcode);
        }
        try {
            logger.info("thirdPartyUrl:{}", thirdPartyUrl);
            response.setHeader("Referrer-Policy", "no-referrer");
            response.sendRedirect(thirdPartyUrl);
            return;
        } catch (IOException e) {
            logger.warn("url:{},error:{}", thirdPartyUrl, e.getMessage());
            throw new UserCenterViewException(TransactionErrorType.APP_REDIRECT_ERROR, tcode);
        }
    }
}
