package com.cyberscraft.uep.iam.controller.api.connector.controller;

import com.cyberscraft.uep.account.client.constant.ThirdPartyEventTagConstant;
import com.cyberscraft.uep.account.client.domain.*;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountErrorType;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountException;
import com.cyberscraft.uep.account.client.service.IExternalConnectService;
import com.cyberscraft.uep.account.client.service.IThirdPartyEventExecutorService;
import com.cyberscraft.uep.account.client.util.AccountUtil;
import com.cyberscraft.uep.common.bean.Result;
import com.cyberscraft.uep.common.exception.ResponseResult;
import com.cyberscraft.uep.common.util.CollectionUtils;
import com.cyberscraft.uep.iam.common.constants.UrlConstant;
import com.cyberscraft.uep.iam.dto.domain.ConnectorEventDomain;
import com.cyberscraft.uep.iam.dto.enums.ConnectorEventType;
import com.cyberscraft.uep.iam.entity.ConnectorEntity;
import com.cyberscraft.uep.iam.service.ITenantService;
import com.cyberscraft.uep.iam.service.connector.IConnectorService;
import com.cyberscraft.uep.iam.service.transfer.ConnectorTransfer;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 *  用户连接器事件通知
 * </p>
 *
 * <AUTHOR>
 * @since 2023/8/15 2:39 下午
 */
@RestController
public class ConnectorEventController {

    @Autowired
    private ITenantService tenantService;

    @Autowired
    private IConnectorService connectorService;

    @Resource
    private ConnectorTransfer connectorTransfer;

    @Resource
    protected IExternalConnectService externalConnectService;

    @Resource
    private IThirdPartyEventExecutorService thirdPartyEventExecutorService;

    private final static Logger LOG = LoggerFactory.getLogger(ConnectorEventController.class);

    @PostMapping(value = UrlConstant.IAM_CONNECTOR_EVENT_API)
    @ResponseBody
    public Result<String> notify(@PathVariable("tenantId") String tenantId,
                                 @PathVariable("connectorId") Long connectorId,
                                 @PathVariable("apiKey") String apiKey,
                                 @RequestBody ConnectorEventDomain eventData) {

        LOG.info("connector event messsage, tenant:{}, connectorId:{}, data:{}", tenantId, connectorId, eventData);

        tenantService.checkTenantId(tenantId);
        TenantHolder.setTenantCode(tenantId);

        ConnectorEntity connectorEntity = connectorService.searchDsById(connectorId);
        if (!connectorEntity.getApiKey().equals(apiKey)) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.EVENT_CALLBACK_PROVIDER_INVALID);
        }
        Connector connector = connectorTransfer.entity2Domain(connectorEntity);

        if (connector == null) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.DS_NOT_FOUND_ERROR);
        }

        ConnectorEventType eventType = eventData.getEventType();
        Object eventBody = eventData.getEventBody();
        List<String> externalIds = eventData.getExternalIds();

        ThirdPartyEvent<GroupEventBody> groupEvent = new ThirdPartyEvent<>();
        groupEvent.setEventTime(LocalDateTime.now());
        groupEvent.setTenantId(connector.getTenantId());
        GroupEventBody deptBody = new GroupEventBody();
        deptBody.setConnector(connector);

        ThirdPartyEvent<UserEventBody> userEvent = new ThirdPartyEvent<>();
        userEvent.setEventTime(LocalDateTime.now());
        userEvent.setTenantId(connector.getTenantId());
        UserEventBody userBody = new UserEventBody();
        userBody.setConnector(connector);

        switch (eventType) {
            case ADD_DEPARTMENT:
            case UPDATE_DEPARTMENT:
                groupEvent.setEventTag(ThirdPartyEventTagConstant.GROUP_ADD_UPDATE);

                if (CollectionUtils.isEmpty(externalIds)) {
                    if (eventBody instanceof List) {
                        deptBody.setGroups((List) eventBody);
                    } else {
                        deptBody.setGroups(Arrays.asList(eventBody));
                    }
                } else {
                    deptBody.setCodes(externalIds);
                }

                groupEvent.setData(deptBody);
                thirdPartyEventExecutorService.onEvent(groupEvent);

                break;
            case DELETE_DEPARTMENT:
                groupEvent.setEventTag(ThirdPartyEventTagConstant.GROUP_REMOVE);
                if (CollectionUtils.isEmpty(externalIds)) {
                    if (eventBody instanceof List) {
                        List<ConnectorOrg<String, Object>> connectorOrgs = AccountUtil.to((List) eventBody, externalConnectService.getExternalOrgFullProfile(connector));
                        List<String> orgIds = connectorOrgs.stream().map(e -> e.getIdValue().toString()).collect(Collectors.toList());
                        deptBody.setCodes(orgIds);
                    } else {
                        ConnectorOrg<String, Object> connectorOrg = AccountUtil.to(eventBody, externalConnectService.getExternalOrgFullProfile(connector));
                        deptBody.setCodes(Arrays.asList(connectorOrg.getIdValue().toString()));
                    }
                } else {
                    deptBody.setCodes(externalIds);
                }
                groupEvent.setData(deptBody);
                thirdPartyEventExecutorService.onEvent(groupEvent);

                break;
            case ADD_USER:
            case UPDATE_USER:
                userEvent.setEventTag(ThirdPartyEventTagConstant.USER_ADD_UPDATE);

                if (CollectionUtils.isEmpty(externalIds)) {
                    if (eventBody instanceof List) {
                        userBody.setAccounts((List) eventBody);
                    } else {
                        userBody.setAccounts(Arrays.asList(eventBody));
                    }
                } else {
                    userBody.setUserIds(externalIds);
                }

                userEvent.setData(userBody);
                thirdPartyEventExecutorService.onEvent(userEvent);

                break;
            case DELETE_USER:
                userEvent.setEventTag(ThirdPartyEventTagConstant.USER_REMOVE);

                if (CollectionUtils.isEmpty(externalIds)) {
                    if (eventBody instanceof List) {
                        List<ConnectorUser<String, Object>> connectorUsers = AccountUtil.to((List) eventBody, externalConnectService.getExternalUserFullProfile(connector));
                        List<String> userIds = connectorUsers.stream().map(e -> e.getIdValue().toString()).collect(Collectors.toList());
                        userBody.setUserIds(userIds);
                    } else {
                        ConnectorUser<String, Object> connectorUser = AccountUtil.to(eventBody, externalConnectService.getExternalUserFullProfile(connector));
                        userBody.setUserIds(Arrays.asList(connectorUser.getIdValue().toString()));
                    }
                } else {
                    userBody.setUserIds(externalIds);
                }
                userEvent.setData(userBody);
                thirdPartyEventExecutorService.onEvent(userEvent);

                break;
        }

        return ResponseResult.success();
    }




}
