package com.cyberscraft.uep.iam.controller.api;

import com.cyberscraft.uep.iam.api.UserCertApi;
import com.cyberscraft.uep.iam.service.auditlog.annotation.Auditable;
import com.cyberscraft.uep.iam.service.auditlog.constant.AuditEventSubtype;
import com.cyberscraft.uep.iam.service.auditlog.constant.AuditEventType;
import com.cyberscraft.uep.iam.dto.request.CertStatusChangeRequestVO;
import com.cyberscraft.uep.iam.dto.response.ReturnResultVO;
import com.cyberscraft.uep.iam.dto.response.UserCertificateListVO;
import com.cyberscraft.uep.iam.service.IUserCertService;
import org.hibernate.validator.constraints.NotEmpty;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

@RestController
public class UserCertController implements UserCertApi {
    @Resource
    private IUserCertService iUserCertService;

    @PreAuthorize("#oauth2.hasPermission('read:user_full_info')")
    @Override
    public ReturnResultVO<UserCertificateListVO> getUserCerts(String username) {
        ReturnResultVO<UserCertificateListVO> rr = new ReturnResultVO<>();
        UserCertificateListVO searchResult = iUserCertService.getCertsByUsername(username);
        rr.setData(searchResult);
        return rr;
    }

    @Auditable(eventType = AuditEventType.USER, eventSubtype = AuditEventSubtype.DELETE_USER_CERT,
            fields = "targetName=#{#username};;" +
                    "parameters={\"cert_id\": \"#{#certId}\"}")
    @PreAuthorize("#oauth2.hasPermission('update:users')")
    @Override
    public ReturnResultVO<?> deleteCert(String username, String certId) {
        ReturnResultVO<UserCertificateListVO> rr = new ReturnResultVO<>();
        iUserCertService.deleteCert(username, certId);
        return rr;
    }

//    @Auditable(eventType = AuditEventType.USER, eventSubtype = AuditEventSubtype.DISABLE_USER_CERT,
//            fields = "targetName=#{#username};;" +
//                    "eventSubtype='#{#status.getStatus()}'=='ACTIVE' ? " +
//                    "'#{T(com.cyberscraft.uep.iam.auditlog.constant.AuditEventSubtype).ENABLE_USER_CERT.code()}':" +
//                    "'#{T(com.cyberscraft.uep.iam.auditlog.constant.AuditEventSubtype).DISABLE_USER_CERT.code()}}';;" +
//                    "parameters={\"cert_id\": \"#{#certId}\"}")
    @PreAuthorize("#oauth2.hasPermission('update:users')")
    @Override
    public ReturnResultVO<?> changeCertStatus(@NotEmpty String username, String certId, @Valid CertStatusChangeRequestVO status) {
        ReturnResultVO<UserCertificateListVO> rr = new ReturnResultVO<>();
        iUserCertService.changeStatus(username, certId, status.getStatus());
        return rr;
    }
}
