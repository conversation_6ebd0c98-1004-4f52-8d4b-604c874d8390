package com.cyberscraft.uep.iam.controller.api;

import com.alibaba.fastjson.JSONObject;
import com.cyberscraft.uep.common.bean.Result;
import com.cyberscraft.uep.common.dto.QueryPage;
import com.cyberscraft.uep.common.dto.ReturnResultVO;
import com.cyberscraft.uep.common.exception.ResponseResult;
import com.cyberscraft.uep.iam.api.UserApi;
import com.cyberscraft.uep.iam.common.constants.DaoConstants;
import com.cyberscraft.uep.iam.common.correlation.InScopeOrgs;
import com.cyberscraft.uep.iam.common.domain.configparams.CfgUserPolicy;
import com.cyberscraft.uep.iam.common.enums.LocalUserAttr;
import com.cyberscraft.uep.iam.common.exception.OrganizationException;
import com.cyberscraft.uep.iam.common.exception.UserCenterException;
import com.cyberscraft.uep.iam.common.exception.UserException;
import com.cyberscraft.uep.iam.common.util.DateUtils;
import com.cyberscraft.uep.iam.common.util.OrgUtil;
import com.cyberscraft.uep.iam.common.util.UserUtil;
import com.cyberscraft.uep.iam.configuration.aop.RequestResponseLogger;
import com.cyberscraft.uep.iam.dbo.ExternalUserDBO;
import com.cyberscraft.uep.iam.dto.enums.CreatedMode;
import com.cyberscraft.uep.iam.dto.enums.ThirdIdpTypeEnum;
import com.cyberscraft.uep.iam.dto.enums.UserStatus;
import com.cyberscraft.uep.iam.dto.request.*;
import com.cyberscraft.uep.iam.dto.request.login.BasicAuthDTO;
import com.cyberscraft.uep.iam.dto.request.login.RegisterDTO;
import com.cyberscraft.uep.iam.dto.response.ExcelUserVO;
import com.cyberscraft.uep.iam.dto.response.UserExtendFieldVo;
import com.cyberscraft.uep.iam.dto.response.UserVO;
import com.cyberscraft.uep.iam.dto.response.UsersRevokedVO;
import com.cyberscraft.uep.iam.entity.ApplyProcessLogEntity;
import com.cyberscraft.uep.iam.entity.ExternalUserEntity;
import com.cyberscraft.uep.iam.entity.ThirdIDPEntity;
import com.cyberscraft.uep.iam.entity.UserEntity;
import com.cyberscraft.uep.iam.errors.TransactionErrorDescription;
import com.cyberscraft.uep.iam.errors.TransactionErrorType;
import com.cyberscraft.uep.iam.query.UserQueryCondition;
import com.cyberscraft.uep.iam.service.*;
import com.cyberscraft.uep.iam.service.access.annotation.AccessControl;
import com.cyberscraft.uep.iam.service.access.constant.ResourceType;
import com.cyberscraft.uep.iam.service.auditlog.annotation.Auditable;
import com.cyberscraft.uep.iam.service.auditlog.constant.AuditEventSubtype;
import com.cyberscraft.uep.iam.service.auditlog.constant.AuditEventType;
import com.cyberscraft.uep.iam.service.auditlog.constant.AuditLevel;
import com.cyberscraft.uep.iam.service.oidc.domain.UserInfo;
import com.cyberscraft.uep.iam.service.oidc.sns.thrid.WechatAuthService;
import com.cyberscraft.uep.iam.service.transfer.TypeMapper;
import com.cyberscraft.uep.iam.service.transfer.UserTransfer;
import com.cyberscraft.uep.iam.service.user.IExcelUserService;
import com.cyberscraft.uep.iam.service.user.IExternalUserService;
import com.cyberscraft.uep.iam.service.user.IUserService;
import com.cyberscraft.uep.mq.service.IMessageSendClient;
import com.cyberscraft.uep.mq.vo.MessageEntry;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Maps;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.time.LocalDateTime;
import java.util.*;

import static com.cyberscraft.uep.iam.common.constants.DaoConstants.LDAP_BIND_ERROR_NO_OK;
import static com.cyberscraft.uep.iam.errors.TransactionErrorDescription.INVALID_REQUEST_PARAM_ERROR_DESC;
import static org.springframework.http.MediaType.APPLICATION_OCTET_STREAM_VALUE;


@RestController
public class UserController implements UserApi {
    private static final Logger logger = LoggerFactory.getLogger(UserController.class);

    @Autowired
    IUserService iUserService;

    @Autowired
    IExcelUserService excelUserService;

    @Autowired
    private ISendUserMessageLogService iSendUserMessageLogService;

    @Autowired
    private UserTransfer userTransfer;

    @Autowired
    private ExternalUserDBO externalUserDBO;
    @Autowired
    private IThirdIDPService thirdIDPService;
    @Autowired
    private IExternalUserService externalUserService;
    @Autowired
    private LoginService loginService;
    @Autowired
    private IConfigService configService;
    @Autowired
    private WechatAuthService wechatAuthService;
    @Autowired
    private IApplyProcessLogService applyProcessLogService;

    @Auditable(eventType = AuditEventType.USER, eventSubtype = AuditEventSubtype.NEW,
            fields = "targetName=#{#userInfo.get('username')};;" +
                    "parameters=#{T(com.cyberscraft.uep.iam.common.util.JacksonJsonUtil).beanToJson(#userInfo)}")
    @PreAuthorize("#oauth2.hasPermission('create:users')")
    //@ControllerLog(description = "createUser", hidden_attributes = "['password']")
    @AccessControl(permission = "create:users", sourceType = ResourceType.USER,
            currentUser = "targetName=#{#userInfo.get('username')};;parameters=#{T(com.cyberscraft.uep.iam.common.util.JacksonJsonUtil).beanToJson(#userInfo)}")
    @Override
    public ReturnResultVO<Map<String, Object>> createUser(Map<String, Object> userInfo, Boolean welcome) {
        logger.debug("request args {}", RequestResponseLogger.toPersonBasicInfo(userInfo));
        Map<String, Object> map = new HashMap<>();
        for (String key : userInfo.keySet()) {
            Object value = userInfo.get(key);
            if (value != null && !StringUtils.isBlank(value.toString())) {
                map.put(key, value);
            }
        }
        Long uid = iUserService.createUser(map, welcome, CreatedMode.BY_ADMIN, Boolean.FALSE);
        Map<String, Object> uuid = userTransfer.toDtoMap(Maps.newHashMap(LocalUserAttr.sub.getFieldName(), uid));

        ReturnResultVO<Map<String, Object>> result = new ReturnResultVO<>();
        result.setData(uuid);
        return result;
    }

    @Auditable(eventType = AuditEventType.USER, eventSubtype = AuditEventSubtype.NEW)
    @Override
    public ReturnResultVO<Map<String, Object>> register(RegisterDTO registerDTO) {
        TenantHolder.setTenantCode(registerDTO.getTcode());

        String openId = registerDTO.getOpenId();
        Map<String, Object> map = new HashMap<>();
        map.put("tcode", registerDTO.getTcode());
        map.put("username", registerDTO.getUserName());
        map.put("password", registerDTO.getPassWord());
        map.put("name", registerDTO.getRealName());
        Map<String, Object> uuid = new HashMap<>();

        ThirdIDPEntity thirdIDPEntity = thirdIDPService.getById(Long.valueOf(registerDTO.getConnectorId()));
        ExternalUserEntity externalUserEntity = externalUserDBO.getByExternalId(thirdIDPEntity.getId(), openId);
        if (StringUtils.isNotEmpty(thirdIDPEntity.getJitConfig())) {
            JSONObject json = JSONObject.parseObject(thirdIDPEntity.getJitConfig());
            List<String> orgids = new ArrayList<>();
            orgids.add(json.getString("group_id"));
            map.put("org_ids", orgids);
        }

        //已创建用户
        UserInfo userInfo = iUserService.getUserInfoByUsername(registerDTO.getUserName());
        //用户名查找审核中用户
        ApplyProcessLogEntity applyProcessLogEntity = applyProcessLogService.getAppAuthApproveLogByUsername(registerDTO.getUserName());
        //用户名重复判断
        if (null != userInfo || null != applyProcessLogEntity) {
            UserCenterException userCenterException = new UserCenterException(HttpStatus.OK, TransactionErrorType.NAME_IS_EXIST);
            userCenterException.setCorrelationId(thirdIDPEntity.getIdpType());
            logger.info("用户名已存在");
            throw userCenterException;
        }

        //绑定关系判断
        //无绑定
        if (null == externalUserEntity) {
            //是否注册审核
            Boolean userCreateVerify = false;
            CfgUserPolicy cfgUserPolicy = configService.getUserPolicyCfg();
            if (null != cfgUserPolicy.getUserCreateVerify()) {
                userCreateVerify = cfgUserPolicy.getUserCreateVerify();
            }
            //新建本地用户
            Long uid = iUserService.createUser(map, false, CreatedMode.BY_WECHAT, !userCreateVerify);
            //关联用户
            ExternalUserEntity newExternalUserEntity = new ExternalUserEntity();
            newExternalUserEntity.setUserId(uid);
            newExternalUserEntity.setExternalId(openId);
            newExternalUserEntity.setExternalOpenId(openId);
            newExternalUserEntity.setCreateTime(LocalDateTime.now());
            newExternalUserEntity.setSyncDirection(0);
            newExternalUserEntity.setTenantId(registerDTO.getTcode());
            newExternalUserEntity.setConnectorId(thirdIDPEntity.getId());
            newExternalUserEntity.setSyncBatchNo(0);
            newExternalUserEntity.setType(ThirdIdpTypeEnum.getValue(thirdIDPEntity.getIdpType()));
            externalUserDBO.save(newExternalUserEntity);
            uuid = userTransfer.toDtoMap(Maps.newHashMap(LocalUserAttr.sub.getFieldName(), uid));
            //有绑定
        } else {
            //用户id查找本地用户
            UserEntity userEntity = iUserService.getUser(externalUserEntity.getUserId());
            //用户id查找审核中用户
            ApplyProcessLogEntity applyProcessLogEntity2 = applyProcessLogService.getAppAuthApproveLogByUserId(externalUserEntity.getUserId());
            //已被删除
            if (userEntity == null && null == applyProcessLogEntity2) {
                externalUserDBO.removeById(externalUserEntity.getId());
                register(registerDTO);

                //该微信号已经绑定其他账号
            } else {
                UserCenterException userCenterException = new UserCenterException(HttpStatus.OK, TransactionErrorType.USER_WECHAT_IS_USED);
                userCenterException.setCorrelationId(thirdIDPEntity.getIdpType());
                logger.info("该微信号已经绑定其他账号");
                throw userCenterException;
            }
        }

        ReturnResultVO<Map<String, Object>> result = new ReturnResultVO<>();
        result.setData(uuid);
        return result;
    }


    @PreAuthorize("#oauth2.hasPermission('delete:users')")
    @Override
    public ReturnResultVO<Boolean> checkDeletedUsers() {
        return new ReturnResultVO<>(iUserService.checkDeletedUser());
    }

    @Auditable(eventType = AuditEventType.USER, eventSubtype = AuditEventSubtype.DELETE,
            fields = "targetName=#{#users.getUsernames()}")
    @PreAuthorize("#oauth2.hasPermission('delete:users')")
    @Override
    public ReturnResultVO<Map<String, Object>> deleteUsers(UserDeleteVO users) throws UserCenterException {
        return new ReturnResultVO<>(iUserService.removeUserByUid(users.getUsernames()));
    }


    @PreAuthorize("#oauth2.hasPermission('search:users')")
    @Override
    public ReturnResultVO<QueryPage<Map<String, Object>>> searchUserWithFuzzyWord(
            String filter,
            UserStatus status,
            String org_id,
            Boolean includeUsersInSubOrgs,
            Integer pageNumber,
            Integer pageSize,
            List<String> sort,
            Set<String> requestAttrs)
            throws UserCenterException {

        String[] attrs = null;
        if (requestAttrs != null && !requestAttrs.isEmpty()) {
            requestAttrs.remove("readonly");
            attrs = requestAttrs.toArray(new String[0]);
        }

        UserQueryCondition queryCondition = new UserQueryCondition();
        queryCondition.setFilter(filter);
        queryCondition.setIncludeUsersInSubOrgs(includeUsersInSubOrgs);
        try {
            Long orgId = OrgUtil.orgIdConver(org_id);
            //根组织的时候，查询全部
            if (DaoConstants.ROOT_ORG_ID.equals(orgId)) {
                orgId = null;
            }
            queryCondition.setOrgId(orgId);
        } catch (OrganizationException e) {
            queryCondition.setOrgId(null);
        }
        queryCondition.setStatus(TypeMapper.asInt(status));
        queryCondition.setRequestAttrs(attrs);
        QueryPage<Map<String, Object>> queryPage = new QueryPage(pageNumber, pageSize, sort);
        QueryPage<Map<String, Object>> result = iUserService.searchUserWithFuzzyWord(queryCondition, queryPage);
        return new ReturnResultVO<>(result);
    }

    @Override
    @PreAuthorize("#oauth2.hasPermission('search:users')")
    public ReturnResultVO<QueryPage<Map<String, Object>>> searchUserWithFuzzyWord(UserSearchDto userSearchDto) {
        String[] attrs = null;
        if (userSearchDto.getAttrs() != null && !userSearchDto.getAttrs().isEmpty()) {
            userSearchDto.getAttrs().remove("readonly");
            attrs = userSearchDto.getAttrs().toArray(new String[0]);
        }

        UserQueryCondition queryCondition = new UserQueryCondition();
        queryCondition.setSearchField(userSearchDto.getSearchField());
        queryCondition.setFilter(userSearchDto.getFilter());
        queryCondition.setIncludeUsersInSubOrgs(userSearchDto.getReturnUsersInSubOrg());
        try {
            Long orgId = OrgUtil.orgIdConver(userSearchDto.getOrgId());
            queryCondition.setOrgId(orgId);
        } catch (OrganizationException e) {
            queryCondition.setOrgId(null);
        }
        queryCondition.setStatus(TypeMapper.asInt(userSearchDto.getStatus()));
        queryCondition.setRequestAttrs(attrs);
        queryCondition.setFields(userSearchDto.getFields());
        queryCondition.setUserIds(userSearchDto.getUserIds());
        QueryPage<Map<String, Object>> queryPage = new QueryPage(userSearchDto.getPage(), userSearchDto.getSize(), userSearchDto.getSort());

        Map<String, Object> queryMap = new HashMap<>();
        List<Long> orgIds = iUserService.getOrgIds(queryCondition, queryMap);
        // 解析用户基础字段 和 扩展字段
        List<UserSearchDto.Fields> baseField = new ArrayList<>();
        List<UserSearchDto.Fields> extendField = new ArrayList<>();
        List<UserSearchDto.Fields> positions = new ArrayList<>();
        iUserService.generateQueryCondition(queryCondition, baseField, extendField, positions, queryMap);

        QueryPage<Map<String, Object>> result = iUserService.searchUserWithFuzzyWord(queryCondition, queryPage, userSearchDto.getPage(), userSearchDto.getSize(), queryMap, baseField, extendField, positions, orgIds);
        // 掩码用户敏感信息
        UserUtil.maskListMapFields(result.getItems());

        return new ReturnResultVO<>(result);
    }

    @Override
    @PreAuthorize("#oauth2.hasPermission('search:users')")
    public ReturnResultVO<List<UserExtendFieldVo>> getExtendField() {
        return new ReturnResultVO<>(iUserService.getExtendField());
    }

    @PreAuthorize("#oauth2.hasPermission('search:users')")
    @Override
    public ReturnResultVO<List<Map<String, Object>>> searchUserByIds(List<String> userIds) {
        return new ReturnResultVO<>(iUserService.searchUserByIds(userIds));
    }

    @PreAuthorize("#oauth2.hasPermission('search:users')")
    @Override
    public ReturnResultVO<List<UserVO>> searchUserWithInfo(String filter) {
        return new ReturnResultVO<>(iUserService.searchUserWithInfo(filter));
    }

    /**
     * incremental search by username/name/email prefix, (starts with search)
     */

    @PreAuthorize("#oauth2.hasPermission('no_scope_search:users')")
    @Override
    public ReturnResultVO<List<Map<String, Object>>> incsearch(String keyword, int limit, String... attrs) {
        final Set<String> inScopeOrgIds = InScopeOrgs.get();
        InScopeOrgs.clear();
        return new ReturnResultVO<>(
                iUserService.incsearch(inScopeOrgIds, keyword, limit, attrs));
    }

    @Override
    @PreAuthorize("#oauth2.hasPermission('search:users')")
    public ReturnResultVO<List<Map<String, Object>>> search(Map<String, Object> queryBody, @NotEmpty String... attrs) {
        return new ReturnResultVO<>(iUserService.search(queryBody, attrs));
    }

    @Override
    public ReturnResultVO<String> checkUserOfPush(UserUpdateDto userUpdate) {
        return new ReturnResultVO<>(iUserService.checkUserOfPush(userUpdate));
    }

    private int toDepth(Boolean includeUsersInSubOrgs) {
        if (includeUsersInSubOrgs == null) {
            return 0;  // default to get users in sub orgs
        }

        if (includeUsersInSubOrgs) {
            return 0;
        } else {
            return 1;
        }
    }

    @Override
    public ReturnResultVO<?> verifyUserAccount(BasicAuthDTO loginInfoVO) throws UserCenterException {
        String loginId = loginInfoVO.getUsername();
        String password = loginInfoVO.getPassword();
        UserEntity userEntity;
        userEntity = iUserService.getUserInfoByLoginId(loginId);
        if (null == userEntity) {
            throw new UserException(HttpStatus.BAD_REQUEST, TransactionErrorType.USER_NOT_FOUND);
        }
        if (!UserStatus.isActive(userEntity.getStatus())) {
            logger.warn("user {} not active", userEntity.getUsername());
            throw new UserException(TransactionErrorType.USER_INVALID);
        }
        ReturnResultVO<?> rr = new ReturnResultVO<>();
        int resultCode = iUserService.verifyUserAccount(userEntity.getUsername(), password);
        if (resultCode != LDAP_BIND_ERROR_NO_OK) {
            //com.cyberscraft.uep.iam.opendj.constants.Constant.LDAP_BIND_ERROR_NO_TIME_BEFORE_EXPIRATION:
            rr.setError(String.valueOf(TransactionErrorDescription.LDAP_TIME_BEFORE_EXPIRATION_ERROR_CODE));
            rr.setErrorDescription(String.valueOf(-resultCode));
        }
        return rr;
    }


    @Override
    @PreAuthorize("#oauth2.hasPermission('update:users')")
    @Auditable(eventType = AuditEventType.USER, eventSubtype = AuditEventSubtype.UPDATE,
            fields = "targetId=#{#status.getUsernames().toString()};;" +
                    "targetName=#{#status.getUsernames().toString()};;" +
                    "parameters=#{T(com.cyberscraft.uep.common.util.JsonUtil).obj2Str(#status)}")
    @AccessControl(permission = "update:users", sourceType = ResourceType.USER,
            currentUser = "targetName=#{#status.getUsernames()}")
    public ReturnResultVO<?> changeUserStatus(ChangeUserStatusVO status) {
        List<String> errors = iUserService.changeStatus(status.getUsernames(), status.getStatus());
        ReturnResultVO<List<String>> result = new ReturnResultVO<>();
        if (errors != null && errors.size() > 0) {
            result.setErrorDescription(errors + " does not exist");
        }
        return result;
    }


    @Auditable(eventType = AuditEventType.USER, eventSubtype = AuditEventSubtype.RESET_PASSWORD,
            auditLevel = AuditLevel.ALWAYS,
            fields = "targetName=#{#passwordByAdminVO.getUsername()}")
    @PreAuthorize("#oauth2.hasPermission('update:users')")
    @ResponseBody
    @Override
    public ReturnResultVO<?> resetUserPWD(ChangePasswordByAdminVO passwordByAdminVO) {
        if (passwordByAdminVO == null) {
            throw new UserException(HttpStatus.BAD_REQUEST, TransactionErrorType.INVALID_REQUEST_PARAM, INVALID_REQUEST_PARAM_ERROR_DESC);
        }
        if (StringUtils.isBlank(passwordByAdminVO.getUsername())) {
            throw new UserException(HttpStatus.BAD_REQUEST, TransactionErrorType.INVALID_REQUEST_PARAM, "username could not be empty");
        }
        if (StringUtils.isBlank(passwordByAdminVO.getPassword())) {
            throw new UserException(HttpStatus.BAD_REQUEST, TransactionErrorType.INVALID_REQUEST_PARAM, "password could not be empty");
        }
        iUserService.resetPasswordByAdmin(passwordByAdminVO.getUsername(), passwordByAdminVO.getPassword());
        return new ReturnResultVO<>();
    }

    @Override
    @PreAuthorize("#oauth2.hasPermission('create:users')")
    public ReturnResultVO<ImportUserHistoryVO> importUser(HttpServletRequest request, CreateUserExcelDto createUserExcelDto) {
        ImportUserHistoryVO importUserHistoryVO = iUserService.importUser(request, createUserExcelDto);
        return new ReturnResultVO<>(importUserHistoryVO);
    }

    @Override
    public ReturnResultVO<List<Map<String, String>>> getImportUserSchema() {
        List<Map<String, String>> importUserSchema = iUserService.getImportUserSchema();
        return new ReturnResultVO<>(importUserSchema);
    }

    @Override
    public ReturnResultVO<List<ExcelUserVO>> getImportUserInfo(Integer saveStatus, String batchNo) {
        List<ExcelUserVO> excelUserInfo = excelUserService.getImportUserInfo(saveStatus, batchNo);
        return new ReturnResultVO<>(excelUserInfo);
    }

    @Override
    public ReturnResultVO<Map<String, Object>> getPersonalUserInfo(String id) {
        return new ReturnResultVO<>(iUserService.getPersonalUserInfo(id));
    }

    @Override
    public ReturnResultVO<List<Map<String, Object>>> getExpireUserInfo(String notificationId) {
        List<UserEntity> sendMessageLog = iSendUserMessageLogService.getSendMessageLog(notificationId);
        ReturnResultVO<List<Map<String, Object>>> result = new ReturnResultVO<>();
        result.setData(userTransfer.entityToMap(sendMessageLog));
        return result;
    }

    @Override
    public void importTemplate(HttpServletResponse response) {
        response.setCharacterEncoding(Charset.defaultCharset().name());
        response.setContentType(APPLICATION_OCTET_STREAM_VALUE);
        String fileName = "userTemplate.xlsx";
        response.addHeader("Content-disposition", "attachment;filename=" + new String(fileName.getBytes(Charset.defaultCharset()), Charset.defaultCharset()));

        iUserService.importTemplate(response);
    }

    @PreAuthorize("#oauth2.hasPermission('search:users')")
    @RequestMapping(value = "/export", method = RequestMethod.POST)
    @Override
    public void exportUsers(HttpServletResponse response,
                            String fileName,
                            UserSearchDto userSearchDto)
            throws IOException {

        response.setCharacterEncoding(Charset.defaultCharset().name());

        response.setContentType(APPLICATION_OCTET_STREAM_VALUE);
        fileName = StringUtils.isBlank(fileName) ? "users.xlsx" : fileName + ".xlsx";
        response.addHeader("Content-disposition", "attachment;filename=" + new String(fileName.getBytes(Charset.defaultCharset()), Charset.defaultCharset()));

        final Set<String> inScopeOrgIds = InScopeOrgs.get();
        InScopeOrgs.clear();

        iUserService.exportUsers(response, inScopeOrgIds, userSearchDto);
    }

    @Override
    @PreAuthorize("#oauth2.hasPermission('search:users')")
    public Result<Map<String, Object>> exportUserCountByStatus(Long startTime, Long endTime) {
        Map<String, Object> map = iUserService.exportUsersCountByStatus(DateUtils.long2LocalDate(startTime), DateUtils.long2LocalDate(endTime));
        return new Result<>(map);
    }

    @PreAuthorize("#oauth2.hasPermission('search:users')")
    @Override
    public void exportUserByStatus(HttpServletResponse response, String fileName, List<String> status, Long startTime, Long endTime) throws IOException {
        response.setCharacterEncoding(Charset.defaultCharset().name());

        response.setContentType(APPLICATION_OCTET_STREAM_VALUE);
        fileName = StringUtils.isBlank(fileName) ? "数犀集成平台用户数据统计表.xlsx" : fileName + ".xlsx";
        response.addHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(fileName, Charset.defaultCharset().name()));
        iUserService.exportUsersByStatus(response, status, DateUtils.long2LocalDate(startTime), DateUtils.long2LocalDate(endTime));
    }

    @PreAuthorize("#oauth2.hasPermission('search:users')")
    @Override
    public void exportImportUsers(HttpServletResponse response, String taskId, List<Integer> status) {
        response.setCharacterEncoding(Charset.defaultCharset().name());
        response.setContentType(APPLICATION_OCTET_STREAM_VALUE);
        String fileName = "importUserDetail.xlsx";
        response.addHeader("Content-disposition", "attachment;filename=" + new String(fileName.getBytes(Charset.defaultCharset()), Charset.defaultCharset()));

        iUserService.exportImportUsers(response, taskId, status);
    }

    @PreAuthorize("#oauth2.hasPermission('update:users')")
    @Override
    public Result<Integer> userActiveInvitation(ActiveUserDto activeUser) {
        Integer sendTotal = iUserService.activeInvitation(activeUser);
        return ResponseResult.success(sendTotal);
    }

    @Override
    public Result<Integer> getNoActiveCount() {
        Integer sendTotal = iUserService.noActiveCount();
        return ResponseResult.success(sendTotal);
    }

    @Override
    public Result<Boolean> userImportActive(Long taskId) {
        iUserService.importActive(taskId);
        return ResponseResult.success(true);
    }


    @PreAuthorize("#oauth2.hasPermission('search:users')")
    @Override
    public ReturnResultVO<UsersRevokedVO> getUsersRevoked(Long timestamp) {
        return null;
//        return {
//            ReturnResultVO<UsersRevokedVO> rr = new ReturnResultVO<>();
//            UsersRevokedVO searchResult = auditlogService.searchUsersRevoked(timestamp);
//            rr.setData(searchResult);
//            return rr;
//        };
    }

    @Override
    @Auditable(eventType = AuditEventType.USER, eventSubtype = AuditEventSubtype.UPDATE,
            fields = "targetId=#{#userOrderDto.getUserId()};;" +
                    "targetName=#{#userOrderDto.getUsername()};;" +
                    "parameters=#{T(com.cyberscraft.uep.common.util.JsonUtil).obj2Str(#userOrderDto)}")
    @PreAuthorize("#oauth2.hasPermission('update:users')")
    public ReturnResultVO<Boolean> updateUserOrder(UpdateUserOrderDto userOrderDto) {
        Boolean isSuccess = iUserService.updateUserOrder(userOrderDto);
        ReturnResultVO<Boolean> resultVO = new ReturnResultVO<>();
        resultVO.setData(isSuccess);
        return resultVO;
    }

    @Resource
    IMessageSendClient messageSendClient;

    @RequestMapping(value = "/push", method = RequestMethod.POST)
    public String dataSyncTest(@RequestParam(value = "name", required = false) String name,
                               @RequestParam(value = "data", required = false) String data,
                               @RequestParam(value = "dataType", required = false) String dataType,
                               @RequestParam(value = "dataStatus", required = false) String dataStatus) {
        MessageEntry messageEntry = new MessageEntry();
        messageEntry.setMsg("test");
        messageSendClient.send("topic-test", messageEntry);
        return "success";
    }

    @PreAuthorize("#oauth2.hasPermission('search:users')")
    @Override
    public ReturnResultVO<Integer> userTotal() {
        return new ReturnResultVO<>(iUserService.userTotal());
    }
}
