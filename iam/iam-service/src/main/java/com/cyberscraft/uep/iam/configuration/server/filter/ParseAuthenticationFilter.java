package com.cyberscraft.uep.iam.configuration.server.filter;

import com.cyberscraft.uep.iam.service.oidc.domain.UserInfo;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.web.filter.GenericFilterBean;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import java.io.IOException;

public class ParseAuthenticationFilter extends GenericFilterBean {
    protected Logger logger = LoggerFactory.getLogger(ParseAuthenticationFilter.class);
    private String tenantHeaderName;

    public ParseAuthenticationFilter(String tenantHeaderName) {
        this.tenantHeaderName = tenantHeaderName;
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        String tenantCodeInRequest = (String) request.getAttribute(tenantHeaderName);
//        logger.info("tenantCodeInRequest:{}", tenantCodeInRequest);
        String tenantCodeInAuthentication = null;
        if (SecurityContextHolder.getContext() !=null && SecurityContextHolder.getContext().getAuthentication() != null){
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication instanceof OAuth2Authentication){//for oauth2 client authentication
                OAuth2Authentication oAuth2Authentication = (OAuth2Authentication)authentication;
                tenantCodeInAuthentication = (String) oAuth2Authentication.getOAuth2Request().getExtensions().get(tenantHeaderName);
                //为了解决平台级cli应用，访问租户内资源
                if(Strings.isNullOrEmpty(tenantCodeInRequest)) {
                    //如果请求中的tcode为空，则使用认证对象中对应的tenantId
                    TenantHolder.setTenantCode(tenantCodeInAuthentication);
                    request.setAttribute(tenantHeaderName, tenantCodeInAuthentication);
                }else{
                    TenantHolder.setTenantCode(tenantCodeInAuthentication);
                    request.setAttribute(tenantHeaderName, tenantCodeInAuthentication);
                }

            }else if (authentication.getPrincipal() != null
                    && authentication.getPrincipal() instanceof UserInfo){//username,password authentication
                UserInfo userInfo = (UserInfo) authentication.getPrincipal();
                if (StringUtils.isNotEmpty(userInfo.getTenantCode())){
                    tenantCodeInAuthentication = userInfo.getTenantCode();
                    TenantHolder.setTenantCode(tenantCodeInAuthentication);
                    request.setAttribute(tenantHeaderName, tenantCodeInAuthentication);
                }
            }
        }
        if (StringUtils.isNotEmpty(tenantCodeInRequest)
                && StringUtils.isNotEmpty(tenantCodeInAuthentication)
                && !tenantCodeInAuthentication.equals(tenantCodeInRequest)){
            String message = String.format("requested tenant %s doesn't match with authentication", tenantCodeInRequest);
            logger.error(message);

            SecurityContextHolder.getContext().setAuthentication(null);
            TenantHolder.setTenantCode(tenantCodeInRequest);
            request.setAttribute(tenantHeaderName, tenantCodeInRequest);        }
        try{
            chain.doFilter(request, response);
        }finally {
            TenantHolder.remove();
        }
    }
}
