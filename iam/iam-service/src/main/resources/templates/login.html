<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=2.0, user-scalable=yes" />
    <meta name="format-detection" content="telephone=no" />
    <title>[[${uc_name}]]</title>
    <link rel="stylesheet" type="text/css" href="./web/css/uclogin.css" data-th-href="@{css/uclogin.css(v=${uc_version})}">
    <link rel="shortcut icon" href="./web/img/favicon.ico" type="image/x-icon">
</head>
<body>
<div class="maskLayer" id="maskLayer" style="display: none;"></div>
<div class="popDiv" id="popDiv" style="display: none;">
    <div style="display: inline-block;
            width: 100%;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid #f1f0ee;">
        <div style="float: left;
            font-size: 18px;
            font-weight: 500;">
            系统提示
        </div>
        <div style="float: right;">
            <span id="closeSelUsernameBtn" class="close"></span>
        </div>
    </div>

    <div style="color: #8E94A6;
            font-size: 12px;
            display: block;">
    存在多个相同名称的用户，请选择以下用户名登录:
    </div>
    <div id="usernameListContainer" style="text-align: center">
    </div>

</div>
<div class="loginPage">
    <div class="loginBg">
        <div class="loginMsg">
            <div class="loginTitle">
                <img src="./web/img/login-title.png">
                <div class="content-title">
                    <span data-th-utext="${uc_name}">用户中心</span>
                </div>
            </div>
            <div class="ant-row" style="height:60px;" id="loginHintDiv">
                <div id="pwdLoginHint" class="ant-col-12 login-hint active">
                    <a href="javascript: void(0)" id="pwdLogin" class="link">
                        <span style="font-size: 18px;">密码登录</span>
                    </a>
                </div>
                <div id="qrcodeLoginHint" class="ant-col-12 login-hint">
                    <a href="javascript: void(0)" id="qrcodeLogin" class="link">
                        <span style="font-size: 18px;">扫码登录</span>
                    </a>
                </div>
            </div>
            <form id="loginForm" class="q-login-hint-pwd" data-th-action="@{login}" method='post'>
                <div class="ant-col-12" style="line-height: 15px; width: 100%">
                    <p id="errorTip" class="errorTip" data-th-utext="${error_description}"></p>
                    <p id="msgTip" class="link" style="display: inline-block;"></p>
                </div>

                <div class="ant-row ant-form-item" id="tenantIdBlock">
                    <div class="ant-form-item-control">
                        <div class="inputControl">
                            <input id="tcode" data-th-type="${tcode_visible} ? 'text' : 'hidden'" data-th-value="${tcode}" class="ant-input" name="tcode" placeholder="租户ID" maxlength="20" style="margin-bottom: 12px;">
                        </div>
                    </div>
                </div>
                <div class="ant-row ant-form-item">
                    <div class="ant-form-item-control">
                        <div class="inputControl">
                            <input type="text" class="ant-input" id="userName" name="username" data-th-placeholder="${placeholder}" maxlength="50" style="margin-top: 0;">
                        </div>
                    </div>
                </div>
                <div class="ant-row ant-form-item" id="userPwdBlock">
                    <div class="ant-form-item-control">
                        <div class="inputControl">
                            <input id="userPwd" type="password" class="ant-input" name="password" placeholder="密码" maxlength="20" style="margin-bottom: 12px;">
                        </div>
                    </div>
                </div>
                <div class="ant-row ant-form-item" id="oneTimePwdBlock" style="display:none">
                    <div class="ant-row" style="width: 100%">
                        <div class="ant-col-12" style="width: 65%; float: left">
                            <input id="oneTimePwd" type="text" class="ant-input" name="password" placeholder="一次性密码" maxlength="20"
                                   style="margin-bottom: 12px;">
                        </div>
                        <div class="ant-col-12" style="width: 30%; float: right">
                            <button type="button" class="ant-btn ant-btn-ghost" id="sendOneTimePwd"
                                    style="margin: 10px 0; font-size: 15px;">发送密码</button>
                        </div>
                    </div>
                </div>
                    <div class="ant-row">
                        <div class="ant-col-12" style="line-height: 32px; width: 40%; text-align: left; color: rgb(248, 245, 249);">
                            <div class="ant-row ant-form-item controlHeight">
                                <div class="ant-form-item-control">
                                    <label class="ant-checkbox-wrapper">
                                       <span class="ant-checkbox">
                                           <input type="checkbox" id="rememberAccount" class="ant-checkbox-input" value="on">
                                           <span class="ant-checkbox-inner"></span>
                                       </span>
                                       <span>
                                           <span class="rememberAccount">记住帐号</span>
                                       </span>
                                   </label>
                               </div>
                           </div>
                        </div>
                        <div id="responseTypeBlock" class="ant-col-12" style="line-height: 32px; width: 60%; text-align: right; display: none">
                            <div class="ant-col-12" style="line-height: 32px; width: 40%; text-align: right">
                                <span class="rememberAccount">发送至</span>
                            </div>
                            <div class="ant-col-12" style="line-height: 32px; width: 60%; text-align: right; color: rgb(248, 245, 249);">
                                <span>
                                    <input id="mobileRep" type="radio" name="responseType" value="SMS" checked
                                           style="width:20px; height: 20px; vertical-align: middle; margin: 0 0" />
                                    <label for="mobileRep" class="rememberAccount">手机</label>
                                </span>
                                <span>
                                    <input id="emailRep" type="radio" name="responseType" value="EMAIL"
                                           style="width:20px; height: 20px; vertical-align: middle; margin: 0 0" />
                                    <label for="emailRep" class="rememberAccount">邮箱</label>
                                </span>
                            </div>
                        </div>
                    </div>
                <button type="submit" id="loginBtn" class="ant-btn ant-btn-primary" style="font-size: 18px;">
                    <span>登 录</span>
                </button>
                <button data-th-if="${allow_register} == true" type="button" id="register" class="ant-btn ant-btn-ghost" style="font-size: 18px;"
                        data-th-register="${register_url}"
                        data-th-onclick="javascript:location.href='this.getAttribute('register')';">
                   <span>注 册</span>
                </button>
                <!--<button id="testBtn" type="button" class="ant-btn ant-btn-ghost" style="font-size: 18px;">-->
                    <!--<span>测试重复别名</span>-->
                <!--</button>-->
                <div class="ant-row">
                    <div id="forgetPwdDiv" class="ant-col-12" style="line-height: 32px; text-align: left; color: rgb(248, 245, 249);">
                        <div class="ant-row ant-form-item controlHeight">
                            <div class="ant-form-item-control">
                                <a href="javascript: void(0)" id="forgetPwd" class="link">忘记密码</a>
                            </div>
                        </div>
                    </div>
                    <div id="oneTimePwdLinkerDiv" class="ant-col-12" style="line-height: 32px; text-align: right; color: rgb(248, 245, 249);">
                        <a href="javascript: void(0)" id="oneTimePwdLinker" class="link">一次性密码登录</a>
                    </div>
                    <div id="userPwdLinkerDiv" class="ant-col-12" style="line-height: 32px; text-align: right; color: rgb(248, 245, 249);display: none;">
                        <a href="javascript: void(0)" id="userPwdLinker" class="link">密码登录</a>
                    </div>
                </div>
                <p class="mt-10"></p>
                <div class="marginBottom20 text-center" data-th-if="${username} != null">
                <span>
                    <!-- <img class="icon-user" data-th-src="${avatar}?: _" src="./img/default_avatar.png" /> -->
                    <span class="msg" data-th-utext="|您已经登录，${username}，再次登录将覆盖您之前的登录信息|"></span>
                </span>
                </div>
                <input type="hidden" data-th-if="${continue} != null" name="continue" data-th-value="${continue}">
                <input type="hidden" name="connection" value="userPwd">
                <input type="hidden" id="clientId" name="client_id" value="">
            </form>
            <input type="hidden" name="IN_UC_LOGIN_PAGE" value="true">
            <input type="hidden" name="QR_CODE_ENABLED" data-th-value="${qrcode_enabled}">
            <input type="hidden" name="ONE_TIME_PWD_ENABLED" data-th-value="${one_time_pwd_enabled}">
            <input type="hidden" name="USERNAME_LIST" data-th-value="${username_list}">
            <div id="ssoQRCode" class="ant-row q-login-hint-qrcode qrcode-area" style="display:none">
            </div>
        </div>
    </div>
</div>
<script src="./web/js/qrcode.min.js" data-th-src="@{js/qrcode.min.js(v=${uc_version})}"></script>
<script src="./sdk/qrcode-login-sdk.min.js" data-th-src="@{sdk/qrcode-login-sdk.min.js(v=${uc_version})}"></script>
<script src="./web/js/jsencrypt.min.js" data-th-src="@{js/jsencrypt.min.js(v=${uc_version})}"></script>
<script src="./sdk/secure-sdk.min.js" data-th-src="@{sdk/secure-sdk.min.js(v=${uc_version})}"></script>
<script>
    window.onload = function () {
        var rememberAccount = document.querySelector("#rememberAccount"),
                forgetPwd = document.querySelector("#forgetPwd"),
                loginBtn = document.querySelector("#loginBtn"),
                errorTip = document.querySelector("#errorTip"),
                msgTip = document.querySelector("#msgTip"),
                loginForm = document.querySelector("#loginForm"),
                userName = document.querySelector("#userName"),
                localUserName = localStorage.getItem("username"),
                localTcode = localStorage.getItem("tcode"),
                //login hint
                loginHintQREnabled = document.querySelector("input[name=QR_CODE_ENABLED][value=true]"),
                oneTimePwdEnabled = document.querySelector("input[name=ONE_TIME_PWD_ENABLED][value=true]"),
                usernameList = document.querySelector("input[name=USERNAME_LIST]"),
                loginHintDiv = document.querySelector("#loginHintDiv"),
                loginHintImg = document.querySelector("#loginHintImg"),
                pwdLogin = document.querySelector("#pwdLogin"),
                pwdLoginHint = document.querySelector("#pwdLoginHint"),
                qrcodeLogin = document.querySelector("#qrcodeLogin"),
                qrcodeLoginHint = document.querySelector("#qrcodeLoginHint"),
                //qrcode display area
                ssoQRCode = document.querySelector("#ssoQRCode"),
                sendOneTimePwd = document.querySelector("#sendOneTimePwd"),
                oneTimePwdLinker = document.querySelector("#oneTimePwdLinker"),
                userPwdLinker = document.querySelector("#userPwdLinker"),
                userPwd = document.querySelector("#userPwd"),
                tcode = document.querySelector("#tcode"),
                oneTimePwd = document.querySelector("#oneTimePwd"),
                closeSelUsernameBtn = document.querySelector("#closeSelUsernameBtn"),
                mobileRep = document.querySelector("#mobileRep"),
                emailRep = document.querySelector("#emailRep"),
                usernameListContainer = document.querySelector("#usernameListContainer");
                //testBtn = document.querySelector("#testBtn");

//        testBtn.onclick = function() {
//            util.showUsernameSelect();
//        };

        closeSelUsernameBtn.onclick = function() {
            util.hide(document.querySelectorAll("#maskLayer"));
            util.hide(document.querySelectorAll("#popDiv"));
        };

        var resendCount = 60;
        var resendTimer;
        util = {
            show: function(elements) {
                [].forEach.call(elements, function(ele) {
                    ele.style.display = "";
                });
            },

            hide: function(elements) {
                [].forEach.call(elements, function(ele) {
                    ele.style.display = "none";
                });
            },

            parseQueryString: function (url) {
                var queryObj = {},
                    result = url.match(/[\?\&\#][^\?\&\#]+=[^\?\&\#]+/g);
                if (result !== null) {
                    for (let i = 0, len = result.length; i < len; i += 1) {
                        let pair = result[i].slice(1).split("=");
                        queryObj[pair[0]] = decodeURIComponent(pair[1]);
                    }
                }

                return queryObj;
            },

            isPC: function() {
                var userAgentInfo = navigator.userAgent,
                    agents = ["Android", "iPhone",
                    "SymbianOS", "Windows Phone",
                    "iPad", "iPod"];
                return agents.every(function (item) {
                    return userAgentInfo.indexOf(item) === -1;
                });
            },

            sendCodeCountDown: function() {
                if(resendCount > 0){
                    --resendCount;
                    sendOneTimePwd.innerText = "重新发送 " + resendCount;
                    sendOneTimePwd.disabled = true;
                }else{
                    sendOneTimePwd.innerText = "发送密码";
                    sendOneTimePwd.disabled = false;
                    clearInterval(resendTimer);
                    resendCount = 60;
                }
            },

            showUsernameSelect: function (usernameValues) {
                document.body.style.margin=0;
                util.show(document.querySelectorAll("#maskLayer"));
                util.show(document.querySelectorAll("#popDiv"));
                util.createUsernameListItem(usernameValues);
            },

            createUsernameListItem: function(usernameValues) {
                while (usernameListContainer.hasChildNodes()){
                    usernameListContainer.removeChild(usernameListContainer.firstChild);
                }
                if(usernameValues.indexOf(",") !== -1) {
                    var usernameList = usernameValues.split(",");
                    for(var i=0; i< usernameList.length; i++) {
                        var usernameItem = document.createElement("div");
                        usernameItem.classList.add("usernameListItem");
                        usernameItem.onclick = function (itemDiv) {
                            console.log(itemDiv.currentTarget);
                            userName.value = itemDiv.currentTarget.children[0].innerText || itemDiv.currentTarget.children[0].textContent;
                            util.hide(document.querySelectorAll("#maskLayer"));
                            util.hide(document.querySelectorAll("#popDiv"));
                        };
                        var textSpan = document.createElement("span");
                        textSpan.innerText = usernameList[i];
                        usernameItem.appendChild(textSpan);
                        usernameListContainer.appendChild(usernameItem);
                    }
                }
            },
            parseUCAddr: function () {
                return window.location.protocol + "//" + window.location.host + "/iam"
            },

            //parse out the client info from the original authorize request
            parseClientInfo: function() {
                var originalAuthReq = document.querySelector("input[name=continue]").value;

                var clientInfo = util.parseQueryString(originalAuthReq);
                document.querySelector("#clientId").value = clientInfo.client_id;
                return clientInfo;
            },

            submitForm: function(loginId, password, tid){
                userName.value = loginId;
                userPwd.value = password;
                tcode.value = tid;
                loginForm.submit();
            },

            error: function (error) {
                var errorMap = {
                    "1011105": "该应用不存在",
                    "1011122": "该应用被禁用",
                    "no_public_key": "应用需加密登录，但login_hello未生成公钥"
                };
                msgTip.innerText = "";
                errorTip.innerText = errorMap[error.error] || "操作失败。";
            },

            disableUserPwdReqParam: function () {
                mobileRep.disabled = "disabled";
                emailRep.disabled = "disabled";
                oneTimePwd.disabled = "disabled";
            },

            disableOneTimPwdReqParam: function () {
                userPwd.disabled = "disabled";
            }
        };

        login = {
            DEFAULT_LOGIN_HINT: "pwd", //default login hint
            LOGINHINTS: {
                pwd: {
                    id: "pwd",
                    next: "qrcode",
                    link: pwdLogin,
                    container: pwdLoginHint,
                    ele_selector: ".q-login-hint-pwd",
                    handle: function() {
                        userName.focus();
                    }
                },
                qrcode: {
                    id: "qrcode",
                    next: "pwd",
                    link: qrcodeLogin,
                    container: qrcodeLoginHint,
                    ele_selector: ".q-login-hint-qrcode",
                    handle: function() {
                        //style tweak
                        var clientInfo = util.parseClientInfo(),
                            uc = util.parseUCAddr(),

                            req = new qrcodesso.QRCodeLogin(uc, clientInfo, ssoQRCode);

                            //requesting a qrcode authorization
                            req.authorize();
                    }
                }
            },
            /**
                login with a specific hint.
            */
            login: function(hintid) {
                if (loginHintQREnabled && util.isPC()) {
                } else {
                    util.hide(document.querySelectorAll(this.LOGINHINTS["qrcode"].ele_selector));
                    util.hide(document.querySelectorAll("#loginHintDiv"));
                }

                if(null === oneTimePwdEnabled){
                    document.querySelector("#forgetPwdDiv").style.width = "100%";
                    util.hide(document.querySelectorAll("#oneTimePwdLinkerDiv"));
                    util.hide(document.querySelectorAll("#userPwdLinkerDiv"));
                }

                var loginView = localStorage.getItem("login_view");
                if(loginView==="oneTimePwd"){
                    oneTimePwdLinker.onclick();
                }

                var usernameListValue = usernameList.value;
                if(usernameListValue.length !== 0){
                    util.showUsernameSelect(usernameListValue);
                }
                var hint = this.LOGINHINTS[hintid],
                      nextHint = this.LOGINHINTS[hint.next];

                hint.link.setAttribute("class", "link active");
                hint.container.setAttribute("class", "ant-col-12 login-hint active");

                nextHint.link.setAttribute("class", "link inactive");
                nextHint.container.setAttribute("class", "ant-col-12 login-hint inactive");

                //toggle login dom elements.
                var hintElements = document.querySelectorAll(hint.ele_selector),
                      nextHintElements = document.querySelectorAll(nextHint.ele_selector);
                util.show(hintElements);
                util.hide(nextHintElements);

                //handle specific login hint.
                hint.handle();
            }
        };

        Ajax = {
            post: function (url, data, sucessCb, errorCb) {
                var obj = new XMLHttpRequest();
                obj.open("POST", url, true);
                obj.setRequestHeader("Content-type", "application/json");
                obj.setRequestHeader("tcode", tcode.value);
                obj.onreadystatechange = function() {
                    if (obj.readyState == 4) {
                        if (obj.status == 200 || obj.status == 304) {
                            sucessCb.call(this, JSON.parse(obj.responseText));
                        } else {
                            errorCb.call(this, JSON.parse(obj.responseText));
                        }
                    }
                };
                obj.send(JSON.stringify(data));
            }
        };

        conf = {
            getBackendUrl: function () {
                //开发时将以下URL设为具体值，但不要提交
                var backendUrl = "";

                if (backendUrl.length === 0) {
                    backendUrl = window.location.protocol + "//" + window.location.host;
                }
                return backendUrl;
            },
            getServiceUrl: function () {
                //using backend api.
                return this.getBackendUrl() + "/api";
            },
            getFrontEndUrl: function() {
                //可用于开发时指定前端路径，如不指定，默认与backendUrl一致
                let frontEndUrl = "";
                if (frontEndUrl.length === 0) {
                    frontEndUrl = this.getBackendUrl();
                }
                return frontEndUrl;
            }
        };

        if (localUserName) {
            userName.value = localUserName;
            tcode.value = tcode.value?localTcode:'';
            rememberAccount.checked = true;
            rememberAccount.parentNode.setAttribute("class", "ant-checkbox ant-checkbox-checked")
        }

        oneTimePwdLinker.onclick = function(){
            util.hide(document.querySelectorAll("#oneTimePwdLinkerDiv"));
            util.hide(document.querySelectorAll("#userPwdBlock"));

            util.show(document.querySelectorAll("#userPwdLinkerDiv"));
            util.show(document.querySelectorAll("#responseTypeBlock"));
            util.show(document.querySelectorAll("#oneTimePwdBlock"));

            document.querySelector("#oneTimePwd").disabled = false;
            document.querySelector("#userPwd").disabled = true;

        };

        userPwdLinker.onclick = function(){
            util.show(document.querySelectorAll("#oneTimePwdLinkerDiv"));
            util.show(document.querySelectorAll("#userPwdBlock"));

            util.hide(document.querySelectorAll("#userPwdLinkerDiv"));
            util.hide(document.querySelectorAll("#responseTypeBlock"));
            util.hide(document.querySelectorAll("#oneTimePwdBlock"));

            document.querySelector("#oneTimePwd").disabled = true;
            document.querySelector("#userPwd").disabled = false;


        };

        rememberAccount.onclick = function (e) {
            var target = e.target;
            if (target.checked) {
                target.parentNode.setAttribute("class", "ant-checkbox ant-checkbox-checked")
            } else {
                target.parentNode.setAttribute("class", "ant-checkbox")
            }
        };

        forgetPwd.onclick = function () {
            var username = userName.value.trim();
            var inTcode = tcode.value.trim();
            if(!inTcode){
                errorTip.innerText = "请输入租户名。";
                return;
            }
            if (username) {
                Ajax.post(conf.getBackendUrl() + "/iam/api/self/forget_password", {
                    "username": username
                }, function (response) {
                    var data = response && response.data, identifier, mode, validity, tcode;
                    if (data) {
                        identifier = data.identifier;
                        mode = data.mode;
                        validity = data.validity;
                        tcode = data.tcode;
                        if (mode === "EMAIL") {
                            location.href = conf.getFrontEndUrl() + "/iam/index.html#/mailSent/" + username + "/" + identifier+ "/" + tcode;
                        } else if (mode === "MOBILE") {
                            location.href = conf.getFrontEndUrl() + "/iam/index.html#/smsSent/" + username + "/" + identifier + "/" + validity+ "/" + tcode;
                        }
                    }
                }, function (error) {
                    var errorMap = {
                        "1010227": "缺少邮箱和手机信息，无法找回密码。",
                        "1010228": "无权限操作只读用户。",
                        "10102100": "租户ID校验不匹配。",
                        "1010221": "用户密码不允许修改。",
                        "1010213": "用户状态异常。",
                        "1010201": "用户不存在或者系统中存在多个该用户名。"
                    };
                    errorTip.innerText = errorMap[error.error] || "操作失败。";
                });
            } else {
                errorTip.innerText = "请输入用户名。"
            }
        };

        sendOneTimePwd.onclick = function () {
            var username = userName.value.trim();
            var clientId = util.parseClientInfo().client_id;
            var responseType = document.querySelector("input[name='responseType']:checked").value;
            if (username && clientId) {
                Ajax.post(conf.getBackendUrl() + "/iam/api/self/password_less", {
                    "user_principle": username,
                    "client_id": clientId,
                    "response_type": responseType
                }, function (response) {
                    var data = response && response.result;
                    if(data){
                        errorTip.innerText = "";
                        msgTip.innerText = "一次性登录密码已发送";
                        resendTimer = setInterval(util.sendCodeCountDown, 1000);
                    }

                }, function (error) {
                    if(error.error === "1010230"){
                        util.showUsernameSelect(error.error_description);
                        return;
                    }
                    var errorMap = {
                        "1010227": "缺少邮箱和手机信息，无法找回密码。",
                        "1010228": "无权限操作只读用户。",
                        "10102100": "租户ID校验不匹配。",
                        "1010221": "用户密码不允许修改。",
                        "1010213": "用户状态异常。",
                        "1010201": "操作失败，可能原因：1.用户不存在。2.系统中存在多个该用户名。3.该手机或邮箱未验证"

                    };
                    msgTip.innerText = "";
                    errorTip.innerText = errorMap[error.error] || "操作失败。";
                });
            }else{
                errorTip.innerText = "请输入用户名, 手机号或邮箱。"
            }
            return false;
        };

        loginForm.onsubmit = function () {
            var username = userName.value.trim();
            var tid = tcode.value.trim();
            if (rememberAccount.checked) {
                localStorage.setItem("username", username);
                localStorage.setItem("tcode", tid);
            } else {
                localStorage.removeItem("username");
                localStorage.removeItem("tcode");
            }
            var oneTimePwdView = document.querySelector("#oneTimePwdBlock").style.display;
            var clientInfo = util.parseClientInfo();
            if("" === oneTimePwdView){
                document.querySelector("input[name=connection]").value = "password_less";
                document.querySelector("input[name=client_id]").value = clientInfo.client_id;
                localStorage.setItem("login_view", "oneTimePwd");
                util.disableOneTimPwdReqParam();
            }else{
                localStorage.setItem("login_view", "userPwd");
                util.disableUserPwdReqParam();
                var uc = util.parseUCAddr(),
                    secureLogin = new secure.Secure(uc, clientInfo.client_id);
                secureLogin.login(userName.value, userPwd.value, tcode.value, util.submitForm, util.error);
                return false;
            }
        };

        pwdLogin.onclick = function() {
            login.login("pwd");
        };
        qrcodeLogin.onclick = function() {
            login.login("qrcode");
        };

        var loginView = localStorage.getItem("login_view");
        if(loginView==="oneTimePwd"){
            oneTimePwdLinker.onclick();
        }
        login.login(login.DEFAULT_LOGIN_HINT);
    };
</script>
</body>
</html>
