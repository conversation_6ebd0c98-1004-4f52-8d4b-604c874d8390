<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta
            name="viewport"
            content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=2.0, user-scalable=no"
    />
    <meta name="format-detection" content="telephone=no" />
    <link rel="shortcut icon" href="/login/images/favicon.ico" type="image/x-icon" />
    <title id="platform_title" data-th-text="${uc_name}"></title>
    <style>
        body {
          width: 100%;
          height: 100%;
        }
        body * {
          outline: none;
          font-family: "Microsoft Yahei", "微软雅黑", Tahoma, Arial, Helvetica, STHeiti;
        }
        .navbar-top {
          width: 100%;
          height: 60px;
          line-height: 60px;
          background: #1f293d;
          color: #ffffff;
          font-size: 20px;
          position: fixed;
          top: 0;
          z-index: 100;
        }
        .error-description {
          font-size: 15px;
          width: 300px;
          word-wrap: break-word;
          margin: 0 auto;
        }
        .iconDefault {
          width: 24px;
          height: 24px;
          display: inline-block;
        }
        .pathNodeIcon {
          position: relative;
          top: 5px;
          margin-right: 5px;
        }
        .icon-favicon-white {
            background: url(
                "[(${logo != null ? logo : '/login/images/icon-favicon-white.png'})]"
            ) no-repeat;
            background-size: 24px 24px;
        }
        .error-icon {
          position: fixed;
          top: 50%;
          width: 800px;
          height: 200px;
          margin-left: -400px;
          margin-top: -100px;
          text-align: center;
          color: rgba(0, 0, 0, 0.65);
        }
        .left {
          position: fixed;
          left: 25%;
          top: 45%;
        }
        .right {
          position: fixed;
          left: 55%;
          top: 40%;
        }
        .error-msg {
          font-size: 26px;
          margin-top: 20px;
        }
        .loginTitle {
          width: 100%;
          text-align: center;
          margin-bottom: 1em;
        }
        .btn {
          border-radius: 5px;
          background: #1890ff;
          color: #fff;
          background-color: #1890ff;
          border-color: #1890ff;
          text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.12);
          box-shadow: 0 2px 0 rgba(0, 0, 0, 0.045);
          width: 100px;
          height: 30px;
        }
        .title {
          margin-bottom: 16px;
          color: rgba(0, 0, 0, 0.45);
          font-size: 20px;
          line-height: 28px;
        }
        @media only screen and (max-width: 600px) {
          .error-icon {
            padding-top: 50px;
            box-sizing: border-box;
            position: unset;
            display: flex;
            width: 100vw;
            margin: 0;
            height: 100vh;
            display: flex;
            flex-direction: column;
          }
          .left {
            top: 180px;
            width: 80%;
          }
          .left img {
            width: 80%;
          }
          .left,
          .right {
            display: block;
            margin: 0 auto;
            text-align: center;
            left: 0;
            right: 0;
          }
          .right {
            margin-top: 20px;
          }
          .error-description {
            width: 80%;
          }
        }
    </style>
</head>
<body style="margin: 0; background: #f5f5f5">
<header class="navbar-top">
      <span>
        <i class="iconDefault pathNodeIcon icon-favicon-white"></i>
        <span id="uc_console_name" data-th-utext="${uc_name}?: _"> </span>
      </span>
</header>
<div class="error-icon">
    <div class="left">
        <img src="/login/images/50x.svg" />
    </div>
    <div class="right">
        <div class="error-msg loginTitle">
            <div class="title" data-th-text="${error_title}"></div>
        </div>

        <div class="error-description">
            <div id="oauth_error_description" data-th-text="${error_description}">错误信息</div>
            <div id="error_message" data-th-text="${message}">错误信息</div>
            <p>
                <button class="btn" id="back" ><span data-th-text="${back_btn}"></span></button>
                <input type="hidden" id="backUrl" th:value="${continue_url}" />
            </p>
        </div>
    </div>
</div>
</body>
<script>
    var continueUrl = document.getElementById("backUrl").value;
    window.onload = function () {
      document.getElementById("back").addEventListener("click", function () {
          if (continueUrl) {
              window.location.href = continueUrl;
          } else {
              window.location.replace(document.referrer);
          }
      });
    };
</script>
</html>
