<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN" monitorInterval="1800" packages="com.cyberscraft.uep.iam.common.plugin">
    <properties>
        <!--日志文件位置-->
        <property name="LOG_HOME">../logs</property>
        <!--日志文件名称-->
        <property name="FILE_NAME">iam-service</property>
    </properties>
    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="%d %highlight{%-5level %tcode [%X{X-B3-TraceId},%X{X-B3-SpanId},%X{X-B3-ParentSpanId},%X{X-Span-Export}]}{Yellow}  %style{[%-21t]}{Magenta} %style{%-30.-80c{1.}:%-3L}{Cyan} - %m%n"/>
        </Console>
        <RollingRandomAccessFile
                name="running-log"
                fileName="${LOG_HOME}/${FILE_NAME}.log"
                filePattern="${LOG_HOME}/${FILE_NAME}-%d{yyyy-MM-dd}-%i.log.gz">
            <PatternLayout pattern="%d %highlight{%-5level %tcode [%X{X-B3-TraceId},%X{X-B3-SpanId},%X{X-B3-ParentSpanId},%X{X-Span-Export}]}{Yellow}  %style{[%-21t]}{Magenta} %-30.-80c{1.}:%-3L{Cyan} - %m%n"/>
            <Policies>
                <TimeBasedTriggeringPolicy />
                <SizeBasedTriggeringPolicy size="100 MB" />
            </Policies>
            <DefaultRolloverStrategy max="50">
                <Delete basePath = "${LOG_HOME}" maxDepth="1">
                    <IfFileName glob="${FILE_NAME}-*.log.gz" />
                    <IfLastModified age="7d" />
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>
        <RollingRandomAccessFile
                name="biz-log"
                fileName="${LOG_HOME}/${FILE_NAME}-biz.log"
                filePattern="${LOG_HOME}/${FILE_NAME}-biz.%d{yyyy-MM-dd}.%i.log">
            <PatternLayout
                    pattern="%d{yyyy-MM-dd HH:mm:ss.SSS}|%m%n"/>
            <Policies>
                <!-- 此处为每个文件大小策略限制，使用它一般会在文件中filePattern采用%i模式 -->
                <SizeBasedTriggeringPolicy size="100 MB" />
                <!-- 每天创建一个日志文件 -->
                <TimeBasedTriggeringPolicy interval="1" />
            </Policies>
            <DefaultRolloverStrategy max="30">
                <Delete basePath="${LOG_HOME}/" maxDepth="2">
                    <IfFileName glob="**-biz.**.log" />
                    <!--!Note: 这里的age必须和filePattern协调, 后者是精确到dd, 这里就要写成xd, xD就不起作用
                    另外, 数字最好>2, 否则可能造成删除的时候, 最近的文件还处于被占用状态,导致删除不成功!-->
                    <!--7天-->
                    <IfLastModified age="7d" />
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>
    </Appenders>
    <Loggers>
        <Root level="info">
            <AppenderRef ref="Console" />
            <AppenderRef ref="running-log" />
        </Root>
        <Logger level="info" name="BIZLOG" additivity="false">
            <appender-ref ref="biz-log"/>
        </Logger>
        <Logger name="org.springframework" level="INFO"/>
        <Logger name="org.springframework.security" level="INFO"/>
        <!--<Logger name="org.springframework.session.web.http" level="DEBUG"/>-->
<!--        <logger name="org.mybatis" level="INFO" />-->
<!--        <logger name="org.springframework.security" level="INFO" />-->
<!--        <logger name="com.cyberscraft.uep.iam.dao" level="INFO" />-->
<!--        <logger name="com.cyberscraft.uep.starter.dds" level="INFO" />-->
<!--        <logger name="com.cyberscraft.uep.iam.service.impl.SelfServiceImpl" level="INFO" />-->
<!--        <logger name="com.cyberscraft.uep.iam.configuration.oidc.session.impl" level="INFO" />-->
<!--        <logger name="com.cyberscraft.uep.iam.service.oidc.provider.code.IAMAuthorizationCodeTokenGranter" level="INFO" />-->
<!--        <logger name="com.cyberscraft.uep.iam.service.base.impl" level="debug" />-->
<!--        <logger name="com.cyberscraft.uep.iam.service.impl.DataSyncServiceImpl" level="debug" />-->
        <logger name="com.cyberscraft.uep" level="INFO" />
<!--        <logger name="org.springframework.security" level="DEBUG" />-->
        <logger name="RocketmqRemoting" level="WARN" />
        <logger name="RocketmqClient" level="WARN" />
        <logger name="RocketmqCommon" level="WARN" />
    </Loggers>
</Configuration>