package com.cyberscraft.uep.iam.common.util;

import com.cyberscraft.uep.iam.service.appstore.util.EncryptUtil;
import org.apache.commons.codec.binary.Base32;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.ByteBuffer;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.UUID;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/9/9 3:30 下午
 */
public class TotpUtil {

    private static Logger logger = LoggerFactory.getLogger(TotpUtil.class);

//    private static final String HMAC_ALGO = "HmacSHA256"; // HMAC 算法
    private static final String HMAC_ALGO = "HmacSHA1"; // HMAC 算法
    private static final int TIME_STEP = 30; // 时间步长，单位：秒
    private static final int TOTP_LENGTH = 6; // OTP 的长度

    public static String genUserSecret() {
        String md5 = EncryptUtil.MD5(UUID.randomUUID().toString());
        Base32 base32 = new Base32();
        String userSecret = base32.encodeToString(md5.getBytes());
        userSecret = userSecret.replaceAll("=", "");

        return userSecret;
    }

    /**
     * 生成TOTP
     * @param key
     * @param time 时间戳（秒）
     * @return
     */
    public static String generateTOTP(String key, long time) {
        // 将密钥转换为字节数组
//        byte[] keyBytes = DatatypeConverter.parseHexBinary(key);
        Base32 base32 = new Base32();
        byte[] keyBytes = base32.decode(key);

        // 将时间步进转换为字节数组
        byte[] data = ByteBuffer.allocate(8).putLong(time / TIME_STEP).array();

        byte[] hash = hmacSHA256(keyBytes, data);

        // 取结果的最后一个字节的低 4 位作为动态截取的起始位置
        int offset = hash[hash.length - 1] & 0xf;

        // 截取 4 个字节，构成一个 31 位的整数
        int binary = ((hash[offset] & 0x7f) << 24) | ((hash[offset + 1] & 0xff) << 16)
                | ((hash[offset + 2] & 0xff) << 8) | (hash[offset + 3] & 0xff);

        // 取结果的后 6 位
        int otp = binary % (int) Math.pow(10, TOTP_LENGTH);

        // 格式化输出
        return String.format("%0" + TOTP_LENGTH + "d", otp);
    }

    /**
     * 验证TOTP
     * @param key
     * @param otp
     * @return
     */
    public static boolean validateTOTP(String key, String otp) {
        long time = System.currentTimeMillis() / 1000;

        String totp = generateTOTP(key, time);
        if (totp.equals(otp)) {
            return true;
        }

        time = time - TIME_STEP;
        totp = generateTOTP(key, time);
        if (totp.equals(otp)) {
            return true;
        }

        time = time + 2 * TIME_STEP;
        totp = generateTOTP(key, time);
        if (totp.equals(otp)) {
            return true;
        }

        return false;
    }

    private static byte[] hmacSHA256(byte[] keyBytes, byte[] text) {
        try {
            Mac hmac = Mac.getInstance(HMAC_ALGO);
            SecretKeySpec macKey = new SecretKeySpec(keyBytes, HMAC_ALGO);
            hmac.init(macKey);
            return hmac.doFinal(text);
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            throw new RuntimeException("Error while generating HMAC", e);
        }
    }

    public static void main(String[] args) {
        String secret = genUserSecret();
        System.out.println(secret);

        String s = generateTOTP(secret, System.currentTimeMillis() / 1000);
        System.out.println(s);

//        boolean b = validateTOTP("GJQTGNBRMQZTQOBWMQ2TCNLDHEYGMYJWGI2TGNZWG4ZWMOJYMRTA", "889371");

        boolean b = validateTOTP(secret, s);
        System.out.println(b);
    }

}
