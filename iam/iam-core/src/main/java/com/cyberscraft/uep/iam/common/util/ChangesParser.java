package com.cyberscraft.uep.iam.common.util;

import com.unboundid.ldap.sdk.Attribute;
import com.unboundid.ldap.sdk.ChangeLogEntry;

/**
 * Created by cuilong on 5/19/17.
 */
public class ChangesParser {

    public static String parseDataType(String dn) {
        String dataType = "";
        return dataType;

    }

    public static String getAttribute(ChangeLogEntry changelog, String attributeName) {
        Attribute attr = changelog.getAttribute(attributeName);
        if (attr == null) {
            return null;
        }

        return attr.getValue();
    }
}
