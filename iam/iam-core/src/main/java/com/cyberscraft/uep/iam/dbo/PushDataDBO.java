package com.cyberscraft.uep.iam.dbo;

import com.cyberscraft.uep.iam.entity.PushDataEntity;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * IAM-业务推送数据表 服务类
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2019-08-16
 */
public interface PushDataDBO extends IService<PushDataEntity> {
    PushDataEntity getOne(PushDataEntity pushDataEntity);

    @Deprecated
    List<PushDataEntity> getUnPushList();

    /**
     * 查询需要补偿的推送数据，只查询创建时间<= 当前时间 - delayMinutesTime
     * @param delayMinutesTime 延迟时间，单位：分钟
     * @return
     */
    List<PushDataEntity> getPushDataList(Long delayMinutesTime);

    Boolean emptyPushData();

    /**
     * 按connectorId清空pushData
     * @param connectorId
     */
    void cleanPushData(Long connectorId);

    /**
     * 删除全表数据
     */
    void cleanAllData();

    /**
     * 获取最先生成的一批pushData
     * @param batchNum
     * @return
     */
    List<PushDataEntity> firstBatchPushData(Long connectorId, int batchNum);

}
