package com.cyberscraft.uep.iam.service.oidc.domain;

import com.cyberscraft.uep.iam.constants.CommonConstants;
import org.springframework.security.web.authentication.WebAuthenticationDetails;

import javax.servlet.http.HttpServletRequest;

public class UCWebAuthenticationDetails extends WebAuthenticationDetails {
    private static final long serialVersionUID = -8076247427108355244L;
    private String connection;
    private String clientId;
    /**
     * Records the remote address and will also set the session Id if a session already
     * exists (it won't create one).
     *
     * @param request that the authentication request was received from
     */
    public UCWebAuthenticationDetails(HttpServletRequest request) {
        super(request);
        connection = request.getParameter(CommonConstants.CONNECTION);
        clientId = request.getParameter(CommonConstants.CLIENT_ID);
    }

    public String getConnection() {
        return connection;
    }
    public String getClientId() {
        return clientId;
    }
}
