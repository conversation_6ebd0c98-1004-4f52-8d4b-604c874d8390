package com.cyberscraft.uep.iam.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

/**
 * <p>
 * IAM-链接器同步失败数据表
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2019-10-30
 */
@TableName("iam_connector_failed_data")
public class ConnectorFailedDataEntity implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 事件类型
     */
    private String eventType;

    /**
     * 钉钉corp_id
     */
    private String corpId;

    /**
     * 第三方业务ID
     */
    private String thridId;

    /**
     * 事件发生时间
     */
    private LocalDateTime eventTime;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 链接器ID
     */
    private Long connectorId;

    /**
     * 租户ID
     */
    private String tenantId;

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getEventType() {
        return eventType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public String getCorpId() {
        return corpId;
    }

    public void setCorpId(String corpId) {
        this.corpId = corpId;
    }

    public String getThridId() {
        return thridId;
    }

    public void setThridId(String thridId) {
        this.thridId = thridId;
    }

    public LocalDateTime getEventTime() {
        return eventTime;
    }

    public void setEventTime(LocalDateTime eventTime) {
        this.eventTime = eventTime;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public Long getConnectorId() {
        return connectorId;
    }

    public void setConnectorId(Long connectorId) {
        this.connectorId = connectorId;
    }

    @Override
    public String toString() {
        return "ConnectorFailedDataEntity{" +
        "id=" + id +
        ", eventType=" + eventType +
        ", corpId=" + corpId +
        ", thridId=" + thridId +
        ", eventTime=" + eventTime +
        ", createTime=" + createTime +
        ", connectorId=" + connectorId +
        "}";
    }
}
