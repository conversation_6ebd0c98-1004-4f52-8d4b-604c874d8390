package com.cyberscraft.uep.iam.common.enums;

import com.cyberscraft.uep.common.domain.DataTypeEnum;
import com.cyberscraft.uep.iam.dto.enums.Mutability;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/12 11:09 上午
 */
public enum LocalCommonAttr{
    status("status", DataTypeEnum.NUMBER, false, "状态", false, Mutability.readWrite, 0),
    connector_id("connector_id", "connector_id", DataTypeEnum.STRING, false, "来源", "来源", true, Mutability.readonly, 0),
    connector_type("connector_type", DataTypeEnum.NUMBER, false, "连接器类型", false, Mutability.readonly, 0),
    create_by("create_by", DataTypeEnum.STRING, false, "被谁创建", false, Mutability.readonly, 0),
    create_time("create_time", DataTypeEnum.NUMBER, false, "创建时间", false, Mutability.readonly, 0),
    created_mode("created_mode", DataTypeEnum.NUMBER, false, "创建方式", false, Mutability.readonly, 0),
    update_by("update_by", DataTypeEnum.STRING, false, "被谁修改", false, Mutability.readonly, 0),
    update_time("update_time", DataTypeEnum.NUMBER, false, "修改时间", false, Mutability.readonly, 0),
    checksum("checksum", DataTypeEnum.STRING, false, "信息是否发生变更的校验码", false, Mutability.none, 0),
    tenant_id("tenant_id", DataTypeEnum.STRING, false, "租户编码", true, Mutability.readonly, 0),
    ;

    //属性名称
    private String domainName;
    //db字段名称
    private String fieldName;
    //数据类型
    private DataTypeEnum dataType;
    //是否多个值
    private Boolean multiValued;
    //显示名称
    private String displayName;
    //描述
    private String description;
    //是否必填
    private Boolean required;
    //可变性
    private Mutability mutability;
    //排序数字（降序）
    private Integer sortNum;

    LocalCommonAttr(String fieldName, String domainName, DataTypeEnum dataType, Boolean multiValued, String displayName, String description, Boolean required, Mutability mutability, Integer sortNum) {
        this.fieldName = fieldName;
        this.domainName = domainName;
        this.dataType = dataType;
        this.multiValued = multiValued;
        this.displayName = displayName;
        this.description = description;
        this.required = required;
        this.mutability = mutability;
        this.sortNum = sortNum;
    }

    LocalCommonAttr(String domainName, DataTypeEnum dataType, Boolean multiValued, String displayName, Boolean required, Mutability mutability, Integer sortNum) {
        this(domainName, domainName, dataType, multiValued, displayName, displayName, required, mutability, sortNum);
    }

    public String getDomainName() {
        return domainName;
    }

    public String getFieldName() {
        return fieldName;
    }

    public DataTypeEnum getDataType() {
        return dataType;
    }

    public Boolean getMultiValued() {
        return multiValued;
    }

    public String getDisplayName() {
        return displayName;
    }

    public String getDescription() {
        return description;
    }

    public Boolean getRequired() {
        return required;
    }

    public Mutability getMutability() {
        return mutability;
    }

    public Integer getSortNum() {
        return sortNum;
    }
}
