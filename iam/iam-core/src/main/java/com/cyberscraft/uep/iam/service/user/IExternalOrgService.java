package com.cyberscraft.uep.iam.service.user;

import com.cyberscraft.uep.account.client.domain.Connector;
import com.cyberscraft.uep.account.client.domain.ConnectorOrg;
import com.cyberscraft.uep.common.dto.LimitResult;
import com.cyberscraft.uep.iam.common.enums.SyncDirectionEnum;
import com.cyberscraft.uep.iam.entity.ExternalOrgEntity;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/6/7 5:54 下午
 */
public interface IExternalOrgService {

    /**
     * 根据外部组织id获取外部组织
     * @param connectorId
     * @param externalId
     * @return
     */
    ExternalOrgEntity getByExternalId(Long connectorId, String externalId, String... attrs);

    /**
     * 根据本地组织id获取外部组织列表
     * @param localOrgId
     * @param syncDirections
     * @return
     */
    List<ExternalOrgEntity> listByLocalOrgId(Long localOrgId, List<Integer> syncDirections, String... attrs);

    List<ExternalOrgEntity> listByLocalOrgId(Long localOrgId, Integer type, List<Integer> syncDirections, String... attrs);

    List<ExternalOrgEntity> listByLocalOrgIds(List<Long> localOrgIds, Integer type, List<Integer> syncDirections, String... attrs);

    List<ExternalOrgEntity> listByLocalOrgIds(Long connectorId, List<Long> localOrgIds, String... attrs);

    List<ExternalOrgEntity> listByExternalIds(Long connectorId, Collection<String> externalIds, String... attrs);

    List<ExternalOrgEntity> listByExternalId(String externalId, Integer type, List<Integer> directions, String... attrs);

    List<ExternalOrgEntity> listByExternalIds(List<String> externalIds, Integer type, List<Integer> directions, String... attrs);

    /**
     * 根据本地组织id获取外部组织
     *
     * @param connectorId
     * @param localOrgId
     * @return
     */
    ExternalOrgEntity getByLocalOrgId(Long connectorId, Long localOrgId, String... attrs);

    /**
     * 获取连接器对应的外部根组织
     * @param connectorId
     * @return
     */
    ExternalOrgEntity getRootOrg(Long connectorId);

    /**
     * 创建或修改外部部门
     * @param externalOrgEntity
     */
    void saveOrUpdateExternalOrg(ExternalOrgEntity externalOrgEntity);

    /**
     * 创建外部组织
     *
     * @param connectorId
     * @param type          连接器类型
     * @param batchNo
     * @param direction
     * @param externalOrgId 外部组织id
     * @param localOrgId    关联的本地组织id
     * @param localOrg      映射后的本地组织对象
     * @param externalOrg
     */
    void createExternalOrg(Long connectorId, Integer type, Integer batchNo, SyncDirectionEnum direction, String externalOrgId, Long localOrgId, ConnectorOrg externalOrg, Map<String, Object> localOrg);

    void createExternalOrg(Long connectorId, Integer type, Integer batchNo, SyncDirectionEnum direction, String externalOrgId, Long localOrgId, ConnectorOrg externalOrg, Map<String, Object> localOrg, Integer orgLevel);

    /**
     * 修改外部组织
     *
     * @param externalOrgEntity 外部组织
     * @param batchNo
     * @param direction
     * @param localOrgId        关联的本地组织id
     * @param externalOrg
     * @param localOrg          映射后的本地组织对象
     */
    void updateExternalOrg(ExternalOrgEntity externalOrgEntity, Integer batchNo, SyncDirectionEnum direction, Long localOrgId, ConnectorOrg externalOrg, Map<String, Object> localOrg, boolean updateBatchNo);

    void updateExternalOrg(ExternalOrgEntity externalOrgEntity, Integer batchNo, SyncDirectionEnum direction, Long localOrgId, ConnectorOrg externalOrg, Map<String, Object> localOrg, boolean updateBatchNo, Integer orgLevel);

    /**
     * 修改批次
     * @param entityId
     * @param batchNo
     */
    void updateExternalOrg(Long entityId, Integer batchNo);

    void updateExternalOrg(Long entityId, Long localOrgId, String profile);

    /**
     * 删除外部组织
     *
     * @param connector
     * @param externalOrgEntity
     */
    void deleteExternalOrg(Connector connector, ExternalOrgEntity externalOrgEntity);

    void deleteExternalOrg(Long connectorId, List<Long> orgIds);

    void deleteExternalOrgByExternalId(Long connector, List<String> orgIds);

    void deleteExternalOrg(Long externalOrgEntityId);

    void deleteExternalByConnectorId(Long connectorId);

    /**
     * 获取某一批次同步后要删除的外部组织
     *
     * @param connector
     * @return
     */
    List<ExternalOrgEntity> findToBeDeleted(Connector connector);

    LimitResult<ExternalOrgEntity> findToBeDeleted(Long connectorId, Integer batchNo, int limit, long offset);

    LimitResult<ExternalOrgEntity> limitExternalOrgForSync(Long connectorId, int batchNo, int limit, long offset);

    /**
     * 获取连接器下的所有外部部门
     *
     * @param connectorId 连接器id
     * @param attrs 需要返回的属性，为空返回所有属性
     * @return 连接器下的所有外部部门
     */
    List<ExternalOrgEntity> getByConnectorId(Long connectorId, String... attrs);

    /**
     * 获取连接器某一批次下的全部外部部门
     * @param connectorId
     * @param batchNo
     * @param attrs
     * @return
     */
    List<ExternalOrgEntity> listByBatchNo(Long connectorId, int batchNo, String... attrs);
}
