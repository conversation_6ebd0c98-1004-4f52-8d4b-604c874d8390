package com.cyberscraft.uep.iam.common.domain.data;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/8/30 8:06 下午
 */
public class FieldDictChangeMessage implements Serializable {
    private Long id;

    private Integer eventType;

    private Integer fieldType;

    private String tenantId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getEventType() {
        return eventType;
    }

    public void setEventType(Integer eventType) {
        this.eventType = eventType;
    }

    public Integer getFieldType() {
        return fieldType;
    }

    public void setFieldType(Integer fieldType) {
        this.fieldType = fieldType;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    @Override
    public String toString() {
        return "FieldDictChangeMessage{" +
                "id=" + id +
                ", eventType=" + eventType +
                ", fieldType=" + fieldType +
                ", tenantId='" + tenantId + '\'' +
                '}';
    }
}
