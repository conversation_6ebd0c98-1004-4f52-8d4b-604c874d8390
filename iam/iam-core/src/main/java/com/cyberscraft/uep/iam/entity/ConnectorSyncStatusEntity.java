package com.cyberscraft.uep.iam.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

/**
 * <p>
 * IAM 数据全局同步状态表
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2020-06-12
 */
@TableName("iam_connector_sync_status")
public class ConnectorSyncStatusEntity implements Serializable {

    private static final long serialVersionUID=1L;

      /**
     * 主键
     */
        @TableId(value = "id", type = IdType.ASSIGN_ID)
      private Long id;

      /**
     * 0未同步(未推送)，1同步中(推送中),只有在未同步状态下，推送才能进行运行,如果正在同步中，则所有同步任务等待下一个时间周期
     */
      private Integer status;

      /**
     * 租户ID
     */
      private String tenantId;

    
    public Long getId() {
        return id;
    }

      public void setId(Long id) {
          this.id = id;
      }
    
    public Integer getStatus() {
        return status;
    }

      public void setStatus(Integer status) {
          this.status = status;
      }
    
    public String getTenantId() {
        return tenantId;
    }

      public void setTenantId(String tenantId) {
          this.tenantId = tenantId;
      }

    @Override
    public String toString() {
        return "ConnectorSyncStatusEntity{" +
              "id=" + id +
                  ", status=" + status +
                  ", tenantId=" + tenantId +
              "}";
    }
}
