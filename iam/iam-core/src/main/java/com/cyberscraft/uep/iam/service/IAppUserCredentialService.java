package com.cyberscraft.uep.iam.service;

import com.cyberscraft.uep.common.dto.QueryPage;
import com.cyberscraft.uep.iam.dto.request.AppUserCredentialsVO;
import com.cyberscraft.uep.iam.dto.request.UpdateAppUserCredentialsVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/10/09 17:17
 * @Version 1.0
 * @Description app用户凭证接口
 */
public interface IAppUserCredentialService {

    /**
     * 添加app用户凭证
     *
     * @param userCredentialsVO app用户凭证对象
     */
    void createAppUserCredential(AppUserCredentialsVO userCredentialsVO);

    /**
     * 删除app用户凭证
     *
     * @param id app用户凭证id
     */
    AppUserCredentialsVO deleteAppUserCredential(Long id);

    /**
     * 删除app关联的所有用户凭证
     *
     * @param clientId 应用id
     */
    void deleteByClientId(String clientId);

    /**
     * 更新app用户凭证
     *
     * @param userCredentialsVO app用户凭证对象
     */
    AppUserCredentialsVO updateAppUserCredential(UpdateAppUserCredentialsVO userCredentialsVO);

    /**
     * 分页搜索app用户凭证
     *
     * @param clientId app客户端id
     * @param page     页码
     * @param size     每页数量
     * @return app用户凭证列表
     */
    QueryPage<AppUserCredentialsVO> searchAppUserCredential(String clientId, String filter, Integer page, Integer size);

    /**
     * 根据app客户端id获取app用户凭证列表
     *
     * @param clientId app客户端id
     * @return app用户凭证列表
     */
    List<Map<String, Object>> getAppUserCredentialByClientId(String clientId);

    /**
     * 加密代填的登录密码
     *
     * @param passwordPlain 密码明文
     * @return 密码密文
     */
    String encryptLoginPassword(String passwordPlain);

    /**
     * 更新app用户凭证状态
     *
     * @param id     app用户凭证id
     * @param status 状态
     */
    AppUserCredentialsVO updateAppUserCredentialStatus(Long id, Boolean status);

    /**
     * 判断是否还有激活的凭证
     *
     * @param clientId app客户端id
     * @return 是否有激活的凭证
     */
    boolean isExistActiveCredential(String clientId);
}
