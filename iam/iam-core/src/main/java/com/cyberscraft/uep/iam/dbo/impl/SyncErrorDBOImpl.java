package com.cyberscraft.uep.iam.dbo.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cyberscraft.uep.common.dto.QueryPage;
import com.cyberscraft.uep.common.util.PagingUtil;
import com.cyberscraft.uep.iam.dao.SyncErrorDao;
import com.cyberscraft.uep.iam.dbo.SyncErrorDBO;
import com.cyberscraft.uep.iam.entity.SyncErrorEntity;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author：xhquan
 * @Project：uep-services
 * @since：2023/6/19 21:12
 */
@Service
public class SyncErrorDBOImpl extends ServiceImpl<SyncErrorDao, SyncErrorEntity> implements SyncErrorDBO {

    @Override
    public List<SyncErrorEntity> getSyncErrorByConnectorId(Long connectorId) {
        LambdaQueryWrapper<SyncErrorEntity> queryWrapper = new QueryWrapper().lambda();
        queryWrapper.eq(SyncErrorEntity::getConnectorId, connectorId);
        List<SyncErrorEntity> syncErrorEntities = this.baseMapper.selectList(queryWrapper);
        return syncErrorEntities;
    }

    @Override
    public QueryPage<SyncErrorEntity> getSyncErrorList(QueryPage<SyncErrorEntity> queryPage, Long taskId, Integer status, String name, String type, Integer syncDirection) {
        IPage<SyncErrorEntity> mybatisPage = PagingUtil.toMybatisPage(queryPage);

        LambdaQueryWrapper<SyncErrorEntity> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(name)) {
            queryWrapper.eq(SyncErrorEntity::getExternalName, name);
        }
        if (StringUtils.isNotBlank(type)) {
            queryWrapper.eq(SyncErrorEntity::getType, type);
        }
        queryWrapper.eq(SyncErrorEntity::getTaskId, taskId);
        if (status != null) {
            queryWrapper.eq(SyncErrorEntity::getStatus, status);
        }
        queryWrapper.eq(SyncErrorEntity::getSyncDirection, syncDirection);

        mybatisPage = page(mybatisPage, queryWrapper);
        return PagingUtil.fromMybatisPageToQueryPage(mybatisPage, queryPage);
    }
}
