package com.cyberscraft.uep.iam.service.config;

import org.jose4j.jwt.consumer.JwtConsumer;
import org.jose4j.jwt.consumer.JwtConsumerBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <p>
 *  钉钉零信任网关配置
 * </p>
 *
 * <AUTHOR>
 * @since 2022/2/20 4:32 下午
 */
@Configuration
public class DDZtnaConfig {
    @Bean(name = "ddZtnaTokenConsumer")
    public JwtConsumer createDDZtnaTokenConsumer(){
        JwtConsumer consumer = new JwtConsumerBuilder()
                .setSkipAllDefaultValidators()
                .setSkipDefaultAudienceValidation()
                .setSkipSignatureVerification()
                .build();
        return consumer;
    }
}
