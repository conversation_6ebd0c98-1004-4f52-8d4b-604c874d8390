package com.cyberscraft.uep.iam.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cyberscraft.uep.iam.entity.FieldExtDataEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * IAM-扩展属性表 Mapper 接口
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2019-07-04
 */
public interface FieldExtDataDao extends BaseMapper<FieldExtDataEntity> {
    boolean updateByRelationAndRef(@Param("entity") FieldExtDataEntity entity);

    List<Long> getListByExtData(@Param("map") Map<Long,Object> extMap);
}
