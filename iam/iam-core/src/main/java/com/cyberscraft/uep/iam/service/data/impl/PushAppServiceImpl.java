package com.cyberscraft.uep.iam.service.data.impl;

import com.cyberscraft.uep.iam.entity.AppEntity;
import com.cyberscraft.uep.iam.entity.PushAppEntity;
import com.cyberscraft.uep.iam.entity.PushDataEntity;
import com.cyberscraft.uep.iam.dbo.PushAppDBO;
import com.cyberscraft.uep.iam.dbo.PushDataDBO;
import com.cyberscraft.uep.iam.dto.enums.PushStatusEnum;
import com.cyberscraft.uep.iam.service.config.PushProperties;
import com.cyberscraft.uep.iam.service.data.IPushAppService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2019-09-27 10:58
 */
@Service
public class PushAppServiceImpl implements IPushAppService {

    private static final Logger logger = LoggerFactory.getLogger(PushAppServiceImpl.class);
    @Autowired
    private PushProperties pushProperties;
    @Autowired
    private PushAppDBO pushAppDBO;
    @Autowired
    private PushDataDBO pushDataDBO;

    @Transactional(propagation = Propagation.REQUIRES_NEW,rollbackFor = Exception.class)
    @Override
    public boolean savePushApp(List<AppEntity> appList, PushDataEntity pushDataEntity,String dataJson) {
        if(CollectionUtils.isEmpty(appList)){
            logger.info("appList is null");
            return false;
        }
        if(pushDataEntity == null || pushDataEntity.getId() == null){
            logger.info("pushDataEntity is null");
            return false;
        }
        if(StringUtils.isBlank(dataJson)){
            logger.info("dataJson is null");
            return false;
        }

        LocalDateTime nowTime = LocalDateTime.now();
        List<PushAppEntity> pushAppEntityList = new ArrayList<>();
        PushAppEntity pushAppEntity ;
        for (AppEntity appEntity : appList) {
            pushAppEntity = new PushAppEntity();
            pushAppEntity.setAppRefId(appEntity.getId());
            pushAppEntity.setPushDataRefId(pushDataEntity.getId());
            pushAppEntity.setDataContent(dataJson);
            pushAppEntity.setStatus(PushStatusEnum.NORMAL.getValue());
            pushAppEntity.setMaxCount(pushProperties.getMaxCount());
            pushAppEntity.setCurrentCount(1);
            pushAppEntity.setNextTime(nowTime.plusSeconds(pushProperties.getIntervalTime()));
            pushAppEntity.setCreateTime(nowTime);

            pushAppEntity.setChangeType(pushDataEntity.getChangeType());
            pushAppEntity.setBusinessDataId(pushDataEntity.getBusinessDataId());
            pushAppEntity.setBusinessType(pushDataEntity.getBusinessType());
            pushAppEntity.setSendTime(pushDataEntity.getCreateTime());
            pushAppEntityList.add(pushAppEntity);
        }

        this.pushAppDBO.saveBatch(pushAppEntityList);
        pushDataDBO.removeById(pushDataEntity.getId());
        return true;
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW,rollbackFor = Exception.class)
    @Override
    public boolean deleteById(Long id) {
        return this.pushAppDBO.removeById(id);
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW,rollbackFor = Exception.class)
    @Override
    public boolean updatePushApp(PushAppEntity pushAppEntity) {
        LocalDateTime nowTime = LocalDateTime.now();
        //更新推送的数据
        pushAppEntity.setCurrentCount(pushAppEntity.getCurrentCount()+1);
        ////已经达到最大推送次数,设置不再推送状态
        //if(pushAppEntity.getCurrentCount() >= pushAppEntity.getMaxCount()){
        //    pushAppEntity.setStatus(PushStatusEnum.DISABLE.getValue());
        //}else{//未达到最大次数，更新下次推送时间及下次推送内容
            LocalDateTime nextSendTime = nowTime.plusSeconds(pushProperties.getIntervalTime());
            pushAppEntity.setNextTime(nextSendTime);
        //}
        pushAppEntity.setUpdateTime(nowTime);
        this.pushAppDBO.updateById(pushAppEntity);
        return true;
    }
}
