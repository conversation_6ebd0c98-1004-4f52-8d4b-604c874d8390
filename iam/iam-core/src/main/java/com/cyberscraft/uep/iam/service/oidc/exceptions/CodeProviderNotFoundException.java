package com.cyberscraft.uep.iam.service.oidc.exceptions;

import org.springframework.security.core.AuthenticationException;

/**
 * <p>
 *     当使用第三方登录时，未找到code的提供者时抛出该异常
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2020-03-11 09:52
 */
public class CodeProviderNotFoundException extends AuthenticationException {
    public CodeProviderNotFoundException(String msg, Throwable t) {
        super(msg, t);
    }

    public CodeProviderNotFoundException(String msg) {
        super(msg);
    }
}
