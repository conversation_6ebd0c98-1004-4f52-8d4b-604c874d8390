package com.cyberscraft.uep.iam.service.data.transfer;

import com.cyberscraft.uep.account.client.domain.ConnectorPushDsProfile;
import com.cyberscraft.uep.common.dto.PageView;
import com.cyberscraft.uep.common.dto.QueryPage;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.iam.dto.enums.ConnectorTypeEnum;
import com.cyberscraft.uep.iam.dto.enums.DataSyncTypeEnum;
import com.cyberscraft.uep.iam.dto.enums.PushConnectorStatusEnum;
import com.cyberscraft.uep.iam.dto.request.connector.ConnectorPushRemoveVO;
import com.cyberscraft.uep.iam.dto.response.data.PushConnectorDsProfileVO;
import com.cyberscraft.uep.iam.dto.response.data.PushConnectorListVO;
import com.cyberscraft.uep.iam.dto.response.data.PushConnectorVO;
import com.cyberscraft.uep.iam.entity.ConnectorPushRemoveEntity;
import com.cyberscraft.uep.iam.entity.PushConnectorEntity;
import com.cyberscraft.uep.iam.service.IFieldDictService;
import com.cyberscraft.uep.iam.service.transfer.TypeMapper;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/***
 *
 * @date 2021/6/15
 * <AUTHOR>
 ***/
@Mapper(componentModel = "spring", uses = TypeMapper.class)
public abstract class PushConnectorTransfer {

    public static PushConnectorTransfer INSTANCE = Mappers.getMapper(PushConnectorTransfer.class);

    @Autowired
    private IFieldDictService fieldDictService;

    /***
     *
     * @param po
     * @return
     */
    public PushConnectorDsProfileVO domain2DsProfileVO(ConnectorPushDsProfile po) {
        if (po == null) {
            return null;
        }

        PushConnectorDsProfileVO ret = new PushConnectorDsProfileVO();

        if (po.getAccountType() != null) {
            ret.setAccountType(TypeMapper.asEnum(Integer.parseInt(po.getAccountType()), ConnectorTypeEnum.class));
        }
        ret.setUserAttrs(po.getUserAttrs());
        ret.setOrgAttrs(po.getOrgAttrs());
        ret.setPushPeriod(po.getPushPeriod());
        ret.setPushOrgs(po.getPushOrgs());
        ret.setOrgFields(po.getOrgFields());
        ret.setUserFields(po.getUserFields());
        return ret;
    }


    /***
     *
     * @param list
     * @return
     */
    public abstract List<PushConnectorDsProfileVO> domain2DsProfileVO(List<ConnectorPushDsProfile> list);


//    public String toFieldNameMappingJson(Map<String, String> domainNameMapping, FieldType fieldType) {
//        Map<String, String> fieldNameMapping = new HashMap<>(domainNameMapping.size());
//        for (Map.Entry<String, String> entry : domainNameMapping.entrySet()) {
//            FieldDictEntity fieldDictEntity = fieldDictService.getByDomainName(entry.getValue(), fieldType);
//            if (fieldDictEntity != null) {
//                fieldNameMapping.put(entry.getKey(), fieldDictEntity.getFieldName());
//            }
//        }
//        return JsonUtil.obj2Str(fieldNameMapping);
//    }

//    protected Map<String, String> toDomainNameMapping(String fieldNameMapAttrs, FieldType fieldType) {
//        Map<String, Object> fieldNameMapping = JsonUtil.str2Map(fieldNameMapAttrs);
//        Map<String, String> domainNameMapping = new HashMap<>(fieldNameMapping.size());
//        for (Map.Entry<String, Object> entry : fieldNameMapping.entrySet()) {
//            FieldDictEntity fieldDictEntity = fieldDictService.getByFieldName(entry.getValue().toString(), fieldType);
//            if (fieldDictEntity == null) {
//                continue;
//            }
//            domainNameMapping.put(entry.getKey(), fieldDictEntity.getDomainName());
//        }
//        return domainNameMapping;
//    }

    /***
     *
     * @param po
     * @return
     */
    public PushConnectorVO entity2VO(PushConnectorEntity po) {
        if (po == null) {
            return null;
        }
        PushConnectorVO vo = new PushConnectorVO();
        vo.setId(String.valueOf(po.getId()));
        vo.setName(po.getName());
        vo.setRootCode(po.getRootCode());
        vo.setPushOrgs(TypeMapper.asBoolean(po.getPushOrgs()));
        vo.setConfigId(po.getConfigId() == null ? null : String.valueOf(po.getConfigId()));
        vo.setCreateBy(po.getCreateBy());
        vo.setCreateTime(po.getCreateTime());
        vo.setUpdateBy(po.getUpdateBy());
        vo.setUpdateTime(po.getUpdateTime());
        vo.setRootGroupId(StringUtils.isNotEmpty(po.getRootGroupId()) ? JsonUtil.str2List(po.getRootGroupId(), String.class) : null);
        vo.setType(TypeMapper.asEnum(po.getType(), ConnectorTypeEnum.class));
        vo.setSyncType(TypeMapper.asEnum(po.getSyncType(), DataSyncTypeEnum.class));
        vo.setStatus(TypeMapper.asEnum(po.getStatus(), PushConnectorStatusEnum.class));
        vo.setPushPeriod(po.getPushPeriod());
        vo.setForceWrite(po.getForceWrite());
        vo.setNextPushTime(po.getNextPushTime());
        vo.setPlannedStartTime(po.getPlannedStartTime() == null ? null : po.getPlannedStartTime().toLocalTime());
        vo.setPushTime(po.getPushTime());
        vo.setPushStartTime(po.getPushStartTime());
        vo.setPushEndTime(po.getPushEndTime());
        vo.setExtraConfig(JsonUtil.str2Map(po.getExtraConfig()));
        vo.setConfig(JsonUtil.str2Map(po.getConfig()));
        vo.setDeleteLimit(po.getDeleteLimit());
        if (po.getProxyId() != null) {
            vo.setProxyId(po.getProxyId().toString());
        }
        vo.setFilterConfig(JsonUtil.str2Map(po.getFilterConfig()));
        vo.setEnableFilter(TypeMapper.asBoolean(po.getEnableFilter()));
//        vo.setMapAttrs(JsonUtil.obj2Str(toDomainNameMapping(po.getUserAttrMapping(), FieldType.USER)));
//        vo.setMapOrgAttrs(JsonUtil.obj2Str(toDomainNameMapping(po.getOrgAttrMapping(), FieldType.ORG)));

        Integer pushType = po.getPushType();
        if (pushType == null) {
            Integer pushPeriod = po.getPushPeriod();
            Integer deleteLimit = po.getDeleteLimit();
            if (pushPeriod == 0) {
                pushType = 2;
            } else {
                if (deleteLimit == 0) {
                    pushType = 1;
                } else {
                    pushType = 3;
                }
            }
        }
        vo.setPushType(pushType);
        vo.setRoleApp(po.getRoleApp());
        vo.setRoles(JsonUtil.str2List(po.getRoles(), Long.class));
        vo.setFlow(po.getFlow());

        return vo;
    }

    /***
     *
     * @param po
     * @return
     */
    public PushConnectorListVO entity2ListVO(PushConnectorEntity po) {
        if (po == null) {
            return null;
        }
        PushConnectorListVO vo = new PushConnectorListVO();
        vo.setId(String.valueOf(po.getId()));
        vo.setName(po.getName());
        vo.setRootCode(po.getRootCode());
        vo.setConfigId(po.getConfigId() == null ? null : String.valueOf(po.getConfigId()));
        vo.setConfig(JsonUtil.str2Map(po.getConfig()));
        vo.setExtraConfig(JsonUtil.str2Map(po.getExtraConfig()));
        vo.setCreateBy(po.getCreateBy());
        vo.setCreateTime(po.getCreateTime());
        vo.setUpdateBy(po.getUpdateBy());
        vo.setUpdateTime(po.getUpdateTime());
        vo.setRootGroupId(StringUtils.isNotEmpty(po.getRootGroupId()) ? JsonUtil.str2List(po.getRootGroupId(), String.class) : null);
        vo.setType(TypeMapper.asEnum(po.getType(), ConnectorTypeEnum.class));
        vo.setSyncType(TypeMapper.asEnum(po.getSyncType(), DataSyncTypeEnum.class));
        vo.setStatus(TypeMapper.asEnum(po.getStatus(), PushConnectorStatusEnum.class));
        vo.setDeleteLimit(po.getDeleteLimit());
        vo.setPushPeriod(po.getPushPeriod());
        vo.setNextPushTime(po.getNextPushTime());
        vo.setPushTime(po.getPushTime());
        vo.setPushStartTime(po.getPushStartTime());
        vo.setPushEndTime(po.getPushEndTime());
        vo.setPushBatchNo(po.getPushBatchNo());
        vo.setFilterConfig(JsonUtil.str2Map(po.getFilterConfig()));
        vo.setEnableFilter(TypeMapper.asBoolean(po.getEnableFilter()));
        vo.setPushOrgs(TypeMapper.asBoolean(po.getPushOrgs()));
        if (po.getProxyId() != null) {
            vo.setProxyId(po.getProxyId().toString());
        }
        vo.setFlow(po.getFlow());
        return vo;
    }

    /***
     *
     * @param list
     * @return
     */
    public abstract List<PushConnectorListVO> entity2ListVO(List<PushConnectorEntity> list);

    /***
     * 转换成页面对像对应的包装的VO列表
     * @param bean
     * @param bean
     * @return
     */
    public PageView<PushConnectorListVO> entity2VOPage(PageView<PushConnectorEntity> bean) {
        PageView<PushConnectorListVO> queryPage = new PageView<>();
        queryPage.setItems(entity2ListVO(bean.getItems()));
        queryPage.setTotal(bean.getTotal());
        queryPage.setSize(bean.getSize());
        queryPage.setPage(bean.getPage());
        return queryPage;
    }

    public abstract QueryPage<ConnectorPushRemoveEntity> pushVoPageToPushEntityPage(QueryPage<ConnectorPushRemoveVO> vo);

    public abstract QueryPage<ConnectorPushRemoveVO> pushEntityPageToPushVoPage(QueryPage<ConnectorPushRemoveEntity> vo);
}
