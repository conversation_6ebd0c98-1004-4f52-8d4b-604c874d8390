package com.cyberscraft.uep.iam.service.saml.service;

import org.opensaml.saml.common.AbstractSAMLObjectBuilder;
import org.opensaml.saml.common.xml.SAMLConstants;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/2/16 6:08 下午
 */
public class AttributeValueBuilder extends AbstractSAMLObjectBuilder<AttributeValue> {
    @Nonnull
    @Override
    public AttributeValue buildObject() {
        return buildObject(SAMLConstants.SAML20_NS, AttributeValue.DEFAULT_ELEMENT_LOCAL_NAME,
                SAMLConstants.SAML20_PREFIX);
    }

    @Nonnull
    @Override
    public AttributeValue buildObject(@Nullable String namespaceURI, @Nonnull String localName, @Nullable String namespacePrefix) {
        return new AttributeValueImpl(namespaceURI, localName, namespacePrefix);
    }
}
