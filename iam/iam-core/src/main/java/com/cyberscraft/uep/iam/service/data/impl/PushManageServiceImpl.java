package com.cyberscraft.uep.iam.service.data.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.cyberscraft.uep.account.client.domain.Connector;
import com.cyberscraft.uep.account.client.domain.ConnectorOrg;
import com.cyberscraft.uep.account.client.exception.ThirdPartyExceedDeleteLimitException;
import com.cyberscraft.uep.account.client.service.IExternalConnectService;
import com.cyberscraft.uep.acm.api.FlowsClientApi;
import com.cyberscraft.uep.common.dto.ReturnResultVO;
import com.cyberscraft.uep.common.util.DateUtil;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.common.util.SnowflakeIDUtil;
import com.cyberscraft.uep.iam.common.FinishedUsersHandler;
import com.cyberscraft.uep.iam.common.exception.UserCenterException;
import com.cyberscraft.uep.iam.common.exception.UserExceedException;
import com.cyberscraft.uep.iam.constants.RedisKeyConstants;
import com.cyberscraft.uep.iam.dbo.ConnectorPushHistoryDBO;
import com.cyberscraft.uep.iam.dbo.PushConnectorDBO;
import com.cyberscraft.uep.iam.dto.domain.SyncStats;
import com.cyberscraft.uep.iam.dto.enums.BooleanEnums;
import com.cyberscraft.uep.iam.dto.enums.SyncStatus;
import com.cyberscraft.uep.iam.dto.response.SyncScheduleDto;
import com.cyberscraft.uep.iam.entity.*;
import com.cyberscraft.uep.iam.errors.TransactionErrorType;
import com.cyberscraft.uep.iam.service.IDataSyncService;
import com.cyberscraft.uep.iam.service.connector.ConnectorSyncTypeEnum;
import com.cyberscraft.uep.iam.service.connector.task.PushTask;
import com.cyberscraft.uep.iam.service.connector.task.PushTaskMonitor;
import com.cyberscraft.uep.iam.service.data.IPushConnectorService;
import com.cyberscraft.uep.iam.service.data.IPushManageService;
import com.cyberscraft.uep.iam.service.user.*;
import com.cyberscraft.uep.mq.constant.MQConstant;
import com.cyberscraft.uep.mq.enums.BroadcastMessageType;
import com.cyberscraft.uep.mq.service.IMessageSendClient;
import com.cyberscraft.uep.mq.vo.BroadcastMessage;
import com.cyberscraft.uep.mq.vo.MessageEntry;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import com.cyberscraft.uep.task.TaskHolder;
import com.cyberscraft.uep.task.TaskIsCanceledException;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/***
 *
 * @date 2021/6/16
 * <AUTHOR>
 ***/
@Service
public class PushManageServiceImpl implements IPushManageService {

    protected final static Logger LOG = LoggerFactory.getLogger(PushManageServiceImpl.class);

    @Resource
    protected PushConnectorDBO pushConnectorDBO;

    @Resource
    private IMessageSendClient messageSendClient;

    @Autowired
    private ConnectorPushHistoryDBO connectorPushHistoryDBO;

    @Autowired
    private IDataSyncService dataSyncService;

    @Autowired
    private IUsersPushService usersPushService;

    @Autowired
    private IUserService userService;

    @Autowired
    private IRolesPushService rolesPushService;

    @Autowired
    private IExternalConnectService externalConnectService;

    @Autowired
    private PushTaskMonitor pushTaskMonitor;

    @Autowired
    private IPushConnectorService pushConnectorService;

    @Resource
    protected RedissonClient redissonClient;

    @Autowired
    private IExternalOrgService externalOrgService;

    @Autowired
    private IExternalUserService externalUserService;

    @Autowired(required = false)
    private FlowsClientApi flowsClientApi;

    @Override
    public void push(PushConnectorEntity pushConnector, boolean isForceFullSync, ConnectorSyncTypeEnum syncTypeEnum) {
        Connector connector = usersPushService.toConnector(pushConnector);
        try {
            FinishedUsersHandler.clearCache();
            TenantHolder.setTenantCode(pushConnector.getTenantId());
            String lockKey = String.format(RedisKeyConstants.CONNECTOR_PUSH_LOCK_KEY, TenantHolder.getTenantCode(), pushConnector.getId());
            RLock lock = redissonClient.getLock(lockKey);
            boolean unlock = true;
            if (!pushTaskMonitor.tryLock(lock, RedisKeyConstants.CONNECTOR_SYNC_WAIT, RedisKeyConstants.CONNECTOR_SYNC_EXPIRE, TimeUnit.MILLISECONDS)) {
                LOG.warn("cannot get lock for tenant:{}, connector:{}", TenantHolder.getTenantCode(), pushConnector.getId());
                return;
            }

            try {
                //先清空本connector对应的pushData
                dataSyncService.cleanAllPushData(pushConnector.getId());

                connector.setSyncBatchNo(connector.getSyncBatchNo() + 1);
                Long taskId = connector.getTaskId();
                if (taskId == null) {
                    taskId = SnowflakeIDUtil.getId();
                }
                connector.setTaskId(taskId);
                connector.setFullSync(isForceFullSync);
                LOG.info("connector info is {}", connector);


                if (pushConnector.getFlow() == 1) {
                    unlock = false;
                    pushNew(connector, syncTypeEnum, true);
                } else {
                    legacyPush(pushConnector, connector, syncTypeEnum);
                }
            } finally {
                FinishedUsersHandler.clearCache();

                if (unlock && lock.isLocked()) {
                    lock.unlock();
                }
            }
        } finally {
            externalConnectService.closeConnection(connector);
        }
    }

    @Override
    public void startConnectorTask(Connector connector, ConnectorSyncTypeEnum connectorSyncTypeEnum) {
        try {
            Integer importPeriod = connector.getImportPeriod();
            LocalDateTime startPushTime = LocalDateTime.now();
            if (importPeriod > 0 && ConnectorSyncTypeEnum.SCHEDULE.equals(connectorSyncTypeEnum)) {
                LocalDateTime plannedStartTime = connector.getPlannedStartTime();
                if (plannedStartTime == null) {
                    plannedStartTime = startPushTime;
                }

                LocalDateTime nextPushTime = DateUtil.nextLatestTime(plannedStartTime, importPeriod, false);
                //更新同步批次号和下次执行时间
                LOG.info("update connector {} plannedStartTime  {} next push time is {}", connector.getId(), plannedStartTime, nextPushTime);
                pushConnectorDBO.updateSyncBatchNoAndNextTime(connector.getId(), connector.getSyncBatchNo(), nextPushTime);
            } else {
                //更新同步批次号
                pushConnectorDBO.updateSyncBatchNo(connector.getId(), connector.getSyncBatchNo());
            }
            ConnectorPushHistoryEntity pushHistory = new ConnectorPushHistoryEntity();
            pushHistory.setType(connectorSyncTypeEnum.getValue());
            pushHistory.setPushStartTime(LocalDateTime.now());
            pushHistory.setStatus(SyncStatus.IN_PROGRESS.getValue());
            pushHistory.setViewStatus(SyncStatus.IN_PROGRESS.getValue());
            pushHistory.setConnectorId(connector.getId());
            pushHistory.setId(connector.getTaskId());
            pushHistory.setBatchNo(connector.getSyncBatchNo());
            pushConnectorDBO.createConnectorHistory(pushHistory);
        } catch (Exception e) {
            LOG.warn("start push connector task error", e);
            throw e;
        }
    }

    @Override
    public void completeConnectorTask(PushConnectorEntity pushConnector, Connector connector, SyncStats syncStats) {
        try {
            Long taskId = connector.getTaskId();
            ConnectorPushHistoryEntity pushHistory = connectorPushHistoryDBO.getById(taskId);
            if (pushHistory == null || SyncStatus.IN_PROGRESS.getValue() != pushHistory.getStatus()) {
                return;
            }

            pushHistory.setStats(JsonUtil.obj2Str(syncStats));
            pushHistory.setStatus(syncStats.status.getValue());
            pushHistory.setViewStatus(syncStats.status.getValue());
            if (syncStats.hasFailure()) {
                pushHistory.setViewStatus(SyncStatus.EXCEPTION.getValue());
            }

            LambdaUpdateWrapper<PushConnectorEntity> queryWrapper = new UpdateWrapper<PushConnectorEntity>().lambda();
            queryWrapper.eq(PushConnectorEntity::getId, pushConnector.getId());
            // 获取当前时间
            LocalDateTime pushStartTime = pushConnector.getPushStartTime();
            queryWrapper.set(PushConnectorEntity::getPushEndTime, LocalDateTime.now());
            // 检查导入周期是否大于0
            Integer pushPeriod = pushConnector.getPushPeriod();
            if (pushPeriod > 0 && ConnectorSyncTypeEnum.SCHEDULE.getValue() == pushHistory.getType()) {
                // 计算基于周期的下一次执行时间
                LocalDateTime plannedStartTime = pushConnector.getPlannedStartTime();
                if (plannedStartTime == null) {
                    plannedStartTime = pushStartTime;
                }

                LocalDateTime nextSyncTime = DateUtil.nextLatestTime(plannedStartTime, pushPeriod, false);

                pushConnector.setNextPushTime(nextSyncTime);
                queryWrapper.set(PushConnectorEntity::getNextPushTime, nextSyncTime);
            }
            pushConnectorDBO.update(queryWrapper);

            if (pushHistory.getStatus() == null) {
                pushHistory.setStatus(SyncStatus.SUCCESS.getValue());
            }
            if (pushHistory.getViewStatus() == null) {
                pushHistory.setViewStatus(SyncStatus.SUCCESS.getValue());
            }
            pushHistory.setPushEndTime(LocalDateTime.now());
            connectorPushHistoryDBO.updateById(pushHistory);

        } catch (Exception e) {
            LOG.warn("complete connector task error", e);
            throw e;
        }
    }

    @Override
    public void failConnectorTask(Long taskId, String message) {
        if (taskId == null || taskId <= 0L) {
            return;
        }
        try {
            String tenantId = TenantHolder.getTenantCode();
            ConnectorPushHistoryEntity connectorHistory = connectorPushHistoryDBO.getById(Long.valueOf(taskId));
            if (connectorHistory != null && connectorHistory.getStatus() == SyncStatus.IN_PROGRESS.getValue()) {
                if (StringUtils.isNotBlank(message)) {
                    SyncStats syncStats = new SyncStats();
                    String stats = connectorHistory.getStats();
                    if (StringUtils.isNotBlank(stats)) {
                        syncStats = JsonUtil.str2Obj(stats, SyncStats.class);
                    }
                    if (syncStats != null) {
                        syncStats.failReason = message;
                        connectorHistory.setStats(JsonUtil.obj2Str(syncStats));
                    }
                }

                connectorHistory.setStatus(SyncStatus.FAILED.getValue());
                connectorHistory.setViewStatus(SyncStatus.FAILED.getValue());
                connectorHistory.setPushEndTime(LocalDateTime.now());
                connectorPushHistoryDBO.updateById(connectorHistory);

                Long connectorId = connectorHistory.getConnectorId();

                //删除redis锁
                String lockKey = String.format(RedisKeyConstants.CONNECTOR_PUSH_LOCK_KEY, tenantId, connectorId);
                RLock lock = redissonClient.getLock(lockKey);
                lock.forceUnlock();

                //删除link表的无效数据
                externalUserService.deleteExternalUserByExternalId(connectorId, Arrays.asList(""));
                externalOrgService.deleteExternalOrgByExternalId(connectorId, Arrays.asList(""));
            }
        } catch (Exception e) {
            LOG.warn("fail connector task error", e);
            throw e;
        } finally {
            FinishedUsersHandler.clearCache();
        }
    }


    @Override
    public void completePushTask(Long connectorId, Long taskId) {
        String tenantId = TenantHolder.getTenantCode();
        try {
            ConnectorPushHistoryEntity pushHistory = connectorPushHistoryDBO.getById(taskId);
            String stats = pushHistory.getStats();
            SyncStats syncStats = JsonUtil.str2Obj(stats, SyncStats.class);
            pushHistory.setStatus(syncStats.status.getValue());
            pushHistory.setViewStatus(syncStats.status.getValue());
            if (syncStats.hasFailure()) {
                pushHistory.setViewStatus(SyncStatus.EXCEPTION.getValue());
            }
            pushHistory.setPushEndTime(LocalDateTime.now());
            connectorPushHistoryDBO.updateById(pushHistory);

            PushConnectorEntity pushConnector = pushConnectorService.get(connectorId);
            LambdaUpdateWrapper<PushConnectorEntity> queryWrapper = new UpdateWrapper<PushConnectorEntity>().lambda();
            queryWrapper.eq(PushConnectorEntity::getId, connectorId);
            // 获取当前时间
            LocalDateTime pushStartTime = pushConnector.getPushStartTime();
            queryWrapper.set(PushConnectorEntity::getPushEndTime, LocalDateTime.now());
            // 检查导入周期是否大于0
            Integer pushPeriod = pushConnector.getPushPeriod();
            if (pushPeriod > 0 && ConnectorSyncTypeEnum.SCHEDULE.getValue() == pushHistory.getType()) {
                // 计算基于周期的下一次执行时间
                LocalDateTime plannedStartTime = pushConnector.getPlannedStartTime();
                if (plannedStartTime == null) {
                    plannedStartTime = pushStartTime;
                }

                LocalDateTime nextSyncTime = DateUtil.nextLatestTime(plannedStartTime, pushPeriod, false);

                pushConnector.setNextPushTime(nextSyncTime);
                queryWrapper.set(PushConnectorEntity::getNextPushTime, nextSyncTime);
            }
            pushConnectorDBO.update(queryWrapper);
        } finally {
            //删除redis锁
            String lockKey = String.format(RedisKeyConstants.CONNECTOR_PUSH_LOCK_KEY, tenantId, connectorId);
            RLock lock = redissonClient.getLock(lockKey);
            lock.forceUnlock();

            // 删除无效数据
            externalUserService.deleteExternalUserByExternalId(connectorId, Arrays.asList(""));
            externalOrgService.deleteExternalOrgByExternalId(connectorId, Arrays.asList(""));
        }

    }

    @Override
    public void stopPushTask(Connector connector, String message) {
        Long taskId = connector.getTaskId();
        if (connector.isFlow()) {
            failConnectorTask(taskId, message);
        } else {
            MessageEntry<BroadcastMessage> messageEntry = new MessageEntry<>();
            messageEntry.setMsg(new BroadcastMessage(
                    BroadcastMessageType.PUSH_TASK_STOP,
                    String.valueOf(taskId),
                    TenantHolder.getTenantCode()));
            messageSendClient.send(MQConstant.BROADCAST_TOPIC, messageEntry);
        }
    }

    private void pushNew(Connector connector, ConnectorSyncTypeEnum connectorSyncTypeEnum, boolean fromIam) {
        // 定时任务执行
        startConnectorTask(connector, connectorSyncTypeEnum);
        Long taskId = connector.getTaskId();

        try {
            if (userService.isUserCountExceed()) {
                throw new UserExceedException();
            }
            if (fromIam) {   //从iam发起
                Long flowId = connector.getId();
                ReturnResultVO<Void> resultVO = flowsClientApi.startFlow(flowId, taskId, new HashMap<>());
                if (!resultVO.getResult()) {
                    failConnectorTask(taskId, resultVO.getErrorDescription());
                }
            }
        } catch (Exception e) {
            LOG.warn("sync flow task fail", e);
            failConnectorTask(taskId, e.getMessage());
        }
    }

    private void legacyPush(PushConnectorEntity pushConnector, Connector connector, ConnectorSyncTypeEnum connectorSyncTypeEnum) {
        Long taskId = connector.getTaskId();
        PushTask task = new PushTask(String.valueOf(taskId));
        task.setTenantId(connector.getTenantId());
        task.setConnectorId(connector.getId().toString());

        try {
            //注册任务监控
            pushTaskMonitor.registerTask(task);

            push(pushConnector, connector, connectorSyncTypeEnum);

            pushTaskMonitor.completeTask(task.getId());
        } catch (Exception e) {
            pushTaskMonitor.terminateTask(task, e);
        }
    }

    public void push(PushConnectorEntity pushConnector, Connector connector, ConnectorSyncTypeEnum syncTypeEnum) {
        startConnectorTask(connector, syncTypeEnum);

        boolean isForceFullSync = connector.isFullSync();
        PushTask task = (PushTask) TaskHolder.getTask();
        SyncStats syncStats = new SyncStats(connector.getId(), "whole");
        SyncScheduleDto scheduleDto = pushConnectorService.getOrgAndUserTotal(pushConnector.getId().toString());
        syncStats.total = scheduleDto.getTotal();
        syncStats.userTotal = scheduleDto.getUserTotal();
        syncStats.orgTotal = scheduleDto.getOrgTotal();
        syncStats.taskId = connector.getTaskId();
        try {
            task.setTenantId(TenantHolder.getTenantCode());
            task.setConnectorId(pushConnector.getId().toString());
            task.setStats(syncStats);

            try {
                if (userService.isUserCountExceed()) {
                    throw new UserExceedException();
                }

                List<OrgEntity> topLevelOrgList = pushConnectorService.getTopLevelOrgList(pushConnector);
                pushConnector.setTopLevelOrgList(topLevelOrgList);

                task.setProgress(1);
                if (BooleanEnums.isTrue(connector.getPushOrgs())) {
                    //全量推送组织机构
                    usersPushService.fullPushOrgs(pushConnector, connector, isForceFullSync, syncStats);
                }

                task.setProgress(40);
                //全量推送用户
                usersPushService.fullPushUsers(pushConnector, connector, isForceFullSync, syncStats);
                task.setProgress(90);

                boolean pushRole = false;
                if (connector.isPushRole()) {
                    pushRole = true;

                    //全量推送角色
                    rolesPushService.fullPushRole(pushConnector, connector, isForceFullSync, syncStats);

                    //全量同步角色组关系
                    rolesPushService.addUserToRole(pushConnector, connector, isForceFullSync, syncStats);
                }

                // 移除已经同步的关联关系
                rolesPushService.removeUserToRole(pushConnector, connector, isForceFullSync, syncStats, pushRole);

                try {
                    //推送用户后的善后处理
                    usersPushService.completeFullPushUsers(pushConnector, connector, syncStats);
                    task.setProgress(97);

                    // 移除已经同步的角色和角色组
                    rolesPushService.completeFullPushRoles(pushConnector, connector, isForceFullSync, syncStats, pushRole);

                    //推送组织机构后的善后处理
                    usersPushService.completeFullPushOrgs(pushConnector, connector, syncStats);

                    task.setProgress(100);
                } catch (ThirdPartyExceedDeleteLimitException de) {
                    LOG.error("push data to connectors has been prevented, connector id:{}, delete {}, exceed limit {}", pushConnector.getId(), de.getDeleteNum(), de.getDeleteLimit(), de);

                    syncStats.status = SyncStatus.PREVENT;
                }

                LOG.info("push data to connectors finished, connector id is:{}, name:{}, type:{}", pushConnector.getId(), pushConnector.getName(), pushConnector.getType());
            } catch (TaskIsCanceledException te) {
                LOG.error("task is killed", te);
                syncStats.failReason = te.getMessage();
                syncStats.status = SyncStatus.FAILED;
            } catch (UserExceedException ue) {
                syncStats.status = SyncStatus.EXCEED;
            } catch (Exception e) {
                LOG.error("push data to connectors has error, connector id:{}, name:{}", pushConnector.getId(), pushConnector.getName(), e);
                syncStats.status = SyncStatus.FAILED;
                syncStats.failReason = e.getMessage();
            }

            completeConnectorTask(pushConnector, connector, syncStats);
        } finally {
            LOG.info("push data to connectors has error, connector id:{}, name:{}", pushConnector.getId(), pushConnector.getName());
        }
    }

    @Override
    public void resetPush(Long connectorId) {
        usersPushService.setLastSyncPosition(connectorId, null);
    }

    @Override
    public void pushUsers(PushConnectorEntity pushConnector, List<UserEntity> users) {
        SyncStats syncStats = new SyncStats(pushConnector.getId(), "user add and update event sync");
        LocalDateTime startTime = LocalDateTime.now();
        Long taskId = SnowflakeIDUtil.getId();

        Connector connector = usersPushService.toConnector(pushConnector);
        connector.setTaskId(taskId);
        syncStats.taskId = taskId;

        try {
            List<OrgEntity> topLevelOrgList = pushConnectorService.getTopLevelOrgList(pushConnector);
            pushConnector.setTopLevelOrgList(topLevelOrgList);

            try {
                for (UserEntity user : users) {
                    usersPushService.pushCreateOrUpdateUser(pushConnector, connector, user, syncStats);
                }
            } catch (Exception e) {
                LOG.warn("pushUsers error", e);
                syncStats.failReason = e.getMessage();
                syncStats.status = SyncStatus.FAILED;
            }

            if (syncStats.isChanged() || SyncStatus.SUCCESS != syncStats.status) {
                ConnectorPushHistoryEntity pushHistory = new ConnectorPushHistoryEntity();
                pushHistory.setId(taskId);
                pushHistory.setType(ConnectorSyncTypeEnum.EVENT.getValue());
                pushHistory.setPushStartTime(startTime);
                pushHistory.setConnectorId(pushConnector.getId());
                pushHistory.setStats(JsonUtil.obj2Str(syncStats));
                pushHistory.setStatus(syncStats.status.getValue());
                pushHistory.setViewStatus(syncStats.status.getValue());
                pushHistory.setPushEndTime(LocalDateTime.now());
                if (syncStats.hasFailure()) {
                    pushHistory.setViewStatus(SyncStatus.EXCEPTION.getValue());
                }
                connectorPushHistoryDBO.saveOrUpdate(pushHistory);
            }
        } finally {
            externalConnectService.closeConnection(connector);
        }
    }

    @Override
    public void pushDeleteUser(PushConnectorEntity pushConnector, List<Long> localUserIds) {
        SyncStats syncStats = new SyncStats(pushConnector.getId(), "user delete event sync");
        LocalDateTime startTime = LocalDateTime.now();
        Long taskId = SnowflakeIDUtil.getId();

        Connector connector = usersPushService.toConnector(pushConnector);
        connector.setTaskId(taskId);
        syncStats.taskId = taskId;

        try {
            try {
                for (Long userId : localUserIds) {
                    usersPushService.pushDeleteUser(pushConnector, connector, userId, syncStats);
                }
            } catch (Exception e) {
                LOG.warn("pushDeleteUser error", e);
            } finally {
                if (syncStats.isChanged()) {
                    ConnectorPushHistoryEntity pushHistory = new ConnectorPushHistoryEntity();
                    pushHistory.setId(taskId);
                    pushHistory.setType(ConnectorSyncTypeEnum.EVENT.getValue());
                    pushHistory.setPushStartTime(startTime);
                    pushHistory.setConnectorId(pushConnector.getId());
                    pushHistory.setStats(JsonUtil.obj2Str(syncStats));
                    pushHistory.setStatus(syncStats.status.getValue());
                    pushHistory.setViewStatus(syncStats.status.getValue());
                    pushHistory.setPushEndTime(LocalDateTime.now());
                    if (syncStats.hasFailure()) {
                        syncStats.status = SyncStatus.FAILED;
                        pushHistory.setViewStatus(SyncStatus.EXCEPTION.getValue());
                    }
                    connectorPushHistoryDBO.saveOrUpdate(pushHistory);
                }
            }
        } finally {
            externalConnectService.closeConnection(connector);
        }
    }

    @Override
    public Map<String, Object> test(PushConnectorEntity connector) {
        Connector conn = usersPushService.toConnector(connector);
        try {
            ConnectorOrg<String, Object> rootExternalOrg = externalConnectService.getRootExternalOrg(conn);
            if (rootExternalOrg == null) {
                throw new UserCenterException(TransactionErrorType.PUSH_CONNECTOR_CONFIG_INVALID);
            }
            String rootCode = rootExternalOrg.getIdValue().toString();
            Map<String, Object> ret = new HashMap<>();
            ret.put("rootOrgCode", rootCode);
            ret.put("rootOrgSubCount", 0);
            return ret;
        } finally {
            externalConnectService.closeConnection(conn);
        }
    }
}
