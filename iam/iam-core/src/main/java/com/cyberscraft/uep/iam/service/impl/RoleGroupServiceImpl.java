package com.cyberscraft.uep.iam.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.common.util.CollectionUtils;
import com.cyberscraft.uep.iam.common.exception.TagException;
import com.cyberscraft.uep.iam.dao.RoleGroupDao;
import com.cyberscraft.uep.iam.dbo.ExternalRoleDBO;
import com.cyberscraft.uep.iam.dbo.RoleGroupDBO;
import com.cyberscraft.uep.iam.dbo.TagDBO;
import com.cyberscraft.uep.iam.dto.request.RoleGroupDto;
import com.cyberscraft.uep.iam.dto.request.TagGroupVO;
import com.cyberscraft.uep.iam.dto.response.RoleGroupVO;
import com.cyberscraft.uep.iam.entity.RoleGroupEntity;
import com.cyberscraft.uep.iam.entity.TagEntity;
import com.cyberscraft.uep.iam.errors.TransactionErrorType;
import com.cyberscraft.uep.iam.service.IDataSyncService;
import com.cyberscraft.uep.iam.service.IRoleGroupService;
import com.cyberscraft.uep.iam.service.ITagGroupService;
import com.cyberscraft.uep.iam.service.ITagService;
import com.cyberscraft.uep.iam.service.as.transfer.TagTransfer;
import com.cyberscraft.uep.iam.service.oidc.util.SecureRequestCheck;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/24 14:12
 */
@Service
public class RoleGroupServiceImpl extends ServiceImpl<RoleGroupDao, RoleGroupEntity> implements IRoleGroupService {


    @Autowired
    private TagTransfer tagTransfer;

    @Autowired
    private RoleGroupDBO roleGroupDBO;


    @Autowired
    private TagDBO tagDBO;

    @Autowired
    private ExternalRoleDBO externalRoleDBO;

    @Autowired
    private IDataSyncService dataSyncService;

    @Autowired
    private ITagService tagService;

    @Autowired
    private ITagGroupService tagGroupService;

    @Override
    public List<RoleGroupVO> getRoleGroupList() {
        List<RoleGroupEntity> roleGroupEntities = roleGroupDBO.listRoleGroup(null);
        return tagTransfer.groupEntities2RoleGroupVO(roleGroupEntities);
    }

    @Override
    public List<RoleGroupVO> getRoleGroupListByApp(Long appId) {
        List<RoleGroupEntity> roleGroupEntities = roleGroupDBO.listRoleGroup(appId);
        return tagTransfer.groupEntities2RoleGroupVO(roleGroupEntities);
    }

    @Override
    public Boolean save(RoleGroupDto roleGroupDto) {
        String appId = roleGroupDto.getAppId();
        checkRoleGroup(roleGroupDto);
        RoleGroupEntity roleGroupEntity = new RoleGroupEntity();
        roleGroupEntity.setName(roleGroupDto.getName());
        roleGroupEntity.setAppId(Long.valueOf(appId));

        roleGroupEntity.setCreateBy(SecureRequestCheck.getUsername());
        roleGroupEntity.setUpdateBy(SecureRequestCheck.getUsername());
        roleGroupEntity.setCreateTime(LocalDateTime.now());
        roleGroupEntity.setUpdateTime(LocalDateTime.now());

        boolean res = roleGroupDBO.saveRoleGroup(roleGroupEntity);
//        dataSyncService.pushDataToApp(roleGroupEntity.getId(), PushBusinessType.ROLE_GROUP, DataChangeType.CREATE);
        return res;
    }

    private void checkRoleGroup(RoleGroupDto roleGroupDto) {
        String appId = roleGroupDto.getAppId();
        if (StringUtils.isBlank(appId)) {
            throw new TagException(TransactionErrorType.UNKNOWN_ERROR);
        }
        TagGroupVO tagGroupById = tagGroupService.getTagGroupById(appId);
        if (tagGroupById == null) {
            throw new TagException(TransactionErrorType.ROLE_GROUP_NOT_FOUND_ERROR);
        }

        RoleGroupEntity roleGroupByName = roleGroupDBO.getRoleGroupByName(roleGroupDto.getName(), roleGroupDto.getAppId());
        if (roleGroupByName != null) {
            throw new TagException(TransactionErrorType.ROLE_GROUP_NAME_EXIST_ERROR);
        }

    }


    @Override
    public Boolean updateRoleGroupById(String id, RoleGroupDto roleGroupDto) {
        RoleGroupEntity roleGroup = roleGroupDBO.getRoleGroupById(id);
        if (roleGroup == null) {
            throw new TagException(TransactionErrorType.ROLE_GROUP_TAG_NOT_EXIST_ERROR);
        }
        roleGroup.setName(roleGroupDto.getName());
        roleGroup.setAppId(Long.valueOf(roleGroupDto.getAppId()));
        roleGroup.setUpdateTime(LocalDateTime.now());
        roleGroup.setUpdateBy(SecureRequestCheck.getUsername());
        Boolean res = roleGroupDBO.updateRoleGroupById(roleGroup);
//        dataSyncService.pushDataToApp(roleGroup.getId(), PushBusinessType.ROLE_GROUP, DataChangeType.UPDATE);
        return res;
    }

    @Override
    public RoleGroupVO deleteRoleGroupById(String id) {
        RoleGroupEntity roleGroup = roleGroupDBO.getRoleGroupById(id);
        if (roleGroup == null) {
            throw new TagException(TransactionErrorType.ROLE_GROUP_TAG_NOT_EXIST_ERROR);
        }
        String groupIds = JsonUtil.obj2Str(Arrays.asList(id));
        List<TagEntity> tags = tagService.getTagByGroupId(groupIds);
        if (CollectionUtils.isNotEmpty(tags)) {
            throw new TagException(TransactionErrorType.TAG_GROUP_INCLUDE_OTHER_TAG_ERROR);
        }

        roleGroupDBO.deleteRoleGroupById(id);

        RoleGroupVO roleGroupVO = tagTransfer.roleGroup2RoleGroupVo(roleGroup);
//        dataSyncService.pushDataToApp(roleGroup.getId(), PushBusinessType.ROLE_GROUP, DataChangeType.DELETE);
        return roleGroupVO;
    }

    @Override
    public void deleteRoleGroupUseLess(Long connectorId) {
        TagGroupVO tagGroupVO = tagGroupService.getTagGroupByCode(connectorId.toString());
        if (null == tagGroupVO) {
            return;
        }
        Long appId = tagGroupVO.getId();
        List<RoleGroupEntity> roleGroupEntityList = roleGroupDBO.listRoleGroup(appId);
        TagEntity query = new TagEntity();
        query.setCreateBy(tagGroupVO.getAppName());
        List<TagEntity> tagEntityList = tagDBO.getList(query);
        List<Long> groupIds = new ArrayList<>();
        for (TagEntity tagEntity : tagEntityList) {
            List<Long> Ids = JsonUtil.str2List(tagEntity.getRoleGroups(), Long.class);
            groupIds.addAll(Ids);
        }
        for (RoleGroupEntity roleGroupEntity : roleGroupEntityList) {
            if (!groupIds.contains(roleGroupEntity.getId())) {
                roleGroupDBO.deleteRoleGroupById(roleGroupEntity.getId().toString());
                externalRoleDBO.deleteByIamId(roleGroupEntity.getId());
            }
        }
    }

    @Override
    public List<RoleGroupEntity> getRoleGroupByIds(Set<Long> ids) {
        return roleGroupDBO.getRoleGroupByIds(ids);
    }
}
