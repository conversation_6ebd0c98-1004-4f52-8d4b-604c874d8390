package com.cyberscraft.uep.iam.service.data.impl;

import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountException;
import com.cyberscraft.uep.account.client.provider.dingding.domain.DingTalkConfig;
import com.cyberscraft.uep.account.client.provider.dingding.domain.ThirdPartyAudit;
import com.cyberscraft.uep.account.client.provider.dingding.service.IThirdPartyAuditService;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.iam.common.constants.ThreadPoolNameConstant;
import com.cyberscraft.uep.iam.common.exception.UserCenterException;
import com.cyberscraft.uep.iam.dbo.DingAuditSyncDBO;
import com.cyberscraft.uep.iam.dbo.DingAuditSyncLogDBO;
import com.cyberscraft.uep.iam.dto.enums.BooleanEnums;
import com.cyberscraft.uep.iam.dto.enums.ConnectorStatusEnum;
import com.cyberscraft.uep.iam.dto.enums.ConnectorTypeEnum;
import com.cyberscraft.uep.iam.dto.response.configs.SnsConfigListVO;
import com.cyberscraft.uep.iam.entity.DingAuditSyncEntity;
import com.cyberscraft.uep.iam.entity.DingAuditSyncLogEntity;
import com.cyberscraft.uep.iam.errors.TransactionErrorType;
import com.cyberscraft.uep.iam.service.IDingAuditSyncLogService;
import com.cyberscraft.uep.iam.service.config.ISnsConfigService;
import com.cyberscraft.uep.iam.service.data.IAuditPullService;
import com.cyberscraft.uep.iam.service.transfer.DingAuditTransfer;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

import static com.cyberscraft.uep.account.client.exception.ThirdPartyAccountErrorType.CONFIG_INVALID;

@Service
public class AuditPullServiceImpl implements IAuditPullService {

    private final static Logger LOG = LoggerFactory.getLogger(AuditPullServiceImpl.class);

    private static final Long PAGE_SIZE = 100L;

    private static final String FIRST_START_TIME = "2015-01-01 00:00:00";

    /**
     * 同步审计日志的redis锁，占位符是租户ID，配置类型
     */
    private static final String SYNC_DING_AUDIT_LOCK = "IAM:SYNC_DING_AUDIT_LOCK:%s";

    /**
     * 锁的等待时间，单位：毫秒
     */
    private static final Long SYNC_DING_AUDIT_LOCK_WAIT_TIME = 5000L;
    /**
     * 锁的超时时间,单位：毫秒
     */
    private static final Long SYNC_DING_AUDIT_LOCK_TIME = 3600000L;

    @Resource
    private RedissonClient redissonClient;

    @Resource(name = ThreadPoolNameConstant.AUDIT_POOL_NAME)
    private ExecutorService executorService;

    @Autowired
    private IThirdPartyAuditService thirdPartyAuditService;

    @Autowired
    private DingAuditSyncDBO dingAuditSyncDBO;

    @Autowired
    private IDingAuditSyncLogService dingAuditSyncLogService;

    @Autowired
    private DingAuditTransfer dingAuditTransfer;

    @Resource
    protected ISnsConfigService snsConfigService;

    @Override
    public void auditPull(String tenantCode, LocalDateTime startTime) {
        LOG.info("ding ding audit log sync start, tenant:{}", tenantCode);

        String lockKey = String.format(SYNC_DING_AUDIT_LOCK, tenantCode);
        RLock lock = redissonClient.getLock(lockKey);
        try {
            if (lock.tryLock(SYNC_DING_AUDIT_LOCK_WAIT_TIME, SYNC_DING_AUDIT_LOCK_TIME, TimeUnit.MILLISECONDS)) {
                try {
                    // 将从钉钉那边同步过来的信息转换为iam 数据库的对象 并保存到数据库
                    TenantHolder.setTenantCode(tenantCode);

                    List<SnsConfigListVO> allSnsConfigsByType = snsConfigService.getAllSnsConfigsByType(ConnectorTypeEnum.DINGDING);
                    for (SnsConfigListVO snsConfig : allSnsConfigsByType) {
                        if (ConnectorStatusEnum.ACTIVE.getValue() == snsConfig.getStatus()) {
                            if (StringUtils.isBlank(snsConfig.getConfig())) {
                                continue;
                            }

                            LOG.info("ding ding audit log sync start, tenant:{}, configId:{}", tenantCode, snsConfig.getId());
                            syncDingAudit(snsConfig, tenantCode, startTime);
                            LOG.info("ding ding audit log sync end, tenant:{}, configId:{}", tenantCode, snsConfig.getId());
                        }
                    }
                } finally {
                    // 释放锁
                    lock.unlock();
                }
            }
            LOG.info("ding ding audit log sync end, tenant:{}", tenantCode);
        } catch (Exception e) {
            LOG.error("sync ding audit info has error", e);
        }
    }

    private void syncDingAudit(SnsConfigListVO snsConfig, String tenantCode, LocalDateTime startTime) {

        // 从钉钉同步过来的审计日志信息
        try {
            DingTalkConfig cfg = JsonUtil.str2Obj(snsConfig.getConfig(), DingTalkConfig.class);
            DingAuditSyncLogEntity dingAuditSyncLogEntity = dingAuditSyncLogService.getDingAuditSyncLogEntity(Long.valueOf(snsConfig.getId()));

            List<ThirdPartyAudit> dingAuditList;
            long startDate = startTime.toInstant(ZoneOffset.of("+8")).toEpochMilli();
            long endDate = System.currentTimeMillis();
            Long loadMoreGmtCreate = dingAuditSyncLogEntity.getGmtCreate();
            Long loadMoreBizId = (dingAuditSyncLogEntity.getBizId() != null ? Long.valueOf(dingAuditSyncLogEntity.getBizId()) : null);

            while (true) {
//                 * 操作记录文件id，作为分页偏移量，与load_more_gmt_create一起使用，
//                 * 返回记录的biz_id为load_more_biz_id且gmt_create为load_more_gmt_create之后的操作列表
//                 * 分页查询获取下一页时，传最后一条记录的biz_id和gmt_create。
                dingAuditList = thirdPartyAuditService.getAudits(startDate, endDate, PAGE_SIZE, loadMoreGmtCreate, loadMoreBizId, cfg, tenantCode);
                List<DingAuditSyncEntity> dingAuditSyncEntities = dingAuditTransfer.thirdPartyAudit2DingAuditSyncVO(dingAuditList);
                if (!dingAuditSyncEntities.isEmpty()) {
                    dingAuditSyncDBO.saveBatch(dingAuditSyncEntities);

                    loadMoreGmtCreate = dingAuditSyncEntities.get(dingAuditSyncEntities.size() - 1).getGmtCreate();
                    loadMoreBizId = Long.valueOf(dingAuditSyncEntities.get(dingAuditSyncEntities.size() - 1).getBizId());

                    dingAuditSyncLogEntity.setBizId(String.valueOf(loadMoreBizId));
                    dingAuditSyncLogEntity.setGmtCreate(loadMoreGmtCreate);
                }

                dingAuditSyncLogEntity.setUpdateTime(LocalDateTime.now());

                dingAuditSyncLogService.saveOrUpdateDingAuditSyncLog(dingAuditSyncLogEntity);

                if (dingAuditList.size() < PAGE_SIZE) {
                    break;
                }

                Thread.sleep(200L);
            }
        } catch (Exception e) {
            LOG.error("ding ding audit log sync fail", e);
        }
    }
}
