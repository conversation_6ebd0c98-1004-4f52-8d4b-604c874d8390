package com.cyberscraft.uep.iam.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * IAM-组织结构表
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2019-07-04
 */
@TableName("iam_org")
public class OrgEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 组ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 父ID
     */
    private Long parentRefId;

    /**
     * 组名称
     */
    private String name;

    /**
     * 组描述
     */
    private String description;

    /**
     * 是否只读
     */
    private Integer readonly;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 该用户被创建的模式 = ['BY_ADMIN', 'BY_SELF', 'BY_IMPORT_CSV', 'BY_IMPORT_LDAP']stringEnum:"BY_ADMIN", "BY_SELF", "BY_IMPORT_CSV", "BY_IMPORT_LDAP"
     */
    private Integer createdMode;

    /**
     * 组织PATH，001,001001,001002
     */
    private String orgPath;

    /**
     * 同级别内排序
     */
    private Integer seq;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 数据来源ID，同步或导入时使用
     */
    private Long connectorId;


    @TableField(exist = false)
    private Long uid;

    /**
     * 是否主部门
     */
    @TableField(exist = false)
    private Integer isMain;

    /**
     * 部门职位
     */
    @TableField(exist = false)
    private String position;


    /**
     * 部门工号
     */
    @TableField(exist = false)
    private String userCode;

    /**
     * org信息的checksum，用于做org信息是否发生变化的判断
     */
    private String checksum;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 部门主管
     */
    private String manager;

    /**
     * 组织扩展属性
     */
    private String orgExtension;

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getChecksum() {
        return checksum;
    }

    public void setChecksum(String checksum) {
        this.checksum = checksum;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getParentRefId() {
        return parentRefId;
    }

    public void setParentRefId(Long parentRefId) {
        this.parentRefId = parentRefId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getReadonly() {
        return readonly;
    }

    public void setReadonly(Integer readonly) {
        this.readonly = readonly;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getCreatedMode() {
        return createdMode;
    }

    public void setCreatedMode(Integer createdMode) {
        this.createdMode = createdMode;
    }

    public String getOrgPath() {
        return orgPath;
    }

    public void setOrgPath(String orgPath) {
        this.orgPath = orgPath;
    }

    public Integer getSeq() {
        return seq;
    }

    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Long getConnectorId() {
        return connectorId;
    }

    public void setConnectorId(Long connectorId) {
        this.connectorId = connectorId;
    }

    public String getManager() {
        return manager;
    }

    public void setManager(String manager) {
        this.manager = manager;
    }

    public Long getUid() {
        return uid;
    }

    public OrgEntity setUid(Long uid) {
        this.uid = uid;
        return this;
    }

    public String getOrgExtension() {
        return orgExtension;
    }

    public void setOrgExtension(String orgExtension) {
        this.orgExtension = orgExtension;
    }

    public Integer getIsMain() {
        return isMain;
    }

    public void setIsMain(Integer isMain) {
        this.isMain = isMain;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    @Override
    public String toString() {
        return "OrgEntity{" +
                "id=" + id +
                ", parentRefId=" + parentRefId +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", readonly=" + readonly +
                ", status=" + status +
                ", createdMode=" + createdMode +
                ", orgPath='" + orgPath + '\'' +
                ", seq=" + seq +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", createBy='" + createBy + '\'' +
                ", updateBy='" + updateBy + '\'' +
                ", connectorId=" + connectorId +
                ", checksum='" + checksum + '\'' +
                ", tenantId='" + tenantId + '\'' +
                ", manager='" + manager + '\'' +
                ", orgExtension='" + orgExtension + '\'' +
                '}';
    }
}
