package com.cyberscraft.uep.iam.dbo;

import com.cyberscraft.uep.iam.entity.ConfigEntity;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * IAM-系统配置表 服务类
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2019-07-08
 */
public interface ConfigDBO extends IService<ConfigEntity> {
    /**
     * 根据key，取value对象
     * @param key
     * @return
     */
    ConfigEntity getValue(String key);

    /**
     * 根据key，取value值
     * @param key
     * @return
     */
    String getKeyValue(String key);

    /**
     * 根据key和type,取value
     * @param key
     * @param type
     * @return
     */
    ConfigEntity getValue(String key,Integer type);

    void saveOrUpdate(String key,String value,int type);

    ConfigEntity buildConfigData(String key,String value,int type);

    List<ConfigEntity> getList(int type);
}
