package com.cyberscraft.uep.iam.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.cyberscraft.uep.iam.common.constants.DaoConstants;
import com.cyberscraft.uep.iam.dto.enums.SsoProtocol;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * IAM-接入应用信息表
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2020-7-1
 */
@TableName("iam_app")
public class AppEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 应用的唯一ID
     */
    private String clientId;

    /**
     * 应用的中文名称
     */
    private String clientName;

    /**
     * 应用类型，1：Native App，2：Trusted App，3：SPA，4：Web App，5：CLI App
     */
    private Integer applicationType;

    /**
     * 应用简介
     */
    private String description;

    /**
     * 是否允许自助申请：1、允许；0、不允许
     */
    private Integer enableApply;

    /**
     * 应用图标
     */
    private String logoUri;

    /**
     * 应用主页
     */
    private String clientUri;

    /**
     * 应用的政策链接
     */
    private String policyUri;

    /**
     * 应用的服务条款链接
     */
    private String tosUri;

    /**
     * 应用管理员的联系方式，如果有多个，以逗号分开
     */
    private String contacts;

    /**
     * 授权的重定向URI，如果有多个，以逗号分开
     */
    private String redirectUris;

    /**
     * Access Token有效期，单位：秒
     */
    private Integer accessTokenTimeout;

    /**
     * Refresh Token有效期，单位：秒
     */
    private Integer refreshTokenTimeout;

    /**
     * ID Token有效期，单位：秒
     */
    private Integer idTokenTimeout;

    /**
     * 应用是否在白名单中，是否启用免授权提示
     */
    private Integer whitelisted;

    /**
     * 是否启用用户信息变动通知
     */
    private Integer webhookEnable;

    /**
     * 用户信息变动通知的Web Hook URI
     */
    private String webhook;

    /**
     * 重定向URI是否必须为HTTPS，默认为是，当应用类型为web和spa时有效
     */
    private Integer enforceHttps;

    /**
     * 代理授权受信应用ID，多个应用id以逗号分开
     */
    private String trustedPeers;

    /**
     * 登录时验证用户属性，多个属性之间以逗号分开
     */
    private String validateFactors;

    /**
     * 是否开启机器帐号，0：否，1：是
     */
    private Integer cliModeEnable;

    /**
     * 应用状态:0-无效 1-有效
     */
    private Integer status;

    /**
     * 是不是精选应用，0：否，1：是
     */
    private Integer featureApp;

    /**
     * 应用的密码
     */
    private String clientSecret;

    /**
     * 应用的密码的过期时间,单位：秒
     */
    private Integer clientSecretExpiresAt;

    /**
     * 验证签名的公共密钥(JWK格式)
     */
    private String publicKey;

    /**
     * 是否该应用可公开访问，不需要进行授权
     */
    private Integer publicAccess;

    /**
     * 验证签名的证书(PEM格式)
     */
    @TableField("certPem")
    private String certPem;

    /**
     * 签名证书指纹信息
     */
    private String certThumbprint;

    /**
     * 验证签名的公共密钥(PEM格式)
     */
    private String publicKeyPem;

    /**
     * 签名算法, none表示不签名，RS256非对称，HS对称
     */
    private String signingAlg;

    /**
     * 对称秘钥base64以后的值
     */
    private String signatureSecret;

    /**
     * 允许扫码登录,0：否，1：是
     */
    private Integer qrcodeEnable;

    /**
     * 扫码登录受信应用Id，多个应用id以逗号分开
     */
    private String trustedScanners;

    /**
     * 应用的英文名称
     */
    private String clientNameEn;

    /**
     * 用户ID:默认为0
     */
    private String uid;

    /**
     * 用户类型，1：platform-平台，2：isp-服务提供商 ，3：dev-自研开发者
     */
    private Integer userType;



    /**
     * 移动应用操作系统，1：ios ，2：android
     */
    private Integer appOs;

    /**
     * 创建时间
     */
    private LocalDateTime clientIdIssuedAt;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 验证签名的私有密钥(JWK格式)
     */
    private String privateKey;

    /**
     * 授权类型
     */
    private String grantType;

    /**
     * scope
     */
    private String scope;

    /**
     * 单点登录认证类型
     */
    private String authProtocol;

    /**
     * 配置信息
     */
    private String config;

    /**
     * 应用配置模板
     */
    private String templateConfig;

    /**
     * 关联应用市场clientId
     */
    private String linkClientId;

    /**
     * 应用预览
     */
    private String previewUri;


    /**
     * 配置指引url
     */
    private String helpUrl;

    /**
     * 应用内部分类
     */
    private String innerCategory;

    /**
     * 支持的sso协议
     */
    private String supportProtocol;

    /**
     * 应用所属的租户Id
     */
    private String tenantOwner;

    /**
     * 是否该应用对租户可公开访问，不需要进行授权，仅当tenantOwner为iam时有意义
     */
    private Integer publicToTenant;

    /**
     * 开放平台open app id
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Long openAppAuthId;

    /**
     * 网关启用状态:0-未启用 1-已启用
     */
    private Integer gateway;

    /**
     * 应用打开方式：CURRENT_PAGE、NEW_WINDOW、CUSTOM
     */
    private String openMethod;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public Integer getApplicationType() {
        return applicationType;
    }

    public void setApplicationType(Integer applicationType) {
        this.applicationType = applicationType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getEnableApply() {
        return enableApply;
    }

    public void setEnableApply(Integer enableApply) {
        this.enableApply = enableApply;
    }

    public String getLogoUri() {
        return logoUri;
    }

    public void setLogoUri(String logoUri) {
        this.logoUri = logoUri;
    }

    public String getClientUri() {
        return clientUri;
    }

    public void setClientUri(String clientUri) {
        this.clientUri = clientUri;
    }

    public String getPolicyUri() {
        return policyUri;
    }

    public void setPolicyUri(String policyUri) {
        this.policyUri = policyUri;
    }

    public String getHelpUrl() {
        return helpUrl;
    }

    public void setHelpUrl(String helpUrl) {
        this.helpUrl = helpUrl;
    }

    public String getTosUri() {
        return tosUri;
    }

    public void setTosUri(String tosUri) {
        this.tosUri = tosUri;
    }

    public String getContacts() {
        return contacts;
    }

    public void setContacts(String contacts) {
        this.contacts = contacts;
    }

    public String getRedirectUris() {
        return redirectUris;
    }

    public void setRedirectUris(String redirectUris) {
        this.redirectUris = redirectUris;
    }

    public Integer getAccessTokenTimeout() {
        return accessTokenTimeout;
    }

    public void setAccessTokenTimeout(Integer accessTokenTimeout) {
        this.accessTokenTimeout = accessTokenTimeout;
    }

    public Integer getRefreshTokenTimeout() {
        return refreshTokenTimeout;
    }

    public void setRefreshTokenTimeout(Integer refreshTokenTimeout) {
        this.refreshTokenTimeout = refreshTokenTimeout;
    }

    public Integer getIdTokenTimeout() {
        return idTokenTimeout;
    }

    public void setIdTokenTimeout(Integer idTokenTimeout) {
        this.idTokenTimeout = idTokenTimeout;
    }

    public Integer getWhitelisted() {
        return whitelisted;
    }

    public void setWhitelisted(Integer whitelisted) {
        this.whitelisted = whitelisted;
    }

    public Integer getWebhookEnable() {
        return webhookEnable;
    }

    public void setWebhookEnable(Integer webhookEnable) {
        this.webhookEnable = webhookEnable;
    }

    public String getWebhook() {
        return webhook;
    }

    public void setWebhook(String webhook) {
        this.webhook = webhook;
    }

    public Integer getEnforceHttps() {
        return enforceHttps;
    }

    public void setEnforceHttps(Integer enforceHttps) {
        this.enforceHttps = enforceHttps;
    }

    public String getTrustedPeers() {
        return trustedPeers;
    }

    public void setTrustedPeers(String trustedPeers) {
        this.trustedPeers = trustedPeers;
    }

    public String getValidateFactors() {
        return validateFactors;
    }

    public void setValidateFactors(String validateFactors) {
        this.validateFactors = validateFactors;
    }

    public Integer getCliModeEnable() {
        return cliModeEnable;
    }

    public void setCliModeEnable(Integer cliModeEnable) {
        this.cliModeEnable = cliModeEnable;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getFeatureApp() {
        return featureApp;
    }

    public void setFeatureApp(Integer featureApp) {
        this.featureApp = featureApp;
    }

    public String getClientSecret() {
        return clientSecret;
    }

    public void setClientSecret(String clientSecret) {
        this.clientSecret = clientSecret;
    }

    public Integer getClientSecretExpiresAt() {
        return clientSecretExpiresAt;
    }

    public void setClientSecretExpiresAt(Integer clientSecretExpiresAt) {
        this.clientSecretExpiresAt = clientSecretExpiresAt;
    }

    public String getPublicKey() {
        return publicKey;
    }

    public void setPublicKey(String publicKey) {
        this.publicKey = publicKey;
    }

    public Integer getPublicAccess() {
        return publicAccess;
    }

    public void setPublicAccess(Integer publicAccess) {
        this.publicAccess = publicAccess;
    }

    public String getCertPem() {
        return certPem;
    }

    public void setCertPem(String certPem) {
        this.certPem = certPem;
    }

    public String getCertThumbprint() {
        return certThumbprint;
    }

    public void setCertThumbprint(String certThumbprint) {
        this.certThumbprint = certThumbprint;
    }

    public String getPublicKeyPem() {
        return publicKeyPem;
    }

    public void setPublicKeyPem(String publicKeyPem) {
        this.publicKeyPem = publicKeyPem;
    }

    public Integer getQrcodeEnable() {
        return qrcodeEnable;
    }

    public void setQrcodeEnable(Integer qrcodeEnable) {
        this.qrcodeEnable = qrcodeEnable;
    }

    public String getSigningAlg() {
        return signingAlg;
    }

    public void setSigningAlg(String signingAlg) {
        this.signingAlg = signingAlg;
    }

    public String getSignatureSecret() {
        return signatureSecret;
    }

    public void setSignatureSecret(String signatureSecret) {
        this.signatureSecret = signatureSecret;
    }

    public String getTrustedScanners() {
        return trustedScanners;
    }

    public void setTrustedScanners(String trustedScanners) {
        this.trustedScanners = trustedScanners;
    }

    public String getClientNameEn() {
        return clientNameEn;
    }

    public void setClientNameEn(String clientNameEn) {
        this.clientNameEn = clientNameEn;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Integer getUserType() {
        return userType;
    }

    public void setUserType(Integer userType) {
        this.userType = userType;
    }

    public Integer getAppOs() {
        return appOs;
    }

    public void setAppOs(Integer appOs) {
        this.appOs = appOs;
    }

    public LocalDateTime getClientIdIssuedAt() {
        return clientIdIssuedAt;
    }

    public void setClientIdIssuedAt(LocalDateTime clientIdIssuedAt) {
        this.clientIdIssuedAt = clientIdIssuedAt;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public String getPrivateKey() {
        return privateKey;
    }

    public void setPrivateKey(String privateKey) {
        this.privateKey = privateKey;
    }

    public String getGrantType() {
        return grantType;
    }

    public void setGrantType(String grantType) {
        this.grantType = grantType;
    }

    public String getScope() {
        return scope;
    }

    public void setScope(String scope) {
        this.scope = scope;
    }

    public String getTenantOwner() {
        return tenantOwner;
    }

    public void setTenantOwner(String tenantOwner) {
        this.tenantOwner = tenantOwner;
    }

    public Integer getPublicToTenant() {
        return publicToTenant;
    }

    public void setPublicToTenant(Integer publicToTenant) {
        this.publicToTenant = publicToTenant;
    }

    public Long getOpenAppAuthId() {
        return openAppAuthId;
    }

    public void setOpenAppAuthId(Long openAppAuthId) {
        this.openAppAuthId = openAppAuthId;
    }

    public String getAuthProtocol() {
        return authProtocol;
    }

    public void setAuthProtocol(String authProtocol) {
        this.authProtocol = authProtocol;
    }

    public String getConfig() {
        return config;
    }

    public void setConfig(String config) {
        this.config = config;
    }

    public String getTemplateConfig() {
        return templateConfig;
    }

    public void setTemplateConfig(String templateConfig) {
        this.templateConfig = templateConfig;
    }

    public String getLinkClientId() {
        return linkClientId;
    }

    public void setLinkClientId(String linkClientId) {
        this.linkClientId = linkClientId;
    }

    public String getPreviewUri() {
        return previewUri;
    }

    public void setPreviewUri(String previewUri) {
        this.previewUri = previewUri;
    }

    public String getInnerCategory() {
        return innerCategory;
    }

    public void setInnerCategory(String innerCategory) {
        this.innerCategory = innerCategory;
    }

    public String getSupportProtocol() {
        return supportProtocol;
    }

    public void setSupportProtocol(String supportProtocol) {
        this.supportProtocol = supportProtocol;
    }

    public Integer getGateway() {
        return gateway;
    }

    public void setGateway(Integer gateway) {
        this.gateway = gateway;
    }

    public String getOpenMethod() {
        return openMethod;
    }

    public void setOpenMethod(String openMethod) {
        this.openMethod = openMethod;
    }

    @Override
    public String toString() {
        return "AppEntity{" +
                "id=" + id +
                ", clientId='" + clientId + '\'' +
                ", clientName='" + clientName + '\'' +
                ", applicationType=" + applicationType +
                ", description='" + description + '\'' +
                ", logoUri='" + logoUri + '\'' +
                ", clientUri='" + clientUri + '\'' +
                ", policyUri='" + policyUri + '\'' +
                ", tosUri='" + tosUri + '\'' +
                ", contacts='" + contacts + '\'' +
                ", redirectUris='" + redirectUris + '\'' +
                ", accessTokenTimeout=" + accessTokenTimeout +
                ", refreshTokenTimeout=" + refreshTokenTimeout +
                ", idTokenTimeout=" + idTokenTimeout +
                ", whitelisted=" + whitelisted +
                ", webhookEnable=" + webhookEnable +
                ", webhook='" + webhook + '\'' +
                ", enforceHttps=" + enforceHttps +
                ", trustedPeers='" + trustedPeers + '\'' +
                ", validateFactors='" + validateFactors + '\'' +
                ", cliModeEnable=" + cliModeEnable +
                ", status=" + status +
                ", featureApp=" + featureApp +
                ", clientSecret='" + clientSecret + '\'' +
                ", clientSecretExpiresAt=" + clientSecretExpiresAt +
                ", publicKey='" + publicKey + '\'' +
                ", publicAccess=" + publicAccess +
                ", certPem='" + certPem + '\'' +
                ", certThumbprint='" + certThumbprint + '\'' +
                ", publicKeyPem='" + publicKeyPem + '\'' +
                ", signingAlg='" + signingAlg + '\'' +
                ", signatureSecret='" + signatureSecret + '\'' +
                ", qrcodeEnable=" + qrcodeEnable +
                ", trustedScanners='" + trustedScanners + '\'' +
                ", clientNameEn='" + clientNameEn + '\'' +
                ", uid='" + uid + '\'' +
                ", userType=" + userType +
                ", appOs=" + appOs +
                ", clientIdIssuedAt=" + clientIdIssuedAt +
                ", updateTime=" + updateTime +
                ", createBy='" + createBy + '\'' +
                ", updateBy='" + updateBy + '\'' +
                ", privateKey='" + privateKey + '\'' +
                ", grantType='" + grantType + '\'' +
                ", scope='" + scope + '\'' +
                ", authProtocol='" + authProtocol + '\'' +
                ", config='" + config + '\'' +
                ", templateConfig='" + templateConfig + '\'' +
                ", tenantOwner='" + tenantOwner + '\'' +
                ", publicToTenant=" + publicToTenant +
                ", openAppAuthId=" + openAppAuthId +
                ", openMethod=" + openMethod +
                '}';
    }

    @JsonIgnore
    public boolean isAppStoreTemplate() {
        return SsoProtocol.APPSTORE.name().equalsIgnoreCase(this.getAuthProtocol()) && DaoConstants.IAM_TENANT_ID.equals(this.getTenantOwner());
    }
}
