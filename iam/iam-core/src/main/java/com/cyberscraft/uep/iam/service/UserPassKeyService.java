package com.cyberscraft.uep.iam.service;


import com.cyberscraft.uep.iam.dto.request.UserPassKeyDto;
import com.cyberscraft.uep.iam.entity.UserPassKeyEntity;

import java.util.List;

public interface UserPassKeyService {

    /**
     * 创建用户生物识别信息
     *
     * @param userPassKey 用户生物识别信息
     * @return
     */
    Boolean createUserPassKey(UserPassKeyEntity userPassKey);

    /**
     * 获取用户生物识别信息
     *
     * @param username 用户名
     * @return
     */
    List<UserPassKeyEntity> getUserPassKeyByUsername(String username);

    /**
     * 获取用户生物识别信息
     *
     * @param id 用户生物识别信息id
     * @return
     */
    UserPassKeyEntity getUserPassKeyById(String id);

    /**
     * 根据算法查询用户生物识别信息
     *
     * @param base64
     * @return
     */
    List<UserPassKeyEntity> selectByAlgorithm(String base64);

    /**
     * 根据credentialId和算法查询用户生物识别信息
     *
     * @param credentialIdBase64
     * @param userAlgorithmBase64
     * @return
     */
    UserPassKeyEntity selectOneByCredentialIdAndAlgorithm(String credentialIdBase64, String userAlgorithmBase64);

    /**
     * 根据credentialId查询用户生物识别信息
     *
     * @param credentialId
     * @return
     */
    List<UserPassKeyEntity> selectByCredentialId(String credentialId);

    /**
     * 删除用户生物识别信息
     *
     * @param id 用户生物识别信息id
     */
    Boolean deleteUserPassKey(String id);

    /**
     * 更新用户生物识别信息
     *
     * @param userPassKeyDto 用户生物识别信息
     */
    Boolean updateUserPassKey(String id, UserPassKeyDto userPassKeyDto);


    /**
     * 更新用户生物识别信息最后使用时间
     *
     * @param id
     * @return
     */
    Boolean updateUserPassKeyLastUseTime(Long id);

    /**
     * 根据passKey查询用户生物识别信息
     *
     * @param publicKey 生物识别信息
     * @return
     */
    UserPassKeyEntity queryUserPassKeyByPassKey(String publicKey);

}
