package com.cyberscraft.uep.iam.service.data.impl;

import com.cyberscraft.uep.starter.dds.TenantHolder;
import com.cyberscraft.uep.common.dto.QueryPage;
import com.cyberscraft.uep.iam.entity.AppEntity;
import com.cyberscraft.uep.iam.dbo.*;
import com.cyberscraft.uep.iam.dto.enums.PushBusinessType;
import com.cyberscraft.uep.iam.common.constants.PushDataConstant;
import com.cyberscraft.uep.iam.service.config.PushProperties;
import com.cyberscraft.uep.iam.service.data.IDataPullService;
import com.cyberscraft.uep.iam.service.data.IDataPushAllService;
import com.cyberscraft.uep.iam.service.data.IDataPushService;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *     全量推送
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2019-08-22 16:00
 */
@Service
public class DataPushAllServiceImpl implements IDataPushAllService {
    private static final Logger LOGGER = LoggerFactory.getLogger(DataPushAllServiceImpl.class);
    @Autowired
    private OrgDBO orgDBO;
    @Autowired
    private AppDBO appDBO;

    @Autowired
    private IDataPullService dataPullService;
    @Autowired
    private IDataPushService dataPushService;

    @Autowired
    private PushProperties pushProperties;



    @Override
    public void pushAllDataToApp(PushBusinessType pushBusinessType, List<String> clientIdList) {
        if(CollectionUtils.isEmpty(clientIdList) || pushBusinessType == null){
            return;
        }

        List<Map<String,Object>> dataMapList;
        Map<String,AppEntity> appCache = new HashMap<>();

        QueryPage<Map<String,Object>> queryPage;
        for(int i = 1 ;i < Integer.MAX_VALUE;i++){
            queryPage = new QueryPage<>() ;
            if(pushBusinessType == PushBusinessType.USER){
                String userFieldArr [] = pushProperties.getUserIncludeField().split(";");
                queryPage = this.dataPullService.pullUsers(i,PushDataConstant.ALL_PUSH_BATCH_SIZE,userFieldArr);

            }else if(pushBusinessType == PushBusinessType.ORG){
                String orgFieldArr [] = pushProperties.getOrgIncludeField().split(";");
                queryPage.setPage(i);
                queryPage.setSize(PushDataConstant.ALL_PUSH_BATCH_SIZE);
                queryPage = this.orgDBO.page(null,null,queryPage,orgFieldArr);
            }

            if(queryPage == null){
                return;
            }
            dataMapList = queryPage.getItems();
            if(CollectionUtils.isEmpty(dataMapList)){
                return;
            }
            LOGGER.info("push all data ,current page:{},data:{}",i,dataMapList.toString());

            for (String clientId : clientIdList) {
                AppEntity appEntity ;
                if(appCache != null && appCache.get(clientId) != null){
                    appEntity = appCache.get(clientId);
                }else{
                    appEntity = appDBO.getAppByClientId(clientId);
                    if(appEntity == null){
                        continue;
                    }
                    appCache.put(clientId,appEntity);
                }

                dataPushService.directPushToApp(pushBusinessType,dataMapList,appEntity.getId(),appEntity.getWebhook(), TenantHolder.getTenantCode());
            }
        }
    }
}
