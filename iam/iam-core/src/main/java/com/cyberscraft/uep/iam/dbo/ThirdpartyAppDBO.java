package com.cyberscraft.uep.iam.dbo;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cyberscraft.uep.iam.entity.ThirdpartyAppEntity;

import java.util.List;

/**
 * <p>
 * 导入外部应用 服务类
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2020-07-01
 */
public interface ThirdpartyAppDBO extends IService<ThirdpartyAppEntity> {
    List<ThirdpartyAppEntity> getThirdPartyByAgentIds(List<String> agentIds);

    ThirdpartyAppEntity getByClientId(String clientId);

    void removeByClientId(String clientId);
}
