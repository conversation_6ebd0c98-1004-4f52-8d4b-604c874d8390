package com.cyberscraft.uep.iam.dbo;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cyberscraft.uep.common.dto.PageView;
import com.cyberscraft.uep.common.dto.Pagination;
import com.cyberscraft.uep.iam.entity.LoginPolicyEntity;
import com.cyberscraft.uep.iam.query.LoginPolicyQueryDto;

/**
 * <p>
 * IAM-标签表 服务类
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2019-07-08
 */
public interface LoginPolicyDBO extends IService<LoginPolicyEntity> {
    LoginPolicyEntity getByName(String name);

    LoginPolicyEntity getById(Long policyId);

    LoginPolicyEntity getDefault(String... columns);

    PageView<LoginPolicyEntity> page(Pagination<LoginPolicyQueryDto> page);

    void evictCache(String policyId);
}
