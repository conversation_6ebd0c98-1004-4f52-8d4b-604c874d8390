package com.cyberscraft.uep.iam.service.impl;

import com.cyberscraft.uep.iam.common.exception.UserCenterException;
import com.cyberscraft.uep.iam.constants.CommonConstants;
import com.cyberscraft.uep.iam.dbo.AppPositionDBO;
import com.cyberscraft.uep.iam.dbo.UserOrgDBO;
import com.cyberscraft.uep.iam.dto.request.PositionDTO;
import com.cyberscraft.uep.iam.dto.response.UserOrgInfoVO;
import com.cyberscraft.uep.iam.entity.AppPositionEntity;
import com.cyberscraft.uep.iam.service.user.IOrganizationService;
import com.cyberscraft.uep.iam.service.IPositionService;
import com.cyberscraft.uep.iam.service.oidc.util.SecureRequestCheck;
import com.cyberscraft.uep.iam.service.transfer.TypeMapper;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.cyberscraft.uep.iam.errors.TransactionErrorType.INVALID_REQUEST_PARAM;


/**
 * <p>职位信息服务实现类</p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2020-07-07
 */
@Service
public class PositionServiceImpl implements IPositionService {
    private static Logger logger = LoggerFactory.getLogger(PositionServiceImpl.class);

    @Autowired
    UserOrgDBO userOrgDBO;

    @Autowired
    IOrganizationService iOrganizationService;

    @Autowired
    AppPositionDBO appPositionDBO;


    /**
     * 检查用户所在的职位，是否允许使用该app
     *
     * @param clientId
     * @param sub
     * @return
     */
    @Override
    public boolean checkAppAllowedUserPositions(String clientId, String sub) {
        Long userId = Long.parseLong(sub);
        List<UserOrgInfoVO> userOrgInfos = iOrganizationService.getUserOrgInfo(userId);
        Set<String> appAllowedUserPositions = appAllowedUserPositions(clientId);
        /**
         * app对职位无要求
         */
        if (appAllowedUserPositions == null){
            logger.info("{} no restriction on postion, user {} access approved", clientId, userId);
            return true;
        }

        /**
         * app对职位有要求，但是用户无职位信息
         */
        if (userOrgInfos == null || userOrgInfos.size() == 0){
            logger.info("client {} require positions {}, user {} no position, access denied ",
                    clientId, appAllowedUserPositions, userId);
            return false;
        }

        for (UserOrgInfoVO userOrgInfoVO: userOrgInfos){
            /**
             * 用户所在职位包含在app所允许的职位列表中，允许访问
             */
            if (appAllowedUserPositions.contains(userOrgInfoVO.getPosition())){
                logger.info("client {} require positions {}, user {} already has {}, access approved ",
                        clientId, appAllowedUserPositions, userId, userOrgInfoVO.getPosition());
                return true;
            }
        }
        Set<String> userPositions = userOrgInfos.stream().map(e -> e.getPosition()).collect(Collectors.toSet());
        logger.info("client {} require positions {}, user {} has {}, access denied ",
                clientId, appAllowedUserPositions, userId, userPositions);
        return false;
    }

    @Override
    public Set<String> appAllowedUserPositions(String clientId) {
        AppPositionEntity appPositionEntity = appPositionDBO.getByClientId(clientId);
        if (appPositionEntity == null){
            return null;
        }else{
            return string2set(appPositionEntity.getPositions());
        }
    }

    private Set<String> string2set(String validFactorsString){
        if (validFactorsString == null){
            return null;
        }
        if(StringUtils.isBlank(validFactorsString)) {
            return new HashSet<>();
        }
        String[] validFactorsArray = validFactorsString.split(CommonConstants.COMMA);
        Set<String> factorsList;
        if (validFactorsArray.length == 0) {
            return new HashSet<>();
        }
        factorsList = new HashSet<>();
        Stream.of(validFactorsArray).forEach(factor -> {
            if(!Strings.isNullOrEmpty(factor)){
                factorsList.add(factor);
            }
        });
        return factorsList;
    }

    @Override
    public void updateAppAllowedUserPositions(String clientId, PositionDTO positionDTO) {
        /**
         * 空值表示不作任何限制，此时删除已有的限制记录
         */
        AppPositionEntity appPositionEntity = appPositionDBO.getByClientId(clientId);
        if (positionDTO.getPositions() == null){
            if (appPositionEntity != null){
                appPositionDBO.removeById(appPositionEntity.getId());
            }
            return;
        }
        Set<String> allowedPositions = userOrgDBO.listAllPositions();
        if (!allowedPositions.containsAll(positionDTO.getPositions())){
            String errMsg = String.format("invalid values, allowed values are %s", allowedPositions);
            logger.error(errMsg);
            throw new UserCenterException(HttpStatus.BAD_REQUEST, INVALID_REQUEST_PARAM, errMsg);
        }
        if (appPositionEntity == null){
            appPositionEntity = new AppPositionEntity();
            appPositionEntity.setClientId(clientId);

            appPositionEntity.setPositions(TypeMapper.set2string(positionDTO.getPositions()));
            appPositionEntity.setCreateBy(SecureRequestCheck.getUsername());
            appPositionEntity.setUpdateBy(SecureRequestCheck.getUsername());
            appPositionEntity.setCreateTime(LocalDateTime.now());
            appPositionEntity.setUpdateTime(LocalDateTime.now());
            appPositionDBO.save(appPositionEntity);
        }else{
            appPositionEntity.setPositions(TypeMapper.set2string(positionDTO.getPositions()));
            appPositionEntity.setUpdateBy(SecureRequestCheck.getUsername());
            appPositionEntity.setUpdateTime(LocalDateTime.now());
            appPositionDBO.updateById(appPositionEntity);
        }
    }

    @Override
    public Set<String> listPositions() {
        return userOrgDBO.listAllPositions();
    }
}
