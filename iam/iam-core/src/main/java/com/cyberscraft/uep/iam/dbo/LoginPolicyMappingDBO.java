package com.cyberscraft.uep.iam.dbo;

import com.cyberscraft.uep.iam.entity.LoginPolicyMappingEntity;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * IAM-登录策略与租户app映射表 服务类
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2020-05-09
 */
public interface LoginPolicyMappingDBO extends IService<LoginPolicyMappingEntity> {
    LoginPolicyMappingEntity getByClientID(String clientId);

    boolean removeByLoginPolicy(Long loginPolicyId);

    boolean removeByClientId(String clientId);

    List<LoginPolicyMappingEntity> getListByPolicyId(Long policyId);
}
