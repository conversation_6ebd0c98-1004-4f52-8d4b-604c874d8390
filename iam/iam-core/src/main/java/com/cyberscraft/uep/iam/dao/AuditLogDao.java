package com.cyberscraft.uep.iam.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cyberscraft.uep.iam.entity.AuditlogEntity;
import com.cyberscraft.uep.iam.query.AuditlogQueryDto;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface AuditLogDao extends BaseMapper<AuditlogEntity> {
    List<AuditlogEntity> queryAuditLogs(Page<AuditlogEntity> page, @Param("auditlog") AuditlogQueryDto auditlog);

    @Delete("DELETE FROM iam_auditlog WHERE occur_time < #{cutoffDateTime}")
    void delExpiredAuditlog(@Param("cutoffDateTime") LocalDateTime cutoffDateTime);

    /**
     * 查询登录日志
     * @param startTime
     * @param endTime
     * @return
     */
    List<String> getLoginUsers(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     *  查询密码连续错误日志
     * @param startTime
     * @param endTime
     * @return
     */
    List<String> getPasswdErrorUsers(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("dbType") String databaseType);

    /**
     *  查询锁定用户
     * @param startTime
     * @param endTime
     * @param times
     * @return
     */
    List<String> getLockUsers(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime, @Param("times") String times);

}
