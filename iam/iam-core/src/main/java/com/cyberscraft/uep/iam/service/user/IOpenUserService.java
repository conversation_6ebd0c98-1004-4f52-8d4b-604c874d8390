package com.cyberscraft.uep.iam.service.user;

import com.cyberscraft.uep.common.dto.PageView;
import com.cyberscraft.uep.common.dto.Pagination;
import com.cyberscraft.uep.iam.dto.response.UserVO;
import com.cyberscraft.uep.iam.query.UserQueryDTO;

import java.util.List;

/**
 * <p>
 *     IAM对外的用户接口，此接口主要提供给其他服务使用
 *     与IUserService的区别：IUserService主要用于IAM内部调用，而IOpenUserService是构建于IUserService之上的一层接口,供外部使用
 *     即：IOpenUserService可以引用IUserService，但IUserService不能引用IOpenUserService
 *     ！！！！！！！！！！！！建议所有走外部调用的接口，都走此接口，不要再单独写接口！！！！！！！！！！！！
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2020-09-25 09:42
 */
public interface IOpenUserService {
    /**
     * 根据用户名查询用户
     * @param userName
     * @return
     */
    UserVO getUserByUserName(String userName);

    /**
     * 根据用户名和密码查询用户
     * @param userName
     * @param password
     * @return
     */
    UserVO getUserByUserName(String userName, String password);

    /**
     * 用户用户ID查询用户
     * @param id
     * @return
     */
    UserVO getUserVO(Long id);

    /**
     * 批量查询用户名对应的用户
     * @param userNames
     * @return
     */
    List<UserVO> getListByUserNames(List<String> userNames);

    /**
     * 批量查询用户ID对应的用户
     * @param ids
     * @return
     */
    List<UserVO> getListByIds(List<Long> ids);

    /**
     * 根据组织ID，查询组下的用户列表
     * @param groupIds
     * @param isIncludeSubOrg
     * @param isIncludeSysUser
     * @return
     */
    List<UserVO> getUsersByOrgIds(List<Long> groupIds,Boolean isIncludeSubOrg,Boolean isIncludeSysUser);

    /**
     * 分页查询用户信息
     * @param isLoadGroup
     * @param page
     * @return
     */
    PageView<UserVO> searchUsers(Boolean isLoadGroup, Pagination<UserQueryDTO> page);
}
