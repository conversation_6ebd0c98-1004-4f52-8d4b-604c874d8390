package com.cyberscraft.uep.iam.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serializable;

/**
 * <p>
 * IAM-用户组织关系表
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2019-07-04
 */
@TableName("iam_user_org")
public class UserOrgEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户ID
     */
    private Long uid;

    /**
     * 组织ID
     */
    private Long orgRefId;

    /**
     * 职位
     */
    private String position;

    /**
     * 用户在本组的编号，如工号、学号
     */
    private String userCode;

    /**
     * 是否为主部门：1、是，0、否
     */
    private Integer isMain;

    /**
     * 是否为部门主管：1、是，0、否
     */
    @TableField(exist = false)
    private Integer isManager;

    @TableField(exist = false)
    private String manager;

    /**
     * 部门内排序
     */
    private Long userOrder;

    /**
     * 员工在部门中的状况，作为一个标记字段，不做代码层面的业务处理
     */
    private String state;

    private Long connectorId;

    /**
     * 租户ID
     */
    private String tenantId;

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public Long getConnectorId() {
        return connectorId;
    }

    public void setConnectorId(Long connectorId) {
        this.connectorId = connectorId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public Long getOrgRefId() {
        return orgRefId;
    }

    public void setOrgRefId(Long orgRefId) {
        this.orgRefId = orgRefId;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getUserCode() {
        return userCode;
    }

    public void setUserCode(String userCode) {
        this.userCode = userCode;
    }

    public Integer getIsMain() {
        return isMain;
    }

    public void setIsMain(Integer isMain) {
        this.isMain = isMain;
    }

    public Integer getIsManager() {
        return isManager;
    }

    public void setIsManager(Integer isManager) {
        this.isManager = isManager;
    }

    public String getManager() {
        return manager;
    }

    public void setManager(String manager) {
        this.manager = manager;
    }

    public Long getUserOrder() {
        return userOrder;
    }

    public void setUserOrder(Long userOrder) {
        this.userOrder = userOrder;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    @Override
    public String toString() {
        return "UserOrgEntity{" +
                "id=" + id +
                ", uid=" + uid +
                ", orgRefId=" + orgRefId +
                ", position='" + position + '\'' +
                ", userCode='" + userCode + '\'' +
                ", isMain=" + isMain +
                ", userOrder=" + userOrder +
                ", state='" + state + '\'' +
                ", connectorId=" + connectorId +
                ", tenantId='" + tenantId + '\'' +
                '}';
    }
}
