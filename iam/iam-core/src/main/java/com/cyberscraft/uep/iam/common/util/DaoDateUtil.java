package com.cyberscraft.uep.iam.common.util;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;

public class DaoDateUtil {
    private static DateTimeFormatter chinaDateTimeFormat = null;
    private static DateTimeFormatter auditLogDateFormat = null;
    private static ZoneId utcZoneId = ZoneId.of("UTC");
    private static ZoneId chinaZoneId = ZoneId.of("Asia/Shanghai");

    static{
        chinaDateTimeFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss 'UTC+08'");
        auditLogDateFormat = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    }

    public static String fromMilli2ChinaDateTimeStr(long timeMs){
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(timeMs), chinaZoneId).format(chinaDateTimeFormat);
    }

    public static String utcDateStr(Date date) {
        return LocalDateTime.ofInstant(date.toInstant(), utcZoneId).format(auditLogDateFormat);
    }

    public static String localDateTime2String(LocalDateTime localDateTime){
        if(localDateTime == null){
            return null;
        }
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        return dateTimeFormatter.format(localDateTime);
    }
}
