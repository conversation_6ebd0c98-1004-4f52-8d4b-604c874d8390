package com.cyberscraft.uep.iam.service.connector.util;

import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.iam.common.exception.UserCenterException;
import com.cyberscraft.uep.iam.dto.enums.ConnectorTypeEnum;
import com.cyberscraft.uep.iam.entity.ConnectorEntity;
import com.cyberscraft.uep.iam.errors.TransactionErrorType;
import com.cyberscraft.uep.iam.service.connector.ldap.LdapConfig;
import com.cyberscraft.uep.iam.service.transfer.TypeMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ConnectorFactory {
    private static final Logger logger = LoggerFactory.getLogger(ConnectorFactory.class);

    public static String buildLdapUrl(ConnectorEntity entity) {
        Integer type = entity.getType();
        String config = entity.getConfig();
        ConnectorTypeEnum connectorType = TypeMapper.asEnum(type, ConnectorTypeEnum.class);

        switch (connectorType) {
            case AD:
            case LDAP:
                LdapConfig ldapConfig = JsonUtil.str2Obj(config, LdapConfig.class);
                String ldapUrl;
                if (ldapConfig.getTls()) {
                    ldapUrl = String.format("ldaps://%s:%d", ldapConfig.getHost(), ldapConfig.getPort());
                }else{
                    ldapUrl = String.format("ldap://%s:%d", ldapConfig.getHost(), ldapConfig.getPort());
                }
                return ldapUrl;
            default:
                throw new UserCenterException(TransactionErrorType.UNKNOWN_ERROR);
        }
    }
}
