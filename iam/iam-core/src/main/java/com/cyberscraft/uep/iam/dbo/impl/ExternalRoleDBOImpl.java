package com.cyberscraft.uep.iam.dbo.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cyberscraft.uep.account.client.domain.Connector;
import com.cyberscraft.uep.iam.common.enums.SyncDirectionEnum;
import com.cyberscraft.uep.iam.dao.ExternalRoleDao;
import com.cyberscraft.uep.iam.dbo.ExternalRoleDBO;
import com.cyberscraft.uep.iam.entity.ExternalRoleEntity;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/11 17:28
 */
@Service
public class ExternalRoleDBOImpl extends ServiceImpl<ExternalRoleDao, ExternalRoleEntity> implements ExternalRoleDBO {

    @Override
    public ExternalRoleEntity getRoleGroupByExternalGroupId(Long connectorId, String externalGroupId, String... attrs) {
        QueryWrapper<ExternalRoleEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ExternalRoleEntity::getConnectorId, connectorId)
                .eq(ExternalRoleEntity::getExternalGroupId, externalGroupId)
                .eq(ExternalRoleEntity::getRoleType, 0);
        return this.getOne(queryWrapper);
    }

    @Override
    public ExternalRoleEntity getByExternalId(Long connectorId, String externalId, String... attrs) {
        QueryWrapper<ExternalRoleEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ExternalRoleEntity::getConnectorId, connectorId)
                .eq(ExternalRoleEntity::getExternalId, externalId)
                .eq(ExternalRoleEntity::getRoleType, 1);
        return this.getOne(queryWrapper);
    }

    @Override
    public ExternalRoleEntity getByLocalId(Long connectorId, Long localId, String... attrs) {
        QueryWrapper<ExternalRoleEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ExternalRoleEntity::getConnectorId, connectorId)
                .eq(ExternalRoleEntity::getIamId, localId);
        return this.getOne(queryWrapper);
    }

    @Override
    public List<ExternalRoleEntity> getByLocalId(Long connectorId, List<Long> localIds, SyncDirectionEnum syncDirectionEnum, String... attrs) {
        QueryWrapper<ExternalRoleEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ExternalRoleEntity::getConnectorId, connectorId)
                .in(ExternalRoleEntity::getIamId, localIds)
                .eq(ExternalRoleEntity::getSyncDirection,syncDirectionEnum.getValue());
        return this.list(queryWrapper);
    }

    @Override
    public List<ExternalRoleEntity> findToBeDeletedRole(Connector connector) {
        QueryWrapper<ExternalRoleEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.select().lambda().eq(ExternalRoleEntity::getConnectorId, connector.getId()).lt(ExternalRoleEntity::getSyncBatchNo, connector.getSyncBatchNo())
                .orderByDesc(ExternalRoleEntity::getExternalGroupId);
        return this.list(queryWrapper);
    }
    @Override
    public List<ExternalRoleEntity> getRolesByConnectorId(Long connectorId,Integer roleType) {
        QueryWrapper<ExternalRoleEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.select().lambda()
                .eq(ExternalRoleEntity::getConnectorId, connectorId)
                .eq(ExternalRoleEntity::getRoleType, roleType);
        return this.list(queryWrapper);
    }

    @Override
    public boolean deleteByIamId(Long iamId) {
        QueryWrapper<ExternalRoleEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ExternalRoleEntity::getIamId, iamId);

        return this.remove(queryWrapper);
    }

    @Override
    public boolean deleteByIamIds(List<Long> iamIds) {
        if (iamIds == null || iamIds.isEmpty()) {
            return false;
        }
        QueryWrapper<ExternalRoleEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(ExternalRoleEntity::getIamId, iamIds);
        return this.remove(queryWrapper);
    }


    @Override
    public List<ExternalRoleEntity> getAllExternalRoles(Connector connector) {
        QueryWrapper<ExternalRoleEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.select().lambda().eq(ExternalRoleEntity::getConnectorId, connector.getId())
                .orderByDesc(ExternalRoleEntity::getExternalGroupId);
        return this.list(queryWrapper);
    }
}

