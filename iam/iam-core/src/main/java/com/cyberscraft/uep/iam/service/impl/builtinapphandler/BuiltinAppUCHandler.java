package com.cyberscraft.uep.iam.service.impl.builtinapphandler;

import com.cyberscraft.uep.iam.dto.enums.BuiltinAppClientId;
import com.cyberscraft.uep.iam.dto.enums.ClientProfileV2;
import com.cyberscraft.uep.iam.entity.AppEntity;
import com.nimbusds.jose.JWSAlgorithm;
import org.springframework.stereotype.Service;

@Service
public class BuiltinAppUCHandler extends AbstractBuiltinAppHandler {

    public BuiltinAppUCHandler() {
        super(BuiltinAppClientId.UC.getName());
    }

    @Override
    public AppEntity buildAppEntity() {
        AppEntity appEntity = buildAppEntity(ClientProfileV2.SPA);
        appEntity.setSigningAlg(JWSAlgorithm.RS256.getName());
        return appEntity;
    }

    @Override
    public String getRedirectUris() {
        return null;
    }
}
