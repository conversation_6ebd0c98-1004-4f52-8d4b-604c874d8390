package com.cyberscraft.uep.iam.service.data.impl;

import com.cyberscraft.uep.account.client.domain.Connector;
import com.cyberscraft.uep.account.client.exception.ThirdPartyExceedDeleteLimitException;
import com.cyberscraft.uep.account.client.service.IExternalConnectService;
import com.cyberscraft.uep.common.util.CollectionUtils;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.common.util.SnowflakeIDUtil;
import com.cyberscraft.uep.iam.common.FinishedUsersHandler;
import com.cyberscraft.uep.iam.common.constants.ThreadPoolNameConstant;
import com.cyberscraft.uep.iam.constants.RedisKeyConstants;
import com.cyberscraft.uep.iam.dbo.ConnectorPushHistoryDBO;
import com.cyberscraft.uep.iam.dto.domain.SyncStats;
import com.cyberscraft.uep.iam.dto.enums.*;
import com.cyberscraft.uep.iam.entity.*;
import com.cyberscraft.uep.iam.service.IDataSyncService;
import com.cyberscraft.uep.iam.service.ISyncErrorService;
import com.cyberscraft.uep.iam.service.ITagService;
import com.cyberscraft.uep.iam.service.connector.ConnectorSyncTypeEnum;
import com.cyberscraft.uep.iam.service.connector.task.PushTask;
import com.cyberscraft.uep.iam.service.connector.task.PushTaskMonitor;
import com.cyberscraft.uep.iam.service.data.IPushConnectorService;
import com.cyberscraft.uep.iam.service.data.IPushDataConsumerService;
import com.cyberscraft.uep.iam.service.data.transfer.UserOrgEntityTransfer;
import com.cyberscraft.uep.iam.service.user.*;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import com.cyberscraft.uep.task.Task;
import com.cyberscraft.uep.task.TaskHolder;
import com.cyberscraft.uep.task.TaskIsCanceledException;
import com.cyberscraft.uep.task.TaskStatus;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

/***
 * 根据事件消息推送数据到第三方平台，调用第三方平台的相关接口，对用户及组进行相关操作处理
 * @date 2021/7/3
 * <AUTHOR>
 ***/
@Service
public class PushDataConsumerServiceImpl implements IPushDataConsumerService {

    private final static Logger LOG = LoggerFactory.getLogger(PushDataConsumerServiceImpl.class);

    @Resource
    protected RedissonClient redissonClient;

    @Resource
    private IPushConnectorService pushConnectorService;

    @Autowired
    private IUserService userService;

    @Autowired
    private IOrganizationService organizationService;

    @Autowired
    private UserOrgEntityTransfer userOrgEntityTransfer;

    @Autowired
    private IUsersPushService usersPushService;

    @Autowired
    private IRolesPushService rolesPushService;

    @Autowired
    private ConnectorPushHistoryDBO connectorPushHistoryDBO;

    @Autowired
    protected IExternalConnectService externalConnectService;

    @Autowired
    private IExternalUserService externalUserService;

    @Autowired
    private IExternalRoleService externalRoleService;

    @Autowired
    private ITagService tagService;

    @Autowired
    private ISyncErrorService syncErrorService;

    @Autowired
    private IDataSyncService dataSyncService;

    @Autowired
    private PushTaskMonitor pushTaskMonitor;

    @Resource(name = ThreadPoolNameConstant.PUSH_DATA_POOL_NAME)
    private ExecutorService pushDataPoolExecutor;

    @Override
    public void rePushUnPushedMessage() {
        String tenantCode = TenantHolder.getTenantCode();
        pushDataPoolExecutor.execute(() -> {
            TenantHolder.setTenantCode(tenantCode);
            LOG.info("push unPushed event data to connector start  tenant is :{}", tenantCode);
            try {
                List<PushConnectorEntity> connectors = pushConnectorService.getAllPushConnectors();

                for (PushConnectorEntity connectorEntity : connectors) {
                    //todo 同步流事件暂未实现
                    if (connectorEntity.getType() == ConnectorTypeEnum.SCIM.getValue()) {
                        LOG.info("scim connector not support push: {}", connectorEntity.getName());
                        continue;
                    }

                    pushToConnectorInner(connectorEntity);

                    Task task = TaskHolder.getTask();
                    if (task != null && task.getStatus() == TaskStatus.CANCELED) {
                        break;
                    }
                }
            } catch (Exception e) {
                LOG.error("push unPushed event data to connector has error", e);
            } finally {
                TaskHolder.remove();
                LOG.info("push unPushed event data to connector unlock,  tenant is :{}", tenantCode);
            }
            LOG.info("push unPushed event data to connector finished  tenant is :{}", tenantCode);
        });
    }

    private void pushToConnectorInner(PushConnectorEntity connectorEntity) {

        String lockKey = String.format(RedisKeyConstants.CONNECTOR_PUSH_LOCK_KEY, TenantHolder.getTenantCode(), connectorEntity.getId());
        RLock lock = redissonClient.getLock(lockKey);

        if (!pushTaskMonitor.tryLock(lock, RedisKeyConstants.CONNECTOR_SYNC_WAIT, RedisKeyConstants.CONNECTOR_SYNC_EXPIRE, TimeUnit.MILLISECONDS)) {
            LOG.warn("cannot get lock for tenant:{}, connector:{}", TenantHolder.getTenantCode(), connectorEntity.getId());
            return;
        }

        try {
            if (PushConnectorStatusEnum.ACTIVE.getValue() != connectorEntity.getStatus()) {
                dataSyncService.cleanAllPushData(connectorEntity.getId());
                return;
            }

            int batchNum = 1000;
            Collection<PushDataEntity> pushDataEntities = dataSyncService.firstBatchPushData(connectorEntity.getId(), batchNum);
            if (pushDataEntities.isEmpty()) {
                return;
            }

            Connector connector = usersPushService.toConnector(connectorEntity);
            try {
                pushToConnectorInner(connectorEntity, connector, pushDataEntities);
            } finally {
                externalConnectService.closeConnection(connector);
            }
        } finally {
            FinishedUsersHandler.clearCache();

            if (lock.isLocked()) {
                lock.unlock();
            }
        }
    }

    private void pushToConnectorInner(PushConnectorEntity connectorEntity, Connector connector, Collection<PushDataEntity> pushDataEntities) {

        List<OrgEntity> topLevelOrgList = pushConnectorService.getTopLevelOrgList(connectorEntity);
        connectorEntity.setTopLevelOrgList(topLevelOrgList);

        // 从push事件表取出数据推送的情况 记录日志
        SyncStats syncStats = new SyncStats(connectorEntity.getId(), "pushData user 、 role or org ");

        long pushHistoryId = SnowflakeIDUtil.getId();
        syncStats.taskId = pushHistoryId;
        connector.setTaskId(pushHistoryId);

        PushTask task = new PushTask(String.valueOf(pushHistoryId));
        try {
            task.setTenantId(TenantHolder.getTenantCode());
            task.setConnectorId(connectorEntity.getId().toString());
            task.setStats(syncStats);
            pushTaskMonitor.registerTask(task);

            ConnectorPushHistoryEntity pushHistory = new ConnectorPushHistoryEntity();
            pushHistory.setId(pushHistoryId);
            pushHistory.setType(ConnectorSyncTypeEnum.EVENT.getValue());
            pushHistory.setPushStartTime(LocalDateTime.now());
            pushHistory.setStatus(SyncStatus.IN_PROGRESS.getValue());
            pushHistory.setViewStatus(SyncStatus.IN_PROGRESS.getValue());
            pushHistory.setConnectorId(connectorEntity.getId());
            pushHistory.setBatchNo(connectorEntity.getPushBatchNo());
            connectorPushHistoryDBO.save(pushHistory);

            try {
                Set<Long> needDeletedUserIds = new HashSet<>();
                Set<Long> needDeletedOrgIds = new HashSet<>();

                List<TagEntity> tags = new ArrayList<>();
                for (PushDataEntity pushDataEntity : pushDataEntities) {
                    task.addProgress(0, 85, pushDataEntities.size());

                    String value = pushDataEntity.getValue();
                    if (PushBusinessType.ROLE.getValue() == pushDataEntity.getBusinessType() && connector.isPushRole()) {
                        //推送角色
                        dataSyncService.deletePushData(connectorEntity.getId(), value);
                        pushRoleToConnector(connectorEntity, connector, pushDataEntity, syncStats, tags);
                    } else if (PushBusinessType.USER.getValue() == pushDataEntity.getBusinessType()) {
                        //推送用户
                        pushUserToConnector(topLevelOrgList, connectorEntity, connector, pushDataEntity, needDeletedUserIds, syncStats);
                    } else if (PushBusinessType.ORG.getValue() == pushDataEntity.getBusinessType()) {
                        //推送部门
                        pushOrgToConnector(connectorEntity, connector, pushDataEntity, needDeletedOrgIds, syncStats);
                    } else {
                        //从缓存里删除，以保证队列里的数据能被清除
                        dataSyncService.deletePushData(connectorEntity.getId(), value);
                    }
                }

                task.setProgress(85);

                if (connector.isPushRole()) {
                    for (TagEntity tag : tags) {
                        rolesPushService.resetRoleUserLinks(connectorEntity, connector, tag, syncStats);
                    }
                }
                task.setProgress(90);

                Integer deleteLimit = connectorEntity.getDeleteLimit();
                if (CollectionUtils.isNotEmpty(needDeletedUserIds)) {
                    if (deleteLimit != null && deleteLimit > -1 && needDeletedUserIds.size() > deleteLimit) {
                        for (Long needDeletedUserId : needDeletedUserIds) {
                            if (task.getStatus() == TaskStatus.CANCELED) {
                                throw new TaskIsCanceledException(task.getId());
                            }

                            ExternalUserEntity mappedExternalUser = externalUserService.getByLocalUserId(connectorEntity.getId(), needDeletedUserId);
                            if (mappedExternalUser != null) {
                                externalUserService.updateExternalUser(mappedExternalUser.getId(), 0);
                                syncErrorService.saveTobeDeletedLog(connector, mappedExternalUser);
                                syncStats.toDeleteLimit++;
                            }

                            dataSyncService.deletePushData(connectorEntity.getId(), "U" + needDeletedUserId);
                        }
                        // 删除数量超阈值
                        LOG.info("need deletes users gt deleteLimit connector {}  delete users {}", connectorEntity.getId(), syncStats.toDeleteLimit);
                        throw new ThirdPartyExceedDeleteLimitException(needDeletedOrgIds.size(), deleteLimit);
                    } else {
                        for (Long needDeletedUserId : needDeletedUserIds) {
                            if (task.getStatus() == TaskStatus.CANCELED) {
                                throw new TaskIsCanceledException(task.getId());
                            }

                            pushDeletedUserToConnector(needDeletedUserId, connectorEntity, connector, syncStats);

                            dataSyncService.deletePushData(connectorEntity.getId(), "U" + needDeletedUserId);
                        }
                    }
                }

                task.setProgress(95);

                if (CollectionUtils.isNotEmpty(needDeletedOrgIds)) {
                    for (Long needDeletedOrgId : needDeletedOrgIds) {
                        if (task.getStatus() == TaskStatus.CANCELED) {
                            throw new TaskIsCanceledException(task.getId());
                        }

                        DeletedOrgEntity org = organizationService.getDeletedOrg(needDeletedOrgId);
                        if (org != null) {
                            pushDeletedOrgToConnector(userOrgEntityTransfer.deletedOrg2Org(org), connectorEntity, connector, syncStats);
                        }

                        dataSyncService.deletePushData(connectorEntity.getId(), "O" + needDeletedOrgId);
                    }
                }

            } catch (TaskIsCanceledException te) {
                LOG.error("task is killed", te);
                syncStats.failReason = te.getMessage();
                syncStats.status = SyncStatus.FAILED;
            } catch (ThirdPartyExceedDeleteLimitException de) {
                syncStats.status = SyncStatus.PREVENT;
            } catch (Exception e) {
                syncStats.status = SyncStatus.FAILED;
                LOG.error("push unPushed event data to connector has error, connector id:{}", connectorEntity.getId());
            }

            if (!syncStats.isChanged() && syncStats.status == SyncStatus.SUCCESS) {
                LOG.info("push not any update");
                connectorPushHistoryDBO.removeById(pushHistoryId);
            } else {
                pushHistory.setStats(JsonUtil.obj2Str(syncStats));
                pushHistory.setPushEndTime(LocalDateTime.now());

                pushHistory.setStatus(syncStats.status.getValue());
                if (syncStats.status == SyncStatus.FAILED) {
                    pushHistory.setViewStatus(SyncStatus.FAILED.getValue());
                } else if (syncStats.hasFailure()) {
                    pushHistory.setViewStatus(SyncStatus.EXCEPTION.getValue());
                } else if (syncStats.toDeleteLimit > 0) {
                    pushHistory.setViewStatus(SyncStatus.PREVENT.getValue());
                } else {
                    pushHistory.setViewStatus(syncStats.status.getValue());
                }
                connectorPushHistoryDBO.updateById(pushHistory);
            }

            pushTaskMonitor.completeTask(task.getId());
        } catch (Exception e) {
            pushTaskMonitor.terminateTask(task, e);
        }
    }

    /**
     * 推送用户到连接器
     *
     * @param topLevelOrgList   推送范围
     * @param pushConnector     连接器
     * @param pushData
     * @param needDeleteUserIds 记录待删除的列表
     * @param syncStats
     */
    private void pushUserToConnector(List<OrgEntity> topLevelOrgList, PushConnectorEntity pushConnector, Connector connector, PushDataEntity pushData, Set<Long> needDeleteUserIds, SyncStats syncStats) {
        Integer changeType = pushData.getChangeType();
        if (DataChangeType.DELETE.getValue() != changeType) {
            if (userService.isUserCountExceed()) {
                LOG.warn("用户总量已经超出license限制,不再往下游同步用户");
                dataSyncService.deletePushData(pushConnector.getId(), "U" + pushData.getBusinessDataId());
                return;
            }

            List<OrgEntity> orgEntityList = organizationService.getOrgEntityList(pushData.getBusinessDataId());
            boolean userIsInPushScope = false;
            for (OrgEntity orgEntity : orgEntityList) {
                boolean inPushScope = usersPushService.isInPushScope(orgEntity, topLevelOrgList);
                if (inPushScope) {
                    userIsInPushScope = true;
                    break;
                }
            }

            if (!userIsInPushScope) {
                ExternalUserEntity mappedExternalUser = externalUserService.getByLocalUserId(pushConnector.getId(), pushData.getBusinessDataId());
                if (mappedExternalUser != null) {
                    //待删除
                    needDeleteUserIds.add(pushData.getBusinessDataId());
                } else {
                    dataSyncService.deletePushData(pushConnector.getId(), "U" + pushData.getBusinessDataId());
                }
            } else {
                dataSyncService.deletePushData(pushConnector.getId(), "U" + pushData.getBusinessDataId());

                //更新
                UserEntity user = userService.getUser(pushData.getBusinessDataId());
                if (user == null) {
                    LOG.info("push user to connector: cannot find user ,user id:{},tenant is :{}", pushData.getBusinessDataId(), pushData.getTcode());
                } else if (CreatedMode.BY_SYSTEM.getValue() == user.getCreatedMode()) {
                    LOG.info("push user to connector: cannot push system admin: user id:{},tenant is :{}", pushData.getBusinessDataId(), pushData.getTcode());
                } else {
                    boolean oldFullSync = connector.isFullSync();
                    try {
                        if (DataChangeType.FORCE.getValue() == changeType) {
                            connector.setFullSync(true);
                        }
                        pushUserToConnector(user, pushConnector, connector, syncStats);
                    } finally {
                        connector.setFullSync(oldFullSync);
                    }
                }
            }
        } else {
            ExternalUserEntity mappedExternalUser = externalUserService.getByLocalUserId(pushConnector.getId(), pushData.getBusinessDataId());
            if (mappedExternalUser != null) {
                //待删除
                needDeleteUserIds.add(pushData.getBusinessDataId());
            } else {
                dataSyncService.deletePushData(pushConnector.getId(), "U" + pushData.getBusinessDataId());
            }
        }
    }

    private void pushRoleToConnector(PushConnectorEntity pushConnector, Connector connector, PushDataEntity pushData, SyncStats syncStats, List<TagEntity> tags) {
        //处理删除事件
        if (DataChangeType.DELETE.getValue() == pushData.getChangeType()) {
            LOG.info("will delete role ,role id is :{} tenant id :{}", pushData.getBusinessDataId(), pushData.getTcode());
            pushDeletedRoleToConnector(pushData.getBusinessDataId(), pushConnector, connector, syncStats);
        } else { //处理增加修改事件
            TagEntity tag = tagService.getTagById(String.valueOf(pushData.getBusinessDataId()));
            if (tag == null) {
                LOG.info("push role to connector,cannot find org in roles ,org id:{},tenant is :{}", pushData.getBusinessDataId(), pushData.getTcode());
            } else {
                if (rolesPushService.isInRolePushScope(tag, pushConnector)) {
                    pushRoleToConnector(tag, pushConnector, connector, syncStats);
                    tags.add(tag);
                } else {
                    LOG.info("current role not in push scope ,role id:{},tenant is :{}", tag.getId(), pushData.getTcode());
                }
            }
        }
    }

    /**
     * 推送部门到连接器
     *
     * @param pushConnector 连接器
     * @param pushData      业务数据
     * @param syncStats     同步状态值
     */
    private void pushOrgToConnector(PushConnectorEntity pushConnector, Connector connector, PushDataEntity pushData, Set<Long> needDeleteOrgIds, SyncStats syncStats) {
        //处理删除事件
        if (DataChangeType.DELETE.getValue() == pushData.getChangeType()) {
            DeletedOrgEntity org = organizationService.getDeletedOrg(pushData.getBusinessDataId());
            if (org == null) {
                dataSyncService.deletePushData(pushConnector.getId(), "O" + pushData.getBusinessDataId());
                LOG.info("push deleted org to connector,cannot find org in deleted's org,org id:{},tenant is :{}", pushData.getBusinessDataId(), pushData.getTcode());
            } else {
                needDeleteOrgIds.add(pushData.getBusinessDataId());

                pushDeletedOrgToConnector(userOrgEntityTransfer.deletedOrg2Org(org), pushConnector, connector, syncStats);
            }
        } else { //处理增加修改事件
            dataSyncService.deletePushData(pushConnector.getId(), "O" + pushData.getBusinessDataId());
            if (userService.isUserCountExceed()) {
                LOG.warn("用户总量已经超出license限制,不再往下游同步部门");
                return;
            }

            OrgEntity org = organizationService.get(pushData.getBusinessDataId());
            if (org == null) {
                LOG.info("push org to connector,cannot find org in orgs ,org id:{},tenant is :{}", pushData.getBusinessDataId(), pushData.getTcode());
            } else {
                boolean oldFullSync = connector.isFullSync();
                try {
                    if (DataChangeType.FORCE.getValue() == pushData.getChangeType()) {
                        connector.setFullSync(true);
                    }
                    pushOrgToConnector(org, pushConnector, connector, syncStats);
                } finally {
                    connector.setFullSync(oldFullSync);
                }
            }
        }
    }

    /**
     * @param localUserId     本地用户ID
     * @param connectorEntity 连接器
     * @param syncStats       状态
     */
    private void pushDeletedUserToConnector(Long localUserId, PushConnectorEntity connectorEntity, Connector connector, SyncStats syncStats) {
        Task task = TaskHolder.getTask();
        if (task != null && task.getStatus() == TaskStatus.CANCELED) {
            throw new TaskIsCanceledException(task.getId());
        }

        usersPushService.pushDeleteUser(connectorEntity, connector, localUserId, syncStats);
    }

    /**
     * @param user          本地用户信息
     * @param pushConnector 推送连接器
     * @param syncStats     状态
     */
    private void pushUserToConnector(UserEntity user, PushConnectorEntity pushConnector, Connector connector, SyncStats syncStats) {
        Task task = TaskHolder.getTask();
        if (task != null && task.getStatus() == TaskStatus.CANCELED) {
            throw new TaskIsCanceledException(task.getId());
        }

        try {
            usersPushService.pushCreateOrUpdateUser(pushConnector, connector, user, syncStats);
        } catch (Exception e) {
            LOG.error("push data to connector,sync user has error", e);
        }
    }

    /**
     * @param org           本地部门
     * @param pushConnector 推送连接器
     * @param connector
     * @param syncStats     状态
     */
    private void pushDeletedOrgToConnector(OrgEntity org, PushConnectorEntity pushConnector, Connector connector, SyncStats syncStats) {
        Task task = TaskHolder.getTask();
        if (task != null && task.getStatus() == TaskStatus.CANCELED) {
            throw new TaskIsCanceledException(task.getId());
        }

        usersPushService.pushDeleteOrg(pushConnector, connector, org.getId(), syncStats);
    }

    /**
     * @param org           本地部门
     * @param pushConnector 推送连接器
     * @param connector
     * @param syncStats     状态
     */
    private void pushOrgToConnector(OrgEntity org, PushConnectorEntity pushConnector, Connector connector, SyncStats syncStats) {
        Task task = TaskHolder.getTask();
        if (task != null && task.getStatus() == TaskStatus.CANCELED) {
            throw new TaskIsCanceledException(task.getId());
        }

        usersPushService.pushOrgs(pushConnector, connector, Arrays.asList(org), syncStats);
    }

    private void pushRoleToConnector(TagEntity tag, PushConnectorEntity pushConnector, Connector connector, SyncStats syncStats) {
        Task task = TaskHolder.getTask();
        if (task != null && task.getStatus() == TaskStatus.CANCELED) {
            throw new TaskIsCanceledException(task.getId());
        }

        try {
            rolesPushService.pushCreateOrUpdateRole(pushConnector, connector, tag, syncStats);
        } catch (Exception e) {
            LOG.error("push data to connector,sync user has error", e);
        }
    }

    private void pushDeletedRoleToConnector(Long tag, PushConnectorEntity pushConnector, Connector connector, SyncStats syncStats) {
        Task task = TaskHolder.getTask();
        if (task != null && task.getStatus() == TaskStatus.CANCELED) {
            throw new TaskIsCanceledException(task.getId());
        }

        rolesPushService.pushDeleteRole(pushConnector, connector, tag, syncStats);
    }
}
