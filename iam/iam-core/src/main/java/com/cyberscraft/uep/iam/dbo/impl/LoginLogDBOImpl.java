package com.cyberscraft.uep.iam.dbo.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cyberscraft.uep.iam.entity.LoginLogEntity;
import com.cyberscraft.uep.iam.dao.LoginLogDao;
import com.cyberscraft.uep.iam.dbo.LoginLogDBO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cyberscraft.uep.iam.service.auditlog.constant.AuditEventSubtype;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * IAM-用户登录历史表 服务实现类
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2024-09-09
 */
@Service
public class LoginLogDBOImpl extends ServiceImpl<LoginLogDao, LoginLogEntity> implements LoginLogDBO {

    @Override
    public LoginLogEntity getLastLoginHistoryByUserId(Long uid) {
        QueryWrapper<LoginLogEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(LoginLogEntity::getUid, uid)
                .ne(LoginLogEntity::getEventSubtype, AuditEventSubtype.AUTHORIZE_USER_DENY.code())
                .ne(LoginLogEntity::getEventSubtype, AuditEventSubtype.SIGNOUT.code())
                .eq(LoginLogEntity::getErrorCode, "0")
                .orderByDesc(LoginLogEntity::getLoginTime)
                .last("LIMIT 1");
        return this.getOne(wrapper);
    }

    @Override
    public List<LoginLogEntity> getRecentLoginLogs(Long uid, int limit, LocalDateTime startTime) {
        QueryWrapper<LoginLogEntity> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .eq(LoginLogEntity::getUid, uid)
                .ne(LoginLogEntity::getEventSubtype, AuditEventSubtype.AUTHORIZE_USER_DENY.code())
                .ne(LoginLogEntity::getEventSubtype, AuditEventSubtype.SIGNOUT.code())
                .eq(LoginLogEntity::getErrorCode, "0")
                .gt(LoginLogEntity::getLoginTime, startTime)
                .orderByDesc(LoginLogEntity::getLoginTime)
                .last("LIMIT " + limit);
        return this.list(wrapper);
    }

}
