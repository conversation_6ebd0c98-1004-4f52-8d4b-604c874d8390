package com.cyberscraft.uep.iam.service;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/7/13 10:56 上午
 */
public interface IAppCategoryLinkService {

    /**
     * 根据分类id获取client_id列表
     * @param categoryId
     * @return
     */
    Set<String> findClientIdByCategoryId(Long categoryId);

    /**
     * 根据client_id获取分类id列表
     * @param clientId
     * @return
     */
    Set<Long> findCategoryByClientId(String clientId);

    /**
     * 获取全部应用client_id与分类id的关联
     * @return
     */
    Map<String, Set<String>> getAllAppCategoryLink();

    /**
     * 增加应用分类与应用的关联
     * @param clientId
     * @param categoryId
     */
    void addAppCategoryLink(String clientId, Long categoryId);

    /**
     * 修改某一应用的全部分类
     * @param clientId
     * @param categoryIds
     */
    void updateAppCategoryLink(String clientId, List<String> categoryIds);

    /**
     * 删除应用分类与应用的关联
     * @param clientId
     * @param categoryId
     */
    void delAppCategoryLink(String clientId, Long categoryId);

    /**
     * 删除应用分类
     * @param categoryId
     */
    void delAppCategory(Long categoryId);

    /**
     * 删除应用
     * @param clientId
     */
    void delApp(String clientId);
}
