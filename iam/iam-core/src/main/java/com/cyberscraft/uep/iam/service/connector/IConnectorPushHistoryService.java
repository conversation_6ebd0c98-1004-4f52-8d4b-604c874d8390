package com.cyberscraft.uep.iam.service.connector;

import com.cyberscraft.uep.common.dto.QueryPage;
import com.cyberscraft.uep.iam.dto.response.ConnectorPushHistoryVO;
import com.cyberscraft.uep.iam.dto.response.SyncScheduleVo;
import com.cyberscraft.uep.iam.entity.ConnectorPushHistoryEntity;

/**
 * @Author：xhquan
 * @Project：uep-services
 * @since：2023/10/12 14:59
 */
public interface IConnectorPushHistoryService {
    /**
     * 按条件分页查询 通讯录集成 历史记录
     *
     * @param page        页面
     * @param size        大小
     * @param connectorId 连接器ID
     * @param type        触发类型
     * @param status      状态
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @return
     */
    QueryPage<ConnectorPushHistoryVO> getPushHistoryList(Integer page, Integer size, String connectorId, Integer type, Integer status, Long startTime, Long endTime);

    /**
     * 根据ID查询出集成历史记录的详细信息
     *
     * @param taskId 执行ID
     * @return
     */
    ConnectorPushHistoryVO getPushHistoryByTaskId(Long taskId);

    /**
     * 获取推送记录的最后一条日志
     *
     * @return
     */
    ConnectorPushHistoryEntity queryLastPushHistory(Long connectorId);


    /**
     * 获取推送记录的进度信息
     *
     * @param connectorId 连接器ID
     * @return 进度信息
     */
    SyncScheduleVo getConnectorPushSchedule(String connectorId, String taskId);
}
