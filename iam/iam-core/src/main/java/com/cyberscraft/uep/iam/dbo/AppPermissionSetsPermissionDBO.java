package com.cyberscraft.uep.iam.dbo;

import com.cyberscraft.uep.iam.entity.AppPermissionSetsPermissionEntity;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * IAM-应用权限组-权限关系表 服务类
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2019-07-08
 */
public interface AppPermissionSetsPermissionDBO extends IService<AppPermissionSetsPermissionEntity> {
    List<AppPermissionSetsPermissionEntity> getList(AppPermissionSetsPermissionEntity appPermissionSetsPermissionEntity);
    void delete(Long permissionSetsRefId);
    AppPermissionSetsPermissionEntity getOne(Long permissionId,Long permissionSetsId);

    List<AppPermissionSetsPermissionEntity> getListByAppId(Long appId, Long permissionSetRefId);
}
