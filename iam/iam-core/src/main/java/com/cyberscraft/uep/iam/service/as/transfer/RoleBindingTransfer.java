package com.cyberscraft.uep.iam.service.as.transfer;

import com.cyberscraft.uep.common.dto.QueryPage;
import com.cyberscraft.uep.iam.common.constants.DaoConstants;
import com.cyberscraft.uep.iam.common.exception.OrganizationException;
import com.cyberscraft.uep.iam.dto.enums.FieldType;
import com.cyberscraft.uep.iam.dto.request.*;
import com.cyberscraft.uep.iam.errors.TransactionErrorType;
import com.cyberscraft.uep.iam.service.IFieldDictService;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;

import java.util.*;
import java.util.stream.Collectors;

@Mapper(componentModel = "spring")
public abstract class RoleBindingTransfer {
    public static RoleBindingTransfer INSTANCE = Mappers.getMapper(RoleBindingTransfer.class);

    @Autowired
    private IFieldDictService fieldDictService;

    public QueryPage<UserRoleBindingVO> entityPageToVoPage(QueryPage<Map<String, Object>> mapEntityList, String... domainAttrs) {
        QueryPage<UserRoleBindingVO> resultPage = new QueryPage<>();
        resultPage.setOrders(mapEntityList.getOrders());
        resultPage.setTotal(mapEntityList.getTotal());
        resultPage.setSize(mapEntityList.getSize());
        resultPage.setPage(mapEntityList.getPage());

        List<UserRoleBindingVO> userRoleBindingVOS = mapToListVo(mapEntityList.getItems(), domainAttrs);

        resultPage.setItems(userRoleBindingVOS);

        return resultPage;
    }

    public List<UserRoleBindingVO> mapToListVo(List<Map<String, Object>> mapEntityList, String... domainAttrs) {

        Map<String, Object> mapVo;
        if (domainAttrs == null || domainAttrs.length == 0) {
            List<String> list = new ArrayList(fieldDictService.getAllFieldDomainNames(FieldType.USER));
            domainAttrs = list.toArray(new String[list.size()]);
        }
        List<UserRoleBindingVO> resultList = new ArrayList<>();
        UserRoleBindingVO userRoleBindingVO;

        Set<RoleBindingScopeInfoVO> roleBindingScopeInfoVOSet;
        RoleBindingScopeInfoVO roleBindingScopeInfoVO;
        for (Map<String, Object> dataMap : mapEntityList) {
            //init
            userRoleBindingVO = new UserRoleBindingVO();
            roleBindingScopeInfoVOSet = new HashSet<>();
            mapVo = new HashMap<>();

            //cover user
            for (String domainName : domainAttrs) {
                String fieldName = fieldDictService.toFieldName(domainName, FieldType.USER);
                if (null != fieldName) {
                    mapVo.put(domainName, dataMap.get(fieldName));
                } else {
//                    throw new OrganizationException(HttpStatus.BAD_REQUEST, TransactionErrorType.ORG_ATTR_UNKNOWN);
                }
            }
            userRoleBindingVO.setUser(mapVo);


            //cover binding scopes
            if (dataMap.get(DaoConstants.APP_ROLE_UTO_ATTR_BINDING_SCOPES) != null) {
                String arrayBindingScope[] = dataMap.get(DaoConstants.APP_ROLE_UTO_ATTR_BINDING_SCOPES).toString().split(DaoConstants.COLLECTION_FIELD_SEPARATOR);
                for (String scope : arrayBindingScope) {
                    roleBindingScopeInfoVO = new RoleBindingScopeInfoVO();
                    roleBindingScopeInfoVO.setScope(scope);
                    roleBindingScopeInfoVOSet.add(roleBindingScopeInfoVO);
                }
                userRoleBindingVO.setBindingScopes(roleBindingScopeInfoVOSet);
            } else {
                userRoleBindingVO.setBindingScopes(null);
            }
            resultList.add(userRoleBindingVO);
        }
        return resultList;
    }


    public abstract RoleBindingCreateInVO toCreateVO(RoleBindingUploadBean vo);


    public RoleBindingBasicInfoVO toBasicVO(RoleBindingVO roleBindingVO) {
        RoleBindingBasicInfoVO infoVO = new RoleBindingBasicInfoVO();
        infoVO.setName(roleBindingVO.getRole().getName());
        infoVO.setBinding_scopes(roleBindingVO.getBindingScopes().stream().map(vo -> vo.getScope()).collect(Collectors.toSet()));
        return infoVO;
    }

    public abstract List<RoleBindingBasicInfoVO> toBasicVO(List<RoleBindingVO> roleBindingVO);
}
