package com.cyberscraft.uep.iam.service.impl;

import com.cyberscraft.uep.common.dto.QueryPage;
import com.cyberscraft.uep.iam.common.constants.DaoConstants;
import com.cyberscraft.uep.iam.common.enums.LocalOrgAttr;
import com.cyberscraft.uep.iam.common.enums.LocalUserAttr;
import com.cyberscraft.uep.iam.common.exception.AppException;
import com.cyberscraft.uep.iam.common.exception.OrganizationException;
import com.cyberscraft.uep.iam.common.exception.UserCenterException;
import com.cyberscraft.uep.iam.common.util.JacksonJsonUtil;
import com.cyberscraft.uep.iam.common.util.OrgUtil;
import com.cyberscraft.uep.iam.common.util.UserUtil;
import com.cyberscraft.uep.iam.dao.AppRoleDao;
import com.cyberscraft.uep.iam.dbo.*;
import com.cyberscraft.uep.iam.dto.enums.CreatedMode;
import com.cyberscraft.uep.iam.dto.enums.RoleBindTypeEnum;
import com.cyberscraft.uep.iam.dto.request.*;
import com.cyberscraft.uep.iam.dto.response.OrgBasicInfoOutVO;
import com.cyberscraft.uep.iam.entity.*;
import com.cyberscraft.uep.iam.service.AppUtil;
import com.cyberscraft.uep.iam.service.IAppService;
import com.cyberscraft.uep.iam.service.IRoleBindingService;
import com.cyberscraft.uep.iam.service.IRoleService;
import com.cyberscraft.uep.iam.service.as.transfer.RoleBindingTransfer;
import com.cyberscraft.uep.iam.service.auditlog.annotation.Auditable;
import com.cyberscraft.uep.iam.service.auditlog.constant.AuditEventSubtype;
import com.cyberscraft.uep.iam.service.auditlog.constant.AuditEventType;
import com.cyberscraft.uep.iam.service.auditlog.constant.AuditRoleType;
import com.cyberscraft.uep.iam.service.oidc.domain.UserInfo;
import com.cyberscraft.uep.iam.service.oidc.util.SecureRequestCheck;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.provider.expression.OAuth2ExpressionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.cyberscraft.uep.iam.errors.TransactionErrorType.*;

/**
 * <p>
 * 角色与用户、组织、标签绑定业务服务实现
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2019-07-22 15:54
 */
@Service
public class RoleBindingServiceImpl implements IRoleBindingService {
    private static final Logger logger = LoggerFactory.getLogger(RoleBindingServiceImpl.class);

    @Autowired
    private IAppService iAppService;
    @Autowired
    private IRoleService iRoleService;
    @Autowired
    private UserDBO userDBO;

    @Autowired
    private UserOrgDBO userOrgDBO;

    @Autowired
    private AppRoleUtoDBO appRoleUtoDBO;
    @Autowired
    private OrgDBO orgDBO;
    @Autowired
    private TagDBO tagDBO;
    @Autowired
    private RoleBindingTransfer roleBindingTransfer;

    @Autowired
    private AppRoleDao appRoleDao;

    public static String ROLE_NAME = "role_name";
    public static String CLIENT_ID = "client_id";

    public static String toAuditLogParameters(String clientId, String roleName, RoleBindingCreateInVO vo) {
        Map<String, Object> map = new HashMap<>();
        if (vo != null) {
            map = vo.toMap();
        }
        map.put(CLIENT_ID, clientId);
        map.put(ROLE_NAME, roleName);
        return JacksonJsonUtil.beanToJson(map);
    }

    public static String toAuditLogParameters(String clientId, String roleName, String operator) {
        Map<String, Object> map = new HashMap<>();
        map.put(CLIENT_ID, clientId);
        map.put(ROLE_NAME, roleName);
        map.put("targets", new String[]{operator});
        return JacksonJsonUtil.beanToJson(map);
    }

    public static String toAuditLogParameters(String target, String clientId, String roleName, RoleBindingUpdateInVO vo) {
        Map<String, Object> map = new HashMap<>();
        if (vo != null) {
            map = vo.toMap();
        }
        map.put(CLIENT_ID, clientId);
        map.put(ROLE_NAME, roleName);
        map.put("target", target);
        return JacksonJsonUtil.beanToJson(map);
    }

    /****************************************************************创建************************************************************************/
    @Auditable(eventType = AuditEventType.APP, eventSubtype = AuditEventSubtype.NEW_USER_ROLE_BINDING,
            fields = "targetId=#{#clientId};;" +
                    "targetName=#{#clientId};;" +
                    "parameters=#{T(com.cyberscraft.uep.iam.service.impl.RoleBindingServiceImpl).toAuditLogParameters(#clientId, #roleName,#roleBindingCreateInVO)}")
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Void createUserRoleBinding(String clientId, String roleName, RoleBindingCreateInVO roleBindingCreateInVO) {
        createRoleBindingsProcess(clientId, roleName, roleBindingCreateInVO, RoleBindTypeEnum.USER.getValue());
        return null;
    }


    @Auditable(eventType = AuditEventType.APP, eventSubtype = AuditEventSubtype.NEW_TAG_ROLE_BINDING,
            fields = "targetId=#{#clientId};;" +
                    "targetName=#{#clientId};;" +
                    "parameters=#{T(com.cyberscraft.uep.iam.service.impl.RoleBindingServiceImpl).toAuditLogParameters(#clientId, #roleName,#roleBindingCreateInVO)}")
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Void createTagRoleBinding(String clientId, String roleName, RoleBindingCreateInVO roleBindingCreateInVO) {
        this.createRoleBindingsProcess(clientId, roleName, roleBindingCreateInVO, RoleBindTypeEnum.TAG.getValue());
        return null;
    }


    @Auditable(eventType = AuditEventType.APP, eventSubtype = AuditEventSubtype.NEW_ORG_ROLE_BINDING,
            fields = "targetId=#{#clientId};;" +
                    "targetName=#{#clientId};;" +
                    "parameters=#{T(com.cyberscraft.uep.iam.service.impl.RoleBindingServiceImpl).toAuditLogParameters(#clientId, #roleName,#roleBindingCreateInVO)}")
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Void createOrgRoleBinding(String clientId, String roleName, RoleBindingCreateInVO roleBindingCreateInVO) {
        this.createRoleBindingsProcess(clientId, roleName, roleBindingCreateInVO, RoleBindTypeEnum.ORG.getValue());
        return null;
    }


    /****************************************************************更新******************************************************************************/
    @Auditable(eventType = AuditEventType.APP, eventSubtype = AuditEventSubtype.UPDATE_USER_ROLE_BINDING,
            fields = "targetId=#{#clientId};;" +
                    "targetName=#{#clientId};;" +
                    "parameters=#{T(com.cyberscraft.uep.iam.service.impl.RoleBindingServiceImpl).toAuditLogParameters(#username,#clientId, #roleName,#roleBindingUpdateInVO)}")
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Void updateUserRoleBinding(String username, String clientId, String roleName, RoleBindingUpdateInVO roleBindingUpdateInVO) {
        updateRoleBindingsProcess(username, clientId, roleName, roleBindingUpdateInVO, RoleBindTypeEnum.USER.getValue());
        return null;
    }


    @Auditable(eventType = AuditEventType.APP, eventSubtype = AuditEventSubtype.UPDATE_TAG_ROLE_BINDING,
            fields = "targetId=#{#clientId};;" +
                    "targetName=#{#clientId};;" +
                    "parameters=#{T(com.cyberscraft.uep.iam.service.impl.RoleBindingServiceImpl).toAuditLogParameters(#tagName,#clientId, #roleName,#roleBindingUpdateInVO)}")
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Void updateTagRoleBinding(String tagName, String clientId, String roleName, RoleBindingUpdateInVO roleBindingUpdateInVO) {
        updateRoleBindingsProcess(tagName, clientId, roleName, roleBindingUpdateInVO, RoleBindTypeEnum.TAG.getValue());
        return null;
    }


    @Auditable(eventType = AuditEventType.APP, eventSubtype = AuditEventSubtype.UPDATE_ORG_ROLE_BINDING,
            fields = "targetId=#{#clientId};;" +
                    "targetName=#{#clientId};;" +
                    "parameters=#{T(com.cyberscraft.uep.iam.service.impl.RoleBindingServiceImpl).toAuditLogParameters(#orgId,#clientId, #roleName,#roleBindingUpdateInVO)}")
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Void updateOrgRoleBinding(String orgId, String clientId, String roleName, RoleBindingUpdateInVO roleBindingUpdateInVO) {
        updateRoleBindingsProcess(orgId, clientId, roleName, roleBindingUpdateInVO, RoleBindTypeEnum.ORG.getValue());
        return null;
    }


    /****************************************************************删除*************************************************************************/
    @Auditable(eventType = AuditEventType.APP, eventSubtype = AuditEventSubtype.DELETE_USER_ROLE_BINDING,
            fields = "targetId=#{#clientId};;" +
                    "targetName=#{#clientId};;" +
                    "parameters=#{T(com.cyberscraft.uep.iam.service.impl.RoleBindingServiceImpl).toAuditLogParameters(#clientId, #roleName,#username)}")
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Void deleteUserRoleBindings(String clientId, String roleName, String username) {
        if (username.equals(SecureRequestCheck.getUsername())) {
            throw new UserCenterException(ROLE_BINDING_SELF_DELETE_NOT_ALLOWED_ERROR);
        }
        deleteRoleBindingsProcess(username, clientId, roleName, RoleBindTypeEnum.USER.getValue());
        return null;
    }


    @Auditable(eventType = AuditEventType.APP, eventSubtype = AuditEventSubtype.DELETE_TAG_ROLE_BINDING,
            fields = "targetId=#{#clientId};;" +
                    "targetName=#{#clientId};;" +
                    "parameters=#{T(com.cyberscraft.uep.iam.service.impl.RoleBindingServiceImpl).toAuditLogParameters(#clientId, #roleName,#tagName)}")
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Void deleteTagRoleBindings(String clientId, String roleName, String tagName) {
        deleteRoleBindingsProcess(tagName, clientId, roleName, RoleBindTypeEnum.TAG.getValue());
        return null;
    }


    @Auditable(eventType = AuditEventType.APP, eventSubtype = AuditEventSubtype.DELETE_ORG_ROLE_BINDING,
            fields = "targetId=#{#clientId};;" +
                    "targetName=#{#clientId};;" +
                    "parameters=#{T(com.cyberscraft.uep.iam.service.impl.RoleBindingServiceImpl).toAuditLogParameters(#clientId, #roleName,#orgId)}")
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Void deleteOrgRoleBindings(String clientId, String roleName, String orgId) {
        deleteRoleBindingsProcess(orgId, clientId, roleName, RoleBindTypeEnum.ORG.getValue());
        return null;
    }


    /****************************************************************按ID查询******************************************************************/
    @Override
    public List<RoleBindingVO> getUserRoleBindings(String username, String clientId, Set<String> scopes) {
        UserEntity userEntity = checkUserName(username);
        return getRoleBindingDetail(clientId, RoleBindTypeEnum.USER.getValue(), userEntity.getId());
    }

    @Override
    public List<AppRoleEntity> getUserRoleBindingsV2(String username, String clientId, Long scope) {
        UserEntity userEntity = checkUserName(username);
        return getRoleBindingDetail(clientId, RoleBindTypeEnum.USER.getValue(), userEntity.getId(), scope);
    }

    private List<AppRoleEntity> getRoleBindingDetail(String clientId, Integer targetType, Long targetValue, Long scope) {
        AppEntity appEntity = iAppService.clientMustExistsAndNotCli(clientId);
        List<AppRoleEntity> appRoleEntities = this.appRoleDao.queryAppRoleByScope(targetValue, appEntity.getId(), targetType, scope);
        return appRoleEntities;
    }

    @Override
    public Boolean checkAppRoleUtoExist(String clientId, String roleName, String username) {
        UserEntity userEntity = checkUserName(username);
        AppEntity appEntity = iAppService.clientMustExists(clientId);
        AppRoleEntity roleEntity = this.iRoleService.checkRoleNameNotExist(clientId, roleName, appEntity.getId());
        AppRoleUtoEntity appRoleUtoEntity = this.appRoleUtoDBO.getOne(appEntity.getId(), roleEntity.getId(), RoleBindTypeEnum.USER.getValue(), userEntity.getId());
        return appRoleUtoEntity != null;
    }

    @Override
    public List<RoleBindingVO> getTagRoleBindings(String tagName, String clientId, Set<String> scopes) {
        TagEntity tagEntity = checkTagName(tagName);
        return getRoleBindingDetail(clientId, RoleBindTypeEnum.TAG.getValue(), tagEntity.getId());
    }


    @Override
    public List<RoleBindingVO> getOrgRoleBindings(String orgId, String clientId, Set<String> scopes) {
        OrgEntity orgEntity = this.checkOrg(orgId);
        return getRoleBindingDetail(clientId, RoleBindTypeEnum.ORG.getValue(), orgEntity.getId());
    }

    @Override
    public List<RoleBindingVO> roleBingingList(String username, String clientId) {
        AppEntity appEntity = iAppService.clientMustExistsAndNotCli(clientId);
        List<AppRoleUtoEntity> appRoleUtoEntityList = roleBingingList(username, appEntity);
        return appRoleUtoEntityToVO(appRoleUtoEntityList, appEntity);
    }

    @Override
    public List<AppRoleUtoEntity> roleBingingList(String username, AppEntity appEntity) {
        UserEntity userEntity = checkUserName(username);
        List<OrgEntity> orgEntityList = orgDBO.getUserOrgList(Stream.of(userEntity.getId()).collect(Collectors.toSet()));
        List<TagEntity> tagEntityList = tagDBO.getUserTagList(userEntity.getId());

        Set<Long> targetSet = orgEntityList.stream().map(OrgEntity::getId).collect(Collectors.toSet());
        targetSet.addAll(tagEntityList.stream().map(TagEntity::getId).collect(Collectors.toSet()));
        targetSet.add(userEntity.getId());

        return appRoleUtoDBO.getListWithTargetAndApp(targetSet, appEntity.getId());
    }

    @Override
    public AuditRoleType getUserRoleType(String username) {
        List<RoleBindingVO> list = getUserRoleBindings(username, DaoConstants.CLIENT_UC_CONSOLE, null);
        List<String> roleNameList = list.stream().map(rb -> rb.getRole().getName()).collect(Collectors.toList());
        roleNameList.remove(DaoConstants.IAM_ROLE_USER);
        // 如果集合为空则代表该用户只有ROLE_USER这一种身份，为普通用户
        return CollectionUtils.isEmpty(roleNameList) ? AuditRoleType.ROLE_USER : AuditRoleType.ROLE_ADMIN;
    }


    /****************************************************************列表查询*****************************************************************/
    @Override
    public QueryPage<UserRoleBindingVO> searchUsers(String clientId, String roleName, String filterStr,
                                                    Integer page, Integer size, String[] requestVOAttrs, String searchField) {
        AppEntity appEntity = iAppService.clientMustExistsAndNotCli(clientId);
        AppRoleEntity roleEntity = iRoleService.checkRoleNameNotExist(clientId, roleName, appEntity.getId());
        QueryPage<Map<String, Object>> queryPageVo = new QueryPage(page, size, null);
        QueryPage<Map<String, Object>> queryPageResult = this.appRoleUtoDBO.getListWithUserExceptAdmin(searchField, filterStr, appEntity.getId(), roleEntity.getId(), queryPageVo, requestVOAttrs);
        // 掩码用户敏感信息
        UserUtil.maskListMapFields(queryPageResult.getItems());
        return this.roleBindingTransfer.entityPageToVoPage(queryPageResult);

    }

    @Override
    public List<UserRoleBindingVO> searchUsersByUcRoleName(String roleName) {
        AppRoleEntity roleEntity = iRoleService.checkRoleNameNotExist(DaoConstants.CLIENT_UC_CONSOLE, roleName, DaoConstants.APP_ID_UC);
        List<Map<String, Object>> listWithUser = this.appRoleUtoDBO.getListWithUser(DaoConstants.APP_ID_UC, roleEntity.getId());
        List<UserRoleBindingVO> userRoleBindingVOS = this.roleBindingTransfer.mapToListVo(listWithUser);
        return userRoleBindingVOS;
    }

    @Override
    public List<Long> searchUserBindingScope() {
        // 审批完成用户创建的时候会出现 SecureRequestCheck.getAuthentication() 为空的情况
        if (SecureRequestCheck.getAuthentication() != null && SecureRequestCheck.getAuthentication().getPrincipal() instanceof UserInfo) {
            UserInfo userInfo = (UserInfo) SecureRequestCheck.getAuthentication().getPrincipal();
            String clientId = SecureRequestCheck.getClientId();
            AppEntity appEntity = iAppService.clientMustExists(clientId);
            String sub = userInfo.getSub();
            AppRoleUtoEntity appRoleUtoEntity = new AppRoleUtoEntity();
            appRoleUtoEntity.setAppRefId(appEntity.getId());
            appRoleUtoEntity.setTarget(Long.valueOf(sub));
            List<AppRoleUtoEntity> list = this.appRoleUtoDBO.getList(appRoleUtoEntity);
            List<Long> roles = list.stream().map(AppRoleUtoEntity::getRoleRefId).collect(Collectors.toList());
            List<Long> result = new ArrayList<>();
            for (Long role : roles) {
                RoleVO roleOnly = iRoleService.getRoleOnly(appEntity.getId(), role);
                Set<String> bindingScope = roleOnly.getBindingScopes();
                if (CollectionUtils.isNotEmpty(bindingScope)) {
                    result.addAll(bindingScope.stream().map(OrgUtil::orgIdConver).collect(Collectors.toList()));
                } else {
                    return new ArrayList<>();
                }
            }
            return result;
        } else {
            return Collections.EMPTY_LIST;
        }
    }

    @Override
    public Boolean userIncludeAdminPermission() {
        String clientId = SecureRequestCheck.getClientId();
        AppEntity appEntity = iAppService.clientMustExists(clientId);
        UserInfo userInfo = (UserInfo) SecureRequestCheck.getAuthentication().getPrincipal();
        String sub = userInfo.getSub();
        AppRoleUtoEntity appRoleUtoEntity = new AppRoleUtoEntity();
        appRoleUtoEntity.setAppRefId(appEntity.getId());
        appRoleUtoEntity.setTarget(Long.valueOf(sub));
        List<AppRoleUtoEntity> list = this.appRoleUtoDBO.getList(appRoleUtoEntity);
        List<Long> roles = list.stream().map(AppRoleUtoEntity::getRoleRefId).collect(Collectors.toList());
        return roles.contains(30001L);
    }


    @Override
    public List<TagRoleBindingVO> searchTags(String clientId, String roleName) {
        AppEntity appEntity = iAppService.clientMustExistsAndNotInternalAndNotCli(clientId);
        AppRoleEntity roleEntity = iRoleService.checkRoleNameNotExist(clientId, roleName, appEntity.getId());

        AppRoleUtoEntity queryEntity = new AppRoleUtoEntity();
        queryEntity.setAppRefId(appEntity.getId());
        queryEntity.setRoleRefId(roleEntity.getId());
        queryEntity.setTargetType(RoleBindTypeEnum.TAG.getValue());

        List<AppRoleUtoEntity> roleUtoEntityList = this.appRoleUtoDBO.getListWithTag(appEntity.getId(), roleEntity.getId());
        List<TagRoleBindingVO> resultList = new ArrayList<>();
        TagRoleBindingVO tagRoleBindingVO;
        Set<RoleBindingScopeInfoVO> roleBindingScopeInfoVOSet;
        for (AppRoleUtoEntity temp : roleUtoEntityList) {
            roleBindingScopeInfoVOSet = new HashSet<>();
            tagRoleBindingVO = new TagRoleBindingVO();

            tagRoleBindingVO.setTagName(temp.getName());

            String bindingScope = temp.getBindingScopes();
            if (bindingScope != null && !"".equals(bindingScope)) {
                RoleBindingScopeInfoVO roleBindingScopeInfoVO;
                String arrayBindingScope[] = bindingScope.split(DaoConstants.COLLECTION_FIELD_SEPARATOR);
                for (String scope : arrayBindingScope) {
                    roleBindingScopeInfoVO = new RoleBindingScopeInfoVO();
                    roleBindingScopeInfoVO.setScope(scope);
                    roleBindingScopeInfoVOSet.add(roleBindingScopeInfoVO);
                }
                tagRoleBindingVO.setBindingScopes(roleBindingScopeInfoVOSet);
            }
            resultList.add(tagRoleBindingVO);
        }

        return resultList;
    }


    @Override
    public List<OrgRoleBindingVO> searchOrgs(String clientId, String roleName) {
        AppEntity appEntity = iAppService.clientMustExistsAndNotInternalAndNotCli(clientId);
        AppRoleEntity roleEntity = iRoleService.checkRoleNameNotExist(clientId, roleName, appEntity.getId());

        AppRoleUtoEntity queryEntity = new AppRoleUtoEntity();
        queryEntity.setAppRefId(appEntity.getId());
        queryEntity.setRoleRefId(roleEntity.getId());
        queryEntity.setTargetType(RoleBindTypeEnum.ORG.getValue());
        List<AppRoleUtoEntity> roleUtoEntityList = this.appRoleUtoDBO.getListWithOrg(appEntity.getId(), roleEntity.getId());

        List<OrgRoleBindingVO> resultList = new ArrayList<>();
        OrgRoleBindingVO orgRoleBindingVO;
        OrgBasicInfoOutVO orgBasicInfoOutVO;
        Set<RoleBindingScopeInfoVO> roleBindingScopeInfoVOSet;
        for (AppRoleUtoEntity temp : roleUtoEntityList) {
            roleBindingScopeInfoVOSet = new HashSet<>();
            orgRoleBindingVO = new OrgRoleBindingVO();
            orgBasicInfoOutVO = new OrgBasicInfoOutVO();

            orgBasicInfoOutVO.setName(temp.getName());
            orgBasicInfoOutVO.setId(temp.getTarget() + "");

            orgRoleBindingVO.setOrg(orgBasicInfoOutVO);

            String bindingScope = temp.getBindingScopes();
            if (bindingScope != null && !"".equals(bindingScope)) {
                RoleBindingScopeInfoVO roleBindingScopeInfoVO;
                String arrayBindingScope[] = bindingScope.split(DaoConstants.COLLECTION_FIELD_SEPARATOR);
                for (String scope : arrayBindingScope) {
                    roleBindingScopeInfoVO = new RoleBindingScopeInfoVO();
                    roleBindingScopeInfoVO.setScope(scope);
                    roleBindingScopeInfoVOSet.add(roleBindingScopeInfoVO);
                }

                orgRoleBindingVO.setBindingScopes(roleBindingScopeInfoVOSet);
            }
            resultList.add(orgRoleBindingVO);
        }

        return resultList;
    }

    @Override
    public List<String> searchOrgPermissions(Long orgId) {
        Authentication authentication = SecureRequestCheck.getAuthentication();
        if (!OAuth2ExpressionUtils.isOAuthClientAuth(authentication)) {
            String username = SecureRequestCheck.getUsername();
            String clientId = SecureRequestCheck.getClientId();
            OrgEntity orgEntity = orgDBO.getById(orgId, LocalOrgAttr.id.getDomainName(), LocalOrgAttr.org_path.getDomainName());
            List<RoleBindingVO> roleBindings = getUserRoleBindings(username, clientId, null);
            List<RoleVO> collect = roleBindings.stream().map(RoleBindingVO::getRole).collect(Collectors.toList());
            List<String> permissions = new ArrayList<>();
            for (RoleVO roleVO : collect) {
                if (CollectionUtils.isNotEmpty(roleVO.getBindingScopes())) {
                    for (String bindingScope : roleVO.getBindingScopes()) {
                        if (orgEntity.getOrgPath().indexOf(bindingScope) > 0) {
                            RoleVO role = iRoleService.getRole(clientId, roleVO.getName());
                            permissions.addAll(role.getPermissionSets());
                            break;
                        }
                    }
                }
            }
            return permissions;
        } else {
            return Collections.emptyList();
        }
    }


    /********************************************************内部方法*********************************************************/
    private void validateRoleBindingScopes(String clientId, Set<String> passedInScopes) {
        if (!AppUtil.isInternalClient(clientId)) {
            //For 3rd app, we don't do anything to validate scopes
            return;
        }

        if (passedInScopes == null || passedInScopes.size() == 0) {
            return;
        }

        for (String orgId : passedInScopes) {
            if (OrgUtil.isRoot(orgId)) {
                String errorMessage = String.format("root org cannot modift");
                logger.error(errorMessage);
                throw new OrganizationException(ORG_ROOT_CANNOT_MODIFY);
            }
        }
    }


    /**
     * 创建角色绑定逻辑
     *
     * @param clientId
     * @param roleName
     * @param roleBindingCreateInVO
     * @param targetType
     */
    private void createRoleBindingsProcess(String clientId, String roleName, RoleBindingCreateInVO roleBindingCreateInVO, Integer targetType) {
        AppUtil.validateUcRole(clientId, roleName);
        AppEntity appEntity = iAppService.clientMustExistsAndNotCli(clientId);
        AppRoleEntity roleEntity = this.iRoleService.checkRoleNameNotExist(clientId, roleName, appEntity.getId());

        List<AppRoleUtoEntity> targetResultList = new ArrayList<>();
        AppRoleUtoEntity appRoleUtoEntity;
        Long targetValue;


        for (String targetName : roleBindingCreateInVO.getTargets()) {
            appRoleUtoEntity = new AppRoleUtoEntity();
            appRoleUtoEntity.setTargetType(targetType);
            appRoleUtoEntity.setAppRefId(appEntity.getId());
            appRoleUtoEntity.setRoleRefId(roleEntity.getId());
            appRoleUtoEntity.setCreateMode(CreatedMode.BY_ADMIN.getValue());
            appRoleUtoEntity.setCreateBy(SecureRequestCheck.getUsername());
            appRoleUtoEntity.setCreateTime(LocalDateTime.now());
            appRoleUtoEntity.setUpdateBy(SecureRequestCheck.getUsername());
            appRoleUtoEntity.setUpdateTime(LocalDateTime.now());


            if (RoleBindTypeEnum.USER.getValue() == targetType) {
                UserEntity userEntity = checkUserName(targetName);
                UserOrgEntity userOrgEntity = new UserOrgEntity();
                userOrgEntity.setUid(userEntity.getId());
                List<UserOrgEntity> list = userOrgDBO.getList(userOrgEntity);
                List<Long> collect = list.stream().map(UserOrgEntity::getOrgRefId).collect(Collectors.toList());
                targetValue = userEntity.getId();

            } else if (RoleBindTypeEnum.ORG.getValue() == targetType) {
                OrgEntity orgEntity = checkOrg(targetName);
                targetValue = orgEntity.getId();

            } else if (RoleBindTypeEnum.TAG.getValue() == targetType) {
                TagEntity tagEntity = this.checkTagName(targetName);
                targetValue = tagEntity.getId();

            } else {
                String errorMessage = String.format("the targettype can not support %s", targetType);
                logger.error(errorMessage);
                throw new UserCenterException(HttpStatus.BAD_REQUEST, INVALID_REQUEST_PARAM, errorMessage);
            }

            if (checkAppRoleUtoExist(clientId, roleName, targetName, appEntity.getId(), roleEntity.getId(), targetValue, targetType)) {
                continue;
            }

            appRoleUtoEntity.setTarget(targetValue);
            targetResultList.add(appRoleUtoEntity);
        }

        this.appRoleUtoDBO.saveBatch(targetResultList);
    }


    /**
     * 修改角色绑定逻辑
     *
     * @param targetName
     * @param clientId
     * @param roleName
     * @param roleBindingUpdateInVO
     * @param targetType
     */
    private void updateRoleBindingsProcess(String targetName, String clientId, String roleName,
                                           RoleBindingUpdateInVO roleBindingUpdateInVO, Integer targetType) {
        AppUtil.validateUcRole(clientId, roleName);
        AppEntity appEntity = iAppService.clientMustExistsAndNotCli(clientId);
        AppRoleEntity roleEntity = this.iRoleService.checkRoleNameNotExist(clientId, roleName, appEntity.getId());
        AppRoleUtoEntity updateEntity;
        Long targetValue;


        if (RoleBindTypeEnum.USER.getValue() == targetType) {
            UserEntity userEntity = checkUserName(targetName);
            targetValue = userEntity.getId();

        } else if (RoleBindTypeEnum.ORG.getValue() == targetType) {
            OrgEntity orgEntity = checkOrg(targetName);
            targetValue = orgEntity.getId();

        } else if (RoleBindTypeEnum.TAG.getValue() == targetType) {
            TagEntity tagEntity = checkTagName(targetName);
            targetValue = tagEntity.getId();

        } else {
            String errorMessage = String.format("the targettype can not support %s", targetType);
            logger.error(errorMessage);
            throw new UserCenterException(HttpStatus.BAD_REQUEST, INVALID_REQUEST_PARAM, errorMessage);
        }

        updateEntity = getUpdateRoleUtoEntity(targetName, clientId, roleName, roleBindingUpdateInVO.getBindingScopes(),
                targetType, appEntity.getId(), roleEntity.getId(), targetValue);
        updateEntity.setUpdateBy(UserUtil.getLoginUserName());
        updateEntity.setUpdateTime(LocalDateTime.now());
        this.update(updateEntity);
    }


    /**
     * 删除角色绑定逻辑
     *
     * @param target     客户端传过来的标识名称，如组织ID，用户名称，标签名称，需要转换成数据库的ID
     * @param clientId   角色的应用ID
     * @param roleName   角色名称
     * @param targetType 绑定类型
     */
    private void deleteRoleBindingsProcess(String target, String clientId, String roleName, Integer targetType) {
        AppEntity appEntity = iAppService.clientMustExistsAndNotCli(clientId);
        AppRoleEntity roleEntity = this.iRoleService.checkRoleNameNotExist(clientId, roleName, appEntity.getId());
        Long targetValue;

        if (RoleBindTypeEnum.USER.getValue() == targetType) {
            UserEntity userEntity = checkUserName(target);
            targetValue = userEntity.getId();

        } else if (RoleBindTypeEnum.ORG.getValue() == targetType) {
            OrgEntity orgEntity = checkOrg(target);
            targetValue = orgEntity.getId();

        } else if (RoleBindTypeEnum.TAG.getValue() == targetType) {
            TagEntity tagEntity = this.checkTagName(target);
            targetValue = tagEntity.getId();

        } else {
            String errorMessage = String.format("the targettype can not support %s", targetType);
            logger.error(errorMessage);
            throw new UserCenterException(HttpStatus.BAD_REQUEST, INVALID_REQUEST_PARAM, errorMessage);
        }

        AppRoleUtoEntity appRoleUtoEntity = checkAppRoleUtoNotExist(clientId, roleName, target, appEntity.getId(), roleEntity.getId(), targetValue, targetType);
        this.delete(appRoleUtoEntity);
    }


    /**
     * 对于绑定标签和用户使用
     * 检查角色绑定的操作范围
     *
     * @param target
     * @param clientId
     * @param roleName
     * @param bindingScopesSets
     * @param targetType
     * @param appId
     * @param roleId
     * @param targetValue
     * @return
     */
    private AppRoleUtoEntity getUpdateRoleUtoEntity(String target, String clientId, String roleName, Set<String> bindingScopesSets,
                                                    Integer targetType, Long appId, Long roleId, Long targetValue) {
        AppRoleUtoEntity updateRoleUtoEntity = checkAppRoleUtoNotExist(clientId, roleName, target, appId, roleId, targetValue, targetType);

        if (bindingScopesSets == null || bindingScopesSets.size() == 0) {
            updateRoleUtoEntity.setBindingScopes("");
        } else {
            for (String orgId : bindingScopesSets) {
                //check orgid
                checkOrg(orgId);
            }
            updateRoleUtoEntity.setBindingScopes(String.join(DaoConstants.COLLECTION_FIELD_SEPARATOR, bindingScopesSets));
        }
        return updateRoleUtoEntity;
    }


    /**
     * 检查用户名称，如果为空，则抛异常
     *
     * @param username
     * @return
     */
    private UserEntity checkUserName(String username) {
        UserEntity userEntity = this.userDBO.getOneByUsername(username, LocalUserAttr.sub.getFieldName());
        if (userEntity == null) {
            String errorMessage = String.format("cound not find the user %s", username);
            logger.error(errorMessage);
            throw new UserCenterException(HttpStatus.BAD_REQUEST, USER_NOT_FOUND, errorMessage);
        }
        return userEntity;
    }


    /**
     * 检查角色绑定关系，如果没有绑定，则抛异常
     *
     * @param clientId
     * @param roleName
     * @param targetName
     * @param appId
     * @param roldId
     * @param targetValue
     * @param targetType
     * @return
     */
    private AppRoleUtoEntity checkAppRoleUtoNotExist(String clientId, String roleName, String targetName, Long appId,
                                                     Long roldId, Long targetValue, Integer targetType) {
        AppRoleUtoEntity appRoleUtoEntity = this.appRoleUtoDBO.getOne(appId, roldId, targetType, targetValue);
        if (appRoleUtoEntity == null) {
            String errorMessage = String.format("Role Binding not found,target type: %s,target:  %s,client:%s,role:%s",
                    targetType, targetName, clientId, roleName);
            logger.error(errorMessage);
            throw new UserCenterException(HttpStatus.BAD_REQUEST, ROLE_BINDING_NOT_FOUND_ERROR, errorMessage);
        }
        return appRoleUtoEntity;
    }

    /**
     * 判断角色是否已经与target绑定
     *
     * @param clientId
     * @param roleName
     * @param targetName
     * @param appId
     * @param roldId
     * @param targetValue
     * @param targetType
     * @return
     */
    private boolean checkAppRoleUtoExist(String clientId, String roleName, String targetName, Long appId, Long roldId,
                                         Long targetValue, Integer targetType) {
        AppRoleUtoEntity appRoleUtoEntity = this.appRoleUtoDBO.getOne(appId, roldId, targetType, targetValue);
        if (appRoleUtoEntity != null) {
            String errorMessage = String.format("%s %s already assigned client %s role %s , will skip it", RoleBindTypeEnum.getName(targetType),
                    targetName, clientId, roleName);
            logger.error(errorMessage);
            return true;
        }
        return false;
    }

    /**
     * 修改角色绑定
     * 1、系统创建的不能修改
     *
     * @param appRoleUtoEntity
     */
    private void update(AppRoleUtoEntity appRoleUtoEntity) {
        if (CreatedMode.BY_SYSTEM.getValue() == appRoleUtoEntity.getCreateMode()) {
            String errorMessage =
                    String.format("Can not update system created Role Binding");
            logger.error(errorMessage);
            throw new UserCenterException(HttpStatus.BAD_REQUEST, INVALID_REQUEST_PARAM, errorMessage);
        }
        this.appRoleUtoDBO.updateById(appRoleUtoEntity);
    }

    /**
     * 删除角色绑定
     * 1、系统创建的不能删
     *
     * @param appRoleUtoEntity
     */
    private void delete(AppRoleUtoEntity appRoleUtoEntity) {
        if (CreatedMode.BY_SYSTEM.getValue() == appRoleUtoEntity.getCreateMode()) {
            String errorMessage =
                    String.format("Can not delete system created Role Binding");
            logger.error(errorMessage);
            throw new UserCenterException(HttpStatus.BAD_REQUEST, INVALID_REQUEST_PARAM, errorMessage);
        }
        this.appRoleUtoDBO.removeById(appRoleUtoEntity);
    }

    /**
     * 组织ID检查
     * 1、不能是根组织
     * 2、不能为空
     *
     * @param orgId
     * @return
     */
    private OrgEntity checkOrg(String orgId) {
        if (OrgUtil.isRoot(orgId)) {
            throw new AppException(HttpStatus.BAD_REQUEST, ORG_ROOT_CANNOT_MODIFY);
        }

        OrgEntity orgEntity = this.orgDBO.getById(OrgUtil.orgIdConver(orgId));
        if (orgEntity == null) {
            String errorMessage =
                    String.format(" org %s can not found", orgId);
            logger.error(errorMessage);
            throw new UserCenterException(HttpStatus.BAD_REQUEST, ORG_NOT_FOUND, errorMessage);
        }
        return orgEntity;
    }


    /**
     * 检查用户名称，如果为空，则抛异常
     *
     * @param tagName
     * @return
     */
    private TagEntity checkTagName(String tagName) {
        TagEntity tagEntity = this.tagDBO.getOneByName(tagName);
        if (tagEntity == null) {
            String errorMessage = String.format("cound not find the tag %s", tagName);
            logger.error(errorMessage);
            throw new UserCenterException(HttpStatus.BAD_REQUEST, TAG_NOT_FOUND_ERROR, errorMessage);
        }
        return tagEntity;
    }

    /**
     * 获取某个用户/组织/标签的角色绑定关系
     *
     * @param clientId
     * @param targetType
     * @param targetValue
     * @return
     */
    private List<RoleBindingVO> getRoleBindingDetail(String clientId, Integer targetType, Long targetValue) {
        AppEntity appEntity = iAppService.clientMustExistsAndNotCli(clientId);
        AppRoleUtoEntity queryRoleUtoEntity = new AppRoleUtoEntity();
        queryRoleUtoEntity.setAppRefId(appEntity.getId());
        queryRoleUtoEntity.setTargetType(targetType);
        queryRoleUtoEntity.setTarget(targetValue);

        List<AppRoleUtoEntity> appRoleUtoEntityList = this.appRoleUtoDBO.getList(queryRoleUtoEntity);

        return appRoleUtoEntityToVO(appRoleUtoEntityList, appEntity);
    }

    private List<RoleBindingVO> appRoleUtoEntityToVO(List<AppRoleUtoEntity> appRoleUtoEntityList, AppEntity appEntity) {

        List<RoleBindingVO> resultList = new ArrayList<>();
        RoleBindingVO roleBindingVO;
        Set<RoleBindingScopeInfoVO> roleBindingScopeInfoVOSet;
        for (AppRoleUtoEntity temp : appRoleUtoEntityList) {
            String bindingScope = temp.getBindingScopes();
            roleBindingScopeInfoVOSet = new HashSet<>();
            roleBindingVO = new RoleBindingVO();


            if (bindingScope != null && !"".equals(bindingScope)) {
                RoleBindingScopeInfoVO roleBindingScopeInfoVO;
                String arrayBindingScope[] = bindingScope.split(DaoConstants.COLLECTION_FIELD_SEPARATOR);
                for (String scope : arrayBindingScope) {
                    roleBindingScopeInfoVO = new RoleBindingScopeInfoVO();
                    roleBindingScopeInfoVO.setScope(scope);
                    roleBindingScopeInfoVOSet.add(roleBindingScopeInfoVO);
                }
            }

            roleBindingVO.setBindingScopes(roleBindingScopeInfoVOSet);
            roleBindingVO.setRole(this.iRoleService.getRoleOnly(appEntity.getId(), temp.getRoleRefId()));

            resultList.add(roleBindingVO);
        }
        return resultList;
    }


}
