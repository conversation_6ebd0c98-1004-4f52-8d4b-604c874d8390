package com.cyberscraft.uep.iam.service.oidc.util;

import com.cyberscraft.uep.iam.constants.CommonConstants;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * Created by xifeng on 7/10/17.
 */
public interface OIDCConstants {

    // redirect uri for uc console debug mode, which is using client_id of usercenter
    String UC_CONSOLE_DEBUG_URL = "http://ui.nscloud.local:8000";

    String UC_CONSOLE_DEBUG_REDIRECT_URI = UC_CONSOLE_DEBUG_URL + "/index.html#/";

    String TC_CONSOLE_DEBUG_URL = "http://ui.nscloud.local:7000";
    String TC_CONSOLE_DEBUG_REDIRECT_URI = TC_CONSOLE_DEBUG_URL + "/index.html#/";

    String UC_CONSOLE_CLIENT_NAME = "usercenter-console";

    String UC_CONSOLE_DEBUG_COOKIE_DOMAIN = "nscloud.local";

    // grant type
    String GRANT_TYPE_IMPLICIT = "implicit";
    String GRANT_TYPE_CODE = "authorization_code";
    String GRANT_TYPE_CLIENT = "client_credentials";
    String GRANT_TYPE_PASSWORD = "password";
    String GRANT_TYPE_REFRESH_TOKEN = "refresh_token";
    String GRANT_TYPE_DEVICE_CODE = CommonConstants.GRANT_TYPE_DEVICE_CODE;
    //使用第三方登录时的grantType
    String GRANT_TYPE_SNS_CODE = "sns_authorization_code";
    //使用STS时的grantType
    String GRANT_TYPE_STS = "urn:ietf:params:oauth:grant-type:token-exchange";

    String GRANTTYPES_SEP = ",";

    //使用第三方登录时，IAM接收的授权码参数名称
    String SNS_CODE_PARAM_NAME ="code";
    //授权码提供方参数名称
    String CODE_PROVIDER_PARAM_NAME ="code_provider";
    //token接口，认证源ID的参数名称
    String PARAM_AUTH_ID ="auth_id";
    //token接口，IAM应用ID的参数名称
    String PARAM_CLIENT_ID ="client_id";
    String PARAM_CLIENT_SECERT ="client_secret";
    //token接口，IAM应用ID的参数名称

    //token sts流程中，用户的参数名称
    String STS_PARAM_USERNAME ="username";
    //token sts流程中，第三方应用ID的参数名称
    String STS_PARAM_THRID_CLIENT_ID ="audience";
    //token sts流程中，原token的参数名称
    String STS_PARAM_TOKEN ="subject_token";
    //token sts流程中，token的类型
    String STS_PARAM_TOKEN_TYPE ="subject_token_type";
    String STS_PARAM_TOKEN_TYPE_VALUE = "urn:ietf:params:oauth:token-type:access_token";

    String SCOPE_CLIENT_DELEGATION_AUTH = "audience:client:client_id:";
    String SCOPE_SERVER_DELEGATION_AUTH = "audience:server:client_id:";
    String CERT_USER_VALID_PARAM_NAME = "nonce_with_cert";


    String USER_PRINCIPLE = "user_principle";
    String RESPONSE_TYPE = "response_type";

    /**
     * 表示需要进行代理验证的clientId
     */
    String AUTH_FOR = "af";

    /**
     * prompt参数，支持consent、login
     * consent：认证提示
     * login：重新认证
     */
    String AUTH_PROMPT = "prompt";

    List<String> GRANT_TYPES = Collections.unmodifiableList(Arrays
            .asList(OIDCConstants.GRANT_TYPE_CODE, OIDCConstants.GRANT_TYPE_CLIENT,
                    OIDCConstants.GRANT_TYPE_REFRESH_TOKEN, OIDCConstants.GRANT_TYPE_PASSWORD,
                    OIDCConstants.GRANT_TYPE_IMPLICIT));
    String SID_COOKIE = "SID";

    // set a larger value to make it looks like never expire.
//    int SID_MAX_AGE = (int) TimeUnit.HOURS.toSeconds(1);

    String SSO_COOKIE = "UCSSO";

//    int SSO_MAX_AGE = (int) TimeUnit.DAYS.toSeconds(7);
    String ACCESS_TOKEN = "access_token";
    String REFRESH_TOKEN = "refresh_token";
    String GRANT_TYPE = "grant_type";
    String ID_TOKEN= "id_token";
    String TOKEN_TYPE = "token_type";
    String AUTH2_RESPONSE_TYPE_TOKEN = "token";
    String AUTH2_RESPONSE_TYPE_CODE = "code";
    String AUTH2_PARAM_REMEMBER_GRANT = "remember_grant";
    String AUTHORIZATION_REQUEST= "authorizationRequest";
    String AUTHORIZEURL = "authorizeURL";
    String EXPIRE_IN = "expires_in";
    String EXTRA_= "extra_";

    String AUTH2_PARAM_SCOPE = "scope";
    String AUTH2_PARAM_CLIENT_ID = "clientId";

    String SCOPE_OIDC_OPENID = "openid";
    String SCOPE_OIDC_PROFILE = "profile";
    String SCOPE_OIDC_ADDRESS = "address";
    String SCOPE_OIDC_EMAIL = "email"; // This scope value requests access to the email and email_verified Claims.
    String SCOPE_OIDC_PHONE = "phone"; //This scope value requests access to the phone_number and phone_number_verified Claims.
    String SCOPE_OFFLINE_ACCESS = "offline_access";
    String SCOPE_NO_ACCESS = "no_access";

    String SCOPE_SEPARATOR = ",";

    int REFRESH_TOKEN_TIMEOUT_MIN = (int) TimeUnit.HOURS.toSeconds(1);
    String AUTH2_PARAMS_EXT_MAIN_APP_ID = "auth2_params_ext_main_app_id";



}
