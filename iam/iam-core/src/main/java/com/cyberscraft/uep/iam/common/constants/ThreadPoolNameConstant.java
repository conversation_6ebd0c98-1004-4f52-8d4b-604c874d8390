package com.cyberscraft.uep.iam.common.constants;

/***
 * 线程池常量名称定义
 * @date 2021-09-19
 * <AUTHOR>
 ***/
public class ThreadPoolNameConstant {

    /***
     * 通用线程池名称
     */
    public final static String COMMON_POOL_NAME = "commonPoolExecutor";

    /**
     * 同步线程池名称
     */
    public final static String AUDIT_POOL_NAME = "auditPoolExecutor";

    /***
     * 推送数据线程池名称
     */
    public final static String PUSH_DATA_POOL_NAME = "pushDataPoolExecutor";

    /***
     * 推送事件数据线程池名称
     */
    public final static String PUSH_EVENT_DATA_POOL_NAME = "pushEventDataPoolExecutor";

    /***
     *
     */
    public final static String COMMON_TASK_NAME = "commonTaskExecutor";

    /***
     * 用户及组事件线程池名称
     */
    public final static String USERGROUP_EVENT_POOL_NAME = "userGroupEventPoolExcutor";

    /**
     * 定时式连接流线程池名称
     */
    public final static String SCHEDULE_API_FLOW_POOL_NAME = "scheduleFLowExecutor";
}
