package com.cyberscraft.uep.iam.service.connector.event;

import com.cyberscraft.uep.account.client.constant.ThirdPartyEventTagConstant;
import com.cyberscraft.uep.account.client.domain.*;
import com.cyberscraft.uep.account.client.event.IThirdPartyEventExecutor;
import com.cyberscraft.uep.account.client.util.AccountUtil;
import com.cyberscraft.uep.iam.service.connector.IConnectorManageService;
import com.cyberscraft.uep.iam.service.connector.IConnectorService;
import com.cyberscraft.uep.iam.service.user.IUsersSyncService;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;

/***
 * 组织增加修改事件
 * @date 2021/3/6
 * <AUTHOR>
 ***/
@Component
public class GroupAddAndUpdateEventHandler extends AbstractEventHandler implements IThirdPartyEventExecutor {

    @Autowired
    private IConnectorService connectorService;

    @Autowired
    protected IUsersSyncService usersSyncService;

    @Autowired
    private IConnectorManageService connectorManageService;

    @Override
    public Set<String> getSupportedEventTypes() {
        return new HashSet<>(Arrays.asList(ThirdPartyEventTagConstant.GROUP_ADD_UPDATE, ThirdPartyEventTagConstant.GROUP_MOVE));
    }

    @Override
    public <E extends EventBody> void onEvent(ThirdPartyEvent<E> event) {
        if (event == null) {
            LOG.error("GROUP_ADD_UPDATE event: event is null");
            return;
        }
        GroupEventBody eventBody = (GroupEventBody) event.getData();
        TenantHolder.setTenantCode(event.getTenantId());

        try {
            List<Connector> connectors = new ArrayList<>();
            Connector connector0 = eventBody.getConnector();
            if (connector0 == null) {
                Long snsId = eventBody.getSnsConfig().getId();
                connectors = connectorService.getConnectorBySnsId(snsId);
                if (connectors.isEmpty()) {
                    LOG.info("GROUP_ADD_UPDATE event connector is empty");
                    return;
                }
            } else {
                connectors.add(connector0);
            }

            for (Connector connector : connectors) {
                List<ConnectorOrg<String, Object>> connectorOrgs;
                List<Object> groups = eventBody.getGroups();
                if (groups == null) {
                    if (eventBody.getCodes() == null || eventBody.getCodes().isEmpty()) {
                        LOG.error("GROUP_ADD_UPDATE event: no group codes");
                        return;
                    }
                    connectorOrgs = externalConnectService.getExternalOrgByIds(eventBody.getCodes(), connector);
                } else {
                    connectorOrgs = AccountUtil.to(groups, externalConnectService.getExternalOrgFullProfile(connector));
                }

                if (connectorOrgs == null) {
                    LOG.error("GROUP_ADD_UPDATE event: groups not found");
                    return;
                }

                connectorManageService.syncCreateOrUpdateOrg(connector, connectorOrgs);
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }
}
