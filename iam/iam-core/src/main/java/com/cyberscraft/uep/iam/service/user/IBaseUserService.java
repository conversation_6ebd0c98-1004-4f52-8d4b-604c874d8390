package com.cyberscraft.uep.iam.service.user;

import com.cyberscraft.uep.iam.dto.domain.SyncResult;
import com.cyberscraft.uep.iam.entity.ConnectorEntity;
import com.cyberscraft.uep.iam.entity.UserEntity;
import com.cyberscraft.uep.iam.entity.UserOrgEntity;
import com.cyberscraft.uep.iam.dto.enums.BaseServiceOpResult;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 用户相关基础业务接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2019-09-29
 */
public interface IBaseUserService {
    /**
     * 根据数据库字段名更新用户
     *
     * @param id
     * @param oldChecksum
     * @param fieldAttrMap
     * @param userOrgEntities
     * @param connectorEntity
     * @return
     */
    SyncResult updateByFieldAttrMap(Long id, String oldChecksum, Map<String, Object> fieldAttrMap, List<UserOrgEntity> userOrgEntities, ConnectorEntity connectorEntity);

    /**
     * 根据属性名更新用户
     *
     * @param id              用户ID
     * @param oldChecksum     修改之前的校验和
     * @param attrMap         用户属性MAP
     * @param userOrgEntities 用户组织属性
     * @param connectorEntity 连接器
     * @param isFullSync      是否强制同步
     * @return
     */
    SyncResult updateByAttrMap(Long id, String oldChecksum, Map<String, Object> attrMap, List<UserOrgEntity> userOrgEntities, ConnectorEntity connectorEntity, boolean isFullSync);

    /**
     * 根据数据库字段名创建用户
     *
     * @param fieldAttrMap
     * @param userOrgEntities
     * @param connectorEntity
     * @return
     */
    SyncResult insertByFieldAttrMap(Map<String, Object> fieldAttrMap, List<UserOrgEntity> userOrgEntities, ConnectorEntity connectorEntity);

    /**
     * 根据属性名创建用户
     *
     * @param attrMap
     * @param userOrgEntities
     * @param connectorEntity
     * @return
     */
    SyncResult insertByAttrMap(Map<String, Object> attrMap, List<UserOrgEntity> userOrgEntities, ConnectorEntity connectorEntity);

    /**
     * 根据用户id删除用户
     *
     * @param uid
     * @return
     */
    SyncResult delete(Long uid);

    /**
     * 根据用户id停用用户
     *
     * @param uid
     * @return
     */
    SyncResult deactivate(Long uid);

    /**
     * 用户离开机构
     *
     * @param orgId
     * @param uid
     */
    SyncResult leaveOrg(Long orgId, Long uid);

    /**
     * 根据id获取用户
     *
     * @param uid
     * @return
     */
    UserEntity getUserById(Long uid);
}
