package com.cyberscraft.uep.iam.service.oidc.sns;

import com.cyberscraft.uep.iam.dto.domain.ThirdIdpAuthConfig;
import com.cyberscraft.uep.iam.service.oidc.sns.common.dto.SnsUserDto;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Map;


/**
 * <p>
 * 第三方登录用户认证接口
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2020-03-11 08:51
 */
public interface IThirdPartyAuthService {
    /**
     * 移动端接入根据获取授权码获取用户信息
     *
     * @param code
     * @param authConfig
     * @return
     */
    default SnsUserDto getUserByClientCode(String code, ThirdIdpAuthConfig authConfig){
        return null;
    }

    /**
     * h5页面根据获取授权码获取用户信息(工作台内登录)
     *
     * @param code
     * @param authConfig
     * @return
     */
    default Map<String, Object> getUserByCodeOnH5(String code, ThirdIdpAuthConfig authConfig){
        return null;
    }

    /**
     * h5页面根据获取授权码获取用户信息(钉钉订阅租户工作台内登录)
     *
     * @param code
     * @return
     */
    default Map<String, Object> getUserFromDingThird(String code){
        return null;
    }


    /**
     * oauth2方式根据获取授权码获取用户信息
     *
     * @param code
     * @param authConfig
     * @return
     */
    default Map<String, Object> getUserByOAuth(String code, ThirdIdpAuthConfig authConfig){
        return null;
    }

    /**
     * 根据userId获取用户信息
     * @param userId
     * @param authConfig
     * @return
     */
    default Map<String, Object> getUserByUserId(String userId, ThirdIdpAuthConfig authConfig){
        return null;
    }

    /**
     * 根据用户名和密码获取用户信息
     * @param username
     * @param password
     * @param authConfig
     * @return
     */
    default UserDetails getUserByPassword(String username, String password, ThirdIdpAuthConfig authConfig){
        return null;
    }

    /**
     * 按用户名修改密码
     * @param username
     * @param password
     * @param authConfig
     * @return
     */
    default Boolean updatePassword(String username, String password, ThirdIdpAuthConfig authConfig){
        return true;
    }

    boolean supports(String type);
}
