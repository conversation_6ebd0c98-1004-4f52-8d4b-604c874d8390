package com.cyberscraft.uep.iam.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.sql.Blob;
import java.io.Serializable;

/**
 * <p>
 * IAM-refresh_token表
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2019-07-04
 */
@TableName("iam_refresh_token")
public class RefreshTokenEntity implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * token绑定的用户id
     */
    private Long uid;

    /**
     * app id
     */
    private Long appRefId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 过期时间
     */
    private LocalDateTime expireTime;

    /**
     * 绑定的authentication对象
     */
    private Blob authenticationObject;

    /**
     * token进行某种hash算法计算后的hash值，防止token篡改
     */
    private String tokenHash;

    /**
     * 租户ID
     */
    private String tenantId;

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public Long getAppRefId() {
        return appRefId;
    }

    public void setAppRefId(Long appRefId) {
        this.appRefId = appRefId;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(LocalDateTime expireTime) {
        this.expireTime = expireTime;
    }

    public Blob getAuthenticationObject() {
        return authenticationObject;
    }

    public void setAuthenticationObject(Blob authenticationObject) {
        this.authenticationObject = authenticationObject;
    }

    public String getTokenHash() {
        return tokenHash;
    }

    public void setTokenHash(String tokenHash) {
        this.tokenHash = tokenHash;
    }

    @Override
    public String toString() {
        return "RefreshTokenEntity{" +
        "id=" + id +
        ", uid=" + uid +
        ", appRefId=" + appRefId +
        ", createTime=" + createTime +
        ", expireTime=" + expireTime +
        ", authenticationObject=" + authenticationObject +
        ", tokenHash=" + tokenHash +
        "}";
    }
}
