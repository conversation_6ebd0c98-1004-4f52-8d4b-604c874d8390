package com.cyberscraft.uep.iam.service.impl;

import com.cyberscraft.uep.iam.dbo.UserStatusDBO;
import com.cyberscraft.uep.iam.dto.enums.BooleanEnums;
import com.cyberscraft.uep.iam.entity.UserStatusEntity;
import com.cyberscraft.uep.iam.service.IUserStatusService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * @Author：xhquan
 * @Project：uep-services
 * @since：2023/5/25 14:48
 */
@Service
public class UserStatusServiceImpl implements IUserStatusService {

    @Resource
    private UserStatusDBO userStatusDBO;

    @Override
    public void createUserStatus(UserStatusEntity userStatus) {
        userStatusDBO.save(userStatus);
    }

    @Override
    public void createOrUpdateUserStatus(UserStatusEntity userStatus) {
        userStatusDBO.saveOrUpdate(userStatus);
    }

    @Override
    public void createOrUpdateLoginUserStatus(Long userId) {
        UserStatusEntity userStatus = new UserStatusEntity();
        userStatus.setId(userId);
        userStatus.setActive(BooleanEnums.TRUE.getValue());
        userStatus.setCreateTime(LocalDateTime.now());
        userStatusDBO.saveOrUpdate(userStatus);
    }

    @Override
    public void createOrUpdateUserStatus(Long userId) {
        UserStatusEntity userStatus = UserStatusEntity.create(userId);
        userStatusDBO.saveOrUpdate(userStatus);
    }

    @Override
    public void updateUserStatus(UserStatusEntity userStatus) {
        userStatusDBO.updateById(userStatus);
    }

    @Override
    public UserStatusEntity getUserStatusById(Long id) {
        return userStatusDBO.getById(id);
    }

    @Override
    public void deleteUserStatus(Long id) {
        userStatusDBO.removeById(id);
    }

}
