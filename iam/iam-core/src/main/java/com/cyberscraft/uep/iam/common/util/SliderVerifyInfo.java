package com.cyberscraft.uep.iam.common.util;

import java.awt.image.BufferedImage;

public class SliderVerifyInfo {

    /**
     * 大图宽度
     */
    private Integer bigWidth;

    /**
     * 大图高度
     */
    private Integer bigHeight;

    /**
     * 大图转BASE64字符串
     */
    private String bigImageBase64;

    /**
     * 大图
     */
    private BufferedImage bigImage;

    /**
     * 随机坐标Y
     */
    private Integer posY;
    /**
     * 随机坐标X
     */
    private Integer posX;

    /**
     * 小图宽度
     */
    private Integer smallWidth;
    /**
     * 小图高度
     */
    private Integer smallHeight;

    /**
     * 小图转BASE64字符串
     */
    private String smallImageBase64;

    /**
     * 小图
     */
    private BufferedImage smallImage;

    public Integer getBigWidth() {
        return bigWidth;
    }

    public void setBigWidth(Integer bigWidth) {
        this.bigWidth = bigWidth;
    }

    public Integer getBigHeight() {
        return bigHeight;
    }

    public void setBigHeight(Integer bigHeight) {
        this.bigHeight = bigHeight;
    }

    public String getBigImageBase64() {
        return bigImageBase64;
    }

    public void setBigImageBase64(String bigImageBase64) {
        this.bigImageBase64 = bigImageBase64;
    }

    public BufferedImage getBigImage() {
        return bigImage;
    }

    public void setBigImage(BufferedImage bigImage) {
        this.bigImage = bigImage;
    }

    public Integer getPosY() {
        return posY;
    }

    public void setPosY(Integer posY) {
        this.posY = posY;
    }

    public Integer getPosX() {
        return posX;
    }

    public void setPosX(Integer posX) {
        this.posX = posX;
    }

    public Integer getSmallWidth() {
        return smallWidth;
    }

    public void setSmallWidth(Integer smallWidth) {
        this.smallWidth = smallWidth;
    }

    public Integer getSmallHeight() {
        return smallHeight;
    }

    public void setSmallHeight(Integer smallHeight) {
        this.smallHeight = smallHeight;
    }

    public String getSmallImageBase64() {
        return smallImageBase64;
    }

    public void setSmallImageBase64(String smallImageBase64) {
        this.smallImageBase64 = smallImageBase64;
    }

    public BufferedImage getSmallImage() {
        return smallImage;
    }

    public void setSmallImage(BufferedImage smallImage) {
        this.smallImage = smallImage;
    }
}
