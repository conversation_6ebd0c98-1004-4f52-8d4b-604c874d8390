package com.cyberscraft.uep.iam.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <p>
 * IAM SNS配置信息表
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2020-06-12
 */
@TableName("iam_sns_config")
public class SnsConfigEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * SNS类型,暂时定义跟连接器类型，第三方平台账户类型一致
     */
    private Integer type;

    /**
     * 1-使用南向，0-使用北向链接器的用户进行企业用户认证，默认使用北向
     */
    private Integer inboundAuth;

    /**
     * 是否为当前类型对应的默认配置
     */
    private Integer isDefault;

    /**
     * 下游连接器ID
     */
    private String name;

    /**
     * 第三方平台的企业ID,或者租户ID,主要用于业务系统中的排重处理
     */
    private String corpId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 链接器配置信息，json合适存储
     */
    private String config;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 租户ID
     */
    private String tenantId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getInboundAuth() {
        return inboundAuth;
    }

    public void setInboundAuth(Integer inboundAuth) {
        this.inboundAuth = inboundAuth;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Integer isDefault) {
        this.isDefault = isDefault;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCorpId() {
        return corpId;
    }

    public void setCorpId(String corpId) {
        this.corpId = corpId;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public String getConfig() {
        return config;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public void setConfig(String config) {
        this.config = config;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    @Override
    public String toString() {
        return "SnsConfigEntity{" +
                "id=" + id +
                ", type=" + type +
                ", isDefault=" + isDefault +
                ", name=" + name +
                ", corpId=" + corpId +
                ", createTime=" + createTime +
                ", createBy=" + createBy +
                ", updateTime=" + updateTime +
                ", updateBy=" + updateBy +
                ", config=" + config +
                ", tenantId=" + tenantId +
                "}";
    }
}
