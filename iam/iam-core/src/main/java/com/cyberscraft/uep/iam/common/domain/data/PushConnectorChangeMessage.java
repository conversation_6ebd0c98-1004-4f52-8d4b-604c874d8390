package com.cyberscraft.uep.iam.common.domain.data;

import java.io.Serializable;

/***
 *
 * @date 2021/7/14
 * <AUTHOR>
 ***/
public class PushConnectorChangeMessage implements Serializable {

    private Long id;

    private Integer eventType;

    private String tenantId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getEventType() {
        return eventType;
    }

    public void setEventType(Integer eventType) {
        this.eventType = eventType;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    @Override
    public String toString() {
        return "PushConnectorChangeMessage{" +
                "id=" + id +
                ", eventType=" + eventType +
                ", tenantId='" + tenantId + '\'' +
                '}';
    }
}
