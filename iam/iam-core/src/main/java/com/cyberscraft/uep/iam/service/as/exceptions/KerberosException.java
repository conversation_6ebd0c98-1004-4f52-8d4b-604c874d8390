package com.cyberscraft.uep.iam.service.as.exceptions;

import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

/**
 * Created by cuilong on 8/30/17.
 */
public class KerberosException extends RuntimeException {
    public static final String ERROR = "error";
    public static final String DESCRIPTION = "error_description";
    public static final String INVALID_REQUEST = "invalid_request";
    public static final String INVALID_REDIRECT_URI = "invalid_redirect_uri";
    public static final String INVALID_CLIENT = "invalid_client";
    public static final String INVALID_SERVICE_TICKET = "invalid_service_ticket";
    public static final String INVALID_USER = "invalid_user";
    public static final String SERVER_ERROR = "server_error";

    private Map<String, String> additionalInformation = null;

    public KerberosException(String msg, Throwable t) {
        super(msg, t);
        addAdditionalInformation(KerberosException.ERROR, getErrorCode());
        addAdditionalInformation(KerberosException.DESCRIPTION, msg);
    }

    public KerberosException(String msg) {
        super(msg);
        addAdditionalInformation(KerberosException.ERROR, getErrorCode());
        addAdditionalInformation(KerberosException.DESCRIPTION, msg);
    }

    public String getErrorCode() {
        return INVALID_REQUEST;
    }

    public int getHttpErrorCode() {
        return 400;
    }

    public Map<String, String> getAdditionalInformation() {
        return this.additionalInformation;
    }

    public void addAdditionalInformation(String key, String value) {
        if(this.additionalInformation == null) {
            this.additionalInformation = new TreeMap();
        }

        this.additionalInformation.put(key, value);
    }

    public static KerberosException create(String errorCode, String errorMessage) {
        if(errorMessage == null) {
            errorMessage = errorCode == null?"Error":errorCode;
        }

        KerberosException ex = null;
        switch (errorCode) {
            case INVALID_CLIENT:
                ex = new KerberosInvalidClientException(errorMessage);
                break;
            case INVALID_REDIRECT_URI:
                ex = new KerberosInvalidRedirectUriException(errorMessage);
                break;
            case INVALID_USER:
                ex = new KerberosInvalidUserException(errorMessage);
                break;
            case INVALID_SERVICE_TICKET:
                ex = new KerberosInvalidServiceTicketException(errorMessage);
                break;
            default:
                break;
        }

        return ex;
    }

    public static KerberosException valueOf(Map<String, String> errorParams) {
        String errorCode = (String)errorParams.get("error");
        String errorMessage = errorParams.containsKey("error_description")?(String)errorParams.get("error_description"):null;
        KerberosException ex = create(errorCode, errorMessage);
        Set<Map.Entry<String, String>> entries = errorParams.entrySet();
        Iterator var5 = entries.iterator();

        while(var5.hasNext()) {
            Map.Entry<String, String> entry = (Map.Entry)var5.next();
            String key = (String)entry.getKey();
            if(!"error".equals(key) && !"error_description".equals(key)) {
                ex.addAdditionalInformation(key, (String)entry.getValue());
            }
        }

        return ex;
    }

    public String toString() {
        return this.getSummary();
    }

    public String getSummary() {
        StringBuilder builder = new StringBuilder();
        String delim = "";
        String error = this.getErrorCode();
        if(error != null) {
            builder.append(delim).append("error=\"").append(error).append("\"");
            delim = ", ";
        }

        String errorMessage = this.getMessage();
        if(errorMessage != null) {
            builder.append(delim).append("error_description=\"").append(errorMessage).append("\"");
            delim = ", ";
        }

        Map<String, String> additionalParams = this.getAdditionalInformation();
        if(additionalParams != null) {
            for(Iterator var6 = additionalParams.entrySet().iterator(); var6.hasNext(); delim = ", ") {
                Map.Entry<String, String> param = (Map.Entry)var6.next();
                builder.append(delim).append((String)param.getKey()).append("=\"").append((String)param.getValue()).append("\"");
            }
        }

        return builder.toString();
    }
}
