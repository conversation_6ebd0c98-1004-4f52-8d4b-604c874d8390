package com.cyberscraft.uep.iam.common.correlation;

/**
 * Utility class which stores ThreadLocal (Request) correlation Id.
 */
public class UsernameThreadLocal {

    private static final ThreadLocal<String> username = new ThreadLocal<String>();
    
    public static String getUsername() {
        return username.get();
    }
    
    public static void setUsername(String username) {
        UsernameThreadLocal.username.set(username);
    }

    public static void clear() {
        UsernameThreadLocal.username.remove();
    }
}
