package com.cyberscraft.uep.iam.common.domain.configparams;

import com.fasterxml.jackson.databind.PropertyNamingStrategy.SnakeCaseStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import org.apache.commons.collections.CollectionUtils;
import org.thymeleaf.util.ListUtils;

import java.util.List;

@JsonNaming(SnakeCaseStrategy.class)
public class CfgPasswordPolicy {

    private Boolean adminResetUserPwd;

    private Boolean pwdMustChangeForAdminResetPwd;

    private Boolean pwdMustChangeForAdminAddUser;

    private Integer pwdMaxAge;

    private Integer pwdExpireWarning;

    private Integer pwdInHistory;

    private Integer continuousFailureCount;

    private Integer lockDuration;

    private List<String> whiteList;

    private boolean loginPageRemember;

    public Boolean getAdminResetUserPwd() {
        return adminResetUserPwd;
    }

    public void setAdminResetUserPwd(Boolean adminResetUserPwd) {
        this.adminResetUserPwd = adminResetUserPwd;
    }

    public Boolean getPwdMustChangeForAdminResetPwd() {
        return pwdMustChangeForAdminResetPwd;
    }

    public void setPwdMustChangeForAdminResetPwd(Boolean pwdMustChange4AdminReset) {
        this.pwdMustChangeForAdminResetPwd = pwdMustChange4AdminReset;
    }

    public Boolean getPwdMustChangeForAdminAddUser() {
        return pwdMustChangeForAdminAddUser;
    }

    public void setPwdMustChangeForAdminAddUser(Boolean pwdMustChange4AdminAddUser) {
        this.pwdMustChangeForAdminAddUser = pwdMustChange4AdminAddUser;
    }

    public Integer getPwdMaxAge() {
        return pwdMaxAge;
    }

    public void setPwdMaxAge(Integer pwdMaxAge) {
        this.pwdMaxAge = pwdMaxAge;
    }

    public Integer getPwdExpireWarning() {
        return pwdExpireWarning;
    }

    public void setPwdExpireWarning(Integer pwdExpireWarning) {
        this.pwdExpireWarning = pwdExpireWarning;
    }

    public Integer getPwdInHistory() {
        return pwdInHistory;
    }

    public void setPwdInHistory(Integer pwdInHistory) {
        this.pwdInHistory = pwdInHistory;
    }

    public Integer getContinuousFailureCount() {
        return continuousFailureCount;
    }

    public void setContinuousFailureCount(Integer continuousFailureCount) {
        this.continuousFailureCount = continuousFailureCount;
    }

    public Integer getLockDuration() {
        return lockDuration;
    }

    public void setLockDuration(Integer lockDuration) {
        this.lockDuration = lockDuration;
    }

    public List<String> getWhiteList() {
        return whiteList;
    }

    public void setWhiteList(List<String> whiteList) {
        this.whiteList = whiteList;
    }

    public boolean isLoginPageRemember() {
        return loginPageRemember;
    }

    public void setLoginPageRemember(boolean loginPageRemember) {
        this.loginPageRemember = loginPageRemember;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((adminResetUserPwd == null) ? 0 : adminResetUserPwd.hashCode());
        result = prime * result + ((pwdInHistory == null) ? 0 : pwdInHistory.hashCode());
        result = prime * result + ((pwdMaxAge == null) ? 0 : pwdMaxAge.hashCode());
        result = prime * result + ((pwdExpireWarning == null) ? 0 : pwdExpireWarning.hashCode());
        result = prime * result + ((pwdMustChangeForAdminAddUser == null) ? 0 : pwdMustChangeForAdminAddUser.hashCode());
        result = prime * result + ((pwdMustChangeForAdminResetPwd == null) ? 0 : pwdMustChangeForAdminResetPwd.hashCode());
        result = prime * result + ((continuousFailureCount == null) ? 0 : continuousFailureCount.hashCode());
        result = prime * result + ((lockDuration == null) ? 0 : lockDuration.hashCode());
        result = prime * result + ((whiteList == null) ? 0 : whiteList.hashCode());
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        CfgPasswordPolicy other = (CfgPasswordPolicy) obj;
        if (adminResetUserPwd == null) {
            if (other.adminResetUserPwd != null) {
                return false;
            }
        } else if (!adminResetUserPwd.equals(other.adminResetUserPwd)) {
            return false;
        }
        if (pwdInHistory == null) {
            if (other.pwdInHistory != null) {
                return false;
            }
        } else if (!pwdInHistory.equals(other.pwdInHistory)) {
            return false;
        }
        if (pwdMaxAge == null) {
            if (other.pwdMaxAge != null) {
                return false;
            }
        } else if (!pwdMaxAge.equals(other.pwdMaxAge)) {
            return false;
        }
        if (pwdMustChangeForAdminAddUser == null) {
            if (other.pwdMustChangeForAdminAddUser != null) {
                return false;
            }
        } else if (!pwdMustChangeForAdminAddUser.equals(other.pwdMustChangeForAdminAddUser)) {
            return false;
        }
        if (pwdMustChangeForAdminResetPwd == null) {
            if (other.pwdMustChangeForAdminResetPwd != null) {
                return false;
            }
        } else if (!pwdMustChangeForAdminResetPwd.equals(other.pwdMustChangeForAdminResetPwd)) {
            return false;
        }
        if (continuousFailureCount == null) {
            if (other.continuousFailureCount != null) {
                return false;
            }
        } else if (!continuousFailureCount.equals(other.continuousFailureCount)) {
            return false;
        }
        if (pwdExpireWarning == null) {
            if (other.pwdExpireWarning != null) {
                return false;
            }
        } else if (!pwdExpireWarning.equals(other.pwdExpireWarning)) {
            return false;
        }
        if (lockDuration == null) {
            if (other.lockDuration != null) {
                return false;
            }
        } else if (!lockDuration.equals(other.lockDuration)) {
            return false;
        }
        if (whiteList == null) {
            if (other.whiteList != null) {
                return false;
            }
        } else if (!CollectionUtils.isEqualCollection(other.whiteList, whiteList)) {
            return false;
        }
        if (loginPageRemember != other.loginPageRemember) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "CfgPasswordPolicy [adminResetUserPwd=" + adminResetUserPwd + ", pwdMustChange4AdminReset="
                + pwdMustChangeForAdminResetPwd + ", pwdMustChange4AdminAddUser=" + pwdMustChangeForAdminAddUser
                + ", pwdMaxAge=" + pwdMaxAge + ", pwdInHistory=" + pwdInHistory + ",continuousFailureCount" + continuousFailureCount + ",lockDuration" + lockDuration + ",pwdExpireWarning" + pwdExpireWarning + ",whiteList" + String.join(",", whiteList) + "]"
                + ", loginPageRemember" + loginPageRemember;
    }


}
