package com.cyberscraft.uep.iam.common.exception;

import com.cyberscraft.uep.iam.errors.TransactionErrorType;
import org.springframework.http.HttpStatus;

public class AppException extends UserCenterException {

	public AppException(HttpStatus httpStatus, TransactionErrorType errorType){
        super(httpStatus, errorType);
    }

    public AppException(Throwable throwable){
        super(throwable);
    }

    public AppException(HttpStatus httpStatus, TransactionErrorType transactionError, String custDesc){
        super(httpStatus, transactionError, custDesc, null);
    }

    public AppException(String message) {
	    super(message);
    }
}
