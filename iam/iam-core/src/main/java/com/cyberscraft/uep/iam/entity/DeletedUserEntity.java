package com.cyberscraft.uep.iam.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cyberscraft.uep.iam.common.util.Decryptable;
import com.cyberscraft.uep.iam.common.util.UserUtil;
import com.cyberscraft.uep.iam.constants.CommonConstants;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.sql.Blob;
import java.time.LocalDateTime;

/**
 * <p>
 * IAM-被删除用户基本信息表
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2019-08-11
 */
@TableName("iam_deleted_user")
public class DeletedUserEntity implements Serializable, Decryptable {

    private static final long serialVersionUID=1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户所在的地址
     */
    private String address;

    /**
     * 用户的生日
     */
    private LocalDateTime birthdate;

    /**
     * 用户的电子邮件
     */
    private String email;

    /**
     * 用户的性别，1：MALE，2：FEMALE
     */
    private Integer gender;

    /**
     * 用户的语言环境
     */
    private String locale;

    /**
     * 用户的电子邮件地址是否已经验证过 ,0：否，1：是
     */
    private Integer emailVerified;

    /**
     * 用户的全名，即包括姓和名
     */
    private String name;

    /**
     * 用户的显示名，即昵称，缺省同用户登录名
     */
    private String nickname;

    /**
     * 用户登录密码
     */
    private String password;

    /**
     * 用户密码的状态，['TEMP', 'NORMAL', 'EXPIRED', 'ADMIN_LOCKED',     'ERR_LOCKED']stringEnum:"TEMP", "NORMAL", "EXPIRED", "ADMIN_LOCKED", "ERR_LOCKED"
     */
    private Integer passwordStatus;

    /**
     * 用户的移动电话号码
     */
    private String phoneNumber;

    /**
     * 用户的移动电话号码是否已经验证过,1:是，0：否
     */
    private Integer phoneNumberVerified;

    /**
     * 用户头像图片，数据是base64编码，最大300K ,
     */
    private Blob picture;

    /**
     * 用户的别名，可以用来登录，缺省同用户登录名
     */
    private String preferredUsername;

    /**
     * 用户简介的URL链接
     */
    private String profile;

    /**
     * 用户的状态， ['ACTIVE', 'INACTIVE']stringEnum:"ACTIVE", "INACTIVE"
     */
    private Integer status;

    /**
     * 用户的固定电话号码 ,
     */
    private String telephoneNumber;

    /**
     * 用户的类别 = 1：END_USER，2：PROGRAM_USER，3：ADMIN
     */
    private Integer type;

    /**
     * 用户登录名
     */
    private String username;

    /**
     * 用户相关网站的网址，例如主页，博客等
     */
    private String website;

    /**
     * 用户所在的时区
     */
    private String zoneinfo;

    /**
     * 上次登录时间
     */
    private LocalDateTime lastLogin;

    /**
     * 设备证书
     */
    private String cert;

    /**
     * 密码过期时间
     */
    private LocalDateTime pwdExpirationTime;

    /**
     * 密码修改时间
     */
    private LocalDateTime pwdChangedTime;

    /**
     * 该用户被创建的模式 = 1：BY_ADMIN，2：BY_SELF，3：BY_IMPORT_CSV，4：BY_IMPORT_LDAP
     */
    private Integer createdMode;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 数据来源ID，同步或导入时使用
     */
    private Long connectorId;

    /**
     * 用户信息的checksum，用于做用户信息是否发生变化的判断
     */
    private String checksum;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 用户扩展属性
     */
    private String userExtension;

    /**
     * 用户职工号
     */
    private String userJobNumber;

    /**
     * 开始时间
     */
    private String startDate;

    /**
     * 结束时间
     */
    private String endDate;

    /**
     * 职位
     */
    private String title;

    /**
     * 直属主管
     */
    private String manager;

    /**
     * 入职日期
     */
    private Long hiredDate;

    /**
     * 身份证号码
     */
    private String idCard;

    /**
     * 身份证号码是否验证 1：是，0：否
     */
    private Integer idCardVerified;

    /**
     * 姓名是否验证 1：是，0：否
     */
    private Integer nameVerified;

    /**
     * 工号是否验证 1：是，0：否
     */
    private Integer userJobNumberVerified;

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getChecksum() {
        return checksum;
    }

    public void setChecksum(String checksum) {
        this.checksum = checksum;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public LocalDateTime getBirthdate() {
        return birthdate;
    }

    public void setBirthdate(LocalDateTime birthdate) {
        this.birthdate = birthdate;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        if (StringUtils.isNotBlank(email)) {
            email = UserUtil.encryptSensitiveInfo(email.toLowerCase());
        }
        this.email = email;
    }

    public Integer getGender() {
        return gender;
    }

    public void setGender(Integer gender) {
        this.gender = gender;
    }

    public String getLocale() {
        return locale;
    }

    public void setLocale(String locale) {
        this.locale = locale;
    }

    public Integer getEmailVerified() {
        return emailVerified;
    }

    public void setEmailVerified(Integer emailVerified) {
        this.emailVerified = emailVerified;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Integer getPasswordStatus() {
        return passwordStatus;
    }

    public void setPasswordStatus(Integer passwordStatus) {
        this.passwordStatus = passwordStatus;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        if (StringUtils.isNotBlank(phoneNumber) && phoneNumber.matches(CommonConstants.CHINA_LOCAL_MOBILE_REG)) {
            phoneNumber = UserUtil.formatMobile(phoneNumber);
        }
        this.phoneNumber = UserUtil.encryptSensitiveInfo(phoneNumber);
    }

    public Integer getPhoneNumberVerified() {
        return phoneNumberVerified;
    }

    public void setPhoneNumberVerified(Integer phoneNumberVerified) {
        this.phoneNumberVerified = phoneNumberVerified;
    }

    public Blob getPicture() {
        return picture;
    }

    public void setPicture(Blob picture) {
        this.picture = picture;
    }

    public String getPreferredUsername() {
        return preferredUsername;
    }

    public void setPreferredUsername(String preferredUsername) {
        this.preferredUsername = preferredUsername;
    }

    public String getProfile() {
        return profile;
    }

    public void setProfile(String profile) {
        this.profile = profile;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getTelephoneNumber() {
        return telephoneNumber;
    }

    public void setTelephoneNumber(String telephoneNumber) {
        this.telephoneNumber = telephoneNumber;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getWebsite() {
        return website;
    }

    public void setWebsite(String website) {
        this.website = website;
    }

    public String getZoneinfo() {
        return zoneinfo;
    }

    public void setZoneinfo(String zoneinfo) {
        this.zoneinfo = zoneinfo;
    }

    public LocalDateTime getLastLogin() {
        return lastLogin;
    }

    public void setLastLogin(LocalDateTime lastLogin) {
        this.lastLogin = lastLogin;
    }

    public String getCert() {
        return cert;
    }

    public void setCert(String cert) {
        this.cert = cert;
    }

    public LocalDateTime getPwdExpirationTime() {
        return pwdExpirationTime;
    }

    public void setPwdExpirationTime(LocalDateTime pwdExpirationTime) {
        this.pwdExpirationTime = pwdExpirationTime;
    }

    public LocalDateTime getPwdChangedTime() {
        return pwdChangedTime;
    }

    public void setPwdChangedTime(LocalDateTime pwdChangedTime) {
        this.pwdChangedTime = pwdChangedTime;
    }

    public Integer getCreatedMode() {
        return createdMode;
    }

    public void setCreatedMode(Integer createdMode) {
        this.createdMode = createdMode;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public Long getConnectorId() {
        return connectorId;
    }

    public void setConnectorId(Long connectorId) {
        this.connectorId = connectorId;
    }

    public String getUserExtension() {
        return userExtension;
    }

    public void setUserExtension(String userExtension) {
        this.userExtension = userExtension;
    }

    public String getUserJobNumber() {
        return userJobNumber;
    }

    public void setUserJobNumber(String userJobNumber) {
        this.userJobNumber = userJobNumber;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Long getHiredDate() {
        return hiredDate;
    }

    public void setHiredDate(Long hiredDate) {
        this.hiredDate = hiredDate;
    }

    public String getManager() {
        return manager;
    }

    public void setManager(String manager) {
        this.manager = manager;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        if (StringUtils.isNotBlank(idCard)) {
            idCard = UserUtil.encryptSensitiveInfo(idCard);
        }
        this.idCard = idCard;
    }

    public Integer getIdCardVerified() {
        return idCardVerified;
    }

    public void setIdCardVerified(Integer idCardVerified) {
        this.idCardVerified = idCardVerified;
    }

    public Integer getNameVerified() {
        return nameVerified;
    }

    public void setNameVerified(Integer nameVerified) {
        this.nameVerified = nameVerified;
    }

    public Integer getUserJobNumberVerified() {
        return userJobNumberVerified;
    }

    public void setUserJobNumberVerified(Integer userJobNumberVerified) {
        this.userJobNumberVerified = userJobNumberVerified;
    }

    public void decryptFields() {
        this.email = UserUtil.decryptSensitiveInfo(email);
        this.phoneNumber = UserUtil.decryptSensitiveInfo(phoneNumber);
        this.idCard = UserUtil.decryptSensitiveInfo(idCard);
    }

    @Override
    public void encryptFields() {
        this.email = UserUtil.encryptSensitiveInfo(email.toLowerCase());
        this.phoneNumber = UserUtil.encryptSensitiveInfo(phoneNumber);
        this.idCard = UserUtil.encryptSensitiveInfo(idCard);
    }

    @Override
    public String toString() {
        return "DeletedUserEntity{" +
                "id=" + id +
                ", address='" + address + '\'' +
                ", birthdate=" + birthdate +
                ", email='" + email + '\'' +
                ", gender=" + gender +
                ", locale='" + locale + '\'' +
                ", emailVerified=" + emailVerified +
                ", name='" + name + '\'' +
                ", nickname='" + nickname + '\'' +
                ", password='" + password + '\'' +
                ", passwordStatus=" + passwordStatus +
                ", phoneNumber='" + phoneNumber + '\'' +
                ", phoneNumberVerified=" + phoneNumberVerified +
                ", picture=" + picture +
                ", preferredUsername='" + preferredUsername + '\'' +
                ", profile='" + profile + '\'' +
                ", status=" + status +
                ", telephoneNumber='" + telephoneNumber + '\'' +
                ", type=" + type +
                ", username='" + username + '\'' +
                ", website='" + website + '\'' +
                ", zoneinfo='" + zoneinfo + '\'' +
                ", lastLogin=" + lastLogin +
                ", cert='" + cert + '\'' +
                ", pwdExpirationTime=" + pwdExpirationTime +
                ", pwdChangedTime=" + pwdChangedTime +
                ", createdMode=" + createdMode +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", createBy='" + createBy + '\'' +
                ", updateBy='" + updateBy + '\'' +
                ", connectorId=" + connectorId +
                ", checksum='" + checksum + '\'' +
                ", tenantId='" + tenantId + '\'' +
                ", userExtension='" + userExtension + '\'' +
                ", userJobNumber='" + userJobNumber + '\'' +
                ", startDate='" + startDate + '\'' +
                ", endDate='" + endDate + '\'' +
                ", title='" + title + '\'' +
                ", hiredDate=" + hiredDate +
                '}';
    }
}
