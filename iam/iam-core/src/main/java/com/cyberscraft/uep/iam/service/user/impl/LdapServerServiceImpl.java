package com.cyberscraft.uep.iam.service.user.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cyberscraft.uep.common.config.ServerConfig;
import com.cyberscraft.uep.common.dto.QueryOrderItem;
import com.cyberscraft.uep.common.dto.QueryPage;
import com.cyberscraft.uep.common.util.PagingUtil;
import com.cyberscraft.uep.iam.common.constants.DaoConstants;
import com.cyberscraft.uep.iam.dbo.LdapInstDBO;
import com.cyberscraft.uep.iam.dbo.OrgDBO;
import com.cyberscraft.uep.iam.dbo.UserDBO;
import com.cyberscraft.uep.iam.dbo.UserOrgDBO;
import com.cyberscraft.uep.iam.dto.enums.LdapOrgStatus;
import com.cyberscraft.uep.iam.dto.enums.UserPWDStatus;
import com.cyberscraft.uep.iam.dto.request.ldapserver.LoginDto;
import com.cyberscraft.uep.iam.dto.response.ldapserver.*;
import com.cyberscraft.uep.iam.entity.LdapInstEntity;
import com.cyberscraft.uep.iam.entity.OrgEntity;
import com.cyberscraft.uep.iam.entity.UserEntity;
import com.cyberscraft.uep.iam.entity.UserOrgEntity;
import com.cyberscraft.uep.iam.service.transfer.OrgTransferV2;
import com.cyberscraft.uep.iam.service.transfer.UserTransfer;
import com.cyberscraft.uep.iam.service.user.ILdapServerService;
import com.cyberscraft.uep.iam.service.user.IUserService;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

/**
 * @Author: liuanyang
 * @Date: 2021/11/1
 */
@Service
public class LdapServerServiceImpl implements ILdapServerService {

    private Logger logger = LoggerFactory.getLogger(LdapServerServiceImpl.class);

    @Resource
    private IUserService userService;
    @Autowired
    private PasswordEncoder passwordEncoder;

    @Resource
    private LdapInstDBO ldapInstDBO;

    @Resource
    private OrgDBO orgDBO;

    @Resource
    private UserOrgDBO userOrgDBO;

    @Resource
    private UserDBO userDBO;

    @Resource
    private OrgTransferV2 orgTransferV2;

    @Resource
    private UserTransfer userTransfer;

    @Resource
    private ServerConfig serverConfig;

    /**
     * 认证普通用户
     *
     * @param loginDto
     * @param instance
     * @param tenant
     * @return
     */
    @Override
    public LoginVo userAuth(LoginDto loginDto, String instance, String tenant) {
        String username = loginDto.getUsername();
        logger.debug("userAuth username:{}", username);

        UserEntity userEntity = userService.getUserInfoByLoginId(username);
        if(userEntity != null) {
            logger.debug("user encode pass:{}", userEntity.getPassword());
            if (UserPWDStatus.needsToChangePassword(userEntity.getPasswordStatus())) {
                logger.error("user's password need change on first auth");
                return null;
            }
            boolean matches = passwordEncoder.matches(loginDto.getPassword(), userEntity.getPassword());
            if (matches) {
                LoginVo loginVo = new LoginVo();
                loginVo.setTenant(tenant);
                loginVo.setInstance(instance);
                loginVo.setAccount(userEntity.getUsername());
                loginVo.setResult(true);
                loginVo.setPermission(Collections.EMPTY_LIST);
                return loginVo;
            }
        }
        return null;
    }

    /**
     * 认证ldap管理帐号
     *
     * @param loginDto
     * @param instance
     * @param tenant
     * @return
     */
    @Override
    public LoginVo srvAuth(LoginDto loginDto, String instance, String tenant) {
        String username = loginDto.getUsername();
        logger.debug("srvAuth username:{}", username);

        LdapInstEntity oneByUsername = ldapInstDBO.getOneByUsername(username);
        if(oneByUsername != null){
            if(!oneByUsername.getInstanceId().equals(instance)){
                logger.debug("instance is different,{}--{}",instance, oneByUsername.getInstanceId());
                return null;
            }
            if (loginDto.getPassword().equals(oneByUsername.getPassword())) {
                LoginVo loginVo = new LoginVo();
                loginVo.setTenant(tenant);
                loginVo.setInstance(instance);
                loginVo.setAccount(oneByUsername.getUsername());
                loginVo.setResult(true);
                loginVo.setPermission(Arrays.asList("users:read","orgs:read"));
                return loginVo;
            }
        }

        return null;
    }

    /**
     * 按照ldapserver的条件搜索组织机构
     *
     * @param pageIndex
     * @param pageSize
     * @param orgDepth
     * @param need_user
     * @param filter
     * @return
     */
    @Override
    public PageResult<SearchVo> searchOrgsForLdapServer(int pageIndex, int pageSize, int orgDepth, boolean need_user, LdapFilter<OrgMap> filter) {
        PageResult<SearchVo> orgVoPageResult = new PageResult<>();

        OrgMap orgMap = new OrgMap();
        if(filter != null) {
            orgMap = filter.getValues();
        }
        if(orgMap.getStatus() == null){
            orgMap.setStatus(LdapOrgStatus.ENABLED);
        }

        List<String> orgNamePath = orgMap.getTrace();
        if(orgNamePath != null && orgNamePath.size() > 0){
            Collections.reverse(orgNamePath);
        }

        QueryPage<OrgEntity> orgEntityQueryPage = new QueryPage<>();
        orgEntityQueryPage.setPage(pageIndex);
        orgEntityQueryPage.setSize(pageSize);
        orgEntityQueryPage.addOrder(QueryOrderItem.asc("org_path"));

        String orgPath = null;
        if(orgNamePath != null && orgNamePath.size() > 0) {
            OrgEntity baseOrg = orgDBO.getByNamePath(orgNamePath);
            if (baseOrg != null) {
                orgPath = baseOrg.getOrgPath();
            } else {
                orgEntityQueryPage = PagingUtil.makeEmptyPageResult(orgEntityQueryPage);
                orgVoPageResult.setTotal(orgEntityQueryPage.getTotal());
                List<SearchVo> orgSearchVos = orgEntityQueryPage.getItems().stream().map(orgEntity -> trans2OrgVo(orgEntity, need_user)).collect(toList());
                orgVoPageResult.setData(orgSearchVos);
                return orgVoPageResult;
            }
        }

        OrgEntity queryEntity = orgTransferV2.mapToEntity(orgMap);

        logger.info("orgPath:{}, orgDepth:{}", orgPath, orgDepth);

        QueryPage<OrgEntity> orgPage = orgDBO.page(orgPath, orgDepth, queryEntity, orgEntityQueryPage);

        orgVoPageResult.setTotal(orgPage.getTotal());
        List<SearchVo> orgSearchVos = orgPage.getItems().stream().map(orgEntity -> trans2OrgVo(orgEntity, need_user)).collect(toList());
        orgVoPageResult.setData(orgSearchVos);

        return orgVoPageResult;
    }

    @Override
    public PageResult<SearchVo> searchOrgsAndUserForLdapServer(int pageIndex, int pageSize, int orgDepth, boolean need_user, LdapFilter<OrgMap> filter) {
        PageResult<SearchVo> orgVoPageResult = new PageResult<>();

        OrgMap orgMap = new OrgMap();
        if(filter != null) {
            orgMap = filter.getValues();
        }
        if(orgMap.getStatus() == null){
            orgMap.setStatus(LdapOrgStatus.ENABLED);
        }

        List<String> orgNamePath = orgMap.getTrace();
        if(orgNamePath != null && orgNamePath.size() > 0){
            Collections.reverse(orgNamePath);
        }

        Long parentOrgId = null;
        boolean searchRoot = false;
        if(orgNamePath != null && orgNamePath.size() > 0) {
            OrgEntity baseOrg = orgDBO.getByNamePath(orgNamePath);
            if (baseOrg != null) {
                parentOrgId = baseOrg.getId();
            }
        } else {
            parentOrgId = DaoConstants.ROOT_ORG_ID;
            searchRoot = true;
        }

        if (parentOrgId == null) {
            return orgVoPageResult;
        }

        List<Long> userIds = new ArrayList<>();
        List<Long> orgIds = new ArrayList<>();
        if (searchRoot) {
            orgIds.add(parentOrgId);
        } else {
            QueryPage<Map<String, Object>> queryPage = new QueryPage<>();
            queryPage.setPage(pageIndex);
            queryPage.setSize(pageSize);
            queryPage.addOrder(QueryOrderItem.asc("id"));

            Page<Map<String, Object>> page = PagingUtil.toMybatisPage(queryPage);
            page.setRecords(userOrgDBO.getOrgAndUserList(page, parentOrgId));

            orgVoPageResult.setTotal(page.getTotal());

            List<Map<String, Object>> records = page.getRecords();
            for (Map<String, Object> record : records) {
                String id = record.get("id").toString();
                String type = record.get("type").toString();
                if ("user".equals(type)) {
                    userIds.add(Long.valueOf(id));
                } else if ("org".equals(type)) {
                    orgIds.add(Long.valueOf(id));
                }
            }
        }

        List<SearchVo> result = new ArrayList<>();

        if (userIds.size() > 0) {
            if(orgNamePath != null && orgNamePath.size() > 0) {
                Collections.reverse(orgNamePath);
            }
            List<UserEntity> userEntityList = userDBO.listByIds(userIds);
            for (UserEntity userEntity : userEntityList) {
                UserSearchVo userSearchVo = trans2UserVo(userEntity, orgNamePath, true, false, false);
                userSearchVo.setType("user");
                userSearchVo.setUsername(userEntity.getUsername());
                userSearchVo.setPaths(orgNamePath);
                result.add(userSearchVo);
            }
        }
        if (orgIds.size() > 0) {
            List<OrgEntity> orgList = orgDBO.listOrgs(orgIds);
            for (OrgEntity orgEntity : orgList) {
                OrgSearchVo orgSearchVo = trans2OrgVo(orgEntity, false);
                orgSearchVo.setType("org");
                orgSearchVo.setPaths(orgSearchVo.getSecondaryDisplayContent().getTrace());
                result.add(orgSearchVo);
            }
        }

        orgVoPageResult.setData(result);

        return orgVoPageResult;
    }

    private OrgSearchVo trans2OrgVo(OrgEntity orgEntity, boolean need_user){
        OrgSearchVo orgSearchVo = new OrgSearchVo();

        orgSearchVo.setId(toUUID(orgEntity.getId()));
        orgSearchVo.setValues(orgTransferV2.entityToMap(orgEntity));

        OrgSearchVo.OrgMeta orgMeta = new OrgSearchVo.OrgMeta();
        List<String> namePathByIdPath = orgDBO.getNamePathByIdPath(orgEntity.getOrgPath());
        Collections.reverse(namePathByIdPath);
        orgMeta.setTrace(namePathByIdPath);
        if(need_user){
            List<Long> userIdList = userOrgDBO.getUserIdListByOrgIds(Arrays.asList(orgEntity.getId()));
            if(userIdList.size() > 0) {
                List<String> usernameList = userDBO.listByIds(userIdList).stream().map(UserEntity::getUsername).collect(Collectors.toList());
                orgMeta.setUsers(usernameList);
            }
        }
        orgSearchVo.setSecondaryDisplayContent(orgMeta);
        return orgSearchVo;
    }

    private String toUUID(Long l19Id) {
        final String s19Id = l19Id.toString();
        if (s19Id.equals("-1")) {
            return "98953be4-d72f-423e-ba40-7b2d8b2ce5b1";
        }
        if (s19Id.equals("-2")) {
            return "98953be4-d72f-423e-ba40-7b2d8b2ce5b2";
        }
        String uuid = "306782f4-2671-4%s-%s-%s";
        return String.format(uuid, s19Id.substring(0,3), s19Id.substring(3,7), s19Id.substring(7));
    }

    /**
     * 按照ldapserver的条件搜索用户
     *
     * @param pageIndex
     * @param pageSize
     * @param userDepth
     * @param need_trace
     * @param need_group
     * @param need_posixGroup
     * @param filter
     * @return
     */
    @Override
    public PageResult<UserSearchVo> searchUsersForLdapServer(int pageIndex, int pageSize, int userDepth, boolean need_trace, boolean need_group, boolean need_posixGroup, LdapFilter<UserMap> filter) {
        PageResult<UserSearchVo> userVoPageResult = new PageResult<>();
        QueryPage<UserEntity> userEntityQueryPage = new QueryPage<>();
        userEntityQueryPage.setPage(pageIndex);
        userEntityQueryPage.setSize(pageSize);

        UserMap userMap = new UserMap();
        if(filter != null) {
            userMap = filter.getValues();
        }
//        if(userMap.getStatus() == null){
//            userMap.setStatus(LdapUserStatus.ACTIVE);
//        }

        Map<Long, OrgEntity> userOrgMap = new HashMap<>();
        Map<Long, OrgEntity> orgEntityMap = new HashMap<>();

        List<Long> userIdList = new ArrayList<>();
        String username = userMap.getUsername();
        String phoneNumber = userMap.getPhoneNumber();
        String email = userMap.getEmail();

        if (StringUtils.isNotBlank(username)) {
            UserEntity userEntity = userService.getUserInfoByLoginId(username);
            if(userEntity != null) {
                userMap.setUsername(null);
                userIdList.add(userEntity.getId());
            }
            if(userIdList.size() == 0){
                userVoPageResult.setTotal(0L);
                userVoPageResult.setData(Collections.EMPTY_LIST);
                return userVoPageResult;
            }
        }else if(StringUtils.isNotBlank(phoneNumber)){
            UserEntity userEntity = userService.getUserInfoByLoginId(phoneNumber);
            if(userEntity != null) {
                userMap.setPhoneNumber(null);
                userIdList.add(userEntity.getId());
            }
            if(userIdList.size() == 0){
                userVoPageResult.setTotal(0L);
                userVoPageResult.setData(Collections.EMPTY_LIST);
                return userVoPageResult;
            }
        }else if(StringUtils.isNotBlank(email)){
            UserEntity userEntity = userService.getUserInfoByLoginId(email);
            if(userEntity != null) {
                userMap.setEmail(null);
                userIdList.add(userEntity.getId());
            }
            if(userIdList.size() == 0){
                userVoPageResult.setTotal(0L);
                userVoPageResult.setData(Collections.EMPTY_LIST);
                return userVoPageResult;
            }
        }

        final OrgEntity defaultOrg = orgDBO.getById(DaoConstants.NULL_ORG_ID);
        final OrgEntity rootOrg = orgDBO.getById(DaoConstants.ROOT_ORG_ID);

        orgEntityMap.put(rootOrg.getId(), rootOrg);
        orgEntityMap.put(defaultOrg.getId(), defaultOrg);

        if (!userIdList.isEmpty()) {
            List<Long> userOrgIdList = userOrgDBO.getUserOrgIdList(userIdList.get(0));
            if (!userOrgIdList.isEmpty()) {
                final OrgEntity userOrg = orgDBO.getById(userOrgIdList.get(0));
                userOrgMap.put(userIdList.get(0), userOrg);
                orgEntityMap.put(userOrg.getId(), userOrg);
            } else {
                userOrgMap.put(userIdList.get(0), defaultOrg);
            }
        }

        List<Long> orgUserIdList = new ArrayList<>();
        List<String> orgNamePath = userMap.getTrace();

        OrgEntity baseOrg = null;
        if(orgNamePath != null && orgNamePath.size() > 0){
            Collections.reverse(orgNamePath);
            baseOrg = orgDBO.getByNamePath(orgNamePath);
        }else if(userIdList.size() == 0){
            baseOrg = rootOrg;
        }

        if (baseOrg != null) {
            Long orgId = baseOrg.getId();
            List<OrgEntity> allOrgList = new ArrayList<>();
            allOrgList.add(baseOrg);

            List<Long> allOrgIds = new ArrayList();
            allOrgIds.add(orgId);

            if (userDepth > 1) {
                List<OrgEntity> subOrgList = orgDBO.getSubOrgs(baseOrg.getOrgPath(), DaoConstants.id, DaoConstants.ORG_NAME, DaoConstants.ORG_PATH);
                allOrgList.addAll(subOrgList);

                List<Long> subOrgIds = subOrgList.stream().map(e -> e.getId()).collect(Collectors.toList());
                allOrgIds.addAll(subOrgIds);
            }

            final Map<Long, OrgEntity> collect = allOrgList.stream().collect(Collectors.toMap(OrgEntity::getId, Function.identity(), (v1, v2) -> v1));
            orgEntityMap.putAll(collect);

            final List<UserOrgEntity> userOrgListByOrgIds = userOrgDBO.getUserOrgListByOrgIds(allOrgIds);
            for (UserOrgEntity userOrgListByOrgId : userOrgListByOrgIds) {
                final Long orgRefId = userOrgListByOrgId.getOrgRefId();
                final Long uid = userOrgListByOrgId.getUid();
                userOrgMap.put(uid, orgEntityMap.get(orgRefId));
                orgUserIdList.add(uid);
            }

            if(orgUserIdList.size() == 0){
                userVoPageResult.setTotal(0L);
                userVoPageResult.setData(Collections.EMPTY_LIST);
                return userVoPageResult;
            }
        }

        List<Long> filterUserIdList = null;
        if(orgUserIdList.size() > 0 && userIdList.size() > 0){
            orgUserIdList.retainAll(userIdList);
            filterUserIdList = orgUserIdList;
        }else if(orgUserIdList.size() > 0 && userIdList.size() == 0){
            filterUserIdList = orgUserIdList;
        }else if(orgUserIdList.size() == 0 && userIdList.size() > 0){
            filterUserIdList = userIdList;
        }

        UserEntity queryEntity = userTransfer.mapToEntity(userMap);
        QueryPage<UserEntity> userPage = userDBO.page(filterUserIdList, userEntityQueryPage, queryEntity);

        if(orgNamePath != null && orgNamePath.size() > 0) {
            Collections.reverse(orgNamePath);
        }
        List<UserSearchVo> userSearchVos = userPage.getItems().stream().map(userEntity -> trans2UserVo(userEntity, userOrgMap, orgEntityMap, need_trace)).collect(toList());

        userVoPageResult.setTotal(userPage.getTotal());
        userVoPageResult.setData(userSearchVos);
        return userVoPageResult;
    }

    private List<String> getOrgTrace(String orgIdPath, Map<Long, OrgEntity> orgEntityMap) {
        List<String> namePath = new ArrayList<>();
        String[] ids = orgIdPath.split(",");
        for (String id : ids) {
            final OrgEntity orgEntity = orgEntityMap.get(Long.valueOf(id));
            if (orgEntity != null) {
                namePath.add(orgEntity.getName());
            } else {
                final OrgEntity orgDBOById = orgDBO.getById(id);
                orgEntityMap.put(Long.valueOf(id), orgDBOById);
                namePath.add(orgDBOById.getName());
            }
        }
        Collections.reverse(namePath);
        return namePath;
    }

    private UserSearchVo trans2UserVo(UserEntity userEntity, Map<Long, OrgEntity> userOrgMap, Map<Long, OrgEntity> orgEntityMap, boolean need_trace){
        UserSearchVo userSearchVo = new UserSearchVo();

        userSearchVo.setId(toUUID(userEntity.getId()));
        userSearchVo.setValues(userTransfer.entityToMap(userEntity));

        final OrgEntity orgEntity = userOrgMap.get(userEntity.getId());
        final List<String> orgTrace = getOrgTrace(orgEntity.getOrgPath(), orgEntityMap);
        userSearchVo.getValues().setTrace(orgTrace);

        UserSearchVo.Membership membership = new UserSearchVo.Membership();
        if(need_trace) {
            membership.setTrace(orgTrace);
        }
        userSearchVo.setSecondaryDisplayContent(membership);
        return userSearchVo;
    }

    private UserSearchVo trans2UserVo(UserEntity userEntity, List<String> orgNamePath, boolean need_trace, boolean need_group, boolean need_posixGroup){
        UserSearchVo userSearchVo = new UserSearchVo();

        userSearchVo.setId(toUUID(userEntity.getId()));
        userSearchVo.setValues(userTransfer.entityToMap(userEntity));

        UserSearchVo.Membership membership = new UserSearchVo.Membership();
        if(need_trace) {
            if(orgNamePath != null && orgNamePath.size() > 0){
                membership.setTrace(orgNamePath);
            }else {
                List<Long> userOrgIdList = userOrgDBO.getUserOrgIdList(userEntity.getId());
                List<String> namePath = orgDBO.getNamePathByIdPath(orgDBO.getById(userOrgIdList.get(0)).getOrgPath());
                Collections.reverse(namePath);
                membership.setTrace(namePath);
            }
        }
        userSearchVo.setSecondaryDisplayContent(membership);
        return userSearchVo;
    }

    /**
     * 根据实例id获取ldapsever配置
     *
     * @param instanceID
     * @return
     */
    @Override
    public Map<String, Object> getLdapServerConfig(String instanceID) {

        if (StringUtils.isBlank(instanceID)) {
            instanceID = "default";
        }
        String tenantCode = TenantHolder.getTenantCode();
        LdapInstEntity oneByInstance = ldapInstDBO.getOneByInstance(instanceID);

        Map<String,Object> config = new HashMap<>();

        //服务器地址
        config.put("host", serverConfig.getLdapInnerHost());

        //认证帐号
        config.put("username",String.format("uid=%s,ou=svc,dc=%s,dc=%s,dc=com",oneByInstance.getUsername(), instanceID, tenantCode));
        //认证密码
        config.put("password",oneByInstance.getPassword());

        //端口
        Map<String,Integer> ports = new HashMap<>();
        ports.put("ldap", serverConfig.getLdapInnerPort());
        ports.put("ldaps", serverConfig.getLdapsInnerPort());
        config.put("port", ports);

//        TLS证书

//        LDAP Schema配置：
//        Base DN： dc=instance,dc=tenant,dc=com
        Map<String,String> base = new HashMap<>();
        base.put("baseDN",String.format("dc=%s,dc=%s,dc=com",instanceID,tenantCode));
        config.put("base",base);

//        Organization Schema配置
//        Additional Organization DN: ou=organization
//        Object Class:  groupOfUniqueNames
//        Object Filter:  (&(objectclass=groupOfUniqueNames)(status=ENABLED))
//        Name Attribute: cn
//        Description Attribute: description
        Map<String,String> org = new HashMap<>();
        org.put("addDN","ou=organization");
        org.put("objectClass","groupOfUniqueNames");
        org.put("filter","(&(objectclass=groupOfUniqueNames)(status=ENABLED))");
        org.put("name","cn");
        org.put("description","description");
        config.put("org",org);
//
//        User Schema配置
//        Additional User DN:  ou=people
//        User Object Class: inetOrgPerson
//        User Object Filter: (&(objectclass=inetOrgPerson)(status=ACTIVE))
//        User Name Attribute: uid
//        User First Name Attribute: giveName
//        User Last Name Attribute: sn
//        User Display Name Attribute: displayName
//        User Email Attribute: mail
//        User Mobile Attribute: mobile
//        User Unique ID Attribute: entryUUID
//        User DN Attribute: entryDN
        Map<String,String> user = new HashMap<>();
        user.put("addDN","ou=people");
        user.put("objectClass","inetOrgPerson");
        user.put("filter","(objectclass=inetOrgPerson)");
        user.put("name","uid");
        user.put("firstName","giveName");
        user.put("lastName","sn");
        user.put("displayName","displayName");
        user.put("mail","mail");
        user.put("mobile","mobile");
        user.put("uniqueID","entryUUID");
        user.put("dn","entryDN");
        config.put("user",user);

//        Membership Schema配置
//        Organization Member Attribute: uniqueMember
//        User Organization Membership Attribute: memberOf
        Map<String,String> membership = new HashMap<>();
        membership.put("orgUser","uniqueMember");
        membership.put("userOrg","memberOf");
        config.put("membership", membership);

        return config;
    }
}
