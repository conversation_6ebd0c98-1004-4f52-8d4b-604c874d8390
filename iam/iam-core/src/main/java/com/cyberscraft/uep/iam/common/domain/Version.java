package com.cyberscraft.uep.iam.common.domain;

public class Version implements Comparable<Version> {
    private int major = 0;
    private int minor1 = 0;
    private int minor2 = 0;

    public static Version of(String versionStr) {
        Version scv = new Version();

        String[] arrVersion = versionStr.split("\\.");
        for (int i = 0; i < arrVersion.length; i++) {
            switch (i) {
                case 0:
                    scv.major = Integer.valueOf(arrVersion[i]);
                    break;
                case 1:
                    scv.minor1 = Integer.valueOf(arrVersion[i]);
                    break;
                case 2:
                    scv.minor2 = Integer.valueOf(arrVersion[i]);
                    break;
            }
        }
        return scv;
    }

    @Override
    public int compareTo(Version v) {
        if (major > v.major) {
            return 1;
        } else if (major < v.major) {
            return -1;
        }

        if (minor1 > v.minor1) {
            return 1;
        } else if (minor1 < v.minor1) {
            return -1;
        }

        if (minor2 > v.minor2) {
            return 1;
        } else if (minor2 < v.minor2) {
            return -1;
        }

        return 0;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        Version version = (Version) o;

        if (major != version.major) return false;
        if (minor1 != version.minor1) return false;
        return minor2 == version.minor2;
    }

    @Override
    public int hashCode() {
        int result = major;
        result = 31 * result + minor1;
        result = 31 * result + minor2;
        return result;
    }
}