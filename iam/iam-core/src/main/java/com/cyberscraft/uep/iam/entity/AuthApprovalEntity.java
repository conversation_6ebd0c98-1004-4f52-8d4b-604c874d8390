package com.cyberscraft.uep.iam.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * IAM-iam_auth_approval表
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2019-07-04
 */
@TableName("iam_auth_approval")
public class AuthApprovalEntity implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * token绑定的用户id
     */
    private Long uid;

    /**
     * client id
     */
    private String clientId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 授权范围
     */
    private String scopes;

    /**
     * 记住授权时间
     */
    private LocalDateTime updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getScopes() {
        return scopes;
    }

    public void setScopes(String scopes) {
        this.scopes = scopes;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "AuthApprovalEntity{" +
                "id=" + id +
                ", uid=" + uid +
                ", clientId='" + clientId + '\'' +
                ", createTime=" + createTime +
                ", scopes='" + scopes + '\'' +
                ", updateTime=" + updateTime +
                '}';
    }
}
