package com.cyberscraft.uep.iam.service.data;

import com.cyberscraft.uep.account.client.domain.Connector;
import com.cyberscraft.uep.account.client.domain.ConnectorOrg;
import com.cyberscraft.uep.iam.entity.ExternalOrgEntity;
import com.cyberscraft.uep.iam.entity.OrgEntity;

import java.util.List;
import java.util.Map;

/***
 *
 * @date 2021/6/15
 * <AUTHOR>
 ***/
public interface IPushSnsGroupService {

    /***
     * 删除连接器对应的第三方平台用户组记录
     * @param id
     */
    void removeByConnector(Long id);

    /***
     *
     * @param thirdGroupCode
     * @param type
     * @param attrs
     * @return
     */
    ExternalOrgEntity getSnsGroupByThirdGroupCodeAndType(String thirdGroupCode, Integer type, String... attrs);

    /***
     *
     * @param thirdGroupCodes
     * @param type
     * @param attrs
     * @return
     */
    List<ExternalOrgEntity> getSnsGroupByThirdGroupCodeAndType(List<String> thirdGroupCodes, Integer type, String... attrs);

    /***
     *
     * @param groupId
     * @param type
     * @param attrs
     * @return
     */
    ExternalOrgEntity getSnsGroupByGroupAndType(Long groupId, Integer type, String... attrs);

    /***
     *
     * @param groupIds
     * @param type
     * @param attrs
     * @return
     */
    List<ExternalOrgEntity> getSnsGroupByGroupAndType(List<Long> groupIds, Integer type, String... attrs);


    /***
     *
     * @param connectorId
     * @param groupId
     * @return
     */
    ExternalOrgEntity getSnsGroupByConnector(Long connectorId, Long groupId);

    /***
     *
     * @param groupId
     * @return
     */
    List<ExternalOrgEntity> getSnsGroupsByGroup(Long groupId);

    /***
     *
     * @param connectorId
     * @param groupIds
     * @return
     */
    Map<Long, String> getSnsGroupCodesMapByConnector(Long connectorId, List<Long> groupIds);


    /***
     *
     * @param connectorId
     * @param groupIds
     * @return
     */
    Map<Long, ExternalOrgEntity> getSnsGroupsMapByConnector(Long connectorId, List<Long> groupIds);

    /****
     *
     * @param obj
     */
    void add(ExternalOrgEntity obj);

    /****
     *
     * @param obj
     */
    void modify(ExternalOrgEntity obj);

    /***
     *
     * @param id
     */
    void remove(Long id);

    void saveSnsGroupResult(ExternalOrgEntity snsGroup, ConnectorOrg externalOrg, OrgEntity org, String localOrgSource, Connector connector, String checksum);
}
