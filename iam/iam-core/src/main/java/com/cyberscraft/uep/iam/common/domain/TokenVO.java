package com.cyberscraft.uep.iam.common.domain;

import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.common.util.OAuth2Utils;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * TokenVO
 *
 * <AUTHOR>
 * @date 2021/12/19
 */

public class TokenVO implements Serializable {
    private static final long serialVersionUID = 4512873694756755815L;
    private String grant_type;
    private String code;
    private String redirect_uri;
    private String client_id;
    private String client_secret;
    private String username;
    private String password;
    private String state;
    private String refresh_token;
    private String code_verifier;
    private String device_code;
    
    private String scope;

    public String getGrant_type() {
        return grant_type;
    }

    public void setGrant_type(String grant_type) {
        this.grant_type = grant_type;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getRedirect_uri() {
        return redirect_uri;
    }

    public void setRedirect_uri(String redirect_uri) {
        this.redirect_uri = redirect_uri;
    }

    public String getClient_id() {
        return client_id;
    }

    public void setClient_id(String client_id) {
        this.client_id = client_id;
    }

    public String getClient_secret() {
        return client_secret;
    }

    public void setClient_secret(String client_secret) {
        this.client_secret = client_secret;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getRefresh_token() {
        return refresh_token;
    }

    public void setRefresh_token(String refresh_token) {
        this.refresh_token = refresh_token;
    }

    public String getCode_verifier() {
        return code_verifier;
    }

    public void setCode_verifier(String code_verifier) {
        this.code_verifier = code_verifier;
    }

    public Map<String, String> toMap(){
        Map<String, String> map =  new HashMap<>();
        if (this.code != null) {
            map.put("code", this.code);
        }

        if (this.redirect_uri != null) {
            map.put("redirect_uri", this.redirect_uri);
        }

        if (this.client_id != null) {
            map.put(OAuth2Utils.CLIENT_ID, this.client_id);
        }

        if (this.client_secret != null) {
            map.put("client_secret",this.client_secret);
        }

        if (this.grant_type != null) {
            map.put(OAuth2Utils.GRANT_TYPE, this.grant_type);
        }

        if (this.username != null) {
            map.put("username", this.username);
        }

        if (this.password != null) {
            map.put("password", this.password);
        }

        if (this.state != null) {
            map.put(OAuth2Utils.STATE, this.state);
        }

        if (this.refresh_token != null) {
            map.put(OAuth2AccessToken.REFRESH_TOKEN, this.refresh_token);
        }

        if (this.code_verifier != null)
            map.put("code_verifier", this.code_verifier);
        
        if (this.scope != null) {
            map.put("scope", this.scope);
        }
        
        if (this.device_code != null) {
            map.put("device_code", this.device_code);
        }

        return map;

    }

    @Override
    public String toString() {
        return "grant_type:" + grant_type + ";code:" + code +";redirect_uri:"+ redirect_uri +";client_id:"+ client_id;
    }

    public String getScope() {
        return scope;
    }

    public void setScope(String scope) {
        this.scope = scope;
    }

    public String getDevice_code() {
        return device_code;
    }

    public void setDevice_code(String device_code) {
        this.device_code = device_code;
    }
}
