package com.cyberscraft.uep.iam.dbo.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cyberscraft.uep.iam.entity.SystemInfoEntity;
import com.cyberscraft.uep.iam.dao.SystemInfoDao;
import com.cyberscraft.uep.iam.dbo.SystemInfoDBO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2019-08-12
 */
@Service
public class SystemInfoDBOImpl extends ServiceImpl<SystemInfoDao, SystemInfoEntity> implements SystemInfoDBO {

    @Override
    public SystemInfoEntity getConfigByKey(String configKey) {
        QueryWrapper<SystemInfoEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.select().lambda().eq(SystemInfoEntity::getConfig<PERSON>ey, config<PERSON>ey);
        return super.getOne(queryWrapper, true);
    }
}
