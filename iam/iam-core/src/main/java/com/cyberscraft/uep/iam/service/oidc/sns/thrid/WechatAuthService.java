package com.cyberscraft.uep.iam.service.oidc.sns.thrid;

import com.alibaba.fastjson.JSONObject;
import com.cyberscraft.uep.account.client.domain.account.WechatAccount;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.common.util.RestAPIUtil;
import com.cyberscraft.uep.iam.dto.domain.ThirdIdpAuthConfig;
import com.cyberscraft.uep.iam.dto.enums.ThirdIdpTypeEnum;
import com.cyberscraft.uep.iam.service.oidc.exceptions.SnsException;
import com.cyberscraft.uep.iam.service.oidc.sns.IThirdPartyAuthService;
import com.cyberscraft.uep.iam.service.user.IUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * @description: 微信网站应用认证服务
 * @author: liusen
 * @date: 2024/1/26
 */
@Service
public class WechatAuthService implements IThirdPartyAuthService {

    @Autowired
    private IUserService userService;

    private static final Logger logger = LoggerFactory.getLogger(WechatAuthService.class);

    @Override
    public Map<String, Object> getUserByOAuth(String code, ThirdIdpAuthConfig authConfig) {
        logger.info("使用第三方[{}]登录请求，code:{}", authConfig.getIdpType(), code);
        Map<String,Object> map = getAccessToken(code, authConfig);
        String accessToken = map.get("access_token").toString();
        String openid = map.get("openid").toString();
        String url = "https://api.weixin.qq.com/sns/userinfo?access_token="+accessToken+"&openid="+openid+"&lang=zh_CN";
        Map userMap;

        try {
            String resp = RestAPIUtil.getStringForEntity(url);
            JSONObject json = JSONObject.parseObject(resp);
            if (null==json||null!=json.get("errcode")) {
                logger.info("请求 Wechat 获取用户信息出错,响应结果为：{}",json+"");
                throw new SnsException("请求 Wechat 获取用户信息失败");
            }
            userMap = json.getInnerMap();
            userMap.put("connectorId",Long.valueOf(authConfig.getId()));
            userMap.put("type","WECHAT");
            WechatAccount wechatAccount = (WechatAccount) authConfig.getConfig();
            userMap.put("canRegistor",""+wechatAccount.getCanRegistor());
        } catch (SnsException se) {
            logger.info("使用第三方[{}]登录请求,异常信息:{}", authConfig.getIdpType(), se.getMessage(), se);
            throw new RuntimeException(authConfig.getIdpType() + " 登录失败, 失败原因: " + se.getMessage());
        } catch (Exception e) {
            logger.info("使用第三方[{}]登录请求,异常信息:{}", authConfig.getIdpType(), e.getMessage(), e);
            throw new RuntimeException(authConfig.getIdpType() + " 登录失败");
        }
        logger.info("使用第三方[{}]登录请求，登录成功，用户信息是:{}", authConfig.getIdpType(), JsonUtil.obj2Str(userMap));
        //微信扫码登录状态判断
        userService.getWechatStatus(userMap);
        return userMap;
    }


    @Override
    public boolean supports(String type) {
        return ThirdIdpTypeEnum.WECHAT.getName().equals(type);
    }

    private Map<String,Object> getAccessToken(String code, ThirdIdpAuthConfig authConfig) {

        WechatAccount WechatAccount = (WechatAccount) authConfig.getConfig();
        logger.info("使用第三方[{}]登录请求，code:{}", authConfig.getIdpType(), code);
        String appId = WechatAccount.getAppId();
        String appSecret = WechatAccount.getAppSecret();
        String tokenUrl = "https://api.weixin.qq.com/sns/oauth2/access_token?appid="+appId
                +"&secret="+appSecret+"&code="+code+"&grant_type=authorization_code";
        Map<String,Object> map;
        try{
            String resp = RestAPIUtil.getStringForEntity(tokenUrl);
            JSONObject json = JSONObject.parseObject(resp);
            if(null!=json.get("access_token")){
                map = json.getInnerMap();
            }else{
                logger.info("微信网站应用获取accessToken失败,响应结果:{}",json.toString() );
                throw new RuntimeException("微信认证源获取accessToken失败");
            }
        }catch (Exception e){
            logger.info("使用第三方[{}]登录请求,异常信息:{}", authConfig.getIdpType(), e.getMessage(), e);
            throw new RuntimeException("微信认证源获取accessToken,请求失败");
        }
        return map;

    }

    public Map<String, Object> getWechatUser(String code, ThirdIdpAuthConfig authConfig) {
        logger.info("使用第三方[{}]登录请求，code:{}", authConfig.getIdpType(), code);
        Map<String,Object> map = getAccessToken(code, authConfig);
        String accessToken = map.get("access_token").toString();
        String openid = map.get("openid").toString();
        String url = "https://api.weixin.qq.com/sns/userinfo?access_token="+accessToken+"&openid="+openid+"&lang=zh_CN";
        Map userMap;

        try {
            String resp = RestAPIUtil.getStringForEntity(url);
            JSONObject json = JSONObject.parseObject(resp);
            if (null==json||null!=json.get("errcode")) {
                logger.info("请求 Wechat 获取用户信息出错,响应结果为：{}",json+"");
                throw new RuntimeException("请求 Wechat 获取用户信息失败");
            }
            userMap = json.getInnerMap();
            userMap.put("connectorId",Long.valueOf(authConfig.getId()));
            userMap.put("type","WECHAT");
            WechatAccount wechatAccount = (WechatAccount) authConfig.getConfig();
            userMap.put("canRegistor",""+wechatAccount.getCanRegistor());
        } catch (Exception e) {
            logger.info("使用第三方[{}]登录请求,异常信息:{}", authConfig.getIdpType(), e.getMessage(), e);
            throw new RuntimeException(authConfig.getIdpType() + " 登录失败");
        }
        logger.info("使用第三方[{}]登录请求，登录成功，用户信息是:{}", authConfig.getIdpType(), JsonUtil.obj2Str(userMap));
        return userMap;
    }

}
