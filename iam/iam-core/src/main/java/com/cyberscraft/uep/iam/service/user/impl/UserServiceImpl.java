package com.cyberscraft.uep.iam.service.user.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.cyberscraft.uep.account.client.constant.ProcessFlowType;
import com.cyberscraft.uep.account.client.domain.LocalUser;
import com.cyberscraft.uep.account.client.domain.account.WechatAccount;
import com.cyberscraft.uep.account.client.domain.account.WechatOfficialAccount;
import com.cyberscraft.uep.common.bean.RestApiResponse;
import com.cyberscraft.uep.common.config.ServerConfig;
import com.cyberscraft.uep.common.dto.PageView;
import com.cyberscraft.uep.common.dto.Pagination;
import com.cyberscraft.uep.common.dto.QueryPage;
import com.cyberscraft.uep.common.util.*;
import com.cyberscraft.uep.common.util.RestTemplateFactory;
import com.cyberscraft.uep.common.util.StringUtil;
import com.cyberscraft.uep.iam.common.constants.CacheNameConstant;
import com.cyberscraft.uep.iam.common.constants.DaoConstants;
import com.cyberscraft.uep.iam.common.constants.ThreadPoolNameConstant;
import com.cyberscraft.uep.iam.common.domain.Pair;
import com.cyberscraft.uep.iam.common.domain.configparams.CfgPasswordPolicy;
import com.cyberscraft.uep.iam.common.domain.configparams.CfgPoliciesLogin;
import com.cyberscraft.uep.iam.common.domain.configparams.CfgPoliciesSignup;
import com.cyberscraft.uep.iam.common.domain.configparams.CfgPwdComplexity;
import com.cyberscraft.uep.iam.common.enums.LocalAttr;
import com.cyberscraft.uep.iam.common.enums.LocalOrgAttr;
import com.cyberscraft.uep.iam.common.enums.LocalUserAttr;
import com.cyberscraft.uep.iam.common.enums.SyncDirectionEnum;
import com.cyberscraft.uep.iam.common.exception.*;
import com.cyberscraft.uep.iam.common.util.*;
import com.cyberscraft.uep.iam.constants.CommonConstants;
import com.cyberscraft.uep.iam.constants.RedisKeyConstants;
import com.cyberscraft.uep.iam.dao.AuditLogDao;
import com.cyberscraft.uep.iam.dao.UserDao;
import com.cyberscraft.uep.iam.dao.UserOrgDao;
import com.cyberscraft.uep.iam.dbo.*;
import com.cyberscraft.uep.iam.dto.domain.ThirdIdpAuthConfig;
import com.cyberscraft.uep.iam.dto.domain.UserQueryDTO;
import com.cyberscraft.uep.iam.dto.enums.*;
import com.cyberscraft.uep.iam.dto.request.*;
import com.cyberscraft.uep.iam.dto.request.configs.MultiLangVO;
import com.cyberscraft.uep.iam.dto.request.criteria.FilterGroup;
import com.cyberscraft.uep.iam.dto.request.criteria.FilterItem;
import com.cyberscraft.uep.iam.dto.request.sso.OidcConfig;
import com.cyberscraft.uep.iam.dto.response.*;
import com.cyberscraft.uep.iam.entity.*;
import com.cyberscraft.uep.iam.errors.TransactionErrorType;
import com.cyberscraft.uep.iam.query.UserQueryCondition;
import com.cyberscraft.uep.iam.service.*;
import com.cyberscraft.uep.iam.service.auditlog.annotation.ServiceLog;
import com.cyberscraft.uep.iam.service.connector.IConnectorService;
import com.cyberscraft.uep.iam.service.data.IPushConnectorService;
import com.cyberscraft.uep.iam.service.data.transfer.UserOrgEntityTransfer;
import com.cyberscraft.uep.iam.service.oidc.domain.UserInfo;
import com.cyberscraft.uep.iam.service.oidc.exceptions.NeedUserChooseDSException;
import com.cyberscraft.uep.iam.service.oidc.exceptions.UserNotFoundException;
import com.cyberscraft.uep.iam.service.oidc.sns.IThirdPartyAuthService;
import com.cyberscraft.uep.iam.service.oidc.transfer.Oauth2Transfer;
import com.cyberscraft.uep.iam.service.oidc.util.SecureRequestCheck;
import com.cyberscraft.uep.iam.service.transfer.*;
import com.cyberscraft.uep.iam.service.user.*;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.assertj.core.util.Strings;
import org.mybatis.spring.MyBatisSystemException;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.session.FindByIndexNameSessionRepository;
import org.springframework.session.Session;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.cyberscraft.uep.iam.common.constants.DaoConstants.ACCOUNT_CREATE;
import static com.cyberscraft.uep.iam.common.constants.DaoConstants.APPLY_EXPLAIN;
import static com.cyberscraft.uep.iam.dto.enums.CreatedMode.*;
import static com.cyberscraft.uep.iam.dto.enums.UserStatus.*;
import static com.cyberscraft.uep.iam.errors.TransactionErrorDescription.INVALID_REQUEST_PARAM_ERROR_DESC;
import static com.cyberscraft.uep.iam.errors.TransactionErrorDescription.USER_DUPLICATED_ERROR_DESC;
import static com.cyberscraft.uep.iam.errors.TransactionErrorType.USER_NOT_IN_SCOPE_ERROR;
import static java.time.format.DateTimeFormatter.ISO_DATE;

/**
 * <p>
 * 用户服务
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2019-08-28 11:26
 */
@Service
public class UserServiceImpl implements IUserService {
    private Logger logger = LoggerFactory.getLogger(UserServiceImpl.class);

    @Autowired
    private IApplyProcessLogService iApplyProcessLogService;

    @Autowired
    private IConfigService configService;
    @Autowired
    private IOrganizationService iOrganizationService;
    @Autowired
    private ITagService iTagService;
    @Autowired
    private Oauth2Transfer oauth2Transfer;
    @Autowired
    private UserCertificateDBO userCertificateDbo;
    @Autowired
    private IDataSyncService dataSyncService;
    @Autowired
    private IFieldDictService fieldDictService;
    @Autowired
    private UserDBO userDBO;
    @Autowired
    private UserDao userDao;
    @Autowired
    private AuthApprovalDBO authApprovalDBO;
    @Autowired
    private AuthApprovalTransfer authApprovalTransfer;
    @Autowired
    private PasswordEncoder passwordEncoder;
    @Autowired
    private UserTransfer userTransfer;
    @Autowired
    protected OrgDBO orgDBO;
    @Autowired
    private EmailService emailService;
    @Autowired
    private SmsService smsService;
    @Autowired
    private DeletedItemTransfer deletedItemTransfer;
    @Autowired
    private DeletedUserDBO deletedUserDBO;
    @Autowired
    private ISnsUserService snsUserService;
    @Autowired
    private ExternalUserDBO externalUserDBO;
    @Autowired
    private FieldDictDBO fieldDictDBO;
    @Resource
    private UserOrgDBO userOrgDBO;
    @Autowired(required = false)
    private TokenStore tokenStore;
    @Autowired
    private FindByIndexNameSessionRepository<? extends Session> sessionRepository;

    @Resource
    private UserOrgDao userOrgDao;

    @Resource
    private AuditLogDao auditLogDao;

    @Resource
    protected UserOrgEntityTransfer userOrgEntityTransfer;

    @Autowired
    private IPermissionService iPermissionService;

    @Autowired
    private IExcelUserService excelUserService;

    @Autowired
    private ITenantService tenantService;

    @Autowired
    private IImportUserHistoryService importUserHistoryService;

    @Autowired
    private IApplyProcessLogService applyProcessLogService;

    @Autowired
    private FieldExtDataHandler fieldExtDataHandler;

    @Resource
    private ServerConfig serverConfig;

    @Resource
    private IUserStatusService userStatusService;

    @Resource
    private IExternalUserService externalUserService;

    @Resource
    private IPushConnectorService pushConnectorService;


    @Autowired
    private IFieldDictService iFieldDictService;

    @Resource
    private IConnectorService connectorService;

    @Resource(name = ThreadPoolNameConstant.COMMON_POOL_NAME)
    private ExecutorService commonExecutor;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private IRoleBindingService roleBindingService;

    @Autowired
    private IThirdIDPService thirdIDPService;

    @Autowired
    private IRoleService roleService;

    @Autowired
    private LoginService loginService;

    @Autowired
    private ITagService tagService;

    @Autowired
    private CacheService cacheService;

    @Autowired
    private UserDetailsService userDetailsService;

    @Autowired
    private UserOtpDBO userOtpDBO;

    @Resource
    private MessageService messageService;
    @Autowired
    private ThreadPoolTaskExecutor iamAsyncExecutor;
    @Autowired
    private AppDBO appDBO;

    @Value("${database.type:mysql}")
    private String databaseType;

    private final static String PASSWD_FAIL_COUNT_FOR_LOCK = "LOCK_USER:%s:%s";

    static final String DELETE_ORG = "DELETE_ORG";

    static final String EDIT_ORG = "EDIT_ORG";

    /**
     * 默认排序间隔
     */
    private static final int USER_ORDER_INCREMENT = 100;

    /**
     * 默认排序最小值
     */
    private static final int MIN_USER_ORDER = 0;

    /**
     * 中间页的第一个/最后一个用户默认的间隔
     */
    private static final int ORDER_ADJUSTMENT = 8;

    @Override
    public LocalUser buildLocalUser(Map<String, Object> localUserMap) {
        LocalUser localUser = new LocalUser() {
            @Override
            public String getStatusKey() {
                return LocalUserAttr.status.getDomainName();
            }
        };
        localUser.putAll(localUserMap);
        return localUser;
    }

    @Override
    public Map<String, Object> getLocalMappingUser(String username, String... domainAttrs) {
        try {
            Set<String> basicFieldNames = new HashSet<>();
            if (domainAttrs != null && domainAttrs.length > 0) {
                Map<Integer, Set<String>> fieldNameMap = TransferUtil.toFieldNames(fieldDictService, FieldType.USER, domainAttrs);
                basicFieldNames = fieldNameMap.get(TypeMapper.asInt(BY_SYSTEM));
                if (basicFieldNames == null) {
                    basicFieldNames = new HashSet<>();
                }
                basicFieldNames.add(LocalUserAttr.sub.getFieldName());
            }

            //query user
            String entityField[] = basicFieldNames.toArray(new String[0]);
            Map<String, Object> userMap = userDBO.getOneMapUser(username, entityField);
            if (userMap == null) {
                throw new UserException(HttpStatus.NOT_FOUND, TransactionErrorType.USER_NOT_FOUND);
            }

            return processMapUser(userMap, domainAttrs);
        } catch (ExceptDbDataNotFound exception) {
            logger.error("getMapUserByUsername: username {} not found", username);
            throw new UserException(HttpStatus.NOT_FOUND, TransactionErrorType.USER_NOT_FOUND);
        }
    }

    @Override
    public Map<String, Object> getLocalMappingUser(Long userId, String... domainAttrs) {
        try {
            List<String> fieldAttrs = toFieldAttrs(domainAttrs);

            //query user
            String entityField[] = fieldAttrs.toArray(new String[0]);
            Map<String, Object> userMap = userDBO.getMapUser(userId, entityField);
            if (userMap == null) {
                throw new UserException(HttpStatus.NOT_FOUND, TransactionErrorType.USER_NOT_FOUND);
            }

            return processMapUser(userMap, domainAttrs);
        } catch (ExceptDbDataNotFound exception) {
            logger.error("getMapUserByUsername: userId {} not found", userId);
            throw new UserException(HttpStatus.NOT_FOUND, TransactionErrorType.USER_NOT_FOUND);
        }
    }

    private List<String> toFieldAttrs(String... domainAttrs) {
        List<String> fieldAttrs = new ArrayList<>();
        if (domainAttrs != null && domainAttrs.length > 0) {
            String[] attrList = ParamValidationUtil.vo2poAttrs(domainAttrs, this::toFieldName);
            fieldAttrs.addAll(Arrays.asList(attrList));
            fieldAttrs.add(LocalUserAttr.sub.getFieldName());
        }

        //过滤null属性
        fieldAttrs = fieldAttrs.stream().filter(e -> StringUtils.isNotBlank(e)).collect(Collectors.toList());
        return fieldAttrs;
    }

    private Map<String, Object> processMapUser(Map<String, Object> userMap, String... domainAttrs) {
        Long uid = Long.parseLong(userMap.get(LocalUserAttr.sub.getFieldName()).toString());
        String userJobNumber = null;
        if (userMap.get(LocalUserAttr.user_job_number.getFieldName()) != null) {
            userJobNumber = userMap.get(LocalUserAttr.user_job_number.getFieldName()).toString();
        }

        //query org
        List<String> orgIds = new ArrayList<>();
        List<Map<String, String>> orgIdNames = new ArrayList<>();
        Set<Long> uidSet = new HashSet<>();
        Map<String, String> orgMapById = new HashMap<>();
        // 用于计算是否为部门主管
        Map<String, String> managerMapById = new HashMap<>();
        uidSet.add(uid);
        Map<Long, List<OrgEntity>> orgMap = this.iOrganizationService.getUserOrgEntityList(uidSet);
        List<OrgEntity> orgEntityList = orgMap.get(uid);
        if (orgEntityList != null) {
            for (OrgEntity orgEntity : orgEntityList) {
                Map<String, String> oneOrgMap = new HashMap<>();
                String orgId = orgEntity.getId().toString();
                orgIds.add(orgId);
                oneOrgMap.put(LocalOrgAttr.id.getFieldName(), orgId);
                oneOrgMap.put(LocalOrgAttr.name.getFieldName(), orgEntity.getName());
                oneOrgMap.put(LocalOrgAttr.org_path.getFieldName(), orgEntity.getOrgPath());
                orgIdNames.add(oneOrgMap);

                orgMapById.put(orgId, orgEntity.getName());
                managerMapById.put(orgId, orgEntity.getManager());
            }
        }

        String mainUserCode = null;
        int mainIndex = -1;
        List<UserOrgInfoVO> userOrgInfos = iOrganizationService.getUserOrgInfo(uid);

        for (int i = 0; i < userOrgInfos.size(); i++) {
            UserOrgInfoVO userOrgInfo = userOrgInfos.get(i);
            userOrgInfo.setOrgName(orgMapById.get(userOrgInfo.getOrgId()));
            // 默认非主管
            userOrgInfo.setIsManager(0);
            String manager = managerMapById.get(userOrgInfo.getOrgId());
            if (StringUtils.isNotBlank(manager)) {
                List<Long> managerIds = JsonUtil.str2List(manager, Long.class);
                if (managerIds.contains(uid)) {
                    userOrgInfo.setIsManager(1);
                }
            }
            if (mainIndex == -1 && StringUtils.isNotBlank(userOrgInfo.getUserCode())) {
                mainUserCode = userOrgInfo.getUserCode();
                mainIndex = i;
            }
            if (userOrgInfo.getIsMain() != null && userOrgInfo.getIsMain() == 1) {
                mainUserCode = userOrgInfo.getUserCode();
                mainIndex = i;
            }
        }
        if (mainIndex != -1) {
            UserOrgInfoVO userOrgInfoVO = userOrgInfos.get(mainIndex);
            userOrgInfoVO.setIsMain(1);
        } else if (CollectionUtils.isNotEmpty(userOrgInfos)) {
            userOrgInfos.get(0).setIsMain(1);
            mainUserCode = userOrgInfos.get(0).getUserCode();
        }

        if (StringUtils.isBlank(userJobNumber)) {
            userJobNumber = mainUserCode;
        }

        Map<Integer, Set<String>> fieldNameMap = TransferUtil.toFieldNames(fieldDictService, FieldType.USER, domainAttrs);
        Set<String> extFieldNames = fieldNameMap.get(TypeMapper.asInt(BY_ADMIN));
        addExtFields(userMap, uid, extFieldNames);

        //获取用户对应的角色信息
        List<Long> tagList = tagService.getUserTagList(uid);

        Map<String, Object> resultMap = userTransfer.toDtoMap(userMap);
        resultMap.put(LocalUserAttr.user_job_number.getDomainName(), userJobNumber);
        resultMap.put(LocalUserAttr.org_ids.getDomainName(), orgIds);
        resultMap.put(DaoConstants.ORGS, orgIdNames);
        resultMap.put(LocalUserAttr.group_positions.getDomainName(), userOrgInfos);
        resultMap.put(LocalUserAttr.role_positions.getDomainName(), tagList);
        resultMap.put(LocalUserAttr.status.getDomainName(), userMap.get(LocalUserAttr.status.getFieldName()));


        return resultMap;
    }

    @Override
    public Map<String, Object> getMapUserByUsername(String username) {
        try {

            Map<String, Object> resultMap = getLocalMappingUser(username);
            resultMap.put(DaoConstants.PERMISSIONS, iPermissionService.findUserClientPermissions(username, DaoConstants.CLIENT_UC_CONSOLE));
            resultMap.put(DaoConstants.PERMISSIONS_SETS, iPermissionService.findUserClientPermissionSet(username, DaoConstants.CLIENT_UC_CONSOLE));
            resultMap.put(DaoConstants.PERMISSIONS_SETS_TAGS, iPermissionService.findUserClientPermissionSetsTags(username, DaoConstants.CLIENT_UC_CONSOLE));

            return resultMap;
        } catch (ExceptDbDataNotFound exception) {
            logger.error("getMapUserByUsername: {} not found", username);
            throw new UserException(HttpStatus.NOT_FOUND, TransactionErrorType.USER_NOT_FOUND);
        }
    }

    @Override
    public Map<String, Object> getMapUserByUsername(String username, String... requestVOAttrs) {
        Map<String, Object> resultMap = getLocalMappingUser(username, requestVOAttrs);

        Long uid = Long.parseLong(resultMap.get(LocalUserAttr.sub.getDomainName()).toString());

//        UserOtpEntity userOtpEntity = userOtpDBO.getById(uid);
//        if (userOtpEntity == null || userOtpEntity.getStatus() != 1) {
//            resultMap.put("totp_enable", false);
//        } else {
//            resultMap.put("totp_enable", true);
//        }
//
        List<OrgEntity> allOrgs = iOrganizationService.getList(new OrgEntity());
        Map<Long, OrgEntity> allOrgMap = allOrgs.stream().collect(Collectors.toMap(OrgEntity::getId, Function.identity()));

        List<UserOrgInfoVO> userOrgInfos = (List<UserOrgInfoVO>) resultMap.get(LocalUserAttr.group_positions.getDomainName());

        // 管理员查看某个用户的具体信息 部门相关的只展示对应权限下的部门
        List<Long> scopes = roleBindingService.searchUserBindingScope();
        List<Long> allOrgList = null;
        if (CollectionUtils.isNotEmpty(scopes)) {
            List<OrgEntity> orgsList = orgDBO.getOrgsList(scopes, true);
            allOrgList = orgsList.stream().map(OrgEntity::getId).collect(Collectors.toList());
            Iterator<UserOrgInfoVO> userOrgInfoVOIterator = userOrgInfos.iterator();
            while (userOrgInfoVOIterator.hasNext()) {
                UserOrgInfoVO orgInfoVO = userOrgInfoVOIterator.next();
                if (CollectionUtils.isEmpty(allOrgList) || !allOrgList.contains(Long.valueOf(orgInfoVO.getOrgId()))) {
                    userOrgInfoVOIterator.remove();
                }
            }

            List orgIds = (List) resultMap.get(LocalUserAttr.org_ids.getFieldName());
            Iterator orgIdsIterator = orgIds.iterator();
            while (orgIdsIterator.hasNext()) {
                String id = (String) orgIdsIterator.next();
                if (CollectionUtils.isEmpty(allOrgList) || !allOrgList.contains(Long.parseLong(id))) {
                    orgIdsIterator.remove();
                }
            }
        }

        userOrgInfos.forEach(e -> {
            OrgEntity orgEntity = allOrgMap.get(Long.valueOf(e.getOrgId()));

            if (orgEntity != null) {
                e.setIsManager(0);
                String manager = orgEntity.getManager();
                if (StringUtils.isNotBlank(manager)) {
                    List<Long> managerIds = JsonUtil.str2List(manager, Long.class);
                    if (managerIds.contains(uid)) {
                        e.setIsManager(1);
                    } else {
                        e.setIsManager(0);
                    }
                }

                String[] split = orgEntity.getOrgPath().split(",");
                StringBuilder deptPath = new StringBuilder();
                for (int i = 1; i < split.length; i++) {
                    deptPath.append("/").append(allOrgMap.get(Long.valueOf(split[i])).getName());
                }
                e.setDeptPath(deptPath.toString());

                List<String> list = roleBindingService.searchOrgPermissions(orgEntity.getId());

                if (CollectionUtils.isNotEmpty(list)) {
                    e.setReadonly(!list.contains(DELETE_ORG) && !list.contains(EDIT_ORG));
                } else {
                    e.setReadonly(false);
                }
            }
        });

        //query tag
        List<TagAndTagGroupVO> tagAndTagGroupInfo = tagService.getTagAndTagGroupByUserId(uid);
        // 完整的标签信息，包括标签以及对应的应用
        resultMap.put(LocalUserAttr.tag.getDomainName(), tagAndTagGroupInfo);
        Map<String, String> userComeFrom = calcUserComeFrom(Arrays.asList(resultMap));
        if (userComeFrom != null && userComeFrom.get(resultMap.get(LocalUserAttr.sub.getDomainName())) != null) {
            resultMap.put(LocalUserAttr.come_from.getDomainName(), userComeFrom.get(resultMap.get(LocalUserAttr.sub.getDomainName())));
        }
        return resultMap;
    }

    @Override
    public List<Map<String, Object>> getAllUsers(List<String> attrs) {
        QueryWrapper<UserEntity> queryWrapper = new QueryWrapper().select(StringUtils.join(attrs, ","));
        return userDBO.getBaseMapper().selectMaps(queryWrapper);
    }

    @Override
    public Map<String, Object> getMapUserByUserId(Long uid) {
        Map<String, Object> userMap = userDBO.getMapUser(uid);

        if (userMap == null) {
            throw new UserException(HttpStatus.NOT_FOUND, TransactionErrorType.USER_NOT_FOUND);
        }

        //query org
        Set<Long> uidSet = new HashSet<>();
        uidSet.add(uid);
        Map<Long, List<OrgEntity>> orgMap = this.iOrganizationService.getUserOrgEntityList(uidSet);
        List<OrgEntity> orgEntityList = orgMap.get(uid);

        List<UserOrgInfoVO> userOrgInfos = iOrganizationService.getUserOrgInfo(uid);

        Map<Integer, Set<String>> fieldNameMap = TransferUtil.toFieldNames(fieldDictService, FieldType.USER, null);
        Set<String> extFieldNames = fieldNameMap.get(TypeMapper.asInt(BY_ADMIN));
        addExtFields(userMap, uid, extFieldNames);

        Map<String, Object> resultMap = userTransfer.toDtoMap(userMap);
        resultMap.put(DaoConstants.ORGS, orgEntityList);
        resultMap.put(LocalUserAttr.group_positions.getDomainName(), userOrgInfos);

        return resultMap;
    }

    @Override
    public UserInfo getUserInfoByUsername(String username) {
        UserEntity userEntity = userDBO.getOneByUsername(username);
        return oauth2Transfer.toUserInfo(userEntity);
    }

    @Override
    public List<UserInfo> getUserInfoByData(String data) {
        UserEntity userEntity = new UserEntity();
        userEntity.setUsername(data);
        userEntity.setPhoneNumber(UserUtil.formatMobile(data));
        userEntity.setEmail(data);
        List<UserEntity> userInfoByData = userDBO.getUserInfoByEntity(userEntity);
        if (CollectionUtils.isNotEmpty(userInfoByData)) {
            return userInfoByData.stream()
                    .map(oauth2Transfer::toUserInfo)
                    .collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public List<UserInfo> getUserInfoByIds(List<Long> userIds) {
        return getUsers(userIds).stream()
                .map(oauth2Transfer::toUserInfo)
                .collect(Collectors.toList());
    }

    /**
     * load user info by loginId.
     *
     * @param loginId the loginId might be an username or an email address or mobile phone number.
     * @return UserEntity
     */
    @Override
    public UserEntity getUserInfoByLoginId(String loginId) {
        return getUserInfoByLoginId(loginId, true);
    }

    @Override
    public UserEntity getBaseUserInfoByLoginId(String loginId) {
        UserEntity userEntity = userDBO.getOneByUsername(loginId);
        if (null != userEntity) {
            return userEntity;
        }
        boolean isAllowEmailLogin = true;
        boolean isAllowMobileLogin = true;
        boolean isAllowAliasLogin = true;
        boolean isAllowJobNumberLogin = true;

        if (isAllowEmailLogin && UserUtil.isLoginIdEmail(loginId)) {
            // by email
            List<UserEntity> userEntities = userDBO.getVerifiedEmailUsers(loginId);
            if (userEntities.isEmpty()) {
                return null;
            }
            if (userEntities.size() == 1) {
                return userEntities.get(0);
            }
            return null;
        } else if (isAllowMobileLogin && UserUtil.isLoginIdMobile(loginId)) {
            // by mobile
            loginId = UserUtil.formatMobile(loginId);
            List<UserEntity> userEntities = userDBO.getVerifiedPhoneUsers(loginId);
            if (userEntities.isEmpty()) {
                return null;
            }
            if (userEntities.size() == 1) {
                return userEntities.get(0);
            }
            return null;
        } else if (isAllowAliasLogin) {
            // by alias
            return this.getUserInfoByAlias(loginId);
        } else if (isAllowJobNumberLogin) {
            return this.getUserInfoByJobNumber(loginId);
        } else {
            return null;
        }
    }

    /**
     * load user info by loginId.
     *
     * @param loginId            the loginId might be an username or an email address or mobile phone number.
     * @param followLoginPolicy, check the login policy
     * @return UserEntity
     */
    @Override
    public UserEntity getUserInfoByLoginId(String loginId, boolean followLoginPolicy) {
        UserEntity userEntity = userDBO.getOneByUsername(loginId);
        if (null != userEntity) {
            return userEntity;
        }
        boolean isAllowEmailLogin = configService.isAllowLoginByEmail();
        boolean isAllowMobileLogin = configService.isAllowLoginByPhone();
        boolean isAllowAliasLogin = configService.isAllowLoginByAlias();
        boolean isAllowJobNumberLogin = configService.isAllowLoginByJobNumber();

        if (isAllowEmailLogin && UserUtil.isLoginIdEmail(loginId)) {
            // by email
            List<UserEntity> userEntities = userDBO.getVerifiedEmailUsers(loginId);
            if (userEntities.isEmpty()) {
                return null;
            }
            if (userEntities.size() == 1) {
                return userEntities.get(0);
            }
            return null;
        } else if (isAllowMobileLogin && UserUtil.isLoginIdMobile(loginId)) {
            // by mobile
            loginId = UserUtil.formatMobile(loginId);
            List<UserEntity> userEntities = userDBO.getVerifiedPhoneUsers(loginId);
            if (userEntities.isEmpty()) {
                return null;
            }
            if (userEntities.size() == 1) {
                return userEntities.get(0);
            }
            return null;
        } else if (isAllowAliasLogin) {
            // by alias
            return this.getUserInfoByAlias(loginId);
        } else if (isAllowJobNumberLogin) {
            return this.getUserInfoByJobNumber(loginId);
        } else {
            return null;
        }

    }
    /**
     * load user info by Email、Phone.
     *
     */
    @Override
    public UserEntity getForgetPasswordUserInfoByLoginId(String loginId) {
        if (configService.isAllowForgetByEmail() && UserUtil.isLoginIdEmail(loginId)) {
            // by email
            List<UserEntity> userEntities = userDBO.getVerifiedEmailUsers(loginId);
            if (userEntities.isEmpty()) {
                return null;
            }
            if (userEntities.size() == 1) {
                return userEntities.get(0);
            }
        }
        if (configService.isAllowForgetByPhone() && UserUtil.isLoginIdMobile(loginId)) {
            // by mobile
            loginId = UserUtil.formatMobile(loginId);
            List<UserEntity> userEntities = userDBO.getVerifiedPhoneUsers(loginId);
            if (userEntities.isEmpty()) {
                return null;
            }
            if (userEntities.size() == 1) {
                return userEntities.get(0);
            }
        }
        return null;
    }

    @Override
    public UserEntity getUserInfoByAlias(String alias) {
        List<UserEntity> userEntityList = userDBO.getByAlias(alias);
        if (userEntityList.size() == 1) {
            return userEntityList.get(0);
        } else if (userEntityList.size() > 1) {
            StringBuilder rtnMsg = new StringBuilder();
            for (UserEntity userEntity : userEntityList) {
                String uid = userEntity.getUsername();
                rtnMsg.append(uid).append(CommonConstants.COMMA);
            }
            String errorMsg = rtnMsg.delete(rtnMsg.length() - 1, rtnMsg.length()).toString();
            logger.error("getBasicPersonByAlias: {} not found duplicated {}", alias, errorMsg);
            throw new DuplicatedUserFoundException(
                    String.format(USER_DUPLICATED_ERROR_DESC, errorMsg));
        } else {
            logger.error("getBasicPersonByAlias: {} not found", alias);
//            throw new UserException(HttpStatus.NOT_FOUND, TransactionErrorType.USER_NOT_FOUND);
            return null;
        }
    }

    @Override
    public UserEntity getUserInfoByJobNumber(String jobNumber) {
        List<UserEntity> userEntityList = userDBO.getByJobNumber(jobNumber);
        if (userEntityList.size() == 1) {
            return userEntityList.get(0);
        } else if (userEntityList.size() > 1) {
            StringBuilder rtnMsg = new StringBuilder();
            for (UserEntity userEntity : userEntityList) {
                String uid = userEntity.getUsername();
                rtnMsg.append(uid).append(CommonConstants.COMMA);
            }
            String errorMsg = rtnMsg.delete(rtnMsg.length() - 1, rtnMsg.length()).toString();
            logger.error("getBasicPersonByJobNumber: {} not found duplicated {}", jobNumber, errorMsg);
            throw new DuplicatedUserFoundException(
                    String.format(USER_DUPLICATED_ERROR_DESC, errorMsg));
        } else {
            logger.error("getBasicPersonByJobNumber: {} not found", jobNumber);
//            throw new UserException(HttpStatus.NOT_FOUND, TransactionErrorType.USER_NOT_FOUND);
            return null;
        }
    }

    @Override
    public List<UserEntity> getUsers(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return Collections.EMPTY_LIST;
        }
        return userDBO.listByIds(ids);
    }

    @Override
    public List<UserEntity> getUsersByOrgIds(List<Long> orgIds) {
        if (orgIds == null || orgIds.isEmpty()) {
            return Collections.EMPTY_LIST;
        }

        List<Long> userIdListByOrgIds = userOrgDBO.getUserIdListByOrgIds(orgIds);
        return getUsers(userIdListByOrgIds);
    }

    @Override
    @ServiceLog(description = "Service updateUser")
    @Transactional(rollbackFor = Exception.class)
    public Void updateUser(String username, Map<String, Object> userInfo, CreatedMode createdMode) throws UserCenterException {
        Pair<Map<String, Object>, Map<String, Object>> userAttrPair;
        // 分离出 扩展字段和基本字段
        Boolean hasUpdateTime = userInfo.containsKey(LocalUserAttr.update_time.getDomainName());
        Object updateTime = null;
        if (hasUpdateTime) {
            updateTime = userInfo.remove(LocalUserAttr.update_time.getDomainName());
        }
        String comeFrom = (String) userInfo.remove(LocalUserAttr.come_from.getDomainName());
        List<String> org_ids = (List<String>) userInfo.remove(LocalUserAttr.org_ids.getDomainName());
        Object groupPositions = userInfo.remove(LocalUserAttr.group_positions.getDomainName());
        if (createdMode != CreatedMode.BY_JIT && CollectionUtils.isNotEmpty(org_ids)) {
            if (CollectionUtils.isNotEmpty(org_ids)) {
                for (String orgId : org_ids) {
                    iOrganizationService.checkOrgInScopes(orgId, null);
                }
            }
        }
        String deptPath = (String) userInfo.remove(LocalUserAttr.dept_path.getDomainName());


        if (StringUtils.isNotBlank(comeFrom) && !comeFrom.equals("0")) {
            userAttrPair = userTransfer.toEntityMap(userInfo, username, OpConstraint.SYSTEM_OPERABLE);
        } else {
            userAttrPair = userTransfer.toEntityMap(userInfo, username, OpConstraint.ADMIN_OPERABLE, LocalUserAttr.username.getFieldName(), LocalUserAttr.password.getFieldName());
        }

        Map<String, Object> first = userAttrPair.getFirst();
        Map<String, Object> second = userAttrPair.getSecond();
        if (comeFrom != null) {
            first.put(LocalUserAttr.come_from.getFieldName(), comeFrom);
        }
        if (hasUpdateTime) {
            first.put(LocalUserAttr.update_time.getFieldName(), updateTime);
        }
        first.put(LocalUserAttr.org_ids.getFieldName(), org_ids);
        first.put(LocalUserAttr.dept_path.getFieldName(), deptPath);
        first.put(LocalUserAttr.group_positions.getFieldName(), groupPositions);
        return updateUserInternal(username, first, second, createdMode);
    }

    /**
     * 修改UserEntity，调用者维护用户的updateTime
     *
     * @param userEntity
     * @return
     */
    @Override
    @ServiceLog(description = "Service updateUser")
    @Transactional(rollbackFor = Exception.class)
    public Void updateUser(UserEntity userEntity) {
        if (userEntity.getStatus() != SUSPENDED.getValue()) {
            userEntity.setStatus(getUserStatusByDate(userEntity.getId(), userEntity.getStatus(), userEntity.getStartDate(), userEntity.getEndDate()));
        }
        userDBO.updateById(userEntity);
        return null;
    }

    @Override
    public Void updateUserInternal(String username, Map<String, Object> userEntity, Map<String, Object> userExtEntity, CreatedMode createdMode) throws UserCenterException {
        if (StringUtil.isEmptyOrNull(username)) {
            throw new UserException(HttpStatus.NOT_FOUND, TransactionErrorType.USER_NOT_FOUND);
        }

        if (userEntity.isEmpty() && userExtEntity.isEmpty()) {
            return null;
        }

        UserEntity userEntityInDB = userDBO.getOneByUsername(username,
                LocalUserAttr.sub.getFieldName(), LocalUserAttr.username.getFieldName(), LocalUserAttr.email.getFieldName(),
                LocalUserAttr.email_verified.getFieldName(), LocalUserAttr.phone_number.getFieldName(),
                LocalUserAttr.phone_number_verified.getFieldName(), LocalUserAttr.created_mode.getFieldName(),
                LocalUserAttr.come_from.getFieldName(), LocalUserAttr.status.getFieldName());
        if (userEntityInDB == null) {
            logger.error("user: {} not found", username);
            throw new UserCenterException(HttpStatus.NOT_FOUND, TransactionErrorType.USER_NOT_FOUND);
        }

//        if (CreatedMode.isReadOnly(userEntityInDB.getCreatedMode())) {
//            logger.warn("user's created mode is readonly, {}", username);
//            return null;
//        }

        //防止用户名大小写被修改
        username = userEntityInDB.getUsername();

        userEntity.put(LocalUserAttr.username.getFieldName(), username);

        Long uid = userEntityInDB.getId();
        if (createdMode != CreatedMode.BY_JIT) {
            List<Long> scopes = roleBindingService.searchUserBindingScope();
            if (CollectionUtils.isNotEmpty(scopes)) {
                Map<String, Object> resultMap = getLocalMappingUser(username);
                List<OrgEntity> orgsList = orgDBO.getOrgsList(scopes, true);
                List<Long> scopeIdList = orgsList.stream().map(OrgEntity::getId).collect(Collectors.toList());
                List oldOrgIds = (List) resultMap.get(LocalUserAttr.org_ids.getFieldName());
                List<UserOrgInfoVO> oldGroupPositions = (List) resultMap.get(LocalUserAttr.group_positions.getFieldName());

                List<OrgEntity> allOrgs = iOrganizationService.getList(new OrgEntity());
                Map<Long, OrgEntity> allOrgMap = allOrgs.stream().collect(Collectors.toMap(OrgEntity::getId, Function.identity()));

                List<UserOrgInfoVO> userOrgInfos = (List<UserOrgInfoVO>) resultMap.get(LocalUserAttr.group_positions.getDomainName());

                AtomicBoolean hasMain = new AtomicBoolean(false);
                Long finalUid = uid;
                userOrgInfos.forEach(e -> {
                    OrgEntity orgEntity = allOrgMap.get(Long.valueOf(e.getOrgId()));
                    if (e.getIsMain() != null && e.getIsMain() == 1) {
                        hasMain.set(true);
                    }
                    e.setIsManager(0);
                    String manager = orgEntity.getManager();
                    if (StringUtils.isNotBlank(manager)) {
                        List<Long> managerIds = JsonUtil.str2List(manager, Long.class);
                        if (managerIds.contains(finalUid)) {
                            e.setIsManager(1);
                        } else {
                            e.setIsManager(0);
                        }
                    }

                    String[] split = orgEntity.getOrgPath().split(",");
                    StringBuilder deptPath = new StringBuilder();
                    for (int i = 1; i < split.length; i++) {
                        deptPath.append("/").append(allOrgMap.get(Long.valueOf(split[i])).getName());
                    }
                    e.setDeptPath(deptPath.toString());

                });

                List newOrgIds = (List) userEntity.get(LocalUserAttr.org_ids.getFieldName());

                List newGroupPositions;
                Object groupPostions = userEntity.get(LocalUserAttr.group_positions.getFieldName());
                if (groupPostions instanceof String) {
                    newGroupPositions = JsonUtil.str2Obj((String) groupPostions, List.class);
                } else {
                    newGroupPositions = (List) groupPostions;
                }

                oldOrgIds.retainAll(scopeIdList);
                Collection orgIds = CollectionUtils.union(oldOrgIds, newOrgIds);
                List collect = (List) oldGroupPositions.stream().filter(e -> !scopeIdList.contains(Long.valueOf(e.getOrgId()))).map(e -> JSON.parse(JsonUtil.obj2Str(e))).collect(Collectors.toList());
                collect.stream().forEach((e) -> {
                    if (hasMain.get() && ((Map) e).get("is_main") != null && (Integer) ((Map) e).get("is_main") == 1) {
                        ((Map) e).put("is_main", 0);
                    }
                });
                Collection union = CollectionUtils.union(collect, newGroupPositions);
                userEntity.put(LocalUserAttr.org_ids.getFieldName(), orgIds);
                userEntity.put(LocalUserAttr.group_positions.getFieldName(), union);
            }
        }
        Long connectorId = 0L;
        String strConnectorId = getUserInfoAsString(userEntity, LocalUserAttr.come_from.getFieldName());
        if (StringUtils.isNotBlank(strConnectorId)) {
            connectorId = Long.valueOf(strConnectorId);
        }
        //else if(Objects.nonNull(userEntityInDB.getConnectorId())){
        //    connectorId = userEntityInDB.getConnectorId();
        //}
        List<String> new_org_ids = (List<String>) userEntity.remove(LocalUserAttr.org_ids.getFieldName());
        //部门职位
        String userOrgJsonStr;
        Object groupPostions = userEntity.get(LocalUserAttr.group_positions.getFieldName());
        if (groupPostions instanceof String) {
            userOrgJsonStr = (String) groupPostions;
        } else {
            userOrgJsonStr = JsonUtil.obj2Str(groupPostions);
        }
        //部门名称路径
        String deptPath = (String) userEntity.remove(LocalUserAttr.dept_path.getFieldName());
        //部门id
        if (StringUtils.isBlank(userOrgJsonStr) && (new_org_ids == null || new_org_ids.size() == 0)) {
            if (StringUtils.isNotBlank(deptPath)) {
                new_org_ids = deptPathToOrgPath(connectorId, deptPath, createdMode);
            }
        }


        if ((null != new_org_ids && 0 < new_org_ids.size()) ||
                StringUtils.isNotEmpty(userOrgJsonStr)) {
            if (createBySystem(userEntityInDB.getCreatedMode())) {
                logger.warn("user's created mode is by_system, {}", username);
                return null;
            }
        }

        /**
         * Logic for email phoneNumber verify flag with global unique attr
         */
        if (userEntity.containsKey(LocalUserAttr.email.getFieldName())) {
            handleEmailVerifyFlag(userEntity);
        }
        if (userEntity.containsKey(LocalUserAttr.phone_number.getFieldName())) {
            handlePhoneNumberVerifyFlag(userEntity);
        }

        userEntity.put(LocalUserAttr.sub.getFieldName(), userEntityInDB.getId());
        if (SUSPENDED.getValue() != userEntityInDB.getStatus()) {
            userEntity.put(LocalUserAttr.status.getFieldName(), getUserStatusByDate(userEntityInDB.getId(), userEntityInDB.getStatus(), (String) userEntity.get(LocalUserAttr.start_date.getFieldName()), (String) userEntity.get(LocalUserAttr.end_date.getFieldName())));
        }

        if (CreatedMode.createByJit(createdMode.getValue())) {
            userEntity.remove(LocalUserAttr.come_from.getFieldName());
        }


        boolean isSucceed = userDBO.updateByMap(userEntityInDB.getId(), userEntity);
        if (isSucceed && MapUtils.isNotEmpty(userExtEntity)) {
            fieldExtDataHandler.update(userEntityInDB.getId(), FieldType.USER, userExtEntity);
        }

        //update for orgs if any
        List<String> orgIdLongs;
        if (StringUtils.isNotBlank(userOrgJsonStr)) {
            List<UserOrgInfoVO> userOrgVoList = JsonUtil.str2List(userOrgJsonStr, UserOrgInfoVO.class);
            List<UserOrgEntity> userOrgPositions = userOrgEntityTransfer.toPO(userOrgVoList);

            orgIdLongs = userOrgDBO.rebuildUserOrgRelations(uid, userOrgPositions, connectorId);
        } else if (null != new_org_ids && new_org_ids.size() > 0) {
            orgIdLongs = userOrgDBO.rebuildUserOrgs(uid, new_org_ids, connectorId);
        } else {
            List<Long> userOrgIdLongList = iOrganizationService.getUserOrgIdLongList(uid);
            orgIdLongs = userOrgIdLongList.stream().map(e -> String.valueOf(e)).collect(Collectors.toList());
        }
        userEntity.put(LocalUserAttr.org_ids.getFieldName(), orgIdLongs);
        //数据推送
        dataSyncService.pushDataToApp(uid, PushBusinessType.USER, DataChangeType.UPDATE);
        // 更新完用户之后清除缓存
        cacheService.evictFromCache(CacheNameConstant.USER_UNIQUE_ATTR_CACHE_NAME, TenantHolder.getTenantCode());

        //update for tag if any
        Object tagGroupObj = userEntity.get(LocalUserAttr.tag.getFieldName());
        List<TagAndTagGroupVO> tagList = new ArrayList<>();
        if (null != tagGroupObj) {
            tagList = JsonUtil.str2List((String) tagGroupObj, TagAndTagGroupVO.class);
        }
        iTagService.removeUserTag(null, uid);
        if (CollectionUtils.isNotEmpty(tagList)) {
            // set去重
            Set<String> tagIds = tagList.stream().map(TagAndTagGroupVO::getId).collect(Collectors.toSet());
            iTagService.addUsersTag(tagIds, uid);
        }

        return null;
    }

    private void handlePhoneNumberVerifyFlag(Map<String, Object> userInfo) {
        String phoneNumber = getUserInfoAsString(userInfo, LocalUserAttr.phone_number.getDomainName(), null);
        if (Strings.isNullOrEmpty(phoneNumber)) {
            userInfo.put(LocalUserAttr.phone_number_verified.getDomainName(), BooleanEnums.FALSE.getValue());
        } else {
            userInfo.put(LocalUserAttr.phone_number_verified.getDomainName(), BooleanEnums.TRUE.getValue());
        }
    }

    private void handleEmailVerifyFlag(Map<String, Object> userInfo) {
        String emailAddr = getUserInfoAsString(userInfo, LocalUserAttr.email.getDomainName(), null);
        if (Strings.isNullOrEmpty(emailAddr)) {
            userInfo.put(LocalUserAttr.email_verified.getDomainName(), BooleanEnums.FALSE.getValue());
        } else {
            userInfo.put(LocalUserAttr.email_verified.getDomainName(), BooleanEnums.TRUE.getValue());
        }
    }

    @Override
    public boolean resetAdminPwd(TcUserResetPwdVO tcUserResetPwdVO) {
        if (StringUtils.isBlank(tcUserResetPwdVO.getNewPassword())) {
            logger.error(configService.getPwdErrorMsg());
            throw new UserException(HttpStatus.BAD_REQUEST, TransactionErrorType.INVALID_REQUEST_PARAM);
        }

        UserEntity userEntity = userDBO.getOneByUsername(DaoConstants.IAM_ADMIN_USER_NAME);
        if (userEntity == null) {
            throw new UserException(HttpStatus.BAD_REQUEST,
                    TransactionErrorType.USER_NOT_FOUND);
        }
        String newPassword = Base64Util.decodeToString(tcUserResetPwdVO.getNewPassword());
        updatePasswd(userEntity, newPassword);
        return true;
    }

    @Override
    public Void moveUserOrg(String username, MoveUserOrgInVO moveOrgRequest) {
        //if(StringUtil.isEmptyOrNull(username)) {
        //    throw new UserException(HttpStatus.NOT_FOUND, TransactionErrorType.USER_NOT_FOUND);
        //}
        //
        //PersonPO personPO = null;
        //try {
        //    personPO = userDao.getBasicPersonByUID(username);
        //    UserUtil.readOnlyUserCannotModify(personPO);
        //} catch (ExceptDbDataNotFound exception) {
        //    logger.error("user {} not found", username);
        //    throw new UserException(HttpStatus.NOT_FOUND, TransactionErrorType.USER_NOT_FOUND);
        //}
        //
        //if (CreatedMode.BY_SYSTEM.name().equals(personPO.getCreatedMode())) {
        //    String errorMessage = String.format("no admin %s to change org", username);
        //    logger.error(errorMessage);
        //    throw new UserException(HttpStatus.BAD_REQUEST, TransactionErrorType.USER_ADMIN_NO_ORG_CHANGE_ERROR);
        //}
        //
        //GroupPO sourceGroup = null;
        //
        //if (GroupPO.isDefaultGroup(moveOrgRequest.getSourceOrg())) {
        //    sourceGroup = null;
        //} else {
        //    try {
        //        sourceGroup = groupDao.getGroupById(moveOrgRequest.getSourceOrg());
        //        organizationService.rootOrgAndReadOnlyOrgCannotModify(sourceGroup);
        //        if (!Stream.of(personPO.getIsOrgOf()).anyMatch(x -> x.equals(moveOrgRequest.getSourceOrg()))) {
        //            logger.error("user {} not in org {}", username, moveOrgRequest.getSourceOrg());
        //            throw new UserException(HttpStatus.BAD_REQUEST, TransactionErrorType.ORG_HAS_NO_USER);
        //        }
        //    } catch (ExceptDbDataNotFound exception) {
        //        logger.error("org {} not found", moveOrgRequest.getSourceOrg());
        //        throw new UserException(HttpStatus.NOT_FOUND, TransactionErrorType.ORG_NOT_FOUND);
        //    }
        //}
        //
        //GroupPO targetGroup = null;
        //
        //if (GroupPO.isDefaultGroup(moveOrgRequest.getTargetOrg())) {
        //    targetGroup = null;
        //} else {
        //    try {
        //        targetGroup = groupDao.getGroupById(moveOrgRequest.getTargetOrg());
        //        organizationService.rootOrgAndReadOnlyOrgCannotModify(targetGroup);
        //        if (Stream.of(personPO.getIsOrgOf()).anyMatch(x -> x.equals(moveOrgRequest.getTargetOrg()))) {
        //            logger.error("user {} already in org {}", username, moveOrgRequest.getTargetOrg());
        //            throw new UserException(HttpStatus.BAD_REQUEST, TransactionErrorType.ORG_ALREADY_HAS_USER);
        //        }
        //    } catch (ExceptDbDataNotFound exception) {
        //        logger.error("org {} not found", moveOrgRequest.getTargetOrg());
        //        throw new UserException(HttpStatus.NOT_FOUND, TransactionErrorType.ORG_NOT_FOUND);
        //    }
        //}
        //
        //if (sourceGroup != null) {
        //    userDao.removeUserFromGroup(personPO, sourceGroup);
        //}
        //
        //if (targetGroup != null) {
        //    userDao.addUserToGroup(personPO, targetGroup);
        //}
        //
        ////TODO push user data
        return null;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createUser(Map<String, Object> userInfo, boolean sendWelcomeEmail, CreatedMode createdMode, Boolean isNotVerify) {
        if (isUserCountExceed()) {
            throw new UserException(HttpStatus.BAD_REQUEST, TransactionErrorType.USER_COUNT_EXCEED);
        }
        //1. first convert all to entity map
        String applyExplain = (String) userInfo.remove(APPLY_EXPLAIN);

        // 管理员创建的进行发送审批
        Boolean userCreateVerify = Boolean.FALSE;
        if (configService.getUserPolicyCfg() != null) {
            userCreateVerify = configService.getUserPolicyCfg().getUserCreateVerify();
        }
        if (BY_ADMIN == createdMode || BY_WECHAT == createdMode) {
            if ((!isNotVerify) && (userCreateVerify)) {
                userInfo.put("createdMode", createdMode);
                Long id = saveUserAndSendMessageByAdmin(userInfo, applyExplain);
                return id;
            }
        }

        String rawPassword = getUserInfoAsString(userInfo, LocalUserAttr.password.getFieldName());
        if (StringUtils.isBlank(rawPassword)) {
            CfgPwdComplexity pwdComplexityCfg = configService.getPwdComplexityCfg();
            int minLen = pwdComplexityCfg.getMinLen();
            if (minLen < 6) minLen = 6;
            rawPassword = RandomUtil.getRandomPassword(minLen);
            userInfo.put(LocalUserAttr.password.getFieldName(), rawPassword);
        }

        Long userId = createUser(userInfo, createdMode);

        if (sendWelcomeEmail && !StringUtil.isEmptyOrNull(getUserInfoAsString(userInfo, LocalUserAttr.email.getDomainName()))) {
            MultiLangVO ucName = configService.getBasicProfileCfg().getUcName();
            String platformName = null;
            if (ucName != null) {
                platformName = ucName.getZhCn();
            }

            userInfo.put("temp_password", rawPassword);
            emailService.sendWelcomeWithPwd(userInfo, platformName);
        }

        return userId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createUser(Map<String, Object> userInfo, CreatedMode createdMode) {
        if (isUserCountExceed()) {
            throw new UserException(HttpStatus.BAD_REQUEST, TransactionErrorType.USER_COUNT_EXCEED);
        }

        Pair<Map<String, Object>, Map<String, Object>> userAttrPair;
        //删除ORG_IDS，防止toEntityMap转化时数据类型发生变化
        List<String> org_ids = (List<String>) userInfo.remove(LocalUserAttr.org_ids.getDomainName());
        Object groupPositions = userInfo.remove(LocalUserAttr.group_positions.getDomainName());

        String comeFrom = (String) userInfo.remove(LocalUserAttr.come_from.getDomainName());
        String deptPath = (String) userInfo.remove(LocalUserAttr.dept_path.getDomainName());
        if (CollectionUtils.isNotEmpty(org_ids)) {
            for (String orgId : org_ids) {
                iOrganizationService.checkOrgInScopes(orgId, null);
            }
        }

        if (StringUtils.isNotBlank(comeFrom) && !comeFrom.equals("0")) {
            userAttrPair = userTransfer.toEntityMap(userInfo, (String) userInfo.get(LocalUserAttr.username.getDomainName()), OpConstraint.SYSTEM_OPERABLE);
        } else {
            userAttrPair = userTransfer.toEntityMap(userInfo, (String) userInfo.get(LocalUserAttr.username.getDomainName()), OpConstraint.ADMIN_OPERABLE);
        }
        Map<String, Object> userEntity = userAttrPair.getFirst();
        Map<String, Object> userExtEntity = userAttrPair.getSecond();

        if (needCheckMandatory(createdMode)) {
            checkUserInputHasMandatoryAttributes(userEntity);
        }

        //校验用户名，不能违反规则，不能是email格式，不能是手机号码格式 (放到username的validateRule实现)
        String password = getUserInfoAsString(userEntity, LocalUserAttr.password.getFieldName());
        CfgPasswordPolicy passwordPolicyCfg = configService.getPasswordPolicyCfg();
        if (StringUtils.isNotBlank(password)) {
            if (BY_ADMIN == createdMode && !(password.matches(configService.getPwdRegExpressionStr()))) {
                logger.error(configService.getPwdErrorMsg());
                throw new UserException(HttpStatus.BAD_REQUEST, TransactionErrorType.USER_ATTR_VALUE_INVALID, configService.getPwdErrorMsg());
            }
            userEntity.put(LocalUserAttr.password.getFieldName(), passwordEncoder.encode(password));
            Integer maxPasswordAgeDays = passwordPolicyCfg.getPwdMaxAge();
            if (maxPasswordAgeDays != null && maxPasswordAgeDays > 0) {
                int adjustedMaxAge = Math.min(maxPasswordAgeDays, 3650);
                userEntity.put(LocalUserAttr.pwd_expired_time.getFieldName(), LocalDateTime.now().plusDays(adjustedMaxAge));
            }
        }

        UserPWDStatus userPWDStausDefaultValue = UserPWDStatus.NORMAL;
        if (!noPasswordStatus(createdMode)) {
            if (configService.getPasswordPolicyCfg().getPwdMustChangeForAdminAddUser()) {
                userPWDStausDefaultValue = UserPWDStatus.TEMP;
            }
        }
        userEntity.put(LocalUserAttr.password_status.getFieldName(), UserUtil.getUserInfoAsInteger(userEntity, LocalUserAttr.password_status.getFieldName(), userPWDStausDefaultValue.getValue()));
        userEntity.put(LocalUserAttr.created_mode.getFieldName(), TypeMapper.asInt(createdMode));
        userEntity.put(LocalUserAttr.type.getFieldName(), TypeMapper.asInt(UserType.END_USER));
        userEntity.put(LocalUserAttr.create_time.getFieldName(), LocalDateTime.now());
        userEntity.put(LocalUserAttr.create_by.getFieldName(), SecureRequestCheck.getUsername());
        userEntity.put(LocalUserAttr.update_time.getFieldName(), LocalDateTime.now());
        userEntity.put(LocalUserAttr.update_by.getFieldName(), SecureRequestCheck.getUsername());
        if (StringUtils.isNotBlank(comeFrom)) {
            userEntity.put(LocalUserAttr.come_from.getFieldName(), comeFrom);
        }

        //通过当前时间 和 开始 结束 时间设置用户状态
        userEntity.put(LocalUserAttr.status.getFieldName(), getUserStatusByDate(null, (Integer) userEntity.get(LocalUserAttr.status.getFieldName()), (String) userEntity.get(LocalUserAttr.start_date.getFieldName()), (String) userEntity.get(LocalUserAttr.end_date.getFieldName())));

        //构造用户属性字段
        handleEmailVerifyFlag(userEntity);
        handlePhoneNumberVerifyFlag(userEntity);

        String username = getUserInfoAsString(userEntity, LocalUserAttr.username.getFieldName());
        //user must not exists
        UserEntity userExist = this.userDBO.getOneByUsername(username);
        if (userExist != null) {
            logger.error("the username  {} id already exists ", username);
            throw new UserException(HttpStatus.BAD_REQUEST, TransactionErrorType.USER_ALREADY_EXISTS);
        }

        try {
            // 微信自助注册，申请的过程中已生成id，无需重新生成id
            if (null == userInfo.get(LocalUserAttr.sub.getFieldName())) {
                userEntity.put(LocalUserAttr.sub.getFieldName(), IdWorker.getId());
            } else {
                userEntity.put(LocalUserAttr.sub.getFieldName(), userInfo.get(LocalUserAttr.sub.getFieldName()));
            }
            Long connectorId = 0L;
            String strConnectorId = getUserInfoAsString(userEntity, LocalUserAttr.come_from.getFieldName());
            if (StringUtils.isNotBlank(strConnectorId)) {
                connectorId = Long.valueOf(strConnectorId);
            }
            userEntity.put(LocalUserAttr.come_from.getFieldName(), connectorId);

            Map<String, Object> entityMap = userDBO.insertByMap(userEntity);
            final Long uid = (Long) entityMap.get(LocalUserAttr.sub.getFieldName());
            // 配置了不允许重复密码，则 保存进入数据库之后当前密码保存进历史密码
            Integer passwordHistoryCount = passwordPolicyCfg.getPwdInHistory();
            if (passwordHistoryCount != null && passwordHistoryCount > 0) {
                UserStatusEntity userStatus = new UserStatusEntity();
                userStatus.setId(uid);
                userStatus.setHistoryPasswd(JsonUtil.obj2Str(Collections.singleton(MD5Util.md5(password))));
                userStatusService.createOrUpdateUserStatus(userStatus);
            }

            //部门职位
            String userOrgJsonStr;
            if (groupPositions instanceof String) {
                userOrgJsonStr = (String) groupPositions;
            } else {
                userOrgJsonStr = JsonUtil.obj2Str(groupPositions);
            }
            //部门名称路径
            if (StringUtils.isBlank(userOrgJsonStr) && (org_ids == null || org_ids.isEmpty())) {
                if (StringUtils.isNotBlank(deptPath)) {
                    org_ids = deptPathToOrgPath(connectorId, deptPath, createdMode);
                } else if (BY_ADMIN == createdMode || BY_WECHAT == createdMode) {
                    org_ids = Arrays.asList(DaoConstants.NULL_ORG_ID.toString());
                }
            }
            if (StringUtils.isNotBlank(userOrgJsonStr)) {
                List<UserOrgInfoVO> userOrgVoList = JsonUtil.str2List(userOrgJsonStr, UserOrgInfoVO.class);
                List<UserOrgInfoVO> mainOrg = userOrgVoList.stream().filter(el -> (el.getIsMain() != null && el.getIsMain() == 1)).collect(Collectors.toList());
                if (mainOrg.size() == 0) {
                    UserOrgInfoVO defaultMain = userOrgVoList.get(0);
                    defaultMain.setIsMain(1);
                }
                List<UserOrgEntity> userOrgPositions = userOrgEntityTransfer.toPO(userOrgVoList);
                userOrgDBO.rebuildUserOrgRelations(uid, userOrgPositions, connectorId);
            } else if (org_ids != null && org_ids.size() > 0) {
                userOrgDBO.rebuildUserOrgs(uid, org_ids, connectorId);
            }

            //bind user tag
            Object tagGroupObj = userEntity.get(LocalUserAttr.tag.getFieldName());
            List<TagAndTagGroupVO> tagList = new ArrayList<>();
            if (null != tagGroupObj) {
                tagList = JsonUtil.str2List((String) tagGroupObj, TagAndTagGroupVO.class);
            }
            if (CollectionUtils.isNotEmpty(tagList)) {
                // set去重
                Set<String> tagIds = tagList.stream().map(TagAndTagGroupVO::getId).collect(Collectors.toSet());
                iTagService.addUsersTag(tagIds, uid);
            }

            // 保存扩展属性
            boolean isSucceed = fieldExtDataHandler.insert(uid, FieldType.USER, userExtEntity);
            // 保存用户信息后，恢复ORG_IDS
//            userInfo.put(DaoConstants.ORG_IDS, org_ids);
            //推送
            dataSyncService.pushDataToApp(uid, PushBusinessType.USER, DataChangeType.CREATE);
            if (isSucceed) {
                return uid;
            }

            logger.error("failed to insert user");
            throw new UserException(HttpStatus.BAD_REQUEST, TransactionErrorType.UNKNOWN_DAO_ERROR);

        } catch (ExceptDbItemExists e) {
            logger.error("", e);
            throw new UserException(HttpStatus.BAD_REQUEST, TransactionErrorType.USER_ALREADY_EXISTS);
        } catch (MyBatisSystemException myBatisErr) {
            if (myBatisErr.getCause().getCause() instanceof UserCenterException) {
                throw (UserCenterException) myBatisErr.getCause().getCause();
            } else {
                throw new UserException(HttpStatus.BAD_REQUEST, TransactionErrorType.UNKNOWN_DAO_ERROR, myBatisErr.getMessage());
            }
        } catch (UserCenterException | DataIntegrityViolationException e) {
            throw e;
        } catch (Exception e) {
            logger.error("", e);
            throw new UserException(HttpStatus.BAD_REQUEST, TransactionErrorType.UNKNOWN_DAO_ERROR, e.getMessage());
        }
    }

    @Override
    public void bindWechat(Long userId, String openId, Long connectorId) {
        RBucket<String> bucket2 = redissonClient.getBucket("headUrl-" + openId);
        String headUrl = bucket2.get();

        UserEntity u = getUser(userId);
        if (null != u && u.getCreatedMode().equals(BY_SYSTEM.getValue())) {
            logger.info("不允许绑定admin");
            //退出登录态
            SecurityContextHolder.getContext().setAuthentication(null);
            //返回给前端不允许绑定admin
            UserCenterException userCenterException = new UserCenterException(HttpStatus.OK, TransactionErrorType.USER_ADMIN_IS_NOT_ALLOWED);
            throw userCenterException;
        }

        if (StringUtils.isNotEmpty(openId)) {
            ThirdIDPEntity thirdIDPEntity = thirdIDPService.getById(connectorId);
            ExternalUserEntity externalUser = externalUserDBO.getByExternalId(thirdIDPEntity.getId(), openId);
            //该微信未绑定
            if (null == externalUser) {
                List<Long> connectorIds = new ArrayList<>();
                connectorIds.add(thirdIDPEntity.getId());
                List<ExternalUserEntity> externalUserEntityList = externalUserService.getBindWechat(userId, connectorIds);
                //该账号已经绑定其他微信
                if (externalUserEntityList.size() > 0) {
                    //返回给前端
                    UserCenterException userCenterException = new UserCenterException(HttpStatus.OK, TransactionErrorType.USER_WECHAT_IS_BIND);
                    userCenterException.setCorrelationId(thirdIDPEntity.getIdpType());
                    logger.info("该账号已经绑定其他微信，userId:" + userId);
                    throw userCenterException;
                }
                ExternalUserEntity externalUserEntity = new ExternalUserEntity();
                externalUserEntity.setUserId(userId);
                externalUserEntity.setExternalId(openId);
                externalUserEntity.setExternalOpenId(openId);
                externalUserEntity.setCreateTime(LocalDateTime.now());
                externalUserEntity.setSyncDirection(0);
                externalUserEntity.setTenantId(TenantHolder.getTenantCode());
                externalUserEntity.setConnectorId(connectorId);
                externalUserEntity.setSyncBatchNo(0);
                externalUserEntity.setType(ThirdIdpTypeEnum.getValue(thirdIDPEntity.getIdpType()));
                //绑定微信
                externalUserDBO.save(externalUserEntity);
                //返回给前端
                HashMap<String, String> errorData = new HashMap<>();
                UserCenterException userCenterException = new UserCenterException(HttpStatus.OK, TransactionErrorType.USER_IS_BIND);
                errorData.put("headUrl", headUrl);
                userCenterException.setData(errorData);
                userCenterException.setCorrelationId(thirdIDPEntity.getIdpType());
                logger.info("完成用户绑定微信，userId:" + userId);
                throw userCenterException;
                //有绑定关系
            } else {
                //改申请已被拒绝
                ApplyProcessLogEntity applyProcessLogEntity = applyProcessLogService.getAppAuthApproveLogById(externalUser.getUserId().toString());
                if (null != applyProcessLogEntity &&
                        ProcessFlowType.ACCOUNT_CREATE_OR_RENEWAL.name().equalsIgnoreCase(applyProcessLogEntity.getProcessType()) &&
                        CheckStatusEnum.REFUSE.name().equals(applyProcessLogEntity.getApproveStatus())) {
                    //移除无效绑定
                    externalUserDBO.removeById(externalUser.getId());
                    //重新绑定
                    bindWechat(userId, openId, connectorId);

                } else {
                    UserCenterException userCenterException = new UserCenterException(HttpStatus.OK, TransactionErrorType.USER_WECHAT_IS_USED);
                    userCenterException.setCorrelationId(thirdIDPEntity.getIdpType());
                    logger.info("该微信号已经绑定其他账号");
                    throw userCenterException;
                }


            }
        }
    }

    @Override
    public Boolean unbindWechat(Long userId, String openId, Long connectorId) {
        return externalUserService.deleteExternalUser(userId, openId, connectorId);
    }

    @Override
    public void getWechatStatus(Map<String, Object> userMap) {
        String headUrl = "";
        String openId = userMap.get("openid").toString();
        if (null != userMap.get("headimgurl")) {
            headUrl = userMap.get("headimgurl").toString();
        }

        ExternalUserEntity externalUserEntity = externalUserDBO.getByExternalId((Long) userMap.get("connectorId"), openId);
        //用户未绑定, 返回前端无绑定，让前端跳转注册页面。
        if (null == externalUserEntity) {
            handleUnbindWeChatUser(userMap, openId, headUrl, "用户未绑定微信,openId:" + openId);
        }

        Long userId = externalUserEntity.getUserId();
        UserEntity userEntity = getUser(userId);
        // 存在绑定关系且存在本地用户，说明已绑定过，返回id进行登录
        if (null != userEntity) {
            userMap.put("userId", userId);
            return;
        }

        ApplyProcessLogEntity applyProcessLogEntity = applyProcessLogService.getAppAuthApproveLogById(userId.toString());
        // iam_link_user != null，iam_user == null，iam_apply_process_log = null，说明数据出现异常，移除关联关系，重新绑定
        if (null == applyProcessLogEntity) {
            externalUserDBO.removeById(externalUserEntity.getId());
            handleUnbindWeChatUser(userMap, openId, headUrl, "本地用户信息异常，重新绑定微信, openId:" + openId);
        }

        // 不存在本地用户，存在创建或续期类型的注册审批记录
        if (ProcessFlowType.ACCOUNT_CREATE_OR_RENEWAL.name().equalsIgnoreCase(applyProcessLogEntity.getProcessType())) {
            String approveStatus = applyProcessLogEntity.getApproveStatus();
            // iam_link_user != null，iam_user == null，approveStatus == AGREE，说明关联的本地用户被删除，移除关联关系，重新绑定
            if (CheckStatusEnum.AGREE.name().equals(approveStatus)) {
                externalUserDBO.removeById(externalUserEntity.getId());
                handleUnbindWeChatUser(userMap, openId, headUrl, "本地用户被删除，重新绑定微信, openId:" + openId);
            }

            // 审批中
            if (CheckStatusEnum.PROCESSING.name().equals(approveStatus)) {
                throwUserUnbindException(userMap, TransactionErrorType.USER_IN_AUDIT, "用户审批中");
            }

            // 审批被拒绝
            if (CheckStatusEnum.REFUSE.name().equalsIgnoreCase(approveStatus)) {
                if (StringUtils.isNotBlank(applyProcessLogEntity.getRemark())) {
                    // 拒绝原因
                    userMap.put("remark", applyProcessLogEntity.getRemark());
                }
                // 原注册申请参数
                String iamFormDataStr = applyProcessLogEntity.getIamFormData();
                Map<String, Object> iamFormData = JsonUtil.str2Map(iamFormDataStr);
                if (iamFormData != null) {
                    iamFormData.remove(LocalUserAttr.org_ids.getDomainName());
                    iamFormData.remove(LocalUserAttr.sub.getFieldName());
                    userMap.put("previousRegistrationData", iamFormData);
                }
                throwUserUnbindException(userMap, TransactionErrorType.USER_AUDIT_FAILED, "用户注册申请被拒绝");
            }
        }
    }

    /**
     * 处理用户未绑定微信
     *
     * @param userMap 用户信息
     * @param openId  微信openId
     * @param headUrl 头像信息
     * @param logInfo 日志信息
     */
    private void handleUnbindWeChatUser(Map<String, Object> userMap, String openId, String headUrl, String logInfo) {
        RBucket<String> bucket = redissonClient.getBucket("headUrl-" + openId);
        bucket.set(headUrl, 2, TimeUnit.HOURS);
        throwUserUnbindException(userMap, TransactionErrorType.USER_NO_BIND, logInfo);
    }

    /**
     * 抛出用户绑定微信异常
     *
     * @param userMap          用户信息
     * @param transactionError
     * @param logInfo          日志信息
     */
    private void throwUserUnbindException(Map<String, Object> userMap, TransactionErrorType transactionError, String logInfo) {
        UserUnbindException userUnbindException = new UserUnbindException(HttpStatus.OK, transactionError);
        userMap.put("code", String.valueOf(transactionError.getErrorCode()));
        userUnbindException.setData(userMap);
        logger.info(logInfo);
        throw userUnbindException;
    }


    @Override
    public Map<String, Object> getAccountBind() {
        Long userId = Long.valueOf(loginService.getUserInfo().getSub());
        UserInfo userInfo = loginService.getUserInfo();
        Map map = new HashMap();
        //微信网站应用
        Map wechatMap = new HashMap();
        List<ThirdIDPEntity> wechatEntities = thirdIDPService.getByType(ThirdIdpTypeEnum.getByName("WECHAT"));
        if (wechatEntities.size() > 0) {
            Long connectorId = wechatEntities.get(0).getId();
            WechatAccount wechatAccount = JsonUtil.str2Obj(wechatEntities.get(0).getConfig(), WechatAccount.class);
            wechatMap.put("auth_id", connectorId);
            wechatMap.put("app_id", wechatAccount.getAppId());
            List<ExternalUserEntity> externalUserEntities = externalUserService.getBindWechat(userId, Arrays.asList(connectorId));
            if (externalUserEntities.size() > 0) {
                ExternalUserEntity externalUserEntity = externalUserEntities.get(0);
                wechatMap.put("type", "WECHAT");
                wechatMap.put("user_id", externalUserEntity.getUserId());
                wechatMap.put("open_id", externalUserEntity.getExternalOpenId());
                wechatMap.put("connector_id", externalUserEntity.getConnectorId().toString());
            }
        }
        map.put("wechat", wechatMap);

        //微信公众号
        Map wechatOfficialMap = new HashMap();
        List<ThirdIDPEntity> wechatofficialEntities = thirdIDPService.getByType(ThirdIdpTypeEnum.getByName("WECHATOFFICIAL"));
        if (wechatofficialEntities.size() > 0) {
            Long connectorId = wechatofficialEntities.get(0).getId();
            WechatOfficialAccount wechatOfficialAccount = JsonUtil.str2Obj(wechatofficialEntities.get(0).getConfig(), WechatOfficialAccount.class);
            wechatOfficialMap.put("auth_id", connectorId);
            wechatOfficialMap.put("app_id", wechatOfficialAccount.getAppId());
            List<ExternalUserEntity> externalUserEntities = externalUserService.getBindWechat(userId, Arrays.asList(connectorId));
            if (externalUserEntities.size() > 0) {
                ExternalUserEntity externalUserEntity = externalUserEntities.get(0);
                wechatOfficialMap.put("type", "WECHATOFFICIAL");
                wechatOfficialMap.put("user_id", externalUserEntity.getUserId());
                wechatOfficialMap.put("open_id", externalUserEntity.getExternalOpenId());
                wechatOfficialMap.put("connector_id", externalUserEntity.getConnectorId().toString());
            }
        }
        map.put("wechat_official", wechatOfficialMap);


        //fido2账号
        map.put("fido2", null);


        return map;


    }


    private Long saveUserAndSendMessageByAdmin(Map<String, Object> userEntity, String applyExplain) {
        String userId = SecureRequestCheck.getSubject();
        long id = IdWorker.getId();
        String detailUrl = String.format(WebUrlUtil.getWebServerUrl(serverConfig.calcOutTerUrl(TenantHolder.getTenantCode()), "uc/users/expireUserDetail?a=%s"), id);
        AccountCreateRenewalDto accountCreateRenewalDto = new AccountCreateRenewalDto();
        accountCreateRenewalDto.setApplyType(ACCOUNT_CREATE);
        accountCreateRenewalDto.setUserNumber("1");
        accountCreateRenewalDto.setApplyExplain(applyExplain);
        accountCreateRenewalDto.setDetailUrl(detailUrl);
        accountCreateRenewalDto.setStartDate((String) userEntity.get(LocalUserAttr.start_date.getDomainName()));
        accountCreateRenewalDto.setEndDate((String) userEntity.get(LocalUserAttr.end_date.getDomainName()));
        userEntity.put("apply_process_id", id);
        if (StringUtils.isNotBlank(userId)) {
            applyProcessLogService.saveAccountCreateOrRenewalLog(accountCreateRenewalDto, userEntity, Long.valueOf(userId));
        } else {
            userEntity.put(LocalUserAttr.sub.getFieldName(), id);
            applyProcessLogService.saveAccountCreateOrRenewalLog(accountCreateRenewalDto, userEntity, null);
        }

        return id;
    }

    @Override
    public boolean checkDeletedUser() {
        List<PushConnectorEntity> pushConnectors = pushConnectorService.getAllPushConnectors();
        return CollectionUtils.isNotEmpty(pushConnectors);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Map<String, Object> removeUserByUid(Set<String> usernames) {
        List<String> failures = new ArrayList<>();
        for (String username : usernames) {
            UserEntity userEntity = userDBO.getOneByUsername(username);

            if (userEntity == null) {
                logger.warn("User {} not found", username);
                failures.add(username);
            } else {
                boolean result = removeUser(userEntity);
                if (!result) {
                    logger.warn("Failed to delete user_id {}", username);
                    failures.add(username);
                }
            }
        }


        Map<String, Object> resultMap = new HashMap<>();
        if (!failures.isEmpty()) {
            resultMap.put("msg", failures);
        } else {
            resultMap.put("msg", "Successful!");
        }
        return resultMap;

    }

    //    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean removeUser(UserEntity userEntity) {
        if (userEntity == null) {
            return true;
        }
        String username = userEntity.getUsername();
        //TODO 这里可能会存在公共调用
//        if (CreatedMode.isReadOnly(userEntity.getCreatedMode())) {
//            logger.warn("User {} is read only cannot deleted ", username);
//            throw new UserException(USER_READ_ONLY_CANNOT_MODIFY_ERROR_DESC);
//        }

        boolean isSucceed = userDBO.removeById(userEntity.getId());
        if (isSucceed) {
            logger.info("remove user: {}", username);
            userOrgDBO.deleteByUserId(userEntity.getId());
            userStatusService.deleteUserStatus(userEntity.getId());
            tagService.removeUserTagBindingsByUserId(userEntity.getId());

            fieldExtDataHandler.deleteByRelationId(userEntity.getId(), FieldType.USER);
            DeletedUserEntity deletedUserEntity = deletedItemTransfer.toDeletedUser(userEntity);
            deletedUserDBO.save(deletedUserEntity);

            userEntity.setUpdateTime(LocalDateTime.now());

            //清除用户缓存
            String tenantCode = TenantHolder.getTenantCode();
            cacheService.evictFromCache(CacheNameConstant.USER_UNIQUE_ATTR_CACHE_NAME, tenantCode);
            //推送
            dataSyncService.pushDataToApp(userEntity.getId(), PushBusinessType.USER, DataChangeType.DELETE);

            //removeByRelationId all use related info
            //user_tag, user_cert,user_org
            return true;
        }

        logger.error("error while removeUserByUid {} ", username);
        throw new UserException(HttpStatus.INTERNAL_SERVER_ERROR, TransactionErrorType.UNKNOWN_DAO_ERROR);
    }

    @Override
    public Map<String, AuthApprovalVO> getAuthApprovalsByUserId(Long userId) {
        List<AuthApprovalEntity> approvals = authApprovalDBO.getByUserId(userId);
        if (approvals == null || approvals.size() == 0) {
            return new HashMap<>();
        } else {
            return approvals.stream()
                    .map(entity -> authApprovalTransfer.entity2vo(entity))
                    .collect(Collectors.toMap(AuthApprovalVO::getClientId, Function.identity()));
        }
    }

    @Override
    public QueryPage<Map<String, Object>> searchUserWithFuzzyWord(UserQueryCondition queryCondition, QueryPage<Map<String, Object>> queryPage) {
        Map<Integer, Set<String>> fieldNameMap = TransferUtil.toFieldNames(fieldDictService, FieldType.USER, queryCondition.getRequestAttrs());

        if (queryCondition.getOrgId() != null) {
            OrgEntity orgEntity = orgDBO.getById(queryCondition.getOrgId());
            if (orgEntity == null) {
                logger.error("Org {} not found", queryCondition.getOrg());
                throw new UserCenterException(HttpStatus.NOT_FOUND, TransactionErrorType.ORG_NOT_FOUND);
            }
            queryCondition.setOrg(orgEntity);
        }
        Set<String> systemFieldNames = fieldNameMap.get(TypeMapper.asInt(BY_SYSTEM));
        systemFieldNames.add(LocalUserAttr.sub.getFieldName());
        systemFieldNames.remove(LocalUserAttr.password.getFieldName());
        boolean hasTag = systemFieldNames.remove(LocalUserAttr.tag.getFieldName());
        boolean hasOrg = systemFieldNames.remove(LocalUserAttr.org_ids.getFieldName());

        queryPage = userDBO.page(queryCondition, queryPage, systemFieldNames);
        Set<String> extFieldNames = fieldNameMap.get(TypeMapper.asInt(BY_ADMIN));
        if (extFieldNames != null && extFieldNames.size() > 0) {
            addExtFields(queryPage, extFieldNames);
        }

        if (hasTag && queryPage.getTotal() > 0) {
            //addTags(querypage);
        }
        if (hasOrg && queryPage.getTotal() > 0) {
            addOrgs(queryPage);
        }
        QueryPage<Map<String, Object>> result = userTransfer.entitiesToDto(queryPage);

        //设置positon
        Set<Long> uidSet = result.getItems().stream().map(user -> Long.valueOf(user.get(LocalUserAttr.sub.getDomainName()).toString())).collect(Collectors.toSet());
        Map<Long, List<OrgEntity>> orgMap = this.iOrganizationService.getUserOrgEntityList(uidSet);
        for (Map<String, Object> userMap : result.getItems()) {
            List<UserOrgInfoVO> userOrgInfos = new ArrayList<>();
            Long uid = Long.parseLong(userMap.get(LocalUserAttr.sub.getDomainName()).toString());
            List<OrgEntity> orgs = orgMap.get(uid);
            if (orgs != null) {
                for (OrgEntity orgEntity : orgs) {
                    UserOrgInfoVO userOrgInfo = new UserOrgInfoVO();
                    userOrgInfo.setIsMain(orgEntity.getIsMain());
                    userOrgInfo.setIsManager(0);
                    userOrgInfo.setPosition(orgEntity.getPosition());
                    userOrgInfo.setUserCode(orgEntity.getUserCode());
                    userOrgInfo.setOrgId(orgEntity.getId().toString());
                    userOrgInfo.setDeptPath(orgEntity.getOrgPath());
                    userOrgInfo.setOrgName(orgEntity.getName());
                    //是否是主管
                    String manager = orgEntity.getManager();
                    if (StringUtils.isNotBlank(manager)) {
                        List<Long> managerIds = JsonUtil.str2List(manager, Long.class);
                        if (managerIds.contains(uid)) {
                            userOrgInfo.setIsManager(1);
                        }
                    }
                    userOrgInfos.add(userOrgInfo);
                }
            }
            userMap.put(LocalUserAttr.group_positions.getDomainName(), userOrgInfos);
            userMap.put(LocalUserAttr.status.getDomainName(), calcStatus((String) userMap.get(LocalUserAttr.status.getDomainName()), userMap.get(LocalUserAttr.username.getDomainName()), userMap.get(LocalUserAttr.email.getDomainName()), userMap.get(LocalUserAttr.user_job_number.getDomainName())));
        }

        return result;
    }

    @Override
    public QueryPage<Map<String, Object>> searchUserWithFuzzyWord(UserQueryCondition queryCondition, QueryPage<Map<String, Object>> queryPage, Integer page, Integer size, Map<String, Object> queryMap, List<UserSearchDto.Fields> baseField, List<UserSearchDto.Fields> extendField, List<UserSearchDto.Fields> positions, List<Long> orgIds) {
        Map<Integer, Set<String>> fieldNameMap = TransferUtil.toFieldNames(fieldDictService, FieldType.USER, queryCondition.getRequestAttrs());

        Set<String> systemFieldNames = fieldNameMap.get(TypeMapper.asInt(BY_SYSTEM));
        boolean hasOrg = false;
        if (systemFieldNames != null) {
            systemFieldNames.remove(LocalUserAttr.password.getFieldName());
            boolean hasTag = systemFieldNames.remove(LocalUserAttr.tag.getFieldName());
            hasOrg = systemFieldNames.remove(LocalUserAttr.org_ids.getFieldName());
        }

        List<Map<String, Object>> userEntities = new ArrayList<>();
        int count = 0;
        Boolean scopes = (Boolean) queryMap.get("scopes");
        if (CollectionUtils.isNotEmpty(orgIds) || (scopes && queryCondition.getOrg() != null) || (!scopes && queryCondition.getOrg() == null)) {
            Set<String> collect = null;
            if (systemFieldNames != null && !systemFieldNames.isEmpty()) {
                collect = systemFieldNames.stream().map(e -> "u." + e).collect(Collectors.toSet());
                // 需要根据id 查询部门 所以必须增加id
                collect.add("u.id");
            }
            List<UserSearchDto.Fields> collect1 = baseField.stream().filter(e -> !LocalUserAttr.status.getDomainName().equals(e.getAttr())).collect(Collectors.toList());
            userEntities = userDBO.searchUser(collect, queryCondition.getSearchField(), queryCondition.getFilter(), queryCondition.getUserIds(), orgIds, queryCondition.getOrgId(), queryCondition.getIncludeUsersInSubOrgs(), collect1, extendField, positions, (page - 1) * size, size);
            count = userDBO.size(queryCondition.getSearchField(), queryCondition.getFilter(), queryCondition.getUserIds(), orgIds, collect1, extendField, positions);
            queryPage.setItems(userEntities);
            Set<String> extFieldNames = fieldNameMap.get(TypeMapper.asInt(BY_ADMIN));
            if (extFieldNames != null && extFieldNames.size() > 0) {
                addExtFields(queryPage, extFieldNames);
            }
            if (hasOrg && queryPage.getSize() > 0) {
                addOrgs(queryPage);
            }
        }
        queryPage.setTotal(count);
        queryPage.setPages(count % size == 0 ? count / size : count / size + 1);
        QueryPage<Map<String, Object>> result = userTransfer.entitiesToDto(queryPage);
        if (CollectionUtils.isNotEmpty(userEntities)) {
            List<Long> ids = userEntities.stream().map(e -> Long.valueOf(String.valueOf(e.get(DaoConstants.id)))).collect(Collectors.toList());
            List<UserEntity> users = this.getUsers(ids);
            List<Long> suspendIds = users.stream().filter(e -> e.getStatus() != null && e.getStatus() == 0).map(UserEntity::getId).collect(Collectors.toList());
            result.getItems().forEach(e -> {
                e.put(LocalUserAttr.status.getDomainName(), calcStatus((String) e.get(LocalUserAttr.status.getDomainName()), e.get(LocalUserAttr.username.getDomainName()), e.get(LocalUserAttr.email.getDomainName()), e.get(LocalUserAttr.user_job_number.getDomainName())));
                if (!SUSPENDED.name().equals((String) e.get(LocalUserAttr.status.getDomainName())) && suspendIds.contains(Long.valueOf((String) e.get(LocalUserAttr.sub.getDomainName())))) {
                    e.put("suspended_status", SUSPENDED.name());
                }
            });
        }

        return result;
    }


    @Override
    public List<UserExtendFieldVo> getExtendField() {
        return fieldDictService.getFieldName2EntityMap(FieldType.USER).values()
                .stream()
                .filter(field -> Objects.equals(field.getCreateMod(), TypeMapper.asInt(BY_ADMIN)))
                .sorted(Comparator.comparing(FieldDictEntity::getSortNum)
                        .thenComparing(FieldDictEntity::getId))
                .map(field -> {
                    UserExtendFieldVo userExtendFieldVo = new UserExtendFieldVo();
                    userExtendFieldVo.setKey(field.getDomainName());
                    userExtendFieldVo.setTitle(field.getDisplayName());
                    userExtendFieldVo.setVisible(Boolean.FALSE);
                    return userExtendFieldVo;
                })
                .collect(Collectors.toList());
    }

    @Override
    public List<Long> getOrgIds(UserQueryCondition queryCondition, Map<String, Object> queryMap) {
        queryMap.put("scopes", false);
        OrgEntity conditionOrg = queryCondition.getOrg();
        if (queryCondition.getOrgId() != null) {
            OrgEntity orgEntity = orgDBO.getById(queryCondition.getOrgId());
            if (orgEntity == null) {
                logger.error("Org {} not found", conditionOrg);
                throw new UserCenterException(HttpStatus.NOT_FOUND, TransactionErrorType.ORG_NOT_FOUND);
            }
            conditionOrg = orgEntity;
            queryCondition.setOrg(orgEntity);
        }

        boolean orderUser = false;
        // 查询指定部门下的用户
        List<Long> orgIds = new ArrayList<>();
        if (conditionOrg != null) {
            orgIds.add(queryCondition.getOrgId());
            //如果不传，默认只取当前org
            if (queryCondition.getIncludeUsersInSubOrgs() == null || !queryCondition.getIncludeUsersInSubOrgs()) {
                List<Long> scopes = roleBindingService.searchUserBindingScope();
                if (CollectionUtils.isNotEmpty(scopes)) {
                    List<Map<String, Object>> subOrgList = orgDBO.getSubOrgList(conditionOrg.getOrgPath(), new String[]{LocalOrgAttr.id.getFieldName(), LocalOrgAttr.org_path.getFieldName()});
                    for (Map<String, Object> map : subOrgList) {
                        String path = (String) map.get(LocalOrgAttr.org_path.getFieldName());
                        for (Long scope : scopes) {
                            if (path.indexOf(String.valueOf(scope)) > 0) {
                                orgIds.add((Long) map.get(LocalOrgAttr.id.getFieldName()));
                                break;
                            }

                        }
                    }
                } else {
                    orgIds = orgDBO.getSubOrgList(conditionOrg.getOrgPath(), new String[]{LocalOrgAttr.id.getFieldName()})
                            .stream().map(map -> (Long) map.get(LocalOrgAttr.id.getFieldName())).collect(Collectors.toList());
                }
            }
        } else {
            // 查询通讯录全部
            List<Long> scopes = roleBindingService.searchUserBindingScope();
            scopes.removeIf(next -> next.equals(String.valueOf(DaoConstants.ROOT_ORG_ID)));
            if (CollectionUtils.isNotEmpty(scopes)) {
                queryMap.put("scopes", true);
                List<OrgEntity> orgs = orgDBO.getOrgsList(scopes, true);
                orgIds = orgs.stream().map(OrgEntity::getId).collect(Collectors.toList());
            }
        }
        return orgIds;
    }

    @Override
    public void generateQueryCondition(UserQueryCondition queryCondition, List<UserSearchDto.Fields> baseField, List<UserSearchDto.Fields> extendField, List<UserSearchDto.Fields> positions, Map<String, Object> queryMap) {
        Set<String> adminFieldNames = iFieldDictService.getAdminCreatedFieldFieldNames(FieldType.USER);
        Set<String> userFieldNames = iFieldDictService.getSystemCreatedFieldFieldNames(FieldType.USER);
        if (CollectionUtils.isNotEmpty(queryCondition.getFields())) {

            for (UserSearchDto.Fields field : queryCondition.getFields()) {
                FieldDictEntity fieldDictEntity = iFieldDictService.getByDomainName(field.getAttr(), FieldType.USER);
                if (LocalUserAttr.gender.getDomainName().equals(fieldDictEntity.getDomainName())) {
                    Object value = TransferUtil.toEntityField(fieldDictEntity, field.getValue());
                    field.setValue(String.valueOf(value).equals("\"\"") ? "" : value);
                }
                if (LocalUserAttr.status.getDomainName().equals(fieldDictEntity.getDomainName())) {
                    Object value = TransferUtil.toEntityField(fieldDictEntity, field.getValue());
                    field.setValue(String.valueOf(value).equals("\"\"") ? null : (Integer) value);
                }

                String fieldName = fieldDictEntity.getFieldName();
                if (userFieldNames.contains(field.getAttr()) && !LocalUserAttr.group_positions.getDomainName().equals(field.getAttr())) {
                    baseField.add(field);
                } else if (userFieldNames.contains(field.getAttr()) && LocalUserAttr.group_positions.getDomainName().equals(field.getAttr())) {
                    positions.add(field);
                } else if (adminFieldNames.contains(fieldName)) {
                    field.setAttr(fieldDictEntity.getFieldName());
                    extendField.add(field);
                }
            }

            queryCondition.getFields().forEach((e) -> {
                if (!LocalUserAttr.group_positions.getDomainName().equals(e.getAttr()) && userFieldNames.contains(e.getAttr())) {
                    e.setAttr("u." + e.getAttr());
                }
                switch (e.getOp()) {
                    case "sw":
                        e.setValue(e.getValue() + "%");
                        break;
                    case "ew":
                        e.setValue("%" + e.getValue());
                        break;
                    case "co":
                        e.setValue("%" + e.getValue() + "%");
                        break;
                }
            });
        }
    }

    private Map<String, String> calcUserComeFrom(List<Map<String, Object>> users) {
        List<Long> ids = users.stream().map(el -> Long.parseLong((String) el.get(LocalUserAttr.sub.getDomainName()))).collect(Collectors.toList());
        Map<String, String> userConeFrom = new HashMap<>();
        if (CollectionUtils.isNotEmpty(ids)) {
            List<Integer> syncDirection = new ArrayList<>();
            syncDirection.add(SyncDirectionEnum.SYNC.getValue());
            List<ExternalUserEntity> externalUsers = externalUserService.listByLocalUserId(ids, syncDirection);
            if (CollectionUtils.isNotEmpty(externalUsers)) {
                Set<Long> connectorIds = externalUsers.stream().map(el -> el.getConnectorId()).collect(Collectors.toSet());
                List<ConnectorEntity> connectors = connectorService.searchDsByIds(connectorIds);
                Map<Long, List<ExternalUserEntity>> userIdGroup = externalUsers.stream().collect(Collectors.groupingBy(ExternalUserEntity::getUserId));
                for (Map.Entry<Long, List<ExternalUserEntity>> entry : userIdGroup.entrySet()) {
                    StringBuilder stringBuilder = new StringBuilder();
                    List<Long> connectorId = entry.getValue().stream().map(el -> el.getConnectorId()).collect(Collectors.toList());
                    for (int i = 0; i < connectors.size(); i++) {
                        if (connectorId.contains(connectors.get(i).getId())) {
                            stringBuilder.append(",");
                            stringBuilder.append(connectors.get(i).getName());
                        }
                    }
                    userConeFrom.put(String.valueOf(entry.getKey()), stringBuilder.toString().replaceFirst(",", ""));
                }
            }
        }

        return userConeFrom;
    }

    private static final List<String> ALLOWED_INC_SEARCH_RET_ATTRS = Arrays.asList(LocalUserAttr.sub.getDomainName(), LocalUserAttr.name.getDomainName(),
            LocalUserAttr.username.getDomainName(), LocalUserAttr.email.getDomainName(), LocalUserAttr.picture.getDomainName());

    @Override
    public List<Map<String, Object>> incsearch(Set<String> inScopeOrgIds, String keyword, int limit, String... attrs) {
        if (ArrayUtils.isNotEmpty(attrs)) {
            Collection<String> unknown = CollectionUtils.subtract(Arrays.asList(attrs), ALLOWED_INC_SEARCH_RET_ATTRS);
            if (!unknown.isEmpty()) {
                throw new UserException(HttpStatus.BAD_REQUEST, TransactionErrorType.USER_ATTR_VALUE_INVALID, "unknown attrs: " + unknown);
            }
        }

        List<String> entityFieldAttrs = new ArrayList<>();
        if (attrs != null && attrs.length > 0) {
            String[] attrList = ParamValidationUtil.vo2poAttrs(attrs,
                    this::toFieldName);
            entityFieldAttrs.addAll(Arrays.asList(attrList));
            entityFieldAttrs.add(LocalUserAttr.sub.getFieldName());
        }

        entityFieldAttrs = entityFieldAttrs.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());

        String entityField[] = entityFieldAttrs != null ? entityFieldAttrs.toArray(new String[0]) : null;

        if (StringUtil.isEmptyOrNull(keyword) || keyword.trim().length() < 2) {
            return Collections.EMPTY_LIST;
        }

        List<Map<String, Object>> pos = this.userDBO.getListMapUser(keyword, entityField);
        if (pos == null) {
            return null;
        }
        return pos.stream().map(userTransfer::toDtoMap).collect(Collectors.toList());
    }

    @Override
    public List<Map<String, Object>> search(Map<String, Object> filter, String... attrs) {
        if (filter == null || filter.size() > 3) {
            throw new UserException(HttpStatus.BAD_REQUEST, TransactionErrorType.INVALID_REQUEST_PARAM, INVALID_REQUEST_PARAM_ERROR_DESC);
        }

        //将查询条件的domainName 转换成 fieldName
        Set<String> domainNames = filter.keySet();
        List<FieldDictEntity> fieldDicts = fieldDictService.getFieldDictSByDomainName(domainNames, FieldType.USER);
        Map<String, String> domainFieldName = fieldDicts.stream().collect(Collectors.toMap(FieldDictEntity::getDomainName, FieldDictEntity::getFieldName));
        return this.userDBO.getListMapUser(filter, domainFieldName, attrs);
    }

    @Override
    public Map<Long, String> search(List<Long> userId, String attr) {
        Map<String, Object> params = new HashMap<>();
        params.put("sub", userId);
        Map<String, String> domainFieldName = new HashMap<>();
        domainFieldName.put("sub", LocalUserAttr.sub.getFieldName());
        List<Map<String, Object>> listMapUser = this.userDBO.getListMapUser(params, domainFieldName, "id", attr);

        Map<Long, String> res = new HashMap<>();
        for (Map<String, Object> map : listMapUser) {
            res.put(Long.parseLong(map.get(LocalUserAttr.sub.getFieldName()).toString()), String.valueOf(map.get(attr)));
        }
        return res;
    }

    @Override
    public int verifyUserAccount(String username, String password) {
        UserEntity userEntity = userDBO.getOneByUsername(username, LocalUserAttr.created_mode.getFieldName(), LocalUserAttr.come_from.getFieldName(), LocalUserAttr.password.getFieldName(), LocalUserAttr.pwd_expired_time.getFieldName());
        if (userEntity.getPwdExpirationTime() != null && LocalDateTime.now().isAfter(userEntity.getPwdExpirationTime())) {
            return -1;
        }
        if (passwordEncoder.matches(password, userEntity.getPassword())) {
            return 0;
        } else {
            return 1;
        }
    }

    @Override
    public List<String> changeStatus(Set<String> usernames, String status) {
        List<String> errors = new ArrayList<>();
        for (String username : usernames) {
            try {
                changeStatusByUsername(username, status);
            } catch (ExceptDbDataNotFound | UserException e) {
                logger.error("changeStatus({}): {}", username, e);
                errors.add(username);
            }
        }
        if (errors.size() == usernames.size()) {
            throw new UserException(HttpStatus.BAD_REQUEST, TransactionErrorType.INVALID_REQUEST_PARAM, INVALID_REQUEST_PARAM_ERROR_DESC);
        }
        return errors;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Void resetPasswordByAdmin(String username, String password) {
        CfgPasswordPolicy passwordPolicyCfg = configService.getPasswordPolicyCfg();
        Boolean canResetUserPwd = passwordPolicyCfg.getAdminResetUserPwd();
        if (canResetUserPwd) {
            password = Base64Util.decodeToString(password);
            if (!password.matches(configService.getPwdRegExpressionStr())) {
                throw new UserException(TransactionErrorType.USER_PASSWORD_NOT_REQUIREMENTS, configService.getPwdErrorMsg());
            }

            UserEntity userEntity = this.getUserInfoByLoginId(username, false);
            // 放开限制，允许集成用户修改密码
//            UserUtil.readOnlyUserCannotModify(userEntity);
            if (UserPWDStatus.canChange.contains(UserPWDStatus.toEnum(userEntity.getPasswordStatus()))) {
                userEntity.setPasswordStatus(UserPWDStatus.NORMAL.getValue());
                this.updatePasswordByPolicy(userEntity, password);
            }

            userStatusService.createOrUpdateUserStatus(userEntity.getId());

            messageService.sendAdminResetPwdMsg(Arrays.asList(String.valueOf(userEntity.getId())), username, password);
            emailService.sendMsg(DaoConstants.ADMIN_RESET_PWD_EMAIL, userEntity.getUsername(), userEntity.getEmail(), password, null);
            smsService.sendMsg(DaoConstants.ADMIN_RESET_PWD_SMS, userEntity.getUsername(), userEntity.getPhoneNumber(), password, null);

            //changePwdPostProcess(userEntity);
            return null;
        } else {
            throw new UserException(TransactionErrorType.USER_PWD_CAN_NOT_RESET_ERROR);
        }
    }

    @Override
    public void oidcSingleLogout(String username, Boolean logout) {
        // 1、退出指定用户的登入态
        if(StringUtils.isNotBlank(username)){
            if(logout){
                this.doOidcSingleLogout(username);
            } else {
                CfgPoliciesLogin loginPolicyCfg = configService.getLoginPolicyCfg();
                // 此时不需要执行用户退出
                if(Objects.isNull(loginPolicyCfg) || !loginPolicyCfg.getSingleLogout()){
                    return;
                }
                this.doOidcSingleLogout(username);
            }
            logger.info("oidcSingleLogout doOidcSlo success username:{}", username);
        }
        // 2、异步执行单点登出
        String tenantCode = TenantHolder.getTenantCode();
        iamAsyncExecutor.submit(() -> {
            try {
                List<AppEntity> appEntities = appDBO.getAppByTenantOwnerAndAuthProtocol(tenantCode, SsoProtocol.OIDC.name());
                if (CollectionUtils.isNotEmpty(appEntities)) {
                    for (AppEntity appEntity : appEntities) {
                        if(StringUtils.isNotBlank(appEntity.getConfig())){
                            OidcConfig oidcConfig = JsonUtil.str2Obj(appEntity.getConfig(), OidcConfig.class);
                            if(StringUtils.isNotBlank(oidcConfig.getExitCallbackUrl())){
                                try{
                                    RestApiResponse forHeadAndBody = RestAPIUtil.getForHeadAndBody(RestTemplateFactory.getDefaultRestTemplate(),
                                            oidcConfig.getExitCallbackUrl(), new HashMap<>(), new HashMap<>(), null);
                                    logger.info("oidcSingleLogout doOidcSlo success clientName:{}, exitCallbackUrl:{}, result:{}",
                                            appEntity.getClientName(), oidcConfig.getExitCallbackUrl(),
                                            forHeadAndBody.getBody());
                                } catch (Exception e){
                                    logger.error("oidcSingleLogout doOidcSlo failed clientName:{}, exitCallbackUrl:{}, error:{}",
                                            appEntity.getClientName(),oidcConfig.getExitCallbackUrl(), e);
                                }
                            }
                        }
                    }
                }
            } catch (Exception e){
                logger.error("doOidcSlo failed ",e);
            }
        });
    }

    /**
     * 执行用户退出
     *
     * @param username
     */
    private void doOidcSingleLogout(String username) {
        // 删除每个session
        Map<String, ? extends Session> sessions = sessionRepository.findByPrincipalName(username);
        sessions.keySet().forEach(sessionRepository::deleteById);

        final String sessionUsernameKey = "spring:session:index:" + FindByIndexNameSessionRepository.PRINCIPAL_NAME_INDEX_NAME + ":" + username;
        redissonClient.getBucket(sessionUsernameKey).delete();

        // usercenter、portal、tc
        AppEntity queryAppEntity = new AppEntity();
        queryAppEntity.setAuthProtocol(SsoProtocol.OIDC.name());
        queryAppEntity.setApplicationType(2);
        List<AppEntity> appList = appDBO.getAppList(queryAppEntity);
        for (AppEntity appEntity : appList) {
            Collection<OAuth2AccessToken> oAuth2AccessTokens =
                    tokenStore.findTokensByClientIdAndUserName(appEntity.getClientId(), username);
            if(CollectionUtils.isNotEmpty(oAuth2AccessTokens)){
                for (OAuth2AccessToken oAuth2AccessToken : oAuth2AccessTokens) {
                    tokenStore.removeAccessToken(oAuth2AccessToken);
                    if(oAuth2AccessToken.getRefreshToken()!= null){
                        tokenStore.removeRefreshToken(oAuth2AccessToken.getRefreshToken());
                    }
                }
            }
        }
    }

    @Override
    public void updatePasswd(UserEntity userEntity, String password) {
        // 获取密码策略配置
        CfgPasswordPolicy passwordPolicyCfg = configService.getPasswordPolicyCfg();
        Integer maxPasswordAgeDays = passwordPolicyCfg.getPwdMaxAge();
        Integer passwordHistoryCount = passwordPolicyCfg.getPwdInHistory();

        // 处理用户密码历史记录
        UserStatusEntity userStatus = handlePasswordHistory(userEntity, password, passwordHistoryCount);

        // 更新用户密码及状态
        updateUserPasswordAndStatus(userEntity, maxPasswordAgeDays, password);
        // 保存或更新用户状态信息
        if (userStatus != null) {
            userStatusService.createOrUpdateUserStatus(userStatus);
        }

    }

    private UserStatusEntity handlePasswordHistory(UserEntity userEntity, String password, Integer passwordHistoryCount) {
        UserStatusEntity userStatus = userStatusService.getUserStatusById(userEntity.getId());

        if (passwordHistoryCount != null && passwordHistoryCount >= 1) {
            if (userStatus == null) {
                UserStatusEntity newUserStatus = new UserStatusEntity();
                newUserStatus.setId(userEntity.getId());
                newUserStatus.setUsePasswd(BooleanEnums.TRUE.getValue());
                newUserStatus.setActive(BooleanEnums.TRUE.getValue());
                newUserStatus.setCreateTime(LocalDateTime.now());
                newUserStatus.setHistoryPasswd(JsonUtil.obj2Str(Collections.singletonList(MD5Util.md5(password))));
                return newUserStatus;
            } else {
                checkAndUpdatePasswordHistory(userStatus, password, passwordHistoryCount);
            }
        }
        return userStatus;
    }

    private void checkAndUpdatePasswordHistory(UserStatusEntity userStatus, String password, int passwordHistoryCount) {
        List<String> historyPasswords = JsonUtil.str2List(userStatus.getHistoryPasswd(), String.class);
        if (historyPasswords == null) {
            historyPasswords = new ArrayList<>();
        }
        String md5Passwd = MD5Util.md5(password);
        int size = historyPasswords.size();
        int count = Math.min(passwordHistoryCount, size);
        int index = size - 1;

        for (int i = 0; i < count; i++) {
            String historyPassword = historyPasswords.get(index);
            if (historyPassword.equals(md5Passwd)) {
                throw new UserException(TransactionErrorType.LDAP_PASSWORD_IN_HISTORY);
            }
            index--;
        }

        // 历史密码最大长度处理
        if (size >= 15) {
            historyPasswords.remove(0);
        }
        historyPasswords.add(md5Passwd);
        userStatus.setHistoryPasswd(JsonUtil.obj2Str(historyPasswords));
    }

    private void updateUserPasswordAndStatus(UserEntity userEntity, Integer maxPasswordAgeDays, String password) {
        final String encodePwd = passwordEncoder.encode(password);
        LocalDateTime pwdExpirationTime = null;
        if (maxPasswordAgeDays != null && maxPasswordAgeDays > 0) {
            int adjustedMaxAge = Math.min(maxPasswordAgeDays, 3650); // Cap at 10 years
            pwdExpirationTime = LocalDateTime.now().plusDays(adjustedMaxAge);
        }
        userDBO.update(new UpdateWrapper<UserEntity>().lambda()
                .set(UserEntity::getPwdExpirationTime, pwdExpirationTime)
                .set(UserEntity::getPasswordStatus, UserPWDStatus.NORMAL.getValue())
                .set(UserEntity::getPassword, encodePwd)
                .set(UserEntity::getPwdChangedTime, LocalDateTime.now())
                .set(UserEntity::getUpdateTime, LocalDateTime.now())
                .eq(UserEntity::getId, userEntity.getId()));
        // 执行用户登出
        this.oidcSingleLogout(userEntity.getUsername(), Boolean.FALSE);
    }

    private void activeResetPassword(Long userId, Integer maxPasswordAgeDays, String password) {
        final String encodePwd = passwordEncoder.encode(password);
        LocalDateTime pwdExpirationTime = null;
        if (maxPasswordAgeDays != null && maxPasswordAgeDays > 0) {
            int adjustedMaxAge = Math.min(maxPasswordAgeDays, 3650); // Cap at 10 years
            pwdExpirationTime = LocalDateTime.now().plusDays(adjustedMaxAge);
        }
        userDBO.update(new UpdateWrapper<UserEntity>().lambda()
                .set(UserEntity::getPwdExpirationTime, pwdExpirationTime)
                .set(UserEntity::getPasswordStatus, UserPWDStatus.TEMP.getValue())
                .set(UserEntity::getPassword, encodePwd)
                .set(UserEntity::getPwdChangedTime, LocalDateTime.now())
                .set(UserEntity::getUpdateTime, LocalDateTime.now())
                .eq(UserEntity::getId, userId));
    }

    @Override
    public ImportUserHistoryVO importUser(HttpServletRequest request, CreateUserExcelDto createUserExcelDto) {
        if (isUserCountExceed()) {
            throw new UserException(HttpStatus.BAD_REQUEST, TransactionErrorType.USER_COUNT_EXCEED);
        }
        ImportUserHistoryEntity importHistory = importUserHistoryService.createImportHistory(createUserExcelDto);
        return userTransfer.to(importHistory);
    }

    @Override
    public Map<String, Object> getPersonalUserInfo(String id) {
        ApplyProcessLogEntity appAuthApproveLogById = iApplyProcessLogService.getAppAuthApproveLogById(id);
        Map<String, Object> map = JsonUtil.str2Map(appAuthApproveLogById.getIamFormData());
        List list = (List) map.get(LocalUserAttr.org_ids.getDomainName());
        List<OrgBasicInfoVO> orgBasicInfoList = iOrganizationService.getOrgBasicInfoList(list);
        String orgNames = orgBasicInfoList.stream().map(OrgBasicInfoVO::getName).collect(Collectors.joining(","));
        map.put(LocalUserAttr.org_ids.getDomainName(), orgNames);
        return map;
    }

    @Override
    public void batchSaveOrUpdateUser(Long importTaskId, CreatedMode createdMode) {
        ImportUserHistoryEntity importTask = importUserHistoryService.getById(importTaskId);
        batchSaveOrUpdateUser(importTask, createdMode, true);
    }

    @Override
    public void batchSaveOrUpdateUser(ImportUserHistoryEntity importTask, CreatedMode createdMode, boolean isNotifyEvent) {
        Long importTaskId = importTask.getId();
        Boolean userCreateVerify;
        if (configService.getUserPolicyCfg() == null) {
            userCreateVerify = Boolean.TRUE;
        } else {
            userCreateVerify = configService.getUserPolicyCfg().getUserCreateVerify();
        }
        List<ExcelUserEntity> userMapList;
        // 通知事件且开启审批，查询所有需要审核的用户，否则查询该所有有效信息
        if (userCreateVerify && isNotifyEvent) {
            userMapList = excelUserService.getValidUsersByBatchNoAndVerify(importTaskId, true);
        } else if (userCreateVerify) {
            userMapList = excelUserService.getValidUsersByBatchNoAndVerify(importTaskId, false);
        } else {
            userMapList = excelUserService.getValidUsersByBatchNo(importTaskId);
        }


        if (userMapList.isEmpty()) {
            logger.info("{} 当前批次需要导入的用户数为 0", importTaskId);
            return;
        }
        // 获取当前用户的角色信息
        String currentOperate = Optional.ofNullable(SecureRequestCheck.getUsername())
                .filter(StringUtils::isNotBlank)
                .orElse("admin");
        String clientId = Optional.ofNullable(SecureRequestCheck.getClientId())
                .filter(StringUtils::isNotBlank)
                .orElse("usercenter");
        List<RoleBindingVO> roleBandings = roleBindingService.getUserRoleBindings(currentOperate, clientId, null);
        List<RoleVO> adminRoles = roleBandings.stream().map(RoleBindingVO::getRole).filter(roleVO -> CollectionUtils.isNotEmpty(roleVO.getBindingScopes())).collect(Collectors.toList());

        for (ExcelUserEntity excelUserEntity : userMapList) {
            String failReason = null;
            String data = excelUserEntity.getData();
            Map<String, Object> userMap = JsonUtil.str2Map(data);
            String deptPath = String.valueOf(userMap.get(LocalUserAttr.dept_path.getDomainName()));
            if (!isDepartmentWithinScope(deptPath)) {
                failReason = "当前部门不在您的管理范围,部门路径是" + deptPath;
                excelUserEntity.setSaveStatus(ImportUserStatus.IMPORT_FAIL.getValue());
                excelUserEntity.setFailReason(failReason);
                importTask.setFailTotal(importTask.getFailTotal() + 1);
                excelUserService.updateUserInfo(excelUserEntity);
            } else {
                List<String> orgIds = deptPathToOrgPath(deptPath, createdMode);
                userMap.put(LocalUserAttr.org_ids.getDomainName(), orgIds);
                // 处理部门职位等信息
                processDeptInfoForExcelUser(excelUserEntity, orgIds, userMap, createdMode);

                // 存在sub代表该用户为更新用户
                Object sub = userMap.get(LocalUserAttr.sub.getDomainName());
                boolean subIsEmpty = ObjectUtils.isEmpty(sub);
                if (subIsEmpty) {
                    String password = (String) userMap.get(LocalUserAttr.password.getDomainName());
                    if (StringUtils.isBlank(password)) {
                        password = RandomUtil.getRandomPassword(10);
                    }
                    userMap.put(LocalUserAttr.password.getDomainName(), password);
                }
                userMap.remove(LocalUserAttr.dept_path.getDomainName());

                try {
                    if (subIsEmpty) {
                        checkCreatePermission(adminRoles, clientId, deptPath, orgIds);
                        // 将数据存储 iam-user 导入之后不立即发送邮件邀请
                        this.createUser(userMap, Boolean.FALSE, createdMode, Boolean.FALSE);
                        importTask.setSuccessTotal(importTask.getSuccessTotal() + 1);
                    } else {
                        // 校验部门信息是否发生改变
                        checkOrgInfo(Long.valueOf(sub.toString()), userMap, deptPath, orgIds, adminRoles, clientId);
                        String username = userMap.get(LocalUserAttr.username.getDomainName()).toString();
                        // 移除不允许更新的字段
                        userMap.remove(LocalUserAttr.sub.getDomainName());
                        userMap.remove(LocalUserAttr.username.getDomainName());
                        userMap.remove(LocalUserAttr.password.getDomainName());

                        this.updateUser(username, userMap, createdMode);
                        importTask.setUpdateSuccessTotal(importTask.getUpdateSuccessTotal() + 1);
                    }
                    excelUserEntity.setSaveStatus(ImportUserStatus.IMPORT_SUCCESS.getValue());
                } catch (Exception e) {
                    logger.error("导入用户出错", e);
                    String errorMsg = ExceptionUtil.getMessage(e);
                    excelUserEntity.setSaveStatus(ImportUserStatus.IMPORT_FAIL.getValue());
                    excelUserEntity.setFailReason(StringUtils.isNotBlank(errorMsg) ? errorMsg : e.getMessage());
                    importTask.setFailTotal(importTask.getFailTotal() + 1);
                }
            }
            excelUserService.updateUserInfo(excelUserEntity);
            importTask.setLastUid(excelUserEntity.getId());
            importUserHistoryService.updateById(importTask);
        }
    }

    /**
     * 处理导入用户的部门职位等信息
     *
     * @param excelUserEntity 部门职位
     * @param orgIds          部门id集合
     * @param userMap         excel导入的用户信息
     * @param createdMode     创建模式
     */
    private void processDeptInfoForExcelUser(ExcelUserEntity excelUserEntity, List<String> orgIds, Map<String, Object> userMap, CreatedMode createdMode) {
        String data = excelUserEntity.getData();
        Map<String, Object> userInfoMap = JsonUtil.str2Map(data);
        // 处理人员关联的部门信息
        Object orgPositionsObj = userInfoMap.get(LocalUserAttr.org_positions.getDomainName());
        String[] orgPositionArr = new String[0];
        if (ObjectUtils.isNotEmpty(orgPositionsObj)) {
            String orgPositions = orgPositionsObj.toString();
            orgPositionArr = orgPositions.split(",", -1);
        }

        String[] orgJobNumberArr = new String[0];
        Object orgJobNumberObj = userInfoMap.get(LocalUserAttr.org_job_number.getDomainName());
        if (ObjectUtils.isNotEmpty(orgJobNumberObj)) {
            String orgJobNumber = orgJobNumberObj.toString();
            orgJobNumberArr = orgJobNumber.split(",", -1);
        }

        String mainOrg = userInfoMap.get(LocalUserAttr.main_org.getDomainName()).toString();
        OrgEntity orgEntity = iOrganizationService.deepCreateOrg(0L, mainOrg, createdMode);
        String mainOrgId = orgEntity.getId().toString();
        List<UserOrgInfoVO> userOrgInfos = new ArrayList<>();
        for (int i = 0; i < orgIds.size(); i++) {
            UserOrgInfoVO userOrgInfoVO = new UserOrgInfoVO();
            userOrgInfoVO.setOrgId(orgIds.get(i));
            // 非空校验和限制边界防止索引越界
            if (orgPositionArr.length > i) {
                userOrgInfoVO.setPosition(orgPositionArr[i]);
            }
            // 非空校验和限制边界防止索引越界
            if (orgJobNumberArr.length > i) {
                userOrgInfoVO.setUserCode(orgJobNumberArr[i]);
            }
            userOrgInfoVO.setIsMain(mainOrgId.equals(orgIds.get(i)) ? 1 : 0);
            userOrgInfos.add(userOrgInfoVO);
        }
        userMap.put(LocalUserAttr.group_positions.getFieldName(), userOrgInfos);
    }

    /**
     * 校验创建权限
     *
     * @param adminRoles 部门角色集合
     * @param clientId   应用id
     * @param deptPath   部门路径
     * @param orgIds     部门id
     */
    private void checkCreatePermission(List<RoleVO> adminRoles, String clientId, String deptPath, List<String> orgIds) {
        String[] deptPathArr = deptPath.split(",", -1);
        for (int i = 0; i < orgIds.size(); i++) {
            String orgIdStr = orgIds.get(i);
            if (!checkOrgAndPermission(orgIdStr, "create:users", adminRoles, clientId)) {
                throw new UserCenterException(USER_NOT_IN_SCOPE_ERROR, "当前部门不在您的管理范围,部门路径是" + deptPathArr[i]);
            }
        }

    }

    /**
     * 校验部门信息是否发生改变
     *
     * @param sub        用户id
     * @param userMap    用户信息
     * @param deptPath   部门路径
     * @param orgIds     部门id
     * @param adminRoles 部门角色集合
     * @param clientId   应用id
     */
    private void checkOrgInfo(Long sub, Map<String, Object> userMap, String deptPath, List<String> orgIds, List<RoleVO> adminRoles, String clientId) {
        // split.("分隔符", -1(保留空数据在数组中))
        String[] deptPathArr = deptPath.split(",", -1);
        Object orgJobNumbersObj = userMap.get(LocalUserAttr.org_job_number.getDomainName());
        String[] orgNumArr = new String[deptPathArr.length];
        if (orgJobNumbersObj != null) {
            String orgJobNumbers = orgJobNumbersObj.toString();
            orgNumArr = orgJobNumbers.split(",", -1);
        }
        Object orgPositionsObj = userMap.get(LocalUserAttr.org_positions.getDomainName());
        String[] orgPositionArr = new String[deptPathArr.length];
        if (orgPositionsObj != null) {
            String orgPositions = orgPositionsObj.toString();
            orgPositionArr = orgPositions.split(",", -1);
        }
        String username = userMap.get(LocalUserAttr.username.getDomainName()).toString();
        Map<String, Object> oldUser = this.getMapUserByUsername(username);
        List<UserOrgInfoVO> groupPositionsList = (List<UserOrgInfoVO>) oldUser.get(LocalUserAttr.group_positions.getDomainName());
        Map<String, String> orgMap = groupPositionsList.parallelStream().collect(Collectors.toMap(UserOrgInfoVO::getOrgId, UserOrgInfoVO::getOrgName));
        List<String> oldOrgIds = groupPositionsList.parallelStream().map(UserOrgInfoVO::getOrgId).collect(Collectors.toList());
        // 移除相同部分
        oldOrgIds.removeAll(orgIds);
        for (int i = 0; i < orgIds.size(); i++) {
            String orgIdStr = orgIds.get(i);
            Long orgId = Long.valueOf(orgIdStr);
            UserOrgEntity userOrgEntity = new UserOrgEntity();
            userOrgEntity.setUid(sub);
            userOrgEntity.setOrgRefId(orgId);
            List<UserOrgEntity> userOrgEntityList = userOrgDBO.getList(userOrgEntity);

            // 本次为新增部门
            if (CollectionUtils.isEmpty(userOrgEntityList)) {
                if (!checkOrgAndPermission(orgIdStr, "create:users", adminRoles, clientId)) {
                    throw new UserCenterException(USER_NOT_IN_SCOPE_ERROR, "当前部门不在您的管理范围,部门路径是" + deptPathArr[i]);
                }
            } else {
                UserOrgEntity oldUserOrgEntity = userOrgEntityList.get(0);
                // 判断部门工号信息是否改变
                if (!safeEquals(orgNumArr[i], oldUserOrgEntity.getUserCode())) {
                    if (!checkOrgAndPermission(orgIdStr, "update:users", adminRoles, clientId)) {
                        throw new UserCenterException(USER_NOT_IN_SCOPE_ERROR, "当前部门不在您的管理范围,部门路径是" + deptPathArr[i]);
                    }
                }
                // 判断部门职位信息是否改变
                if (!safeEquals(orgPositionArr[i], oldUserOrgEntity.getPosition())) {
                    if (!checkOrgAndPermission(orgIdStr, "update:users", adminRoles, clientId)) {
                        throw new UserCenterException(USER_NOT_IN_SCOPE_ERROR, "当前部门不在您的管理范围,部门路径是" + deptPathArr[i]);
                    }
                }
                // 不为空说明删除了一部分部门信息，现在判断操作员是否拥有修改这些部门的权限
                if (CollectionUtils.isNotEmpty(oldOrgIds)) {
                    for (String oldOrgId : oldOrgIds) {
                        if (!checkOrgAndPermission(oldOrgId, "update:users", adminRoles, clientId)) {
                            throw new UserCenterException(USER_NOT_IN_SCOPE_ERROR, "当前部门不在您的管理范围,部门是" + orgMap.get(oldOrgId));
                        }
                    }
                }

            }

        }
    }

    /**
     * 比较两个字符串是否相等
     *
     * @param str1 字符串1
     * @param str2 字符串2
     * @return true 相同 false 不同
     */
    private boolean safeEquals(String str1, String str2) {
        // 如果两个字符串都是null或空，认为它们相等
        if (StringUtils.isBlank(str1) && StringUtils.isBlank(str2)) {
            return true;
        }
        // 如果其中一个为null（另一个不是空字符串），则认为它们不相等
        if ((StringUtils.isBlank(str1) && StringUtils.isNotBlank(str2)) || (StringUtils.isBlank(str2) && StringUtils.isNotBlank(str1))) {
            return false;
        }
        // 如果两个字符串都不是null，使用equals方法比较
        return str1.equals(str2);
    }

    /**
     * 校验该用户是否有权限操作修改部门信息
     *
     * @param orgId      组织id
     * @param permission 权限名
     * @param adminRoles 角色集合
     * @param clientId   当前应用id
     * @return 校验结果 true 有权限 false 无权限
     */
    private Boolean checkOrgAndPermission(String orgId, String permission, List<RoleVO> adminRoles, String clientId) {
        boolean hasPermission = true;
        // 遍历所有角色，直到无权限或全部遍历完，停止
        for (RoleVO roleVO : adminRoles) {
            Set<String> bindingScopes = roleVO.getBindingScopes();
            // 在管理范围内，检验是否有对应的权限
            if (bindingScopes.contains(orgId)) {
                if (!isAllowOperateSource(permission, clientId, roleVO)) {
                    return false;
                }
            }
        }
        return hasPermission;
    }

    /**
     * 判断该角色是否有权限
     *
     * @param permission 权限名
     * @param clientId   当前应用id
     * @param roleVO     当前角色
     * @return true 有权限 false 无权限
     */
    private Boolean isAllowOperateSource(String permission, String clientId, RoleVO roleVO) {
        RoleVO role = roleService.getRole(clientId, roleVO.getName());
        if (role == null) {
            logger.info("role {} not found", roleVO.getName());
            return false;
        } else {
            if (roleService.hasPermission(role, permission)) {
                return true;
            } else {
                logger.debug("app has role {}, no permission {}", roleVO.getName(), permission);
                return false;
            }
        }
    }

    private boolean isDepartmentWithinScope(String deptPath) {
        List<Long> scopes = roleBindingService.searchUserBindingScope();
        if (CollectionUtils.isNotEmpty(scopes)) {
            List<String> scopeDeptPath = iOrganizationService.orgPathToOrgNamePath(scopes);
            if (CollectionUtils.isNotEmpty(scopeDeptPath)) {
                for (String path : scopeDeptPath) {
                    if (deptPath.startsWith(path)) {
                        return true;
                    }
                }
                return false;
            }
        } else {
            return true;
        }
        return false;
    }

    private List<String> deptPathToOrgPath(String deptPath, CreatedMode createdMode) {
        return deptPathToOrgPath(0L, deptPath, createdMode);
    }

    @Override
    public List<String> deptPathToOrgPath(Long connectorId, String deptPath, CreatedMode createdMode) {
        String[] deptPaths = deptPath.split(",");
        List<String> deptIds = new ArrayList<>();
        for (String dept : deptPaths) {
            OrgEntity orgEntity = iOrganizationService.deepCreateOrg(connectorId, dept, createdMode);
            deptIds.add(String.valueOf(orgEntity.getId()));
        }
        return deptIds;
    }

    @Override
    public List<Map<String, String>> getImportUserSchema() {
        List<Map<String, String>> userSchema = new ArrayList<>();

        List<FieldDictEntity> basicFieldDicts = iFieldDictService.listFieldDicts(FieldType.USER.getValue(), FieldMode.BASIC.getValue());
        Map<String, FieldDictEntity> basicFieldMap = basicFieldDicts.stream().collect(Collectors.toMap(FieldDictEntity::getDomainName, Function.identity()));

        List<FieldDictEntity> extFieldDicts = iFieldDictService.listFieldDicts(FieldType.USER.getValue(), FieldMode.DYNAMIC.getValue());

        LocalAttr[] basicLocalUserAttrs = this.getImportOrExportBasicAttrs();
        for (LocalAttr basicLocalUserAttr : basicLocalUserAttrs) {
            String domainName = basicLocalUserAttr.getDomainName();
            String displayName = basicLocalUserAttr.getDisplayName();

            FieldDictEntity basicField = basicFieldMap.get(domainName);
            if (basicField != null) {
                displayName = basicField.getDisplayName();
            }

            HashMap<String, String> schema = new HashMap<>();
            schema.put("domain_name", domainName);
            schema.put("display_name", displayName);
            userSchema.add(schema);
        }

        for (FieldDictEntity extFieldDict : extFieldDicts) {
            HashMap<String, String> schema = new HashMap<>();
            schema.put("domain_name", extFieldDict.getDomainName());
            schema.put("display_name", extFieldDict.getDisplayName());
            userSchema.add(schema);
        }

        return userSchema;
    }

    private void generateImportTemplate(HttpServletResponse response, List<Map<String, Object>> userInfoList, boolean withImportResult) {
        StringBuilder topTip = new StringBuilder();
        topTip.append("填写须知：\r\n")
                .append("<1>全部单元格的格式都按文本格式输入，从第3行开始导入\r\n")
                .append("<2>部门填写的时候填写部门路径，/表示根部门，多个部门路径使用“,”分隔。如/一级部门/二级部门,/事业部/开发部\r\n")
                .append("<3>用户名不能包含中文，性别需选填：男、女\r\n")
                .append("<4>生日、生效日期、失效日期格式为：“年-月-日“\r\n")
                .append("<5>密码：新建用户可指定用户的密码，密码需要符合密码复杂性策略，更新操作无法更新用户密码\r\n")
                .append("<6>状态和创建时间为只读属性，修改无效\r\n")
                .append("<7>用户id：新建用户为非必填项，更新用户为必填项\r\n")
                .append("<8>部门职位：填写的时候填写与部门顺序一致，多个部门职位使用“,”分隔。如采购员,工程师\r\n")
                .append("<9>部门工号：填写的时候填写与部门顺序一致，多个部门职位使用“,”分隔。如1001,1002\r\n")
                .append("<10>主部门只能填写一个\r\n")
                .append("<11>Excel填写用户时需先将示例数据删除再填写");

        LocalAttr[] basicLocalUserAttrs = this.getImportOrExportBasicAttrs();

        XSSFWorkbook workbook = new XSSFWorkbook();
        try {
            XSSFCellStyle tipStyle = workbook.createCellStyle();
            tipStyle.setWrapText(true);
            XSSFFont tipFont = workbook.createFont();
            tipFont.setBold(true);
            tipFont.setFontName("宋体");
            tipFont.setFontHeightInPoints((short) 13);
            tipStyle.setFont(tipFont);

            List<FieldDictEntity> basicFieldDicts = iFieldDictService.listFieldDicts(FieldType.USER.getValue(), FieldMode.BASIC.getValue());
            Map<String, FieldDictEntity> basicFieldMap = basicFieldDicts.stream().collect(Collectors.toMap(FieldDictEntity::getDomainName, Function.identity()));

            List<FieldDictEntity> extFieldDicts = iFieldDictService.listFieldDicts(FieldType.USER.getValue(), FieldMode.DYNAMIC.getValue());

            XSSFSheet xssfSheet = workbook.createSheet("用户导入");

            CellRangeAddress rangeAddress = new CellRangeAddress(0, 0, 0, basicLocalUserAttrs.length + extFieldDicts.size() - 1);
            xssfSheet.addMergedRegion(rangeAddress);
            XSSFRow row = xssfSheet.createRow(rangeAddress.getFirstRow());
            XSSFCell cell = row.createCell(rangeAddress.getFirstColumn());
            row.setHeightInPoints((short) 210);
            cell.setCellValue(topTip.toString());
            cell.setCellStyle(tipStyle);

            XSSFFont redFont = workbook.createFont();
            redFont.setColor(IndexedColors.RED.index);

            int rowIndex = 1;
            row = xssfSheet.createRow(rowIndex);
            int colIndex = 0;

            for (LocalAttr basicLocalUserAttr : basicLocalUserAttrs) {
                String displayName = basicLocalUserAttr.getDisplayName();
                Boolean required = basicLocalUserAttr.getRequired();

                FieldDictEntity basicField = basicFieldMap.get(basicLocalUserAttr.getDomainName());
                if (basicField != null) {
                    displayName = basicField.getDisplayName();
                    required = TypeMapper.asBoolean(basicField.getMandatory());
                }
                XSSFCell basicTitleCell = row.createCell(colIndex);
                if (required && !LocalUserAttr.sub.getDisplayName().equals(displayName)) {
                    displayName = "*" + displayName;
                }
                basicTitleCell.setCellValue(displayName);
                if (required && !LocalUserAttr.sub.getDisplayName().equals(displayName)) {
                    basicTitleCell.getRichStringCellValue().applyFont(0, 1, redFont);
                }

                xssfSheet.setColumnWidth(colIndex, 3000);
                colIndex++;
            }

            XSSFDrawing drawingPatriarch = xssfSheet.createDrawingPatriarch();
            for (FieldDictEntity extFieldDict : extFieldDicts) {
                XSSFCell extTitleCell = row.createCell(colIndex);
                Boolean required = TypeMapper.asBoolean(extFieldDict.getMandatory());
                if (required) {
                    extTitleCell.setCellValue("*" + extFieldDict.getDisplayName());
                    extTitleCell.getRichStringCellValue().applyFont(0, 1, redFont);
                } else {
                    extTitleCell.setCellValue(extFieldDict.getDisplayName());
                }
                xssfSheet.setColumnWidth(colIndex, 3000);

                if (StringUtils.isNotBlank(extFieldDict.getDescription())) {
                    XSSFComment cellComment = drawingPatriarch.createCellComment(new XSSFClientAnchor(0, 0, 0, 0, colIndex + 2, rowIndex, colIndex + 4, rowIndex + 5));
                    cellComment.setString(extFieldDict.getDescription());
                    extTitleCell.setCellComment(cellComment);
                }
                colIndex++;
            }

            if (withImportResult) {
                XSSFCell resultStatus = row.createCell(colIndex);
                resultStatus.setCellValue("导入状态");
                xssfSheet.setColumnWidth(colIndex, 3000);

                colIndex++;
                XSSFCell errorDesc = row.createCell(colIndex);
                errorDesc.setCellValue("错误描述");
                xssfSheet.setColumnWidth(colIndex, 3000);
                colIndex++;
            }

            for (Map<String, Object> userInfo : userInfoList) {
                // 处理部门信息，将其中的职位、工号等信息提出放入用户map中，用于便利
                processDeptInfoForUserMap(userInfo);
                rowIndex++;
                row = xssfSheet.createRow(rowIndex);
                int columnIndex = 0;
                for (LocalAttr basicLocalUserAttr : basicLocalUserAttrs) {
                    Object obj = userInfo.get(basicLocalUserAttr.getDomainName());
                    String value = (obj == null ? "" : obj.toString());
                    if (StringUtils.isNotBlank(value)) {
                        XSSFCell dataCell = row.createCell(columnIndex);
                        dataCell.setCellValue(value);
                    }
                    columnIndex++;
                }

                for (FieldDictEntity extFieldDict : extFieldDicts) {
                    Object obj = userInfo.get(extFieldDict.getDomainName());
                    String value = (obj == null ? "" : obj.toString());
                    if (StringUtils.isNotBlank(value)) {
                        XSSFCell dataCell = row.createCell(columnIndex);
                        dataCell.setCellValue(value);
                    }
                    columnIndex++;
                }

                if (withImportResult) {
                    Integer status = (Integer) userInfo.get("$status");
                    String error = (String) userInfo.get("$error");

                    XSSFCell statusCell = row.createCell(columnIndex);
                    if (ImportUserStatus.DATA_VALID.getValue() == status) {
                        statusCell.setCellValue("数据合法");
                    } else if (ImportUserStatus.DATA_INVALID.getValue() == status) {
                        statusCell.setCellValue("数据非法");
                    } else if (ImportUserStatus.IMPORT_FAIL.getValue() == status) {
                        statusCell.setCellValue("导入失败");
                    } else if (ImportUserStatus.IMPORT_SUCCESS.getValue() == status) {
                        statusCell.setCellValue("导入成功");
                    }

                    columnIndex++;
                    XSSFCell errorCell = row.createCell(columnIndex);
                    errorCell.setCellValue(error);

                    columnIndex++;
                }
            }
            ServletOutputStream outputStream = response.getOutputStream();
            workbook.write(outputStream);
            outputStream.close();
        } catch (Exception e) {
            logger.error("generate import template error", e);
            throw new UserException(TransactionErrorType.UNKNOWN_ERROR);
        } finally {
            try {
                workbook.close();
            } catch (IOException e) {
                logger.warn("workbook close error", e);
            }
        }
    }

    /**
     * 处理用户的部门信息，将其中的职位、工号等信息提取出来，用于便利
     *
     * @param userInfo 用户信息
     */
    private void processDeptInfoForUserMap(Map<String, Object> userInfo) {
        Object groupPositions = userInfo.get(LocalUserAttr.group_positions.getDomainName());
        if (groupPositions != null) {
            List<UserOrgInfoVO> userOrgInfos;
            if (groupPositions instanceof List) {
                userOrgInfos = (List<UserOrgInfoVO>) groupPositions;
            } else {
                userOrgInfos = JsonUtil.str2List(groupPositions.toString(), UserOrgInfoVO.class);
            }
            // 确保userOrgInfos不为空
            if (CollectionUtils.isNotEmpty(userOrgInfos)) {
                String mainOrg = null;
                for (int i = 0; i < userOrgInfos.size(); i++) {
                    UserOrgInfoVO userOrgInfo = userOrgInfos.get(i);
                    if (Optional.ofNullable(userOrgInfo).map(UserOrgInfoVO::getIsMain).orElse(0) == 1) {
                        mainOrg = userOrgInfo.getDeptPath();
                        break;
                    }
                }
                StringJoiner orgPositions = new StringJoiner(",");
                StringJoiner orgJobNums = new StringJoiner(",");
                userOrgInfos.forEach(userOrgInfo -> {
                    String position = userOrgInfo.getPosition();
                    orgPositions.add(StringUtils.isNotBlank(position) ? position : "");
                    String userCode = userOrgInfo.getUserCode();
                    orgJobNums.add(StringUtils.isNotBlank(userCode) ? userCode : "");
                });

                if (orgPositions.length() > 0) {
                    userInfo.put(LocalUserAttr.org_positions.getDomainName(), orgPositions.toString());
                }
                if (orgJobNums.length() > 0) {
                    userInfo.put(LocalUserAttr.org_job_number.getDomainName(), orgJobNums.toString());
                }
                userInfo.put(LocalUserAttr.main_org.getDomainName(), mainOrg);
            }
        }
    }


    @Override
    public void importTemplate(HttpServletResponse response) {
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put(LocalUserAttr.username.getDomainName(), "zhangsan");
        userInfo.put(LocalUserAttr.name.getDomainName(), "张三");
        userInfo.put(LocalUserAttr.phone_number.getDomainName(), "+86-1883265265");
        userInfo.put(LocalUserAttr.email.getDomainName(), "<EMAIL>");
        userInfo.put(LocalUserAttr.id_card.getDomainName(), "132433197005094029");
        userInfo.put(LocalUserAttr.user_job_number.getDomainName(), "G1010");
        userInfo.put(LocalUserAttr.org_positions.getDomainName(), "采购员");
        userInfo.put(LocalUserAttr.org_job_number.getDomainName(), "ORG1");
        userInfo.put(LocalUserAttr.main_org.getDomainName(), "/一级部门");
        userInfo.put(LocalUserAttr.dept_path.getDomainName(), "/一级部门/二级部门");
        userInfo.put(LocalUserAttr.start_date.getDomainName(), "2022-07-26");
        userInfo.put(LocalUserAttr.end_date.getDomainName(), "2022-08-26");
        userInfo.put(LocalUserAttr.picture.getDomainName(), "https://pss.bdstatic.com/static/superman/img/topnav/newbaike-889054f349.png");

        generateImportTemplate(response, Arrays.asList(userInfo), false);
    }

    @Override
    public LocalAttr[] getImportOrExportBasicAttrs() {
        return new LocalAttr[]{
                LocalUserAttr.sub,
                LocalUserAttr.username,
                LocalUserAttr.name,
                LocalUserAttr.phone_number,
                LocalUserAttr.email,
                LocalUserAttr.id_card,
                LocalUserAttr.user_job_number,
                LocalUserAttr.title,
                LocalUserAttr.password,
                LocalUserAttr.dept_path,
                LocalUserAttr.org_positions,
                LocalUserAttr.org_job_number,
                LocalUserAttr.main_org,
                LocalUserAttr.nickname,
                LocalUserAttr.gender,
                LocalUserAttr.birthdate,
                LocalUserAttr.address,
                LocalUserAttr.telephone_number,
                LocalUserAttr.start_date,
                LocalUserAttr.end_date,
                LocalUserAttr.picture
        };
    }

    @Override
    public void exportUsers(HttpServletResponse response, Set<String> inScopeOrgIds, UserSearchDto userSearchDto) throws IOException {
        List<Map<String, Object>> userInfoList = new ArrayList<>();

        UserQueryCondition queryCondition = new UserQueryCondition();
        queryCondition.setSearchField(userSearchDto.getSearchField());
        queryCondition.setFilter(userSearchDto.getFilter());
        queryCondition.setUserIds(userSearchDto.getUserIds());
        queryCondition.setFields(userSearchDto.getFields());
        // 是否导出子部门
        queryCondition.setIncludeUsersInSubOrgs(userSearchDto.getReturnUsersInSubOrg());

        try {
            Long orgId = OrgUtil.orgIdConver(userSearchDto.getOrgId());
            //根组织的时候，查询全部
            if (DaoConstants.ROOT_ORG_ID.equals(orgId)) {
                orgId = null;
            }
            queryCondition.setOrgId(orgId);
        } catch (OrganizationException e) {
            queryCondition.setOrgId(null);
        }
        QueryPage<Map<String, Object>> queryPage = new QueryPage();
        QueryPage<Map<String, Object>> mapQueryPage;

        Integer beginId = 1;
        int pageSize = 1000;
        try {

            Map<String, Object> queryMap = new HashMap<>();
            List<Long> userIds = getOrgIds(queryCondition, queryMap);
            // 解析用户基础字段 和 扩展字段
            List<UserSearchDto.Fields> baseField = new ArrayList<>();
            List<UserSearchDto.Fields> extendField = new ArrayList<>();
            List<UserSearchDto.Fields> positions = new ArrayList<>();
            generateQueryCondition(queryCondition, baseField, extendField, positions, queryMap);

            do {
                mapQueryPage = searchUserWithFuzzyWord(queryCondition, queryPage, beginId, pageSize, queryMap, baseField, extendField, positions, userIds);
                // 整理部门职位等信息
                List<Map<String, Object>> items = mapQueryPage.getItems();
                // 不输出头像
                for (Map<String, Object> item : items) {
                    if (item.containsKey(LocalUserAttr.picture.getDomainName())) {
                        item.put(LocalUserAttr.picture.getDomainName(), null);
                    }
                }
                generateGroupPositions(items);
                userInfoList.addAll(items);
                // 查询结果修改为对应中文输出为csv文件
                int size = items.size();
                if (size < pageSize) {
                    break;
                } else {
                    beginId++;
                }
            } while (!mapQueryPage.getItems().isEmpty());

        } catch (Exception e) {
            logger.error("exportUsers error", e);
            throw new UserCenterException(TransactionErrorType.UNKNOWN_ERROR);
        }

        generateImportTemplate(response, userInfoList, false);
    }

    @Override
    public Map<String, Object> exportUsersCountByStatus(LocalDateTime startTime, LocalDateTime endTime) {
        Duration between = Duration.between(startTime, endTime);
        long days = between.toDays();
        if (Math.abs(days) > 30) {
            startTime = endTime.plusDays(-30);
        }
        logger.info("exportUsersCountByStatus startTime:{}, endTime:{}", startTime, endTime);
        Map<String, Object> userMap = new HashMap<>();
        // 不活跃用户
        List<String> loginUsers = auditLogDao.getLoginUsers(startTime, endTime);
        statisticUserCount(userMap, "noLoginUserCount", () -> userDao.getNoLoginUsers(loginUsers, endTime));

        LocalDateTime finalStartTime = startTime;
        statisticUserCount(userMap, "suspendUserCount", () -> userDao.getSuspendUsers(finalStartTime, endTime));

        statisticUserCount(userMap, "noactiveUserCount", () -> userDao.getNoActiveUsers(finalStartTime, endTime));

        LocalDateTime pwdExpireTime = LocalDateTime.of(LocalDate.now().plusDays(3), LocalTime.MAX);
        statisticUserCount(userMap, "passwdExpireUserCount", () -> userDao.getPasswdExpire(finalStartTime, endTime.plusDays(3), pwdExpireTime));

        List<String> passwdErrorUsers = auditLogDao.getPasswdErrorUsers(startTime, endTime, databaseType);
        if (CollectionUtils.isNotEmpty(passwdErrorUsers)) {
            userMap.put("passwdErrorUserCount", passwdErrorUsers.size());
        } else {
            userMap.put("passwdErrorUserCount", 0);
        }

        Integer continuousFailureCount = configService.getPasswordPolicyCfg().getLockDuration();
        Date lockTimeThreshold = new Date(System.currentTimeMillis() - continuousFailureCount * 60 * 1000);
        List<UserEntity> users = userDao.getLockUsers(startTime, endTime, lockTimeThreshold);
        if (CollectionUtils.isNotEmpty(users)) {
            userMap.put("lockUserCount", users.size());
        } else {
            userMap.put("lockUserCount", 0);
        }

        List<PushConnectorEntity> activedPushConnectors = pushConnectorService.getActivedPushConnectors();
        Set<Long> toDeleteUserIds = collectToDeleteUserIds(activedPushConnectors, null);
        List<ConnectorEntity> allConnectors = connectorService.getAllConnectorEntities();
        Set<Long> syncDeleted = collectToSyncDeleteUserIds(allConnectors, null);
        if (CollectionUtils.isNotEmpty(syncDeleted)) {
            toDeleteUserIds.addAll(syncDeleted);
        }
        if (CollectionUtils.isNotEmpty(toDeleteUserIds)) {
            userMap.put("deleteUserCount", toDeleteUserIds.size());
        } else {
            userMap.put("deleteUserCount", 0);
        }
        return userMap;
    }

    @Override
    public void exportUsersByStatus(HttpServletResponse response, List<String> status, LocalDateTime startTime, LocalDateTime endTime) {
        Set<Long> userIds = new HashSet<>();
        Map<String, List<UserEntity>> userMap = new HashMap<>();

        Duration between = Duration.between(startTime, endTime);
        long days = between.toDays();
        if (Math.abs(days) > 30) {
            startTime = endTime.plusDays(-30);
        }
        LocalDateTime finalStartTime = startTime;
        logger.info("exportUsersByStatus startTime:{}, endTime:{}", startTime, endTime);

        // 不活跃用户
        List<String> loginUsers = auditLogDao.getLoginUsers(startTime, endTime);
        addUsersAndCollect(userIds, userMap, "noLoginUserCount", () -> userDBO.getNoLoginUsers(loginUsers, endTime));

        // 禁用用户
        addUsersAndCollect(userIds, userMap, "suspendUserCount", () -> userDBO.getSuspendUsers(finalStartTime, endTime));

        // 未激活用户
        addUsersAndCollect(userIds, userMap, "noactiveUserCount", () -> userDBO.getNoActiveUsers(finalStartTime, endTime));

        // 密码即将过期
        LocalDateTime pwdExpireTime = LocalDateTime.of(LocalDate.now().plusDays(3), LocalTime.MAX);
        addUsersAndCollect(userIds, userMap, "passwdExpireUserCount", () -> userDBO.getPasswdExpire(finalStartTime, endTime.plusDays(3), pwdExpireTime));

        // 密码连续错误
        List<String> passwdErrorUsers = auditLogDao.getPasswdErrorUsers(startTime, endTime, databaseType);
        addUsersAndCollect(userIds, userMap, "passwdErrorUserCount", () -> userDBO.getListByUserNames(passwdErrorUsers));

        // 锁定用户
        Integer continuousFailureCount = configService.getPasswordPolicyCfg().getLockDuration();
        Date lockTimeThreshold = new Date(System.currentTimeMillis() - continuousFailureCount * 60 * 1000);
        List<UserEntity> users = userDao.getLockUsers(startTime, endTime, lockTimeThreshold);
        if (CollectionUtils.isNotEmpty(users)) {
            userMap.put("lockUserCount", users);
            userIds.addAll(users.stream().map(UserEntity::getId).collect(Collectors.toList()));
        }

        // 待删除用户
        Map<Long, Map<String, String>> userConMap = new HashMap<>();
        List<PushConnectorEntity> activedPushConnectors = pushConnectorService.getActivedPushConnectors();
        Set<Long> toDeleteUserIds = collectToDeleteUserIds(activedPushConnectors, userConMap);

        List<ConnectorEntity> allConnectors = connectorService.getAllConnectorEntities();
        Set<Long> syncDeleted = collectToSyncDeleteUserIds(allConnectors, userConMap);
        if (CollectionUtils.isNotEmpty(syncDeleted)) {
            toDeleteUserIds.addAll(syncDeleted);
        }

        if (CollectionUtils.isNotEmpty(toDeleteUserIds)) {
            List<UserEntity> toDeleteUsers = userDBO.getListByIds(new ArrayList<>(toDeleteUserIds));
            if (CollectionUtils.isNotEmpty(toDeleteUsers)) {
                userMap.put("deleteUserCount", toDeleteUsers);
                userIds.addAll(toDeleteUserIds);
            }
        }


        // 组织关系
        Map<Long, List<Long>> userIdOrgMap = new HashMap<>();
        Map<Long, String> orgPathMap = new HashMap<>();
        if (!userIds.isEmpty()) {
            List<Long> userIdList = new ArrayList<>(userIds);
            List<UserOrgEntity> userOrgs = new ArrayList<>();
            int batchSize = 5000; // 每批次处理的用户ID数量

            for (int i = 0; i < userIdList.size(); i += batchSize) {
                // 计算当前批次的结束索引
                int endIndex = Math.min(i + batchSize, userIdList.size());
                // 正确截取当前批次的用户ID
                List<Long> currentBatch = userIdList.subList(i, endIndex);

                // 执行批量查询
                List<UserOrgEntity> currentUserOrgs = userOrgDBO.getListByUser(currentBatch);
                userOrgs.addAll(currentUserOrgs);
            }

            userIdOrgMap.putAll(userOrgs.stream().collect(Collectors.groupingBy(UserOrgEntity::getUid, Collectors.mapping(UserOrgEntity::getOrgRefId, Collectors.toList()))));
            List<Long> orgIds = userOrgs.stream().map(UserOrgEntity::getOrgRefId).collect(Collectors.toList());
            List<OrgEntity> orgsList = new ArrayList<>();
            for (int i = 0; i < orgIds.size(); i += batchSize) {
                // 计算当前批次的结束索引
                int endIndex = Math.min(i + batchSize, orgIds.size());
                // 正确截取当前批次的部门ID
                List<Long> currentBatch = orgIds.subList(i, endIndex);
                // 执行批量查询
                List<OrgEntity> currentOrgs = orgDBO.getOrgsList(currentBatch, false, LocalOrgAttr.id.getDomainName(), LocalOrgAttr.org_path.getDomainName());
                orgsList.addAll(currentOrgs);
            }

            for (OrgEntity orgEntity : orgsList) {
                List<String> namePath = orgDBO.getNamePathByIdPath(orgEntity.getOrgPath());
                namePath.remove(0);
                orgPathMap.put(orgEntity.getId(), "/" + String.join("/", namePath));
            }
        }


        try (Workbook workbook = new XSSFWorkbook()) {
            // 创建 数据概览页
            createOverviewSheet(workbook, userMap, toDeleteUserIds, startTime, endTime);
            // 填充其他数据详情页
            if (CollectionUtils.isNotEmpty(status)) {
                for (String sheetName : status) {
                    Sheet sheet = workbook.createSheet(sheetName);
                    setSheetTitle(sheet, userMap.get(sheetName), userIdOrgMap, orgPathMap, userConMap, "deleteUserCount".equals(sheetName));
                }
            } else {
                String[] sheetNames = {"noLoginUserCount", "suspendUserCount", "lockUserCount", "noactiveUserCount", "deleteUserCount", "passwdExpireUserCount", "passwdErrorUserCount"};
                for (String sheetName : sheetNames) {
                    Sheet sheet = workbook.createSheet(sheetName);
                    setSheetTitle(sheet, userMap.get(sheetName), userIdOrgMap, orgPathMap, userConMap, "deleteUserCount".equals(sheetName));
                }
            }

            ServletOutputStream outputStream = response.getOutputStream();
            workbook.write(outputStream);
            outputStream.close();
        } catch (Exception e) {
            logger.info("exportUsers error", e);
            throw new UserCenterException(e);
        }
    }

    private void createOverviewSheet(Workbook workbook, Map<String, List<UserEntity>> userMap, Set<Long> toDeleteUserIds, LocalDateTime startTime, LocalDateTime endTime) {
        // 创建第一个 sheet
        Sheet sheet1 = workbook.createSheet("User Data Overview");
        sheet1.setColumnWidth(0, 15 * 256);
        sheet1.setColumnWidth(1, 18 * 256);
        sheet1.setColumnWidth(2, 18 * 256);
        sheet1.setColumnWidth(3, 21 * 256);
        sheet1.setColumnWidth(4, 18 * 256);
        sheet1.setColumnWidth(5, 18 * 256);
        sheet1.setColumnWidth(6, 21 * 256);
        sheet1.setColumnWidth(7, 21 * 256);
        sheet1.setColumnWidth(8, 27 * 256);
        sheet1.setColumnWidth(9, 30 * 256);

        // 设置第一行的标题
        Row row1 = sheet1.createRow(0);
        Cell cell1 = row1.createCell(0);
        cell1.setCellValue("数犀集成平台用户数据统计表");
        sheet1.addMergedRegion(new CellRangeAddress(0, 0, 0, 9));

        // 设置第二行的内容
        Row row2 = sheet1.createRow(1);
        Cell cell2 = row2.createCell(0);
        cell2.setCellValue("统计周期：");

        Cell cell3 = row2.createCell(1);
        cell3.setCellValue(startTime.getYear() + "年" + startTime.getMonthValue() + "月" + startTime.getDayOfMonth() + "日-" + endTime.getYear() + "年" + endTime.getMonthValue() + "月" + endTime.getDayOfMonth() + "日");
        sheet1.addMergedRegion(new CellRangeAddress(1, 1, 1, 9));

        // 创建一个绘图对象
        Drawing<?> drawing = sheet1.createDrawingPatriarch();

        // 设置第三行的标题
        Row row3 = sheet1.createRow(2);
        String[] headers = {
                "总用户数", "新增用户数", "活跃用户数", "不活跃用户数",
                "禁用用户数", "锁定用户数", "未激活用户数", "待删除用户数",
                "密码即将过期用户数", "连续密码错误用户数"
        };
        String[] commons = {
                "租户平台的全部用户数", "租户平台创建时间统计周期内的用户数", "查询统计周期内所有登录的用户", "查询登录时间不在统计周期内，且在截止时间前创建的用户",
                "查询禁用时间在统计周期内，且在截止时间前创建的用户", "查询锁定时间在统计周期内，且在截止时间前创建的用户", "查询统计截止时间前创建且没有激活的用户", "查询最近一次集成器和同步器的待删除用户，不去重，同步器用户已删除无法导出",
                "查询密码过期时间在统计开始时间至统计截止时间+3天的所有用户", "查询在统计开始时间至统计截止时间密码输入错误连续3次以上的所有用户"
        };
        for (int i = 0; i < headers.length; i++) {
            Cell headerCell = row3.createCell(i);
            headerCell.setCellValue(headers[i]);
            // 创建一个锚点对象
            ClientAnchor anchor = drawing.createAnchor(0, 0, 0, 0, headerCell.getColumnIndex(), row3.getRowNum(), headerCell.getColumnIndex() + 2, row3.getRowNum() + 2);

            // 创建一个批注对象
            Comment comment = drawing.createCellComment(anchor);
            // 设置批注作者
            comment.setAuthor("Digitalsee");
            // 设置批注内容
            comment.setString(new XSSFRichTextString(commons[i]));

            headerCell.setCellComment(comment);
        }

        int count = userDBO.getUserCount() - 1;
        int addUserCount = userDBO.getAddUserCount(startTime, endTime);

        Row row4 = sheet1.createRow(3);
        String[] headers1 = {
                String.valueOf(count),
                String.valueOf(addUserCount),
                String.valueOf(count - userMap.getOrDefault("noLoginUserCount", Collections.emptyList()).size()),
                String.valueOf(userMap.getOrDefault("noLoginUserCount", Collections.emptyList()).size()),
                String.valueOf(userMap.getOrDefault("suspendUserCount", Collections.emptyList()).size()),
                String.valueOf(userMap.getOrDefault("lockUserCount", Collections.emptyList()).size()),
                String.valueOf(userMap.getOrDefault("noactiveUserCount", Collections.emptyList()).size()),
                String.valueOf(toDeleteUserIds.size()),
                String.valueOf(userMap.getOrDefault("passwdExpireUserCount", Collections.emptyList()).size()),
                String.valueOf(userMap.getOrDefault("passwdErrorUserCount", Collections.emptyList()).size())
        };
        for (int i = 0; i < headers1.length; i++) {
            Cell headerCell = row4.createCell(i);
            headerCell.setCellValue(headers1[i]);
        }
        // 设置单元格样式
        CellStyle headerStyle = workbook.createCellStyle();
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        Font font = workbook.createFont();
        font.setBold(true);
        headerStyle.setFont(font);

        // 应用样式
        cell1.setCellStyle(headerStyle);
        cell2.setCellStyle(headerStyle);
        for (Cell headerCell : row3) {
            headerCell.setCellStyle(headerStyle);
        }
    }

    private void statisticUserCount(Map<String, Object> userMap, String status, Supplier<List<UserEntity>> userSupplier) {
        List<UserEntity> users = userSupplier.get();
        if (CollectionUtils.isNotEmpty(users)) {
            long count = users.stream().filter(e -> (!DaoConstants.IAM_ADMIN_USER_NAME.equals(e.getUsername()) || !"linkadmin".equals(e.getUsername()))).count();
            userMap.put(status, count);
        } else {
            userMap.put(status, 0);
        }
    }

    private void addUsersAndCollect(Set<Long> userIds, Map<String, List<UserEntity>> userMap, String status, Supplier<List<UserEntity>> userSupplier) {
        List<UserEntity> users = userSupplier.get();
        if (CollectionUtils.isNotEmpty(users)) {
            List<UserEntity> entities = users.stream().filter(e -> (!DaoConstants.IAM_ADMIN_USER_NAME.equals(e.getUsername()) || !"linkadmin".equals(e.getUsername()))).collect(Collectors.toList());
            if (!entities.isEmpty()) {
                userIds.addAll(entities.stream().map(UserEntity::getId).collect(Collectors.toSet()));
                userMap.put(status, entities);
            }
        } else {
            userMap.put(status, new ArrayList<>(0));
        }
    }

    private Set<Long> collectToDeleteUserIds(List<PushConnectorEntity> connectors, Map<Long, Map<String, String>> userConMap) {
        Set<Long> ids = new HashSet<>();
        for (PushConnectorEntity connector : connectors) {
            List<ExternalUserEntity> toBeDeleted = externalUserService.findToBeDeleted(connector.getId(), connector.getPushBatchNo());
            Set<Long> collect = toBeDeleted.stream().map(ExternalUserEntity::getUserId).collect(Collectors.toSet());
            ids.addAll(collect);
            if (userConMap != null) {
                Map<String, String> map = new HashMap<>();
                map.put("同步", connector.getName());
                for (Long id : ids) {
                    userConMap.put(id, map);
                }
            }
        }
        return ids;
    }

    private Set<Long> collectToSyncDeleteUserIds(List<ConnectorEntity> connectors, Map<Long, Map<String, String>> userConMap) {
        Set<Long> ids = new HashSet<>();
        for (ConnectorEntity connector : connectors) {
            List<ExternalUserEntity> toBeDeleted = externalUserService.findToBeDeleted(connector.getId(), connector.getSyncBatchNo());
            Set<Long> collect = toBeDeleted.stream().map(ExternalUserEntity::getUserId).collect(Collectors.toSet());
            ids.addAll(collect);
            if (userConMap != null) {
                Map<String, String> map = new HashMap<>();
                map.put("集成", connector.getName());
                for (Long id : ids) {
                    userConMap.put(id, map);
                }
            }
        }
        return ids;
    }

    private void setSheetTitle(Sheet sheet, List<UserEntity> userEntities, Map<Long, List<Long>> userIdOrgMap, Map<Long, String> orgPathMap, Map<Long, Map<String, String>> userConMap, boolean isCon) {
        sheet.setColumnWidth(0, 20 * 256);
        sheet.setColumnWidth(1, 20 * 256);
        sheet.setColumnWidth(2, 12 * 256);
        sheet.setColumnWidth(3, 15 * 256);
        sheet.setColumnWidth(4, 20 * 256);
        sheet.setColumnWidth(5, 20 * 256);
        Row row1 = sheet.createRow(0);
        String[] headers = {"用户ID", "用户名", "姓名", "手机号", "邮箱", "部门路径"};
        for (int i = 0; i < headers.length; i++) {
            Cell headerCell = row1.createCell(i);
            headerCell.setCellValue(headers[i]);
        }
        if (isCon) {
            sheet.setColumnWidth(6, 20 * 256);
            sheet.setColumnWidth(7, 20 * 256);
            row1 = sheet.createRow(0);
            headers = new String[]{"集成类型", "连接器名称", "用户ID", "用户名", "姓名", "手机号", "邮箱", "部门路径"};
            for (int i = 0; i < headers.length; i++) {
                Cell headerCell = row1.createCell(i);
                headerCell.setCellValue(headers[i]);
            }
        }


        if (CollectionUtils.isNotEmpty(userEntities)) {
            for (int i = 0; i < userEntities.size(); i++) {
                UserEntity userEntity = userEntities.get(i);
                Row row = sheet.createRow(i + 1);
                if (isCon) {
                    Map<String, String> map = userConMap.get(userEntity.getId());
                    String res = map.get("集成");
                    if (res == null) {
                        row.createCell(0).setCellValue("同步");
                        row.createCell(1).setCellValue(map.get("同步"));
                    } else {
                        row.createCell(0).setCellValue("集成");
                        row.createCell(1).setCellValue(res);
                    }
                    row.createCell(2).setCellValue(String.valueOf(userEntity.getId()));
                    row.createCell(3).setCellValue(userEntity.getUsername());
                    row.createCell(4).setCellValue(userEntity.getName());
                    row.createCell(5).setCellValue(userEntity.getPhoneNumber());
                    row.createCell(6).setCellValue(userEntity.getEmail());
                    List<Long> orgIds = userIdOrgMap.get(userEntity.getId());
                    if (CollectionUtils.isNotEmpty(orgIds)) {
                        List<String> orgPaths = orgIds.stream().map(orgPathMap::get).collect(Collectors.toList());
                        row.createCell(7).setCellValue(String.join(",", orgPaths));
                    }
                } else {
                    row.createCell(0).setCellValue(String.valueOf(userEntity.getId()));
                    row.createCell(1).setCellValue(userEntity.getUsername());
                    row.createCell(2).setCellValue(userEntity.getName());
                    row.createCell(3).setCellValue(userEntity.getPhoneNumber());
                    row.createCell(4).setCellValue(userEntity.getEmail());
                    List<Long> orgIds = userIdOrgMap.get(userEntity.getId());
                    if (CollectionUtils.isNotEmpty(orgIds)) {
                        List<String> orgPaths = orgIds.stream().map(orgPathMap::get).collect(Collectors.toList());
                        row.createCell(5).setCellValue(String.join(",", orgPaths));
                    }
                }
            }
        }
    }

    /**
     * 整理部门职位等信息
     *
     * @param items 用户信息列表
     */
    private void generateGroupPositions(List<Map<String, Object>> items) {
        List<OrgEntity> allOrgs = iOrganizationService.getList(new OrgEntity());
        Map<Long, OrgEntity> allOrgMap = allOrgs.stream().collect(Collectors.toMap(OrgEntity::getId, Function.identity()));

        for (Map<String, Object> userMap : items) {
            // 截取日期部分，只保留日期去除时间
            Object birthdate = userMap.get(LocalUserAttr.birthdate.getDomainName());
            if (ObjectUtils.isNotEmpty(birthdate)) {
                userMap.put(LocalUserAttr.birthdate.getDomainName(), birthdate.toString().substring(0, 10));
            }
            Map<String, OrgEntity> orgMapById = new HashMap<>();
            // 用于计算是否为部门主管
            Map<String, String> managerMapById = new HashMap<>();
            Long uid = Long.parseLong(userMap.get(LocalUserAttr.sub.getDomainName()).toString());
            Object orgIdsObj = userMap.get(LocalUserAttr.org_ids.getDomainName());
            if (ObjectUtils.isEmpty(orgIdsObj)) {
                continue;
            }

            String orgIds = orgIdsObj.toString();
            List<Long> orgIdSet = JsonUtil.str2List(orgIds, Long.class);
            List<OrgEntity> orgs = this.iOrganizationService.getOrgs(orgIdSet);
            if (orgs != null) {
                for (OrgEntity orgEntity : orgs) {
                    orgMapById.put(orgEntity.getId().toString(), orgEntity);
                    managerMapById.put(orgEntity.getId().toString(), orgEntity.getManager());
                }
            }
            String mainUserCode = null;
            int mainIndex = -1;
            List<UserOrgInfoVO> userOrgInfos = iOrganizationService.getUserOrgInfo(uid);
            // 筛选出在管理范围内的部门信息
            List<UserOrgInfoVO> userOrgList = userOrgInfos.stream().filter(org -> orgIdSet.contains(Long.parseLong(org.getOrgId()))).collect(Collectors.toList());

            StringBuilder deptPaths = new StringBuilder();
            for (int i = 0; i < userOrgList.size(); i++) {
                UserOrgInfoVO userOrgInfo = userOrgList.get(i);
                String orgId = userOrgInfo.getOrgId();
                OrgEntity orgEntity = orgMapById.get(orgId);
                userOrgInfo.setOrgName(orgEntity.getName());
                String orgPath = orgEntity.getOrgPath();
                if (StringUtils.isNotBlank(orgPath)) {
                    String[] deptIdPaths = orgPath.split(",");
                    StringBuilder deptPath = new StringBuilder();
                    for (int j = 1; j < deptIdPaths.length; j++) {
                        deptPath.append("/").append(allOrgMap.get(Long.valueOf(deptIdPaths[j])).getName());
                    }
                    if (deptPaths.length() == 0) {
                        deptPaths.append(deptPath);
                    } else {
                        deptPaths.append(",").append(deptPath);
                    }
                    userOrgInfo.setDeptPath(deptPath.toString());
                }
                // 默认非主管
                userOrgInfo.setIsManager(0);
                String manager = managerMapById.get(orgId);
                if (StringUtils.isNotBlank(manager)) {
                    List<Long> managerIds = JsonUtil.str2List(manager, Long.class);
                    if (managerIds.contains(uid)) {
                        userOrgInfo.setIsManager(1);
                    }
                }
                if (mainUserCode == null && StringUtils.isNotBlank(userOrgInfo.getUserCode())) {
                    mainUserCode = userOrgInfo.getUserCode();
                    mainIndex = i;
                }
                if (userOrgInfo.getIsMain() != null && userOrgInfo.getIsMain() == 1) {
                    mainUserCode = userOrgInfo.getUserCode();
                    mainIndex = i;
                }
            }
            if (mainIndex != -1) {
                UserOrgInfoVO userOrgInfoVO = userOrgInfos.get(mainIndex);
                userOrgInfoVO.setIsMain(1);
            } else if (CollectionUtils.isNotEmpty(userOrgInfos)) {
                userOrgInfos.get(0).setIsMain(1);
                mainUserCode = userOrgInfos.get(0).getUserCode();
            }
            // 工号为空取主部门工号
            String userJobNumber = "";
            if (userMap.get(LocalUserAttr.user_job_number.getFieldName()) != null) {
                userJobNumber = userMap.get(LocalUserAttr.user_job_number.getFieldName()).toString();
            }
            if (StringUtils.isBlank(userJobNumber)) {
                userJobNumber = mainUserCode;
            }
            userMap.put(LocalUserAttr.user_job_number.getDomainName(), userJobNumber);
            userMap.put(LocalUserAttr.group_positions.getDomainName(), userOrgList);
            // 重新保存部门路径确保与部门其他信息顺序保持一致
            userMap.put(LocalUserAttr.dept_path.getDomainName(), deptPaths.toString());
        }
    }

    @Override
    public void exportImportUsers(HttpServletResponse response, String taskId, List<Integer> status) {
        List<ExcelUserEntity> usersByBatchNo = excelUserService.getUsersByBatchNo(Long.valueOf(taskId), status);

        List<Map<String, Object>> userInfoList = usersByBatchNo.stream().map(e -> {
            Map<String, Object> map = JsonUtil.str2Map(e.getData());
            map.put("$status", e.getSaveStatus());
            map.put("$error", e.getFailReason());
            return map;
        }).collect(Collectors.toList());

        generateImportTemplate(response, userInfoList, true);
    }

    @Override
    public Integer activeInvitation(ActiveUserDto activeUser) {
        List<String> usernames = activeUser.getUsernames();
        String type = activeUser.getType();
        // 多选情况需要处理 推送给钉钉重新邀请的逻辑
        List<UserEntity> needActiveUsers;
        if (CollectionUtils.isEmpty(usernames)) {
            needActiveUsers = userDao.getNeedActiveUserByUserNames(null);
        } else {
            needActiveUsers = userDao.getNeedActiveUserByUserNames(usernames);

            // 激活与重新邀请的处理分开，激活需要修改密码，重新邀请不修改密码
            String tenantCode = TenantHolder.getTenantCode();
            commonExecutor.submit(() -> {
                TenantHolder.setTenantCode(tenantCode);
                List<UserEntity> listByUserNames = userDBO.getListByUserNames(usernames);
                for (UserEntity user : listByUserNames) {
                    dataSyncService.pushDataToApp(user.getId(), PushBusinessType.USER, DataChangeType.FORCE);
                }
            });
        }
        UserUtil.decryptEntityListFields(needActiveUsers);
        if (Sender.EMAIL.name().equals(type)) {
            sendActiveEmail(needActiveUsers);
        } else {
            sendActiveSms(needActiveUsers);
        }

        return needActiveUsers.size();
    }

    @Override
    public Integer noActiveCount() {
        return userDao.getNoActiveCount();
    }

    @Override
    public Integer userTotal() {
        return userDBO.getUserCount() - 1;
    }

    @Override
    public boolean isUserCountExceed() {
        SysTenantEntity tenantEntity = tenantService.getTenantEntity(TenantHolder.getTenantCode());
        if (tenantEntity != null) {
            if (StringUtils.isNotBlank(tenantEntity.getTempTime())) {
                LocalDate tempLocalDate = LocalDate.parse(tenantEntity.getTempTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                if (!tempLocalDate.isBefore(LocalDate.now())) {
                    return false;
                }
            }
            Integer allowNum = (int) (tenantEntity.getLicenseNum() * 1.1);
            if (this.userTotal() > allowNum) {
                return true;
            }
        }
        return false;
    }

    @Override
    public void importActive(Long taskId) {
        List<UserEntity> needActiveUserByBatch = userDao.getNeedActiveUserByBatch(taskId);
        UserUtil.decryptEntityListFields(needActiveUserByBatch);
        sendActiveEmail(needActiveUserByBatch);
    }

    @Override
    public String checkUserOfPush(UserUpdateDto userUpdateDto) {
        List<PushConnectorEntity> pushConnectors = pushConnectorService.getAllPushConnectors();
        Set<PushConnectorEntity> allSync = pushConnectors.stream().filter(el -> el.getSyncType().equals(DataSyncTypeEnum.INCR.getValue()) && StringUtils.isNotBlank(el.getRootGroupId())).collect(Collectors.toSet());
        //都是全量同步的就不需要提示
        if (CollectionUtils.isEmpty(allSync)) {
            return "";
        }

        Set<Long> userIds = userUpdateDto.getUserIds().stream().map(Long::valueOf).collect(Collectors.toSet());
        Map<Long, String> pushConnectorIdName = allSync.stream().collect(Collectors.toMap(PushConnectorEntity::getId, PushConnectorEntity::getName));
        // 获取推送范围的映射
        Map<Long, List<Long>> pushScopes = allSync.stream().collect(Collectors.toMap(PushConnectorEntity::getId, el -> JsonUtil.str2List(el.getRootGroupId(), Long.class)));
        if ("delete".equalsIgnoreCase(userUpdateDto.getOperate())) {
            return checkDeleteOperation(userIds, pushScopes, pushConnectorIdName);
        } else {
            return checkUpdateOperation(userIds, userUpdateDto, pushScopes, pushConnectorIdName);
        }
    }

    /**
     * 删除操作
     *
     * @param userIds             用户id集合
     * @param pushScopes          推送范围集合
     * @param pushConnectorIdName 推送连接器id与名称的映射
     * @return 推送连接器名称
     */
    private String checkDeleteOperation(Set<Long> userIds, Map<Long, List<Long>> pushScopes, Map<Long, String> pushConnectorIdName) {
        for (Map.Entry<Long, List<Long>> entry : pushScopes.entrySet()) {
            List<Long> pushOrgIds = getPushOrgIds(entry.getValue());
            Set<Long> pushUserIds = new HashSet<>(userOrgDBO.getUserIdListByOrgIds(pushOrgIds));

            for (Long userId : userIds) {
                if (pushUserIds.contains(userId)) {
                    return pushConnectorIdName.get(entry.getKey());
                }
            }
        }
        return "";
    }


    /**
     * 更新操作
     *
     * @param userIds             用户id集合
     * @param userUpdateDto       用户更新信息
     * @param pushScopes          推送范围集合
     * @param pushConnectorIdName 推送连接器id与名称的映射
     * @return 推送连接器名称
     */
    private String checkUpdateOperation(Set<Long> userIds, UserUpdateDto userUpdateDto, Map<Long, List<Long>> pushScopes, Map<Long, String> pushConnectorIdName) {
        Long userId = userIds.iterator().next();

        for (Map.Entry<Long, List<Long>> entry : pushScopes.entrySet()) {
            List<Long> pushOrgIds = getPushOrgIds(entry.getValue());
            Set<Long> pushUserIds = new HashSet<>(userOrgDBO.getUserIdListByOrgIds(pushOrgIds));

            if (pushUserIds.contains(userId)) {
                Set<String> userOrgIdSet = new HashSet<>((List<String>) userUpdateDto.getUserInfo().get(LocalUserAttr.org_ids.getDomainName()));
                for (String userOrgId : userOrgIdSet) {
                    if (!pushOrgIds.contains(Long.valueOf(userOrgId))) {
                        return pushConnectorIdName.get(entry.getKey());
                    }
                }
            }
        }
        return "";
    }

    /**
     * 获取推送范围内的组织id集合
     *
     * @param orgGroupIds 组织组id集合
     * @return 组织id集合
     */
    private List<Long> getPushOrgIds(List<Long> orgGroupIds) {
        List<OrgEntity> orgEntities = orgDBO.getOrgsList(orgGroupIds, true, "id, org_path");
        return orgEntities.stream().map(OrgEntity::getId).collect(Collectors.toList());
    }

    private void sendActiveEmail(List<UserEntity> users) {
        String tenantCode = TenantHolder.getTenantCode();
        commonExecutor.submit(() -> {
            TenantHolder.setTenantCode(tenantCode);
            int num = 0;
            MultiLangVO ucName = configService.getBasicProfileCfg().getUcName();
            String platformName = null;
            if (ucName != null) {
                platformName = ucName.getZhCn();
            }

            CfgPasswordPolicy passwordPolicyCfg = configService.getPasswordPolicyCfg();
            CfgPwdComplexity pwdComplexityCfg = configService.getPwdComplexityCfg();

            Integer maxPasswordAgeDays = passwordPolicyCfg.getPwdMaxAge();
            int minLen = pwdComplexityCfg.getMinLen();
            if (minLen < 6) minLen = 6;

            for (UserEntity userEntity : users) {
                try {
                    if (redissonClient.getLock("EMAIL:" + userEntity.getUsername()).tryLock(RedisKeyConstants.TRY_LOCK_WAIT_TIME_IN_MS, RedisKeyConstants.SEND_EMAIL_INTERVAL_IN_MS, TimeUnit.MILLISECONDS)) {
                        num++;
                        if (num % 100 == 0) {
                            Thread.sleep(5000L);
                        }

                        String randomPassword = RandomUtil.getRandomPassword(minLen);
                        final Map<String, Object> userInfo = JsonUtil.obj2Map(userEntity);
                        userInfo.put("temp_password", randomPassword);

                        final boolean sendResult = emailService.sendWelcomeWithPwd(userInfo, platformName);

                        if (sendResult) {
                            activeResetPassword(userEntity.getId(), maxPasswordAgeDays, randomPassword);
                        }
                    } else {
                        logger.warn("send active by email already sent to {} within {} ms", userEntity.getUsername(), RedisKeyConstants.SEND_EMAIL_INTERVAL_IN_MS);
                    }
                } catch (InterruptedException e) {
                    logger.error(e.getMessage(), e);
                }
            }
        });
    }

    private void sendActiveSms(List<UserEntity> users) {
        String tenantCode = TenantHolder.getTenantCode();
        commonExecutor.submit(() -> {
            TenantHolder.setTenantCode(tenantCode);
            int num = 0;
            MultiLangVO ucName = configService.getBasicProfileCfg().getUcName();

            CfgPasswordPolicy passwordPolicyCfg = configService.getPasswordPolicyCfg();
            CfgPwdComplexity pwdComplexityCfg = configService.getPwdComplexityCfg();

            Integer maxPasswordAgeDays = passwordPolicyCfg.getPwdMaxAge();
            int minLen = pwdComplexityCfg.getMinLen();
            if (minLen < 6) minLen = 6;

            for (UserEntity userEntity : users) {
                try {
                    if (redissonClient.getLock("SMS:" + userEntity.getUsername()).tryLock(RedisKeyConstants.TRY_LOCK_WAIT_TIME_IN_MS, RedisKeyConstants.SEND_SMS_INTERVAL_IN_MS, TimeUnit.MILLISECONDS)) {
                        num++;
                        if (num % 100 == 0) {
                            Thread.sleep(5000L);
                        }

                        String randomPassword = RandomUtil.getRandomPassword(minLen);
                        final Map<String, Object> userInfo = JsonUtil.obj2Map(userEntity);
                        userInfo.put("temp_password", randomPassword);

                        smsService.sendActiveSMS(userInfo, ucName);

                        activeResetPassword(userEntity.getId(), maxPasswordAgeDays, randomPassword);
                    } else {
                        logger.warn("send active by mobile already sent to {} within {} ms", userEntity.getUsername(), RedisKeyConstants.SEND_SMS_INTERVAL_IN_MS);
                    }
                } catch (InterruptedException e) {
                    logger.error(e.getMessage(), e);
                }
            }
        });
    }

    @Override
    public String checkUserEntityEmail(UserEntity userEntity, boolean requireVerified) {
        String emailAddr = userEntity.getEmail();
        if (Strings.isNullOrEmpty(emailAddr)) {
            throw new UserException(HttpStatus.BAD_REQUEST, TransactionErrorType.USER_EMAIL_MISSED);
        }

        if (requireVerified && BooleanEnums.isFalse(userEntity.getEmailVerified())) {
            throw new UserException(HttpStatus.BAD_REQUEST, TransactionErrorType.USER_EMAIL_NOT_VERIFIED);
        }
        return emailAddr;
    }

    @Override
    public String checkUserEntitySMS(UserEntity userEntity, boolean requireVerified) {
        String mobile = userEntity.getPhoneNumber();
        if (Strings.isNullOrEmpty(mobile)) {
            throw new UserException(HttpStatus.BAD_REQUEST, TransactionErrorType.USER_MOBILE_MISSED);
        }
        if (requireVerified && userEntity.getPhoneNumberVerified() == BooleanEnums.FALSE.getValue()) {
            throw new UserException(HttpStatus.BAD_REQUEST, TransactionErrorType.USER_MOBILE_NOT_VERIFIED);
        }
        return mobile;
    }


    @Override
    public void updateConnectorBatchNoByIds(Integer syncBatchNo, Set<Long> ids) {
        if (ids == null || ids.size() == 0) {
            return;
        }
        this.userDBO.updateConnectorBatchNoByIds(syncBatchNo, ids);
    }

    @Override
    public List<UserOrgEntity> getUserOrgs(Long userId) {
        if (userId == null) {
            throw new UserException(TransactionErrorType.USER_INVALID);
        }
        UserOrgEntity userOrg = new UserOrgEntity();
        userOrg.setUid(userId);
        return userOrgDBO.getList(userOrg);
    }

    @Override
    public Map<Long, List<UserOrgEntity>> getUserOrgsMap(List<Long> userIds) {
        if (userIds == null || userIds.size() == 0) {
            throw new UserException(TransactionErrorType.USER_INVALID);
        }
        List<UserOrgEntity> list = userOrgDBO.getListByUser(userIds);
        return list != null && list.size() > 0 ? list.stream().filter(el -> el.getUid() != null).collect(Collectors.groupingBy(UserOrgEntity::getUid)) : null;
    }

    @Override
    public PageView<UserEntity> getUsersPage(Pagination<UserQueryDTO> page) {
        PageView<UserEntity> res = userDBO.page(page);
        return res;
    }

    @Override
    public PageView<DeletedUserEntity> getDeletedUsersPage(Pagination<UserQueryDTO> page) {
        PageView<DeletedUserEntity> res = deletedUserDBO.page(page);
        return res;
    }

    @Override
    public UserEntity getUser(Long id) {
        if (id == null) {
            return null;
        }
        UserEntity user = userDBO.getById(id);
        if (user != null) {
            user.decryptFields();
        }
        return user;
    }

    @Override
    public UserDetails getUserDetails(String username) {
        UserDetails loadedUser;
        try {
            loadedUser = userDetailsService.loadUserByUsername(username);
        } catch (UserNotFoundException | UsernameNotFoundException | NeedUserChooseDSException var6) {
            logger.info("user {} not found", username);
            throw new UserCenterException(TransactionErrorType.USER_NAME_PASSWORD_INCONSISTENT);
        } catch (UserException ue) {
            if (HttpStatus.NOT_FOUND == ue.getHttpStatus()) {
                throw new UserCenterException(TransactionErrorType.USER_NAME_PASSWORD_INCONSISTENT);
            }
            throw ue;
        } catch (UserCenterException ex) {
            throw ex;
        } catch (Exception var7) {
            logger.error(username, var7);
            throw new UserCenterException(TransactionErrorType.UNKNOWN_DAO_ERROR);
        }

        if (loadedUser == null) {
            logger.info("user {} not found", username);
            throw new UserCenterException(TransactionErrorType.USER_NAME_PASSWORD_INCONSISTENT);
        } else {
            return loadedUser;
        }
    }

    @Override
    public void userStatuscheck(UserInfo userInfo) {
        UserStatus status = userInfo.getStatus();
        UserEntity user = getUser(Long.valueOf(userInfo.getSub()));
        if (INACTIVE.equals(status) && StringUtils.isNotBlank(user.getEndDate()) && LocalDate.now().isBefore(LocalDate.parse(user.getEndDate(), ISO_DATE))) {
            logger.error("Authentication failed: user {} status is inactive", user.getUsername());
            throw new UserCenterException(HttpStatus.FORBIDDEN, TransactionErrorType.USER_NAME_STATUS_INACTIVE);
        }
        if (SUSPENDED.equals(status)) {
            logger.error("Authentication failed: user {} status is suspended", user.getUsername());
            throw new UserCenterException(HttpStatus.FORBIDDEN, TransactionErrorType.USER_NAME_STATUS_SUSPENDED);
        }
        if (DEACTIVATE.equals(status)) {
            logger.error("Authentication failed: user {} status is deactivate", user.getUsername());
            throw new UserCenterException(HttpStatus.FORBIDDEN, TransactionErrorType.USER_NAME_STATUS_DEACTIVATE);
        }
        if (StringUtils.isNotBlank(user.getEndDate()) && OVERDUE.equals(status)) {
            logger.error("Authentication failed: user {} status is OVERDUE", user.getUsername());
            throw new UserCenterException(HttpStatus.FORBIDDEN, TransactionErrorType.USER_NAME_STATUS_OVERDUE);
        }
    }

    @Override
    public DeletedUserEntity getDeletedUser(Long id) {
        if (id == null) {
            return null;
        }
        return deletedUserDBO.getById(id);
    }

    private void writeBOM(HttpServletResponse response) throws IOException {
        response.getWriter().write('\ufeff');
    }

    private String plainConverter(String poPropertyName) {
        return poPropertyName;
    }


    private CsvWriter csvWriter = new CsvWriter();

    private void writeCsvHeader(HttpServletResponse response, String[] requestPOAttrs) throws IOException {
        StringBuilder sb = new StringBuilder(CsvWriter.INITIAL_STRING_SIZE);
        csvWriter.writeToStringBuilder(requestPOAttrs, this::plainConverter, false, sb);
        response.getWriter().print(sb.toString());
        logger.info("exportUsers: header {}", sb.deleteCharAt(sb.length() - 1).toString());
    }


    public String toFieldName(String domainName) {
        return fieldDictService.toFieldName(domainName, FieldType.USER);
    }

    /**
     * get user info as a String by key
     *
     * @param mapUser user info map
     * @param key     the user info key
     * @return user info as a String
     */
    private List getUserInfoAsList(Map<?, ?> mapUser, String key) {
        Object value = mapUser.get(key);
        if (value != null) {
            return (List) value;
        } else {
            return null;
        }
    }

    /**
     * get user info as a String by key, or user defaultValue
     *
     * @param mapUser      user info map
     * @param key          the user info key
     * @param defaultValue
     * @return user info as a String
     */
    private String getUserInfoAsString(Map<?, ?> mapUser, String key, String defaultValue) {
        if (mapUser.containsKey(key)) {
            return getUserInfoAsString(mapUser, key);
        } else {
            return defaultValue;
        }
    }


    /**
     * get user info as a String by key
     *
     * @param mapUser user info map
     * @param key     the user info key
     * @return user info as a String
     */
    private String getUserInfoAsString(Map<?, ?> mapUser, String key) {
        Object value = mapUser.get(key);
        if (value != null) {
            return value.toString();
        } else {
            return "";
        }
    }

    private String getUserInfoAsJson(Map<?, ?> mapUser, String key) {
        Object value = mapUser.get(key);
        if (value != null) {
            return JsonUtil.obj2Str(value);
        } else {
            return null;
        }
    }

    private boolean whetherEmailChanged(Map<String, Object> userInfo, UserEntity userEntity) {
        String passedEmail = getUserInfoAsString(userInfo, LocalUserAttr.email.getDomainName(), null);

        if (StringUtil.isEmptyOrNull(passedEmail)) {
            //email param not passed, email not changed
            return false;
        }

        return !StringUtil.equalsIgnoreCase(passedEmail, userEntity.getEmail());
    }

    private boolean whetherMobileChanged(Map<String, Object> userInfo, UserEntity userEntity) {
        String userInputMobile = getUserInfoAsString(userInfo, LocalUserAttr.phone_number.getDomainName(), null);

        if (StringUtil.isEmptyOrNull(userInputMobile)) {
            //mobile param not passed, mobile not changed
            return false;
        }

        return !StringUtil.equalsIgnoreCase(userInputMobile, userEntity.getPhoneNumber());
    }

    private boolean isEmailVerifiedFlagChange(Map<String, Object> userInfo, UserEntity userEntity) {
        String emailVerified = getUserInfoAsString(userInfo, LocalUserAttr.email_verified.getDomainName(), null);

        if (StringUtil.isEmptyOrNull(emailVerified)) {
            //email param not passed, email not changed
            return false;
        }

        return !StringUtil.equalsIgnoreCase(emailVerified, userEntity.getEmailVerified().toString());
    }

    private boolean isPhoneNumberVerifiedFlagChange(Map<String, Object> userInfo, UserEntity userEntity) {
        String phoneNumberVerified = getUserInfoAsString(userInfo, LocalUserAttr.phone_number_verified.getDomainName(), null);

        if (StringUtil.isEmptyOrNull(phoneNumberVerified)) {
            //phone number param not passed, email not changed
            return false;
        }

        return !StringUtil.equalsIgnoreCase(phoneNumberVerified, userEntity.getEmailVerified().toString());
    }

    private void addExtFields(QueryPage<Map<String, Object>> queryPage, Set<String> extFieldNames) {
        Set<Long> userIds = queryPage.getItems().stream()
                .map(e -> (Long) e.get(LocalUserAttr.sub.getFieldName()))
                .collect(Collectors.toSet());
        if (userIds == null || userIds.size() == 0) {
            return;
        }
        Map<Long, Map<String, String>> mapExtEntities = fieldExtDataHandler.list(userIds, extFieldNames, FieldType.USER);
        if (mapExtEntities != null && mapExtEntities.size() > 0) {
            queryPage.getItems().stream().forEach(e -> {
                Long id = (Long) e.get(LocalUserAttr.sub.getFieldName());
                Map<String, String> extAttrs = mapExtEntities.get(id);
                if (extAttrs != null) {
                    e.putAll(extAttrs);
                }
            });
        }
    }

    private void addExtFields(Map dataMap, Long uid, Set<String> extFieldNames) {
        Set<Long> userIds = new HashSet<>();
        userIds.add(uid);
        if (userIds == null || userIds.size() == 0) {
            return;
        }
        Map<Long, Map<String, String>> mapExtEntities = fieldExtDataHandler.list(userIds, extFieldNames, FieldType.USER);
        if (mapExtEntities != null && mapExtEntities.get(uid) != null) {
            dataMap.putAll(mapExtEntities.get(uid));
        }

    }

    private void checkUserInputHasMandatoryAttributes(Map<String, Object> userInput) {
        CfgPoliciesSignup cfgPoliciesSignup = configService.getSignupPolicy();

        List<String> missed = new ArrayList<>(3);

        if (cfgPoliciesSignup != null) {
            for (String attr : cfgPoliciesSignup.getMandatoryAttrs()) {
                String attributeValue = getUserInfoAsString(userInput, attr, null);
                if (StringUtil.isEmptyOrNull(attributeValue)) {
                    String domainName = fieldDictService.toDomainName(attr, FieldType.USER);
                    if (StringUtils.isNotBlank(domainName)) {
                        missed.add(domainName);
                    }
                }
            }
        }

        if (!missed.isEmpty()) {
            String errMsg = String.format("%s Required", missed);
            logger.error(errMsg);
            throw new UserException(HttpStatus.BAD_REQUEST, TransactionErrorType.USER_ATTR_MISSED, errMsg);
        }
    }

    private void addOrgs(QueryPage<Map<String, Object>> queryPage) {
        Set<Long> userIds = queryPage.getItems().stream()
                .map(e -> (Long) e.get(LocalUserAttr.sub.getFieldName()))
                .collect(Collectors.toSet());
        if (userIds == null || userIds.size() == 0) {
            return;
        }
        OrgEntity rootOrgEntity = new OrgEntity();
        List<OrgEntity> allOrgs = iOrganizationService.getList(rootOrgEntity);
        Map<Long, OrgEntity> allOrgMap = allOrgs.stream().collect(Collectors.toMap(OrgEntity::getId, Function.identity()));

        Map<Long, List<OrgEntity>> userOrgMapList = this.iOrganizationService.getUserOrgEntityList(userIds);
        List<Long> scopes = roleBindingService.searchUserBindingScope();
        Set<Long> scopesSet = new HashSet<>(scopes);
        queryPage.getItems().stream().forEach(e -> {
            Long id = (Long) e.get(LocalUserAttr.sub.getFieldName());
            List<OrgEntity> orgEntityList = userOrgMapList.get(id);
            if (CollectionUtils.isEmpty(orgEntityList)) {
                e.put(DaoConstants.ORGS, null);
                e.put(LocalUserAttr.org_ids.getFieldName(), null);
                return;
            }
            if (CollectionUtils.isNotEmpty(scopesSet)) {
                // 筛选出管理范围内的部门
                orgEntityList = orgEntityList.stream()
                        .filter(orgEntity -> {
                            // 解析orgPath为集合
                            Set<String> orgPathStringSet = new HashSet<>(Arrays.asList(orgEntity.getOrgPath().split(",")));
                            Set<Long> orgPathSet = orgPathStringSet.stream().map(Long::parseLong).collect(Collectors.toSet());
                            // 创建scopesSet的副本，以避免修改原始的scopesSet
                            Set<Long> tempScopesSet = new HashSet<>(scopesSet);
                            // 计算tempScopesSet和orgPathSet的交集
                            tempScopesSet.retainAll(orgPathSet);
                            // 判断是否存在交集,存在说明在管理范围内
                            return !tempScopesSet.isEmpty();
                        })
                        .collect(Collectors.toList());
            }

            e.put(DaoConstants.ORGS, orgEntityList.stream().map(OrgEntity::getName).collect(Collectors.toList()));
            e.put(LocalUserAttr.org_ids.getFieldName(), orgEntityList.stream().map(OrgEntity::getId).collect(Collectors.toList()));
            StringBuilder deptPaths = new StringBuilder();
            for (OrgEntity orgEntity : orgEntityList) {
                String[] split = orgEntity.getOrgPath().split(",");
                StringBuilder deptPath = new StringBuilder();
                for (int i = 1; i < split.length; i++) {
                    deptPath.append("/").append(allOrgMap.get(Long.valueOf(split[i])).getName());
                }
                if (deptPaths.length() == 0) {
                    deptPaths.append(deptPath);
                } else {
                    deptPaths.append(",").append(deptPath);
                }
            }
            e.put(LocalUserAttr.dept_path.getFieldName(), deptPaths.toString());
        });
    }

    public void changeStatusByUsername(String username, String status) throws UserCenterException, DaoException {
        UserEntity userEntity = userDBO.getOneByUsername(username);
        if (userEntity == null) {
            throw new UserException(HttpStatus.BAD_REQUEST, TransactionErrorType.USER_NOT_FOUND);
        }

        if (BY_SYSTEM.getValue() == userEntity.getCreatedMode()) {
            logger.warn("administrator {} cannot be change status", username);
            throw new UserException(HttpStatus.BAD_REQUEST, TransactionErrorType.USER_ADMIN_NO_STATUS_CHANGE_ERROR);
        }
        // 已停用的用户不允许修改用户状态
        if (DEACTIVATE.getValue() == userEntity.getStatus()) {
            logger.error("Deactivate users are not allowed to modify their user status.");
            throw new UserException(HttpStatus.BAD_REQUEST, TransactionErrorType.USER_STATUS_INVALID);
        }

        UserStatus userStatus = UserStatus.valueOf(status);
        if (INACTIVE.getValue() == userEntity.getStatus() && userStatus == ACTIVE) {
            logger.error("inactive status can't be changed to active.");
            throw new UserException(HttpStatus.BAD_REQUEST, TransactionErrorType.USER_STATUS_INVALID);
        }

        if (OVERDUE.getValue() == userEntity.getStatus() && userStatus == ACTIVE) {
            logger.error("overdue status can't be changed to active.");
            throw new UserException(HttpStatus.BAD_REQUEST, TransactionErrorType.USER_NAME_STATUS_OVERDUE);
        }
        try {
            // 启用的同时，重置密码失败次数
            if (userStatus == ACTIVE) {
                delLoginFailCount(username, userEntity.getEmail(), userEntity.getUserJobNumber());
            }
            //  禁用直接返回禁用。其他要要判断是否到达生效时间，不到时间 状态=未生效，到生效时间 状态=激活；状态=未激活，返回未激活；状态等于激活，返回激活
            userEntity.setStatus(getUserStatusByDate(userEntity.getId(), userStatus.getValue(), userEntity.getStartDate(), userEntity.getEndDate()));
            userEntity.setUpdateTime(LocalDateTime.now());
            userEntity.setUpdateBy(UserUtil.getLoginUserName());
            this.userDBO.updateById(userEntity);
            if (SUSPENDED.name().equals(status)) {
                UserStatusEntity userStatusEntity = new UserStatusEntity();
                userStatusEntity.setId(userEntity.getId());
                userStatusEntity.setSuspendTime(LocalDateTime.now());
                userStatusService.createOrUpdateUserStatus(userStatusEntity);
            }
            // 推送
            dataSyncService.pushDataToApp(userEntity.getId(), PushBusinessType.USER, DataChangeType.UPDATE);
        } catch (Exception e) {
            throw new UserException(HttpStatus.BAD_REQUEST, TransactionErrorType.USER_SCHEMA_ATTR_DATA_TYPE_INVALID);
        }
    }

    /**
     * 清除用户登录失败次数
     *
     * @param username      用户名
     * @param email         邮箱
     * @param userJobNumber 工号
     */
    private void delLoginFailCount(String username, String email, String userJobNumber) {
        if (StringUtils.isNotBlank(username)) {
            String key = String.format(PASSWD_FAIL_COUNT_FOR_LOCK, TenantHolder.getTenantCode(), username);
            // 清除redis记数
            redissonClient.getAtomicLong(key).delete();
        }
        if (StringUtils.isNotBlank(email)) {
            String key = String.format(PASSWD_FAIL_COUNT_FOR_LOCK, TenantHolder.getTenantCode(), email);
            // 清除redis记数
            redissonClient.getAtomicLong(key).delete();
        }
        if (StringUtils.isNotBlank(userJobNumber)) {
            String key = String.format(PASSWD_FAIL_COUNT_FOR_LOCK, TenantHolder.getTenantCode(), userJobNumber);
            // 清除redis记数
            redissonClient.getAtomicLong(key).delete();
        }
    }

    @Override
    public List<UserBasicInfoVO> getUserBasicInfoList(List<Long> userIds) {
        List<UserEntity> userList = getUsers(userIds);
        if (userList != null && !userList.isEmpty()) {
            Map<Long, List<UserOrgEntity>> userOrgs = getUserOrgsMap(userIds);
            List<UserBasicInfoVO> userInfos = new ArrayList<>(userList.size());
            userList.forEach(user -> {
                // 查询用户列表及转换基本信息
                UserBasicInfoVO vo = userTransfer.entityToBasicVO(user);
                // 查询用户所在组信息，组名称和用户编号
                List<UserOrgEntity> userOrgList = userOrgs.get(user.getId());
                if (userOrgList != null && userOrgList.size() > 0) {
                    Set<String> userCodes = new HashSet<>(userOrgList.size());
                    List<String> orgNames = new ArrayList<>(userOrgList.size());
                    userOrgList.forEach(userOrg -> {
                        userCodes.add(userOrg.getUserCode());
                        OrgEntity org = orgDBO.getById(userOrg.getOrgRefId());
                        if (org != null) {
                            orgNames.add(org.getName());
                        }
                    });
                    vo.setOrgNames(orgNames.stream().collect(Collectors.joining(",")));
                    vo.setUserCodes(userCodes.stream().collect(Collectors.joining(",")));
                }
                userInfos.add(vo);
            });
            return userInfos;
        }
        return Collections.emptyList();
    }

    @Override
    public List<UserBasicInfoVO> getUserBasicInfoVoList(List<Long> userIds) {
        List<UserEntity> userList = getUsers(userIds);
        if (userList != null && !userList.isEmpty()) {
            Map<Long, List<UserOrgEntity>> userOrgs = getUserOrgsMap(userIds);
            List<UserBasicInfoVO> userInfos = new ArrayList<>(userList.size());
            userList.forEach(user -> {
                String email = user.getEmail();
                String phoneNumber = user.getPhoneNumber();
                // 查询用户列表及转换基本信息
                UserBasicInfoVO vo = userTransfer.entityToBasicVO(user);
                vo.setEmail(email);
                vo.setPhoneNumber(phoneNumber);
                // 查询用户所在组信息，组名称和用户编号
                List<UserOrgEntity> userOrgList = userOrgs.get(user.getId());
                if (userOrgList != null && userOrgList.size() > 0) {
                    Set<String> userCodes = new HashSet<>(userOrgList.size());
                    List<String> orgNames = new ArrayList<>(userOrgList.size());
                    userOrgList.forEach(userOrg -> {
                        userCodes.add(userOrg.getUserCode());
                        OrgEntity org = orgDBO.getById(userOrg.getOrgRefId());
                        if (org != null) {
                            orgNames.add(org.getName());
                        }
                    });
                    vo.setOrgNames(orgNames.stream().collect(Collectors.joining(",")));
                    vo.setUserCodes(userCodes.stream().collect(Collectors.joining(",")));
                }
                userInfos.add(vo);
            });
            return userInfos;
        }
        return Collections.emptyList();
    }

    @Override
    public List<UserBasicInfoVO> getUserBasicInfoListByEmails(List<String> emails) {
        List<UserEntity> userList = userDBO.getVerifiedEmailUsers(emails);
        List<Long> userIds = userList.stream().map(UserEntity::getId).collect(Collectors.toList());
        if (!userList.isEmpty()) {
            Map<Long, List<UserOrgEntity>> userOrgs = getUserOrgsMap(userIds);
            List<UserBasicInfoVO> userInfos = new ArrayList<>(userList.size());
            userList.forEach(user -> {
                // 查询用户列表及转换基本信息
                UserBasicInfoVO vo = userTransfer.entityToBasicVO(user);
                // 查询用户所在组信息，组名称和用户编号
                List<UserOrgEntity> userOrgList = userOrgs.get(user.getId());
                if (userOrgList != null && !userOrgList.isEmpty()) {
                    Set<String> userCodes = new HashSet<>(userOrgList.size());
                    List<String> orgNames = new ArrayList<>(userOrgList.size());
                    userOrgList.forEach(userOrg -> {
                        userCodes.add(userOrg.getUserCode());
                        OrgEntity org = orgDBO.getById(userOrg.getOrgRefId());
                        if (org != null) {
                            orgNames.add(org.getName());
                        }
                    });
                    vo.setOrgNames(String.join(",", orgNames));
                    vo.setUserCodes(String.join(",", userCodes));
                }
                userInfos.add(vo);
            });
            return userInfos;
        }
        return Collections.emptyList();
    }

    @Override
    public List<UserBasicInfoVO> getUserBasicInfoVoListByEmails(List<String> emails) {
        List<UserEntity> userList = userDBO.getVerifiedEmailUsers(emails);
        List<Long> userIds = userList.stream().map(UserEntity::getId).collect(Collectors.toList());
        if (!userList.isEmpty()) {
            Map<Long, List<UserOrgEntity>> userOrgs = getUserOrgsMap(userIds);
            List<UserBasicInfoVO> userInfos = new ArrayList<>(userList.size());
            userList.forEach(user -> {
                String email = user.getEmail();
                String phoneNumber = user.getPhoneNumber();
                // 查询用户列表及转换基本信息
                UserBasicInfoVO vo = userTransfer.entityToBasicVO(user);
                vo.setEmail(email);
                vo.setPhoneNumber(phoneNumber);
                // 查询用户所在组信息，组名称和用户编号
                List<UserOrgEntity> userOrgList = userOrgs.get(user.getId());
                if (userOrgList != null && !userOrgList.isEmpty()) {
                    Set<String> userCodes = new HashSet<>(userOrgList.size());
                    List<String> orgNames = new ArrayList<>(userOrgList.size());
                    userOrgList.forEach(userOrg -> {
                        userCodes.add(userOrg.getUserCode());
                        OrgEntity org = orgDBO.getById(userOrg.getOrgRefId());
                        if (org != null) {
                            orgNames.add(org.getName());
                        }
                    });
                    vo.setOrgNames(String.join(",", orgNames));
                    vo.setUserCodes(String.join(",", userCodes));
                }
                userInfos.add(vo);
            });
            return userInfos;
        }
        return Collections.emptyList();
    }

    @Override
    public List<UserBasicInfoVO> getUserBasicInfoListByPhones(List<String> phones) {
        List<String> phoneList = phones.stream().map(UserUtil::formatMobile).collect(Collectors.toList());
        List<UserEntity> userList = userDBO.getVerifiedPhoneUsers(phoneList);

        List<Long> userIds = userList.stream().map(UserEntity::getId).collect(Collectors.toList());
        if (!userList.isEmpty()) {
            Map<Long, List<UserOrgEntity>> userOrgs = getUserOrgsMap(userIds);
            List<UserBasicInfoVO> userInfos = new ArrayList<>(userList.size());
            userList.forEach(user -> {
                // 查询用户列表及转换基本信息
                UserBasicInfoVO vo = userTransfer.entityToBasicVO(user);
                // 查询用户所在组信息，组名称和用户编号
                List<UserOrgEntity> userOrgList = userOrgs.get(user.getId());
                if (userOrgList != null && !userOrgList.isEmpty()) {
                    Set<String> userCodes = new HashSet<>(userOrgList.size());
                    List<String> orgNames = new ArrayList<>(userOrgList.size());
                    userOrgList.forEach(userOrg -> {
                        userCodes.add(userOrg.getUserCode());
                        OrgEntity org = orgDBO.getById(userOrg.getOrgRefId());
                        if (org != null) {
                            orgNames.add(org.getName());
                        }
                    });
                    vo.setOrgNames(String.join(",", orgNames));
                    vo.setUserCodes(String.join(",", userCodes));
                }
                userInfos.add(vo);
            });
            return userInfos;
        }
        return Collections.emptyList();
    }

    @Override
    public List<UserBasicInfoVO> getUserBasicInfoList(String filter, List<Long> deptIds) {
        List<Long> userIds = null;
        if (StringUtils.isNotBlank(filter)) {
            userIds = searchUserWithInfo(filter).stream().map(UserVO::getId).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(deptIds)) {
            List<Long> userIdsByOrgId = userOrgDBO.getUserIdListByOrgIds(deptIds);
            if (userIds != null) {
                userIds.retainAll(userIdsByOrgId);
            } else {
                userIds = userIdsByOrgId;
            }
        }
        return getUserBasicInfoList(userIds);
    }

    @Override
    public List<UserVO> searchUserWithInfo(String filter) {
        LambdaQueryWrapper<UserEntity> queryWrapper = new QueryWrapper().select().lambda();
        if (StringUtils.isNotBlank(filter)) {
            queryWrapper.nested(e -> e.likeRight(UserEntity::getUsername, filter)
                    .or().likeRight(UserEntity::getName, filter)
                    .or().eq(UserEntity::getEmail, UserUtil.encryptSensitiveInfo(filter.toLowerCase()))
                    .or().eq(UserEntity::getPhoneNumber, UserUtil.encryptSensitiveInfo(filter)));
        }
        List<UserVO> userVOS = userTransfer.entityListToVoList(userDBO.list(queryWrapper));
        return userVOS;
    }

    @Override
    public List<UserVO> searchUserCreateTag(UserDTO userDTO) {
        LambdaQueryWrapper<UserEntity> queryWrapper = new QueryWrapper().select().lambda();
        if (userDTO.getEmails() != null && userDTO.getEmails().size() != 0) {
            List<String> emails = new ArrayList<>();
            for (String email : userDTO.getEmails()) {
                emails.add(UserUtil.encryptSensitiveInfo(email.toLowerCase()));
            }
            queryWrapper.in(UserEntity::getEmail, emails);
        }
        if (userDTO.getPhones() != null && userDTO.getPhones().size() != 0) {
            List<String> phones = new ArrayList<>();
            for (String phone : userDTO.getPhones()) {
                phones.add(UserUtil.encryptSensitiveInfo(phone));
            }
            queryWrapper.in(UserEntity::getPhoneNumber, phones);
        }
        return userTransfer.entityListToVoList(userDBO.list(queryWrapper));
    }

    @Override
    public List<UserEntity> searchNeedSendMessage() {
        LambdaQueryWrapper<UserEntity> queryWrapper = new QueryWrapper().select().lambda();
        Integer expireRemindDays;
        Integer remindDays;
        if (configService.getUserPolicyCfg() == null) {
            expireRemindDays = 30;
            remindDays = 5;
        } else {
            expireRemindDays = configService.getUserPolicyCfg().getExpireRemindDays();
            remindDays = configService.getUserPolicyCfg().getCycleRemindDays();
        }
        logger.info("expireRemindDays is : {}, cycleRemindDays: {}", expireRemindDays, remindDays);
        queryWrapper.nested(e -> e.isNotNull(UserEntity::getStartDate).or().isNotNull(UserEntity::getEndDate));
        List<UserEntity> list = userDBO.list(queryWrapper);
        // 更新状态
        Iterator<UserEntity> userIterator = list.iterator();
        while (userIterator.hasNext()) {
            UserEntity userEntity = userIterator.next();
            if (userEntity.getStatus() != SUSPENDED.getValue()) {
                userEntity.setStatus(getUserStatusByDate(userEntity.getId(), userEntity.getStatus(), userEntity.getStartDate(), userEntity.getEndDate()));
            }

            userDBO.updateById(userEntity);
            LocalDate endDate = StringUtils.isBlank(userEntity.getEndDate()) ? LocalDate.MAX : LocalDate.parse(userEntity.getEndDate(), ISO_DATE);
            if (!(LocalDate.now().until(endDate, ChronoUnit.DAYS) <= expireRemindDays && LocalDate.now().until(endDate, ChronoUnit.DAYS) >= 0)) {
                userIterator.remove();
            }
        }

        return list;
    }

    @Override
    public UserInfo searchByUserAttribute(Long fieldId, String value) {
        FieldDictEntity fieldDict = fieldDictService.getEntityById(fieldId);
        String fieldName = fieldDict.getFieldName();
        UserEntity userEntity = null;
        List<UserEntity> userList;
        if (BY_SYSTEM.getValue() == fieldDict.getCreateMod()) {
            Map<String, Object> map = new HashMap<>();
            map.put(fieldName, value);
            userList = userDBO.selectByMap(map);
        } else {
            userList = userDao.getUserListByExtAttribute(fieldName, value);
        }

        List<UserEntity> collect = userList.stream().filter(e -> !e.getCreatedMode().equals(BY_SYSTEM.getValue())).collect(Collectors.toList());
        if (userList.size() != 1) {
            logger.warn("matched not only one user");
            return null;
        }
        if (!collect.isEmpty()) {
            userEntity = userList.get(0);
        } else {
            return null;
        }
        return assembleUserInfo(userEntity);
    }

    @Override
    public void sendMsgToPasswdExpireUser() {
        String tenantId = TenantHolder.getTenantCode();
        String key = String.format(RedisKeyConstants.USER_PASSWD_EXPIRE_TENANT_LOCK_KEY, tenantId);

        RLock checkLock = redissonClient.getLock(key);
        if (checkLock.isLocked()) {
            throw new UserCenterException("The current tenant is locked");
        }
        RLock lock = redissonClient.getLock(key);
        try {
            if (!lock.tryLock(RedisKeyConstants.USER_PASSWD_EXPIRE_WAIT, RedisKeyConstants.USER_PASSWD_EXPIRE, TimeUnit.MILLISECONDS)) {
                logger.warn("sync user cannot get lock for :{}", tenantId);
                return;
            }

            CfgPasswordPolicy passwordPolicyCfg = configService.getPasswordPolicyCfg();
            if (passwordPolicyCfg != null && passwordPolicyCfg.getPwdExpireWarning() != null) {
                Integer passwdExpireDays = passwordPolicyCfg.getPwdExpireWarning();
                LocalDateTime expireTime = LocalDateTime.of(LocalDate.now().plusDays(passwdExpireDays), LocalTime.MAX);
                LambdaQueryWrapper<UserEntity> queryWrapper = new QueryWrapper().select("id", "pwd_expiration_time", "username", "email", "phone_number").lambda();
                queryWrapper.isNotNull(UserEntity::getPwdExpirationTime);
                queryWrapper.lt(UserEntity::getPwdExpirationTime, expireTime);
                List<UserEntity> list = userDBO.list(queryWrapper);
                UserUtil.decryptEntityListFields(list);
                logger.info("need send passwd expire time user num is {}", list.size());
                for (UserEntity userEntity : list) {
                    messageService.sendPwdExpireMsg(String.valueOf(userEntity.getId()), userEntity.getPwdExpirationTime());
                    emailService.sendMsg(DaoConstants.EXPIRE_PWD_EMAIL, userEntity.getUsername(), userEntity.getEmail(), null, userEntity.getPwdExpirationTime());
                    smsService.sendMsg(DaoConstants.EXPIRE_PWD_SMS, userEntity.getUsername(), userEntity.getPhoneNumber(), null, userEntity.getPwdExpirationTime());
                }
            }

        } catch (Exception e) {
            logger.error("send msg to passwd expire user error", e);
        } finally {
            lock.unlock();
        }
    }

    @Override
    public UserInfo searchByUserAttribute(String domainName, String value) {
        UserEntity userEntity = getUserEntityByAttribute(domainName, value);
        return assembleUserInfo(userEntity);
    }

    @Override
    public UserEntity getUserEntityByAttribute(String domainName, String value) {
        UserEntity userEntity = null;
        if (LocalUserAttr.connector_user_id.getDomainName().equals(domainName)) {
            List<ExternalUserEntity> externalUserEntityList = externalUserService.listByExternalId(value, DaoConstants.USER_ID);
            Set<Long> userIds = externalUserEntityList.stream().map(e -> e.getUserId()).collect(Collectors.toSet());
            if (userIds.size() > 1) {
                logger.warn("matched not only one user");
                return null;
            }
            if (userIds.size() > 0) {
                Long userId = externalUserEntityList.get(0).getUserId();
                userEntity = getUser(userId);
            }
        } else {
            if (domainName.equalsIgnoreCase(LocalUserAttr.phone_number.getDomainName())) {
                if (StringUtils.isNotBlank(value) && value.matches(CommonConstants.CHINA_LOCAL_MOBILE_REG)) {
                    value = UserUtil.formatMobile(value);
                }
            }
            FieldDictEntity fieldDict = fieldDictService.getByDomainName(domainName, FieldType.USER);
            String fieldName = fieldDict.getFieldName();
            List<UserEntity> userList;
            if (BY_SYSTEM.getValue() == fieldDict.getCreateMod()) {
                Map<String, Object> map = new HashMap<>();
                map.put(fieldName, value);
                userList = userDBO.selectByMap(map);
            } else {
                userList = userDao.getUserListByExtAttribute(fieldName, value);
            }

            List<UserEntity> collect = userList.stream().filter(e -> !e.getCreatedMode().equals(BY_SYSTEM.getValue())).collect(Collectors.toList());
            if (collect.size() > 1) {
                logger.warn("matched not only one user");
                return null;
            }
            if (collect.size() > 0) {
                userEntity = userList.get(0);
            }
        }

        return userEntity;
    }

    /**
     * 用户管理界面，增加临时禁用，让管理员可以手动解除登录锁定
     *
     * @param status        状态
     * @param username      用户名
     * @param email         邮箱
     * @param userJobNumber 工号
     * @return 对应的状态码
     */
    private String calcStatus(String status, Object username, Object email, Object userJobNumber) {
        if (checkAccountLock(username) || checkAccountLock(email) || checkAccountLock(userJobNumber)) {
            // 检测到用户名被锁定，返回状态临时禁用
            return TMPSUSPENDED.name();
        }
        return status;
    }

    /**
     * 校验该账户是否被锁定
     *
     * @param account 账户
     * @return 被锁定返回true，反之false
     */
    private Boolean checkAccountLock(Object account) {
        if (account == null || account.toString().trim().length() == 0) {
            return false;
        }
        CfgPasswordPolicy passwordPolicyCfg = configService.getPasswordPolicyCfg();
        String key = String.format(PASSWD_FAIL_COUNT_FOR_LOCK, TenantHolder.getTenantCode(), account);
        Integer allowFailCount = passwordPolicyCfg.getContinuousFailureCount();
        long times = redissonClient.getAtomicLong(key).get();
        // 如果失败次数大于允许失败次数，返回true，表示用户被锁定
        return times > allowFailCount;
    }

    private Integer getUserStatusByDate(Long userId, Integer status, String start, String end) {
        // 1. 禁用
        if (status != null && SUSPENDED.getValue() == status) {
            return TypeMapper.asInt(SUSPENDED);
        }
        // 2. 停用，已停用用户不允许修改用户状态
        if(Objects.nonNull(userId)){
            UserEntity user = userDBO.getById(userId);
            if(user.getStatus() == UserStatus.DEACTIVATE.getValue()){
                return TypeMapper.asInt(DEACTIVATE);
            }
        } else {
            if (status != null && DEACTIVATE.getValue() == status) {
                return TypeMapper.asInt(DEACTIVATE);
            }
        }

        // 2. 判断时间相关的状态
        return determineStatusByDate(userId, status, start, end);
    }

    // 判断是否未激活
    private boolean isNotActivated(Long userId) {
        if (userId != null) {
            // 没登录过就算未激活
            UserStatusEntity userStatusEntity = userStatusService.getUserStatusById(userId);
            return userStatusEntity == null || userStatusEntity.getActive() == null;
        }
        return false;
    }

    private Integer determineStatusByDate(Long userId, Integer status, String start, String end) {
        LocalDate now = LocalDate.now();
        LocalDate startDate = StringUtils.isBlank(start) ? LocalDate.MIN : LocalDate.parse(start, ISO_DATE);
        LocalDate endDate = StringUtils.isBlank(end) ? LocalDate.MAX : LocalDate.parse(end, ISO_DATE);

        // 1. 判断是否未生效
        if (now.isBefore(startDate)) {
            return TypeMapper.asInt(INACTIVE);
        }

        // 2. 判断是否过期
        if (now.isAfter(endDate)) {
            return TypeMapper.asInt(OVERDUE);
        }

        // 3. userId和status都为空时，表示新创建用户，默认未激活
        if (userId == null && status == null) {
            return TypeMapper.asInt(NOLOGIN);
        }

        // 4. 判断未激活状态, 正常的用户不再次判断,不允许从 ACTIVE -> NOLOGIN
        if (status != null && ACTIVE.getValue() != status) {
            if (isNotActivated(userId)) {
                return TypeMapper.asInt(NOLOGIN);
            }
            if (NOLOGIN.getValue() == status) {
                return TypeMapper.asInt(NOLOGIN);
            }
        }

        // 5. 处理激活状态
        // 无时间限制或在有效期内且原始状态为激活
        if (status != null) {
            if (LocalDate.MIN.equals(startDate) && LocalDate.MAX.equals(endDate)) {
                return TypeMapper.asInt(ACTIVE);
            }
            if (!now.isBefore(startDate) && !now.isAfter(endDate)) {
                return TypeMapper.asInt(ACTIVE);
            }
            // 6. 返回原始状态
            return status;
        }

        // 7. 默认返回未激活状态
        return TypeMapper.asInt(NOLOGIN);
    }


    /**
     * push用户到第三方应用
     *
     * @param username
     * @return
     */
    @Override
    @ServiceLog(description = "Service updateUser")
    @Transactional(rollbackFor = Exception.class)
    public Void pushUser(String username) {
        return pushUserInternal(username);
    }

    /**
     * 系统内部push用户的方法，不单独走审计
     *
     * @param username
     * @return
     */
    @Override
    public Void pushUserInternal(String username) {
        if (StringUtil.isEmptyOrNull(username)) {
            throw new UserException(HttpStatus.NOT_FOUND, TransactionErrorType.USER_NOT_FOUND);
        }

        UserEntity userEntityInDB = userDBO.getOneByUsername(username,
                LocalUserAttr.sub.getFieldName(), LocalUserAttr.email.getFieldName(),
                LocalUserAttr.email_verified.getFieldName(), LocalUserAttr.phone_number.getFieldName(),
                LocalUserAttr.phone_number_verified.getFieldName(), LocalUserAttr.created_mode.getFieldName(),
                LocalUserAttr.come_from.getFieldName());
        if (userEntityInDB == null) {
            logger.error("user: {} not found", username);
            throw new UserCenterException(HttpStatus.NOT_FOUND, TransactionErrorType.USER_NOT_FOUND);
        }

        Long uid = userEntityInDB.getId();
        //数据推送
        dataSyncService.pushDataToApp(uid, PushBusinessType.USER, DataChangeType.UPDATE);
        return null;
    }

    @Override
    public UserInfo assembleUserInfo(UserEntity userEntity) {
        UserInfo userInfo = oauth2Transfer.toUserInfo(userEntity);
        if (userInfo != null) {
            List<UserCertificateEntity> certificationEntities = userCertificateDbo.findCertByUsername(userEntity.getUsername());
            if (certificationEntities != null && certificationEntities.size() > 0) {
                List<String> certIds = certificationEntities.stream().map(el -> el.getCertId()).collect(Collectors.toList());
                userInfo.setUserCertIds(certIds);
            }
        }
        return userInfo;
    }

    @Override
    public Boolean hasOrgUser(Long orgId) {
        return userDBO.hasOrgUser(orgId);
    }

    @Override
    public List<Map<String, Object>> searchUserByIds(List<String> userIds) {
        List<Map<String, Object>> list = userDBO.searchUserByIds(userIds);
        // mfa使用该接口需要sub属性作为唯一值
        if (CollectionUtils.isNotEmpty(list)) {
            for (Map<String, Object> map : list) {
                if (map != null) {
                    map.put("sub", map.get("id"));
                }
            }
        }
        // 掩码用户敏感信息
        UserUtil.maskListMapFields(list);
        return list;
    }

    @Override
    public Set<Long> getUserIdByCriteria(List<FilterGroup> orList, List<Long> orgIds) {
        // 将条件中的字段转为数据库表中的字段
        generateField(orList);
        Set<String> exFields = iFieldDictService.getAdminCreatedFieldDomainNames(FieldType.USER);
        Set<String> baseFields = iFieldDictService.getSystemCreatedFieldFieldNames(FieldType.USER);
        List<OrgEntity> orgsList = orgDBO.getOrgsList(orgIds, true, "id, org_path");
        List<Long> orgIdList = orgsList.stream().map(OrgEntity::getId).collect(Collectors.toList());
        // 当前组织范围内的用户
        Set<Long> userIdSetByOrgIds = userOrgDBO.getUserIdSetByOrgIds(orgIdList);
        // 当前组织范围内的用户为空时，直接返回空集合
        if (CollectionUtils.isEmpty(userIdSetByOrgIds)) {
            return new HashSet<>();
        }
        try {
            // 1. 获取总记录数
            int totalCount = userDao.selectTotalCountByDynamicCondition(orList, baseFields, exFields);
            if (totalCount == 0) {
                return new HashSet<>();
            }
            // 对大量数据进行分块并行处理
            int pageSize = totalCount > 1000 ? 1000 : 5000;
            int totalPages = (totalCount + pageSize - 1) / pageSize;
            // 创建异步查询任务列表
            List<CompletableFuture<Set<Long>>> futures = IntStream.range(0, totalPages)
                    .mapToObj(pageIndex ->
                            CompletableFuture.supplyAsync(() -> {
                                        int offset = pageIndex * pageSize;
                                        List<Long> selectUserIdList = userDao.selectCountByDynamicCondition(
                                                orList, baseFields, exFields, pageSize, offset);
                                        // 取查询结果和当前组织范围内的用户进行交集，得到的就是当前组织范围内的符合查询条件的用户
                                        // 选择较小的集合作为调用方
                                        Set<Long> selectUserIdSet = new HashSet<>(selectUserIdList);
                                        selectUserIdSet.retainAll(userIdSetByOrgIds);
                                        return selectUserIdSet;
                                    }, commonExecutor)
                                    .exceptionally(ex -> {
                                        return new HashSet<>();  // 使用不可变空列表
                                    }))
                    .collect(Collectors.toList());

            // 获取查询的最终结果
            return futures.parallelStream()
                    .map(CompletableFuture::join)
                    .flatMap(Set::stream)
                    .collect(Collectors.toSet());

        } catch (Exception e) {
            logger.error("异步查询用户ID发生异常", e);
            // 如果异步处理失败，回退到同步处理
            throw new UserCenterException();
        }
    }

    @Override
    public Boolean updateUserOrder(UpdateUserOrderDto userOrderDto) {
        // 前一个用户和后一个用户，不能同时为空
        if (StringUtils.isBlank(userOrderDto.getPreviousUserId()) && StringUtils.isBlank(userOrderDto.getNextUserId())) {
            throw new UserCenterException(TransactionErrorType.USER_ORDER_INVALID);
        }

        long userId = Long.parseLong(userOrderDto.getUserId());
        long orgId = Long.parseLong(userOrderDto.getOrgId());

        // 获取前一个用户和后一个用户的排序值
        UserOrgEntity preResult = handleUserOrder(userOrderDto.getPreviousUserId(), orgId);
        UserOrgEntity nextResult = handleUserOrder(userOrderDto.getNextUserId(), orgId);
        adjustUserOrders(preResult, nextResult, userOrderDto);
        Long preOrder = preResult.getUserOrder();
        Long nextOrder = nextResult.getUserOrder();

        long newOrder = calculateNewOrder(preOrder, nextOrder);
        boolean needReorder = (newOrder == preOrder) || (newOrder == nextOrder) || (preOrder - nextOrder) <= 1;
        // 判断是否需要重排序
        if (needReorder) {
            // 重置排序
            userOrgDBO.rebalanceSortUserOrder(orgId);
            // 重新获取前一个用户和后一个用户的排序值
            Long preId = preResult.getId(), nextId = nextResult.getId();
            preResult = handleUserOrder(preId);
            nextResult = handleUserOrder(nextId);
            adjustUserOrders(preResult, nextResult, userOrderDto);
            preOrder = preResult.getUserOrder();
            nextOrder = nextResult.getUserOrder();

            newOrder = calculateNewOrder(preOrder, nextOrder);
        }

        userOrgDBO.updateUserOrder(userId, orgId, newOrder);
        return true;
    }

    /**
     * 处理用户排序
     *
     * @param userIdStr 用户ID
     * @param orgId     部门id
     * @return 关联关系表的id和用户排序值
     */
    private UserOrgEntity handleUserOrder(String userIdStr, long orgId) {
        if (StringUtils.isBlank(userIdStr)) {
            UserOrgEntity defaultUserOrg = new UserOrgEntity();
            defaultUserOrg.setId(0L);
            return defaultUserOrg;
        }

        long userId = Long.parseLong(userIdStr);
        UserOrgEntity userOrg = userOrgDBO.getUserOrgByUidAndOrgId(userId, orgId);
        if (userOrg == null) {
            throw new UserCenterException(HttpStatus.NOT_FOUND, TransactionErrorType.USER_NOT_FOUND);
        }
        return userOrg;
    }

    /**
     * 处理用户排序
     *
     * @param userOrgId 关联关系表的id
     * @return 关联关系表的id和用户排序值
     */
    private UserOrgEntity handleUserOrder(Long userOrgId) {
        if (userOrgId == 0L) {
            UserOrgEntity defaultUserOrg = new UserOrgEntity();
            defaultUserOrg.setId(0L);
            return defaultUserOrg;
        }

        UserOrgEntity userOrg = userOrgDBO.getById(userOrgId);
        if (userOrg == null) {
            throw new UserCenterException(HttpStatus.NOT_FOUND, TransactionErrorType.USER_NOT_FOUND);
        }
        return userOrg;
    }


    /**
     * 计算首尾页/中间页，将用户挪到第一或最后情形下的排序值
     *
     * @param preResult    前一个用户的信息
     * @param nextResult   后一个用户的信息
     * @param userOrderDto 排序参数
     */
    private void adjustUserOrders(UserOrgEntity preResult, UserOrgEntity nextResult, UpdateUserOrderDto userOrderDto) {
        Long preId = preResult.getId(), nextId = nextResult.getId();
        // 如果是首页
        if (userOrderDto.getFirstPage()) {
            if (preId == 0L) {
                long userOrder = Integer.MAX_VALUE;
                preResult.setUserOrder(userOrder);
            } else if (nextId == 0L) {
                nextResult.setUserOrder(preResult.getUserOrder() - ORDER_ADJUSTMENT);
            }
            // 尾页
        } else if (userOrderDto.getLastPage()) {
            if (preId == 0L) {
                preResult.setUserOrder(nextResult.getUserOrder() + ORDER_ADJUSTMENT);
            } else if (nextId == 0L) {
                nextResult.setUserOrder(Math.max(preResult.getUserOrder() - USER_ORDER_INCREMENT, MIN_USER_ORDER));
            }
            //中间页
        } else {
            if (preId == 0L) {
                preResult.setUserOrder(nextResult.getUserOrder() + ORDER_ADJUSTMENT);
            } else if (nextId == 0L) {
                nextResult.setUserOrder(preResult.getUserOrder() - ORDER_ADJUSTMENT);
            }

        }
    }

    /**
     * 计算新的排序值
     *
     * @param preOrder  前一个排序值
     * @param nextOrder 后一个排序值
     * @return 新的排序值
     */
    private long calculateNewOrder(long preOrder, long nextOrder) {
        return preOrder - ((preOrder - nextOrder) / 2);
    }

    @Override
    public Map<String, Map<Object, Object>> getUsersUniqueInfo() {
        return userDBO.getUsersUniqueAttrs();
    }

    /**
     * 将条件中的字段转为数据库表中的字段
     *
     * @param orList 条件集合
     */
    private void generateField(List<FilterGroup> orList) {
        for (FilterGroup filterConfigGroup : orList) {
            List<FilterItem> andList = filterConfigGroup.getAndList();
            for (FilterItem filterConfigItem : andList) {
                String query = filterConfigItem.getQuery();
                if (LocalUserAttr.sub.getDomainName().equals(query)) {
                    filterConfigItem.setQuery(LocalUserAttr.sub.getFieldName());
                } else if (LocalUserAttr.status.getDomainName().equals(query)) {
                    filterConfigItem.setValue(UserStatus.getValueByName(filterConfigItem.getValue().toString()));
                }
            }
        }
    }


    @Override
    public Boolean updatePasswordByPolicy(UserEntity userEntity, String newPassword) {

        String username = userEntity.getUsername();
        if (!username.equals("admin")) {
            //只要配了AD认证源就按AD修改密码
            List<ThirdIDPEntity> adIdpList = thirdIDPService.getByType(ThirdIdpTypeEnum.AD);
            for (ThirdIDPEntity idpEntity : adIdpList) {
                if (TypeMapper.asBoolean(idpEntity.getEnableSyncPwd())) {
                    ThirdIdpAuthConfig thirdIdpConfig = thirdIDPService.getThirdIdpConfig(idpEntity);
                    IThirdPartyAuthService thirdPartyAuthService = snsUserService.getAuthProvider(thirdIdpConfig.getIdpType().getName());

                    try {
                        thirdPartyAuthService.updatePassword(username, newPassword, thirdIdpConfig);
                    } catch (UserNotFoundException e) {
                        logger.warn("user not found: {}", username);
                    } catch (UserCenterException e) {
                        throw e;
                    } catch (Exception e) {
                        throw new UserCenterException(e);
                    }
                }
            }
        }

        updatePasswd(userEntity, newPassword);

        return true;
    }

    @Override
    public Boolean verifyPasswordByPolicy(UserEntity userEntity, String oldPassword) {
//        String username = userEntity.getUsername();
//        if (!username.equals("admin")) {
//            //只要配了AD认证源就按AD修改密码
//            List<ThirdIDPEntity> adIdpList = thirdIDPService.getByType(ThirdIdpTypeEnum.AD);
//            for (ThirdIDPEntity idpEntity : adIdpList) {
//                ThirdIdpAuthConfig thirdIdpConfig = thirdIDPService.getThirdIdpConfig(idpEntity);
//                IThirdPartyAuthService thirdPartyAuthService = snsUserService.getAuthProvider(thirdIdpConfig.getIdpType().getName());
//                Map<String, Object> idpUserObj = thirdPartyAuthService.getUserByPassword(username, oldPassword, thirdIdpConfig);
//
//                if (idpUserObj == null) {
//                    logger.error("user not found by third idp");
//                    throw new UserCenterException(TransactionErrorType.THIRDPARTY_USER_NOT_FOUND);
//                }
//                return true;
//            }
//        }
        return passwordEncoder.matches(oldPassword, userEntity.getPassword());
    }

    @Override
    public UserOtpEntity enableUserOtp(Long userId, String otpCode) {
        UserOtpEntity userOtpEntity = userOtpDBO.getById(userId);
        if (userOtpEntity == null) {
            userOtpEntity = new UserOtpEntity();
            userOtpEntity.setId(userId);
            userOtpEntity.setStatus(2);
//            userOtpEntity.setCreateTime(LocalDateTime.now());
//            userOtpEntity.setUpdateTime(LocalDateTime.now());
            userOtpEntity.setTotpSecret(TotpUtil.genUserSecret());
            userOtpDBO.save(userOtpEntity);
        } else {
            Integer status = userOtpEntity.getStatus();
            if (status == 0) {
                userOtpEntity.setStatus(2);
//                userOtpEntity.setUpdateTime(LocalDateTime.now());
                userOtpEntity.setTotpSecret(TotpUtil.genUserSecret());
                userOtpDBO.updateById(userOtpEntity);
            } else if (status == 2) {
                if (otpCode == null) {
                    return userOtpEntity;
                }

                String totpSecret = userOtpEntity.getTotpSecret();
                boolean valid = TotpUtil.validateTOTP(totpSecret, otpCode);
                if (!valid) {
                    throw new UserCenterException(TransactionErrorType.REQUEST_TOKEN_INVALID);
                }
                userOtpEntity.setStatus(1);
                userOtpEntity.setCreateTime(LocalDateTime.now());
                userOtpDBO.updateById(userOtpEntity);
            }
        }
        return userOtpEntity;
    }

    @Override
    public UserOtpEntity getUserOtp(Long userId) {
        UserOtpEntity userOtpEntity = userOtpDBO.getById(userId);
        return userOtpEntity;
    }

    @Override
    public Void disableUserOtp(Long userId) {
        UserOtpEntity userOtpEntity = userOtpDBO.getById(userId);
        if (userOtpEntity != null && userOtpEntity.getStatus() != 0) {
            userOtpEntity.setStatus(0);
//            userOtpEntity.setCreateTime(userOtpEntity.getUpdateTime());
            userOtpEntity.setUpdateTime(LocalDateTime.now());
            userOtpDBO.updateById(userOtpEntity);
        }
        return null;
    }

    @Override
    public Void disableUserOtp(String username) {
        UserEntity userEntity = userDBO.getOneByUsername(username, DaoConstants.id);
        if (userEntity != null) {
            return disableUserOtp(userEntity.getId());
        }
        return null;
    }

    @Override
    public Map<String, Object> toDomainMap(Map<String, Object> extFieldMap, String... filterDomainAttrs) {
        Map<String, Object> result = new HashMap<>();
        List<FieldDictEntity> fieldDictEntities = fieldDictDBO.listFieldDictsByType(TypeMapper.asInt(FieldType.USER));
        Map<String, String> fieldMap = fieldDictEntities.stream().filter(e -> e.getCreateMod().equals(CreatedMode.BY_ADMIN.getValue())).collect(Collectors.toMap(FieldDictEntity::getFieldName, FieldDictEntity::getDomainName));

        for (String extFieldName : fieldMap.keySet()) {
            final Object extValue = extFieldMap.get(extFieldName);
            final String extDomainName = fieldMap.get(extFieldName);
            if (filterDomainAttrs != null && filterDomainAttrs.length > 0) {
                result.put(extDomainName, extValue);
            }
        }

        if (filterDomainAttrs != null && filterDomainAttrs.length > 0) {
            Map<String, Object> filterResult = new HashMap();
            for (String filterDomainAttr : filterDomainAttrs) {
                if (result.containsKey(filterDomainAttr)) {
                    filterResult.put(filterDomainAttr, result.get(filterDomainAttr));
                }
            }
            return filterResult;
        }

        return result;
    }
}
