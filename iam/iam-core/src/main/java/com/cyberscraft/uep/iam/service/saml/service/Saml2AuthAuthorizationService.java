package com.cyberscraft.uep.iam.service.saml.service;

import com.cyberscraft.uep.account.client.constant.AccountConstant;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.common.util.MappingParser;
import com.cyberscraft.uep.common.util.ServerContext;
import com.cyberscraft.uep.iam.common.constants.DaoConstants;
import com.cyberscraft.uep.iam.common.domain.resource.ServerContextThreadLocal;
import com.cyberscraft.uep.iam.common.exception.UserCenterViewException;
import com.cyberscraft.uep.iam.dbo.AppDBO;
import com.cyberscraft.uep.iam.dto.enums.ClientStatusV2;
import com.cyberscraft.uep.iam.dto.request.AppDetailsVO;
import com.cyberscraft.uep.iam.dto.request.AssertionAttribute;
import com.cyberscraft.uep.iam.dto.request.sso.SamlConfig;
import com.cyberscraft.uep.iam.entity.AppEntity;
import com.cyberscraft.uep.iam.errors.TransactionErrorType;
import com.cyberscraft.uep.iam.service.IAppService;
import com.cyberscraft.uep.iam.service.as.transfer.AppTransferV2;
import com.cyberscraft.uep.iam.service.oidc.domain.UserInfo;
import com.cyberscraft.uep.iam.service.oidc.service.inf.DaoPersistenceService;
import com.cyberscraft.uep.iam.service.oidc.util.SecureRequestCheck;
import com.cyberscraft.uep.iam.service.saml.domain.SAMLRequestDomain;
import com.cyberscraft.uep.iam.service.saml.util.OpenSAMLUtils;
import com.cyberscraft.uep.iam.service.user.IUserService;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import net.shibboleth.utilities.java.support.component.ComponentInitializationException;
import org.apache.commons.lang3.StringUtils;
import org.apache.velocity.app.VelocityEngine;
import org.apache.velocity.runtime.RuntimeConstants;
import org.joda.time.DateTime;
import org.opensaml.core.xml.config.XMLObjectProviderRegistrySupport;
import org.opensaml.core.xml.io.MarshallingException;
import org.opensaml.messaging.context.MessageContext;
import org.opensaml.messaging.encoder.MessageEncodingException;
import org.opensaml.saml.common.SAMLObject;
import org.opensaml.saml.common.binding.security.impl.SAMLOutboundProtocolMessageSigningHandler;
import org.opensaml.saml.common.messaging.context.SAMLBindingContext;
import org.opensaml.saml.common.messaging.context.SAMLEndpointContext;
import org.opensaml.saml.common.messaging.context.SAMLPeerEntityContext;
import org.opensaml.saml.common.xml.SAMLConstants;
import org.opensaml.saml.saml2.binding.encoding.impl.BaseSAML2MessageEncoder;
import org.opensaml.saml.saml2.binding.encoding.impl.HTTPPostEncoder;
import org.opensaml.saml.saml2.binding.encoding.impl.HTTPRedirectDeflateEncoder;
import org.opensaml.saml.saml2.core.*;
import org.opensaml.saml.saml2.metadata.SingleSignOnService;
import org.opensaml.security.credential.BasicCredential;
import org.opensaml.security.credential.Credential;
import org.opensaml.xmlsec.SignatureSigningParameters;
import org.opensaml.xmlsec.context.SecurityParametersContext;
import org.opensaml.xmlsec.signature.Signature;
import org.opensaml.xmlsec.signature.support.SignatureConstants;
import org.opensaml.xmlsec.signature.support.SignatureException;
import org.opensaml.xmlsec.signature.support.Signer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.security.KeyPair;
import java.security.Principal;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static net.shibboleth.utilities.java.support.xml.XMLConstants.XSI_TYPE_ATTRIB_NAME;

/**
 * <p>
 * Saml2 service
 * </p>
 *
 * <AUTHOR>
 * @since 2022/2/8 8:19 下午
 */
@Service
public class Saml2AuthAuthorizationService {
    private Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private AppDBO appDBO;
    @Autowired
    private IAppService appService;
    @Autowired
    private IUserService userService;
    @Autowired
    private DaoPersistenceService daoPersistenceService;
    @Resource
    private AppTransferV2 appTransferV2;

    public AppDetailsVO encodeOutputStream(String clientId, HttpServletRequest request, HttpServletResponse response, SAMLRequestDomain requestDomain, Principal principal) {

        String tenantId = TenantHolder.getTenantCode();
        String spEntityId = requestDomain.getSpEntityID();

        String assertionConsumerServiceURL = requestDomain.getAssertionConsumerServiceURL();

        AppEntity appEntity = appDBO.getAppByClientId(clientId);
        if (appEntity == null || !appEntity.getStatus().equals(ClientStatusV2.ACTIVE.getValue())) {
            logger.warn("saml app is null or app's status is not actived : sp : {} ,tenant {}, is null:{}", spEntityId, tenantId, appEntity == null);
            String desc = TransactionErrorType.APP_NOT_FOUND_ERROR.getDesc(clientId);
            throw new UserCenterViewException(TransactionErrorType.APP_NOT_FOUND_ERROR, desc, TenantHolder.getTenantCode());
        }

        appService.validateAppTenant(appEntity);

        if (DaoConstants.SAML.equals(appEntity.getAuthProtocol())) {
            String strSamlConfig = appEntity.getConfig();
            SamlConfig samlConfig = JsonUtil.str2Obj(strSamlConfig, SamlConfig.class);
            if (!spEntityId.equals(samlConfig.getAudienceRestriction())) {
                logger.warn("saml sp entity id is invalid: {}", spEntityId);
                String desc = TransactionErrorType.APP_INVALID_ERROR.getDesc(spEntityId);
                throw new UserCenterViewException(TransactionErrorType.APP_INVALID_ERROR, desc, TenantHolder.getTenantCode());
            }

            String ssoUrl = samlConfig.getSsoUrl();
            if (StringUtils.isBlank(assertionConsumerServiceURL)) {
                assertionConsumerServiceURL = ssoUrl;
                requestDomain.setAssertionConsumerServiceURL(assertionConsumerServiceURL);
            }
            if (!assertionConsumerServiceURL.equals(ssoUrl)) {
                logger.warn("app {} assertionConsumerServiceURL is not matched registered {}", clientId, ssoUrl);
                String desc = TransactionErrorType.APP_SSO_URL_MISMATCH_ERROR.getDesc(clientId, ssoUrl);
                throw new UserCenterViewException(TransactionErrorType.APP_SSO_URL_MISMATCH_ERROR, desc, TenantHolder.getTenantCode());
            }

            try {
                Authentication authentication = (Authentication) principal;
                Object authUser = authentication.getPrincipal();
                UserInfo userInfo;
                if (authUser instanceof UserInfo) {
                    userInfo = (UserInfo) authUser;
                } else {
                    userInfo = daoPersistenceService.getUserInfoByLoginId(principal.getName());
                }
                if (userInfo != null) {
                    doResponse(request, response, requestDomain, samlConfig, appEntity, userInfo);
                } else {
                    logger.warn("user is invalid:{}", principal.getName());
                    throw new UserCenterViewException(TransactionErrorType.USER_NOT_FOUND, TenantHolder.getTenantCode());
                }
            } catch (UserCenterViewException e) {
                logger.error("doResponse userCenterViewException.", e);
                throw e;
            } catch (Exception e) {
                logger.error("doResponse error.", e);
                throw new UserCenterViewException(e.getMessage(), TenantHolder.getTenantCode());
            }

        } else {
            logger.warn("app {} is not saml protocol", clientId);
            String desc = TransactionErrorType.APP_NOT_SAML_PROTOCOL_ERROR.getDesc(clientId);
            throw new UserCenterViewException(TransactionErrorType.APP_NOT_SAML_PROTOCOL_ERROR, desc, TenantHolder.getTenantCode());
        }
        return appTransferV2.entity2DetailVo(appEntity);
    }

    private void doResponse(HttpServletRequest request, HttpServletResponse response, SAMLRequestDomain requestDomain, SamlConfig samlConfig, AppEntity appEntity, UserInfo userInfo) throws Exception {
        String relayState = requestDomain.getRelayState();

        DateTime issueInstant = new DateTime();
        String issuerValue = buildIDPIssuerValue(request);

        Issuer assertionIssuer = buildIDPIssuer(issuerValue);
        Issuer responseIssuer = buildIDPIssuer(issuerValue);
        Assertion assertion = buildAssertion(requestDomain.getRequestId(), issueInstant, assertionIssuer, samlConfig, userInfo);

        MessageContext context = new MessageContext();

        SignatureSigningParameters signatureSigningParameters = buildSignatureSigningParameters(appEntity, samlConfig);
        if (signatureSigningParameters != null) {
            context.getSubcontext(SecurityParametersContext.class, true).setSignatureSigningParameters(signatureSigningParameters);

            SAMLOutboundProtocolMessageSigningHandler handler = new SAMLOutboundProtocolMessageSigningHandler();
            handler.setSignErrorResponses(false);
            handler.initialize();

            //给assertion签名
            if (samlConfig.getAssertionSigned()) {
//                signAssertion(assertion, signatureSigningParameters);
                context.setMessage(assertion);
                handler.invoke(context);
                assertion = (Assertion) context.getMessage();
            }

            Response samlResponse = buildSamlResponse(requestDomain.getRequestId(), issueInstant, responseIssuer, assertion, samlConfig, false);
            context.setMessage(samlResponse);
            //给response签名
            if (samlConfig.getResponseSigned()) {
                handler.invoke(context);
            }
        } else {
            Response samlResponse = buildSamlResponse(requestDomain.getRequestId(), issueInstant, responseIssuer, assertion, samlConfig, false);
            context.setMessage(samlResponse);
        }

        if (StringUtils.isNotBlank(relayState)) {
            SAMLBindingContext bindingContext = context.getSubcontext(SAMLBindingContext.class, true);
            bindingContext.setRelayState(relayState);
        }

        SingleSignOnService endpoint = OpenSAMLUtils.buildSAMLObject(SingleSignOnService.class);
        endpoint.setBinding(SAMLConstants.SAML2_POST_BINDING_URI);
        endpoint.setLocation(requestDomain.getAssertionConsumerServiceURL());

        SAMLPeerEntityContext peerEntityContext = context.getSubcontext(SAMLPeerEntityContext.class, true);
        SAMLEndpointContext endpointContext = peerEntityContext.getSubcontext(SAMLEndpointContext.class, true);
        endpointContext.setEndpoint(endpoint);

        OpenSAMLUtils.logSAMLObject((SAMLObject) context.getMessage());

        BaseSAML2MessageEncoder encoder = null;
        if (SAMLConstants.SAML2_REDIRECT_BINDING_URI.equals(requestDomain.getProtocolBinding())) {
            encoder = new HTTPRedirectDeflateEncoder();
            encoder.setMessageContext(context);
            encoder.setHttpServletResponse(response);
        } else {
            encoder = new HTTPPostEncoder();
            VelocityEngine velocityEngine = new VelocityEngine();
            velocityEngine.setProperty(RuntimeConstants.RESOURCE_LOADERS, "classpath");
            velocityEngine.setProperty("classpath.resource.loader.class",
                    "org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader");
            velocityEngine.init();

            encoder.setMessageContext(context);
            encoder.setHttpServletResponse(response);
            ((HTTPPostEncoder) encoder).setVelocityEngine(velocityEngine);
        }

        try {
            encoder.initialize();
        } catch (ComponentInitializationException e) {
            throw new RuntimeException(e);
        }

        logger.info("Sending auto-submitting form to receiver with AuthnRequest");
        try {
            encoder.encode();
        } catch (MessageEncodingException e) {
            throw new RuntimeException(e);
        }
    }

    private void signAssertion(Assertion assertion, SignatureSigningParameters parameters) {
        Credential credential = parameters.getSigningCredential();
        Signature signature = OpenSAMLUtils.buildSAMLObject(Signature.class);
        signature.setSigningCredential(credential);
        signature.setSignatureAlgorithm(parameters.getSignatureAlgorithm());
        signature.setCanonicalizationAlgorithm(parameters.getSignatureCanonicalizationAlgorithm());

        assertion.setSignature(signature);

        try {
            XMLObjectProviderRegistrySupport.getMarshallerFactory().getMarshaller(assertion).marshall(assertion);
        } catch (MarshallingException e) {
            throw new RuntimeException(e);
        }

        try {
            Signer.signObject(signature);
        } catch (SignatureException e) {
            throw new RuntimeException(e);
        }
    }

    public Issuer buildIDPIssuer(String issuerValue) {
        Issuer issuer = OpenSAMLUtils.buildSAMLObject(Issuer.class);
        issuer.setValue(issuerValue);
        return issuer;
    }

    private Response buildSamlResponse(String reqId, DateTime issueInstant, Issuer issuer, Assertion assertion, SamlConfig samlConfig, Boolean idpInitiated) {
        String resId = OpenSAMLUtils.generateSecureRandomId();
        Response response = OpenSAMLUtils.buildSAMLObject(Response.class);
        response.setID(resId);
        response.setIssueInstant(issueInstant);
        response.setDestination(samlConfig.getDestinationUrl());
        if (!idpInitiated) {
            response.setInResponseTo(reqId);
        }

        response.setIssuer(issuer);

        //----------start status-------------
        Status status = OpenSAMLUtils.buildSAMLObject(Status.class);
        StatusCode statusCode = OpenSAMLUtils.buildSAMLObject(StatusCode.class);
        statusCode.setValue(StatusCode.SUCCESS);
        status.setStatusCode(statusCode);
        response.setStatus(status);
        //----------end status-------------

        //----------start assertion-------------
        response.getAssertions().add(assertion);
        //----------end assertion-------------

        return response;
    }

    private Assertion buildAssertion(String requestId, DateTime issueInstant, Issuer issuer, SamlConfig samlConfig, UserInfo userInfo) {
        String resId = OpenSAMLUtils.generateSecureRandomId();
        Assertion assertion = OpenSAMLUtils.buildSAMLObject(Assertion.class);
        assertion.setID(resId);
        assertion.setIssueInstant(issueInstant);
        assertion.setIssuer(issuer);

        Subject subject = OpenSAMLUtils.buildSAMLObject(Subject.class);

        NameID nameID = OpenSAMLUtils.buildSAMLObject(NameID.class);
        if ("none".equals(samlConfig.getNameIdFormat())) {
            nameID.setFormat(NameID.UNSPECIFIED);
        } else if ("email_address".equals(samlConfig.getNameIdFormat())) {
            nameID.setFormat(NameID.EMAIL);
//        } else if ("cert_zhuti_name".equals(samlConfig.getNameIdFormat())) {
//            nameID.setFormat(NameID.X509_SUBJECT);
        } else {
            nameID.setFormat(NameID.UNSPECIFIED);
        }

        Map<String, Object> mapUserInfo = userService.getMapUserByUsername(userInfo.getUsername());
        nameID.setValue(mapUserInfo.get(samlConfig.getAutofillSetting()).toString());

        SubjectConfirmation subjectConfirmation = OpenSAMLUtils.buildSAMLObject(SubjectConfirmation.class);
        subjectConfirmation.setMethod(SubjectConfirmation.METHOD_BEARER);
        SubjectConfirmationData subjectConfirmationData = OpenSAMLUtils.buildSAMLObject(SubjectConfirmationData.class);
        if (requestId != null) {
            subjectConfirmationData.setInResponseTo(requestId);
        }
        //有效期5分钟
        subjectConfirmationData.setNotOnOrAfter(issueInstant.plusMinutes(5));
        subjectConfirmationData.setRecipient(samlConfig.getRecipientUrl());
        subjectConfirmation.setSubjectConfirmationData(subjectConfirmationData);

        Conditions conditions = OpenSAMLUtils.buildSAMLObject(Conditions.class);
        conditions.setNotBefore(issueInstant.minusMinutes(5));
        conditions.setNotOnOrAfter(issueInstant.plusMinutes(5));

        AudienceRestriction audienceRestriction = OpenSAMLUtils.buildSAMLObject(AudienceRestriction.class);
        Audience audience = OpenSAMLUtils.buildSAMLObject(Audience.class);
        audience.setAudienceURI(samlConfig.getAudienceRestriction());
        audienceRestriction.getAudiences().add(audience);
        conditions.getAudienceRestrictions().add(audienceRestriction);

        AuthnStatement authnStatement = OpenSAMLUtils.buildSAMLObject(AuthnStatement.class);
        authnStatement.setAuthnInstant(issueInstant);
        authnStatement.setSessionNotOnOrAfter(issueInstant.plusMinutes(5));
        authnStatement.setSessionIndex(resId);

        AuthnContext authnContext = OpenSAMLUtils.buildSAMLObject(AuthnContext.class);
        AuthnContextClassRef authnContextClassRef = OpenSAMLUtils.buildSAMLObject(AuthnContextClassRef.class);
        authnContextClassRef.setAuthnContextClassRef(AuthnContext.UNSPECIFIED_AUTHN_CTX);
        authnContext.setAuthnContextClassRef(authnContextClassRef);
        authnStatement.setAuthnContext(authnContext);

        subject.setNameID(nameID);
        subject.getSubjectConfirmations().add(subjectConfirmation);
        assertion.setSubject(subject);
        assertion.setConditions(conditions);
        assertion.getAuthnStatements().add(authnStatement);

        List<AssertionAttribute> assertionAttributes = samlConfig.getAssertionAttributes();
        if (assertionAttributes != null && assertionAttributes.size() > 0) {
            AttributeStatement attributeStatement = OpenSAMLUtils.buildSAMLObject(AttributeStatement.class);
            for (AssertionAttribute assertionAttribute : assertionAttributes) {

                Attribute attribute = OpenSAMLUtils.buildSAMLObject(Attribute.class);
                attribute.setName(assertionAttribute.getAttributeName());
                String attributeFormat = assertionAttribute.getAttributeFormat();
                if ("none".equals(attributeFormat)) {
                    attribute.setNameFormat(Attribute.UNSPECIFIED);
//                } else if ("ref_url".equals(attributeFormat)) {
//                    attribute.setNameFormat(Attribute.URI_REFERENCE);
                } else if ("base_info".equals(attributeFormat)) {
                    attribute.setNameFormat(Attribute.BASIC);
                } else {
                    attribute.setNameFormat(Attribute.UNSPECIFIED);
                }

                AttributeValue attributeValue = OpenSAMLUtils.buildSAMLObject(AttributeValue.class);
                String fixValue = assertionAttribute.getFixValue();
                if (StringUtils.isNotBlank(fixValue)) {
                    HashMap<String, Object> params = new HashMap<>();
                    params.put(AccountConstant.USER_PROFILE_ROOTNAME, mapUserInfo);
                    fixValue = (String) MappingParser.jsParse(fixValue, params);
                    attributeValue.setTextContent(fixValue);
                } else {
                    String attrValue = assertionAttribute.getAttributeValue();
                    Object objValue = mapUserInfo.get(attrValue);
                    attributeValue.setTextContent(objValue == null ? "" : String.valueOf(objValue));
                }
                attributeValue.getUnknownAttributes().put(XSI_TYPE_ATTRIB_NAME, "xs:string");

                attribute.getAttributeValues().add(attributeValue);
                attributeStatement.getAttributes().add(attribute);
            }
            assertion.getAttributeStatements().add(attributeStatement);
        }

        return assertion;
    }

    private String buildIDPIssuerValue(HttpServletRequest request) {
        ServerContext serverContext = ServerContext.of(request);
        ServerContextThreadLocal.setContextInfo(serverContext);
        return serverContext.getURL();
    }

    private SignatureSigningParameters buildSignatureSigningParameters(AppEntity appEntity, SamlConfig samlConfig) {
        if (samlConfig.getAssertionSigned() || samlConfig.getResponseSigned()) {
            KeyPair keyPair = appDBO.getKeyPair(appEntity.getClientId());
            PrivateKey aPrivate = keyPair.getPrivate();
            PublicKey aPublic = keyPair.getPublic();

            BasicCredential basicCredential = new BasicCredential(aPublic, aPrivate);

            SignatureSigningParameters signingParameters = new SignatureSigningParameters();
            signingParameters.setSigningCredential(basicCredential);
            signingParameters.setSignatureCanonicalizationAlgorithm(SignatureConstants.ALGO_ID_C14N_EXCL_OMIT_COMMENTS);

            String signatureAlgorithm = samlConfig.getSignatureAlgorithm();
            if (signatureAlgorithm != null && signatureAlgorithm.equalsIgnoreCase("rsa-sha1")) {
                signingParameters.setSignatureAlgorithm(SignatureConstants.ALGO_ID_SIGNATURE_RSA_SHA1);
            } else {
                signingParameters.setSignatureAlgorithm(SignatureConstants.ALGO_ID_SIGNATURE_RSA_SHA256);
            }
            String digestAlgorithm = samlConfig.getDigestAlgorithm();
            if (digestAlgorithm != null && digestAlgorithm.equalsIgnoreCase("sha1")) {
                signingParameters.setSignatureReferenceDigestMethod(SignatureConstants.ALGO_ID_DIGEST_SHA1);
            } else {
                signingParameters.setSignatureReferenceDigestMethod(SignatureConstants.ALGO_ID_DIGEST_SHA256);
            }
            return signingParameters;
        }
        return null;
    }

    /**
     * IDP-Initiated POST SSO
     *
     * @param request  request
     * @param response response
     * @param clientId clientId
     */
    public void idpInitiatedSso(HttpServletRequest request, HttpServletResponse response, String clientId) {
        String tenantId = TenantHolder.getTenantCode();
        AppEntity appEntity = appDBO.getAppByClientId(clientId);
        if (appEntity == null || !appEntity.getStatus().equals(ClientStatusV2.ACTIVE.getValue())) {
            logger.warn(" app is null or app's status is not actived , tenant {}, is null:{}", tenantId, appEntity == null);
            String desc = TransactionErrorType.APP_NOT_FOUND_ERROR.getDesc(clientId);
            throw new UserCenterViewException(TransactionErrorType.APP_NOT_FOUND_ERROR, desc, tenantId);
        }

        appService.validateAppTenant(appEntity);
        String strSamlConfig = appEntity.getConfig();
        SamlConfig samlConfig = JsonUtil.str2Obj(strSamlConfig, SamlConfig.class);
        try {
            Authentication authentication = SecureRequestCheck.getAuthentication();
            Principal principal = SecureRequestCheck.getAuthentication();
            Object authUser = authentication.getPrincipal();
            UserInfo userInfo;
            if (authUser instanceof UserInfo) {
                userInfo = (UserInfo) authUser;
            } else {
                userInfo = daoPersistenceService.getUserInfoByLoginId(principal.getName());
            }
            if (userInfo != null) {
                DateTime issueInstant = new DateTime();
                String issuerValue = buildIDPIssuerValue(request);
                Issuer assertionIssuer = buildIDPIssuer(issuerValue);
                Issuer responseIssuer = buildIDPIssuer(issuerValue);

                // 创建SAML断言
                Assertion assertion = buildAssertion(null, issueInstant, assertionIssuer, samlConfig, userInfo);

                // 创建SAML响应
                String resId = OpenSAMLUtils.generateSecureRandomId();
                Response samlResponse = buildSamlResponse(resId, issueInstant, responseIssuer, assertion, samlConfig, true);

                // 对SAML响应进行签名
                signResponse(request, response, appEntity, samlConfig, assertion, samlResponse);
            } else {
                logger.warn("user is invalid:{}", principal.getName());
                throw new UserCenterViewException(TransactionErrorType.USER_NOT_FOUND, tenantId);
            }
        } catch (UserCenterViewException e) {
            logger.error("doResponse userCenterViewException.", e);
            throw e;
        } catch (Exception e) {
            logger.error("doResponse error.", e);
            throw new UserCenterViewException(e.getMessage(), tenantId);
        }
    }


    private void signResponse(HttpServletRequest request, HttpServletResponse response, AppEntity appEntity, SamlConfig samlConfig, Assertion assertion, Response samlResponse) throws Exception {
        MessageContext context = new MessageContext();

        SignatureSigningParameters signatureSigningParameters = buildSignatureSigningParameters(appEntity, samlConfig);
        if (signatureSigningParameters != null) {
            context.getSubcontext(SecurityParametersContext.class, true).setSignatureSigningParameters(signatureSigningParameters);

            SAMLOutboundProtocolMessageSigningHandler handler = new SAMLOutboundProtocolMessageSigningHandler();
            handler.setSignErrorResponses(false);
            handler.initialize();

            //给assertion签名
            if (samlConfig.getAssertionSigned()) {
                context.setMessage(assertion);
                handler.invoke(context);
                assertion = (Assertion) context.getMessage();
                samlResponse.getAssertions().add(assertion);
            }

            context.setMessage(samlResponse);
            //给response签名
            if (samlConfig.getResponseSigned()) {
                handler.invoke(context);
            }
        } else {
            context.setMessage(samlResponse);
        }

        SingleSignOnService endpoint = OpenSAMLUtils.buildSAMLObject(SingleSignOnService.class);
        endpoint.setBinding(SAMLConstants.SAML2_POST_BINDING_URI);
        endpoint.setLocation(samlConfig.getSsoUrl());

        SAMLPeerEntityContext peerEntityContext = context.getSubcontext(SAMLPeerEntityContext.class, true);
        SAMLEndpointContext endpointContext = peerEntityContext.getSubcontext(SAMLEndpointContext.class, true);
        endpointContext.setEndpoint(endpoint);

        OpenSAMLUtils.logSAMLObject((SAMLObject) context.getMessage());

        BaseSAML2MessageEncoder encoder = new HTTPPostEncoder();
        VelocityEngine velocityEngine = new VelocityEngine();
        velocityEngine.setProperty(RuntimeConstants.RESOURCE_LOADERS, "classpath");
        velocityEngine.setProperty("classpath.resource.loader.class",
                "org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader");
        velocityEngine.init();

        encoder.setMessageContext(context);
        encoder.setHttpServletResponse(response);
        ((HTTPPostEncoder) encoder).setVelocityEngine(velocityEngine);

        try {
            encoder.initialize();
        } catch (ComponentInitializationException e) {
            throw new RuntimeException(e);
        }

        logger.info("Sending auto-submitting form to receiver with AuthRequest");
        try {
            encoder.encode();
        } catch (MessageEncodingException e) {
            throw new RuntimeException(e);
        }
    }
}
