package com.cyberscraft.uep.iam.service.oidc.provider.token.serialization;

import org.nustaq.serialization.FSTConfiguration;
import org.springframework.security.oauth2.provider.token.store.redis.StandardStringSerializationStrategy;

public class FstSerializationStrategy extends StandardStringSerializationStrategy {

    private final FSTConfiguration config;

    public FstSerializationStrategy(){
        config = FSTConfiguration.createDefaultConfiguration();
    }

    @Override
    @SuppressWarnings("unchecked")
    protected <T> T deserializeInternal(byte[] bytes, Class<T> clazz) {
        return (T) config.asObject(bytes);
    }

    @Override
    protected byte[] serializeInternal(Object object) {
        if (object == null) {
            return null;
        }
        return config.asByteArray(object);
    }
}