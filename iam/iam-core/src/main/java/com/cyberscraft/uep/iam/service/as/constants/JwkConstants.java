package com.cyberscraft.uep.iam.service.as.constants;

public interface JwkConstants {
    /**
     * The &quot;kid&quot; (key ID) parameter used in a JWT header and in a JWK.
     */
    static final String KEY_ID = "kid";

    /**
     * The &quot;kty&quot; (key type) parameter identifies the cryptographic algorithm family
     * used by a JWK, for example, &quot;RSA&quot; or &quot;EC&quot;.
     */
    static final String KEY_TYPE = "kty";

    /**
     * The &quot;alg&quot; (algorithm) parameter used in a JWT header and in a JWK.
     */
    static final String ALGORITHM = "alg";

    /**
     * The &quot;use&quot; (public key use) parameter identifies the intended use of the public key.
     * For example, whether a public key is used for encrypting data or verifying the signature on data.
     */
    static final String PUBLIC_KEY_USE = "use";

    /**
     * The &quot;n&quot; (modulus) parameter contains the modulus value for a RSA public key.
     */
    static final String RSA_PUBLIC_KEY_MODULUS = "n";

    /**
     * The &quot;e&quot; (exponent) parameter contains the exponent value for a RSA public key.
     */
    static final String RSA_PUBLIC_KEY_EXPONENT = "e";

    /**
     * A JWK Set is a JSON object that has a &quot;keys&quot; member
     * and its value is an array (set) of JWKs.
     */
    static final String KEYS = "keys";
}
