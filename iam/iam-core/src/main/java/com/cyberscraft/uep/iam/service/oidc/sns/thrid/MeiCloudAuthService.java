package com.cyberscraft.uep.iam.service.oidc.sns.thrid;

import com.cyberscraft.uep.account.client.domain.account.MeiCloudAccount;
import com.cyberscraft.uep.common.util.CookieUtil;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.common.util.RestAPIUtil;
import com.cyberscraft.uep.common.util.WebUrlUtil;
import com.cyberscraft.uep.iam.dto.domain.ThirdIdpAuthConfig;
import com.cyberscraft.uep.iam.dto.enums.ThirdIdpTypeEnum;
import com.cyberscraft.uep.iam.service.oidc.sns.IThirdPartyAuthService;
import com.cyberscraft.uep.proxy.meta.service.IProxyMetaService;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * @description: 美云认证服务
 * @author:行家玮
 * @date:2024/1/16
 */
@Service
public class MeiCloudAuthService implements IThirdPartyAuthService {

    private static final Logger logger = LoggerFactory.getLogger(MeiCloudAuthService.class);

    @Autowired
    IProxyMetaService proxyMetaService;

    @Override
    public Map<String, Object> getUserByOAuth(String code, ThirdIdpAuthConfig authConfig) {
        MeiCloudAccount meiCloudAccount = (MeiCloudAccount) authConfig.getConfig();
        // 从cookies中获取身份令牌
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        code = CookieUtil.getCookieValue(request, "token");
        logger.info("使用第三方[{}]登录请求，token:{}", authConfig.getIdpType(), code);
        if (StringUtils.isBlank(code)) {
            throw new RuntimeException("invalid token");
        }

        String urlFormat = "%s?accessToken=%s";
        String url = String.format(urlFormat, meiCloudAccount.getHost(), code);
        String virtualIp = proxyMetaService.getVirtualIp(TenantHolder.getTenantCode(), String.valueOf(authConfig.getProxyId()), WebUrlUtil.getHost(meiCloudAccount.getHost()));
        Map userMap;
        try {
            Map result = RestAPIUtil.getForEntity(virtualIp, url, null);
            if (result == null) {
                throw new RuntimeException("请求 meicloud 获取用户信息出错，响应信息为空");
            }
            String errcode = result.get("code").toString();
            if (!"0".equals(errcode)) {
                throw new RuntimeException("请求 meicloud 获取用户信息失败，响应信息为" + JsonUtil.obj2Str(result));
            }
            Map<String, Object> data = (Map<String, Object>) result.get("data");
            userMap = (Map) data.get("userInfo");
        } catch (Exception e) {
            logger.info("使用第三方[{}]登录请求,异常信息:{}", authConfig.getIdpType(), e.getMessage(), e);
            throw new RuntimeException(authConfig.getIdpType() + " 登录失败");
        }
        logger.info("使用第三方[{}]登录请求，登录成功，用户信息是:{}", authConfig.getIdpType(), JsonUtil.obj2Str(userMap));
        return userMap;
    }


    @Override
    public boolean supports(String type) {
        return ThirdIdpTypeEnum.MEICLOUD.getName().equals(type);
    }
}
