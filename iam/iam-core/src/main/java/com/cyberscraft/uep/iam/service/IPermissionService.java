package com.cyberscraft.uep.iam.service;

import com.cyberscraft.uep.common.dto.QueryPage;
import com.cyberscraft.uep.iam.entity.AppPermissionBindEntity;
import com.cyberscraft.uep.iam.entity.AppRolePermissionEntity;
import com.cyberscraft.uep.iam.dto.request.*;

import java.util.List;
import java.util.Set;

/**
 * <p>
 *     应用权限、权限组业务服务
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2019-07-19 09:58
 */
public interface IPermissionService {
    /**
     * 创建权限
     * @param clientId
     * @param permissionCreateInVO
     * @return
     */
    Void createPermission(String clientId, PermissionCreateInVO permissionCreateInVO);

    /**
     * 更新权限
     * @param clientId
     * @param permissionName
     * @param permissionUpdateInVO
     * @return
     */
    Void updatePermission(String clientId, String permissionName, PermissionUpdateInVO permissionUpdateInVO);


    /**
     * 删除权限
     * @param clientId
     * @param permissionName
     * @return
     */
    Void deletePermission(String clientId, String permissionName);

    /**
     * 获取一个权限详情
     * @param clientId
     * @param permissionName
     * @return
     */
    PermissionVO getPermission(String clientId, String permissionName);

    /**
     * 查找用户对应应用中的所有权限组集合
     * @param username
     * @param clientId
     * @return
     */
    Set<String> findUserClientPermissionSet(String username, String clientId);

    /**
     * 查找用户对应应用中的所有权限集合
     * @param username
     * @param clientId
     * @return
     */
    Set<String> findUserClientPermissions(String username, String clientId);

    /**
     * 查找用户对应应用的所有tags(菜单)集合
     * @param username
     * @param clientId
     * @return
     */
    Set<String> findUserClientPermissionSetsTags(String username, String clientId);

    /**
     * 查询某个应用的权限列表
     * @param clientId
     * @param filterStr
     * @param page
     * @param size
     * @return
     */
    QueryPage<PermissionVO> searchPermissions(String clientId, String filterStr, Integer page, Integer size);

    /**
     *  创建权限组
     * @param clientId
     * @param permissionSetCreateInVO
     * @return
     */
    Void createPermissionSet(String clientId,PermissionSetCreateInVO permissionSetCreateInVO);

    /**
     * 更新权限组
     * @param clientId
     * @param permissionSetName
     * @param permissionSetUpdateInVO
     * @return
     */
    Void updatePermissionSet(String clientId, String permissionSetName,PermissionSetUpdateInVO permissionSetUpdateInVO);

    /**
     * 删除权限组
     * @param clientId
     * @param permissionSetName
     * @return
     */
    Void deletePermissionSet(String clientId, String permissionSetName);

    /**
     * 查询一个权限组详情
     * @param clientId
     * @param permissionSetName
     * @return
     */
    PermissionSetVO getPermissionSet(String clientId, String permissionSetName);

    /**
     * 分页查询权限组
     * @param clientId
     * @param filterStr
     * @param page
     * @param size
     * @return
     */
    QueryPage<PermissionSetVO> searchPermissionSets(String clientId, String filterStr, Integer page, Integer size);

    /**
     * 权限校验，如果通过，则返回角色与权限的实体
     * @param appId
     * @param roleId
     * @param permissions
     * @return
     */
    List<AppRolePermissionEntity> validatePermissionsByRole(Long appId, Long roleId, Set<String> permissions);

    /**
     * 权限组校验，如果通过，则返回角色与权限组的实体
     * @param appId
     * @param roleId
     * @param permissionSets
     * @return
     */
    List<AppRolePermissionEntity> validatePermissionSetsByRole(Long appId, Long roleId, Set<String> permissionSets);

    /**
     * 应用权限校验，如果通过，则返回应用与权限的实体
     * @param appId 应用id
     * @return 应用与权限的实体
     */
    List<AppPermissionBindEntity> validatePermissionByApp(Long appId, Set<String> permissions);

    /**
     * 应用权限组校验，如果通过，则返回应用与权限组的实体
     * @param appId 应用id
     * @return 应用与权限组的实体
     */
    List<AppPermissionBindEntity> validatePermissionSetsByApp(Long appId, Set<String> permissionSets);

}
