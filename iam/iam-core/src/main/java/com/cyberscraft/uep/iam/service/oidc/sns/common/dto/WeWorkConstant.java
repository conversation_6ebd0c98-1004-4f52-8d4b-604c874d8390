package com.cyberscraft.uep.iam.service.oidc.sns.common.dto;

/**
 * <p>
 *     企业微信相关配置
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2020-03-12 10:11
 */
public class WeWorkConstant {
    /**
     * 获取access_token的api
     */
    public static final String API_ACCESS_TOKEN = "/cgi-bin/gettoken?corpid=%s&corpsecret=%s";
    /**
     * 根据code获取用户信息的api
     */
    public static final String API_GET_USER_INFO = "/cgi-bin/user/getuserinfo?access_token=%s&code=%s";

    /**
     * oauth2授权地址
     */
    public static final String AUTHORIZATION_ENDPOINT = "https://open.weixin.qq.com/connect/oauth2/authorize";

    /**
     * 获取访问用户身份
     */
    public static final String USER_BASEINFO_ENDPOINT = "/cgi-bin/auth/getuserinfo";

    /**
     * 获取访问用户敏感信息
     */
    public static final String USER_PRIVATEINFO_ENDPOINT = "/cgi-bin/auth/getuserdetail";

    /**
     * 读取成员接口
     */
    public static final String USER_GET_ENDPOINT = "/cgi-bin/user/get";

    /**
     * 调用企业微信获取用户信息接口，当授权码不正确时返回的错误码
     */
    public static final String ERROR_CODE_INVALID_CODE = "40029";

    /**
     * 企业微信accessToken缓存的key，占位符1为租户ID，占用符2为corpid
     */
    public static final String WEWORK_TOKEN_REDIS_KEY = "WEWORK_ACCESS_TOKEN:%s:%s";

    /**
     * 请求token时，锁的前缀，占位符为accessToken缓存的key
     */
    public static final String WEWORK_TOKEN_LOCK_KEY = "lock:%s";
    /**
     * 请求token时，锁的等待时间，单位：毫秒
     */
    public static final Long WEWORK_TOKEN_LOCK_WAIT_TIME = 5000L;
    /**
     * 请求token时，锁的超时时间,单位：毫秒
     */
    public static final Long WEWORK_TOKEN_LOCK_TIME = 10000L;



    /***************************** 调用企业微信接口，返回的字段名称 ****************************/
    public static final String API_RESULT_USER_ID = "UserId";
    public static final String API_RESULT_ERRCODE = "errcode";
    public static final String API_RESULT_ERRMSG = "errmsg";
    public static final String API_RESULT_ACCESS_TOKEN = "access_token";
    public static final String API_RESULT_ACCESS_EXPIRES_IN = "expires_in";

}
