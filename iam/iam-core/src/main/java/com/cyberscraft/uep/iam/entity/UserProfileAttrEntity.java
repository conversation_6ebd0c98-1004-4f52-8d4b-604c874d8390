package com.cyberscraft.uep.iam.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

/**
 * <p>
 * IAM-用户画像属性表
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2019-07-04
 */
@TableName("iam_user_profile_attr")
public class UserProfileAttrEntity implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 画像ID
     */
    private Long profileRefId;

    /**
     * 该属性是否可以用在IdToken的claim中
     */
    private Integer asClaim;

    /**
     * 属性Domain名称
     */
    private String domainName;

    /**
     * 可读的参与者，可取值：system, admin, app, self, everyone ,
     */
    private String readers;

    /**
     * 属性排序号
     */
    private Integer sortOrder;

    /**
     * 可写的参与者，可取值：1：system, 2：admin, 3：app,4： self
     */
    private String writers;

    /**
     * 对应idtoken的claim name
     */
    private String claimName;

    /**
     * 如果fixValue不为空，则覆盖domainName对应的value
     */
    private String fixValue;

    /**
     * 租户ID
     */
    private String tenantId;

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getProfileRefId() {
        return profileRefId;
    }

    public void setProfileRefId(Long profileRefId) {
        this.profileRefId = profileRefId;
    }

    public Integer getAsClaim() {
        return asClaim;
    }

    public void setAsClaim(Integer asClaim) {
        this.asClaim = asClaim;
    }

    public String getDomainName() {
        return domainName;
    }

    public void setDomainName(String domainName) {
        this.domainName = domainName;
    }

    public String getReaders() {
        return readers;
    }

    public void setReaders(String readers) {
        this.readers = readers;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public String getWriters() {
        return writers;
    }

    public void setWriters(String writers) {
        this.writers = writers;
    }

    public String getClaimName() {
        return claimName;
    }

    public void setClaimName(String claimName) {
        this.claimName = claimName;
    }

    public String getFixValue() {
        return fixValue;
    }

    public void setFixValue(String fixValue) {
        this.fixValue = fixValue;
    }

    @Override
    public String toString() {
        return "UserProfileAttrEntity{" +
                "id=" + id +
                ", profileRefId=" + profileRefId +
                ", asClaim=" + asClaim +
                ", domainName='" + domainName + '\'' +
                ", readers='" + readers + '\'' +
                ", sortOrder=" + sortOrder +
                ", writers='" + writers + '\'' +
                ", claimName='" + claimName + '\'' +
                ", fixValue='" + fixValue + '\'' +
                ", tenantId='" + tenantId + '\'' +
                '}';
    }
}
