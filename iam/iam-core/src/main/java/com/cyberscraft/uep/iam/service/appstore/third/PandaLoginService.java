package com.cyberscraft.uep.iam.service.appstore.third;

import com.cyberscraft.uep.common.bean.RestApiResponse;
import com.cyberscraft.uep.common.util.ByteUtil;
import com.cyberscraft.uep.common.util.HMACSignUtil;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.common.util.RestAPIUtil;
import com.cyberscraft.uep.iam.common.exception.UserCenterException;
import com.cyberscraft.uep.iam.dto.enums.AppStoreAppClientId;
import com.cyberscraft.uep.iam.dto.request.sso.PandaConfig;
import com.cyberscraft.uep.iam.entity.AppEntity;
import com.cyberscraft.uep.iam.service.appstore.IAppStoreLoginService;
import com.cyberscraft.uep.iam.service.appstore.util.SSOAccountUtil;
import com.cyberscraft.uep.iam.service.oidc.domain.UserInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * @Description 熊猫优福单点登录
 * <AUTHOR>
 * @Date 2025/2/5 10:20
 */
@Service
public class PandaLoginService implements IAppStoreLoginService {

    protected Logger logger = LoggerFactory.getLogger(getClass());

    @Override
    public String constructSsoUrl(Authentication authentication, AppEntity appEntity) {
        String ssoUrl = null;
        try {

            PandaConfig pandaConfig = JsonUtil.str2Obj(appEntity.getConfig(), PandaConfig.class);

            String userCodeMappingAttr = pandaConfig.getUserCodeMappingAttr();
            UserInfo userDetails = (UserInfo) authentication.getPrincipal();
            String userCode = SSOAccountUtil.basicGetUserCode(userDetails, userCodeMappingAttr);
            logger.info("userCode:{}", userCode);

            String queryFormat = "ding_user_id=%s&timestamp=%s";
            long timestamp = System.currentTimeMillis() / 1000;

            String queryString = String.format(queryFormat, userCode, timestamp);

            byte[] bytes = HMACSignUtil.hMacSign("HmacSHA256", queryString.getBytes(StandardCharsets.UTF_8), pandaConfig.getSecret().getBytes(StandardCharsets.UTF_8));
            String sign = ByteUtil.byte2Hex(bytes);

            String ssoFormat = "%s&ding_user_id=%s&sign=%s&timestamp=%s";
            ssoUrl = String.format(ssoFormat, pandaConfig.getSsoUrl(), userCode, sign, timestamp);

        } catch (Exception e) {
            logger.error("单点登录失败！", e);
            throw new UserCenterException("单点登录失败！");
        }
        return ssoUrl;
    }

    @Override
    public boolean supports(AppStoreAppClientId clientId) {
        return AppStoreAppClientId.PANDA == clientId;
    }

    public static void main(String[] args) throws Exception {
        String secret = "60d3a8621cbb990813db241dc5796260e73bd63c14e15007b7ff2920daa741d8";
        String phone = "huaiquan.xia";

        String queryFormat = "ding_user_id=%s&timestamp=%s";

        long timestamp = System.currentTimeMillis() / 1000;

        String queryString = String.format(queryFormat, phone, timestamp);

//        String sign = HMACSignUtil.hMacSignBase64("HmacSHA256", "ding_user_id=13800138000&timestamp=1698765432", secret);
        byte[] bytes = HMACSignUtil.hMacSign("HmacSHA256", queryString.getBytes(StandardCharsets.UTF_8), secret.getBytes(StandardCharsets.UTF_8));
        String sign = ByteUtil.byte2Hex(bytes);
//        String sign = new String(bytes, StandardCharsets.UTF_8);

        String url = "http://test.pandayoufu.com/thirdlogin?type=titanium&ding_user_id=%s&sign=%s&timestamp=%s";
        System.out.println(String.format(url, phone, sign, timestamp));
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("ding_user_id", "huaiquan.xia");
        body.add("sign", sign);
        body.add("timestamp", timestamp);

        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "multipart/form-data");
        RestApiResponse restApiResponse = RestAPIUtil.modifyEntityForString("http://test-api.pandayoufu.com/v1/titanium/login", "POST", body, headers, null, null);
        if (restApiResponse.getHttpStatus() == 200) {
            Map<String, Object> responseBody = JsonUtil.str2Map((String) restApiResponse.getBody());
            Map<String, Object> data = (Map<String, Object>) responseBody.get("data");
            String sss = data.get("redirect_url").toString();
            System.out.println(1123);
        }
    }
}