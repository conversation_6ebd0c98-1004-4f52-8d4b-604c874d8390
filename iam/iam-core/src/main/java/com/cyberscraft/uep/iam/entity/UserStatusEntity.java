package com.cyberscraft.uep.iam.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cyberscraft.uep.iam.dto.enums.BooleanEnums;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Author：xhquan
 * @Project：uep-services
 * @since：2023/5/25 14:16
 */
@TableName("iam_user_status")
public class UserStatusEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 判断是否使用过密码 是否发送激活邀请相关
     */
    private Integer usePasswd;

    /**
     * 判断用户是否为激活状态
     */
    private Integer active;

    private String tenantId;

    /**
     * 历史密码
     */
    private String historyPasswd;

    /**
     * 激活时间根据该字段
     */
    private LocalDateTime createTime;

    /**
     * 锁定时间
     */
    private LocalDateTime lockTime;

    /**
     * 禁用时间
     */
    private LocalDateTime suspendTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getUsePasswd() {
        return usePasswd;
    }

    public void setUsePasswd(Integer usePasswd) {
        this.usePasswd = usePasswd;
    }


    public Integer getActive() {
        return active;
    }

    public void setActive(Integer active) {
        this.active = active;
    }

    public String getHistoryPasswd() {
        return historyPasswd;
    }

    public void setHistoryPasswd(String historyPasswd) {
        this.historyPasswd = historyPasswd;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getLockTime() {
        return lockTime;
    }

    public void setLockTime(LocalDateTime lockTime) {
        this.lockTime = lockTime;
    }

    public LocalDateTime getSuspendTime() {
        return suspendTime;
    }

    public void setSuspendTime(LocalDateTime suspendTime) {
        this.suspendTime = suspendTime;
    }

    public static UserStatusEntity create(Long userId) {
        UserStatusEntity userStatusEntity = new UserStatusEntity();
        userStatusEntity.setId(userId);
        userStatusEntity.setUsePasswd(BooleanEnums.TRUE.getValue());
        userStatusEntity.setActive(BooleanEnums.TRUE.getValue());
        userStatusEntity.setCreateTime(LocalDateTime.now());
        return userStatusEntity;
    }



    @Override
    public String toString() {
        return "UserStatusEntity{" +
                "id=" + id +
                ", usePasswd=" + usePasswd +
                ", tenantId='" + tenantId + '\'' +
                '}';
    }
}
