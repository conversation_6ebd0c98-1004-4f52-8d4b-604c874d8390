package com.cyberscraft.uep.iam.service.oidc.sns.common.utils;

import com.cyberscraft.uep.starter.dds.TenantHolder;
import com.cyberscraft.uep.common.util.RestAPIUtil;
import com.cyberscraft.uep.iam.common.exception.UserCenterException;
import com.cyberscraft.uep.iam.errors.TransactionErrorType;
import com.cyberscraft.uep.iam.service.oidc.sns.common.dto.WeWorkConstant;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.TimeUnit;


/**
 * <p>
 * 企业微信相关类
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2020-03-12 10:17
 */
@Component
public class WeWorkUtil {
    private static final Logger logger = LoggerFactory.getLogger(WeWorkUtil.class);
    @Autowired
    private RedissonClient redissonClient;

    /**
     * 获取企业微信的accessToken
     * @param apiBaseUrl     请求URL HOST
     * @param corpId         租户在企业微信的corpId，租户全局唯一
     * @param appSecret      接入方在企业微信的密钥
     * @return
     */
    public String getAccessToken(String apiBaseUrl,String corpId,String appSecret) {
        String tokenCacheKey = String.format(WeWorkConstant.WEWORK_TOKEN_REDIS_KEY, TenantHolder.getTenantCode(),corpId);
        String accessToken = getAccessTokenFromCache(tokenCacheKey);
        if (StringUtils.isNotBlank(accessToken)) {
            return accessToken;
        }

        String lockKey = String.format(WeWorkConstant.WEWORK_TOKEN_LOCK_KEY, tokenCacheKey);
        RLock lock = redissonClient.getLock(lockKey);

        boolean holdLock ;
        try {
            holdLock = lock.tryLock(WeWorkConstant.WEWORK_TOKEN_LOCK_WAIT_TIME, WeWorkConstant.WEWORK_TOKEN_LOCK_TIME, TimeUnit.MILLISECONDS);

            accessToken = getAccessTokenFromCache(tokenCacheKey);
            if (StringUtils.isNotBlank(accessToken)) {
                return accessToken;
            }
            if (!holdLock) {
                throw new UserCenterException(TransactionErrorType.WEWORK_GET_ACCESS_TOKEN_ERROR);
            }

            return getAccessTokenFromWeWork(tokenCacheKey,apiBaseUrl,corpId,appSecret);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }finally {
            if(lock.isHeldByCurrentThread()){
                lock.unlock();
            }
        }

        throw new UserCenterException(TransactionErrorType.WEWORK_GET_ACCESS_TOKEN_ERROR);
    }


    private String getAccessTokenFromWeWork(String tokenKey,String apiBaseUrl,String corpId,String appSecret) {
        logger.info("get access token from wework");
        String api = apiBaseUrl + String.format(WeWorkConstant.API_ACCESS_TOKEN,corpId,appSecret);
        Map result = RestAPIUtil.getForEntity(api);
        if (result == null) {
            throw new RuntimeException("请求企业微信获取accessToken出错。");
        }

        if (!"0".equals(result.get(WeWorkConstant.API_RESULT_ERRCODE).toString())) {
            logger.error("请求企业微信获取accessToken出错。返回值：{}", result.toString());
            throw new RuntimeException("请求企业微信获取accessToken出错。");
        }

        logger.info("get access token from wework success");
        String accessToken = result.get(WeWorkConstant.API_RESULT_ACCESS_TOKEN).toString();
        Long expireIn = Long.valueOf(result.get(WeWorkConstant.API_RESULT_ACCESS_EXPIRES_IN).toString());
        //缓存token的时间需要略小于企业微信返回的失效时间，正常每次请求，失效时间都会刷新到7200秒
        if(expireIn > 10){
            expireIn = expireIn - 10;
        }

        redissonClient.getBucket(tokenKey).set(accessToken,expireIn,TimeUnit.SECONDS);
        return accessToken;
    }

    private String getAccessTokenFromCache(String tokenKey) {
        Object accessTokenObject = redissonClient.getBucket(tokenKey).get();
        String accessToken = accessTokenObject == null ? null : accessTokenObject.toString();
        return accessToken;
    }

}
