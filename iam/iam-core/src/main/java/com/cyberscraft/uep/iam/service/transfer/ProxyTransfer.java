package com.cyberscraft.uep.iam.service.transfer;

import com.cyberscraft.uep.acm.dto.ProxyNodeVO;
import com.cyberscraft.uep.common.dto.QueryPage;
import com.cyberscraft.uep.iam.dto.domain.ProxyGroupDto;
import com.cyberscraft.uep.iam.dto.request.ProxyClientDto;
import com.cyberscraft.uep.iam.dto.response.ProxyGroupVO;
import com.cyberscraft.uep.iam.dto.response.ProxyClientVO;
import com.cyberscraft.uep.iam.dto.response.ProxyServerVo;
import com.cyberscraft.uep.iam.entity.ProxyClientEntity;
import com.cyberscraft.uep.iam.query.FlowProxyBo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Author：xhquan
 * @Project：uep-services
 * @since：2023/12/6 10:23
 */
@Mapper(componentModel = "spring", uses = {TypeMapper.class})
public interface ProxyTransfer {

    ProxyTransfer INSTANCE = Mappers.getMapper(ProxyTransfer.class);

    ProxyClientVO to(ProxyClientEntity proxyClientEntity);

    QueryPage<ProxyClientVO> proxyEntityToVo(QueryPage<ProxyClientEntity> proxyClientEntities);

    List<ProxyClientVO> proxyEntityToVo(List<ProxyClientEntity> proxyClientEntities);

    ProxyClientEntity to(ProxyClientDto proxyClientDto);

    List<ProxyGroupVO> proxyGroupDtoToVo(List<ProxyGroupDto> list);

    List<ProxyServerVo> boToVo(List<ProxyNodeVO> list);

    @Mappings({
            @Mapping(target = "id", source = "nodeId"),
            @Mapping(target = "name", source = "nodeName")
    })
    ProxyServerVo boToVo(FlowProxyBo bo);
}
