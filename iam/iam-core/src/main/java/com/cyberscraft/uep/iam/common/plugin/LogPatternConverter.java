package com.cyberscraft.uep.iam.common.plugin;


import com.cyberscraft.uep.starter.dds.TenantHolder;
import org.apache.logging.log4j.core.LogEvent;
import org.apache.logging.log4j.core.config.plugins.Plugin;
import org.apache.logging.log4j.core.pattern.ConverterKeys;
import org.apache.logging.log4j.core.pattern.LogEventPatternConverter;
import org.apache.logging.log4j.core.pattern.PatternConverter;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2020-03-06 14:56
 */
@Plugin(name = "TcodePatternConverter", category = PatternConverter.CATEGORY)
@ConverterKeys({"tcode"})
public class LogPatternConverter extends LogEventPatternConverter {

    private static final LogPatternConverter INSTANCE = new LogPatternConverter();

    public static LogPatternConverter newInstance(
            final String[] options) {
        return INSTANCE;
    }

    private LogPatternConverter(){
        super("tcode", "tcode");
    }

    @Override
    public void format(LogEvent event, StringBuilder toAppendTo) {
        String tcode = TenantHolder.getTenantCode();
        if(tcode == null){
            tcode = "";
        }
        toAppendTo.append(tcode);
    }

}
