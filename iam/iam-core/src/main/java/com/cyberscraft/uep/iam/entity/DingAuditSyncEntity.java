package com.cyberscraft.uep.iam.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

@TableName("iam_sync_ding_audit")
public class DingAuditSyncEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    private Long gmtModified;

    private Integer operateModule;

    private String operateModuleView;

    private String bizId;

    private String operatorName;

    private Long platform;

    private String platformView;

    private Integer status;

    private Integer action;

    private String actionView;

    private String resource;

    private Long gmtCreate;

    private String userid;

    private String ipAddress;

    private String orgName;

    private String receiverName;

    private String receiverTypeView;

    private Integer receiverType;

    private String resourceExtension;

    private Long resourceSize;

    private Long targetSpaceId;

    private String realName;

    private Long batchNo;

    private String tenantId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Long gmtModified) {
        this.gmtModified = gmtModified;
    }

    public Integer getOperateModule() {
        return operateModule;
    }

    public void setOperateModule(Integer operateModule) {
        this.operateModule = operateModule;
    }

    public String getOperateModuleView() {
        return operateModuleView;
    }

    public void setOperateModuleView(String operateModuleView) {
        this.operateModuleView = operateModuleView;
    }

    public String getBizId() {
        return bizId;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public Long getPlatform() {
        return platform;
    }

    public void setPlatform(Long platform) {
        this.platform = platform;
    }

    public String getPlatformView() {
        return platformView;
    }

    public void setPlatformView(String platformView) {
        this.platformView = platformView;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getAction() {
        return action;
    }

    public void setAction(Integer action) {
        this.action = action;
    }

    public String getActionView() {
        return actionView;
    }

    public void setActionView(String actionView) {
        this.actionView = actionView;
    }

    public String getResource() {
        return resource;
    }

    public void setResource(String resource) {
        this.resource = resource;
    }

    public Long getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Long gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public String getIpAddress() {
        return ipAddress;
    }

    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getReceiverName() {
        return receiverName;
    }

    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }

    public String getReceiverTypeView() {
        return receiverTypeView;
    }

    public void setReceiverTypeView(String receiverTypeView) {
        this.receiverTypeView = receiverTypeView;
    }

    public Integer getReceiverType() {
        return receiverType;
    }

    public void setReceiverType(Integer receiverType) {
        this.receiverType = receiverType;
    }

    public String getResourceExtension() {
        return resourceExtension;
    }

    public void setResourceExtension(String resourceExtension) {
        this.resourceExtension = resourceExtension;
    }

    public Long getResourceSize() {
        return resourceSize;
    }

    public void setResourceSize(Long resourceSize) {
        this.resourceSize = resourceSize;
    }

    public Long getTargetSpaceId() {
        return targetSpaceId;
    }

    public void setTargetSpaceId(Long targetSpaceId) {
        this.targetSpaceId = targetSpaceId;
    }

    public Long getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(Long batchNo) {
        this.batchNo = batchNo;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    @Override
    public String toString() {
        return "DingAuditSyncEntity{" +
                "id=" + id +
                ", gmtModified=" + gmtModified +
                ", operateModule=" + operateModule +
                ", operateModuleView='" + operateModuleView + '\'' +
                ", bizId='" + bizId + '\'' +
                ", operatorName='" + operatorName + '\'' +
                ", platform=" + platform +
                ", platformView='" + platformView + '\'' +
                ", status=" + status +
                ", action=" + action +
                ", actionView='" + actionView + '\'' +
                ", resource='" + resource + '\'' +
                ", gmtCreate=" + gmtCreate +
                ", userid='" + userid + '\'' +
                ", ipAddress='" + ipAddress + '\'' +
                ", orgName='" + orgName + '\'' +
                ", receiverName='" + receiverName + '\'' +
                ", receiverTypeView='" + receiverTypeView + '\'' +
                ", receiverType=" + receiverType +
                ", resourceExtension='" + resourceExtension + '\'' +
                ", resourceSize=" + resourceSize +
                ", targetSpaceId=" + targetSpaceId +
                ", realName='" + realName + '\'' +
                ", batchNo=" + batchNo +
                ", tenantId='" + tenantId + '\'' +
                '}';
    }
}
