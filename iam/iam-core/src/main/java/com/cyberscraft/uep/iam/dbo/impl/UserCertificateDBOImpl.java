package com.cyberscraft.uep.iam.dbo.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cyberscraft.uep.iam.entity.UserCertificateEntity;
import com.cyberscraft.uep.iam.dao.UserCertificateDao;
import com.cyberscraft.uep.iam.dbo.UserCertificateDBO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * IAM-用户设备证书表 服务实现类
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2019-07-08
 */
@Service
public class UserCertificateDBOImpl extends ServiceImpl<UserCertificateDao, UserCertificateEntity> implements UserCertificateDBO {
    private static Logger logger = LoggerFactory.getLogger(UserCertificateDBOImpl.class);

    @Override
    public UserCertificateEntity findCertByUsernameAndCertId(String username, String certId) {
        UserCertificateEntity entity = new UserCertificateEntity();
        entity.setCertId(certId);
        entity.setUsername(username);
        QueryWrapper<UserCertificateEntity> queryWrapper = new QueryWrapper<>(entity);
        UserCertificateEntity result = super.getOne(queryWrapper);
        return result;
    }

    @Override
    public UserCertificateEntity getByCertId(String certId) {
        UserCertificateEntity entity = new UserCertificateEntity();
        entity.setCertId(certId);
        QueryWrapper<UserCertificateEntity> queryWrapper = new QueryWrapper<>(entity);
        UserCertificateEntity result = super.getOne(queryWrapper);
        return result;
    }

    @Override
    public List<UserCertificateEntity> findCertByUsername(String username) {
        UserCertificateEntity entity = new UserCertificateEntity();
        entity.setUsername(username);
        LambdaQueryWrapper<UserCertificateEntity> queryWrapper = new QueryWrapper<>(entity).lambda();
        queryWrapper.orderByDesc(UserCertificateEntity::getUploadedAt);
        List<UserCertificateEntity> result = super.list(queryWrapper);
        return result;
    }

    @Override
    public UserCertificateEntity getByDeviceId(String username, String deviceId, String kid) {
        UserCertificateEntity entity = new UserCertificateEntity();
        entity.setDeviceId(deviceId);
        entity.setUsername(username);
        entity.setCertId(kid);
        LambdaQueryWrapper<UserCertificateEntity> queryWrapper = new QueryWrapper<>(entity).lambda();
        return super.getOne(queryWrapper);
    }
}
