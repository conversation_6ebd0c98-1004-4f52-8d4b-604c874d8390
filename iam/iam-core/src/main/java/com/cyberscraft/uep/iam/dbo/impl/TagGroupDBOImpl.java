package com.cyberscraft.uep.iam.dbo.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cyberscraft.uep.common.util.CollectionUtils;
import com.cyberscraft.uep.iam.dao.TagGroupDao;
import com.cyberscraft.uep.iam.dbo.TagGroupDBO;
import com.cyberscraft.uep.iam.dto.enums.TagGroupTypeEnum;
import com.cyberscraft.uep.iam.entity.TagGroupEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/3/8 17:37
 * @Version 1.0
 * @Description 应用服务实现类
 */
@Service
public class TagGroupDBOImpl extends ServiceImpl<TagGroupDao, TagGroupEntity> implements TagGroupDBO {

    @Autowired
    private TagGroupDao tagGroupDao;

    @Override
    public List<Map<String, Object>> getList(String filter) {
        return this.tagGroupDao.getList(filter);
    }

    @Override
    public TagGroupEntity geTagGroupByCode(String groupCode) {
        LambdaQueryWrapper<TagGroupEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TagGroupEntity::getAppCode, groupCode);
        return this.getOne(queryWrapper);
    }

    @Override
    public TagGroupEntity geTagGroupByName(String groupName) {
        LambdaQueryWrapper<TagGroupEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TagGroupEntity::getAppName, groupName);
        return this.getOne(queryWrapper);
    }

    @Override
    public int getTagGroupCount() {
        LambdaQueryWrapper<TagGroupEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TagGroupEntity::getIsDefault, TagGroupTypeEnum.OTHER.getValue());
        return this.count(queryWrapper);
    }

    @Override
    public List<TagGroupEntity> getTagGroupEntityByIds(List<String> ids) {
        LambdaQueryWrapper<TagGroupEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TagGroupEntity::getId, ids);
        return this.list(queryWrapper);
    }

    @Override
    public List<TagGroupEntity> getAllTagGroupEntity() {
        return this.list();
    }
}
