package com.cyberscraft.uep.iam.service.config.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cyberscraft.uep.account.client.domain.Connector;
import com.cyberscraft.uep.account.client.domain.SnsConfig;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountErrorType;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountException;
import com.cyberscraft.uep.account.client.provider.dingding.domain.DingTalkConfig;
import com.cyberscraft.uep.account.client.service.IThirdPartyCallBackConfigService;
import com.cyberscraft.uep.account.client.service.IThirdPartyProcessService;
import com.cyberscraft.uep.common.constant.SysConstant;
import com.cyberscraft.uep.common.dto.PageView;
import com.cyberscraft.uep.common.dto.Pagination;
import com.cyberscraft.uep.common.util.CollectionUtils;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.common.util.SnowflakeIDUtil;
import com.cyberscraft.uep.iam.common.domain.configparams.CfgUserPolicy;
import com.cyberscraft.uep.iam.common.exception.UserCenterException;
import com.cyberscraft.uep.iam.dbo.ConnectorDBO;
import com.cyberscraft.uep.iam.dbo.PushConnectorDBO;
import com.cyberscraft.uep.iam.dbo.SnsConfigDBO;
import com.cyberscraft.uep.iam.dto.domain.config.SnsConfigQueryDTO;
import com.cyberscraft.uep.iam.dto.enums.ConnectorStatusEnum;
import com.cyberscraft.uep.iam.dto.enums.ConnectorTypeEnum;
import com.cyberscraft.uep.iam.dto.request.configs.SnsConfigDTO;
import com.cyberscraft.uep.iam.dto.response.configs.SnsConfigListVO;
import com.cyberscraft.uep.iam.dto.response.configs.SnsConfigVO;
import com.cyberscraft.uep.iam.entity.ConnectorEntity;
import com.cyberscraft.uep.iam.entity.SnsConfigEntity;
import com.cyberscraft.uep.iam.errors.TransactionErrorType;
import com.cyberscraft.uep.iam.service.IAppService;
import com.cyberscraft.uep.iam.service.IConfigService;
import com.cyberscraft.uep.iam.service.config.ISnsConfigCacheService;
import com.cyberscraft.uep.iam.service.config.ISnsConfigService;
import com.cyberscraft.uep.iam.service.config.transfer.SnsConfigTransfer;
import com.cyberscraft.uep.iam.service.connector.IConnectorService;
import com.cyberscraft.uep.iam.service.oidc.util.SecureRequestCheck;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.cyberscraft.uep.iam.errors.TransactionErrorType.SNS_CONFIG_ID_INVALID;

/***
 * 因为CachePut等操作，不好同时指定两个key,比如说ID，及默认key，所以采用自定义代码实现
 * @date 2021/6/12
 * <AUTHOR>
 ***/
@Service
//@CacheConfig(cacheNames = "snsConfigs", cacheManager = "cacheManager")
public class SnsConfigServiceImpl implements ISnsConfigService {

    @Resource
    private ISnsConfigCacheService snsConfigCacheService;

    @Resource
    private SnsConfigDBO snsConfigDBO;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private SnsConfigTransfer snsConfigTransfer;

    @Resource
    private PushConnectorDBO pushConnectorDBO;

    @Resource
    private ConnectorDBO connectorDBO;

    @Resource
    private IConnectorService connectorService;

    @Resource
    private IAppService iAppService;

    @Resource
    private IThirdPartyCallBackConfigService thirdPartyCallBackConfigService;

    @Resource
    private IThirdPartyProcessService thirdPartyProcessService;

    @Autowired
    private IConfigService configService;

    /***
     *
     */
    private final static Logger LOG = LoggerFactory.getLogger(SnsConfigServiceImpl.class);

    /**
     * 企业SNS配置redis锁，占位符是租户ID，配置类型
     */
    private static final String SNS_CONFIG_LOCK = "IAM:SNS_CONFIG_LOCK:%s:%s";

    private final static String TEMP_SNS_CONFIG_KEY = "TEMP_SNS_CONFIG_KEY:%s:%s";

    private final static Long TEMP_SNS_CONFIG_EXPIRE = 60L;

    /**
     * 企业SNS配置redis锁，锁的等待时间，单位：毫秒
     */
    private static final Long SNS_CONFIG_LOCK_WAIT_TIME = 5000L;
    /**
     * 企业SNS配置redis锁，锁的超时时间,单位：毫秒
     */
    private static final Long SNS_CONFIG_LOCK_TIME = 10000L;

    @Override
//    @CachePut(key = "#dto.getId()")
//    @CachePut(key = "DefaultSnsConfigs"+dto.getType(),condition ="#dto.getIsDefault().getValue()==1")
    @Transactional
    public Long createSnsConfig(SnsConfigDTO dto) {
        String lockKey = String.format(SNS_CONFIG_LOCK, TenantHolder.getTenantCode(), dto.getType().getValue());
        try {
            RLock lock = redissonClient.getLock(lockKey);
            if (!lock.tryLock(SNS_CONFIG_LOCK_WAIT_TIME, SNS_CONFIG_LOCK_TIME, TimeUnit.MILLISECONDS)) {
                throw new UserCenterException(TransactionErrorType.LOCK_ERROR);
            }

            SnsConfigEntity entity = snsConfigDBO.getByName(dto.getName());
            if (entity != null) {
                throw new UserCenterException(TransactionErrorType.SNS_CONFIG_NAME_DUPLICATE);
            }

            if (dto.getCorpId() != null) {
                entity = snsConfigDBO.getByCorpId(dto.getCorpId());
                if (entity != null) {
                    throw new UserCenterException(TransactionErrorType.SNS_CONFIG_CORPID_DUPLICATE);
                }
            }

            entity = new SnsConfigEntity();
            entity.setId(SnowflakeIDUtil.getId());
            entity.setName(dto.getName());
            entity.setType(dto.getType().getValue());
            entity.setCorpId(dto.getCorpId() == null ? String.valueOf(entity.getId()) : dto.getCorpId());

            Map<String, Object> config = dto.getConfig();
            entity.setConfig(JsonUtil.obj2Str(config));

            entity.setIsDefault(dto.getDefault() ? SysConstant.TRUE_VALUE : SysConstant.FALSE_VALUE);

            SnsConfigEntity oldDefault = snsConfigDBO.getDefaultConfigByType(dto.getType().getValue());

            if (SysConstant.FALSE_VALUE.equals(entity.getIsDefault())) {
                //原来没有默认数据，则设置为默认数据
                if (oldDefault == null) {
                    entity.setIsDefault(SysConstant.TRUE_VALUE);
                }
            } else { //如果当前是默认，则清除原来的默认配置
                if (oldDefault != null) {
                    //更改原默认配置数据
                    oldDefault.setIsDefault(SysConstant.FALSE_VALUE);
                    snsConfigDBO.updateById(oldDefault);
                    snsConfigCacheService.save(oldDefault);
                }
            }

            entity.setCreateBy(SecureRequestCheck.getUsername());
            entity.setUpdateBy(SecureRequestCheck.getUsername());
            entity.setCreateTime(LocalDateTime.now());
            entity.setUpdateTime(LocalDateTime.now());

            // entity 对象转换为 回调事件注册对象
            SnsConfig snsConfig = snsConfigTransfer.entityToVo(entity);
            snsConfig.setTenantId(TenantHolder.getTenantCode());
            completeSnsConfig(snsConfig);
            entity.setConfig(snsConfig.getConfig());

            snsConfigDBO.save(entity);
//            iAppService.updateGatewayThirdPartyInfo(entity);
            //缓存，并且保存默认配置
            entity.setTenantId(TenantHolder.getTenantCode());

            snsConfigCacheService.save(entity);
            if (SysConstant.TRUE_VALUE.equals(entity.getIsDefault())) {
                snsConfigCacheService.setDefault(entity.getType(), entity);
            }

            CfgUserPolicy userPolicyCfg = configService.getUserPolicyCfg();
            if (userPolicyCfg.getAppVerify() || userPolicyCfg.getUserRenewalVerify() || userPolicyCfg.getUserCreateVerify()) {
                snsConfig.setHasProcess(Boolean.TRUE);
                snsConfig.setHasConnector(Boolean.FALSE);

                // 保存临时企业信息配置 供回调注册使用
                saveTempSnsConfig(snsConfig);
                // 注册回调信息
                registerEventCallBack(snsConfig);
                // 清楚临时企业配置信息
                cleanTempSnsConfig(entity.getId());
            }
            return entity.getId();
        } catch (UserCenterException e) {
            throw e;
        } catch (Exception e) {
            LOG.error("create sns config has error", e);
            throw new UserCenterException(e.getMessage());
        }
    }

    @Override
    public void saveTempSnsConfig(SnsConfig snsConfig) {
        String key = String.format(TEMP_SNS_CONFIG_KEY, TenantHolder.getTenantCode(), snsConfig.getId());
        redissonClient.getBucket(key).set(snsConfig, TEMP_SNS_CONFIG_EXPIRE, TimeUnit.SECONDS);
    }

    @Override
    public SnsConfig getTempSnsConfig(Long snsId) {
        String key = String.format(TEMP_SNS_CONFIG_KEY, TenantHolder.getTenantCode(), snsId);
        RBucket<SnsConfig> bucket = redissonClient.getBucket(key);
        return bucket.get();
    }

    @Override
    public void cleanTempSnsConfig(Long snsId) {
        String key = String.format(TEMP_SNS_CONFIG_KEY, TenantHolder.getTenantCode(), snsId);
        redissonClient.getBucket(key).delete();
    }

    @Override
    @Transactional
    public Long modifySnsConfig(SnsConfigDTO dto) {

        String lockKey = String.format(SNS_CONFIG_LOCK, TenantHolder.getTenantCode(), dto.getType().getValue());
        try {
            SnsConfigEntity entity = snsConfigDBO.getById(dto.getId());
            if (entity == null) {
                throw new UserCenterException(SNS_CONFIG_ID_INVALID);
            }
            RLock lock = redissonClient.getLock(lockKey);
            if (!lock.tryLock(SNS_CONFIG_LOCK_WAIT_TIME, SNS_CONFIG_LOCK_TIME, TimeUnit.MILLISECONDS)) {
                throw new UserCenterException(TransactionErrorType.LOCK_ERROR);
            }

            SnsConfigEntity old = snsConfigDBO.getByName(dto.getName());
            if (old != null && !old.getId().equals(entity.getId())) {
                throw new UserCenterException(TransactionErrorType.SNS_CONFIG_NAME_DUPLICATE);
            }
            if (dto.getCorpId() != null) {
                old = snsConfigDBO.getByCorpId(dto.getCorpId());
                if (old != null && !old.getId().equals(entity.getId())) {
                    throw new UserCenterException(TransactionErrorType.SNS_CONFIG_CORPID_DUPLICATE);
                }
            }

            //获取原snsConfig
            SnsConfig oldSnsConfig = snsConfigTransfer.entityToVo(entity);

            entity.setName(dto.getName());
            entity.setType(dto.getType().getValue());
            Map<String, Object> config = dto.getConfig();
            entity.setConfig(JsonUtil.obj2Str(config));

            if (dto.getCorpId() != null) {
                entity.setCorpId(dto.getCorpId());
            }
            entity.setIsDefault(dto.getDefault() ? SysConstant.TRUE_VALUE : SysConstant.FALSE_VALUE);

            //原来没有默认数据，则设置为默认数据
            SnsConfigEntity oldDefault = snsConfigDBO.getDefaultConfigByType(dto.getType().getValue());
            if (SysConstant.FALSE_VALUE.equals(entity.getIsDefault())) {
                //原来没有默认数据，则设置为默认数据
                if (oldDefault == null) {
                    entity.setIsDefault(SysConstant.TRUE_VALUE);
                }
            } else { //如果当前是默认,并且原来的默认配置不是当前配置，则清除原来的默认配置
                if (oldDefault != null && !oldDefault.getId().equals(entity.getId())) {
                    //更改原默认配置数据
                    oldDefault.setIsDefault(SysConstant.FALSE_VALUE);
                    snsConfigDBO.updateById(oldDefault);
                    snsConfigCacheService.save(oldDefault);
                }
            }
            entity.setUpdateBy(SecureRequestCheck.getUsername());
            entity.setUpdateTime(LocalDateTime.now());


            // 更新注册事件
            SnsConfig newSnsConfig = snsConfigTransfer.entityToVo(entity);
            newSnsConfig.setTenantId(TenantHolder.getTenantCode());
            completeSnsConfig(newSnsConfig);
            entity.setConfig(newSnsConfig.getConfig());

            snsConfigDBO.updateById(entity);
            iAppService.updateGatewayThirdPartyInfo(entity);

            //缓存，并且保存默认配置
            entity.setTenantId(TenantHolder.getTenantCode());
            snsConfigCacheService.save(entity);
            if (SysConstant.TRUE_VALUE.equals(entity.getIsDefault())) {
                snsConfigCacheService.setDefault(entity.getType(), entity);
            }

            CfgUserPolicy userPolicyCfg = configService.getUserPolicyCfg();
            if (userPolicyCfg.getAppVerify() || userPolicyCfg.getUserRenewalVerify() || userPolicyCfg.getUserCreateVerify()) {
                newSnsConfig.setHasProcess(Boolean.TRUE);
            }

            List<Connector> connectorBySnsId = connectorService.getConnectorBySnsId(entity.getId());
            if (connectorBySnsId.size() > 0) {
                newSnsConfig.setHasConnector(Boolean.TRUE);
            }

            // 将修改后企业信息临时保存
            this.saveTempSnsConfig(newSnsConfig);
            updateEventCallBack(oldSnsConfig, newSnsConfig);
            this.cleanTempSnsConfig(entity.getId());

            return entity.getId();
        } catch (UserCenterException e) {
            throw e;
        } catch (Exception e) {
            LOG.error("create sns config has error :{}", e.getMessage());
            throw new UserCenterException(e.getMessage());
        }
    }

    @Override
    public SnsConfigVO changeSnsStatus(Long id, Integer status) {
        if (status != 0 && status != 1) {
            throw new UserCenterException(TransactionErrorType.SNS_CONFIG_INVALID);
        }
        SnsConfigEntity entity = snsConfigDBO.getById(id);
        if (null == entity) {
            throw new UserCenterException(SNS_CONFIG_ID_INVALID);
        }
        entity.setUpdateBy(SecureRequestCheck.getUsername());
        entity.setUpdateTime(LocalDateTime.now());
        entity.setStatus(status);
        boolean rtn = snsConfigDBO.updateById(entity);
        if (rtn) {
            snsConfigCacheService.save(entity);
            if (SysConstant.TRUE_VALUE.equals(entity.getIsDefault())) {
                snsConfigCacheService.setDefault(entity.getType(), entity);
            }
            return snsConfigTransfer.entity2VO(entity);
        }
        return null;
    }

    /***
     * 删除配置
     * @param id
     */
    @Override
    @Transactional
    public SnsConfigVO removeSnsConfig(Long id) {
        try {
            SnsConfigEntity entity = snsConfigDBO.getById(id);
            SnsConfig snsConfig = snsConfigTransfer.entityToVo(entity);
            if (entity == null) {
                throw new UserCenterException(SNS_CONFIG_ID_INVALID);
            }
            checkConfigIsUsed(entity.getId());
            String lockKey = String.format(SNS_CONFIG_LOCK, TenantHolder.getTenantCode(), entity.getType());
            RLock lock = redissonClient.getLock(lockKey);
            if (!lock.tryLock(SNS_CONFIG_LOCK_WAIT_TIME, SNS_CONFIG_LOCK_TIME, TimeUnit.MILLISECONDS)) {
                throw new UserCenterException(TransactionErrorType.LOCK_ERROR);
            }
            //如果删除的是默认配置
            if (SysConstant.TRUE_VALUE.equals(entity.getIsDefault())) {
                //删除当前配置
                snsConfigCacheService.remove(id);
                snsConfigDBO.removeById(id);
                snsConfigCacheService.setDefault(entity.getType(), null);
                //查询第一个配置设置为默认
                SnsConfigEntity defaultConfig = snsConfigDBO.getOneSnsConfigByTypeWithoutId(entity.getType(), entity.getId());
                if (defaultConfig != null) {
                    defaultConfig.setIsDefault(SysConstant.TRUE_VALUE);
                    snsConfigDBO.updateById(defaultConfig);
                    snsConfigCacheService.save(defaultConfig);
                    snsConfigCacheService.setDefault(entity.getType(), defaultConfig);
                }
            } else {
                //删除当前配置
                snsConfigCacheService.remove(id);
                snsConfigDBO.removeById(id);
            }

            // 删除回调信息
            removeEventCallBack(snsConfig);
            return snsConfigTransfer.entity2VO(entity);
        } catch (UserCenterException e) {
            throw e;
        } catch (Exception e) {
            LOG.error("create sns config has error :{}", e.getMessage());
            throw new UserCenterException(e.getMessage());
        }
    }

    /***
     *
     * @param id
     */
    private void checkConfigIsUsed(Long id) {
        List<ConnectorEntity> connectorList = connectorDBO.getByConfigId(id);
        int size = connectorList.size();
        if (size > 0) {
            throw new UserCenterException(TransactionErrorType.SNS_CONFIG_USED);
        }

        size = pushConnectorDBO.countByConfigId(id);
        if (size > 0) {
            throw new UserCenterException(TransactionErrorType.SNS_CONFIG_USED);
        }
    }


    @Override
    public SnsConfigVO setDefaultSnsConfig(Long id) {
        try {
            SnsConfigEntity entity = snsConfigDBO.getById(id);
            if (entity == null) {
                throw new UserCenterException(SNS_CONFIG_ID_INVALID);
            }
            if (SysConstant.TRUE_VALUE.equals(entity.getIsDefault())) {
                return snsConfigTransfer.entity2VO(entity);
            }
            String lockKey = String.format(SNS_CONFIG_LOCK, TenantHolder.getTenantCode(), entity.getType());
            RLock lock = redissonClient.getLock(lockKey);
            if (!lock.tryLock(SNS_CONFIG_LOCK_WAIT_TIME, SNS_CONFIG_LOCK_TIME, TimeUnit.MILLISECONDS)) {
                throw new UserCenterException(TransactionErrorType.LOCK_ERROR);
            }
            //如果原来不是默认配置，则查询原来的默认配置，并且清除默认标志
            if (SysConstant.FALSE_VALUE.equals(entity.getIsDefault())) {
                //查询第一个配置设置为默认
                SnsConfigEntity defaultConfig = snsConfigDBO.getDefaultConfigByType(entity.getType());
                if (defaultConfig != null && !defaultConfig.getId().equals(entity.getId())) {
                    defaultConfig.setIsDefault(SysConstant.FALSE_VALUE);
                    snsConfigDBO.updateById(defaultConfig);
                    snsConfigCacheService.save(defaultConfig);
                }
            }

            entity.setIsDefault(SysConstant.TRUE_VALUE);
            snsConfigDBO.updateById(entity);
            snsConfigCacheService.save(entity);
            snsConfigCacheService.setDefault(entity.getType(), entity);

            return snsConfigTransfer.entity2VO(entity);
        } catch (UserCenterException e) {
            throw e;
        } catch (Exception e) {
            LOG.error("create sns config has error :{}", e.getMessage());
            throw new UserCenterException(e.getMessage());
        }
    }

    @Override
    public SnsConfigEntity get(Long id) {
        if (id == null) {
            throw new UserCenterException(SNS_CONFIG_ID_INVALID);
        }
        SnsConfigEntity obj = null;
        try {
            obj = this.snsConfigCacheService.get(id);
        } catch (Exception e) {
            LOG.error("get Cache error.", e);
        }
        if (obj == null) {
            obj = this.snsConfigDBO.getById(id);
            if (obj != null) {
                this.snsConfigCacheService.save(obj);
                // 判断是否为默认配置
                if (SysConstant.TRUE_VALUE.equals(obj.getIsDefault())) {
                    this.snsConfigCacheService.setDefault(obj.getType(), obj);
                }
            }
        }
        return obj;
    }

    @Override
    public SnsConfigEntity get(String type, String appKey) {
        ConnectorTypeEnum snsType = ConnectorTypeEnum.getByName(type);
        List<SnsConfigEntity> list = getSnsConfigsByStatus(ConnectorStatusEnum.ACTIVE.getValue());
        List<SnsConfigEntity> collect = list.stream().filter(e -> e.getType().equals(snsType.getValue())).collect(Collectors.toList());
        for (SnsConfigEntity snsConfigEntity : collect) {
            switch (snsType) {
                case DINGDING:
                    DingTalkConfig dingTalkConfig = JsonUtil.str2Obj(snsConfigEntity.getConfig(), DingTalkConfig.class);
                    if (dingTalkConfig.getAppKey().equals(appKey)) {
                        return snsConfigEntity;
                    }
                    break;
            }
        }
        return null;
    }

    @Override
    //@Cacheable(key = "#id", unless = "#result == null")
    public SnsConfigVO getSnsConfigVO(Long id) {
        SnsConfigEntity obj = get(id);
        return obj != null ? snsConfigTransfer.entity2VO(obj) : null;
    }

    @Override
    public PageView<SnsConfigVO> getPageSnsConfigs(Pagination<SnsConfigQueryDTO> page) {
        PageView<SnsConfigEntity> res = snsConfigDBO.page(page);
        return snsConfigTransfer.entity2VOPage(res);
    }


    @Override
    public SnsConfigEntity getDefault(Integer type) {
        if (type == null) {
            throw new UserCenterException(TransactionErrorType.SNS_CONFIG_TYPE_INVALID);
        }
        SnsConfigEntity obj = this.snsConfigCacheService.getDefault(type);
        if (obj == null) {
            obj = this.snsConfigDBO.getDefaultConfigByType(type);
            if (obj != null) {
                this.snsConfigCacheService.save(obj);
                this.snsConfigCacheService.setDefault(obj.getType(), obj);
            }
        }
        return obj;
    }

    @Override
    public String getConfigValue(Long id, Integer type) {
        // 通过ID查询
        SnsConfigEntity obj = get(id);
        if (obj != null) {
            return obj.getConfig();
        }
        // 通过类型查询
//        obj = getDefault(type);
//        return obj != null ? obj.getConfig() : null;
        return null;
    }

    @Override
    public SnsConfigVO getConfigWithDefault(Long id, Integer type) {

        return snsConfigTransfer.entity2VO(getConfigEntityWithDefault(id, type));
    }


    public SnsConfigEntity getConfigEntityWithDefault(Long id, Integer type) {
        SnsConfigEntity obj = get(id);
        if (obj != null) {
            return obj;
        }
        obj = getDefault(type);
        return obj;
    }

    @Override
    public SnsConfig getSnsConfig(Long id) {
        return snsConfigTransfer.entityToVo(get(id));
    }

    @Override
    public Map<String, Object> getConfigMapValueWithDefault(Long id, Integer type) {
        String value = getConfigValue(id, type);
        return value != null ? JsonUtil.str2Map(value) : null;
    }

    @Override
    public List<SnsConfigListVO> getAllSnsConfigs() {
        List<SnsConfigEntity> list = snsConfigDBO.list();

        return snsConfigTransfer.entity2ListVO(list);
    }

    @Override
    public List<SnsConfigListVO> getAllSnsConfigsByType(ConnectorTypeEnum type) {
        LambdaQueryWrapper<SnsConfigEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SnsConfigEntity::getType, type.getValue());
        List<SnsConfigEntity> list = snsConfigDBO.list(queryWrapper);
        return snsConfigTransfer.entity2ListVO(list);
    }

    @Override
    public List<SnsConfigEntity> getSnsConfigsByStatus(Integer status) {
        return snsConfigDBO.getByStatus(status);
    }

    @Override
    public void completeSnsConfig(SnsConfig snsConfig) throws UserCenterException {
        LOG.info("开始完善认证源配置，认证源类型:{}", snsConfig.getType());
        thirdPartyCallBackConfigService.improveSnsConfig(snsConfig.getTenantId(), String.valueOf(snsConfig.getType()), snsConfig);
        LOG.info("完善认证源配置完成，认证源类型:{}", snsConfig.getType());
    }

    @Override
    public void registerEventCallBack(SnsConfig snsConfig) throws UserCenterException {
        LOG.info("当前进行认证源回调注册操作，认证源类型:{}", snsConfig.getType());
        thirdPartyCallBackConfigService.registerEventCallBack(snsConfig.getTenantId(), String.valueOf(snsConfig.getType()), snsConfig);
        LOG.info("当前进行认证源回调注册操作完成，认证源类型:{}", snsConfig.getType());
    }

    @Override
    public void updateEventCallBack(SnsConfig oldSnsConfig, SnsConfig newSnsConfig) throws UserCenterException {
        LOG.info("当前进行认证源回调更新操作，认证源类型:{}", newSnsConfig.getType());
        thirdPartyCallBackConfigService.updateEventCallBack(newSnsConfig.getTenantId(), String.valueOf(newSnsConfig.getType()), oldSnsConfig, newSnsConfig);
        LOG.info("当前进行认证源回调更新操作完成，认证源类型:{}", newSnsConfig.getType());
    }

    @Override
    public void removeEventCallBack(SnsConfig snsConfig) throws UserCenterException {
        LOG.info("当前进行认证源回调删除操作，认证源类型:{}", snsConfig.getType());
        thirdPartyCallBackConfigService.removeEventCallBack(snsConfig.getTenantId(), String.valueOf(snsConfig.getType()), snsConfig);
        LOG.info("当前进行认证源回调删除操作完成，认证源类型:{}", snsConfig.getType());

    }

    @Override
    public Map<String, String> getSnsSchema(Long id, Map<String, String> query) {
        SnsConfig snsConfig = getSnsConfig(id);
        String tenantId = TenantHolder.getTenantCode();
        return thirdPartyProcessService.getProcessSchema(tenantId, snsConfig, query);
    }

    @Override
    public Boolean checkSnsUser(Long configId, String userId) {
        SnsConfig snsConfig = getSnsConfig(configId);
        String tenantId = TenantHolder.getTenantCode();
        Map<String, Object> userMap = thirdPartyProcessService.checkSnsUser(tenantId, snsConfig, userId);
        if (CollectionUtils.isEmpty(userMap)) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.ACCOUNT_ID_INVALID.getCode(), "账户[" + userId + "]无效");
        }
        return true;
    }

}
