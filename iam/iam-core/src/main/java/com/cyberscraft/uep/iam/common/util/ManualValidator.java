package com.cyberscraft.uep.iam.common.util;

import com.cyberscraft.uep.iam.errors.TransactionErrorType;
import com.cyberscraft.uep.iam.common.exception.UserException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;

public class ManualValidator {
	private static Logger logger = LoggerFactory.getLogger(ManualValidator.class);
	
	public static void validate(Object obj) {
		validate(buildValidator(), obj);
	}
	
	public static void validateList(List<?> objList) {
		Validator validator = buildValidator();
		  
		for (Object obj : objList) {
			validate(validator, obj);
		}
	}

	private static Validator buildValidator() {
		return Validation.buildDefaultValidatorFactory().getValidator();
	}

	private static void validate(Validator validator, Object obj) {
		Set<ConstraintViolation<Object>> constraintViolations = validator.validate(obj);

		if(!constraintViolations.isEmpty()) {
			List<String> listViol = new ArrayList<>();
			for (ConstraintViolation<Object> constraintViolation : constraintViolations) {
				logger.error("Invalid Request Parameter!({})", constraintViolation.getMessage());
				listViol.add(constraintViolation.getMessage());
			}
			Collections.sort(listViol);
			throw new UserException(HttpStatus.BAD_REQUEST, TransactionErrorType.INVALID_REQUEST_PARAM, listViol.get(0));
		}
	}
}