package com.cyberscraft.uep.iam.common.domain;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;

public class PagedResult<T> {
    /**
     * which page
     */
    private int page;
    private int pageSize;

    /**
     * all the entry count
     */
    private int total;
    /**
     * entries in this page
     */
    private Collection<T> results;

    public PagedResult(int page, int pageSize, int total) {
        this.page = page;
        this.pageSize = pageSize;
        this.total = total;
        this.results = new ArrayList<T>(pageSize);
    }

    public static <T> PagedResult<T> EMPTY() {
        return new PagedResult<T>(1,1,0);
    }

    public void addResult(T result){
        this.results.add(result);
    }

    public void addResults(Collection<? extends T> results) {
        if (results != null && !results.isEmpty()) {
            this.results.addAll(results);
        }
    }
    /**
     * @return a copy of the entries in this page
     */
    public Collection<T> getResults(){
        return Collections.unmodifiableCollection(this.results);
    }

    public void clearResults() {
        this.results.clear();
    }

    /**
     * @return the actual entries count in this page
     */
    public int getActualCount() {
        return this.results.size();
    }

    /**
     * @return how many pages can be returned in this query
     */
    public int getPageCount() {
        return (total/pageSize) + (total%pageSize >0 ? 1 :0);
    }

    /**
     * @return the current requested page #.
     */
    public int getPage() {
        return this.page;
    }

    public int getPageSize() {
        return this.pageSize;
    }

    /**
     * @return the total entries count in this query.
     */
    public int getTotal() {
        return this.total;
    }
}