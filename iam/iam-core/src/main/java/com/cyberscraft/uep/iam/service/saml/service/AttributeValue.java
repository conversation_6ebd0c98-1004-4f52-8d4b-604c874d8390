package com.cyberscraft.uep.iam.service.saml.service;

import net.shibboleth.utilities.java.support.annotation.constraint.NotEmpty;
import org.opensaml.core.xml.schema.XSAny;
import org.opensaml.saml.common.SAMLObject;
import org.opensaml.saml.common.xml.SAMLConstants;

import javax.annotation.Nonnull;
import javax.xml.namespace.QName;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/2/16 6:43 下午
 */
public interface AttributeValue extends SAMLObject, XSAny {
    @Nonnull @NotEmpty
    String DEFAULT_ELEMENT_LOCAL_NAME = "AttributeValue";

    @Nonnull
    QName DEFAULT_ELEMENT_NAME = new QName(SAMLConstants.SAML20_NS, DEFAULT_ELEMENT_LOCAL_NAME,
            SAMLConstants.SAML20_PREFIX);
}
