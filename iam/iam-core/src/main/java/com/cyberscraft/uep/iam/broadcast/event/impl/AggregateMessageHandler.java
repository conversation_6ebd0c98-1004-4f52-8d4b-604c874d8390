package com.cyberscraft.uep.iam.broadcast.event.impl;

import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.iam.broadcast.event.IBroadcastMessageHandler;
import com.cyberscraft.uep.iam.dbo.ConnectorSyncHistoryDBO;
import com.cyberscraft.uep.iam.dto.domain.SyncStats;
import com.cyberscraft.uep.iam.dto.enums.SyncStatus;
import com.cyberscraft.uep.iam.entity.ConnectorSyncHistoryEntity;
import com.cyberscraft.uep.iam.service.connector.task.AggregateTaskMonitor;
import com.cyberscraft.uep.mq.enums.BroadcastMessageType;
import com.cyberscraft.uep.mq.vo.BroadcastMessage;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/14 下午9:29
 */
@Component
public class AggregateMessageHandler implements IBroadcastMessageHandler {
    private Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    private AggregateTaskMonitor aggregateTaskMonitor;
    @Resource
    protected ConnectorSyncHistoryDBO connectorSyncHistoryDBO;

    @Override
    public void handleMessage(BroadcastMessage broadcastMessage) {
        logger.info("stop aggregate task, target {} of tenant {}, resource {}, trigger by action {}",
                broadcastMessage.getTarget(),
                broadcastMessage.getTenantId(),
                broadcastMessage.getResourceType(),
                broadcastMessage.getAction());

        try {
            TenantHolder.setTenantCode(broadcastMessage.getTenantId());
            final String taskId = broadcastMessage.getTarget();
            aggregateTaskMonitor.cancelTask(String.valueOf(taskId));
            //1、修改数据库状态
            ConnectorSyncHistoryEntity connectorHistory = connectorSyncHistoryDBO.getById(taskId);
            if (connectorHistory != null && connectorHistory.getStatus() == SyncStatus.IN_PROGRESS.getValue()) {
                SyncStats syncStats = new SyncStats();
                String stats = connectorHistory.getStats();
                if (StringUtils.isNotBlank(stats)) {
                    syncStats = JsonUtil.str2Obj(stats, SyncStats.class);
                }
                if (syncStats != null) {
                    syncStats.failReason = "The task is canceled";
                    connectorHistory.setStats(JsonUtil.obj2Str(syncStats));
                }
                connectorHistory.setStatus(SyncStatus.FAILED.getValue());
                connectorHistory.setViewStatus(SyncStatus.FAILED.getValue());
                connectorHistory.setEndTime(LocalDateTime.now());
                connectorSyncHistoryDBO.updateById(connectorHistory);
            }
        } finally {
            TenantHolder.remove();
        }
    }

    @Override
    public String getSupportedMessageType() {
        return BroadcastMessageType.AGGREGATE_TASK_STOP.getSupportedType();
    }
}