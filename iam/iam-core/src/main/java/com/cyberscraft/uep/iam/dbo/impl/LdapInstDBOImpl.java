package com.cyberscraft.uep.iam.dbo.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cyberscraft.uep.iam.dao.LdapInstDao;
import com.cyberscraft.uep.iam.dbo.LdapInstDBO;
import com.cyberscraft.uep.iam.entity.LdapInstEntity;
import org.springframework.stereotype.Service;

/**
 * @Author: liuanyang
 * @Date: 2021/11/1
 */
@Service
public class LdapInstDBOImpl extends ServiceImpl<LdapInstDao, LdapInstEntity> implements LdapInstDBO {

    @Override
    public LdapInstEntity getOneByUsername(String username) {
        QueryWrapper<LdapInstEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(LdapInstEntity::getUsername, username);
        return getOne(queryWrapper);
    }

    @Override
    public LdapInstEntity getOneByInstance(String instance){
        QueryWrapper<LdapInstEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(LdapInstEntity::getInstanceId, instance);
        return getOne(queryWrapper);
    }
}
