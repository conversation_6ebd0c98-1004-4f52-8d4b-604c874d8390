package com.cyberscraft.uep.iam.service.as.tag.parser;

import com.cyberscraft.uep.iam.common.enums.LocalUserAttr;
import com.cyberscraft.uep.iam.common.exception.UserCenterException;
import com.cyberscraft.uep.iam.dto.enums.FieldDataType;
import com.cyberscraft.uep.iam.dto.enums.FieldType;
import com.cyberscraft.uep.iam.entity.FieldDictEntity;
import com.cyberscraft.uep.iam.entity.UserEntity;
import com.cyberscraft.uep.iam.errors.TransactionErrorType;
import com.cyberscraft.uep.iam.service.IFieldDictService;
import com.cyberscraft.uep.iam.service.transfer.TypeMapper;
import com.querydsl.core.types.*;
import com.querydsl.core.types.dsl.Expressions;
import org.antlr.v4.runtime.ANTLRInputStream;
import org.antlr.v4.runtime.CommonTokenStream;
import org.antlr.v4.runtime.tree.ParseTree;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.beans.Introspector;

@Service("tagExpressionParser")
public class TagExpressionParser extends RqlBaseVisitor<Predicate> {
    /**
     * 动态标签解析器
     */
    private static final Logger logger = LoggerFactory.getLogger(TagExpressionParser.class);

    @Autowired
    private IFieldDictService fieldDictService;

    private String rootStr = Introspector.decapitalize(UserEntity.class.getSimpleName());
    private Path<UserEntity> path = Expressions.path(UserEntity.class, rootStr);

    private TagExpressionVisitor visitor = new TagExpressionVisitor();

    @Override
    public Predicate visitBool(RqlParser.BoolContext ctx) {
        String domainName = ctx.AttrName().getText();
        FieldDataType dataType = getDataType(domainName);
        // 判断类型是否为布尔类型
        if (!FieldDataType.BOOL.equals(dataType)) {
            String errorMsg = String.format("attr %s expected type %s, passed value is %s",
                    domainName, dataType, FieldDataType.BOOL);
            logger.error(errorMsg);
            throw new UserCenterException(HttpStatus.BAD_REQUEST, TransactionErrorType.INVALID_REQUEST_PARAM, errorMsg);
        }

        Boolean fieldValue = Boolean.parseBoolean(ctx.BooleanLiteral().getText());
        Path<Boolean> name = Expressions.path(Boolean.class, path, fieldDictService.toFieldName(domainName, FieldType.USER));
        Expression<Boolean> value = Expressions.constant(fieldValue);

        switch (ctx.op.getType()) {
            case RqlParser.EQ:
                return Expressions.predicate(Ops.EQ, name, value);
            default:
                throw new ParserException("invalid operator " + ctx.op.toString());
        }
    }

    /**
     * {@inheritDoc}
     *
     * <p>
     * The default implementation returns the result of calling {@link #visitChildren} on
     * {@code ctx}.
     * </p>
     */
    @Override
    public Predicate visitStr(RqlParser.StrContext ctx) {
        String domainName = ctx.AttrName().getText();
        FieldDataType dataType = getDataType(domainName);
        // 判断类型字符串
        if (!FieldDataType.STRING.equals(dataType)) {
            String errorMsg = String.format("attr %s expected type %s, passed value is %s",
                    domainName, dataType, FieldDataType.STRING);
            logger.error(errorMsg);
            throw new UserCenterException(HttpStatus.BAD_REQUEST, TransactionErrorType.INVALID_REQUEST_PARAM, errorMsg);
        }

        String fieldValue = trimDoubleQuoteAndUnescape(ctx.StringLiteral().getText(), domainName);
        Path<String> name = Expressions.path(String.class, path, fieldDictService.toFieldName(domainName, FieldType.USER));
        Expression<String> value = Expressions.constant(fieldValue);

        switch (ctx.op.getType()) {
            case RqlParser.EQ:
                return Expressions.predicate(Ops.EQ, name, value);
            case RqlParser.NE:
                return Expressions.predicate(Ops.NE, name, value);
            case RqlParser.HAS:
                return Expressions.predicate(Ops.STRING_CONTAINS, name, value);
            case RqlParser.SW:
                return Expressions.predicate(Ops.STARTS_WITH, name, value);
            case RqlParser.EW:
                return Expressions.predicate(Ops.ENDS_WITH, name, value);
            default:
                throw new ParserException("invalud operator " + ctx.op.toString());
        }
    }

    private String trimDoubleQuoteAndUnescape(String fieldValue, String fieldName) {
        if (fieldValue == null) {
            throw new ParserException("Search value for " + fieldName + "can't be empty");
        }
        int length = fieldValue.length();
        if (length <= 2) {
            throw new ParserException("Search value for " + fieldName + "can't be empty");
        }
        StringBuffer tmp = new StringBuffer();

        for (int i = 0; i < length; i++) {
            char c = fieldValue.charAt(i);
            if (i == 0 || i == length - 1)
                continue;

            if (c == '\\') {
                char aheadChar = fieldValue.charAt(i + 1);
                if (aheadChar == '\\' || aheadChar == '"') {
                    tmp.append(aheadChar);
                    i++;
                }
            } else {
                tmp.append(c);
            }
        }
        return tmp.toString();
    }

    private FieldDataType getDataType(String domainName) {

        FieldDictEntity vo = fieldDictService.getByDomainName(domainName, FieldType.USER);
        if (vo == null) {
            String errorMsg = String.format("attr %s doesn't exist", domainName);
            logger.error(errorMsg);
            throw new UserCenterException(HttpStatus.BAD_REQUEST, TransactionErrorType.INVALID_REQUEST_PARAM, errorMsg);
        }

        if (TypeMapper.asInt(false).equals(vo.getSearchable())) {
            String errorMsg = String.format("attr %s exist, but not searchable in its definition", domainName);
            logger.error(errorMsg);
            throw new UserCenterException(HttpStatus.BAD_REQUEST, TransactionErrorType.INVALID_REQUEST_PARAM, errorMsg);
        }

        try {
            return TypeMapper.asEnum(vo.getDataType(), FieldDataType.class);
        } catch (Exception e) {
            String errorMsg = String.format("attr %s doesn't have a valid data type", domainName);
            logger.error(errorMsg);
            throw new UserCenterException(HttpStatus.BAD_REQUEST, TransactionErrorType.INVALID_REQUEST_PARAM, errorMsg);
        }

    }

    @Override
    public Predicate visitAnd(RqlParser.AndContext ctx) {
        Predicate left = visit(ctx.expr(0));
        Predicate right = visit(ctx.expr(1));
        return ExpressionUtils.allOf(left, right);
    }

    /**
     * {@inheritDoc}
     *
     * <p>
     * The default implementation returns the result of calling {@link #visitChildren} on
     * {@code ctx}.
     * </p>
     */
    @Override
    public Predicate visitOr(RqlParser.OrContext ctx) {
        Predicate left = visit(ctx.expr(0));
        Predicate right = visit(ctx.expr(1));
        return ExpressionUtils.anyOf(left, right);
    }

    /**
     * {@inheritDoc}
     *
     * <p>
     * The default implementation returns the result of calling {@link #visitChildren} on
     * {@code ctx}.
     * </p>
     */
    @Override
    public Predicate visitNot(RqlParser.NotContext ctx) {
        Predicate expr = visit(ctx.expr());
        return expr.not();
    }

    /**
     * {@inheritDoc}
     *
     * <p>
     * The default implementation returns the result of calling {@link #visitChildren} on
     * {@code ctx}.
     * </p>
     */
    @Override
    public Predicate visitParens(RqlParser.ParensContext ctx) {
        return visit(ctx.expr());
    }

    /**
     * {@inheritDoc}
     *
     * <p>
     * The default implementation returns the result of calling {@link #visitChildren} on
     * {@code ctx}.
     * </p>
     */
    @Override
    public Predicate visitInt(RqlParser.IntContext ctx) {
        String domainName = ctx.AttrName().getText();
        FieldDataType dataType = getDataType(domainName);
        if (!FieldDataType.INT.equals(dataType)
                && !FieldDataType.LONG.equals(dataType)) {
            String errorMsg = String.format("attr %s expected type %s, passed value is %s or %s",
                    domainName, dataType, FieldDataType.INT, FieldDataType.LONG);
            logger.error(errorMsg);
            throw new UserCenterException(HttpStatus.BAD_REQUEST, TransactionErrorType.INVALID_REQUEST_PARAM, errorMsg);
        }

        String ldapName = fieldDictService.toFieldName(domainName, FieldType.USER);

        Long fieldValue = Long.parseLong(ctx.IntegerLiteral().getText()) * 1000;

        Path<?> name = null;
        Expression<?> value = null;
        if (!LocalUserAttr.create_time.getFieldName().equals(ldapName) && !LocalUserAttr.update_time.getFieldName().equals(ldapName)) {
            name = Expressions.path(Long.class, path, ldapName);
            value = Expressions.constant(fieldValue);
        }

        switch (ctx.op.getType()) {
            case RqlParser.EQ:
                return Expressions.predicate(Ops.EQ, name, value);
            case RqlParser.NE:
                return Expressions.predicate(Ops.NE, name, value);
            case RqlParser.GT:
                return Expressions.predicate(Ops.GT, name, value);
            case RqlParser.GE:
                return Expressions.predicate(Ops.GOE, name, value);
            case RqlParser.LT:
                return Expressions.predicate(Ops.LT, name, value);
            case RqlParser.LE:
                return Expressions.predicate(Ops.LOE, name, value);
            default:
                throw new ParserException("invalud operator " + ctx.op.toString());
        }
    }

    private Predicate toPredicate(String query) {
        ANTLRInputStream input = new ANTLRInputStream(query);
        RqlLexer lexer = new RqlLexer(input);
        lexer.removeErrorListeners();
        RethrowParserExceptionListener listener = new RethrowParserExceptionListener();
        lexer.addErrorListener(listener);
        CommonTokenStream tokens = new CommonTokenStream(lexer);
        RqlParser parser = new RqlParser(tokens);
        parser.removeErrorListeners(); // remove ConsoleErrorListener
        parser.addErrorListener(listener); // add ours
        ParseTree tree = parser.expr(); // begin parsing at init rule

        return this.visit(tree);
    }

//    public Filter toFilter(String query){
//        try{
//            Predicate predicate = toPredicate(query);
//            return visitor.handle(predicate);
//        } catch (ParserException e){
//            logger.error(CommonConstants.BLANK, e);
//            throw new UserCenterException(HttpStatus.BAD_REQUEST, TransactionErrorType.INVALID_REQUEST_PARAM, e.getMessage());
//        } catch (UserCenterException e){
//            throw e;
//        } catch (Exception e){
//            logger.error(CommonConstants.BLANK, e);
//            throw new UserCenterException(HttpStatus.BAD_REQUEST, TransactionErrorType.INVALID_REQUEST_PARAM, "invalid tag expression");
//        }
//    }
}
