package com.cyberscraft.uep.iam.service.data;

import com.cyberscraft.uep.account.client.domain.Connector;
import com.cyberscraft.uep.account.client.domain.ConnectorUser;
import com.cyberscraft.uep.iam.entity.ExternalUserEntity;
import com.cyberscraft.uep.iam.entity.UserEntity;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/***
 *
 * @date 2021/6/15
 * <AUTHOR>
 ***/
public interface IPushSnsUserService {

    /***
     * 删除连接器对应的第三方平台用户记录
     * @param id
     */
    void removeByConnector(Long id);

    /***
     *
     * @param snsUserId
     * @param type
     * @param attrs
     * @return
     */
    ExternalUserEntity getSnsUserBySnsUserIdAndType(String snsUserId, Integer type, String... attrs);

    /***
     *
     * @param snsUserIds
     * @param type
     * @param attrs
     * @return
     */
    List<ExternalUserEntity> getSnsUserBySnsUserIdAndType(List<String> snsUserIds, Integer type, String... attrs);

    /***
     *
     * @param userId
     * @param type
     * @param attrs
     * @return
     */
    ExternalUserEntity getSnsUserByUserAndType(Long userId, Integer type, String... attrs);

    /***
     *
     * @param userIds
     * @param type
     * @param attrs
     * @return
     */
    List<ExternalUserEntity> getSnsUserByUserAndType(List<Long> userIds, Integer type, String... attrs);


    /**
     * 保存外部用户同步结果
     * @param snsUserEntity
     * @param externalUser
     * @param user
     * @param connector
     * @param profile
     * @param localOrgIds
     */
    void saveSnsUserResult(ExternalUserEntity snsUserEntity, ConnectorUser externalUser, UserEntity user, String localUserSource, Connector connector, String profile, Collection<Long> localOrgIds, List<String> roles);

    /***
     *
     * @param connectorId
     * @param userId
     * @return
     */
    ExternalUserEntity getSnsUserByConnector(Long connectorId, Long userId);

    /***
     * 查询用户对应的所有的第三方平台用户信息列表
     * @param userId
     * @return
     */
    List<ExternalUserEntity> getSnsUsersByUserId(Long userId);

    /**
     * 判断该链接器是否已同步了用户
     * @param connectorId
     * @return
     */
    Boolean hasSnsUsers(Long connectorId);

    /***
     *
     * @param connectorId
     * @param userIds
     * @return
     */
    Map<Long, ExternalUserEntity> getSnsUsersMapByConnectorAndUserId(Long connectorId, List<Long> userIds);

    /***
     *
     * @param ConnectorId
     * @param userIds
     * @return
     */
    Map<Long, String> getSnsUserIdsMapByConnectorAndUserId(Long ConnectorId, List<Long> userIds);

    /****
     *
     * @param obj
     */
    void add(ExternalUserEntity obj);

    /***
     *
     * @param obj
     */
    void modify(ExternalUserEntity obj);

    /***
     *
     * @param id
     */
    void remove(Long id);
}
