package com.cyberscraft.uep.iam.common.util;

import com.maxmind.geoip2.DatabaseReader;
import com.maxmind.geoip2.exception.GeoIp2Exception;
import com.maxmind.geoip2.model.CityResponse;
import com.maxmind.geoip2.record.City;
import com.maxmind.geoip2.record.Country;
import com.maxmind.geoip2.record.Location;
import com.maxmind.geoip2.record.Subdivision;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.net.InetAddress;
import java.util.Date;
import java.util.concurrent.TimeUnit;

public class GeoUtil {
    private static final Logger logger = LoggerFactory.getLogger(GeoUtil.class);

    public static DatabaseReader reader = null;

    public static class GeoLocation implements Comparable {
        private Long timestamp = TimeUnit.MILLISECONDS.toSeconds(new Date().getTime());
        private String ip;
        private String countryCode;
        private String countryName;
        private String regionCode;
        private String regionName;
        private String city;
        private float latitude;
        private float longitude;

        public Long getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(Long timestamp) {
            this.timestamp = timestamp;
        }

        public String getIp() {
            return ip;
        }

        public void setIp(String ip) {
            this.ip = ip;
        }

        public String getCountryCode() {
            return countryCode;
        }

        public void setCountryCode(String countryCode) {
            this.countryCode = countryCode;
        }

        public String getCountryName() {
            return countryName;
        }

        public void setCountryName(String countryName) {
            this.countryName = countryName;
        }

        public String getRegionCode() {
            return regionCode;
        }

        public void setRegionCode(String regionCode) {
            this.regionCode = regionCode;
        }

        public String getRegionName() {
            return regionName;
        }

        public void setRegionName(String regionName) {
            this.regionName = regionName;
        }

        public String getCity() {
            return city;
        }

        public void setCity(String city) {
            this.city = city;
        }

        public float getLatitude() {
            return latitude;
        }

        public void setLatitude(float latitude) {
            this.latitude = latitude;
        }

        public float getLongitude() {
            return longitude;
        }

        public void setLongitude(float longitude) {
            this.longitude = longitude;
        }

        @Override
        public int compareTo(Object o) {
            GeoLocation location = (GeoLocation)o;

            Long timestamp = location.getTimestamp();

            return this.getTimestamp().compareTo(timestamp);
        }
    }

     public static GeoLocation getLocationByIpAddr(String ipAddr) {
         if (reader == null) {
             try {
                 InputStream isData = GeoUtil.class.getResourceAsStream("/geo/GeoLite2-City.mmdb");
                 if(isData == null) {
                     logger.error("Fail to get geo resource file");
                     return null;
                 }
                 reader = new DatabaseReader.Builder(isData).build();
                 isData.close();
             } catch (IOException e) {
                 logger.error("" + e);
                 return null;
             }
         }

         try {
             InetAddress ipAddress = InetAddress.getByName(ipAddr);
             CityResponse response = reader.city(ipAddress);
             Country country = response.getCountry();
             Subdivision subdivision = response.getMostSpecificSubdivision();
             City city = response.getCity();
             Location location = response.getLocation();

             GeoLocation geoLocation = new GeoLocation();
             geoLocation.setIp(ipAddr);
             geoLocation.setCountryCode(country.getIsoCode());
             geoLocation.setCountryName(country.getName());
             geoLocation.setRegionCode(subdivision.getIsoCode());
             geoLocation.setRegionName(subdivision.getName());
             geoLocation.setCity(city.getName());
             geoLocation.setLatitude(location.getLatitude().floatValue());
             geoLocation.setLongitude(location.getLongitude().floatValue());
             return geoLocation;

         }catch (GeoIp2Exception e) {
             logger.error("GeoIp2: " + e);
         } catch (IOException e) {
             logger.error("IOException: " + e);
         }

        return null;
    }

    public static void main(String[] args) {
        GeoLocation geoLocation = GeoUtil.getLocationByIpAddr("**************");
        System.out.println(
                "countryCode: " + geoLocation.getCountryCode() +"\n"+
                        "countryName: " + geoLocation.getCountryName() +"\n"+
                        "regionCode: " + geoLocation.getRegionCode() +"\n"+
                        "regionName: " + geoLocation.getRegionName() +"\n"+
                        "city: " + geoLocation.getCity() +"\n"+
                        "latitude: " + geoLocation.getLatitude() +"\n"+
                        "longitude: " + geoLocation.getLongitude());

    }
}
