package com.cyberscraft.uep.iam.service;


import com.cyberscraft.uep.common.dto.QueryPage;
import com.cyberscraft.uep.iam.common.exception.AppException;
import com.cyberscraft.uep.iam.common.exception.UserCenterException;
import com.cyberscraft.uep.iam.dto.domain.AppGatewayConfig;
import com.cyberscraft.uep.iam.dto.request.*;
import com.cyberscraft.uep.iam.dto.request.login.LoginMfaPolicyDTO;
import com.cyberscraft.uep.iam.dto.request.login.LoginPolicyDTO;
import com.cyberscraft.uep.iam.dto.request.login.WhitelistScene;
import com.cyberscraft.uep.iam.dto.request.sso.SamlConfig;
import com.cyberscraft.uep.iam.dto.response.*;
import com.cyberscraft.uep.iam.dto.response.configs.SnsConfigVO;
import com.cyberscraft.uep.iam.dto.response.login.LoginPolicyVO;
import com.cyberscraft.uep.iam.entity.AppEntity;
import com.cyberscraft.uep.iam.entity.SnsConfigEntity;
import com.cyberscraft.uep.iam.service.oidc.domain.DefaultBaseClientDetails;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * <p>应用服务业务接口</p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2019-07-09 13:50
 */
public interface IAppService {
    String TRUST_ALL_PEERS_WILDCARD = "*";

    /****************************************************APP应用接口**************************************************************/
    /**
     * 应用列表
     *
     * @param appType
     * @param status
     * @return
     */
    AppListVO searchApp(String appType, String status,String inner_category,String support_protocol,String filter, List<String> clientIds);

    /**
     * 管理员创建应用
     *
     * @param appBaseInfoVO 应用的基本信息
     * @return 应用的详细信息
     */
    AppDetailsVO createAppBaseInfo(AppBaseInfoVO appBaseInfoVO);

    /**
     * 管理员修改应用
     *
     * @param appBaseInfoVO 应用的基本信息
     * @return 应用的详细信息
     */
    AppDetailsVO updateAppBaseInfo(AppBaseInfoVO appBaseInfoVO);

    /**
     * 更新SSO信息
     *
     * @param clientId     应用ID
     * @param appSSOInfoVO 单点基本配置信息
     * @return
     */
    AppDetailsVO updateAppSSOInfo(String clientId, AppSSOInfoVO appSSOInfoVO);

    /**
     * 设置应用认证策略
     *
     * @param clientId
     * @param loginPolicyDTO
     */
    Void setLoginPolicy(String clientId, LoginPolicyDTO loginPolicyDTO);

    /**
     * 预览SAML assertion
     *
     * @param issuer
     * @param samlConfig
     * @return
     */
    String previewAssertion(String issuer, SamlConfig samlConfig);

    /**
     * 根据应用ID查询应用详细信息
     *
     * @param clientId
     * @return
     */
    AppDetailsVO getAppDetails(String clientId);

    /**
     * 根据应用ID查询应用详细信息
     *
     * @param clientId
     * @return
     */
    AppDetailsVO getAppDetailsWithTenantPerm(String clientId);

    /**
     * 通过应用密钥、应用ID和授权方式 判断应用是否存在
     *
     * @param clientId   应用ID
     * @param clientInfo 应用信息
     * @return
     */
    boolean isExistApp(String clientId, Map<String, String> clientInfo);

    /**
     * 根据应用ID删除应用
     *
     * @param clientId
     * @throws AppException
     */
    AppDetailsVO deleteApp(String clientId) throws AppException;

    /**
     * 根据应用ID更新应用
     *
     * @param clientId
     * @param appUpdateInVO
     * @return
     */
    AppDetailsVO updateApp(String clientId, AppUpdateInVO appUpdateInVO);

    /**
     * 重新生成应用密钥
     *
     * @param clientId
     * @return
     */
    AppSecretRotationVO rotateClientSecret(String clientId);

    @Transactional(rollbackFor = Exception.class)
    AppSignatureSecretRotationVO rotateClientSignatureSecret(String clientId);

    /**
     * 修改APP的状态
     *
     * @param clientId
     * @param status
     */
    AppDetailsVO changeStatus(String clientId, String status);

    /**
     * 更新应用的公开访问配置
     *
     * @param clientId
     * @param appPublicAccessUpdateInVO
     * @return
     */
    Void updateAppPublicAccess(String clientId, AppPublicAccessUpdateInVO appPublicAccessUpdateInVO);

    /**
     * 获取应用的签名证书
     *
     * @param clientId
     * @param format
     * @return
     */
    String getAppPublicKey(String clientId, String format);

    /**
     * 设置应用是否加入或退出授权白名单
     *
     * @param clientId
     * @param whitelisted
     */
    void changeWhitelisted(String clientId, boolean whitelisted);

    /**
     * app检查
     * 1、不能是UC
     * 2、在数据库中必须存在
     * 3、不能是cli类型
     *
     * @param clientId
     * @return
     */
    AppEntity clientMustExistsAndNotInternalAndNotCli(String clientId);


    /**
     * app检查
     * 1、在数据库中必须存在
     * 2、不能是cli类型
     *
     * @param clientId
     * @return
     */
    AppEntity clientMustExistsAndNotCli(String clientId);

    AppEntity clientMustExists(String clientId);


    /****************************************************APP对组织结构授权接口**************************************************************/
    /**
     * 给单个组织分配应用
     *
     * @param clientId
     * @param orgId
     * @return
     */
    Void entitleOrg(String clientId, String orgId);

    /**
     * 从应用中已分配的组织结构列表中移除单个组织结构
     *
     * @param clientId
     * @param orgId
     * @return
     */
    Void revokeOrgEntitlement(String clientId, String orgId);

    /**
     * 查询应用已经分配的组织结构树
     *
     * @param clientId
     * @return
     */
    Map<String, Object> listEntitledOrgTree(String clientId);


    /****************************************************APP对用户授权接口**************************************************************/
    /**
     * 给单个用户分配应用
     *
     * @param clientId
     * @param username
     * @return
     */
    Void entitleUser(String clientId, String username);

    Void entitleUser(String clientId, String username, String start_date, String end_date);


    /**
     * 给多个用户分配应用
     *
     * @param clientId
     * @param addUsersToAppInVO
     * @return
     */
    BatchOperationVO<String> entitleUsers(String clientId, AddUsersToAppInVO addUsersToAppInVO);

    /**
     * 从应用中已分配的用户列表中移除多个用户
     *
     * @param clientId
     * @param removeUsersFromAppInVO
     * @return
     */
    BatchOperationVO<String> revokeUsersEntitlement(String clientId, RemoveUsersFromAppInVO removeUsersFromAppInVO);

    /**
     * 从应用中已分配的用户列表中移除单个用户
     *
     * @param clientId
     * @param username
     * @return
     */
    Void revokeUserEntitlement(String clientId, String username);

    /**
     * 查询应用授权的用户列表
     *
     * @param clientId
     * @param filterStr
     * @param queryPage
     * @param requestAttrs
     * @return
     */
    QueryPage<Map<String, Object>> listEntitledUsers(String clientId, String searchField, String filterStr, QueryPage<Map<String, Object>> queryPage, String[] requestAttrs);

    /**
     * 查询某个用户的所有应用
     *
     * @param username
     * @return
     */
    ApplicationListVO getApplicationsByUsername(String username);

    /**
     * 查询某个用户可见的应用列表
     *
     * @param username
     * @param searchAppName 搜索应用名称
     * @param categoryId    应用分类id
     * @return
     */
    ApplicationListVO getVisibleAppsByUsername(String username, String searchAppName, String categoryId);

    /**
     * 根据clientId 集合获取应用信息对象
     *
     * @param clientIds 应用ID集合
     * @return 应用ID对应的应用信息
     */
    ApplicationListVO getApplicationsByClientIds(List<String> clientIds);


    /****************************************************APP对标签授权接口**************************************************************/
    /**
     * 给多个标签分配应用
     *
     * @param clientId
     * @param entitleOrRevokeTagsInVO
     * @return
     */
    BatchOperationVO<String> entitleTags(String clientId, EntitleOrRevokeTagsInVO entitleOrRevokeTagsInVO);

    /**
     * 从应用中已分配的标签列表中移除多个标签
     *
     * @param clientId
     * @param entitleOrRevokeTagsInVO
     * @return
     */
    BatchOperationVO<String> revokeTagsEntitlement(String clientId, EntitleOrRevokeTagsInVO entitleOrRevokeTagsInVO);

    /**
     * 查询应用授权的标签列表
     *
     * @param clientId
     * @param filterStr
     * @return
     */
    List<TagVO> listEntitledTags(String clientId, String filterStr);

    boolean userAllowToUseApp(String username, DefaultBaseClientDetails authenticatedClient);

    /**
     * 检查clientId是否信任trustClientId
     *
     * @param clientId
     * @param trustClientId
     */
    void checkClientIsTrustToApp(String clientId, String trustClientId);

    /**
     * 查询应用被授权的租户列表
     *
     * @param clientId
     * @return
     */
    List<SysTenantVo> getAssignedTenants(String clientId);

    /**
     * 为应用分配可使用的租户
     *
     * @param clientId
     * @param dto
     * @return
     */
    Void setAssignedTenants(String clientId, AppTenantListDto dto);

    /**
     * 检查应用对用户是否有权限
     *
     * @param clientId
     * @param sub
     * @return
     */
    boolean checkAppUserPermission(String clientId, String sub);

    /**
     * 检查应用对用户是否有权限
     *
     * @param clientId
     * @return
     */
    boolean checkAppUserPermissionByUserName(String clientId, String username);

    List<ThirdPartyAppVO> syncAppsByCrop(long snsId);

    void updateGatewayThirdPartyInfo(SnsConfigEntity snsConfigEntity);

    SnsConfigVO saveThirdPartyApps(ThirdPartyAppListVO thirdPartyAppListVO);

    QueryPage<AppBriefVO> searchApp(AppsQueryVo appsQueryVo, List<String> queryClientIds);

    QueryPage<AppBriefVO> searchAppStore(String clientName, String category, List<String> protocol, Integer featureApp, Integer page, Integer size);

    //boolean saveOrUpdateGateway(String clientId, String accessPrefix, String routeUrl, int status,int predicateType);
    boolean saveOrUpdateGateway(AppGateWayDto appGateWayDto);

    AppEntity processCreateInVo(AppCreateInVOV2 appCreateInVO);

    AppGateWayVo loadGateWay(String clientId);

    void evictCache(String clientId);

    void evictKeyPairCache(String clientId);

    /**
     * 获取应用关联的用户画像详情
     *
     * @param clientId
     * @return
     * @throws UserCenterException
     */
    UserProfileDetailVO getAttachedProfileDetail(String clientId) throws UserCenterException;

    /**
     * 为应用关联用户画像
     *
     * @param clientId
     * @param profileId
     */
    void attachUserProfile(String clientId, Long profileId);

    List<AppBaseInfoVO> listAppBaseInfo();

    void validateAppTenant(AppEntity appEntity);

    /**
     * 获取关联应用
     *
     * @param appEntity
     * @return
     */
    List<AppEntity> getLinkAppEntity(AppEntity appEntity);

    /**
     * 获取应用模板关联应用数量
     *
     * @param clientId 模板clientId
     * @return 数量
     */
    Integer getLinkAppCount(String clientId);

    /**
     * 给应用分配接口访问权限
     * @param clientId 应用id
     * @param dto 权限配置
     */
    Void entitleInterface(String clientId, AppInterfaceDto dto);

    /**
     * 获取应用关联的所有权限和权限组
     * @param clientId 应用id
     * @return 权限和权限组
     */
    AppInterfaceDto getAppPermission(String clientId);

    /**
     * 获取应用关联的权限组
     * @param clientId 应用id
     * @return 权限组
     */
    AppInterfaceDto getAppPermissionSets(String clientId);

    /**
     * 判断应用是否拥有接口访问权限
     * @param appPermission 应用接口权限
     * @param permission 接口权限
     * @return true:拥有权限，false:没有权限
     */
    boolean hasPermission(AppInterfaceDto appPermission, String permission);


    /**
     * 获取应用登录策略
     * @param clientId 应用id
     * @return 登录策略
     */
    LoginPolicyVO getLoginPolicy(String clientId);

    /**
     * 获取应用登录白名单
     * @param clientId 应用id 
     * @param scene 场景
     * @return 白名单
     */
    LoginWhitelistVO getLoginWhitelist(String clientId, WhitelistScene scene);

    /**
     * 更新应用登录白名单
     * @param vo 白名单信息
     */
    Void saveOrUpdateLoginWhitelist(LoginWhitelistVO vo);


    /**
     * 设置应用登录策略
     * @param clientId 应用id
     * @param loginMfaPolicyDTO 设置策略
     * @return
     */
    Void updateLoginMfaPolicy(String clientId, LoginMfaPolicyDTO loginMfaPolicyDTO);

    /**
     * 获取应用登录策略
     * @param clientId 应用id
     * @return 策略信息
     */
    LoginMfaPolicyDTO getLoginMfaPolicy(String clientId);

    AppGatewayConfig appGatewayConfig(AppGatewayConfig appGatewayConfig);

    AppGatewayConfig getGatewayConfig(String clientId);

    Void delGatewayConfig(String clientId);
}
