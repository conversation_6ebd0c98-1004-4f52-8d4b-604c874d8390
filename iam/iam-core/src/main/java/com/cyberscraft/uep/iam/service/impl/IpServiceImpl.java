package com.cyberscraft.uep.iam.service.impl;

import com.alibaba.cloudapi.sdk.constant.SdkConstant;
import com.alibaba.cloudapi.sdk.model.ApiResponse;
import com.alibaba.cloudapi.sdk.model.HttpClientBuilderParams;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.common.util.SnowflakeIDUtil;
import com.cyberscraft.uep.iam.common.constants.ThreadPoolNameConstant;
import com.cyberscraft.uep.iam.common.util.aliyun.cz88.HttpsApiClientCZ88_GEO;
import com.cyberscraft.uep.iam.dbo.IpDBO;
import com.cyberscraft.uep.iam.entity.IpEntity;
import com.cyberscraft.uep.iam.service.IIpService;
import com.cyberscraft.uep.iam.service.oidc.util.SecureRequestCheck;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;

/**
 * <AUTHOR>
 * @Date 2024/9/9 10:17
 * @Version 1.0
 * @Description ip服务接口
 */
@Service
@RefreshScope
public class IpServiceImpl implements IIpService {

    private static final Logger logger = LoggerFactory.getLogger(IpServiceImpl.class);

    @Value("${third.tool.ip.key:test}")
    private String appKey;

    @Value("${third.tool.ip.secret:test}")
    private String appSecret;

    @Resource
    private IpDBO ipDBO;

    @Resource(name = ThreadPoolNameConstant.COMMON_POOL_NAME)
    private ExecutorService commonPoolExecutor;


    @Override
    public IpEntity getGeoInfo(String ip) {
        IpEntity ipEntity = ipDBO.getGeoInfo(ip);
        if (ipEntity == null) {
            ipEntity = getIpDataFromThirdParty(ip);
            IpEntity finalIpEntity = ipEntity;
            // 异步保存数据
            CompletableFuture.runAsync(() -> saveIp(finalIpEntity), commonPoolExecutor);
        }
        return ipEntity;
    }

    @Override
    public void saveIp(IpEntity ipEntity) {
        if (ipEntity == null) {
            return;
        }
        if (ipEntity.getId() == null) {
            ipEntity.setId(SnowflakeIDUtil.getId());
        }
        String username = StringUtils.isBlank(SecureRequestCheck.getUsername()) ? "system" : SecureRequestCheck.getUsername();
        ipEntity.setUpdateBy(username);
        ipEntity.setCreateBy(username);
        LocalDateTime now = LocalDateTime.now();
        ipEntity.setCreateTime(now);
        ipEntity.setUpdateTime(now);
        ipDBO.save(ipEntity);
    }

    @Override
    public void updateById(IpEntity ipEntity) {
        ipDBO.updateById(ipEntity);
    }

    @Override
    public List<IpEntity> getGeoInfoByUpdateMoreThanDays(int days) {
        return ipDBO.getGeoInfoByUpdateMoreThanDays(days);
    }


    @Override
    public IpEntity getIpDataFromThirdParty(String ip) {
        HttpClientBuilderParams httpsParam = new HttpClientBuilderParams();
        httpsParam.setAppKey(appKey);
        httpsParam.setAppSecret(appSecret);
        HttpsApiClientCZ88_GEO.getInstance().init(httpsParam);
        ApiResponse response = HttpsApiClientCZ88_GEO.getInstance().postSyncMode(ip, null);
        Map<String, Object> data = getResultString(response);
        data.putAll((Map<String, String>) data.get("geoCenter"));
        return JsonUtil.convert(data, IpEntity.class);
    }


    private static Map<String, Object> getResultString(ApiResponse response) {
        StringBuilder result = new StringBuilder();
        if (response.getCode() != 200) {
            result.append("Error description:").append(response.getHeaders().get("X-Ca-Error-Message")).append(SdkConstant.CLOUDAPI_LF).append(SdkConstant.CLOUDAPI_LF);
            logger.error("调用纯真IP接口失败:{}", result);
        }
        String bodyStr = new String(response.getBody(), SdkConstant.CLOUDAPI_ENCODING);
        Map<String, Object> respBody = JsonUtil.str2Map(bodyStr);
        if (!respBody.get("code").toString().equals("200")) {
            logger.error("调用纯真IP接口失败:{}", respBody.get("message"));
        }
        System.out.println(bodyStr);
        return (Map<String, Object>) respBody.get("data");
    }


}
