package com.cyberscraft.uep.iam.common.correlation;

import java.util.Set;

public class InScopeOrgs {

    private static final ThreadLocal<Set<String>> orgIds = new ThreadLocal<Set<String>>();

    public static Set<String> get() {
        return orgIds.get();
    }

    public static void set(Set<String> orgIds) {
        InScopeOrgs.orgIds.set(orgIds);
    }
    
    public static void clear() {
        orgIds.remove();
    }
}
