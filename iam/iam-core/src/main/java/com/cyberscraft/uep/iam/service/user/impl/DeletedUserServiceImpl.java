package com.cyberscraft.uep.iam.service.user.impl;

import com.cyberscraft.uep.iam.common.constants.ThreadPoolNameConstant;
import com.cyberscraft.uep.iam.dbo.DeletedUserDBO;
import com.cyberscraft.uep.iam.service.user.IDeletedUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.ExecutorService;

/***
 *
 * @date 2021/10/15
 * <AUTHOR>
 ***/
@Service
public class DeletedUserServiceImpl implements IDeletedUserService {

    @Resource(name = ThreadPoolNameConstant.COMMON_POOL_NAME)
    private ExecutorService commonPoolExcutor;

    @Resource
    private DeletedUserDBO deletedUserDBO;

    private final static Logger LOG = LoggerFactory.getLogger(DeletedUserServiceImpl.class);

    @Override
    public void onUserCreateOrUpdate(Long id) {
        deletedUserDBO.removeById(id);
//        String tenantId = TenantHolder.getTenantCode();
//        commonPoolExcutor.submit(() -> {
//            try {
//                TenantHolder.setTenantCode(tenantId);
//                deletedUserDBO.removeById(id);
//                if (userAttrs != null) {
//                    String loginName = (String) userAttrs.get(LocalUserAttr.username.getFieldName());
//                    String mobile = (String) userAttrs.get(LocalUserAttr.phone_number.getFieldName());
//                    if (StringUtils.isNotBlank(loginName) || StringUtils.isNotBlank(mobile)) {
//                        deletedUserDBO.removeByUserNameAndMobile(loginName, mobile);
//                    }
//                }
//                //String loginName = userAttrs != null && userAttrs.get(LocalUserAttr.username.getFieldName())!=null ? String.valueOf(userAttrs.get(LocalUserAttr.username.getFieldName())) : null;
//
//            } catch (Exception e) {
//                LOG.warn("delete the deleted user has error:{}", e.getMessage());
//            }
//        });
    }
}