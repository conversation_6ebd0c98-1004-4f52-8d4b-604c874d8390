package com.cyberscraft.uep.iam.common.exception;

import com.cyberscraft.uep.iam.errors.TransactionErrorType;
import com.cyberscraft.uep.iam.dto.domain.ImportErrorDomain;
import org.springframework.http.HttpStatus;

import java.util.List;
import java.util.Map;

public class ImportException extends UserCenterException {

    private static final long serialVersionUID = 1291654460131850656L;

    public static final HttpStatus DEFAULT_STATUS = HttpStatus.BAD_REQUEST;
    public static final TransactionErrorType DEFAULT_TRANSACTION_ERROR = TransactionErrorType.IMPORT_FILE_CSV_ERROR;
    public static final String CONNECTOR_SYNC_IS_RUNING = "connector sync is running,cannot trigger duplicate sync";
    private Map<String, List<ImportErrorDomain>> csvErrors;
    /**
     * Import user count:
     * if value equals -1(initial value), the import processing should be work with csv upload manner.
     * if value granter or equals 0, the import processing should be work with datasource import manner.
     */
    private int importedCount = -1;

    public ImportException(Throwable t) {
        super(t);
    }

    public ImportException() {
        super(DEFAULT_STATUS, DEFAULT_TRANSACTION_ERROR);
    }

    public ImportException(HttpStatus httpStatus, TransactionErrorType errorType) {
        super(httpStatus, errorType);
    }

    public ImportException(TransactionErrorType errorType) {
        super(DEFAULT_STATUS, errorType);
    }

    public ImportException(TransactionErrorType errorType, String desc) {
        super(DEFAULT_STATUS, errorType, desc);
    }

    public ImportException(String desc) {
        super(DEFAULT_STATUS, DEFAULT_TRANSACTION_ERROR, desc);
    }

    public ImportException(HttpStatus httpStatus, TransactionErrorType transactionError, String custDesc) {
        super(httpStatus, transactionError, custDesc);
    }

    public ImportException(TransactionErrorType errorType, Map<String, List<ImportErrorDomain>> csvErrors) {
        super(DEFAULT_STATUS, errorType);
        this.csvErrors = csvErrors;
    }

    public ImportException(TransactionErrorType errorType, Map<String, List<ImportErrorDomain>> csvErrors, int importedCount) {
        super(DEFAULT_STATUS, errorType);
        this.csvErrors = csvErrors;
        this.importedCount = importedCount;
    }

    public Map<String, List<ImportErrorDomain>> getCsvErrors() {
        return csvErrors;
    }


    public int getImportedCount() {
        return importedCount;
    }
}
