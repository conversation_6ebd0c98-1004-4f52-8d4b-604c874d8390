package com.cyberscraft.uep.iam.service.transfer;

import com.cyberscraft.uep.common.domain.DataTypeEnum;
import com.cyberscraft.uep.common.dto.QueryPage;
import com.cyberscraft.uep.iam.dto.enums.CreatedMode;
import com.cyberscraft.uep.iam.dto.enums.FieldDataType;
import com.cyberscraft.uep.iam.dto.enums.FieldType;
import com.cyberscraft.uep.iam.dto.request.AttrCreateInVO;
import com.cyberscraft.uep.iam.dto.request.UserAttrCreateInVO;
import com.cyberscraft.uep.iam.dto.response.FieldDictVO;
import com.cyberscraft.uep.iam.entity.FieldDictEntity;
import com.cyberscraft.uep.iam.query.FieldDictQueryDto;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.InheritConfiguration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Mapper(componentModel = "spring", uses = TypeMapper.class)
public abstract class FieldDictTransfer {

    @Autowired
    TypeMapper typeMapper;

    @Mappings({
            @Mapping(source = "createVo.displayName", target = "displayName"),
            @Mapping(source = "createVo.domainName", target = "domainName"),
            @Mapping(source = "createVo.dataType", target = "dataType"),
            @Mapping(target = "uniqueAble", expression = "java(TypeMapper.asInt(false))"),
            @Mapping(source = "createVo.mandatory", target = "mandatory"),
//			@Mapping(target = "searchable", expression = "java(TypeMapper.asInt(false))"),
            @Mapping(source = "createVo.opConstraint", target = "opConstraint"),
            @Mapping(source = "createVo.constraintRule", target = "validateRule"),
            @Mapping(source = "createVo.description", target = "description"),
            @Mapping(source = "createVo.asImport", target = "importAble"),
            @Mapping(source = "fieldType", target = "fieldType"),
            @Mapping(target = "createTime", expression = "java(java.time.LocalDateTime.now())"),
            @Mapping(target = "fieldName", expression = "java(\"ex_\"+createVo.getDomainName())"),
            @Mapping(source = "createVo.singleValue", target = "singleValue"),
    })
    public abstract FieldDictEntity dto2entity(AttrCreateInVO createVo, FieldType fieldType);

    @Mappings({
            @Mapping(source = "createVo.extraAuthFactor", target = "loginFactor", defaultValue = "0"),
            @Mapping(source = "createVo.asClaim", target = "asClaim", defaultValue = "0"),
            @Mapping(source = "createVo.asProfile", target = "asProfile", defaultValue = "0"),
    })
    @InheritConfiguration(name = "dto2entity")
    public abstract FieldDictEntity userCreateDto2entity(UserAttrCreateInVO createVo, FieldType fieldType);

    public List<FieldDictEntity> dtos2entities(List<? extends AttrCreateInVO> dtos, FieldType fieldType) {
        if (dtos == null || dtos.size() == 0) {
            return Collections.emptyList();
        }

        List<FieldDictEntity> entities = new ArrayList<>(dtos.size());
        for (AttrCreateInVO dto : dtos) {
            if (FieldDataType.DATETIME.equals(dto.getDataType()) && StringUtils.isBlank(dto.getConstraintRule())) {
                dto.setConstraintRule("yyyy-MM-dd HH:mm:ss");
            }
            if (dto instanceof UserAttrCreateInVO) {
                UserAttrCreateInVO userAttrCreateInVO = (UserAttrCreateInVO) dto;
                entities.add(userCreateDto2entity(userAttrCreateInVO, fieldType));
            } else {
                entities.add(dto2entity(dto, fieldType));
            }
        }
        return entities;
    }

    public static boolean isBasic(FieldDictEntity entity) {
        return TypeMapper.asEnum(entity.getCreateMod(), CreatedMode.class) == CreatedMode.BY_SYSTEM;
    }

    public DataTypeEnum from(FieldDataType fieldDataType) {
        switch (fieldDataType) {
            case INT:
            case LONG:
            case FLOAT:
                return DataTypeEnum.NUMBER;
            case BLOB:
                return DataTypeEnum.BOOLEAN;
            default:
                return DataTypeEnum.STRING;
        }
    }

    @Mappings({
            @Mapping(source = "loginFactor", target = "extraAuthFactor"),
            @Mapping(source = "uniqueAble", target = "unique"),
            @Mapping(source = "updateAble", target = "update"),
            @Mapping(source = "validateRule", target = "constraintRule"),
            @Mapping(source = "importAble", target = "asImport"),
            @Mapping(target = "basicAttribute", expression = "java(com.cyberscraft.uep.iam.service.transfer.FieldDictTransfer.isBasic(entity))"),
    })
    public abstract FieldDictVO entity2dto(FieldDictEntity entity);

    public abstract List<FieldDictVO> entities2dtos(List<FieldDictEntity> entities);

    public abstract QueryPage<FieldDictVO> entities2dtos(QueryPage<FieldDictEntity> pages);

    public abstract QueryPage<FieldDictEntity> dtos2entities(QueryPage<FieldDictVO> pages);

    @Mappings({
            @Mapping(source = "extraAuthFactor", target = "loginFactor"),
            @Mapping(source = "filter", target = "domainName"),
            @Mapping(source = "filter", target = "displayName"),
            @Mapping(source = "filter", target = "description"),
            @Mapping(source = "asImport", target = "importAble"),
    })
    public abstract FieldDictEntity dto2entity(FieldDictQueryDto dto);
}
