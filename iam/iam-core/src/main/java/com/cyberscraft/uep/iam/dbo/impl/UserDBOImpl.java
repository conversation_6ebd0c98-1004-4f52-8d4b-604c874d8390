package com.cyberscraft.uep.iam.dbo.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cyberscraft.uep.common.dto.IQueryConditonApply;
import com.cyberscraft.uep.common.dto.PageView;
import com.cyberscraft.uep.common.dto.Pagination;
import com.cyberscraft.uep.common.dto.QueryPage;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.common.util.PagingUtil;
import com.cyberscraft.uep.common.util.SqlUtil;
import com.cyberscraft.uep.iam.common.domain.DynamicExpressDto;
import com.cyberscraft.uep.iam.common.enums.LocalOrgAttr;
import com.cyberscraft.uep.iam.common.enums.LocalUserAttr;
import com.cyberscraft.uep.iam.common.util.UserUtil;
import com.cyberscraft.uep.iam.constants.CommonConstants;
import com.cyberscraft.uep.iam.dao.UserDao;
import com.cyberscraft.uep.iam.dao.UserOrgDao;
import com.cyberscraft.uep.iam.dbo.FieldDictDBO;
import com.cyberscraft.uep.iam.dbo.OrgDBO;
import com.cyberscraft.uep.iam.dbo.UserDBO;
import com.cyberscraft.uep.iam.dto.enums.CreatedMode;
import com.cyberscraft.uep.iam.dto.enums.UserPWDStatus;
import com.cyberscraft.uep.iam.dto.enums.UserStatus;
import com.cyberscraft.uep.iam.dto.request.UserSearchDto;
import com.cyberscraft.uep.iam.dto.scim.filter.OperatorVariables;
import com.cyberscraft.uep.iam.entity.FieldDictEntity;
import com.cyberscraft.uep.iam.entity.UserEntity;
import com.cyberscraft.uep.iam.entity.UserOrgEntity;
import com.cyberscraft.uep.iam.query.UserQueryCondition;
import com.cyberscraft.uep.iam.query.UserQueryDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * IAM-用户基本信息表 服务实现类
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2019-07-08
 */
@Service
public class UserDBOImpl extends ServiceImpl<UserDao, UserEntity> implements UserDBO {

    @Autowired
    private OrgDBO orgDBO;

    @Autowired
    private UserOrgDao userOrgDao;

    @Autowired
    private UserDao userDao;

    @Autowired
    private FieldDictDBO fieldDictDBO;

    private void encryptFieldsList(List<UserSearchDto.Fields> baseFields) {
        if (baseFields == null) {
            return;
        }

        baseFields.forEach(field -> {
            String attr = field.getAttr();
            if (attr != null && UserUtil.ENCRYPTED_BASE_FIELDS.contains(attr)) {
                Object value = field.getValue();
                if (value != null) {
                    String valueString = value.toString();
                    if (("u." + LocalUserAttr.phone_number.getFieldName()).equals(attr)) {
                        valueString = UserUtil.formatMobile(valueString);
                    } else if (("u." + LocalUserAttr.email.getFieldName()).equals(attr)) {
                        valueString = valueString.toLowerCase();
                    }
                    field.setValue(UserUtil.encryptSensitiveInfo(valueString));
                }
            }
        });
    }

    @Override
    public List<UserEntity> getList(UserEntity userEntity, String... attrs) {
        QueryWrapper<UserEntity> queryWrapper = new QueryWrapper<>(userEntity);
        queryWrapper.select(attrs);
        List<UserEntity> list = super.list(queryWrapper);
        UserUtil.decryptEntityListFields(list);
        return list;
    }

    @Override
    public UserEntity getOneByUsername(String username, String... attrs) {
        QueryWrapper<UserEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.select(attrs).lambda().eq(UserEntity::getUsername, username);
        UserEntity user = getOne(queryWrapper);
        if (user != null) {
            user.decryptFields();
        }
        return user;
    }

    @Override
    public List<UserEntity> getUserInfoByEntity(UserEntity userEntity, String... attrs) {
        QueryWrapper<UserEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.select(attrs).lambda().nested(
                e -> e.like(UserEntity::getUsername, userEntity.getUsername())
                        .or().eq(UserEntity::getPhoneNumber, UserUtil.encryptSensitiveInfo(userEntity.getPhoneNumber()))
                        .or().eq(UserEntity::getEmail, UserUtil.encryptSensitiveInfo(userEntity.getEmail().toLowerCase()))
        );
        List<UserEntity> list = list(queryWrapper);
        UserUtil.decryptEntityListFields(list);
        return list;
    }

    @Override
    public UserEntity getOneById(Long id, String... attrs) {
        QueryWrapper<UserEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.select(attrs).lambda().eq(UserEntity::getId, id);
        UserEntity user = getOne(queryWrapper);
        if (user != null) {
            user.decryptFields();
        }
        return user;
    }

    @Override
    public Map<String, Object> getOneMapUser(String username, String... attrs) {
        QueryWrapper<UserEntity> queryWrapper = new QueryWrapper();
        queryWrapper.select(attrs).lambda().eq(UserEntity::getUsername, username);
        List<Map<String, Object>> mapList = baseMapper.selectMaps(queryWrapper);
        if (mapList == null || mapList.size() == 0) {
            return null;
        }
        Map<String, Object> result = mapList.get(0);
        UserUtil.decryptMapFields(result);
        return result;
    }

    @Override
    public Map<String, Object> getMapUser(Long id, String... attrs) {
        QueryWrapper<UserEntity> queryWrapper = new QueryWrapper();
        queryWrapper.select(attrs).lambda().eq(UserEntity::getId, id);
        Map<String, Object> result = getMap(queryWrapper);
        UserUtil.decryptMapFields(result);
        return result;
    }

    @Override
    public Map<String, Object> getOneMapUser(UserEntity queryEntity, String... attrs) {
        QueryWrapper<UserEntity> queryWrapper = new QueryWrapper<>(queryEntity);
        queryWrapper.select(attrs);
        Map<String, Object> result = getMap(queryWrapper);
        UserUtil.decryptMapFields(result);
        return result;
    }

    @Override
    public List<Map<String, Object>> getListMapUser(String filter, String... attrs) {
        QueryWrapper<UserEntity> queryWrapper = new QueryWrapper();
        queryWrapper.select(attrs).lambda().like(UserEntity::getUsername, filter);
        List<Map<String, Object>> mapList = baseMapper.selectMaps(queryWrapper);
        if (mapList == null || mapList.isEmpty()) {
            return null;
        }
        UserUtil.decryptMapListFields(mapList, false);
        return mapList;
    }

    @Override
    public List<Map<String, Object>> getListMapUser(Map<String, Object> filter, Map<String, String> domainFieldName, String... attrs) {
        QueryWrapper<UserEntity> queryWrapper = new QueryWrapper().select(attrs);
        for (Map.Entry<String, Object> item : filter.entrySet()) {
            String fieldName = domainFieldName.get(item.getKey());
            Object value = item.getValue();
            
            if (value instanceof List && CollectionUtils.isNotEmpty((List) value)) {
                if (UserUtil.isEncrypt(fieldName)) {
                    if (LocalUserAttr.email.getFieldName().equals(fieldName)) {
                        ((List<String>) value).forEach(itemValue -> {
                            itemValue = itemValue.toLowerCase();
                        });
                    }
                    List<String> encryptedValues = ((List<String>) value).stream()
                            .map(UserUtil::encryptSensitiveInfo)
                            .collect(Collectors.toList());
                    queryWrapper.in(fieldName, encryptedValues);
                } else {
                    queryWrapper.in(fieldName, (List) value);
                }
            } else {
                if (UserUtil.isEncrypt(fieldName)) {
                    String valueString = value.toString();
                    if (LocalUserAttr.email.getFieldName().equals(fieldName)) {
                        valueString = valueString.toLowerCase();
                    }
                    queryWrapper.eq(fieldName, UserUtil.encryptSensitiveInfo(valueString));
                } else {
                    queryWrapper.eq(item.getKey(), value);
                }
            }
        }
        
        List<Map<String, Object>> mapList = baseMapper.selectMaps(queryWrapper);
        if (mapList == null || mapList.isEmpty()) {
            return null;
        }
        UserUtil.decryptMapListFields(mapList, false);
        return mapList;
    }

    @Override
    public List<UserEntity> getByAlias(String userAlias) {
        QueryWrapper<UserEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(UserEntity::getPreferredUsername, userAlias);
        List<UserEntity> list = list(queryWrapper);
        UserUtil.decryptEntityListFields(list);
        return list;
    }

    @Override
    public List<UserEntity> getByJobNumber(String jobNumber) {
        QueryWrapper<UserEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(UserEntity::getUserJobNumber, jobNumber);
        List<UserEntity> list = list(queryWrapper);
        UserUtil.decryptEntityListFields(list);
        return list;
    }

    @Override
    public UserEntity getByVerifiedEmail(String email) {
        QueryWrapper<UserEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(UserEntity::getEmail, UserUtil.encryptSensitiveInfo(email.toLowerCase()));
        return getOne(queryWrapper);
    }

    @Override
    public List<UserEntity> getVerifiedEmailUsers(String email) {
        QueryWrapper<UserEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(UserEntity::getEmail, UserUtil.encryptSensitiveInfo(email.toLowerCase()));
        List<UserEntity> list = list(queryWrapper);
        UserUtil.decryptEntityListFields(list);
        return list;
    }

    @Override
    public List<UserEntity> getVerifiedEmailUsers(List<String> emails) {
        List<String> emailList = new ArrayList<>();
        for (String email : emails) {
            emailList.add(UserUtil.encryptSensitiveInfo(email.toLowerCase()));
        }
        QueryWrapper<UserEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(UserEntity::getEmail, emailList);
        List<UserEntity> list = list(queryWrapper);
        // 后续转换自动解密无需重复解密
//        UserUtil.decryptEntityListFields(list);
        return list;
    }

    @Override
    public List<UserEntity> getVerifiedPhoneUsers(List<String> phones) {
        List<String> phoneList = new ArrayList<>();
        for (String phone : phones) {
            phoneList.add(UserUtil.encryptSensitiveInfo(phone));
        }
        QueryWrapper<UserEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(UserEntity::getPhoneNumber, phoneList);
        List<UserEntity> list = list(queryWrapper);
        // 后续转换自动解密无需重复解密
//        UserUtil.decryptEntityListFields(list);
        return list;
    }

    @Override
    public UserEntity getByVerifiedPhone(String phone) {
        QueryWrapper<UserEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(UserEntity::getPhoneNumber, UserUtil.encryptSensitiveInfo(phone));
        List<UserEntity> list = super.list(queryWrapper);
        UserUtil.decryptEntityListFields(list);
        return list != null && list.size() > 0 ? list.get(0) : null;
        //return getOne(queryWrapper);
    }

    @Override
    public List<UserEntity> getVerifiedPhoneUsers(String phone) {
        
        QueryWrapper<UserEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(UserEntity::getPhoneNumber, UserUtil.encryptSensitiveInfo(phone));
        List<UserEntity> list = list(queryWrapper);
        UserUtil.decryptEntityListFields(list);
        return list;
    }

    @Override
    public Boolean hasOrgUser(Long orgId) {
        List<UserEntity> userEntityList = userDao.limitOneOrgUser(orgId);
        return userEntityList.size() > 0;
    }

    @Override
    public int getUserCount() {
        return super.count();
    }

    @Override
    public int getAddUserCount(LocalDateTime startTime, LocalDateTime endTime) {
        QueryWrapper<UserEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().ne(UserEntity::getUsername, "admin");
        queryWrapper.lambda().ge(UserEntity::getCreateTime, startTime);
        queryWrapper.lambda().le(UserEntity::getCreateTime, endTime);
        return super.count(queryWrapper);
    }

    @Override
    public List<UserEntity> getByConnectorId(Long connectorId, String... attrs) {
        QueryWrapper<UserEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.select(attrs);
        queryWrapper.lambda().eq(UserEntity::getConnectorId, connectorId);
        List<UserEntity> list = super.list(queryWrapper);
        UserUtil.decryptEntityListFields(list);
        return list;
    }

    @Override
    public Map<String, Object> insertByMap(Map<String, Object> entry) {
        String mobile = (String) entry.get(LocalUserAttr.phone_number.getFieldName());
        if (StringUtils.isNotBlank(mobile) && mobile.matches(CommonConstants.CHINA_LOCAL_MOBILE_REG)) {
            mobile = UserUtil.formatMobile(mobile);
            entry.put(LocalUserAttr.phone_number.getFieldName(), mobile);
        }
        UserUtil.encryptMapFields(entry);
        filterBlankUniqueField(entry);
        getBaseMapper().insertByMap(entry);
        return entry;
    }

    @Override
    public boolean updateByMap(Long id, Map<String, Object> userEntity) {
        if (userEntity.isEmpty()) {
            return true;
        }
        userEntity.put(LocalUserAttr.sub.getFieldName(), id);
        String mobile = (String) userEntity.get(LocalUserAttr.phone_number.getFieldName());
        if (StringUtils.isNotBlank(mobile) && mobile.matches(CommonConstants.CHINA_LOCAL_MOBILE_REG)) {
            mobile = UserUtil.formatMobile(mobile);
            userEntity.put(LocalUserAttr.phone_number.getFieldName(), mobile);
        }
        UserUtil.encryptMapFields(userEntity);
        //密码更新时间
        String password = (String) userEntity.get(LocalUserAttr.password.getFieldName());
        if (StringUtils.isNotBlank(password)) {
            userEntity.put(LocalUserAttr.pwd_changed_time.getFieldName(), LocalDateTime.now());
        }

        final List<LocalUserAttr> blankUniqueField = filterBlankUniqueField(userEntity);
        // 已停用用户不允许修改用户状态
        UserEntity user = getById(id);
        if(user.getStatus() == UserStatus.DEACTIVATE.getValue()){
            userEntity.remove(LocalUserAttr.status.getFieldName());
        }
        final boolean b = getBaseMapper().updateByMap(userEntity);
        putNull(id, blankUniqueField);
        return b;
    }

    @Override
    public QueryPage<Map<String, Object>> page(
            UserQueryCondition queryCondition, QueryPage<Map<String, Object>> queryPage,
            Set<String> requestAttrs) {
        Page<Map<String, Object>> page = PagingUtil.toMybatisPage(queryPage);

        LambdaQueryWrapper<UserEntity> queryWrapper = new QueryWrapper().select(requestAttrs.toArray(new String[0])).lambda();

        final String filterStr = queryCondition.getFilter();
        if (StringUtils.isNotBlank(filterStr)) {
            String mobile = UserUtil.formatMobile(filterStr);
            queryWrapper.nested(e -> e.likeRight(UserEntity::getUsername, filterStr)
                    .or().likeRight(UserEntity::getName, filterStr)
                    .or().eq(UserEntity::getEmail, UserUtil.encryptSensitiveInfo(filterStr.toLowerCase()))
                    .or().like(UserEntity::getUserJobNumber, filterStr)
                    .or().eq(UserEntity::getPhoneNumber, UserUtil.encryptSensitiveInfo(mobile)));
        }
        if (queryCondition.getStatus() != null) {
            queryWrapper.eq(UserEntity::getStatus, queryCondition.getStatus());
        }

        queryWrapper.ne(UserEntity::getCreatedMode, CreatedMode.BY_SYSTEM.getValue());

        boolean orderUser = false;
        if (queryCondition.getOrg() != null) {
            List<Long> orgIds = new ArrayList() {{
                add(queryCondition.getOrg().getId());
            }};
            //如果不传，默认只取当前org
            if (queryCondition.getIncludeUsersInSubOrgs() != null && queryCondition.getIncludeUsersInSubOrgs() == false) {
                //单个部门下的用户按userOrder排序
                if (orgIds.size() == 1) {
                    orderUser = true;
                }
            } else {//取所有子org的用户
                orgIds = orgDBO.getSubOrgList(queryCondition.getOrg().getOrgPath(), new String[]{LocalOrgAttr.id.getFieldName()})
                        .stream().map(map -> (Long) map.get(LocalOrgAttr.id.getFieldName())).collect(Collectors.toList());
            }
            LambdaQueryWrapper<UserOrgEntity> userOrgEntityLambdaQueryWrapper =
                    new QueryWrapper<UserOrgEntity>().lambda().select(UserOrgEntity::getUid)
                            .in(UserOrgEntity::getOrgRefId, orgIds);
            if (orderUser) {
                userOrgEntityLambdaQueryWrapper.orderByDesc(UserOrgEntity::getUserOrder);
            }
            List<Object> userOrgEntityList = userOrgDao.selectObjs(userOrgEntityLambdaQueryWrapper);

            if (userOrgEntityList == null || userOrgEntityList.size() == 0) {
                IPage<Map<String, Object>> resultPage = PagingUtil.toMybatisPage(queryPage);
                PagingUtil.fromMybatisPageToQueryPage(resultPage, queryPage, requestAttrs);
                return queryPage;
            } else {
                queryWrapper.in(UserEntity::getId, userOrgEntityList);
            }
        }
        List<String> userIds = queryCondition.getUserIds();
        if (CollectionUtils.isNotEmpty(userIds)) {
            queryWrapper.in(UserEntity::getUsername, userIds);
        }
        if (!orderUser) {
            //增加排序，按照ID,增加时间类同
            queryWrapper.orderByDesc(UserEntity::getCreateTime);
            queryWrapper.orderByDesc(UserEntity::getId);
        }

        IPage<Map<String, Object>> resultPage = baseMapper.selectMapsPage(page, queryWrapper);
        List<Map<String, Object>> records = resultPage.getRecords();
        UserUtil.decryptMapListFields(records, false);
        PagingUtil.fromMybatisPageToQueryPage(resultPage, queryPage, requestAttrs);
        return queryPage;
    }

    @Override
    public QueryPage<Map<String, Object>> page(QueryPage<Map<String, Object>> queryPage, UserEntity userEntity, LocalDateTime startTime, LocalDateTime endTime, Set<String> requestAttrs) {
        Page<Map<String, Object>> page = PagingUtil.toMybatisPage(queryPage);

        LambdaQueryWrapper<UserEntity> queryWrapper;
        if (CollectionUtils.isEmpty(requestAttrs)) {
            queryWrapper = new QueryWrapper(userEntity).lambda();
        } else {
            queryWrapper = new QueryWrapper(userEntity).select(requestAttrs.toArray(new String[0])).lambda();
        }

        if (startTime != null) {
            queryWrapper.ge(UserEntity::getUpdateTime, startTime);
        }
        if (endTime != null) {
            queryWrapper.le(UserEntity::getUpdateTime, endTime);
        }
        IPage<Map<String, Object>> resultPage = baseMapper.selectMapsPage(page, queryWrapper);
        PagingUtil.fromMybatisPageToQueryPage(resultPage, queryPage, requestAttrs);
        UserUtil.decryptMapListFields(queryPage.getItems(), false);
        return queryPage;
    }

    @Override
    public QueryPage<UserEntity> page(List<Long> userIds, QueryPage<UserEntity> queryPage, UserEntity queryEntity) {

        Page<UserEntity> page = PagingUtil.toMybatisPage(queryPage);
        LambdaQueryWrapper<UserEntity> queryWrapper = new QueryWrapper(queryEntity).lambda();
        if (userIds != null && userIds.size() == 0) {
            return PagingUtil.makeEmptyPageResult(queryPage);
        } else if (userIds != null) {
            queryWrapper.in(UserEntity::getId, userIds);
        }
        queryWrapper.ne(UserEntity::getCreatedMode, CreatedMode.BY_SYSTEM.getValue());
        queryWrapper.orderByAsc(UserEntity::getId);

        Page<UserEntity> resultPage = baseMapper.selectPage(page, queryWrapper);

        PagingUtil.fromMybatisPageToQueryPage(resultPage, queryPage);
        UserUtil.decryptEntityListFields(queryPage.getItems());
        return queryPage;
    }

    @Override
    public void deleteByConnectorId(Long dataFrom) {
        QueryWrapper<UserEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(UserEntity::getConnectorId, dataFrom);
        this.remove(queryWrapper);
    }

    @Override
    public void resetPassword(long userId, String newEncodedPassword) {
        Map<String, Object> entityMap = new HashMap<>(1);
        entityMap.put(LocalUserAttr.password.getFieldName(), newEncodedPassword);
        entityMap.put(LocalUserAttr.password_status.getFieldName(), UserPWDStatus.NORMAL.getValue());
        updateByMap(userId, entityMap);
    }

    @Override
    public int updateConnectorBatchNoByIds(Integer syncBatchNo, Set<Long> userIds) {
        return this.getBaseMapper().updateConnectorBatchNoByIds(syncBatchNo, userIds);
    }

    /***
     * 进行条件转换处理
     */
    private IQueryConditonApply<com.cyberscraft.uep.iam.dto.domain.UserQueryDTO, UserEntity> queryApply = (queryDto, queryWrapper) -> {
        if (queryDto.getStartUpdateTime() != null) {
            queryWrapper.ge(UserEntity::getUpdateTime, queryDto.getStartUpdateTime());
        }
        if (queryDto.getEndUpdateTime() != null) {
            queryWrapper.lt(UserEntity::getUpdateTime, queryDto.getEndUpdateTime());
        }
        if (queryDto.getMinUserId() != null) {
            queryWrapper.gt(UserEntity::getId, queryDto.getMinUserId());
        }
        if (queryDto.getMaxUserId() != null) {
            queryWrapper.lt(UserEntity::getId, queryDto.getMaxUserId());
        }
    };

    /***
     * 进行条件转换处理
     */
    private IQueryConditonApply<UserQueryDTO, UserEntity> uamUserQueryApply = (queryDto, queryWrapper) -> {
        String filterStr = queryDto.getQ();
        if (StringUtils.isNotBlank(filterStr)) {
            queryWrapper.nested(e -> e.likeRight(UserEntity::getUsername, filterStr)
                    .or().eq(UserEntity::getEmail, UserUtil.encryptSensitiveInfo(filterStr.toLowerCase()))
                    .or().eq(UserEntity::getPhoneNumber, UserUtil.encryptSensitiveInfo(filterStr)));
        }

        if (queryDto.getStatus() != null) {
            queryWrapper.eq(UserEntity::getStatus, queryDto.getStatus());
        }

        if (CollectionUtils.isNotEmpty(queryDto.getIds())) {
            queryWrapper.in(UserEntity::getId, queryDto.getIds());
        }

        if (CollectionUtils.isNotEmpty(queryDto.getOrgIds())) {
            queryWrapper.inSql(UserEntity::getId, "select uid from iam_user_org where org_ref_id in(" + SqlUtil.getInSqlValues(queryDto.getOrgIds()) + ")");
        }
    };

    @Override
    public PageView<UserEntity> page(Pagination<com.cyberscraft.uep.iam.dto.domain.UserQueryDTO> queryPage) {
        Page<UserEntity> page = PagingUtil.toMybatisPage(queryPage);
        String[] selectStr = queryPage.getRequestAttrs() != null ? queryPage.getRequestAttrs().toArray(new String[0]) : new String[0];
        //TODO 实现对应的查询条件
        QueryWrapper<UserEntity> queryWrapper = new QueryWrapper().select(selectStr);//.lambda();
        com.cyberscraft.uep.iam.dto.domain.UserQueryDTO queryDto = queryPage.getQueryDto();
        LambdaQueryWrapper<UserEntity> lambdaQueryWrapper = queryWrapper.lambda();
        //设置排序条件
        if (page.getOrders() != null && page.getOrders().size() > 0) {
        } else {
            lambdaQueryWrapper.orderByDesc(UserEntity::getId);
        }
        //应用对应的条件
        queryApply.apply(queryDto, lambdaQueryWrapper);

        IPage<UserEntity> resultPage = baseMapper.selectPage(page, lambdaQueryWrapper);

        PageView<UserEntity> pageView = PagingUtil.toPageView(resultPage);
        UserUtil.decryptEntityListFields(pageView.getItems());
        return pageView;
    }

    @Override
    public PageView<UserEntity> pageForUam(Pagination<UserQueryDTO> queryPage) {
        Page<UserEntity> page = PagingUtil.toMybatisPage(queryPage);
        String[] selectStr = queryPage.getRequestAttrs() != null ? queryPage.getRequestAttrs().toArray(new String[0]) : new String[0];
        QueryWrapper<UserEntity> queryWrapper = new QueryWrapper().select(selectStr);//.lambda();
        UserQueryDTO queryDto = queryPage.getQueryDto();
        LambdaQueryWrapper<UserEntity> lambdaQueryWrapper = queryWrapper.lambda();
        //设置排序条件
        if (page.getOrders() != null && page.getOrders().size() > 0) {
        } else {
            lambdaQueryWrapper.orderByDesc(UserEntity::getId);
        }
        //应用对应的条件
        uamUserQueryApply.apply(queryDto, lambdaQueryWrapper);

        IPage<UserEntity> resultPage = baseMapper.selectPage(page, lambdaQueryWrapper);

        PageView<UserEntity> pageView = PagingUtil.toPageView(resultPage);
        UserUtil.decryptEntityListFields(pageView.getItems());
        return pageView;
    }

    @Override
    public String getTenantIdByMobile(String mobile) {
        return this.getBaseMapper().getTenantIdByMobile(UserUtil.encryptSensitiveInfo(mobile));
    }

    @Override
    public List<String> getTenantIdByEmail(String email) {
        return this.getBaseMapper().getTenantIdByEmail(UserUtil.encryptSensitiveInfo(email.toLowerCase()));
    }

    @Override
    public List<UserEntity> getUserByEmail(String email) {
        QueryWrapper<UserEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(UserEntity::getEmail, UserUtil.encryptSensitiveInfo(email.toLowerCase()));
        List<UserEntity> list = super.list(queryWrapper);
        UserUtil.decryptEntityListFields(list);
        return list;
    }

    @Override
    public List<UserEntity> getListByUserNames(List<String> userNames) {
        if (CollectionUtils.isEmpty(userNames)) {
            return null;
        }

        QueryWrapper<UserEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(UserEntity::getUsername, userNames);
        List<UserEntity> list = super.list(queryWrapper);
        UserUtil.decryptEntityListFields(list);
        return list;
    }

    @Override
    public List<UserEntity> getListByIds(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return null;
        }

        QueryWrapper<UserEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(UserEntity::getId, userIds);
        List<UserEntity> list = super.list(queryWrapper);
        UserUtil.decryptEntityListFields(list);
        return list;
    }

    @Override
    public List<Long> getListByExtension(Map<String, Object> userEntity) {
        if (userEntity.isEmpty()) {
            return Collections.emptyList();
        }
        List<UserEntity> listByExtension = getBaseMapper().getListByExtension(userEntity);
        UserUtil.decryptEntityListFields(listByExtension);
        return listByExtension.stream().map(UserEntity::getId).collect(Collectors.toList());
    }

    @Override
    public List<Long> getDynamicTagIdListByExtension(List<DynamicExpressDto> userEntity) {
        if (userEntity.isEmpty()) {
            return Collections.emptyList();
        }
        return getBaseMapper().getDynamicTagIdListByExtension(userEntity);
    }

    @Override
    public List<Long> getUserIdListByMap(List<DynamicExpressDto> dynamicExpressDtos) {
        QueryWrapper<UserEntity> queryWrapper = new QueryWrapper<>();
        for (DynamicExpressDto dynamicExpressDto : dynamicExpressDtos) {
            // 判断是否加密
            if (UserUtil.isEncrypt(dynamicExpressDto.getAttribute())) {
                String value = dynamicExpressDto.getValue();
                if (LocalUserAttr.email.getFieldName().equals(dynamicExpressDto.getAttribute())) {
                    value = value.toLowerCase();
                }
                UserUtil.encryptSensitiveInfo(value);
            }
            switch (dynamicExpressDto.getOp()) {
                case OperatorVariables.ge:
                    queryWrapper.ge(dynamicExpressDto.getAttribute(), dynamicExpressDto.getValue());
                    break;
                case OperatorVariables.le:
                    queryWrapper.le(dynamicExpressDto.getAttribute(), dynamicExpressDto.getValue());
                    break;
                case OperatorVariables.lt:
                    queryWrapper.lt(dynamicExpressDto.getAttribute(), dynamicExpressDto.getValue());
                    break;
                case OperatorVariables.gt:
                    queryWrapper.gt(dynamicExpressDto.getAttribute(), dynamicExpressDto.getValue());
                    break;
                case OperatorVariables.eq:
                    queryWrapper.eq(dynamicExpressDto.getAttribute(), dynamicExpressDto.getValue());
                    break;
                case OperatorVariables.ne:
                    queryWrapper.ne(dynamicExpressDto.getAttribute(), dynamicExpressDto.getValue());
                    break;
                case OperatorVariables.sw:
                    queryWrapper.likeRight(dynamicExpressDto.getAttribute(), dynamicExpressDto.getValue());
                    break;
                case OperatorVariables.ew:
                    queryWrapper.likeLeft(dynamicExpressDto.getAttribute(), dynamicExpressDto.getValue());
                    break;
                case OperatorVariables.co:
                    queryWrapper.like(dynamicExpressDto.getAttribute(), dynamicExpressDto.getValue());
                    break;
                case OperatorVariables.pr:
                    queryWrapper.isNotNull(dynamicExpressDto.getAttribute());
                    break;
                default:
                    break;
            }
        }
        queryWrapper.select("id");
        queryWrapper.ne(LocalUserAttr.username.getDomainName(), "admin");

        return getBaseMapper().selectList(queryWrapper).stream().map(UserEntity::getId).collect(Collectors.toList());
    }

    @Override
    public Set<String> getUserUniqueDomainNames() {
        Set<String> result = new HashSet<>();
        List<FieldDictEntity> fieldDictEntities = fieldDictDBO.listUniqueFieldDict();
        for (FieldDictEntity fieldDictEntity : fieldDictEntities) {
            String domainName = fieldDictEntity.getDomainName();
            if (LocalUserAttr.username.getDomainName().equals(domainName)) {
                continue;
            }
            if (LocalUserAttr.sub.getDomainName().equals(domainName)) {
                continue;
            }
            result.add(domainName);
        }
        return result;
    }

    @Override
    public Map<String, Map<Object, Object>> getUsersUniqueAttrs() {
        Map<String, Map<Object, Object>> result = new HashMap<>();

        Set<String> basicFieldNames = new HashSet<>();
        Set<String> extFieldNames = new HashSet<>();
        List<FieldDictEntity> fieldDictEntities = fieldDictDBO.listUniqueFieldDict();
        for (FieldDictEntity fieldDictEntity : fieldDictEntities) {
            if (fieldDictEntity.getCreateMod() == 1) {
                basicFieldNames.add(fieldDictEntity.getFieldName());
            } else {
                extFieldNames.add(fieldDictEntity.getFieldName());
            }
        }
        if (!extFieldNames.isEmpty()) {
            basicFieldNames.add("user_extension");
        }

        //id、username必然有，如果没有其他的唯一属性，则直接返回空
        if (basicFieldNames.size() == 2) {
            return result;
        }

        //保证username这个key必须得有
        Map<Object, Object> usernameMap = new HashMap<>();
        result.put(LocalUserAttr.username.getDomainName(), usernameMap);

        Map<String, String> fieldMap = fieldDictEntities.stream().collect(Collectors.toMap(FieldDictEntity::getFieldName, FieldDictEntity::getDomainName));

        QueryWrapper<UserEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.select(basicFieldNames.toArray(new String[basicFieldNames.size()]));
        List<Map<String, Object>> mapList = this.listMaps(queryWrapper);
        mapList.forEach(e -> {
            HashMap<String, Object> userInfo = new HashMap<>();
            Object username = e.get(LocalUserAttr.username.getFieldName());
            UserUtil.decryptMapFields(e);

            Set<String> keySet = e.keySet();
            for (String fieldName : keySet) {
                if (LocalUserAttr.username.getFieldName().equals(fieldName)) {
                    continue;
                }
                if (LocalUserAttr.sub.getFieldName().equals(fieldName)) {
                    continue;
                }
                Object value = e.get(fieldName);
                if (value == null || value.toString().equals("")) {
                    continue;
                }

                if (!fieldName.equals(LocalUserAttr.user_extension.getFieldName())) {
                    String domainName = fieldMap.get(fieldName);

                    Map<Object, Object> map = result.get(domainName);
                    if (map == null) {
                        map = new HashMap<>();
                    }

                    map.put(value, username);
                    userInfo.put(domainName, value);

                    result.put(domainName, map);
                } else {
                    String userExtension = (String) value;
                    if (StringUtils.isNotBlank(userExtension)) {
                        Map<String, Object> extMap = JsonUtil.str2Map(userExtension);
                        for (String extFieldName : extFieldNames) {
                            Object extValue = extMap.get(extFieldName);
                            if (extValue == null || extValue.toString().equals("")) {
                                continue;
                            }

                            String extDomainName = fieldMap.get(extFieldName);
                            Map<Object, Object> map = result.get(extDomainName);
                            if (map == null) {
                                map = new HashMap<>();
                            }

                            map.put(extValue, username);
                            userInfo.put(extDomainName, extValue);

                            result.put(extDomainName, map);
                        }
                    }
                }
            }
            usernameMap.put(username, userInfo);
        });
        return result;
    }

    @Override
    public List<LocalUserAttr> filterBlankUniqueField(Map<String, Object> localUserBasicInfo) {
        List<LocalUserAttr> result = new ArrayList<>();
        for (LocalUserAttr localUserAttr : LocalUserAttr.getUniqueAttr()) {
            final Object attrValue = localUserBasicInfo.get(localUserAttr.getFieldName());
            if (attrValue != null && StringUtils.isBlank(attrValue.toString())) {
                localUserBasicInfo.remove(localUserAttr.getFieldName());
                result.add(localUserAttr);
            }
        }
        return result;
    }

    @Override
    public List<UserEntity> getUserByPhone(String phoneNumber) {
        QueryWrapper<UserEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(UserEntity::getPhoneNumber, UserUtil.encryptSensitiveInfo(phoneNumber));
        List<UserEntity> list = super.list(queryWrapper);
        UserUtil.decryptEntityListFields(list);
        return list;
    }

    @Override
    public List<Map<String, Object>> searchUser(Set<String> attrs, String searchField, String filter, List<String> usernames, List<Long> orgIds, Long orgId, Boolean includeUsersInSubOrgs, List<UserSearchDto.Fields> baseFields, List<UserSearchDto.Fields> extendField, List<UserSearchDto.Fields> positions, int page, Integer size) {
        if (StringUtils.isNotBlank(filter)) {
            // 为空的话，默认判断是否为手机号自动 +86
            if (StringUtils.isBlank(searchField) || LocalUserAttr.phone_number.getFieldName().equals(searchField)) {
                filter = UserUtil.formatMobile(filter);
            }
            if (UserUtil.isEncrypt(searchField)) {
                if (LocalUserAttr.email.getFieldName().equals(searchField)) {
                    filter = filter.toLowerCase();
                }
                filter = UserUtil.encryptSensitiveInfo(filter);
            }
        }
        encryptFieldsList(baseFields);
        List<Map<String, Object>> list = userDao.searchUser(attrs, searchField, filter, usernames, orgIds, orgId, includeUsersInSubOrgs, baseFields, extendField, positions, page, size);
        UserUtil.decryptMapListFields(list, false);
        return list;
    }

    @Override
    public List<Map<String, Object>> searchUserByIds(List<String> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        QueryWrapper<UserEntity> queryWrapper = new QueryWrapper();
        queryWrapper.lambda().in(UserEntity::getId, userIds);
        List<Map<String, Object>> list = userDao.selectMaps(queryWrapper);
        // 暂时不展示铭感信息不做解密
//        UserUtil.decryptMapListFields(list, false);
        return list;
    }

    @Override
    public int size(String searchField, String filter, List<String> usernames, List<Long> orgIds, List<UserSearchDto.Fields> baseFields, List<UserSearchDto.Fields> extendField, List<UserSearchDto.Fields> positions) {
        if (StringUtils.isNotBlank(filter)) {
            // 为空的话，默认判断是否为手机号自动 +86
            if (StringUtils.isBlank(searchField) || LocalUserAttr.phone_number.getFieldName().equals(searchField)) {
                filter = UserUtil.formatMobile(filter);
            }
            if (UserUtil.isEncrypt(searchField)) {
                if (LocalUserAttr.email.getFieldName().equals(searchField)) {
                    filter = filter.toLowerCase();
                }
                filter = UserUtil.encryptSensitiveInfo(filter);
            }
        }
        encryptFieldsList(baseFields);
        return userDao.size(searchField, filter, usernames, orgIds, baseFields, extendField, positions);
    }

    @Override
    public List<UserEntity> selectByMap(Map<String, Object> map) {
        UserUtil.encryptMapFields(map);
        List<UserEntity> list = userDao.selectByMap(map);
        UserUtil.decryptEntityListFields(list);
        return list;
    }

    @Override
    public boolean updateById(UserEntity entity) {
        entity.encryptFields();
        // 已停用用户不允许修改用户状态
        UserEntity user = getById(entity.getId());
        if(user.getStatus() == UserStatus.DEACTIVATE.getValue()){
            entity.setStatus(user.getStatus());
        }
        return baseMapper.updateById(entity) > 0;
    }

    @Override
    public List<UserEntity> getPasswdExpire(LocalDateTime finalStartTime, LocalDateTime localDateTime, LocalDateTime pwdExpireTime) {
        List<UserEntity> passwdExpire = userDao.getPasswdExpire(finalStartTime, localDateTime, pwdExpireTime);
        UserUtil.decryptEntityListFields(passwdExpire);
        return passwdExpire;
    }

    @Override
    public List<UserEntity> getNoActiveUsers(LocalDateTime finalStartTime, LocalDateTime endTime) {
        List<UserEntity> passwdExpire = userDao.getNoActiveUsers(finalStartTime, endTime);
        UserUtil.decryptEntityListFields(passwdExpire);
        return passwdExpire;
    }

    @Override
    public List<UserEntity> getSuspendUsers(LocalDateTime finalStartTime, LocalDateTime endTime) {
        List<UserEntity> suspendUsers = userDao.getSuspendUsers(finalStartTime, endTime);
        UserUtil.decryptEntityListFields(suspendUsers);
        return suspendUsers;
    }

    @Override
    public List<UserEntity> getNoLoginUsers(List<String> loginUsers, LocalDateTime endTime) {
        List<UserEntity> noLoginUsers = userDao.getNoLoginUsers(loginUsers, endTime);
        UserUtil.decryptEntityListFields(noLoginUsers);
        return noLoginUsers;
    }

    @Override
    public void putNull(Long userId, List<LocalUserAttr> localUserAttrs) {
        if (!localUserAttrs.isEmpty()) {
            LambdaUpdateWrapper<UserEntity> wrapper = Wrappers.lambdaUpdate();
            for (LocalUserAttr localUserAttr : localUserAttrs) {
                wrapper.setSql(String.format("%s=null", localUserAttr.getFieldName()));
            }
            wrapper.eq(UserEntity::getId, userId);
            update(null, wrapper);
        }
    }
}
