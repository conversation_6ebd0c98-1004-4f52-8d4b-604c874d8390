package com.cyberscraft.uep.iam.service.user.impl;

import com.cyberscraft.uep.account.client.domain.*;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountException;
import com.cyberscraft.uep.account.client.exception.ThirdPartyRoleGroupExistException;
import com.cyberscraft.uep.account.client.provider.dingding.constant.CommonDingRoleAttr;
import com.cyberscraft.uep.account.client.service.IExternalConnectService;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.common.util.MappingParser;
import com.cyberscraft.uep.iam.common.constants.DaoConstants;
import com.cyberscraft.uep.iam.common.enums.LocalRoleAttr;
import com.cyberscraft.uep.iam.common.enums.SyncDirectionEnum;
import com.cyberscraft.uep.iam.common.exception.UserCenterException;
import com.cyberscraft.uep.iam.dto.enums.*;
import com.cyberscraft.uep.iam.entity.*;
import com.cyberscraft.uep.iam.service.*;
import com.cyberscraft.uep.iam.service.config.ISnsConfigService;
import com.cyberscraft.uep.iam.dto.domain.SyncStats;
import com.cyberscraft.uep.iam.service.data.IPushSnsGroupService;
import com.cyberscraft.uep.iam.service.transfer.AppProfileTransfer;
import com.cyberscraft.uep.iam.service.user.IExternalRoleService;
import com.cyberscraft.uep.iam.service.user.IExternalUserService;
import com.cyberscraft.uep.iam.service.user.IRolesPushService;
import com.cyberscraft.uep.task.Task;
import com.cyberscraft.uep.task.TaskHolder;
import com.cyberscraft.uep.task.TaskIsCanceledException;
import com.cyberscraft.uep.task.TaskStatus;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/11 13:59
 */
@Service
public class RolesPushServiceImpl implements IRolesPushService {

    private static final Logger logger = LoggerFactory.getLogger(RolesPushServiceImpl.class);

    @Autowired
    private ITagGroupService tagGroupService;

    @Autowired
    private ITagService tagService;

    @Autowired
    private ITagGroupTagService tagGroupTagService;

    @Autowired
    private IRoleGroupService roleGroupService;

    @Autowired
    protected IExternalConnectService externalConnectService;

    @Autowired
    private IExternalRoleService externalRoleService;


    @Autowired
    private ISyncErrorService errorService;

    @Autowired
    private IPushSnsGroupService pushSnsGroupService;

    @Autowired
    private IAppProfileService appProfileService;

    @Autowired
    private AppProfileTransfer appProfileTransfer;

    @Resource
    protected ISnsConfigService snsConfigService;

    @Autowired
    private IExternalUserService externalUserService;


    @Override
    public void fullPushRoleGroup(PushConnectorEntity connectorEntity, Connector connector, boolean isForceFullSync, SyncStats syncStats) {
        // 获取全部的角色组信息
        List<TagGroupEntity> tagGroups = tagGroupService.getAllTagGroupEntity();
        for (TagGroupEntity tagGroup : tagGroups) {
//            pushCreateOrUpdateRoleGroup(connectorEntity, connector, tagGroup, syncStats, true);
        }
    }

    @Override
    public void fullPushRole(PushConnectorEntity connectorEntity, Connector connector, boolean isForceFullSync, SyncStats syncStats) {
        // 获取需要同步的角色 先同步获取小范围 没有再获取大范围
        List<TagEntity> allTags = null;
        List<Long> roles = JsonUtil.str2List(connectorEntity.getRoles(), Long.class);
        if (CollectionUtils.isNotEmpty(roles)) {
            allTags = tagService.getTagByIds(new HashSet<>(roles));
        }

        // 如果选择了角色 就不同步角色组下的所有角色
        if (CollectionUtils.isEmpty(allTags) && StringUtils.isNotBlank(connectorEntity.getRoleApp())) {
            allTags = tagService.getTagByGroupIds(Collections.singletonList(Long.valueOf(connectorEntity.getRoleApp())));
        }

        if (CollectionUtils.isEmpty(allTags)) {
            logger.info("未找到需要同步的角色 或角色组信息，跳过角色同步");
            return;
        }
        for (TagEntity tag : allTags) {
            pushCreateOrUpdateRole(connectorEntity, connector, tag, syncStats, true);
        }
    }

    @Override
    public void pushCreateOrUpdateRole(PushConnectorEntity connectorEntity, Connector connector, TagEntity tag, SyncStats syncStats) {
        pushCreateOrUpdateRole(connectorEntity, connector, tag, syncStats, false);
    }

    @Override
    public void pushDeleteRole(PushConnectorEntity connectorEntity, Connector connector, Long roleId, SyncStats syncStats) {
        Task task = TaskHolder.getTask();
        if (task != null && task.getStatus() == TaskStatus.CANCELED) {
            throw new TaskIsCanceledException(task.getId());
        }

        String profile = "";
        String externalName = "";
        try {
            ExternalRoleEntity externalRole = externalRoleService.getByLocalId(connector.getId(), roleId);
            if (externalRole == null) {
                logger.info("push data to connector remove thirdParty role iam role id is :{}, thirdParty role code is null,this will return ", roleId);
                return;
            }
            profile = externalRole.getProfile();

            logger.info("push data to connector remove thirdParty role iam role id is :{},name:{},thirdParty role code is :{}", roleId, externalRole.getExternalName(), externalRole.getExternalId());
            externalConnectService.deleteExternalRole(externalRole.getExternalId(), connector);
            syncStats.deletedRole++;
            pushSnsGroupService.remove(externalRole.getId());
        } catch (Exception e) {
            logger.error("pushDeleteRole error", e);
            //记录错误信息，并且尝试清除垃圾数据，包括本地的及服务器的
            recordError(connector.getId(), connector.getTaskId(), externalName, profile, e.getMessage(), BindingTargetType.ROLE.getValue());
        }
    }

    @Override
    public void resetRoleUserLinks(PushConnectorEntity connectorEntity, Connector connector, TagEntity tag, SyncStats syncStats) {
        removeUserToRole(tag, connector, false);

        addUserToRole(tag, connector);
    }

    @Override
    public boolean isInRolePushScope(TagEntity tag, PushConnectorEntity connectorEntity) {
        List<Long> roleIds = JsonUtil.str2List(connectorEntity.getRoles(), Long.class);
        List<TagEntity> tags = null;
        if (CollectionUtils.isNotEmpty(roleIds)) {
            tags = tagService.getTagByIds(new HashSet<>(roleIds));
        } else if (StringUtils.isNotBlank(connectorEntity.getRoleApp())) {
            tags = tagService.getTagByGroupIds(Collections.singletonList(Long.valueOf(connectorEntity.getRoleApp())));
        }
        if (CollectionUtils.isNotEmpty(tags)) {
            return tags.stream().anyMatch(t -> t.getId().equals(tag.getId()));
        }
        return false;
    }

    @Override
    public void addUserToRole(PushConnectorEntity connectorEntity, Connector connector, boolean isForceFullSync, SyncStats syncStats) {
        // 获取角色下需要同步过去的用户信息
        List<Long> allGroupIds = tagGroupService.getAllTagGroupEntity().stream().map(TagGroupEntity::getId).collect(Collectors.toList());
        List<TagEntity> allTags = tagService.getTagByGroupIds(allGroupIds);
        for (TagEntity tag : allTags) {
            addUserToRole(tag, connector);
        }
    }

    private void addUserToRole(TagEntity tag, Connector connector) {
        ExternalRoleEntity externalRole = externalRoleService.getByLocalId(connector.getId(), tag.getId());
        if (externalRole == null) {
            return;
        }

        //已经推送给下游角色的用户
        List<String> alreadyPushUser = JsonUtil.str2List(externalRole.getUserIds(), String.class);

        // 执行添加操作
        Set<String> roleUserRes;
        if (CollectionUtils.isNotEmpty(alreadyPushUser)) {
            roleUserRes = new HashSet<>(alreadyPushUser);
        } else {
            roleUserRes = new HashSet<>();
        }


        Set<String> externalUserIds = localTagExternalUserIds(connector, tag);
        Set<String> needAdd = new HashSet<>();
        if (CollectionUtils.isNotEmpty(externalUserIds)) {
            needAdd.addAll(externalUserIds);
            // 移除已存在的用户
            if (CollectionUtils.isNotEmpty(alreadyPushUser)) {
                needAdd.removeAll(new HashSet<>(alreadyPushUser));
            }
        }

        if (CollectionUtils.isNotEmpty(needAdd)) {
            try {
                externalConnectService.addUserToRole(connector, externalRole.getExternalId(), new ArrayList<>(needAdd));
                roleUserRes.addAll(needAdd);
            } catch (Exception e) {
                logger.error("add user to role error, roleId:{},userIds:{}", externalRole.getExternalId(), needAdd);
            }
        }

        externalRole.setUserIds(JsonUtil.obj2Str(roleUserRes));
        externalRoleService.updateExternalRole(externalRole);
    }

    /**
     * @param connector
     * @param tag
     * @return
     */
    private Set<String> localTagExternalUserIds(Connector connector, TagEntity tag) {
        List<Long> roleUserIs = null;
        if (TagTypeEnum.DYNAMIC.getValue() == tag.getType()) {
            roleUserIs = tagService.analysisDynamicTagExpression(tag.getExpression());
        } else {
            roleUserIs = tagService.getUserTagListByTagId(tag.getId());
        }

        //最近一次需要添加到角色的用户
        Set<String> externalUserIds = new HashSet<>(1);

        if (CollectionUtils.isNotEmpty(roleUserIs)) {
            List<ExternalUserEntity> externalRoleUsers = externalUserService.getByLocalUserId(connector.getId(), roleUserIs, Arrays.asList(SyncDirectionEnum.PUSH.getValue()));
            externalUserIds = externalRoleUsers.stream().map(ExternalUserEntity::getExternalId).collect(Collectors.toSet());
        }
        return externalUserIds;
    }

    @Override
    public void removeUserToRole(PushConnectorEntity connectorEntity, Connector connector, boolean isForceFullSync, SyncStats syncStats, boolean pushRole) {
        // 获取角色下需要同步过去的用户信息
        List<Long> allGroupIds = tagGroupService.getAllTagGroupEntity().stream().map(TagGroupEntity::getId).collect(Collectors.toList());
        List<TagEntity> allTags = tagService.getTagByGroupIds(allGroupIds);
        for (TagEntity tag : allTags) {
            removeUserToRole(tag, connector, pushRole);
        }
    }

    private void removeUserToRole(TagEntity tag, Connector connector, boolean pushRole) {
        ExternalRoleEntity externalRole = externalRoleService.getByLocalId(connector.getId(), tag.getId());
        if (externalRole == null) {
            return;
        }
        //已经推送给下游角色的用户
        List<String> alreadyPushUser = JsonUtil.str2List(externalRole.getUserIds(), String.class);

        // 执行添加操作
        Set<String> roleUserRes;
        if (CollectionUtils.isNotEmpty(alreadyPushUser)) {
            roleUserRes = new HashSet<>(alreadyPushUser);
        } else {
            roleUserRes = new HashSet<>();
        }

        Set<String> needDelete = new HashSet<>();
        if (pushRole) {
            Set<String> externalUserIds = localTagExternalUserIds(connector, tag);
            // 确定需要删除的用户
            if (CollectionUtils.isNotEmpty(alreadyPushUser)) {
                needDelete.addAll(new HashSet<>(alreadyPushUser));
                needDelete.removeAll(externalUserIds);
            }
        } else {
            needDelete.addAll(alreadyPushUser);
        }


        // 执行删除操作
        if (CollectionUtils.isNotEmpty(needDelete)) {
            try {
                logger.info("remove user from role, roleId:{},userIds:{}", externalRole.getExternalId(), needDelete);
                externalConnectService.removeUserFromRole(connector, externalRole.getExternalId(), new ArrayList<>(needDelete));
                roleUserRes.removeAll(needDelete);
            } catch (Exception e) {
                logger.error("remove user from role error, roleId:{},userIds:{}", externalRole.getExternalId(), needDelete);
            }
        }

        externalRole.setUserIds(JsonUtil.obj2Str(roleUserRes));
        externalRoleService.updateExternalRole(externalRole);
    }

    @Override
    public void completeFullPushRoles(PushConnectorEntity connectorEntity, Connector connector, boolean isForceFullSync, SyncStats syncStats, boolean pushRole) {
        List<ExternalRoleEntity> toBeDeletedRoles = null;
        if (pushRole) {
            toBeDeletedRoles = externalRoleService.findToBeDeletedRole(connector);
        } else {
            toBeDeletedRoles = externalRoleService.getAllExternalRoles(connector);
        }

        for (ExternalRoleEntity externalRole : toBeDeletedRoles) {
            try {

                externalRoleService.remove(externalRole.getId());

                if (StringUtils.isBlank(externalRole.getExternalGroupId())) {
                    // 删除角色组
                    externalConnectService.deleteExternalRole(externalRole.getExternalId(), connector);
                    syncStats.deletedRoleGroup++;
                } else {
                    //删除角色先删除下游的角色用户
                    List<String> userIds = JsonUtil.str2List(externalRole.getUserIds(), String.class);
                    if (CollectionUtils.isNotEmpty(userIds)) {
                        externalConnectService.removeUserFromRole(connector, externalRole.getExternalId(), userIds);
                    }
                    externalConnectService.deleteExternalRole(externalRole.getExternalId(), connector);
                    syncStats.deletedRole++;
                }
                logger.info("delete push role or roleGroup : {}", externalRole.getProfile());


            } catch (Exception e) {
                syncStats.failedRole++;
                logger.error("completeFullPushRoles error", e);
                recordError(connectorEntity.getId(), connector.getTaskId(), externalRole.getExternalName(), externalRole.getProfile(), e.getMessage(), BindingTargetType.ROLE.name());
                //记录错误信息
            }
        }
    }

    private String pushCreateOrUpdateRoleGroup(PushConnectorEntity connectorEntity, Connector connector, RoleGroupEntity tagGroup, SyncStats syncStats, boolean updateBatchNo) {
        // 确认是更新还是修改
        ExternalRoleEntity externalRole = externalRoleService.getByLocalId(connectorEntity.getId(), tagGroup.getId());
        boolean isCreate = false;
        boolean forceWrite = BooleanEnums.isTrue(connector.getForceWrite());
        ConnectorRole oldRole = null;
        try {
            if (externalRole == null) {
                isCreate = true;
            } else {
                try {
                    oldRole = externalConnectService.getExternalRoleById(externalRole.getExternalId(), connector);
                } catch (ThirdPartyRoleGroupExistException e) {
                    logger.error("pushCreateOrUpdateRoleGroup error", e);
                    if (forceWrite) {
                        isCreate = true;
                    } else {
                        throw new ThirdPartyAccountException("第三方角色组已存在");
                    }
                } catch (Exception e) {
                    throw new ThirdPartyAccountException("第三方角色组已存在");
                }
            }

            Map<String, Object> localMapping = JsonUtil.obj2Map(tagGroup);

            LocalRole localRole = new LocalRole() {
            };
            localRole.putAll(localMapping);


            String roleId = null;
            String profile = "";
            ConnectorRole<String, Object> newExternalRole;
            if (isCreate) {
                // 创建外部角色
                newExternalRole = externalConnectService.toConnectorRole(localRole, null, connector, false);
                roleId = externalConnectService.createExternalGroup(newExternalRole, connector);
                newExternalRole.setIdValue(roleId);
                profile = JsonUtil.obj2Str(newExternalRole);
                syncStats.addedRoleGroup++;

            } else {
                // 更新外部角色
                newExternalRole = externalConnectService.toConnectorRole(localRole, oldRole, connector, true);
                MappingParser.mergeMaps(newExternalRole, oldRole);
                roleId = externalConnectService.modifyExternalRole(externalRole.getExternalId(), newExternalRole, connector, true);
                newExternalRole.setIdValue(roleId);
                profile = JsonUtil.obj2Str(newExternalRole);
                syncStats.updatedRoleGroup++;
            }
            externalRoleService.saveOrUpdatePushResult(externalRole, newExternalRole, tagGroup.getId(), connector, profile,0);
            logger.info("push data to connector,modify sns group code to db the role id :{}, name:{} ,third party code is :{}", tagGroup.getId(), tagGroup.getName(), newExternalRole.getIdValue());
            return roleId;
        } catch (Exception e) {
            if (externalRole != null && updateBatchNo) {
                externalRoleService.updateExternalRole(externalRole, connector.getSyncBatchNo());
            }
            logger.error("pushCreateOrUpdateRoleGroup error", e);
            syncStats.failedRoleGroup++;
            recordError(connectorEntity.getId(), connector.getTaskId(), tagGroup.getName(), JsonUtil.obj2Str(oldRole), e.getMessage(), BindingTargetType.ROLE_GROUP.name());
        }
        return null;
    }

    private void pushCreateOrUpdateRole(PushConnectorEntity connectorEntity, Connector connector, TagEntity tag, SyncStats syncStats, boolean updateBatchNo) {
        // 确认创建 还是  修改
        ExternalRoleEntity externalRole = externalRoleService.getByLocalId(connectorEntity.getId(), tag.getId());
        boolean isCreate = false;
        ConnectorRole oldRole = null;
        boolean forceWrite = BooleanEnums.isTrue(connector.getForceWrite());
        try {
            if (externalRole == null) {
                isCreate = true;
            } else {
                try {
                    oldRole = externalConnectService.getExternalRoleById(externalRole.getExternalId(), connector);
                } catch (ThirdPartyRoleGroupExistException e) {
                    logger.error("get external role  error {}", e.getMessage());
                    if (forceWrite) {
                        isCreate = true;
                    } else {
                        throw new UserCenterException("第三方角色组已存在");
                    }
                } catch (Exception e) {
                    throw new UserCenterException("第三方角色组已存在");
                }
            }

            Map<String, Object> localMapping = tagService.getLocalMappingTag(tag.getId());

            LocalRole localRole = new LocalRole() {
            };
            localRole.putAll(localMapping);


            String roleId = null;
            ConnectorRole<String, Object> newExternalRole;
            String profile = "";
            if (isCreate) {
                // 创建外部角色
                newExternalRole = externalConnectService.toConnectorRole(localRole, null, connector, false);

                ExternalRoleEntity externalRoleGroup = getRoleGroup(tag, connector);
                updateExternalRoleGroup(newExternalRole, connector, localRole, syncStats, updateBatchNo, connectorEntity, externalRoleGroup);
                try {
                    roleId = externalConnectService.createExternalRole(newExternalRole, connector);
                } catch (ThirdPartyRoleGroupExistException e) {
                    logger.error("create external role  error {}", e.getMessage());
                    // 角色组异常
                    updateExternalRoleGroup(newExternalRole, connector, localRole, syncStats, updateBatchNo, connectorEntity, null);
                    roleId = externalConnectService.createExternalRole(newExternalRole, connector);
                }
                newExternalRole.setIdValue(roleId);
                profile = JsonUtil.obj2Str(newExternalRole);
                syncStats.addedRole++;
            } else {
                // 更新外部角色
                newExternalRole = externalConnectService.toConnectorRole(localRole, oldRole, connector, true);
                ExternalRoleEntity externalRoleGroup = getRoleGroup(tag, connector);
                updateExternalRoleGroup(newExternalRole, connector, localRole, syncStats, updateBatchNo, connectorEntity, externalRoleGroup);
                try {
                    MappingParser.mergeMaps(newExternalRole, oldRole);
                    roleId = externalConnectService.modifyExternalRole(externalRole.getExternalId(), newExternalRole, connector, false);
                } catch (ThirdPartyRoleGroupExistException e) {
                    logger.error("update external role  error {}", e.getMessage());
                    updateExternalRoleGroup(newExternalRole, connector, localRole, syncStats, updateBatchNo, connectorEntity, null);
                    MappingParser.mergeMaps(newExternalRole, oldRole);
                    roleId = externalConnectService.modifyExternalRole(externalRole.getExternalId(), newExternalRole, connector, false);
                }
                newExternalRole.setIdValue(roleId);
                profile = JsonUtil.obj2Str(newExternalRole);
                syncStats.updatedRole++;
            }
            externalRoleService.saveOrUpdatePushResult(externalRole, newExternalRole, tag.getId(), connector, profile,1);
            logger.info("push data to connector,modify sns group code to db the role id :{}, name:{} ,third party code is :{}", tag.getId(), tag.getName(), newExternalRole.getIdValue());
        } catch (Exception e) {
            if (externalRole != null && updateBatchNo) {
                externalRoleService.updateExternalRole(externalRole, connector.getSyncBatchNo());
            }
            logger.error("pushCreateOrUpdateRole error", e);
            syncStats.failedRole++;
            recordError(connectorEntity.getId(), connector.getTaskId(), tag.getName(), JsonUtil.obj2Str(oldRole), e.getMessage(), BindingTargetType.ROLE.name());
        }
    }

    private void updateExternalRoleGroup(ConnectorRole<String, Object> newExternalRole, Connector connector, LocalRole localRole, SyncStats syncStats, boolean updateBatchNo, PushConnectorEntity connectorEntity, ExternalRoleEntity externalRoleGroup) {
        if (newExternalRole.get(CommonDingRoleAttr.groupId.getAttrName()) != null) {
            List<RoleGroupEntity> roleGroups = (List<RoleGroupEntity>) localRole.get(LocalRoleAttr.connector_role_groups.getDomainName());
            if (externalRoleGroup == null || StringUtils.isBlank(externalRoleGroup.getExternalId())) {
                for (RoleGroupEntity roleGroup : roleGroups) {
                    String roleGroupCode = pushCreateOrUpdateRoleGroup(connectorEntity, connector, roleGroup, syncStats, updateBatchNo);
                    newExternalRole.setGroupIdValue(roleGroupCode);
                }
            } else {
                List<RoleGroupEntity> collect = roleGroups.stream().filter(roleGroup -> roleGroup.getId().equals(externalRoleGroup.getIamId())).collect(Collectors.toList());
                pushCreateOrUpdateRoleGroup(connectorEntity, connector, collect.get(0), syncStats, updateBatchNo);
                newExternalRole.setGroupIdValue(externalRoleGroup.getExternalId());
            }
        }
    }

    private ExternalRoleEntity getRoleGroup(TagEntity tag, Connector connector) {
        List<Long> groupIds = JsonUtil.str2List(tag.getRoleGroups(), Long.class);
        if (CollectionUtils.isEmpty(groupIds)) {
            throw new UserCenterException("需要同步角色组 但是 " + tag.getName() + "角色组为空");
        }
        return externalRoleService.getByLocalId(connector.getId(), groupIds.get(0), DaoConstants.EXTERNAL_ID);
    }

    /**
     * 记录同步错误信息
     *
     * @param connectorId
     * @param taskId
     * @param externalName
     * @param source
     * @param errorContent
     * @param type
     */
    private void recordError(Long connectorId, Long taskId, String externalName, String source, String errorContent, String type) {
        SyncErrorEntity syncError = new SyncErrorEntity();
        syncError.setSyncDirection(SyncDirectionEnum.PUSH.getValue());
        syncError.setSource(source);
        syncError.setErrorContent(errorContent);
        syncError.setStatus(0);
        syncError.setType(type);
        syncError.setConnectorId(connectorId);
        syncError.setTaskId(taskId);
        syncError.setExternalName(externalName);
        syncError.setCreateTime(LocalDateTime.now());
        errorService.saveSyncError(syncError);
    }
}
