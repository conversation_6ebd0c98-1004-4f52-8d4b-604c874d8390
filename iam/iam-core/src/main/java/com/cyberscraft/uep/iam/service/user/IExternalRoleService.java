package com.cyberscraft.uep.iam.service.user;

import com.cyberscraft.uep.account.client.domain.Connector;
import com.cyberscraft.uep.account.client.domain.ConnectorRole;
import com.cyberscraft.uep.iam.common.enums.SyncDirectionEnum;
import com.cyberscraft.uep.iam.entity.ExternalRoleEntity;

import java.util.List;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/7/11 16:56
 */
public interface IExternalRoleService {

    ExternalRoleEntity getRoleGroupByExternalGroupId(Long connectorId, String externalGroupId, String... attrs);

    ExternalRoleEntity getRoleByExternalId(Long connectorId, String externalId, String... attrs);

    ExternalRoleEntity getByLocalId(Long connectorId, Long localId, String... attrs);

    List<ExternalRoleEntity> getByLocalId(Long connectorId, List<Long> localIds, SyncDirectionEnum syncDirectionEnum, String... attrs);

    void saveOrUpdateSyncResult(ExternalRoleEntity externalRole, Map<String,Object> connectorRole, Long id, Connector connector, String profile, Integer roleType);

    void saveOrUpdatePushResult(ExternalRoleEntity externalRole, ConnectorRole connectorRole, Long id, Connector connector, String profile, Integer roleType);

    List<ExternalRoleEntity> findToBeDeletedRole(Connector connector);

    List<ExternalRoleEntity> getAllExternalRoles(Connector connector);

    void remove(Long id);

    /**
     * 修改角色组批次号
     *
     * @param externalRole
     * @param batchNo
     */
    void updateExternalRole(ExternalRoleEntity externalRole, Integer batchNo);


    /**
     * 更新角色信息
     *
     * @param externalRole
     */
    void updateExternalRole(ExternalRoleEntity externalRole);

    /**
     * 根据connector获取role
     * @param connectorId
     * @param type
     * @return
     */
    List<ExternalRoleEntity> getRolesByConnectorId(Long connectorId,Integer type);
}
