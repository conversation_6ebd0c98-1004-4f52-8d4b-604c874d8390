package com.cyberscraft.uep.iam.service.user;

import com.cyberscraft.uep.iam.entity.UserEntity;

import java.util.List;
import java.util.Map;

/***
 * SNS 用户加载处理器
 * @date 2021/7/20
 * <AUTHOR>
 ***/
public interface ISnsUserLoadHandler {

    /***
     *
     * @return
     */
    Boolean isInbound();

    /***
     * 根据第三方平台用户ID,获取IAM用户ID
     * @param snsUserId
     * @param type 第三方平台类型，参考连接器，及ThirdPartyAccountType定义
     * @return
     */
    Long getIamUserId(String snsUserId, Integer type);

    /***
     * 根据第三方平台用户ID,获取IAM用户ID
     * @param snsUnionId
     * @param type 第三方平台类型，参考连接器，及ThirdPartyAccountType定义
     * @return
     */
    UserEntity getIamUserByUnionId(String snsUnionId, Integer type);

    /***
     * 根据第三方平台用户ID,获取IAM用户ID
     * @param snsUserIds
     * @param  type 第三方平台类型，参考连接器，及ThirdPartyAccountType定义
     * @return
     */
    Map<String, Long> getIamUserIdsMap(List<String> snsUserIds, Integer type);


    /***
     * 根据第三方平台组代码,获取IAM组ID
     * @param snsGroupCode
     * @param type 第三方平台类型，参考连接器，及ThirdPartyAccountType定义
     * @return
     */
    Long getIamOrgId(String snsGroupCode, Integer type);

    /***
     * 根据第三方平台组代码,获取IAM组ID列表
     * @param snsGroupCodes
     * @param  type 第三方平台类型，参考连接器，及ThirdPartyAccountType定义
     * @return
     */
    Map<String, Long> getIamOrgIdsMap(List<String> snsGroupCodes, Integer type);


    /***
     * 根据IAM用户ID,第三方平台账户类型，南向或者北向连接类型，返回第三方平台用户ID
     * @param iamUserId
     * @param  type 第三方平台类型，参考连接器，及ThirdPartyAccountType定义
     * @return
     */
    String getSnsUserId(Long iamUserId, Integer type);

    /***
     * 根据IAM用户ID,第三方平台账户类型，南向或者北向连接类型，返回第三方平台用户ID列表
     * @param iamUserIds
     * @param  type 第三方平台类型，参考连接器，及ThirdPartyAccountType定义
     * @return
     */
    Map<Long, String> getSnsUserIdsMap(List<Long> iamUserIds, Integer type);

    /***
     * 根据IAM用户ID,第三方平台账户类型，南向或者北向连接类型，返回第三方平台用户ID列表
     * @param iamUserIds
     * @param  type 第三方平台类型，参考连接器，及ThirdPartyAccountType定义
     * @return
     */
    Map<Long, String> getSnsUnionIdsMap(List<Long> iamUserIds, Integer type);

    /***
     * 根据IAM组ID,第三方平台账户类型，南向或者北向连接类型，返回第三方平台组代码
     * @param iamOrgId
     * @param  type 第三方平台类型，参考连接器，及ThirdPartyAccountType定义
     * @return
     */
    String getSnsGroupCode(Long iamOrgId, Integer type);

    /***
     * 根据IAM组ID,第三方平台账户类型，南向或者北向连接类型，返回第三方平台组代码列表
     * @param iamOrgIds
     * @param  type 第三方平台类型，参考连接器，及ThirdPartyAccountType定义
     * @return
     */
    Map<Long, String> getSnsGroupCodesMap(List<Long> iamOrgIds, Integer type);

}
