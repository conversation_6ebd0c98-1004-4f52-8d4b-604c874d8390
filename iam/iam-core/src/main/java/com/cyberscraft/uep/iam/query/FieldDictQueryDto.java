package com.cyberscraft.uep.iam.query;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.cyberscraft.uep.iam.dto.enums.FieldType;

@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FieldDictQueryDto {
    private FieldType fieldType;
    private String filter;
    private Integer createMod;
    private Integer opConstraint;
    private Integer mandatory;
    private Integer extraAuthFactor;
    private Integer asProfile;
    private Integer asImport;
    private Integer asClaim;
    private Integer searchable;

    public FieldType getFieldType() {
        return fieldType;
    }

    public void setFieldType(FieldType fieldType) {
        this.fieldType = fieldType;
    }

    public String getFilter() {
        return filter;
    }

    public void setFilter(String filter) {
        this.filter = filter;
    }

    public Integer getCreateMod() {
        return createMod;
    }

    public void setCreateMod(Integer createMod) {
        this.createMod = createMod;
    }

    public Integer getOpConstraint() {
        return opConstraint;
    }

    public void setOpConstraint(Integer opConstraint) {
        this.opConstraint = opConstraint;
    }

    public Integer getMandatory() {
        return mandatory;
    }

    public void setMandatory(Integer mandatory) {
        this.mandatory = mandatory;
    }

    public Integer getExtraAuthFactor() {
        return extraAuthFactor;
    }

    public void setExtraAuthFactor(Integer extraAuthFactor) {
        this.extraAuthFactor = extraAuthFactor;
    }

    public Integer getAsProfile() {
        return asProfile;
    }

    public void setAsProfile(Integer asProfile) {
        this.asProfile = asProfile;
    }

    public Integer getAsImport() {
        return asImport;
    }

    public void setAsImport(Integer asImport) {
        this.asImport = asImport;
    }

    public Integer getAsClaim() {
        return asClaim;
    }

    public void setAsClaim(Integer asClaim) {
        this.asClaim = asClaim;
    }

    public Integer getSearchable() {
        return searchable;
    }

    public void setSearchable(Integer searchable) {
        this.searchable = searchable;
    }

    public static FieldDictQueryDto build(){
        return new FieldDictQueryDto();
    }

    public FieldDictQueryDto fieldType(FieldType fieldType){
        this.fieldType = fieldType;
        return this;
    }

    public FieldDictQueryDto filter(String filter){
        this.filter = filter;
        return this;
    }

    public FieldDictQueryDto createMod(Integer createMod){
        this.createMod = createMod;
        return this;
    }

    public FieldDictQueryDto opConstraint(Integer opConstraint){
        this.opConstraint = opConstraint;
        return this;
    }

    public FieldDictQueryDto mandatory(Integer mandatory){
        this.mandatory = mandatory;
        return this;
    }

    public FieldDictQueryDto extraAuthfactor(Integer extraAuthfactor){
        this.extraAuthFactor = extraAuthfactor;
        return this;
    }

    public FieldDictQueryDto asProfile(Integer asProfile){
        this.asProfile = asProfile;
        return this;
    }

    public FieldDictQueryDto asImport(Integer asImport){
        this.asImport = asImport;
        return this;
    }

    public FieldDictQueryDto asClaim(Integer asClaim){
        this.asClaim = asClaim;
        return this;
    }

    public FieldDictQueryDto searchable(Integer searchable){
        this.searchable = searchable;
        return this;
    }
}
