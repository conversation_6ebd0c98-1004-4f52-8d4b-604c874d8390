package com.cyberscraft.uep.iam.service.impl;

import com.cyberscraft.uep.common.bean.RestApiResponse;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.common.util.RestAPIUtil;
import com.cyberscraft.uep.common.util.SnowflakeIDUtil;
import com.cyberscraft.uep.iam.common.constants.ThreadPoolNameConstant;
import com.cyberscraft.uep.iam.dbo.HolidaysDBO;
import com.cyberscraft.uep.iam.dto.request.login.DailyTimeRange;
import com.cyberscraft.uep.iam.dto.request.login.DateTimeRange;
import com.cyberscraft.uep.iam.dto.request.login.DateType;
import com.cyberscraft.uep.iam.entity.HolidaysEntity;
import com.cyberscraft.uep.iam.service.IHolidaysService;
import org.apache.commons.collections4.CollectionUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2024/9/4 10:07
 * @Version 1.0
 * @Description 节假日服务接口实现类
 */
@Service
@RefreshScope
public class HolidaysServiceImpl implements IHolidaysService {

    private static final Logger logger = LoggerFactory.getLogger(HolidaysServiceImpl.class);

    @Value("${third.tool.holidays.key:test}")
    private String key;

    @Resource
    private HolidaysDBO holidaysDBO;

    @Resource(name = ThreadPoolNameConstant.COMMON_POOL_NAME)
    private ExecutorService commonPoolExecutor;


    @Resource
    private RedissonClient redissonClient;


    @Override
    public List<HolidaysEntity> getAllHolidaysByYear(int year) {
        List<HolidaysEntity> holidaysEntityList = holidaysDBO.getAllHolidaysByYear(year);
        if (CollectionUtils.isNotEmpty(holidaysEntityList)) {
            return holidaysEntityList;
        }
        List<HolidaysEntity> resp = null;
        RLock lock = redissonClient.getLock("HOLIDAYS:2025");
        boolean unlock = true;
        try {
            unlock = lock.tryLock(5, 5, TimeUnit.SECONDS);
            if (!unlock) {
                logger.info("get holidays failed, cannot get lock");
                return new ArrayList<>();
            }
            resp = getHolidaysDataFromThirdParty(year);
        } catch (InterruptedException e) {
            logger.info("get holidays failed, lock err {}", e.getMessage());
        } finally {
            if (unlock) {
                lock.unlock();
            }
        }
        return resp;
    }


    @Override
    public boolean isCurrentTimeOnHoliday(LocalDate currentDate) {
        int year = currentDate.getYear();
        List<HolidaysEntity> holidays = getAllHolidaysByYear(year);
        if (CollectionUtils.isEmpty(holidays)) {
            return false;
        }
        for (HolidaysEntity holiday : holidays) {
            List<String> vacationList = JsonUtil.str2List(holiday.getVacation(), String.class);
            if (vacationList.contains(currentDate.toString())) {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean isCurrentTimeOnWeekend(LocalDate currentDate) {
        DayOfWeek currentDayOfWeek = currentDate.getDayOfWeek();
        return DayOfWeek.SATURDAY.equals(currentDayOfWeek) || DayOfWeek.SUNDAY.equals(currentDayOfWeek);
    }

    @Override
    public boolean isCurrentTimeWithinBaseDateRules(List<DateType> baseDatePolicy, LocalDateTime currentDateTime) {
        if (CollectionUtils.isEmpty(baseDatePolicy)) {
            return false;
        }
        LocalDate currentDate = currentDateTime.toLocalDate();
        for (DateType dateType : baseDatePolicy) {
            switch (dateType) {
                case WEEKEND:
                    return isCurrentTimeOnWeekend(currentDate);
                case LEGAL_HOLIDAY:
                    return isCurrentTimeOnHoliday(currentDate);
            }
        }
        return false;
    }

    @Override
    public boolean isCurrentTimeWithinDailyRules(List<DailyTimeRange> rules, LocalDateTime currentDateTime) {
        if (CollectionUtils.isEmpty(rules)) {
            return false;
        }
        DayOfWeek currentDayOfWeek = currentDateTime.getDayOfWeek();
        LocalTime currentTime = currentDateTime.toLocalTime().withSecond(0).withNano(0);

        for (DailyTimeRange rule : rules) {
            if (rule.getDayOfWeek().contains(currentDayOfWeek)) {
                LocalTime startTime = rule.getRangeTime().get(0);
                LocalTime endTime = rule.getRangeTime().get(1);
                if ((currentTime.isAfter(startTime) || currentTime.equals(startTime)) &&
                        (currentTime.isBefore(endTime) || currentTime.equals(endTime))) {
                    logger.info("customDayMfaPolicies currentTime:{}, rule:{}", currentTime, JsonUtil.obj2Str(rule));
                    return true;
                }
            }
        }
        return false;
    }


    @Override
    public boolean isCurrentTimeWithinDateRules(List<DateTimeRange> rules, LocalDateTime currentDateTime) {
        if (CollectionUtils.isEmpty(rules)) {
            return false;
        }
        LocalDate currentDate = currentDateTime.toLocalDate();
        LocalTime currentTime = currentDateTime.toLocalTime().withSecond(0).withNano(0);
        for (DateTimeRange rule : rules) {
            LocalDate startDate = rule.getRangeDate().get(0);
            LocalDate endDate = rule.getRangeDate().get(1);
            LocalTime startTime = rule.getRangeTime().get(0);
            LocalTime endTime = rule.getRangeTime().get(1);

            if ((currentDate.isAfter(startDate) || currentDate.isEqual(startDate)) &&
                    (currentDate.isBefore(endDate) || currentDate.isEqual(endDate)) &&
                    (currentTime.isAfter(startTime) || currentTime.equals(startTime)) &&
                    (currentTime.isBefore(endTime) || currentTime.equals(endTime))) {
                logger.info("specialDayMfaPolicies currentDate:{} currentTime:{}, rule:{}", currentDate, currentTime, JsonUtil.obj2Str(rule));
                return true;
            }
        }

        return false;
    }

    private List<HolidaysEntity> getHolidaysDataFromThirdParty(int year) {
        // 如按年查询（type=1&date=2020）、按月查询（type=2&date=2020-10）、按日期范围查询（type=3&date=2020-11-1~2020-11-10）、按多个日期批量查询（date=2020-10-1,2020-11-12）
        // 请注意，按年查询只返回中国官方节假日信息；按月查询如只传年月，则为从该月第一天到最后一天；按范围查询，起点和终点用波浪号~分隔；按多个日期批量查询用英文半角逗号分隔。无论哪种查询方式，单次查询所包含的总日期数不能超过31天。
        String url = "https://apis.tianapi.com/jiejiari/index";
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/x-www-form-urlencoded;charset=UTF-8");
        Map<String, Object> requestParams = new HashMap<>(16);
        requestParams.put("key", key);
        // 查询日期或日期范围
        requestParams.put("date", year);
        // 查询类型，0批量、1按年、2按月、3按范围
        requestParams.put("type", 1);
        // 查询模式，为1时同时返回中外特殊节日信息，按年查询时该参数无效
//        requestParams.put("mode", 0);
        ArrayList<HolidaysEntity> holidaysEntities = null;
        try {
            RestApiResponse apiResponse = RestAPIUtil.modifyEntityForString("", url, HttpMethod.POST.name(), null, header, requestParams, null);
            if (apiResponse.getHttpStatus() != 200) {
                logger.error("获取节假日数据失败：{}", apiResponse.getBody());
                return Collections.emptyList();
            }
            Map<String, Object> response;
            if (apiResponse.getBody() instanceof String) {
                // 如果响应体是字符串类型，尝试将其转换为Map
                String responseBodyStr = (String) apiResponse.getBody();
                response = JsonUtil.str2Map(responseBodyStr);
            } else {
                response = (Map<String, Object>) apiResponse.getBody();
            }
            Object code = response.get("code");
            if (!Objects.equals(code, 200)) {
                logger.error("获取节假日数据失败：{}", response);
                return Collections.emptyList();
            }
            Map<String, Object> result = (Map<String, Object>) response.get("result");
            if (result.get("update").toString().equals("false")) {
                logger.info("{} 年无节假日数据", year);
                return Collections.emptyList();
            }
            List<Map<String, Object>> respholidayList = (List<Map<String, Object>>) result.get("list");
            holidaysEntities = new ArrayList<>();
            for (Map<String, Object> holiday : respholidayList) {
                HolidaysEntity holidaysEntity = new HolidaysEntity();
                holidaysEntity.setId(SnowflakeIDUtil.getId());
                holidaysEntity.setName(holiday.get("name").toString());
                holidaysEntity.setYear(year);
                String[] vacations = holiday.get("vacation").toString().split("\\|");
                List<String> valist = Arrays.asList(vacations);
                holidaysEntity.setVacation(JsonUtil.obj2Str(valist));
                holidaysEntities.add(holidaysEntity);
            }
            List<HolidaysEntity> finalHolidaysEntities = holidaysEntities;
            // 异步保存数据
            CompletableFuture.runAsync(() -> holidaysDBO.saveBatch(finalHolidaysEntities), commonPoolExecutor);
        } catch (Exception e) {
            logger.error("获取节假日数据失败：{}", e.getMessage());
            return Collections.emptyList();
        }

        return holidaysEntities;
    }
}
