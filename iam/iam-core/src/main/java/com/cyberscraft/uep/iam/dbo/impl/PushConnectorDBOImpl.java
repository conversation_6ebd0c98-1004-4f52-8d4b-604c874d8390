package com.cyberscraft.uep.iam.dbo.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cyberscraft.uep.common.constant.SysConstant;
import com.cyberscraft.uep.common.dto.IQueryConditonApply;
import com.cyberscraft.uep.common.dto.PageView;
import com.cyberscraft.uep.common.dto.Pagination;
import com.cyberscraft.uep.common.util.DateUtil;
import com.cyberscraft.uep.common.util.PagingUtil;
import com.cyberscraft.uep.iam.dbo.ConnectorPushHistoryDBO;
import com.cyberscraft.uep.iam.entity.ConnectorEntity;
import com.cyberscraft.uep.iam.entity.ConnectorPushHistoryEntity;
import com.cyberscraft.uep.iam.entity.PushConnectorEntity;
import com.cyberscraft.uep.iam.dao.PushConnectorDao;
import com.cyberscraft.uep.iam.dbo.PushConnectorDBO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cyberscraft.uep.iam.dto.domain.config.PushConnectorQueryDTO;
import com.cyberscraft.uep.iam.dto.enums.PushConnectorStatusEnum;
import com.cyberscraft.uep.iam.service.connector.ConnectorSyncTypeEnum;
import com.cyberscraft.uep.iam.dto.enums.SyncStatus;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * IAM-数据推送链接器 服务实现类
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2020-06-12
 */
@Service
public class PushConnectorDBOImpl extends ServiceImpl<PushConnectorDao, PushConnectorEntity> implements PushConnectorDBO {

    @Resource
    protected ConnectorPushHistoryDBO connectorPushHistoryDBO;

    /***
     * 进行条件转换处理
     */
    private IQueryConditonApply<PushConnectorQueryDTO, PushConnectorEntity> queryApply = (queryDto, queryWrapper) -> {
        if (queryDto.getType() != null) {
            queryWrapper.eq(PushConnectorEntity::getType, queryDto.getType());
        }
        if (queryDto.getIsDeleted() != null && SysConstant.TRUE_VALUE.equals(queryDto.getIsDeleted())) {
            queryWrapper.eq(PushConnectorEntity::getStatus, PushConnectorStatusEnum.DELETED.getValue());
        } else if (queryDto.getIsDeleted() != null && SysConstant.FALSE_VALUE.equals(queryDto.getIsDeleted())) {
            queryWrapper.ne(PushConnectorEntity::getStatus, PushConnectorStatusEnum.DELETED.getValue());
        }
        if (queryDto.getStatus() != null) {
            queryWrapper.eq(PushConnectorEntity::getStatus, queryDto.getStatus());
        }
        if (StringUtils.isNotBlank(queryDto.getQ())) {
            queryWrapper.nested((query) ->
                    query.like(PushConnectorEntity::getName, queryDto.getQ())
            );
        }
    };

    @Override
    public PushConnectorEntity getByName(String name) {
        LambdaQueryWrapper<PushConnectorEntity> queryWrapper = new QueryWrapper<PushConnectorEntity>().lambda();
        queryWrapper.eq(PushConnectorEntity::getName, name);
        List<PushConnectorEntity> list = super.list(queryWrapper);
        return list != null && list.size() > 0 ? list.get(0) : null;
    }

    @Override
    public List<PushConnectorEntity> getListByStatus(Integer status) {
        LambdaQueryWrapper<PushConnectorEntity> queryWrapper = new QueryWrapper<PushConnectorEntity>().lambda();
        queryWrapper.eq(PushConnectorEntity::getStatus, status);
        return super.list(queryWrapper);
    }

    @Override
    public List<PushConnectorEntity> getListByStatus(List<Long> ids, Integer status) {
        LambdaQueryWrapper<PushConnectorEntity> queryWrapper = new QueryWrapper<PushConnectorEntity>().lambda();
        queryWrapper.eq(PushConnectorEntity::getStatus, status);
        queryWrapper.in(PushConnectorEntity::getId, ids);
        return super.list(queryWrapper);
    }

    @Override
    public List<PushConnectorEntity> listPushConnectors(PushConnectorEntity pushConnectorEntity) {
        QueryWrapper<PushConnectorEntity> pushConnectorEntityQueryWrapper = new QueryWrapper<>(pushConnectorEntity);
        return super.list(pushConnectorEntityQueryWrapper);
    }

    @Override
    public void updateNextPushTime(Long id, LocalDateTime nextPushTime) {
        LambdaUpdateWrapper<PushConnectorEntity> queryWrapper = new UpdateWrapper<PushConnectorEntity>().lambda();
        queryWrapper.eq(PushConnectorEntity::getId, id);
        queryWrapper.set(PushConnectorEntity::getPushEndTime, LocalDateTime.now());
        queryWrapper.set(PushConnectorEntity::getNextPushTime, nextPushTime);
        super.update(queryWrapper);
    }

    @Override
    public PageView<PushConnectorEntity> page(Pagination<PushConnectorQueryDTO> queryPage) {
        Page<PushConnectorEntity> page = PagingUtil.toMybatisPage(queryPage);
        String[] selectStr = queryPage.getRequestAttrs() != null ? queryPage.getRequestAttrs().toArray(new String[0]) : new String[0];
        //TODO 实现对应的查询条件
        QueryWrapper<PushConnectorEntity> queryWrapper = new QueryWrapper().select(selectStr);//.lambda();
        PushConnectorQueryDTO queryDto = queryPage.getQueryDto();
        LambdaQueryWrapper<PushConnectorEntity> lambdaQueryWrapper = queryWrapper.lambda();
        //设置排序条件
        if (page.getOrders() == null || page.getOrders().isEmpty()) {
            lambdaQueryWrapper.orderByDesc(PushConnectorEntity::getId);
        }
        //应用对应的条件
        queryApply.apply(queryDto, lambdaQueryWrapper);

        IPage<PushConnectorEntity> resultPage = baseMapper.selectPage(page, lambdaQueryWrapper);

        return PagingUtil.toPageView(resultPage);
    }

    @Override
    public int countByConfigId(Long configId) {
        LambdaQueryWrapper<PushConnectorEntity> queryWrapper = new QueryWrapper<PushConnectorEntity>().lambda();
        queryWrapper.eq(PushConnectorEntity::getConfigId, configId);
        return super.count(queryWrapper);
    }

    @Override
    public List<PushConnectorEntity> getListByConfigId(Long configId) {
        QueryWrapper<PushConnectorEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(PushConnectorEntity::getConfigId, configId);
        return super.list(queryWrapper);
    }

    @Override
    public void updateSyncBatchNo(Long id, Integer syncBatchNo) {
        LocalDateTime currentTime = LocalDateTime.now();
        LambdaUpdateWrapper<PushConnectorEntity> updateWrapper = new UpdateWrapper<PushConnectorEntity>()
                .lambda()
                .eq(PushConnectorEntity::getId, id)
                .set(PushConnectorEntity::getPushBatchNo, syncBatchNo)
                .set(PushConnectorEntity::getPushTime, currentTime)
                .set(PushConnectorEntity::getPushStartTime, currentTime)
                .set(PushConnectorEntity::getUpdateTime, currentTime);
        this.update(updateWrapper);
    }

    @Override
    public void updateSyncBatchNoAndNextTime(Long id, Integer syncBatchNo, LocalDateTime nextPushTime) {
        LocalDateTime currentTime = LocalDateTime.now();
        LambdaUpdateWrapper<PushConnectorEntity> updateWrapper = new UpdateWrapper<PushConnectorEntity>()
                .lambda()
                .eq(PushConnectorEntity::getId, id)
                .set(PushConnectorEntity::getPushBatchNo, syncBatchNo)
                .set(PushConnectorEntity::getNextPushTime, nextPushTime)
                .set(PushConnectorEntity::getPushTime, currentTime)
                .set(PushConnectorEntity::getPushStartTime, currentTime)
                .set(PushConnectorEntity::getUpdateTime, currentTime);
        this.update(updateWrapper);
    }

    @Override
    public boolean createConnectorHistory(ConnectorPushHistoryEntity pushHistoryEntity) {
        return connectorPushHistoryDBO.save(pushHistoryEntity);
    }
}
