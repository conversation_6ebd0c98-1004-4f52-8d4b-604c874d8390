package com.cyberscraft.uep.iam.service.impl;

import com.cyberscraft.uep.iam.dbo.DingAuditSyncLogDBO;
import com.cyberscraft.uep.iam.entity.DingAuditSyncLogEntity;
import com.cyberscraft.uep.iam.service.IDingAuditSyncLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class DingAuditSyncLogServiceImpl implements IDingAuditSyncLogService {

    @Autowired
    private DingAuditSyncLogDBO dingAuditSyncLogDBO;

    @Override
    public Boolean saveOrUpdateDingAuditSyncLog(DingAuditSyncLogEntity dingAuditSyncLogEntity) {
        return dingAuditSyncLogDBO.saveOrUpdate(dingAuditSyncLogEntity);
    }

    @Override
    public DingAuditSyncLogEntity getDingAuditSyncLogEntity(Long configId) {
        DingAuditSyncLogEntity dingAuditSyncLogEntity = dingAuditSyncLogDBO.getByConfigId(configId);
        if (dingAuditSyncLogEntity == null) {
            dingAuditSyncLogEntity = new DingAuditSyncLogEntity();
            dingAuditSyncLogEntity.setConfigId(configId);
        }
        return dingAuditSyncLogEntity;
    }
}
