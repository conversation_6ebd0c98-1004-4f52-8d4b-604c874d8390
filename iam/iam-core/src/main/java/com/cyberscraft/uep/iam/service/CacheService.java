package com.cyberscraft.uep.iam.service;

import com.cyberscraft.uep.iam.common.constants.CacheNameConstant;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.stereotype.Service;

/**
 * @Description 本体缓存设置
 * <AUTHOR>
 * @Date 2024/5/28 18:52
 */
@Service
public class CacheService {

    @Autowired
    @Qualifier(value = CacheNameConstant.USER_UNIQUE_ATTR_CACHE_NAME)
    private CacheManager cacheManager;

    /**
     * 从缓存获取内容
     *
     * @param cacheName 缓存名称
     * @param key       键
     * @param type      数据类型
     * @param <T>
     * @return
     */
    public synchronized  <T> T getFromCache(String cacheName, Object key, Class<T> type) {
        Cache cache = cacheManager.getCache(cacheName);
        if (cache != null) {
            Cache.ValueWrapper valueWrapper = cache.get(key);
            if (valueWrapper != null) {
                return type.cast(valueWrapper.get());
            }
        }
        return null;
    }

    /**
     * 更新缓存内容
     *
     * @param cacheName 缓存名称
     * @param key       键
     * @param value     值
     */
    public synchronized void putInCache(String cacheName, Object key, Object value) {
        Cache cache = cacheManager.getCache(cacheName);
        if (cache != null) {
            cache.put(key, value);
        }
    }

    /**
     * 清除缓存内容
     *
     * @param cacheName 缓存名
     * @param key       键
     */
    public synchronized void evictFromCache(String cacheName, Object key) {
        Cache cache = cacheManager.getCache(cacheName);
        if (cache != null) {
            cache.evict(key);
        }
    }

    /**
     * 根据缓存名清除缓存内容
     *
     * @param cacheName 缓存名
     */
    public synchronized void clearCache(String cacheName) {
        Cache cache = cacheManager.getCache(cacheName);
        if (cache != null) {
            cache.clear();
        }
    }
}
