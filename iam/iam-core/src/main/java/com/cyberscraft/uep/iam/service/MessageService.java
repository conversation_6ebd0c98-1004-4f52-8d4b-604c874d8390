package com.cyberscraft.uep.iam.service;

import com.cyberscraft.uep.common.config.ServerConfig;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.iam.common.constants.DaoConstants;
import com.cyberscraft.uep.iam.dto.response.TemplateVO;
import com.cyberscraft.uep.ncm.client.domain.Message;
import com.cyberscraft.uep.ncm.client.domain.message.MessageBodyText;
import com.cyberscraft.uep.ncm.client.service.INotificationClientService;
import com.cyberscraft.uep.ncm.enums.*;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;

/**
 * @Description 消息通知
 * <AUTHOR>
 * @Date 2025/1/3 18:25
 */
@Service("MessageService")
public class MessageService {

    private final static Logger LOG = LoggerFactory.getLogger(MessageService.class);

    @Autowired
    private IMsgTemplateService msgTemplateService;

    @Autowired
    private IConfigService configService;

    @Autowired
    private INotificationClientService notificationClientService;

    @Resource
    private ServerConfig serverConfig;

    public void sendPwdExpireMsg(String userId, LocalDateTime expireTime) {
        TemplateVO template = getTemplate(DaoConstants.EXPIRE_PWD_MSG);
        if (template == null) {
            return;
        }
        String content = template.getContent();
        if (StringUtils.isNotBlank(content)) {
            Duration between = Duration.between(LocalDateTime.of(LocalDate.now(), LocalTime.MAX), LocalDateTime.of(expireTime.toLocalDate(), LocalTime.MAX));
            long days = between.toDays();
            if (days > 0) {
                content = content.replace("{days}", String.valueOf(Math.abs(days)));
                sendTextMsg(Arrays.asList(userId), template.getTitle(), content, template);
            } else {
                LOG.info("当前用户【 " + userId + "】 密码有效期小于1天 不发送提醒");
            }
        }
    }

    public void sendAdminResetPwdMsg(List<String> userIds, String username, String passwd) {
        TemplateVO template = getTemplate(DaoConstants.ADMIN_RESET_PWD_MSG);
        if (template == null) {
            return;
        }
        String content = template.getContent();
        if (StringUtils.isNotBlank(content)) {
            content = content.replace("{loginName}", username).replace("{password}", passwd);
        }
        sendTextMsg(userIds, template.getTitle(), content, template);

    }

    public void sendResetPwdMsg(List<String> userIds, String passwd) {
        TemplateVO template = getTemplate(DaoConstants.RESET_PWD_MSG);
        if (template == null) {
            return;
        }
        String content = template.getContent();
        if (StringUtils.isNotBlank(content)) {
            content = content.replace("{password}", passwd);
        }
        sendTextMsg(userIds, template.getTitle(), content, template);
    }

    public void sendUpdatePwdMsg(List<String> userIds) {
        TemplateVO template = getTemplate(DaoConstants.UPDATE_PWD_MSG);
        if (template == null) {
            return;
        }
        String content = template.getContent();
        sendTextMsg(userIds, template.getTitle(), content, template);
    }

    private TemplateVO getTemplate(String templateName) {
        Map<String, Object> enterpriseConfig = configService.getEnterpriseConfig();
        Map<String, Object> config = (Map) enterpriseConfig.get(DaoConstants.ENTERPRISE_MSG_CONFIG);
        if (config == null) {
            LOG.info("{} 未开启 发送企业消息", TenantHolder.getTenantCode());
            return null;
        }
        if (config.get("enabled") == null || !(Boolean) config.get("enabled")) {
            LOG.info("{} 未开启 发送企业消息", TenantHolder.getTenantCode());
            return null;
        }
        TemplateVO template = msgTemplateService.getTemplate(templateName);
        if (template.getEnable() == null || !template.getEnable()) {
            LOG.info("{} 未开启 {}", TenantHolder.getTenantCode(), template.getTitle());
            return null;
        }
        return template;
    }


    private void sendTextMsg(List<String> userIds, String title, String content, TemplateVO templateVO) {
        Map<String, Object> enterpriseConfig = configService.getEnterpriseConfig();
        Map config = (Map) enterpriseConfig.get(DaoConstants.ENTERPRISE_MSG_CONFIG);
        Boolean enable = (Boolean) config.get("enabled");
        if (enable && templateVO.getEnable() != null && templateVO.getEnable()) {
            String flag = (String) config.get("flg");
            String configId = (String) config.get("config_id");
            String userAttr = (String) config.get("user_attr");
            Message message = generateMessage(userIds, TargetTypeEnum.of(flag), title, content, Long.valueOf(configId), userAttr);
            LOG.info("send message to mq :{}", JsonUtil.obj2Str(message));
            notificationClientService.send(message);
        }
    }


    private Message generateMessage(List<String> userIds, TargetTypeEnum targetType, String title, String content, Long configId, String userAttr) {
        Message message = new Message();
        message.setBizType(MessageBizTypeEnum.MESSAGE);
        message.setType(MessageTypeEnum.TEXT);
        MessageBodyText bodyText = new MessageBodyText();
        bodyText.setText(content);
        bodyText.setTitle(title);
        message.setBody(bodyText);
        if (StringUtils.isNotBlank(userAttr)) {
            message.setIdType(MessageIdTypeEnum.IAMATTR);
        } else {
            message.setIdType(MessageIdTypeEnum.IAM);
        }
        message.setUserType(MessageUserTypeEnum.USER);
        message.setTargetType(targetType);
        Set<Long> configIds = new HashSet<>();
        configIds.add(configId);
        message.setConfigIds(configIds);
        message.setUserDataIds(userIds);
        message.setAttr(userAttr);
        message.setTenantId(TenantHolder.getTenantCode());
        return message;
    }

}
