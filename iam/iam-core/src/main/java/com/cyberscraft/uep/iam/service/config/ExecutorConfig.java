package com.cyberscraft.uep.iam.service.config;

import com.cyberscraft.uep.common.util.MDCThreadWrapUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <p>
 *     线程池配置
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2019-08-01 10:23
 */
@Configuration
@EnableAsync
public class ExecutorConfig {
    @Value("${data.push.threadpool.corePoolSize:5}")
    private int dataPushCorePoolSize;
    @Value("${data.push.threadpool.MaxPoolSize:10}")
    private int dataPushCoreMaxPoolSize;
    @Value("${data.push.job.threadpool.corePoolSize:5}")
    private int dataPushJobCorePoolSize;
    @Value("${data.push.job.threadpool.MaxPoolSize:10}")
    private int dataPushJobMaxPoolSize;

    @Bean
    public Executor dataPushServiceExecutor() {
        ThreadPoolTaskExecutor executor = new MDCThreadWrapUtil.MdcThreadPoolTaskExecutorWrapper();
        executor.setCorePoolSize(dataPushCorePoolSize);
        executor.setMaxPoolSize(dataPushCoreMaxPoolSize);
        executor.setQueueCapacity(400);
        executor.setThreadNamePrefix("data-push-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }


    @Bean
    public Executor dataPushJobExecutor() {
        ThreadPoolTaskExecutor executor = new MDCThreadWrapUtil.MdcThreadPoolTaskExecutorWrapper();
        executor.setCorePoolSize(dataPushJobCorePoolSize);
        executor.setMaxPoolSize(dataPushJobMaxPoolSize);
        executor.setQueueCapacity(400);
        executor.setThreadNamePrefix("data-push-job-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }

    @Bean
    public Executor dataPullJobExecutor() {
        ThreadPoolTaskExecutor executor = new MDCThreadWrapUtil.MdcThreadPoolTaskExecutorWrapper();
        executor.setCorePoolSize(dataPushJobCorePoolSize);
        executor.setMaxPoolSize(dataPushJobMaxPoolSize);
        executor.setQueueCapacity(400);
        executor.setThreadNamePrefix("data-pull-job-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }
}