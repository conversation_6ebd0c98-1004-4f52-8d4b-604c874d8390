package com.cyberscraft.uep.iam.service.data.impl;

import com.cyberscraft.uep.starter.dds.TenantHolder;
import com.cyberscraft.uep.iam.common.constants.PushDataConstant;
import com.cyberscraft.uep.iam.dbo.AppDBO;
import com.cyberscraft.uep.iam.dbo.PushDataDBO;
import com.cyberscraft.uep.iam.dto.enums.BooleanEnums;
import com.cyberscraft.uep.iam.dto.enums.ClientStatusV2;
import com.cyberscraft.uep.iam.entity.AppEntity;
import com.cyberscraft.uep.iam.entity.PushDataEntity;
import com.cyberscraft.uep.iam.service.data.IDataPushRetryService;
import com.cyberscraft.uep.iam.service.data.IDataPushService;
import com.cyberscraft.uep.mq.constant.MQConstant;
import com.cyberscraft.uep.mq.service.IMessageSendClient;
import com.cyberscraft.uep.mq.vo.MessageEntry;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 *     数据推送-数据补偿业务服务
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2019-08-21 09:51
 */
@Service
public class DataPushRetryServiceImpl implements IDataPushRetryService {
    private static final Logger LOGGER = LoggerFactory.getLogger(DataPushRetryServiceImpl.class);

    @Autowired
    private PushDataDBO pushDataDBO;
    @Autowired
    private IDataPushService dataPushService;
    @Resource
    private IMessageSendClient messageSendClient;
    @Autowired
    private AppDBO appDBO;

    @Async("dataPushJobExecutor")
    @Override
    public void retryPushApp(String tcode) {
        if(StringUtils.isBlank(tcode)){
            return;
        }
        TenantHolder.setTenantCode(tcode);

        try {
            AppEntity queryAppEntity = new AppEntity();
            queryAppEntity.setWebhookEnable(BooleanEnums.TRUE.getValue());
            queryAppEntity.setStatus(ClientStatusV2.ACTIVE.getValue());
            List<AppEntity> appEntityList = appDBO.getAppList(queryAppEntity);
            if(CollectionUtils.isEmpty(appEntityList)){
                return;
            }
            for (AppEntity appEntity : appEntityList) {
                dataPushService.handPushApp(appEntity.getId(),appEntity.getWebhook(),TenantHolder.getTenantCode());
            }

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            LOGGER.info("data push to app job of tenant {} has been completed.",tcode);
            TenantHolder.remove();
        }
    }

    @Async("dataPushJobExecutor")
    @Override
    public void retryPushMQ(String tcode) {
        if(StringUtils.isBlank(tcode)){
            return;
        }
        TenantHolder.setTenantCode(tcode);

        try {
            List<PushDataEntity> pushDataEntityList = this.pushDataDBO.getPushDataList(PushDataConstant.PUSH_DATA_TOMQ_JOB_DELAYTIME);
            if (CollectionUtils.isEmpty(pushDataEntityList)) {
                return;
            }

            for (PushDataEntity pushDataEntity : pushDataEntityList) {
                pushDataEntity.setTcode(tcode);
                MessageEntry messageEntry = new MessageEntry();
                messageEntry.setMsg(pushDataEntity);
                messageSendClient.send(MQConstant.QUEUE_PUSH_DATA,messageEntry);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            LOGGER.info("data push to mq job of tenant {} has been completed.",tcode);
            TenantHolder.remove();
        }
    }
}
