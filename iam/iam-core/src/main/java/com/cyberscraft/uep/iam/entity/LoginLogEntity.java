package com.cyberscraft.uep.iam.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import java.io.Serializable;

/**
 * <p>
 * IAM-用户登录历史表
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2024-09-09
 */
@TableName("iam_login_log")
public class LoginLogEntity implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 登录的IP地址
     */
    private String srcIp;

    /**
     * 国家名称
     */
    private String country;

    /**
     * 省级编码
     */
    private String provinceCode;

    /**
     * 省级名称
     */
    private String province;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 城市名称
     */
    private String city;

    /**
     * 区/县编码
     */
    private String districtCode;

    /**
     * 区/县名称
     */
    private String districts;

    /**
     * 经度
     */
    private Double longitude;

    /**
     * 纬度
     */
    private Double latitude;

    /**
     * 登录时间
     */
    private LocalDateTime loginTime;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 用户id
     */
    private Long uid;

    /**
     * 角色类型，1：ROLE_USER（普通用户）， 2：ROLE_ADMIN（管理员）
     */
    private Integer roleType;

    /**
     * 操作类型
     */
    private Integer eventType;

    /**
     * 操作子类型
     */
    private Integer eventSubtype;

    /**
     * 关联ID,usercenter
     */
    private String targetId;

    /**
     * 关联名称，usercenter
     */
    private String targetName;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误描述
     */
    private String errorDescription;

    /**
     * 操作系统
     */
    private String srcDeviceModel;

    /**
     * 租户ID
     */
    private String tenantId;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSrcIp() {
        return srcIp;
    }

    public void setSrcIp(String srcIp) {
        this.srcIp = srcIp;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getDistrictCode() {
        return districtCode;
    }

    public void setDistrictCode(String districtCode) {
        this.districtCode = districtCode;
    }

    public String getDistricts() {
        return districts;
    }

    public void setDistricts(String districts) {
        this.districts = districts;
    }

    public Double getLongitude() {
        return longitude;
    }

    public void setLongitude(Double longitude) {
        this.longitude = longitude;
    }

    public Double getLatitude() {
        return latitude;
    }

    public void setLatitude(Double latitude) {
        this.latitude = latitude;
    }

    public LocalDateTime getLoginTime() {
        return loginTime;
    }

    public void setLoginTime(LocalDateTime loginTime) {
        this.loginTime = loginTime;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public Integer getRoleType() {
        return roleType;
    }

    public void setRoleType(Integer roleType) {
        this.roleType = roleType;
    }

    public Integer getEventType() {
        return eventType;
    }

    public void setEventType(Integer eventType) {
        this.eventType = eventType;
    }

    public Integer getEventSubtype() {
        return eventSubtype;
    }

    public void setEventSubtype(Integer eventSubtype) {
        this.eventSubtype = eventSubtype;
    }

    public String getTargetId() {
        return targetId;
    }

    public void setTargetId(String targetId) {
        this.targetId = targetId;
    }

    public String getTargetName() {
        return targetName;
    }

    public void setTargetName(String targetName) {
        this.targetName = targetName;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public String getErrorDescription() {
        return errorDescription;
    }

    public void setErrorDescription(String errorDescription) {
        this.errorDescription = errorDescription;
    }

    public String getSrcDeviceModel() {
        return srcDeviceModel;
    }

    public void setSrcDeviceModel(String srcDeviceModel) {
        this.srcDeviceModel = srcDeviceModel;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    @Override
    public String toString() {
        return "LoginLogEntity{" +
        "id=" + id +
        ", srcIp=" + srcIp +
        ", country=" + country +
        ", provinceCode=" + provinceCode +
        ", province=" + province +
        ", cityCode=" + cityCode +
        ", city=" + city +
        ", districtCode=" + districtCode +
        ", districts=" + districts +
        ", longitude=" + longitude +
        ", latitude=" + latitude +
        ", loginTime=" + loginTime +
        ", operator=" + operator +
        ", uid=" + uid +
        ", roleType=" + roleType +
        ", eventType=" + eventType +
        ", eventSubtype=" + eventSubtype +
        ", targetId=" + targetId +
        ", targetName=" + targetName +
        ", errorCode=" + errorCode +
        ", errorDescription=" + errorDescription +
        ", srcDeviceModel=" + srcDeviceModel +
        ", tenantId=" + tenantId +
        "}";
    }
}
