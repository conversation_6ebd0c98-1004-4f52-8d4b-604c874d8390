package com.cyberscraft.uep.iam.service.connector.event;

import com.cyberscraft.uep.account.client.constant.ThirdPartyEventTagConstant;
import com.cyberscraft.uep.account.client.domain.DingSuiteTicketEventBody;
import com.cyberscraft.uep.account.client.domain.EventBody;
import com.cyberscraft.uep.account.client.domain.ThirdPartyEvent;
import com.cyberscraft.uep.account.client.event.IThirdPartyEventExecutor;
import com.cyberscraft.uep.account.client.provider.dingding.service.IDingTalkAccessTokenService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/9/19 7:18 下午
 */
@Component
public class DingSuiteTicketUpdateEventHandler extends AbstractEventHandler implements IThirdPartyEventExecutor {

    @Resource
    private IDingTalkAccessTokenService dingTalkAccessTokenService;

    @Override
    public Set<String> getSupportedEventTypes() {
        return new HashSet<>(Arrays.asList(ThirdPartyEventTagConstant.SUITE_TICKET_UPDATE));
    }

    @Override
    public <E extends EventBody> void onEvent(ThirdPartyEvent<E> event) {
        DingSuiteTicketEventBody eventBody = (DingSuiteTicketEventBody) event.getData();
        String suiteTicket = eventBody.getSuiteTicket();
        dingTalkAccessTokenService.updateSuiteTicket(suiteTicket);
        LOG.info("update suite_ticket");
    }
}
