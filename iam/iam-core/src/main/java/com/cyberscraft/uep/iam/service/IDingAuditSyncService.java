package com.cyberscraft.uep.iam.service;

import com.cyberscraft.uep.common.dto.PageView;
import com.cyberscraft.uep.iam.dto.request.DingAuditSyncDto;
import com.cyberscraft.uep.iam.dto.response.DingAuditSyncVO;

import javax.servlet.http.HttpServletResponse;

public interface IDingAuditSyncService {

    /**
     * 获取钉钉同步升级日志列表信息
     *
     * @param page             页号
     * @param size             每一页的条数
     * @param dingAuditSyncDto 查询条件
     * @return 查询结果
     */
    PageView<DingAuditSyncVO> getDingAuditList(Integer page, Integer size, DingAuditSyncDto dingAuditSyncDto);

    /**
     * 查询条件
     *
     * @param id 审计日志ID
     * @return 升级日志详细信息
     */
    DingAuditSyncVO getDingAuditInfo(String id);


    /**
     * 导出文件审计日志
     *
     * @param response          返回值
     * @param dingAuditFile     文件名称
     * @param filter            过滤字段
     * @param orgName           组织名称
     * @param operatorName      操作名称
     * @param receiverName      接受者
     * @param action            操作类型
     * @param resourceExtension 文件类型
     * @param platform          操作平台
     * @param receiverType      空间类型
     * @param startTime         开始时间
     * @param endTime           结束时间
     */
    void exportDingAuditLog(HttpServletResponse response, String dingAuditFile, String filter, String orgName, String operatorName, String receiverName, Integer action,
                            String resourceExtension, Long platform, Integer receiverType, Long startTime, Long endTime);
}
