package com.cyberscraft.uep.iam.service.oidc.token;

import com.cyberscraft.uep.iam.service.oidc.token.entity.MultiFactorAuthenticationToken;
import com.cyberscraft.uep.iam.service.oidc.util.OIDCConstants;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.authentication.AccountStatusException;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.common.exceptions.InvalidGrantException;
import org.springframework.security.oauth2.provider.*;
import org.springframework.security.oauth2.provider.token.AbstractTokenGranter;
import org.springframework.security.oauth2.provider.token.AuthorizationServerTokenServices;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <p>
 *     STS Granter
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2020-06-23 18:43
 */
public class StsTokenGranter extends AbstractTokenGranter {

    private static final String GRANT_TYPE = OIDCConstants.GRANT_TYPE_STS;

    private final AuthenticationManager authenticationManager;
    private ClientDetailsService clientDetailsService;

    public StsTokenGranter(AuthenticationManager authenticationManager,
                           AuthorizationServerTokenServices tokenServices, ClientDetailsService clientDetailsService, OAuth2RequestFactory requestFactory) {
        this(authenticationManager, tokenServices, clientDetailsService, requestFactory, GRANT_TYPE);
    }

    protected StsTokenGranter(AuthenticationManager authenticationManager, AuthorizationServerTokenServices tokenServices,
                              ClientDetailsService clientDetailsService, OAuth2RequestFactory requestFactory, String grantType) {
        super(tokenServices, clientDetailsService, requestFactory, grantType);
        this.authenticationManager = authenticationManager;
        this.clientDetailsService = clientDetailsService;
    }

    @Override
    protected OAuth2Authentication getOAuth2Authentication(ClientDetails client, TokenRequest tokenRequest) {

        Map<String, String> parameters = new LinkedHashMap<String, String>(tokenRequest.getRequestParameters());
        String username = parameters.get(OIDCConstants.STS_PARAM_USERNAME);
        String thridClientId = parameters.get(OIDCConstants.STS_PARAM_THRID_CLIENT_ID);
        // Protect from downstream leaks of password

        Authentication userAuth = new MultiFactorAuthenticationToken(username,null,null);
        ((AbstractAuthenticationToken) userAuth).setDetails(parameters);
        try {
            userAuth = authenticationManager.authenticate(userAuth);
        }
        catch (AccountStatusException ase) {
            //covers expired, locked, disabled cases (mentioned in section 5.2, draft 31)
            throw new InvalidGrantException(ase.getMessage());
        }
        catch (BadCredentialsException e) {
            // If the username/password are wrong the spec says we should send 400/invalid grant
            throw new InvalidGrantException(e.getMessage());
        }
        if (userAuth == null || !userAuth.isAuthenticated()) {
            throw new InvalidGrantException("Could not authenticate user: " + username);
        }

        ClientDetails snsClientDetails = clientDetailsService.loadClientByClientId(thridClientId);
        OAuth2Request storedOAuth2Request = getRequestFactory().createOAuth2Request(snsClientDetails, tokenRequest);
        return new OAuth2Authentication(storedOAuth2Request, userAuth);
    }


}