package com.cyberscraft.uep.iam.service.auditlog.constant;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Author：xhquan
 * @Project：uep-services
 * @since：2023/10/18 16:44
 */
public class AuditTranslation {

    private static Map<String, String> ERROR_COED_MAP = new HashMap<>();

    private static Map<String, String> RESULT_MAP = new HashMap<>();

    private static final Map<String, String> FIELD_MAP = new ConcurrentHashMap<>();


    static {
        ERROR_COED_MAP.put("3018003", "下发对象无效");
        ERROR_COED_MAP.put("1011133", "不能更新此应用");
        ERROR_COED_MAP.put("1010310", "不能删除用户部门,因为该用户部门存在子部门或者用户");
        ERROR_COED_MAP.put("1010014", "租户未创建");
        ERROR_COED_MAP.put("8001", "连接错误");
        ERROR_COED_MAP.put("1010946", "不能改变import_orgs标志后同步一次");
        ERROR_COED_MAP.put("1111110", "参数不能为空");
        ERROR_COED_MAP.put("1011105", "应用没找到");
        ERROR_COED_MAP.put("10006", "列数不匹配");
        ERROR_COED_MAP.put("1010005", "组内用户过多，请缩小查询范围");
        ERROR_COED_MAP.put("1010007", "平台未初始化成功，请联系管理员");
        ERROR_COED_MAP.put("1010103", "验证码不存在");
        ERROR_COED_MAP.put("1010104", "验证码过期");
        ERROR_COED_MAP.put("1010105", "验证码无效，请重新获取");
        ERROR_COED_MAP.put("1010200", "用户名或密码错误");
        ERROR_COED_MAP.put("1010249", "管理员已开启账号锁定功能，您输入密码错误次数已达到上线该账号已被锁定，请稍后再试");
        ERROR_COED_MAP.put("1010201", "系统无此用户");
        ERROR_COED_MAP.put("1010202", "该用户已失效，返回到登录页面");
        ERROR_COED_MAP.put("1010203", "用户名重复，请重新创建");
        ERROR_COED_MAP.put("1010209", "用户邮箱未验证");
        ERROR_COED_MAP.put("1010210", "用户邮箱为空");
        ERROR_COED_MAP.put("1010211", "此邮箱已被验证");
        ERROR_COED_MAP.put("1010212", "您需要修改密码");
        ERROR_COED_MAP.put("1010213", "用户不能修改密码，旧密码状态无效");
        ERROR_COED_MAP.put("1010214", "用户被禁用，请联系管理员");
        ERROR_COED_MAP.put("1010215", "旧密码输入错误");
        ERROR_COED_MAP.put("1010217", "管理员重置密码后，需要修改密码");
        ERROR_COED_MAP.put("1010218", "密码已过期。请尝试忘记密码或者联系管理员重置密码");
        ERROR_COED_MAP.put("1010219", "新密码和历史密码重复，请重新设置");
        ERROR_COED_MAP.put("1010224", "用户手机号未验证");
        ERROR_COED_MAP.put("1010225", "用户手机号为空");
        ERROR_COED_MAP.put("1010227", "缺少邮箱和手机信息，无法找回密码");
        ERROR_COED_MAP.put("1010228", "无权限操作只读用户");
        ERROR_COED_MAP.put("1010236", "无效的验证码");
        ERROR_COED_MAP.put("1010237", "请勿频繁发送验证码，请稍后再试");
        ERROR_COED_MAP.put("1010241", "用户未注册");
        ERROR_COED_MAP.put("1010242", "用户未注册");
        ERROR_COED_MAP.put("1010244", "用户被禁用，请联系管理员。");
        ERROR_COED_MAP.put("1010247", "当前登录非钉钉用户");
        ERROR_COED_MAP.put("14000003", "用户未注册");
        ERROR_COED_MAP.put("1010302", "创建失败！所属上级组织内已有重名组织，请重新操作");
        ERROR_COED_MAP.put("1010304", "包含下级组，不能删除，请先删除子组。");
        ERROR_COED_MAP.put("1010402", "扩展属性名称已经存在，请重新填写");
        ERROR_COED_MAP.put("1010407", "扩展属性数量已经达到上限");
        ERROR_COED_MAP.put("1010602", "存在相同名称的连接器");
        ERROR_COED_MAP.put("1010603", "存在相同目标服务的连接器");
        ERROR_COED_MAP.put("1010607", "同步到系统中的组与现有组重名");
        ERROR_COED_MAP.put("1010901", "导入文件类型错误");
        ERROR_COED_MAP.put("1010902", "导入文件内容不能为空");
        ERROR_COED_MAP.put("1010903", "导入文件大小不能超过 5 MB");
        ERROR_COED_MAP.put("1010904", "不能识别导入文件的字符集");
        ERROR_COED_MAP.put("1010905", "另一个用户导入文件正在上传中，请等待");
        ERROR_COED_MAP.put("1010906", "另一个用户导入任务正在处理中，请继续处理或取消该任务");
        ERROR_COED_MAP.put("1010909", "导入用户任务正在取消");
        ERROR_COED_MAP.put("1010910", "导入文件读取错误");
        ERROR_COED_MAP.put("1010911", "导入文件第一行缺少必要用户属性信息");
        ERROR_COED_MAP.put("1010912", "导入文件中包含系统内置用户，请在文件中删除此用户后重新导入");
        ERROR_COED_MAP.put("1010913", "导入文件中存在重复的用户扩展属性列");
        ERROR_COED_MAP.put("1010914", "导入文件中存在不支持的用户扩展属性列");
        ERROR_COED_MAP.put("1010915", "用户名与已同步用户冲突");
        ERROR_COED_MAP.put("1010920", "连接请求被拒绝");
        ERROR_COED_MAP.put("1010921", "服务器连接超时");
        ERROR_COED_MAP.put("1010922", "与目标服务器握手失败");
        ERROR_COED_MAP.put("1010923", "用户名或密码错误");
        ERROR_COED_MAP.put("1010924", "目标baseDN未找到");
        ERROR_COED_MAP.put("1010925", "未找到用户数据");
        ERROR_COED_MAP.put("1010927", "管理员帐号权限不足");
        ERROR_COED_MAP.put("1010928", "未找到相关组");
        ERROR_COED_MAP.put("1010931", "文件导入用户不能超过 5000 条");
        ERROR_COED_MAP.put("1010940", "一个任务正在同步中，请稍后操作");
        ERROR_COED_MAP.put("1010941", "不能修改该配置文件的BaseDN");
        ERROR_COED_MAP.put("1010942", "此链接器正在同步中，请耐心等待");
        ERROR_COED_MAP.put("1011304", "证书不存在");
        ERROR_COED_MAP.put("1011122", "该应用已被禁用");
        ERROR_COED_MAP.put("1011129", "应用设置为不允许使用手机号发送一次性密码，请使用其他方式登录");
        ERROR_COED_MAP.put("1011130", "应用设置为不允许使用邮箱发送一次性密码，请使用其他方式登录");
        ERROR_COED_MAP.put("1011132", "单点登录的协议未选择");
        ERROR_COED_MAP.put("1111102", "无权限访问");
        ERROR_COED_MAP.put("1111104", "无权限访问");
        ERROR_COED_MAP.put("1011207", "权限组未找到");
        ERROR_COED_MAP.put("1011210", "标签已存在，请重新创建");
        ERROR_COED_MAP.put("1011211", "未找到指定标签");
        ERROR_COED_MAP.put("1011212", "无法删除自身的管理员权限");
        ERROR_COED_MAP.put("1010002", "输入参数有误");
        ERROR_COED_MAP.put("1011505", "用户画像已存在，请重新创建");
        ERROR_COED_MAP.put("1011500", "未找到指定用户画像");
        ERROR_COED_MAP.put("40086", "不合法的第三方应用appid");
        ERROR_COED_MAP.put("40013", "不合法的corpid");
        ERROR_COED_MAP.put("40089", "不合法的corpid或corpsecret或者不合法的appkey或appsecret");
        ERROR_COED_MAP.put("41002", "缺少corpid参数");
        ERROR_COED_MAP.put("41027", "需要授权企业的corpid参数");
        ERROR_COED_MAP.put("52010", "无效的corpid");
        ERROR_COED_MAP.put("90004", "您当前使用的CorpId及CorpSecret被暂时禁用了，仅对企业自己的Accesstoken有效");
        ERROR_COED_MAP.put("90006", "前使用的CorpId及CorpSecret调用当前接口次数过多，请求被暂时禁用了，仅对企业自己的Accesstoken有效");
        ERROR_COED_MAP.put("900010", "计算解密文字corpid不匹配");
        ERROR_COED_MAP.put("90017", "此IP使用CorpId及CorpSecret调用接口的CorpId个数超过限制");
        ERROR_COED_MAP.put("90001", "您的服务器调用钉钉开放平台所有接口的请求都被暂时禁用了");
        ERROR_COED_MAP.put("90002", "您的服务器调用钉钉开放平台当前接口的所有请求都被暂时禁用了");
        ERROR_COED_MAP.put("40056", "不合法的agentid");
        ERROR_COED_MAP.put("41011", "缺少agentid");
        ERROR_COED_MAP.put("52019", "无效的agentid");
        ERROR_COED_MAP.put("70003", "agentid对应微应用不存在");
        ERROR_COED_MAP.put("70004", "企业下没有对应该agentid的微应用");
        ERROR_COED_MAP.put("52023", "无效的服务窗agentid");
        ERROR_COED_MAP.put("71006", "回调地址已经存在");
        ERROR_COED_MAP.put("71007", "回调地址已不存在");
        ERROR_COED_MAP.put("71012", "url地址访问异常");
        ERROR_COED_MAP.put("400040", "回调不存在");
        ERROR_COED_MAP.put("400041", "回调已经存在");
        ERROR_COED_MAP.put("400050", "回调地址无效");
        ERROR_COED_MAP.put("400051", "回调地址访问异常");
        ERROR_COED_MAP.put("400052", "回调地址访返回数据错误");
        ERROR_COED_MAP.put("400053", "回调地址在黑名单中无法注册");
        ERROR_COED_MAP.put("400054", "回调URL访问超时");
        ERROR_COED_MAP.put("60020", "访问ip不在白名单之中");
        ERROR_COED_MAP.put("60121", "找不到该用户");
        ERROR_COED_MAP.put("1010012", "租户不存在");
        ERROR_COED_MAP.put("1011602", "已存在同名应用认证策略");
        ERROR_COED_MAP.put("1011609", "应用认证策略具有CERT方法，无法选择！");
        ERROR_COED_MAP.put("13000300", "corpid重复！");
        ERROR_COED_MAP.put("13000301", "企业名称重复！");
        ERROR_COED_MAP.put("13000200", "链接器名称重复！");
        ERROR_COED_MAP.put("13000305", "认证源被通讯录集成、通讯录同步、企业认证 关联后无法被删除。");
        ERROR_COED_MAP.put("13000308", "认证源配置错误");
        ERROR_COED_MAP.put("13000210", "请禁用当前连接器");
        ERROR_COED_MAP.put("1010015", "您的请求过于频繁，请您稍后再试！");
        ERROR_COED_MAP.put("1010221", "管理员不能修改用户密码");
        ERROR_COED_MAP.put("1010238", "重复的电话号码！");
        ERROR_COED_MAP.put("1010239", "重复的邮件地址！");
        ERROR_COED_MAP.put("1010246", "用户在群聊中存在");
        ERROR_COED_MAP.put("1010248", "账号已经被禁用，不能登录，请联系管理员");
        ERROR_COED_MAP.put("1010271", "账号已经被停用，不能登录，请联系管理员");
        ERROR_COED_MAP.put("17", "访问域名或应用标识重复！");
        ERROR_COED_MAP.put("1011515", "用户画像数量已达上限！");
        ERROR_COED_MAP.put("1011516", "不允许添加重名用户画像！");
        ERROR_COED_MAP.put("13000304", "sns类型不合法！");
        ERROR_COED_MAP.put("13000306", "sns配置重复！");
        ERROR_COED_MAP.put("13000307", "sns名称重复！");
        ERROR_COED_MAP.put("1010314", "不能修改同步的部门组");
        ERROR_COED_MAP.put("14111001", "此连接器已经在连接流中的使用，不能删除。");
        ERROR_COED_MAP.put("1010953", "上传文件不合法");


        RESULT_MAP.put("101", "登录成功");
        RESULT_MAP.put("102", "登出成功");
        RESULT_MAP.put("103", "授权应用");
        RESULT_MAP.put("104", "登录应用");
        RESULT_MAP.put("105", "扫码登录");
        RESULT_MAP.put("106", "同意授权");
        RESULT_MAP.put("107", "拒绝授权");

        FIELD_MAP.put("name", "姓名");
        FIELD_MAP.put("occur_time", "登录时间");
        FIELD_MAP.put("operator", "用户名");
        FIELD_MAP.put("src_ip", "来源IP");
        FIELD_MAP.put("src_device_model", "来源设备");
        FIELD_MAP.put("result", "结果");
        FIELD_MAP.put("event_type", "操作类型");
        FIELD_MAP.put("event_subtype", "具体操作");
        FIELD_MAP.put("target_name", "操作对象");
        FIELD_MAP.put("error_code", "结果");

    }

    public static String getValueByKey(String key) {
        return ERROR_COED_MAP.get(key);
    }

    public static String getLoginResultByKey(String key) {
        return RESULT_MAP.get(key);
    }

    public static String getFieldByKey(String key) {
        return FIELD_MAP.get(key);
    }
}
