package com.cyberscraft.uep.iam.dbo.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cyberscraft.uep.common.dto.PageView;
import com.cyberscraft.uep.common.dto.Pagination;
import com.cyberscraft.uep.common.util.PagingUtil;
import com.cyberscraft.uep.iam.entity.AuditlogEntity;
import com.cyberscraft.uep.common.util.Util;
import com.cyberscraft.uep.iam.dao.AuditLogDao;
import com.cyberscraft.uep.iam.dbo.AuditlogDBO;
import com.cyberscraft.uep.iam.query.AuditlogQueryDto;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <p>
 * IAM-审计日志表 服务实现类
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2019-07-08
 */
@Service
public class AuditlogDBOImpl extends ServiceImpl<AuditLogDao, AuditlogEntity> implements AuditlogDBO {

    @Override
    public PageView<AuditlogEntity> page(Pagination<AuditlogQueryDto> page) {
        IPage<AuditlogEntity> myPage = PagingUtil.toMybatisPage(page);
        QueryWrapper<AuditlogEntity> queryWrapper = new QueryWrapper<>();

        LambdaQueryWrapper<AuditlogEntity> lambdaQueryWrapper = queryWrapper.lambda();

        AuditlogQueryDto queryDto = page.getQueryDto();

        if(queryDto.getBeginId() != null) {
            lambdaQueryWrapper.gt(AuditlogEntity::getId, queryDto.getBeginId());
        }

        if(queryDto.getAuditType() != null) {
            lambdaQueryWrapper.eq(AuditlogEntity::getAuditType, queryDto.getAuditType());
        }

        if(queryDto.getEventType() != null) {
            lambdaQueryWrapper.eq(AuditlogEntity::getEventType, queryDto.getEventType());
        }

        if(queryDto.getEventSubType() != null) {
            lambdaQueryWrapper.eq(AuditlogEntity::getEventSubtype, queryDto.getEventSubType());
        }

        if(queryDto.getPrincipal() != null) {
            // 参与者名称匹配操作者或目标对象
            lambdaQueryWrapper.nested(e ->e.eq(AuditlogEntity::getOperator, queryDto.getPrincipal())
                                        .or().eq(AuditlogEntity::getTargetId, queryDto.getPrincipal())
                                        .or().eq(AuditlogEntity::getTargetName, queryDto.getPrincipal()));
        }

        if(queryDto.getStartTime() != null) {
            lambdaQueryWrapper.gt(AuditlogEntity::getOccurTime, Util.getDateTimeOfTimestamp(queryDto.getStartTime()));
        }

        if(queryDto.getEndTime() != null) {
            lambdaQueryWrapper.lt(AuditlogEntity::getOccurTime, Util.getDateTimeOfTimestamp(queryDto.getEndTime()));
        }

        if (StringUtils.isNotBlank(queryDto.getSort())) {
            if ("asc".equalsIgnoreCase(queryDto.getOrder())) {
                queryWrapper.orderByAsc(queryDto.getSort());
            } else {
                queryWrapper.orderByDesc(queryDto.getSort());
            }
        } else {
            queryWrapper.orderByDesc("occur_time");
        }

        IPage<AuditlogEntity> temp = this.page(myPage, lambdaQueryWrapper);
        return PagingUtil.toPageView(temp);
    }
}
