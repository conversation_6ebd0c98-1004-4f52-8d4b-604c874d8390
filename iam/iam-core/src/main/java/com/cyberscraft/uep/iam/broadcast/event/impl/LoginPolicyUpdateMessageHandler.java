package com.cyberscraft.uep.iam.broadcast.event.impl;

import com.cyberscraft.uep.iam.broadcast.event.IBroadcastMessageHandler;
import com.cyberscraft.uep.iam.service.ILoginPolicyService;
import com.cyberscraft.uep.mq.enums.BroadcastMessageType;
import com.cyberscraft.uep.mq.vo.BroadcastMessage;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * role更新操作广播时间处理器
 * <AUTHOR>
 */
@Component
public class LoginPolicyUpdateMessageHandler implements IBroadcastMessageHandler {
    private Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    ILoginPolicyService loginPolicyService;

    @Override
    public void handleMessage(BroadcastMessage broadcastMessage) {
        try{
            TenantHolder.setTenantCode(broadcastMessage.getTenantId());
            loginPolicyService.evictCache(broadcastMessage.getTarget());
            logger.info("evict target {} cache of tenant {}, resource {}, trigger by action {}",
                    broadcastMessage.getTarget(),
                    broadcastMessage.getTenantId(),
                    broadcastMessage.getResourceType(),
                    broadcastMessage.getAction());
        }finally {
            TenantHolder.remove();
        }
    }

    @Override
    public String getSupportedMessageType() {
        return BroadcastMessageType.LOGIN_POLICY_UPDATE.getSupportedType();
    }
}
