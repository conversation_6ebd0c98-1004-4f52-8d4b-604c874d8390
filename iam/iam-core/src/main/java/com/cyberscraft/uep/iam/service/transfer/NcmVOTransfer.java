package com.cyberscraft.uep.iam.service.transfer;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import com.cyberscraft.uep.common.bean.Result;
import com.cyberscraft.uep.common.dto.PageView;
import com.cyberscraft.uep.ncm.dto.MediaVO;
import com.cyberscraft.uep.ncm.dto.MessageSendProgressVO;
import com.cyberscraft.uep.ncm.dto.SendMessageListVO;
import com.cyberscraft.uep.ncm.dto.SendMessageUserListVO;
import com.cyberscraft.uep.ncm.dto.SendMessageVO;
import com.cyberscraft.uep.ncm.dto.SendingMessageListVO;
import com.cyberscraft.uep.ncm.dto.SendingMessageVO;

/**
 * NCM VO到IAM VO的转换
 *
 * <AUTHOR>
 * @date Created：2020-08-17
 */
@Mapper(componentModel = "spring", uses = TypeMapper.class)
public interface NcmVOTransfer {

    /**
     * 将NCM分页查询的结果对象转换为IAM的
     *
     * @param ncmResult NCM分页查询结果
     * @param transFunc VO转换方法
     * @return
     */
    default <T, R> Result<PageView<R>> resultPageTrans(Result<PageView<T>> ncmResult, Function<T, R> transFunc) {
        Result<PageView<R>> result = new Result<PageView<R>>();
        result.setCode(ncmResult.getCode());
        result.setMessage(ncmResult.getMessage());
        PageView<T> ncmData = ncmResult.getData();
        if (ncmData != null) {
            PageView<R> data = new PageView<R>();
            data.setPage(ncmData.getPage());
            data.setSize(ncmData.getSize());
            data.setTotal(ncmData.getTotal());
            if (ncmData.getItems() != null && ncmData.getItems().size() > 0) {
                List<R> items = new ArrayList<R>(ncmData.getItems().size());
                ncmData.getItems().forEach(t -> {
                    items.add(transFunc.apply(t));
                });
                data.setItems(items);
            }
            result.setData(data);
        }
        return result;
    }

    /**
     * 将NCM返回的VO结果对象转换为IAM的
     *
     * @param ncmResult
     * @param transFunc
     * @return
     */
    default <T, R> Result<R> resultVOTrans(Result<T> ncmResult, Function<T, R> transFunc) {
        Result<R> result = new Result<R>();
        result.setCode(ncmResult.getCode());
        result.setMessage(ncmResult.getMessage());
        if (ncmResult.getData() != null) {
            result.setData(transFunc.apply(ncmResult.getData()));
        }
        return result;
    }

    com.cyberscraft.uep.iam.dto.response.ncm.SendMessageListVO sendMessageListVOTrans(SendMessageListVO vo);

    @Mapping(target = "media", expression = "java( mediaVOTrans(vo.getMedia()) )")
    com.cyberscraft.uep.iam.dto.response.ncm.SendMessageVO sendMessageVOTrans(SendMessageVO vo);

    com.cyberscraft.uep.iam.dto.response.ncm.SendMessageUserListVO sendMessageUserListVOTrans(SendMessageUserListVO vo);

    com.cyberscraft.uep.iam.dto.response.ncm.SendingMessageListVO sendingMessageListVOTrans(SendingMessageListVO vo);

    @Mapping(target = "media", expression = "java( mediaVOTrans(vo.getMedia()) )")
    com.cyberscraft.uep.iam.dto.response.ncm.SendingMessageVO sendingMessageVOTrans(SendingMessageVO vo);

    com.cyberscraft.uep.iam.dto.response.ncm.MessageSendProgressVO messageSendProgressVOTrans(MessageSendProgressVO vo);

    com.cyberscraft.uep.iam.dto.response.ncm.MediaVO mediaVOTrans(MediaVO vo);

}
