package com.cyberscraft.uep.iam.service.data;

import com.cyberscraft.uep.iam.entity.PushDataEntity;
import com.cyberscraft.uep.iam.dto.enums.PushBusinessType;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <p>
 *     数据推送业务服务接口
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2019-08-20 14:09
 */
public interface IDataPushService {

    /**
     * 当前批次，全部应用的数据推送成功处理
     * @param pushDataEntity
     */
    void allSuccessHander(PushDataEntity pushDataEntity);

    /**
     * 单个应用推送完成后的处理
     * @param pushDataEntity
     * @param appId
     * @param dataJson
     * @param result
     */
    void pushPostHander(PushDataEntity pushDataEntity, Long appId, String dataJson,boolean result);

    /**
     * 数据推送日志记录
     * @param appId
     * @param requestBody
     * @param responseBody
     * @param result
     */
    void saveLog(Long appId,String requestBody,String responseBody,String result);

    /**
     * 获取待发送的数据
     * @param pushDataEntity
     * @return
     */
    Map<String,Object> getPushData(PushDataEntity pushDataEntity);

    /**
     * 数据推送
     * @param pushDataEntity
     * @param dataMap
     * @param successCount
     * @param countDownLatch
     */
    @Deprecated
    void handPushApp(PushDataEntity pushDataEntity, List<Map<String, Object>> dataMap, AtomicInteger successCount, CountDownLatch countDownLatch,Long appId,String pushUrl);

    /**
     * 数据推送
     * @param appId
     * @param pushUrl
     * @param tcode
     */
    void handPushApp(Long appId,String pushUrl,String tcode);


    /**
     * 直接推送
     */
    void directPushToApp(PushBusinessType pushBusinessType,List<Map<String,Object>> dataMap, Long appId, String pushUrl, String tcode);

}
