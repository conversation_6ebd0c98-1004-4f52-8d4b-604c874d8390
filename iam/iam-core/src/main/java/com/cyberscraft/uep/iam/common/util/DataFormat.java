package com.cyberscraft.uep.iam.common.util;

/**
 * This class implements static methods for formating data.
 */
public class DataFormat {

    /**
     * Use to specify that the bytes should be on one line,
     * preceeded by an offset
     */
    public static final int ONE_LINE_WITH_OFFSET    = 0;

    /**
     * Use to specify that the bytes should be on one line, no offset.
     */
    public static final int ONE_LINE                = -1;

    public static final int DEFAULT_BYTES_PER_LINE  = 16;

    static CharFormatter    sToHex;             // Hex Format
    static CharFormatter    sToPrintCompact;    // Char Format no spaces
    static CharFormatter    sToPrint;           // Char Formatter with spaces

    // Interface for different character formatters
    //
    private interface CharFormatter {
        void add(StringBuilder buf, int ch);
    }

    static {
        sToHex = new CharFormatter () {
            public void add(StringBuilder b, int ch) {
                ch &= 0xFF;

                if (ch < 16)
                    b.append('0');
                b.append(Integer.toHexString(ch));
            }
        };

        sToPrint = new CharFormatter () {
            public void add(StringBuilder b, int ch) {
                ch &= 0xFF;     // normalize to 0 <= ch <= 255

                switch (ch) {
                    case '\n':  //LF
                        b.append("\\n");
                        break;
                    case '\r':  //CR
                        b.append("\\r");
                        break;
                    case '\t':  //TAB
                        b.append("\\t");
                        break;
                    default:
                        if (ch<128 && ch>=32) {
                            b.append(' ').append(NORMALIZED_CHAR[ch]);
                        }
                        else {
                            if (ch<16) {
                                b.append('0');
                            }
                            b.append(Integer.toHexString(ch));
                        }
                }
            }
        };

        sToPrintCompact = new CharFormatter () {
            public void add(StringBuilder b, int ch) {
                ch &= 0xFF;

                if (ch<128) {
                    b.append(NORMALIZED_CHAR[ch]);
                }
                else {
                    b.append('.');
                }
            }
        };
    }


    /**
     * Format a value as a right-justified string.
     *
     * @param value    The value to be formatted
     * @param fieldLen The length of the formatting field
     */
    private static String asRightJustified(int value, int fieldLen) {
        String    str = Integer.toString(value);
        int        strLen = str.length();

        if (strLen < fieldLen) {
            StringBuffer b = new StringBuffer(Math.max(fieldLen, strLen));

            while (strLen < fieldLen--) {
                b.append(' ');
            }
            b.append(str);

            str = b.toString();
        }
        return str;
    }


    /**
     * Format the byte array as hexidecimal characters.  The String output is
     * broken up into lines.
     *
     * @param array    The array of data
     * @param offset    Starting index in the array
     * @param len        Number of bytes to format
     * @param bytesPerLine    Number of bytes on a line of output.  If 0
     *    there is only one line of output.
     */
    private static String asString(byte [] array, int offset, int len,
        int bytesPerLine, CharFormatter fmt) {

        if (array == null) {
            return "NULL";
        }

        int    first    = offset;                // start of data index
        int    last    = offset + len;            // end of data index
        int    ndx;                            // current index


        // If <bytesPerLen> is given move <ndx> to the next lowest boundary.
        //
        if (bytesPerLine > 0) {
            ndx = ((offset - bytesPerLine + 1) / bytesPerLine) * bytesPerLine;
        }
        else {
            ndx    = offset;
        }


        // Start with a string buffer to hold the results and guess
        // at the length;
        //
        StringBuilder    b = new StringBuilder((last - ndx) * 4);

        if (bytesPerLine > 0) {
            char[] line = new char[bytesPerLine];   //holds the current line of bytes
            int i = 0;
            while (ndx < last) {
                if (ndx % bytesPerLine == 0) {
                    b.append(asRightJustified(ndx, 4) + ":");
                }

                if (ndx < offset) {
                    b.append("   ");
                }
                else {
                    b.append(' ');
                    fmt.add(b, array[ndx]);
                    line[i] = (char)array[ndx];     //add the current byte to the line
                }

                ndx++;
                if (ndx < last && ndx % bytesPerLine == 0) {

                    if (fmt==sToHex) {
                        appendCharString(line, bytesPerLine, b);    // append characters after the hex string
                    }
                    i = 0;

                    b.append('\n');
                }
                else {
                    i++;
                }
            }

            if (fmt==sToHex) {
                // pad the remainder of the line with spaces
                String padString = "   ";
                while (ndx % bytesPerLine > 0) {
                    b.append(padString);
                    ndx++;
                }
                appendCharString(line, i, b);   // append remaining characters
            }
        }
        else {
            if ( bytesPerLine == ONE_LINE_WITH_OFFSET )
                b.append(asRightJustified(offset, 4) + ":");

            while (ndx < last) {
                if (ndx > first)
                    b.append(' ');
                fmt.add(b, array[ndx++]);
            }
        }

        return b.toString();
    }

    private static
    void appendCharString(char[] qLine, int qLen, StringBuilder qBuf){
        qBuf.append("  ");
        for (int i = 0 ; i<qLen ; i++) {
            sToPrintCompact.add(qBuf, qLine[i]);
        }
    }

    /**
     * Format the byte array as hexidecimal characters.  The String output is
     * broken up into lines.
     *
     * @param array    The array of data
     * @param offset    Starting index in the array
     * @param len        Number of bytes to format
     * @param bytesPerLine    Number of bytes on a line of output.
     */
    public static String asHexString(byte [] array, int offset, int len,
        int bytesPerLine) {

        return asString(array, offset, len, bytesPerLine, sToHex);
    }



    /**
     * Format the byte array as hexidecimal characters.  The String output is
     * broken up into lines, with the default number of bytes per line.
     *
     * @param array    The array of data
     * @param offset    Starting index in the array
     * @param len        Number of bytes to format
     */
    public static String asHexString(byte [] array, int offset, int len) {
        return asHexString(array, offset, len, DEFAULT_BYTES_PER_LINE);
    }


    /**
     * Format the byte array as hexidecimal characters.  The String output is
     * broken up into lines, with the default number of bytes per line.
     *
     * @param array    The array of data
     */
    public static String asHexString(byte [] array) {
        if (array == null) {
            return "NULL";
        }
        return asHexString(array, 0, array.length);
    }


    /**
     * Format the byte array as printable characters.  The String output is
     * broken up into lines.
     *
     * @param array    The array of data
     * @param offset    Starting index in the array
     * @param len        Number of bytes to format
     * @param bytesPerLine    Number of bytes on a line of output.  If 0
     *    there is only one line of output.
     */
    public static String asCharString(byte [] array, int offset, int len,
        int bytesPerLine) {

        return asString(array, offset, len, bytesPerLine, sToPrint);
    }


    /**
     * Format the byte array as printable characters.  The String output is
     * broken up into lines, with the default number of bytes per line.
     *
     * @param array    The array of data
     * @param offset    Starting index in the array
     * @param len        Number of bytes to format
     */
    public static String asCharString(byte [] array, int offset, int len) {
        return asCharString(array, offset, len, DEFAULT_BYTES_PER_LINE);
    }


    /**
     * Format the byte array as printable characters.  The String output is
     * broken up into lines, with the default number of bytes per line.
     *
     * @param array    The array of data
     */
    public static String asCharString(byte [] array) {
        if (array == null) {
            return "NULL";
        }
        return asCharString(array, 0, array.length);
    }


    /**
     * Format the byte array as a hexidecimal string.
     *
     * @param array    The array of data
     * @param offset    Starting index in the array
     * @param len        Number of bytes to format
     */
    public static String asHexStringPacked(byte [] array, int offset, int len) {

        if (array == null) {
            return "NULL";
        }

        // Allocate a string buffer that will hold the length.
        //
        StringBuilder    b = new StringBuilder(len * 2);
        int                last = offset + len;

        while (offset < last) {
            sToHex.add(b, array[offset++]);
        }
        return b.toString();
    }

    /**
     * Format the byte array as a hexidecimal string.
     *
     * @param array    The array of data
     */
    public static String asHexStringPacked(byte [] array) {
        if (array == null) {
            return "NULL";
        }
        return asHexStringPacked(array, 0, array.length);
    }

    public static
    byte [] fromHexStringPacked (String hexString)
    {
        char [] chars = hexString.toCharArray ();

        if (chars.length % 2 != 0)
        {
            throw new IllegalArgumentException
                        ("fromHexString input isn't even-lengthed: "
                         + hexString);
        }

        int len = chars.length / 2;
        byte [] result = new byte [len];

        for (int i = 0, j = 0; i < len; i++, j += 2)
        {
            result [i] = (byte) ((toInt (chars[j]) << 4) + toInt (chars[j+1]));
        }

        return result;
    }

    private static
    int toInt (char b)
    {
        if ('a' <= b && b <= 'f')
            return b - 'a' + 10;
        if ('A' <= b && b <= 'F')
            return b - 'A' + 10;
        if ('0' <= b && b <= '9')
            return b - '0';
        throw new IllegalArgumentException ("Not an acceptable hex character");
    }

  
    /**
     * Get a printable character for the char.  Non printable characters such as bell or 
     * non-ASCII characters are output as a '.'
     */
    public
    static char normalized(int qChar)
    {
        return ( qChar<128 ? NORMALIZED_CHAR[qChar] : '.' );
    }


    /** 
     * Special characters like TAB, LF, CR replaced with space, 
     * all other chars less than 0x20 replaced with a '.' 
     */
    private static final char[] NORMALIZED_CHAR = 
    {
       //0    1    2    3    4    5    6    7    8    9    A    B    C    D    E    F    HEX
       //0    1    2    3    4    5    6    7    8    9    10   11   12   13   14   15      DEC
        '.', '.', '.', '.', '.', '.', '.', '.', '.', ' ', ' ', '.', '.', ' ', '.', '.',//00   0
        '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.', '.',//10  16
        ' ', '!', '\"','#', '$', '%', '&', '\'','(', ')', '*', '+', ',', '-', '.', '/',//20  32
        '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', ':', ';', '<', '=', '>', '?',//30  48
        '@', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O',//40  64
        'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z', '[', '\\',']', '^', '_',//50  80
        '`', 'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o',//60  96
        'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z', '{', '|', '}', '~', ''//70 112
    };

}
