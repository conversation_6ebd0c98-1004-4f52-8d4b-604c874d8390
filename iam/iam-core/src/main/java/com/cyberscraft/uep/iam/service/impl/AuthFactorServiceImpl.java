package com.cyberscraft.uep.iam.service.impl;

import com.cyberscraft.uep.iam.common.exception.UserCenterException;
import com.cyberscraft.uep.iam.dbo.AuthFactorDBO;
import com.cyberscraft.uep.iam.dto.request.login.AuthMethod;
import com.cyberscraft.uep.iam.dto.request.login.factors.AuthFactorDTO;
import com.cyberscraft.uep.iam.dto.request.login.factors.AuthFactorStatusChangeDTO;
import com.cyberscraft.uep.iam.dto.response.login.AuthFactorVO;
import com.cyberscraft.uep.iam.entity.AuthFactorEntity;
import com.cyberscraft.uep.iam.errors.TransactionErrorType;
import com.cyberscraft.uep.iam.service.IAuditlogService;
import com.cyberscraft.uep.iam.service.IAuthFactorService;
import com.cyberscraft.uep.iam.service.as.transfer.AuthFactorTransfer;
import com.cyberscraft.uep.iam.service.oidc.util.SecureRequestCheck;
import com.cyberscraft.uep.mq.service.IMessageSendClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class AuthFactorServiceImpl implements IAuthFactorService {
    private static final Logger logger = LoggerFactory.getLogger(AuthFactorServiceImpl.class);

    @Autowired
    private AuthFactorDBO authFactorDBO;
    @Autowired
    private AuthFactorTransfer authFactorTransfer;


    @Override
    public Void update(AuthFactorDTO authFactorDTO) {
        validateConfig(authFactorDTO);
        AuthFactorEntity oldEntity = authFactorDBO.get(authFactorDTO.getAuthMethod().name());
        if (oldEntity == null) {
            logger.error("auth factor {} doesn't exist", authFactorDTO.getAuthMethod());
            throw new UserCenterException(TransactionErrorType.AUTH_FACTOR_NOT_FOUND_ERROR);
        }

        oldEntity.setConfig(authFactorTransfer.toJsonString(authFactorDTO.getConfig()));
        oldEntity.setUpdateBy(SecureRequestCheck.getUsername());
        oldEntity.setUpdateTime(LocalDateTime.now());
        authFactorDBO.updateById(oldEntity);

        return null;
    }

    private void validateConfig(AuthFactorDTO authFactorDTO) {
        switch (authFactorDTO.getAuthMethod()) {
            case PWD:
                if (authFactorDTO.getConfig().getSecureLoginEnabled() == null) {
                    throw new UserCenterException(HttpStatus.BAD_REQUEST, TransactionErrorType.INVALID_REQUEST_PARAM,
                            "secure_login_enabled property required");
                }
                if (authFactorDTO.getConfig().getCount() != null) {
                    throw new UserCenterException(HttpStatus.BAD_REQUEST, TransactionErrorType.INVALID_REQUEST_PARAM,
                            "count property not supported");
                }
                if (authFactorDTO.getConfig().getRotateEnabled() != null) {
                    throw new UserCenterException(HttpStatus.BAD_REQUEST, TransactionErrorType.INVALID_REQUEST_PARAM,
                            "rotate_enabled property not supported");
                }
                if (authFactorDTO.getConfig().getValidity() != null) {
                    throw new UserCenterException(HttpStatus.BAD_REQUEST, TransactionErrorType.INVALID_REQUEST_PARAM,
                            "validity property not supported");
                }
                break;
            case SMS:
            case EMAIL:
                if (authFactorDTO.getConfig().getValidity() == null) {
                    throw new UserCenterException(HttpStatus.BAD_REQUEST, TransactionErrorType.INVALID_REQUEST_PARAM,
                            "validity property required");
                }

                if (authFactorDTO.getConfig().getValidity().intValue() < 1) {
                    throw new UserCenterException(HttpStatus.BAD_REQUEST, TransactionErrorType.INVALID_REQUEST_PARAM,
                            "validity must be greater than 0");
                }
                if (authFactorDTO.getConfig().getSecureLoginEnabled() != null) {
                    throw new UserCenterException(HttpStatus.BAD_REQUEST, TransactionErrorType.INVALID_REQUEST_PARAM,
                            "secure_login_enabled property not supported");
                }
                if (authFactorDTO.getConfig().getCount() != null) {
                    throw new UserCenterException(HttpStatus.BAD_REQUEST, TransactionErrorType.INVALID_REQUEST_PARAM,
                            "count property not supported");
                }
                if (authFactorDTO.getConfig().getRotateEnabled() != null) {
                    throw new UserCenterException(HttpStatus.BAD_REQUEST, TransactionErrorType.INVALID_REQUEST_PARAM,
                            "rotate_enabled property not supported");
                }
                break;
            case CERT:
                if (authFactorDTO.getConfig().getSecureLoginEnabled() != null) {
                    throw new UserCenterException(HttpStatus.BAD_REQUEST, TransactionErrorType.INVALID_REQUEST_PARAM,
                            "secure_login_enabled property not supported");
                }
                if (authFactorDTO.getConfig().getCount() == null) {
                    throw new UserCenterException(HttpStatus.BAD_REQUEST, TransactionErrorType.INVALID_REQUEST_PARAM,
                            "count config property required");
                }
                if (authFactorDTO.getConfig().getRotateEnabled() == null) {
                    throw new UserCenterException(HttpStatus.BAD_REQUEST, TransactionErrorType.INVALID_REQUEST_PARAM,
                            "rotate_enabled property required");
                }
                if (authFactorDTO.getConfig().getValidity() == null) {
                    throw new UserCenterException(HttpStatus.BAD_REQUEST, TransactionErrorType.INVALID_REQUEST_PARAM,
                            "validity property required");
                }
                break;
            default:
                throw new UserCenterException(HttpStatus.BAD_REQUEST, TransactionErrorType.INVALID_REQUEST_PARAM, "auth_method not supported");
        }
    }

    @Override
    public Void changeStatus(AuthMethod authMethod, AuthFactorStatusChangeDTO authFactorStatusChangeDTO) {
        AuthFactorEntity oldEntity = authFactorDBO.get(authMethod.name());
        if (oldEntity == null) {
            logger.error("auth factor {} doesn't exist", authMethod);
            throw new UserCenterException(TransactionErrorType.AUTH_FACTOR_NOT_FOUND_ERROR);
        }

        if (authFactorStatusChangeDTO.getStatus() == null) {
            logger.error("login policy {} update request invalid, status is null", authMethod.name());
            throw new UserCenterException(TransactionErrorType.INVALID_REQUEST_PARAM);
        }

        if (authFactorStatusChangeDTO.getStatus().getValue() == oldEntity.getStatus()) {
            logger.info("login policy {} status {} not changed", authMethod, authFactorStatusChangeDTO.getStatus());
            return null;
        }

        oldEntity.setStatus(authFactorStatusChangeDTO.getStatus().getValue());
        authFactorDBO.updateById(oldEntity);

        return null;
    }

    @Override
    public List<AuthFactorVO> list() {
        return authFactorTransfer.toVO(authFactorDBO.list());
    }

    @Override
    public AuthFactorVO get(AuthMethod authMethod) {
        AuthFactorEntity oldEntity = authFactorDBO.get(authMethod.name());
        if (oldEntity == null) {
            logger.error("auth factor {} doesn't exist", authMethod);
            throw new UserCenterException(TransactionErrorType.AUTH_FACTOR_NOT_FOUND_ERROR);
        }
        return authFactorTransfer.toVO(oldEntity);
    }

    @Override
    public void evictCache(String authMethod){
        authFactorDBO.evictCache(authMethod);
    }
}
