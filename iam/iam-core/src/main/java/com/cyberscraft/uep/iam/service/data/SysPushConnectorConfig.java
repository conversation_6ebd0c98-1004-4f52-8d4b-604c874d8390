package com.cyberscraft.uep.iam.service.data;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/***
 *
 * @date 2021/6/17
 * <AUTHOR>
 ***/

@Component
@ConfigurationProperties(prefix = "sys.push.connector")
@RefreshScope
public class SysPushConnectorConfig {

    /***
     *
     */
    private Integer failureSendMaxTimes =5;

    /***
     *
     */
    private Integer failureSendPeriod=10;

    /***
     *
     */
    private Integer failureSendPeriodType=2;

    public Integer getFailureSendMaxTimes() {
        return failureSendMaxTimes;
    }

    public void setFailureSendMaxTimes(Integer failureSendMaxTimes) {
        this.failureSendMaxTimes = failureSendMaxTimes;
    }

    public Integer getFailureSendPeriod() {
        return failureSendPeriod;
    }

    public void setFailureSendPeriod(Integer failureSendPeriod) {
        this.failureSendPeriod = failureSendPeriod;
    }

    public Integer getFailureSendPeriodType() {
        return failureSendPeriodType;
    }

    public void setFailureSendPeriodType(Integer failureSendPeriodType) {
        this.failureSendPeriodType = failureSendPeriodType;
    }
}
