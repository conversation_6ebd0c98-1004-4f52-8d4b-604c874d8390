package com.cyberscraft.uep.iam.service.user.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cyberscraft.uep.account.client.domain.Connector;
import com.cyberscraft.uep.account.client.domain.ConnectorUser;
import com.cyberscraft.uep.common.dto.LimitResult;
import com.cyberscraft.uep.common.dto.QueryPage;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.iam.common.enums.SyncDirectionEnum;
import com.cyberscraft.uep.iam.dbo.ExternalUserDBO;
import com.cyberscraft.uep.iam.dto.response.ConnectorDeleteVo;
import com.cyberscraft.uep.iam.entity.ExternalUserEntity;
import com.cyberscraft.uep.iam.entity.UserOrgEntity;
import com.cyberscraft.uep.iam.service.user.IExternalUserService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/11/3 11:45 上午
 */
@Service
public class ExternalUserServiceImpl implements IExternalUserService {

    private static final Logger logger = LoggerFactory.getLogger(ExternalUserServiceImpl.class);

    @Autowired
    private ExternalUserDBO externalUserDBO;

    @Override
    public ExternalUserEntity getByExternalId(Long connectorId, String externalId, String... attrs) {
        return externalUserDBO.getByExternalId(connectorId, externalId);
    }

    @Override
    public List<ExternalUserEntity> getByConnectorId(Long connectorId, String... attrs) {
        return externalUserDBO.getByConnectorId(connectorId);
    }

    @Override
    public List<ExternalUserEntity> listByExternalIds(Long connectorId, List<String> externalIds, String... attrs) {
        return externalUserDBO.listByExternalIds(connectorId, externalIds, attrs);
    }

    @Override
    public List<ExternalUserEntity> listByUnionIds(Long connectorId, List<String> unionIds, String... attrs) {
        return externalUserDBO.listByUnionIds(connectorId, unionIds, attrs);
    }

    @Override
    public List<ExternalUserEntity> listByExternalId(String externalId, String... attrs) {
        return externalUserDBO.listByExternalId(externalId, attrs);
    }

    @Override
    public ExternalUserEntity getByLocalUserId(Long connectorId, Long localUserId, String... attrs) {
        return externalUserDBO.getByLocalUserId(connectorId, localUserId, attrs);
    }

    @Override
    public List<ExternalUserEntity> listBylocalUserIds(Long connectorId, List<Long> localUserIds, String... attrs) {
        return externalUserDBO.listBylocalUserIds(connectorId, localUserIds, attrs);
    }

    @Override
    public void saveOrUpdateExternalUser(ExternalUserEntity externalUserEntity) {
        if (externalUserEntity != null) {
            externalUserDBO.saveOrUpdate(externalUserEntity);
        }
    }

    @Override
    public ExternalUserEntity createExternalUser(Long connectorId, Integer type, Integer batchNo, SyncDirectionEnum direction, String externalUserId, Long localUserId, ConnectorUser externalUser, Map<String, Object> localUser, List<UserOrgEntity> userOrgEntities) {
        if (userOrgEntities == null || userOrgEntities.isEmpty()) {
            return null;
        }
        //创建外部用户对象
        ExternalUserEntity externalUserEntity = new ExternalUserEntity();
        externalUserEntity.setExternalId(externalUserId);

        List<Long> userOrgIdList = userOrgEntities.stream().map(UserOrgEntity::getOrgRefId).collect(Collectors.toList());

        String unionIdValue = externalUser.getUnionIdValue() == null ? null : externalUser.getUnionIdValue().toString();
        String openIdValue = externalUser.getOpenIdValue() == null ? null : externalUser.getOpenIdValue().toString();
        String mobileValue = externalUser.getMobileValue() == null ? null : externalUser.getMobileValue().toString();
        externalUserEntity.setExternalUnionId(unionIdValue);
        externalUserEntity.setExternalOpenId(openIdValue);
        externalUserEntity.setExternalMobile(mobileValue);
        Object newExternalUserDeptValue = externalUser.getDeptValue();
        if (newExternalUserDeptValue instanceof Collection) {
            externalUserEntity.setExternalDept(JsonUtil.obj2Str(externalUser.getDeptValue()));
        } else {
            externalUserEntity.setExternalDept(JsonUtil.obj2Str(Arrays.asList(externalUser.getDeptValue())));
        }
        externalUserEntity.setLocalOrgIds(JsonUtil.obj2Str(userOrgIdList));
        externalUserEntity.setLocalOrgs(JsonUtil.obj2Str(userOrgEntities));

        externalUserEntity.setUserId(localUserId);
        externalUserEntity.setConnectorId(connectorId);
        externalUserEntity.setType(type);
        externalUserEntity.setSyncDirection(direction.getValue());
        externalUserEntity.setSyncBatchNo(batchNo);
        externalUserEntity.setProfile(JsonUtil.obj2Str(localUser));
        externalUserEntity.setSource(JsonUtil.obj2Str(externalUser));
        externalUserEntity.setCreateTime(LocalDateTime.now());

        externalUserDBO.save(externalUserEntity);
        return externalUserEntity;
    }

    @Override
    public void appendExternalDept(ExternalUserEntity externalUserEntity, Integer batchNo, ConnectorUser newExternalUser) {
        Integer oldSyncBatchNo = externalUserEntity.getSyncBatchNo();
        Set<Object> allExternalDeptList = new HashSet();

        String externalDept = null;
        if (batchNo.equals(oldSyncBatchNo)) {
            externalDept = externalUserEntity.getExternalDept();
            newExternalUser.setAppend(true);
        }

        if (StringUtils.isNotBlank(externalDept)) {
            List<Object> partExternalDeptList = JsonUtil.str2List(externalDept, Object.class);
            allExternalDeptList.addAll(partExternalDeptList);
        }

        Object newExternalUserDeptValue = newExternalUser.getDeptValue();
        if (newExternalUserDeptValue instanceof Collection) {
            allExternalDeptList.addAll((Collection) newExternalUserDeptValue);
        } else {
            allExternalDeptList.add(newExternalUserDeptValue);
        }

        externalUserEntity.setExternalDept(JsonUtil.obj2Str(allExternalDeptList));
        newExternalUser.setDeptValue(allExternalDeptList);
    }

    @Override
    public ExternalUserEntity updateExternalUser(ExternalUserEntity externalUserEntity, Integer batchNo, SyncDirectionEnum direction, Long localUserId, ConnectorUser newExternalUser, Map<String, Object> localUser, List<UserOrgEntity> userOrgEntities, boolean updateBatchNo, boolean appendOrg) {
        externalUserEntity.setUserId(localUserId);

        String unionIdValue = newExternalUser.getUnionIdValue() == null ? null : newExternalUser.getUnionIdValue().toString();
        String openIdValue = newExternalUser.getOpenIdValue() == null ? null : newExternalUser.getOpenIdValue().toString();
        String mobileValue = newExternalUser.getMobileValue() == null ? null : newExternalUser.getMobileValue().toString();
        externalUserEntity.setExternalUnionId(unionIdValue);
        externalUserEntity.setExternalOpenId(openIdValue);
        externalUserEntity.setExternalMobile(mobileValue);

        String newContent = JsonUtil.obj2Str(localUser);
        externalUserEntity.setProfile(newContent);
        externalUserEntity.setSource(JsonUtil.obj2Str(newExternalUser));

        List<Long> userOrgIdList = userOrgEntities.stream().map(UserOrgEntity::getOrgRefId).collect(Collectors.toList());

        if (appendOrg && updateBatchNo) {
            Integer oldSyncBatchNo = externalUserEntity.getSyncBatchNo();
            Set<Object> allExternalDeptList = new HashSet();
            Set<Object> allLocalOrgIdList = new HashSet();
            List<Object> allLocalOrgs = new ArrayList<>();

            String externalDept = null;
            String localOrgIds = null;
            String localOrgs = null;
            if (oldSyncBatchNo.equals(batchNo)) {
                externalDept = externalUserEntity.getExternalDept();
                localOrgIds = externalUserEntity.getLocalOrgIds();
                localOrgs = externalUserEntity.getLocalOrgs();
            }

            if (StringUtils.isNotBlank(externalDept)) {
                List<Object> partExternalDeptList = JsonUtil.str2List(externalDept, Object.class);
                allExternalDeptList.addAll(partExternalDeptList);
            }
            if (StringUtils.isNotBlank(localOrgIds)) {
                List<Object> partLocalOrgIdList = JsonUtil.str2List(localOrgIds, Object.class);
                allLocalOrgIdList.addAll(partLocalOrgIdList);
            }
            if (StringUtils.isNotBlank(localOrgs)) {
                List<UserOrgEntity> partUserOrgEntities = JsonUtil.str2List(localOrgs, UserOrgEntity.class);
                allLocalOrgs.addAll(partUserOrgEntities);
            }

            Object newExternalUserDeptValue = newExternalUser.getDeptValue();
            if (newExternalUserDeptValue instanceof Collection) {
                allExternalDeptList.addAll((Collection) newExternalUserDeptValue);
            } else {
                allExternalDeptList.add(newExternalUserDeptValue);
            }

            for (UserOrgEntity userOrgEntity : userOrgEntities) {
                if (!allLocalOrgIdList.contains(userOrgEntity.getOrgRefId())) {
                    allLocalOrgs.add(userOrgEntity);
                }
            }

            allLocalOrgIdList.addAll(userOrgIdList);

            externalUserEntity.setExternalDept(JsonUtil.obj2Str(allExternalDeptList));
            externalUserEntity.setLocalOrgIds(JsonUtil.obj2Str(allLocalOrgIdList));
            externalUserEntity.setLocalOrgs(JsonUtil.obj2Str(allLocalOrgs));
        } else {
            externalUserEntity.setExternalDept(JsonUtil.obj2Str(newExternalUser.getDeptValue()));
            externalUserEntity.setLocalOrgIds(JsonUtil.obj2Str(userOrgIdList));
            externalUserEntity.setLocalOrgs(JsonUtil.obj2Str(userOrgEntities));
        }

        externalUserEntity.setSyncBatchNo(batchNo);
        externalUserEntity.setUpdateTime(LocalDateTime.now());
        externalUserDBO.updateById(externalUserEntity);
        return externalUserEntity;
    }

    @Override
    public void updateExternalUser(Long entityId, Integer batchNo) {
        LambdaUpdateWrapper<ExternalUserEntity> updateWrapper = new UpdateWrapper<ExternalUserEntity>()
                .lambda()
                .eq(ExternalUserEntity::getId, entityId)
                .set(ExternalUserEntity::getUpdateTime, LocalDateTime.now())
                .set(ExternalUserEntity::getSyncBatchNo, batchNo);
        externalUserDBO.update(updateWrapper);
    }

    @Override
    public void updateExternalUser(List<Long> userId, Long connectorId, Integer batchNo) {
        LambdaUpdateWrapper<ExternalUserEntity> updateWrapper = new UpdateWrapper<ExternalUserEntity>()
                .lambda()
                .in(ExternalUserEntity::getId, userId)
                .eq(ExternalUserEntity::getConnectorId, connectorId)
                .set(ExternalUserEntity::getUpdateTime, LocalDateTime.now())
                .set(ExternalUserEntity::getSyncBatchNo, batchNo);
        externalUserDBO.update(updateWrapper);
    }

    @Override
    public void updateExternalUser(Long entityId, Long localUserId, String profile, List<UserOrgEntity> userOrgEntities) {
        Set<Long> userOrgIdSet = userOrgEntities.stream().map(UserOrgEntity::getOrgRefId).collect(Collectors.toSet());

        LambdaUpdateWrapper<ExternalUserEntity> updateWrapper = new UpdateWrapper<ExternalUserEntity>()
                .lambda()
                .eq(ExternalUserEntity::getId, entityId)
                .set(ExternalUserEntity::getUpdateTime, LocalDateTime.now())
                .set(ExternalUserEntity::getUserId, localUserId)
                .set(ExternalUserEntity::getProfile, profile)
                .set(ExternalUserEntity::getLocalOrgIds, JsonUtil.obj2Str(userOrgIdSet))
                .set(ExternalUserEntity::getLocalOrgs, JsonUtil.obj2Str(userOrgEntities));
        externalUserDBO.update(updateWrapper);
    }

    @Override
    public void deleteExternalUser(Connector connector, ExternalUserEntity externalUserEntity) {
        Long connectorId = externalUserEntity.getConnectorId();
        if (connector.getId().equals(connectorId)) {
            externalUserDBO.removeById(externalUserEntity.getId());
        } else {
            logger.warn("connector id is not matched, invoke id:{}, actual id:{}", connector.getId(), externalUserEntity.getConnectorId());
        }
    }

    @Override
    public void deleteExternalUser(Long connectorId, List<Long> userIds) {
        QueryWrapper<ExternalUserEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("user_id", userIds)
                .eq("connector_id", connectorId)
                .eq("sync_direction", SyncDirectionEnum.PUSH.getValue());
        externalUserDBO.remove(queryWrapper);
    }

    @Override
    public void deleteExternalUserByExternalId(Long connectorId, List<String> userIds) {
        QueryWrapper<ExternalUserEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("external_id", userIds)
                .eq("connector_id", connectorId)
                .eq("sync_direction", SyncDirectionEnum.PUSH.getValue());
        externalUserDBO.remove(queryWrapper);
    }

    @Override
    public void deleteExternalUser(Long externalUserEntityId) {
        externalUserDBO.removeById(externalUserEntityId);
    }

    @Override
    public Boolean deleteExternalUser(Long userId, String openId, Long connectorId) {
        QueryWrapper<ExternalUserEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                .eq("external_id", openId)
                .eq("connector_id", connectorId);
        return externalUserDBO.remove(queryWrapper);
    }

    @Override
    public List<ExternalUserEntity> getBindWechat(Long userId, List<Long> connectorIds) {
        QueryWrapper<ExternalUserEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                .in("connector_id", connectorIds);
        return externalUserDBO.list(queryWrapper);
    }

    @Override
    public void deleteExternalByConnectorId(Long connectorId) {
        externalUserDBO.removeByConnector(connectorId);
    }

    @Override
    public List<ExternalUserEntity> getByLocalUserId(Long localUserId, List<Integer> syncDirections) {
        return externalUserDBO.getByLocalUserId(localUserId, syncDirections);
    }

    @Override
    public List<ExternalUserEntity> getByLocalUserId(Long connectorId, List<Long> localUserIds, List<Integer> syncDirections) {
        return externalUserDBO.getByLocalUserId(connectorId, localUserIds, syncDirections);
    }

    @Override
    public List<ExternalUserEntity> listByLocalUserId(Long localUserId, Integer type, List<Integer> syncDirections) {
        return externalUserDBO.listByLocalUserId(localUserId, type, syncDirections);
    }

    @Override
    public List<ExternalUserEntity> listByLocalUserId(List<Long> localUserIds, Integer type, List<Integer> syncDirections) {
        return externalUserDBO.listByLocalUserId(localUserIds, type, syncDirections);
    }

    @Override
    public List<ExternalUserEntity> listByLocalUserId(List<Long> localUserIds, List<Integer> directions, String... attrs) {
        return externalUserDBO.listByLocalUserId(localUserIds, directions);
    }

    @Override
    public List<ExternalUserEntity> listBySnsUserId(String snsUserId, Integer type, List<Integer> syncDirections) {
        return externalUserDBO.listBySnsUserId(snsUserId, type, syncDirections);
    }

    @Override
    public List<ExternalUserEntity> listBySnsUserId(List<String> snsUserId, Integer type, List<Integer> syncDirections) {
        return externalUserDBO.listBySnsUserId(snsUserId, type, syncDirections);
    }

    @Override
    public List<ExternalUserEntity> listBySnsUnionId(String snsUnionId, Integer type, List<Integer> syncDirections) {
        return externalUserDBO.listBySnsUnionId(snsUnionId, type, syncDirections);
    }

    @Override
    public List<ExternalUserEntity> findToBeDeleted(Connector connector) {
        return externalUserDBO.findToBeDeleted(connector.getId(), connector.getSyncBatchNo());
    }

    @Override
    public List<ExternalUserEntity> findToBeDeleted(Long connectorId, Integer batchNo) {
        return externalUserDBO.findToBeDeleted(connectorId, batchNo);
    }

    @Override
    public LimitResult<ExternalUserEntity> findToBeDeleted(Long connectorId, Integer batchNo, int limit, long offset) {
        return externalUserDBO.findToBeDeleted(connectorId, batchNo, limit, offset);
    }

    @Override
    public Page<ExternalUserEntity> pageUpdatedUsers(Connector connector, int pageSize, int page) {
        Page<ExternalUserEntity> updatedUsers = externalUserDBO.findUpdatedUsers(connector.getId(), connector.getSyncBatchNo(), pageSize, page);
        return updatedUsers;
    }

    @Override
    public QueryPage<ConnectorDeleteVo> getDeleteLimit(Connector connector, Integer page, Integer pageSize) {
        return externalUserDBO.getDeleteLimit(connector, page, pageSize);
    }

    @Override
    public LimitResult<ExternalUserEntity> limitExternalUserForSync(Long connectorId, int batchNo, int limit, long offset) {
        return externalUserDBO.limitValidUserByBatchNo(connectorId, batchNo, limit, offset);
    }

    @Override
    public Set<Long> selectUserIdsByConnectorId(Long connectorId) {
        return externalUserDBO.selectUserIdsByConnectorId(connectorId);
    }
}
