package com.cyberscraft.uep.iam.dbo;

import com.cyberscraft.uep.common.dto.QueryPage;
import com.cyberscraft.uep.iam.entity.AppAuthEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cyberscraft.uep.iam.entity.TagEntity;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * IAM-应用授权表 服务类
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2019-07-08
 */
public interface AppAuthDBO extends IService<AppAuthEntity> {
    /**
     * 根据AppId，授权类型和关联Id，查询关联的记录
     *
     * @param appId
     * @param type
     * @param relationId
     * @return
     */
    AppAuthEntity getOne(Long appId, int type, Long relationId);

    /**
     * 根据AppId，授权类型和多个关联Id，查询关联的记录列表
     *
     * @param appId
     * @param type
     * @param relationIds
     * @return
     */
    List<AppAuthEntity> getList(Long appId, int type, List<Long> relationIds);

    List<AppAuthEntity> getList(Long appId, int type);

    List<AppAuthEntity> getList(AppAuthEntity appAuthEntity);

    void remove(Long appId, Long relationId, int type);

    void remove(Long appId);

    void removeByTag(Long tagId);

    void removeByTag(Long appId, int type);

    void save(Long appId, Long relationId, int type);

    QueryPage<Map<String, Object>> getAllAppUsers(String searchField, String filterStr, Long appId, Integer type, QueryPage<Map<String, Object>> queryPage, String... attrs);

    List<TagEntity> getAllAppTag(String filterStr, Long appId, Integer type);

}
