package com.cyberscraft.uep.iam.common.protocol;

/**
 * Created by cuilong on 5/27/17.
 */
public enum ChangeType {
    ADD("add"),
    MODIFY("modify"),
    DELETE("delete"),
    ENABLE("enable"),
    DISABLE("disable"),
    JOIN_ORG("join_org"),
    LEAVE_ORG("leave_org");

    private String value;
    private ChangeType(String value) {
        this.value = value;
    }
    public String getValue() {
        return this.value;
    }
}
