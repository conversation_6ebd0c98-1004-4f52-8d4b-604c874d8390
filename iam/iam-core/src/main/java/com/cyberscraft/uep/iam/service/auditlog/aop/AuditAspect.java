package com.cyberscraft.uep.iam.service.auditlog.aop;

import com.cyberscraft.uep.common.enums.ExceptionCodeEnum;
import com.cyberscraft.uep.common.util.StringUtil;
import com.cyberscraft.uep.common.util.Util;
import com.cyberscraft.uep.iam.common.domain.configparams.CfgPoliciesAuditLog;
import com.cyberscraft.uep.iam.common.exception.UserCenterException;
import com.cyberscraft.uep.iam.common.util.IAMHttpUtil;
import com.cyberscraft.uep.iam.constants.CommonConstants;
import com.cyberscraft.uep.iam.dto.response.AuditlogItemVO;
import com.cyberscraft.uep.iam.errors.TransactionErrorDescription;
import com.cyberscraft.uep.iam.errors.TransactionErrorType;
import com.cyberscraft.uep.iam.service.IAuditlogService;
import com.cyberscraft.uep.iam.service.IConfigService;
import com.cyberscraft.uep.iam.service.as.exceptions.AppRegistrationException;
import com.cyberscraft.uep.iam.service.auditlog.annotation.AuditField;
import com.cyberscraft.uep.iam.service.auditlog.annotation.Auditable;
import com.cyberscraft.uep.iam.service.auditlog.constant.*;
import com.cyberscraft.uep.iam.service.oidc.domain.UserInfo;
import com.cyberscraft.uep.iam.service.utils.AspectUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.common.TemplateParserContext;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.common.exceptions.OAuth2Exception;
import org.springframework.stereotype.Component;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static com.cyberscraft.uep.iam.errors.TransactionErrorDescription.SUCCESS_ERROR_CODE;

@Component
@Aspect
public class AuditAspect {
    private static Logger logger = LoggerFactory.getLogger(AuditAspect.class);

    @Autowired
    IAuditlogService auditlogService;

    @Autowired
    IConfigService iConfigService;


    static private ExpressionParser spelParser = new SpelExpressionParser();

    static final String POINT_CUT = "execution(public * com.cyberscraft.uep.iam..*.*(..)) &&@annotation(auditable)";
    static final String SPEL_SPLIT1 = ";;";
    static final String SPEL_SPLIT2 = "=";

    @Before(POINT_CUT)
    public void auditlogPerpare(JoinPoint point, Auditable auditable) {
//        try {
//            AuditLevel auditLevelCfg = getAuditLevelConfig();
//            AuditLevel auditLevel = auditable.auditLevel();
//
//            if (auditLevel.ordinal() >= auditLevelCfg.ordinal()) {
//                Map<String, Object> mapFields = this.prepareAuditlogMap(point, auditable, null);
//                String targetId = (String) mapFields.get(AuditFieldName.targetId.name());
//                if (!StringUtil.isEmptyOrNull(targetId)) {
//                    AuditEventType eventType = auditable.eventType();
//                    AuditEventSubtype eventSubtype = auditable.eventSubtype();
////                auditlogBackCacheService.prepareCache(targetId, eventType, eventSubtype);
//                }
//            }
//        }catch (Exception e) {
//            logger.error("auditlogPrepare: {}", e.getMessage());
//        }
    }

    @AfterReturning(pointcut = POINT_CUT, returning = "returnValue")
    public void auditlogSuccess(JoinPoint point, Auditable auditable, Object returnValue) {
        try {
            AuditLevel auditLevelCfg = getAuditLevelConfig();
            AuditLevel auditLevel = auditable.auditLevel();

            if (auditLevel.ordinal() >= auditLevelCfg.ordinal()) {
                Map<String, Object> mapFields = this.prepareAuditlogMap(point, auditable, returnValue);

                AuditlogItemVO vo = (AuditlogItemVO) Util.mapToObject(mapFields, AuditlogItemVO.class);
                if (StringUtils.isBlank(vo.getClientId())) {
                    vo.setClientId("iam");
                }
                auditlogService.ansyncRecord(vo);
            }
        } catch (Exception e) {
            logger.error("auditlog exception", e);
        }
    }

    @AfterThrowing(pointcut = POINT_CUT, throwing = "ex")
    public void auditlogFail(JoinPoint point, Auditable auditable, RuntimeException ex) {
        try {
            MethodSignature signature = (MethodSignature) point.getSignature();
            Method method = signature.getMethod();
            Auditable annotation = method.getAnnotation(Auditable.class);

            AuditType auditType = auditable.auditType();
            if (AuditType.LOGIN.code() != auditType.code()) {
                return;
            }
            AuditEventType eventType = auditable.eventType();
            AuditEventSubtype eventSubtype = auditable.eventSubtype();
            AuditLevel auditLevel = AuditLevel.ERROR;

            AuditLevel auditLevelCfg = getAuditLevelConfig();

            if (auditLevel.ordinal() >= auditLevelCfg.ordinal()) {
                String errorCode = TransactionErrorDescription.UNKNOWN_ERROR_CODE + "";
                String errorDescription = TransactionErrorDescription.UNKNOWN_ERROR_DESC;
                if (ex instanceof UserCenterException) {
                    UserCenterException exception = (UserCenterException) ex;
                    errorCode = exception.getTransactionError().getErrorCode() + "";
                    errorDescription = exception.getTransactionError().getDesc();

                    //微信绑定成功特殊处理
                    if("WECHAT".equals(exception.getCorrelationId())){
                        if(TransactionErrorType.USER_IS_BIND.getErrorCode()==exception.getTransactionError().getErrorCode()){
                            auditlogSuccess(point,auditable,TransactionErrorType.USER_IS_BIND);
                            return;
                        }
                    }


                } else if (ex instanceof AppRegistrationException) {
                    AppRegistrationException exception = (AppRegistrationException) ex;
                    errorCode = exception.getErrorCode();
                    errorDescription = exception.getMessage();
                } else if (ex instanceof OAuth2Exception) {
                    OAuth2Exception exception = (OAuth2Exception) ex;
                    errorCode = exception.getOAuth2ErrorCode();
                    errorDescription = exception.getMessage();
                } else if (ex instanceof AuthenticationException) {
                    AuthenticationException exception = (AuthenticationException) ex;
                    errorCode = CommonConstants.INVALID_AUTHENTICATION;
                    errorDescription = exception.getMessage();
                }

                Map<String, Object> mapFields = buildBasicAuditLog(auditType, eventType, eventSubtype, auditLevel);
                mapFields = getOperator(mapFields);
                mapFields = getFieldsViaAnnotation(point.getArgs(), mapFields);
                mapFields = appendErrorCode(mapFields, errorCode, errorDescription);
                String fields = annotation.fields();
                if (!StringUtil.isEmptyOrNull(fields)) {
                    if (fields.indexOf("#returnValue") > -1) {
                        fields = fields.replaceAll("#returnValue[^\\}]*", "").replaceAll("#\\{\\}", "");
                    }
                    mapFields = getFieldsViaSpel(mapFields, signature.getParameterNames(), point.getArgs(), null, fields);
                }

                mapFields = appendSourceHostInfo(mapFields);

                AuditlogItemVO vo = (AuditlogItemVO) Util.mapToObject(mapFields, AuditlogItemVO.class);
                if (StringUtils.isBlank(vo.getClientId())) {
                    vo.setClientId("iam");
                }
                auditlogService.ansyncRecord(vo);

            }
        } catch (Exception e) {
            logger.error("auditlogFail: {}", e.getMessage());
        }

    }

    private Map<String, Object> prepareAuditlogMap(JoinPoint point, Auditable auditable, Object returnValue) {
        MethodSignature signature = (MethodSignature) point.getSignature();
        Method method = signature.getMethod();
        Auditable annotation = method.getAnnotation(Auditable.class);

        AuditType auditType = auditable.auditType();
        AuditEventType eventType = auditable.eventType();
        AuditEventSubtype eventSubtype = auditable.eventSubtype();
        AuditLevel auditLevel = auditable.auditLevel();

        Map<String, Object> mapFields = buildBasicAuditLog(auditType, eventType, eventSubtype, auditLevel);
        mapFields = getOperator(mapFields);
        mapFields = getFieldsViaAnnotation(point.getArgs(), mapFields);
        if (returnValue != null) {
            if (returnValue instanceof Map) {
                mapFields = getFieldsViaMap((Map<String, Object>) returnValue, mapFields);
            } else {
                mapFields = getFieldsViaAnnotation(new Object[]{returnValue}, mapFields);
            }
        }

        if (!StringUtil.isEmptyOrNull(annotation.fields())) {
            mapFields = getFieldsViaSpel(mapFields, signature.getParameterNames(), point.getArgs(), returnValue, annotation.fields());
        }

        mapFields = appendSourceHostInfo(mapFields);
        return mapFields;

    }

    private Map<String, Object> getFieldsViaMap(Map<String, Object> mapParam, Map<String, Object> fields) {
        Object targetId = mapParam.get(AuditFieldName.targetId.name());
        if (targetId != null) {
            fields.put(AuditFieldName.targetId.name(), targetId);
        } else {
            if (null != mapParam.get("sub")) {
                fields.put(AuditFieldName.targetId.name(), mapParam.get("sub"));
            }
        }

        Object targetName = mapParam.get(AuditFieldName.targetName.name());

        if (targetName != null) {
            fields.put(AuditFieldName.targetName.name(), targetName);
        }

        return fields;

    }

    private Map<String, Object> getFieldsViaAnnotation(Object[] objects, Map<String, Object> fields) {
        if (fields == null) {
            fields = new ConcurrentHashMap<>();
        }

        if (objects == null || objects.length == 0) {
            logger.debug("Objects is empty");
            return fields;
        }

        for (int i = 0; i < objects.length; i++) {
            Object obj = objects[i];
            if (obj == null) {
                continue;
            }
            Class cl = obj.getClass();
            while (cl != Object.class) {
                try {
                    for (Field f : cl.getDeclaredFields())
                        for (Annotation a : f.getAnnotations()) {
                            if (a.annotationType() == AuditField.class) {
                                AuditField auditField = (AuditField) a;
                                f.setAccessible(true);
                                String fieldName = auditField.field();
                                Object annotatedFieldVal = f.get(obj);
                                if (!StringUtils.isEmpty(fieldName) && annotatedFieldVal != null) {
                                    logger.debug("Found auditing annotation. type=" + a.annotationType() + " value=" + annotatedFieldVal.toString());
                                    fields.put(fieldName, annotatedFieldVal);
                                }
                            }
                        }
                } catch (Exception e) {
                    logger.error("Error extracting auditing annotations from obj" + obj.getClass());
                }

                cl = cl.getSuperclass();
            }
        }
        return fields;
    }


    private Map<String, Object> getFieldsViaSpel(Map<String, Object> mapFields, String[] parameterNames, Object[] args, Object returnValue, String fields) {
        try {
            StandardEvaluationContext context = new StandardEvaluationContext();

            if (parameterNames != null) {
                for (int i = 0; i < parameterNames.length; i++) {
                    context.setVariable(parameterNames[i], args[i]);
                }
            }
            //将returnValue设置到context中，spel表达式可以利用
            context.setVariable("returnValue", returnValue);
            String spelResult = spelParser.parseExpression(fields, new TemplateParserContext()).getValue(context, String.class);

            if (!StringUtil.isEmptyOrNull(spelResult)) {
                Map<String, String> mapSpelResult = AspectUtils.string2Map(spelResult);
                mapFields.putAll(mapSpelResult);
            }
        } catch (Exception e) {
            logger.error("getFieldsViaSPEL error", e);
        }

        return mapFields;
    }

    private Map<String, Object> buildBasicAuditLog(AuditType auditType, AuditEventType eventType, AuditEventSubtype eventSubtype, AuditLevel level) {
        Map<String, Object> mapFields = new ConcurrentHashMap<>();
        mapFields.put(AuditFieldName.auditType.name(), auditType.code());
        mapFields.put(AuditFieldName.errorCode.name(), SUCCESS_ERROR_CODE);
        mapFields.put(AuditFieldName.occurTime.name(), System.currentTimeMillis() / 1000);
        mapFields.put(AuditFieldName.eventType.name(), eventType.code());
        mapFields.put(AuditFieldName.eventSubtype.name(), eventSubtype.code());
        mapFields.put(AuditFieldName.level.name(), level.code());
        return mapFields;
    }

    private Map<String, Object> getOperator(Map<String, Object> mapFields) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication != null) {
            Object principal = authentication.getPrincipal();
            if (principal != null && (principal instanceof UserInfo)) {
                UserInfo tUser = (UserInfo) (authentication.getPrincipal());
                String username = tUser.getUsername();
                if (username.startsWith(CommonConstants.BACK_SLASH)) {
                    username = username.substring(1);
                }
                mapFields.put(AuditFieldName.operator.name(), username);
                mapFields.put(AuditFieldName.roleType.name(), tUser.getRoleType());
                mapFields.put(AuditFieldName.uid.name(), tUser.getSub());
            }
        }

        return mapFields;
    }

    private Map<String, Object> appendErrorCode(Map<String, Object> mapFields, String errorCode, String errorDescription) {
        mapFields.put(AuditFieldName.errorCode.name(), errorCode);
        mapFields.put(AuditFieldName.errorDescription.name(), errorDescription);
        return mapFields;
    }

    private Map<String, Object> appendSourceHostInfo(Map<String, Object> mapFields) {
        IAMHttpUtil.RemoteHostInfo remoteHostInfo = IAMHttpUtil.getRemoteHostInfoFromContext();
        if (remoteHostInfo != null) {
            mapFields.put(AuditFieldName.srcIp.name(), remoteHostInfo.ip);
            mapFields.put(AuditFieldName.srcDeviceModel.name(), remoteHostInfo.deviceModel);
            mapFields.put(AuditFieldName.srcUserAgent.name(), remoteHostInfo.userAgent);
        }

        return mapFields;
    }

    private AuditLevel getAuditLevelConfig() {
        CfgPoliciesAuditLog audilogPolicy = iConfigService.getAuditLogPolicyCfg();
        AuditLevel auditLevel = AuditLevel.valueOf(audilogPolicy.getAuditLevel().toUpperCase());
        return auditLevel;
    }
}
