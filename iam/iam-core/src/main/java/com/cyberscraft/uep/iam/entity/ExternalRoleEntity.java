package com.cyberscraft.uep.iam.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description 外部系统角色/角色组
 * <AUTHOR>
 * @Date 2024/7/11 16:58
 */
@TableName("iam_link_role")
public class ExternalRoleEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * iam本地角色/角色组id
     */
    private Long iamId;

    /**
     * 外部角色ID
     */
    private String externalId;

    /**
     * 外部角色组ID
     */
    private String externalGroupId;

    /**
     * 外部角色组ID
     */
    private String externalName;

    /**
     * 角色所属用户ID 只是记录从我们推送到下游的数据
     */
    private String userIds;

    /**
     * 同步连接器id
     */
    private Long connectorId;

    /**
     * 连接器类型
     */
    private Integer type;

    /**
     * 角色类型，0角色组，1角色
     */
    private Integer roleType;
    /**
     * 同步方向，0、从上游来（sync）；1、到下游去（push）
     */
    private Integer syncDirection;

    /**
     * 外部角色/角色组的完整信息，json格式
     */
    private String profile;

    /**
     * 同步批次
     */
    private Integer syncBatchNo;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 租户ID
     */
    private String tenantId;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getIamId() {
        return iamId;
    }

    public void setIamId(Long iamId) {
        this.iamId = iamId;
    }

    public String getExternalId() {
        return externalId;
    }

    public void setExternalId(String externalId) {
        this.externalId = externalId;
    }

    public String getExternalGroupId() {
        return externalGroupId;
    }

    public void setExternalGroupId(String externalGroupId) {
        this.externalGroupId = externalGroupId;
    }

    public String getExternalName() {
        return externalName;
    }

    public void setExternalName(String externalName) {
        this.externalName = externalName;
    }

    public String getUserIds() {
        return userIds;
    }

    public void setUserIds(String userIds) {
        this.userIds = userIds;
    }

    public Long getConnectorId() {
        return connectorId;
    }

    public void setConnectorId(Long connectorId) {
        this.connectorId = connectorId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getRoleType() {
        return roleType;
    }

    public void setRoleType(Integer roleType) {
        this.roleType = roleType;
    }

    public Integer getSyncDirection() {
        return syncDirection;
    }

    public void setSyncDirection(Integer syncDirection) {
        this.syncDirection = syncDirection;
    }

    public String getProfile() {
        return profile;
    }

    public void setProfile(String profile) {
        this.profile = profile;
    }

    public Integer getSyncBatchNo() {
        return syncBatchNo;
    }

    public void setSyncBatchNo(Integer syncBatchNo) {
        this.syncBatchNo = syncBatchNo;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }
}
