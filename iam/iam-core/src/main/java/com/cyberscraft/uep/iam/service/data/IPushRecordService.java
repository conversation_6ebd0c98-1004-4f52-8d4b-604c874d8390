package com.cyberscraft.uep.iam.service.data;

import com.cyberscraft.uep.common.dto.PageView;
import com.cyberscraft.uep.common.dto.Pagination;
import com.cyberscraft.uep.iam.entity.PushRecordEntity;
import com.cyberscraft.uep.iam.common.exception.UserCenterException;
import com.cyberscraft.uep.iam.dto.response.data.PushRecordListVO;
import com.cyberscraft.uep.iam.dto.response.data.PushRecordVO;
import com.cyberscraft.uep.iam.dto.domain.config.PushRecordQueryDTO;

/***
 *
 * @date 2021/6/9
 * <AUTHOR>
 ***/
public interface IPushRecordService {

    /***
     * 获取推送记录详情
     * @param id
     * @return
     * @throws UserCenterException
     */
    PushRecordVO getPushRecordVO(Long id) throws UserCenterException;

    /****
     * 进行分页查询
     * @param page
     * @return
     * @throws UserCenterException
     */
    PageView<PushRecordListVO> page(Pagination<PushRecordQueryDTO> page) throws UserCenterException;

    /***
     *
     * @param page
     * @return
     * @throws UserCenterException
     */
    PageView<PushRecordEntity> getPushRecordEntitiesPage(Pagination<PushRecordQueryDTO> page) throws UserCenterException;

    /**
     * 不存在推送记录
     * @return
     */
    Boolean emptyPushRecord();

    /***
     * 删除连接器对应的第三方平台推送记录
     * @param id
     */
    void removeByConnector(Long id);

    /***
     *
     * @param connectorId
     * @param status
     */
    void setStatusByConnector(Long connectorId, Integer status);

    /***
     *
     * @param connectorId
     * @param businessDataRefId
     * @return
     */
    PushRecordEntity getPushRecordByBusinessData(Long connectorId, Long businessDataRefId, Integer businessType);

    /***
     *
     * @param obj
     */
    void add(PushRecordEntity obj);

    /***
     *
     * @param obj
     */
    void modify(PushRecordEntity obj);

    /***
     *
     * @param id
     */
    void remove(Long id);
}
