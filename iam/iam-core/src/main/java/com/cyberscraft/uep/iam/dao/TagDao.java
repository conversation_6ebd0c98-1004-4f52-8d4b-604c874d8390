package com.cyberscraft.uep.iam.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cyberscraft.uep.iam.entity.TagEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TagDao extends BaseMapper<TagEntity> {
    List<TagEntity> getUserTagList(@Param("uid") Long uid);

    List<TagEntity> getTags(@Param("userId") String userId, @Param("groupCode") String groupCode);

    List<TagEntity> getTagsByGroupIds(@Param("ids") List<Long> groupIds);
}
