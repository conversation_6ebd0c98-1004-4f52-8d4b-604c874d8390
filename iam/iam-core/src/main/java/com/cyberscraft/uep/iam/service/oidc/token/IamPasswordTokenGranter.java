package com.cyberscraft.uep.iam.service.oidc.token;

import com.cyberscraft.uep.iam.common.exception.UserCertificateException;
import com.cyberscraft.uep.iam.service.IPositionService;
import com.cyberscraft.uep.iam.service.oidc.util.SecureRequestCheck;
import com.cyberscraft.uep.iam.common.util.SpringUtil;
import com.cyberscraft.uep.iam.errors.TransactionErrorType;
import com.cyberscraft.uep.iam.service.oidc.util.OIDCConstants;
import com.cyberscraft.uep.iam.service.oidc.domain.DefaultBaseClientDetails;
import com.cyberscraft.uep.iam.service.IAppService;
import com.cyberscraft.uep.iam.service.ISelfService;
import com.nimbusds.jose.JOSEException;
import com.nimbusds.jose.JOSEObjectType;
import com.nimbusds.jose.JWSAlgorithm;
import com.nimbusds.jose.JWSHeader;
import com.nimbusds.jose.crypto.RSASSASigner;
import com.nimbusds.jose.jwk.JWK;
import com.nimbusds.jose.jwk.RSAKey;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.SignedJWT;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.authentication.*;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.client.resource.OAuth2AccessDeniedException;
import org.springframework.security.oauth2.common.exceptions.InvalidGrantException;
import org.springframework.security.oauth2.provider.*;
import org.springframework.security.oauth2.provider.token.AbstractTokenGranter;
import org.springframework.security.oauth2.provider.token.AuthorizationServerTokenServices;

import java.security.PrivateKey;
import java.text.ParseException;
import java.util.Calendar;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;

public class IamPasswordTokenGranter extends AbstractTokenGranter {
    private static Logger logger = LoggerFactory.getLogger(IamPasswordTokenGranter.class);

    private static final String GRANT_TYPE = "password";


    private final AuthenticationManager authenticationManager;

    public static JWSAlgorithm signingAlg = JWSAlgorithm.parse("RS256");

    public IamPasswordTokenGranter(AuthenticationManager authenticationManager,
                                             AuthorizationServerTokenServices tokenServices, ClientDetailsService clientDetailsService, OAuth2RequestFactory requestFactory) {
        this(authenticationManager, tokenServices, clientDetailsService, requestFactory, GRANT_TYPE);
    }

    protected IamPasswordTokenGranter(AuthenticationManager authenticationManager, AuthorizationServerTokenServices tokenServices,
                                                ClientDetailsService clientDetailsService, OAuth2RequestFactory requestFactory, String grantType) {
        super(tokenServices, clientDetailsService, requestFactory, grantType);
        this.authenticationManager = authenticationManager;
    }

    @Override
    protected OAuth2Authentication getOAuth2Authentication(ClientDetails client, TokenRequest tokenRequest) {

        Map<String, String> parameters = new LinkedHashMap<String, String>(tokenRequest.getRequestParameters());
        String username = parameters.get("username");
        String password = parameters.get("password");
        // Protect from downstream leaks of password
        parameters.remove("password");

        Authentication userAuth = new UsernamePasswordAuthenticationToken(username, password);
        ((AbstractAuthenticationToken) userAuth).setDetails(parameters);
        try {
            userAuth = authenticationManager.authenticate(userAuth);
        }
        catch (AccountStatusException ase) {
            //covers expired, locked, disabled cases (mentioned in section 5.2, draft 31)
            throw new InvalidGrantException(ase.getMessage());
        }
        catch (BadCredentialsException e) {
            // If the username/password are wrong the spec says we should send 400/invalid grant
            throw new InvalidGrantException(e.getMessage());
        }
        if (userAuth == null || !userAuth.isAuthenticated()) {
            throw new InvalidGrantException("Could not authenticate user: " + username);
        }
        boolean isPublic = false;
        if (client instanceof DefaultBaseClientDetails) {
            isPublic = ((DefaultBaseClientDetails) client).isPublicAccess();
        }
        IAppService appService = SpringUtil.getBean(IAppService.class);
        if (!isPublic && !appService.checkAppUserPermission(client.getClientId(), SecureRequestCheck.getSubject(userAuth))) {
            throw new OAuth2AccessDeniedException("Application [" + client.getClientId() + "] access denied");
        }

//        IPositionService positionService = SpringUtil.getBean(IPositionService.class);
//        if (!positionService.checkAppAllowedUserPositions(client.getClientId(), SecureRequestCheck.getSubject(userAuth))) {
//            throw new OAuth2AccessDeniedException("Application [" + client.getClientId() + "] access denied");
//        }

        if(client instanceof DefaultBaseClientDetails){
            DefaultBaseClientDetails clientDetails = (DefaultBaseClientDetails)client;
            if(clientDetails.isAuthWithCertEnable()){
                String jwtSignatureValue = tokenRequest.getRequestParameters().get(OIDCConstants.CERT_USER_VALID_PARAM_NAME);
                ISelfService selfService = SpringUtil.getBean(ISelfService.class);
                selfService.checkUserCertification(userAuth, jwtSignatureValue);
            }
        }

        OAuth2Request storedOAuth2Request = getRequestFactory().createOAuth2Request(client, tokenRequest);
        return new OAuth2Authentication(storedOAuth2Request, userAuth);
    }



    public static void main(String[] args){
        String jwkStr = "{\"kty\":\"RSA\",\"kid\":\"liuyang_cert1\",\"use\":\"sig\",\"alg\":\"RS256\",\"n\":\"lYuhzbG3QwexwtWTV1IjbDmwhj-tU1zoEq_yyXXiOaV40hVUmHu3iW8G5oXemlg3rS3KH5aQG8RmXnLvpu59_iGhA9Bm8ufSZtQ11odqZwhdeAbo0rHRQYmMbv1l_Mm4MDn1YnCQuwqrfxbRtmtLQxw72onzrdTnRjroLMW8B_TokyTJz2IcEqWboTkM-8iOFFB6is-CKghOomSU1mxkQGZ3Rc_hOcWrvfMS2QKtEEQN1uduHKLhsJjAxUb3NHMfB8RZ7KSmapitcXvrxcE8aq90ODqQ4QRLgsJ-JRTzFP7V3Jp_OfaJ6HTyYFGTv0Jw3fdR1hr-YQfBBHcLWiISBQ\",\"e\":\"AQAB\",\"d\":\"XPnlbWeykwAzqvjylHXDBESR1cW9U3kN6KBwCIU8U67ngWY1pGA26kGXPre4gKmcZBV191G64yimpM485cKdnuT1oNNvTlKHY9tIZcrngouW4UsacfSQL3j_UPpG8HXSCFngkIV_1mp52ZgsZogq0Ny68dKXKFqYN_fj5gJCN4c-mqYMvdvxl3WPFxgMOdx1ONGRribuCbE9EzLex_ustQoN2uPh1oygrTtJ1ud7DOhj-SzdWIa6VPc4UepVNzPlt76XJ8I6r0FyCnyL84cPJWEKLx5bRT8yxn4Rl_ILJgdINWtFq_xF7kSjB_lDf9mHfKlhDZ2W1p6GiOZIpdUVwQ\",\"p\":\"2BMR2NwtfNtEqflfhyvzN7eFU9bV1S4Yjmj8IQXSmnjHmYTMebKKoD5GpULoEpYqJ07eqUsXknbV_9fefw0_twtvuaJnoIg6fE6gp2R2RkXiH0S4yXJlL4hScJqugM024RnvaJOhUABvgX_eE-eAYtmVo0omybvauRvm3fg4vSk\",\"q\":\"sS2NHvDdMY9Ep72LHghf3emmR8pGhqrkgyo92t95wQeTL-b5zLd61UDyL5Lai0Uj9ca4EaYUQdHryPMfEPeIRNEQqlpWvflpmyC6rn8a0Xl0qKZmlaMOFsctV2BVKWlncBZ_PXUGkNUEt23WiFe-yrbbzyX3kgTuHrf950PxrX0\",\"dp\":\"qL_j7VivkdZxPspKR_gUIEyMrRiYscApX3cwid3RR-DSCdyEEPtWdqWMaN7i_jkWvNJE5TSHOVTj9saxtsozG5Kr8--ZPO-HtbN2lqTIFkPXwJHcOz_DNKcjs1uqs18R4CVwK10425ErhJjHaQYg55KE4h00Q9MZk_3thSepsFk\",\"dq\":\"T_5F6cNLyrVphOZB6jRTvChih6UexGxhsYqf1LLzEKCv5n-9Jp1sWt3wumPuQdTckHnA308BdFonv1ltHEb2AkP150PUR_9Um9JiFqiSXiMc9YvzYvn0QXp50SpQKNtK9UNi_UdZyIg0kfCIXJRpMrLueQ8mQInzTHE7tG9rrS0\",\"qi\":\"eteoFEG3g_ye9IFsz0-hoP0xK1vZXmLVHkz96i2YJ4O3sGnZejdHfmuhUBjRob_oPQ2XKEfWRbT7bEUbVfeiGsYb_1wAte0947kfLYaPGu4FxWWlHniqEKisNjRbHSi1dn_XJ9DmuQow87edHaYIXPMaxMTzdoUfIcvCHlIxJoI\"}";
        JWK userCertJWK;
        try {
            userCertJWK = JWK.parse(jwkStr);
        } catch (ParseException e) {
            throw new UserCertificateException(TransactionErrorType.CERT_PARSE_ERROR);
        }
        PrivateKey rsaPrivateKey = null;
        try {
            rsaPrivateKey = ((RSAKey) userCertJWK).toPrivateKey();
        } catch (JOSEException e) {
            e.printStackTrace();
        }
        RSASSASigner signer = new RSASSASigner(rsaPrivateKey);

        Date today = new Date();
        Calendar c = Calendar.getInstance();
        c.setTime(today);
        c.add(Calendar.MINUTE, 10);

        JWTClaimsSet.Builder builder = new JWTClaimsSet.Builder()
                .issuer("https://digitalsee.cn")
                .issueTime(new Date())
                .expirationTime(c.getTime())
                .claim("username", "liuyang")
                .claim("device_id", "8902748675");

        JWSHeader header = new JWSHeader(
                signingAlg,
                JOSEObjectType.JWT, null, null, null, null, null, null,
                null, null, "liuyang_cert", null, null);
        SignedJWT signedJWTToken = new SignedJWT(header, builder.build());
        try {
            signedJWTToken.sign(signer);
            System.out.println(signedJWTToken.serialize());
        } catch (JOSEException e) {}
    }
}
