package com.cyberscraft.uep.iam.dao;

import com.cyberscraft.uep.iam.entity.PushAppEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * IAM-应用推送数据表 Mapper 接口
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2019-08-16
 */
public interface PushAppDao extends BaseMapper<PushAppEntity> {
    /**
     * 根据状态获取待推送的应用数据
     * @return
     */
    List<PushAppEntity> getPushAppList(@Param("status")int status);
}
