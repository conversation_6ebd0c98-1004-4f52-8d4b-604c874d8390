package com.cyberscraft.uep.iam.service.appstore.third;

import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.iam.dto.enums.AppStoreAppClientId;
import com.cyberscraft.uep.iam.dto.request.sso.EcsConfig;
import com.cyberscraft.uep.iam.entity.AppEntity;
import com.cyberscraft.uep.iam.service.appstore.IAppStoreLoginService;
import com.cyberscraft.uep.iam.service.appstore.util.SSOAccountUtil;
import com.cyberscraft.uep.iam.service.appstore.util.SignatureUtils;
import com.cyberscraft.uep.iam.service.oidc.domain.UserInfo;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

/**
 * 易道单点登录
 * @Author：xhquan
 * @Project：uep-services
 * @since：2023/8/3 11:11
 */
@Service
@RefreshScope
public class EcsLoginService implements IAppStoreLoginService {

    protected Logger logger = LoggerFactory.getLogger(getClass());

    @Override
    public boolean supports(AppStoreAppClientId clientId) {
        return AppStoreAppClientId.ECS == clientId;
    }

    @Override
    public String constructSsoUrl(Authentication authentication, AppEntity appEntity) {
        EcsConfig ecsConfig = JsonUtil.str2Obj(appEntity.getConfig(), EcsConfig.class);

        String current = ecsConfig.getSignType();
        StringBuilder ssoUrl = new StringBuilder();
        try {
            String ssoPath = ecsConfig.getSsoUrl();
            if (ssoPath.endsWith("?")) {
                ssoUrl.append(ssoPath);
            } else {
                ssoUrl.append(ssoPath + "?");
            }
            ssoUrl.append("appMenu=" + URLEncoder.encode(ecsConfig.getAppMenu(), "utf-8"));
            ssoUrl.append("&cId=" + ecsConfig.getcId());
            ssoUrl.append("&clientType=" + ecsConfig.getClientType());
            ssoUrl.append("&language=" + ecsConfig.getLanguage());

            String userCodeMappingAttr = ecsConfig.getUserCodeMappingAttr();
            UserInfo userDetails = (UserInfo) authentication.getPrincipal();
            String userCode = SSOAccountUtil.basicGetUserCode(userDetails, userCodeMappingAttr);
            ssoUrl.append("&loginName=" + userCode);
            logger.info("userCode:{}", userCode);

            Map<String, Object> parameters = new HashMap<>();
            parameters.put("clientType", ecsConfig.getClientType());
            parameters.put("specialFunc", ecsConfig.getSpecialFunc());
            parameters.put("appMenu", ecsConfig.getAppMenu());
            parameters.put("language", ecsConfig.getLanguage());
            parameters.put("type", ecsConfig.getType());
            parameters.put("cId", ecsConfig.getcId());
            parameters.put("loginName", userCode);

            String tenantCode = ecsConfig.getTenantCode();
            boolean notBlank = StringUtils.isNotBlank(tenantCode);
            if (ecsConfig.getType().contains("sign")) {
                long timeStamp = System.currentTimeMillis();
                ssoUrl.append("&timestamp=" + timeStamp);
                parameters.put("timestamp", timeStamp);
                if (null == parameters.get("specialFunc") || "".equals(parameters.get("specialFunc").toString())) {
                    parameters.remove("specialFunc");
                }

                String sign = null;
                if (current.equals("SHA_RSA")) {
                    if (notBlank) {
                        parameters.put("tenantCode", tenantCode);
                    }

                    sign = SignatureUtils.createSign(parameters, ecsConfig.getSecretKey(), "SHA_RSA");
                } else if (current.equals("AES_MD5")) {
                    sign = SignatureUtils.createSign(parameters, ecsConfig.getSecretKey(), "AES_MD5");
                }
                ssoUrl.append("&sign=" + sign);
            }

            ssoUrl.append("&type=" + ecsConfig.getType());
            if (null != ecsConfig.getSpecialFunc() && !"".equals(ecsConfig.getSpecialFunc())) {
                ssoUrl.append("&specialFunc=" + ecsConfig.getSpecialFunc());
            }

            if (notBlank) {
                ssoUrl.append("&TenantCode=" + tenantCode);
            }
        } catch (Exception e) {
            logger.info("ecs create sso url exception {}", e.getMessage());
        }

        return ssoUrl.toString();
    }

    public static void main(String[] args) {
        String userCode = "10103001";
        EcsConfig ecsConfig = new EcsConfig();
        ecsConfig.setcId("client1");
        ecsConfig.setClientType("WEB");
        ecsConfig.setAppMenu("openConsole|console|home");
        ecsConfig.setLanguage("zh_CN");
        ecsConfig.setType("sign");
        ecsConfig.setSecretKey("ba2631ee44149bbe");
        ecsConfig.setSignType("AES_MD5");
        ecsConfig.setSsoUrl("http://121.40.207.240:8005/ecs-console/api/sso/login?");

    }
}
