package com.cyberscraft.uep.iam.common.util;

import com.cyberscraft.uep.iam.dto.enums.UserStatus;
import com.cyberscraft.uep.iam.constants.PageableConstant;
import com.cyberscraft.uep.iam.dto.response.PageableView;
import com.cyberscraft.uep.iam.dto.response.SortKey;
import com.cyberscraft.uep.iam.constants.ParamsConstraints;
import com.cyberscraft.uep.iam.errors.TransactionErrorType;
import com.cyberscraft.uep.iam.common.exception.UserCenterException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.domain.Sort.Order;
import org.springframework.http.HttpStatus;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Stream;

public class ParamValidationUtil {
    private static Logger logger = LoggerFactory.getLogger(ParamValidationUtil.class);

    public static void validateUserStatus(String userStatus){
        if (userStatus == null){
            return;
        }
        
        try{
            UserStatus.valueOf(userStatus.trim().toUpperCase());
        }catch(Exception e){
            logger.warn("invalid user status {}", userStatus);
            throw new UserCenterException(HttpStatus.BAD_REQUEST, TransactionErrorType.USER_STATUS_INVALID);
        }
    }
    
    public static void validateOrgName(String orgName){
        if (orgName == null){
            return;
        }
        
        if (orgName.length() > ParamsConstraints.ORG_NAME_MAX_LENGTH){
        	logger.warn("org name exceed {} max length {}", orgName, ParamsConstraints.ORG_NAME_MAX_LENGTH);
            throw new UserCenterException(HttpStatus.BAD_REQUEST, TransactionErrorType.ORG_NAME_EXCEED_MAX_LENGTH);
        }
    }
    
    public static void validateOrgDescription(String orgDesc){
        if (orgDesc == null){
            return;
        }
        
        if (orgDesc.length() > ParamsConstraints.ORG_DESC_MAX_LENGTH){
        	logger.warn("org desc exceed {} max length {}", orgDesc, ParamsConstraints.ORG_DESC_MAX_LENGTH);
            throw new UserCenterException(HttpStatus.BAD_REQUEST, TransactionErrorType.ORG_DESC_EXCEED_MAX_LENGTH);
        }
    }

    public static <T> PageableView<T> toPageableView(Pageable pageable) {
        int pageNumber = pageable.getPageNumber();
        
        if (pageNumber < 1) {
            logger.warn("invalid page number: {}, use default page number {}", pageable.getPageNumber(), PageableConstant.DEFAULT_PAGE_NUMBER);
            pageNumber = PageableConstant.DEFAULT_PAGE_NUMBER;
        }
        
        int pageSize = pageable.getPageSize();
        if (pageSize < 1) {
            logger.warn("invalid page size: {}, use default page size {}", pageable.getPageSize(), PageableConstant.DEFAULT_PAGE_SIZE);
            pageSize = PageableConstant.DEFAULT_PAGE_SIZE;
        }
        
        if (pageSize > 100) {
            logger.warn("invalid page size: {}, allowed max size {}", pageable.getPageSize(), PageableConstant.MAX_PAGE_SIZE);
            throw new UserCenterException(HttpStatus.BAD_REQUEST, TransactionErrorType.PAGE_SIZE_EXCEED_MAX);
        }

        return new PageableView<>(pageNumber, pageSize);
    }

    public static <T> PageableView<T> toPageableView(int page, int size) {
        int pageNumber = page;

        if (pageNumber < 1) {
            logger.warn("invalid page number: {}, use default page number {}", pageNumber, PageableConstant.DEFAULT_PAGE_NUMBER);
            pageNumber = PageableConstant.DEFAULT_PAGE_NUMBER;
        }

        int pageSize = size;
        if (pageSize < 1) {
            logger.warn("invalid page size: {}, use default page size {}", pageSize, PageableConstant.DEFAULT_PAGE_SIZE);
            pageSize = PageableConstant.DEFAULT_PAGE_SIZE;
        }

        if (pageSize > 100) {
            logger.warn("invalid page size: {}, allowed max size {}", pageSize, PageableConstant.MAX_PAGE_SIZE);
            throw new UserCenterException(HttpStatus.BAD_REQUEST, TransactionErrorType.PAGE_SIZE_EXCEED_MAX);
        }

        return new PageableView<>(pageNumber, pageSize);
    }



    public static SortKey[] toSortKeyArray(Sort sort, SortKey[] defaultSortKey,
                                           Function<String, String> sortPropertyNameConverter) {
        if (sort == null) {
            logger.debug("use default keys {}", Arrays.asList(defaultSortKey));
            return defaultSortKey;
        }

        List<SortKey> list = new ArrayList<SortKey>();
        for (Order order : sort) {
            String property = sortPropertyNameConverter.apply(order.getProperty());
            list.add(new SortKey(property, Direction.DESC == order.getDirection()));
        }
        logger.info("SortKey {}", list);
        return list.toArray(new SortKey[0]);
    }

    public static String[] vo2poAttrs(String[] voAttrs, Function<String, String> converter) {
        if (voAttrs == null || voAttrs.length == 0)
            return new String[0];

        return vo2poAttrs(Arrays.stream(voAttrs), converter);
    }

    public static String[] vo2poAttrs(Stream<String> voAttrs, Function<String, String> converter) {
        if (voAttrs == null)
            return new String[0];

        return voAttrs.map(converter).toArray(size -> new String[size]);
    }
}
