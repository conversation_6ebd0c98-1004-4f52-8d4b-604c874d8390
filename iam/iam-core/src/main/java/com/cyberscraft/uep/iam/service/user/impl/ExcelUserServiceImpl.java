package com.cyberscraft.uep.iam.service.user.impl;

import com.cyberscraft.uep.iam.dbo.ExcelUserDBO;
import com.cyberscraft.uep.iam.dto.response.ExcelUserVO;
import com.cyberscraft.uep.iam.entity.ExcelUserEntity;
import com.cyberscraft.uep.iam.service.transfer.ExcelUserTransfer;
import com.cyberscraft.uep.iam.service.user.IExcelUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class ExcelUserServiceImpl implements IExcelUserService {

    private static Logger logger = LoggerFactory.getLogger(ExcelUserServiceImpl.class);

    @Autowired
    private ExcelUserDBO excelUserDBO;


    @Autowired
    private ExcelUserTransfer excelUserTransfer;

    @Override
    public List<ExcelUserEntity> getValidUsersByBatchNo(Long batchNo) {
        return excelUserDBO.getValidUsersByBatchNo(batchNo);
    }

    @Override
    public List<ExcelUserEntity> getUsersByBatchNo(Long batchNo, List<Integer> status) {
        return excelUserDBO.getUsersByBatchNoAndStatus(batchNo, status);
    }

    @Override
    public List<ExcelUserEntity> getLastUsersByBatchNo(Long batchNo, Long lastUid) {
        return excelUserDBO.getLastUsersByBatchNo(batchNo, lastUid);
    }

    @Override
    public Boolean updateUserInfo(ExcelUserEntity ExcelUserEntity) {
        return excelUserDBO.saveOrUpdate(ExcelUserEntity);
    }

    @Override
    public List<ExcelUserVO> getImportUserInfo(Integer saveStatus, String batchNo) {
        List<ExcelUserEntity> usersByBatchNo = getUsersByBatchNo(Long.valueOf(batchNo), Arrays.asList(saveStatus));

        List<ExcelUserVO> excelUserVOS = excelUserTransfer.excelUserEntityListToExcelUserVOList(usersByBatchNo);
//        excelUserVOS.forEach(e->{
//            Map<String, Object> data = e.getData();
//            data.remove(LocalUserAttr.password.getDomainName());
//        });
        return excelUserVOS;
    }

    @Override
    public List<ExcelUserEntity> getValidUsersByBatchNoAndVerify(Long batchNo, Boolean userCreateVerify) {
        return excelUserDBO.getValidUsersByBatchNoAndVerify(batchNo, userCreateVerify);
    }
}
