package com.cyberscraft.uep.iam.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cyberscraft.uep.iam.dbo.TagGroupTagDBO;
import com.cyberscraft.uep.iam.entity.TagGroupTagEntity;
import com.cyberscraft.uep.iam.service.ITagGroupTagService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/3/8 17:37
 * @Version 1.0
 * @Description 应用与标签关联关系服务接口实现类
 */
@Service
public class TagGroupTagServiceImpl implements ITagGroupTagService {

    @Autowired
    private TagGroupTagDBO tagGroupTagDBO;


    @Override
    public List<TagGroupTagEntity> getTagGroupTagByTagGroupId(Long appId) {
        LambdaQueryWrapper<TagGroupTagEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TagGroupTagEntity::getGroupId, appId);
        return tagGroupTagDBO.list(queryWrapper);
    }

    @Override
    public Boolean save(TagGroupTagEntity tagGroupTagEntity) {
        return tagGroupTagDBO.save(tagGroupTagEntity);
    }

    @Override
    public boolean deleteByTagId(Long id) {
        LambdaQueryWrapper<TagGroupTagEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TagGroupTagEntity::getTagId, id);
        return tagGroupTagDBO.remove(queryWrapper);
    }

    @Override
    public TagGroupTagEntity getTagGroupTagByTagId(Long id) {
        LambdaQueryWrapper<TagGroupTagEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TagGroupTagEntity::getTagId, id);
        return tagGroupTagDBO.getOne(queryWrapper);
    }

    @Override
    public List<TagGroupTagEntity> getTagGroupTagByTagIds(List<Long> tagIds) {
        LambdaQueryWrapper<TagGroupTagEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(TagGroupTagEntity::getTagId, tagIds);
        return tagGroupTagDBO.list(queryWrapper);
    }
}
