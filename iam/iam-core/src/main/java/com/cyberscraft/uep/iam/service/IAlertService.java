package com.cyberscraft.uep.iam.service;

import com.cyberscraft.uep.iam.dto.response.AuditlogItemVO;

public interface IAlertService {
//    private static final Logger logger = LoggerFactory.getLogger(AlertService.class);
//
//    @Autowired
//    CacheService cacheService;
//
//    @Autowired
//    private FieldDictService fieldDictService;
//
//    @Autowired protected UserDao userDao;
//
//    public void sendAlertIfNecessary(String username, HttpServletRequest httpServletRequest) {
//        try{
//            AlertPolicyPO po = cacheService.getAlertPolicy(AlertPolicyPO.FAILED_LOGIN_ALERT_POLICY_ID);
//            if (!po.isEnabled()){
//                //policy not enabled, ignore
//                logger.debug("{} policy not enabled", AlertPolicyPO.FAILED_LOGIN_ALERT_POLICY_ID);
//                return;
//            }
//
//            NotificationChannels channels = po.getNotificationChannels();
//            if (channels == null){
//                logger.error("{} policy doesn't have notification channels", AlertPolicyPO.FAILED_LOGIN_ALERT_POLICY_ID);
//                return;
//            }
//            if(!Strings.isNullOrEmpty(httpServletRequest.getParameter(CommonConstants.USERNAME))){
//                username = httpServletRequest.getParameter(CommonConstants.USERNAME);
//            }
//
//            Set<String> emailTo = getEmailTo(channels, username);
//            if (emailTo == null || emailTo.size() == 0){
//                logger.error("{} policy doesn't have email_to ", AlertPolicyPO.FAILED_LOGIN_ALERT_POLICY_ID);
//                return;
//            }
//
//            AlertPolicyThreshold threshold = po.getThreshold();
//            if (threshold == null){
//                logger.error("{} policy doesn't have threshold ", AlertPolicyPO.FAILED_LOGIN_ALERT_POLICY_ID);
//                return;
//            }
//
//            if (threshold.getInterval() <= 0){
//                logger.error("{} policy interval <= 0", AlertPolicyPO.FAILED_LOGIN_ALERT_POLICY_ID);
//                return;
//            }
//
//            if (threshold.getCount() <= 0){
//                logger.error("{} policy count <= 0", AlertPolicyPO.FAILED_LOGIN_ALERT_POLICY_ID);
//                return;
//            }
//
//            long alertStartTime = new Date().getTime() - threshold.getInterval() * 1000L;
//
//            PwdFailureTimesPO pwdFailureTimesPO = userDao.getPwdFailureTimeAfterAlertStartTime(username, alertStartTime);
//            if (pwdFailureTimesPO.getPwdFailureTimeCount() == 0){
//                logger.debug("no pwdFailureTime for {}", username);
//                return;
//            }
//            if (pwdFailureTimesPO.getPwdFailureTimeCount() >= threshold.getCount() && pwdFailureTimesPO.getPwdFailureTimeCount() % threshold.getCount() == 0){
//                sendOutEmailAlert(
//                        HttpUtil.getRemoteHost(httpServletRequest), username, threshold.getInterval()/60,pwdFailureTimesPO, emailTo);
//            }
//        }catch(Exception e){
//            logger.error(CommonConstants.BLANK, e);
//        }
//    }
//
//    private Set<String> getEmailTo(NotificationChannels channels, String username) {
//        Set<String> emailTo = new HashSet<>(2);
//        if (channels.isNotifyAdmin()){
//            PersonPO admin = userDao.getAdminUserEmail();
//            if (admin.getMail() != null){
//                emailTo.add(admin.getMail());
//            }
//        }
//
//        if(channels.isNotifyUser()){
//            PersonPO user = userDao.getUserEmail(username);
//            if (user.getMail() != null){
//                emailTo.add(user.getMail());
//            }
//        }
//
//        if (channels.getEmailTo() != null){
//            emailTo.addAll(channels.getEmailTo());
//        }
//        return emailTo;
//    }
//
//    @Autowired EmailService emailService;
//    private void sendOutEmailAlert(String srcIp, String username, int intervalInMinutes, PwdFailureTimesPO pwdFailureTimesPO, Set<String> emailTo) {
//        logger.error("user {} enter wrong password {} times", username, pwdFailureTimesPO.getPwdFailureTimeCount());
//        Map<String, Object> model = new HashMap<>();
//        model.put("username", username);
//        model.put("interval", intervalInMinutes);
//        model.put("count", pwdFailureTimesPO.getPwdFailureTimeCount());
//        model.put("lastFailureTime", DaoDateUtil.fromMilli2ChinaDateTimeStr(pwdFailureTimesPO.getLatestPwdFailureTime()));
//        model.put("srcIp", srcIp);
//        emailService.sendFailedLoginAlertEmail(model ,emailTo);
//    }
//

    /**
     * 检查该用户是否存在异地登录
     * @param username
     * @param loginIp
     */
    void checkCrossRegionLogin(String username, String loginIp);

    /**
     * 异步检查该用户是否存在异地登录
     * @param username
     * @param loginIp
     */
    void ansyncCheckCrossRegionLogin(String username, String loginIp);
}
