/*******************************************************************************
 * Copyright 2016 The MITRE Corporation
 *   and the MIT Internet Trust Consortium
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *******************************************************************************/
package com.cyberscraft.uep.iam.service.oidc.domain;

import com.cyberscraft.uep.iam.dto.enums.UserPWDStatus;
import com.cyberscraft.uep.iam.dto.enums.UserStatus;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.util.Collection;
import java.util.List;
import java.util.Map;


public class UserInfo implements UserDetails {
	public static final String AUTHORITIES = "authorities";
	private static final long serialVersionUID = -315141773695518982L;
	private Map<String, String> extensions;
	private String sub;
	private UserPWDStatus passwordStatus;
	private String username;
	private String userJobNumber;
	private String preferredUsername;
	private String name;
	private String email;
	private List<String> userCertIds;
	/**
	 * 用户的电子邮件地址是否已经验证过 ,0：否，1：是
	 */
	private Integer emailVerified;
	private UserStatus status;
	private String password;
	private String tenantCode;
	/**
	 * 用户的移动电话号码
	 */
	private String phoneNumber;

	/**
	 * 用户的移动电话号码是否已经验证过,1:是，0：否
	 */
	private Integer phoneNumberVerified;

	/**
	 * 同步源的用户登录名
	 */
	private String connectorUid;

	/**
	 * 角色类型，1：ROLE_USER，2：ROLE_ADMIN
	 */
	private Integer roleType;

	/**
	 * 身份证号码
	 */
	private String idCard;

	/**
	 * 身份证号码是否经过验证 1：是，0：否
	 */
	private Integer idCardVerified;

	/**
	 * 姓名是否经过验证 1：是，0：否
	 */
	private Integer nameVerified;

	/**
	 * 工号是否经过验证 1：是，0：否
	 */
	private Integer userJobNumberVerified;

	public String getConnectorUid() {
		return connectorUid;
	}

	public void setConnectorUid(String connectorUid) {
		this.connectorUid = connectorUid;
	}

	public Integer getEmailVerified() {
		return emailVerified;
	}

	public void setEmailVerified(Integer emailVerified) {
		this.emailVerified = emailVerified;
	}

	public String getPhoneNumber() {
		return phoneNumber;
	}

	public void setPhoneNumber(String phoneNumber) {
		this.phoneNumber = phoneNumber;
	}

	public Integer getPhoneNumberVerified() {
		return phoneNumberVerified;
	}

	public void setPhoneNumberVerified(Integer phoneNumberVerified) {
		this.phoneNumberVerified = phoneNumberVerified;
	}

	public String getTenantCode() {
		return tenantCode;
	}

	public void setTenantCode(String tenantCode) {
		this.tenantCode = tenantCode;
	}

	public UserPWDStatus getPasswordStatus() {
		return passwordStatus;
	}

	public void setPassword(String password) {
		this.password = password;
	}

	public void setPasswordStatus(UserPWDStatus passwordStatus) {
		this.passwordStatus = passwordStatus;
	}

	public UserStatus getStatus() {
		return status;
	}

	public void setStatus(UserStatus status) {
		this.status = status;
	}

	@Override
	public String getUsername() {
		return username;
	}

	public void setUsername(String username) {
		this.username = username;
	}

	public String getUserJobNumber() {
		return userJobNumber;
	}

	public void setUserJobNumber(String userJobNumber) {
		this.userJobNumber = userJobNumber;
	}

	public String getPreferredUsername() {
		return preferredUsername;
	}

	public void setPreferredUsername(String preferredUsername) {
		this.preferredUsername = preferredUsername;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public List<String> getUserCertIds() {
		return userCertIds;
	}

	public void setUserCertIds(List<String> userCertIds) {
		this.userCertIds = userCertIds;
	}

	public Map<String, String> getExtensions() {
		return extensions;
	}

	public void setExtensions(Map<String, String> extensions) {
		this.extensions = extensions;
	}

	public String getSub() {
		return sub;
	}

	public void setSub(String sub) {
		this.sub = sub;
	}

	@Override
	public Collection<? extends GrantedAuthority> getAuthorities() {
		return null;
	}

	@Override
	public String getPassword() {
		return password;
	}

	@Override
	public boolean isAccountNonExpired() {
		return status == UserStatus.ACTIVE;
	}

	@Override
	public boolean isAccountNonLocked() {
		return UserPWDStatus.ADMIN_LOCKED != passwordStatus;
	}

	@Override
	public boolean isCredentialsNonExpired() {
		return UserPWDStatus.EXPIRED != passwordStatus;
	}

	@Override
	public boolean isEnabled() {
		return status == UserStatus.ACTIVE;
	}



	public String getName() {
		return this.name;
	}

	public String getPicture() {
		return null;
	}

	public String getDsUid() {
		return null;
	}

	public Integer getRoleType() {
		return roleType;
	}

	public void setRoleType(Integer roleType) {
		this.roleType = roleType;
	}

	public String getIdCard() {
		return idCard;
	}

	public void setIdCard(String idCard) {
		this.idCard = idCard;
	}

	public Integer getIdCardVerified() {
		return idCardVerified;
	}

	public void setIdCardVerified(Integer idCardVerified) {
		this.idCardVerified = idCardVerified;
	}

	public Integer getNameVerified() {
		return nameVerified;
	}

	public void setNameVerified(Integer nameVerified) {
		this.nameVerified = nameVerified;
	}

	public Integer getUserJobNumberVerified() {
		return userJobNumberVerified;
	}

	public void setUserJobNumberVerified(Integer userJobNumberVerified) {
		this.userJobNumberVerified = userJobNumberVerified;
	}

	@Override
	public String toString() {
		return "UserInfo{" +
				"extensions=" + extensions +
				", sub='" + sub + '\'' +
				", passwordStatus=" + passwordStatus +
				", username='" + username + '\'' +
				", preferredUsername='" + preferredUsername + '\'' +
				", name='" + name + '\'' +
				", email='" + email + '\'' +
				", userCertIds=" + userCertIds +
				", emailVerified=" + emailVerified +
				", status=" + status +
				", password='" + password + '\'' +
				", tenantCode='" + tenantCode + '\'' +
				", phoneNumber='" + phoneNumber + '\'' +
				", phoneNumberVerified=" + phoneNumberVerified +
				", roleType=" + roleType +
				'}';
	}
}
