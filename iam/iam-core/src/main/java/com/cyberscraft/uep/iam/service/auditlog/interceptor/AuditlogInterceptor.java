package com.cyberscraft.uep.iam.service.auditlog.interceptor;

import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.iam.dto.request.login.AuthenMethod;
import com.cyberscraft.uep.iam.service.auditlog.AuditlogRecordBuilder;
import com.cyberscraft.uep.iam.service.auditlog.aop.AuditAspect;
import com.cyberscraft.uep.iam.service.auditlog.constant.*;
import com.cyberscraft.uep.iam.entity.AppEntity;
import com.cyberscraft.uep.iam.service.oidc.domain.UserInfo;
import com.cyberscraft.uep.iam.service.oidc.token.entity.MultiFactorAuthenticationToken;
import com.cyberscraft.uep.iam.service.oidc.util.SecureRequestCheck;
import com.cyberscraft.uep.iam.dbo.AppDBO;
import com.cyberscraft.uep.iam.dto.constraint.as.AuthServerConstants;
import com.cyberscraft.uep.iam.dto.response.AuditlogItemVO;
import com.cyberscraft.uep.iam.service.oidc.util.OIDCConstants;
import com.cyberscraft.uep.iam.service.IAuditlogService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.common.exceptions.OAuth2Exception;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.ModelAndView;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import static com.cyberscraft.uep.iam.dto.constraint.as.AuthServerConstants.STR_USER_OAUTH_APPROVAL;

@Component
public class AuditlogInterceptor extends HandlerInterceptorAdapter {
    private static Logger logger = LoggerFactory.getLogger(AuditAspect.class);

    @Autowired
    private IAuditlogService auditlogService;

    @Autowired
    private AppDBO appDBO;

    private Map<String, String> getRequestParams(HttpServletRequest request) {
        Map<String, String> paramsRet = new HashMap<>();
        Map<String, String[]> map = request.getParameterMap();
        Set<String> keys = map.keySet();
        for (String key : keys) {
            String[] value = map.get(key);
            paramsRet.put(key, value[0]);
        }
        return paramsRet;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler,
                           @Nullable ModelAndView modelAndView) throws Exception {
        String requestURI = request.getRequestURI();
        String params = JsonUtil.obj2Str(getRequestParams(request));
        String clientId = request.getParameter(AuthServerConstants.STR_CLIENT_ID);
        String username = SecureRequestCheck.getUsername();
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String authId = null;
        String uid = null;
        if (authentication instanceof MultiFactorAuthenticationToken) {
            MultiFactorAuthenticationToken authenticationToken = (MultiFactorAuthenticationToken) authentication;
            Set<AuthenMethod> authenMethods = authenticationToken.authenMethods();
            for (AuthenMethod authenMethod : authenMethods) {
                authId = authenMethod.getAuthId();
            }
        }
        Object principal = authentication.getPrincipal();
        if (principal != null && (principal instanceof UserInfo)) {
            UserInfo tUser = (UserInfo) (authentication.getPrincipal());
            uid = tUser.getSub();
        }
        if (StringUtils.isEmpty(username)) {
            username = request.getParameter(AuthServerConstants.LOGIN_USERNAME);
        }
        String responsType = request.getParameter(OIDCConstants.RESPONSE_TYPE);
        String grantType = request.getParameter(OIDCConstants.GRANT_TYPE);
        String method = request.getMethod();
        if (username == null) {
            username = "";
        }
        Integer eventSubType = AuditEventSubtype.AUTHORIZE.code();
        switch (requestURI) {
            case AuthServerConstants.AUTHORIZE_ENDPOINT:
                eventSubType = AuditEventSubtype.AUTHORIZE.code();
                if (method.equalsIgnoreCase("GET")) {
                    if (OIDCConstants.AUTH2_RESPONSE_TYPE_TOKEN.equalsIgnoreCase(responsType)) {
                        eventSubType = AuditEventSubtype.AUTHORIZE_TOKEN.code();
                    }
                } else if (method.equalsIgnoreCase("POST")) {
                    String userApproval = request.getParameter(STR_USER_OAUTH_APPROVAL);
                    if (userApproval != null && userApproval.equalsIgnoreCase("true")) {
                        eventSubType = AuditEventSubtype.AUTHORIZE_USER_APPROVAL.code();
                    } else {
                        eventSubType = AuditEventSubtype.AUTHORIZE_USER_DENY.code();
                    }
                }
                recordAuditlog(clientId, authId, eventSubType, username, uid, "0", "", AuditLevel.INFO.code(), params);

                break;
            case AuthServerConstants.TOKEN_ENDPOINT:
                if (OIDCConstants.GRANT_TYPE_DEVICE_CODE.equals(grantType)) {
                    recordAuditlog(clientId, authId, AuditEventSubtype.AUTHORIZE_QRCODE.code(), username, uid, "0", "", AuditLevel.INFO.code(), params);
                } else {
//                    recordAuditlog(clientId, AuditEventSubtype.AUTHORIZE_TOKEN.code(), username, "0", "", AuditLevel.INFO.code(), params);
                }
                break;
            case AuthServerConstants.ERROR_ENDPOINT:
                eventSubType = AuditEventSubtype.AUTHORIZE.code();
                if (OIDCConstants.AUTH2_RESPONSE_TYPE_TOKEN.equalsIgnoreCase(responsType)) {
                    eventSubType = AuditEventSubtype.AUTHORIZE_TOKEN.code();
                }

                Object error = request.getAttribute("error");
                if (error instanceof OAuth2Exception) {
                    OAuth2Exception ex = (OAuth2Exception) error;
                    String errorcode = ex.getOAuth2ErrorCode();
                    String errormsg = ex.getMessage();
                    recordAuditlog(clientId, authId, eventSubType, username, uid, errorcode, errormsg, AuditLevel.ERROR.code(), params);
                } else {
                    logger.error("Unknown error: {}", error);
                }
                break;
        }
    }

    private void recordAuditlog(String clientId, String authId, Integer eventSubType, String username, String uid, String errorCode, String errorMsg, Integer level, String params) {
        if (StringUtils.isNotEmpty(clientId)) {
            AppEntity appEntity = appDBO.getAppByClientId(clientId);
            if (appEntity != null) {
                String clientName = appEntity.getClientName();
                AuditlogItemVO auditlogItemVO = AuditlogRecordBuilder.create()
                        .setAuditType(AuditType.LOGIN.code())
                        .setEventType(AuditEventType.LOGIN.code())
                        .setEventSubtype(eventSubType)
                        .setLevel(level)
                        .setTargetId(clientId)
                        .setTargetName(clientName)
                        .setErrorCode(errorCode)
                        .setErrorDescription((errorMsg != null && errorMsg.length() > 125) ? errorMsg.substring(0, 125) : errorMsg)
                        .setOperator(username)
                        .setParameters(params)
                        .setClientId(clientId)
                        .setAuthId(authId)
                        .setUid(uid)
                        .build();
                auditlogItemVO.setRoleType(getRoleTypeByAuthToken());
                auditlogService.ansyncRecord(auditlogItemVO);
            }
        }
    }

    private Integer getRoleTypeByAuthToken() {
        Authentication authentication = SecureRequestCheck.getAuthentication();
        ;
        Object principal = authentication.getPrincipal();
        if (principal instanceof UserInfo) {
            UserInfo userInfo = (UserInfo) principal;
            return userInfo.getRoleType();
        }
        return null;
    }
}
