package com.cyberscraft.uep.iam.common.exception;

/**
 * Constraint violation, possibly unique constraint violation.
 * 
 * For example: when adding a new user account into LDAP, and the new uses's email 
 * address is used by an existing account, connection manager will throw this exception.  
 */
public class ExceptDbConstraintViolation extends DaoException {

    /**
     * 
     */
    private static final long serialVersionUID = 7919726839173373290L;

    public ExceptDbConstraintViolation(String message) {
        super(message);
    }

    public ExceptDbConstraintViolation(Throwable throwable) {
        super(throwable);
    }

    public ExceptDbConstraintViolation(String message, Throwable throwable) {
        super(message, throwable);
    }

}
