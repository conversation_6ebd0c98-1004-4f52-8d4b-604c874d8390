package com.cyberscraft.uep.iam.service.scim.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cyberscraft.uep.account.client.domain.Connector;
import com.cyberscraft.uep.account.client.domain.ConnectorUser;
import com.cyberscraft.uep.account.client.service.IExternalConnectService;
import com.cyberscraft.uep.account.client.util.AccountUtil;
import com.cyberscraft.uep.common.dto.OffsetQuery;
import com.cyberscraft.uep.common.util.BeanUtil;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.common.util.PagingUtil;
import com.cyberscraft.uep.iam.common.constants.DaoConstants;
import com.cyberscraft.uep.iam.common.enums.LocalUserAttr;
import com.cyberscraft.uep.iam.common.exception.UserCenterException;
import com.cyberscraft.uep.iam.dao.UserOrgDao;
import com.cyberscraft.uep.iam.dbo.ConnectorDBO;
import com.cyberscraft.uep.iam.dbo.OrgDBO;
import com.cyberscraft.uep.iam.dbo.UserDBO;
import com.cyberscraft.uep.iam.dto.domain.SyncStats;
import com.cyberscraft.uep.iam.dto.enums.CreatedMode;
import com.cyberscraft.uep.iam.dto.enums.SyncStatus;
import com.cyberscraft.uep.iam.dto.scim.*;
import com.cyberscraft.uep.iam.dto.scim.exception.ScimException;
import com.cyberscraft.uep.iam.dto.scim.filter.*;
import com.cyberscraft.uep.iam.entity.*;
import com.cyberscraft.uep.iam.errors.TransactionErrorType;
import com.cyberscraft.uep.iam.service.connector.*;
import com.cyberscraft.uep.iam.service.scim.IResourceService;
import com.cyberscraft.uep.iam.service.transfer.UserTransfer;
import com.cyberscraft.uep.iam.service.user.IExternalUserService;
import com.cyberscraft.uep.iam.service.user.IOrganizationService;
import com.cyberscraft.uep.iam.service.user.IUserService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.cyberscraft.uep.iam.errors.TransactionErrorType.USER_NOT_FOUND;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/5/5 3:46 下午
 */
@Service
public class ResourceServiceImpl implements IResourceService {
    private final static Logger logger = LoggerFactory.getLogger(ResourceServiceImpl.class);

    @Autowired
    IUserService iUserService;

    @Autowired
    IOrganizationService organizationService;

    @Autowired
    UserTransfer userTransfer;

    @Autowired
    private OrgDBO orgDBO;

    @Autowired
    private UserOrgDao userOrgDao;

    @Autowired
    private UserDBO userDBO;

    @Autowired
    private IConnectorService connectorService;

    @Autowired
    private IConnectorManageService connectorManageService;

    @Autowired
    private IExternalUserService externalUserService;

    @Autowired
    private IExternalConnectService externalConnectService;

    @Autowired
    private IConnectorSyncHistoryService connectorSyncHistoryService;

    @Autowired
    private ConnectorDBO connectorDBO;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public User createUser(User user) {
        UserExtension userExtension = user.getUserExtension();
        String departmentPath = (userExtension == null ? "" : userExtension.getDepartment());
        OrgEntity orgEntity = organizationService.deepCreateOrg(departmentPath, CreatedMode.BY_SCIM);

        Map<String, Object> userInfo = userTransfer.userResourceToMap(user);
        userInfo.put(LocalUserAttr.org_ids.getDomainName(), Arrays.asList(String.valueOf(orgEntity.getId())));

        Map<String, Object> map = new HashMap<>();
        for (String key : userInfo.keySet()) {
            Object value = userInfo.get(key);
            if (value != null && !StringUtils.isBlank(value.toString())) {
                map.put(key, value);
            }
        }
        Long uid = iUserService.createUser(map, CreatedMode.BY_SCIM);

        UserEntity userEntity = iUserService.getUser(uid);
        User createdUser = userTransfer.entityToResource(userEntity);
        createdUser.setUserExtension(userExtension);
        return createdUser;
    }

    @Override
    public User getUser(String id) {
        try {
            UserEntity userEntity = iUserService.getUser(Long.valueOf(id));
            if (userEntity == null) {
                throw new UserCenterException(HttpStatus.NOT_FOUND, USER_NOT_FOUND);
            }

            return getUser(userEntity);
        } catch (Exception e) {
            throw new UserCenterException(HttpStatus.NOT_FOUND, USER_NOT_FOUND);
        }
    }

    private User getUser(UserEntity userEntity) {
        User user = userTransfer.entityToResource(userEntity);

        String orgNamePath = getOrgNamePathByUserId(String.valueOf(userEntity.getId()));

        UserExtension userExtension = new UserExtension();
        userExtension.setDepartment(orgNamePath);
        userExtension.setEmployeeNumber(userEntity.getUserJobNumber());

        user.setUserExtension(userExtension);
        return user;
    }

    private String getOrgNamePathByUserId(String uid) {
        LambdaQueryWrapper<UserOrgEntity> userOrgEntityLambdaQueryWrapper =
                new QueryWrapper<UserOrgEntity>().lambda().select(UserOrgEntity::getOrgRefId).in(UserOrgEntity::getUid, uid);
        List<Object> userOrgEntityList = userOrgDao.selectObjs(userOrgEntityLambdaQueryWrapper);
        Long orgId = (Long) userOrgEntityList.get(0);
        OrgEntity orgEntity = orgDBO.getById(orgId);

        List<String> namePathByIdPath = orgDBO.getNamePathByIdPath(orgEntity.getOrgPath());
        StringBuilder namePath = new StringBuilder();
        for (String s : namePathByIdPath) {
            namePath.append("/").append(s);
        }
        return namePath.toString();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public User replaceUser(String id, User user) {
        UserEntity userEntity = iUserService.getUser(Long.valueOf(id));
        if (userEntity == null) {
            throw new UserCenterException(HttpStatus.NOT_FOUND, USER_NOT_FOUND);
        }

        Map<String, Object> userInfo = userTransfer.userResourceToMap(user);

        UserExtension userExtension = user.getUserExtension();
        String departmentPath = (userExtension == null ? "" : userExtension.getDepartment());
        OrgEntity orgEntity = organizationService.deepCreateOrg(departmentPath, CreatedMode.BY_SCIM);

        userInfo.put(LocalUserAttr.org_ids.getDomainName(), Arrays.asList(String.valueOf(orgEntity.getId())));
        userInfo.put(LocalUserAttr.update_time.getDomainName(), LocalDateTime.now());

        userInfo.remove(LocalUserAttr.password.getDomainName());
        userInfo.remove(LocalUserAttr.username.getDomainName());
        iUserService.updateUser(userEntity.getUsername(), userInfo, CreatedMode.BY_SCIM);

        //重新获取一下userEntity
        userEntity = iUserService.getUser(Long.valueOf(id));
        User replacedUser = userTransfer.entityToResource(userEntity);
        replacedUser.setUserExtension(userExtension);
        return replacedUser;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public User patchUser(String id, PatchParameter patchParameter) {
        UserEntity userEntity = iUserService.getUser(Long.valueOf(id));
        if (userEntity == null) {
            throw new UserCenterException(HttpStatus.NOT_FOUND, USER_NOT_FOUND);
        }

        User user = patchUser(userEntity, patchParameter);

        Map<String, Object> userInfo = userTransfer.userResourceToMap(user);

        UserExtension userExtension = user.getUserExtension();
        String departmentPath = (userExtension == null ? "" : userExtension.getDepartment());
        OrgEntity orgEntity = organizationService.deepCreateOrg(departmentPath, CreatedMode.BY_SCIM);
        userInfo.put(LocalUserAttr.org_ids.getDomainName(), Arrays.asList(String.valueOf(orgEntity.getId())));

        userInfo.put(LocalUserAttr.update_time.getDomainName(), user.getMeta().getLastModified());
        userInfo.remove(LocalUserAttr.password.getDomainName());
        userInfo.remove(LocalUserAttr.username.getDomainName());
        iUserService.updateUser(userEntity.getUsername(), userInfo, CreatedMode.BY_SCIM);

        return user;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteUser(String id) {
        UserEntity userEntity = iUserService.getUser(Long.valueOf(id));
        if (userEntity == null) {
            throw new UserCenterException(HttpStatus.NOT_FOUND, USER_NOT_FOUND);
        }

        iUserService.removeUser(userEntity);
    }

    @Override
    public QueryResponse<User> filter(QueryParameter queryParameter) {
        String filter = queryParameter.getFilter();
//        Set<String> attributes = queryParameter.getAttributes();
//        Set<String> excludedAttributes = queryParameter.getExcludedAttributes();
        Long startIndex = queryParameter.getStartIndex();
        Integer count = queryParameter.getCount();
        String sortBy = queryParameter.getSortBy();
        OrderEnum sortOrder = queryParameter.getSortOrder();

        if (startIndex != null) {
            startIndex = startIndex - 1;
        }
        OffsetQuery<UserEntity> userEntityOffsetQuery = new OffsetQuery<>(startIndex, count, sortBy, OrderEnum.descending == sortOrder ? true : false);
        Page<UserEntity> page = PagingUtil.toMybatisPage(userEntityOffsetQuery);

        String[] userEntityAttrs = {LocalUserAttr.sub.getFieldName(), LocalUserAttr.username.getFieldName(), LocalUserAttr.name.getFieldName(), LocalUserAttr.nickname.getFieldName(), LocalUserAttr.email.getFieldName(), LocalUserAttr.phone_number.getFieldName(), LocalUserAttr.user_job_number.getFieldName(), LocalUserAttr.create_time.getFieldName(), LocalUserAttr.update_time.getFieldName()};

        LambdaQueryWrapper<UserEntity> queryWrapper = new QueryWrapper().select(userEntityAttrs).lambda();
        FilterGroupExpress rootExpress = FilterExpressParser.parse(filter);
        Consumer<LambdaQueryWrapper<UserEntity>> consumer = nestedQuery(rootExpress);
        consumer.accept(queryWrapper);

        IPage<UserEntity> resultPage = userDBO.getBaseMapper().selectPage(page, queryWrapper);
        List<User> users = new ArrayList<>();
        for (UserEntity record : resultPage.getRecords()) {
            User user = userTransfer.entityToResource(record);
            UserExtension userExtension = new UserExtension();
            userExtension.setDepartment(getOrgNamePathByUserId(user.getId()));
            userExtension.setEmployeeNumber(record.getUserJobNumber());
            user.setUserExtension(userExtension);
            users.add(user);
        }

        QueryResponse<User> userQueryResponse = new QueryResponse<>(users, resultPage.getTotal(), userEntityOffsetQuery.getOffset() + 1, userEntityOffsetQuery.getCount());
        return userQueryResponse;
    }

    /**
     * 查询条件嵌套
     *
     * @param groupExpress
     * @return
     */
    Consumer<LambdaQueryWrapper<UserEntity>> nestedQuery(FilterGroupExpress groupExpress) {
        return (e) -> {
            if (groupExpress != null) {
                List<FilterExpress> childs = groupExpress.getChilds();
                for (FilterExpress child : childs) {
                    if (child instanceof FilterSingleExpress) {
                        Consumer<LambdaQueryWrapper<UserEntity>> single = singleQuery((FilterSingleExpress) child);
                        single.accept(e);
                    } else {
                        FilterGroupExpress group = (FilterGroupExpress) child;
                        if (OperatorVariables.or.equals(group.getJoin())) {
                            e.or().nested(nestedQuery(group));
                        } else {
                            e.nested(nestedQuery(group));
                        }
                    }
                }
            }
        };
    }

    /**
     * 单个查询条件
     *
     * @param single
     * @return
     */
    Consumer<LambdaQueryWrapper<UserEntity>> singleQuery(FilterSingleExpress single) {
        return (e) -> {
            String join = single.getJoin();
            String attr = single.getAttr();
            String value = single.getValue();
            String op = single.getOp();

            if (attr.startsWith(ScimConstant.ENTERPRISE_USER_SCHEMA)) {
                attr = attr.substring(ScimConstant.ENTERPRISE_USER_SCHEMA.length() + 1);
                if (ScimConstant.ENTERPRISE_USER_ATTR_DEPARTMENT.equals(attr)) {
                    OrgEntity orgEntity = null;
                    if (StringUtils.isBlank(value)) {
                        orgEntity = orgDBO.getById(DaoConstants.NULL_ORG_ID);
                    }
                    String[] split = value.split("/");
                    List<String> pathNames = Arrays.stream(split).filter(e1 -> StringUtils.isNotBlank(e1)).collect(Collectors.toList());
                    if (pathNames.size() == 0) {
                        orgEntity = orgDBO.getById(DaoConstants.NULL_ORG_ID);
                    } else {
                        if (!pathNames.get(0).equals(DaoConstants.ROOT_ORG_NAME)) {
                            pathNames.add(0, DaoConstants.ROOT_ORG_NAME);
                        }
                        orgEntity = orgDBO.getByNamePath(pathNames);
                    }
                    List<Object> userOrgEntityList = new ArrayList<>();
                    if (orgEntity != null) {
                        if (op.equals(OperatorVariables.eq)) {
                            LambdaQueryWrapper<UserOrgEntity> userOrgEntityLambdaQueryWrapper =
                                    new QueryWrapper<UserOrgEntity>().lambda().select(UserOrgEntity::getUid).eq(UserOrgEntity::getOrgRefId, orgEntity.getId());
                            userOrgEntityList = userOrgDao.selectObjs(userOrgEntityLambdaQueryWrapper);
                        }
                    }
                    if (userOrgEntityList.size() > 0) {
                        if (OperatorVariables.or.equals(join)) {
                            e.or().in(UserEntity::getId, userOrgEntityList);
                        } else {
                            e.in(UserEntity::getId, userOrgEntityList);
                        }
                    } else {
                        if (OperatorVariables.or.equals(join)) {
                            e.or().eq(UserEntity::getId, -1L);
                        } else {
                            e.eq(UserEntity::getId, -1L);
                        }
                    }
                    return;
                }
            }

            SFunction<UserEntity, ?> userEntitySFunction = userAttrToColumn(attr);
            if (userEntitySFunction == null) {
                return;
            }

            switch (op) {
                case OperatorVariables.eq:
                    if (OperatorVariables.or.equals(join)) {
                        e.or().eq(userEntitySFunction, value);
                    } else {
                        e.eq(userEntitySFunction, value);
                    }
                    break;
                case OperatorVariables.ne:
                    if (OperatorVariables.or.equals(join)) {
                        e.or().ne(userEntitySFunction, value);
                    } else {
                        e.ne(userEntitySFunction, value);
                    }
                    break;
                case OperatorVariables.co:
                    if (OperatorVariables.or.equals(join)) {
                        e.or().like(userEntitySFunction, value);
                    } else {
                        e.like(userEntitySFunction, value);
                    }
                    break;
                case OperatorVariables.sw:
                    if (OperatorVariables.or.equals(join)) {
                        e.or().likeLeft(userEntitySFunction, value);
                    } else {
                        e.likeLeft(userEntitySFunction, value);
                    }
                    break;
                case OperatorVariables.ew:
                    if (OperatorVariables.or.equals(join)) {
                        e.or().likeRight(userEntitySFunction, value);
                    } else {
                        e.likeRight(userEntitySFunction, value);
                    }
                    break;
                case OperatorVariables.pr:
                    if (OperatorVariables.or.equals(join)) {
                        e.or().isNotNull(userEntitySFunction);
                    } else {
                        e.isNotNull(userEntitySFunction);
                    }
                    break;
                case OperatorVariables.gt:
                    if (OperatorVariables.or.equals(join)) {
                        e.or().gt(userEntitySFunction, value);
                    } else {
                        e.gt(userEntitySFunction, value);
                    }
                    break;
                case OperatorVariables.ge:
                    if (OperatorVariables.or.equals(join)) {
                        e.or().ge(userEntitySFunction, value);
                    } else {
                        e.ge(userEntitySFunction, value);
                    }
                    break;
                case OperatorVariables.lt:
                    if (OperatorVariables.or.equals(join)) {
                        e.or().lt(userEntitySFunction, value);
                    } else {
                        e.lt(userEntitySFunction, value);
                    }
                    break;
                case OperatorVariables.le:
                    if (OperatorVariables.or.equals(join)) {
                        e.or().le(userEntitySFunction, value);
                    } else {
                        e.le(userEntitySFunction, value);
                    }
                    break;
            }
        };
    }

    /**
     * 将属性path转成UserEntity的Column Function
     *
     * @param fullAttrPath
     * @return
     */
    SFunction<UserEntity, ?> userAttrToColumn(String fullAttrPath) {
        Map<String, ResourceAttributes> userAttrMap = ScimConstant.USER_ATTRIBUTES.stream().collect(Collectors.toMap(ResourceAttributes::getName, Function.identity()));
        Map<String, ResourceAttributes> enterpriseUserAttrMap = ScimConstant.ENTERPRISE_USER_ATTRIBUTES.stream().collect(Collectors.toMap(ResourceAttributes::getName, Function.identity()));

        String shortAttrPath = fullAttrPath;
        ResourceAttributes attributes;
        if (fullAttrPath.startsWith(ScimConstant.ENTERPRISE_USER_SCHEMA)) {
            shortAttrPath = fullAttrPath.substring(ScimConstant.ENTERPRISE_USER_SCHEMA.length() + 1);
            attributes = FilterExpressParser.findAttrObjByAttrPath(shortAttrPath, enterpriseUserAttrMap);
        } else if (fullAttrPath.startsWith(ScimConstant.USER_SCHEMA)) {
            shortAttrPath = fullAttrPath.substring(ScimConstant.USER_SCHEMA.length() + 1);
            attributes = FilterExpressParser.findAttrObjByAttrPath(shortAttrPath, userAttrMap);
        } else {
            attributes = FilterExpressParser.findAttrObjByAttrPath(shortAttrPath, userAttrMap);
        }

        if (attributes != null) {
            MutabilityEnum mutability = attributes.getMutability();
            if (mutability == MutabilityEnum.writeOnly) {
                logger.error("this attribute is writeOnly:{}", fullAttrPath);
                return null;
            }

            if (fullAttrPath.startsWith(ScimConstant.USER_ATTR_EMAILS)) {
                return UserEntity::getEmail;
            }

            if (fullAttrPath.startsWith(ScimConstant.USER_ATTR_PHONENUMBERS)) {
                return UserEntity::getPhoneNumber;
            }

            String name = attributes.getName();
            switch (name) {
                case ScimConstant.COMMON_ATTR_ID:
                    return UserEntity::getId;
                case ScimConstant.COMMON_ATTR_META_CREATED:
                    return UserEntity::getCreateTime;
                case ScimConstant.COMMON_ATTR_META_LASTMODIFIED:
                    return UserEntity::getUpdateTime;
                case ScimConstant.USER_ATTR_USERNAME:
                    return UserEntity::getUsername;
                case ScimConstant.USER_ATTR_DISPLAYNAME:
                    return UserEntity::getName;
                case ScimConstant.USER_ATTR_NICKNAME:
                    return UserEntity::getNickname;
            }
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public User createUser(String connectorId, User user) {
        Boolean isSuccess = false;
        User result = null;

        try {
            result = syncUser(connectorId, user);
            isSuccess = true;
        } catch (Exception e) {
            logger.error("createUser fail", e);
        }

        if (!isSuccess) {
            throw new UserCenterException(TransactionErrorType.DS_SYNC_JOB_FAILED_ERROR);
        }
        return result;
    }

    private User syncUser(String connectorId, User user) {
        Connector connector = connectorService.getConnector(Long.valueOf(connectorId));
        PlainUser plainUser = userTransfer.userToPlainUser(user);
        ConnectorUser<String, Object> externalUser = AccountUtil.to(plainUser, externalConnectService.getExternalUserFullProfile(connector));
        SyncStats syncStats = connectorManageService.syncCreateOrUpdateUser(connector, Arrays.asList(externalUser));

        if (syncStats.status == SyncStatus.SUCCESS) {
            String externalUserId = (String) externalUser.getIdValue();
            ExternalUserEntity externalUserEntity = externalUserService.getByExternalId(connector.getId(), externalUserId);
            UserEntity userEntity = iUserService.getUser(externalUserEntity.getUserId());
            User result = userTransfer.entityToResource(userEntity);

            UserExtension userExtension = user.getUserExtension();
            result.setUserExtension(userExtension);

            return result;
        } else {
            logger.error("syncUser fail");
            throw new UserCenterException(TransactionErrorType.DS_SYNC_JOB_FAILED_ERROR);
        }
    }

    @Override
    public User getUser(String connectorId, String id) {
        try {
            UserEntity userEntity = iUserService.getUser(Long.valueOf(id));
            if (userEntity == null) {
                throw new UserCenterException(HttpStatus.NOT_FOUND, USER_NOT_FOUND);
            }

//            ExternalUserEntity externalUserEntity = externalUserService.getByLocalUserId(Long.valueOf(connectorId), Long.valueOf(id));
//            if (externalUserEntity == null) {
//                throw new UserCenterException(HttpStatus.NOT_FOUND, USER_NOT_FOUND);
//            }

            return getUser(userEntity);
        } catch (Exception e) {
            throw new UserCenterException(HttpStatus.NOT_FOUND, USER_NOT_FOUND);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public User replaceUser(String connectorId, String id, User user) {
        Boolean isSuccess = false;
        User result = null;
        try {
            verifyUser(id);
            result = syncUser(connectorId, user);
            isSuccess = true;
        } catch (Exception e) {
            logger.error("replaceUser error", e);
        }

        if (!isSuccess) {
            throw new UserCenterException(TransactionErrorType.DS_SYNC_JOB_FAILED_ERROR);
        }
        return result;
    }

    @Override
    public User patchUser(String connectorId, String id, PatchParameter patchParameter) {
        Boolean isSuccess = false;
        User result = null;
        try {
            UserEntity userEntity = verifyUser(id);
            User user = patchUser(userEntity, patchParameter);
            result = syncUser(connectorId, user);
            isSuccess = true;
        } catch (Exception e) {
            logger.error("patchUser error", e);
        }

        if (!isSuccess) {
            throw new UserCenterException(TransactionErrorType.DS_SYNC_JOB_FAILED_ERROR);
        }
        return result;
    }

    @Override
    public void deleteUser(String connectorId, String id) {
        verifyUser(id);
        ExternalUserEntity externalUserEntity = externalUserService.getByLocalUserId(Long.valueOf(connectorId), Long.valueOf(id));
        if (externalUserEntity == null) {
            throw new UserCenterException(HttpStatus.NOT_FOUND, USER_NOT_FOUND);
        }
        Connector connector = connectorService.getConnector(Long.valueOf(connectorId));
        connectorManageService.syncDeleteUser(connector, Arrays.asList(externalUserEntity.getExternalId()), true);
    }

    @Override
    public QueryResponse<User> filter(String connectorId, QueryParameter queryParameter) {
        return filter(queryParameter);
    }

    private UserEntity verifyUser(String id) {
        UserEntity userEntity = iUserService.getUser(Long.valueOf(id));
        if (userEntity == null) {
            throw new UserCenterException(HttpStatus.NOT_FOUND, USER_NOT_FOUND);
        }
        return userEntity;
    }

    private User patchUser(UserEntity userEntity, PatchParameter patchParameter) {
        User user = getUser(userEntity);

        String userJson = JsonUtil.obj2Str(user);
        boolean jsonUpdated = false;
        boolean hasUpdate = false;

        List<PatchOperation> operations = patchParameter.getOperations();
        for (PatchOperation operation : operations) {
            PatchOperation.PatchOp op = operation.getOp();
            String path = operation.getPath();
            Object value = operation.getValue();
            switch (op) {
                case remove:
                case Remove:
                    if (StringUtils.isBlank(path)) {
                        throw new ScimException(HttpStatus.BAD_REQUEST, "noTarget", "path is unspecified");
                    }
                    userJson = FilterExpressParser.deletedScimByPath(userJson, path, value);
                    jsonUpdated = true;
                    hasUpdate = true;
                    break;
                case add:
                case Add:
                    if (StringUtils.isBlank(path)) {
                        Map<String, Object> userPatchInfo = (Map) value;
                        if (jsonUpdated) {
                            user = JsonUtil.str2Obj(userJson, User.class);
                        }
                        BeanUtil.setBeanProperties(user, userPatchInfo, false);
                        userJson = JsonUtil.obj2Str(user);
                        jsonUpdated = true;
                    } else {
                        userJson = FilterExpressParser.addScimByPath(userJson, path, value);
                        jsonUpdated = true;
                    }
                    break;
                case replace:
                case Replace:
                    if (StringUtils.isBlank(path)) {
                        Map<String, Object> userPatchInfo = (Map) value;
                        BeanUtil.setBeanProperties(user, userPatchInfo, false);
                    } else {
                        userJson = FilterExpressParser.updateScimByPath(userJson, path, value);
                        jsonUpdated = true;
                    }
                    hasUpdate = true;
                    break;
            }
        }

        if (jsonUpdated) {
            user = JsonUtil.str2Obj(userJson, User.class);
        }
        if (hasUpdate) {
            user.getMeta().setLastModified(LocalDateTime.now());
        }
        return user;
    }
}
