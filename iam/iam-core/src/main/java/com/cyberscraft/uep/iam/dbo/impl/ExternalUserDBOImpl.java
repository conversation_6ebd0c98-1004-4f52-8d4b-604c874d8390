package com.cyberscraft.uep.iam.dbo.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cyberscraft.uep.account.client.domain.Connector;
import com.cyberscraft.uep.common.dto.LimitResult;
import com.cyberscraft.uep.common.dto.QueryPage;
import com.cyberscraft.uep.common.util.CollectionUtils;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.iam.common.enums.LocalUserAttr;
import com.cyberscraft.uep.iam.dao.ExternalUserDao;
import com.cyberscraft.uep.iam.dbo.ExternalUserDBO;
import com.cyberscraft.uep.iam.dto.response.ConnectorDeleteVo;
import com.cyberscraft.uep.iam.entity.ExternalUserEntity;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/11/3 11:20 上午
 */
@Service
public class ExternalUserDBOImpl extends ServiceImpl<ExternalUserDao, ExternalUserEntity> implements ExternalUserDBO {

    @Autowired
    private ExternalUserDao externalUserDao;

    @Override
    public ExternalUserEntity getByExternalId(Long connectorId, String externalId, String... attrs) {
        LambdaQueryWrapper<ExternalUserEntity> queryWrapper = new QueryWrapper<ExternalUserEntity>().select(attrs).lambda();
        queryWrapper.eq(ExternalUserEntity::getConnectorId, connectorId);
        queryWrapper.eq(ExternalUserEntity::getExternalId, externalId);

        return this.getOne(queryWrapper);
    }

    @Override
    public List<ExternalUserEntity> listByExternalIds(Long connectorId, List<String> externalIds, String... attrs) {
        LambdaQueryWrapper<ExternalUserEntity> queryWrapper = new QueryWrapper<ExternalUserEntity>().select(attrs).lambda();
        queryWrapper.eq(ExternalUserEntity::getConnectorId, connectorId);
        queryWrapper.in(ExternalUserEntity::getExternalId, externalIds);

        return this.list(queryWrapper);
    }

    @Override
    public List<ExternalUserEntity> listByUnionIds(Long connectorId, List<String> unionIds, String... attrs) {
        LambdaQueryWrapper<ExternalUserEntity> queryWrapper = new QueryWrapper<ExternalUserEntity>().select(attrs).lambda();
        if (connectorId != null) {
            queryWrapper.eq(ExternalUserEntity::getConnectorId, connectorId);
        }
        queryWrapper.in(ExternalUserEntity::getExternalUnionId, unionIds);

        return this.list(queryWrapper);
    }

    @Override
    public List<ExternalUserEntity> listByExternalId(String externalId, String... attrs) {
        LambdaQueryWrapper<ExternalUserEntity> queryWrapper = new QueryWrapper<ExternalUserEntity>().select(attrs).lambda();
        queryWrapper.eq(ExternalUserEntity::getExternalId, externalId);

        return this.list(queryWrapper);
    }

    @Override
    public List<ExternalUserEntity> getByConnectorId(Long connectorId, String... attrs) {
        LambdaQueryWrapper<ExternalUserEntity> queryWrapper = new QueryWrapper<ExternalUserEntity>().select(attrs).lambda();
        queryWrapper.eq(ExternalUserEntity::getConnectorId, connectorId);

        return this.list(queryWrapper);
    }

    @Override
    public List<ExternalUserEntity> getByLocalUserId(Long localUserId, List<Integer> syncDirections) {
        LambdaQueryWrapper<ExternalUserEntity> queryWrapper = new QueryWrapper<ExternalUserEntity>().lambda();
        queryWrapper.eq(ExternalUserEntity::getUserId, localUserId);
        queryWrapper.in(ExternalUserEntity::getSyncDirection, syncDirections);

        return this.list(queryWrapper);
    }

    @Override
    public List<ExternalUserEntity> getByLocalUserId(Long connectorId, List<Long> localUserIds, List<Integer> syncDirections) {
        LambdaQueryWrapper<ExternalUserEntity> queryWrapper = new QueryWrapper<ExternalUserEntity>().lambda();
        if (connectorId != null) {
            queryWrapper.eq(ExternalUserEntity::getConnectorId, connectorId);
        }
        if (CollectionUtils.isNotEmpty(localUserIds)) {
            queryWrapper.in(ExternalUserEntity::getUserId, localUserIds);
        }
        if (CollectionUtils.isNotEmpty(syncDirections)) {
            queryWrapper.in(ExternalUserEntity::getSyncDirection, syncDirections);
        }
        return this.list(queryWrapper);
    }

    @Override
    public ExternalUserEntity getByLocalUserId(Long connectorId, Long localUserId, String... attrs) {
        LambdaQueryWrapper<ExternalUserEntity> queryWrapper = new QueryWrapper<ExternalUserEntity>().select(attrs).lambda();
        queryWrapper.eq(ExternalUserEntity::getConnectorId, connectorId);
        queryWrapper.eq(ExternalUserEntity::getUserId, localUserId);

        return this.getOne(queryWrapper);
    }

    @Override
    public List<ExternalUserEntity> listBylocalUserIds(Long connectorId, List<Long> localUserIds, String... attrs) {
        if (CollectionUtils.isEmpty(localUserIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<ExternalUserEntity> queryWrapper = new QueryWrapper<ExternalUserEntity>().select(attrs).lambda();
        queryWrapper.eq(ExternalUserEntity::getConnectorId, connectorId);
        queryWrapper.in(ExternalUserEntity::getUserId, localUserIds);

        return this.list(queryWrapper);
    }

    @Override
    public List<ExternalUserEntity> getByLocalUserIds(Long connectorId, List<Long> localUserIds, String... attrs) {
        LambdaQueryWrapper<ExternalUserEntity> queryWrapper = new QueryWrapper<ExternalUserEntity>().select(attrs).lambda();
        queryWrapper.eq(ExternalUserEntity::getConnectorId, connectorId);
        queryWrapper.in(ExternalUserEntity::getUserId, localUserIds);
        return this.list(queryWrapper);
    }

    @Override
    public List<ExternalUserEntity> listByLocalUserId(Long localUserId, Integer type, List<Integer> directions, String... attrs) {
        LambdaQueryWrapper<ExternalUserEntity> queryWrapper = new QueryWrapper<ExternalUserEntity>().select(attrs).lambda();
        queryWrapper.eq(ExternalUserEntity::getType, type);
        queryWrapper.eq(ExternalUserEntity::getUserId, localUserId);
        queryWrapper.in(ExternalUserEntity::getSyncDirection, directions);
        List<ExternalUserEntity> list = this.list(queryWrapper);
        return list;
    }

    @Override
    public List<ExternalUserEntity> listByLocalUserId(List<Long> localUserIds, Integer type, List<Integer> directions, String... attrs) {
        LambdaQueryWrapper<ExternalUserEntity> queryWrapper = new QueryWrapper<ExternalUserEntity>().select(attrs).lambda();
        queryWrapper.eq(ExternalUserEntity::getType, type);
        queryWrapper.in(ExternalUserEntity::getUserId, localUserIds);
        queryWrapper.in(ExternalUserEntity::getSyncDirection, directions);
        List<ExternalUserEntity> list = this.list(queryWrapper);
        return list;
    }

    @Override
    public List<ExternalUserEntity> listByLocalUserId(List<Long> localUserIds, List<Integer> directions, String... attrs) {
        LambdaQueryWrapper<ExternalUserEntity> queryWrapper = new QueryWrapper<ExternalUserEntity>().select(attrs).lambda();
        queryWrapper.in(ExternalUserEntity::getUserId, localUserIds);
        queryWrapper.in(ExternalUserEntity::getSyncDirection, directions);
        List<ExternalUserEntity> list = this.list(queryWrapper);
        return list;
    }

    @Override
    public List<ExternalUserEntity> listBySnsUserId(String snsUserId, Integer type, List<Integer> directions, String... attrs) {
        LambdaQueryWrapper<ExternalUserEntity> queryWrapper = new QueryWrapper<ExternalUserEntity>().select(attrs).lambda();
        queryWrapper.eq(ExternalUserEntity::getType, type);
        queryWrapper.eq(ExternalUserEntity::getExternalId, snsUserId);
        queryWrapper.in(ExternalUserEntity::getSyncDirection, directions);
        List<ExternalUserEntity> list = this.list(queryWrapper);
        return list;
    }

    @Override
    public List<ExternalUserEntity> listBySnsUserId(List<String> snsUserIds, Integer type, List<Integer> directions, String... attrs) {
        LambdaQueryWrapper<ExternalUserEntity> queryWrapper = new QueryWrapper<ExternalUserEntity>().select(attrs).lambda();
        queryWrapper.eq(ExternalUserEntity::getType, type);
        queryWrapper.in(ExternalUserEntity::getExternalId, snsUserIds);
        queryWrapper.in(ExternalUserEntity::getSyncDirection, directions);
        List<ExternalUserEntity> list = this.list(queryWrapper);
        return list;
    }

    @Override
    public List<ExternalUserEntity> listBySnsUnionId(String snsUnionId, Integer type, List<Integer> directions, String... attrs) {
        LambdaQueryWrapper<ExternalUserEntity> queryWrapper = new QueryWrapper<ExternalUserEntity>().select(attrs).lambda();
        queryWrapper.eq(ExternalUserEntity::getType, type);
        queryWrapper.eq(ExternalUserEntity::getExternalUnionId, snsUnionId);
        queryWrapper.in(ExternalUserEntity::getSyncDirection, directions);
        List<ExternalUserEntity> list = this.list(queryWrapper);
        return list;
    }

    @Override
    public List<ExternalUserEntity> listByUnionId(String unionId) {
        QueryWrapper<ExternalUserEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ExternalUserEntity::getExternalUnionId, unionId);
        List<ExternalUserEntity> list = this.list(queryWrapper);
        return list;
    }

    @Override
    public List<ExternalUserEntity> findToBeDeleted(Long connectorId, Integer batchNo) {
        QueryWrapper<ExternalUserEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.select().lambda().eq(ExternalUserEntity::getConnectorId, connectorId).lt(ExternalUserEntity::getSyncBatchNo, batchNo);
        return list(queryWrapper);
    }

    @Override
    public LimitResult<ExternalUserEntity> findToBeDeleted(Long connectorId, Integer batchNo, int limit, long offset) {
        LambdaQueryWrapper<ExternalUserEntity> queryWrapper = new QueryWrapper<ExternalUserEntity>().lambda();
        queryWrapper.eq(ExternalUserEntity::getConnectorId, connectorId).lt(ExternalUserEntity::getSyncBatchNo, batchNo);
        queryWrapper.orderByDesc(ExternalUserEntity::getId);
        queryWrapper.last(String.format("limit %s,%s", offset, limit));

        List<ExternalUserEntity> items = this.list(queryWrapper);
        LimitResult<ExternalUserEntity> limitResult = new LimitResult<>();
        limitResult.setItems(items);
        limitResult.setLimit(limit);
        limitResult.setOffset(offset);
        limitResult.setMore(limit == items.size());
        return limitResult;
    }

    @Override
    public Page<ExternalUserEntity> findUpdatedUsers(Long connectorId, Integer batchNo, int pageSize, int page) {
        Page<ExternalUserEntity> entityPage = new Page<>(page, pageSize);

        LambdaQueryWrapper<ExternalUserEntity> queryWrapper = new QueryWrapper<ExternalUserEntity>().lambda();
        queryWrapper.eq(ExternalUserEntity::getConnectorId, connectorId).eq(ExternalUserEntity::getSyncBatchNo, batchNo);
        queryWrapper.orderByAsc(ExternalUserEntity::getId);

        Page<ExternalUserEntity> resultPage = baseMapper.selectPage(entityPage, queryWrapper);
        return resultPage;
    }

    @Override
    public Boolean hasConnectorUser(Long connectorId) {
        QueryWrapper<ExternalUserEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ExternalUserEntity::getConnectorId, connectorId);
        queryWrapper.last("limit 1");
        List<ExternalUserEntity> list = super.list(queryWrapper);
        return list.size() > 0;
    }

    @Override
    public void removeByConnector(Long connectorId) {
        LambdaQueryWrapper<ExternalUserEntity> queryWrapper = new QueryWrapper<ExternalUserEntity>().lambda();
        queryWrapper.eq(ExternalUserEntity::getConnectorId, connectorId);

        super.remove(queryWrapper);
    }

    @Override
    public QueryPage<ConnectorDeleteVo> getDeleteLimit(Connector connector, Integer page, Integer pageSize) {
        int validSyncBatchNo = connector.getSyncBatchNo();
        List<ConnectorDeleteVo> needDelete = externalUserDao.getNeedDelete(connector.getId(), validSyncBatchNo,
                connector.getDeleteMethod(),(page - 1) * pageSize, pageSize);
        needDelete.forEach(e -> {
            String username = e.getUsername();
            if (StringUtils.isBlank(username)) {
                String iamData = e.getSource();
                Map<String, Object> userMap = JsonUtil.str2Map(iamData);
                if (userMap != null && !userMap.isEmpty()) {
                    username = (String) userMap.get(LocalUserAttr.username.getDomainName());
                    String name = (String) userMap.get(LocalUserAttr.name.getDomainName());
                    e.setUsername(username);
                    e.setName(name);
                }
            }
        });
        Integer total = externalUserDao.getNeedDeleteSize(connector.getId(), validSyncBatchNo, connector.getDeleteMethod());

        QueryPage<ConnectorDeleteVo> queryPage = new QueryPage<>();
        int pages = total % pageSize == 0 ? total % pageSize : total / pageSize + 1;
        queryPage.setPage(page);
        queryPage.setPages(pages);
        queryPage.setItems(needDelete);
        queryPage.setTotal(total);
        return queryPage;
    }

    @Override
    public LimitResult<ExternalUserEntity> limitValidUserByBatchNo(Long connectorId, Integer batchNo, int limit, long offset) {
        LambdaQueryWrapper<ExternalUserEntity> queryWrapper = new QueryWrapper<ExternalUserEntity>().lambda();
        queryWrapper.eq(ExternalUserEntity::getConnectorId, connectorId).eq(ExternalUserEntity::getSyncBatchNo, batchNo);
        queryWrapper.orderByAsc(ExternalUserEntity::getId);
        queryWrapper.last(String.format("limit %s,%s", offset, limit));

        List<ExternalUserEntity> items = this.list(queryWrapper);
        LimitResult<ExternalUserEntity> limitResult = new LimitResult<>();
        limitResult.setItems(items);
        limitResult.setLimit(limit);
        limitResult.setOffset(offset);
        limitResult.setMore(limit == items.size());
        return limitResult;
    }

    @Override
    public Set<Long> selectUserIdsByConnectorId(Long connectorId) {
        return externalUserDao.selectUserIdsByConnectorId(connectorId);
    }
}
