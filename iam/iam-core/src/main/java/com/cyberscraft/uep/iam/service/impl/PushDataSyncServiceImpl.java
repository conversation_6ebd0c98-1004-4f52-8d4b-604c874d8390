package com.cyberscraft.uep.iam.service.impl;

import com.cyberscraft.uep.iam.dto.enums.ConnectorTypeEnum;
import com.cyberscraft.uep.iam.dto.enums.DataChangeType;
import com.cyberscraft.uep.iam.dto.enums.PushBusinessType;
import com.cyberscraft.uep.iam.entity.PushConnectorEntity;
import com.cyberscraft.uep.iam.entity.PushDataEntity;
import com.cyberscraft.uep.iam.service.IDataSyncService;
import com.cyberscraft.uep.iam.service.IPushDataMQService;
import com.cyberscraft.uep.iam.service.data.IPushConnectorService;
import com.cyberscraft.uep.iam.service.transfer.PushDataTransfer;
import com.cyberscraft.uep.iam.service.user.IUserService;
import com.cyberscraft.uep.mq.constant.MQConstant;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RedissonClient;
import org.redisson.client.protocol.ScoredEntry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/31 6:47 下午
 */
@Service
public class PushDataSyncServiceImpl implements IDataSyncService {

    private static final Logger logger = LoggerFactory.getLogger(PushDataSyncServiceImpl.class);

    @Resource
    protected RedissonClient redissonClient;
    @Autowired
    private IPushDataMQService pushDataMQService;
    @Autowired
    private IPushConnectorService pushConnectorService;
    @Autowired
    private PushDataTransfer pushDataTransfer;
    @Autowired
    private IUserService userService;

    @Value("${data.push.enable:true}")
    private boolean pushEnable;

    private String pushDataKey = "PUSH:CONNECTOR:DATA:%s";

    @Override
    public void pushDataToApp(Long businessDataId, PushBusinessType pushBusinessType, DataChangeType dataChangeType) {
        logger.debug("pushDataToApp {} {} {}", businessDataId, pushBusinessType.getName(), dataChangeType.getName());

        if (!pushEnable) {
            logger.info("push data,the config of data.push.enable is {},push will stop;", pushEnable);
            return;
        }

        Assert.notNull(businessDataId, "businessDataId is required");
        Assert.notNull(pushBusinessType, "pushBusinessType is required");
        Assert.notNull(dataChangeType, "dataChangeType is required");

        List<PushConnectorEntity> activedPushConnectors = pushConnectorService.getActivedPushConnectors();
        activedPushConnectors = activedPushConnectors.stream().filter(e -> e.getType() != ConnectorTypeEnum.SCIM.getValue()).collect(Collectors.toList());
        if (activedPushConnectors.isEmpty()) {
            logger.debug("push data,the active connector is empty, {}", businessDataId);
            return;
        }

        if (userService.isUserCountExceed() && DataChangeType.DELETE != dataChangeType) {
            logger.warn("用户总量已经超出license限制,不再生成通讯录更新事件");
            return;
        }

        RScoredSortedSet<String> scoredSortedSet;
        String key;
        String value = null;
        for (PushConnectorEntity activedPushConnector : activedPushConnectors) {
            key = String.format(pushDataKey, activedPushConnector.getId());
            scoredSortedSet = redissonClient.getScoredSortedSet(key);

            if (PushBusinessType.ORG == pushBusinessType) {
                value = "O" + businessDataId;
            } else if (PushBusinessType.USER == pushBusinessType) {
                value = "U" + businessDataId;
            } else if (PushBusinessType.ROLE == pushBusinessType) {
                value = "R" + businessDataId;
            }

            if (DataChangeType.FORCE == dataChangeType) {
                scoredSortedSet.add(System.currentTimeMillis() + 0.8, value);
            } else if (DataChangeType.DELETE == dataChangeType) {
                scoredSortedSet.add(System.currentTimeMillis() + 0.6, value);
            } else {
                scoredSortedSet.add(System.currentTimeMillis() + 0.2, value);
            }
        }

        //对于create和update类的消息，目前没什么用
        PushDataEntity pushDataEntity = buildPushData(businessDataId, pushBusinessType, dataChangeType);
        if (DataChangeType.DELETE == dataChangeType) {
            pushDataMQService.sendToMq(pushDataTransfer.entity2dto(pushDataEntity), MQConstant.QUEUE_PUSH_DATA);
        }
    }

    @Override
    public void pushDataToApp(Long connectorId, Long businessDataId, PushBusinessType pushBusinessType, DataChangeType dataChangeType) {
        if (!pushEnable) {
            logger.info("push data,the config of data.push.enable is {},push will stop;", pushEnable);
            return;
        }

        if (userService.isUserCountExceed() && DataChangeType.DELETE != dataChangeType) {
            logger.warn("用户总量已经超出license限制,不再生成通讯录更新事件");
            return;
        }

        RScoredSortedSet<String> scoredSortedSet;
        String key;
        String value;
        key = String.format(pushDataKey, connectorId);
        scoredSortedSet = redissonClient.getScoredSortedSet(key);

        if (PushBusinessType.ORG == pushBusinessType) {
            value = "O" + businessDataId;
        } else if (PushBusinessType.USER == pushBusinessType) {
            value = "U" + businessDataId;
        } else {
            value = "R" + businessDataId;
        }

        if (DataChangeType.FORCE == dataChangeType) {
            scoredSortedSet.add(System.currentTimeMillis() + 0.8, value);
        } else if (DataChangeType.DELETE == dataChangeType) {
            scoredSortedSet.add(System.currentTimeMillis() + 0.6, value);
        } else {
            scoredSortedSet.add(System.currentTimeMillis() + 0.2, value);
        }
    }

    @Override
    public void pushDataToApp(List<Long> businessDataIdList, PushBusinessType pushBusinessType, DataChangeType dataChangeType) {
        logger.debug("pushDataToApp {} {} {}", businessDataIdList, pushBusinessType.getName(), dataChangeType.getName());
        if (!pushEnable) {
            logger.info("push data,the config of data.push.enable is {},push will stop;", pushEnable);
            return;
        }

        Assert.notNull(businessDataIdList, "businessDataId is required");
        Assert.notNull(pushBusinessType, "pushBusinessType is required");
        Assert.notNull(dataChangeType, "dataChangeType is required");

        List<PushConnectorEntity> activedPushConnectors = pushConnectorService.getActivedPushConnectors();
        activedPushConnectors = activedPushConnectors.stream().filter(e -> e.getType() != ConnectorTypeEnum.SCIM.getValue()).collect(Collectors.toList());
        if (activedPushConnectors.isEmpty()) {
            logger.info("push data,the active connector is empty, {}", businessDataIdList);
            return;
        }

        if (userService.isUserCountExceed() && DataChangeType.DELETE != dataChangeType) {
            logger.warn("用户总量已经超出license限制,不再生成通讯录更新事件");
            return;
        }

        RScoredSortedSet<String> scoredSortedSet;
        String key;
        String value;
        for (PushConnectorEntity activedPushConnector : activedPushConnectors) {
            key = String.format(pushDataKey, activedPushConnector.getId());
            scoredSortedSet = redissonClient.getScoredSortedSet(key);

            if (PushBusinessType.ORG == pushBusinessType) {
                for (Long businessDataId : businessDataIdList) {
                    value = "O" + businessDataId;
                    if (DataChangeType.FORCE == dataChangeType) {
                        scoredSortedSet.add(System.currentTimeMillis() + 0.8, value);
                    } else if (DataChangeType.DELETE == dataChangeType) {
                        scoredSortedSet.add(System.currentTimeMillis() + 0.6, value);
                    } else {
                        scoredSortedSet.add(System.currentTimeMillis() + 0.2, value);
                    }
                }
            } else if (PushBusinessType.USER == pushBusinessType) {
                for (Long businessDataId : businessDataIdList) {
                    value = "U" + businessDataId;
                    if (DataChangeType.FORCE == dataChangeType) {
                        scoredSortedSet.add(System.currentTimeMillis() + 0.8, value);
                    } else if (DataChangeType.DELETE == dataChangeType) {
                        scoredSortedSet.add(System.currentTimeMillis() + 0.6, value);
                    } else {
                        scoredSortedSet.add(System.currentTimeMillis() + 0.2, value);
                    }
                }
            } else {
                for (Long businessDataId : businessDataIdList) {
                    value = "R" + businessDataId;
                    if (DataChangeType.FORCE == dataChangeType) {
                        scoredSortedSet.add(System.currentTimeMillis() + 0.8, value);
                    } else if (DataChangeType.DELETE == dataChangeType) {
                        scoredSortedSet.add(System.currentTimeMillis() + 0.6, value);
                    } else {
                        scoredSortedSet.add(System.currentTimeMillis() + 0.2, value);
                    }
                }
            }
        }

        List<PushDataEntity> pushDataEntities = new ArrayList<>();
        for (Long businessDataId : businessDataIdList) {
            PushDataEntity pushDataEntity = buildPushData(businessDataId, pushBusinessType, dataChangeType);
            pushDataEntities.add(pushDataEntity);
        }

        if (!pushDataEntities.isEmpty()) {
            //对于create和update类的消息，目前没什么用
            if (DataChangeType.DELETE == dataChangeType) {
                pushDataMQService.sendToMq(pushDataTransfer.entity2dto(pushDataEntities), MQConstant.QUEUE_PUSH_DATA);
            }
        }
    }

    private PushDataEntity buildPushData(Long businessDataId, PushBusinessType pushBusinessType, DataChangeType dataChangeType) {
        PushDataEntity pushDataEntity = new PushDataEntity();

        pushDataEntity.setBusinessDataId(businessDataId);
        pushDataEntity.setBusinessType(pushBusinessType.getValue());
        pushDataEntity.setCreateTime(LocalDateTime.now());
        pushDataEntity.setChangeType(dataChangeType.getValue());
        pushDataEntity.setTcode(TenantHolder.getTenantCode());
        return pushDataEntity;
    }

    @Override
    public void cleanAllPushData(Long connectorId) {
        String key = String.format(pushDataKey, connectorId);
        RScoredSortedSet<String> scoredSortedSet = redissonClient.getScoredSortedSet(key);
        scoredSortedSet.delete();
    }

    @Override
    public Collection<PushDataEntity> firstBatchPushData(Long connectorId, int batchNum) {
        String key = String.format(pushDataKey, connectorId);
        RScoredSortedSet<String> scoredSortedSet = redissonClient.getScoredSortedSet(key);
        Collection<ScoredEntry<String>> scoredEntries = scoredSortedSet.entryRange(0, batchNum - 1);
        List<PushDataEntity> result = new ArrayList<>(batchNum);
        for (ScoredEntry<String> scoredEntry : scoredEntries) {
            PushDataEntity pushDataEntity = new PushDataEntity();
            pushDataEntity.setConnectorId(connectorId);
            String value = scoredEntry.getValue();
            Long pushDataId;
            PushBusinessType pushBusinessType;
            if (value == null) {
                scoredSortedSet.remove(null);
                continue;
            }
            if (value.startsWith("O")) {
                pushBusinessType = PushBusinessType.ORG;
            } else if (value.startsWith("U")) {
                pushBusinessType = PushBusinessType.USER;
            } else {
                pushBusinessType = PushBusinessType.ROLE;
            }
            pushDataId = Long.valueOf(value.substring(1));
            pushDataEntity.setBusinessType(pushBusinessType.getValue());
            pushDataEntity.setBusinessDataId(pushDataId);
            pushDataEntity.setValue(value);
            pushDataEntity.setTcode(TenantHolder.getTenantCode());
            pushDataEntity.setTenantId(TenantHolder.getTenantCode());

            Double entryScore = scoredEntry.getScore();
            String decimalPart = getDecimalPart(entryScore);

            if (decimalPart.compareTo("0.7") > 0) {
                pushDataEntity.setChangeType(DataChangeType.FORCE.getValue());
            } else if (decimalPart.compareTo("0.5") > 0) {
                pushDataEntity.setChangeType(DataChangeType.DELETE.getValue());
            } else {
                pushDataEntity.setChangeType(DataChangeType.UPDATE.getValue());
            }
            result.add(pushDataEntity);
        }

        return result;
    }

    @Override
    public void deletePushData(Long connectorId, String dataId) {
        String key = String.format(pushDataKey, connectorId);
        RScoredSortedSet<String> scoredSortedSet = redissonClient.getScoredSortedSet(key);
        scoredSortedSet.remove(dataId);
    }

    private String getDecimalPart(Double score) {
        BigDecimal bigDecimal = new BigDecimal(score.toString());
        BigDecimal integerPart = new BigDecimal(bigDecimal.longValue());
        BigDecimal decimalPart = bigDecimal.subtract(integerPart);
        return decimalPart.toString();
    }
}
