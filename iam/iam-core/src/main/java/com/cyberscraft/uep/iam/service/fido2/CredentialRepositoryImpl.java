package com.cyberscraft.uep.iam.service.fido2;

import com.cyberscraft.uep.iam.common.exception.UserCenterException;
import com.cyberscraft.uep.iam.entity.UserPassKeyEntity;
import com.cyberscraft.uep.iam.errors.TransactionErrorType;
import com.cyberscraft.uep.iam.service.UserPassKeyService;
import com.yubico.webauthn.CredentialRepository;
import com.yubico.webauthn.RegisteredCredential;
import com.yubico.webauthn.data.ByteArray;
import com.yubico.webauthn.data.PublicKeyCredentialDescriptor;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class CredentialRepositoryImpl implements CredentialRepository {

    @Autowired
    private UserPassKeyService userPassKeyService;

    @Override
    public Set<PublicKeyCredentialDescriptor> getCredentialIdsForUsername(String username) {
        List<UserPassKeyEntity> userPassKey = userPassKeyService.getUserPassKeyByUsername(username);
        return userPassKey.stream()
                .map(
                        WebAuthUser ->
                                PublicKeyCredentialDescriptor.builder()
                                        .id(ByteArray.fromBase64(WebAuthUser.getCredentialId()))
                                        .build())
                .collect(Collectors.toSet());
    }

    @Override
    public Optional<ByteArray> getUserHandleForUsername(String username) {
        List<UserPassKeyEntity> userPassKey = userPassKeyService.getUserPassKeyByUsername(username);
        if (CollectionUtils.isEmpty(userPassKey)) {
            throw new UserCenterException(TransactionErrorType.USER_NOT_FOUND);
        }
        return Optional.of(ByteArray.fromBase64(userPassKey.get(0).getAlgoCode()));
    }

    @Override
    public Optional<String> getUsernameForUserHandle(ByteArray userHandle) {
        List<UserPassKeyEntity> userPassKey = userPassKeyService.selectByAlgorithm(userHandle.getBase64());
        if (CollectionUtils.isEmpty(userPassKey)) {
            throw new UserCenterException(TransactionErrorType.USER_NOT_FOUND);
        }
        return Optional.of(userPassKey.get(0).getUsername());
    }

    @Override
    public Optional<RegisteredCredential> lookup(ByteArray credentialId, ByteArray userHandle) {
        UserPassKeyEntity userPassKey = userPassKeyService.selectOneByCredentialIdAndAlgorithm(credentialId.getBase64(), userHandle.getBase64());
        Optional<UserPassKeyEntity> auth = Optional.of(userPassKey);
        return auth.map(
                credential ->
                        RegisteredCredential.builder()
                                .credentialId(ByteArray.fromBase64(credential.getCredentialId()))
                                .userHandle(ByteArray.fromBase64(credential.getAlgoCode()))
                                .publicKeyCose(ByteArray.fromBase64(credential.getPublicKey()))
//                                .signatureCount(credential.getCount())
                                .build());
    }

    @Override
    public Set<RegisteredCredential> lookupAll(ByteArray credentialId) {
        List<UserPassKeyEntity> auth = userPassKeyService.selectByCredentialId(new String(credentialId.getBytes()));
        return auth.stream()
                .map(
                        credential ->
                                RegisteredCredential.builder()
                                        .credentialId(ByteArray.fromBase64(credential.getCredentialId()))
                                        .userHandle(ByteArray.fromBase64(credential.getAlgoCode()))
                                        .publicKeyCose(ByteArray.fromBase64(credential.getPublicKey()))
//                                        .signatureCount(credential.getCount())
                                        .build())
                .collect(Collectors.toSet());
    }
}
