//package com.cyberscraft.uep.iam.common.domain.resource;
//
//import com.cyberscraft.uep.common.util.HttpUtil;
//
//import javax.servlet.http.HttpServletRequest;
//
//public class ServerContext {
//
//    private String scheme = "http";
//    private String server;
//    private String contextPath;
//    private int port;
//
//    private ServerContext(String scheme, String server, String contextPath, int port) {
//        this.scheme = scheme;
//        this.server = server;
//        this.contextPath = contextPath;
//        this.port = port;
//    }
//
//    public static ServerContext of(String scheme, String server, String contextPath, int port) {
//        return new ServerContext(scheme, server, contextPath, port);
//    }
//
//    public static ServerContext of(final HttpServletRequest request) {
//        return of(HttpUtil.getScheme(request),
//                HttpUtil.getServerName(request),
//                request.getContextPath(),
//                HttpUtil.getServerPort(request));
//    }
//
//    public String getServer() {
//        return server;
//    }
//
//    public String getURL() {
//        if (isHttp80(scheme, port)) {
//            return String.format("http://%s%s", server, contextPath);
//
//        } else if (isHttps443(scheme, port)) {
//            return String.format("https://%s%s", server, contextPath);
//        } else {
//             if (isContainsPort(server)) {
//                 return String.format("%s://%s%s", scheme, server, contextPath);
//            }
//             else {
//                 return String.format("%s://%s:%d%s", scheme, server, port, contextPath);
//             }
//        }
//    }
//
//    /***
//     * 服务地址是否带端口
//     * @param server
//     * @return
//     */
//    private boolean isContainsPort(String server){
//        if(server.indexOf(":")>0){
//            return true;
//        }
//        return false;
//    }
//
//    private static final String DOCUMENT_ROOT = "/docs/api/index.html";
//
//    public String getDocURL() {
//        return getURL() + DOCUMENT_ROOT;
//    }
//
//    /**
//     * @return login url
//     */
//    public String getLoginPage() {
//        return getURL();
//    }
//
//    /**
//     * is the service listening on http 80 ?
//     */
//    private static boolean isHttp80(String scheme, int port) {
//        return 80 == port && "http".equalsIgnoreCase(scheme);
//    }
//
//    /**
//     * is the service listening on https 443 ?
//     */
//    private static boolean isHttps443(String scheme, int port) {
//        return 443 == port && "https".equalsIgnoreCase(scheme);
//    }
//}