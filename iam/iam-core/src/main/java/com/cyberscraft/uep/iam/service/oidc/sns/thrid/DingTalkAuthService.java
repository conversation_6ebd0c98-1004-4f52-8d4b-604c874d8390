package com.cyberscraft.uep.iam.service.oidc.sns.thrid;

import com.aliyun.dingtalkcontact_1_0.models.GetUserHeaders;
import com.aliyun.dingtalkcontact_1_0.models.GetUserResponseBody;
import com.aliyun.dingtalkoauth2_1_0.models.GetUserTokenRequest;
import com.aliyun.dingtalkoauth2_1_0.models.GetUserTokenResponse;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.cyberscraft.uep.account.client.provider.dingding.domain.DingTalkConfig;
import com.cyberscraft.uep.account.client.provider.dingding.service.IDingTalkAccessTokenService;
import com.cyberscraft.uep.common.domain.ParamProfile;
import com.cyberscraft.uep.common.domain.ParamSchema;
import com.cyberscraft.uep.common.util.AliYunSDKUtil;
import com.cyberscraft.uep.common.util.MappingParser;
import com.cyberscraft.uep.iam.common.exception.UserCenterException;
import com.cyberscraft.uep.iam.common.util.StringUtil;
import com.cyberscraft.uep.iam.dbo.SysTenantDBO;
import com.cyberscraft.uep.iam.dto.domain.AppProfileDto;
import com.cyberscraft.uep.iam.dto.domain.OrgAndUserProfile;
import com.cyberscraft.uep.iam.dto.domain.ThirdIdpAuthConfig;
import com.cyberscraft.uep.iam.dto.enums.AppProfile;
import com.cyberscraft.uep.iam.dto.enums.ConnectorTypeEnum;
import com.cyberscraft.uep.iam.dto.request.configs.AccountLinkDto;
import com.cyberscraft.uep.iam.errors.TransactionErrorType;
import com.cyberscraft.uep.iam.service.IAppProfileService;
import com.cyberscraft.uep.iam.service.connector.dingtalk.DingTalkConstant;
import com.cyberscraft.uep.iam.service.oidc.exceptions.SnsException;
import com.cyberscraft.uep.iam.service.oidc.sns.IThirdPartyAuthService;
import com.cyberscraft.uep.iam.service.oidc.sns.common.dto.SnsUserDto;
import com.cyberscraft.uep.iam.service.oidc.util.OIDCConstants;
import com.cyberscraft.uep.iam.service.transfer.AppProfileTransfer;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiSnsGetuserinfoBycodeRequest;
import com.dingtalk.api.request.OapiUserGetbyunionidRequest;
import com.dingtalk.api.request.OapiV2UserGetRequest;
import com.dingtalk.api.request.OapiV2UserGetuserinfoRequest;
import com.dingtalk.api.response.OapiSnsGetuserinfoBycodeResponse;
import com.dingtalk.api.response.OapiUserGetbyunionidResponse;
import com.dingtalk.api.response.OapiV2UserGetResponse;
import com.dingtalk.api.response.OapiV2UserGetuserinfoResponse;
import com.taobao.api.ApiException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 外部认证源：钉钉
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2020-03-06 15:52
 */
@Component
public class DingTalkAuthService implements IThirdPartyAuthService {

    private static final Logger logger = LoggerFactory.getLogger(DingTalkAuthService.class);

    private static final ConnectorTypeEnum SNS_TYPE = ConnectorTypeEnum.DINGDING;


    @Autowired
    IDingTalkAccessTokenService accessTokenService;

    @Autowired
    private IAppProfileService appProfileService;

    @Autowired
    private AppProfileTransfer appProfileTransfer;

    @Autowired
    private SysTenantDBO sysTenantDBO;

    @Override
    public SnsUserDto getUserByClientCode(String code, ThirdIdpAuthConfig authConfig) {
        logger.info("使用第三方[{}]登录请求，code:{}", SNS_TYPE.getName(), code);
        DingTalkConfig dingTalkConfig = (DingTalkConfig) authConfig.getConfig();

        DefaultDingTalkClient dingTalkClient = new DefaultDingTalkClient(
                StringUtil.buildRequestUrl(dingTalkConfig.getApiBaseUrl(),
                        DingTalkConstant.API_AUTH_GETUSERINFO_BYCODE));

        OapiSnsGetuserinfoBycodeRequest req = new OapiSnsGetuserinfoBycodeRequest();
        req.setTmpAuthCode(code);
        SnsUserDto snsUserDto = null;
        try {
            OapiSnsGetuserinfoBycodeResponse response = dingTalkClient.execute(req, dingTalkConfig.getAppKey(),
                    dingTalkConfig.getAppSecret());

            if (!response.isSuccess()) {
                logger.error("通过code查询钉钉用户信息失败，错误码：{}，错误描述：{}", response.getErrcode(), response.getErrmsg());
                throw new SnsException(response.getErrorCode() + ":" + response.getErrmsg());
            }

            OapiSnsGetuserinfoBycodeResponse.UserInfo userInfo = response.getUserInfo();
            if (userInfo == null) {
                logger.error("查询钉钉用户详情，查询结果为空，code:{}", code);
                throw new SnsException("查询钉钉用户详情，查询结果为空");
            }

            logger.info("查询钉钉用户信息接口，返回值：getOpenid()={},getUnionid()={},getNick()={},getMainOrgAuthHighLevel()={}",
                    userInfo.getOpenid(), userInfo.getUnionid(), userInfo.getNick(), userInfo.getMainOrgAuthHighLevel());

            snsUserDto = new SnsUserDto();
            snsUserDto.setOpenId(userInfo.getUnionid());
            snsUserDto.setUserName(userInfo.getNick());

            logger.info("使用第三方[{}]登录，用户信息：{}", SNS_TYPE.getName(), snsUserDto.toString());
        } catch (ApiException e) {
            e.printStackTrace();
        }
        return snsUserDto;
    }

    @Override
    public Map<String, Object> getUserByCodeOnH5(String code, ThirdIdpAuthConfig authConfig) {
        logger.info("使用第三方[{}]登录请求，code:{}", SNS_TYPE.getName(), code);

        List<AccountLinkDto> accountLink = authConfig.getAccountLink();
        if (accountLink == null || accountLink.size() == 0) {
            throw new UserCenterException(TransactionErrorType.SNS_AUTH_FAILED_ERROR);
        }

        AppProfileDto appProfileDto = appProfileService.getFullAppProfile(Long.valueOf(authConfig.getId()), AppProfile.IDP);
        OrgAndUserProfile idpProfile = appProfileDto.getOrgAndUserProfile();
        ParamProfile userProfile = idpProfile.getUserProfile();

        ParamSchema dingParamSchema = appProfileTransfer.to(userProfile);
        String rootName = dingParamSchema.getName();
        List<String> dingDingKeys = MappingParser.printNameKeys(dingParamSchema, Boolean.TRUE);

        DingTalkConfig dingTalkConfig = (DingTalkConfig) authConfig.getConfig();

        String userDetailUrl = "https://oapi.dingtalk.com/topapi/v2/user/get";
        String accessToken = accessTokenService.getAccessToken(TenantHolder.getTenantCode(), dingTalkConfig);

        DefaultDingTalkClient client = new DefaultDingTalkClient(
                StringUtil.buildRequestUrl(dingTalkConfig.getApiBaseUrl(),
                        DingTalkConstant.API_H5_GET_USER_BY_CODE));


        OapiV2UserGetuserinfoRequest request = new OapiV2UserGetuserinfoRequest();
        request.setCode(code);
        OapiV2UserGetuserinfoResponse response;
        try {
            response = client.execute(request, accessToken);
            if (response.isSuccess()) {
                OapiV2UserGetuserinfoResponse.UserGetByCodeResponse userGetByCodeResponse = response.getResult();
                //替换别名
                Map<String, Object> userInfo = (Map<String, Object>)MappingParser.replaceAlias(dingParamSchema, userGetByCodeResponse, false);

//                List<String> userInfoNames = MappingParser.printAttrNames(userInfo, rootName, true);

//                Boolean isEnough = userInfoIsEnough(accountLink, dingDingKeys, userInfoNames);
//                if (isEnough) {
//                    logger.info("使用第三方[{}]登录，用户信息：{}", SNS_TYPE.getName(), userGetByCodeResponse);
//                    return userInfo;
//                }

                // 上一步匹配不通过使用通过userId查询出用户详情匹配
                String userId = userGetByCodeResponse.getUserid();
                DingTalkClient detailClient = new DefaultDingTalkClient(userDetailUrl);
                OapiV2UserGetRequest detailRequest = new OapiV2UserGetRequest();
                detailRequest.setUserid(userId);
                detailRequest.setLanguage("zh_CN");
                OapiV2UserGetResponse detailResponse = detailClient.execute(detailRequest, accessToken);
                OapiV2UserGetResponse.UserGetResponse userGetResponse = detailResponse.getResult();

                //替换别名
                Map<String, Object> userAllInfo = (Map<String, Object>)MappingParser.replaceAlias(dingParamSchema, userGetResponse, false);
                userInfo.putAll(userAllInfo);
                logger.info("使用第三方[{}]登录，用户信息：{}", SNS_TYPE.getName(), userInfo);
                return userInfo;
            } else {
                logger.error("{} {}", response.getErrcode(), response.getErrmsg());
                throw new UserCenterException(TransactionErrorType.SNS_AUTH_FAILED_ERROR);
            }
        } catch (ApiException e) {
            logger.error("{} {}", e.getErrCode(), e.getErrMsg());
            logger.error(e.getMessage(), e);
            throw new UserCenterException(e);
        }
    }



    @Override
    public Map<String, Object> getUserByOAuth(String code, ThirdIdpAuthConfig authConfig) {
        List<AccountLinkDto> accountLink = authConfig.getAccountLink();
        if (accountLink == null || accountLink.size() == 0) {
            throw new UserCenterException(TransactionErrorType.SNS_AUTH_FAILED_ERROR);
        }

        AppProfileDto appProfileDto = appProfileService.getFullAppProfile(Long.valueOf(authConfig.getId()), AppProfile.IDP);
        OrgAndUserProfile idpProfile = appProfileDto.getOrgAndUserProfile();
        ParamProfile userProfile = idpProfile.getUserProfile();
        ParamSchema dingParamSchema = appProfileTransfer.to(userProfile);
        String rootName = dingParamSchema.getName();
        List<String> dingDingKeys = MappingParser.printNameKeys(dingParamSchema, Boolean.TRUE);

        DingTalkConfig authConfigConfig = (DingTalkConfig) authConfig.getConfig();
        String userIdUrl = "https://oapi.dingtalk.com/topapi/user/getbyunionid";
        String userDetailUrl = "https://oapi.dingtalk.com/topapi/v2/user/get";
        try {
            Config config = AliYunSDKUtil.getAliYunSdkConfig();

            com.aliyun.dingtalkoauth2_1_0.Client authClient = new com.aliyun.dingtalkoauth2_1_0.Client(config);
            GetUserTokenRequest getUserTokenRequest = new GetUserTokenRequest()
                    .setClientId(authConfigConfig.getAppKey())
                    .setClientSecret(authConfigConfig.getAppSecret())
                    .setCode(code)
                    .setGrantType(OIDCConstants.GRANT_TYPE_CODE);
            GetUserTokenResponse userToken = authClient.getUserToken(getUserTokenRequest);
            String accessToken = userToken.getBody().getAccessToken();

            com.aliyun.dingtalkcontact_1_0.Client client = new com.aliyun.dingtalkcontact_1_0.Client(config);
            GetUserHeaders getUserHeaders = new GetUserHeaders();
            getUserHeaders.xAcsDingtalkAccessToken = accessToken;
            GetUserResponseBody me = client.getUserWithOptions("me", getUserHeaders, new RuntimeOptions()).getBody();

            //替换别名
            Map<String, Object> userInfo = (Map<String, Object>)MappingParser.replaceAlias(dingParamSchema, me, false);

            // 通过使用unionId查询userid匹配
            String unionId = me.getUnionId();
            DingTalkClient userIdClient = new DefaultDingTalkClient(userIdUrl);
            OapiUserGetbyunionidRequest useridRequest = new OapiUserGetbyunionidRequest();
            useridRequest.setUnionid(unionId);
            String token = accessTokenService.getAccessToken(TenantHolder.getTenantCode(), authConfigConfig);
            OapiUserGetbyunionidResponse userResponse = userIdClient.execute(useridRequest, token);
            OapiUserGetbyunionidResponse.UserGetByUnionIdResponse userGetByUnionIdResponse = userResponse.getResult();

            //替换别名
            Map<String, Object> userIdInfo = (Map<String, Object>)MappingParser.replaceAlias(dingParamSchema, userGetByUnionIdResponse, false);

            userInfo.putAll(userIdInfo);

            List<String> userInfoNames = MappingParser.printAttrNames(userInfo, rootName, true);

//            Boolean isEnough = userInfoIsEnough(accountLink, dingDingKeys, userInfoNames);
//            if (isEnough) {
//                return userInfo;
//            }

            // 上一步匹配不通过使用通过userId查询出用户详情匹配
            String userId = userGetByUnionIdResponse.getUserid();
            DingTalkClient detailClient = new DefaultDingTalkClient(userDetailUrl);
            OapiV2UserGetRequest detailRequest = new OapiV2UserGetRequest();
            detailRequest.setUserid(userId);
            detailRequest.setLanguage("zh_CN");
            OapiV2UserGetResponse detailResponse = detailClient.execute(detailRequest, token);
            OapiV2UserGetResponse.UserGetResponse userGetResponse = detailResponse.getResult();

            //替换别名
            Map<String, Object> userAllInfo = (Map<String, Object>)MappingParser.replaceAlias(dingParamSchema, userGetResponse, false);

            List<String> userAllNames = MappingParser.printAttrNames(userAllInfo, rootName, true);
            userInfoNames.addAll(userAllNames);

            Boolean isEnough = userInfoIsEnough(accountLink, dingDingKeys, userInfoNames);
            if (isEnough) {
                userInfo.putAll(userAllInfo);
                return userInfo;
            }

            // 上面没有匹配通过的表示表达式与用户属性匹配不通过
            throw new UserCenterException(TransactionErrorType.SNS_AUTH_FAILED_ERROR);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            throw new UserCenterException(e);
        }
    }

    @Override
    public Map<String, Object> getUserByUserId(String userId, ThirdIdpAuthConfig authConfig) {

        List<AccountLinkDto> accountLink = authConfig.getAccountLink();
        if (accountLink == null || accountLink.size() == 0) {
            throw new UserCenterException(TransactionErrorType.SNS_AUTH_FAILED_ERROR);
        }

        AppProfileDto appProfileDto = appProfileService.getFullAppProfile(Long.valueOf(authConfig.getId()), AppProfile.IDP);
        OrgAndUserProfile idpProfile = appProfileDto.getOrgAndUserProfile();
        ParamProfile userProfile = idpProfile.getUserProfile();
        ParamSchema dingParamSchema = appProfileTransfer.to(userProfile);
        String rootName = dingParamSchema.getName();
        List<String> dingDingKeys = MappingParser.printNameKeys(dingParamSchema, Boolean.TRUE);

        HashMap<String, Object> userIdMap = new HashMap<>();
        userIdMap.put("user_id", userId);

        //替换别名
        Map<String, Object> userInfo = (Map<String, Object>)MappingParser.replaceAlias(dingParamSchema, userIdMap, true);

        List<String> userInfoNames = MappingParser.printAttrNames(userInfo, rootName, true);

        Boolean isEnough = userInfoIsEnough(accountLink, dingDingKeys, userInfoNames);
        if (isEnough) {
            logger.info("使用第三方[{}]登录，用户信息：{}", SNS_TYPE.getName(), userIdMap);
            return userInfo;
        }

        DingTalkConfig dingTalkConfig = (DingTalkConfig) authConfig.getConfig();

        String accessToken = accessTokenService.getAccessToken(TenantHolder.getTenantCode(), dingTalkConfig);

        String userDetailUrl = "https://oapi.dingtalk.com/topapi/v2/user/get";
        DingTalkClient detailClient = new DefaultDingTalkClient(userDetailUrl);
        OapiV2UserGetRequest detailRequest = new OapiV2UserGetRequest();
        detailRequest.setUserid(userId);
        detailRequest.setLanguage("zh_CN");

        OapiV2UserGetResponse response;
        try {
            response = detailClient.execute(detailRequest, accessToken);
            if (response.isSuccess()) {
                OapiV2UserGetResponse.UserGetResponse userGetResponse = response.getResult();

                //替换别名
                userInfo = (Map<String, Object>)MappingParser.replaceAlias(dingParamSchema, userGetResponse, false);

                return userInfo;
            } else {
                logger.error("{} {}", response.getErrcode(), response.getErrmsg());
                throw new UserCenterException(TransactionErrorType.SNS_AUTH_FAILED_ERROR);
            }
        } catch (ApiException e) {
            logger.error("{} {}", e.getErrCode(), e.getErrMsg());
            logger.error(e.getMessage(), e);
            throw new UserCenterException(TransactionErrorType.SNS_AUTH_FAILED_ERROR);
        }
    }

    private Boolean userInfoIsEnough(List<AccountLinkDto> accountLinks, List<String> paramKeys, List<String> userKey) {
        for (String paramKey : paramKeys) {
            for (AccountLinkDto accountLink : accountLinks) {
                String sourceExp = accountLink.getSourceExp();
                if (sourceExp.contains(paramKey) && !userKey.contains(paramKey)) {
                    return false;
                }
            }
        }
        return true;
    }

    @Override
    public boolean supports(String type) {
        return SNS_TYPE.getName().equals(type);
    }
}
