package com.cyberscraft.uep.iam.handler.user;

import com.cyberscraft.uep.account.client.constant.AccountConstant;
import com.cyberscraft.uep.account.client.constant.ConnectorType;
import com.cyberscraft.uep.account.client.constant.SeeyonOrgAttr;
import com.cyberscraft.uep.account.client.constant.SeeyonUserAttr;
import com.cyberscraft.uep.account.client.domain.*;
import com.cyberscraft.uep.account.client.domain.account.SeeyonAccount;
import com.cyberscraft.uep.account.client.exception.*;
import com.cyberscraft.uep.account.client.provider.IExternalConnectHandler;
import com.cyberscraft.uep.account.client.service.IAuthenticateService;
import com.cyberscraft.uep.account.client.util.AccountUtil;
import com.cyberscraft.uep.common.bean.RestApiResponse;
import com.cyberscraft.uep.common.domain.DataTypeEnum;
import com.cyberscraft.uep.common.domain.ParamSchema;
import com.cyberscraft.uep.common.domain.auth.AuthorizeCredential;
import com.cyberscraft.uep.common.enums.SysCodeEnum;
import com.cyberscraft.uep.common.util.CollectionUtils;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.common.util.RestAPIUtil;
import com.cyberscraft.uep.common.util.WebUrlUtil;
import com.cyberscraft.uep.proxy.meta.service.IProxyMetaService;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/8/11 10:59 上午
 */
@Service
public class SeeyonExternalConnectHandler implements IExternalConnectHandler {

    private final Logger LOG = LoggerFactory.getLogger(SeeyonExternalConnectHandler.class);

    @Autowired
    private IAuthenticateService authenticateService;

    /**
     * 获取全部单位信息
     */
    private String getAllOrgApi = "/seeyon/rest/orgAccounts";

    /**
     * 根据单位ID获取下级单位
     */
    private String getChildOrgAccountApi = "/seeyon/rest/orgAccounts/{orgAccountId}";

    /**
     * 根据单位ID获取单位详情
     */
    private String getOrgAccountApi = "/seeyon/rest/orgAccount/{orgAccountId}";

    /***
     * 获取部门详情
     */
    private String getDepartmentApi = "/seeyon/rest/orgDepartment/{id}";

    /**
     * 获取子部门
     */
    private String getChildDeptApi = "/seeyon/rest/orgDepartments/children/{id}";

    /**
     * 获取单位下全部部门（不包含停用）
     */
    private String getAllDeptApi = "/seeyon/rest/orgDepartments/{orgAccountId}";

    /**
     * 获取部门下的人员，不包含停用人员，不包含子部门人员
     */
    private String getDeptMembersApi = "/seeyon/rest/orgMembers/department/{id}";

    /**
     * 按ID获取职务信息
     */
    private String getOrgLevelApi = "/seeyon/rest/orgLevel/{orgLevelId}";

    /**
     * 按ID获取岗位信息
     */
    private String getOrgPostApi = "/seeyon/rest/orgPost/{orgPostId}";

    /**
     * 按Id取人员信息
     */
    private String getMemberApi = "/seeyon/rest/orgMember/{id}";

    /**
     * 获取人员信息
     */
    private String getMemberByLoginNameApi = "/seeyon/rest/orgMember";

    /**
     * 创建或修改部门
     */
    private String createOrUpdateDeptApi = "/seeyon/rest/orgDepartment";

    /**
     * 删除部门
     */
    private String deleteDeptApi = "/seeyon/rest/orgDepartment/{id}";

    /**
     * 创建或修改用户
     */
    private String createOrUpdateUserApi = "/seeyon/rest/orgMember";

    /**
     * 删除用户
     */
    private String deleteUserApi = "/seeyon/rest/orgMember/{id}";

    private final ParamSchema orgBasicAttrSchema;
    private final ParamSchema userBasicAttrSchema;

    @Autowired
    private IProxyMetaService proxyMetaService;

    //单位缓存
    private Map<String, String> orgCache = new HashMap<>();
    //岗位缓存
    private Map<String, String> orgPostCache = new HashMap<>();
    //职务缓存
    private Map<String, String> orgLevelCache = new HashMap<>();

    public SeeyonExternalConnectHandler() {
        orgBasicAttrSchema = new ParamSchema(AccountConstant.APPORG_PROFILE_ROOTNAME, DataTypeEnum.OBJECT, "部门profile");
        for (SeeyonOrgAttr value : SeeyonOrgAttr.values()) {
            orgBasicAttrSchema.addSubParam(value.ps());
        }

        userBasicAttrSchema = new ParamSchema(AccountConstant.APPUSER_PROFILE_ROOTNAME, DataTypeEnum.OBJECT, "用户profile");
        for (SeeyonUserAttr value : SeeyonUserAttr.values()) {
            userBasicAttrSchema.addSubParam(value.ps());
        }
    }

    private void handleException(Exception e) {
        LOG.error("handleException", e);
        if (e instanceof ThirdPartyAccessTokenInvalidException) {
            throw (ThirdPartyAccessTokenInvalidException) e;
        } else if (e instanceof ThirdPartyGroupExistException) {
            throw (ThirdPartyGroupExistException) e;
        } else if (e instanceof ThirdPartyAccountExistException) {
            throw (ThirdPartyAccountExistException) e;
        } else if (e instanceof ThirdPartyAccountException) {
            throw (ThirdPartyAccountException) e;
        } else {
            throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), e.getMessage());
        }
    }

    private void checkResponse(RestApiResponse response, String api) {
        if (HttpStatus.UNAUTHORIZED.value() == response.getHttpStatus()) {
            throw new ThirdPartyAccessTokenInvalidException("401", "认证失败");
        }

        Object body = response.getBody();

        if (body instanceof Map) {
            Map<String, Object> map = (Map<String, Object>) body;
            String errorCode = (String) map.get("code");
            String success = (map.get("success")==null?null:map.get("success").toString());
            String message = (String) map.get("message");
            List<Map<String, Object>> errorMsgs = (List<Map<String, Object>>) map.get("errorMsgs");

            if (StringUtils.isNotBlank(success) && !success.equals("true")) {
                if ("1010".equals(errorCode)) {
                    throw new ThirdPartyAccessTokenInvalidException("1010", message);
                }
                if (CollectionUtils.isNotEmpty(errorMsgs)) {
                    String code = String.valueOf(errorMsgs.get(0).get("code"));
                    Map<String, Object> ent = (Map<String, Object>) errorMsgs.get(0).get("ent");
                    if ("DEPARTMENT_REPEAT_CODE".equals(code)) {
                        throw new ThirdPartyGroupExistException("DEPARTMENT_REPEAT_CODE", "部门code重复", ent.get("id").toString());
                    } else if ("PRINCIPAL_REPEAT_NAME".equals(code)) {
                        throw new ThirdPartyAccountExistException(ent.get("id").toString(), "登录名重复");
                    }
                }

                String errorMsg = (StringUtils.isNotBlank(message) ? message : String.valueOf(errorMsgs));
                LOG.error("{},调用错误:{}", api, errorMsg);
                throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), errorMsg);
            }
        } else if (HttpStatus.OK.value() != response.getHttpStatus()) {
            String errorMsg = String.format("%s %s", response.getHttpStatus(), response.getBody());
            throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), errorMsg);
        }
    }

    @Override
    public AuthorizeCredential getAuthCredential(Connector connector, Boolean refresh) {
        try {
            SeeyonAccount account = convertAccount(connector);
            AuthorizeCredential credential = authenticateService.auth(ConnectorType.SEEYON.name(), account, String.valueOf(connector.getId()), refresh);
            return credential;
        } catch (Exception e) {
            throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), e.getMessage());
        }
    }

    @Override
    public boolean isSupported(ConnectorType connectorType) {
        return ConnectorType.SEEYON == connectorType;
    }

    @Override
    public ParamSchema getExternalOrgBasicAttrSchema(Connector connector) {
        return orgBasicAttrSchema;
    }

    @Override
    public ParamSchema getExternalUserBasicAttrSchema(Connector connector) {
        return userBasicAttrSchema;
    }

    @Override
    public ConnectorOrgProfile getExternalOrgFullProfile(Connector connector) {
        ParamSchema orgAllProfile = buildExternalOrgFullSchema(connector);

        ConnectorOrgProfile connectorOrgProfile = new ConnectorOrgProfile();
        connectorOrgProfile.setIdName(SeeyonOrgAttr.id.getAttrName());
        connectorOrgProfile.setParentIdName(SeeyonOrgAttr.superior.getAttrName());
        connectorOrgProfile.setNameName(SeeyonOrgAttr.name.getAttrName());
        connectorOrgProfile.setAttrSchema(orgAllProfile);

        return connectorOrgProfile;
    }

    @Override
    public ConnectorUserProfile getExternalUserFullProfile(Connector connector) {
        ParamSchema userAllProfile = buildExternalUserFullSchema(connector);

        ConnectorUserProfile connectorUserProfile = new ConnectorUserProfile();
        connectorUserProfile.setIdName(SeeyonUserAttr.id.getAttrName());
        connectorUserProfile.setMobileName(SeeyonUserAttr.telNumber.getAttrName());
        connectorUserProfile.setAttrSchema(userAllProfile);

        return connectorUserProfile;
    }

    @Override
    public ConnectorOrg<String, Object> getRootExternalOrg(Connector connector) throws ThirdPartyAccountException {
        String rootOrgId = connector.getRootCode();
        if ("-1".equals(rootOrgId)) {
            Map<String, Object> rootMap = new HashMap<>();
            rootMap.put(SeeyonOrgAttr.id.getAttrName(), rootOrgId);
            rootMap.put(SeeyonOrgAttr.superior.getAttrName(), rootOrgId);
            rootMap.put(SeeyonOrgAttr.name.getAttrName(), connector.getName());
            return AccountUtil.to(rootMap, getExternalOrgFullProfile(connector));
        } else {
            ConnectorOrg rootOrg = getExternalOrgById(rootOrgId, connector);
            orgCache.put(rootOrg.getIdValue().toString(), rootOrg.getNameValue().toString());
            return rootOrg;
        }
    }

    @Override
    public List<ConnectorOrg<String, Object>> getAllSubExternalOrgs(ConnectorOrg orgAccount, Connector connector) throws ThirdPartyAccountException {
        if (connector == null) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.CONNECTOR_INVALID);
        }
        String orgAccountId = orgAccount.getIdValue().toString();

        SeeyonAccount account = convertAccount(connector);
        AuthorizeCredential authCredential = getAuthCredential(connector, false);

        List<Map<String, Object>> allExternalOrgs = new ArrayList<>();
        try {
            //全部单位
            List<Map<String, Object>> allOrgAccounts;
            List<String> parentIds = new ArrayList<>();
            if (orgAccountId.equals("-1")) {
                //获取集团下全部单位
                allOrgAccounts = getAllExternalAccount(account, authCredential);
            } else {
                //获取指定单位下的全部子单位（包括子单位的子单位）
                allOrgAccounts = getChildOrgAccountByParentId(account, authCredential, orgAccountId);
                parentIds.add(orgAccountId);
            }
            allExternalOrgs.addAll(allOrgAccounts);

            List<String> collect = allOrgAccounts.stream().map(e -> e.get(SeeyonOrgAttr.id.getAttrName()).toString()).collect(Collectors.toList());
            parentIds.addAll(collect);

            for (String parentId : parentIds) {
                allExternalOrgs.addAll(getAllExternalDeptByAccountId(account, authCredential, parentId));
            }

        } catch (ThirdPartyAccessTokenInvalidException e) {
            authCredential = getAuthCredential(connector, true);
            //全部单位
            List<Map<String, Object>> allOrgAccounts;
            List<String> parentIds = new ArrayList<>();
            if (orgAccountId.equals("-1")) {
                allOrgAccounts = getAllExternalAccount(account, authCredential);
            } else {
                allOrgAccounts = getChildOrgAccountByParentId(account, authCredential, orgAccountId);
                parentIds.add(orgAccountId);
            }
            allExternalOrgs.addAll(allOrgAccounts);

            List<String> collect = allOrgAccounts.stream().map(e1 -> e1.get(SeeyonOrgAttr.id.getAttrName()).toString()).collect(Collectors.toList());
            parentIds.addAll(collect);

            for (String parentId : parentIds) {
                allExternalOrgs.addAll(getAllExternalDeptByAccountId(account, authCredential, parentId));
            }
        } catch (Exception e) {
            throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), e.getMessage());
        }

        return AccountUtil.to(allExternalOrgs, getExternalOrgFullProfile(connector));
    }

    @Override
    public List<ConnectorOrg<String, Object>> getSubExternalOrgs(String parentOrgId, Connector connector) throws ThirdPartyAccountException {
        if (connector == null) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.CONNECTOR_INVALID);
        }

        SeeyonAccount account = convertAccount(connector);
        AuthorizeCredential authCredential = getAuthCredential(connector, false);

        List<Map<String, Object>> allExternalOrgs;
        try {
            allExternalOrgs = getSubExternalOrgs(account, authCredential, parentOrgId, false);
        } catch (ThirdPartyAccessTokenInvalidException e) {
            authCredential = getAuthCredential(connector, true);
            allExternalOrgs = getSubExternalOrgs(account, authCredential, parentOrgId, false);
        } catch (Exception e) {
            throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), e.getMessage());
        }

        return AccountUtil.to(allExternalOrgs, getExternalOrgFullProfile(connector));
    }

    @Override
    public List<ConnectorOrg<String, Object>> getExternalOrgByIds(List<String> externalOrgIds, Connector connector) throws ThirdPartyAccountException {
        return null;
    }

    @Override
    public ConnectorOrg getExternalOrgById(String externalOrgId, Connector connector) throws ThirdPartyAccountException {
        if (externalOrgId == null) {
            return null;
        }
        if (connector == null) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.CONNECTOR_INVALID);
        }


        SeeyonAccount account = convertAccount(connector);
        AuthorizeCredential authCredential = getAuthCredential(connector, false);

        Map<String, Object> response;
        try {
            response = getExternalOrgById(account, authCredential, externalOrgId);
        } catch (ThirdPartyAccessTokenInvalidException e) {
            authCredential = getAuthCredential(connector, true);
            response = getExternalOrgById(account, authCredential, externalOrgId);
        } catch (Exception e) {
            throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), e.getMessage());
        }

        return AccountUtil.to(response, getExternalOrgFullProfile(connector));
    }

    /**
     * 根据ID获取部门或单位详情
     *
     * @param account
     * @param credential
     * @param externalOrgId
     * @return
     */
    private Map<String, Object> getExternalOrgById(SeeyonAccount account, AuthorizeCredential credential, String externalOrgId) {
        try {
            String apiPath = WebUrlUtil.getWebServerUrl(account.getBaseUrl(), getDepartmentApi);
            Map<String, Object> pathParam = new HashMap<>();
            pathParam.put(SeeyonOrgAttr.id.getAttrName(), externalOrgId);

            RestApiResponse response = RestAPIUtil.getForEntityMapVariables(account.getProxyIp(), apiPath, credential.getHead(), new HashMap<>(), pathParam);

            checkResponse(response, getDepartmentApi);

            return (Map<String, Object>) response.getBody();
        } catch (Exception e) {
            handleException(e);
        }
        return null;
    }

    /**
     * 根据部门ID获取子部门信息
     *
     * @param account
     * @param credential
     * @param parentOrgId
     * @param isFetchChild
     * @return
     */
    private List<Map<String, Object>> getSubExternalOrgs(SeeyonAccount account, AuthorizeCredential credential, String parentOrgId, boolean isFetchChild) {
        try {
            String apiPath = WebUrlUtil.getWebServerUrl(account.getBaseUrl(), getChildDeptApi);
            Map<String, Object> pathParam = new HashMap<>();
            pathParam.put(SeeyonOrgAttr.id.getAttrName(), parentOrgId);

            Map<String, Object> queryParam = new HashMap<>();
            if (isFetchChild) {
                queryParam.put("firstLayer", false);
            } else {
                queryParam.put("firstLayer", true);
            }
            RestApiResponse response = RestAPIUtil.getForEntityMapVariables(account.getProxyIp(), apiPath, credential.getHead(), queryParam, pathParam);

            checkResponse(response, getChildDeptApi);

            return (List<Map<String, Object>>) response.getBody();
        } catch (Exception e) {
            handleException(e);
        }
        return null;
    }

    private List<Map<String, Object>> getAllExternalDeptByAccountId(SeeyonAccount account, AuthorizeCredential credential, String orgAccountId) {
        try {
            String apiPath = WebUrlUtil.getWebServerUrl(account.getBaseUrl(), getAllDeptApi);
            Map<String, Object> pathParam = new HashMap<>();
            pathParam.put(SeeyonOrgAttr.orgAccountId.getAttrName(), orgAccountId);

            Map<String, Object> queryParam = new HashMap<>();

            RestApiResponse response = RestAPIUtil.getForEntityMapVariables(account.getProxyIp(), apiPath, credential.getHead(), queryParam, pathParam);

            checkResponse(response, getAllDeptApi);

            return (List<Map<String, Object>>) response.getBody();
        } catch (Exception e) {
            handleException(e);
        }
        return null;
    }

    private List<Map<String, Object>> getChildOrgAccountByParentId(SeeyonAccount account, AuthorizeCredential credential, String parentAccountId) {
        List<Map<String, Object>> externalAllOrgAccounts = new ArrayList<>();

        List<String> parentAccountFindList = new ArrayList<>();
        parentAccountFindList.add(parentAccountId);

        while (parentAccountFindList.size() > 0) {
            List<Map<String, Object>> accounts = new ArrayList<>();
            for (String parentId : parentAccountFindList) {
                try {
                    String apiPath = WebUrlUtil.getWebServerUrl(account.getBaseUrl(), getChildOrgAccountApi);
                    Map<String, Object> pathParam = new HashMap<>();
                    pathParam.put(SeeyonOrgAttr.orgAccountId.getAttrName(), parentId);

                    Map<String, Object> queryParam = new HashMap<>();

                    RestApiResponse response = RestAPIUtil.getForEntityMapVariables(account.getProxyIp(), apiPath, credential.getHead(), queryParam, pathParam);

                    checkResponse(response, parentId);

                    List<Map<String, Object>> subAccounts = (List<Map<String, Object>>) response.getBody();
                    List<Map<String, Object>> subFilterAccounts = subAccounts.stream().filter(e -> !String.valueOf(e.get(SeeyonOrgAttr.orgAccountId.getAttrName())).equals(parentId)).collect(Collectors.toList());
                    accounts.addAll(subFilterAccounts);
                    externalAllOrgAccounts.addAll(subFilterAccounts);
                } catch (Exception e) {
                    handleException(e);
                }
            }
            parentAccountFindList.clear();
            for (Map<String, Object> subAccount : accounts) {
                parentAccountFindList.add(subAccount.get(SeeyonOrgAttr.id.getAttrName()).toString());
            }
        }
        return externalAllOrgAccounts;
    }

    private List<Map<String, Object>> getAllExternalAccount(SeeyonAccount account, AuthorizeCredential credential) {
        try {
            String apiPath = WebUrlUtil.getWebServerUrl(account.getBaseUrl(), getAllOrgApi);
            Map<String, Object> pathParam = new HashMap<>();

            Map<String, Object> queryParam = new HashMap<>();

            RestApiResponse response = RestAPIUtil.getForEntityMapVariables(account.getProxyIp(), apiPath, credential.getHead(), queryParam, pathParam);

            checkResponse(response, getAllOrgApi);

            List<Map<String, Object>> body = (List<Map<String, Object>>) response.getBody();
            body.forEach(e -> orgCache.put(SeeyonOrgAttr.id.getAttrName(), e.get(SeeyonOrgAttr.id.getAttrName()).toString()));
            return body;
        } catch (Exception e) {
            handleException(e);
        }
        return null;
    }

    @Override
    public boolean isSortedByOrgLevel() {
        return false;
    }

    @Override
    public List<ConnectorUser<String, Object>> getExternalUsersByOrg(ConnectorOrg externalOrg, Connector connector) throws ThirdPartyAccountException {
        if (connector == null) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.CONNECTOR_INVALID);
        }
        if (externalOrg == null) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.GROUP_CODE_INVALID);
        }

        String externalOrgId = externalOrg.getIdValue().toString();
        SeeyonAccount account = convertAccount(connector);
        AuthorizeCredential authCredential = getAuthCredential(connector, false);

        List<Map<String, Object>> result;
        try {
            result = getExternalUsersByOrg(account, authCredential, externalOrgId);
        } catch (ThirdPartyAccessTokenInvalidException e) {
            authCredential = getAuthCredential(connector, true);
            result = getExternalUsersByOrg(account, authCredential, externalOrgId);
        } catch (Exception e) {
            throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), e.getMessage());
        }

        return AccountUtil.to(result, getExternalUserFullProfile(connector));
    }

    @Override
    public List<ConnectorUser<String, Object>> getExternalUsers(List<String> externalUserIds, Connector connector) throws ThirdPartyAccountException {
        List<ConnectorUser<String, Object>> result = new ArrayList<>();
        for (String externalUserId : externalUserIds) {
            ConnectorUser externalUser = getExternalUserById(externalUserId, connector);
            result.add(externalUser);
        }

        return result;
    }

    @Override
    public Boolean limitListExternalUsers(int page, int pageSize, Connector connector, List<ConnectorUser<String, Object>> output) throws ThirdPartyAccountException {
        return null;
    }

    @Override
    public ConnectorUser getExternalUserById(String externalAccountId, Connector connector) throws ThirdPartyAccountException {
        if (externalAccountId == null) {
            return null;
        }
        if (connector == null) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.CONNECTOR_INVALID);
        }

        SeeyonAccount account = convertAccount(connector);
        AuthorizeCredential authCredential = getAuthCredential(connector, false);

        Map<String, Object> result;
        try {
            result = getExternalUserById(account, authCredential, externalAccountId);
        } catch (ThirdPartyAccessTokenInvalidException e) {
            authCredential = getAuthCredential(connector, true);
            result = getExternalUserById(account, authCredential, externalAccountId);
        } catch (Exception e) {
            throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), e.getMessage());
        }

        return AccountUtil.to(result, getExternalUserFullProfile(connector));
    }

    private List<Map<String, Object>> getExternalUsersByOrg(SeeyonAccount account, AuthorizeCredential credential, String externalOrgId) {
        try {
            String apiPath = WebUrlUtil.getWebServerUrl(account.getBaseUrl(), getDeptMembersApi);
            Map<String, Object> pathParam = new HashMap<>();
            pathParam.put(SeeyonOrgAttr.id.getAttrName(), externalOrgId);

            Map<String, Object> queryParam = new HashMap<>();

            RestApiResponse response = RestAPIUtil.getForEntityMapVariables(account.getProxyIp(), apiPath, credential.getHead(), queryParam, pathParam);

            checkResponse(response, getDeptMembersApi);

            List<Map<String, Object>> body = (List<Map<String, Object>>) response.getBody();
            body.forEach(e -> {
                String orgAccountId = e.get(SeeyonUserAttr.orgAccountId.getAttrName()).toString();
                String orgPostId = e.get(SeeyonUserAttr.orgPostId.getAttrName()).toString();
                String orgLevelId = e.get(SeeyonUserAttr.orgLevelId.getAttrName()).toString();
                e.put(SeeyonUserAttr.orgPostName.getAttrName(), getExternalOrgPostById(account, credential, orgPostId));
                e.put(SeeyonUserAttr.orgLevelName.getAttrName(), getExternalOrgLevelById(account, credential, orgLevelId));
                e.put(SeeyonUserAttr.orgAccountName.getAttrName(), getExternalOrgAccountNameById(account, credential, orgAccountId));
            });
            return body;
        } catch (Exception e) {
            handleException(e);
        }
        return null;
    }

    private Map<String, Object> getExternalUserById(SeeyonAccount account, AuthorizeCredential credential, String externalUserId) {
        try {
            String apiPath = WebUrlUtil.getWebServerUrl(account.getBaseUrl(), getMemberApi);
            Map<String, Object> pathParam = new HashMap<>();
            pathParam.put(SeeyonUserAttr.id.getAttrName(), externalUserId);

            Map<String, Object> queryParam = new HashMap<>();

            RestApiResponse response = RestAPIUtil.getForEntityMapVariables(account.getProxyIp(), apiPath, credential.getHead(), queryParam, pathParam);

            checkResponse(response, getMemberApi);

            Map<String, Object> body = (Map<String, Object>) response.getBody();
            String orgAccountId = body.get(SeeyonUserAttr.orgAccountId.getAttrName()).toString();
            String orgPostId = body.get(SeeyonUserAttr.orgPostId.getAttrName()).toString();
            String orgLevelId = body.get(SeeyonUserAttr.orgLevelId.getAttrName()).toString();
            body.put(SeeyonUserAttr.orgPostName.getAttrName(), getExternalOrgPostById(account, credential, orgPostId));
            body.put(SeeyonUserAttr.orgLevelName.getAttrName(), getExternalOrgLevelById(account, credential, orgLevelId));
            body.put(SeeyonUserAttr.orgAccountName.getAttrName(), getExternalOrgAccountNameById(account, credential, orgAccountId));

            return body;
        } catch (Exception e) {
            handleException(e);
        }
        return null;
    }

    private String getExternalOrgPostById(SeeyonAccount account, AuthorizeCredential credential, String externalPostId) {
        String postName = orgPostCache.get(externalPostId);
        if (StringUtils.isNotBlank(postName)) {
            return postName;
        }
        try {
            String apiPath = WebUrlUtil.getWebServerUrl(account.getBaseUrl(), getOrgPostApi);
            Map<String, Object> pathParam = new HashMap<>();
            pathParam.put(SeeyonUserAttr.orgPostId.getAttrName(), externalPostId);

            Map<String, Object> queryParam = new HashMap<>();

            RestApiResponse response = RestAPIUtil.getForEntityMapVariables(account.getProxyIp(), apiPath, credential.getHead(), queryParam, pathParam);

            checkResponse(response, getOrgPostApi);

            Map<String, Object> body = (Map<String, Object>) response.getBody();
            if (body != null) {
                postName = (String) body.get("name");
            } else {
                LOG.error("orgPost error:{}", externalPostId);
            }
            orgPostCache.put(externalPostId, postName);
        } catch (Exception e) {
            handleException(e);
        }
        return postName;
    }

    private String getExternalOrgLevelById(SeeyonAccount account, AuthorizeCredential credential, String externalLevelId) {
        String levelName = orgLevelCache.get(externalLevelId);
        if (StringUtils.isNotBlank(levelName)) {
            return levelName;
        }
        try {
            String apiPath = WebUrlUtil.getWebServerUrl(account.getBaseUrl(), getOrgLevelApi);
            Map<String, Object> pathParam = new HashMap<>();
            pathParam.put(SeeyonUserAttr.orgLevelId.getAttrName(), externalLevelId);

            Map<String, Object> queryParam = new HashMap<>();

            RestApiResponse response = RestAPIUtil.getForEntityMapVariables(account.getProxyIp(), apiPath, credential.getHead(), queryParam, pathParam);

            checkResponse(response, getOrgLevelApi);

            Map<String, Object> body = (Map<String, Object>) response.getBody();
            if (body != null) {
                levelName = (String) body.get("name");
            } else {
                LOG.error("orgLevel error:{}", externalLevelId);
            }
            orgLevelCache.put(externalLevelId, levelName);
        } catch (Exception e) {
            handleException(e);
        }
        return levelName;
    }

    private String getExternalOrgAccountNameById(SeeyonAccount account, AuthorizeCredential credential, String orgAccountId) {
        String orgAccountName = orgCache.get(orgAccountId);
        if (StringUtils.isNotBlank(orgAccountName)) {
            return orgAccountName;
        }
        try {
            String apiPath = WebUrlUtil.getWebServerUrl(account.getBaseUrl(), getOrgAccountApi);
            Map<String, Object> pathParam = new HashMap<>();
            pathParam.put(SeeyonUserAttr.orgAccountId.getAttrName(), orgAccountId);

            Map<String, Object> queryParam = new HashMap<>();

            RestApiResponse response = RestAPIUtil.getForEntityMapVariables(account.getProxyIp(), apiPath, credential.getHead(), queryParam, pathParam);

            checkResponse(response, getOrgAccountApi);

            Map<String, Object> body = (Map<String, Object>) response.getBody();

            orgAccountName = (String) body.get("name");
            orgLevelCache.put(orgAccountId, orgAccountName);
        } catch (Exception e) {
            handleException(e);
        }
        return orgAccountName;
    }

    private Map<String, Object> getExternalUserByLoginName(SeeyonAccount account, AuthorizeCredential credential, String externalLoginName) {
        try {
            String apiPath = WebUrlUtil.getWebServerUrl(account.getBaseUrl(), getMemberByLoginNameApi);
            Map<String, Object> pathParam = new HashMap<>();

            Map<String, Object> queryParam = new HashMap<>();
            queryParam.put(SeeyonUserAttr.loginName.getAttrName(), externalLoginName);

            RestApiResponse response = RestAPIUtil.getForEntityMapVariables(account.getProxyIp(), apiPath, credential.getHead(), queryParam, pathParam);

            checkResponse(response, getMemberByLoginNameApi);

            Map<String, Object> body = (Map<String, Object>) response.getBody();
            String orgAccountId = body.get(SeeyonUserAttr.orgAccountId.getAttrName()).toString();
            String orgPostId = body.get(SeeyonUserAttr.orgPostId.getAttrName()).toString();
            String orgLevelId = body.get(SeeyonUserAttr.orgLevelId.getAttrName()).toString();
            body.put(SeeyonUserAttr.orgPostName.getAttrName(), getExternalOrgPostById(account, credential, orgPostId));
            body.put(SeeyonUserAttr.orgLevelName.getAttrName(), getExternalOrgLevelById(account, credential, orgLevelId));
            body.put(SeeyonUserAttr.orgAccountName.getAttrName(), getExternalOrgAccountNameById(account, credential, orgAccountId));

            return body;
        } catch (Exception e) {
            handleException(e);
        }
        return null;
    }

    @Override
    public int login(Connector connector, String username, String password) {
        return 0;
    }

    @Override
    public ConnectorUser test(Connector connector) {
        if (connector == null) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.CONNECTOR_INVALID);
        }
        String testAccount = connector.getTestAccount();
        if (testAccount == null) {
            return null;
        }

        SeeyonAccount account = convertAccount(connector);
        AuthorizeCredential authCredential = getAuthCredential(connector, true);

        Map<String, Object> result;
        try {
            result = getExternalUserByLoginName(account, authCredential, testAccount);
        } catch (ThirdPartyAccessTokenInvalidException e) {
            authCredential = getAuthCredential(connector, true);
            result = getExternalUserByLoginName(account, authCredential, testAccount);
        } catch (Exception e) {
            throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), e.getMessage());
        }

        return AccountUtil.to(result, getExternalUserFullProfile(connector));
    }

    @Override
    public void closeConnection(Connector connector) {
        orgPostCache.clear();
        orgLevelCache.clear();
        orgCache.clear();
    }

    private SeeyonAccount convertAccount(Connector connector) {
        SeeyonAccount account = JsonUtil.str2Obj(connector.getConfig(), SeeyonAccount.class);
        String virtualIp = proxyMetaService.getVirtualIp(TenantHolder.getTenantCode(), String.valueOf(connector.getProxyId()), account.getRealIp());
        account.setProxyIp(virtualIp);
        return account;
    }


    @Override
    public Boolean fullyModify(Connector connector) {
        return true;
    }

    @Override
    public String createExternalOrg(ConnectorOrg externalOrg, Connector connector) throws ThirdPartyAccountException {
        String parentCode = externalOrg.getParentIdValue() != null ? externalOrg.getParentIdValue().toString() : null;
        if (StringUtils.isBlank(parentCode)) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.PARENT_GROUP_CODE_INVALID);
        }

        SeeyonAccount account = convertAccount(connector);
        AuthorizeCredential authCredential = getAuthCredential(connector, false);

        try {
            return createExternalOrg(account, authCredential, externalOrg);
        } catch (ThirdPartyAccessTokenInvalidException e) {
            authCredential = getAuthCredential(connector, true);
            return createExternalOrg(account, authCredential, externalOrg);
        } catch (ThirdPartyGroupExistException e) {
            throw e;
        } catch (Exception e) {
            throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), e.getMessage());
        }
    }

    @Override
    public String modifyExternalOrg(String externalOrgId, ConnectorOrg externalOrg, Connector connector) throws ThirdPartyAccountException {
        externalOrg.setIdValue(externalOrgId);
        if (StringUtils.isBlank(externalOrgId)) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.GROUP_CODE_INVALID);
        }

        SeeyonAccount account = convertAccount(connector);
        AuthorizeCredential authCredential = getAuthCredential(connector, false);

        try {
            return modifyExternalOrg(account, authCredential, externalOrg);
        } catch (ThirdPartyAccessTokenInvalidException e) {
            authCredential = getAuthCredential(connector, true);
            return modifyExternalOrg(account, authCredential, externalOrg);
        } catch (ThirdPartyGroupNotExistException e) {
            throw e;
        } catch (Exception e) {
            throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), e.getMessage());
        }
    }

    @Override
    public void deleteExternalOrg(String externalOrgId, Connector connector) throws ThirdPartyAccountException {
        if (StringUtils.isBlank(externalOrgId)) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.GROUP_CODE_INVALID);
        }

        SeeyonAccount account = convertAccount(connector);
        AuthorizeCredential authCredential = getAuthCredential(connector, false);

        try {
            deleteExternalOrg(account, authCredential, externalOrgId);
        } catch (ThirdPartyAccessTokenInvalidException e) {
            authCredential = getAuthCredential(connector, true);
            deleteExternalOrg(account, authCredential, externalOrgId);
        } catch (Exception e) {
            throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), e.getMessage());
        }
    }

    @Override
    public void deleteExternalOrgs(List<String> externalOrgIds, Connector connector) throws ThirdPartyAccountException {
        for (String externalOrgId : externalOrgIds) {
            deleteExternalOrg(externalOrgId, connector);
        }
    }

    @Override
    public String createExternalUser(ConnectorUser externalUser, Connector connector) throws ThirdPartyAccountException {
        SeeyonAccount account = convertAccount(connector);
        AuthorizeCredential authCredential = getAuthCredential(connector, false);

        try {
            return createExternalUser(account, authCredential, externalUser);
        } catch (ThirdPartyAccessTokenInvalidException e) {
            authCredential = getAuthCredential(connector, true);
            return createExternalUser(account, authCredential, externalUser);
        } catch (ThirdPartyAccountExistException e) {
            throw e;
        } catch (Exception e) {
            throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), e.getMessage());
        }
    }

    @Override
    public String modifyExternalUser(String externalUserId, ConnectorUser externalUser, Connector connector) throws ThirdPartyAccountException {
        if (StringUtils.isBlank(externalUserId)) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.ACCOUNT_ID_INVALID);
        }
        externalUser.setIdValue(externalUserId);

        SeeyonAccount account = convertAccount(connector);
        AuthorizeCredential authCredential = getAuthCredential(connector, false);

        try {
            return modifyExternalUser(account, authCredential, externalUser);
        } catch (ThirdPartyAccessTokenInvalidException e) {
            authCredential = getAuthCredential(connector, true);
            return modifyExternalUser(account, authCredential, externalUser);
        } catch (ThirdPartyAccountNotExistException e) {
            throw e;
        } catch (Exception e) {
            throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), e.getMessage());
        }
    }

    @Override
    public void deleteExternalUser(String externalUserId, Connector connector) throws ThirdPartyAccountException {
        if (StringUtils.isBlank(externalUserId)) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.ACCOUNT_ID_INVALID);
        }

        SeeyonAccount account = convertAccount(connector);
        AuthorizeCredential authCredential = getAuthCredential(connector, false);

        try {
            deleteExternalUser(account, authCredential, externalUserId);
        } catch (ThirdPartyAccessTokenInvalidException e) {
            authCredential = getAuthCredential(connector, true);
            deleteExternalUser(account, authCredential, externalUserId);
        } catch (Exception e) {
            throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), e.getMessage());
        }
    }

    @Override
    public void deleteExternalUsers(List<String> externalUserIds, Connector connector) throws ThirdPartyAccountException {
        for (String externalUserId : externalUserIds) {
            deleteExternalUser(externalUserId, connector);
        }
    }

    private String createExternalOrg(SeeyonAccount account, AuthorizeCredential credential, ConnectorOrg externalOrg) {
        try {
            String apiPath = WebUrlUtil.getWebServerUrl(account.getBaseUrl(), createOrUpdateDeptApi);
            Map<String, Object> pathParam = new HashMap<>();
            Map<String, Object> queryParam = new HashMap<>();

            RestApiResponse response = RestAPIUtil.modifyForEntity(account.getProxyIp(), apiPath, "POST", externalOrg, credential.getHead(), queryParam, pathParam);

            checkResponse(response, createOrUpdateDeptApi);

            Map<String, Object> body = (Map<String, Object>) response.getBody();
            if (body == null) {
                throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), "应答体为空");
            }
            List<Map<String, Object>> successMsgs = (List<Map<String, Object>>) body.get("successMsgs");
            Map<String, Object> ent = (Map<String, Object>) successMsgs.get(0).get("ent");
            return ent.get(SeeyonOrgAttr.id.getAttrName()).toString();
        } catch (Exception e) {
            handleException(e);
        }
        return null;
    }

    private String modifyExternalOrg(SeeyonAccount account, AuthorizeCredential credential, ConnectorOrg externalOrg) {
        try {
            String apiPath = WebUrlUtil.getWebServerUrl(account.getBaseUrl(), createOrUpdateDeptApi);
            Map<String, Object> pathParam = new HashMap<>();
            Map<String, Object> queryParam = new HashMap<>();

            RestApiResponse response = RestAPIUtil.modifyForEntity(account.getProxyIp(), apiPath, "PUT", externalOrg, credential.getHead(), queryParam, pathParam);

            checkResponse(response, createOrUpdateDeptApi);

            Map<String, Object> body = (Map<String, Object>) response.getBody();
            if (body == null) {
                throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), "应答体为空");
            }
            List<Map<String, Object>> successMsgs = (List<Map<String, Object>>) body.get("successMsgs");
            Map<String, Object> ent = (Map<String, Object>) successMsgs.get(0).get("ent");
            return ent.get(SeeyonOrgAttr.id.getAttrName()).toString();
        } catch (Exception e) {
            handleException(e);
        }
        return null;
    }

    private void deleteExternalOrg(SeeyonAccount account, AuthorizeCredential credential, String deptId) {
        if (StringUtils.isBlank(deptId)) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.GROUP_CODE_INVALID);
        }

        try {
            String apiPath = WebUrlUtil.getWebServerUrl(account.getBaseUrl(), deleteDeptApi);
            Map<String, Object> pathParam = new HashMap<>();
            pathParam.put(SeeyonOrgAttr.id.getAttrName(), deptId);
            Map<String, Object> queryParam = new HashMap<>();
            HashMap<String, Object> bodyParam = new HashMap<>();

            RestApiResponse response = RestAPIUtil.modifyForEntity(account.getProxyIp(), apiPath, "DELETE", bodyParam, credential.getHead(), queryParam, pathParam);

            checkResponse(response, deleteDeptApi);
        } catch (Exception e) {
            handleException(e);
        }
    }

    private String createExternalUser(SeeyonAccount account, AuthorizeCredential credential, ConnectorUser externalUser) {
        try {
            String apiPath = WebUrlUtil.getWebServerUrl(account.getBaseUrl(), createOrUpdateUserApi);
            Map<String, Object> pathParam = new HashMap<>();
            Map<String, Object> queryParam = new HashMap<>();

            RestApiResponse response = RestAPIUtil.modifyForEntity(account.getProxyIp(), apiPath, "POST", externalUser, credential.getHead(), queryParam, pathParam);

            checkResponse(response, createOrUpdateUserApi);

            Map<String, Object> body = (Map<String, Object>) response.getBody();
            if (body == null) {
                throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), "应答体为空");
            }
            List<Map<String, Object>> successMsgs = (List<Map<String, Object>>) body.get("successMsgs");
            Map<String, Object> ent = (Map<String, Object>) successMsgs.get(0).get("ent");
            return ent.get(SeeyonUserAttr.id.getAttrName()).toString();
        } catch (Exception e) {
            handleException(e);
        }
        return null;
    }

    private String modifyExternalUser(SeeyonAccount account, AuthorizeCredential credential, ConnectorUser externalUser) {
        try {
            String apiPath = WebUrlUtil.getWebServerUrl(account.getBaseUrl(), createOrUpdateUserApi);
            Map<String, Object> pathParam = new HashMap<>();
            Map<String, Object> queryParam = new HashMap<>();

            RestApiResponse response = RestAPIUtil.modifyForEntity(account.getProxyIp(), apiPath, "PUT", externalUser, credential.getHead(), queryParam, pathParam);

            checkResponse(response, createOrUpdateUserApi);

            Map<String, Object> body = (Map<String, Object>) response.getBody();
            if (body == null) {
                throw new ThirdPartyAccountException(SysCodeEnum.THIRDPARTYACCOUNT.getCode(), "应答体为空");
            }
            List<Map<String, Object>> successMsgs = (List<Map<String, Object>>) body.get("successMsgs");
            Map<String, Object> ent = (Map<String, Object>) successMsgs.get(0).get("ent");
            return ent.get(SeeyonUserAttr.id.getAttrName()).toString();
        } catch (Exception e) {
            handleException(e);
        }
        return null;
    }

    private void deleteExternalUser(SeeyonAccount account, AuthorizeCredential credential, String userId) {
        if (StringUtils.isBlank(userId)) {
            throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.ACCOUNT_ID_EMPTY);
        }

        try {
            String apiPath = WebUrlUtil.getWebServerUrl(account.getBaseUrl(), deleteUserApi);
            Map<String, Object> pathParam = new HashMap<>();
            pathParam.put(SeeyonUserAttr.id.getAttrName(), userId);
            Map<String, Object> queryParam = new HashMap<>();
            HashMap<String, Object> bodyParam = new HashMap<>();

            RestApiResponse response = RestAPIUtil.modifyForEntity(account.getProxyIp(), apiPath, "DELETE", bodyParam, credential.getHead(), queryParam, pathParam);

            checkResponse(response, deleteUserApi);
        } catch (Exception e) {
            handleException(e);
        }
    }
}
