package com.cyberscraft.uep.iam.service.user;

import com.cyberscraft.uep.iam.dto.request.CreateUserExcelDto;
import com.cyberscraft.uep.iam.entity.ImportUserHistoryEntity;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/5/6 11:58 上午
 */
public interface IImportUserHistoryService {

    ImportUserHistoryEntity getById(Long importTaskId);

    boolean updateById(ImportUserHistoryEntity importUserHistoryEntity);

    /**
     * 创建一个用户导入任务（历史）
     * @param createUserExcelDto
     * @return
     */
    ImportUserHistoryEntity createImportHistory(CreateUserExcelDto createUserExcelDto);

    /**
     * 运行用户导入任务
     * @param importTask
     */
    void runImportUserTask(ImportUserHistoryEntity importTask);

    /**
     * 查询用户导入历史
     * @param startDate
     * @param endDate
     * @return
     */
    List<ImportUserHistoryEntity> selectImportUserHistory(LocalDate startDate, LocalDate endDate);
}
