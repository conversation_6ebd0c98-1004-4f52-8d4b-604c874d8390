<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cyberscraft.uep.iam.dao.AppRoleUtoDao">

    <select id="getListWithTag" resultType="com.cyberscraft.uep.iam.entity.AppRoleUtoEntity">
        select
            t.name,ru.*
        from
            iam_app_role_uto ru,iam_tag t
        where
            ru.target = t.id and ru.target_type = 3 and ru.app_ref_id = #{appRefId} and ru.role_ref_id = #{roleRefId}
    </select>
    <select id="getListWithOrg" resultType="com.cyberscraft.uep.iam.entity.AppRoleUtoEntity">
        select
            t.name,ru.*
        from
            iam_app_role_uto ru,iam_org t
        where
            ru.target = t.id and ru.target_type = 1 and ru.app_ref_id = #{appRefId} and ru.role_ref_id = #{roleRefId}
    </select>
    <select id="getListWithUser" resultType="java.util.Map">
        select
            u.*,ru.binding_scopes
        from
            iam_app_role_uto ru,iam_user u
        where
            ru.target = u.id and ru.target_type = 2 and ru.app_ref_id = #{appRefId} and ru.role_ref_id = #{roleRefId}
            <if test="filterStr != null and filterStr != '' and searchField != null and searchField != ''">
                <choose>
                    <!-- 当 searchField 为 u.name 或 u.username 时 -->
                    <when test="searchField == 'name' or searchField == 'username' or searchField == 'telephone_number'">
                        AND u.${searchField} LIKE CONCAT('%', #{filterStr,jdbcType=VARCHAR}, '%')
                    </when>
                    <!-- 其他情况 -->
                    <otherwise>
                        AND u.${searchField} = #{filterStr,jdbcType=VARCHAR}
                    </otherwise>
                </choose>
            </if>
    </select>
    <select id="getListWithUserExceptAdmin" resultType="java.util.Map">
        select
        u.*,ru.binding_scopes
        from
        iam_app_role_uto ru,iam_user u
        where
        ru.target = u.id and u.username != 'admin' and ru.target_type = 2 and ru.app_ref_id = #{appRefId} and ru.role_ref_id = #{roleRefId}
        <if test="filterStr != null and filterStr != '' and searchField != null and searchField != ''">
            <choose>
                <!-- 当 searchField 为 u.name 或 u.username 时 -->
                <when test="searchField == 'name' or searchField == 'username' or searchField == 'telephone_number'">
                    AND u.${searchField} LIKE CONCAT('%', #{filterStr,jdbcType=VARCHAR}, '%')
                </when>
                <!-- 其他情况 -->
                <otherwise>
                    AND u.${searchField} = #{filterStr,jdbcType=VARCHAR}
                </otherwise>
            </choose>
        </if>
    </select>
    <select id="getListWithTargetAndApp" resultType="com.cyberscraft.uep.iam.entity.AppRoleUtoEntity">
        SELECT
            id, role_ref_id, app_ref_id, target_type, target
        FROM iam_app_role_uto
        WHERE app_ref_id = #{appRefId} and target in
            <foreach collection="targets" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
    </select>
</mapper>
