<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cyberscraft.uep.iam.dao.TagGroupDao">

    <select id="getList" resultType="java.util.Map">
        select
            itg.*
        from
            iam_tag_group itg
        where
            1=1
        <if test="filterStr != null and filterStr != '' ">
            and(
               itg.app_name like concat(#{filterStr,jdbcType=VARCHAR},'%')
            or itg.app_code like concat(#{filterStr,jdbcType=VARCHAR},'%')
            or itg.id like concat(#{filterStr,jdbcType=VARCHAR},'%')
            )
        </if>
        ORDER BY itg.update_time DESC
    </select>
</mapper>
