<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cyberscraft.uep.iam.dao.PushDataDao">

    <select id="getUnPushList" resultType="com.cyberscraft.uep.iam.entity.PushDataEntity">
        select * from iam_push_data p where not exists(select 1 from iam_push_app a where p.id = a.push_data_ref_id)
    </select>
</mapper>
