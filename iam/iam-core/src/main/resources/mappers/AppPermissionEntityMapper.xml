<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cyberscraft.uep.iam.dao.AppPermissionDao">

    <select id="getList" resultType="com.cyberscraft.uep.iam.entity.AppPermissionEntity">
        select  p.*
        from iam_app_permission p,iam_app_permission_sets_permission s
        where p.id = s.permission_ref_id
        and p.app_ref_id = #{appRefId}
        and s.permission_sets_ref_id = #{permissionSetsRefId}
    </select>

    <select id="getPermissionAndSets" resultType="com.cyberscraft.uep.iam.entity.AppPermissionEntity">
        select  p.*,s.permission_sets_ref_id
        from iam_app_permission p,iam_app_permission_sets_permission s
        where p.id = s.permission_ref_id
        and p.app_ref_id = #{appRefId}
    </select>
</mapper>
