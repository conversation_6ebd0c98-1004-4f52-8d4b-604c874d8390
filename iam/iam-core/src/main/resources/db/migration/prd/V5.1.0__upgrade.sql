ALTER TABLE iam.iam_user_status
    CHANGE COLUMN login active tinyint NULL DEFAULT NULL COMMENT '用户登录状态 该状态用于 返回用户状态 激活 还是未激活' AFTER tenant_id;
-- 新增配置
ALTER TABLE `iam`.`iam_connector`
    DROP COLUMN `email_verified`,
    DROP COLUMN `phone_number_verified`,
    ADD COLUMN `trust_enable` tinyint(1) NULL DEFAULT 0 COMMENT '是否开启身份属性验证 0:关闭, 1:开启' AFTER `flow`,
    ADD COLUMN `trust_config` varchar(255) NULL COMMENT '待验证身份属性配置' AFTER `trust_enable`;

-- 用户表新增身份证、身份证校验、工号校验、姓名校验, 将手机号、邮箱、身份证号设为唯一
ALTER TABLE `iam`.`iam_user`
    ADD COLUMN `id_card` varchar(20) NULL DEFAULT NULL COMMENT '用户的身份证号码' AFTER `manager`,
    ADD COLUMN `id_card_verified` tinyint NULL DEFAULT 1 COMMENT '用户的身份证号码是否已经验证过,1:是，0：否' AFTER `id_card`,
    ADD COLUMN `name_verified` tinyint NULL DEFAULT 1 COMMENT '用户的姓名是否已经验证过,1:是，0：否' AFTER `id_card_verified`,
    ADD COLUMN `user_job_number_verified` tinyint NULL DEFAULT 1 COMMENT '用户的工号是否已经验证过,1:是，0：否' AFTER `name_verified`,
    ADD UNIQUE INDEX `id_card_index`(`id_card`, `tenant_id`) USING BTREE;

ALTER TABLE `iam`.`iam_deleted_user`
    ADD COLUMN `id_card` varchar(20) NULL DEFAULT NULL COMMENT '用户的身份证号码' AFTER `manager`,
    ADD COLUMN `id_card_verified` tinyint NULL DEFAULT 1 COMMENT '用户的身份证号码是否已经验证过,1:是，0：否' AFTER `id_card`,
    ADD COLUMN `name_verified` tinyint NULL DEFAULT 1 COMMENT '用户的姓名是否已经验证过,1:是，0：否' AFTER `id_card_verified`,
    ADD COLUMN `user_job_number_verified` tinyint NULL DEFAULT 1 COMMENT '用户的工号是否已经验证过,1:是，0：否' AFTER `name_verified`,
    ADD INDEX `delete_id_card_index`(`id_card`, `tenant_id`) USING BTREE;


-- 线上已有租户新增属性
INSERT IGNORE INTO iam_field_dict (id, field_name, domain_name, display_name, description, login_factor, create_mod, data_type, field_type, mandatory, import_able, searchable, unique_able, update_able, show_portal, as_profile, validate_rule, as_claim, single_value, op_constraint, create_time, create_by, update_time, update_by, sort_num, tenant_id)
SELECT 1039, 'id_card', 'id_card', '身份证号', '用户的身份证号', 1, 1, 4, 1, 0, 1, 1, 1, 0, 1, 1, '{"reg_expr":"^[1-9]\\\\d{5}(18|19|20)\\\\d{2}((0[1-9])|(1[0-2]))((0[1-9])|(1[0-9])|(2[0-8]))\\\\d{3}[0-9Xx]$","err_msg":"id card is invalid"}', 1, 1, 1, sysdate(), 'system', null, null, 6,fd.tenant_id
FROM iam_field_dict fd
group by fd.tenant_id;

INSERT IGNORE INTO iam_field_dict (id, field_name, domain_name, display_name, description, login_factor, create_mod, data_type, field_type, mandatory, import_able, searchable, unique_able, update_able, show_portal, as_profile, validate_rule, as_claim, single_value, op_constraint, create_time, create_by, update_time, update_by, tenant_id)
SELECT 1040, 'id_card_verified', 'id_card_verified', 'id card verified', 'whether user''s id card verified', 0, 1, 3, 1, 0, 0, 0, 0, 0, 0, 0, null, 0, 1, 2, sysdate(), 'system', null, null,fd.tenant_id
FROM iam_field_dict fd
group by fd.tenant_id;

INSERT IGNORE INTO iam_field_dict (id, field_name, domain_name, display_name, description, login_factor, create_mod, data_type, field_type, mandatory, import_able, searchable, unique_able, update_able, show_portal, as_profile, validate_rule, as_claim, single_value, op_constraint, create_time, create_by, update_time, update_by, tenant_id)
SELECT 1041, 'name_verified', 'name_verified', 'name verified', 'whether user''s name verified', 0, 1, 3, 1, 0, 0, 0, 0, 0, 0, 0, null, 0, 1, 2, sysdate(), 'system', null, null,fd.tenant_id
FROM iam_field_dict fd
group by fd.tenant_id;

INSERT IGNORE INTO iam_field_dict (id, field_name, domain_name, display_name, description, login_factor, create_mod, data_type, field_type, mandatory, import_able, searchable, unique_able, update_able, show_portal, as_profile, validate_rule, as_claim, single_value, op_constraint, create_time, create_by, update_time, update_by, tenant_id)
SELECT 1042, 'user_job_number_verified', 'user_job_number_verified', 'user job number verified', 'whether user''s user job number verified', 0, 1, 3, 1, 0, 0, 0, 0, 0, 0, 0, null, 0, 1, 2, sysdate(), 'system', null, null,fd.tenant_id
FROM iam_field_dict fd
group by fd.tenant_id;

-- 人工校验认证过需记录
DROP TABLE IF EXISTS `iam_user_trust`;
CREATE TABLE `iam_user_trust` (
      `id` bigint(20) NOT NULL,
      `uid` bigint(20) NOT NULL COMMENT '用户id',
      `attr` varchar(32) NOT NULL COMMENT '认证过的属性',
      `source` varchar(32) DEFAULT NULL COMMENT '创建来源，参考 CreatedMode',
      `verified_time` datetime DEFAULT NULL COMMENT '验证时间',
      `tenant_id` varchar(64) NOT NULL COMMENT '租户ID',
      PRIMARY KEY (`id`),
      KEY `trust_uid_index` (`uid`,`tenant_id`) USING BTREE COMMENT '用户id'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


INSERT INTO `iam`.`iam_app` (`id`, `client_id`, `client_name`, `application_type`, `description`, `logo_uri`, `client_uri`, `policy_uri`, `tos_uri`, `contacts`, `redirect_uris`, `access_token_timeout`, `refresh_token_timeout`, `id_token_timeout`, `whitelisted`, `webhook_enable`, `webhook`, `enforce_https`, `trusted_peers`, `validate_factors`, `auth_with_cert_enable`, `cli_mode_enable`, `one_time_pwd_enable`, `secure_login_enable`, `status`, `feature_app`, `open_app_auth_id`, `client_secret`, `client_secret_expires_at`, `public_key`, `public_access`, `certPem`, `cert_thumbprint`, `public_key_pem`, `qrcode_enable`, `trusted_scanners`, `client_name_en`, `uid`, `user_type`, `app_os`, `client_id_issued_at`, `update_time`, `create_by`, `update_by`, `private_key`, `grant_type`, `scope`, `tenant_owner`, `public_to_tenant`, `signing_alg`, `signature_secret`, `auth_protocol`, `config`, `enable_apply`, `link_client_id`, `preview_uri`, `inner_category`, `support_protocol`, `template_config`, `help_url`, `gateway`) VALUES (2380, 'PANDA', '熊猫优福', NULL, '熊猫优福，专业企业福利服务平台！为企业客户提供多元化福利产品，包括电影在线选座、通兑电子票服务、员工生日、年节礼包等', NULL, 'https://pandayoufu.com/', NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, NULL, 0, NULL, NULL, NULL, 0, 0, 0, 1, 1, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, 0, NULL, NULL, '0', NULL, NULL, NULL, '2025-02-11 11:06:32', NULL, NULL, NULL, NULL, NULL, 'iam', 1, NULL, NULL, 'APPSTORE', NULL, 1, NULL, NULL, 'marketing', 'APPSTORE', '{"template_config":[{"label":"单点登录URL","name":"sso_url","rules":[{"required":true,"message":""}],"placeholder":"请输入单点登录URL","controlType":"input","options":[]},{"label":"加解密密钥","name":"secret","rules":[{"required":true,"message":""}],"placeholder":"请输入加解密密钥","controlType":"input","options":[]},{"label":"用户标识","name":"user_code_mapping_attr","rules":[{"required":true,"message":""}],"placeholder":"","controlType":"select","options":[{"label":"用户ID","value":"sub"},{"label":"用户名","value":"username"},{"label":"工号","value":"user_job_number"},{"label":"手机号","value":"phone_number"},{"label":"邮箱","value":"email"},{"label":"姓名","value":"name"}]}]}', NULL, 0);
INSERT INTO `iam`.`iam_app` (`id`, `client_id`, `client_name`, `application_type`, `description`, `logo_uri`, `client_uri`, `policy_uri`, `tos_uri`, `contacts`, `redirect_uris`, `access_token_timeout`, `refresh_token_timeout`, `id_token_timeout`, `whitelisted`, `webhook_enable`, `webhook`, `enforce_https`, `trusted_peers`, `validate_factors`, `auth_with_cert_enable`, `cli_mode_enable`, `one_time_pwd_enable`, `secure_login_enable`, `status`, `feature_app`, `open_app_auth_id`, `client_secret`, `client_secret_expires_at`, `public_key`, `public_access`, `certPem`, `cert_thumbprint`, `public_key_pem`, `qrcode_enable`, `trusted_scanners`, `client_name_en`, `uid`, `user_type`, `app_os`, `client_id_issued_at`, `update_time`, `create_by`, `update_by`, `private_key`, `grant_type`, `scope`, `tenant_owner`, `public_to_tenant`, `signing_alg`, `signature_secret`, `auth_protocol`, `config`, `enable_apply`, `link_client_id`, `preview_uri`, `inner_category`, `support_protocol`, `template_config`, `help_url`, `gateway`) VALUES (2381, 'EKUAIBAO', '易快报', NULL, '合思费控系统是敏捷的企业报销费控与聚合消费平台，为企业提供移动报销、聚合消费、全程费控、预算管理、发票管理等一站式解决方案。同时，易快报可集成多家银行及第三方支付平台，打通对公对私付款，对接财务软件自动生成凭证，实现申请-订购-报销-验票-支付-记账全流程，并可与企业现有ERP、OA、CRM等业务系统无缝集成，打破信息孤岛，激活数据价值。', NULL, 'https://www.ekuaibao.com/', NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, NULL, 0, NULL, NULL, NULL, 0, 0, 0, 1, 1, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, 0, NULL, NULL, '0', NULL, NULL, NULL, '2025-02-11 11:06:32', NULL, NULL, NULL, NULL, NULL, 'iam', 1, NULL, NULL, 'APPSTORE', NULL, 1, NULL, NULL, 'tax', 'APPSTORE', '{"template_config":[{"label":"端点地址","name":"endpoint","rules":[{"required":true,"message":""}],"placeholder":"请输入端点地址（域名）","controlType":"input","options":[]},{"label":"App Key","name":"appKey","rules":[{"required":true,"message":""}],"placeholder":"请输入App Key","controlType":"input","options":[]},{"label":"App Security","name":"appSecurity","rules":[{"required":true,"message":""}],"placeholder":"请输入App Security","controlType":"input","options":[]},{"label":"页面类型","name":"pageType","rules":[{"required":false,"message":""}],"placeholder":"请选择页面类型","controlType":"select","options":[{"label":"首页","value":"frontPage"},{"label":"我的单据","value":"home"},{"label":"待我审批","value":"approve"},{"label":"待我支付","value":"payment"},{"label":"单据详情页（待我审批 进入单据页面效果）","value":"form"},{"label":"新建单据","value":"new"},{"label":"编辑/提交草稿、驳回的单据（我的单据 进入单据页面效果）","value":"edit"},{"label":"商城（不支持移动端）","value":"mall"},{"label":"机票订购","value":"mallFlight"},{"label":"酒店订购","value":"mallHotel"},{"label":"火车订购","value":"mallTrain"},{"label":"用车订购","value":"mallCar"},{"label":"企业购","value":"mallShop"},{"label":"查看待办详情，同时底部菜单显示指定审批按钮（不支持移动端）","value":"backlogDetail"},{"label":"协助链接授权页面（只支持移动端）","value":"assistPlatform"},{"label":"随手记","value":"expenseTracker"},{"label":"用车补贴（只支持移动端）","value":"recordingTrip"}]},{"label":"有效时间（秒）","name":"expireDate","rules":[{"required":true,"message":""}],"placeholder":"请输入有效时间 最大604800秒","controlType":"input","options":[]},{"label":"用户标识","name":"user_code_mapping_attr","rules":[{"required":true,"message":""}],"placeholder":"","controlType":"select","options":[{"label":"用户ID","value":"sub"},{"label":"用户名","value":"username"},{"label":"工号","value":"user_job_number"},{"label":"手机号","value":"phone_number"},{"label":"邮箱","value":"email"},{"label":"姓名","value":"name"}]}]}', NULL, 0);


INSERT INTO `iam`.`iam_field_dict` (`id`, `field_name`, `domain_name`, `display_name`, `description`, `login_factor`, `create_mod`, `data_type`, `field_type`, `mandatory`, `import_able`, `searchable`, `unique_able`, `as_profile`, `validate_rule`, `as_claim`, `single_value`, `op_constraint`, `update_able`, `show_portal`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `sort_num`, `page_control`, `mutability`)
SELECT 2030, 'create_time', 'create_time', '创建时间', 'created time', 0, 1, 8, 2, 0, 0, 1, 0, 0, NULL, 0, 1, 3, 0, 0, sysdate(), 'system', NULL, NULL,fd.tenant_id , 0, NULL, 1
FROM iam_field_dict fd
group by fd.tenant_id;

INSERT INTO `iam`.`iam_field_dict` (`id`, `field_name`, `domain_name`, `display_name`, `description`, `login_factor`, `create_mod`, `data_type`, `field_type`, `mandatory`, `import_able`, `searchable`, `unique_able`, `as_profile`, `validate_rule`, `as_claim`, `single_value`, `op_constraint`, `update_able`, `show_portal`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `sort_num`, `page_control`, `mutability`)
SELECT 2031, 'update_time', 'update_time', '更新时间', 'last update unix timestamp', 0, 1, 8, 2, 0, 0, 1, 0, 0, NULL, 0, 1, 3, 0, 0, sysdate(), 'system', NULL, NULL,fd.tenant_id , 0, NULL, 1
FROM iam_field_dict fd
group by fd.tenant_id;

UPDATE iam_field_dict set validate_rule ='com.cyberscraft.uep.iam.dto.enums.OrgStatusEnum' where id ='2003' and field_type = 2;