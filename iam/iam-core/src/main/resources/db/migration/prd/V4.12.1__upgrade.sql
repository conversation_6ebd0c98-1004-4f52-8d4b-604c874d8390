ALTER TABLE iam_push_connector ADD COLUMN push_orgs tinyint(2) DEFAULT 1 COMMENT '是否同步部门';

INSERT INTO `iam_config` (`id`, `config_key`, `config_value`, `config_type`, `create_time`, `update_time`, `create_by`, `update_by`, `tenant_id`) select 1863862867464704002, 'active_sms', '{\"tmpl_code\":\"SMS_code\",\"channel\":\"SMS\",\"title\":\"数犀科技\",\"type\":\"ACTIVE\",\"content\":\"尊敬的{username}您好，{platformName}已为您配置好了使用账号：{user_job_number}。在使用之前，请您通过如下链接 {resetpwd_url} 重置密码\",\"name\":\"active_sms\"}', 4, NOW(), NOW(), 'admin', 'admin', st.tenant_id from iam_sys_tenant st  where st.tenant_id<>'iam';

update iam_config set config_value ='{\"channel\":\"EMAIL\",\"title\":\"帐号注册通知\",\"type\":\"WELCOME\",\"content\":\"<p>[(${username})]，</p>\\n<p>[(${platformName})]已为您配置好了使用账号：[(${user_job_number})]。</p>\\n<p>在使用之前，请您通过如下链接<a href=\\\"[(${resetpwd_url})]\\\">[(${resetpwd_url})]</a> 重置密码。</p>\\n<p>重置成功后您可使用您的账号和密码访问<a href=\\\"[(${login_url})]\\\">[(${login_url})] 。</a></p>\\n<p><br></p>\",\"name\":\"welcome_email\"}' where id ='1154962722206457812' and CAST(config_value AS CHAR) = '{"name":"welcome_email","title":"帐号注册通知","channel":"EMAIL","type":"WELCOME","content":"<p>[(${username})]，</p>\\n<p>管理员已经为您配置好了用户帐号，以下是您的帐号信息：</p>\\n<p>用户名： [(${username})]</p>\\n<p>密码： [(${password})]</p>\\n<p>您现在可以通过用户名和密码访问<a href=\\\"[(${login_url})]\\\">[(${login_url})]</a></p>\\n<p>登录成功后，完善自己的用户信息。</p>\\n<p>为了您的帐号安全，我们强烈建议验证自己的邮箱地址，以便将来可以找回密码。</p>","description":null,"display_name":null,"redirect_url":null}';
