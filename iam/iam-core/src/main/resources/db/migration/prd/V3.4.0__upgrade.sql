DROP TABLE IF EXISTS `iam_profile_template`;

update iam_user set connector_id=0 where connector_id is null;
alter table iam_user modify column `connector_id` bigint(20) DEFAULT 0 COMMENT '集成链接器ID，非集成创建的为0';

update iam_org set connector_id=0 where connector_id is null;
alter table iam_org modify column `connector_id` bigint(20) DEFAULT 0 COMMENT '集成链接器ID，非集成创建的为0';

DROP TABLE IF EXISTS `iam_link_org`;
CREATE TABLE `iam_link_org` (
                                 `id` bigint(20) NOT NULL COMMENT '主键',
                                 `org_id` bigint(20) NOT NULL COMMENT 'iam组织id',
                                 `external_id` varchar(64) NOT NULL COMMENT '外部组织id',
                                 `external_parent_id` varchar(64) DEFAULT NULL COMMENT '外部组织父id',
                                 `external_name` varchar(200) DEFAULT NULL COMMENT '外部组织名称',
                                 `connector_id` bigint(20) NOT NULL COMMENT '连接器id',
                                 `type` int(4) NOT NULL COMMENT '连接器类型',
                                 `sync_direction` tinyint(4) NOT NULL COMMENT '同步方向：0、从上游来；1、到下游去',
                                 `profile` text COMMENT '部门完整信息，json格式',
                                 `sync_batch_no` int(11) NOT NULL DEFAULT 1 COMMENT '同步批次',
                                 `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                 `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                 `tenant_id` varchar(64) NOT NULL COMMENT '租户ID',
                                 PRIMARY KEY (`id`),
                                 KEY `iam_link_org_index` (`external_id`,`connector_id`,`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='IAM外部组织关联信息表';

alter table iam_link_user add column `external_dept` varchar(2000) DEFAULT NULL COMMENT '外部用户所属外部部门';
alter table iam_link_user add column `local_org_ids` varchar(200) DEFAULT NULL COMMENT '用户所属本地部门ID';

-- 删除以下索引
DROP INDEX connector_id_index ON iam_org;
DROP INDEX iam_org_idx_1 ON iam_org;

CREATE INDEX org_tenant_index ON iam_org (parent_ref_id,name,tenant_id);

CREATE INDEX link_user_index ON iam_link_user (user_id,sync_direction,tenant_id);
CREATE INDEX link_userid_index ON iam_link_user (user_id,connector_id,tenant_id);
CREATE INDEX link_external_index ON iam_link_user (external_id,connector_id,tenant_id);

DROP TABLE IF EXISTS `iam_sync_error`;
CREATE TABLE `iam_sync_error` (
            `id` bigint(20) NOT NULL COMMENT '主键',
            `type` varchar(10) NULL COMMENT '失败的数据类型，USER表示用户，ORG表示部门',
            `connector_id` bigint(20) NOT NULL COMMENT '集成连接器Id',
            `source` text NULL COMMENT '数据源信息',
            `error_content` text NULL COMMENT '错误信息',
            `tenant_id` varchar(64) NOT NULL COMMENT '租户ID',
            PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='IAM通讯录集成异常信息表';

alter table iam_org add column `manager` varchar(200) DEFAULT NULL COMMENT '部门主管';
alter table iam_deleted_org add column `manager` varchar(200) DEFAULT NULL COMMENT '部门主管';
alter table iam_user add column `manager` varchar(200) DEFAULT NULL COMMENT '直属主管';
alter table iam_deleted_user add column `manager` varchar(200) DEFAULT NULL COMMENT '直属主管';

INSERT INTO iam_field_dict (`id`, `field_name`, `domain_name`, `display_name`, `description`, `login_factor`, `create_mod`, `data_type`, `field_type`, `mandatory`, `import_able`, `searchable`, `unique_able`, `as_profile`, `validate_rule`, `as_claim`, `single_value`, `op_constraint`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`) VALUES (2028, 'manager', 'manager', '部门主管', '部门主管', 0, 1, 4, 2, 0, 1, 1, 0, 1, null, 0, 1, 3, sysdate(), 'system', null, null, 'iam');
insert into iam_field_dict(create_by, mandatory, validate_rule, unique_able, update_by, create_time, domain_name, op_constraint, id, single_value, import_able, as_profile, as_claim, data_type, login_factor, searchable, display_name, create_mod, update_time, field_type, field_name, tenant_id, description) select fd.create_by, fd.mandatory, fd.validate_rule, fd.unique_able, fd.update_by, fd.create_time, fd.domain_name, fd.op_constraint, fd.id, fd.single_value, fd.import_able, fd.as_profile, fd.as_claim, fd.data_type, fd.login_factor, fd.searchable, fd.display_name, fd.create_mod, fd.update_time, fd.field_type, fd.field_name, st.tenant_id, fd.description from iam_field_dict fd, iam_sys_tenant st  where st.tenant_id<>'iam' and fd.id=2028;

insert into iam_field_dict(create_by, mandatory, validate_rule, unique_able, update_by, create_time, domain_name, op_constraint, id, single_value, import_able, as_profile, as_claim, data_type, login_factor, searchable, display_name, create_mod, update_time, field_type, field_name, tenant_id, description) values ( 'system', '0', null, '0', null, sysdate(), 'manager', '1', '2029', '1', '1', '0', '0', '4', '0', '0', '直属主管', '1', null, '1', 'manager', 'iam', '直属主管');
insert into iam_field_dict(create_by, mandatory, validate_rule, unique_able, update_by, create_time, domain_name, op_constraint, id, single_value, import_able, as_profile, as_claim, data_type, login_factor, searchable, display_name, create_mod, update_time, field_type, field_name, tenant_id, description) select fd.create_by, fd.mandatory, fd.validate_rule, fd.unique_able, fd.update_by, fd.create_time, fd.domain_name, fd.op_constraint, fd.id, fd.single_value, fd.import_able, fd.as_profile, fd.as_claim, fd.data_type, fd.login_factor, fd.searchable, fd.display_name, fd.create_mod, fd.update_time, fd.field_type, fd.field_name, st.tenant_id, fd.description from iam_field_dict fd, iam_sys_tenant st  where st.tenant_id<>'iam' and fd.id=2029;

alter table iam_connector add column `root_code` varchar(64) DEFAULT NULL COMMENT '外部根组织编码';
alter table iam_push_connector add column `root_code` varchar(64) DEFAULT NULL COMMENT '外部根组织编码';

update iam_field_dict set display_name='排序值',description='同级别内排序' where domain_name='seq';

update iam_field_dict set mutability=0 where domain_name='readonly';
update iam_field_dict set mutability=0 where domain_name='connector_org_id';
update iam_field_dict set mutability=0 where domain_name='connector_parent_org_id';
update iam_field_dict set mutability=0 where domain_name='org_extension';
update iam_field_dict set mutability=0 where domain_name='group_positions' and field_type=2;
update iam_field_dict set mutability=0 where domain_name='type';
update iam_field_dict set mutability=0 where domain_name='connector_type';
update iam_field_dict set mutability=0 where domain_name='user_extension';

update iam_field_dict set data_type=1 where domain_name='hired_date';
alter table iam_user modify column `hired_date` bigint(20) DEFAULT NULL COMMENT '入职日期';
alter table iam_deleted_user modify column `hired_date` bigint(20) DEFAULT NULL COMMENT '入职日期';

alter table iam_user_org add column `user_order` int(11) DEFAULT 100 COMMENT '部门内排序';
alter table iam_user_org add column `is_main` tinyint(2) DEFAULT NULL COMMENT '是否主部门：1、是，0、否';
alter table iam_user_org add column `state` varchar (32) DEFAULT NULL COMMENT '部门内状况';



-- 升级数据库历史数据
-- update iam_connector set root_code=1 where type=3;
-- update iam_connector set root_code=0 where type=6;

-- update iam_push_connector set root_code=1 where type=3;
-- 确认下易道的配置修改是否正确
-- update iam_push_connector set root_code='3f0e31a123ce402d9f6f5c35029d8c43' where type=9;
-- update iam_push_connector set type=11 where type=9;

-- 迁移部门推送数据
-- insert into iam_link_org(id,org_id,external_id,external_parent_id,connector_id,type,sync_direction,sync_batch_no,create_time,update_time,tenant_id)
-- select g.id,g.group_id,g.third_group_code,g.third_group_parent_code,g.connector_id,p.type,1,p.push_batch_no,g.create_time,g.update_time,g.tenant_id from iam_push_connector p, iam_push_sns_group g where p.id=g.connector_id;

-- 迁移用户推送数据
-- insert into iam_link_user(id,user_id,external_id,external_union_id,external_mobile,connector_id,sync_direction,sync_batch_no,type,create_time,update_time,tenant_id)
-- select u.id,u.user_id,u.third_account_id,u.third_union_id,u.mobile,u.connector_id,1,p.push_batch_no,p.type,u.create_time,u.update_time,u.tenant_id from iam_push_connector p, iam_push_sns_user u where p.id=u.connector_id and not exists(select 1 from iam_link_user l where l.connector_id=u.connector_id);
