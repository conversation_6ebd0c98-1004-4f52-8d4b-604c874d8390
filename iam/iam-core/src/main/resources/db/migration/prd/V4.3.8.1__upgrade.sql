-- 修改type字段的注释
ALTER TABLE iam_link_role MODIFY COLUMN type VARCHAR(255) COMMENT '连接器类型';

-- 增加role_type字段并设置注释
ALTER TABLE iam_link_role ADD COLUMN role_type TINYINT(4) NOT NULL COMMENT '角色类型：0、角色组；1、角色';

-- 角色集成类型
ALTER TABLE iam_connector ADD COLUMN import_roles_type tinyint DEFAULT null COMMENT '集成角色类型，0不集成角色，1按角色组集成，2按角色集成';
ALTER TABLE iam_connector ADD COLUMN roles text DEFAULT null COMMENT '角色id或角色组id';

-- 重命名并修改列的注释
ALTER TABLE iam_tag_group CHANGE COLUMN group_code app_code VARCHAR(255) COMMENT '应用code';
ALTER TABLE iam_tag_group CHANGE COLUMN group_name app_name VARCHAR(255) COMMENT '应用name';

ALTER TABLE iam_user_tag ADD COLUMN type TINYINT DEFAULT 0 COMMENT '用户角色关系类型，0自建，1集成';

ALTER TABLE iam_tag ADD COLUMN create_type TINYINT DEFAULT 0 COMMENT '角色来源类型，0自建，1集成';

-- 修改长度
alter table iam_link_user modify external_dept text null comment '外部用户所属外部部门';
ALTER TABLE iam_auditlog ADD COLUMN auth_id bigint(20) DEFAULT NULL COMMENT '认证方式';

alter table iam_third_idp add column `enable_sync_pwd` tinyint(3) DEFAULT 0 COMMENT '是否启用同步修改密码';

UPDATE iam_field_dict SET validate_rule = '{"min_len":1,"max_len":64}' WHERE id = 2002;
UPDATE iam_field_dict SET validate_rule = '{"max_len":128}' WHERE id = 2005;
UPDATE iam_field_dict SET validate_rule = '{"max_len":64}' WHERE id = 1015;
UPDATE iam_field_dict SET validate_rule = '{"max_len":32}' WHERE id = 2020;
-- 职位
UPDATE iam_field_dict SET validate_rule = '{"max_len":200}' WHERE id = 2026;
-- 主管
UPDATE iam_field_dict SET validate_rule = '{"max_len":200}' WHERE id = 2028;
UPDATE iam_field_dict SET validate_rule = '{"max_len":200}' WHERE id = 2029;




