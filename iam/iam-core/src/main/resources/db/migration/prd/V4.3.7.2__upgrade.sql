DROP TABLE IF EXISTS `iam_link_role`;
CREATE TABLE `iam_link_role` (
             `id` bigint(20) NOT NULL COMMENT '主键',
             `iam_id` bigint(20) NOT NULL COMMENT 'iam角色 id',
             `external_id` varchar(64) NOT NULL COMMENT '外部角色 id',
             `external_group_id` varchar(64) DEFAULT NULL COMMENT '外部角色组ID',
             `external_name` varchar(64) DEFAULT NULL COMMENT '外部角色名称',
             `user_ids` text DEFAULT NULL COMMENT '和角色绑定的用户id列表记录的是我们推送到下游的 用于处理后期的增加和删除比较',
             `connector_id` bigint(20) NOT NULL COMMENT '连接器id',
             `sync_direction` tinyint(4) NOT NULL COMMENT '同步方向：0、从上游来；1、到下游去',
             `type` tinyint(4) NOT NULL COMMENT '类型：0、角色组；1、角色',
             `profile` text COMMENT '用户完整信息，json格式',
             `sync_batch_no` int(11) NOT NULL COMMENT '同步批次',
             `create_time` datetime DEFAULT NULL COMMENT '创建时间',
             `update_time` datetime DEFAULT NULL COMMENT '更新时间',
             `tenant_id` varchar(64) NOT NULL COMMENT '租户ID',
             PRIMARY KEY (`id`),
             KEY `iam_link_role_index` (`external_id`,`connector_id`,`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='IAM外部角色关联信息表';

ALTER TABLE iam_push_connector ADD COLUMN role_app varchar(20) default NULL COMMENT '角色应用' after root_group_id;

ALTER TABLE iam_push_connector ADD COLUMN roles text NULL COMMENT '非全量同步的角色或者角色组' after role_app;

ALTER TABLE iam_push_connector ADD COLUMN role_attr_mapping text NULL COMMENT '角色属性映射关系' after org_attr_mapping;

ALTER TABLE iam_tag ADD COLUMN role_groups text DEFAULT NULL COMMENT '所属角色组';

ALTER TABLE iam_app_profile ADD COLUMN role_mapping text DEFAULT NULL COMMENT '角色映射关系' after org_mapping;

update iam_tag_group set group_name = 'Digitalsee' where group_name = '默认角色组' and group_code="DEFAULT" and is_default = 0 ;

ALTER TABLE iam_link_user ADD COLUMN external_roles text DEFAULT NULL COMMENT '所属外部角色集合';


DROP TABLE IF EXISTS `iam_role_group`;
CREATE TABLE `iam_role_group` (
             `id` bigint(20) NOT NULL COMMENT '主键',
             `app_id` bigint(20) NOT NULL COMMENT '角色组所属应用id',
             `name` varchar(64) NOT NULL COMMENT '角色组名称',
             `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
             `create_time` datetime DEFAULT NULL COMMENT '创建时间',
             `update_by` varchar(32) DEFAULT NULL COMMENT '修改人',
             `update_time` datetime DEFAULT NULL COMMENT '更新时间',
             `tenant_id` varchar(64) NOT NULL COMMENT '租户ID',
             PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='IAM应用角色组表';

DROP TABLE IF EXISTS `iam_push_data`;
CREATE TABLE `iam_push_data` (
                                 `id` bigint(20) NOT NULL,
                                 `business_data_id` bigint(20) NOT NULL COMMENT '业务数据ID',
                                 `business_type` tinyint(3) NOT NULL COMMENT '业务数据类型',
                                 `change_type` tinyint(3) NOT NULL COMMENT '数据变化类型',
                                 `create_time` datetime NOT NULL COMMENT '创建时间',
                                 `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                 `batch_no` bigint(20) DEFAULT NULL COMMENT '批次号',
                                 `connector_id` bigint(20) NOT NULL COMMENT '连接器id',
                                 `tenant_id` varchar(64) NOT NULL COMMENT '租户ID',
                                 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='IAM-业务推送数据表';

CREATE TABLE temp_targets (
  target INT PRIMARY KEY
) ENGINE=InnoDB;

INSERT INTO temp_targets (target) VALUES
                                      (10004), (10012), (10015), (10023), (10036),
                                      (10045), (10059), (10062), (10068), (10080), (10096);

DROP TABLE IF EXISTS `iam_app_permission_bind`;
create table iam_app_permission_bind
(
    id          bigint      not null primary key,
    app_ref_id  bigint      not null comment '应用ID',
    target_type tinyint     not null comment '绑定类型，4：PERMISSION，5：PERMISSION_SET',
    target      bigint      not null comment '绑定值，权限ID，权限组ID',
    create_time datetime    null comment '创建时间',
    update_time datetime    null comment '修改时间',
    create_by   varchar(64) null comment '创建人',
    update_by   varchar(64) null comment '更新人'
) comment 'IAM-应用-权限/权限组绑定表';

-- 设置行号变量
SET @row_number = 0;

-- 插入记录
INSERT INTO iam_app_permission_bind (id, app_ref_id, target_type, target, create_time, update_time, create_by, update_by)
SELECT
            UNIX_TIMESTAMP() * 1000 + (@row_number:=@row_number + 1),
            a.id,
            4,
            t.target,
            NOW(),
            NULL,
            'system',
            NULL
FROM iam_app a
         CROSS JOIN temp_targets t
WHERE a.id > 200 and a.auth_protocol != 'APPSTORE' and a.link_client_id is null
ORDER BY a.id, t.target;

-- 清除临时表
DROP TABLE IF EXISTS temp_targets;
