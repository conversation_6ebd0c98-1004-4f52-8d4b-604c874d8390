alter table iam_connector add column `account_link` text COMMENT '帐号关联配置';

alter table iam_field_dict add column `sort_num` int(4) default 0 COMMENT '字段排序';
-- 部门name属性为单值
update iam_field_dict set single_value=1 where id=2002 and field_type=2;

update iam_field_dict set import_able=1 where id=2015 and field_type=1;
update iam_field_dict set import_able=0 where id=2025 and field_type=2;
update iam_field_dict set import_able=1 where id=2005 and field_type=2;
update iam_field_dict set import_able=0 where id=2009 and field_type=2;
update iam_field_dict set import_able=0 where id=2001 and field_type=2;
update iam_field_dict set import_able=0 where id=2003 and field_type=2;
update iam_field_dict set import_able=0 where id=2006 and field_type=2;
update iam_field_dict set import_able=0 where id=2007 and field_type=2;
update iam_field_dict set import_able=0 where id=2008 and field_type=2;

update iam_field_dict set sort_num=11,display_name='用户ID' where field_type=1 and domain_name='sub';
update iam_field_dict set sort_num=10,display_name='用户名' where field_type=1 and domain_name='username';
update iam_field_dict set sort_num=9,display_name='手机号' where field_type=1 and domain_name='phone_number';
update iam_field_dict set sort_num=8,display_name='电子邮箱' where field_type=1 and domain_name='email';
update iam_field_dict set sort_num=7,display_name='工号' where field_type=1 and domain_name='user_job_number';
update iam_field_dict set sort_num=6,display_name='姓名' where field_type=1 and domain_name='name';
update iam_field_dict set sort_num=5,display_name='职位名称' where field_type=1 and domain_name='group_positions';
update iam_field_dict set sort_num=4,display_name='电话' where field_type=1 and domain_name='telephone_number';
update iam_field_dict set sort_num=3,display_name='常用名' where field_type=1 and domain_name='preferred_username';
update iam_field_dict set sort_num=2,display_name='昵称' where field_type=1 and domain_name='nickname';
update iam_field_dict set sort_num=1,display_name='头像' where field_type=1 and domain_name='picture';

update iam_field_dict set sort_num=4 where field_type=2 and domain_name='name';
update iam_field_dict set sort_num=3 where field_type=2 and domain_name='description';
update iam_field_dict set sort_num=2 where field_type=2 and domain_name='connector_org_id';
update iam_field_dict set sort_num=1 where field_type=2 and domain_name='connector_parent_org_id';

-- ALTER TABLE iam_user DROP INDEX phone_number_index;
-- ALTER TABLE iam_user ADD UNIQUE phone_number_index(phone_number,tenant_id) USING BTREE;
-- ALTER TABLE iam_user DROP INDEX email_index;
-- ALTER TABLE iam_user ADD INDEX email_index(email,tenant_id) USING BTREE;

ALTER TABLE iam_deleted_user DROP INDEX phone_number_index;
ALTER TABLE iam_deleted_user ADD INDEX phone_number_index(phone_number,tenant_id) USING BTREE;
ALTER TABLE iam_deleted_user DROP INDEX email_index;
ALTER TABLE iam_deleted_user ADD INDEX email_index(email,tenant_id) USING BTREE;

alter table iam_third_idp add column `enable_jit` tinyint(3) DEFAULT NULL COMMENT '启用JIT';
alter table iam_third_idp add column `jit_config` text DEFAULT NULL COMMENT 'jit配置信息';
alter table iam_third_idp add column `account_link` text DEFAULT NULL COMMENT '账号关联';
alter table iam_third_idp drop account_express;
alter table iam_third_idp drop link_field_id;

DROP TABLE IF EXISTS `iam_app_profile`;
CREATE TABLE `iam_app_profile` (
                                   `id` bigint(20) NOT NULL COMMENT '主键',
                                   `ref_id` bigint(20) COMMENT '关联ID connector id 或者 idp id',
                                   `ref_type` varchar(16) DEFAULT NULL COMMENT '通讯录相关 和 认证源相关',
                                   `app_profile` text COMMENT '应用profile',
                                   `user_mapping` text COMMENT '用户映射关系',
                                   `org_mapping` text COMMENT '部门映射关系',
                                   `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                   `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
                                   `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                   `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
                                   `tenant_id` varchar(64) NOT NULL COMMENT '租户ID',
                                   PRIMARY KEY (`id`, `tenant_id`) USING BTREE,
                                   UNIQUE KEY `app_ref_index` (`ref_id`,`ref_type`,`tenant_id`) USING BTREE COMMENT 'app_ref_index'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='应用的profile表';

DROP TABLE IF EXISTS `iam_idp_profile`;

alter table iam_app add column `template_config` text DEFAULT NULL COMMENT '应用配置模板';
alter table iam_deleted_app add column `template_config` text DEFAULT NULL COMMENT '应用配置模板';

update iam_app set support_protocol='APPSTORE',client_name='金蝶云星空',template_config='{"template_config":[{"label":"单点登录地址","name":"sso_url","rules":[{"required":false,"message":""}],"placeholder":"","controlType":"input","options":[]},{"label":"数据中心ID","name":"db_id","rules":[{"required":false,"message":""}],"placeholder":"","controlType":"input","options":[]},{"label":"应用程序ID","name":"app_id","rules":[{"required":false,"message":""}],"placeholder":"","controlType":"input","options":[]},{"label":"语言ID","name":"lc_id","rules":[{"required":false,"message":""}],"placeholder":"","controlType":"select","options":[{"label":"中文","value":"2052"},{"label":"繁体","value":"1033"},{"label":"英文","value":"3076"}]},{"label":"集成类型","name":"origin_type","rules":[{"required":false,"message":""}],"controlType":"radioBox","options":[{"label":"云之家集成","value":"XT"},{"label":"简单通行证集成","value":"SimPas"}]},{"label":"应用密钥","name":"app_secret","rules":[{"required":false,"message":""}],"placeholder":"","controlType":"input","options":[]},{"label":"用户属性","name":"user_code_mapping_attr","rules":[{"required":false,"message":""}],"placeholder":"","controlType":"select","options":[{"label":"用户ID","value":"sub"},{"label":"用户名","value":"username"},{"label":"工号","value":"user_job_number"},{"label":"手机号","value":"phone_number"},{"label":"邮箱","value":"email"},{"label":"姓名","value":"name"}]}]}' where client_id='K3Cloud';
update iam_app set support_protocol='APPSTORE',template_config='{"template_config":[{"label":"数据源名称","name":"ds_name","rules":[{"required":true,"message":""}],"placeholder":"请输入数据源名称","controlType":"input","options":[]},{"label":"获取AccessToken的URL","name":"token_url","rules":[{"required":true,"message":""}],"placeholder":"请输入AccessTokenURL","controlType":"input","options":[]},{"label":"第三方系统编码","name":"client_id","rules":[{"required":true,"message":""}],"placeholder":" 请输入你的系统编码","controlType":"input","options":[]},{"label":"单点登录入口地址","name":"sso_url","rules":[{"required":true,"message":""}],"placeholder":"请输入你的入口地址","controlType":"input","options":[]},{"label":"密钥","name":"security","rules":[{"required":true,"message":""}],"placeholder":" 请输入你的秘钥","controlType":"input","options":[]},{"label":"redirect URI","name":"redirect_uri","rules":[{"required":true,"message":""}],"placeholder":" 请输入你的redirectUrl","controlType":"input","options":[]},{"label":"多语语种","name":"lang_code","rules":[{"required":true,"message":""}],"placeholder":" 请输入多语语种","controlType":"input","options":[]},{"label":"账套编码","name":"busi_center_code","rules":[{"required":true,"message":""}],"placeholder":"请输入账套编码","controlType":"input","options":[]},{"label":"用户属性","name":"user_code_mapping_attr","rules":[{"required":true,"message":""}],"placeholder":"","controlType":"select","options":[{"label":"用户ID","value":"sub"},{"label":"用户名","value":"username"},{"label":"工号","value":"user_job_number"},{"label":"手机号","value":"phone_number"},{"label":"邮箱","value":"email"}]}]}' where client_id='NCC';
update iam_app set support_protocol='OIDC,OAUTH,SAML,CAS,APPSTORE',template_config='{"template_config":[{"label":"应用appId","name":"app_id","rules":[{"required":true,"message":""}],"placeholder":"请输入应用appId","controlType":"input","options":[]},{"label":"获取token地址","name":"token_url","rules":[{"required":true,"message":""}],"placeholder":" 请输入获取token地址","controlType":"input","options":[]},{"label":"检查Token的地址","name":"check_token_url","rules":[{"required":true,"message":""}],"placeholder":"请输入检查Token的地址","controlType":"input","options":[]},{"label":"单点登录URL","name":"sso_url","rules":[{"required":true,"message":""}],"placeholder":"请输入单点登录URL","controlType":"input","options":[]},{"label":"用户身份对应的属性名","name":"user_code_mapping_attr","rules":[{"required":true,"message":""}],"placeholder":"","controlType":"select","options":[{"label":"用户ID","value":"sub"},{"label":"用户名","value":"username"},{"label":"工号","value":"user_job_number"},{"label":"手机号","value":"phone_number"},{"label":"邮箱","value":"email"}]}]}' where client_id='WEAVER';

-- 用户Profile管理权限组
INSERT INTO iam_app_permission_sets (`id`, `app_ref_id`, `name`, `display_name`, `description`, `create_mode`, `create_time`, `create_by`, `update_time`, `update_by`, `tags`) VALUES (20055, 2, 'VIEW_APP_PROFILE', '用户属性管理', '查看用户属性管理', 1, sysdate(), 'system', NULL, NULL, 'USER, ACCOUNTING');
INSERT INTO iam_app_role_permission (`id`, `role_ref_id`, `app_ref_id`, `target_type`, `target`, `create_mode`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (**********, 30001, 2, 5, 20055, 1, sysdate(), NULL, 'system', NULL);

-- 链接中心权限组
INSERT INTO iam_app_permission_sets (`id`, `app_ref_id`, `name`, `display_name`, `description`, `create_mode`, `create_time`, `create_by`, `update_time`, `update_by`, `tags`) VALUES (20056, 2, 'VIEW_LINK', '连接器', '连接器查看', 1, sysdate(), 'system', NULL, NULL, 'LINK,CENTER');
INSERT INTO iam_app_permission_sets (`id`, `app_ref_id`, `name`, `display_name`, `description`, `create_mode`, `create_time`, `create_by`, `update_time`, `update_by`, `tags`) VALUES (20057, 2, 'VIEW_FLOW', '链接流', '链接流查看', 1, sysdate(), 'system', NULL, NULL, 'LINK,CENTER');
INSERT INTO iam_app_role_permission (`id`, `role_ref_id`, `app_ref_id`, `target_type`, `target`, `create_mode`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (3000120056, 30001, 2, 5, 20056, 1, sysdate(), NULL, 'system', NULL);
INSERT INTO iam_app_role_permission (`id`, `role_ref_id`, `app_ref_id`, `target_type`, `target`, `create_mode`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (**********, 30001, 2, 5, 20057, 1, sysdate(), NULL, 'system', NULL);

-- 平台集成权限组
INSERT INTO iam_app_permission_sets (`id`, `app_ref_id`, `name`, `display_name`, `description`, `create_mode`, `create_time`, `create_by`, `update_time`, `update_by`, `tags`) VALUES (20058, 2, 'VIEW_PLATFORM', '平台集成', '查看平台集成信息', 1, sysdate(), 'system', NULL, NULL, 'USER, ACCOUNTING');
INSERT INTO iam_app_role_permission (`id`, `role_ref_id`, `app_ref_id`, `target_type`, `target`, `create_mode`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (**********, 30001, 2, 5, 20058, 1, sysdate(), NULL, 'system', NULL);

alter table iam_link_user add column `external_mobile` varchar(32) DEFAULT NULL COMMENT '外部用户mobile';
alter table iam_link_user add column `type` int(4) NOT NULL COMMENT '连接器类型';
alter table iam_link_user modify column `sync_batch_no` int(11) NOT NULL DEFAULT 1 COMMENT '同步批次';

DROP TABLE IF EXISTS `iam_sync_ding_audit`;
CREATE TABLE `iam_sync_ding_audit` (
                                       `id` bigint(20) NOT NULL COMMENT '主键ID',
                                       `gmt_modified` bigint(20) DEFAULT NULL COMMENT '记录修改时间，unix时间戳，单位ms',
                                       `operate_module` int(10) DEFAULT NULL COMMENT '操作来源空间',
                                       `operate_module_view` varchar(255) DEFAULT NULL COMMENT '操作来源翻译者 ',
                                       `biz_id` varchar(32) DEFAULT NULL COMMENT '文件ID ',
                                       `operator_name` varchar(32) DEFAULT NULL COMMENT '用户昵称',
                                       `platform` bigint(20) DEFAULT NULL COMMENT '操作端',
                                       `platform_view` varchar(64) DEFAULT NULL COMMENT '操作端翻译值',
                                       `status` int(10) DEFAULT NULL COMMENT '记录状态',
                                       `action` int(10) DEFAULT NULL COMMENT '操作类型',
                                       `action_view` varchar(64) DEFAULT NULL COMMENT '操作类型值翻译',
                                       `resource` varchar(255) DEFAULT NULL COMMENT '文件名',
                                       `gmt_create` bigint(20) DEFAULT NULL COMMENT '记录生成时间，Unix时间戳，单位ms',
                                       `userid` varchar(32) DEFAULT NULL COMMENT '员工userID',
                                       `ip_address` varchar(200) DEFAULT NULL COMMENT '操作机器IP',
                                       `org_name` varchar(255) DEFAULT NULL COMMENT '文件所属组织名称',
                                       `receiver_name` varchar(255) DEFAULT NULL COMMENT '文件接收方名称',
                                       `receiver_type_view` varchar(32) DEFAULT NULL COMMENT '接收方类型翻译值',
                                       `receiver_type` int(5) DEFAULT NULL COMMENT '文件接收方类型',
                                       `resource_extension` varchar(32) DEFAULT NULL COMMENT '文件类型',
                                       `resource_size` bigint(20) DEFAULT NULL COMMENT '文件大小',
                                       `target_space_id` bigint(20) DEFAULT NULL COMMENT '空间ID',
                                       `real_name` varchar(32) DEFAULT NULL COMMENT '用户姓名',
                                       `batch_no` bigint(20) DEFAULT NULL COMMENT '同步批次号',
                                       `tenant_id` varchar(64) NOT NULL COMMENT '租户ID',
                                       PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='IAM-文件审计日志同步表';

-- 审计日志同步过程的信息记录表
DROP TABLE IF EXISTS `iam_sync_ding_audit_log`;
CREATE TABLE `iam_sync_ding_audit_log` (
                                           `id` bigint(20) NOT NULL COMMENT '主键ID',
                                           `config_id` bigint(20) NOT NULL COMMENT '对应平台信息的ID',
                                           `biz_id` varchar(32) DEFAULT NULL COMMENT '文件ID ',
                                           `gmt_create` bigint(20) DEFAULT NULL COMMENT '记录生成时间，Unix时间戳，单位ms',
                                           `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                           `tenant_id` varchar(64) NOT NULL COMMENT '租户ID',
                                           PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='IAM-文件审计同步日志表';

alter table iam_user modify column `email_verified` tinyint(3) default 1 comment '用户的电子邮件地址是否已经验证过 ,0：否，1：是';
alter table iam_user modify column `phone_number_verified` tinyint(3) default 1 comment '用户的移动电话号码是否已经验证过,1:是，0：否';