INSERT INTO `iam_app` (`id`, `client_id`, `client_name`, `application_type`, `description`, `logo_uri`, `client_uri`, `policy_uri`, `tos_uri`, `contacts`, `redirect_uris`, `access_token_timeout`, `refresh_token_timeout`, `id_token_timeout`, `whitelisted`, `webhook_enable`, `webhook`, `enforce_https`, `trusted_peers`, `validate_factors`, `auth_with_cert_enable`, `cli_mode_enable`, `one_time_pwd_enable`, `secure_login_enable`, `status`, `open_app_auth_id`, `client_secret`, `client_secret_expires_at`, `public_key`, `public_access`, `certPem`, `cert_thumbprint`, `public_key_pem`, `qrcode_enable`, `trusted_scanners`, `client_name_en`, `uid`, `user_type`, `app_os`, `client_id_issued_at`, `update_time`, `create_by`, `update_by`, `private_key`, `grant_type`, `scope`, `tenant_owner`, `public_to_tenant`, `signing_alg`, `signature_secret`, `auth_protocol`, `config`, `enable_apply`, `link_client_id`, `preview_uri`, `inner_category`, `support_protocol`, `template_config`, `help_url`) VALUES (173, 'COOL', '酷学院', 3, '酷学院数字化企业培训平台,针对中大型企业在线企业培训系统，提供员工elearning平台搭建、满足企业线上培训、新员工培训、在线培训考试、企业内训等。', NULL, 'https://www.coolcollege.com/', NULL, NULL, NULL, NULL, 0, 0, 0, 0, 0, NULL, 0, NULL, NULL, NULL, 0, 0, 0, 1, NULL, NULL, 0, NULL, 0, NULL, NULL, NULL, 0, NULL, NULL, '0', NULL, NULL, NULL, '2023-10-23 11:34:35', NULL, NULL, NULL, NULL, NULL, 'iam', 1, NULL, NULL, 'APPSTORE', NULL, 1, NULL, NULL, 'office', 'APPSTORE', '{\"template_config\":[{\"controlType\":\"input\",\"name\":\"key\",\"rules\":[{\"required\":true}],\"label\":\"Key\",\"placeholder\":\"请输入Key\"},{\"controlType\":\"input\",\"name\":\"secret\",\"rules\":[{\"required\":true}],\"label\":\"Secret\",\"placeholder\":\"请输入Secret\"},{\"controlType\":\"input\",\"name\":\"enterpriseId\",\"rules\":[{\"required\":true}],\"label\":\"企业ID\",\"placeholder\":\"请输入企业ID\"},{\"controlType\":\"input\",\"name\":\"ssoUrl\",\"rules\":[{\"required\":true}],\"label\":\"单点地址\",\"placeholder\":\"请输入单点地址\"},{\"controlType\":\"select\",\"name\":\"userCodeMappingAttr\",\"options\":[{\"label\":\"用户ID\",\"value\":\"sub\"},{\"label\":\"用户名\",\"value\":\"username\"},{\"label\":\"工号\",\"value\":\"user_job_number\"},{\"label\":\"手机号\",\"value\":\"phone_number\"},{\"label\":\"邮箱\",\"value\":\"email\"},{\"label\":\"姓名\",\"value\":\"name\"}],\"rules\":[{\"required\":true}],\"label\":\"用户标识\",\"placeholder\":\"请选择用户标识\"}]}', NULL);
update iam_app set template_config='{"template_config":[{"controlType":"input","name":"tenantId","rules":[{"required":true}],"label":"租户ID","placeholder":"请输入租户ID"},{"controlType":"select","name":"type","options":[{"label":"用户名","value":"email"},{"label":"BeisenUserID","value":"id"},{"label":"工号","value":"jobcode"}],"rules":[{"required":true}],"label":"北森用户标识","placeholder":"请选择北森用户表示"},{"controlType":"select","name":"userCodeMappingAttr","options":[{"label":"用户ID","value":"sub"},{"label":"用户名","value":"username"},{"label":"工号","value":"user_job_number"},{"label":"手机号","value":"phone_number"},{"label":"邮箱","value":"email"},{"label":"姓名","value":"name"}],"rules":[{"required":true}],"label":"用户标识","placeholder":"请选择用户标识"},{"controlType":"textArea","name":"publicKey","rules":[{"required":true}],"label":"公钥","placeholder":"请输入公钥"},{"controlType":"textArea","name":"privateKey","rules":[{"required":true}],"label":"私钥","placeholder":"请输入私钥"}]}',support_protocol='OIDC,APPSTORE' where id ='107' and client_id='BEISEN' and tenant_owner='iam';

alter table iam_config modify column config_value mediumtext NOT NULL comment '配置value';

update iam_config set config_value=replace(config_value,'{"uc_name":','{"uc_name":{"zh_cn":') where id=1154652285359333378;
update iam_config set config_value=replace(config_value,',"uc_logo"','},"uc_logo"') where config_value like '%"uc_logo":%' and id=1154652285359333378;
update iam_config set config_value=replace(config_value,'}','}}') where config_value not like '%"uc_logo":%' and id=1154652285359333378;
