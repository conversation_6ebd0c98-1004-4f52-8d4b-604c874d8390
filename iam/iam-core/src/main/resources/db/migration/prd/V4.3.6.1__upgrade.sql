-- 用户头像支持设置到500K
alter table iam_user MODIFY column `picture` mediumtext DEFAULT NULL COMMENT '用户头像';
alter table iam_deleted_user MODIFY column `picture` mediumtext DEFAULT NULL COMMENT '用户头像';

update iam_field_dict set validate_rule="{\"min_len\":1,\"max_len\":1392663, \"err_msg\":\"picture is invalid, length < 1Mb\"}" where id = 1021 and field_name ='picture';

alter table iam_connector add column import_type tinyint(1) DEFAULT NULL COMMENT '集成方式：1、增量；2、手动全量；3、自动全量';
alter table iam_push_connector add column push_type tinyint(1) DEFAULT NULL COMMENT '同步方式：1、增量；2、手动全量；3、自动全量';

-- bip旗舰版单点登录
INSERT INTO iam_app (id, client_id, client_name, application_type, description, logo_uri, client_uri, policy_uri, tos_uri, contacts, redirect_uris, access_token_timeout, refresh_token_timeout, id_token_timeout, whitelisted, webhook_enable, webhook, enforce_https, trusted_peers, validate_factors, auth_with_cert_enable, cli_mode_enable, one_time_pwd_enable, secure_login_enable, status, open_app_auth_id, client_secret, client_secret_expires_at, public_key, public_access, certPem, cert_thumbprint, public_key_pem, qrcode_enable, trusted_scanners, client_name_en, uid, user_type, app_os, client_id_issued_at, update_time, create_by, update_by, private_key, grant_type, scope, tenant_owner, public_to_tenant, signing_alg, signature_secret, auth_protocol, config, enable_apply, link_client_id, preview_uri, inner_category, support_protocol, template_config, help_url) VALUES (180, 'YONBIPULTIMATE', '用友BIP旗舰版', 3, '用友,全球领先的企业数智化软件与服务提供商,致力于用创想与技术,推动商业和社会进步,用友商业创新平台—用友BIP,提供10大领域创新服务,包括智能财务、数智人力、数智供应链、数智采购、智能制造...', NULL, 'https://c4.yonyoucloud.com', null, null, null, null, 0, 0, 0, 0, 0, null, 0, null, null, null, 0, 0, 0, 1, null, null, 0, null, 0, null, null, null, 0, null, null, '0', null, null, null, now(), 'admin', 'admin', null, null, null, 'iam', 1, null, null, 'APPSTORE', null, 0, null, '', 'office', 'APPSTORE', '{"template_config":[{"controlType":"input","name":"appKey","rules":[{"required":true}],"label":"appKey","placeholder":"请输入应用key"},{"controlType":"input","name":"appSecret","rules":[{"required":true}],"label":"appSecret","placeholder":"请输入应用密钥"},{"controlType":"input","name":"tokenUrl","rules":[{"required":true}],"label":"接口鉴权的地址","placeholder":"请输入获取AccessToken的地址"},{"controlType":"input","name":"getCodeUrl","rules":[{"required":true}],"label":"用户认证临时编码的地址","placeholder":"请输入获取用户认证临时编码地址"},{"controlType":"input","name":"thirdUcId","rules":[{"required":true}],"label":"thirdUcId","placeholder":"请输入thirdUcId"},{"controlType":"input","name":"tenantId","rules":[{"required":true}],"label":"租户ID","placeholder":"请输入租户ID"},{"controlType":"select","name":"userCodeMappingAttr","options":[{"label":"用户ID","value":"sub"},{"label":"用户名","value":"username"},{"label":"工号","value":"user_job_number"},{"value":"phone_number","label":"手机号"},{"value":"email","label":"邮箱"}],"rules":[{"required":true}],"label":"用户身份"},{"controlType":"textArea","name":"redirectUri","rules":[{"required":false}],"label":"重定向地址","placeholder":"请输入重定向地址，如访问地址后已拼接redirect参数,以访问链接为准"}]}', null);

-- 手机号前缀校验规则更改
update iam_field_dict set validate_rule='{\"reg_expr\":\"^1([\\\\d]{10})|(\\\\+[0-9]{1,4}-)[0-9]{4,11}$\",\"err_msg\":\"phone number is invalid\"}' where field_name='phone_number';

ALTER TABLE iam_sync_error ADD COLUMN status tinyint(4) default 0 COMMENT '同一次集成和同步会同时存在 异常和超过阈值需要删除的 通过改状态区分异常和删除 0 异常 1 删除';

-- 北理钉钉视图
-- CREATE ALGORITHM=UNDEFINED DEFINER=`uep`@`%` SQL SECURITY DEFINER VIEW `iam_view_user_code_ding_uid` AS select distinct `a`.`user_code` AS `user_code`,`b`.`external_id` AS `ding_user_id` from (`iam_user_org` `a` left join `iam_link_user` `b` on((`a`.`uid` = `b`.`user_id`))) where ((`a`.`user_code` is not null) and (`b`.`external_id` is not null) and `b`.`sync_direction`=1 and `b`.`type`=3);