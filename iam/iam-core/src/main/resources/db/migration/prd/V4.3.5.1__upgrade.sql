ALTER TABLE iam_link_user ADD COLUMN source text COMMENT '外部用户原始信息，json格式';
ALTER TABLE iam_link_user ADD COLUMN local_orgs text COMMENT '用户所属本地部门，json格式';

ALTER TABLE iam_connector ADD COLUMN append_orgs tinyint NULL DEFAULT 0 COMMENT '更新用户所属部门方式: 0、覆盖 1、追加';

-- 审批意见增加了审批意见
alter table iam_apply_process_log add remark text null comment '审批意见' after approve_status;

-- 增加计划任务开始执行时间
ALTER TABLE iam_push_connector ADD COLUMN planned_start_time datetime default null comment '计划开始同步时间';
ALTER TABLE iam_connector ADD COLUMN planned_start_time datetime default null comment '计划开始集成时间';

UPDATE iam_field_dict SET mandatory = 0 WHERE id in (1014);

-- 为账号管理员增加部门管理权限
INSERT INTO `iam`.`iam_app_role_permission` (`id`, `role_ref_id`, `app_ref_id`, `target_type`, `target`, `create_mode`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (3000320004, 30003, 2, 5, 20004, 1, now(), NULL, 'system', NULL);
INSERT INTO `iam`.`iam_app_role_permission` (`id`, `role_ref_id`, `app_ref_id`, `target_type`, `target`, `create_mode`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (3000320026, 30003, 2, 5, 20026, 1, now(), NULL, 'system', NULL);
INSERT INTO `iam`.`iam_app_role_permission` (`id`, `role_ref_id`, `app_ref_id`, `target_type`, `target`, `create_mode`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (3000320012, 30003, 2, 5, 20012, 1, now(), NULL, 'system', NULL);