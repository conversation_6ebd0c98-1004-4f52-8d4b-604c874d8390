-- V1.1.3
-- 增加用户授权信息字段
ALTER TABLE iam_sys_tenant ADD COLUMN `license_config` text COMMENT '租户的模块授权信息,JSON字符串';
ALTER TABLE iam_sys_tenant ADD COLUMN `license_num` tinyint(4) NOT NULL DEFAULT 0 COMMENT '授权用户数';

-- 应用接入表增加SAML单点登录认证信息
ALTER TABLE iam_app ADD COLUMN auth_protocol varchar(32) default "OIDC" COMMENT '单点登录认证类型';
ALTER TABLE iam_app ADD COLUMN saml_config text COMMENT 'SAML协议配置信息';
ALTER TABLE iam_deleted_app ADD COLUMN auth_protocol varchar(32) default "OIDC" COMMENT '单点登录认证类型';
ALTER TABLE iam_deleted_app ADD COLUMN saml_config text COMMENT 'SAML协议配置信息';

-- ALTER TABLE iam_app ADD COLUMN response_signed varchar(10) COMMENT '响应数据';
-- ALTER TABLE iam_app ADD COLUMN assertion_signed varchar(10) COMMENT 'Assertion签名';
-- ALTER TABLE iam_app ADD COLUMN signature_algorithm varchar(10) COMMENT '签名算法';
-- ALTER TABLE iam_app ADD COLUMN digest_algorithm varchar(10) COMMENT '签名摘要';

DROP TABLE IF EXISTS `iam_ldap_instance`;
CREATE TABLE `iam_ldap_instance` (
                            `id` varchar(32) NOT NULL COMMENT '主键ID',
                            `instance_id` varchar(16) NOT NULL COMMENT '实例id',
                            `username` varchar(16) NOT NULL COMMENT '管理帐号',
                            `password` varchar(255) DEFAULT NULL COMMENT '用户登录密码 ',
                            `create_time` datetime DEFAULT NULL COMMENT '创建时间 ',
                            `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                            `tenant_id` varchar(64) NOT NULL COMMENT '租户ID',
                            PRIMARY KEY (`id`) USING BTREE,
                            UNIQUE KEY `username_index` (`username`,`tenant_id`) USING BTREE,
                            UNIQUE KEY `instance_index` (`instance_id`,`tenant_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='IAM-ldap实例信息表';

-- 为iam_user 和 iam_user_org 表增加扩展字段
ALTER TABLE iam_user ADD COLUMN user_extension text COMMENT '用户扩展字段';
ALTER TABLE iam_user ADD COLUMN user_job_number varchar(32) COMMENT '用户职工号';
ALTER TABLE iam_user ADD COLUMN group_positions varchar(20) COMMENT '占位字段';
ALTER TABLE iam_org ADD COLUMN org_extension text COMMENT '用户组织关系扩展字段';
ALTER TABLE iam_org ADD COLUMN group_positions varchar(20) COMMENT '占位字段';
ALTER TABLE iam_deleted_user ADD COLUMN user_extension text COMMENT '用户扩展字段';
ALTER TABLE iam_deleted_user ADD COLUMN user_job_number varchar(32) COMMENT '用户职工号';
ALTER TABLE iam_deleted_user ADD COLUMN group_positions varchar(20) COMMENT '占位字段';
ALTER TABLE iam_deleted_org ADD COLUMN org_extension text COMMENT '用户组织关系扩展字段';
ALTER TABLE iam_deleted_org ADD COLUMN group_positions varchar(20) COMMENT '占位字段';
ALTER TABLE iam_app MODIFY COLUMN `application_type` tinyint(3) NULL COMMENT '应用类型，1：Native App，2：Trusted App，3：SPA，4：Web App，5：CLI App';
ALTER TABLE iam_deleted_app MODIFY COLUMN `application_type` tinyint(3) NULL COMMENT '应用类型，1：Native App，2：Trusted App，3：SPA，4：Web App，5：CLI App';

INSERT INTO iam_app_permission_sets (`id`, `app_ref_id`, `name`, `display_name`, `description`, `create_mode`, `create_time`, `create_by`, `update_time`, `update_by`, `tags`) VALUES (30015, 2, 'VIEW_LDAPSERVICE', '查看Ldap服务', '查看Ldap服务', 1, sysdate(), 'system', NULL, NULL, 'USER,ACCOUNTING');
INSERT INTO iam_app_permission_sets (`id`, `app_ref_id`, `name`, `display_name`, `description`, `create_mode`, `create_time`, `create_by`, `update_time`, `update_by`, `tags`) VALUES (30016, 2, 'MANAGE_ABMAPP', 'ABM应用分发', 'ABM应用分发', 1, sysdate(), 'system', NULL, NULL, 'VPP,ADM');
INSERT INTO iam_app_permission_sets (`id`, `app_ref_id`, `name`, `display_name`, `description`, `create_mode`, `create_time`, `create_by`, `update_time`, `update_by`, `tags`) VALUES (30017, 2, 'MANAGE_ENTAPP', '企业应用分发', '企业应用分发', 1, sysdate(), 'system', NULL, NULL, 'CLIENT_APP,ADM');
INSERT INTO iam_app_permission_sets (`id`, `app_ref_id`, `name`, `display_name`, `description`, `create_mode`, `create_time`, `create_by`, `update_time`, `update_by`, `tags`) VALUES (30018, 2, 'MANAGE_INVITATIONS', '分发用户邀请', '分发用户邀请', 1, sysdate(), 'system', NULL, NULL, 'INVITATION,ADM');
INSERT INTO iam_app_permission_sets (`id`, `app_ref_id`, `name`, `display_name`, `description`, `create_mode`, `create_time`, `create_by`, `update_time`, `update_by`, `tags`) VALUES (30019, 2, 'MANAGE_DEVICE', '设备中心', '设备中心', 1, sysdate(), 'system', NULL, NULL, 'MDM');
INSERT INTO iam_app_permission_sets (`id`, `app_ref_id`, `name`, `display_name`, `description`, `create_mode`, `create_time`, `create_by`, `update_time`, `update_by`, `tags`) VALUES (30020, 2, 'MANAGE_APPPOLICY', '应用策略', '应用策略', 1, sysdate(), 'system', NULL, NULL, 'MDM');
INSERT INTO iam_app_permission_sets (`id`, `app_ref_id`, `name`, `display_name`, `description`, `create_mode`, `create_time`, `create_by`, `update_time`, `update_by`, `tags`) VALUES (30021, 2, 'MANAGE_DEVICEPOLICY', '设备准入策略', '设备准入策略', 1, sysdate(), 'system', NULL, NULL, 'MDM');
INSERT INTO iam_app_permission_sets (`id`, `app_ref_id`, `name`, `display_name`, `description`, `create_mode`, `create_time`, `create_by`, `update_time`, `update_by`, `tags`) VALUES (30022, 2, 'MANAGE_LIC', '授权管理', '授权管理', 1, sysdate(), 'system', NULL, NULL, 'SYSTEM');
INSERT INTO iam_app_permission_sets (`id`, `app_ref_id`, `name`, `display_name`, `description`, `create_mode`, `create_time`, `create_by`, `update_time`, `update_by`, `tags`) VALUES (30023, 2, 'MANAGE_NEWSLIST', '消息中心', '消息中心', 1, sysdate(), 'system', NULL, NULL, 'MESSAGE');
INSERT INTO iam_app_permission_sets (`id`, `app_ref_id`, `name`, `display_name`, `description`, `create_mode`, `create_time`, `create_by`, `update_time`, `update_by`, `tags`) VALUES (30024, 2, 'VIEW_LOG_LOGIN', '登录日志', '登录日志', 1, sysdate(), 'system', NULL, NULL, 'LOG');
INSERT INTO iam_app_permission_sets (`id`, `app_ref_id`, `name`, `display_name`, `description`, `create_mode`, `create_time`, `create_by`, `update_time`, `update_by`, `tags`) VALUES (30025, 2, 'VIEW_LOG_SYSTEM', '系统日志', '系统日志', 1, sysdate(), 'system', NULL, NULL, 'LOG');
INSERT INTO iam_app_permission_sets (`id`, `app_ref_id`, `name`, `display_name`, `description`, `create_mode`, `create_time`, `create_by`, `update_time`, `update_by`, `tags`) VALUES (30026, 2, 'VIEW_PERM_ADMIN', '管理员账号', '管理员账号', 1, sysdate(), 'system', NULL, NULL, 'SYSTEM');
INSERT INTO iam_app_permission_sets (`id`, `app_ref_id`, `name`, `display_name`, `description`, `create_mode`, `create_time`, `create_by`, `update_time`, `update_by`, `tags`) VALUES (30027, 2, 'VIEW_PERM_ROLES', '角色管理', '角色管理', 1, sysdate(), 'system', NULL, NULL, 'SYSTEM');

INSERT INTO `iam_field_dict` ( `create_by`, `mandatory`, `validate_rule`, `unique_able`, `update_by`, `create_time`, `domain_name`, `op_constraint`, `id`, `single_value`,`import_able`, `as_profile`, `as_claim`, `data_type`, `login_factor`, `searchable`, `display_name`, `create_mod`, `update_time`, `field_type`, `field_name`, `tenant_id`, `description`) values ( 'system', '0', null, '1', null, sysdate(), 'user_job_number', '1', '2020', '1', '1', '1', '1', '4', '0', '1', '工号', '1', null, '1', 'user_job_number', 'iam', 'user job number name');
INSERT INTO `iam_field_dict` ( `create_by`, `mandatory`, `validate_rule`, `unique_able`, `update_by`, `create_time`, `domain_name`, `op_constraint`, `id`, `single_value`,`import_able`, `as_profile`, `as_claim`, `data_type`, `login_factor`, `searchable`, `display_name`, `create_mod`, `update_time`, `field_type`, `field_name`, `tenant_id`, `description`) values ( 'system', '0', null, '0', null, sysdate(), 'user_extension', '1', '2021', '1', '0', '1', '1', '4', '0', '0', '用户扩展属性', '1', null, '1', 'user_extension', 'iam', 'user extension name');
INSERT INTO `iam_field_dict` ( `create_by`, `mandatory`, `validate_rule`, `unique_able`, `update_by`, `create_time`, `domain_name`, `op_constraint`, `id`, `single_value`,`import_able`, `as_profile`, `as_claim`, `data_type`, `login_factor`, `searchable`, `display_name`, `create_mod`, `update_time`, `field_type`, `field_name`, `tenant_id`, `description`) values ( 'system', '0', null, '0', null, sysdate(), 'org_extension', '1', '2022', '1', '0', '1', '1', '4', '0', '0', '组织扩展属性', '1', null, '2', 'org_extension', 'iam', 'org extension name');
