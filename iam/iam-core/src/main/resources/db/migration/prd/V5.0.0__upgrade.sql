alter table iam_app add column gateway tinyint(1) DEFAULT '0' COMMENT '启用网关状态: 0-未启用 1-已启用';
alter table iam_deleted_app add column gateway tinyint(1) DEFAULT '0' COMMENT '启用网关状态: 0-未启用 1-已启用';
-- userinfo 接口增加返回工号
INSERT INTO `iam`.`iam_user_profile_attr` (`id`, `profile_ref_id`, `as_claim`, `domain_name`, `readers`, `sort_order`, `writers`, `tenant_id`, `claim_name`, `fix_value`)
SELECT 1016, 1301072217508618241, 0, 'user_job_number', 'SYSTEM, ADMIN, APP, SELF, EVERYONE', 150, 'SYSTEM, ADMIN', st.tenant_id, NULL, NULL
FROM iam_sys_tenant st;

alter table iam_sys_tenant add column `temp_time` varchar(10) DEFAULT NULL COMMENT '临时有效时间';

INSERT INTO `iam_config` (`id`, `config_key`, `config_value`, `config_type`, `create_time`, `update_time`, `create_by`, `update_by`, `tenant_id`) select 1154962722206457859, 'update_pwd_msg', '{\"name\":\"update_pwd_msg\",\"title\":\"密码修改通知\",\"channel\":\"FEISHU\",\"type\":\"MESSAGE\",\"config_id\":\"1830810851578220545\",\"content\":\"您的密码已修改成功。如非本人操作，请及时联系管理员。\",\"description\":null,\"display_name\":null,\"redirect_url\":null}', 4, NOW(), NOW(), 'admin', 'admin', st.tenant_id from iam_sys_tenant st  where st.tenant_id<>'iam';
INSERT INTO `iam_config` (`id`, `config_key`, `config_value`, `config_type`, `create_time`, `update_time`, `create_by`, `update_by`, `tenant_id`) select 1154962722206457860, 'reset_pwd_msg', '{\"name\":\"reset_pwd_msg\",\"title\":\"密码重置通知\",\"channel\":\"FEISHU\",\"type\":\"MESSAGE\",\"config_id\":\"1830810851578220545\",\"content\":\"您的密码已重置，新密码为：{password}。为了账户安全，请尽快登录并修改密码。\",\"description\":null,\"display_name\":null,\"redirect_url\":null}', 4, NOW(), NOW(), 'admin', 'admin', st.tenant_id from iam_sys_tenant st  where st.tenant_id<>'iam';
INSERT INTO `iam_config` (`id`, `config_key`, `config_value`, `config_type`, `create_time`, `update_time`, `create_by`, `update_by`, `tenant_id`) select 1154962722206457861, 'admin_reset_pwd_msg', '{\"name\":\"admin_reset_pwd_msg\",\"title\":\"管理员重置密码通知\",\"channel\":\"FEISHU\",\"type\":\"MESSAGE\",\"config_id\":\"1830810851578220545\",\"content\":\"管理员已为您重置账号{loginName}的密码，新密码为：{password}。请及时登录并修改密码。\",\"description\":null,\"display_name\":null,\"redirect_url\":null}', 4, NOW(), NOW(), 'admin', 'admin', st.tenant_id from iam_sys_tenant st  where st.tenant_id<>'iam';
INSERT INTO `iam_config` (`id`, `config_key`, `config_value`, `config_type`, `create_time`, `update_time`, `create_by`, `update_by`, `tenant_id`) select 1154962722206457862, 'expire_pwd_msg', '{\"name\":\"expire_pwd_msg\",\"title\":\"密码过期提醒\",\"channel\":\"FEISHU\",\"type\":\"MESSAGE\",\"config_id\":\"1830810851578220545\",\"content\":\"您的密码将在{days}天后过期，请及时修改密码。\",\"description\":null,\"display_name\":null,\"redirect_url\":null}', 4, NOW(), NOW(), 'admin', 'admin', st.tenant_id from iam_sys_tenant st  where st.tenant_id<>'iam';
INSERT INTO `iam_config` (`id`, `config_key`, `config_value`, `config_type`, `create_time`, `update_time`, `create_by`, `update_by`, `tenant_id`) select 1154962722206457863, 'enterprise_msg_config', '{\"enabled\":false,\"flg\":"DINGDING",\"config_id\":null}', 5, NOW(), NOW(), 'admin', 'admin', st.tenant_id from iam_sys_tenant st  where st.tenant_id<>'iam';


INSERT INTO `iam_config` (`id`, `config_key`, `config_value`, `config_type`, `create_time`, `update_time`, `create_by`, `update_by`, `tenant_id`) select 1154962722206457864, 'update_pwd_email', '{\"name\":\"update_pwd_email\",\"title\":\"密码修改通知\",\"channel\":\"EMAIL\",\"type\":\"MESSAGE\",\"content\":\"尊敬的用户：<br><br>您的账户密码已成功修改。<br>如果这不是您本人的操作，请立即联系系统管理员。<br><br>此致<br>系统管理团队<br>\",\"description\":null,\"display_name\":null,\"redirect_url\":null}', 4, NOW(), NOW(), 'admin', 'admin', st.tenant_id from iam_sys_tenant st  where st.tenant_id<>'iam';
INSERT INTO `iam_config` (`id`, `config_key`, `config_value`, `config_type`, `create_time`, `update_time`, `create_by`, `update_by`, `tenant_id`) select 1154962722206457865, 'reset_pwd_email', '{\"name\":\"reset_pwd_email\",\"title\":\"密码重置通知\",\"channel\":\"EMAIL\",\"type\":\"MESSAGE\",\"content\":\"尊敬的用户：<br><br>您的账户密码已重置。<br>密码为：[(${password})]。为了账户安全，请尽快登录并修改密码。<br><br>此致<br>系统管理团队<br>\",\"description\":null,\"display_name\":null,\"redirect_url\":null}', 4, NOW(), NOW(), 'admin', 'admin', st.tenant_id from iam_sys_tenant st  where st.tenant_id<>'iam';
INSERT INTO `iam_config` (`id`, `config_key`, `config_value`, `config_type`, `create_time`, `update_time`, `create_by`, `update_by`, `tenant_id`) select 1154962722206457866, 'admin_reset_pwd_email', '{\"name\":\"admin_reset_pwd_email\",\"title\":\"管理员重置密码通知\",\"channel\":\"EMAIL\",\"type\":\"MESSAGE\",\"content\":\"尊敬的用户：<br><br>系统管理员已为您的账号进行了密码重置。<br>账号：[(${loginName})]<br>新密码：[(${password})]。<br>为了您的账户安全，请及时登录系统并修改密码。<br><br>此致<br>系统管理团队<br>\",\"description\":null,\"display_name\":null,\"redirect_url\":null}', 4, NOW(), NOW(), 'admin', 'admin', st.tenant_id from iam_sys_tenant st  where st.tenant_id<>'iam';
INSERT INTO `iam_config` (`id`, `config_key`, `config_value`, `config_type`, `create_time`, `update_time`, `create_by`, `update_by`, `tenant_id`) select 1154962722206457867, 'expire_pwd_email', '{\"name\":\"expire_pwd_email\",\"title\":\"密码过期提醒\",\"channel\":\"EMAIL\",\"type\":\"MESSAGE\",\"content\":\"尊敬的用户：<br><br>您的账户密码即将过期，距离过期时间还剩[(${days})]天。<br>为避免影响您的正常使用，请及时登录系统修改密码。<br><br>此致<br>系统管理团队<br>\",\"description\":null,\"display_name\":null,\"redirect_url\":null}', 4, NOW(), NOW(), 'admin', 'admin', st.tenant_id from iam_sys_tenant st  where st.tenant_id<>'iam';


INSERT INTO `iam_config` (`id`, `config_key`, `config_value`, `config_type`, `create_time`, `update_time`, `create_by`, `update_by`, `tenant_id`) select 1154962722206457868, 'update_pwd_sms', '{\"name\":\"update_pwd_sms\",\"title\":\"密码修改通知\",\"channel\":\"SMS\",\"type\":\"MESSAGE\",\"content\":\"【系统通知】您的密码已修改成功。如非本人操作，请及时联系管理员。\",\"description\":null,\"display_name\":null,\"redirect_url\":null}', 4, NOW(), NOW(), 'admin', 'admin', st.tenant_id from iam_sys_tenant st  where st.tenant_id<>'iam';
INSERT INTO `iam_config` (`id`, `config_key`, `config_value`, `config_type`, `create_time`, `update_time`, `create_by`, `update_by`, `tenant_id`) select 1154962722206457869, 'reset_pwd_sms', '{\"name\":\"reset_pwd_sms\",\"title\":\"密码重置通知\",\"channel\":\"SMS\",\"type\":\"MESSAGE\",\"content\":\"【系统通知】您的密码已重置，新密码为：{password}。为了账户安全，请尽快登录并修改密码。\",\"description\":null,\"display_name\":null,\"redirect_url\":null}', 4, NOW(), NOW(), 'admin', 'admin', st.tenant_id from iam_sys_tenant st  where st.tenant_id<>'iam';
INSERT INTO `iam_config` (`id`, `config_key`, `config_value`, `config_type`, `create_time`, `update_time`, `create_by`, `update_by`, `tenant_id`) select 1154962722206457870, 'admin_reset_pwd_sms', '{\"name\":\"admin_reset_pwd_sms\",\"title\":\"管理员重置密码通知\",\"channel\":\"SMS\",\"type\":\"MESSAGE\",\"content\":\"【系统通知】管理员已为您重置账号{loginName}的密码，新密码为：{password}。请及时登录并修改密码。\",\"description\":null,\"display_name\":null,\"redirect_url\":null}', 4, NOW(), NOW(), 'admin', 'admin', st.tenant_id from iam_sys_tenant st  where st.tenant_id<>'iam';
INSERT INTO `iam_config` (`id`, `config_key`, `config_value`, `config_type`, `create_time`, `update_time`, `create_by`, `update_by`, `tenant_id`) select 1154962722206457871, 'expire_pwd_sms', '{\"name\":\"expire_pwd_sms\",\"title\":\"密码过期提醒\",\"channel\":\"SMS\",\"type\":\"MESSAGE\",\"content\":\"【系统通知】您的密码将在{days}天后过期，请及时修改密码。\",\"description\":null,\"display_name\":null,\"redirect_url\":null}', 4, NOW(), NOW(), 'admin', 'admin', st.tenant_id from iam_sys_tenant st  where st.tenant_id<>'iam';


alter table iam_user_status add column lock_time datetime DEFAULT NULL COMMENT '锁定时间';
alter table iam_user_status add column suspend_time datetime DEFAULT NULL COMMENT '禁用时间';

update iam_app set inner_category = 'office,hr' where id = '107';
update iam_app set inner_category = 'tools,other' where id = '111';
update iam_app set inner_category = 'marketing,office' where id = '114';
update iam_app set inner_category = 'marketing,office' where id = '115';
update iam_app set inner_category = 'other' where id = '135';
update iam_app set inner_category = 'other' where id = '136';
update iam_app set inner_category = 'hr' where id = '140';
update iam_app set inner_category = 'other' where id = '141';
update iam_app set inner_category = 'other' where id = '145';
update iam_app set inner_category = 'other' where id = '146';
update iam_app set inner_category = 'other' where id = '153';
update iam_app set inner_category = 'hr' where id = '155';
update iam_app set inner_category = 'other' where id = '158';
update iam_app set inner_category = 'other,hr' where id = '163';