DROP TABLE IF EXISTS `iam_api_connector`;
CREATE TABLE `iam_api_connector` (
                                     `id` bigint(20) NOT NULL COMMENT '主键',
                                     `name` varchar(50)  NULL COMMENT '名称',
                                     `description` varchar(200)  NULL COMMENT '描述',
                                     `http_host` varchar(100)  NULL COMMENT '接口域名',
                                     `auth_type` varchar(20)  NULL COMMENT '鉴权方式',
                                     `auth_config` varchar(500)  NULL COMMENT '鉴权配置参数,json字符串',
                                     `icon` text COMMENT '图标',
                                     `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                     `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
                                     `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                     `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
                                     `tenant_id` varchar(64) NOT NULL COMMENT '租户ID',
                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='API连接器信息表';

DROP TABLE IF EXISTS `iam_api_trigger`;
CREATE TABLE `iam_api_trigger` (
                                   `id` bigint(20) NOT NULL COMMENT '主键',
                                   `name` varchar(50)  NULL COMMENT '名称',
                                   `description` varchar(200)  NULL COMMENT '描述',
                                   `connector_id` bigint(20) NOT NULL COMMENT '连接器ID',
                                   `status` int(1) NOT NULL COMMENT '状态：1、启用；0、禁用',
                                   `config` text COMMENT '参数模型配置，json字符串',
                                   `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                   `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
                                   `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                   `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
                                   `tenant_id` varchar(64) NOT NULL COMMENT '租户ID',
                                   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='API触发事件配置表';

DROP TABLE IF EXISTS `iam_api_action`;
CREATE TABLE `iam_api_action` (
                                  `id` bigint(20) NOT NULL COMMENT '主键',
                                  `name` varchar(50)  NULL COMMENT '名称',
                                  `description` varchar(200)  NULL COMMENT '描述',
                                  `connector_id` bigint(20) NOT NULL COMMENT '连接器ID',
                                  `status` int(1) NOT NULL COMMENT '状态：1、启用；0、禁用',
                                  `config` text COMMENT '参数模型配置，json字符串',
                                  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
                                  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
                                  `tenant_id` varchar(64) NOT NULL COMMENT '租户ID',
                                  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='API执行动作配置表';

DROP TABLE IF EXISTS `iam_api_flow`;
CREATE TABLE `iam_api_flow` (
                                `id` bigint(20) NOT NULL COMMENT '主键',
                                `name` varchar(50)  NULL COMMENT '名称',
                                `description` varchar(200)  NULL COMMENT '描述',
                                `trigger_id` bigint(20) DEFAULT NULL COMMENT '触发器ID',
                                `flow_type` int(1) NOT NULL COMMENT '连接流类型',
                                `status` int(1) NOT NULL COMMENT '状态：1、启用；0、禁用',
                                `config` text COMMENT '参数模型配置，json字符串',
                                `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
                                `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
                                `tenant_id` varchar(64) NOT NULL COMMENT '租户ID',
                                PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='API连接流配置表';


update iam_config set config_value='{\"name\":\"resetpwd_email\",\"title\":\"hello\",\"channel\":\"EMAIL\",\"type\":\"RESETPWD\",\"content\":\"<p>您的用户中心的帐号：[(${username})] 已申请了重置密码操作。</p>\\n<p>验证码有效期为<em>[(${expired})]</em> 分钟。</p>\\n<p>重置密码的验证码是 ： [(${verify_code})]</p>\\n<p>如果非本人操作，请忽略此邮件。您的帐号密码仍然有效。</p>\",\"description\":null,\"display_name\":null,\"redirect_url\":null}' where id='1154962722206457857' and tenant_id='iam';
-- 审批流程配置
DROP TABLE IF EXISTS `iam_process_config`;

insert into `iam_field_dict` ( `create_by`, `mandatory`, `validate_rule`, `unique_able`, `update_by`, `create_time`, `domain_name`, `op_constraint`, `id`, `single_value`, `import_able`, `as_profile`, `as_claim`, `data_type`, `login_factor`, `searchable`, `display_name`, `create_mod`, `update_time`, `field_type`, `field_name`, `tenant_id`, `description`) values ( 'system', '0', '{\"min_len\":1,\"max_len\":64}', '0', null, sysdate(), 'connector_parent_org_id', '3', '2025', '1', '1', '0', '0', '4', '0', '0', 'connector parent org id', '1', null, '2', 'connector_parent_org_id', 'iam', 'connector parent org id');
alter table iam_org add column `connector_parent_org_id` varchar(64) DEFAULT NULL COMMENT '同步源的父组织结构id';
alter table iam_deleted_org add column `connector_parent_org_id` varchar(64) DEFAULT NULL COMMENT '同步源的父组织结构id';
alter table iam_user modify column `name` varchar(64) default null comment '用户的全名，即包括姓和名';
alter table iam_deleted_user modify column `name` varchar(64) default null comment '用户的全名，即包括姓和名';

update iam_field_dict set mandatory=0 where field_name in ('picture','preferred_username','user_job_number','user_extension');

update iam_config set config_value=replace(config_value,'UEP-用户中心','云身份连接器-数犀科技') where id=1154652285359333378;

alter table iam_config modify column config_value text NOT NULL comment '配置value';

DROP TABLE IF EXISTS `iam_idp_profile`;
CREATE TABLE `iam_idp_profile` (
                                 `id` bigint(20) NOT NULL COMMENT '主键',
                                 `idp_type` varchar(16) NOT NULL COMMENT '第三方idp标识',
                                 `name` varchar(50)  NULL COMMENT '名称',
                                 `config` text COMMENT '配置信息，json字符串',
                                 `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                 `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
                                 `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                 `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
                                 `tenant_id` varchar(64) NOT NULL COMMENT '租户ID',
                                 PRIMARY KEY (`id`, `tenant_id`) USING BTREE,
                                 UNIQUE KEY `idp_type_index` (`idp_type`,`tenant_id`) USING BTREE COMMENT 'idp_type_index'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='idp用户profile表';

insert into iam_idp_profile(id,idp_type,name,config,tenant_id) values(1,'AD','AD Profile','{"name":"idp","type":"OBJECT","description":"用户profile","multi_valued":false,"sub_params":[{"name":"sAMAccountName","type":"STRING","description":"sAMAccountName","multi_valued":false},{"name":"name","type":"STRING","description":"name","multi_valued":false},{"name":"telephoneNumber","type":"STRING","description":"telephoneNumber","multi_valued":false},{"name":"mail","type":"STRING","description":"mail","multi_valued":false},{"name":"userPrincipalName","type":"STRING","description":"userPrincipalName","multi_valued":false},{"name":"dn","type":"STRING","description":"dn","multi_valued":false}]}','iam');
insert into iam_idp_profile(id,idp_type,name,config,tenant_id) values(2,'LDAP','LDAP Profile','{"name":"idp","type":"OBJECT","description":"用户profile","multi_valued":false,"sub_params":[{"name":"uid","type":"STRING","description":"uid","multi_valued":false},{"name":"cn","type":"STRING","description":"cn","multi_valued":false},{"name":"dn","type":"STRING","description":"dn","multi_valued":false},{"name":"displayName","type":"STRING","description":"displayName","multi_valued":false},{"name":"mobile","type":"STRING","description":"mobile","multi_valued":false},{"name":"mail","type":"STRING","description":"mail","multi_valued":false}]}','iam');
insert into iam_idp_profile(id,idp_type,name,config,tenant_id) values(3,'DINGDING','钉钉Profile','{"name":"idp","type":"OBJECT","description":"用户profile","multi_valued":false,"sub_params":[{"name":"user_id","alias":["userid","userId"],"type":"STRING","description":"UserID","multi_valued":false},{"name":"union_id","alias":["unionid","unionId"],"type":"STRING","description":"UnionId","multi_valued":false},{"name":"mobile","type":"STRING","description":"手机号","multi_valued":false},{"name":"email","type":"STRING","description":"邮箱","multi_valued":false},{"name":"job_number","type":"STRING","description":"工号","multi_valued":false}]}','iam');
insert into iam_idp_profile(id,idp_type,name,config,tenant_id) values(6,'FEISHU','飞书Profile','{"name":"idp","type":"OBJECT","description":"用户profile","multi_valued":false,"sub_params":[{"name":"mobile","type":"STRING","description":"手机号","multi_valued":false,"value":"idp.mobile.replace(/\\\\s/g,'''').replace(/^(86|\\\\+86)(\\\\d+)/,''$2'')","value_type":"JS_EXP"},{"name":"email","type":"STRING","description":"邮箱","multi_valued":false},{"name":"employee_no","type":"STRING","description":"工号","multi_valued":false},{"name":"user_id","type":"STRING","description":"UserId","multi_valued":false},{"name":"union_id","type":"STRING","description":"UnionID","multi_valued":false}]}','iam');
insert into iam_idp_profile(id,idp_type,name,config,tenant_id) values(7,'AZUREAD','Azure Profile','{"name":"idp","type":"OBJECT","description":"用户profile","multi_valued":false,"sub_params":[{"name":"jobTitle","type":"STRING","description":"职务","multi_valued":false},{"name":"mail","type":"STRING","description":"邮箱","multi_valued":false},{"name":"mobilePhone","type":"STRING","description":"手机号","multi_valued":false},{"name":"userPrincipalName","type":"STRING","description":"主体名","multi_valued":false}]}','iam');
insert into iam_idp_profile(id,idp_type,name,config,tenant_id) values(8,'OAUTH2','OAuth2 Profile','{"name":"idp","type":"OBJECT","description":"用户profile","multi_valued":false,"sub_params":[{"name":"username","type":"STRING","description":"用户名","multi_valued":false},{"name":"email","type":"STRING","description":"邮箱","multi_valued":false},{"name":"phone_number","type":"STRING","description":"手机号","multi_valued":false},{"name":"sub","type":"STRING","description":"主题","multi_valued":false}]}','iam');
insert into iam_idp_profile(id,idp_type,name,config,tenant_id) values(9,'OIDC','OIDC Profile','{"name":"idp","type":"OBJECT","description":"用户profile","multi_valued":false,"sub_params":[{"name":"username","type":"STRING","description":"用户名","multi_valued":false},{"name":"email","type":"STRING","description":"邮箱","multi_valued":false},{"name":"phone_number","type":"STRING","description":"手机号","multi_valued":false},{"name":"sub","type":"STRING","description":"主题","multi_valued":false}]}','iam');

insert into iam_idp_profile(id,idp_type,name,config,tenant_id) select ip.id,ip.idp_type,ip.name,ip.config,st.tenant_id from iam_idp_profile ip, iam_sys_tenant st  where st.tenant_id<>'iam';

alter table iam_third_idp change idp_id idp_type varchar(16) NOT NULL COMMENT '第三方idp类型';

update iam_field_dict set display_name='地址' where field_type=1 and domain_name='address';
update iam_field_dict set display_name='生日' where field_type=1 and domain_name='birthdate';
update iam_field_dict set display_name='性别' where field_type=1 and domain_name='gender';
update iam_field_dict set display_name='用户ID' where field_type=1 and domain_name='sub';
update iam_field_dict set display_name='头像' where field_type=1 and domain_name='picture';
update iam_field_dict set display_name='电话' where field_type=1 and domain_name='telephone_number';
update iam_field_dict set display_name='部门' where field_type=1 and domain_name='org_ids';
update iam_field_dict set display_name='连接器类型' where field_type=1 and domain_name='connector_type';

DROP TABLE IF EXISTS `iam_connector_auth`;

ALTER TABLE iam_app_category DROP PRIMARY KEY ,ADD PRIMARY KEY ( `id`,`tenant_id` ) USING BTREE;
insert into iam_app_category(id,category_name,create_time,update_time,create_by,update_by,tenant_id,sort_num) values(1,'协同办公',sysdate(),sysdate(),'admin','admin','iam',1);
insert into iam_app_category(id,category_name,create_time,update_time,create_by,update_by,tenant_id,sort_num) values(2,'营销管理',sysdate(),sysdate(),'admin','admin','iam',2);
insert into iam_app_category(id,category_name,create_time,update_time,create_by,update_by,tenant_id,sort_num) values(3,'项目管理',sysdate(),sysdate(),'admin','admin','iam',3);
insert into iam_app_category(id,category_name,create_time,update_time,create_by,update_by,tenant_id,sort_num) values(4,'开发工具',sysdate(),sysdate(),'admin','admin','iam',4);
insert into iam_app_category(id,category_name,create_time,update_time,create_by,update_by,tenant_id,sort_num) values(5,'人力资源',sysdate(),sysdate(),'admin','admin','iam',5);

insert into iam_app_category(id,category_name,create_time,update_time,create_by,update_by,tenant_id,sort_num) select ac.id,ac.category_name,ac.create_time,ac.update_time,ac.create_by,ac.update_by,st.tenant_id,ac.sort_num from iam_app_category ac,iam_sys_tenant st where ac.tenant_id='iam' and st.tenant_id<>'iam' and not exists(select 1 from iam_app_category pc where st.tenant_id=pc.tenant_id);