-- 清除字典表中不需要的id_card_verified,name_verified,user_job_number_verified
delete from "iam"."iam_field_dict" where id in (1040,1041,1042);
-- 添加登录控制
ALTER TABLE "iam"."iam_login_policy" ADD COLUMN "identity_verify_attrs" text;
COMMENT ON COLUMN "iam"."iam_login_policy"."identity_verify_attrs" IS '身份验证属性';

ALTER TABLE "iam"."iam_push_connector" ADD COLUMN "flow" TINYINT DEFAULT (0);
COMMENT ON COLUMN "iam"."iam_push_connector"."flow" IS '流程可设计：0、不支持  1、支持';