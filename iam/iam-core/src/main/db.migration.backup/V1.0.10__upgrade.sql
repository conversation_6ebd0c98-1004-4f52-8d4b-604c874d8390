-- ----------------------------
-- Table structure for iam_login_policy
-- ----------------------------
DROP TABLE IF EXISTS `iam_login_policy`;
CREATE TABLE `iam_login_policy` (
                                    `id` bigint(20) NOT NULL,
                                    `as_default` tinyint(1) NOT NULL COMMENT '是否是租户内的默认登录策略',
                                    `name` varchar(100) NOT NULL COMMENT '登录策略名',
                                    `description` varchar(255) NOT NULL COMMENT '登录策略的描述',
                                    `status` tinyint(3) NOT NULL COMMENT '登录策略状态:0-禁用 1-启用',
                                    `primary_auth_method` VARCHAR(255) COMMENT '第一阶段认证因子设置',
                                    `mfa` text COMMENT '多因子认证设置',
                                    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                    `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                    `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                    `tenant_id` varchar(64) NOT NULL COMMENT '租户ID',
                                    PRIMARY KEY (`id`),
                                    UNIQUE KEY `name_idx` (`name`,`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='IAM-用户登录策略表';

DROP PROCEDURE IF EXISTS updateLoginPolicy;
CREATE PROCEDURE updateLoginPolicy()
BEGIN
    DECLARE finished INT DEFAULT FALSE;

    DECLARE tid VARCHAR(64);
    DECLARE policy_id BIGINT DEFAULT 0;

    DECLARE curs CURSOR FOR SELECT `tenant_id` FROM iam_sys_tenant;
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET finished = TRUE;

    OPEN curs;

    l: LOOP
        FETCH curs INTO tid ;
        IF finished THEN
            LEAVE l;
        END IF;
        SET policy_id = policy_id + 1;

        INSERT INTO iam_login_policy (id, as_default, name, description, status, primary_auth_method, mfa, create_time, create_by, update_time, update_by, tenant_id) VALUES (policy_id, 1, 'Default Policy', 'Default Policy', 1, '{"pwd_enabled":true,"sms_enabled":true,"email_enabled":true}', '[]', sysdate(), 'system', null, null, tid);

    END LOOP ;
    CLOSE curs;
END;

call updateLoginPolicy();
DROP PROCEDURE IF EXISTS updateLoginPolicy;

-- ----------------------------
-- Table structure for iam_login_policy_mapping
-- ----------------------------
DROP TABLE IF EXISTS `iam_login_policy_mapping`;
CREATE TABLE `iam_login_policy_mapping` (
                                            `id` bigint(20) NOT NULL,
                                            `policy_id` bigint(20) NOT NULL COMMENT '登录策略ID',
                                            `client_id` varchar(64) NOT NULL COMMENT '应用的唯一ID',
                                            `tenant_id` varchar(64) NOT NULL COMMENT '租户ID',
                                            `create_time` datetime NOT NULL COMMENT '创建时间',
                                            PRIMARY KEY (`id`),
                                            UNIQUE KEY `client_id_idx` (`client_id`,`tenant_id`,`policy_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='IAM-登录策略与租户app映射表';


-- ----------------------------
-- Table structure for iam_challenge
-- ----------------------------
DROP TABLE IF EXISTS `iam_challenge`;
CREATE TABLE `iam_challenge` (
                                 `id` bigint(20) NOT NULL COMMENT 'id',
                                 `client_id` varchar(64) NOT NULL COMMENT 'clientId',
                                 `nonce` varchar(255) NOT NULL COMMENT 'PKCE模式下的nonce',
                                 `create_time` datetime NOT NULL COMMENT '创建时间',
                                 `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
                                 `kid` varchar(255) NOT NULL COMMENT 'kid',
                                 `alg` varchar(255) NOT NULL COMMENT 'PKCE模式下的算法',
                                 `enc` varchar(255) NOT NULL COMMENT '编码方式',
                                 `tenant_id` varchar(64) NOT NULL COMMENT '租户ID',
                                 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='IAM-challenge表';