
-- ----------------------------
-- Table structure for iam_device_code
-- ----------------------------
DROP TABLE IF EXISTS `iam_device_code`;
CREATE TABLE `iam_device_code` (
                                   `id` bigint(20) NOT NULL COMMENT 'ID',
                                   `uid` bigint(20) NULL COMMENT 'token绑定的用户id',
                                   `device_code` varchar(255) NOT NULL COMMENT 'device模式下的device code',
                                   `user_code` varchar(255) NOT NULL COMMENT 'device模式下的user code',
                                   `client_id` varchar(255) NOT NULL COMMENT '应用的client id',
                                   `create_time` datetime NOT NULL COMMENT '创建时间',
                                   `expire_time` datetime NOT NULL COMMENT '过期时间',
                                   `scopes` varchar(255) NOT NULL COMMENT 'device模式下的请求的授权范围',
                                   `request_parameters` varchar(1000) NOT NULL COMMENT 'device模式下的请求参数',
                                   `approved` tinyint(1) COMMENT '是否已同意授权',
                                   `authentication_object` blob COMMENT '绑定的authentication对象',
                                   `authorization_request` blob COMMENT '绑定的authorization request对象',
                                   `tenant_id` varchar(64) COMMENT '租户ID',
                                   PRIMARY KEY (`id`),
                                   KEY `uid_index` (`uid`) COMMENT 'uid_index',
                                   KEY `user_code_index` (`user_code`) COMMENT 'user_code_index',
                                   KEY `device_code_index` (`device_code`) COMMENT 'device_code_index',
                                   KEY `client_id_index` (`client_id`) COMMENT 'client_id_index'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='IAM-device_code表';

SET @preparedStatement = (SELECT IF(
    (SELECT COUNT(*)
        FROM INFORMATION_SCHEMA.COLUMNS
        WHERE  table_name = 'iam_app'
        AND table_schema = 'iam'
        AND column_name = 'trusted_scanners'
    ) > 0,
    "SELECT 1",
    "ALTER TABLE `iam_app` add `trusted_scanners` varchar(255) DEFAULT NULL COMMENT '扫码登录受信应用ID，多个应用id以逗号分开';"
));

PREPARE alterIfNotExists FROM @preparedStatement;
EXECUTE alterIfNotExists;
DEALLOCATE PREPARE alterIfNotExists;
