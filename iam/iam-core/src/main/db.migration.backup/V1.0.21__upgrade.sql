-- ----------------------------
-- Table structure for iam_app_position
-- ----------------------------
DROP TABLE IF EXISTS `iam_app_position`;
CREATE TABLE `iam_app_position` (
                                    `id` bigint(20) NOT NULL,
                                    `client_id` varchar(64) NOT NULL COMMENT '应用id',
                                    `positions` varchar(255) NOT NULL COMMENT '多个职位信息',
                                    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                                    `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
                                    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                                    `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
                                    `tenant_id` varchar(64) NOT NULL COMMENT '租户ID',
                                    PRIMARY KEY (`id`),
                                    KEY `client_id_idx` (`client_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='IAM-定制化应用允许访问的职位对应表';