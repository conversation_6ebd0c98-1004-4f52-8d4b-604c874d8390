DROP TABLE IF EXISTS `iam_etl_sync_history`;
CREATE TABLE `iam_etl_sync_history` (
  `id` varchar(100) NOT NULL COMMENT 'ID',
  `datasource_id` varchar(64) NOT NULL COMMENT '数据来源ID',
  `batch_no` int(11) NOT NULL DEFAULT '0' COMMENT '同步批次号',
  `start_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `status` varchar(64) DEFAULT NULL COMMENT '同步状态或结果',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `datasource_id_index` (`datasource_id`) COMMENT 'datasource_id_index'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='ETL同步历史记录';
