DELIMITER $$

DROP PROCEDURE IF EXISTS `CreateIndexIfNotExists` $$
CREATE PROCEDURE `CreateIndexIfNotExists`
(
    is_unique INTEGER,
    given_table    VARCHAR(64),
    given_index    VARCHAR(64),
    given_columns  VARCHAR(64)
)
BEGIN
    DECLARE IndexIsThere INTEGER;

    SELECT COUNT(1) INTO IndexIsThere
    FROM INFORMATION_SCHEMA.STATISTICS
    WHERE table_name   = given_table
      AND   index_name   = given_index;

    IF IndexIsThere = 0 THEN
        SET @createStmt = 'CREATE INDEX ';
        IF is_unique = 1 THEN
            set @createStmt = 'CREATE UNIQUE INDEX ';
        END IF;
        SET @sqlstmt = CONCAT(@createStmt,given_index,' ON ',
                              given_table,' (',given_columns,')');
        PREPARE st FROM @sqlstmt;
        EXECUTE st;
        DEALLOCATE PREPARE st;
    ELSE
        SELECT CONCAT('Index ',given_index,' already exists on Table ',
                      given_table) CreateindexErrorMessage;
    END IF;
END $$
DELIMITER ;

-- call CreateIndexIfNotExists(1, 'iam_user', 'phone_number_index', 'phone_number');
-- call CreateIndexIfNotExists(1, 'iam_user', 'email_index', 'email');
call CreateIndexIfNotExists(0, 'iam_deleted_user', 'phone_number_index', 'phone_number');
call CreateIndexIfNotExists(0, 'iam_deleted_user', 'email_index', 'email');

DROP PROCEDURE IF EXISTS `CreateIndexIfNotExists`;