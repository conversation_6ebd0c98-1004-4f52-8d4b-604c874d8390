-- 消息中心权限组
INSERT INTO iam_app_permission_sets (`id`, `app_ref_id`, `name`, `display_name`, `description`, `create_mode`, `create_time`, `create_by`, `update_time`, `update_by`, `tags`) VALUES (20053, 2, 'VIEW_MESSAGE', '查看消息推送历史', '查看消息推送历史权限', 1, sysdate(), 'system', NULL, NULL, 'MESSAGES');
INSERT INTO iam_app_permission_sets (`id`, `app_ref_id`, `name`, `display_name`, `description`, `create_mode`, `create_time`, `create_by`, `update_time`, `update_by`, `tags`) VALUES (20054, 2, 'EDIT_MESSAGE_MEDIA', '编辑消息媒体文件', '编辑消息媒体文件权限', 1, sysdate(), 'system', NULL, NULL, 'MESSAGES');


-- 消息中心权限
INSERT INTO iam_app_permission (`id`, `app_ref_id`, `name`, `display_name`, `description`, `create_mode`, `content`, `create_time`, `create_by`, `update_time`, `update_by`, `payload`) VALUES (10113, 2, 'list:messages', 'list:messages', NULL, 1, NULL, sysdate(), 'system', NULL, NULL, NULL);
INSERT INTO iam_app_permission (`id`, `app_ref_id`, `name`, `display_name`, `description`, `create_mode`, `content`, `create_time`, `create_by`, `update_time`, `update_by`, `payload`) VALUES (10114, 2, 'read:message', 'read:message', NULL, 1, NULL, sysdate(), 'system', NULL, NULL, NULL);
INSERT INTO iam_app_permission (`id`, `app_ref_id`, `name`, `display_name`, `description`, `create_mode`, `content`, `create_time`, `create_by`, `update_time`, `update_by`, `payload`) VALUES (10115, 2, 'read:message_media', 'read:message_media', NULL, 1, NULL, sysdate(), 'system', NULL, NULL, NULL);
INSERT INTO iam_app_permission (`id`, `app_ref_id`, `name`, `display_name`, `description`, `create_mode`, `content`, `create_time`, `create_by`, `update_time`, `update_by`, `payload`) VALUES (10116, 2, 'update:message_media', 'update:message_media', NULL, 1, NULL, sysdate(), 'system', NULL, NULL, NULL);


-- 消息中心权限组和权限对应关系
INSERT INTO iam_app_permission_sets_permission (`id`, `permission_sets_ref_id`, `permission_ref_id`, `create_mode`) VALUES (2005310113, 20053, 10113, 1);
INSERT INTO iam_app_permission_sets_permission (`id`, `permission_sets_ref_id`, `permission_ref_id`, `create_mode`) VALUES (2005310114, 20053, 10114, 1);
INSERT INTO iam_app_permission_sets_permission (`id`, `permission_sets_ref_id`, `permission_ref_id`, `create_mode`) VALUES (2005410115, 20054, 10115, 1);
INSERT INTO iam_app_permission_sets_permission (`id`, `permission_sets_ref_id`, `permission_ref_id`, `create_mode`) VALUES (2005410116, 20054, 10116, 1);


-- SUPER_ADMIN增加消息中心相关权限
INSERT INTO iam_app_role_permission (`id`, `role_ref_id`, `app_ref_id`, `target_type`, `target`, `create_mode`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (3000120053, 30001, 2, 5, 20053, 1, sysdate(), NULL, 'system', NULL);
INSERT INTO iam_app_role_permission (`id`, `role_ref_id`, `app_ref_id`, `target_type`, `target`, `create_mode`, `create_time`, `update_time`, `create_by`, `update_by`) VALUES (3000120054, 30001, 2, 5, 20054, 1, sysdate(), NULL, 'system', NULL);