-- ----------------------------
-- Table structure for iam_login_policy
-- ----------------------------
DROP TABLE IF EXISTS `iam_login_policy`;
CREATE TABLE `iam_login_policy` (
        `id` bigint(20) NOT NULL,
        `as_default` tinyint(1) NOT NULL COMMENT '是否是租户内的默认登录策略',
        `name` varchar(100) NOT NULL COMMENT '登录策略名',
        `description` varchar(255) NOT NULL COMMENT '登录策略的描述',
        `status` tinyint(3) NOT NULL COMMENT '登录策略状态:0-禁用 1-启用',
        `primary_auth_method` VARCHAR(255) COMMENT '第一阶段认证因子设置',
        `mfa` text COMMENT '多因子认证设置',
        `create_time` datetime DEFAULT NULL COMMENT '创建时间',
        `create_by` varchar(64) DEFAULT NULL COMMENT '创建人',
        `update_time` datetime DEFAULT NULL COMMENT '更新时间',
        `update_by` varchar(64) DEFAULT NULL COMMENT '更新人',
        `tenant_id` varchar(64) NOT NULL COMMENT '租户ID',
        PRIMARY KEY (`id`),
        UNIQUE KEY `name_idx` (`name`,`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='IAM-用户登录策略表';