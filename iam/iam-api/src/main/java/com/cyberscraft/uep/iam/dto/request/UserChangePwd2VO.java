package com.cyberscraft.uep.iam.dto.request;

import com.cyberscraft.uep.iam.dto.enums.ChangePasswordType;
import com.cyberscraft.uep.iam.dto.enums.Sender;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * Created by Intellij IDEA.
 *
 * @Author: linyuxb
 * @Date: 2025/3/13 16:04
 * @Desc:
 */
@ApiModel(value = "UserChangePasswordInfo", description = "This view contains info for changing user's password. ")
public class UserChangePwd2VO {

    @NotNull(message = "could not be null")
    @ApiModelProperty(name = "type", value = "修改密码类型：支持密码和手机号修改", required = true, example = "SMS", allowableValues = "SMS, PASSWORD")
    private ChangePasswordType type;

    @ApiModelProperty(name = "code", value = "手机验证吗", dataType = "String", example = "123456")
    private String code;

    @ApiModelProperty(name = "oldPassword", value = "旧密码", dataType = "String", example = "a1234567")
    private String oldPassword;
    @NotEmpty(message = "could not be empty")
    @ApiModelProperty(name = "newPassword", value = "新密码", dataType = "String", required = true, example = "A7654321")
    private String newPassword;

    public ChangePasswordType getType() {
        return type;
    }

    public void setType(ChangePasswordType type) {
        this.type = type;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getOldPassword() {
        return oldPassword;
    }

    public void setOldPassword(String oldPassword) {
        this.oldPassword = oldPassword;
    }

    public String getNewPassword() {
        return newPassword;
    }

    public void setNewPassword(String newPassword) {
        this.newPassword = newPassword;
    }

    @Override
    public String toString() {
        return "UserChangePwd2VO{" +
                "type=" + type +
                ", code='" + code + '\'' +
                ", oldPassword='" + oldPassword + '\'' +
                ", newPassword='" + newPassword + '\'' +
                '}';
    }

    /**
     * userChangePwd2VO -> userChangePwdVO
     *
     * @return
     */
    public UserChangePwdVO convert() {
        UserChangePwdVO userChangePwdVO = new UserChangePwdVO();
        userChangePwdVO.setCode(StringUtils.EMPTY);
        userChangePwdVO.setOldPassword(this.getOldPassword());
        userChangePwdVO.setNewPassword(this.getNewPassword());
        return userChangePwdVO;
    }
}
