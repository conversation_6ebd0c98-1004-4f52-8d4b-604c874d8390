package com.cyberscraft.uep.iam.dto.response.ncm;

import java.io.Serializable;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

/**
 * 消息发送的用户对象
 * <AUTHOR>
 * @date Created：2020-08-06
 */
@JsonNaming(value = PropertyNamingStrategy.class)
public class SendMessageUserListVO extends MessageUserOrgInfoVO implements Serializable {
    private static final long serialVersionUID = 1L;
    
    public SendMessageUserListVO() {
    }
    
    public SendMessageUserListVO(MessageUserOrgInfoVO vo){
        this.setId(vo.getId());
        this.setEmail(vo.getEmail());
        this.setName(vo.getName());
        this.setOrgNames(vo.getOrgNames());
        this.setPhoneNumber(vo.getPhoneNumber());
        this.setUserCodes(vo.getUserCodes());
    }
    /**
     * 消息发送的用户类型
     */
    private Integer userType;
    /**
     * 消息发送结果
     */
    private Integer result;
    
    public Integer getUserType() {
        return userType;
    }
    public void setUserType(Integer userType) {
        this.userType = userType;
    }
    public Integer getResult() {
        return result;
    }
    public void setResult(Integer result) {
        this.result = result;
    }
    
    
}
