package com.cyberscraft.uep.iam.dto.request.login;

import java.util.Arrays;
import java.util.List;

/**
 * 登录流程
 */
public enum LoginFlow {

    /**
     * 密码登录
     */
    PWD_LOGIN,
    /**
     * 修改密码
     */
    PWD_RESET,
    /**
     * 身份校验
     */
    IDP_CHECK,
    /**
     * MFA多因子认证
     */
    MFA,
    /**
     * 账号绑定
     */
    ACCOUNT_BIND
    ;

    public static List<LoginFlow> getDefaultLoginFlow() {
        return Arrays.asList(LoginFlow.values());
    }
}
