package com.cyberscraft.uep.iam.dto.domain;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "mapUserGetReturnResult")
public class MapUserGetReturnResult extends BaseReturnResult {

    @ApiModelProperty(name = "data")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private MapUserGetDomain data;

    public MapUserGetDomain getData() {
        return data;
    }

    public void setData(MapUserGetDomain data) {
        this.data = data;
    }
}
