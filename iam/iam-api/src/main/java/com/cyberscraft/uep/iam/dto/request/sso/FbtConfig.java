package com.cyberscraft.uep.iam.dto.request.sso;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <p>
 *  云星空 单点登录配置
 * </p>
 *
 * <AUTHOR>
 * @since 2022/11/30 4:45 下午
 */
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class FbtConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "单点登录入口url")
    private String ssoUrl;

    @ApiModelProperty(value = "跳转url")
    private String jumpUrl;

    @ApiModelProperty(value = "应用程序ID")
    private String appId;

    @ApiModelProperty(value = "应用密钥")
    private String appKey;

    @ApiModelProperty(value = "用户身份对应的属性名")
    private String userCodeMappingAttr;

    @ApiModelProperty(value = "跳转地址")
    private String jumpPath;




    public String getSsoUrl() {
        return ssoUrl;
    }

    public void setSsoUrl(String ssoUrl) {
        this.ssoUrl = ssoUrl;
    }

    public String getJumpUrl() {
        return jumpUrl;
    }

    public void setJumpUrl(String jumpUrl) {
        this.jumpUrl = jumpUrl;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }


    public String getUserCodeMappingAttr() {
        return userCodeMappingAttr;
    }

    public void setUserCodeMappingAttr(String userCodeMappingAttr) {
        this.userCodeMappingAttr = userCodeMappingAttr;
    }

    public String getJumpPath() {
        return jumpPath;
    }

    public void setJumpPath(String jumpPath) {
        this.jumpPath = jumpPath;
    }
}
