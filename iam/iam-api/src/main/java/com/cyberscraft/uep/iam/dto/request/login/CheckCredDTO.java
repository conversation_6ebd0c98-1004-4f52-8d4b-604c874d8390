package com.cyberscraft.uep.iam.dto.request.login;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @Date 2025/1/2 11:39
 * @Version 1.0
 * @Description 校验身份属性
 */
@ApiModel(value = "CheckCredDTO", description = "校验身份属性")
public class CheckCredDTO {

    @ApiModelProperty(value = "登录id", example = "激活邀请或验证码登录需要传:用户名/邮箱/手机号")
    private String loginId;

    @ApiModelProperty(value = "姓名", example = "张三")
    private String name;

    @ApiModelProperty(value = "手机号", example = "13835389896")
    private String phoneNumber;

    @ApiModelProperty(value = "邮箱", example = "<EMAIL>")
    private String email;

    @ApiModelProperty(value = "身份证", example = "1421231231231")
    private String idCard;

    @ApiModelProperty(value = "工号", example = "123")
    private String userJobNumber;

    public String getLoginId() {
        return loginId;
    }

    public void setLoginId(String loginId) {
        this.loginId = loginId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getUserJobNumber() {
        return userJobNumber;
    }

    public void setUserJobNumber(String userJobNumber) {
        this.userJobNumber = userJobNumber;
    }
}
