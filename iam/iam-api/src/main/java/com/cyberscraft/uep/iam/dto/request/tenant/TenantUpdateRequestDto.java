package com.cyberscraft.uep.iam.dto.request.tenant;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2020-02-28 11:29
 */
@ApiModel(value = "TenantUpdateRequestDto", description = "租户更新请求对像")
public class TenantUpdateRequestDto implements Serializable {
    private static final long serialVersionUID = -3323583518508635166L;

    @ApiModelProperty(value = "租户名称")
    protected String name;

    @ApiModelProperty(value = "租户联系地址")
    protected String address;

    @ApiModelProperty(value = "租户联系人")
    protected String contacts;

    @ApiModelProperty(value = "租户联系人电话")
    protected String phone;

    @ApiModelProperty(value = "租户联系人邮箱")
    protected String email;

    @ApiModelProperty(value = "管理员信息")
    protected TenantAdminRequestDto admin;

    @ApiModelProperty(value = "授权用户数")
    private Integer licenseNum;

    @ApiModelProperty(value = "授权模块")
    private LicenseConfig licenseConfig;

    @ApiModelProperty(value = "租户类型",required = true)
    private Integer tenantType;

    @ApiModelProperty(value = "自定义版权")
    private String copyright;

    @ApiModelProperty(value = "租户到期日期",required = false)
    private LocalDateTime expiredTime;

    @ApiModelProperty(value = "临时有效日期",required = false)
    private String tempTime;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getContacts() {
        return contacts;
    }

    public void setContacts(String contacts) {
        this.contacts = contacts;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public TenantAdminRequestDto getAdmin() {
        return admin;
    }

    public void setAdmin(TenantAdminRequestDto admin) {
        this.admin = admin;
    }

    public void setLicenseConfig(LicenseConfig licenseConfig) {
        this.licenseConfig = licenseConfig;
    }

    public LicenseConfig getLicenseConfig() {
        return licenseConfig;
    }

    public Integer getLicenseNum() {
        return licenseNum;
    }

    public void setLicenseNum(Integer licenseNum) {
        this.licenseNum = licenseNum;
    }

    public Integer getTenantType() {
        return tenantType;
    }

    public void setTenantType(Integer tenantType) {
        this.tenantType = tenantType;
    }

    public String getTempTime() {
        return tempTime;
    }

    public void setTempTime(String tempTime) {
        this.tempTime = tempTime;
    }

    public String getCopyright() {
        return copyright;
    }

    public void setCopyright(String copyright) {
        this.copyright = copyright;
    }

    @Override
    public String toString() {
        return "TenantUpdateRequestDto{" +
                "name='" + name + '\'' +
                ", address='" + address + '\'' +
                ", contacts='" + contacts + '\'' +
                ", phone='" + phone + '\'' +
                ", email='" + email + '\'' +
                ", admin=" + admin +
                ", licenseNum=" + licenseNum +
                ", licenseConfig=" + licenseConfig +
                ", tenantType=" + tenantType +
                ", expiredTime=" + expiredTime +
                '}';
    }
}
