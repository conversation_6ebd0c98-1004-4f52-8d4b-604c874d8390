package com.cyberscraft.uep.iam.dto.request;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;

@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class TagSearchInVO {

    @ApiModelProperty(value = "应用id", example = "123")
    private String appId;

    @ApiModelProperty(value = "筛选条件", example = "该标签表示高管")
    private String filter;

    @ApiModelProperty(value = "动态标签或者静态标签, STATIC或者DYNAMIC", example = "STATIC")
    private String type;

    private int page;

    private int size;

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getFilter() {
        return filter;
    }

    public void setFilter(String filter) {
        this.filter = filter;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public int getPage() {
        return page;
    }

    public void setPage(int page) {
        this.page = page;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }
}
