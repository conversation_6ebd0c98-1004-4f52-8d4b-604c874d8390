package com.cyberscraft.uep.iam.dto.enums;



/**
 * <AUTHOR>
 * @date 2021-07-06 11:27
 * @description
 */
public enum OrgStatusEnum  implements IBaseEnum{
    NORMAL(1,"NORMAL"),   DELETE(-1,"DELETE" );

    OrgStatusEnum(int value,String name) {
        this.value = value;
        this.name = name;
    }

    private  int value;
    private  String name;

    @Override
    public int getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public static String getName(int value) {
        for (OrgStatusEnum ele : values()) {
            if(ele.getValue() == (value)) {
                return ele.getName();
            }
        }
        return null;
    }

    public static Integer getValue(String name) {
        for (OrgStatusEnum ele : values()) {
            if(ele.getName().equals(name)) {
                return ele.getValue();
            }
        }
        return null;
    }


}
