package com.cyberscraft.uep.iam.dto.domain;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@ApiModel(value = "map_user_update_request_domain", description = "user attributes for updating user operation. Please note this contains basic attributes that user can have. "
		+ "response info can also contains extended attributes.")
public class MapUserUpdateInDomain extends MapUserSelfUpdateInDomain{
	@ApiModelProperty(value = "用户的电子邮件地址是否已经验证过", example = "true", allowableValues = "true,false")
	private Boolean emailVerified;
	
	@ApiModelProperty(value = "用户的移动电话号码是否已经验证过", example = "true", allowableValues = "true,false")
	private Boolean phoneNumberVerified;
	
	@ApiModelProperty(value = "用户所属的组织id列表", example = "[\"e8d7f190-a41f-49f7-a798-bd63e52a413b\", \"e8d7f190-a41f-49f7-a798-bd63e52a413c\"]")
    private String[] orgIds;

	@ApiModelProperty(value = "用户所属的静态标签列表", example = "[\"高管\", \"总监\"]")
	private String[] tag;

	public String[] getTag() {
		return tag;
	}

	public void setTag(String[] tag) {
		this.tag = tag;
	}

	public Boolean getEmailVerified() {
		return emailVerified;
	}

	public void setEmailVerified(Boolean emailVerified) {
		this.emailVerified = emailVerified;
	}

	public Boolean getPhoneNumberVerified() {
		return phoneNumberVerified;
	}

	public void setPhoneNumberVerified(Boolean phoneNumberVerified) {
		this.phoneNumberVerified = phoneNumberVerified;
	}

	public String[] getOrgIds() {
		return orgIds;
	}

	public void setOrgIds(String[] orgIds) {
		this.orgIds = orgIds;
	}
}
