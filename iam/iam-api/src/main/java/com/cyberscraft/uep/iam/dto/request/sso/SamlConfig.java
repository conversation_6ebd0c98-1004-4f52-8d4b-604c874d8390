package com.cyberscraft.uep.iam.dto.request.sso;

import com.cyberscraft.uep.iam.dto.request.AssertionAttribute;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class SamlConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * SSO Url
     */
    @ApiModelProperty(value = "SSO Url")
    private String ssoUrl;
    /**
     * 接受者 URL
     */
    @ApiModelProperty(value = "接受者 Url")
    private String recipientUrl;
    /**
     * 目的URL
     */
    @ApiModelProperty(value = "目的 Url")
    private String destinationUrl;

    /**
     * SP实例ID
     */
    @ApiModelProperty(value = "服务提供者实例ID")
    private String audienceRestriction;
    /**
     * 用户ID格式
     */
    @ApiModelProperty(value = "用户ID格式")
    private String nameIdFormat;
    /**
     * 应用用户名
     */
    @ApiModelProperty(value = "应用用户名")
    private String autofillSetting;

    @ApiModelProperty(value = "响应数据")
    private Boolean responseSigned;

    @ApiModelProperty(value = "Assertion签名")
    private Boolean assertionSigned;

    @ApiModelProperty(value = "签名算法")
    private String signatureAlgorithm;

    @ApiModelProperty(value = "签名摘要")
    private String digestAlgorithm;

    /**
     * Assertion  属性
     */
    @ApiModelProperty(value = "Assertion 属性集合")
    private List<AssertionAttribute> assertionAttributes;

    public String getSsoUrl() {
        return ssoUrl;
    }

    public void setSsoUrl(String ssoUrl) {
        this.ssoUrl = ssoUrl;
    }

    public String getRecipientUrl() {
        return recipientUrl;
    }

    public void setRecipientUrl(String recipientUrl) {
        this.recipientUrl = recipientUrl;
    }

    public String getDestinationUrl() {
        return destinationUrl;
    }

    public void setDestinationUrl(String destinationUrl) {
        this.destinationUrl = destinationUrl;
    }

    public String getAudienceRestriction() {
        return audienceRestriction;
    }

    public void setAudienceRestriction(String audienceRestriction) {
        this.audienceRestriction = audienceRestriction;
    }

    public String getNameIdFormat() {
        return nameIdFormat;
    }

    public void setNameIdFormat(String nameIdFormat) {
        this.nameIdFormat = nameIdFormat;
    }

    public String getAutofillSetting() {
        return autofillSetting;
    }

    public void setAutofillSetting(String autofillSetting) {
        this.autofillSetting = autofillSetting;
    }

    public List<AssertionAttribute> getAssertionAttributes() {
        return assertionAttributes;
    }

    public void setAssertionAttributes(List<AssertionAttribute> assertionAttributes) {
        this.assertionAttributes = assertionAttributes;
    }

    public Boolean getResponseSigned() {
        return responseSigned;
    }

    public void setResponseSigned(Boolean responseSigned) {
        this.responseSigned = responseSigned;
    }

    public Boolean getAssertionSigned() {
        return assertionSigned;
    }

    public void setAssertionSigned(Boolean assertionSigned) {
        this.assertionSigned = assertionSigned;
    }

    public String getSignatureAlgorithm() {
        return signatureAlgorithm;
    }

    public void setSignatureAlgorithm(String signatureAlgorithm) {
        this.signatureAlgorithm = signatureAlgorithm;
    }

    public String getDigestAlgorithm() {
        return digestAlgorithm;
    }

    public void setDigestAlgorithm(String digestAlgorithm) {
        this.digestAlgorithm = digestAlgorithm;
    }
}
