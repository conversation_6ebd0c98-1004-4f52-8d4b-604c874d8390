package com.cyberscraft.uep.iam.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;

/**
 * @Author：xhquan
 * @Project：uep-services
 * @since：2023/9/25 15:01
 */
@ApiModel(value = "用户变更", description = "删除或者修改用户")
public class UserUpdateDto {

    @NotEmpty(message = "could not be empty")
    @ApiModelProperty(name = "operate", value = "操作", dataType = "String", required = true, example = "DELETE")
    private String operate;

    @NotEmpty(message = "user_ids could not be empty")
    @ApiModelProperty(name = "userIds", value = "用户ID列表", dataType = "String", required = true, example = "123456")
    private List<String> userIds;

    @ApiModelProperty(name = "userInfo", value = "用户信息", dataType = "String", required = true, example = "a1234567")
    private Map<String, Object> userInfo;

    public String getOperate() {
        return operate;
    }

    public void setOperate(String operate) {
        this.operate = operate;
    }

    public List<String> getUserIds() {
        return userIds;
    }

    public void setUserIds(List<String> userIds) {
        this.userIds = userIds;
    }

    public Map<String, Object> getUserInfo() {
        return userInfo;
    }

    public void setUserInfo(Map<String, Object> userInfo) {
        this.userInfo = userInfo;
    }
}
