package com.cyberscraft.uep.iam.dto.request;

import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;

/**
 * ChangePasswordByAdminVO
 *
 * <AUTHOR>
 * @date 2021/6/5
 */
public class ChangePasswordByAdminVO {
    @NotEmpty(message = "could not be empty")
    @ApiModelProperty(name = "username", value = "用户登录名", dataType = "String", required = true, example="mike")
    private String username;

    @NotEmpty(message = "could not be empty")
    @ApiModelProperty(name = "password", value = "用户登录密码", dataType = "String", required = true, example="A1234567")
    private String password;

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }
}
