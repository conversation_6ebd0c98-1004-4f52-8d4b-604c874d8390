package com.cyberscraft.uep.iam.dto.request.configs;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@ApiModel(value = "failed_login_alert_policy",
        description = "登录失败告警策略")
public class AlertPolicyVO {

    @ApiModelProperty(value = "是否启用告警策略", required = true, example="true", allowableValues = "true,false")
    private boolean enabled;

//    @ApiModelProperty(value = "告警邮件发送给谁", required = true, example="[\"<EMAIL>\",\"<EMAIL>\"]")
//    private Set<String> emailTo;

    @ApiModelProperty(value = "是否通知告警触发的用户", required = true, example="true", allowableValues = "true,false")
    private boolean notifyUser;

    @ApiModelProperty(value = "是否通知管理员", required = true, example="true", allowableValues = "true,false")
    private boolean notifyAdmin;

    public boolean isNotifyUser() {
        return notifyUser;
    }

    public void setNotifyUser(boolean notifyUser) {
        this.notifyUser = notifyUser;
    }

    public boolean isNotifyAdmin() {
        return notifyAdmin;
    }

    public void setNotifyAdmin(boolean notifyAdmin) {
        this.notifyAdmin = notifyAdmin;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

//    public Set<String> getEmailTo() {
//        return emailTo;
//    }
//
//    public void setEmailTo(Set<String> emailTo) {
//        this.emailTo = emailTo;
//    }

//    public AlertPolicyPO toUpdatePO(String alertPolichyId){
//        AlertPolicyPO po = new AlertPolicyPO(alertPolichyId);
//        po.setEnabled(isEnabled());
//
//        // only update the config when the policy is enabled
//        if (isEnabled()){
//            NotificationChannels channels = new NotificationChannels();
//
//            channels.setEmailEnabled(true);
//            channels.setEmailTo(null);
//            channels.setSmsEnabled(false);
//            channels.setSmsTo(null);
//
//            channels.setNotifyAdmin(isNotifyAdmin());
//            channels.setNotifyUser(isNotifyUser());
//
//            po.setNotificationChannels(channels);
//        }
//        return po;
//    }
//
//    public static AlertPolicyVO fromPO(AlertPolicyPO po){
//        AlertPolicyVO vo = new AlertPolicyVO();
//        vo.setEnabled(po.isEnabled());
//        NotificationChannels channels = po.getNotificationChannels();
//        if (channels == null){
//            vo.setNotifyAdmin(false);
//            vo.setNotifyUser(false);
//        }else{
//            vo.setNotifyAdmin(channels.isNotifyAdmin());
//            vo.setNotifyUser(channels.isNotifyUser());
//        }
//        return vo;
//    }
}
