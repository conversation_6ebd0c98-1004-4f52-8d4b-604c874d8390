package com.cyberscraft.uep.iam.dto.request;

import com.cyberscraft.uep.iam.dto.enums.FieldDataType;
import com.cyberscraft.uep.common.dto.PageControl;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Size;

/**
 * SchemaAttributInfoVO
 *
 * <AUTHOR>
 * @date 2021/11/16
 */
@ApiModel(value = "update_attribute_request",
        description = "update extended attribute request")
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class AttributeUpdateInVO {

    public AttributeUpdateInVO() {

    }

    @Size(message = "The length of description is too long, max length is 256", max = 256)
    @ApiModelProperty(name = "description", value = "用户属性定义的描述", example = "描述: blah, blah...")
    private String description;

    @Size(message = "The length of displayName is between 2 to 20", min = 2, max = 20)
    @ApiModelProperty(name = "displayName", value = "用户属性定义的显示名称", example = "业余爱好")
    private String displayName;

    @ApiModelProperty(value = "属性的值的约束条件，主要是针对扩展属性", example = "\"{\"min_len\":1, \"max_len\":100 }\"")
    private String constraintRule;

    @ApiModelProperty(name = "searchable", value = "用户属性定义的是否可做作为搜索", dataType = "boolean", example = "true")
    private Boolean searchable = true;

    @ApiModelProperty(name = "importable", value = "用户属性定义的是否可作为导入", dataType = "boolean", example = "false")
    private Boolean asImport = false;

    @ApiModelProperty(value = "属性是否必须的")
    private Boolean mandatory;

    @ApiModelProperty(value = "页面控件", allowableValues = "TEXT,DATETIME,SELECT", example="TEXT")
    private PageControl pageControl;

    @ApiModelProperty(value = "属性的数据类型", required = false, allowableValues = "STRING, INT, BOOL, FLOAT, DATETIME", example="STRING")
    private FieldDataType dataType;

    @ApiModelProperty(value = "全局唯一", required = false, allowableValues = "boolean", example="true")
    private Boolean unique;

    @ApiModelProperty(value = "是否更新", required = false, allowableValues = "boolean", example="true")
    private Boolean update;

    @ApiModelProperty(value = "portal展示", required = false, allowableValues = "boolean", example="true")
    private Boolean showPortal;

    public String getConstraintRule() {
        return constraintRule;
    }

    public void setConstraintRule(String constraintRule) {
        this.constraintRule = constraintRule;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public Boolean getSearchable() {
        return searchable;
    }

    public void setSearchable(Boolean searchable) {
        this.searchable = searchable;
    }

    public Boolean getAsImport() {
        return asImport;
    }

    public void setAsImport(Boolean asImport) {
        this.asImport = asImport;
    }

    public Boolean getMandatory() {
        return mandatory;
    }

    public void setMandatory(Boolean mandatory) {
        this.mandatory = mandatory;
    }

    public PageControl getPageControl() {
        return pageControl;
    }

    public void setPageControl(PageControl pageControl) {
        this.pageControl = pageControl;
    }

    public FieldDataType getDataType() {
        return dataType;
    }

    public void setDataType(FieldDataType dataType) {
        this.dataType = dataType;
    }

    public Boolean getUnique() {
        return unique;
    }

    public void setUnique(Boolean unique) {
        this.unique = unique;
    }

    public Boolean getUpdate() {
        return update;
    }

    public void setUpdate(Boolean update) {
        this.update = update;
    }

    public Boolean getShowPortal() {
        return showPortal;
    }

    public void setShowPortal(Boolean showPortal) {
        this.showPortal = showPortal;
    }

    @Override
    public String toString() {
        return "AttributeUpdateInVO{" +
                "description='" + description + '\'' +
                ", displayName='" + displayName + '\'' +
                ", searchable=" + searchable +
                ", asImport=" + asImport +
                '}';
    }
}
