package com.cyberscraft.uep.iam.api;

import com.cyberscraft.uep.common.dto.PageView;
import com.cyberscraft.uep.common.dto.ReturnResultVO;
import com.cyberscraft.uep.iam.constants.UrlConstants;
import com.cyberscraft.uep.iam.dto.domain.BaseReturnResult;
import com.cyberscraft.uep.iam.dto.response.AuditlogItemVO;
import com.cyberscraft.uep.iam.dto.response.AuditlogListVO;
import io.swagger.annotations.*;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

import static com.cyberscraft.uep.common.constant.UEPSwaggerConstants.HEADER_BEARER_XXX;
import static com.cyberscraft.uep.common.constant.UEPSwaggerConstants.HEADER_UC_ACCESS_TOKEN;
import static org.springframework.web.bind.annotation.RequestMethod.GET;

@RestController
@RequestMapping(value = UrlConstants.URL_PREFIX_API_VERSION + "/auditlog", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(description = "审计日志", tags = "Auditlog")
public interface AuditlogApi {

    @RequestMapping(value = "", method = GET)
    @ResponseBody
    @ApiOperation(response = AuditlogListVO.class,
            nickname = "searchAuditlog",
            value = "查询审计日志",
            notes = "查询审计日志",
            authorizations = {
                    @Authorization(value = "uc_auth", scopes = {
                            @AuthorizationScope(scope = "search:auditlog", description = "") }) })
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
                    required = true)})
    ReturnResultVO<PageView<AuditlogItemVO>> searchAuditlog(
            @RequestParam(value = "audit_type", required = false)
            @ApiParam(value = "以审计日志类型为过滤条件（可选的）,1: 登录日志; 2: 操作日志", allowableValues = "1, 2", example = "1")
                    Integer auditType,
            @RequestParam(value = "search_field", required = false)
            @ApiParam(value = "指定搜索的字段，用于限定搜索范围，如 'name'、'email' ", example = "name")
            String searchField,
            @RequestParam(value = "principal", required = false)
            @ApiParam(value = "以操作者为过滤条件（可选的）", example = "admin")
                    String principal,
            @RequestParam(value = "target_name", required = false)
            @ApiParam(value = "以操作对象为过滤条件（可选的）", example = "user001")
                    String targetName,
            @RequestParam(value = "client_id", required = false)
            @ApiParam(value = "应用client_id", example = "iam")
                    String client_id,
            @RequestParam(value = "event_type", required = false)
            @ApiParam(value = "以事件类型作为过滤条件（可选的），1：登录；2：用户；3：组；4：用户扩展属性；5：组扩展属性：6：应用；7：标签；8：自服务；9：系统设置；10：用户同步；11：数据源；12：用户画像", example = "1")
                    Integer eventType,
            @RequestParam(value = "event_subtype", required = false)
            @ApiParam(value = "以事件子类型作为过滤条件（可选的）", example = "201")
                    Integer eventSubtype,
            @RequestParam(value = "start_time", required = false)
            @ApiParam(value = "查询的起始时间，单位是距离1970-01-01 00:00:00 UTC的秒数", example = "1562303988")
                    Long startTime,
            @RequestParam(value = "end_time", required = false)
            @ApiParam(value = "查询的截止时间，单位是距离1970-01-01 00:00:00 UTC的秒数", example = "1562303988")
                    Long endTime,
            @RequestParam(value = "role_type", required = false)
            @ApiParam(value = "角色类型 普通用户:1，管理员:2", allowableValues = "1, 2", example = "1")
                    Integer roleType,
            @RequestParam(value = "page", required = false, defaultValue = "1")
            @ApiParam(value = "要获取的结果页号[1..N], 缺省是：1", example = "1")
                    Integer page,
            @RequestParam(value = "size", required = false, defaultValue = "50")
            @ApiParam(value = "获取的每一页的记录数[1..N], 缺省是:50, **每页允许的最大记录数为:100", example = "50")
                    Integer size);

    @RequestMapping(value = "/export", method = GET)
    @ResponseBody
    @ApiOperation(response = BaseReturnResult.class,
            nickname = "exportAuditlog",
            value = "获取登录日志",
            notes = "获取登录日志",
            authorizations = {
                    @Authorization(value = "uc_auth", scopes = {
                            @AuthorizationScope(scope = "", description = "") }) })
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
                    required = true)})
    void exportAuditlog(HttpServletResponse response,
             @RequestParam(value = "filename", required = false, defaultValue = "systemlog.csv")
             @ApiParam(
                     value = "用来保存导出的系统日志的csv文件名称",
                     example = "systemlog.csv")
                                String auditlogFile,
            @RequestParam(value = "audit_type", required = false)
            @ApiParam(value = "以审计日志类型为过滤条件（可选的）,1: 登录日志; 2: 操作日志",
                    allowableValues = "1, 2",
                    example = "1")
                                Integer auditType,
            @RequestParam(value = "search_field", required = false)
            @ApiParam(value = "指定搜索的字段，用于限定搜索范围，如 'name'、'email' ", example = "name")
                                String searchField,
             @RequestParam(value = "principal", required = false)
             @ApiParam(
                     value = "以操作者或操作对象为过滤条件（可选的）",
                     example = "admin")
                                String principal,
             @RequestParam(value = "client_id", required = false)
             @ApiParam(
                    value = "应用client_id",
                    example = "iam")
                    String client_id,
             @RequestParam(value = "event_type", required = false)
                        @ApiParam(value = "以事件类型作为过滤条件（可选的），1：登录；2：用户；3：组；4：用户扩展属性；5：组扩展属性：6：应用；7：标签；8：自服务；9：系统设置；10：用户同步；11：数据源；12：用户画像",
                                example = "1")
                                Integer eventType,
             @RequestParam(value = "event_subtype", required = false)
             @ApiParam(value = "以事件子类型作为过滤条件（可选的）", example = "201")
                                Integer eventSubtype,
             @RequestParam(value = "start_time", required = false)
             @ApiParam(value = "查询的起始时间，单位是距离1970-01-01 00:00:00 UTC的秒数", example = "1562303988")
                     Long startTime,
             @RequestParam(value = "end_time", required = false)
             @ApiParam(value = "查询的截止时间，单位是距离1970-01-01 00:00:00 UTC的秒数", example = "1562303988")
                     Long endTime,
             @RequestParam(value = "role_type", required = false)
             @ApiParam(value = "角色类型 普通用户:1，管理员:2", allowableValues = "1, 2", example = "1")
                     Integer roleType);

}
