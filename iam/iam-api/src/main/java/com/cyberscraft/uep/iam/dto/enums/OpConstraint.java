package com.cyberscraft.uep.iam.dto.enums;

public enum OpConstraint implements IBaseEnum{
	USER(1),
	ADMIN(2),
	SYSTEM(3);
	private int value;
	
	OpConstraint (int value) {
		this.value = value;
	}

	@Override
	public int getValue() {
		return value;
	}

	public static OpConstraint[] USER_OPERABLE = new OpConstraint[]{USER};
	public static OpConstraint[] ADMIN_OPERABLE = new OpConstraint[]{USER, ADMIN};
	public static OpConstraint[] SYSTEM_OPERABLE = new OpConstraint[]{USER, ADMIN, SYSTEM};
}
