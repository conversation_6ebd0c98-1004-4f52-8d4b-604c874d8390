package com.cyberscraft.uep.iam.dto.request.sso;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;

@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class MingYuanYunConfig {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "应用的appKey", required = true)
    private String appKey;

    @ApiModelProperty(value = "应用的appSecret", required = true)
    private String appSecret;

    @ApiModelProperty(value = "token有效期", required = true)
    private String validTime;

    @ApiModelProperty(value = "明源云获取AccessToken的url")
    private String tokenUrl;

    @ApiModelProperty(value = "跳转地址")
    private String callbackUrl;

    @ApiModelProperty(value = "单点登录URL")
    private String ssoUrl;

    @ApiModelProperty(value = "redirectUri是否urlencode，为1表示需要urlencode，否则不需要urlencode")
    private String transcoding = "1";

    @ApiModelProperty(value = "租户代码", required = true)
    private String tenantCode;

    @ApiModelProperty(value = "用户身份对应的属性名")
    private String userCodeMappingAttr;

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public String getValidTime() {
        return validTime;
    }

    public void setValidTime(String validTime) {
        this.validTime = validTime;
    }

    public String getTokenUrl() {
        return tokenUrl;
    }

    public void setTokenUrl(String tokenUrl) {
        this.tokenUrl = tokenUrl;
    }

    public String getCallbackUrl() {
        return callbackUrl;
    }

    public void setCallbackUrl(String callbackUrl) {
        this.callbackUrl = callbackUrl;
    }

    public String getSsoUrl() {
        return ssoUrl;
    }

    public void setSsoUrl(String ssoUrl) {
        this.ssoUrl = ssoUrl;
    }

    public String getTranscoding() {
        return transcoding;
    }

    public void setTranscoding(String transcoding) {
        this.transcoding = transcoding;
    }

    public String getTenantCode() {
        return tenantCode;
    }

    public void setTenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
    }

    public String getUserCodeMappingAttr() {
        return userCodeMappingAttr;
    }

    public void setUserCodeMappingAttr(String userCodeMappingAttr) {
        this.userCodeMappingAttr = userCodeMappingAttr;
    }
}
