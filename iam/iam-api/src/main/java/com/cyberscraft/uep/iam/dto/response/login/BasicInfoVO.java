package com.cyberscraft.uep.iam.dto.response.login;

import io.swagger.annotations.ApiModelProperty;

public class BasicInfoVO {
    @ApiModelProperty(value = "iam版本", example = "1.0.0")
    private String ucVersion;
    @ApiModelProperty(value = "iam名称", example = "用户中心")
    private String ucName;
    @ApiModelProperty(value = "租户ID", example = "true")
    private String tenantId;

    @ApiModelProperty(value = "租户ID是否展示", example = "true")
    private boolean tenantIdVisible;

    public String getUcVersion() {
        return ucVersion;
    }

    public void setUcVersion(String ucVersion) {
        this.ucVersion = ucVersion;
    }

    public String getUcName() {
        return ucName;
    }

    public void setUcName(String ucName) {
        this.ucName = ucName;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public boolean isTenantIdVisible() {
        return tenantIdVisible;
    }

    public void setTenantIdVisible(boolean tenantIdVisible) {
        this.tenantIdVisible = tenantIdVisible;
    }
}
