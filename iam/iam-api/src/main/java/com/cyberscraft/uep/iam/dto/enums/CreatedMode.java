package com.cyberscraft.uep.iam.dto.enums;

public enum CreatedMode implements IBaseEnum{
	BY_SYSTEM(1,"BY_SYSTEM"),
	BY_ADMIN(2,"BY_ADMIN"),
	BY_SYSTEM_INVISIBLE(3,"BY_SYSTEM_INVISIBLE"),
	BY_SELF(4,"BY_SELF"),
	BY_IMPORT_CSV(5,"BY_IMPORT_CSV"),
	BY_IMPORT_DS(6,"BY_IMPORT_DS"),
	BY_SYNC_DS(7,"BY_SYNC_DS"),
	BY_SCIM(8, "BY_SCIM"),
	BY_JIT(9, "BY_JIT"),
	/**
	 * client-credential app调用iam开放平台接口创建用户/用户组
	 */
	BY_CLIENT(10, "BY_CLIENT"),

	/**
	 * 微信扫码注册
	 */
	BY_WECHAT(11,"BY_WECHAT"),
	/**
	 * 微信公众号注册
	 */
	BY_WECHAT_OFFICIAL(12,"BY_WECHAT_OFFICIAL");

	private int code;
	private String name;

	CreatedMode(int code,String name) {
		this.code = code;
		this.name = name;
	}

	@Override
	public int getValue() {
		return code;
	}
	public String getName() {
		return name;
	}

	public static CreatedMode getByName(String name){
		for (CreatedMode mode : values()) {
			if (mode.name().equals(name)) {
				return mode;
			}
		}
		return null;
	}

	public static boolean createBySystem(Integer createMode){
		if (createMode == null){
			throw new RuntimeException("create mode is null");
		}
		return BY_SYSTEM.getValue() == createMode.intValue()
				||BY_SYSTEM_INVISIBLE.getValue() == createMode.intValue();
	}

	public static boolean createByAdmin(Integer createMode){
		if (createMode == null){
			throw new RuntimeException("create mode is null");
		}
		return BY_ADMIN.getValue() == createMode.intValue();
	}

	public static boolean isReadOnly(Integer createMode){
		if (createMode == null){
			throw new RuntimeException("create mode is null");
		}
		return BY_SYNC_DS.getValue() == createMode.intValue();
	}

	public static boolean createBySync(Integer createMode){
		if (createMode == null){
			throw new RuntimeException("create mode is null");
		}
		return BY_SYNC_DS.getValue() == createMode.intValue() || BY_SCIM.getValue() == createMode.intValue();
	}

	public static boolean createByJit(Integer createMode){
		if (createMode == null){
			throw new RuntimeException("create mode is null");
		}
		return BY_JIT.getValue() == createMode;
	}

	/**
	 * 是否需强制性检查
	 * @param createdMode
	 * @return
	 */
	public static boolean needCheckMandatory(CreatedMode createdMode){
		return BY_ADMIN == createdMode || BY_CLIENT == createdMode;
	}

	public static boolean noPasswordStatus(CreatedMode createdMode){
		return BY_SCIM == createdMode || BY_JIT == createdMode;
	}

	public static Integer getValue(String name) {
		for (CreatedMode ele : values()) {
			if (ele.getName().equals(name)) {
				return ele.getValue();
			}
		}
		return null;
	}

	public static CreatedMode byValue(int value) {
		for (CreatedMode ele : values()) {
			if (ele.getValue() == value) {
				return ele;
			}
		}
		return null;
	}
}
