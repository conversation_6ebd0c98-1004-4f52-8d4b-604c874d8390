package com.cyberscraft.uep.iam.dto.response;

import com.cyberscraft.uep.iam.dto.request.configs.DataSourceProfileVO;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

@Component
public class PreImportConfigTips {
    private List<DataSourceProfileVO> dsProfileList;

    @PostConstruct
    public void init(){
        dsProfileList = new ArrayList<>(2);
        dsProfileList.add(initProfile4AD());
        dsProfileList.add(initProfile4LDAP());
    }
    private DataSourceProfileVO initProfile4AD(){
//        OperationPolicyDomain dsPolicy4AD = new OperationPolicyDomain();
//        Map<String, String> attrsTip = new HashMap<>(7);
//        attrsTip.put(CommonConstants.USERNAME, DaoConstants.TIP_AD_USERNAME);
//        attrsTip.put(CommonConstants.VO_ATTR_KEY_NAME, DaoConstants.TIP_AD_NAME);
//        attrsTip.put(CommonConstants.VO_ATTR_KEY_FIRSTNAME, DaoConstants.TIP_AD_USER_GIVEN_NAME);
//        attrsTip.put(CommonConstants.VO_ATTR_KEY_LASTNAME, DaoConstants.TIP_AD_USER_SN);
//        attrsTip.put(CommonConstants.VO_ATTR_KEY_EMAIL, DaoConstants.TIP_AD_USER_MAIL);
//        attrsTip.put(CommonConstants.VO_ATTR_KEY_NICKNAME, DaoConstants.TIP_AD_USER_DISPLAY_NAME);
//        attrsTip.put(CommonConstants.VO_ATTR_KEY_PHONENUMBER, DaoConstants.TIP_AD_USER_TELEPHONE_NUMBER);
//        dsPolicy4AD.setMapAttrs(attrsTip);
//        dsPolicy4AD.setUserFilter(DaoConstants.TIP_AD_USER_FILTER);
//        dsPolicy4AD.setOrgFilter(DaoConstants.TIP_AD_GROUP_FILTER);
//        dsPolicy4AD.setImportOrgs(true);
//        UserNameOpDomain userNameOpDomain = new UserNameOpDomain();
//        userNameOpDomain.setFormatString(CommonConstants.BLANK);
//        userNameOpDomain.setUsernameFormat(CommonConstants.BLANK);
//        dsPolicy4AD.setUsernamePolicy(userNameOpDomain);
//        DataSourceConnDomain connDomain = new DataSourceConnDomain();
//        connDomain.setTls(true);
//        connDomain.setPort(636);
//        DataSourceProfileVO profileVO4AD = new DataSourceProfileVO();
//        profileVO4AD.setDsType(DataSourceType.AD.name());
//        profileVO4AD.setConnInfo(connDomain);
//        profileVO4AD.setImportPolicy(dsPolicy4AD);
//        return profileVO4AD;
        return null;
    }
    private DataSourceProfileVO initProfile4LDAP(){
//        OperationPolicyDomain dsPolicy4LDAP = new OperationPolicyDomain();
//        Map<String, String> attrsTip = new HashMap<>(7);
//        attrsTip.put(CommonConstants.USERNAME, DaoConstants.TIP_LDAP_USERNAME);
//        attrsTip.put(CommonConstants.VO_ATTR_KEY_NAME, DaoConstants.TIP_LDAP_NAME);
//        attrsTip.put(CommonConstants.VO_ATTR_KEY_FIRSTNAME, DaoConstants.TIP_LDAP_USER_GIVEN_NAME);
//        attrsTip.put(CommonConstants.VO_ATTR_KEY_LASTNAME, DaoConstants.TIP_LDAP_USER_SN);
//        attrsTip.put(CommonConstants.VO_ATTR_KEY_EMAIL, DaoConstants.TIP_LDAP_USER_MAIL);
//        attrsTip.put(CommonConstants.VO_ATTR_KEY_NICKNAME, DaoConstants.TIP_LDAP_USER_DISPLAY_NAME);
//        attrsTip.put(CommonConstants.VO_ATTR_KEY_PHONENUMBER, DaoConstants.TIP_LDAP_USER_TELEPHONE_NUMBER);
//        dsPolicy4LDAP.setMapAttrs(attrsTip);
//        dsPolicy4LDAP.setUserFilter(DaoConstants.TIP_LDAP_USER_FILTER);
//        dsPolicy4LDAP.setOrgFilter(DaoConstants.TIP_LDAP_GROUP_FILTER);
//        dsPolicy4LDAP.setImportOrgs(true);
//        UserNameOpDomain userNameOpDomain = new UserNameOpDomain();
//        userNameOpDomain.setFormatString(CommonConstants.BLANK);
//        userNameOpDomain.setUsernameFormat(CommonConstants.BLANK);
//        dsPolicy4LDAP.setUsernamePolicy(userNameOpDomain);
//        DataSourceConnDomain connDomain = new DataSourceConnDomain();
//        connDomain.setTls(true);
//        connDomain.setPort(636);
//        DataSourceProfileVO profileVO4LDAP = new DataSourceProfileVO();
//        profileVO4LDAP.setDsType(DataSourceType.LDAP.name());
//        profileVO4LDAP.setConnInfo(connDomain);
//        profileVO4LDAP.setImportPolicy(dsPolicy4LDAP);
//        return profileVO4LDAP;
        return null;
    }
    public List<DataSourceProfileVO> getDsProfileList() {
        return dsProfileList;
    }

    public void setDsProfileList(List<DataSourceProfileVO> dsProfileList) {
        this.dsProfileList = dsProfileList;
    }
}
