package com.cyberscraft.uep.iam.dto.enums;

import java.util.*;

public enum TokenStatus {
    ACTIVE("ACTIVE"),
    EXPIRED("EXPIRED"),
    INACTIVE("INACTIVE");

    private static final Map<String, TokenStatus> code2Enum = new HashMap<String, TokenStatus>();
    static {
        for (TokenStatus type : TokenStatus.values()) {
            code2Enum.put(type.value, type);
        }
    }

    private TokenStatus(String value) {
        this.value = value;
    }
    
    private String value;
    
    public String getValue() {
        return this.value;
    }
    
    public static final Set<TokenStatus> KNOWN_VALUES;  // all values, except UNKNOWN

    static {
        EnumSet<TokenStatus> kv = EnumSet.allOf(TokenStatus.class);
        KNOWN_VALUES = Collections.unmodifiableSet(kv);
    }
}
