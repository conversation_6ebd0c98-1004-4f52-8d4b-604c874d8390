package com.cyberscraft.uep.iam.dto.domain;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@ApiModel(value="base_error_response")
public class BaseReturnResult implements Serializable {

    private static final long serialVersionUID = -2831433456047912832L;

    public final static String SUCCESS = "SUCCESS";
    public final static String ERROR = "ERROR";

    @ApiModelProperty(name = "result", value = "API请求处理的结果。如果请求处理成功，结果是true;如果处理失败，结果是false",dataType = "boolean", required = true, example="true")
    private Boolean result;

    @ApiModelProperty(name = "error", value = "请求处理结果的错误码。如果处理成功，则是0", dataType = "String", required = true, example="0")
    private String error;

    @ApiModelProperty(name = "error_description", value = "请求处理结果的详细描述。如果处理成功，则是SUCCESS", dataType = "string", required = false, example="SUCCESS")
    private String errorDescription;

    public BaseReturnResult() {
        this.result = true;
        this.error = "0";
        this.errorDescription = SUCCESS;
    }

    public BaseReturnResult(Boolean result, String errorDescription, String errorCode) {
        this.result = result;
        this.errorDescription = errorDescription;
        this.error = errorCode;
    }

    public BaseReturnResult(Boolean result, String errorDescription, int errorCode) {
        this.result = result;
        this.errorDescription = errorDescription;
        this.error = String.valueOf(errorCode);
    }

    public Boolean getResult() {
        return result;
    }

    public void setResult(Boolean result) {
        this.result = result;
    }

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }

    public String getErrorDescription() {
        return errorDescription;
    }

    public void setErrorDescription(String errorDescription) {
        this.errorDescription = errorDescription;
    }
}
