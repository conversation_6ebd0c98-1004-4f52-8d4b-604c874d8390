package com.cyberscraft.uep.iam.dto.enums;



/**
 * <p>IAM-应用授权类型</p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2019-07-17 14:28
 */
public enum AppAuthTypeEnum  implements IBaseEnum{
    ORG(1,"ORG"),
    USER(2,"USER" ),
    TAG(3,"TAG" );

    AppAuthTypeEnum(int value,String name) {
        this.value = value;
        this.name = name;
    }

    private  int value;
    private  String name;

    @Override
    public int getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public static String getName(int value) {
        for (AppAuthTypeEnum ele : values()) {
            if(ele.getValue() == (value)) {
                return ele.getName();
            }
        }
        return null;
    }

    public static Integer getValue(String name) {
        for (AppAuthTypeEnum ele : values()) {
            if(ele.getName().equals(name)) {
                return ele.getValue();
            }
        }
        return null;
    }


}
