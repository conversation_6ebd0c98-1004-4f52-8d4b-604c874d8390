package com.cyberscraft.uep.iam.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.cyberscraft.uep.common.dto.PageView;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "get_auditlog_list_response", description = "response to get audit log list")
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class AuditlogListVO {
    @ApiModelProperty(name = "audit log list", value = "审计日志列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private PageView<AuditlogItemVO> data;

    public PageView<AuditlogItemVO> getData() {
        return data;
    }

    public void setData(PageView<AuditlogItemVO> data) {
        this.data = data;
    }
}
