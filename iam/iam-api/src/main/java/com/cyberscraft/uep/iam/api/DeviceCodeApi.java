package com.cyberscraft.uep.iam.api;

import com.cyberscraft.uep.common.dto.BaseReturnResult;
import com.cyberscraft.uep.iam.dto.constraint.as.AuthServerConstants;
import com.cyberscraft.uep.iam.dto.response.DeviceCodeScanResultVO;
import com.cyberscraft.uep.iam.dto.response.DeviceCodeVO;
import com.cyberscraft.uep.iam.errors.TransactionErrorDescription;
import io.swagger.annotations.*;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import static com.cyberscraft.uep.common.constant.UEPSwaggerConstants.HEADER_APPLICATION_JSON_CHARSET_UTF_8;
import static com.cyberscraft.uep.common.constant.UEPSwaggerConstants.HEADER_REQUEST_CONTENT_TYPE;
import static com.cyberscraft.uep.iam.constants.HttpStatus.SC_BAD_REQUEST;
import static com.cyberscraft.uep.iam.constants.HttpStatus.SC_INTERNAL_SERVER_ERROR;
import static com.cyberscraft.uep.iam.dto.constraint.as.AuthServerConstants.*;
import static com.cyberscraft.uep.iam.errors.TransactionErrorDescription.*;

@RestController
@RequestMapping(value = AuthServerConstants.URL_PREFIX_DEVICECODE, produces = MediaType.APPLICATION_JSON_VALUE)
@Api(description = "扫码登录", tags = "DeviceCode")
public interface DeviceCodeApi {

    @RequestMapping(value = "", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(response = DeviceCodeVO.class,
            value = "获取扫码登录的二维码数据",
            notes = "获取扫码登录的二维码数据",
            produces = "application/json")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true)
    })
    @ApiResponses(value = {
            @ApiResponse(code = SC_BAD_REQUEST,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + TransactionErrorDescription.INVALID_REDIRECT_URI + ", 错误消息: " + APP_REDIRECT_URIS_INVALID_ERROR_DESC
                            + "\r\n" + "* 错误码：" + TransactionErrorDescription.INVALID_CLIENT + ", 错误消息: " + APP_NOT_FOUND_ERROR_DESC),
            @ApiResponse(code = SC_INTERNAL_SERVER_ERROR,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + TransactionErrorDescription.SERVER_ERROR + ", 错误消息: " + APP_GEN_KEY_PAIR_ERROR_DESC
                            + "\r\n" + "* 错误码：" + TransactionErrorDescription.SERVER_ERROR + ", 错误消息: " + UNKNOWN_DAO_ERROR_DESC)
    })
    DeviceCodeVO  genDeviceCodeByGet(
            @ApiParam(value = "应用Id", example = "webapp")
            @RequestParam(value = "client_id") String clientId,
            @ApiParam(value = "授权请求的scope", example = "openid profile email")
            @RequestParam(value = "scope", required = false) String scope,
            @ApiParam(value = "授权请求的应答类型", example = "code")
            @RequestParam(value = "response_type") String responseType,
            @ApiParam(value = "授权请求的重定向URI", example = "https://www.example.com/callback")
            @RequestParam(value = "redirect_uri", required = false) String redirectUri,
            @ApiParam(value = "授权请求的state值，由发起授权的应用指定", example = "some_state")
            @RequestParam(value = "state", required = false) String state);

    @RequestMapping(value = "/scan", method = {RequestMethod.POST},
            consumes = {MediaType.APPLICATION_JSON_VALUE, MediaType.APPLICATION_JSON_UTF8_VALUE},
            produces = MediaType.APPLICATION_JSON_VALUE)
    @ApiOperation(response = DeviceCodeScanResultVO.class,
            value = "手机端扫码二维码",
            notes = "手机端扫码二维码",
            produces = "application/json")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true)
    })
    @ApiResponses(value = {
            @ApiResponse(code = SC_BAD_REQUEST,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + TransactionErrorDescription.INVALID_REDIRECT_URI + ", 错误消息: " + APP_REDIRECT_URIS_INVALID_ERROR_DESC
                            + "\r\n" + "* 错误码：" + TransactionErrorDescription.INVALID_CLIENT + ", 错误消息: " + APP_NOT_FOUND_ERROR_DESC),
            @ApiResponse(code = SC_INTERNAL_SERVER_ERROR,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + TransactionErrorDescription.SERVER_ERROR + ", 错误消息: " + APP_GEN_KEY_PAIR_ERROR_DESC
                            + "\r\n" + "* 错误码：" + TransactionErrorDescription.SERVER_ERROR + ", 错误消息: " + UNKNOWN_DAO_ERROR_DESC)
    })
    DeviceCodeScanResultVO scanDeviceCode(
            @ApiParam(value = "用户随机码", example = "5PZ7Qn")
            @RequestParam(STR_USER_CODE) String userCode,
            @ApiParam(value = "主应用的refresh token", example = "3rGGYktLS9reqN9CVFwSu28E2LMVqdPN")
            @RequestParam(STR_REFRESH_TOKEN) String refreshToken,
            @ApiParam(value = "用户是否同意授权", example = "true")
            @RequestParam(name = STR_USER_OAUTH_APPROVAL, required = false) String userOauthApproval,
            @ApiParam(value = "是否记住同意授权", example = "true")
            @RequestParam(name = STR_REMEMBER_APPROVAL, required = false, defaultValue = "false") String rememberApproval);

}
