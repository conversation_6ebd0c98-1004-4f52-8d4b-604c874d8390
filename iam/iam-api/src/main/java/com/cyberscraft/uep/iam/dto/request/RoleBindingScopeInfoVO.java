package com.cyberscraft.uep.iam.dto.request;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class RoleBindingScopeInfoVO {
   private String scope;
   private String scopeDescription;

   public RoleBindingScopeInfoVO(){

   }

   public RoleBindingScopeInfoVO(String scope){
      this.scope = scope;
   }

   public RoleBindingScopeInfoVO(String scope, String scopeDescription){
      this.scope = scope;
      this.scopeDescription =  scopeDescription;
   }

   public String getScope() {
      return scope;
   }

   public void setScope(String scope) {
      this.scope = scope;
   }

   public String getScopeDescription() {
      return scopeDescription;
   }

   public void setScopeDescription(String scopeDescription) {
      this.scopeDescription = scopeDescription;
   }
}
