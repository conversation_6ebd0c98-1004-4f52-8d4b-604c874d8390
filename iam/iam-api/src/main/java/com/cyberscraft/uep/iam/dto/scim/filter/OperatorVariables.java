package com.cyberscraft.uep.iam.dto.scim.filter;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/5/11 2:42 下午
 */
public interface OperatorVariables {

    //equal
    String eq = "eq";

    //not equal
    String ne = "ne";

    //contains
    String co = "co";

    //starts with
    String sw = "sw";

    //ends with
    String ew = "ew";

    //present(has value)
    String pr = "pr";

    //greater than
    String gt = "gt";

    //greater than or equal to
    String ge = "ge";

    //less than
    String lt = "lt";

    //less than or equal to
    String le = "le";

    //logical "and"
    String and = "and";

    //logical "or"
    String or = "or";

}
