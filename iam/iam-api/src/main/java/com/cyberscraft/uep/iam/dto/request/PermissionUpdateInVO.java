package com.cyberscraft.uep.iam.dto.request;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;

import java.util.HashMap;
import java.util.Map;

@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class PermissionUpdateInVO {
    @ApiModelProperty(value = "权限显示名，一般为权限的短描述", example = "创建组织权限")
    private String displayName;

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    @ApiModelProperty(value = "权限的长描述", example = "创建组织结构的权限")
    private String description;

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @ApiModelProperty(value = "权限的具体定义", example = "create:organizations")
    private String payload;

    public String getPayload() {
        return payload;
    }

    public void setPayload(String payload) {
        this.payload = payload;
    }

    public Map<String, Object> toMap(){
        Map<String,Object> map = new HashMap<>(1);
        if (description != null){
            map.put("description", description);
        }

        if (displayName != null){
            map.put("display_name", displayName);
        }

        if (payload != null){
            map.put("payload", payload);
        }

        return map;
    }
}
