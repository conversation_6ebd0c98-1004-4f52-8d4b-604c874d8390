package com.cyberscraft.uep.iam.api;

import com.cyberscraft.uep.common.dto.ReturnResultVO;
import com.cyberscraft.uep.iam.constants.UrlConstants;
import com.cyberscraft.uep.iam.dto.domain.BaseReturnResult;
import com.cyberscraft.uep.iam.dto.request.AppCategoryLinkVO;
import com.cyberscraft.uep.iam.dto.request.AppCategoryVO;
import io.swagger.annotations.*;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.cyberscraft.uep.common.constant.UEPSwaggerConstants.*;
import static org.springframework.web.bind.annotation.RequestMethod.*;

@RestController
@RequestMapping(value = UrlConstants.URL_PREFIX_API_VERSION + "/app", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(description = "App category 管理配置", tags = "AppCategory")
public interface AppCategoryApi {

    @RequestMapping(value = "/category", method = GET)
    @ResponseBody
    @ApiOperation(response = BaseReturnResult.class,
            value = "获取应用分类列表",
            notes = "获取应用分类列表",
            produces = "application/json")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true),
    })
    ReturnResultVO<List<AppCategoryVO>> getAppCategoryList(
            @ApiParam(value = "应用client_id", required = false)
            @RequestParam(value = "client_id", required = false) String client_id);

    @RequestMapping(value = "/category", method = POST)
    @ResponseBody
    @ApiOperation(response = BaseReturnResult.class,
            value = "新增应用分类",
            notes = "新增应用分类",
            produces = "application/json",
            authorizations = {@Authorization(value = "uc_auth", scopes = {@AuthorizationScope(scope = "create:apps", description = "")})})
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true),
    })
    ReturnResultVO<String> createAppCategory(@ApiParam(value = "应用信息", required = true) @RequestBody AppCategoryVO appCategoryVO);

    @RequestMapping(value = "/category", method = PUT)
    @ResponseBody
    @ApiOperation(response = BaseReturnResult.class,
            value = "批量修改应用分类",
            notes = "批量修改应用分类",
            produces = "application/json",
            authorizations = {@Authorization(value = "uc_auth", scopes = {@AuthorizationScope(scope = "update:apps", description = "")})})
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true),
    })
    ReturnResultVO<Boolean> batchReplaceAppCategory(@ApiParam(value = "多个应用信息", required = true) @RequestBody List<AppCategoryVO> appCategoryVOS);

    @RequestMapping(value = "/category/{id}", method = PATCH)
    @ResponseBody
    @ApiOperation(response = BaseReturnResult.class,
            value = "修改应用分类信息",
            notes = "修改应用分类信息",
            produces = "application/json",
            authorizations = {@Authorization(value = "uc_auth", scopes = {@AuthorizationScope(scope = "update:apps", description = "")})})
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true),
    })
    ReturnResultVO<Boolean> updateAppCategory(@ApiParam(value = "应用信息", required = true) @RequestBody AppCategoryVO appCategoryVO,
                                              @ApiParam(value = "应用分类ID", required = true) @PathVariable String id);

    @RequestMapping(value = "/category/{id}", method = DELETE)
    @ResponseBody
    @ApiOperation(response = BaseReturnResult.class,
            value = "删除应用分类",
            notes = "删除应用分类信息",
            produces = "application/json",
            authorizations = {@Authorization(value = "uc_auth", scopes = {@AuthorizationScope(scope = "delete:apps", description = "")})})
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true),
    })
    ReturnResultVO<Boolean> deleteAppCategory(@ApiParam(value = "应用分类ID", required = true) @PathVariable("id") String id);

    @RequestMapping(value = "/category/apps", method = POST)
    @ResponseBody
    @ApiOperation(response = BaseReturnResult.class,
            value = "批量将应用添加到分类中",
            notes = "批量将应用添加到分类中",
            produces = "application/json",
            authorizations = {@Authorization(value = "uc_auth", scopes = {@AuthorizationScope(scope = "update:apps", description = "")})})
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true),
    })
    ReturnResultVO<Boolean> addAppLinks(@ApiParam(value = "应用分类关联信息", required = true) @RequestBody List<AppCategoryLinkVO> linkVO);

    @RequestMapping(value = "/category/apps/{client_id}", method = PUT)
    @ResponseBody
    @ApiOperation(response = BaseReturnResult.class,
            value = "修改应用全部分类",
            notes = "修改应用全部分类",
            produces = "application/json",
            authorizations = {@Authorization(value = "uc_auth", scopes = {@AuthorizationScope(scope = "update:apps", description = "")})})
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true),
    })
    ReturnResultVO<Boolean> updateAppLinks(@PathVariable("client_id") String client_id, @ApiParam(value = "应用分类id", required = true) @RequestBody List<String> categoryIds);

    @RequestMapping(value = "/category/apps", method = DELETE)
    @ResponseBody
    @ApiOperation(response = BaseReturnResult.class,
            value = "批量将应用从分类中删除",
            notes = "批量将应用从分类中删除",
            produces = "application/json",
            authorizations = {@Authorization(value = "uc_auth", scopes = {@AuthorizationScope(scope = "update:apps", description = "")})})
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true),
    })
    ReturnResultVO<Boolean> delAppLinks(@ApiParam(value = "应用分类关联信息", required = true) @RequestBody List<AppCategoryLinkVO> linkVO);

}
