package com.cyberscraft.uep.iam.dto.request.login;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.HashSet;
import java.util.Set;

@ApiModel(value = "本地认证因子设置", description = "本地认证因子设置")
public class LocalAuthMethod {
    @ApiModelProperty(value = "是否启用密码认证因子", example = "true")
    private boolean pwdEnabled;

    @ApiModelProperty(value = "是否启用手机验证码认证因子", example = "true")
    private boolean smsEnabled;

    @ApiModelProperty(value = "是否启用邮箱验证码认证因子", example = "true")
    private boolean emailEnabled;

    @ApiModelProperty(value = "是否启用生物识别认证因子", example = "true")
    private boolean fido2Enabled;

    public boolean isPwdEnabled() {
        return pwdEnabled;
    }

    public void setPwdEnabled(boolean pwdEnabled) {
        this.pwdEnabled = pwdEnabled;
    }

    public boolean isSmsEnabled() {
        return smsEnabled;
    }

    public void setSmsEnabled(boolean smsEnabled) {
        this.smsEnabled = smsEnabled;
    }

    public boolean isEmailEnabled() {
        return emailEnabled;
    }

    public void setEmailEnabled(boolean emailEnabled) {
        this.emailEnabled = emailEnabled;
    }

    public boolean isFido2Enabled() {
        return fido2Enabled;
    }

    public void setFido2Enabled(boolean fido2Enabled) {
        this.fido2Enabled = fido2Enabled;
    }

    @JsonIgnore
    public Set<AuthenMethod> authenMethods(){
        Set<AuthenMethod> methods = new HashSet<>();
        if (pwdEnabled){
            methods.add(new AuthenMethod(AuthenType.PWD));
        }
        if (smsEnabled){
            methods.add(new AuthenMethod(AuthenType.SMS));
        }
        if (emailEnabled){
            methods.add(new AuthenMethod(AuthenType.EMAIL));
        }
        if (fido2Enabled){
            methods.add(new AuthenMethod(AuthenType.FIDO2));
        }
        return methods;
    }
}
