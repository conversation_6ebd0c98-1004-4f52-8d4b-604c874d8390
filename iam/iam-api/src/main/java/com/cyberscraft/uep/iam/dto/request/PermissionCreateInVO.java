package com.cyberscraft.uep.iam.dto.request;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.cyberscraft.uep.iam.constants.CommonConstants;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;

@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class PermissionCreateInVO extends PermissionUpdateInVO{
    @NotEmpty(message = "must NOT be empty")
    @ApiModelProperty(value = "权限的名称，权限名称加应用ID唯一标识一个权限", example = "create:organization", required = true)
    @Pattern(regexp = CommonConstants.URL_SAFE_CHARS_REG_STR, message="invalid name")
    private String name;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}
