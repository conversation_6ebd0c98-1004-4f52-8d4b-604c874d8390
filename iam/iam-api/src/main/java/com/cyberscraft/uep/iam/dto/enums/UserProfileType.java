package com.cyberscraft.uep.iam.dto.enums;

import java.util.HashMap;
import java.util.Map;

public enum UserProfileType  implements IBaseEnum {
    BUILTIN(0),
    NORMAL(1),
    ;

    private int value;

    private static final Map<String, UserProfileType> str2Enum = new HashMap();
     static {
        for (UserProfileType type : UserProfileType.values()) {
            str2Enum.put(type.name(), type);
        }
    }

    @Override
    public int getValue() {
        return value;
    }

    UserProfileType(int value) {
        this.value = value;
    }
    public static UserProfileType fromString(String symbol) {
        return str2Enum.get(symbol);
    }
}
