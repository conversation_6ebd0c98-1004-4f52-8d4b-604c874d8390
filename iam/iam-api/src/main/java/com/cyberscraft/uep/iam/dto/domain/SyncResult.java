package com.cyberscraft.uep.iam.dto.domain;

import com.cyberscraft.uep.iam.dto.enums.BaseServiceOpResult;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/11/29 3:50 下午
 */
public class SyncResult {

    private BaseServiceOpResult opResult;
    private String message;

    public SyncResult(BaseServiceOpResult opResult, String message) {
        this.opResult = opResult;
        this.message = message;
    }

    public SyncResult(BaseServiceOpResult opResult, Throwable throwable) {
        this.opResult = opResult;
        if (throwable instanceof NullPointerException) {
            this.message = "操作对象为空";
        } else {
            this.message = throwable.getMessage();
        }
    }

    public SyncResult(Throwable throwable) {
        this(BaseServiceOpResult.ERROR, throwable);
    }

    public SyncResult(String message) {
        this(BaseServiceOpResult.ERROR, message);
    }

    public SyncResult(BaseServiceOpResult opResult) {
        this.opResult = opResult;
    }

    public BaseServiceOpResult getOpResult() {
        return opResult;
    }

    public String getMessage() {
        return message;
    }
}
