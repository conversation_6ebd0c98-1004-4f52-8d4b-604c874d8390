package com.cyberscraft.uep.iam.dto.request.login;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.cyberscraft.uep.iam.dto.enums.Sender;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 多因子认证阶段中第二阶段认证需要的请求参数
 */
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@ApiModel(value = "MfaOtpRequestDTO", description = "多因子认证阶段中获取一次性密码需要的请求参数")
public class MfaOtpRequestDTO implements Serializable {
    private static final long serialVersionUID = -7888185701278035852L;
    @NotNull(message = "could not be null")
    @ApiModelProperty(name = "sendBy", value = "一次性密码发送途径，例如通过手机或者邮箱发送", required = true, example="SMS", allowableValues = "SMS, EMAIL")
    private Sender sendBy;

    @NotEmpty(message = "could not be empty")
    @ApiModelProperty(name = "clientId", value = "应用clientId", dataType = "String", required = true, example="usercenter")
    private String clientId;

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public Sender getSendBy() {
        return sendBy;
    }

    public void setSendBy(Sender sendBy) {
        this.sendBy = sendBy;
    }
}
