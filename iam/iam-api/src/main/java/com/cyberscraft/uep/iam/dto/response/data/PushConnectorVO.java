package com.cyberscraft.uep.iam.dto.response.data;

import com.cyberscraft.uep.iam.dto.enums.*;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Map;

/***
 *
 * @date 2021/6/8
 * <AUTHOR>
 ***/
public class PushConnectorVO implements Serializable {

    private static final long serialVersionUID = -8110415122452957434L;

    @ApiModelProperty(value = "连接器ID", required = true, dataType = "String", example = "1")
    @NotBlank(message = "could not be empty")
    private String id;

    @ApiModelProperty(value = "目标目录服务类型", required = true, dataType = "String", example = "AD")
    @NotNull(message = "could not be empty")
    private ConnectorTypeEnum type;

    @ApiModelProperty(value = "一次推送时删除用户数量限制", required = false, dataType = "int", example = "30")
    private Integer deleteLimit;

    @ApiModelProperty(value = "连接器名称", required = true, dataType = "String", example = "dingding")
    @NotBlank(message = "could not be empty")
    private String name;

    @ApiModelProperty(value = "外部根组织编码", dataType = "String", example = "1")
    private String rootCode = "1";

    @ApiModelProperty(value = "是否同步角色", hidden = true)
    private String roleApp;

    @ApiModelProperty(value = "同步的范围", hidden = true)
    private List<Long> roles;

    @ApiModelProperty(value = "连接器数据推送周期执行时间间隔", required = true, dataType = "int", example = "30", notes = "时间单位为: 分钟")
    @Min(0)
    @Max(10080) //最大值为7天
    private Integer pushPeriod = 0;


    @ApiModelProperty(value = "是否同步用户组", required = true, dataType = "boolean")
    @NotNull
    private Boolean pushOrgs;

    @ApiModelProperty(value = "链接器的状态", required = false, dataType = "String")
    private PushConnectorStatusEnum status;

    @ApiModelProperty(value = "连接器推送状态", required = true, dataType = "String", example = "AVAILABLE", notes = "AVAILABLE or PROCESSING")
    private ConnectorPushStatus pushStatus;

//    @ApiModelProperty(value = "映射字段", required = true, dataType = "domain")
//    @NotNull
//    @Valid
//    private String mapAttrs;
//
//    @ApiModelProperty(value = "组织结构映射字段", required = true, dataType = "domain")
//    private String mapOrgAttrs;

    @ApiModelProperty(value = "链接器账号配置", required = true, dataType = "String")
    private String configId;

    @ApiModelProperty(value = "是否强制同步到下游", required = true, dataType = "String")
    private Integer forceWrite;

    @ApiModelProperty(value = "链接器扩展配置", required = false, dataType = "domain")
    private Map<String, Object> extraConfig;

    @ApiModelProperty(value = "配置信息", required = false, dataType = "domain")
    private Map<String, Object> config;

    @ApiModelProperty(value = "链接器数据同步范围,多个组之间用，进行分隔", dataType = "String")
    private List<String> rootGroupId;

    @ApiModelProperty(value = "数据推送类型")
    private DataSyncTypeEnum syncType;

    private Integer pushType;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "最后修改人")
    private String updateBy;

    @ApiModelProperty(value = "最后的时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "最后的同步时间")
    private LocalDateTime pushTime;


    @ApiModelProperty(value = "最后的同步开始时间")
    private LocalDateTime pushStartTime;

    @ApiModelProperty(value = "最后的同步结束时间")
    private LocalDateTime pushEndTime;

    @ApiModelProperty(value = "开始时间")
    private LocalTime plannedStartTime;


    @ApiModelProperty(value = "下一同步时间")
    private LocalDateTime nextPushTime;

    @ApiModelProperty(value = "推送失败时，最大重试次数，默认值为3", required = true, dataType = "int", example = "3", notes = "重试次数")
    private Integer failureSendMaxTimes;

    @ApiModelProperty(value = "推送失败时，重试周期,单位为分钟", required = true, dataType = "int", example = "30", notes = "重试周期,单位为分钟")
    private Integer failureSendPeriod;

    @ApiModelProperty(value = "推送失败时，重试周期类型,FIXED,MULTIPLE", required = true, dataType = "domain", example = "FIXED", notes = "重试周期,单位为分钟")
    private DateTimePeriodTypeEnum failureSendPeriodType;

    @ApiModelProperty("网络代理组ID")
    private String proxyId;

    @ApiModelProperty("筛选配置json格式，用于筛选符合条件的对象")
    private Map<String, Object> filterConfig;

    @ApiModelProperty("是否开启过滤")
    private Boolean enableFilter;

    @ApiModelProperty("是否为自动化流")
    private int flow;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public ConnectorTypeEnum getType() {
        return type;
    }

    public void setType(ConnectorTypeEnum type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getDeleteLimit() {
        return deleteLimit;
    }

    public void setDeleteLimit(Integer deleteLimit) {
        this.deleteLimit = deleteLimit;
    }

    public Integer getPushPeriod() {
        return pushPeriod;
    }

    public void setPushPeriod(Integer pushPeriod) {
        this.pushPeriod = pushPeriod;
    }

    public Boolean getPushOrgs() {
        return pushOrgs;
    }

    public void setPushOrgs(Boolean pushOrgs) {
        this.pushOrgs = pushOrgs;
    }

    public PushConnectorStatusEnum getStatus() {
        return status;
    }

    public void setStatus(PushConnectorStatusEnum status) {
        this.status = status;
    }

//    public String getMapAttrs() {
//        return mapAttrs;
//    }
//
//    public void setMapAttrs(String mapAttrs) {
//        this.mapAttrs = mapAttrs;
//    }
//
//    public String getMapOrgAttrs() {
//        return mapOrgAttrs;
//    }
//
//    public void setMapOrgAttrs(String mapOrgAttrs) {
//        this.mapOrgAttrs = mapOrgAttrs;
//    }


    public ConnectorPushStatus getPushStatus() {
        return pushStatus;
    }

    public void setPushStatus(ConnectorPushStatus pushStatus) {
        this.pushStatus = pushStatus;
    }

    public String getRootCode() {
        return rootCode;
    }

    public void setRootCode(String rootCode) {
        this.rootCode = rootCode;
    }

    public String getRoleApp() {
        return roleApp;
    }

    public void setRoleApp(String roleApp) {
        this.roleApp = roleApp;
    }

    public List<Long> getRoles() {
        return roles;
    }

    public void setRoles(List<Long> roles) {
        this.roles = roles;
    }

    public String getConfigId() {
        return configId;
    }

    public void setConfigId(String configId) {
        this.configId = configId;
    }

    public Map<String, Object> getConfig() {
        return config;
    }

    public void setConfig(Map<String, Object> config) {
        this.config = config;
    }

    public Integer getForceWrite() {
        return forceWrite;
    }

    public void setForceWrite(Integer forceWrite) {
        this.forceWrite = forceWrite;
    }

    public LocalTime getPlannedStartTime() {
        return plannedStartTime;
    }

    public void setPlannedStartTime(LocalTime plannedStartTime) {
        this.plannedStartTime = plannedStartTime;
    }

    public Map<String, Object> getExtraConfig() {
        return extraConfig;
    }

    public void setExtraConfig(Map<String, Object> extraConfig) {
        this.extraConfig = extraConfig;
    }

    public List<String> getRootGroupId() {
        return rootGroupId;
    }

    public void setRootGroupId(List<String> rootGroupId) {
        this.rootGroupId = rootGroupId;
    }

    public DataSyncTypeEnum getSyncType() {
        return syncType;
    }

    public void setSyncType(DataSyncTypeEnum syncType) {
        this.syncType = syncType;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public LocalDateTime getPushTime() {
        return pushTime;
    }

    public void setPushTime(LocalDateTime pushTime) {
        this.pushTime = pushTime;
    }

    public LocalDateTime getNextPushTime() {
        return nextPushTime;
    }

    public void setNextPushTime(LocalDateTime nextPushTime) {
        this.nextPushTime = nextPushTime;
    }

    public Integer getFailureSendMaxTimes() {
        return failureSendMaxTimes;
    }

    public void setFailureSendMaxTimes(Integer failureSendMaxTimes) {
        this.failureSendMaxTimes = failureSendMaxTimes;
    }

    public Integer getFailureSendPeriod() {
        return failureSendPeriod;
    }

    public void setFailureSendPeriod(Integer failureSendPeriod) {
        this.failureSendPeriod = failureSendPeriod;
    }

    public DateTimePeriodTypeEnum getFailureSendPeriodType() {
        return failureSendPeriodType;
    }

    public void setFailureSendPeriodType(DateTimePeriodTypeEnum failureSendPeriodType) {
        this.failureSendPeriodType = failureSendPeriodType;
    }

    public LocalDateTime getPushStartTime() {
        return pushStartTime;
    }

    public void setPushStartTime(LocalDateTime pushStartTime) {
        this.pushStartTime = pushStartTime;
    }

    public LocalDateTime getPushEndTime() {
        return pushEndTime;
    }

    public void setPushEndTime(LocalDateTime pushEndTime) {
        this.pushEndTime = pushEndTime;
    }

    public String getProxyId() {
        return proxyId;
    }

    public void setProxyId(String proxyId) {
        this.proxyId = proxyId;
    }

    public Map<String, Object> getFilterConfig() {
        return filterConfig;
    }

    public void setFilterConfig(Map<String, Object> filterConfig) {
        this.filterConfig = filterConfig;
    }


    public Boolean getEnableFilter() {
        return enableFilter;
    }

    public void setEnableFilter(Boolean enableFilter) {
        this.enableFilter = enableFilter;
    }

    public Integer getPushType() {
        return pushType;
    }

    public void setPushType(Integer pushType) {
        this.pushType = pushType;
    }

    public int getFlow() {
        return flow;
    }

    public void setFlow(int flow) {
        this.flow = flow;
    }

    @Override
    public String toString() {
        return "PushConnectorVO{" +
                "id='" + id + '\'' +
                ", type=" + type +
                ", name='" + name + '\'' +
                ", pushPeriod=" + pushPeriod +
                ", status=" + status +
                ", configId='" + configId + '\'' +
                ", rootGroupId=" + rootGroupId +
                ", syncType=" + syncType +
                ", createBy='" + createBy + '\'' +
                ", createTime=" + createTime +
                ", updateBy='" + updateBy + '\'' +
                ", updateTime=" + updateTime +
                ", pushTime=" + pushTime +
                ", pushStartTime=" + pushStartTime +
                ", pushEndTime=" + pushEndTime +
                ", nextPushTime=" + nextPushTime +
                ", failureSendMaxTimes=" + failureSendMaxTimes +
                ", failureSendPeriod=" + failureSendPeriod +
                ", failureSendPeriodType=" + failureSendPeriodType +
                '}';
    }
}
