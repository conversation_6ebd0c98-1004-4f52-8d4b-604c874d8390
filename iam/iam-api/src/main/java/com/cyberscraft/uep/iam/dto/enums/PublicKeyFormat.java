package com.cyberscraft.uep.iam.dto.enums;

import java.util.*;

public enum PublicKeyFormat {
    JWK("JWK"),
    CERT("CERT"),
    PEM("PEM");

    private static final Map<String, PublicKeyFormat> code2Enum = new HashMap<String, PublicKeyFormat>();
    static {
        for (PublicKeyFormat type : PublicKeyFormat.values()) {
            code2Enum.put(type.value, type);
        }
    }

    PublicKeyFormat(String value) {
        this.value = value;
    }
    
    private String value;
    
    public String getValue() {
        return this.value;
    }
    
    public static final Set<PublicKeyFormat> KNOWN_VALUES;  // all values, except UNKNOWN

    static {
        EnumSet<PublicKeyFormat> kv = EnumSet.allOf(PublicKeyFormat.class);
        KNOWN_VALUES = Collections.unmodifiableSet(kv);
    }

    public static PublicKeyFormat fromString(String symbol) {
        return code2Enum.get(symbol);
    }
}