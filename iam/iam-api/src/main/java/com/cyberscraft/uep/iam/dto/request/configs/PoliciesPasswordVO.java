package com.cyberscraft.uep.iam.dto.request.configs;

import com.fasterxml.jackson.databind.PropertyNamingStrategy.SnakeCaseStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.collections4.CollectionUtils;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.util.Collections;
import java.util.List;

@JsonNaming(SnakeCaseStrategy.class)
public class PoliciesPasswordVO {

    @ApiModelProperty(value = "管理员能否重置用户密码. [true|false]，缺省值false", required = false, example = "false")
    private boolean adminResetUserPwd = false;

    @ApiModelProperty(value = "管理员重置密码后，第一次登录必须修改密码. [true|false]，缺省值true", required = false, example = "true")
    private boolean pwdMustChangeForAdminResetPwd = true;

    @ApiModelProperty(value = "管理员创建的用户，第一次登录必须修改密码. [true|false]，缺省值true.但是必须和pwd_must_change_for_admin_reset_pwd的值一致", required = false, example = "true")
    private boolean pwdMustChangeForAdminAddUser = true;

    //bug with: allowableValues = "range[0,7776000]"
    @ApiModelProperty(value = "密码最长有效期:0 长期有效，缺省值 0。建议不小于7天，因为密码过期的预警时间是7天。", required = false, example = "3650")
    @Min(value = 0, message = "pwdMaxAge >= 0")
    @Max(value = 3650, message = "pwdMaxAge <= 3650")
    private int pwdMaxAge = 0;

    @ApiModelProperty(value = "尝试失败次数锁定", required = false, example = "5")
    private Integer continuousFailureCount = 5;

    @ApiModelProperty(value = "锁定时长", required = false, example = "5")
    private Integer lockDuration = 5;

    @ApiModelProperty(value = "密码过期提醒", required = false, example = "5")
    private Integer pwdExpireWarning;

    @ApiModelProperty(value = "不锁定白名单", required = false, example = "null")
    private List<String> whiteList = Collections.EMPTY_LIST;

//	@JsonIgnore
//	@NotEmpty(message = "pwdExpireWarning can not be empty")
//	@ApiModelProperty(value = "从开始返回过期预警到真实过期的时间", required = true, allowableValues = "7 days", example="7 days")
//    private Integer pwdExpireWarning;

    @ApiModelProperty(value = "不能重复使用的旧密码的个数，range:0,[2,9]，缺省值0。如果取值0，则新密码没有历史限制，即使新密码和当前密码相同也可以；如果取值2，新密码不能和当前密码以及最近的一个旧密码相同。即有2个密码不可用;以此类推", required = false, example = "0")
    @Min(value = 0, message = "pwdInHistory >= 0")
    @Max(value = 15, message = "pwdInHistory <= 15")
    private int pwdInHistory = 0;

    @ApiModelProperty(value = "登录页是否开启记住密码. [true|false]，缺省值false", required = false, example = "false")
    private boolean loginPageRemember = false;

    public boolean isAdminResetUserPwd() {
        return adminResetUserPwd;
    }

    public void setAdminResetUserPwd(boolean adminResetUserPwd) {
        this.adminResetUserPwd = adminResetUserPwd;
    }

    public boolean isPwdMustChangeForAdminResetPwd() {
        return pwdMustChangeForAdminResetPwd;
    }

    public void setPwdMustChangeForAdminResetPwd(boolean pwdMustChange4AdminResetPwd) {
        this.pwdMustChangeForAdminResetPwd = pwdMustChange4AdminResetPwd;
    }

    public boolean isPwdMustChangeForAdminAddUser() {
        return pwdMustChangeForAdminAddUser;
    }

    public void setPwdMustChangeForAdminAddUser(boolean pwdMustChange4AdminAddUser) {
        this.pwdMustChangeForAdminAddUser = pwdMustChange4AdminAddUser;
    }

    public int getPwdMaxAge() {
        return pwdMaxAge;
    }

    public void setPwdMaxAge(int pwdMaxAge) {
        this.pwdMaxAge = pwdMaxAge;
    }

    public int getPwdInHistory() {
        return pwdInHistory;
    }

    public void setPwdInHistory(int pwdInHistory) {
        this.pwdInHistory = pwdInHistory;
    }

    public Integer getContinuousFailureCount() {
        return continuousFailureCount;
    }

    public void setContinuousFailureCount(Integer continuousFailureCount) {
        this.continuousFailureCount = continuousFailureCount;
    }

    public Integer getLockDuration() {
        return lockDuration;
    }

    public void setLockDuration(Integer lockDuration) {
        this.lockDuration = lockDuration;
    }

    public Integer getPwdExpireWarning() {
        return pwdExpireWarning;
    }

    public void setPwdExpireWarning(Integer pwdExpireWarning) {
        this.pwdExpireWarning = pwdExpireWarning;
    }

    public List<String> getWhiteList() {
        return whiteList;
    }

    public void setWhiteList(List<String> whiteList) {
        this.whiteList = whiteList;
    }

    public boolean isLoginPageRemember() {
        return loginPageRemember;
    }

    public void setLoginPageRemember(boolean loginPageRemember) {
        this.loginPageRemember = loginPageRemember;
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + (adminResetUserPwd ? 1231 : 1237);
        result = prime * result + pwdInHistory;
        result = prime * result + pwdMaxAge;
        result = prime * result + (pwdMustChangeForAdminAddUser ? 1231 : 1237);
        result = prime * result + (pwdMustChangeForAdminResetPwd ? 1231 : 1237);
        return result;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        PoliciesPasswordVO other = (PoliciesPasswordVO) obj;
        if (adminResetUserPwd != other.adminResetUserPwd) {
            return false;
        }
        if (pwdInHistory != other.pwdInHistory) {
            return false;
        }
        if (pwdMaxAge != other.pwdMaxAge) {
            return false;
        }
        if (pwdMustChangeForAdminAddUser != other.pwdMustChangeForAdminAddUser) {
            return false;
        }
        if (pwdMustChangeForAdminResetPwd != other.pwdMustChangeForAdminResetPwd) {
            return false;
        }
        if (continuousFailureCount == null) {
            if (other.continuousFailureCount != null) {
                return false;
            }
        } else if (!continuousFailureCount.equals(other.continuousFailureCount)) {
            return false;
        }
        if (pwdExpireWarning == null) {
            if (other.pwdExpireWarning != null) {
                return false;
            }
        } else if (!pwdExpireWarning.equals(other.pwdExpireWarning)) {
            return false;
        }
        if (lockDuration == null) {
            if (other.lockDuration != null) {
                return false;
            }
        } else if (!lockDuration.equals(other.lockDuration)) {
            return false;
        }
        if (whiteList == null) {
            if (other.whiteList != null) {
                return false;
            }
        } else if (!CollectionUtils.isEqualCollection(other.whiteList, whiteList)) {
            return false;
        }
        if (loginPageRemember != other.loginPageRemember) {
            return false;
        }
        return true;
    }

    @Override
    public String toString() {
        return "PoliciesPasswordVO [adminResetUserPwd=" + adminResetUserPwd + ", pwdMustChange4AdminResetPwd="
                + pwdMustChangeForAdminResetPwd + ", pwdMustChange4AdminAddUser=" + pwdMustChangeForAdminAddUser
                + ", pwdMaxAge=" + pwdMaxAge + ", pwdInHistory=" + pwdInHistory + ",continuousFailureCount" + continuousFailureCount + ",lockDuration" + lockDuration + ",pwdExpireWarning" + pwdExpireWarning + ",whiteList" + String.join(",", whiteList) + "]"
                + ", loginPageRemember" + loginPageRemember;
    }


}

