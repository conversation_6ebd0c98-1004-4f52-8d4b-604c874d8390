package com.cyberscraft.uep.iam.dto.domain;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.cyberscraft.uep.common.dto.QueryPage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "search_org_pageable_info_response",
        description = "response object for search orgs operations")
public class MapOrgGetPageableViewReturnResult extends BaseReturnResult {

    @ApiModelProperty(name = "data", value="返回的当前页的所有用户列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private QueryPage<CustomizeMapOrgGetDomain> data;

    public QueryPage<CustomizeMapOrgGetDomain> getData() {
        return data;
    }

    public void setData(QueryPage<CustomizeMapOrgGetDomain> data) {
        this.data = data;
    }
}
