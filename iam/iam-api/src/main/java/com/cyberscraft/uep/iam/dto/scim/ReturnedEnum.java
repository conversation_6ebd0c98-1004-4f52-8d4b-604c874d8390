package com.cyberscraft.uep.iam.dto.scim;

/**
 * <p>
 *  返回方式
 * </p>
 *
 * <AUTHOR>
 * @since 2022/4/27 12:15 下午
 */
public enum ReturnedEnum {
    //总是返回，忽略attributes参数
    ALWAYS("always"),
    //从不返回原值
    NEVER("never"),
    //有attributes参数则按attributes参数返回，无attributes参数时总是返回
    DEFAULT("default"),
    //请求里有该属性则返回，包括update该属性或attributes参数包含该属性
    REQUEST("request");

    private String value;
    private ReturnedEnum(String value){
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
