package com.cyberscraft.uep.iam.dto.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.cyberscraft.uep.iam.dto.constraint.as.AuthServerConstants;
import com.cyberscraft.uep.iam.dto.enums.ClientStatusV2;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Pattern.Flag;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@ApiModel(value = "active/inactive app", description = "This parameter is used for just the app status will be changed.")
public class AppStatusChangeRequestVO {
    @NotNull(message = "status is not null")
    //@Pattern(regexp = AuthServerConstants.REGEX_APP_STATUS, flags = { Flag.CASE_INSENSITIVE }, message = "active or inactive.")
    @ApiModelProperty(name = "status", value = "应用的新状态", required = true, example = "INACTIVE", allowableValues = "ACTIVE,INACTIVE")
    private ClientStatusV2 status;

    public AppStatusChangeRequestVO() {}

    public ClientStatusV2 getStatus() {
        return status;
    }

    public void setStatus(ClientStatusV2 status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "ChangeAppStatusVO [status=" + status + "]";
    }
}