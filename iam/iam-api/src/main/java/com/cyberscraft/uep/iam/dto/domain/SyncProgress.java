package com.cyberscraft.uep.iam.dto.domain;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/10/15 1:50 下午
 */
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class SyncProgress {
    /**
     * 进度起点
     */
    private int start;

    /**
     * 进度终点
     */
    private int end;

    /**
     * 本次数量
     */
    private int count;

    /**
     * 该任务跨度对应的数量
     */
    private int total;

    /**
     * 一次集成的统计
     */
    private SyncStats syncStats;

    public SyncProgress() {}

//    public SyncProgress(int start, int end, int total) {
//        this.start = start;
//        this.end = end;
//        this.count = 1;
//        this.total = total;
//    }

    public SyncProgress(int start, int end, int count, Integer total) {
        this.start = start;
        this.end = end;
        this.count = count;
        if (total == null) {
            total = 10000;
        }
        this.total = total;
    }

    public int getStart() {
        return start;
    }

    public void setStart(int start) {
        this.start = start;
    }

    public int getEnd() {
        return end;
    }

    public void setEnd(int end) {
        this.end = end;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public SyncStats getSyncStats() {
        return syncStats;
    }

    public void setSyncStats(SyncStats syncStats) {
        this.syncStats = syncStats;
    }

    @Override
    public String toString() {
        return "SyncProgress{" +
                "start=" + start +
                ", end=" + end +
                ", count=" + count +
                ", total=" + total +
                ", syncStats=" + syncStats +
                '}';
    }
}
