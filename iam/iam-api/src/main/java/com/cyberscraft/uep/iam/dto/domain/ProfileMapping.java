package com.cyberscraft.uep.iam.dto.domain;

import com.cyberscraft.uep.common.domain.ParamProfile;
import com.cyberscraft.uep.common.util.StringUtil;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/2/24 11:07 上午
 */
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class ProfileMapping implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 从app到iam的映射
     */
    private ParamProfile toIam;

    /**
     * 从iam到app的映射
     */
    private ParamProfile toApp;

    public ParamProfile getToIam() {
        return toIam;
    }

    public void setToIam(ParamProfile toIam) {
        this.toIam = toIam;
    }

    public ParamProfile getToApp() {
        return toApp;
    }

    public void setToApp(ParamProfile toApp) {
        this.toApp = toApp;
    }

    public boolean containExternalArgument(String userArg) {
        if (toApp == null) {
            return false;
        }
        List<ParamProfile> subParams = toApp.getSubParams();
        for (ParamProfile subParam : subParams) {
            String value = subParam.getValue();
            if (StringUtils.isNotBlank(value)) {
                boolean b = StringUtil.containsVariable(value, userArg);
                if (b) {
                    return true;
                }
            }
        }
        return false;
    }
}
