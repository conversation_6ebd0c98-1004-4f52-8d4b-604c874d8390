package com.cyberscraft.uep.iam.api;

import com.cyberscraft.uep.common.dto.BaseReturnResult;
import com.cyberscraft.uep.common.dto.QueryPage;
import com.cyberscraft.uep.common.dto.ReturnResultVO;
import com.cyberscraft.uep.iam.constants.UrlConstants;
import com.cyberscraft.uep.iam.dto.domain.MapUserGetListReturnResult;
import com.cyberscraft.uep.iam.dto.request.*;
import com.cyberscraft.uep.iam.dto.response.BatchOperationVO;
import io.swagger.annotations.*;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Map;

import static com.cyberscraft.uep.common.constant.UEPSwaggerConstants.*;
import static com.cyberscraft.uep.iam.constants.HttpStatus.*;
import static com.cyberscraft.uep.iam.errors.TransactionErrorDescription.*;

/**
 * <p>
 * 用户标签API
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2019-07-16 17:51
 */
@RestController
@RequestMapping(value = UrlConstants.URL_PREFIX_API_VERSION, produces = MediaType.APPLICATION_JSON_VALUE)
@Api(description = "用户标签管理配置", tags = "User Tag")
public interface TagApi {

    /********************************************************创建标签*********************************************************/
    @ResponseStatus(HttpStatus.CREATED)
    @RequestMapping(value = {"/tags"}, method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(nickname = "createTag",
            value = "创建标签",
            notes = "创建标签",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "create:tags", description = "")})})
    @ApiResponses(value = {
            @ApiResponse(code = SC_CONFLICT, response = BaseReturnResult.class,
                    message = "错误码: " + TAG_CONFLICT_ERROR_CODE + ", 错误消息: "
                            + TAG_CONFLICT_ERROR_DESC),
            @ApiResponse(code = SC_BAD_REQUEST, response = BaseReturnResult.class,
                    message = "* 错误码: " + INVALID_REQUEST_PARAM_ERROR_CODE + ", 错误消息: "
                            + INVALID_REQUEST_PARAM_ERROR_DESC
            )})
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String",
                    value = HEADER_REQUEST_CONTENT_TYPE,
                    defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
                    required = true)})
    ReturnResultVO<String> createTag(@Valid @RequestBody TagCreateInVO tagCreateInVO);

    /********************************************************更新标签*********************************************************/
    @RequestMapping(value = "/tags/{id}", method = {RequestMethod.PUT})
    @ResponseBody
    @ApiOperation(nickname = "updateTag",
            value = "更新标签",
            notes = "更新标签",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "update:tags", description = "")})})
    @ApiResponses(value = {
            @ApiResponse(code = SC_CONFLICT, response = BaseReturnResult.class,
                    message = "错误码: " + TAG_CONFLICT_ERROR_CODE + ", 错误消息: "
                            + TAG_CONFLICT_ERROR_DESC),
            @ApiResponse(code = SC_BAD_REQUEST, response = BaseReturnResult.class,
                    message = "* 错误码: " + INVALID_REQUEST_PARAM_ERROR_CODE + ", 错误消息: "
                            + INVALID_REQUEST_PARAM_ERROR_DESC
            )})
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String",
                    value = HEADER_REQUEST_CONTENT_TYPE,
                    defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
                    required = true)})
    ReturnResultVO<Void> updateTag(
            @ApiParam(required = true, value = "标签id", example = "123")
            @PathVariable("id") String id,
            @Valid @RequestBody TagUpdateInVO tagUpdateInVO);

    @ResponseStatus(HttpStatus.CREATED)
    @RequestMapping(value = {"/tags/importExcel"}, method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(nickname = "createTag",
            value = "导入Excel 创建标签并且添加用户",
            notes = "导入Excel 创建标签并且添加用户",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "create:tags", description = "")})})
    @ApiResponses(value = {
            @ApiResponse(code = SC_CONFLICT, response = BaseReturnResult.class,
                    message = "错误码: " + TAG_CONFLICT_ERROR_CODE + ", 错误消息: "
                            + TAG_CONFLICT_ERROR_DESC),
            @ApiResponse(code = SC_BAD_REQUEST, response = BaseReturnResult.class,
                    message = "* 错误码: " + INVALID_REQUEST_PARAM_ERROR_CODE + ", 错误消息: "
                            + INVALID_REQUEST_PARAM_ERROR_DESC
            )})
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String",
                    value = HEADER_REQUEST_CONTENT_TYPE,
                    defaultValue = HEADER_MULTIPART_FORM_DATA, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
                    required = true)})
    ReturnResultVO<Void> createTagWithExcel(
            HttpServletRequest request, CreateTagExcelDto createTagExcel);


    /********************************************************更新标签*********************************************************/
    @RequestMapping(value = "/tags/{id}", method = {RequestMethod.PATCH})
    @ResponseBody
    @ApiOperation(nickname = "updateTag",
            value = "更新标签",
            notes = "更新标签",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "update:tags", description = "")})})
    @ApiResponses(value = {
            @ApiResponse(code = SC_NOT_FOUND, response = BaseReturnResult.class,
                    message = "* 错误码: " + TAG_NOT_FOUND_ERROR_CODE + ", 错误消息: "
                            + TAG_NOT_FOUND_ERROR_DESC)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String",
                    value = HEADER_REQUEST_CONTENT_TYPE,
                    defaultValue = HEADER_MULTIPART_FORM_DATA, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
                    required = true)})
    ReturnResultVO<Void> updateTagWithExcel(
            HttpServletRequest request,
            @ApiParam(required = true, value = "标签id", example = "123")
            @PathVariable("id") String id,
            TagUpdateInVO tagUpdateInVO);


    /********************************************************删除标签*********************************************************/
    @RequestMapping(value = "/tags/{id}", method = RequestMethod.DELETE)
    @ResponseBody
    @ApiOperation(nickname = "deleteTag",
            value = "删除某个标签",
            notes = "删除某个标签",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "delete:tags", description = "")})})
    @ApiResponses(value = {
            @ApiResponse(code = SC_NOT_FOUND, response = BaseReturnResult.class,
                    message = "* 错误码: " + TAG_NOT_FOUND_ERROR_CODE + " , 错误消息:"
                            + TAG_NOT_FOUND_ERROR_DESC)
    })
    @ApiImplicitParams({@ApiImplicitParam(paramType = "header", name = "Authorization",
            dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
            required = true)})
    ReturnResultVO<TagVO> deleteTag(
            @ApiParam(required = true, value = "标签id", example = "123")
            @PathVariable("id") String id);


    /********************************************************查询标签详情*********************************************************/
    @RequestMapping(value = "/tags/{id}", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(nickname = "getTag",
            value = "获取某个标签详情",
            notes = "获取某个标签详情",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "read:tags", description = "")})})
    @ApiResponses(value = {
            @ApiResponse(code = SC_NOT_FOUND, response = BaseReturnResult.class,
                    message = "* 错误码: " + TAG_NOT_FOUND_ERROR_CODE + " , 错误消息:"
                            + TAG_NOT_FOUND_ERROR_DESC)
    })
    @ApiImplicitParams({@ApiImplicitParam(paramType = "header", name = "Authorization",
            dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
            required = true)})
    ReturnResultVO<TagVO> getTag(
            @ApiParam(required = true, value = "标签id", example = "123")
            @PathVariable("id") String id);


    @RequestMapping(value = "/tag/aes", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(nickname = "getTag",
            value = "标签信息加密",
            notes = "标签信息加密",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "read:tags", description = "")})})
    @ApiResponses(value = {
            @ApiResponse(code = SC_NOT_FOUND, response = BaseReturnResult.class,
                    message = "* 错误码: " + TAG_NOT_FOUND_ERROR_CODE + " , 错误消息:"
                            + TAG_NOT_FOUND_ERROR_DESC)
    })
    @ApiImplicitParams({@ApiImplicitParam(paramType = "header", name = "Authorization",
            dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
            required = true)})
    ReturnResultVO<String> tagAes(
            @ApiParam(required = true, value = "角色组Code", example = "123")
            @RequestParam("tag_code") String tagCode,
            @ApiParam(required = true, value = "用户ID", example = "123")
            @RequestParam("username") String username);


    /********************************************************获取标签列表*********************************************************/
    @RequestMapping(value = "/tags/search", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(nickname = "searchTags",
            value = "分页获取标签列表",
            notes = "可以获取所有权限列表，也可以通过对名称和描述进行搜索来获取满足条件的权限列表（搜索的标准是字符串的匹配）",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "search:tags", description = "")})})
    @ApiResponses(value = {@ApiResponse(code = SC_BAD_REQUEST, response = BaseReturnResult.class,
            message = "\n\r* 错误码: " + TOO_MANY_ENTRIES_ERROR_CODE
                    + ", 错误消息: " + TOO_MANY_ENTRIES_ERROR_DESC
    )})
    @ApiImplicitParams({@ApiImplicitParam(paramType = "header", name = "Authorization",
            dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
            required = true)})
    ReturnResultVO<QueryPage<TagVO>> searchTags(@RequestBody TagSearchInVO searchInVO);

    @RequestMapping(value = "/tags", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(nickname = "searchTagList",
            value = "获取标签列表",
            notes = "获取所有标签无查询条件",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "search:tags", description = "")})})
    @ApiResponses(value = {@ApiResponse(code = SC_BAD_REQUEST, response = BaseReturnResult.class,
            message = "\n\r* 错误码: " + TOO_MANY_ENTRIES_ERROR_CODE
                    + ", 错误消息: " + TOO_MANY_ENTRIES_ERROR_DESC
    )})
    @ApiImplicitParams({@ApiImplicitParam(paramType = "header", name = "Authorization",
            dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
            required = true)})
    ReturnResultVO<List<TagVO>> searchTagList();


    @RequestMapping(value = "/tags/searchByIds", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(nickname = "searchTagListByIds",
            value = "获取标签及对应的应用列表",
            notes = "获取标签及对应的应用列表",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "search:tags", description = "")})})
    @ApiResponses(value = {@ApiResponse(code = SC_BAD_REQUEST, response = ReturnResultVO.class,
            message = "\n\r* 错误码: " + TOO_MANY_ENTRIES_ERROR_CODE
                    + ", 错误消息: " + TOO_MANY_ENTRIES_ERROR_DESC
    )})
    @ApiImplicitParams({@ApiImplicitParam(paramType = "header", name = "Authorization",
            dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
            required = true)})
    ReturnResultVO<List<TagAndTagGroupVO>> searchTagListByIds(@ApiParam(value = "角色id列表，不能为空") @NotEmpty(message = "role ids must not be empty")
                                                              @RequestBody List<String> roleIds);

    @RequestMapping(value = "/tags/getTagList", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(nickname = "getTagListByGroupIdAndUserId",
            value = "获取应用列表根据应用id和用户id",
            notes = "获取应用列表根据应用id和用户id",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "search:tags", description = "")})})
    ReturnResultVO<List<TagVO>> getTagListByGroupIdAndUserId(
            @NotEmpty @RequestParam(value = "app_id", required = true) @ApiParam(
                    value = "应用id",
                    example = "123") String appId,
            @NotEmpty @RequestParam(value = "user_id", required = true) @ApiParam(
                    value = "用户id",
                    example = "123") String userId);

    @RequestMapping(value = "/tags/getTagList/user", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(nickname = "getTagAndGroupByUser",
            value = "获取用户的标签以及对应的应用信息",
            notes = "获取用户的标签以及对应的应用信息",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "search:tags", description = "")})})
    ReturnResultVO<List<TagAndTagGroupVO>> getTagByUserIdOrUsername(@ApiParam(required = false, value = "用户id", example = "123")
                                                                @RequestParam(value = "user_id", required = false) String uid,
                                                                @ApiParam(required = false, value = "用户名", example = "admin")
                                                                @RequestParam(value = "username", required = false) String username);


    /********************************************************查询标签所对应的用户列表*********************************************************/
    @RequestMapping(value = "/tags/{id}/users", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(response = MapUserGetListReturnResult.class, nickname = "listUsersInTag",
            value = "查看标签所对应的用户列表",
            notes = "查看标签所对应的用户列表，可针对用户登录名，全名，电子邮件，移动电话号码和状态属性，模糊查询用户。"
                    + "\n\r查询匹配的标准是字符串的子串匹配方式。"
                    + "\n\r**如果查询参数'q'没有指定，即使指定了返回用户列表的排序方式，API也会使用缺省的'username asc, name asc, email asc'来排序。** "
                    + "\n\r**注意: 即使在查询参数'attrs'中指定了敏感信息（如密码），API也不会返回。**",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "search:tag_users", description = "")})})
    @ApiResponses(value = {
            @ApiResponse(code = SC_BAD_REQUEST,
                    response = BaseReturnResult.class,
                    message = "* 错误码: " + USER_ATTR_UNKNOWN_ERROR_CODE + ", 错误消息: " + "Unknown user attribute name/Sort not supported"
                            + "\n\r* 错误码: " + PAGE_SIZE_EXCEED_MAX_ERROR_CODE + ", 错误消息: " + PAGE_SIZE_EXCEED_MAX_ERROR_DESC
                            + "\n\r* 错误码: " + TOO_MANY_ENTRIES_ERROR_CODE + ", 错误消息: " + TOO_MANY_ENTRIES_ERROR_DESC
            ),
            @ApiResponse(code = SC_NOT_FOUND,
                    response = BaseReturnResult.class,
                    message = "* 错误码: " + TAG_NOT_FOUND_ERROR_CODE + ", 错误消息: " + TAG_NOT_FOUND_ERROR_DESC)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
                    required = true)})
    ReturnResultVO<QueryPage<Map<String, Object>>> listTagUsers(
            @ApiParam(required = true, value = "标签id", example = "123")
            @PathVariable("id") String id,
            @RequestParam(value = "search_field", required = false)
            @ApiParam(value = "指定搜索的字段，用于限定搜索范围，如 'name'、'email' ", example = "name")
            String searchField,
            @NotEmpty @RequestParam(value = "filter", required = false) @ApiParam(
                    value = "作为过滤条件，在子串匹配时过滤用户登录名，全名，电子邮件和移动电话的属性的字符串",
                    example = "name1") String filter,

            @ApiParam(
                    value = "要获取的结果页号[1..N], 缺省是："
                            + QueryPage.DEFAULT_PAGE_NUMBER
                            + ". \n\r如果使用了无效的页号，API将使用缺省页号。", example = QueryPage.DEFAULT_PAGE_NUMBER + "")
            @RequestParam(value = "page", required = false) Integer page,

            @ApiParam(
                    value = "获取的每页的记录数[1..N], 缺省是："
                            + QueryPage.DEFAULT_PAGE_SIZE
                            + ". \n\r**每页允许的最大记录数为:" + QueryPage.MAX_PAGE_SIZE
                            + ". \n\r如果使用了无效的记录数，API将使用缺省记录数。", example = QueryPage.DEFAULT_PAGE_SIZE + "")
            @RequestParam(value = "size", required = false) Integer size,

            @ApiParam(
                    value = "排序标准的格式如下：'property1,(asc|desc)'"
                            + "\n\r多列排序也支持，例如： 'sort=property1,asc&sort=property2,desc'")
            @RequestParam(value = "sort", required = false) List<String> sort,
            @NotEmpty @RequestParam(value = "attrs", required = false) @ApiParam(
                    value = "需要返回的用户属性的列表，用逗号分隔，或者用格式类似于'attrs=sub&attrs=username'的方式来表示.\n\r如果不指定参数，所有有效的用户属性都会返回。"
                            + "\n\r**注意：即使用户没有一个属性是'orgs'，但是为了方便客户端的使用，这个额外的属性还是会被返回。**",
                    example = "sub, username, nickname") String... requestAttrs);


    /********************************************************给用户添加标签*********************************************************/
    @RequestMapping(value = "/tags/{id}/users", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(nickname = "AddUsersTag",
            value = "给多个用户添加静态标签",
            notes = "给多个用户添加静态标签",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "update:user_tags", description = "")})})
    @ApiResponses(value = {
            @ApiResponse(code = SC_BAD_REQUEST,
                    response = BaseReturnResult.class,
                    message = "* 错误码: " + INVALID_REQUEST_PARAM_ERROR_CODE + ", 错误消息: " + INVALID_REQUEST_PARAM_ERROR_DESC
            ),
            @ApiResponse(code = SC_NOT_FOUND,
                    response = BaseReturnResult.class,
                    message = "* 错误码: " + APP_NOT_FOUND_ERROR_CODE + ", 错误消息: " + APP_NOT_FOUND_ERROR_DESC)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
                    required = true)})
    ReturnResultVO<BatchOperationVO<String>> AddUsersTag(
            @ApiParam(required = true, value = "标签id", example = "123")
            @PathVariable("id") String id,
            @RequestBody @Valid AddUsersTagInVO addUsersTagInVO);

    /********************************************************给用户添加标签*********************************************************/
    @RequestMapping(value = "/createTagAndAddUsers", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(nickname = "createTagAndAddUsers",
            value = "创建标签并且添加用户",
            notes = "创建标签并且添加用户",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "update:user_tags", description = "")})})
    @ApiResponses(value = {
            @ApiResponse(code = SC_BAD_REQUEST,
                    response = BaseReturnResult.class,
                    message = "* 错误码: " + INVALID_REQUEST_PARAM_ERROR_CODE + ", 错误消息: " + INVALID_REQUEST_PARAM_ERROR_DESC
            ),
            @ApiResponse(code = SC_NOT_FOUND,
                    response = BaseReturnResult.class,
                    message = "* 错误码: " + APP_NOT_FOUND_ERROR_CODE + ", 错误消息: " + APP_NOT_FOUND_ERROR_DESC)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
                    required = true)})
    ReturnResultVO<BatchOperationVO<String>> createTagAndAddUsers(@RequestBody @Valid CreateTagDto tagCreateInVO);


    /********************************************************删除用户静态标签*********************************************************/
    @RequestMapping(value = "/tags/{id}/users", method = RequestMethod.DELETE)
    @ResponseBody
    @ApiOperation(nickname = "removeUserTag",
            value = "删除用户静态标签",
            notes = "删除用户静态标签",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "update:user_tags", description = "")})})
    @ApiResponses(value = {
            @ApiResponse(code = SC_BAD_REQUEST,
                    response = BaseReturnResult.class,
                    message = "* 错误码: " + INVALID_REQUEST_PARAM_ERROR_CODE + ", 错误消息: " + INVALID_REQUEST_PARAM_ERROR_DESC
            ),
            @ApiResponse(code = SC_NOT_FOUND,
                    response = BaseReturnResult.class,
                    message = "* 错误码: " + APP_NOT_FOUND_ERROR_CODE + ", 错误消息: " + APP_NOT_FOUND_ERROR_DESC)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
                    required = true)})
    ReturnResultVO<TagVO> RemoveUserTag(
            @ApiParam(required = true, value = "标签id", example = "123")
            @PathVariable("id") String id,
            @ApiParam(required = true, value = "用户名", example = "zhangsan")
            @NotEmpty @RequestParam(value = "username", required = true) String username);
}
