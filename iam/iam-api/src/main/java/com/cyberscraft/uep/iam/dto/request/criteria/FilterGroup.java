package com.cyberscraft.uep.iam.dto.request.criteria;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/4/22 17:37
 * @Version 1.0
 * @Description 筛选对象组，包含多个筛选基础遍历条件
 */
public class FilterGroup {

    /**
     * 条件与列表
     */
    private List<FilterItem> andList;

    public List<FilterItem> getAndList() {
        return andList;
    }

    public void setAndList(List<FilterItem> andList) {
        this.andList = andList;
    }


}
