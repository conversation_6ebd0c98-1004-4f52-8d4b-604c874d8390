package com.cyberscraft.uep.iam.dto.request;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import javax.validation.constraints.NotNull;
import java.util.List;

@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class UserAttrBatchCreateInVO {

	@NotNull(message = "Attribute list must NOT be empty")
	private List<UserAttrCreateInVO> extAttrs;

	public List<UserAttrCreateInVO> getExtAttrs() {
		return extAttrs;
	}

	public void setExtAttrs(List<UserAttrCreateInVO> extAttrs) {
		this.extAttrs = extAttrs;
	}

    @Override
    public String toString() {
        return "UserAttrBatchCreateInVO [extAttrs=" + extAttrs + "]";
    }
}
