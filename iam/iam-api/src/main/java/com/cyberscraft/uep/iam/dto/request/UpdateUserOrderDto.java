package com.cyberscraft.uep.iam.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2024/5/17 11:34
 * @Version 1.0
 * @Description 修改用户的排序值
 */
@ApiModel(value = "UpdateUserOrderDto", description = "To update the user order!")
public class UpdateUserOrderDto implements Serializable {

    @ApiModelProperty(value = "上一个用户id", required = false, dataType = "String", example = "1")
    private String previousUserId;


    @ApiModelProperty(value = "下一个用户id", required = false, dataType = "String", example = "3")
    private String nextUserId;

    @NotBlank(message = "user_id could not be empty")
    @ApiModelProperty(value = "当前被移动的用户id", required = true, dataType = "String", example = "3")
    private String userId;

    @NotBlank(message = "username could not be empty")
    @ApiModelProperty(value = "当前被移动的用户的用户名", required = true, dataType = "String", example = "3")
    private String username;

    @NotBlank(message = "org_id could not be empty")
    @ApiModelProperty(value = "当前被移动的用户所在部门id", required = true, dataType = "String", example = "3")
    private String orgId;

    @NotNull(message = "first_page could not be empty")
    @ApiModelProperty(value = "当前所在页是否为首页", required = true, dataType = "Boolean")
    private Boolean firstPage;

    @NotNull(message = "last_page could not be empty")
    @ApiModelProperty(value = "当前所在页是否为尾页", required = true, dataType = "Boolean")
    private Boolean lastPage;

    public String getPreviousUserId() {
        return previousUserId;
    }

    public void setPreviousUserId(String previousUserId) {
        this.previousUserId = previousUserId;
    }

    public String getNextUserId() {
        return nextUserId;
    }

    public void setNextUserId(String nextUserId) {
        this.nextUserId = nextUserId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getOrgId() {
        return orgId;
    }

    public void setOrgId(String orgId) {
        this.orgId = orgId;
    }

    public Boolean getFirstPage() {
        return firstPage;
    }

    public void setFirstPage(Boolean firstPage) {
        this.firstPage = firstPage;
    }

    public Boolean getLastPage() {
        return lastPage;
    }

    public void setLastPage(Boolean lastPage) {
        this.lastPage = lastPage;
    }
}
