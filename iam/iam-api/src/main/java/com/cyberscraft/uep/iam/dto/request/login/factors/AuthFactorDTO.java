package com.cyberscraft.uep.iam.dto.request.login.factors;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.cyberscraft.uep.iam.dto.request.login.AuthMethod;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@ApiModel(value = "auth_factor", description = "认证因子配置信息")
public class AuthFactorDTO {
    @NotNull
    private AuthMethod authMethod;
    @NotNull
    private Config config;

    public AuthMethod getAuthMethod() {
        return authMethod;
    }

    public void setAuthMethod(AuthMethod authMethod) {
        this.authMethod = authMethod;
    }

    public Config getConfig() {
        return config;
    }

    public void setConfig(Config config) {
        this.config = config;
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class Config {
        @ApiModelProperty(value = "当auth_method=PWD, 表示是否开启密码加密登录", example = "true")
        private Boolean secureLoginEnabled;

        @ApiModelProperty(value = "当auth_method=CERT, 表示用户通过需要软证书验证的app进行登陆时，每个用户可以上传的最大证书数", required = true, example = "5")
        private Integer count;

        @ApiModelProperty(
                value = "当auth_method=CERT时，表示用户软证书的有效期时长，单位为天，0表示永不过期。" +
                        "当auth_method=EMAIL时，表示邮箱验证的有效期，单位为分。" +
                        "当auth_method=SMS，表示短信验证的有效期，单位为分。",
                required = true, example = "90", reference = "aaaaaa")
        private Integer validity;

        @ApiModelProperty(value = "当authentication_method=CERT时，表示是否启用证书的轮换，即证书总数到达指定上限时，新上传的证书是否覆盖最不经常使用的证书", required = true, example = "true")
        private Boolean rotateEnabled;

        public void setSecureLoginEnabled(Boolean secureLoginEnabled) {
            this.secureLoginEnabled = secureLoginEnabled;
        }

        public void setCount(Integer count) {
            this.count = count;
        }

        public void setValidity(Integer validity) {
            this.validity = validity;
        }

        public void setRotateEnabled(Boolean rotateEnabled) {
            this.rotateEnabled = rotateEnabled;
        }

        public Boolean getSecureLoginEnabled() {
            return secureLoginEnabled;
        }

        public Integer getCount() {
            return count;
        }

        public Integer getValidity() {
            return validity;
        }

        public Boolean getRotateEnabled() {
            return rotateEnabled;
        }
    }
}
