package com.cyberscraft.uep.iam.dto.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.ArrayList;
import java.util.List;

@ApiModel(value = "get_sub_orgs_app_info_response", description = "response object for get sub orgs app info")
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class OrgAppInfoChildTreeOutVO extends OrgAppInfoOutVO {
    @ApiModelProperty(value="下级组列表", example=
            "["+
             "{"+
               "\"id\": \"63f3a979-9856-4aa8-96e4-596294d0c0c0\","+
               "\"name\": \"研发部\","+
               "\"app_enabled\": true,"+
               "\"children\": ["+
               "  {"+
                "   \"id\": \"1089e9ba-e7fd-4d68-872e-590e06693bc4\","+
                 "  \"name\": \"研发二部\","+
                  " \"app_enabled\": true,"+
                   "\"children\": []"+
                 "}"+
               "]"+
             "}"+
           "]")
    private List<OrgAppInfoChildTreeOutVO> children = new ArrayList<>();

    public List<OrgAppInfoChildTreeOutVO> getChildren() {
        return children;
    }

    public void setChildren(List<OrgAppInfoChildTreeOutVO> children) {
        this.children = children;
    }

    public void add(int index, OrgAppInfoChildTreeOutVO child) {
        if (children == null) {
            children = new ArrayList<OrgAppInfoChildTreeOutVO>();
        }
        children.add(index, child);
    }

    /**
     * Appends the child to the end of this node's child array. Sets the child's parent to this
     * node.
     *
     * @param child the child node to be appended under this node
     * @throws NullPointerException if the child is null
     * @throws IllegalArgumentException if the child is an ancestor of this node
     */
    public void add(OrgAppInfoChildTreeOutVO child) {
        add(getChildCount(), child);
    }

    @JsonIgnore
    public int getChildCount() {
        if (children == null) {
            return 0;
        } else {
            return children.size();
        }
    }

    @Override
    public String toString() {
        return "OrgAppInfoChildTreeOutVO [children=" + children.size() + "]";
    }
}
