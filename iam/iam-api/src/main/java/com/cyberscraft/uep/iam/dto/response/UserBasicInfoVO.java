package com.cyberscraft.uep.iam.dto.response;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import io.swagger.annotations.ApiModel;

@ApiModel(value = "User Basic Info", description = "This object describe the user basic info .")
@JsonNaming(value = PropertyNamingStrategy.class)
public class UserBasicInfoVO {
    /**
     * 用户ID
     */
    private Long id;
    /**
     * 用户名
     */
    private String username;
    /**
     * 姓名
     */
    private String name;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 移动电话号码
     */
    private String phoneNumber;
    /**
     * 用户状态
     */
    private Integer status;
    /**
     * 员工在组里的编号
     */
    private String userCodes;
    
    /**
     * 用户所属的组名称，以逗号分隔
     */
    private String orgNames;
    
    public Long getId() {
        return id;
    }
    public void setId(Long id) {
        this.id = id;
    }
    public String getUsername() {
        return username;
    }
    public void setUsername(String username) {
        this.username = username;
    }
    public String getName() {
        return name;
    }
    public void setName(String name) {
        this.name = name;
    }
    public String getEmail() {
        return email;
    }
    public void setEmail(String email) {
        this.email = email;
    }
    public String getPhoneNumber() {
        return phoneNumber;
    }
    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }
    public Integer getStatus() {
        return status;
    }
    public void setStatus(Integer status) {
        this.status = status;
    }
    public String getUserCodes() {
        return userCodes;
    }
    public void setUserCodes(String userCodes) {
        this.userCodes = userCodes;
    }
    public String getOrgNames() {
        return orgNames;
    }
    public void setOrgNames(String orgNames) {
        this.orgNames = orgNames;
    }
    
}
