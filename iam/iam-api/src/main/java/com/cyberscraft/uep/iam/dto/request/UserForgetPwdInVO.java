package com.cyberscraft.uep.iam.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;

@ApiModel(value = "forget_password_request", description = "request to trigger forget password action.")
public class UserForgetPwdInVO {

    @NotBlank
    @ApiModelProperty(name = "username", value = "用户登录名", dataType = "String", required = true, example="tiber")
    private String username;

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    @Override
    public String toString() {
        return "UserForgetPwdInVO [username=" + username + "]";
    }
}
