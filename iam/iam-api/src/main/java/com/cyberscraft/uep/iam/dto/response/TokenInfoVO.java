package com.cyberscraft.uep.iam.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;

public class TokenInfoVO {
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String accessTokenValue;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String refreshTokenValue;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String resetPasswordTokenValue;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String emailTokenValue;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String smsTokenValue;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer expireTime;

    public String getSmsTokenValue() {
        return smsTokenValue;
    }

    public void setSmsTokenValue(String smsTokenValue) {
        this.smsTokenValue = smsTokenValue;
    }

    public String getAccessTokenValue() {
        return accessTokenValue;
    }

    public void setAccessTokenValue(String accessTokenValue) {
        this.accessTokenValue = accessTokenValue;
    }

    public String getRefreshTokenValue() {
        return refreshTokenValue;
    }

    public void setRefreshTokenValue(String refreshTokenValue) {
        this.refreshTokenValue = refreshTokenValue;
    }

    public String getResetPasswordTokenValue() {
        return resetPasswordTokenValue;
    }

    public void setResetPasswordTokenValue(String resetPasswordTokenValue) {
        this.resetPasswordTokenValue = resetPasswordTokenValue;
    }

    public String getEmailTokenValue() {
        return emailTokenValue;
    }

    public void setEmailTokenValue(String emailTokenValue) {
        this.emailTokenValue = emailTokenValue;
    }

    public Integer getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Integer expireTime) {
        this.expireTime = expireTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("TokenInfoVO [");
        if (accessTokenValue != null){
            sb.append("accessTokenValue=*,");
        }
        if (refreshTokenValue != null){
            sb.append("refreshTokenValue=*,");
        }
        if ( resetPasswordTokenValue!= null){
            sb.append("resetPasswordTokenValue=*,");
        }
        if (emailTokenValue != null){
            sb.append("emailTokenValue=*,");
        }

        sb.append("]");

        return sb.toString();
    }
}
