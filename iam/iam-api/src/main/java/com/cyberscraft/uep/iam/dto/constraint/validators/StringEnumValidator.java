package com.cyberscraft.uep.iam.dto.constraint.validators;

import com.cyberscraft.uep.iam.dto.constraint.annotations.StringEnum;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

public class StringEnumValidator implements ConstraintValidator<StringEnum, String> {
    private Set<String> AVAILABLE_ENUM_NAMES;
    private StringEnum annotation;

    public static Set<String> getNamesSet(Class<? extends Enum<?>> e) {
        Enum<?>[] enums = e.getEnumConstants();
        String[] names = new String[enums.length];
        for (int i = 0; i < enums.length; i++) {
            names[i] = enums[i].name();
        }
        Set<String> mySet = new HashSet<String>(Arrays.asList(names));
        return mySet;
    }

    @Override public void initialize(StringEnum stringEnumeration) {
        this.annotation = stringEnumeration;
        Class<? extends Enum<?>> enumSelected = stringEnumeration.enumClass();
        AVAILABLE_ENUM_NAMES = getNamesSet(enumSelected);
    }

    @Override public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null) {
            return true;
        }

        boolean result = false;
        for (String enumValue : AVAILABLE_ENUM_NAMES) {
            if (value.equals(enumValue) || (this.annotation.ignoreCase() && value
                    .equalsIgnoreCase(enumValue))) {
                result = true;
                break;
            }
        }

        if (!result) {
            context.disableDefaultConstraintViolation();
            context.buildConstraintViolationWithTemplate(
                    String.format("Invalid value %s, allowable values are: %s", value,
                            AVAILABLE_ENUM_NAMES.toString())).addConstraintViolation();
        }
        return result;
    }
}
