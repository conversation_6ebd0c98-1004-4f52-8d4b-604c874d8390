package com.cyberscraft.uep.iam.dto.request.configs;

import com.fasterxml.jackson.databind.PropertyNamingStrategy.SnakeCaseStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;

import java.util.Map;

@JsonNaming(SnakeCaseStrategy.class)
@ApiModel(value = "sms_configs", 
description = "request object for update sms configs")
public class SmsConfigVO {
//	private SmsGatewayVO smsGateway;
	private Map<String,Object> smsGateway;

    public Map<String, Object> getSmsGateway() {
        return smsGateway;
    }

    public void setSmsGateway(Map<String, Object> smsGateway) {
        this.smsGateway = smsGateway;
    }

    @Override
    public String toString() {
        return "SmsConfigVO [smsGateway=" + smsGateway + "]";
    }
}
