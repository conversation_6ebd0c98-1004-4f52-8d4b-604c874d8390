package com.cyberscraft.uep.iam.dto.constraint.annotations;

import com.cyberscraft.uep.iam.dto.constraint.validators.ImportUserProfileValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

@Target({ElementType.TYPE, ElementType.ANNOTATION_TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = ImportUserProfileValidator.class)
@Documented
public @interface ImportUserProfile {
    String message() default "";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
