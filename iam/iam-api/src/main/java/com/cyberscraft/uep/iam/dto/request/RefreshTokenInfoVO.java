package com.cyberscraft.uep.iam.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;

@ApiModel(value = "RefreshTokenInfoVO", description = "Refresh token Info ")
public class RefreshTokenInfoVO {
    @NotEmpty(message = "could not be empty")
    @ApiModelProperty(name = "refresh_token", value = "用来换取access token的refresh token", dataType = "String", required = true, example="82ce33440f1a4169b34f71d4e5638fd1-101F9E46212FE7634CAD16B489C3B9C79B15B7FC77D7EF858FE40EA0423F75D1")
    @JsonProperty("refresh_token")
    private String refresh_token;

    public String getRefresh_token() {
        return refresh_token;
    }

    public void setRefresh_token(String refreshToken) {
        this.refresh_token = refreshToken;
    }

    @Override
    public String toString() {
        return "RefreshTokenInfoVO [refreshToken=*]";
    }
}
