package com.cyberscraft.uep.iam.dto.request.tenant;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Arrays;

@ApiModel(value = "DlpModule", description = "数据防泄漏模块信息")
public class DlpModule extends NormalModule implements Serializable {

    private static final long serialVersionUID = -3323583518508635166L;

    @ApiModelProperty(value = "SDk授权个数")
    private Integer sdkNum;

    public Integer getSdkNum() {
        return sdkNum;
    }

    public void setSdkNum(Integer sdkNum) {
        this.sdkNum = sdkNum;
    }

    @Override
    public String toString() {
        return "DlpModule{" +
                "sdkNum=" + sdkNum +
                "canUse=" + getCanUse() +
                "sdkNum=" + sdkNum +
                '}';
    }
}
