package com.cyberscraft.uep.iam.dto.request;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;

@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class BasicRoleInfoVO {
    @NotEmpty(message = "must NOT be empty")
    @ApiModelProperty(value = "角色的名称", example = "ROLE_ADMIN", required = true)
    private String name;

    @ApiModelProperty(value = "角色显示名，一般为角色的短描述", example = "用户管理员")
    private String displayName;

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
