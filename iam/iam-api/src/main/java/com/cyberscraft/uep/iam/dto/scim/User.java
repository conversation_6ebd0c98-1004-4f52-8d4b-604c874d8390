package com.cyberscraft.uep.iam.dto.scim;

import com.fasterxml.jackson.annotation.JsonProperty;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * SCIM用户对象实体
 * </p>
 *
 * <AUTHOR>
 * @since 2022/4/28 2:41 下午
 */
public class User extends Resource {

    private String userName;

    private String displayName;

    private String nickName;

    private String password;

    private List<MultiValueAttribute> emails;

    private List<MultiValueAttribute> phoneNumbers;

    @JsonProperty(ScimConstant.ENTERPRISE_USER_SCHEMA)
    private UserExtension userExtension;

    @Override
    public List<String> getSchemas() {
        List<String> schemas = new ArrayList<>();
        schemas.add(ResourceEnum.Users.getSchema());
        if (userExtension != null) {
            schemas.add(ResourceEnum.EnterpriseUser.getSchema());
        }
        return schemas;
    }

    @Override
    public String getExternalId() {
        String externalId = super.getExternalId();
        if (StringUtils.isBlank(externalId)) {
            return getUserName();
        }
        return externalId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public UserExtension getUserExtension() {
        return userExtension;
    }

    public void setUserExtension(UserExtension userExtension) {
        this.userExtension = userExtension;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public List<MultiValueAttribute> getEmails() {
        return emails;
    }

    public void setEmails(List<MultiValueAttribute> emails) {
        this.emails = emails;
    }

    public List<MultiValueAttribute> getPhoneNumbers() {
        return phoneNumbers;
    }

    public void setPhoneNumbers(List<MultiValueAttribute> phoneNumbers) {
        this.phoneNumbers = phoneNumbers;
    }
}
