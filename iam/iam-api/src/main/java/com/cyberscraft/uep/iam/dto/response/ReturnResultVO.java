package com.cyberscraft.uep.iam.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.cyberscraft.uep.common.dto.BaseReturnResult;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value="response")
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class ReturnResultVO<T> extends BaseReturnResult {

    @ApiModelProperty(name = "data", value="如有必要，可以返回更加具体的信息")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private T data;

    public ReturnResultVO() {
        super();
    }

    public ReturnResultVO(T data){
        super();
        this.data = data;
    }

    public ReturnResultVO(Boolean result, String message, int transactionErrorCode){
        super(result, message, transactionErrorCode);
    }

    public ReturnResultVO(Boolean result, String message, int transactionErrorCode, T errorData){
        super(result, message, transactionErrorCode);
        this.data = errorData;
    }

    public T getData() {
        return data;
    }

    public void setData(T contents) {
        this.data = contents;
    }

    @Override
    public String toString() {
        return "ReturnResultVO [result=" + this.getResult() + ", error_description=" + this.getErrorDescription() + ", error=" + this.getError() + ", data="
                + data + "]";
    }
}
