package com.cyberscraft.uep.iam.api;

import com.cyberscraft.uep.common.bean.Result;
import com.cyberscraft.uep.common.dto.BaseReturnResult;
import com.cyberscraft.uep.common.dto.QueryPage;
import com.cyberscraft.uep.common.dto.ReturnResultVO;
import com.cyberscraft.uep.iam.constants.UrlConstants;
import com.cyberscraft.uep.iam.dto.domain.MapUserCreateReturnResult;
import com.cyberscraft.uep.iam.dto.domain.MapUserGetListReturnResult;
import com.cyberscraft.uep.iam.dto.domain.ReturnResultUsersRevokedList;
import com.cyberscraft.uep.iam.dto.enums.UserStatus;
import com.cyberscraft.uep.iam.dto.request.*;
import com.cyberscraft.uep.iam.dto.request.login.BasicAuthDTO;
import com.cyberscraft.uep.iam.dto.request.login.RegisterDTO;
import com.cyberscraft.uep.iam.dto.response.ExcelUserVO;
import com.cyberscraft.uep.iam.dto.response.UserExtendFieldVo;
import com.cyberscraft.uep.iam.dto.response.UserVO;
import com.cyberscraft.uep.iam.dto.response.UsersRevokedVO;
import io.swagger.annotations.*;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.cyberscraft.uep.common.constant.UEPSwaggerConstants.*;
import static com.cyberscraft.uep.iam.constants.HttpStatus.*;
import static com.cyberscraft.uep.iam.errors.TransactionErrorDescription.*;
import static org.springframework.web.bind.annotation.RequestMethod.*;

/**
 * <p>
 * 用户相关API
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2019-08-05 15:25
 */
@RestController
@RequestMapping(value = UrlConstants.URL_PREFIX_API_VERSION + "/users", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(description = "用户管理", tags = "User")
public interface UserApi {


    @RequestMapping(method = POST)
    @ApiOperation(response = MapUserCreateReturnResult.class, value = "管理员创建新用户", notes = "管理员创建新用户。\n\r**Notes:**用户的组/部门属性可以是默认组/缺省组，但不能是根组", produces = "application/json",
            authorizations = {
                    @Authorization(value = "uc_auth",
                            scopes = {
                                    @AuthorizationScope(scope = "create:users", description = "")
                            }
                    )
            })
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true),
            @ApiImplicitParam(dataType = "map_user_create_request_domain", name = "userInfo", paramType = "body", value = "用来创建新用户的属性值", required = true)
    })
    @ApiResponses(value = {
            @ApiResponse(code = SC_BAD_REQUEST, response = BaseReturnResult.class, message = "错误码: " + USER_ATTR_MISSED_ERROR_CODE + ", 错误消息: " + USER_ATTR_MISSED_ERROR_DESC
                    + "\n\r " + "错误码: " + USER_ATTR_VALUE_INVALID_ERROR_CODE + ", 错误消息: " + USER_ATTR_VALUE_INVALID_ERROR_DESC
                    + "\n\r " + "错误码: " + USER_ALREADY_EXISTS_ERROR_CODE + ", 错误消息: " + USER_ALREADY_EXISTS_ERROR_DESC)
    })
    ReturnResultVO<Map<String, Object>> createUser(
            @ApiIgnore @RequestBody Map<String, Object> userInfo,
            @NotEmpty @RequestParam(value = "welcome", defaultValue = "false", required = false)
            @ApiParam(value = "是否发送welcome邮件", example = "true", allowableValues = "true,false", defaultValue = "false") Boolean welcome);


    @RequestMapping(value = "/register", method = POST)
    @ApiOperation(response = MapUserCreateReturnResult.class, value = "微信注册新用户", notes = "新用户注册。\n\r**Notes:**用户的组/部门属性可以是默认组/缺省组，但不能是根组", produces = "application/json")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true)
    })
    @ApiResponses(value = {
            @ApiResponse(code = SC_BAD_REQUEST, response = BaseReturnResult.class, message = "错误码: " + USER_ATTR_MISSED_ERROR_CODE + ", 错误消息: " + USER_ATTR_MISSED_ERROR_DESC
                    + "\n\r " + "错误码: " + USER_ATTR_VALUE_INVALID_ERROR_CODE + ", 错误消息: " + USER_ATTR_VALUE_INVALID_ERROR_DESC
                    + "\n\r " + "错误码: " + USER_ALREADY_EXISTS_ERROR_CODE + ", 错误消息: " + USER_ALREADY_EXISTS_ERROR_DESC)
    })
    ReturnResultVO<Map<String, Object>> register(@Valid @RequestBody RegisterDTO registerDTO);


    @RequestMapping(value = "/check/delete", method = {POST})
    @ResponseBody
    @ApiOperation(value = "检查删除的数据", notes = "检查删除的数据。", produces = "application/json",
            authorizations = {
                    @Authorization(value = "uc_auth",
                            scopes = {
                                    @AuthorizationScope(scope = "delete:users", description = "")
                            }
                    )
            })
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true)
    })
    ReturnResultVO<Boolean> checkDeletedUsers();

    @RequestMapping(value = "/delete", method = {POST})
    @ResponseBody
    @ApiOperation(value = "删除一个或多个用户", notes = "删除一个或多个用户。\n\r注意：当操作部分成功时，返回结果中的data值格式类似：{\"msg\": \"[\"John\", \"Mike\"] delete failed!}\"", produces = "application/json",
            authorizations = {
                    @Authorization(value = "uc_auth",
                            scopes = {
                                    @AuthorizationScope(scope = "delete:users", description = "")
                            }
                    )
            })
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true)
    })
    ReturnResultVO<Map<String, Object>> deleteUsers(@ApiParam(value = "用户名列表") @RequestBody @Valid UserDeleteVO users);


    @RequestMapping(method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(response = MapUserGetListReturnResult.class, nickname = "searchUserWithFuzzyWord",
            value = "模糊查询用户",
            notes = "针对用户登录名，全名，电子邮件，移动电话号码和状态属性，模糊查询用户。"
                    + "\n\r查询匹配的标准是字符串的子串匹配方式。"
                    + "\n\r**如果查询参数'q'没有指定，即使指定了返回用户列表的排序方式，API也会使用缺省的'username asc, name asc, email asc'来排序。** "
                    + "\n\r**注意: 即使在查询参数'attrs'中指定了敏感信息（如密码），API也不会返回。**",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "search:users", description = "")})})
    @ApiResponses(value = {@ApiResponse(code = SC_BAD_REQUEST,
            response = BaseReturnResult.class,
            message = "* 错误码: "
                    + USER_ATTR_UNKNOWN_ERROR_CODE + ", 错误消息: " + "Unknown user attribute name/Sort not supported"
                    + "\n\r* 错误码: " + PAGE_SIZE_EXCEED_MAX_ERROR_CODE
                    + ", 错误消息: " + PAGE_SIZE_EXCEED_MAX_ERROR_DESC
                    + "\n\r* 错误码: " + TOO_MANY_ENTRIES_ERROR_CODE
                    + ", 错误消息: " + TOO_MANY_ENTRIES_ERROR_DESC
                    + "\n\r* 错误码: " + USER_STATUS_INVALID_ERROR_CODE
                    + ", 错误消息: " + USER_STATUS_INVALID_ERROR_DESC)})
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
                    required = true)})
    ReturnResultVO<QueryPage<Map<String, Object>>> searchUserWithFuzzyWord(
            @NotEmpty @RequestParam(value = "q", required = false) @ApiParam(
                    value = "作为过滤条件，在子串匹配时过滤用户登录名，全名，电子邮件和移动电话的属性的字符串",
                    example = "name1") String filter,
            @RequestParam(value = "status", required = false) @ApiParam(
                    value = "用户状态作为查询用户的过滤条件（可选的）", example = "ACTIVE",
                    allowableValues = "ACTIVE,INACTIVE") UserStatus status,
            @NotEmpty @RequestParam(value = "org_id", required = false) @ApiParam(required = false,
                    value = "组/部门的id, 查询的所有用户都必须（直接或间接地）属于该组/部门。"
                            + "\n\r为了查询不属于任何组/部门的用户，请使用'_null'；"
                            + "\n\r为了查询整个根组下的所有用户，可以忽略该参数，或者使用'_root'") String org_id,
            @NotEmpty @RequestParam(value = "return_users_in_sub_org", defaultValue = "true") @ApiParam(required = false,
                    value = "是否查询子组/子部门下的用户："
                            + "\n\r如果true，则查询子组/子部门下的用户，"
                            + "\n\r如果false,则不查询子组/子部门下的用户") Boolean includeUsersInSubOrgs,
            @ApiParam(
                    value = "要获取的结果页号[1..N], 缺省是："
                            + QueryPage.DEFAULT_PAGE_NUMBER
                            + ". \n\r如果使用了无效的页号，API将使用缺省页号。", example = QueryPage.DEFAULT_PAGE_NUMBER + "")
            @RequestParam(value = "page", required = false) Integer pageNumber,

            @ApiParam(
                    value = "获取的每页的记录数[1..N], 缺省是："
                            + QueryPage.DEFAULT_PAGE_SIZE
                            + ". \n\r**每页允许的最大users记录数为:" + QueryPage.MAX_PAGE_SIZE
                            + ". \n\r如果使用了无效的记录数，API将使用缺省记录数。", example = QueryPage.DEFAULT_PAGE_SIZE + "")
            @RequestParam(value = "size", required = false) Integer pageSize,

            @ApiParam(
                    value = "排序标准的格式如下：'property1:(asc|desc)'"
                            + "\n\r多列排序也支持，例如： 'sort=property1:asc&sort=property2:desc'")
            @RequestParam(value = "sort", required = false) List<String> sort,

            @NotEmpty @RequestParam(value = "attrs", required = false) @ApiParam(
                    value = "需要返回的用户属性的列表，用逗号分隔，或者用格式类似于'attrs=sub&attrs=username'的方式来表示.\n\r如果不指定参数，所有有效的用户属性都会返回。"
                            + "\n\r**注意：即使用户没有一个属性是'orgs'，但是为了方便客户端的使用，这个额外的属性还是会被返回。**",
                    example = "sub, username, nickname") Set<String> requestAttrs);

    @RequestMapping(value = "/search", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(response = MapUserGetListReturnResult.class, nickname = "searchUserWithFuzzyWord",
            value = "模糊查询用户",
            notes = "针对用户登录名，全名，电子邮件，移动电话号码和状态属性，模糊查询用户。"
                    + "\n\r查询匹配的标准是字符串的子串匹配方式。"
                    + "\n\r**如果查询参数'q'没有指定，即使指定了返回用户列表的排序方式，API也会使用缺省的'username asc, name asc, email asc'来排序。** "
                    + "\n\r**注意: 即使在查询参数'attrs'中指定了敏感信息（如密码），API也不会返回。**",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "search:users", description = "")})})
    @ApiResponses(value = {@ApiResponse(code = SC_BAD_REQUEST,
            response = BaseReturnResult.class,
            message = "* 错误码: "
                    + USER_ATTR_UNKNOWN_ERROR_CODE + ", 错误消息: " + "Unknown user attribute name/Sort not supported"
                    + "\n\r* 错误码: " + PAGE_SIZE_EXCEED_MAX_ERROR_CODE
                    + ", 错误消息: " + PAGE_SIZE_EXCEED_MAX_ERROR_DESC
                    + "\n\r* 错误码: " + TOO_MANY_ENTRIES_ERROR_CODE
                    + ", 错误消息: " + TOO_MANY_ENTRIES_ERROR_DESC
                    + "\n\r* 错误码: " + USER_STATUS_INVALID_ERROR_CODE
                    + ", 错误消息: " + USER_STATUS_INVALID_ERROR_DESC)})
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
                    required = true)})
    ReturnResultVO<QueryPage<Map<String, Object>>> searchUserWithFuzzyWord(
            @RequestBody UserSearchDto userSearchDto
    );

    @RequestMapping(value = "/getExtendField", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(response = MapUserGetListReturnResult.class, nickname = "查询用户测扩展字段",
            value = "查询用户测扩展字段",
            notes = "查询用户测扩展字段",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "search:users", description = "")})})
    @ApiResponses(value = {@ApiResponse(code = SC_BAD_REQUEST,
            response = BaseReturnResult.class,
            message = "* 错误码: "
                    + USER_ATTR_UNKNOWN_ERROR_CODE + ", 错误消息: " + "Unknown user attribute name/Sort not supported"
                    + "\n\r* 错误码: " + PAGE_SIZE_EXCEED_MAX_ERROR_CODE
                    + ", 错误消息: " + PAGE_SIZE_EXCEED_MAX_ERROR_DESC
                    + "\n\r* 错误码: " + TOO_MANY_ENTRIES_ERROR_CODE
                    + ", 错误消息: " + TOO_MANY_ENTRIES_ERROR_DESC
                    + "\n\r* 错误码: " + USER_STATUS_INVALID_ERROR_CODE
                    + ", 错误消息: " + USER_STATUS_INVALID_ERROR_DESC)})
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
                    required = true)})
    ReturnResultVO<List<UserExtendFieldVo>> getExtendField();

    @RequestMapping(value = "/searchByIds", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(response = UserVO.class, nickname = "searchUserByIds",
            value = "根据id查询用户",
            notes = "根据id查询用户",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "search:users", description = "")})})
    @ApiResponses(value = {@ApiResponse(code = SC_BAD_REQUEST,
            response = BaseReturnResult.class,
            message = "* 错误码: "
                    + USER_ATTR_UNKNOWN_ERROR_CODE + ", 错误消息: " + "Unknown user attribute name/Sort not supported"
                    + "\n\r* 错误码: " + PAGE_SIZE_EXCEED_MAX_ERROR_CODE
                    + ", 错误消息: " + PAGE_SIZE_EXCEED_MAX_ERROR_DESC
                    + "\n\r* 错误码: " + TOO_MANY_ENTRIES_ERROR_CODE
                    + ", 错误消息: " + TOO_MANY_ENTRIES_ERROR_DESC
                    + "\n\r* 错误码: " + USER_STATUS_INVALID_ERROR_CODE
                    + ", 错误消息: " + USER_STATUS_INVALID_ERROR_DESC)})
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
                    required = true)})
    ReturnResultVO<List<Map<String, Object>>> searchUserByIds(@ApiParam(value = "用户id列表") @NotEmpty @RequestBody List<String> userIds);

    @RequestMapping(value = "/queryUserInfo", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(response = MapUserGetListReturnResult.class, nickname = "searchUserWithInfo",
            value = "模糊查询用户",
            notes = "针对用户登录名，全名，电子邮件，移动电话号码和状态属性，模糊查询用户。"
                    + "\n\r查询匹配的标准是字符串的子串匹配方式。"
                    + "\n\r**如果查询参数'q'没有指定，即使指定了返回用户列表的排序方式，API也会使用缺省的'username asc, name asc, email asc'来排序。** "
                    + "\n\r**注意: 即使在查询参数'attrs'中指定了敏感信息（如密码），API也不会返回。**",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "search:users", description = "")})})
    @ApiResponses(value = {@ApiResponse(code = SC_BAD_REQUEST,
            response = BaseReturnResult.class,
            message = "* 错误码: "
                    + USER_ATTR_UNKNOWN_ERROR_CODE + ", 错误消息: " + "Unknown user attribute name/Sort not supported"
                    + "\n\r* 错误码: " + PAGE_SIZE_EXCEED_MAX_ERROR_CODE
                    + ", 错误消息: " + PAGE_SIZE_EXCEED_MAX_ERROR_DESC
                    + "\n\r* 错误码: " + TOO_MANY_ENTRIES_ERROR_CODE
                    + ", 错误消息: " + TOO_MANY_ENTRIES_ERROR_DESC
                    + "\n\r* 错误码: " + USER_STATUS_INVALID_ERROR_CODE
                    + ", 错误消息: " + USER_STATUS_INVALID_ERROR_DESC)})
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
                    required = true)})
    ReturnResultVO<List<UserVO>> searchUserWithInfo(
            @RequestParam(value = "q", required = false) @ApiParam(
                    value = "作为过滤条件，在子串匹配时过滤用户登录名，全名，电子邮件和移动电话的属性的字符串",
                    example = "name1") String filter);


    /**
     * incremental search by username/name/email prefix, (starts with search)
     */
    @RequestMapping(method = RequestMethod.GET, params = {"m=inc"})
    @ResponseBody
    @ApiOperation(value = "incsearch user", hidden = true)
    ReturnResultVO<List<Map<String, Object>>> incsearch(
            @NotEmpty @RequestParam(value = "q", required = false) String keyword,
            @NotEmpty @RequestParam(value = "返回的匹配用户数量", required = false, defaultValue = "10") int limit,
            @NotEmpty @RequestParam(value = "attrs", required = false) String... attrs);

    @RequestMapping(value = "/mapSearch", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "search user ", hidden = true)
    ReturnResultVO<List<Map<String, Object>>> search(
            @NotEmpty @RequestBody() Map<String, Object> queryBody,
            @NotEmpty @RequestParam(value = "attrs", required = false) String... attrs);


    @RequestMapping(value = "/userOfPush", method = POST)
    @ResponseBody
    @ApiOperation(value = "incsearch user", hidden = true)
    ReturnResultVO<String> checkUserOfPush(@ApiIgnore @Valid @RequestBody UserUpdateDto userUpdateDto);

    @RequestMapping(
            value = "/verify",
            method = RequestMethod.POST,
            consumes = MediaType.APPLICATION_JSON_UTF8_VALUE
    )
    @ResponseBody
    @ApiOperation(nickname = "VerifyUserAccount", value = "验证用户的鉴权信息", response = BaseReturnResult.class,
            notes = "验证用户登录名和密码",
            authorizations = {@Authorization(value = "uc_auth", scopes = {})})
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = "application/json;charset=UTF-8", required = true)
    })
    @ApiResponses(value = {
            @ApiResponse(code = SC_BAD_REQUEST, response = BaseReturnResult.class,
                    message = "错误码: " + INVALID_REQUEST_PARAM_ERROR_CODE + ", 错误消息: " + "username: could not be empty" + "\n\r" +
                            "错误码: " + INVALID_REQUEST_PARAM_ERROR_CODE + ", 错误消息: " + "password: could not be empty" + "\n\r" +
                            "错误码: " + USER_NAME_PASSWORD_INCONSISTENT_ERROR_CODE + ", 错误消息: " + USER_NAME_PASSWORD_INCONSISTENT_ERROR_DESC + "\n\r" +
                            "错误码: " + USER_NOT_FOUND_ERROR_CODE + ", 错误消息: " + USER_NOT_FOUND_ERROR_DESC + "\n\r" +
                            "错误码: " + LDAP_CHANGE_AFTER_RESET_ERROR_CODE + ", 错误消息: " + LDAP_CHANGE_AFTER_RESET_ERROR_DESC + "\n\r" +
                            "错误码: " + LDAP_PASSWORD_EXPIRED_ERROR_CODE + ", 错误消息: " + LDAP_PASSWORD_EXPIRED_ERROR_DESC),
            @ApiResponse(code = SC_OK, response = BaseReturnResult.class,
                    message = "错误码: 0, 错误消息: SUCCESS\n\r" +
                            "错误码: " + LDAP_TIME_BEFORE_EXPIRATION_ERROR_CODE + ", 错误消息: " + LDAP_TIME_BEFORE_EXPIRATION_ERROR_DESC)})
    ReturnResultVO<?> verifyUserAccount(@Valid @RequestBody @ApiParam(name = "AuthenticationInfo",
            value = "验证用户的鉴权信息请求包含的参数") BasicAuthDTO loginInfoVO);


    @ApiOperation(nickname = "changeUserStatus", value = "改变用户的状态",
            notes = "改变用户的状态。\n\r注意：如果部分用户的状态修改失败，则返回结果的message字段，类似：\"[af243edb-c011-4b7b-a50a-632d042abddd] does not exist\"",
            authorizations = {@Authorization(value = "uc_auth", scopes = {})})
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true),
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = "application/json;charset=UTF-8", required = true)
    })
    @ApiResponses(value =
            {
                    @ApiResponse(code = SC_BAD_REQUEST, response = BaseReturnResult.class,
                            message = "错误码: " + INVALID_REQUEST_PARAM_ERROR_CODE + ", 错误消息: status: status is not null/uids: uids is not null" + "\n\r" +
                                    "错误码: " + USER_ADMIN_NO_STATUS_CHANGE_ERROR_CODE + ", 错误消息: " + USER_ADMIN_NO_STATUS_CHANGE_ERROR_DESC)
            })
    @RequestMapping(value = "/status", method = RequestMethod.PUT, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    ReturnResultVO<?> changeUserStatus(@ApiParam(value = "新的用户状态") @Validated @RequestBody ChangeUserStatusVO status);


    @ApiOperation(nickname = "changeUserPWD", value = "管理员重置用户密码",
            notes = "管理员重置用户密码。",
            authorizations = {@Authorization(value = "uc_auth", scopes = {})})
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true),
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = "application/json;charset=UTF-8", required = true)
    })
    @ApiResponses(value =
            {
                    @ApiResponse(code = SC_BAD_REQUEST, response = BaseReturnResult.class,
                            message = "错误码: " + USER_PWD_CAN_NOT_RESET_ERROR_CODE + ", 错误消息: " + USER_PWD_CAN_NOT_RESET_ERROR_DESC
                                    + "\n\r错误码: " + INVALID_REQUEST_PARAM_ERROR_CODE + ", 错误消息: " + INVALID_REQUEST_PARAM_ERROR_DESC
                                    + "\n\r错误码: " + LDAP_PASSWORD_IN_HISTORY_ERROR_CODE + ", 错误消息: " + LDAP_PASSWORD_IN_HISTORY_ERROR_DESC
                                    + "\n\r错误码: " + USER_PASSWORD_ERROR_CODE + ", 错误消息: " + USER_PASSWORD_ERROR_DESC
                                    + "\n\r错误码: " + USER_PWD_STATUS_ERROR_CODE + ", 错误消息: " + USER_PWD_STATUS_ERROR_DESC
                                    + "\n\r错误码: " + USER_NOT_FOUND_ERROR_CODE + ", 错误消息: " + USER_NOT_FOUND_ERROR_DESC
                                    + "\n\r错误码: " + USER_READ_ONLY_CANNOT_MODIFY_ERROR_CODE + ", 错误消息: " + USER_READ_ONLY_CANNOT_MODIFY_ERROR_DESC
                    )
            })
    @RequestMapping(value = "/password", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ResponseBody
    ReturnResultVO<?> resetUserPWD(@RequestBody @Valid ChangePasswordByAdminVO passwordByAdminVO);

    @RequestMapping(value = "/importUser", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(nickname = "createExcelUser",
            value = "通过Excel文件导入用户",
            notes = "通过Excel文件导入用户",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "create:users", description = "")})})
    @ApiResponses(value = {
            @ApiResponse(code = SC_BAD_REQUEST, response = BaseReturnResult.class, message = "错误码: " + USER_ATTR_MISSED_ERROR_CODE + ", 错误消息: " + USER_ATTR_MISSED_ERROR_DESC
                    + "\n\r " + "错误码: " + USER_ATTR_VALUE_INVALID_ERROR_CODE + ", 错误消息: " + USER_ATTR_VALUE_INVALID_ERROR_DESC
                    + "\n\r " + "错误码: " + USER_ALREADY_EXISTS_ERROR_CODE + ", 错误消息: " + USER_ALREADY_EXISTS_ERROR_DESC)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String",
                    value = HEADER_REQUEST_CONTENT_TYPE,
                    defaultValue = HEADER_MULTIPART_FORM_DATA, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
                    required = true)})
    ReturnResultVO<ImportUserHistoryVO> importUser(HttpServletRequest request, CreateUserExcelDto createUserExcelDto);

    @RequestMapping(value = "/import/userSchema", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(nickname = "userSchema",
            value = "用户导入的属性信息",
            notes = "用户导入的属性信息",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "query:fail_user", description = "")})})
    @ApiResponses(value = {
            @ApiResponse(code = SC_BAD_REQUEST, response = BaseReturnResult.class, message = "错误码: " + USER_ATTR_MISSED_ERROR_CODE + ", 错误消息: " + USER_ATTR_MISSED_ERROR_DESC
                    + "\n\r " + "错误码: " + USER_ATTR_VALUE_INVALID_ERROR_CODE + ", 错误消息: " + USER_ATTR_VALUE_INVALID_ERROR_DESC
                    + "\n\r " + "错误码: " + USER_ALREADY_EXISTS_ERROR_CODE + ", 错误消息: " + USER_ALREADY_EXISTS_ERROR_DESC)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String",
                    value = HEADER_REQUEST_CONTENT_TYPE,
                    defaultValue = HEADER_MULTIPART_FORM_DATA, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
                    required = true)})
    ReturnResultVO<List<Map<String, String>>> getImportUserSchema();

    @RequestMapping(value = "/import/user", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(nickname = "importUserInfo",
            value = "分页查询导入信息",
            notes = "分页查询导入信息",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "query:fail_user", description = "")})})
    @ApiResponses(value = {
            @ApiResponse(code = SC_BAD_REQUEST, response = BaseReturnResult.class, message = "错误码: " + USER_ATTR_MISSED_ERROR_CODE + ", 错误消息: " + USER_ATTR_MISSED_ERROR_DESC
                    + "\n\r " + "错误码: " + USER_ATTR_VALUE_INVALID_ERROR_CODE + ", 错误消息: " + USER_ATTR_VALUE_INVALID_ERROR_DESC
                    + "\n\r " + "错误码: " + USER_ALREADY_EXISTS_ERROR_CODE + ", 错误消息: " + USER_ALREADY_EXISTS_ERROR_DESC)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String",
                    value = HEADER_REQUEST_CONTENT_TYPE,
                    defaultValue = HEADER_MULTIPART_FORM_DATA, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
                    required = true)})
    ReturnResultVO<List<ExcelUserVO>> getImportUserInfo(@ApiParam("保存是否成功") @RequestParam("saveStatus") Integer saveStatus,
                                                        @ApiParam("批次号") @RequestParam("batchNo") String batchNo);

    @RequestMapping(value = "/apply/user", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(nickname = "createUser",
            value = "创建用户的审批信息",
            notes = "创建用户的审批信息",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "query:fail_user", description = "")})})
    @ApiResponses(value = {
            @ApiResponse(code = SC_BAD_REQUEST, response = BaseReturnResult.class, message = "错误码: " + USER_ATTR_MISSED_ERROR_CODE + ", 错误消息: " + USER_ATTR_MISSED_ERROR_DESC
                    + "\n\r " + "错误码: " + USER_ATTR_VALUE_INVALID_ERROR_CODE + ", 错误消息: " + USER_ATTR_VALUE_INVALID_ERROR_DESC
                    + "\n\r " + "错误码: " + USER_ALREADY_EXISTS_ERROR_CODE + ", 错误消息: " + USER_ALREADY_EXISTS_ERROR_DESC)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
                    required = true)})
    ReturnResultVO<Map<String, Object>> getPersonalUserInfo(@ApiParam("个人审批Id") @RequestParam("id") String id);

    @RequestMapping(value = "/expire/user", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(nickname = "expireUser",
            value = "查看即将到期用户",
            notes = "分页查询即将到期用户信息",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "query:fail_user", description = "")})})
    @ApiResponses(value = {
            @ApiResponse(code = SC_BAD_REQUEST, response = BaseReturnResult.class, message = "错误码: " + USER_ATTR_MISSED_ERROR_CODE + ", 错误消息: " + USER_ATTR_MISSED_ERROR_DESC
                    + "\n\r " + "错误码: " + USER_ATTR_VALUE_INVALID_ERROR_CODE + ", 错误消息: " + USER_ATTR_VALUE_INVALID_ERROR_DESC
                    + "\n\r " + "错误码: " + USER_ALREADY_EXISTS_ERROR_CODE + ", 错误消息: " + USER_ALREADY_EXISTS_ERROR_DESC)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
                    required = true)})
    ReturnResultVO<List<Map<String, Object>>> getExpireUserInfo(@ApiParam("发送工作通知记录Id") @RequestParam("id") String id);

    @RequestMapping(value = "/importTemplate", method = GET)
    @ResponseBody
    @ApiOperation(nickname = "importTemplate",
            value = "生成用户导入模版",
            notes = "生成用户导入模版")
    void importTemplate(HttpServletResponse response);


    @ApiOperation(nickname = "export_users",
            value = "根据过滤条件导出用户",
            notes = "根据过滤条件导出用户"
                    + "\n\r**注意：即使在查询参数'attrs'中，指定了敏感信息（如密码），API也不会返回这样的属性值**",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "export:users", description = "")
            })})
    @ApiResponses(value = {@ApiResponse(code = SC_BAD_REQUEST, response = BaseReturnResult.class,
            message = "错误码: " + USER_ATTR_UNKNOWN_ERROR_CODE + ", 错误消息: "
                    + "Unknown user attribute name/Sort not supported")})
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true),
    })
    @RequestMapping(value = "/export", method = RequestMethod.POST)
    void exportUsers(HttpServletResponse response,
                     @NotEmpty @RequestParam(value = "filename", required = false, defaultValue = "users") @ApiParam(
                             value = "用来保存导出用户的csv文件名称",
                             example = "users") String fileName,
                     @RequestBody UserSearchDto userSearchDto) throws IOException;

    @ApiOperation(nickname = "export_users_count_by_status",
            value = "按照状态统计用户数",
            notes = "按照状态统计用户数",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "export:users", description = "")
            })})
    @ApiResponses(value = {@ApiResponse(code = SC_BAD_REQUEST, response = BaseReturnResult.class,
            message = "错误码: " + USER_ATTR_UNKNOWN_ERROR_CODE + ", 错误消息: "
                    + "Unknown user attribute name/Sort not supported")})
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true),
    })
    @RequestMapping(value = "/getCountByStatus", method = RequestMethod.GET)
    Result<Map<String, Object>> exportUserCountByStatus(
            @RequestParam(value = "startTime", required = false) Long startTime,
            @RequestParam(value = "endTime", required = false) Long endTime);

    @ApiOperation(nickname = "export_users_by_status",
            value = "按状态导出对应用户",
            notes = "按状态导出对应用户",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "export:users", description = "")
            })})
    @ApiResponses(value = {@ApiResponse(code = SC_BAD_REQUEST, response = BaseReturnResult.class,
            message = "错误码: " + USER_ATTR_UNKNOWN_ERROR_CODE + ", 错误消息: "
                    + "Unknown user attribute name/Sort not supported")})
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true),
    })
    @RequestMapping(value = "/exportByStatus", method = RequestMethod.GET)
    void exportUserByStatus(HttpServletResponse response,
                            @RequestParam(value = "filename", required = false, defaultValue = "数犀集成平台用户数据统计表") String fileName,
                            @RequestParam(value = "status", required = false) List<String> status,
                            @RequestParam(value = "startTime", required = false) Long startTime,
                            @RequestParam(value = "endTime", required = false) Long endTime) throws IOException;


    @ApiOperation(nickname = "import_history_users_export",
            value = "根据批次号导出该批导入的用户列表信息",
            notes = "根据批次号导出该批导入的用户列表信息",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "export:users", description = "")
            })})
    @ApiResponses(value = {@ApiResponse(code = SC_BAD_REQUEST, response = BaseReturnResult.class,
            message = "* 错误码: " + USER_ATTR_UNKNOWN_ERROR_CODE + " , 错误消息: "
                    + "Unknown user attribute name/Sort not supported")})
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true),
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = "application/json;charset=UTF-8", required = true)
    })
    @RequestMapping(value = "/importHistory/export/{taskId}", method = RequestMethod.GET)
    void exportImportUsers(HttpServletResponse response,
                           @ApiParam("导入任务批次号") @PathVariable("taskId") String taskId,
                           @ApiParam("批量导入的状态 0 不合格数据 1 合格数据 2 导入成功 3 导入失败") @RequestParam(value = "status", required = false) List<Integer> status);


    @ApiOperation(nickname = "active invitation user",
            value = "激活邀请未使用过的用户",
            notes = "激活邀请未使用过的用户",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "export:users", description = "")
            })})
    @ApiResponses(value = {@ApiResponse(code = SC_BAD_REQUEST, response = BaseReturnResult.class,
            message = "* 错误码: " + USER_ATTR_UNKNOWN_ERROR_CODE + " , 错误消息: "
                    + "Unknown user attribute name/Sort not supported")})
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true),
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = "application/json;charset=UTF-8", required = true)
    })
    @RequestMapping(value = "/active/invitation", method = POST)
    Result<Integer> userActiveInvitation(@RequestBody ActiveUserDto activeUser);

    @ApiOperation(nickname = "active invitation user",
            value = "获取未激活的用户数",
            notes = "获取未激活的用户数",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "export:users", description = "")
            })})
    @ApiResponses(value = {@ApiResponse(code = SC_BAD_REQUEST, response = BaseReturnResult.class,
            message = "* 错误码: " + USER_ATTR_UNKNOWN_ERROR_CODE + " , 错误消息: "
                    + "Unknown user attribute name/Sort not supported")})
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true),
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = "application/json;charset=UTF-8", required = true)
    })
    @RequestMapping(value = "/noActiveCount", method = GET)
    Result<Integer> getNoActiveCount();

    @ApiOperation(nickname = "active invitation user",
            value = "导入的用户激活",
            notes = "导入的用户激活",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "export:users", description = "")
            })})
    @ApiResponses(value = {@ApiResponse(code = SC_BAD_REQUEST, response = BaseReturnResult.class,
            message = "* 错误码: " + USER_ATTR_UNKNOWN_ERROR_CODE + " , 错误消息: "
                    + "Unknown user attribute name/Sort not supported")})
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true),
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = "application/json;charset=UTF-8", required = true)
    })
    @RequestMapping(value = "/import/active/{taskId}", method = POST)
    Result<Boolean> userImportActive(@ApiParam("导入任务批次号") @PathVariable("taskId") Long taskId);

    @RequestMapping(value = "/revoked", method = GET)
    @ResponseBody
    @ApiOperation(response = ReturnResultUsersRevokedList.class, nickname = "getUsersRevoked",
            value = "获取revoked的用户列表",
            notes = "获取revoked的用户列表",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "search:users", description = "")})})
    @ApiResponses(value = {
            @ApiResponse(code = SC_INTERNAL_SERVER_ERROR,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + UNKNOWN_DAO_ERROR_CODE + ", 错误消息: " + UNKNOWN_DAO_ERROR_DESC)
    })
    /**
     * Temporarily, without authentication
     */
//    @ApiImplicitParams({
//            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
//                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
//                    required = true)})
//    @PreAuthorize("#oauth2.hasPermission('search:users')")
    public ReturnResultVO<UsersRevokedVO> getUsersRevoked(
            @RequestParam(value = "timestamp", required = false) @ApiParam(
                    value = "时间戳信息，是距离1970-01-01 00:00:00 UTC的秒数，" +
                            "表示想要查询在此时间以后revoke的用户列表", example = "1511493330"
            ) Long timestamp);

    @RequestMapping(method = PATCH, value = "/update/order")
    @ApiOperation(value = "管理员移动用户进行排序", notes = "管理员移动用户进行排序。", produces = "application/json",
            authorizations = {
                    @Authorization(value = "uc_auth",
                            scopes = {
                                    @AuthorizationScope(scope = "update:users", description = "")
                            }
                    )
            })
    @ApiResponses(value = {
            @ApiResponse(code = SC_BAD_REQUEST, response = BaseReturnResult.class, message = "错误码: " + USER_ATTR_MISSED_ERROR_CODE + ", 错误消息: " + USER_ATTR_MISSED_ERROR_DESC
                    + "\n\r " + "错误码: " + USER_ATTR_VALUE_INVALID_ERROR_CODE + ", 错误消息: " + USER_ATTR_VALUE_INVALID_ERROR_DESC
                    + "\n\r " + "错误码: " + USER_ALREADY_EXISTS_ERROR_CODE + ", 错误消息: " + USER_ALREADY_EXISTS_ERROR_DESC)
    })
    ReturnResultVO<Boolean> updateUserOrder(@RequestBody @Valid UpdateUserOrderDto userOrderDto);

    @GetMapping(value = "/userTotal")
    ReturnResultVO<Integer> userTotal();
}
