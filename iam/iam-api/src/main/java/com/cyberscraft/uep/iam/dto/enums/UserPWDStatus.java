package com.cyberscraft.uep.iam.dto.enums;


import java.util.EnumSet;

/**
 * UserPWDStatus
 *
 * <AUTHOR>
 * @date 2021/11/3
 */
public enum UserPWDStatus implements IBaseEnum{
    TEMP(1),NORMAL(2), EXPIRED(3), ADMIN_LOCKED(4), ERR_LOCKED(5);

    private int value;
    UserPWDStatus(int value){
        this.value = value;
    }

    @Override
    public int getValue() {
        return value;
    }

    public static final EnumSet<UserPWDStatus> canChange = EnumSet.of(TEMP, NORMAL);

    /**
     * to check if it's an valid status string.
     * @param status
     * @return
     */
    public static UserPWDStatus toEnum(int status) {
        for (UserPWDStatus s : UserPWDStatus.values()) {
           if(s.value == status) {
               return s;
           }
        }
        return null;
    }

    public static boolean needsToChangePassword(Integer status) {
        return UserPWDStatus.TEMP.value == status;
    }
}