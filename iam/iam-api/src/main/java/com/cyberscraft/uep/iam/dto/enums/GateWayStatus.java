package com.cyberscraft.uep.iam.dto.enums;

public enum GateWayStatus implements IBaseEnum{
    ACTIVE(1), INACTIVE(0);

    private int value;

    private GateWayStatus(int value) {
        this.value =  value;
    }

    @Override
    public int getValue() {
        return value;
    }

    public static GateWayStatus fromStringValue(String value){
        for (GateWayStatus status : GateWayStatus.values()) {
            if (String.valueOf(status.getValue()).equals(value)) {
                return status;
            }
        }
        return null;
    }
}
