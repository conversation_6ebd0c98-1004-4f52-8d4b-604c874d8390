package com.cyberscraft.uep.iam.dto.scim;

/**
 * <p>
 *  scim message schema uri
 * </p>
 *
 * <AUTHOR>
 * @since 2022/4/26 5:46 下午
 */
public enum MessagesEnum {
    ListResponse("urn:ietf:params:scim:api:messages:2.0:ListResponse"),
    SearchRequest("urn:ietf:params:scim:api:messages:2.0:SearchRequest"),
    PatchOp("urn:ietf:params:scim:api:messages:2.0:PatchOp"),
    BulkRequest("urn:ietf:params:scim:api:messages:2.0:BulkRequest"),
    BulkResponse("urn:ietf:params:scim:api:messages:2.0:BulkResponse"),
    Error("urn:ietf:params:scim:api:messages:2.0:Error");

    private final String schema;
    private MessagesEnum(String schema){
        this.schema = schema;
    }

    public String getSchema() {
        return schema;
    }
}
