package com.cyberscraft.uep.iam.dto.request;

import com.cyberscraft.uep.common.dto.IQueryDto;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDateTime;
import java.util.List;

public class DingAuditSyncDto implements IQueryDto {

    private static final long serialVersionUID = 1L;

    /**
     * 输入框过滤字段
     */
    @ApiModelProperty(value = "输入框过滤字段")
    private String filter;

    /**
     * 组织来源
     */
    @ApiModelProperty(value = "组织来源")
    private String orgName;

    /**
     * 操作人员
     */
    @ApiModelProperty(value = "操作来源")
    private String operatorName;

    /**
     * 接收人员
     */
    @ApiModelProperty(value = "接收人员")
    private String receiverName;

    /**
     * 操作类型
     */
    @ApiModelProperty(value = "操作类型：上传(0)、删除(1)、下载(2)、预览(3)、覆盖(4)、创建外链分享(5) 、重命名(6) 、移动(7)、 复制或转发(8)")
    private Integer action;

    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件类型")
    private String resourceExtension;

    /**
     * 终端类型
     */
    @ApiModelProperty(value = "终端类型： IOS(0)、ANDROID(1)、WEB(2)、SYS(4)、DINGDING(8)、WIN(11)、MAC(12)")
    private Long platform;

    /**
     * 文件类型集合 查询条件转换
     */
    private List<String> resourceExtensions;

    /**
     * 文件所属类型
     */
    @ApiModelProperty(value = "文件所属类型: 单聊(0)、群聊(1)、钉盘(2)")
    private Integer receiverType;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private Long startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private Long endTime;

    /**
     * 页数
     */
    @ApiModelProperty(value = "要获取的结果页号[1..N], 缺省是：1", example = "1")
    private Integer page;

    /**
     * 每页大小
     */
    @ApiModelProperty(value = "获取的每一页的记录数[1..N], 缺省是:50, **每页允许的最大记录数为:100", example = "50")
    private Integer size;

    public String getFilter() {
        return filter;
    }

    public void setFilter(String filter) {
        this.filter = filter;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public String getReceiverName() {
        return receiverName;
    }

    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }

    public Integer getAction() {
        return action;
    }

    public void setAction(Integer action) {
        this.action = action;
    }

    public String getResourceExtension() {
        return resourceExtension;
    }

    public void setResourceExtension(String resourceExtension) {
        this.resourceExtension = resourceExtension;
    }

    public Long getPlatform() {
        return platform;
    }

    public void setPlatform(Long platform) {
        this.platform = platform;
    }

    public List<String> getResourceExtensions() {
        return resourceExtensions;
    }

    public void setResourceExtensions(List<String> resourceExtensions) {
        this.resourceExtensions = resourceExtensions;
    }

    public Integer getReceiverType() {
        return receiverType;
    }

    public void setReceiverType(Integer receiverType) {
        this.receiverType = receiverType;
    }

    public Long getStartTime() {
        return startTime;
    }

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public Long getEndTime() {
        return endTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }
}
