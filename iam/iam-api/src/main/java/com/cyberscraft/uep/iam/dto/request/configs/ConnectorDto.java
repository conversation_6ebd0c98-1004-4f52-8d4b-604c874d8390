package com.cyberscraft.uep.iam.dto.request.configs;

import com.cyberscraft.uep.iam.dto.enums.ConnectorImportStatus;
import com.cyberscraft.uep.iam.dto.enums.ConnectorStatusEnum;
import com.cyberscraft.uep.iam.dto.enums.ConnectorTypeEnum;
import com.cyberscraft.uep.iam.dto.enums.UsernameFormatEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.io.Serializable;
import java.time.LocalTime;
import java.util.List;
import java.util.Map;

@ApiModel(value = "ConnectorDto",
        description = "从AD/LDAP/钉钉同步用户的参数信息")
public class ConnectorDto implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "连接器的唯一ID", hidden = true)
    private String id;

    @ApiModelProperty(value = "上次同步的批次号", hidden = true)
    private Integer syncBatchNo;

    @ApiModelProperty(value = "外部根组织编码", dataType = "String", example = "1")
    private String rootCode = "1";

    @ApiModelProperty(value = "一次同步时删除用户数量限制", hidden = true)
    @Min(-1)
    private Integer deleteLimit;

    @Min(0)
    @Max(1)
    @ApiModelProperty(value = "删除用户方式：常规删除 0，用户停用 1")
    private Integer deleteMethod = 0;

    @ApiModelProperty(value = "计划开始集成时间", required = false)
    private LocalTime plannedStartTime;

    @ApiModelProperty(value = "是否按追加方式更新用户所属部门")
    private Boolean appendOrgs = false;

    @ApiModelProperty(value = "目标目录服务类型", required = true, dataType = "String", example = "AD")
    @NotNull(message = "could not be empty")
    private ConnectorTypeEnum dsType;

    @ApiModelProperty(value = "目标服务是否开启TLS", required = false, dataType = "boolean", example = "true")
    private Boolean tls;

    @ApiModelProperty(value = "连接器名称", required = true, dataType = "String", example = "ldapSyncProfile")
    @NotBlank(message = "could not be empty")
    private String profileName;

    @ApiModelProperty(value = "用户查询根节点", required = false, dataType = "String", example = "ou=dev,dc=tae,dc=uc,dc=nsky,dc=com")
    @Size(max = 256)
    private String userBaseDn;

    @ApiModelProperty(value = "管理员账号", dataType = "String", example = "tae\\administrator")
    private String adminAccount;

    @ApiModelProperty(value = "管理员密码", dataType = "String")
    private String adminPassword;

    @ApiModelProperty(value = "目标LDAP服务地址", required = false, dataType = "String", example = "*************")
    @Size(max = 128)
    private String host;

    @Min(0)
    @Max(65535)
    @ApiModelProperty(value = "目标服务端口", required = false, dataType = "int", example = "636")
    private Integer port;

    @ApiModelProperty(value = "链接器账号配置", required = true, dataType = "String")
    private String configId;

    @ApiModelProperty(value = "目标服务查询用户的过滤条件，若无过滤条件，可以为空", dataType = "String", example = "(objectclass=organizationalPerson)")
    @Size(max = 256)
    private String userFilter;

    @ApiModelProperty(value = "目标服务查询组织的过滤条件，若无过滤条件，可以为空", dataType = "String", example = "(objectclass=organizationalUnit)")
    @Size(max = 256)
    private String orgFilter;

    @ApiModelProperty(value = "导入用户冲突解决策略", required = true, dataType = "domain")
    @Valid
    private Boolean conflictPolicy = false;

    @ApiModelProperty(value = "连接器周期执行时间间隔", required = true, dataType = "int", example = "24", notes = "时间单位为: 小时")
    @Min(0)
    @Max(9999)
    private Integer importPeriod = 0;

    private Integer importType;

    @ApiModelProperty(value = "用户名格式化策略", dataType = "String", example = "NOTCONVERT",
            notes = "NOTCONVERT表示不转换，PRE表示用户名前面加指定字符, SUF表示用户名后面加指定字符。指定的字符由formatString表示")
    private UsernameFormatEnum usernameFormat = UsernameFormatEnum.NOTCONVERT;

    @ApiModelProperty(value = "用户名格式化字符", dataType = "String", example = "nsky",
            notes = "如果usernameFormat不为空，此值必填")
    private String formatString = "";

    @ApiModelProperty(value = "是否同步用户组", required = true, dataType = "boolean")
    @NotNull
    private Boolean importOrgs;

    @ApiModelProperty(value = "角色同步类型,0不同步，1按角色组同步，2按角色同步", required = false, dataType = "int")
    private Integer importRolesType;

    @ApiModelProperty(value = "角色id或角色组id", required = false, dataType = "List")
    private List<String> roles;

    @ApiModelProperty(value = "链接器的状态", required = false, dataType = "String")
    private ConnectorStatusEnum status;

    @ApiModelProperty(value = "测试账号对应的属性path", required = false, dataType = "String")
    private String testPath;

    @ApiModelProperty(value = "测试账号", required = false, dataType = "String")
    private String testAccount;

    @ApiModelProperty(value = "是否创建与连接器同名的组", required = true, dataType = "boolean", example = "true")
    private Boolean createDefaultOrg = true;

    /**
     * 连接器关联组织机构ID
     */
    @ApiModelProperty(value = "连接器组织机构树挂载节点的ID", dataType = "string", example = "*************")
    private String connectorOrgRefId;

    @ApiModelProperty(value = "连接器最后一次同步开始时间", dataType = "date", example = "*************")
    private Long lastImportStartDatetime;

    @ApiModelProperty(value = "连接器最后一次同步完成时间", dataType = "date", example = "*************")
    private Long lastImportFinishDatetime;

    @ApiModelProperty(value = "连接器导入状态", required = true, dataType = "String", example = "AVAILABLE", notes = "AVAILABLE or PROCESSING")
    private ConnectorImportStatus importStatus;

    @ApiModelProperty(value = "链接器账号配置", required = true, dataType = "domain")
    private Map<String, Object> config;

    /**
     * api key，用于事件通知webhook地址
     */
    private String apiKey;

    @ApiModelProperty("网络代理组ID")
    private String proxyId;

    @ApiModelProperty("筛选配置json格式，用于筛选符合条件的对象")
    private Map<String, Object> filterConfig;

    @ApiModelProperty(value = "是否开启过滤", required = false, dataType = "boolean")
    private Boolean enableFilter = false;

    /**
     * 连接器对应的任务id
     */
    private Long taskId;

    /**
     * 流化
     */
    private Integer flow = 0;


    @ApiModelProperty(value = "是否开启身份属性验证", required = false, dataType = "boolean")
    private boolean trustEnable;

    @ApiModelProperty(value = "待验证身份属性配置", required = false)
    private List<String> trustConfig;

    /**
     * 帐号关联
     */
    private List<Map> accountLink;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getSyncBatchNo() {
        return syncBatchNo;
    }

    public void setSyncBatchNo(Integer syncBatchNo) {
        this.syncBatchNo = syncBatchNo;
    }

    public ConnectorTypeEnum getDsType() {
        return dsType;
    }

    public void setDsType(ConnectorTypeEnum dsType) {
        this.dsType = dsType;
    }

    public Boolean getTls() {
        return tls;
    }

    public void setTls(Boolean tls) {
        this.tls = tls;
    }

    public String getProfileName() {
        return profileName;
    }

    public void setProfileName(String profileName) {
        this.profileName = profileName;
    }

    public String getAdminAccount() {
        return adminAccount;
    }

    public void setAdminAccount(String adminAccount) {
        this.adminAccount = adminAccount;
    }

    public String getAdminPassword() {
        return adminPassword;
    }

    public void setAdminPassword(String adminPassword) {
        this.adminPassword = adminPassword;
    }

    public String getRootCode() {
        return rootCode;
    }

    public void setRootCode(String rootCode) {
        this.rootCode = rootCode;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

    public String getConfigId() {
        return configId;
    }

    public void setConfigId(String configId) {
        this.configId = configId;
    }

    public String getOrgFilter() {
        return orgFilter;
    }

    public void setOrgFilter(String orgFilter) {
        this.orgFilter = orgFilter;
    }

    public String getUserFilter() {
        return userFilter;
    }

    public void setUserFilter(String userFilter) {
        this.userFilter = userFilter;
    }

    public Boolean getConflictPolicy() {
        return conflictPolicy;
    }

    public void setConflictPolicy(Boolean conflictPolicy) {
        this.conflictPolicy = conflictPolicy;
    }

    public Integer getImportPeriod() {
        return importPeriod;
    }

    public void setImportPeriod(Integer importPeriod) {
        this.importPeriod = importPeriod;
    }

    public Integer getImportType() {
        return importType;
    }

    public void setImportType(Integer importType) {
        this.importType = importType;
    }

    public UsernameFormatEnum getUsernameFormat() {
        return usernameFormat;
    }

    public void setUsernameFormat(UsernameFormatEnum usernameFormat) {
        this.usernameFormat = usernameFormat;
    }

    public Boolean getImportOrgs() {
        return importOrgs;
    }

    public void setImportOrgs(Boolean importOrgs) {
        this.importOrgs = importOrgs;
    }

    public Integer getImportRolesType() {
        return importRolesType;
    }

    public void setImportRolesType(Integer importRolesType) {
        this.importRolesType = importRolesType;
    }

    public List<String> getRoles() {
        return roles;
    }

    public void setRoles(List<String> roles) {
        this.roles = roles;
    }

    public String getTestAccount() {
        return testAccount;
    }

    public void setTestAccount(String testAccount) {
        this.testAccount = testAccount;
    }

    public String getTestPath() {
        return testPath;
    }

    public void setTestPath(String testPath) {
        this.testPath = testPath;
    }

    public String getUserBaseDn() {
        return userBaseDn;
    }

    public void setUserBaseDn(String userBaseDn) {
        this.userBaseDn = userBaseDn;
    }

    public String getFormatString() {
        return formatString;
    }

    public void setFormatString(String formatString) {
        this.formatString = formatString;
    }

    public Boolean getCreateDefaultOrg() {
        return createDefaultOrg;
    }

    public void setCreateDefaultOrg(Boolean createDefaultOrg) {
        this.createDefaultOrg = createDefaultOrg;
    }

    public String getConnectorOrgRefId() {
        return connectorOrgRefId;
    }

    public void setConnectorOrgRefId(String connectorOrgRefId) {
        this.connectorOrgRefId = connectorOrgRefId;
    }

    public Long getLastImportStartDatetime() {
        return lastImportStartDatetime;
    }

    public void setLastImportStartDatetime(Long lastImportStartDatetime) {
        this.lastImportStartDatetime = lastImportStartDatetime;
    }

    public Long getLastImportFinishDatetime() {
        return lastImportFinishDatetime;
    }

    public void setLastImportFinishDatetime(Long lastImportFinishDatetime) {
        this.lastImportFinishDatetime = lastImportFinishDatetime;
    }

    public ConnectorImportStatus getImportStatus() {
        return importStatus;
    }

    public void setImportStatus(ConnectorImportStatus importStatus) {
        this.importStatus = importStatus;
    }

    public Map<String, Object> getConfig() {
        return config;
    }

    public void setConfig(Map<String, Object> config) {
        this.config = config;
    }

    public List<Map> getAccountLink() {
        return accountLink;
    }

    public void setAccountLink(List<Map> accountLink) {
        this.accountLink = accountLink;
    }

    public ConnectorStatusEnum getStatus() {
        return status;
    }

    public void setStatus(ConnectorStatusEnum status) {
        this.status = status;
    }

    public Integer getDeleteLimit() {
        return deleteLimit;
    }

    public void setDeleteLimit(Integer deleteLimit) {
        this.deleteLimit = deleteLimit;
    }

    public Integer getDeleteMethod() {
        return deleteMethod;
    }

    public void setDeleteMethod(Integer deleteMethod) {
        this.deleteMethod = deleteMethod;
    }

    public LocalTime getPlannedStartTime() {
        return plannedStartTime;
    }

    public void setPlannedStartTime(LocalTime plannedStartTime) {
        this.plannedStartTime = plannedStartTime;
    }

    public Boolean getAppendOrgs() {
        return appendOrgs;
    }

    public void setAppendOrgs(Boolean appendOrgs) {
        this.appendOrgs = appendOrgs;
    }

    public String getApiKey() {
        return apiKey;
    }

    public void setApiKey(String apiKey) {
        this.apiKey = apiKey;
    }

    public String getProxyId() {
        return proxyId;
    }

    public void setProxyId(String proxyId) {
        this.proxyId = proxyId;
    }

    public Map<String, Object> getFilterConfig() {
        return filterConfig;
    }

    public void setFilterConfig(Map<String, Object> filterConfig) {
        this.filterConfig = filterConfig;
    }

    public Boolean getEnableFilter() {
        return enableFilter;
    }

    public void setEnableFilter(Boolean enableFilter) {
        this.enableFilter = enableFilter;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public Integer getFlow() {
        return flow;
    }

    public void setFlow(Integer flow) {
        this.flow = flow;
    }

    public boolean getTrustEnable() {
        return trustEnable;
    }

    public void setTrustEnable(boolean trustEnable) {
        this.trustEnable = trustEnable;
    }

    public List<String> getTrustConfig() {
        return trustConfig;
    }

    public void setTrustConfig(List<String> trustConfig) {
        this.trustConfig = trustConfig;
    }

    @Override
    public String toString() {
        return "ConnectorDto{" +
                "dsType=" + dsType +
                ", tls=" + tls +
                ", profileName='" + profileName + '\'' +
                ", userBaseDn='" + userBaseDn + '\'' +
                ", adminAccount='" + adminAccount + '\'' +
                ", adminPassword='" + adminPassword + '\'' +
                ", host='" + host + '\'' +
                ", port=" + port +
                ", userFilter='" + userFilter + '\'' +
                ", orgFilter='" + orgFilter + '\'' +
                ", conflictPolicy=" + conflictPolicy +
                ", importPeriod=" + importPeriod +
                ", usernameFormat=" + usernameFormat +
                ", formatString='" + formatString + '\'' +
                ", importOrgs=" + importOrgs +
                ", status=" + status +
                ", deleteLimit=" + deleteLimit +
                ", deleteMethod=" + deleteMethod +
                ", testAccount='" + testAccount + '\'' +
                ", createDefaultOrg=" + createDefaultOrg +
                ", lastImportStartDatetime=" + lastImportStartDatetime +
                ", lastImportFinishDatetime=" + lastImportFinishDatetime +
                ", importStatus=" + importStatus +
                ", config=" + config +
                '}';
    }
}
