package com.cyberscraft.uep.iam.dto.request;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Map;

@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@ApiModel(value = "CertUploadVO", description = "This object describe the certificate info .")
public class CertUploadVO {

    @ApiModelProperty(value = "用户名", required = true, dataType = "String", example="user1")
//    @NotBlank(message = "could not be empty")
    private String username;

    @ApiModelProperty(value = "证书激活码", required = true, dataType = "String", example="123456")
    @NotBlank(message = "could not be empty")
    private String activeCode;

    @ApiModelProperty(value = "设备唯一标示", required = true, dataType = "String", example="8902748675")
    @NotBlank(message = "could not be empty")
    private String deviceId;

    @ApiModelProperty(value = "设备型号", required = true, dataType = "String", example="iPhone8,1")
    private String deviceModel;

    @ApiModelProperty(value = "证书内容", required = true, dataType = "String", notes = "JWK or PEM")
    @NotNull
    private Map<String, Object> certContent;

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getActiveCode() {
        return activeCode;
    }

    public void setActiveCode(String activeCode) {
        this.activeCode = activeCode;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getDeviceModel() {
        return deviceModel;
    }

    public void setDeviceModel(String deviceModel) {
        this.deviceModel = deviceModel;
    }

    public Map<String, Object> getCertContent() {
        return certContent;
    }

    public void setCertContent(Map<String, Object> certContent) {
        this.certContent = certContent;
    }
}
