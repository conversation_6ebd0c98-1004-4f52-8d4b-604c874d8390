package com.cyberscraft.uep.iam.dto.response.login;

import io.swagger.annotations.ApiModelProperty;

public class LoginMetaVO {
    @ApiModelProperty(value = "是否允许自注册", example = "true")
    private boolean allowRegister;

    @ApiModelProperty(value = "是否开启扫码登录", example = "true")
    private boolean qrCodeEnabled;

    @ApiModelProperty(value = "自注册URL", example = "")
    private String registerUrl;

    @ApiModelProperty(value = "登录框placeholder", example = "true")
    private String placeHolder;

    public boolean isAllowRegister() {
        return allowRegister;
    }

    public void setAllowRegister(boolean allowRegister) {
        this.allowRegister = allowRegister;
    }

    public boolean isQrCodeEnabled() {
        return qrCodeEnabled;
    }

    public void setQrCodeEnabled(boolean qrCodeEnabled) {
        this.qrCodeEnabled = qrCodeEnabled;
    }

    public String getRegisterUrl() {
        return registerUrl;
    }

    public void setRegisterUrl(String registerUrl) {
        this.registerUrl = registerUrl;
    }

    public String getPlaceHolder() {
        return placeHolder;
    }

    public void setPlaceHolder(String placeHolder) {
        this.placeHolder = placeHolder;
    }

}
