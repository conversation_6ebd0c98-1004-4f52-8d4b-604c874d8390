package com.cyberscraft.uep.iam.dto.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;

/**
 * <p>
 *     一次性密码登录返回信息
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2019-09-11 16:13
 */
@ApiModel(value = "get_user_opt_info",
        description = "response object for get user opt info")
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class OptResponseDto {
    private String username;
    private String oneTimePassword;
    @JsonProperty("is_valid")
    private boolean isValid;

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getOneTimePassword() {
        return oneTimePassword;
    }

    public void setOneTimePassword(String oneTimePassword) {
        this.oneTimePassword = oneTimePassword;
    }

    @JsonIgnore
    public boolean isValid() {
        return isValid;
    }

    public void setValid(boolean valid) {
        isValid = valid;
    }
}
