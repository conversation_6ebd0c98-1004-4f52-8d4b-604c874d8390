package com.cyberscraft.uep.iam.dto.scim;

/**
 * <p>
 *  scim core schema uri and resource
 * </p>
 *
 * <AUTHOR>
 * @since 2022/4/26 4:56 下午
 */
public enum ResourceEnum {
    ServiceProviderConfig(ScimConstant.SERVICE_PROVIDER_CONFIG_SCHEMA),
    ResourceType(ScimConstant.RESOURCETYPE_SCHEMA),
    Schema(ScimConstant.SCHEMA_SCHEMA),
    Users(ScimConstant.USER_SCHEMA),
    User(ScimConstant.USER_SCHEMA),
    EnterpriseUser(ScimConstant.ENTERPRISE_USER_SCHEMA),
    Groups(ScimConstant.GROUP_SCHEMA),
    Group(ScimConstant.GROUP_SCHEMA);

    private final String schema;
    private ResourceEnum(String schema){
        this.schema = schema;
    }

    public String getSchema() {
        return schema;
    }
}
