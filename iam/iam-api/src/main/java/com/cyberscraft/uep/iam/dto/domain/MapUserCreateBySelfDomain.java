package com.cyberscraft.uep.iam.dto.domain;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * MapUserCreateBySelfDomain
 *
 * <AUTHOR>
 * @date 2021/12/16
 */
@ApiModel(value = "map_user_create_by_self_request_domain", description = "user attributes for Registered user operation. Please note this only contains basic attributes that use can have. " + "response info can also contains extended attributes.")
public class MapUserCreateBySelfDomain extends MapUserSelfUpdateInDomain {
    @ApiModelProperty(value = "用户登录名", example = "david", required=true)
    private String username;

    @ApiModelProperty(value = "用户登录密码", example = "a1234567", required=true)
    private String password;

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }
}
