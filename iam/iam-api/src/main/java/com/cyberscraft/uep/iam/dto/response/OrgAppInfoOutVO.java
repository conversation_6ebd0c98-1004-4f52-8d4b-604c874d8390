package com.cyberscraft.uep.iam.dto.response;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "org_app_info_response",
        description = "basic app info of the organization, including id and name, whether app enabled.",
        subTypes = OrgInfoOutVO.class)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class OrgAppInfoOutVO extends OrgBasicInfoOutVO{
    @ApiModelProperty(value = "该app是否被授权使用", example = "true")
    private boolean entitled;

    public boolean isEntitled() {
        return entitled;
    }

    public void setEntitled(boolean entitled) {
        this.entitled = entitled;
    }
}
