package com.cyberscraft.uep.iam.dto.request.criteria;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/4/23 17:37
 * @Version 1.0
 * @Description 筛选配置对象，包括多个FilterGroup
 */
public class FilterConfig {

    private List<FilterGroup> orList;

    public List<FilterGroup> getOrList() {
        return orList;
    }

    public void setOrList(List<FilterGroup> orList) {
        this.orList = orList;
    }
}
