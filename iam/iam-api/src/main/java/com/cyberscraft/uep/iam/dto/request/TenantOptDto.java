package com.cyberscraft.uep.iam.dto.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.cyberscraft.uep.common.enums.UepTenantMassageType;
import com.cyberscraft.uep.iam.dto.constraint.annotations.StringEnum;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;

@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class TenantOptDto {

    @Size(message = "The length of tenant domain must between 2 ~ 64", max = 32, min = 2)
    @ApiModelProperty(value = "租户域名", example = "cyberscraft")
    private String tenantDomain;

    @Size(message = "The length of tenant display name must between 2 ~ 64", max = 64, min = 2)
    @ApiModelProperty(value = "租户名称", example = "0.0.1")
    private String tenantDisplayName;

    @Size(message = "The length of tenant db host must between 2 ~ 64", max = 64, min = 2)
    @ApiModelProperty(value = "租户数据库实例标识", example = "cyberscraft")
    private String tenantDbhost;

    @ApiModelProperty(value = "创建服务序列号", example = "01a3eb93-150f-4ccb-9cc9-e6ce06259c4c")
    @NotNull(message = "sequence is required")
    @Size(min = 10, max = 10)
    private String sequence;

    @NotNull(message = "Invalid tenant service license start_at")
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd")
    private Date startedAt;

    @NotNull(message = "Invalid tenant service license expired time")
    @JsonFormat(shape=JsonFormat.Shape.STRING, pattern="yyyy-MM-dd")
    private Date expireAt;

    @Size(message = "The length of admin password must between 2 ~ 16", max = 16, min = 2)
    private String adminPwd;

    @ApiModelProperty(value = "消息类型", example = "tenant_create or tenant_delete")
    @NotNull(message = "Invalid message type")
    @StringEnum(enumClass = UepTenantMassageType.class, message = "message type value error")
    private String messageType;


    public String getTenantDomain() {
        return tenantDomain;
    }

    public void setTenantDomain(String tenantDomain) {
        this.tenantDomain = tenantDomain;
    }

    public String getTenantDisplayName() {
        return tenantDisplayName;
    }

    public void setTenantDisplayName(String tenantDisplayName) {
        this.tenantDisplayName = tenantDisplayName;
    }

    public String getSequence() {
        return sequence;
    }

    public void setSequence(String sequence) {
        this.sequence = sequence;
    }

    public String getTenantDbhost() {
        return tenantDbhost;
    }

    public void setTenantDbhost(String tenantDbhost) {
        this.tenantDbhost = tenantDbhost;
    }


    public Date getStartedAt() {
        return startedAt;
    }

    public void setStartedAt(Date startedAt) {
        this.startedAt = startedAt;
    }

    public Date getExpireAt() {
        return expireAt;
    }

    public void setExpireAt(Date expireAt) {
        this.expireAt = expireAt;
    }

    public String getAdminPwd() {
        return adminPwd;
    }

    public void setAdminPwd(String adminPwd) {
        this.adminPwd = adminPwd;
    }

    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }
}
