package com.cyberscraft.uep.iam.dto.request.tenant;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2020-02-28 11:29
 */
@ApiModel(value = "TenantCreateRequestDto", description = "租户创建请求对像")
public class TenantCreateRequestDto extends TenantUpdateRequestDto implements Serializable {

    @ApiModelProperty(value = "租户ID",required = true)
    /**
     * 创建租户，理论上不应该让其指定租户id，但是对于某些大客户,例如企业级用户，一个好识别的租户id，更友好
     */
    private String tenantId;

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId.toLowerCase();
    }

    @Override
    public String toString() {
        return "TenantCreateRequestDto{" +
                "tenantId='" + tenantId + '\'' +
                ", name='" + name + '\'' +
                ", address='" + address + '\'' +
                ", contacts='" + contacts + '\'' +
                ", phone='" + phone + '\'' +
                ", email='" + email + '\'' +
                ", admin=" + admin +
                '}';
    }
}
