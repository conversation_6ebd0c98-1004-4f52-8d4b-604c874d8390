package com.cyberscraft.uep.iam.api;

import com.cyberscraft.uep.common.dto.BaseReturnResult;
import com.cyberscraft.uep.common.dto.ReturnResultVO;
import com.cyberscraft.uep.iam.constants.UrlConstants;
import com.cyberscraft.uep.iam.dto.request.ThirdPartyAppListVO;
import com.cyberscraft.uep.iam.dto.response.ThirdPartyAppVO;
import com.cyberscraft.uep.iam.dto.response.configs.SnsConfigVO;
import com.cyberscraft.uep.iam.dto.response.thirdparty.ThirdPartyApp;
import io.swagger.annotations.*;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.cyberscraft.uep.common.constant.UEPSwaggerConstants.*;
import static com.cyberscraft.uep.iam.constants.HttpStatus.SC_INTERNAL_SERVER_ERROR;
import static com.cyberscraft.uep.iam.constants.HttpStatus.SC_NOT_FOUND;
import static com.cyberscraft.uep.iam.errors.TransactionErrorDescription.*;

@RestController
@RequestMapping(value = UrlConstants.URL_PREFIX_API_VERSION + "/thirdPartyApp", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(description = "第三方Application 管理", tags = "ThirdParty Application")
public interface ThirdPartyAppApi {
    /*****************************************************************同步第三方应用********************************************************************/
    @RequestMapping(value = "/sync", method = {RequestMethod.GET})
    @ResponseBody
    @ApiResponses(value = {
            @ApiResponse(code = SC_NOT_FOUND,
                    response = BaseReturnResult.class,
                    message = "* 错误码: " + APP_NOT_FOUND_ERROR_CODE + ", 错误消息: " + APP_NOT_FOUND_ERROR_DESC
                            + "\n\r* 错误码: " + APP_USER_NOT_ALLOW_ERROR_CODE + ", 错误消息: " + APP_USER_NOT_ALLOW_ERROR_DESC),
            @ApiResponse(code = SC_INTERNAL_SERVER_ERROR,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + UNKNOWN_DAO_ERROR_CODE + ", 错误消息: " + UNKNOWN_DAO_ERROR_DESC)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String",
                    value = HEADER_REQUEST_CONTENT_TYPE,
                    defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
                    required = true)})
    @ApiOperation(response = ThirdPartyApp.class, nickname = "syncAppsByCrop", value = "通过企业信息获取DD应用", notes = "通过企业信息同步DD应用")
    ReturnResultVO<List<ThirdPartyAppVO>> syncAppsByCrop(@ApiParam(required = true, value = "企业id", example = "0") @RequestParam(value = "sns_id") long snsId);

    /*****************************************************************保存第三方应用********************************************************************/
    @RequestMapping(method = {RequestMethod.POST})
    @ResponseBody
    @ApiResponses(value = {
            @ApiResponse(code = SC_NOT_FOUND,
                    response = BaseReturnResult.class,
                    message = "* 错误码: " + APP_NOT_FOUND_ERROR_CODE + ", 错误消息: " + APP_NOT_FOUND_ERROR_DESC
                            + "\n\r* 错误码: " + APP_USER_NOT_ALLOW_ERROR_CODE + ", 错误消息: " + APP_USER_NOT_ALLOW_ERROR_DESC),
            @ApiResponse(code = SC_INTERNAL_SERVER_ERROR,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + UNKNOWN_DAO_ERROR_CODE + ", 错误消息: " + UNKNOWN_DAO_ERROR_DESC)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String",
                    value = HEADER_REQUEST_CONTENT_TYPE,
                    defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
                    required = true)})
    @ApiOperation(response = Boolean.class, nickname = "saveThirdPartyApp", value = "保存第三方应用到IAM", notes = "保存第三方应用到IAM")
    ReturnResultVO<SnsConfigVO> saveThirdPartyApp(@RequestBody ThirdPartyAppListVO thirdPartyAppListVO);

}
