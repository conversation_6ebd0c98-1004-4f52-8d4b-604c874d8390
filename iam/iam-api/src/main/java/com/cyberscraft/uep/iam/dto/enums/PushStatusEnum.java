package com.cyberscraft.uep.iam.dto.enums;

/**
 * <p>
 * 数据推送状态枚举
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2019-08-14 11:26
 */
public enum PushStatusEnum implements IBaseEnum {

    NORMAL(1, "NORMAL"),
    DISABLE(2, "DISABLE")
    ;

    PushStatusEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    private int value;
    private String name;

    @Override
    public int getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public static String getName(int value) {
        for (PushStatusEnum ele : values()) {
            if (ele.getValue() == (value)) {
                return ele.getName();
            }
        }
        return null;
    }

    public static Integer getValue(String name) {
        for (PushStatusEnum ele : values()) {
            if (ele.getName().equals(name)) {
                return ele.getValue();
            }
        }
        return null;
    }

}