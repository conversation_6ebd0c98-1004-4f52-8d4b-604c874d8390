package com.cyberscraft.uep.iam.errors;


import com.cyberscraft.uep.iam.constants.PageableConstant;
import com.cyberscraft.uep.iam.constants.ParamsConstraints;

public interface TransactionErrorDescription {

    String SUCCESS_ERROR_CODE = "0";

    int WECHAT_SUCCESS_CODE = 0;
    String WECHAT_SUCCESS_DESC = "SUCCESS";

    String UNKNOWN_ERROR_DESC = "Server Unknown Error";
    int UNKNOWN_ERROR_CODE = 1010000;

    String UNKNOWN_DAO_ERROR_DESC = "Internal server error";
    int UNKNOWN_DAO_ERROR_CODE = 1010001;

    String INVALID_REQUEST_PARAM_ERROR_DESC = "Invalid Request Parameter";
    int INVALID_REQUEST_PARAM_ERROR_CODE = 1010002;

    String PAGE_SIZE_EXCEED_MAX_ERROR_DESC = "page size exceed allowed max value " + PageableConstant.MAX_PAGE_SIZE;
    int PAGE_SIZE_EXCEED_MAX_ERROR_CODE = 1010003;

    String REQUEST_TIMEOUT_ERROR_DESC = "Request timeout! Please retry";
    int REQUEST_TIMEOUT_ERROR_CODE = 1010004;

    String TOO_MANY_ENTRIES_ERROR_DESC = "Too Many Entries!";
    int TOO_MANY_ENTRIES_ERROR_CODE = 1010005;

    String REQUEST_STILL_PROCESS_DESC = "Request is still processing. Please check later.";
    int REQUEST_STILL_PROCESS_CODE = 1010006;

    String UC_NOT_INITIALIZED_DESC = "User Center Not Initialized Yet";
    int UC_NOT_INITIALIZED_CODE = 1010007;

    String UC_SERVICE_NOT_READY_DESC = "User Center Service is not ready. Please wait.";
    int UC_SERVICE_NOT_READY_CODE = 1010008;

    int UC_TENANT_SCHEMA_CREATE_ERROR_CODE = 1010009;
    String UC_TENANT_SCHEMA_CREATE_ERROR_DESC = "Specified tenant database schema create fail";

    int UC_TENANT_DATABASE_EXIST_ERROR_CODE = 1010010;
    String UC_TENANT_DATABASE_EXIST_ERROR_DESC = "specified tenant or same tenant creation request already exists";

    int UC_TENANT_CREATE_ERROR_CODE = 1010011;
    String UC_TENANT_CREATE_ERROR_DESC = "Specified tenant create error";

    int UC_TENANT_NOT_FOUND_ERROR_CODE = 1010012;
    String UC_TENANT_NOT_FOUND_ERROR_DESC = "tenant not found";

    int UC_TENANT_FORBID_DEL_SUPER_TENANT_ERROR_CODE = 1010013;
    String UC_TENANT_FORBID_DEL_SUPER_TENANT_ERROR_DESC = "Deleting super tenant is not allowed";
    int UC_TENANT_EMPTY_ERROR_CODE = 1010014;
    String UC_TENANT_EMPTY_ERROR_DESC = "tenant code is empty";

    int LOCK_ERROR_CODE = 1010015;
    String LOCK_ERROR_DESC = "cannot get lock for operation";

    int TENANT_LICENSE_CONFIG_ERROR_CODE = 1010016;
    String TENANT_LICENSE_CONFIG_ERROR_DESC = "tenant license config info error";

    int TENANT_LICENSE_IS_INVALID_ERROR_CODE = 1010017;
    // 平台授权已到期，请联系您的专属服务顾问获取帮助
    String TENANT_LICENSE_IS_INVALID_ERROR_DESC = "Tenant license has expired, please contact your dedicated service consultant";

    int COMMON_PENDING_DELETION_ERROR_CODE = 1010018;
    String COMMON_PENDING_DELETION_ERROR_DESC = "To be deleted";


    // Error about token; 10101xx
    int TOKEN_INVALID_ERROR_CODE = 1010101;
    String TOKEN_INVALID_ERROR_DESC = "Token invalid ";
    int TOKEN_EXPIRED_ERROR_CODE = 1010102;
    String TOKEN_EXPIRED_ERROR_DESC = "Token expired";
    int TOKEN_REQUEST_NOTFOUND_ERROR_CODE = 1010103;
    String TOKEN_REQUEST_NOTFOUND_ERROR_DESC = "Request token not found";
    int TOKEN_REQUEST_EXPIRED_ERROR_CODE = 1010104;
    String TOKEN_REQUEST_EXPIRED_ERROR_DESC = "Request token expired";
    int TOKEN_REQUEST_INVALID_ERROR_CODE = 1010105;
    String TOKEN_REQUEST_INVALID_ERROR_DESC = "Request token invalid";
    int TOKEN_CONFLICT_PARAM_ERROR_CODE = 1010106;
    String TOKEN_CONFLICT_PARAM_ERROR_DESC = "Token is conflict with parameters";
    int AUTH_REQUIRED_ERROR_CODE = 1010107;
    String AUTH_REQUIRED_ERROR_DESC = "authentication required";

    int NO_PERMISSION_ERROR_CODE = 1010108;
    String NO_PERMISSION_ERROR_DESC = "no permission";

    int NO_PERMISSION_DELETE_ORG_ERROR_CODE = 1010109;
    String NO_PERMISSION_DELETE_ORG_ERROR_DESC = "no permission delete org";

    int NO_PERMISSION_EDIT_ORG_ERROR_CODE = 1010110;
    String NO_PERMISSION_EDIT_ORG_ERROR_DESC = "no permission edit org";

    int TOKEN_UPDATE_PASSWORD_INVALID_ERROR_CODE = 1010111;
    String TOKEN_UPDATE_PASSWORD_INVALID_ERROR_DESC = "update password token invalid";


    // Error about user; 10102xx
    String USER_NAME_PASSWORD_INCONSISTENT_ERROR_DESC = "username or password is not correct";
    int USER_NAME_PASSWORD_INCONSISTENT_ERROR_CODE = 1010200;
    String USER_NOT_FOUND_ERROR_DESC = "User Not Found";
    int USER_NOT_FOUND_ERROR_CODE = 1010201;
    String USER_INVALID_ERROR_DESC = "User invalid";
    int USER_INVALID_ERROR_CODE = 1010202;
    String USER_ALREADY_EXISTS_ERROR_DESC = "user with the same id already exists";
    int USER_ALREADY_EXISTS_ERROR_CODE = 1010203;
    String USER_ATTR_NOT_ALLOWED_TO_OPEATE_ERROR_DESC = "not allowed to operate on user attribute";
    int USER_ATTR_NOT_ALLOWED_TO_OPEATE_ERROR_CODE = 1010204;
    String ILLEGAL_USER_ATTR_FOR_UPDATE_ERROR_DESC = "illegal user attribute for update";
    int ILLEGAL_USER_ATTR_FOR_UPDATE_ERROR_CODE = 1010205;
    String USER_ATTR_UNKNOWN_ERROR_DESC = "user attribute is unknown";
    int USER_ATTR_UNKNOWN_ERROR_CODE = 1010206;
    String USER_ATTR_VALUE_INVALID_ERROR_DESC = "user attribute value is invalid";
    int USER_ATTR_VALUE_INVALID_ERROR_CODE = 1010207;
    String USER_ATTR_MISSED_ERROR_DESC = "user mandatory attribute value is missed";
    int USER_ATTR_MISSED_ERROR_CODE = 1010208;
    String USER_EMAIL_NOT_VARIFIED_ERROR_DESC = "user email address not be verified";
    int USER_EMAIL_NOT_VARIFIED_ERROR_CODE = 1010209;
    String USER_EMAIL_MISSED_ERROR_DESC = "user email address is missed";
    int USER_EMAIL_MISSED_ERROR_CODE = 1010210;
    String USER_EMAIL_ALREADY_VERIFIED_ERROR_DESC = "user email address is verified already";
    int USER_EMAIL_ALREADY_VERIFIED_ERROR_CODE = 1010211;
    String USER_NEED_CHANGE_PWD_ERROR_DESC = "Need to change password";
    int USER_NEED_CHANGE_PWD_ERROR_CODE = 1010212;
    String USER_PWD_STATUS_ERROR_DESC = "User could not change password, password status invalid.";
    int USER_PWD_STATUS_ERROR_CODE = 1010213;
    String USER_STATUS_INVALID_ERROR_DESC = "invalid user status";
    int USER_STATUS_INVALID_ERROR_CODE = 1010214;
    String USER_PASSWORD_ERROR_DESC = "password is not correct";
    int USER_PASSWORD_ERROR_CODE = 1010215;
    String LDAP_TIME_BEFORE_EXPIRATION_ERROR_DESC = "the seconds in which the password is expired";
    int LDAP_TIME_BEFORE_EXPIRATION_ERROR_CODE = 1010216;
    String LDAP_CHANGE_AFTER_RESET_ERROR_DESC = "need to change password after admin reset password";
    int LDAP_CHANGE_AFTER_RESET_ERROR_CODE = 1010217;
    String LDAP_PASSWORD_EXPIRED_ERROR_DESC = "password is expired";
    int LDAP_PASSWORD_EXPIRED_ERROR_CODE = 1010218;
    String LDAP_PASSWORD_IN_HISTORY_ERROR_DESC = "password is in history passwords";
    int LDAP_PASSWORD_IN_HISTORY_ERROR_CODE = 1010219;
    String LDAP_ACCOUNT_LOCKED_ERROR_DESC = "user account is locked";
    int LDAP_ACCOUNT_LOCKED_ERROR_CODE = 1010220;
    int USER_PWD_CAN_NOT_RESET_ERROR_CODE = 1010221;
    String USER_PWD_CAN_NOT_RESET_ERROR_DESC = "The administrator can not modify the user password";
    int USER_ADMIN_NO_STATUS_CHANGE_ERROR_CODE = 1010222;
    String USER_ADMIN_NO_STATUS_CHANGE_ERROR_DESC = "The administrator cannot be changed with status";
    int USER_ADMIN_NO_ORG_CHANGE_ERROR_CODE = 1010223;
    String USER_ADMIN_NO_ORG_CHANGE_ERROR_DESC = "The administrator cannot be changed with org";

    String USER_MOBILE_NOT_VARIFIED_ERROR_DESC = "mobile not verified";
    int USER_MOBILE_NOT_VARIFIED_ERROR_CODE = 1010224;
    String USER_MOBILE_MISSED_ERROR_DESC = "mobile missing";
    int USER_MOBILE_MISSED_ERROR_CODE = 1010225;
    String USER_MOBILE_ALREADY_VERIFIED_ERROR_DESC = "mobile already verified";
    int USER_MOBILE_ALREADY_VERIFIED_ERROR_CODE = 1010226;
    String USER_MOBILE_AND_EMAIL_MISSED_ERROR_DESC = "email missing, mobile missing or not verified";
    int USER_MOBILE_AND_EMAIL_MISSED_ERROR_CODE = 1010227;
    String USER_READ_ONLY_CANNOT_MODIFY_ERROR_DESC = "Cannot delete,move,change/reset password for read only user, read only user cannot join/leave org";
    int USER_READ_ONLY_CANNOT_MODIFY_ERROR_CODE = 1010228;

    String USER_SMS_GATEWAY_UNAVAILABLE_ERROR_DESC = "SMS Gateway Unavailable";
    int USER_SMS_GATEWAY_UNAVAILABLE_ERROR_CODE = 1010229;

    int USER_DUPLICATED_ERROR_CODE = 1010230;
    String USER_DUPLICATED_ERROR_DESC = "Duplicated user:%s";
    int USER_NOT_ALLOW_USE_APP_ERROR_CODE = 1010231;
    String USER_NOT_ALLOW_USE_APP_ERROR_DESC = "User not allowed to use this app";
    int BAD_NONCE_ERROR_CODE = 1010232;
    String BAD_NONCE_ERROR_DESC = "bad nonce in password";
    int BAD_USER_CREDENTIALS_ERROR_CODE = 1010233;
    String BAD_USER_CREDENTIALS_ERROR_DESC = "bad user credentials in cookie";
    int LOGIN_ID_IS_EMPTY_ERROR_CODE = 1010234;
    String LOGIN_ID_IS_EMPTY_ERROR_DESC = "login id is empty";
    int BAD_LOGIN_ID_ERROR_CODE = 1010235;
    String BAD_LOGIN_ID_ERROR_DESC = "wrong login id format";
    int WRONG_OTP_ERROR_CODE = 1010236;
    String WRONG_OTP_ERROR_DESC = "wrong otp";
    int SEND_OTP_TOO_FREQUENTLY_CODE = 1010237;
    String SEND_OTP_TOO_FREQUENTLY_DESC = "sending otp too frequently";
    int USER_MOBILE_ALREADY_EXIST_ERROR_CODE = 1010238;
    String USER_MOBILE_ALREADY_EXIST_ERROR_DESC = "duplicated user phone_number";
    int USER_EMAIL_ALREADY_EXIST_ERROR_CODE = 1010239;
    String USER_EMAIL_ALREADY_EXIST_ERROR_DESC = "duplicated user email address";
    int USER_PWD_LOGIN_NOT_SUPPORTED_ERROR_CODE = 1010240;
    String USER_PWD_LOGIN_NOT_SUPPORTED_ERROR_DESC = "synced user can't login with pwd because of no pwd";
    int OUTBOUND_USER_NOT_FOUND_ERROR_CODE = 1010241;
    String OUTBOUND_USER_NOT_FOUND_ERROR_DESC = "outbound user not found";
    int INBOUND_USER_NOT_FOUND_ERROR_CODE = 1010242;
    String INBOUND_USER_NOT_FOUND_ERROR_DESC = "inbound user not found";
    int THIRDPARTY_USER_NOT_FOUND_ERROR_CODE = 1010243;
    String THIRDPARTY_USER_NOT_FOUND_ERROR_DESC = "third party user not found";
    String USER_NAME_STATUS_INACTIVE_ERROR_DESC = "username status is inactive";
    int USER_NAME_STATUS_INACTIVE_ERROR_CODE = 1010244;
    int USER_MULTIPLE_ERROR_CODE = 1010245;
    String USER_MULTIPLE_ERROR_DESC = "Multiple user";
    int USER_NOT_CHAT_ERROR_CODE = 1010246;
    String USER_NOT_CHAT_ERROR_DESC = "user is exist group chat";
    int DING_USER_NOT_FOUND_ERROR_CODE = 1010247;
    String DING_USER_NOT_FOUND_ERROR_DESC = "ding ding user not found";
    int USER_NAME_STATUS_SUSPENDED_ERROR_CODE = 1010248;
    String USER_NAME_STATUS_SUSPENDED_ERROR_DESC = "username status is suspended";
    int USER_IP_ADDRESS_LOCK_ERROR_CODE = 1010249;
    String USER_IP_ADDRESS_LOCK__ERROR_DESC = "username is locked";
    int USER_SLIDER_ERROR_CODE = 1010250;
    String USER_SLIDER_ERROR_DESC = "user slider exception";
    int USER_SLIDER_TIME_OUT_ERROR_CODE = 1010251;
    String USER_SLIDER_TIME_OUT_ERROR_DESC = "slider verify time out";
    int USER_SLIDER_VERIFY_FAIL_ERROR_CODE = 1010252;
    String USER_SLIDER_VERIFY_FAIL_ERROR_DESC = "slider verify fail";
    int NEED_SLIDER_VERIFY_ERROR_CODE = 1010253;
    String NEED_SLIDER_VERIFY_ERROR_DESC = "need slider verify";
    int USER_PASSWORD_NOT_REQUIREMENTS_ERROR_CODE = 1010254;
    String USER_PASSWORD_NOT_REQUIREMENTS_ERROR_DESC = "User password does not meet complexity requirements";

    int USER_NAME_STATUS_OVERDUE_ERROR_CODE = 1010255;
    String USER_NAME_STATUS_OVERDUE_ERROR_DESC = "username status is overdue";

    int USER_INACTIVE_UPDATE_PASSWD_ERROR_CODE = 1010256;
    String USER_INACTIVE_UPDATE_PASSWD_ERROR_DESC = "User cannot change password until it is INACTIVE";
    int USER_OVERDUE_UPDATE_PASSWD_ERROR_CODE = 1010257;
    String USER_OVERDUE_UPDATE_PASSWD_ERROR_DESC = "User cannot change password until it is OVERDUE";

    int USER_ORDER_INVALID_ERROR_CODE = 1010258;
    String USER_ORDER_INVALID_ERROR_DESC = "user order is invalid";

    int USER_COUNT_EXCEED_ERROR_CODE = 1010259;
    String USER_COUNT_EXCEED_ERROR_DESC = "The number of users exceeds the maximum allowed limit";

    int USER_ID_CARD_ALREADY_EXIST_ERROR_CODE = 1010260;
    String USER_ID_CARD_ALREADY_EXIST_ERROR_DESC = "duplicated user id_card";
    int USER_DEPARTED_ERROR_CODE = 1010261;
    String USER_DEPARTED_ERROR_DESC = "User has departed";

    int USER_FROZEN_ERROR_CODE = 1010262;
    String USER_FROZEN_ERROR_DESC = "User is frozen";

    int USER_REGISTRATION_INCOMPLETE_ERROR_CODE = 1010263;
    String USER_REGISTRATION_INCOMPLETE_ERROR_DESC = "User registration incomplete";


    int USER_ID_CANNOT_BE_EMPTY_CODE = 1010264;
    String USER_ID_CANNOT_BE_EMPTY_DESC = "用户ID不能为空";

    int USER_BIOMETRIC_ID_CANNOT_BE_EMPTY_CODE = 1010265;
    String USER_BIOMETRIC_ID_CANNOT_BE_EMPTY_DESC = "用户生物信息ID不能为空";

    int USER_BIOMETRIC_NOT_EXIST_CODE = 1010266;
    String USER_BIOMETRIC_NOT_EXIST_DESC = "用户生物信息不存在";

    int USER_VERIFY_TIME_OUT_ERROR_CODE = 1010267;
    String USER_VERIFY_TIME_OUT_ERROR_DESC = "验证超时";

    int INVALID_CREDENTIALS_ERROR_CODE = 1010268;
    String INVALID_CREDENTIALS_ERROR_DESC = "无效的凭据";

    int AUTHENTICATION_FAILED_ERROR_CODE = 1010269;
    String AUTHENTICATION_FAILED_ERROR_DESC = "验证失败";

    int USER_VERIFY_USER_NOT_MATCH_ERROR_CODE = 1010270;
    String USER_VERIFY_USER_NOT_MATCH_ERROR_DESC = "登录账号与上一步不匹配";

    int USER_NAME_STATUS_DEACTIVATE_ERROR_CODE = 1010271;
    String USER_NAME_STATUS_DEACTIVATE_ERROR_DESC = "username status is deactivating";

    int USER_BIOMETRIC_NAME_EXIST_CODE = 1010272;
    String USER_BIOMETRIC_NAME_EXIST_DESC  = "用户生物识别名称已存在";

    // Error about organization; 10103xx
    String ORG_INVALID_ID_ERROR_DESC = "Invalid Organization ID";
    int ORG_INVALID_ID_ERROR_CODE = 1010300;

    String ORG_NOT_FOUND_ERROR_DESC = "Organization Not Found";
    int ORG_NOT_FOUND_ERROR_CODE = 1010301;
    String ORG_IS_NULL_ERROR_DESC = "Organization Not Found";
    int ORG_IS_NULL_ERROR_CODE = 1010319;

    String ORG_ALREADY_EXISTS_ERROR_DESC = "Organization with the same name already exists";
    int ORG_ALREADY_EXISTS_ERROR_CODE = 1010302;

    String ORG_ROOT_CANNOT_MODIFY_ERROR_DESC = "Cannot modify,delete,move root org, user cannot join/leave root org, cannot entitle app";
    int ORG_ROOT_CANNOT_MODIFY_ERROR_CODE = 1010303;

    String ORG_NEED_CASCADE_DELETE_ERROR_DESC = "org has children, but cascade delete is false or not specified";
    int ORG_NEED_CASCADE_DELETE_ERROR_CODE = 1010304;

    String ORG_ALREADY_HAS_USER_ERROR_DESC = "Organization Already Has User";
    int ORG_ALREADY_HAS_USER_ERROR_CODE = 1010305;

    String ORG_HAS_NO_USER_ERROR_DESC = "Organization Doesn't Have User";
    int ORG_HAS_NO_USER_ERROR_CODE = 1010306;

    String ORG_JOIN_ERROR_DESC = "User Join Organzation Error";
    int ORG_JOIN_ERROR_ERROR_CODE = 1010307;

    String ORG_LEAVE_ERROR_DESC = "User Leave Organzation Error";
    int ORG_LEAVE_ERROR_ERROR_CODE = 1010308;

    String ORG_MOVE_ERROR_DESC = "parent org is equal to or a subordinate of the source org";
    int ORG_MOVE_ERROR_ERROR_CODE = 1010309;

    String ORG_WITH_USER_OR_CHILDREN_CANNOT_DELETE_ERROR_DESC = "cannot delete org, because it has users or children";
    int ORG_WITH_USER_OR_CHILDREN_CANNOT_DELETE_ERROR_CODE = 1010310;

    String ORG_NULL_CANNOT_MODIFY_ERROR_DESC = "Cannot modify,delete,move default org, cannot create org under default org, user cannot join/leave default org";
    int ORG_NULL_CANNOT_MODIFY_ERROR_CODE = 1010311;

    String ORG_NAME_CANNOT_BE_EMPTY_ERROR_DESC = "name: may not be empty";
    int ORG_NAME_CANNOT_BE_EMPTY_ERROR_CODE = INVALID_REQUEST_PARAM_ERROR_CODE;

    String ORG_ID_EQUALS_PARENT_REF_ID_ERROR_DESC = "org_id: org_id is equals parent_ref_id";
    int ORG_ID_EQUALS_PARENT_REF_ID_ERROR_CODE = INVALID_REQUEST_PARAM_ERROR_CODE;

    String ORG_ID_CANNOT_BE_EMPTY_ERROR_DESC = "org_id: may not be empty";
    int ORG_ID_CANNOT_BE_EMPTY_ERROR_CODE = INVALID_REQUEST_PARAM_ERROR_CODE;

    String ORG_NAME_EXCEED_MAX_LENGTH_ERROR_DESC = "organization name exceed max length " + ParamsConstraints.ORG_NAME_MAX_LENGTH;
    int ORG_NAME_EXCEED_MAX_LENGTH_ERROR_CODE = 1010312;

    String ORG_DESC_EXCEED_MAX_LENGTH_ERROR_DESC = "organization desc exceed max length " + ParamsConstraints.ORG_DESC_MAX_LENGTH;
    int ORG_DESC_EXCEED_MAX_LENGTH_ERROR_CODE = 1010313;

    String ORG_READ_ONLY_CANNOT_MODIFY_ERROR_DESC = "Cannot modify,delete,move read only org, user cannot join/leave read only org. Cannot create org under read only org.";
    int ORG_READ_ONLY_CANNOT_MODIFY_ERROR_CODE = 1010314;

    String ORG_ATTR_UNKNOWN_ERROR_DESC = "Org attribute is unknown";
    int ORG_ATTR_UNKNOWN_ERROR_CODE = 1010315;

    String ORG_ATTR_VALUE_INVALID_ERROR_DESC = "Org attribute value is invalid";
    int ORG_ATTR_VALUE_INVALID_ERROR_CODE = 1010316;

    String ORG_ORDER_INVALID_ERROR_DESC = "Org order is invalid";
    int ORG_ORDER_INVALID_ERROR_CODE = 1010317;

    String ORG_ORDER_LEVEL_ERROR_DESC = "Only departments at the same level are allowed to sort";
    int ORG_ORDER_LEVEL_ERROR_CODE = 1010318;

    String ORG_ID_EXCEED_MAX_LENGTH_ERROR_DESC = "organization id exceed max length";
    int ORG_ID_EXCEED_MAX_LENGTH_ERROR_CODE = 1010319;

    // Error about user data schema; 10104xx
    String USER_SCHEMA_NOT_FOUND_ERROR_DESC = "User Schema Not Found";
    int USER_SCHEMA_NOT_FOUND_ERROR_CODE = 1010401;
    String USER_SCHEMA_ATTR_EXIST_ERROR_DESC = "Attribute already exists";
    int USER_SCHEMA_ATTR_EXIST_ERROR_CODE = 1010402;
    String USER_SCHEMA_OBJECT_CALSS_NOT_FOUND_ERROR_DESC = "User schema object class is not exist";
    int USER_SCHEMA_OBJECT_CALSS_NOT_FOUND_ERROR_CODE = 1010403;
    String USER_SCHEMA_ATTR_NOT_FOUND_ERROR_DESC = "Attribute not exists";
    int USER_SCHEMA_ATTR_NOT_FOUND_ERROR_CODE = 1010404;
    String USER_SCHEMA_ATTR_DATA_TYPE_INVALID_ERROR_DESC = "Attribute data type is not support";
    int USER_SCHEMA_ATTR_DATA_TYPE_INVALID_ERROR_CODE = 1010405;
    String USER_SCHEMA_ATTR_DATA_CAN_NOT_FORMAT_ERROR_DESC = "Attribute data is can not format, May contain special characters!";
    int USER_SCHEMA_ATTR_DATA_CAN_NOT_FORMAT_ERROR_CODE = 1010406;
    String USER_SCHEMA_ATTR_MAX_COUNT_REACHED_ERROR_DESC = "Max count of extended attributes is reached!";
    int USER_SCHEMA_ATTR_MAX_COUNT_REACHED_ERROR_CODE = 1010407;
    String USER_SCHEMA_BASIC_ATTR_CAN_NOT_DELETE_ERROR_DESC = "Basic User Attribute can not delete";
    int USER_SCHEMA_BASIC_ATTR_CAN_NOT_DELETE_ERROR_CODE = 1010408;

    String USER_NOT_IN_SCOPE_DESC = "Do not have permission to operate this org";
    int USER_NOT_IN_SCOPE_CODE = 1010409;

    //Error about Self Service, 10105xx
    String SELF_DO_NOT_SUPPORT_SIGNUP_ERROR_DESC = "Does not support user registration by self";
    int SELF_DO_NOT_SUPPORT_SIGNUP_ERROR_CODE = 1010501;
    String SELF_DO_NOT_SUPPORT_ERROR_DESC = "Does not support self service";
    int SELF_DO_NOT_SUPPORT_ERROR_CODE = 1010502;

    //Error about global configs, 10106xx
    int CONFIG_INIT_ONCE_ALREADY_DONE_ERROR_CODE = 1010601;
    String CONFIG_INIT_ONCE_ALREADY_DONE_ERROR_DESC = "Init once policy has been done";

    int CONFIG_SYNC_PROFILE_EXIST_ERROR_CODE = 1010602;
    String CONFIG_SYNC_PROFILE_EXIST_ERROR_DESC = "Same sync profile already exist.";
    int CONFIG_SYNC_PROFILE_EXIST_TARGET_CODE = 1010603;
    String CONFIG_SYNC_PROFILE_EXIST_TARGET_DESC = "Same sync profile target already exist.";

    String CONFIG_SYNC_ATTRMAP_USERNAME_NONE_ERROR_DESC = "mapAttrs: username could not be empty";
    String CONFIG_SYNC_ATTRMAP_NAME_NONE_ERROR_DESC = "mapAttrs: name could not be empty";
    int CONFIG_SYNC_PROFILE_NOT_EXIST_ERROR_CODE = 1010608;
    String CONFIG_SYNC_PROFILE_NOT_EXIST_ERROR_DESC = "Request sync profile not found.";

    int CONFIG_SYNC_PROFILE_CONNINFO_ERROR_CODE = 1010604;
    String CONFIG_SYNC_PROFILE_CONNINFO_ERROR_DESC = "Connection info wrong.";

    int CONFIG_INIT_ALREADY_DONE_ERROR_CODE = 1010605;
    String CONFIG_INIT_ALREADY_DONE_ERROR_DESC = "Init config has been done";

    int CONFIG_INIT_ONCE_REQUIRED_ERROR_CODE = 1010606;
    String CONFIG_INIT_ONCE_REQUIRED_ERROR_DESC = "Init config is required";

    int CONFIG_SYNC_PROFILE_RELATED_GROUP_NAME_EXIST_ERROR_CODE = 1010607;
    String CONFIG_SYNC_PROFILE_RELATED_GROUP_NAME_EXIST_ERROR_DESC = "connector related group exist";

    int CONFIG_PWP_PWD_IN_HISTORY_VALUE_UNSUPPORT_ERROR_CODE = 1010609;
    String CONFIG_PWP_PWD_IN_HISTORY_VALUE_UNSUPPORT_ERROR_DESC = "Password policy: the value of pwdInHistory is not supported";

    int CONNECTOR_IS_LOCKED_CODE = 1010610;
    String CONNECTOR_IS_LOCKED_DESC = "The current connector is locked";

    //Error about send email, 10107xx
    int SEND_EMAIL_DEFAULT_ERROR_CODE = 10107001;
    String SEND_EMAIL_DEFAULT_ERROR_DESC = "Occur an error when sending email";
    int SEND_EMAIL_TEMPLATE_ERROR_CODE = 10107002;
    String SEND_EMAIL_TEMPLATE_ERROR_DESC = "Email template content error";

    //Error about message template, 10108xx
    int TEMPLATE_NOT_FOUND_ERROR_CODE = 1010801;
    String TEMPLATE_NOT_FOUND_ERROR_DESC = "Template not found";

    //Error about import, 10109xx
    int IMPORT_COMMON_ERROR_CODE = 1010900;
    String IMPORT_COMMON_ERROR_DESC = "Import Sync User error";
    int IMPORT_FILE_CSV_TYPE_ERROR_CODE = 1010901;
    String IMPORT_FILE_CSV_TYPE_ERROR_DESC = "Import csv file content type error. Only application/vnd.ms-excel and text/csv permitted";
    int IMPORT_FILE_CSV_EMPTY_ERROR_CODE = 1010902;
    String IMPORT_FILE_CSV_EMPTY_ERROR_DESC = "Import csv file content could not be empty";
    int IMPORT_FILE_CSV_OVER_SIZE_ERROR_CODE = 1010903;
    String IMPORT_FILE_CSV_OVER_SIZE_ERROR_DESC = "Import csv file size could not over %d";
    int IMPORT_FILE_CSV_CHARSET_ERROR_CODE = 1010904;
    String IMPORT_FILE_CSV_CHARSET_ERROR_DESC = "Could not determine upload file charset.";
    int IMPORT_TASK_PROCESSING_ERROR_CODE = 1010905;
    String IMPORT_TASK_PROCESSING_ERROR_DESC = "An import task is processing";
    int IMPORT_TASK_OCCUPIED_ERROR_CODE = 1010906;
    String IMPORT_TASK_OCCUPIED_ERROR_DESC = "An import task does not complete yet, please continue or cancel it.";
    int IMPORT_TASK_NO_DONE_ERROR_CODE = 1010907;
    String IMPORT_TASK_NO_DONE_ERROR_DESC = "No task need to import";
    int IMPORT_TASK_NO_CANCEL_ERROR_CODE = 1010908;
    String IMPORT_TASK_NO_CANCEL_ERROR_DESC = "No task need to cancel";
    int IMPORT_TASK_DELETING_ERROR_CODE = 1010909;
    String IMPORT_TASK_DELETING_ERROR_DESC = "An import task is deleting.";
    int IMPORT_FILE_CSV_READ_ERROR_CODE = 1010910;
    String IMPORT_FILE_CSV_READ_ERROR_DESC = "Read import csv file error.";

    int IMPORT_FILE_CSV_HEADER_NUMBER_ERROR_CODE = 1010911;
    String IMPORT_FILE_CSV_HEADER_NUMBER_ERROR_DESC = "CSV file header lack necessary column.";

    int IMPORT_USERS_CONTAIN_SYSTEM_BUILDIN_USER_ERROR_CODE = 1010912;
    String IMPORT_USERS_CONTAIN_SYSTEM_BUILDIN_USER_NAME_ERROR_DESC = "Import users contains system built-in username";

    int IMPORT_FILE_CSV_CONTAIN_DUPLICATED_ATTR_CODE = 1010913;
    String IMPORT_FILE_CSV_CONTAIN_DUPLICATED_ATTR_DESC = "Import csv file contains duplicate attributes: %s";

    int IMPORT_FILE_CSV_CONTAIN_UNSUPPORTED_ATTR_CODE = 1010914;
    String IMPORT_FILE_CSV_CONTAIN_UNSUPPORTED_ATTR_DESC = "Import csv file contains un supported attributes: %s";

    int IMPORT_USERS_CONFLICT_WITH_SYNC_USER_ERROR_CODE = 1010915;
    String IMPORT_USERS_CONFLICT_WITH_SYNC_USER_ERROR_DESC = "not allow to change username";

    String IMPORT_USERS_TO_TEMP_UNKNOWN_ERROR = "Import occurred an except error. Please try again";

    int DS_DESTINATION_CONNECT_UNKNOWN_ERROR_CODE = 1010920;
    String DS_DESTINATION_CONNECT_UNKNOWN_ERROR_DESC = "Connect refused";

    int DS_DESTINATION_CONNECT_TIMEOUT_ERROR_CODE = 1010921;
    String DS_DESTINATION_CONNECT_TIMEOUT_ERROR_DESC = "attempt connect to server timeout";

    int DS_DESTINATION_CONNECT_TLS_ERROR_CODE = 1010922;
    String DS_DESTINATION_CONNECT_TLS_ERROR_DESC = "server handshake failure";

    int DS_CREDENTIAL_ERROR_CODE = 1010923;
    String DS_CREDENTIAL_ERROR_DESC = "credential error";

    int DS_IMPORT_USER_BASEDN_NOT_FOUND_CODE = 1010924;
    String DS_IMPORT_USER_BASEDN_NOT_FOUND_DESC = "Target baseDN not found";

    int DS_IMPORT_USER_NOTFOUND_CODE = 1010925;
    String DS_IMPORT_USER_NOTFOUND_DESC = "No user found";

    int DS_IMPORT_PROFILE_ERROR_CODE = 1010926;
    String DS_IMPORT_PROFILE_ERROR_DESC = "Datasource import profile parse error";

    int DS_IMPORT_CONN_SERVER_UNAVAILABLE_SEARCH_CODE = 1010927;
    String DS_IMPORT_CONN_SERVER_UNAVAILABLE_SEARCH_DESC = "Could not search on target server";

    int DS_IMPORT_GROUP_NOTFOUND_CODE = 1010928;
    String DS_IMPORT_GROUP_NOTFOUND_DESC = "No group found";

    int IMPORT_USER_DETAIL_ERROR_CODE = 1010930;
    String IMPORT_USER_DETAIL_ERROR_DESC = "Import users content detail error.";

    int IMPORT_FILE_CSV_OVER_COUNT_ERROR_CODE = 1010931;
    String IMPORT_FILE_CSV_OVER_COUNT_ERROR_DESC = "Import csv file could not over %d items.";

    int DS_SYNC_JOB_PROCESSING_ERROR_CODE = 1010940;
    String DS_SYNC_JOB_PROCESSING_ERROR_DESC = "An sync job is processing";

    int DS_SYNC_PROFILE_USER_BASE_DN_COULD_NOT_UPDATE_ERROR_CODE = 1010941;
    String DS_SYNC_PROFILE_USER_BASE_DN_COULD_NOT_UPDATE_ERROR_DESC = "Could not change base dn for this profile";

    int DS_SYNC_CURRENT_JOB_PROCESSING_ERROR_CODE = 1010942;
    String DS_SYNC_CURRENT_JOB_PROCESSING_ERROR_DESC = "Current sync profile is processing sync job";

    int DS_SYNC_NOT_AD_ERROR_CODE = 1010943;
    String DS_SYNC_NOT_AD_ERROR_DESC = "LDAP Server Not AD";

    int DS_SYNC_PROFILE_USERNAME_FORMAT_COULD_NOT_UPDATE_ERROR_CODE = 1010944;
    String DS_SYNC_PROFILE_USERNAME_FORMAT_COULD_NOT_UPDATE_ERROR_DESC = "Could not change username format after synced once";

    int DS_SYNC_PROFILE_USERNAME_FORMAT_STRING_COULD_NOT_UPDATE_ERROR_CODE = 1010945;
    String DS_SYNC_PROFILE_USERNAME_FORMAT_STRING_COULD_NOT_UPDATE_ERROR_DESC = "Could not change username format after synced once";

    int DS_SYNC_PROFILE_IMPORT_ORGS_NOT_UPDATE_ERROR_CODE = 1010946;
    String DS_SYNC_PROFILE_IMPORT_ORGS_NOT_UPDATE_ERROR_DESC = "Could not change import_orgs flag after synced once";

    int DS_ID_INVALID_ERROR_CODE = 1010947;
    String DS_ID_INVALID_ERROR_DESC = "connector id invalid error";

    int DS_CALLBACK_MESSAGE_INVALID_ERROR_CODE = 1010948;
    String DS_CALLBACK_MESSAGE_INVALID_ERROR_DESC = "connector handle invalid error";

    int DS_HANDLE_INVALID_ERROR_CODE = 1010949;
    String DS_HANDLE_INVALID_ERROR_DESC = "connector handle invalid error";

    int DS_NOT_FOUND_ERROR_CODE = 1010950;
    String DS_NOT_FOUND_ERROR_DESC = "connector not found";

    int DS_TOO_MANY_ERROR_CODE = 1010951;
    String DS_TOO_MANY_ERROR_DESC = "more than one connectors found";

    int DS_SYNC_JOB_FAILED_ERROR_CODE = 1010952;
    String DS_SYNC_JOB_FAILED_ERROR_DESC = "sync job is failed";

    int IMPORT_FILE_INVALID_ERROR_CODE = 1010953;
    String IMPORT_FILE_INVALID_ERROR_DESC = "import file invalid";

    int CONNECTOR_DATABASE_SQL_ERROR_CODE = 1010954;
    String CONNECTOR_DATABASE_SQL_ERROR_DESC = "current sql is error";

    int CONNECTOR_DATABASE_SQL_ALISE_EXIST_CHINESE_CODE = 1010955;
    String CONNECTOR_DATABASE_SQL_ALISE_EXIST_CHINESE_DESC = "Aliases in SQL statements exist in Chinese";

    int CONNECTOR_DATABASE_SQL_IS_NOT_QUERY_CODE = 1010956;
    String CONNECTOR_DATABASE_SQL_IS_NOT_QUERY_DESC = "The current SQL statement is not a query statement";

    int CONNECTOR_DATABASE_SQL_IS_CHANGE_CODE = 1010957;
    String CONNECTOR_DATABASE_SQL_IS_CHANGE_DESC = "The current connector has synchronized with the primary key and cannot be changed";

    int CONNECTOR_DATABASE_SQL_PRIMARY_KEY_CHANGE_CODE = 1010958;
    String CONNECTOR_DATABASE_SQL_PRIMARY_KEY_CHANGE_DESC = "The current connector has synchronized with the primary key and cannot be changed";

    int CONNECTOR_REF_ORG_FORBIDDEN_ERROR_CODE = 1010959;
    String CONNECTOR_REF_ORG_FORBIDDEN_ERROR_DESC = "The ref org of current connector can not be delete";

    int OIDC_CLIENT_NOT_FOUND_ERROR_CODE = 1011001;
    String OIDC_CLIENT_NOT_FOUND_ERROR_DESC = "Client not found";

    int OIDC_APPROVED_NOT_FOUND_ERROR_CODE = 1011002;
    String OIDC_APPROVED_NOT_FOUND_ERROR_DESC = "No approved record";

    // Error about application, 10111xx
    int APP_APPTYPE_INVALIDE_ERROR_CODE = 1011101;
    String APP_APPTYPE_INVALIDE_ERROR_DESC = "Application type invalid";
    int APP_GEN_KEY_PAIR_ERROR_CODE = 1011102;
    String APP_GEN_KEY_PAIR_ERROR_DESC = "Fail to generate key pair";
    int APP_APP_CONFLICT_ERROR_CODE = 1011103;
    String APP_APP_CONFLICT_ERROR_DESC = "Application already exists";
    int APP_REDIRECT_URIS_INVALID_ERROR_CODE = 1011104;
    String APP_REDIRECT_URIS_INVALID_ERROR_DESC = "One or more redirect_uri values are invalid";
    int APP_NOT_FOUND_ERROR_CODE = 1011105;
    String APP_NOT_FOUND_ERROR_DESC = "Application [%s] not found";
    int APP_CANNOT_HAS_SECRET_ERROR_CODE = 1011106;
    String APP_CANNOT_HAS_SECRET_ERROR_DESC = "Application has no secret";
    int APP_PUBLIC_KEY_FORMAT_ERROR_CODE = 1011107;
    String APP_PUBLIC_KEY_FORMAT_ERROR_DESC = "Public key format error";
    int APP_CLIENT_PROFILE_UNKNOWN_ERROR_CODE = 1011109;
    String APP_CLIENT_PROFILE_UNKNOWN_ERROR_DESC = "Application type unknown";
    int APP_CLIENT_STATUS_UNKNOWN_ERROR_CODE = 1011110;
    String APP_CLIENT_STATUS_UNKNOWN_ERROR_DESC = "Application status unknown";
    int APP_REFRESH_TOKEN_TIMEOUT_ERROR_CODE = 1011111;
    String APP_REFRESH_TOKEN_TIMEOUT_ERROR_DESC = "Refresh token should be equal or greater than 3600 (one day) and equal or less than 3600 * 24 * 30 (2592000 seconds, one month), value 0 means refresh token never expire\"";
    int APP_TRUSTED_PEERS_ERROR_CODE = 1011112;
    String APP_TRUSTED_PEERS_ERROR_DESC = "There is at least one client that could NOT be trusted peer";
    int APP_NO_PUBLICKEY_ERROR_CODE = 1011113;
    String APP_NO_PUBLICKEY_ERROR_DESC = "No public key for this application";
    int APP_CLIENT_VALIDATE_FACTORS_ERROR_CODE = 1011114;
    String APP_CLIENT_VALIDATE_FACTORS_ERROR_DESC = "Specified validation factor error";
    int APP_CLI_TYPE_NOT_ALLOWED_ERROR_CODE = 1011115;
    String APP_CLI_TYPE_NOT_ALLOWED_ERROR_DESC = "CLI client type not allowed";
    int APP_REVOKE_ENTITLEMENT_FAILURE_REASON_NOT_EXISTS_ERROR_CODE = 1011116;
    String APP_REVOKE_ENTITLEMENT_FAILURE_REASON_NOT_EXISTS_ERROR_DESC = "Revoke failed, user/org not entitled to client before";
    int APP_IS_INTERNAL_ERROR_CODE = 1011117;
    String APP_IS_INTERNAL_ERROR_DESC = "Application is internal, CRUD not allowed";
    int APP_CLI_TRUST_ERROR_CODE = 1011118;
    String APP_CLI_TRUST_ERROR__DESC = "CLI app can NOT be delegated to authorize";
    int APP_AUTH_NOT_FOUND_ERROR_CODE = 1011119;
    String APP_AUTH_NOT_FOUND_ERROR_DESC = "Application auth approved id does not exist";
    int APP_INVALID_CLIENT_ERROR_CODE = 1011120;
    String APP_INVALID_CLIENT_ERROR_DESC = "Invalid client";
    int APP_NOT_ALLOW_PASSWORD_LESS_CODE = 1011121;
    String APP_NOT_ALLOW_PASSWORD_LESS_DESC = "App not allow using one-time password.";
    int APP_INACTIVE_ERROR_CODE = 1011122;
    String APP_INACTIVE_ERROR_DESC = "App not enabled.";
    int APP_PROFILE_UNKNOWN_ERROR_CODE = 1011123;
    String APP_PROFILE_UNKNOWN_ERROR_DESC = "Profile reference unknown";
    int APP_NOT_TRUSTED_REQUESTAPP_ERROR_CODE = 1011124;
    String APP_NOT_TRUSTED_REQUESTAPP_ERROR_DESC = "the third App does not trust the request App";
    int APP_TENANT_OWNER_ERROR_CODE = 1011125;
    String APP_TENANT_OWNER_ERROR_DESC = "tenant owner can NOT be empty";
    int APP_TENANTID_NOT_FOUND_ERROR_CODE = 1011126;
    String APP_TENANTID_NOT_FOUND_ERROR_DESC = "tenant id not found";
    int APP_SCANNERS_CAN_NOT_EMTPY_ERROR_CODE = 1011127;
    String APP_SCANNERS_CAN_NOT_EMTPY_ERROR_DESC = "scanners can NOT be empty";
    int APP_USER_NOT_ALLOW_ERROR_CODE = 1011128;
    String APP_USER_NOT_ALLOW_ERROR_DESC = "user not allow to use the app ";
    int APP_NOT_ALLOW_SMS_CODE = 1011129;
    String APP_NOT_ALLOW_SMS_DESC = "App not allow using sms.";
    int APP_NOT_ALLOW_EMAIL_CODE = 1011130;
    String APP_NOT_ALLOW_EMAIL_DESC = "App not allow using email.";
    int APP_BAD_SIGNING_ALG_CODE = 1011131;
    String APP_BAD_SIGNING_ALG_DESC = "APP Bad Signing Algorithm";
    int APP_AUTH_PROTOCAL_INVALIDE_ERROR_CODE = 1011132;
    String APP_AUTH_PROTOCAL_INVALIDE_ERROR_DESC = "Application auth protocal invalid";
    int APP_READ_ONLY_CANNOT_MODIFY_ERROR_CODE = 1011133;
    String APP_READ_ONLY_CANNOT_MODIFY_ERROR_DESC = "Cannot update read only application";
    int APP_USER_NAME_IS_EXIST_CODE = 1011134;
    String APP_USER_NAME_IS_EXIST_ERROR_DESC = "The username already exists";
    int APP_USER_CREDENTIAL_NOT_FOUND_CODE = 1011135;
    String APP_USER_CREDENTIAL_NOT_FOUND_ERROR_DESC = "The user credential not found";
    int APP_USER_CREDENTIAL_ERROR_CODE = 1011136;
    String APP_USER_CREDENTIAL_ERROR_DESC = "The user credential error";

    int APP_REDIRECT_ERROR_CODE = 1011137;
    String APP_REDIRECT_ERROR_DESC = "Redirect Failed";

    int APP_ACCESS_DENIED_ERROR_CODE = 1011138;
    String APP_ACCESS_DENIED_ERROR_DESC = "Application [%s] access denied";

    int APP_SERVER_URL_EMPTY_ERROR_CODE = 1011139;
    String APP_SERVER_URL_EMPTY_ERROR_DESC = "Application [%s] server URL is empty";

    int APP_CLIENT_URI_EMPTY_ERROR_CODE = 1011140;
    String APP_CLIENT_URI_EMPTY_ERROR_DESC = "Application [%s] with protocol [%s] has empty client URI";

    int APP_INVALID_ERROR_CODE = 1011141;
    String APP_INVALID_ERROR_DESC = "Application [%s] is invalid";

    int APP_SSO_URL_MISMATCH_ERROR_CODE = 1011142;
    String APP_SSO_URL_MISMATCH_ERROR_DESC = "Application [%s] assertion consumer service URL does not match registered URL [%s]";

    int APP_NOT_SAML_PROTOCOL_ERROR_CODE = 1011143;
    String APP_NOT_SAML_PROTOCOL_ERROR_DESC = "Application [%s] is not SAML protocol";

    // Role/Tag 10112xx
    int PERMISSION_CONFLICT_ERROR_CODE = 1011201;
    String PERMISSION_CONFLICT_ERROR_DESC = "Permission already exists";
    int PERMISSION_NOT_FOUND_ERROR_CODE = 1011202;
    String PERMISSION_NOT_FOUND_ERROR_DESC = "Permission not found";
    int ROLE_CONFLICT_ERROR_CODE = 1011203;
    String ROLE_CONFLICT_ERROR_DESC = "Role already exists";
    int ROLE_NOT_FOUND_ERROR_CODE = 1011204;
    String ROLE_NOT_FOUND_ERROR_DESC = "Role not found";
    int ACTION_NOT_FOUND_ERROR_CODE = 1011205;
    String ACTION_NOT_FOUND_ERROR_DESC = "Action not supported";
    int PERMISSION_SET_CONFLICT_ERROR_CODE = 1011206;
    String PERMISSION_SET_CONFLICT_ERROR_DESC = "Permission Set already exists";
    int PERMISSION_SET_NOT_FOUND_CODE = 1011207;
    String PERMISSION_SET_NOT_FOUND_DESC = "Permission Set not found";
    int ROLE_BINDING_CONFLICT_ERROR_CODE = 1011208;
    String ROLE_BINDING_CONFLICT_ERROR_DESC = "Role Binding with same username, client_id and role_name already exists";
    int ROLE_BINDING_NOT_FOUND_ERROR_CODE = 1011209;
    String ROLE_BINDING_NOT_FOUND_ERROR_DESC = "Role Binding not found";
    int TAG_CONFLICT_ERROR_CODE = 1011210;
    String TAG_CONFLICT_ERROR_DESC = "Tag already exists";
    int TAG_NOT_FOUND_ERROR_CODE = 1011211;
    String TAG_NOT_FOUND_ERROR_DESC = "Tag not found";
    int ROLE_BINDING_SELF_DELETE_NOT_ALLOWED_ERROR_CODE = 1011212;
    String ROLE_BINDING_SELF_DELETE_NOT_ALLOWED_ERROR_DESC = "Not allowed to self-revoke role";
    int TAG_CREATE_FAIL_ERROR_CODE = 1011213;
    String TAG_CREATE_FAIL_ERROR_DESC = "Create tag and add user fail!";
    int ROLE_GROUP_NOT_FOUND_ERROR_CODE = 1011214;
    String ROLE_GROUP_NOT_FOUND_ERROR_DESC = "Role group not found!";
    int ROLE_GROUP_VALIDITY_ERROR_CODE = 1011215;
    String ROLE_GROUP_VALIDITY_ERROR_DESC = "Role group size should less than 255!";
    int ROLE_GROUP_WITH_CHILDREN_CANNOT_DELETE_ERROR_CODE = 1011216;
    String ROLE_GROUP_WITH_CHILDREN_CANNOT_DELETE_ERROR_DESC = "cannot delete role group, because it has children";
    int ROLE_GROUP_DEFAULT_CANNOT_DELETE_ERROR_CODE = 1011217;
    String ROLE_GROUP_DEFAULT_CANNOT_DELETE_ERROR_DESC = "cannot delete default role group";
    int ROLE_GROUP_CODE_EXIST_ERROR_CODE = 1011218;
    String ROLE_GROUP_CODE_EXIST_ERROR_DESC = "Role group code already exists";
    int ROLE_GROUP_NAME_EXIST_ERROR_CODE = 1011219;
    String ROLE_GROUP_NAME_EXIST_ERROR_DESC = "Role group name already exists";
    int ROLE_NAME_EXIST_ERROR_CODE = 1011220;
    String ROLE_NAME_EXIST_ERROR_DESC = "Role name already exists";
    int ROLE_GROUP_TAG_NOT_EXIST_ERROR_CODE = 1011221;
    String ROLE_GROUP_TAG_NOT_EXIST_ERROR_DESC = "Role group or the role does not exist";
    int ROLE_CODE_EXIST_ERROR_CODE = 1011222;
    String ROLE_CODE_EXIST_ERROR_DESC = "Role code already exists";
    int TAG_GROUP_DUPLICATE_ERROR_CODE = 1011223;
    String TAG_GROUP_DUPLICATE_ERROR_DESC = "Multiple identical groups found ";

    int TAG_GROUP_INCLUDE_OTHER_TAG_ERROR_CODE = 1011224;
    String TAG_GROUP_INCLUDE_OTHER_TAG_ERROR_DESC = "current roleGroup has other role ";

    int ROLE_BINDING_USER_ERROR_CODE = 1011225;
    String ROLE_BINDING_USER_ERROR_DESC = "Role Binding with same user and role_name not found";


    //Certification error 10113xx
    int CERT_PARSE_ERROR_CODE = 1011300;
    String CERT_PARSE_ERROR_DESC = "Could not parse Certification Content";
    int CERT_DUPLICATE_ERROR_CODE = 1011301;
    String CERT_DUPLICATE_ERROR_DESC = "Duplicated certification id";
    int CERT_USER_OVER_COUNT_ERROR_CODE = 1011302;
    String CERT_USER_OVER_COUNT_ERROR_DESC = "Reach user certification maximum limitation.";
    int CERT_ACTIVE_CODE_RESPONSE_TYPE_ERROR_CODE = 1011303;
    String CERT_ACTIVE_CODE_RESPONSE_TYPE_ERROR_DESC = "Error active code response type";
    int CERT_NOT_FOUND_ERROR_CODE = 1011304;
    String CERT_NOT_FOUND_ERROR_DESC = "Certification not found";
    int CERT_USERNAME_ERROR_CODE = 1011305;
    String CERT_USERNAME_ERROR_DESC = "Username not consistent";
    int CERT_DEVICEID_ERROR_CODE = 1011306;
    String CERT_DEVICEID_ERROR_DESC = "DeviceId not consistent";
    int CAS_INVALID_TICKET_CODE = 1011307;
    String CAS_INVALID_TICKET_DESC = "invalid ticket";

    // OTP;10114xx
    int PASSWORD_LESS_CONNECTOR_ERROR_CODE = 1011400;
    String PASSWORD_LESS_CONNECTOR_ERROR_DESC = "Password less connector error. Value should be SMS or EMAIL.";

    // User Profile 10115xx
    int PROFILE_NOT_FOUND_ERROR_CODE = 1011500;
    String PROFILE_NOT_FOUND_ERROR_DESC = "Profile not found";
    int PROFILE_DOMAINS_EMPTY_ERROR_CODE = 1011501;
    String PROFILE_DOMAINS_EMPTY_ERROR_DESC = "Domains in profile could not be empty";
    int PROFILE_READER_UNKNOWN_ERROR_CODE = 1011502;
    String PROFILE_READER_UNKNOWN_ERROR_DESC = "Reader unknown";
    int PROFILE_DOMAIN_NAME_UNKNOWN_ERROR_CODE = 1011503;
    String PROFILE_DOMAIN_NAME_UNKNOWN_ERROR_DESC = "Domain name unknown";
    int PROFILE_IS_INTERNAL_ERROR_CODE = 1011504;
    String PROFILE_IS_INTERNAL_ERROR_DESC = "Profile is internal and could not be deleted";
    int PROFILE_ID_EXISTS_ERROR_CODE = 1011505;
    String PROFILE_ID_EXISTS_ERROR_DESC = "Profile Id exists";
    int PROFILE_DOMAIN_NAME_EMPTY_ERROR_CODE = 1011506;
    String PROFILE_DOMAIN_NAME_EMPTY_ERROR_DESC = "Domain name could not be empty";
    int PROFILE_WRITER_EVERYONE_ERROR_CODE = 1011507;
    String PROFILE_WRITER_EVERYONE_ERROR_DESC = "Writers could not be everyone";
    int PROFILE_READER_EMPTY_ERROR_CODE = 1011508;
    String PROFILE_READER_EMPTY_ERROR_DESC = "Readers could not be empty";
    int PROFILE_WRITER_EMPTY_ERROR_CODE = 1011509;
    String PROFILE_WRITER_EMPTY_ERROR_DESC = "Writers could not be empty";
    int PROFILE_WRITER_UNKNOWN_ERROR_CODE = 1011510;
    String PROFILE_WRITER_UNKNOWN_ERROR_DESC = "Writer unknown";
    int PROFILE_DOMAIN_AS_PROFILE_ERROR_CODE = 1011511;
    String PROFILE_DOMAIN_AS_PROFILE_ERROR_DESC = "Attribute is not as profile";
    int PROFILE_DOMAIN_AS_CLAIM_ERROR_CODE = 1011512;
    String PROFILE_DOMAIN_AS_CLAIM_ERROR_DESC = "Attribute is not as claim";
    int PROFILE_IN_USE_ERROR_CODE = 1011513;
    String PROFILE_IN_USE_ERROR_DESC = "Profile is in use";
    int PROFILE_BUITLIN_CANNOT_BE_MODIFIED_ERROR_CODE = 1011514;
    String PROFILE_BUITLIN_CANNOT_BE_MODIFIED_ERROR_DESC = "Builtin profile can NOT be modified";
    int PROFILE_MAX_COUNT_ERROR_CODE = 1011515;
    String PROFILE_MAX_COUNT_ERROR_DESC = "User profile total size reach max count";
    int PROFILE_NAME_DUPLICATED_ERROR_CODE = 1011516;
    String PROFILE_NAME_DUPLICATED_ERROR_DESC = "User profile name duplicated";

    // Login Policy 10116xx
    int LOGIN_POLICY_NAME_EMPTY_ERROR_CODE = 1011601;
    String LOGIN_POLICY_NAME_EMPTY_ERROR_DESC = "login policy name can't be empty";
    int LOGIN_POLICY_ALREADY_EXIST_ERROR_CODE = 1011602;
    String LOGIN_POLICY_ALREADY_EXIST_ERROR_DESC = "login policy already exists";
    int LOGIN_POLICY_NOT_FOUND_ERROR_CODE = 1011603;
    String LOGIN_POLICY_NOT_FOUND_ERROR_DESC = "login policy not found";
    int LOGIN_POLICY_INVALID_PWD_AUTH_METHOD_ERROR_CODE = 1011604;
    String LOGIN_POLICY_INVALID_PWD_AUTH_METHOD_ERROR_DESC = "pwd auth method not allowed in mfa settings";
    int LOGIN_POLICY_DELETE_UPDATE_NOT_ALLOWED_ERROR_CODE = 1011605;
    String LOGIN_POLICY_DELETE_UPDATE_NOT_ALLOWED_ERROR_DESC = "not allowed to delete default login policy or update its status";
    int LOGIN_POLICY_PRIMARY_AUTH_METHOD_REQUIRED_ERROR_CODE = 1011606;
    String LOGIN_POLICY_PRIMARY_AUTH_METHOD_REQUIRED_ERROR_DESC = "primary auth method required";
    int LOGIN_POLICY_INACTIVE_CANNOT_SET_AS_DEFAULT_ERROR_CODE = 1011607;
    String LOGIN_POLICY_INACTIVE_CANNOT_SET_AS_DEFAULT_ERROR_DESC = "inactive login policy can't set as default login policy";
    int LOGIN_POLICY_NOT_APPLICABLE_TO_CLI_APP_ERROR_CODE = 1011608;
    String LOGIN_POLICY_NOT_APPLICABLE_TO_CLI_APP_ERROR_DESC = "login policy not applicable to CLI app";
    int LOGIN_POLICY_CERT_AUTH_METHOD_MUST_ASSIGN_TO_NATIVE_APP_ERROR_CODE = 1011609;
    String LOGIN_POLICY_CERT_AUTH_METHOD_MUST_ASSIGN_TO_NATIVE_APP_ERROR_DESC = "login policy has CERT auth method, but client is not NATIVE type";
    int LOGIN_POLICY_PWD_AUTH_MUST_BE_TRUE_ERROR_CODE = 1011610;
    String LOGIN_POLICY_PWD_AUTH_MUST_BE_TRUE_ERROR_DESC = "login policy primary auth pwd_enabled must be true";

    // Auth factor 10118xx
    int AUTH_FACTOR_NOT_FOUND_ERROR_CODE = 1011801;
    String AUTH_FACTOR_NOT_FOUND_ERROR_DESC = "auth factor not found";

    //auth errors 10117xx
    int AUTH_NO_FACTOR_ERROR_CODE = 1011701;
    String AUTH_NO_FACTOR_ERROR_DESC = "auth factor doesn't exist";

    //auth errors 10117xx
    int AUTH_NEED_MFA_ERROR_CODE = 1011702;
    String AUTH_NEED_MFA_ERROR_DESC = "auth need mfa";


    // 11111xx
    int UNAUTHORIZED_ERROR_CODE = 1111101;
    String UNAUTHORIZED_ERROR_DESC = "Unauthorized request";
    int INSUFFICIENT_PRIVILEGES_ERROR_CODE = 1111102;
    String INSUFFICIENT_PRIVILEGES_ERROR_DESC = "Insufficient privileges";

    int SELF_OPERATION_LIMIT_ERROR_CODE = 1111103;
    String SELF_OPERATION_LIMIT_ERROR_DESC = "Could not request others resource";

    int SIGNUP_POLICY_MISS_USERNAME_ERROR_CODE = 1111104;
    String SIGNUP_POLICY_MISS_USERNAME_ERROR_DESC = "username attribute is required for sign up";

    int SIGNUP_POLICY_MISS_PASSWORD_ERROR_CODE = 1111105;
    String SIGNUP_POLICY_MISS_PASSWORD_ERROR_DESC = "password attribute is required for sign up";

    int CONFIG_LOGIN_POLICY_COOKIE_VALIDITY_ERROR_CODE = 1111106;
    String CONFIG_LOGIN_POLICY_COOKIE_VALIDITY_ERROR_DESC = "cookie validity can't be 0 or less than -1";

    int CONFIG_LOGIN_POLICY_USER_CERT_COUNT_ERROR_CODE = 1111107;
    String CONFIG_LOGIN_POLICY_USER_CERT_COUNT_ERROR_DESC = "user certification count can't be 0 or less than -1 and over 10";

    int CONFIG_LOGIN_POLICY_USER_CERT_VALIDITY_ERROR_CODE = 1111108;
    String CONFIG_LOGIN_POLICY_USER_CERT_VALIDITY_ERROR_DESC = "user certification validity period can't be less than 0";

    int CONFIG_LOGIN_POLICY_MESSAGE_VALIDITY_ERROR_CODE = 1111109;
    String CONFIG_LOGIN_POLICY_MESSAGE_VALIDITY_ERROR_DESC = "message verify validity greater 10 or less than 0";

    int CONFIG_LOGIN_POLICY_EMAIL_VALIDITY_ERROR_CODE = 1111110;
    String CONFIG_LOGIN_POLICY_EMAIL_VALIDITY_ERROR_DESC = "email verify validity greater 10 or less than 0";

    int GENERAL_LOGIN_FAILED_ERROR_CODE = 1111111;

    String GENERAL_LOGIN_FAILED_ERROR_DESC = "Login failed!";


    //dingtalk connector errorcode
    int TENANT_NOT_MATCH_ERROR_CODE = 10102100;
    String TENANT_NOT_MATCH_DESC = "the input tcode is not match with system token";
    int DING_TALK_RESPONSE_NULL_CODE = 12000100;
    String DING_TALK_RESPONSE_NULL_DESC = "dingtalk response null";
    int DING_TALK_RESPONSE_ERROR_CODE = 12000101;
    String DING_TALK_RESPONSE_ERROR_DESC = "dingtalk response error";
    int CORPID_INVALID__ERROR_CODE = 12000102;
    String CORPID_INVALID__ERROR_DESC = "The parameter name in the URL is inconsistent with it in the request";
    int DING_TALK_REGISTER_FAIL_CODE = 12000103;
    String DING_TALK_REGISTER_FAIL_DESC = "dingtalk response fail";
    int AUTH_DATA_IS_USED_ERROR_CODE = 12000104;
    String AUTH_DATA_IS_USED_ERROR_DESC = "the auth data is used for app,please delete the app auth data first";

    int WEWORK_GET_ACCESS_TOKEN_ERROR_CODE = 13000100;
    String WEWORK_GET_ACCESS_TOKEN_ERROR_DESC = "get wework accessToken error";
    int GET_ACCESS_TOKEN_LOCK_ERROR_CODE = 13000101;
    String GET_ACCESS_TOKEN_LOCK_ERROR_DESC = "get wework accesstoken lock error";

    //第三方SSO auth provider设置
    int SNS_AUTH_NOT_FOUND_ERROR_CODE = 14000001;
    String SNS_AUTH_NOT_FOUND_ERROR_DESC = "No Third Party SSO Auth Provider";
    int AUTH_TYPE_NOT_FOUND_ERROR_CODE = 14000002;
    String AUTH_TYPE_NOT_FOUND_ERROR_DESC = "required auth provider not found";
    int SNS_AUTH_FAILED_ERROR_CODE = 14000003;
    String SNS_AUTH_FAILED_ERROR_DESC = "Sns auth failed";
    int SNS_CODE_BLANK_ERROR_CODE = 14000004;
    String SNS_CODE_BLANK_ERROR_DESC = "Sns code is blank";

    // 认证失败: IDP entityID不合法
    int AUTH_INVALID_IDP_ENTITYID_ERROR_CODE = 14000005;
    String AUTH_INVALID_IDP_ENTITYID_ERROR_DESC = "Authentication failed: Invalid IDP entityId";

    // 认证失败: sp entityID与请求的不一致
    int AUTH_SP_ENTITYID_MISMATCH_ERROR_CODE = 14000006;
    String AUTH_SP_ENTITYID_MISMATCH_ERROR_DESC = "Authentication failed: SP entityId does not match the request";

    // 认证失败: assertion未签名
    int AUTH_UNSIGNED_ASSERTION_ERROR_CODE = 14000007;
    String AUTH_UNSIGNED_ASSERTION_ERROR_DESC = "Authentication failed: Assertion is not signed";

    int SSO_LOGIN_FAILED_ERROR_CODE = 14000008;
    String SSO_LOGIN_FAILED_ERROR_DESC = "Single Sign-On (SSO) failed!";

    // 认证失败: 具体错误自行补充
    int AUTH_FAILED_CUSTOM_ERROR_CODE = 14000009;
    String AUTH_FAILED_CUSTOM_ERROR_DESC = "Authentication failed: [%s]";

    int REQUEST_DOMAIN_BLANK_ERROR_CODE = 14000010;
    String REQUEST_DOMAIN_BLANK_ERROR_DESC = "Request Domain is blank";


    int PUSH_CONNECTOR_NAME_DUPLICATE_ERROR_CODE = 13000200;
    String PUSH_CONNECTOR_NAME_DUPLICATE_ERROR_DESC = "push connector name duplicate";
    int PUSH_CONNECTOR_TENANTKEY_EXIST_ERROR_CODE = 13000201;
    String PUSH_CONNECTOR_TENANTKEY_EXIST_ERROR_DESC = "push connector tenant key exsits,the corpId exist or ad/ldap's basdn exist ";

    int PUSH_CONNECTOR_INVALID_ERROR_CODE = 13000202;
    String PUSH_CONNECTOR_INVALID_ERROR_DESC = "push connector invalid";
    int PUSH_CONNECTOR_ID_INVALID_ERROR_CODE = 13000203;
    String PUSH_CONNECTOR_ID_INVALID_ERROR_DESC = "push connector id invalid";

    int PUSH_CONNECTOR_CREATE_GROUP_ERROR_CODE = 13000204;
    String PUSH_CONNECTOR_CREATE_GROUP_ERROR_DESC = "push data to connector create group error ";
    int PUSH_CONNECTOR_CREATE_USER_ERROR_CODE = 13000205;
    String PUSH_CONNECTOR_CREATE_USER_ERROR_DESC = "push data to connector create user error ";
    int PUSH_CONNECTOR_MODIFY_GROUP_ERROR_CODE = 13000206;
    String PUSH_CONNECTOR_MODIFY_GROUP_ERROR_DESC = "push data to connector modify group error ";
    int PUSH_CONNECTOR_MODIFY_USER_ERROR_CODE = 13000207;
    String PUSH_CONNECTOR_MODIFY_USER_ERROR_DESC = "push data to connector modify user error ";
    int PUSH_CONNECTOR_CONFIG_INVALID_ERROR_CODE = 13000208;
    String PUSH_CONNECTOR_CONFIG_INVALID_ERROR_DESC = "push connector config invalid";
    int PUSH_CONNECTOR_NAME_TYPE_ERROR_CODE = 13000209;
    String PUSH_CONNECTOR_NAME_TYPE_ERROR_DESC = "exist same type connector";
    int DELETE_CONNECTOR_CHECK_STATUS_ERROR_CODE = 13000210;
    String DELETE_CONNECTOR_CHECK_STATUS_ERROR_DESC = "please disable the connector before deleting it";
    int PUSH_CONNECTOR_RUNNING_ERROR_CODE = 13000211;
    String PUSH_CONNECTOR_RUNNING_ERROR_DESC = "push connector is running";

    int PUSH_CONNECTOR_NOT_DELETE_LIMIT_ERROR_CODE = 13000212;
    String PUSH_CONNECTOR_NOT_DELETE_LIMIT_ERROR_DESC = "Push connector did not trigger delete limit";

    int PUSH_CONNECTOR_REF_ORG_FORBIDDEN_ERROR_CODE = 13000213;
    String PUSH_CONNECTOR_REF_ORG_FORBIDDEN_ERROR_DESC = "The ref org of current push connector can not be delete";

    int SNS_CONFIG_CORPID_DUPLICATE_ERROR_CODE = 13000300;
    String SNS_CONFIG_CORPID_DUPLICATE_ERROR_DESC = "sns config corpid duplicate";
    int SNS_CONFIG_NAME_DUPLICATE_ERROR_CODE = 13000301;
    String SNS_CONFIG_NAME_DUPLICATE_ERROR_DESC = "sns config name duplicate ";
    int SNS_CONFIG_ID_INVALID_ERROR_CODE = 13000302;
    String SNS_CONFIG_ID_INVALID_ERROR_DESC = "sns config id invalid";
    int SNS_CONFIG_INVALID_ERROR_CODE = 13000303;
    String SNS_CONFIG_INVALID_ERROR_DESC = "sns config invalid";
    int SNS_CONFIG_TYPE_INVALID_ERROR_CODE = 13000304;
    String SNS_CONFIG_TYPE_INVALID_ERROR_DESC = "sns config type invalid";
    int SNS_CONFIG_USED_ERROR_CODE = 13000305;
    String SNS_CONFIG_USED_ERROR_DESC = "sns config is used invalid";
    int SNS_CONFIG_EXIST_IN_PUSH_CONNECTOR_CODE = 13000306;
    String SNS_CONFIG_EXIST_IN_PUSH_CONNECTOR_DESC = "push connector used same sns config";
    int SNS_CONFIG_EXIST_IN_SYNC_CONNECTOR_CODE = 13000307;
    String SNS_CONFIG_EXIST_IN_SYNC_CONNECTOR_DESC = "sync connector used same sns config";

    int THIRD_IDP_INVALID_ERROR_CODE = 13000308;
    String THIRD_IDP_INVALID_ERROR_DESC = "third idp config id invalid";

    int THIRD_IDP_REF_ORG_FORBIDDEN_ERROR_CODE = 13000309;
    String THIRD_IDP_REF_ORG_FORBIDDEN_ERROR_DESC = "The ref org of current third idp can not be delete";


    int PUSH_RECORD_INVALID_ERROR_CODE = 13000400;
    String PUSH_RECORD_INVALID_ERROR_DESC = "push record invalid";
    int PUSH_RECORD_ID_INVALID_ERROR_CODE = 13000401;
    String PUSH_RECORD_ID_INVALID_ERROR_DESC = "push record id is invalid";
    int PUSH_RECORD_BUSINESSDATAID_INVALID_ERROR_CODE = 13000402;
    String PUSH_RECORD_BUSINESSDATAID_INVALID_ERROR_DESC = "push record businessDataRefId is invalid";
    int PUSH_RECORD_BUSINESSTYPE_INVALID_ERROR_CODE = 13000403;
    String PUSH_RECORD_BUSINESSTYPE_INVALID_ERROR_DESC = "push record business type is invalid";

    int THIRD_PARTY_APP_NOT_FOUND_ERROR_CODE = 13000500;
    String THIRD_PARTY_APP_NOT_FOUND_ERROR_DESC = "thirdparty app not found";

    // 13111xxx
    int GET_DING_DING_ACCESS_TOKEN_ERROR_CODE = 13111001;
    String GET_DING_DING_ACCESS_TOKEN_ERROR_DESC = "get ding ding access token error";
    int GET_DING_DING_APPS_ERROR_CODE = 13111002;
    String GET_DING_DING_APPS_ERROR_DESC = "get ding ding apps error";

    int DATA_FLOW_COMMON_ERROR_CODE = 14000000;

    int DELETE_DATA_CONNECT_ERROR_CODE = 14111001;
    String DELETE_DATA_CONNECT_ERROR_DESC = "exist actions or trigger";


    //15000000
    int PROXY_CLIENT_IS_NULL_ERROR_CODE = 15000000;
    String PROXY_CLIENT_IS_NULL_ERROR_DESC = "proxy client is null";

    int PROXY_GROUP_IS_NULL_ERROR_CODE = 15000001;
    String PROXY_GROUP_IS_NULL_ERROR_DESC = "proxy group is null";

    int PROXY_GROUP_VALIDITY_ERROR_CODE = ********;
    String PROXY_GROUP_VALIDITY_ERROR_DESC = "proxy group size should less than 255";

    int PROXY_GROUP_EXIST_ERROR_CODE = ********;
    String PROXY_GROUP_EXIST_ERROR_DESC = "proxy group is exist";

    int PROXY_CLIENT_EXIST_ERROR_CODE = ********;
    String PROXY_CLIENT_EXIST_ERROR_DESC = "proxy client is exist";

    /**
     * wechat相关状态码
     */
    int USER_NO_BIND_CODE = ********;
    String USER_NO_BIND_DESC = "WeChat is not linked to the user account";

    int NAME_IS_EXIST_CODE = ********;
    String NAME_IS_EXIST_DESC = "Username already exists";

    int USER_IN_AUDIT_CODE = ********;
    String USER_IN_AUDIT_DESC = "User registration pending approval";

    int USER_AUDIT_FAILED_CODE = ********;
    String USER_AUDIT_FAILED_DESC = "The administrator rejected the user registration request. Please contact your administrator";

    int USER_IS_BIND_CODE = ********;
    String USER_IS_BIND_DESC = "Bind WeChat successfully";

    int USER_WECHAT_IS_BIND_CODE = ********;
    String USER_WECHAT_IS_BIND_DESC = "The account is already bound to another WeChat";

    int USER_WECHAT_IS_USED_CODE = ********;
    String USER_WECHAT_IS_USED_DESC = "This WeChat is already bound to another account";

    int USER_ADMIN_IS_NOT_ALLOWED_CODE = ********;
    String USER_ADMIN_IS_NOT_ALLOWED_DESC = "The admin account cannot be linked to WeChat";


    String ERROR = "error";
    String DESCRIPTION = "error_description";
    String INDICATE = "indicate";
    String INVALID_REQUEST = "invalid_request";
    String CLIENT_ALREADY_EXISTS = "client_already_exists";
    String INVALID_CLIENT_PROFILE = "invalid_client_profile";
    String INVALID_REDIRECT_URI = "invalid_redirect_uri";
    String INVALID_CLIENT = "invalid_client";
    String INVALID_USER_PROFILE = "invalid_user_profile";
    String SERVER_ERROR = "server_error";
}
