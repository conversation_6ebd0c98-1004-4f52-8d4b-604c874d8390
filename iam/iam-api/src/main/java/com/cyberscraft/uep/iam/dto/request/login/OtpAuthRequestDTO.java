package com.cyberscraft.uep.iam.dto.request.login;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;

/**
 * 通过手机或邮箱收到的验证码登录
 */
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@ApiModel(value = "OtpAuthRequestDTO", description = "通过手机或邮箱收到的验证码登录请求")
public class OtpAuthRequestDTO extends OtpRequestDTO{
    private static final long serialVersionUID = -1580731726110605455L;
    @NotEmpty(message = "could not be empty")
    @ApiModelProperty(name = "otp", value = "通过邮箱或者手机接收到的一次性密码", required = true, example="123321")
    private String otp;

    @ApiModelProperty(name = "movePosX", value = "滑块移动的像素", dataType = "Integer", required = false, example = "234")
    private Integer movePosX = 0;

    public String getOtp() {
        return otp;
    }

    public void setOtp(String otp) {
        this.otp = otp;
    }

    public Integer getMovePosX() {
        return movePosX;
    }

    public void setMovePosX(Integer movePosX) {
        this.movePosX = movePosX;
    }
}
