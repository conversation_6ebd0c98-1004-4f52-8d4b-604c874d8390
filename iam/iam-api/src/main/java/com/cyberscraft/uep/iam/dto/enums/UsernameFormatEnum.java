package com.cyberscraft.uep.iam.dto.enums;

/**
 * <p>
 * 链接器用户名转换策略
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2019-07-29 16:09
 */
public enum UsernameFormatEnum implements IBaseEnum {
    /**不转换**/
    NOTCONVERT(1, "NOTCONVERT"),
    /**固定前缀**/
    PRE(2, "PRE"),
    /**固定后缀**/
    SUF(3, "SUF")
    ;

    UsernameFormatEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    private int value;
    private String name;

    @Override
    public int getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public static String getName(int value) {
        for (UsernameFormatEnum ele : values()) {
            if (ele.getValue() == (value)) {
                return ele.getName();
            }
        }
        return null;
    }

    public static Integer getValue(String name) {
        for (UsernameFormatEnum ele : values()) {
            if (ele.getName().equals(name)) {
                return ele.getValue();
            }
        }
        return null;
    }

}