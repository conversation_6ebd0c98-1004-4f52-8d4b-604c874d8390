package com.cyberscraft.uep.iam.dto.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@ApiModel(value = "rotate_client_signature_secret_response",
        description = "response object for rotating client signature secret.")
public class AppSignatureSecretRotationVO {

    public AppSignatureSecretRotationVO() {

    }

    public AppSignatureSecretRotationVO(String clientId, String signatureSecret) {
        setClientId(clientId);
        setSignatureSecret(signatureSecret);
    }

    @ApiModelProperty(value = "应用的唯一id", example = "digitalsee_sag", required = true)
    private String clientId;

    @ApiModelProperty(value = "应用更新后的对称签名秘钥", required = true)
    private String signatureSecret;

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getSignatureSecret() {
        return signatureSecret;
    }

    public void setSignatureSecret(String signatureSecret) {
        this.signatureSecret = signatureSecret;
    }

    @Override
    public String toString() {
        return "AppSecretRotationVO{" +
                "clientId='" + clientId + '\'' +
                ", signatureSecret='" + signatureSecret + '\'' +
                '}';
    }
}