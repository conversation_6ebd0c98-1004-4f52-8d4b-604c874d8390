package com.cyberscraft.uep.iam.dto.request;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import javax.validation.constraints.NotNull;
import java.util.List;

@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class AttrBatchCreateInVO{

	@NotNull(message = "Attribute list must NOT be empty")
	private List<AttrCreateInVO> extAttrs;

	public List<AttrCreateInVO> getExtAttrs() {
		return extAttrs;
	}

	public void setExtAttrs(List<AttrCreateInVO> extAttrs) {
		this.extAttrs = extAttrs;
	}

    @Override
    public String toString() {
        return "AttrBatchCreateInVO [extAttrs=" + extAttrs + "]";
    }
}
