package com.cyberscraft.uep.iam.dto.domain;


import com.fasterxml.jackson.annotation.JsonInclude;
import com.cyberscraft.uep.iam.dto.request.configs.DataSourceProfileVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "pre_import_config_result_domain")
public class PreImportConfigResultDomain extends BaseReturnResult {

    @ApiModelProperty(name = "data", value="AD和LDAP填写提示信息")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private DataSourceProfileVO data;

    public DataSourceProfileVO getData() {
        return data;
    }

    public void setData(DataSourceProfileVO data) {
        this.data = data;
    }
}
