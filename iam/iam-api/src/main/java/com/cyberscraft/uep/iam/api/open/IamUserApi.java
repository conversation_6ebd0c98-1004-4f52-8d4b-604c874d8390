package com.cyberscraft.uep.iam.api.open;

import com.cyberscraft.uep.common.dto.BaseReturnResult;
import com.cyberscraft.uep.common.dto.ReturnResultVO;
import com.cyberscraft.uep.iam.constants.UrlConstants;
import io.swagger.annotations.*;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

import static com.cyberscraft.uep.common.constant.UEPSwaggerConstants.HEADER_BEARER_XXX;
import static com.cyberscraft.uep.common.constant.UEPSwaggerConstants.HEADER_UC_ACCESS_TOKEN;
import static com.cyberscraft.uep.iam.constants.HttpStatus.SC_NOT_FOUND;
import static com.cyberscraft.uep.iam.errors.TransactionErrorDescription.TAG_NOT_FOUND_ERROR_DESC;
import static com.cyberscraft.uep.iam.errors.TransactionErrorDescription.USER_NOT_FOUND_ERROR_CODE;

/**
 * <p>
 *     IAM对外的用户服务接口
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2019-09-11 15:50
 */
@Api(description = "IAM对外不需要鉴权的服务接口", tags = "OpenApi")
public interface IamUserApi {

    @RequestMapping(value = UrlConstants.URL_PREFIX_API_VERSION+"/one_time_password",method = RequestMethod.GET)
    @ApiOperation(nickname = "one_time_password",
            value = "获取一次性密码登录的信息",
            notes = "获取一次性密码登录的信息",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "read:users", description = "")})})
    @ApiResponses(value = {
            @ApiResponse(code = SC_NOT_FOUND, response = BaseReturnResult.class,
                    message = "* 错误码: " + USER_NOT_FOUND_ERROR_CODE + " , 错误消息:"
                            + TAG_NOT_FOUND_ERROR_DESC)
    })
    @ApiImplicitParams({@ApiImplicitParam(paramType = "header", name = "Authorization",
            dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
            required = true)})
    ReturnResultVO generateOtpInfo(
                   @ApiParam(required = true, value = "uid", example = "1") @RequestParam(value = "uid") String uid,
                   @ApiParam(value = "expire_time", example = "1") @RequestParam(value = "expire_time", required = false) Integer expire_time,
                   @ApiParam(value = "send_by", example = "SMS") @RequestParam(value = "send_by") String sendBy ) ;



    @RequestMapping(value = UrlConstants.URL_PREFIX_API_VERSION+"/one_time_password_batch",method = RequestMethod.GET)
    @ApiOperation(nickname = "one_time_password",
            value = "批量获取一次性密码登录的信息",
            notes = "批量获取一次性密码登录的信息",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "read:users", description = "")})})
    @ApiResponses(value = {
            @ApiResponse(code = SC_NOT_FOUND, response = BaseReturnResult.class,
                    message = "* 错误码: " + USER_NOT_FOUND_ERROR_CODE + " , 错误消息:"
                            + TAG_NOT_FOUND_ERROR_DESC)
    })
    @ApiImplicitParams({@ApiImplicitParam(paramType = "header", name = "Authorization",
            dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
            required = true)})
    ReturnResultVO generateOtpInfoBatch(
            @ApiParam(required = true, value = "uids", example = "1") @RequestParam(value = "uids") List<String> uids,
            @ApiParam(value = "expire_time", example = "1") @RequestParam(value = "expire_time", required = false) Integer expire_time,
            @ApiParam(value = "send_by", example = "SMS") @RequestParam(value = "send_by") String sendBy ) ;
}
