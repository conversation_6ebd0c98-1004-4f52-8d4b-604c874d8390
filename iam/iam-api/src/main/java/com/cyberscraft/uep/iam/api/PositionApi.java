package com.cyberscraft.uep.iam.api;

import com.cyberscraft.uep.common.dto.ReturnResultVO;
import com.cyberscraft.uep.iam.constants.UrlConstants;
import com.cyberscraft.uep.iam.dto.request.PositionDTO;
import io.swagger.annotations.*;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.Set;

import static com.cyberscraft.uep.common.constant.UEPSwaggerConstants.*;
import static com.cyberscraft.uep.iam.constants.HttpStatus.SC_INTERNAL_SERVER_ERROR;
import static com.cyberscraft.uep.iam.constants.HttpStatus.SC_NOT_FOUND;
import static com.cyberscraft.uep.iam.errors.TransactionErrorDescription.*;


/**
 * <p>
 *     职位相关API
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2019-07-10 18:32
 */
@RestController
@RequestMapping(value = UrlConstants.URL_PREFIX_API_VERSION + "/positions", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(description = "职位管理配置", tags = "positions")
public interface PositionApi {
	@RequestMapping(value = "", method = {RequestMethod.GET})
	@ResponseBody
	@ApiOperation(nickname = "listPositions",
			value = "获取用户职位列表",
			notes = "获取用户职位列表",
			authorizations = {@Authorization(value = "uc_auth", scopes = {
					@AuthorizationScope(scope = "read:users", description = "")})})
	@ApiResponses(value = {
	})
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String",
					value = HEADER_REQUEST_CONTENT_TYPE,
					defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
			@ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
					value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
					required = true)})
	/**
	 * 获取所有职位列表
	 */
	ReturnResultVO<Set<String>> listPositions();

	@RequestMapping(value = "/app_allow_positions/{client_id}", method = {RequestMethod.GET})
	@ResponseBody
	@ApiOperation(nickname = "getAppPositions",
			value = "获取app所允许的用户职位列表",
			notes = "获取app所允许的用户职位列表",
			authorizations = {@Authorization(value = "uc_auth", scopes = {
					@AuthorizationScope(scope = "read:apps", description = "")})})
	@ApiResponses(value = {
			@ApiResponse(code = SC_NOT_FOUND,
					message = "* 错误码: " + APP_NOT_FOUND_ERROR_CODE + ", 错误消息: " + APP_NOT_FOUND_ERROR_DESC),
			@ApiResponse(code = SC_INTERNAL_SERVER_ERROR,
					message = "* 错误码：" + UNKNOWN_DAO_ERROR_CODE + ", 错误消息: " + UNKNOWN_DAO_ERROR_DESC)
	})
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String",
					value = HEADER_REQUEST_CONTENT_TYPE,
					defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
			@ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
					value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
					required = true)})
	ReturnResultVO<PositionDTO> appAllowedUserPositions(
			@NotEmpty @ApiParam(required = true,
					value = "应用id")@PathVariable(value = "client_id") String clientId);

	@RequestMapping(value = "/app_allow_positions/{client_id}", method = {RequestMethod.POST})
	@ResponseBody
	@ApiOperation(nickname = "updateAppPositions",
			value = "更新app所允许的用户职位列表",
			notes = "更新app所允许的用户职位列表",
			authorizations = {@Authorization(value = "uc_auth", scopes = {
					@AuthorizationScope(scope = "update:apps", description = "")})})
	@ApiResponses(value = {
			@ApiResponse(code = SC_NOT_FOUND,
					message = "* 错误码: " + APP_NOT_FOUND_ERROR_CODE + ", 错误消息: " + APP_NOT_FOUND_ERROR_DESC),
			@ApiResponse(code = SC_INTERNAL_SERVER_ERROR,
					message = "* 错误码：" + UNKNOWN_DAO_ERROR_CODE + ", 错误消息: " + UNKNOWN_DAO_ERROR_DESC)
	})
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String",
					value = HEADER_REQUEST_CONTENT_TYPE,
					defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
			@ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
					value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
					required = true)})
	ReturnResultVO<Void> updateAppAllowedUserPositions(
			@NotEmpty @ApiParam(required = true,
					value = "应用id")@PathVariable(value = "client_id") String clientId,
			@Valid @RequestBody PositionDTO positionDTO);

}
