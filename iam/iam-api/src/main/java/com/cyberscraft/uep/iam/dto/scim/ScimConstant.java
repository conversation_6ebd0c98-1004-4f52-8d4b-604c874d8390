package com.cyberscraft.uep.iam.dto.scim;

import com.cyberscraft.uep.common.dto.QueryPage;
import com.cyberscraft.uep.iam.constants.UrlConstants;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/4/26 5:56 下午
 */
public class ScimConstant {

    public final static String ENTERPRISE_USER_SCHEMA = "urn:ietf:params:scim:schemas:extension:enterprise:2.0:User";
    public final static String USER_SCHEMA = "urn:ietf:params:scim:schemas:core:2.0:User";
    public final static String GROUP_SCHEMA = "urn:ietf:params:scim:schemas:core:2.0:Group";
    public final static String SCHEMA_SCHEMA = "urn:ietf:params:scim:schemas:core:2.0:Schema";
    public final static String RESOURCETYPE_SCHEMA = "urn:ietf:params:scim:schemas:core:2.0:ResourceType";
    public final static String SERVICE_PROVIDER_CONFIG_SCHEMA = "urn:ietf:params:scim:schemas:core:2.0:ServiceProviderConfig";

    public final static String USER_ID = "User";
    public final static String USER_NAME = "User";
    public final static String ENTERPRISE_USER_NAME = "EnterpriseUser";
    public final static String USERS_ENDPOINT = UrlConstants.URL_PREFIX_IAM_SCIM + "/Users";

    public final static String GROUP_ID = "Group";
    public final static String GROUP_NAME = "Group";
    public final static String GROUPS_ENDPOINT = UrlConstants.URL_PREFIX_IAM_SCIM + "/Groups";

    //一次返回的最大条数
    public final static int MAX_RESULTS = QueryPage.MAX_PAGE_SIZE;
    //默认每页条数
    public final static int DEFAULT_PAGESIZE = QueryPage.DEFAULT_PAGE_SIZE;

    public final static String COMMON_ATTR_ID = "id";
    public final static String COMMON_ATTR_META = "meta";
    public final static String COMMON_ATTR_META_RESOURCETYPE = "resourceType";
    public final static String COMMON_ATTR_META_CREATED = "created";
    public final static String COMMON_ATTR_META_LASTMODIFIED = "lastModified";
    public final static String COMMON_ATTR_META_LOCATION = "location";

    public final static String MULTI_ATTR_VALUE = "value";
    public final static String MULTI_ATTR_PRIMARY = "primary";

    public final static String USER_ATTR_USERNAME = "userName";
    public final static String USER_ATTR_DISPLAYNAME = "displayName";
    public final static String USER_ATTR_NICKNAME = "nickName";
    public final static String USER_ATTR_PASSWORD = "password";
    public final static String USER_ATTR_EMAILS = "emails";
    public final static String USER_ATTR_PHONENUMBERS = "phoneNumbers";

    public final static String ENTERPRISE_USER_ATTR_EMPLOYEENUMBER = "employeeNumber";
    public final static String ENTERPRISE_USER_ATTR_ORGANIZATION = "organization";
    public final static String ENTERPRISE_USER_ATTR_DEPARTMENT = "department";

    public final static String GROUP_ATTR_DISPLAYNAME = "displayName";

    public final static List<ResourceAttributes> USER_ATTRIBUTES;
    public final static List<ResourceAttributes> ENTERPRISE_USER_ATTRIBUTES;
    public final static List<ResourceAttributes> GROUP_ATTRIBUTES;
    static {
        ResourceAttributes idAttr = new ResourceAttributes(COMMON_ATTR_ID, DataTypeEnum.STRING.getValue(), false, true, true, MutabilityEnum.readonly, ReturnedEnum.ALWAYS.getValue(), UniquenessEnum.global);
        idAttr.setDescription("A unique identifier for a SCIM resource as defined by the service provider.");

//        ResourceAttributes externalIdAttr = new ResourceAttributes("externalId", DataTypeEnum.STRING.getValue(), false, false, true, MutabilityEnum.readWrite, ReturnedEnum.DEFAULT.getValue(), UniquenessEnum.none);
//        externalIdAttr.setDescription("A String that is an identifier for the resource as defined by the provisioning client.");

        ResourceAttributes metaAttr = new ResourceAttributes(COMMON_ATTR_META, DataTypeEnum.COMPLEX.getValue(), false, false, true, MutabilityEnum.readonly, ReturnedEnum.DEFAULT.getValue(), UniquenessEnum.none);
        metaAttr.setDescription("A complex attribute containing resource metadata.");
        List<ResourceAttributes> metaSubAttrs = new ArrayList<>();
        ResourceAttributes resourceTypeAttr = new ResourceAttributes(COMMON_ATTR_META_RESOURCETYPE, DataTypeEnum.STRING.getValue(), false, false, true, MutabilityEnum.readonly, ReturnedEnum.DEFAULT.getValue(), UniquenessEnum.none);
        ResourceAttributes createdAttr = new ResourceAttributes(COMMON_ATTR_META_CREATED, DataTypeEnum.DATETIME.getValue(), false, false, false, MutabilityEnum.readonly, ReturnedEnum.DEFAULT.getValue(), UniquenessEnum.none);
        ResourceAttributes lastModifiedAttr = new ResourceAttributes(COMMON_ATTR_META_LASTMODIFIED, DataTypeEnum.DATETIME.getValue(), false, false, false, MutabilityEnum.readonly, ReturnedEnum.DEFAULT.getValue(), UniquenessEnum.none);
        ResourceAttributes locationAttr = new ResourceAttributes(COMMON_ATTR_META_LOCATION, DataTypeEnum.STRING.getValue(), false, false, true, MutabilityEnum.readonly, ReturnedEnum.DEFAULT.getValue(), UniquenessEnum.none);
        metaSubAttrs.add(resourceTypeAttr);
        metaSubAttrs.add(createdAttr);
        metaSubAttrs.add(lastModifiedAttr);
        metaSubAttrs.add(locationAttr);
        metaAttr.setSubAttributes(metaSubAttrs);

        ResourceAttributes userNameAttr = new ResourceAttributes(USER_ATTR_USERNAME, DataTypeEnum.STRING.getValue(), false, true, true, MutabilityEnum.readWrite, ReturnedEnum.DEFAULT.getValue(), UniquenessEnum.server);
        userNameAttr.setDescription("A service provider's unique identifier for the user.");

        ResourceAttributes userDisplayNameAttr = new ResourceAttributes(USER_ATTR_DISPLAYNAME, DataTypeEnum.STRING.getValue(), false, false, true, MutabilityEnum.readWrite, ReturnedEnum.DEFAULT.getValue(), UniquenessEnum.none);
        userDisplayNameAttr.setDescription("The name of the user, suitable for display to end-users.");

        ResourceAttributes nickNameAttr = new ResourceAttributes(USER_ATTR_NICKNAME, DataTypeEnum.STRING.getValue(), false, false, true, MutabilityEnum.readWrite, ReturnedEnum.DEFAULT.getValue(), UniquenessEnum.none);
        ResourceAttributes passwordAttr = new ResourceAttributes(USER_ATTR_PASSWORD, DataTypeEnum.STRING.getValue(), false, false, true, MutabilityEnum.writeOnly, ReturnedEnum.NEVER.getValue(), UniquenessEnum.none);
        ResourceAttributes emailsAttr = new ResourceAttributes(USER_ATTR_EMAILS, DataTypeEnum.COMPLEX.getValue(), true, false, true, MutabilityEnum.readWrite, ReturnedEnum.DEFAULT.getValue(), UniquenessEnum.none);
        ResourceAttributes phoneNumbersAttr = new ResourceAttributes(USER_ATTR_PHONENUMBERS, DataTypeEnum.COMPLEX.getValue(), true, false, true, MutabilityEnum.readWrite, ReturnedEnum.DEFAULT.getValue(), UniquenessEnum.none);

        List<ResourceAttributes> multiSubAttrs = new ArrayList<>();
        ResourceAttributes valueAttr = new ResourceAttributes(MULTI_ATTR_VALUE, DataTypeEnum.STRING.getValue(), false, false, true, MutabilityEnum.readWrite, ReturnedEnum.DEFAULT.getValue(), UniquenessEnum.none);
        ResourceAttributes primaryAttr = new ResourceAttributes(MULTI_ATTR_PRIMARY, DataTypeEnum.BOOLEAN.getValue(), false, false, false, MutabilityEnum.readWrite, ReturnedEnum.DEFAULT.getValue(), UniquenessEnum.none);
        multiSubAttrs.add(valueAttr);
        multiSubAttrs.add(primaryAttr);

        emailsAttr.setSubAttributes(multiSubAttrs);
        phoneNumbersAttr.setSubAttributes(multiSubAttrs);

        USER_ATTRIBUTES = new ArrayList<>();
        USER_ATTRIBUTES.add(idAttr);
//        USER_ATTRIBUTES.add(externalIdAttr);
        USER_ATTRIBUTES.add(metaAttr);
        USER_ATTRIBUTES.add(userNameAttr);
        USER_ATTRIBUTES.add(userDisplayNameAttr);
        USER_ATTRIBUTES.add(nickNameAttr);
        USER_ATTRIBUTES.add(passwordAttr);
        USER_ATTRIBUTES.add(emailsAttr);
        USER_ATTRIBUTES.add(phoneNumbersAttr);

        ResourceAttributes employeeNumberAttr = new ResourceAttributes(ENTERPRISE_USER_ATTR_EMPLOYEENUMBER, DataTypeEnum.STRING.getValue(), false, false, true, MutabilityEnum.readWrite, ReturnedEnum.DEFAULT.getValue(), UniquenessEnum.none);
        ResourceAttributes organizationAttr = new ResourceAttributes(ENTERPRISE_USER_ATTR_ORGANIZATION, DataTypeEnum.STRING.getValue(), false, false, true, MutabilityEnum.readWrite, ReturnedEnum.DEFAULT.getValue(), UniquenessEnum.none);
        ResourceAttributes departmentAttr = new ResourceAttributes(ENTERPRISE_USER_ATTR_DEPARTMENT, DataTypeEnum.STRING.getValue(), false, true, true, MutabilityEnum.readWrite, ReturnedEnum.DEFAULT.getValue(), UniquenessEnum.none);
        departmentAttr.setDescription("user's department path.");

        ENTERPRISE_USER_ATTRIBUTES = new ArrayList<>();
        ENTERPRISE_USER_ATTRIBUTES.add(employeeNumberAttr);
        ENTERPRISE_USER_ATTRIBUTES.add(organizationAttr);
        ENTERPRISE_USER_ATTRIBUTES.add(departmentAttr);

        ResourceAttributes groupDisplayNameAttr = new ResourceAttributes(GROUP_ATTR_DISPLAYNAME, DataTypeEnum.STRING.getValue(), false, true, true, MutabilityEnum.readWrite, ReturnedEnum.DEFAULT.getValue(), UniquenessEnum.none);
        groupDisplayNameAttr.setDescription("A human-readable name for the Group.");

        GROUP_ATTRIBUTES = new ArrayList<>();
        GROUP_ATTRIBUTES.add(idAttr);
//        GROUP_ATTRIBUTES.add(externalIdAttr);
        GROUP_ATTRIBUTES.add(metaAttr);
        GROUP_ATTRIBUTES.add(groupDisplayNameAttr);
    }
}
