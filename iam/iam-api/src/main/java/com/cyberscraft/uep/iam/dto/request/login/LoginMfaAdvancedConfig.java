package com.cyberscraft.uep.iam.dto.request.login;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @Date 2024/8/30 14:20
 * @Version 1.0
 * @Description 登录MFA高级设置创建或更新对象
 */
@ApiModel(value = "LoginMfaAdvancedConfig", description = "登录MFA高级设置创建或更新对象")
public class LoginMfaAdvancedConfig {

    @ApiModelProperty(value = "是否开启管理平台的mfa")
    private Boolean enableUsercenter = true;

    @ApiModelProperty(value = "是否开启个人服务台的mfa")
    private Boolean enablePortal = false;


    public Boolean getEnableUsercenter() {
        return enableUsercenter;
    }

    public void setEnableUsercenter(Boolean enableUsercenter) {
        this.enableUsercenter = enableUsercenter;
    }

    public Boolean getEnablePortal() {
        return enablePortal;
    }

    public void setEnablePortal(Boolean enablePortal) {
        this.enablePortal = enablePortal;
    }
}
