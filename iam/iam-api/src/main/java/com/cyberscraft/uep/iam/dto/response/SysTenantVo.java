package com.cyberscraft.uep.iam.dto.response;

import com.cyberscraft.uep.iam.dto.enums.TenantStatusEnum;
import com.cyberscraft.uep.iam.dto.request.tenant.LicenseConfig;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2020-02-28 11:02
 */
@ApiModel(value = "SysTenantVo", description = "租户信息")
public class SysTenantVo implements Serializable {

    private static final long serialVersionUID = -3323583518508635166L;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty(value = "租户名称")
    private String name;

    @ApiModelProperty(value = "租户地址")
    private String address;

    @ApiModelProperty(value = "联系人")
    private String contacts;

    @ApiModelProperty(value = "租户电话")
    private String phone;

    @ApiModelProperty(value = "租户邮箱")
    private String email;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "租户状态")
    private TenantStatusEnum status;

    @ApiModelProperty(value = "租户过期时间")
    private LocalDate expiredTime;

    @ApiModelProperty(value = "临时有效时间")
    private String tempTime;

    @ApiModelProperty(value = "租户开通功能")
    private String[] openModule;

    @ApiModelProperty(value = "租户管理员信息")
    private SysTenantAdminVO admin;

    @ApiModelProperty(value = "授权模块")
    private LicenseConfig licenseConfig;

    @ApiModelProperty(value = "授权用户数")
    private Integer licenseNum;

    @ApiModelProperty(value = "租户类型")
    private Integer tenantType;

    @ApiModelProperty(value = "登录密钥")
    private String loginSecret;

    @ApiModelProperty(value = "版权")
    private String copyright;

    public SysTenantAdminVO getAdmin() {
        return admin;
    }

    public void setAdmin(SysTenantAdminVO admin) {
        this.admin = admin;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getContacts() {
        return contacts;
    }

    public void setContacts(String contacts) {
        this.contacts = contacts;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public TenantStatusEnum getStatus() {
        return status;
    }

    public void setStatus(TenantStatusEnum status) {
        this.status = status;
    }

    public LocalDate getExpiredTime() {
        return expiredTime;
    }

    public void setExpiredTime(LocalDate expiredTime) {
        this.expiredTime = expiredTime;
    }

    public String getTempTime() {
        return tempTime;
    }

    public void setTempTime(String tempTime) {
        this.tempTime = tempTime;
    }

    public String[] getOpenModule() {
        return openModule;
    }

    public void setOpenModule(String[] openModule) {
        this.openModule = openModule;
    }

    public LicenseConfig getLicenseConfig() {
        return licenseConfig;
    }

    public void setLicenseConfig(LicenseConfig licenseConfig) {
        this.licenseConfig = licenseConfig;
    }

    public Integer getLicenseNum() {
        return licenseNum;
    }

    public void setLicenseNum(Integer licenseNum) {
        this.licenseNum = licenseNum;
    }

    public Integer getTenantType() {
        return tenantType;
    }

    public void setTenantType(Integer tenantType) {
        this.tenantType = tenantType;
    }

    public String getLoginSecret() {
        return loginSecret;
    }

    public void setLoginSecret(String loginSecret) {
        this.loginSecret = loginSecret;
    }

    public String getCopyright() {
        return copyright;
    }

    public void setCopyright(String copyright) {
        this.copyright = copyright;
    }
}