package com.cyberscraft.uep.iam.dto.enums;

/**
 * @Author: l<PERSON><PERSON><PERSON>
 * @Date: 2021/11/5
 */
public enum LdapUserStatus implements IBaseEnum{

    //正常
    active(1),
    ACTIVE(1),

    //禁用
    suspended(0),
    SUSPENDED(0),

    // 未生效
    inactive(2),
    INACTIVE(2),

    // 过期
    overdue(4),
    OVERDUE(4),

    // 未登录
    nologin(5),
    NOLOGIN(5);

    private int value;

    LdapUserStatus(int value){
        this.value = value;
    }

    @Override
    public int getValue() {
        return value;
    }
}
