package com.cyberscraft.uep.iam.dto.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.cyberscraft.uep.iam.dto.constraint.as.AuthServerConstants;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Pattern.Flag;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@ApiModel(value = "active/inactive certificate", description = "This parameter is used for just the certificate status will be changed.")
public class CertStatusChangeRequestVO {
    @NotNull(message = "status is not null")
    @Pattern(regexp = AuthServerConstants.REGEX_APP_STATUS, flags = { Flag.CASE_INSENSITIVE }, message = "active or inactive.")
    @ApiModelProperty(name = "status", value = "证书的新状态", required = true, example = "INACTIVE", allowableValues = "ACTIVE,INACTIVE")
    private String status;

    public CertStatusChangeRequestVO() {}

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "ChangeCertStatusVO [status=" + status + "]";
    }
}