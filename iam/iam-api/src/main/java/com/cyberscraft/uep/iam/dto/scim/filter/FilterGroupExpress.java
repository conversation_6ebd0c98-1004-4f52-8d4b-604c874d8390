package com.cyberscraft.uep.iam.dto.scim.filter;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/5/11 2:13 下午
 */
public class FilterGroupExpress implements FilterExpress{

    //and/or
    String join;

    List<FilterExpress> childs = new ArrayList<>();

    boolean whole = false;

    public String getJoin() {
        return join;
    }

    public void setJoin(String join) {
        this.join = join;
    }

    public List<FilterExpress> getChilds() {
        return childs;
    }

    public void setChilds(List<FilterExpress> childs) {
        this.childs = childs;
    }

    @Override
    public boolean isWhole() {
        return whole;
    }

    public void setWhole(boolean whole) {
        this.whole = whole;
    }
}
