package com.cyberscraft.uep.iam.dto.response.ncm;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import java.io.Serializable;

/**
 * Created by Intellij IDEA.
 *
 * @Author: linyuxb
 * @Date: 2025/3/29 11:03
 * @Desc: 消息发送日志列表对象
 */
@JsonNaming(value = PropertyNamingStrategy.class)
public class SendMessageLogListVO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 用户名称
     */
    private String userName;
    /**
     * 用户邮箱
     */
    private String userEmail;
    /**
     * 用户手机号
     */
    private String userPhone;
    /**
     * 模版类型
     */
    private String template;
    /**
     * 短信网关
     */
    private String gateway;
    /**
     * 邮件标题
     */
    private String title;
    /**
     * 消息内容
     */
    private String content;
    /**
     * 邮件、消息 服务
     */
    private String server;

    /**
     * 消息id
     */
    private String id;
    /**
     * 消息发送时间，yyyy-MM-dd HH:mm:ss
     */
    private String sendTime;
    /**
     * 通知类型
     *
     * @see com.cyberscraft.uep.ncm.enums.MessageBizTypeEnum
     */
    private Integer bizType;

    /**
     * 消息发送状态，只返回：发送中 0、成功 1、失败 4
     *
     * @see com.cyberscraft.uep.ncm.enums.MessageSendResultEnum
     */
    private Integer status;
    /**
     * 失败原因
     */
    private String error;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserEmail() {
        return userEmail;
    }

    public void setUserEmail(String userEmail) {
        this.userEmail = userEmail;
    }

    public String getUserPhone() {
        return userPhone;
    }

    public void setUserPhone(String userPhone) {
        this.userPhone = userPhone;
    }

    public String getTemplate() {
        return template;
    }

    public void setTemplate(String template) {
        this.template = template;
    }

    public String getGateway() {
        return gateway;
    }

    public void setGateway(String gateway) {
        this.gateway = gateway;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getServer() {
        return server;
    }

    public void setServer(String server) {
        this.server = server;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getSendTime() {
        return sendTime;
    }

    public void setSendTime(String sendTime) {
        this.sendTime = sendTime;
    }

    public Integer getBizType() {
        return bizType;
    }

    public void setBizType(Integer bizType) {
        this.bizType = bizType;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }
}
