package com.cyberscraft.uep.iam.api.inner;

import com.cyberscraft.uep.common.dto.BaseReturnResult;
import com.cyberscraft.uep.iam.constants.UrlConstants;
import com.cyberscraft.uep.iam.dto.request.AppDetailsVO;
import com.cyberscraft.uep.iam.errors.TransactionErrorDescription;
import io.swagger.annotations.*;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static com.cyberscraft.uep.iam.constants.HttpStatus.SC_INTERNAL_SERVER_ERROR;
import static com.cyberscraft.uep.iam.constants.HttpStatus.SC_NOT_FOUND;
import static com.cyberscraft.uep.iam.errors.TransactionErrorDescription.APP_NOT_FOUND_ERROR_DESC;
import static com.cyberscraft.uep.iam.errors.TransactionErrorDescription.UNKNOWN_DAO_ERROR_DESC;
import static org.springframework.web.bind.annotation.RequestMethod.GET;

@RestController
@RequestMapping(value = UrlConstants.URL_PREFIX_API_VERSION + "/inner/apps", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(description = "内置应用管理", tags = "Inner Api")
public interface BuiltinAppApi {

    /*****************************************************************获取应用详细信息******************************************************************************/
    @RequestMapping(value = "/{client_id}", method = GET)
    @ApiOperation(response = AppDetailsVO.class, nickname = "getAppDetails",
            value = "获取应用详细信息",
            notes = "获取应用详细信息")
    @ApiResponses(value = {
            @ApiResponse(code = SC_NOT_FOUND,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + TransactionErrorDescription.INVALID_CLIENT + ", 错误消息: " + APP_NOT_FOUND_ERROR_DESC),
            @ApiResponse(code = SC_INTERNAL_SERVER_ERROR,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + TransactionErrorDescription.SERVER_ERROR + ", 错误消息: " + UNKNOWN_DAO_ERROR_DESC)
    })
    AppDetailsVO getAppDetails(
            @ApiParam(required = true, value = "应用Id")
            @PathVariable("client_id") String clientId);
}
