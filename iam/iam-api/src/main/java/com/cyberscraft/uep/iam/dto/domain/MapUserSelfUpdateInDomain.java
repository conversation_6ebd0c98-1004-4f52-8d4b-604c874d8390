package com.cyberscraft.uep.iam.dto.domain;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.cyberscraft.uep.iam.dto.enums.UserGender;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@ApiModel(value = "map_user_self_update_request_domain", description = "以下属性支持用户自服务更新用户的信息. 这儿只列出了用户的基本属性，API返回结果可能还包括用户的自定义（扩展）属性")
public class MapUserSelfUpdateInDomain {

	@ApiModelProperty(value = "用户的全名，即包括姓和名", example = "David Beck")
	private String name;
	
	@ApiModelProperty(value = "用户的显示名，即昵称，缺省同用户登录名", example = "david")
	private String nickname;
	
	@ApiModelProperty(value = "用户的别名，可以用来登录，缺省同用户登录名", example = "david_beck")
	private String preferredUsername;
	
	@ApiModelProperty(value = "用户简介的URL链接", example = "http://host:port/imgs/xxxx.jpg")
	private String profile;
	
	@ApiModelProperty(value = "用户头像图片，数据是base64编码，最大300K", example = "data:image/jpeg;base64,")
	private String picture;
	
	@ApiModelProperty(value = "用户相关网站的网址，例如主页，博客等", example = "http://host:port/home")
	private String website;
	
	@ApiModelProperty(value = "用户的电子邮件", example = "<EMAIL>")
	private String email;
	
	@ApiModelProperty(value = "用户的性别", example = UserGender.Description.FEMALE, allowableValues = UserGender.Description.MALE
			+ "," + UserGender.Description.FEMALE + "," + UserGender.Description.SECRET)
	private String gender;
	
	@ApiModelProperty(value = "用户的生日", example = "19821116232050Z")
	private String birthdate;
	
	@ApiModelProperty(value = "用户所在的时区", example = "UTC+08:00")
	private String zoneinfo;
	
	@ApiModelProperty(value = "用户的语言环境", example = "CN")
	private String locale;
	
	@ApiModelProperty(value = "用户的固定电话号码", example = "010-12345678")
	private String telephoneNumber;
	
	@ApiModelProperty(value = "用户的移动电话号码", example = "\"13901234567\"")
	private String phoneNumber;
	
	@ApiModelProperty(value = "用户所在的地址", example = "北京东城区")
	private String address;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getNickname() {
		return nickname;
	}

	public void setNickname(String nickname) {
		this.nickname = nickname;
	}

	public String getPreferredUsername() {
		return preferredUsername;
	}

	public void setPreferredUsername(String preferredUsername) {
		this.preferredUsername = preferredUsername;
	}

	public String getProfile() {
		return profile;
	}

	public void setProfile(String profile) {
		this.profile = profile;
	}

	public String getPicture() {
		return picture;
	}

	public void setPicture(String picture) {
		this.picture = picture;
	}

	public String getWebsite() {
		return website;
	}

	public void setWebsite(String website) {
		this.website = website;
	}

	public String getEmail() {
		return email;
	}

	public void setEmail(String email) {
		this.email = email;
	}

	public String getGender() {
		return gender;
	}

	public void setGender(String gender) {
		this.gender = gender;
	}

	public String getBirthdate() {
		return birthdate;
	}

	public void setBirthdate(String birthdate) {
		this.birthdate = birthdate;
	}

	public String getZoneinfo() {
		return zoneinfo;
	}

	public void setZoneinfo(String zoneinfo) {
		this.zoneinfo = zoneinfo;
	}

	public String getLocale() {
		return locale;
	}

	public void setLocale(String locale) {
		this.locale = locale;
	}

	public String getTelephoneNumber() {
		return telephoneNumber;
	}

	public void setTelephoneNumber(String telephoneNumber) {
		this.telephoneNumber = telephoneNumber;
	}

	public String getPhoneNumber() {
		return phoneNumber;
	}

	public void setPhoneNumber(String phoneNumber) {
		this.phoneNumber = phoneNumber;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}
}
