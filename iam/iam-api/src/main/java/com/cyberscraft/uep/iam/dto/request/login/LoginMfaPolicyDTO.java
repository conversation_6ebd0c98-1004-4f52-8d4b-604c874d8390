package com.cyberscraft.uep.iam.dto.request.login;

import com.cyberscraft.uep.iam.dto.request.LoginWhitelistVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @Date 2024/9/1 10:20
 * @Version 1.0
 * @Description 登录MFA策略创建或更新对象
 */
@ApiModel(value = "LoginMfaPolicyDTO", description = "登录MFA策略创建或更新对象")
public class LoginMfaPolicyDTO {

    @ApiModelProperty(value = "登录白名单")
    private LoginWhitelistVO loginWhitelistVO;

    @ApiModelProperty(value = "登录MFA策略配置")
    private LoginMfaPolicy loginMfaPolicy;

    public LoginWhitelistVO getLoginWhitelistVO() {
        return loginWhitelistVO;
    }

    public void setLoginWhitelistVO(LoginWhitelistVO loginWhitelistVO) {
        this.loginWhitelistVO = loginWhitelistVO;
    }

    public LoginMfaPolicy getLoginMfaPolicy() {
        return loginMfaPolicy;
    }

    public void setLoginMfaPolicy(LoginMfaPolicy loginMfaPolicy) {
        this.loginMfaPolicy = loginMfaPolicy;
    }
}
