package com.cyberscraft.uep.iam.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;

@ApiModel(value = "UserResetPasswordInfo", description = "This view contains info for changing user's password. ")
public class UserResetPwdVO {

    @NotEmpty(message = "could not be empty")
    @ApiModelProperty(name = "newPassword", value = "新密码", dataType = "String", required = true, example="a7654321")
    private String newPassword;

    @NotEmpty(message = "could not be empty")
    @ApiModelProperty(name = "resetPasswordToken", value = "重置密码的token", dataType = "String", required = true, example="82ce33440f1a4169b34f71d4e5638fd1-101F9E46212FE7634CAD16B489C3B9C79B15B7FC77D7EF858FE40EA0423F75D1")
    private String resetPasswordToken;

    @ApiModelProperty(name = "tcode", value = "租户ID", dataType = "String", required = false, example="db1")
    private String tcode;

    public String getResetPasswordToken() {
        return resetPasswordToken;
    }

    public void setResetPasswordToken(String resetPasswordToken) {
        this.resetPasswordToken = resetPasswordToken;
    }

    public String getNewPassword() {
        return newPassword;
    }

    public void setNewPassword(String newPassword) {
        this.newPassword = newPassword;
    }

    public String getTcode() {
        return tcode;
    }

    public void setTcode(String tcode) {
        this.tcode = tcode;
    }

    @Override
    public String toString() {
        return "UserResetPwdVO [newPassword=*, resetPasswordToken=*]";
    }
}
