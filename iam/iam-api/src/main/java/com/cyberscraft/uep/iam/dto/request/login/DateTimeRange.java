package com.cyberscraft.uep.iam.dto.request.login;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

/**
 * @<PERSON> xingjiawei
 * @Date 2024/9/4 16:45
 * @Version 1.0
 * @Description 日期时间段
 */
public class DateTimeRange {

    /**
     * 星期
     */
    private List<LocalDate> rangeDate;

    /**
     * 时间范围["开始时间","结束时间"]
     */
    private List<LocalTime> rangeTime;

    public List<LocalDate> getRangeDate() {
        return rangeDate;
    }

    public void setRangeDate(List<LocalDate> rangeDate) {
        this.rangeDate = rangeDate;
    }

    public List<LocalTime> getRangeTime() {
        return rangeTime;
    }

    public void setRangeTime(List<LocalTime> rangeTime) {
        this.rangeTime = rangeTime;
    }


}
