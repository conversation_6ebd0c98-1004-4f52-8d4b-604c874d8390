package com.cyberscraft.uep.iam.dto.request;

import com.cyberscraft.uep.iam.dto.request.login.LoginPolicyDTO;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.Size;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by cuilong on 8/24/17.
 */

@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class AppUpdateInVO {
    @ApiModelProperty(value = "应用名称", example = "产品信息管理系统")
    @Size(message = "The length of application name is too short or long, min length is 2 and max length is 50", min = 2, max = 50)
    private String clientName;

    @ApiModelProperty(value = "应用图标, 是图片经过Base64编码后的字符串")
    private String logoUri;

    @ApiModelProperty(value = "应用的主页链接", example = "http://example.com")
    private String clientUri;

    @Size(message = "The length of description is too long, max length is 256", max = 256)
    @ApiModelProperty(value = "应用简介", example = "这是一款超赞的软件")
    private String description;

    @ApiModelProperty(value = "授权的重定向URI")
    private Set<String> redirectUris;

    @ApiModelProperty(value = "应用管理员的联系方式")
    private Set<String> contacts;

    @ApiModelProperty(value = "应用的政策链接")
    private String policyUri;

    @ApiModelProperty(value = "应用的服务条款链接")
    private String tosUri;

    @Min(value = 60)
    @Max(value = 7200)
    @ApiModelProperty(value = "访问令牌的超时时长，单位为秒，范围1分钟～2小时，缺省值为900秒（15分钟）", example = "900")
    private Long accessTokenTimeout;

    @Min(value = 0)
    @Max(value = 3600 * 24 * 30)
    @ApiModelProperty(value = "刷新令牌的超时时长，单位为秒，范围1小时～30天，缺省值7天，为0表示永不过期", example = "604800")
    private Long refreshTokenTimeout;

    @Min(value = 60)
    @Max(value = 3600 * 24)
    @ApiModelProperty(value = "身份令牌的超时时长，单位为秒，范围1分钟～1天，缺省值30分钟", example = "1800")
    private Long idTokenTimeout;

    @ApiModelProperty(value = "是否启用用户信息变动通知")
    private Boolean webhookEnable;

    @ApiModelProperty(value = "用户信息变动通知的Web Hook URI")
    private String webhook;

    @ApiModelProperty(value = "重定向URI是否必须为HTTPS，当应用类型为web和spa时有效", example = "true")
    private Boolean enforceHttps;

    @ApiModelProperty(value = "是否加入白名单")
    private Boolean whitelisted;

    @ApiModelProperty(value = "代理授权受信应用ID，可以指定多个")
    private Set<String> trustedPeers;

    @ApiModelProperty(value = "是否启用二维码扫描授权，默认为不启用", example = "true")
    private Boolean qrcodeEnable;

    @ApiModelProperty(value = "扫码登录受信应用ID，可以指定多个")
    private Set<String> trustedScanners;

    @ApiModelProperty(value = "拓展认证属性")
    private List<String> validateFactors;

    @ApiModelProperty(value = "是否开启用户证书认证", example = "true")
    private Boolean authWithCertEnable;

    @ApiModelProperty(value = "是否开启机器帐号", example = "true")
    private Boolean cliModeEnable;

    @ApiModelProperty(value = "是否开启一次性密码登录", example = "true")
    private Boolean oneTimePwdEnable;

    @ApiModelProperty(value = "是否开启一次性密码登录", example = "true")
    private Boolean secureLoginEnable;

    @ApiModelProperty(value = "与该应用关联的用户画像配置的引用Id", example = "abcedfg")
    private String profileRef;

    @ApiModelProperty(value = "更新是的用户信息设置")
    private UserProfileUpdateInVO userProfile;

    @ApiModelProperty(value = "更新APP的登录策略")
    private LoginPolicyDTO loginPolicy;

    @ApiModelProperty(value = "签名算法, none表示不签名，RS256非对称，HS对称，默认采用RS256", example = "RS256")
    private String signingAlg;

    @ApiModelProperty(value = "单点登录认证协议信息 SAML 和 OIDC 协议", example = "OIDC")
    private String authProtocol;

    @ApiModelProperty(value = "配置信息", example = "saml{}")
    private Map<String,Object> config;

    public Boolean getSecureLoginEnable() {
        return secureLoginEnable;
    }

    public void setSecureLoginEnable(Boolean secureLoginEnable) {
        this.secureLoginEnable = secureLoginEnable;
    }

    public Boolean getCliModeEnable() {
        return cliModeEnable;
    }

    public void setCliModeEnable(Boolean cliModeEnable) {
        this.cliModeEnable = cliModeEnable;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public String getLogoUri() {
        return logoUri;
    }

    public void setLogoUri(String logoUri) {
        this.logoUri = logoUri;
    }

    public String getClientUri() {
        return clientUri;
    }

    public void setClientUri(String clientUri) {
        this.clientUri = clientUri;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Set<String> getRedirectUris() {
        return redirectUris;
    }

    public void setRedirectUris(Set<String> redirectUris) {
        this.redirectUris = redirectUris;
    }

    public Set<String> getContacts() {
        return contacts;
    }

    public void setContacts(Set<String> contacts) {
        this.contacts = contacts;
    }

    public String getPolicyUri() {
        return policyUri;
    }

    public void setPolicyUri(String policyUri) {
        this.policyUri = policyUri;
    }

    public String getTosUri() {
        return tosUri;
    }

    public void setTosUri(String tosUri) {
        this.tosUri = tosUri;
    }

    public Long getAccessTokenTimeout() {
        return accessTokenTimeout;
    }

    public void setAccessTokenTimeout(Long accessTokenTimeout) {
        this.accessTokenTimeout = accessTokenTimeout;
    }

    public Long getRefreshTokenTimeout() {
        return refreshTokenTimeout;
    }

    public void setRefreshTokenTimeout(Long refreshTokenTimeout) {
        this.refreshTokenTimeout = refreshTokenTimeout;
    }

    public Long getIdTokenTimeout() {
        return idTokenTimeout;
    }

    public void setIdTokenTimeout(Long idTokenTimeout) {
        this.idTokenTimeout = idTokenTimeout;
    }

    public Boolean getWebhookEnable() {
        return webhookEnable;
    }

    public void setWebhookEnable(Boolean webhookEnable) {
        this.webhookEnable = webhookEnable;
    }

    public String getWebhook() {
        return webhook;
    }

    public void setWebhook(String webhook) {
        this.webhook = webhook;
    }

    public Boolean getEnforceHttps() {
        return enforceHttps;
    }

    public void setEnforceHttps(Boolean enforceHttps) {
        this.enforceHttps = enforceHttps;
    }

    public Boolean getWhitelisted() {
        return whitelisted;
    }

    public void setWhitelisted(Boolean whitelisted) {
        this.whitelisted = whitelisted;
    }

    public Set<String> getTrustedPeers() {
        return trustedPeers;
    }

    public void setTrustedPeers(Set<String> trustedPeers) {
        this.trustedPeers = trustedPeers;
    }

    public Boolean getQrcodeEnable() {
        return qrcodeEnable;
    }

    public void setQrcodeEnable(Boolean qrcodeEnable) {
        this.qrcodeEnable = qrcodeEnable;
    }

    public Set<String> getTrustedScanners() {
        return trustedScanners;
    }

    public void setTrustedScanners(Set<String> trustedScanners) {
        this.trustedScanners = trustedScanners;
    }

    public List<String> getValidateFactors() {
        return validateFactors;
    }

    public void setValidateFactors(List<String> validateFactors) {
        this.validateFactors = validateFactors;
    }

    public Boolean getAuthWithCertEnable() {
        return authWithCertEnable;
    }

    public void setAuthWithCertEnable(Boolean authWithCertEnable) {
        this.authWithCertEnable = authWithCertEnable;
    }

    public Boolean getOneTimePwdEnable() {
        return oneTimePwdEnable;
    }

    public void setOneTimePwdEnable(Boolean oneTimePwdEnable) {
        this.oneTimePwdEnable = oneTimePwdEnable;
    }

    public String getProfileRef() {
        return profileRef;
    }

    public void setProfileRef(String profileRef) {
        this.profileRef = profileRef;
    }

    public String getSigningAlg() {
        return signingAlg;
    }

    public void setSigningAlg(String signingAlg) {
        this.signingAlg = signingAlg;
    }

    public UserProfileUpdateInVO getUserProfile() {
        return userProfile;
    }

    public void setUserProfile(UserProfileUpdateInVO userProfile) {
        this.userProfile = userProfile;
    }

    public LoginPolicyDTO getLoginPolicy() {
        return loginPolicy;
    }

    public void setLoginPolicy(LoginPolicyDTO loginPolicy) {
        this.loginPolicy = loginPolicy;
    }

    public String getAuthProtocol() {
        return authProtocol;
    }

    public void setAuthProtocol(String authProtocol) {
        this.authProtocol = authProtocol;
    }

    public Map<String, Object> getConfig() {
        return config;
    }

    public void setConfig(Map<String, Object> config) {
        this.config = config;
    }

    @Override
    public String toString() {
        return "AppUpdateInVO{" +
                "clientName='" + clientName + '\'' +
                ", logoUri='" + logoUri + '\'' +
                ", clientUri='" + clientUri + '\'' +
                ", description='" + description + '\'' +
                ", redirectUris=" + redirectUris +
                ", contacts=" + contacts +
                ", policyUri='" + policyUri + '\'' +
                ", tosUri='" + tosUri + '\'' +
                ", accessTokenTimeout=" + accessTokenTimeout +
                ", refreshTokenTimeout=" + refreshTokenTimeout +
                ", idTokenTimeout=" + idTokenTimeout +
                ", webhookEnable=" + webhookEnable +
                ", webhook='" + webhook + '\'' +
                ", enforceHttps=" + enforceHttps +
                ", whitelisted=" + whitelisted +
                ", trustedPeers=" + trustedPeers +
                ", qrcodeEnable=" + qrcodeEnable +
                ", trustedScanners=" + trustedScanners +
                ", validateFactors=" + validateFactors +
                ", authWithCertEnable=" + authWithCertEnable +
                ", cliModeEnable=" + cliModeEnable +
                ", oneTimePwdEnable=" + oneTimePwdEnable +
                ", secureLoginEnable=" + secureLoginEnable +
                ", profileRef='" + profileRef + '\'' +
                ", userProfile=" + userProfile +
                ", loginPolicy=" + loginPolicy +
                ", signingAlg='" + signingAlg + '\'' +
                ", authProtocol='" + authProtocol + '\'' +
                ", config=" + config +
                '}';
    }
}
