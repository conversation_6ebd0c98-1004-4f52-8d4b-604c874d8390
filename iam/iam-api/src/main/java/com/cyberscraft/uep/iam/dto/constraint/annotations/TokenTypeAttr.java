package com.cyberscraft.uep.iam.dto.constraint.annotations;

import com.cyberscraft.uep.iam.dto.constraint.validators.TokenTypeAttrValidator;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.FIELD, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = TokenTypeAttrValidator.class)
public @interface TokenTypeAttr {
    String message() default "";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
}
