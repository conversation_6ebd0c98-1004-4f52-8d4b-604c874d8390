package com.cyberscraft.uep.iam.dto.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@ApiModel(value = "sign_in_domain")
public class SignInDomain extends BaseReturnResult {

    @ApiModelProperty(name = "data", value="登录后返回的相关信息")
    private SignInVO data;

    public SignInVO getData() {
        return data;
    }

    public void setData(SignInVO data) {
        this.data = data;
    }

    @ApiModel(value = "SignInVO", description = "After authenticated return user's access token and app's refresh token")
    class SignInVO implements Serializable {
        @ApiModelProperty(name = "access_token_value", value = "用户的access token。这个token遵循OIDC的标准，是一个JWT令牌", dataType = "String", required = true, example = "eyJhbGciOiJIUzI1NiJ9.eyJhdWQiOiJuYXRpb25za3kiLCJzdWIiOiJ0aWJlciIsImhpZCI6IkVNUFRZIiwibnMtdWMtY3R4IjoiRU1QVFkiLCJpc3MiOiJodHRwOi8vd3d3Lm5hdGlvbnNreS5jb20vIiwidHlwIjoiUEFTU1dPUkQiLCJleHAiOjE0Nzk0NDI5MzMxNjMsImlhdCI6MTQ3OTQzOTMzMzE2MywibnMtdWMtc2NwIjoib3JnOmdldCxvcmcuc3ViOmdldCx1c2VyOmdldCx1c2VyOnVwZGF0ZSx1c2VyOnNlbGVjdCJ9.umWHqzjmQV5fk1inc3zfeQLLvSTGSK4RDv3jq9vEF58")
        private String accessTokenValue;

        @ApiModelProperty(name = "refresh_token_value", value = "refresh token.", dataType = "String", required = true, example = "82ce33440f1a4169b34f71d4e5638fd1-101F9E46212FE7634CAD16B489C3B9C79B15B7FC77D7EF858FE40EA0423F75D1")
        private String refreshTokenValue;

        public String getAccessTokenValue() {
            return accessTokenValue;
        }

        public void setAccessTokenValue(String accessTokenValue) {
            this.accessTokenValue = accessTokenValue;
        }

        public String getRefreshTokenValue() {
            return refreshTokenValue;
        }

        public void setRefreshTokenValue(String refreshTokenValue) {
            this.refreshTokenValue = refreshTokenValue;
        }
    }
}
