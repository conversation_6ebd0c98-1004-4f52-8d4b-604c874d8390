package com.cyberscraft.uep.iam.dto.domain;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/12/17 5:42 下午
 */
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class RewriteRule implements Serializable {

    /**
     * 重写操作，0、设置；1、简单替换；2、正则替换
     */
    @ApiModelProperty(value="重写操作，0、设置；1、简单替换；2、正则替换", required = true)
    private int op = 0;

    /**
     * 被替换的key或内容
     */
    @ApiModelProperty(value="要被替换的key或内容", required = true)
    @NotEmpty(message = "target is empty")
    private String target;

    /**
     * 替换后的内容
     */
    @ApiModelProperty(value="替换后的内容", required = true)
    private String replacement;

    public int getOp() {
        return op;
    }

    public void setOp(int op) {
        this.op = op;
    }

    public String getTarget() {
        return target;
    }

    public void setTarget(String target) {
        this.target = target;
    }

    public String getReplacement() {
        return replacement;
    }

    public void setReplacement(String replacement) {
        this.replacement = replacement;
    }
}
