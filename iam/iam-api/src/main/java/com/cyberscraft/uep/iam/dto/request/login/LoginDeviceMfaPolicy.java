package com.cyberscraft.uep.iam.dto.request.login;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/9/5 10:20
 * @Version 1.0
 * @Description 登录MFA设备策略创建或更新对象
 */
@ApiModel(value = "LoginDeviceMfaPolicy", description = "登录MFA设备策略创建或更新对象")
public class LoginDeviceMfaPolicy {

    @ApiModelProperty(value = "是否开启设备mfa策略")
    private boolean enableDeviceMfa = false;

    @ApiModelProperty(value = "设备mfa规则")
    private List<DeviceDetectionRule> deviceDetectionRules;

    public boolean isEnableDeviceMfa() {
        return enableDeviceMfa;
    }

    public void setEnableDeviceMfa(boolean enableDeviceMfa) {
        this.enableDeviceMfa = enableDeviceMfa;
    }

    public List<DeviceDetectionRule> getDeviceDetectionRules() {
        return deviceDetectionRules;
    }

    public void setDeviceDetectionRules(List<DeviceDetectionRule> deviceDetectionRules) {
        this.deviceDetectionRules = deviceDetectionRules;
    }

}
