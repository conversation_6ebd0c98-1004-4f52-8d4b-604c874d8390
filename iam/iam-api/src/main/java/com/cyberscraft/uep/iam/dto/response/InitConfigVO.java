package com.cyberscraft.uep.iam.dto.response;

import java.util.Map;

public class InitConfigVO {
	private Map<String, Object> basic;
	private Map<String, Object> policies;
	private Map<String, Object> email;
//	private List<FieldDictVO> extAttrs;
	
	public Map<String, Object> getBasic() {
		return basic;
	}
	public void setBasic(Map<String, Object> basic) {
		this.basic = basic;
	}
	public Map<String, Object> getPolicies() {
		return policies;
	}
	public void setPolicies(Map<String, Object> policies) {
		this.policies = policies;
	}
	public Map<String, Object> getEmail() {
		return email;
	}
	public void setEmail(Map<String, Object> email) {
		this.email = email;
	}
//	public List<FieldDictVO> getExtAttrs() {
//		return extAttrs;
//	}
//	public void setExtAttrs(List<FieldDictVO> extAttrs) {
//		this.extAttrs = extAttrs;
//	}
    @Override
    public String toString() {
        return "InitConfigVO [basic=" + basic + ", policies=" + policies + ", email=" + email
                + "]";
    }
}
