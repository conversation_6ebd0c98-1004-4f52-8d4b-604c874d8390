package com.cyberscraft.uep.iam.dto.request.configs;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModel;

import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2025/1/6 11:26
 */
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@ApiModel(value = "enterprise_configs",
        description = "request object for update enterprise configs")
public class EnterpriseMsgConfigVO {

    private Map<String,Object> enterpriseMsgConfig;

    public Map<String, Object> getEnterpriseMsgConfig() {
        return enterpriseMsgConfig;
    }

    public void setEnterpriseMsgConfig(Map<String, Object> enterpriseMsgConfig) {
        this.enterpriseMsgConfig = enterpriseMsgConfig;
    }

    @Override
    public String toString() {
        return "EnterpriseMsgConfigVO [enterpriseMsgConfig=" + enterpriseMsgConfig + "]";
    }
}
