package com.cyberscraft.uep.iam.dto.response.ncm;

import java.io.Serializable;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

/***
 *
 * @date 2021/7/8
 * <AUTHOR>
 ***/
@JsonNaming(value = PropertyNamingStrategy.class)
public class MediaVO implements Serializable {

    private static final long serialVersionUID = 1L;
    /***
     * 通知中心对应的媒体文件ID
     */
    private String id;
    /***
     * 媒体文件类型
     */
    private Integer type;

    /***
     * 媒体文件标题
     */
    private String title;

    /***
     * 媒体文件所在平台类型
     */
    private Integer targetType;

    /***
     * 媒体文件所在目标平台文件ID
     */
    private String thirdMediaId;

    /***
     * 已经使用的次数
     */
    private Integer useTimes;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getTargetType() {
        return targetType;
    }

    public void setTargetType(Integer targetType) {
        this.targetType = targetType;
    }

    public String getThirdMediaId() {
        return thirdMediaId;
    }

    public void setThirdMediaId(String thirdMediaId) {
        this.thirdMediaId = thirdMediaId;
    }

    public Integer getUseTimes() {
        return useTimes;
    }

    public void setUseTimes(Integer useTimes) {
        this.useTimes = useTimes;
    }
}
