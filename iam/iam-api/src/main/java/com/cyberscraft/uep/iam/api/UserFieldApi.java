package com.cyberscraft.uep.iam.api;

import com.cyberscraft.uep.common.dto.BaseReturnResult;
import com.cyberscraft.uep.common.dto.QueryPage;
import com.cyberscraft.uep.common.dto.ReturnResultVO;
import com.cyberscraft.uep.iam.constants.HttpStatus;
import com.cyberscraft.uep.iam.constants.UrlConstants;
import com.cyberscraft.uep.iam.dto.enums.OpConstraint;
import com.cyberscraft.uep.iam.dto.request.UserAttrBatchCreateInVO;
import com.cyberscraft.uep.iam.dto.request.UserAttributeUpdateInVO;
import com.cyberscraft.uep.iam.dto.response.FieldDictVO;
import io.swagger.annotations.*;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

import static com.cyberscraft.uep.common.constant.UEPSwaggerConstants.*;
import static com.cyberscraft.uep.iam.constants.HttpStatus.SC_BAD_REQUEST;
import static com.cyberscraft.uep.iam.errors.TransactionErrorDescription.*;

@RestController
@RequestMapping(value = UrlConstants.URL_PREFIX_API_VERSION+"/user_attrs", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(description = "用户属性定义", tags = "User attribute schema")
public interface UserFieldApi {
	@RequestMapping(value = "", method = RequestMethod.GET)
	@ResponseBody
	@ApiOperation(nickname = "getAllAttributes",
		value = "查询用户属性的定义",
		notes = "查询用户属性的定义。"
				+ "\n\r查询匹配的标准是字符串的子串匹配方式(不区分大小写)。"
				+ "\n\r**该API不支持排序。** ",
        authorizations = {
        		@Authorization(value = "uc_auth",
        				scopes = {
        						@AuthorizationScope(scope = "read:user_attr_all", description = "查询用户的所有属性的定义")
        						}
        		)
        		})
	@ApiResponses(value = {@ApiResponse(code = SC_BAD_REQUEST,
			response = BaseReturnResult.class,
			message = "* 错误码: " + PAGE_SIZE_EXCEED_MAX_ERROR_CODE
					+ ", 错误消息: " + PAGE_SIZE_EXCEED_MAX_ERROR_DESC
					)})
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true),
	})
	ReturnResultVO<QueryPage<FieldDictVO>> searchFieldDicts(
			@ApiParam(
					value = "作为过滤条件，在子串匹配时过滤属性名称，属性显示名，属性描述",
					example = "name")
			@RequestParam(value = "q", required = false) String filter,

			@ApiParam(
					value = "返回扩展属性还是基本属性?", example = "false", allowableValues = "true,false")
			@RequestParam(value = "basic", required = false) Boolean basic,

			@ApiParam(
					value = "设置扩展属性操作约束查询条件。1：用户可修改；2：管理员可修改；3：系统维护",
					example = "1", allowableValues = "USER,ADMIN,SYSTEM")
			@RequestParam(value = "op_constraint", required = false) OpConstraint opConstraint,

			@ApiParam(
					value = "是否强制输入项", example = "true", allowableValues = "true, false")
			@RequestParam(value = "mandatory", required = false) Boolean mandatory,

			@ApiParam(
					value = "是否可以作为身份认证的额外因子", example = "true", allowableValues = "true, false")
			@RequestParam(value = "extra_auth_factor", required = false)  Boolean extraAuthfactor,

			@ApiParam(
					value = "是否可以作为用户Profile属性", example = "true", allowableValues = "true, false")
			@RequestParam(value = "as_profile", required = false)  Boolean asProfile,

			@ApiParam(
					value = "是否可以作为同步导入属性", example = "true", allowableValues = "true,false")
			@RequestParam(value = "as_import", required = false) Boolean asImport,

			@ApiParam(
					value = "是否可以作为ID Token的Claim属性", example = "true", allowableValues = "true,false")
			@RequestParam(value = "as_claim", required = false) Boolean asClaim,

			@ApiParam(
					value = "是否支持搜索条件?", example = "false", allowableValues = "true,false")
			@RequestParam(value = "searchable", required = false) Boolean searchable,

			@ApiParam(
					value = "要获取的结果页号[1..N], 缺省是："
							+ QueryPage.DEFAULT_PAGE_NUMBER
							+ ". \n\r如果使用了无效的页号，API将使用缺省页号。", example = QueryPage.DEFAULT_PAGE_NUMBER +"")
			@RequestParam(value = "page", required = false) Integer page,

			@ApiParam(
					value = "获取的每页的记录数[1..N], 缺省是："
							+ QueryPage.DEFAULT_PAGE_SIZE
							+ ". \n\r**每页允许的最大记录数为:" + QueryPage.MAX_PAGE_SIZE
							+ ". \n\r如果使用了无效的记录数，API将使用缺省记录数。", example = QueryPage.DEFAULT_PAGE_SIZE +"")
			@RequestParam(value = "size", required = false) Integer size,

			@ApiParam(
					value = "排序标准的格式如下：'property1,(asc|desc)'"
							+ "\n\r多列排序也支持，例如： 'sort=property1,asc&sort=property2,desc'")
			@RequestParam(value = "sort", required = false) List<String> sort);

	@RequestMapping(value = "/{id}", method = RequestMethod.GET)
	@ResponseBody
	@ApiOperation(nickname = "getAttributeDetailById",
		value = "根据用户属性定义的id获取属性定义的详细信息",
		notes = "根据用户属性定义的id获取属性定义的详细信息",
		        authorizations = {
		        		@Authorization(value = "uc_auth",
		        				scopes = {
		        						@AuthorizationScope(scope = "read:user_attrs", description = "")
		        						}
		        		)
		        		})
	@ApiResponses(value = {
		@ApiResponse(
				code = HttpStatus.SC_NOT_FOUND,
				response = BaseReturnResult.class,
				message = "错误码: " + USER_SCHEMA_ATTR_NOT_FOUND_ERROR_CODE + ", 错误消息: " + USER_SCHEMA_ATTR_NOT_FOUND_ERROR_DESC)})
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true)
	})
	ReturnResultVO<FieldDictVO> getById(@ApiParam(required=true, value = "用户属性定义的id", example = "1") @PathVariable("id") Long id);

	@RequestMapping(value = { "" }, method = RequestMethod.POST)
	@ResponseBody
	@ApiOperation(nickname = "createAttributes",
		notes = "创建多个用户的扩展属性",
		value = "创建多个用户的扩展属性",
		        authorizations = {
		        		@Authorization(value = "uc_auth",
		        				scopes = {
		        						@AuthorizationScope(scope = "create:user_attrs", description = "")
		        						}
		        		)
		        		})
	@ApiResponses(value = {
		@ApiResponse(
				code = HttpStatus.SC_BAD_REQUEST,
				response = BaseReturnResult.class,
				message = "错误码: " + INVALID_REQUEST_PARAM_ERROR_CODE + ", 错误消息: " + INVALID_REQUEST_PARAM_ERROR_DESC),
		@ApiResponse(
				code = HttpStatus.SC_CONFLICT,
				response = BaseReturnResult.class,
				message = "错误码: " + USER_SCHEMA_ATTR_EXIST_ERROR_CODE + ", 错误消息: " + USER_SCHEMA_ATTR_EXIST_ERROR_DESC),
		@ApiResponse(
				code = HttpStatus.SC_NOT_ACCEPTABLE,
				response = BaseReturnResult.class,
				message = "错误码: " + USER_SCHEMA_ATTR_MAX_COUNT_REACHED_ERROR_CODE + ", 错误消息: " + USER_SCHEMA_ATTR_MAX_COUNT_REACHED_ERROR_DESC)})
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
			@ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true)
	})
	ReturnResultVO<Void> batchCreateFieldDicts(@Valid @RequestBody UserAttrBatchCreateInVO input);

	@RequestMapping(value = "/{domainName}", method = RequestMethod.DELETE)
	@ResponseBody
	@ApiOperation(nickname = "deleteAttribute",
			notes = "删除用户扩展属性",
			value = "删除用户扩展属性",
			authorizations = {
					@Authorization(value = "uc_auth",
							scopes = {
									@AuthorizationScope(scope = "delete:user_attrs", description = "")
							}
					)
			})
	@ApiResponses(value = {
			@ApiResponse(
					code = HttpStatus.SC_NOT_FOUND,
					response = BaseReturnResult.class,
					message = "错误码: " + USER_SCHEMA_ATTR_NOT_FOUND_ERROR_CODE + ", 错误消息: " + USER_SCHEMA_ATTR_NOT_FOUND_ERROR_DESC)})
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
			@ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true)
	})
	ReturnResultVO<?> deleteFieldDict(@PathVariable("domainName") String domainName);

	@RequestMapping(value = "/{id}", method = RequestMethod.PATCH)
	@ResponseBody
	@ApiOperation(nickname = "updateAttribute",
		notes = "根据用户属性定义的id来更新用户属性定义的信息",
		value = "根据用户属性定义的id来更新用户属性定义的信息",
		        authorizations = {
		        		@Authorization(value = "uc_auth",
		        				scopes = {
		        						@AuthorizationScope(scope = "update:user_attrs", description = "")
		        						}
		        		)
		        		})
	@ApiResponses(value = {
			@ApiResponse(
					code = HttpStatus.SC_NOT_FOUND,
					response = BaseReturnResult.class,
					message = "错误码: " + USER_SCHEMA_ATTR_NOT_FOUND_ERROR_CODE + ", 错误消息: " + USER_SCHEMA_ATTR_NOT_FOUND_ERROR_DESC)})
	@ApiImplicitParams({
			@ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
			@ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true)
	})
	ReturnResultVO<FieldDictVO> updateFieldDict(
			@ApiParam(required=true, value = "用户属性定义的id",example = "1")
			@PathVariable("id") Long id,
			@ApiParam(value="更新用户属性定义的请求信息" )
			@Valid @RequestBody UserAttributeUpdateInVO userAttribute);
}
