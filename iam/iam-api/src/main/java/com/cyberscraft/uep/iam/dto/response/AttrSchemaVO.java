package com.cyberscraft.uep.iam.dto.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.cyberscraft.uep.iam.dto.enums.FieldDataType;
import com.cyberscraft.uep.common.util.StringUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "extended_attribute",
	description = "response object for create, get, search user attributes operations")
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
//@JsonInclude(Include.NON_NULL)
public class AttrSchemaVO implements  Comparable<AttrSchemaVO> {

    public AttrSchemaVO() {

    }

    public AttrSchemaVO(AttrSchemaVO vo) {
        this.setId(vo.getId());
        this.setSingleValue(vo.getSingleValue());
        this.setBasicAttribute(vo.getBasicAttribute());
        this.setDisplayName(vo.getDisplayName());
        this.setDomainName(vo.getDomainName());
        this.setDataType(vo.getDataType());
        this.setUnique(vo.getUnique());
        this.setMandatory(vo.getMandatory());
        this.setSearchable(vo.getSearchable());
        this.setOpConstraint(vo.getOpConstraint());
        this.setConstraintRule(vo.getConstraintRule());
        this.setDescription(vo.getDescription());
        this.setAsImport(vo.getAsImport());
        this.setUpdate(vo.getUpdate());
        this.setShowPortal(vo.getShowPortal());
    }

	@ApiModelProperty(value = "属性的id", required = true, example="eb60a5cc-1f26-4abe-b6ac-76d220a7a477")
	private String id;

	@ApiModelProperty(value = "属性是否是基本属性", required = true)
	private Boolean basicAttribute;

	@ApiModelProperty(value = "属性的显示名称", required = true, example="业余爱好")
	private String displayName;

	@ApiModelProperty(value = "属性的名称，即领域名称", required = true, example="hobby")
	private String domainName;

	@JsonIgnore
	private String ldapName;

	@ApiModelProperty(value = "属性值的数据类型", required = true, allowableValues = "STRING, INT, FLOAT, DATETIME", example="STRING")
	private FieldDataType dataType;

	@ApiModelProperty(value = "属性值是否唯一", required = true)
	private Boolean unique;

	@ApiModelProperty(value = "属性值是否是必须的", required = true)
	private Boolean mandatory;

	@ApiModelProperty(value = "属性是否可以作为搜索条件", required = true)
	private Boolean searchable;

	@ApiModelProperty(value = "属性的操作约束，比如是否只有管理员才可以读写", required = true, allowableValues = "1,2,3", example="2")
	private Integer opConstraint;

	@ApiModelProperty(value = "属性的值的约束条件，主要是针对扩展属性，目前未实现", example="{ \"min\":1, \"max\":100 }")
	private String constraintRule = "{}";

	@ApiModelProperty(value = "属性的描述信息", example="属性的注意事项: blah, blah...")
	private String description;

	@ApiModelProperty(value = "属性值是否支持单值属性：单值属性(true), 多值属性(false)", example="false")
	private Boolean singleValue = true;

    @ApiModelProperty(value = "属性是否可以作为导入属性", example = "true")
    private Boolean asImport;

	@ApiModelProperty(value = "属性是否可以更新", example = "true")
	private Boolean update;

	@ApiModelProperty(value = "属性是否在个人门户展示", example = "true")
	private Boolean showPortal;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public Boolean getBasicAttribute() {
		return basicAttribute;
	}

	public void setBasicAttribute(Boolean basicAttribute) {
		this.basicAttribute = basicAttribute;
	}

	public String getDisplayName() {
		return displayName;
	}

	public void setDisplayName(String displayName) {
		this.displayName = displayName;
	}

	public String getDomainName() {
		return domainName;
	}

	public void setDomainName(String domainName) {
		this.domainName = domainName;
	}

	public String getLdapName() {
		return ldapName;
	}

	public void setLdapName(String ldapName) {
		this.ldapName = ldapName;
	}

	public FieldDataType getDataType() {
		return dataType;
	}

	public void setDataType(FieldDataType dataType) {
		this.dataType = dataType;
	}

	public Boolean getUnique() {
		return unique;
	}

	public void setUnique(Boolean unique) {
		this.unique = unique;
	}

	public Boolean getMandatory() {
		return mandatory;
	}

	public void setMandatory(Boolean mandatory) {
		this.mandatory = mandatory;
	}

	public Boolean getSearchable() {
		return searchable;
	}

	public void setSearchable(Boolean searchable) {
		this.searchable = searchable;
	}

	public Integer getOpConstraint() {
		return opConstraint;
	}

	public void setOpConstraint(Integer opConstraint) {
		this.opConstraint = opConstraint;
	}

	public String getConstraintRule() {
		return constraintRule;
	}

	public void setConstraintRule(String constraintRule) {
		this.constraintRule = constraintRule;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public Boolean getSingleValue() {
		return singleValue;
	}

	public void setSingleValue(Boolean singleValue) {
		this.singleValue = singleValue;
	}

    public Boolean getAsImport() {
        return asImport;
    }

    public void setAsImport(Boolean asImport) {
        this.asImport = asImport;
    }

	public Boolean getUpdate() {
		return update;
	}

	public void setUpdate(Boolean update) {
		this.update = update;
	}

	public Boolean getShowPortal() {
		return showPortal;
	}

	public void setShowPortal(Boolean showPortal) {
		this.showPortal = showPortal;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((basicAttribute == null) ? 0 : basicAttribute.hashCode());
		result = prime * result + ((constraintRule == null) ? 0 : constraintRule.hashCode());
		result = prime * result + ((dataType == null) ? 0 : dataType.hashCode());
		result = prime * result + ((description == null) ? 0 : description.hashCode());
		result = prime * result + ((displayName == null) ? 0 : displayName.hashCode());
		result = prime * result + ((domainName == null) ? 0 : domainName.hashCode());
		result = prime * result + ((id == null) ? 0 : id.hashCode());
		result = prime * result + ((ldapName == null) ? 0 : ldapName.hashCode());
		result = prime * result + ((mandatory == null) ? 0 : mandatory.hashCode());
		result = prime * result + ((opConstraint == null) ? 0 : opConstraint.hashCode());
		result = prime * result + ((searchable == null) ? 0 : searchable.hashCode());
		result = prime * result + ((singleValue == null) ? 0 : singleValue.hashCode());
		result = prime * result + ((unique == null) ? 0 : unique.hashCode());
		result = prime * result + ((update == null) ? 0 : update.hashCode());
		result = prime * result + ((showPortal == null) ? 0 : showPortal.hashCode());

		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		AttrSchemaVO other = (AttrSchemaVO) obj;
		if (domainName == null) {
			if (other.domainName != null)
				return false;
		} else if (!domainName.equals(other.domainName))
			return false;
		if (id == null) {
			if (other.id != null)
				return false;
		} else if (!id.equals(other.id))
			return false;
		if (ldapName == null) {
			if (other.ldapName != null)
				return false;
		} else if (!ldapName.equals(other.ldapName))
			return false;

		return true;
	}

	@Override
	public String toString() {
		return "FieldDictVO [id=" + id + ", basicAttribute=" + basicAttribute + ", displayName="
				+ displayName + ", domainName=" + domainName + ", ldapName=" + ldapName + ", dataType=" + dataType
				+ ", unique=" + unique + ", mandatory=" + mandatory + ", searchable=" + searchable + ", opConstraint="
				+ opConstraint + ", constraintRule=" + constraintRule + ", description=" + description
				+ ", singleValue=" + singleValue + "]";
	}


	@Override
	/**
	 * default sorting.
	 * sort by domainName, displayName, description.
	 */
	public int compareTo(AttrSchemaVO o) {
		int i = StringUtil.compareTo(this.domainName, o.domainName);
		if (i != 0 ) {
			return i;
		}

		i = StringUtil.compareTo(this.displayName, o.displayName);
		if ( i != 0 ) {
			return i;
		}

		i = StringUtil.compareTo(this.description, o.description);
		return i;
	}
}
