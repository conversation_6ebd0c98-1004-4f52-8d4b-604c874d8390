package com.cyberscraft.uep.iam.dto.response;

import com.cyberscraft.uep.iam.dto.request.AppLoginVO;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.cyberscraft.uep.iam.dto.enums.ClientProfileV2;
import com.cyberscraft.uep.iam.dto.enums.ClientStatusV2;
import com.cyberscraft.uep.iam.dto.enums.GateWayStatus;
import io.swagger.annotations.ApiModelProperty;

import java.util.Set;

/**
 * Created by cuilong on 9/6/17.
 */
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class ApplicationItemVO {
    @ApiModelProperty(value = "应用的唯一id", example = "0LiolBsiCVInH6wZQ7Rb5q5wiLcB4jfB", required = true)
    private String clientId;

    @ApiModelProperty(value = "关联应用市场的应用clientId")
    private String linkClientId;

    @ApiModelProperty(value = "应用名称", example = "产品信息管理系统", required = true)
    private String clientName;

    @ApiModelProperty(value = "应用类型", example = "native", required = true)
    private ClientProfileV2 applicationType;

    @ApiModelProperty(value = "应用图标, 是图片经过Base64编码后的字符串")
    private String logoUri;

    @ApiModelProperty(value = "应用主页链接", example = "http://example.com")
    private String clientUri;

    @ApiModelProperty(value = "应用简介", example = "这是一款超赞的软件")
    private String description;

    @ApiModelProperty(value = "应用已授权", example = "true")
    private Boolean authed = true;

    @ApiModelProperty(value = "应用状态", dataType = "String", example="active")
    private ClientStatusV2 status;

    @ApiModelProperty(value = "授权记录")
    private AuthApprovalVO approval;

    @ApiModelProperty(value = "用户ID")
    private String uid;

    @ApiModelProperty(value = "授权范围")
    private Set<String> scope;

    @ApiModelProperty(value = "应用来源", example = "玎玎同步")
    private String appSrc;

    @ApiModelProperty(value = "应用所属企业", example = "某企业")
    private String belongCorp;

    @ApiModelProperty(value = "应用分类id数组")
    private Set<String> customClass;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private long updateTime;

    @ApiModelProperty(value = "应用的网关状态", example = "active", required = true)
    private GateWayStatus gwStatus;

    private Long openAppAuthId;

    /**
     * 是否允许自助申请：1、允许；0、不允许
     */
    private Integer enableApply;

    private AppLoginVO appLoginVO;

    @ApiModelProperty(value = "是否开启密码凭证", example = "false", required = false)
    private Boolean enableCredential;

    @ApiModelProperty(value = "应用打开方式：CURRENT_PAGE、NEW_WINDOW、CUSTOM")
    private String openMethod;

    public Long getOpenAppAuthId() {
        return openAppAuthId;
    }

    public void setOpenAppAuthId(Long openAppAuthId) {
        this.openAppAuthId = openAppAuthId;
    }
//    @ApiModelProperty(value = "许可类型")
//    private String grantType;

    public String getAppSrc() {
        return appSrc;
    }

    public void setAppSrc(String appSrc) {
        this.appSrc = appSrc;
    }

    public String getBelongCorp() {
        return belongCorp;
    }

    public void setBelongCorp(String belongCorp) {
        this.belongCorp = belongCorp;
    }

    public long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(long updateTime) {
        this.updateTime = updateTime;
    }

    public GateWayStatus getGwStatus() {
        return gwStatus;
    }

    public void setGwStatus(GateWayStatus gwStatus) {
        this.gwStatus = gwStatus;
    }

    public ClientStatusV2 getStatus() {
        return status;
    }

    public void setStatus(ClientStatusV2 status) {
        this.status = status;
    }

    public String getClientId() {
        return clientId;
    }

    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    public String getLinkClientId() {
        return linkClientId;
    }

    public void setLinkClientId(String linkClientId) {
        this.linkClientId = linkClientId;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public ClientProfileV2 getApplicationType() {
        return applicationType;
    }

    public void setApplicationType(ClientProfileV2 applicationType) {
        this.applicationType = applicationType;
    }

    public String getLogoUri() {
        return logoUri;
    }

    public void setLogoUri(String logoUri) {
        this.logoUri = logoUri;
    }

    public String getClientUri() {
        return clientUri;
    }

    public void setClientUri(String clientUri) {
        this.clientUri = clientUri;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Boolean getAuthed() {
        return authed;
    }

    public void setAuthed(Boolean authed) {
        this.authed = authed;
    }

    public AuthApprovalVO getApproval() {
        return approval;
    }

    public void setApproval(AuthApprovalVO approval) {
        this.approval = approval;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public Set<String> getScope() {
        return scope;
    }

    public void setScope(Set<String> scope) {
        this.scope = scope;
    }

    public Set<String> getCustomClass() {
        return customClass;
    }

    public void setCustomClass(Set<String> customClass) {
        this.customClass = customClass;
    }

    public Integer getEnableApply() {
        return enableApply;
    }

    public void setEnableApply(Integer enableApply) {
        this.enableApply = enableApply;
    }

    public AppLoginVO getAppLoginVO() {
        return appLoginVO;
    }

    public void setAppLoginVO(AppLoginVO appLoginVO) {
        this.appLoginVO = appLoginVO;
    }

    public Boolean getEnableCredential() {
        return enableCredential;
    }

    public void setEnableCredential(Boolean enableCredential) {
        this.enableCredential = enableCredential;
    }

    public String getOpenMethod() {
        return openMethod;
    }

    public void setOpenMethod(String openMethod) {
        this.openMethod = openMethod;
    }

    //    public String getGrantType() {
//        return grantType;
//    }
//
//    public void setGrantType(String grantType) {
//        this.grantType = grantType;
//    }
//
}
