package com.cyberscraft.uep.iam.dto.enums;

/**
 * Created by <PERSON> on 13/1/2017.
 * enum for import task status.
 * UC app will only process one task in the same time, and this status in ldap to take task control.
 */
public enum ImportTaskStatus implements IBaseEnum{
    /**
     * 初始化中
     */
    INITIALING(0),

    /**
     * 可用
     */
    AVAILABLE(1),

    /**
     * 处理中
     */
    PROCESSING(2),

    /**
     * 已结束
     */
    DONE(3),

    /**
     * 因错误而结束
     */
    ERROR(4),

    /**
     * 已删除
     */
    DELETED(-1);

    private int value;

    ImportTaskStatus(int value){
        this.value = value;
    }

    @Override
    public int getValue() {
        return this.value;
    }
}
