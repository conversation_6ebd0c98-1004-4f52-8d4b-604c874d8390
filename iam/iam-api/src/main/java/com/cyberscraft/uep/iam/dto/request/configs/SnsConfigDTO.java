package com.cyberscraft.uep.iam.dto.request.configs;

import com.cyberscraft.uep.iam.dto.enums.BooleanEnums;
import com.cyberscraft.uep.iam.dto.enums.ConnectorStatusEnum;
import com.cyberscraft.uep.iam.dto.enums.ConnectorTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Map;

/***
 *
 * @date 2021/6/12
 * <AUTHOR>
 ***/
@ApiModel(value = "SnsConfigDTO",
        description = "企业第三方平台配置的增加修修改请求对像")
public class SnsConfigDTO implements Serializable {

    @ApiModelProperty(value = "SNS配置ID", required = true, hidden = true)
    private Long id;

    @ApiModelProperty(value = "目标目录服务类型", required = true, dataType = "String", example = "AD")
    @NotNull(message = "could not be empty")
    private ConnectorTypeEnum type;


    @ApiModelProperty(value = "连接器名称", required = true, dataType = "String", example = "dingding")
    @NotBlank(message = "could not be empty")
    private String name;

    @ApiModelProperty(value = "目标服务租户业务关键字,钉钉，企业微信等对应的配置中的corpId,AD/LDAP中的BaseDN值，IAM/UEM中的租户ID", required = true, dataType = "String", example = "abcdefg")
    @Size(max = 128)
    private String corpId;

    @ApiModelProperty(value = "是否为默认配置", required = false, dataType = "String")
    private Boolean isDefault = false;

    @ApiModelProperty(value = "链接器的状态", required = false, dataType = "String")
    private ConnectorStatusEnum status;

    @ApiModelProperty(value = "详情配置信息", required = true, dataType = "domain")
    private Map<String, Object> config;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public ConnectorTypeEnum getType() {
        return type;
    }

    public void setType(ConnectorTypeEnum type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCorpId() {
        return corpId;
    }

    public void setCorpId(String corpId) {
        this.corpId = corpId;
    }

    public Boolean getDefault() {
        return isDefault;
    }

    public void setDefault(Boolean aDefault) {
        isDefault = aDefault;
    }

    public ConnectorStatusEnum getStatus() {
        return status;
    }

    public void setStatus(ConnectorStatusEnum status) {
        this.status = status;
    }

    public Map<String, Object> getConfig() {
        return config;
    }

    public void setConfig(Map<String, Object> config) {
        this.config = config;
    }
}
