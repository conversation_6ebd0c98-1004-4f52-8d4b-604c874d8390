package com.cyberscraft.uep.iam.dto.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.cyberscraft.uep.common.dto.BaseReturnResult;
import com.cyberscraft.uep.iam.dto.response.UserCertificateListVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "search_certificate_list_response")
public class ReturnResultCertList extends BaseReturnResult {

    @ApiModelProperty(name = "data", value="返回证书列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private UserCertificateListVO data;

    public UserCertificateListVO getData() {
        return data;
    }

    public void setData(UserCertificateListVO data) {
        this.data = data;
    }
}