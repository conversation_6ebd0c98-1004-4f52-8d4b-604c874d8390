package com.cyberscraft.uep.iam.dto.response;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;

@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class AppGateWayVo {
    @ApiModelProperty(value = "应用名称", example = "产品信息管理系统", required = true)
    private int status = 0;
    @ApiModelProperty(value = "应用路由地址")
    @NotEmpty(message = "routeUrl")
    @Pattern(regexp = "^(?:https?://)[\\w]{1,}(?:\\.?[\\w]{1,})+[\\w-_/?&=#%:]*$", message = "invalid routeUrl")
    private String routeUrl = "";

    @ApiModelProperty(value = "访问标识")
    @NotEmpty(message = "accessPrefix")
    @Pattern(regexp = "^/[a-zA-Z0-9_\\-]+")
    private String accessPrefix = "";
    @ApiModelProperty(value = "路由断言类型，1：path，2：host")
    private Integer predicateType;

    @ApiModelProperty(value = "是否支持STS")
    private Boolean enableSts;

    @ApiModelProperty(value = "path截取数")
    private Integer pathStripPrefix = 0;

    @ApiModelProperty(value = "post类型时，域名地址")
    private String hostAddr;

    @ApiModelProperty(value = "STS路由凭证参数名称")
    private String stsParamName;

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getRouteUrl() {
        return routeUrl;
    }

    public void setRouteUrl(String routeUrl) {
        this.routeUrl = routeUrl;
    }

    public String getAccessPrefix() {
        return accessPrefix;
    }

    public void setAccessPrefix(String accessPrefix) {
        this.accessPrefix = accessPrefix;
    }

    public Integer getPredicateType() {
        return predicateType;
    }

    public void setPredicateType(Integer predicateType) {
        this.predicateType = predicateType;
    }

    public Boolean getEnableSts() {
        return enableSts;
    }

    public void setEnableSts(Boolean enableSts) {
        this.enableSts = enableSts;
    }

    public Integer getPathStripPrefix() {
        return pathStripPrefix;
    }

    public void setPathStripPrefix(Integer pathStripPrefix) {
        this.pathStripPrefix = pathStripPrefix;
    }

    public String getHostAddr() {
        return hostAddr;
    }

    public void setHostAddr(String hostAddr) {
        this.hostAddr = hostAddr;
    }

    public String getStsParamName() {
        return stsParamName;
    }

    public void setStsParamName(String stsParamName) {
        this.stsParamName = stsParamName;
    }
}
