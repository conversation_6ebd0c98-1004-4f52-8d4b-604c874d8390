package com.cyberscraft.uep.iam.api;

import com.cyberscraft.uep.common.dto.BaseReturnResult;
import com.cyberscraft.uep.common.dto.QueryPage;
import com.cyberscraft.uep.common.dto.ReturnResultVO;
import com.cyberscraft.uep.iam.constants.UrlConstants;
import com.cyberscraft.uep.iam.dto.domain.AppGatewayConfig;
import com.cyberscraft.uep.iam.dto.domain.MapUserGetListReturnResult;
import com.cyberscraft.uep.iam.dto.request.*;
import com.cyberscraft.uep.iam.dto.request.login.LoginMfaPolicyDTO;
import com.cyberscraft.uep.iam.dto.request.login.LoginPolicyDTO;
import com.cyberscraft.uep.iam.dto.request.sso.SamlConfig;
import com.cyberscraft.uep.iam.dto.response.BatchOperationVO;
import com.cyberscraft.uep.iam.dto.response.OrgAppInfoChildTreeOutVO;
import com.cyberscraft.uep.iam.dto.response.SysTenantVo;
import com.cyberscraft.uep.iam.dto.response.login.LoginPolicyVO;
import com.cyberscraft.uep.iam.errors.TransactionErrorDescription;
import io.swagger.annotations.*;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.io.IOException;
import java.util.List;
import java.util.Map;

import static com.cyberscraft.uep.common.constant.UEPSwaggerConstants.*;
import static com.cyberscraft.uep.iam.constants.HttpStatus.*;
import static com.cyberscraft.uep.iam.errors.TransactionErrorDescription.*;
import static org.springframework.web.bind.annotation.RequestMethod.*;


/**
 * <p>
 * 应用相关API
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2019-07-10 18:32
 */
@RestController
@RequestMapping(value = UrlConstants.URL_PREFIX_API_VERSION + "/apps", produces = MediaType.APPLICATION_JSON_VALUE)
@Api(description = "Application 管理配置", tags = "Application")
public interface AppApi {


    /*****************************************************************管理员创建应用******************************************************************************/

    @RequestMapping(value = "/createAppBaseInfo", method = POST)
    @ResponseBody
    @ApiOperation(response = AppDetailsVO.class,
            value = "管理员创建应用(基础信息)",
            notes = "管理员创建应用(基础信息)",
            produces = "application/json",
            authorizations = {@Authorization(value = "uc_auth", scopes = {@AuthorizationScope(scope = "create:apps", description = "")})})
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true),
    })
    @ApiResponses(value = {
            @ApiResponse(code = SC_BAD_REQUEST,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + TransactionErrorDescription.INVALID_CLIENT_PROFILE + ", 错误消息: " + APP_APPTYPE_INVALIDE_ERROR_DESC
                            + "\r\n" + "* 错误码：" + TransactionErrorDescription.INVALID_CLIENT_PROFILE + ", 错误消息: " + APP_CLIENT_VALIDATE_FACTORS_ERROR_DESC
                            + "\r\n" + "* 错误码：" + TransactionErrorDescription.INVALID_REDIRECT_URI + ", 错误消息: " + APP_REDIRECT_URIS_INVALID_ERROR_DESC
                            + "\r\n" + "* 错误码：" + TransactionErrorDescription.INVALID_CLIENT + ", 错误消息: " + APP_NOT_FOUND_ERROR_DESC),
            @ApiResponse(code = SC_CONFLICT,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + TransactionErrorDescription.CLIENT_ALREADY_EXISTS + ", 错误消息: " + APP_APP_CONFLICT_ERROR_DESC),
            @ApiResponse(code = SC_INTERNAL_SERVER_ERROR,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + TransactionErrorDescription.SERVER_ERROR + ", 错误消息: " + APP_GEN_KEY_PAIR_ERROR_DESC
                            + "\r\n" + "* 错误码：" + TransactionErrorDescription.SERVER_ERROR + ", 错误消息: " + UNKNOWN_DAO_ERROR_DESC)
    })
    AppDetailsVO createAppBaseInfo(
            @Valid @RequestBody @ApiParam(name = "AppBaseInfoVO", value = "管理员创建或修改应用(基础信息)")
            AppBaseInfoVO appBaseInfoVO);

    @RequestMapping(value = "/updateAppBaseInfo", method = PATCH)
    @ResponseBody
    @ApiOperation(response = AppDetailsVO.class,
            value = "管理员修改应用(基础信息)",
            notes = "管理员修改应用(基础信息)",
            produces = "application/json",
            authorizations = {@Authorization(value = "uc_auth", scopes = {@AuthorizationScope(scope = "update:apps", description = "")})})
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true),
    })
    @ApiResponses(value = {
            @ApiResponse(code = SC_BAD_REQUEST,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + TransactionErrorDescription.INVALID_CLIENT_PROFILE + ", 错误消息: " + APP_APPTYPE_INVALIDE_ERROR_DESC
                            + "\r\n" + "* 错误码：" + TransactionErrorDescription.INVALID_CLIENT_PROFILE + ", 错误消息: " + APP_CLIENT_VALIDATE_FACTORS_ERROR_DESC
                            + "\r\n" + "* 错误码：" + TransactionErrorDescription.INVALID_REDIRECT_URI + ", 错误消息: " + APP_REDIRECT_URIS_INVALID_ERROR_DESC
                            + "\r\n" + "* 错误码：" + TransactionErrorDescription.INVALID_CLIENT + ", 错误消息: " + APP_NOT_FOUND_ERROR_DESC),
            @ApiResponse(code = SC_CONFLICT,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + TransactionErrorDescription.CLIENT_ALREADY_EXISTS + ", 错误消息: " + APP_APP_CONFLICT_ERROR_DESC),
            @ApiResponse(code = SC_INTERNAL_SERVER_ERROR,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + TransactionErrorDescription.SERVER_ERROR + ", 错误消息: " + APP_GEN_KEY_PAIR_ERROR_DESC
                            + "\r\n" + "* 错误码：" + TransactionErrorDescription.SERVER_ERROR + ", 错误消息: " + UNKNOWN_DAO_ERROR_DESC)
    })
    AppDetailsVO updateAppBaseInfo(
            @Valid @RequestBody @ApiParam(name = "AppBaseInfoVO", value = "管理员修改应用(基础信息)")
            AppBaseInfoVO appBaseInfoVO);

    @RequestMapping(value = "/updateSSOInfo/{client_id}", method = PATCH)
    @ResponseBody
    @ApiOperation(response = AppDetailsVO.class,
            value = "修改单点配置信息",
            notes = "修改单点配置信息",
            produces = "application/json",
            authorizations = {@Authorization(value = "uc_auth", scopes = {@AuthorizationScope(scope = "update:apps", description = "")})})
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true),
    })
    @ApiResponses(value = {
            @ApiResponse(code = SC_BAD_REQUEST,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + TransactionErrorDescription.INVALID_CLIENT_PROFILE + ", 错误消息: " + APP_APPTYPE_INVALIDE_ERROR_DESC
                            + "\r\n" + "* 错误码：" + TransactionErrorDescription.INVALID_REDIRECT_URI + ", 错误消息: " + APP_REDIRECT_URIS_INVALID_ERROR_DESC
                            + "\r\n" + "* 错误码：" + TransactionErrorDescription.INVALID_CLIENT + ", 错误消息: " + APP_NOT_FOUND_ERROR_DESC),
            @ApiResponse(code = SC_CONFLICT,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + TransactionErrorDescription.CLIENT_ALREADY_EXISTS + ", 错误消息: " + APP_APP_CONFLICT_ERROR_DESC),
            @ApiResponse(code = SC_INTERNAL_SERVER_ERROR,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + TransactionErrorDescription.SERVER_ERROR + ", 错误消息: " + APP_GEN_KEY_PAIR_ERROR_DESC
                            + "\r\n" + "* 错误码：" + TransactionErrorDescription.SERVER_ERROR + ", 错误消息: " + UNKNOWN_DAO_ERROR_DESC)
    })
    AppDetailsVO updateAppSSOInfo(
            @ApiParam(required = true, value = "应用Id", example = "digitalsee_sag")
            @PathVariable("client_id") String clientId,
            @Valid @RequestBody @ApiParam(name = "AppSSOInfoVO", value = "修改单点配置信息")
                    AppSSOInfoVO appSSOInfoVO);

    @RequestMapping(value = "/loginPolicy/{client_id}", method = PUT)
    @ResponseBody
    @ApiOperation(response = Void.class,
            value = "设置应用认证策略",
            notes = "设置应用认证策略",
            produces = "application/json",
            authorizations = {@Authorization(value = "uc_auth", scopes = {@AuthorizationScope(scope = "update:apps", description = "")})})
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true),
    })
    @ApiResponses(value = {
            @ApiResponse(code = SC_BAD_REQUEST,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + TransactionErrorDescription.INVALID_CLIENT + ", 错误消息: " + APP_NOT_FOUND_ERROR_DESC),
            @ApiResponse(code = SC_CONFLICT,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + TransactionErrorDescription.CLIENT_ALREADY_EXISTS + ", 错误消息: " + APP_APP_CONFLICT_ERROR_DESC),
            @ApiResponse(code = SC_INTERNAL_SERVER_ERROR,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + TransactionErrorDescription.SERVER_ERROR + ", 错误消息: " + UNKNOWN_DAO_ERROR_DESC)
    })
    ReturnResultVO<Void> setLoginPolicy(
            @ApiParam(required = true, value = "应用Id", example = "usercenter")
            @PathVariable("client_id") String clientId,
            @Valid @RequestBody @ApiParam(name = "LoginPolicyDTO", value = "认证策略配置信息")
                    LoginPolicyDTO loginPolicyDTO);

    @RequestMapping(value = "/preview/assertion", method = POST, produces = MediaType.TEXT_XML_VALUE)
    @ResponseBody
    @ApiOperation(response = String.class,
            value = "预览SAML断言",
            notes = "预览SAML断言",
            produces = "text/xml")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true),
    })
    String previewAssertion(@RequestBody @ApiParam(name = "SamlConfig", value = "SAML配置信息") SamlConfig samlConfig);

    /*****************************************************************获取应用的列表******************************************************************************/
    @RequestMapping(value = "/v2", method = GET)
    @ResponseBody
    @ApiOperation(response = ReturnResultAppList.class, nickname = "searchApp",
            value = "获取应用的列表",
            notes = "获取应用的列表，可以通过应用状态和应用类型做过滤。",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "search:apps", description = "")})})
    @ApiResponses(value = {
            @ApiResponse(code = SC_BAD_REQUEST,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + APP_CLIENT_PROFILE_UNKNOWN_ERROR_CODE + ", 错误消息: " + APP_CLIENT_PROFILE_UNKNOWN_ERROR_DESC),
            @ApiResponse(code = SC_BAD_REQUEST,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + APP_CLIENT_STATUS_UNKNOWN_ERROR_CODE + ", 错误消息: " + APP_CLIENT_STATUS_UNKNOWN_ERROR_DESC),
            @ApiResponse(code = SC_INTERNAL_SERVER_ERROR,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + UNKNOWN_DAO_ERROR_CODE + ", 错误消息: " + UNKNOWN_DAO_ERROR_DESC)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
                    required = true),
            @ApiImplicitParam(paramType = "query", name = "status", dataType = "String",
                    value = "状态", allowableValues = "INACTIVE,ACTIVE",
                    required = true)})
    ReturnResultVO<QueryPage<AppBriefVO>> searchApp2(@ApiParam(value = "查询条件") @ModelAttribute AppsQueryVo appsQueryVo);

    /*****************************************************************获取应用市场的列表******************************************************************************/
    @RequestMapping(value = "/appstore", method = GET)
    @ResponseBody
    @ApiOperation(response = ReturnResultAppList.class, nickname = "searchAppStore",
            value = "获取应用市场的列表",
            notes = "获取应用市场的列表，可以通过应用名称和应用类型做过滤。",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "search:apps", description = "")})})
    @ApiResponses(value = {
            @ApiResponse(code = SC_BAD_REQUEST,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + APP_CLIENT_PROFILE_UNKNOWN_ERROR_CODE + ", 错误消息: " + APP_CLIENT_PROFILE_UNKNOWN_ERROR_DESC),
            @ApiResponse(code = SC_BAD_REQUEST,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + APP_CLIENT_STATUS_UNKNOWN_ERROR_CODE + ", 错误消息: " + APP_CLIENT_STATUS_UNKNOWN_ERROR_DESC),
            @ApiResponse(code = SC_INTERNAL_SERVER_ERROR,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + UNKNOWN_DAO_ERROR_CODE + ", 错误消息: " + UNKNOWN_DAO_ERROR_DESC)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
                    required = true),
            @ApiImplicitParam(paramType = "query", name = "client_name", dataType = "String",
                    value = "应用名称", required = false),
            @ApiImplicitParam(paramType = "query", name = "category", dataType = "String",
                    value = "应用分类", required = false)
    })
    ReturnResultVO<QueryPage<AppBriefVO>> searchAppStore(
            @RequestParam(value = "client_name", required = false) String clientName,
            @RequestParam(value = "category", required = false) String category,
            @RequestParam(value = "protocol", required = false) List<String> protocol,
            @RequestParam(value = "feature_app", required = false) Integer featureApp,
            @RequestParam(value = "page", required = false) Integer page,
            @RequestParam(value = "size", required = false) Integer size);

    @RequestMapping(method = GET)
    @ResponseBody
    @ApiOperation(response = ReturnResultAppList.class, nickname = "searchApp",
            value = "获取应用的列表",
            notes = "获取应用的列表，可以通过应用状态和应用类型做过滤。",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "search:apps", description = "")})})
    @ApiResponses(value = {
            @ApiResponse(code = SC_BAD_REQUEST,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + APP_CLIENT_PROFILE_UNKNOWN_ERROR_CODE + ", 错误消息: " + APP_CLIENT_PROFILE_UNKNOWN_ERROR_DESC),
            @ApiResponse(code = SC_BAD_REQUEST,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + APP_CLIENT_STATUS_UNKNOWN_ERROR_CODE + ", 错误消息: " + APP_CLIENT_STATUS_UNKNOWN_ERROR_DESC),
            @ApiResponse(code = SC_INTERNAL_SERVER_ERROR,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + UNKNOWN_DAO_ERROR_CODE + ", 错误消息: " + UNKNOWN_DAO_ERROR_DESC)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
                    required = true),
            @ApiImplicitParam(paramType = "query", name = "application_type", dataType = "String",
                    value = "应用类型作为查询应用的过滤条件（可选的）", allowableValues = "NATIVE,SPA,WEB,CLI,TRUSTED",
                    required = true),
            @ApiImplicitParam(paramType = "query", name = "status", dataType = "String",
                    value = "状态", allowableValues = "INACTIVE,ACTIVE",
                    required = true)})
    ReturnResultVO<AppListVO> searchApp(
            @RequestParam(value = "application_type", required = false) String application_type,
            @RequestParam(value = "status", required = false) String status,
            @RequestParam(value = "inner_category", required = false) String inner_category,
            @RequestParam(value = "support_protocol", required = false) String support_protocol,
            @RequestParam(value = "filter", required = false) String filter,
            String clientId);


    /*****************************************************************获取应用详细信息******************************************************************************/
    @RequestMapping(value = "/{client_id}", method = GET)
    @ResponseBody
    @ApiOperation(response = AppDetailsVO.class, nickname = "getAppDetails",
            value = "获取应用详细信息",
            notes = "获取应用详细信息",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "read:apps", description = "")})})
    @ApiResponses(value = {
            @ApiResponse(code = SC_NOT_FOUND,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + TransactionErrorDescription.INVALID_CLIENT + ", 错误消息: " + APP_NOT_FOUND_ERROR_DESC),
            @ApiResponse(code = SC_INTERNAL_SERVER_ERROR,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + TransactionErrorDescription.SERVER_ERROR + ", 错误消息: " + UNKNOWN_DAO_ERROR_DESC)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
                    required = true)})
    AppDetailsVO getAppDetails(
            @ApiParam(required = true, value = "应用Id")
            @PathVariable("client_id") String clientId);

    @RequestMapping(value = "/isExist/{clientId}", method = POST)
    @ResponseBody
    @ApiOperation(response = AppDetailsVO.class, nickname = "AppExist",
            value = "判断应用是否存在",
            notes = "判断应用是否存在",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "read:apps", description = "")})})
    @ApiResponses(value = {
            @ApiResponse(code = SC_NOT_FOUND,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + TransactionErrorDescription.INVALID_CLIENT + ", 错误消息: " + APP_NOT_FOUND_ERROR_DESC),
            @ApiResponse(code = SC_INTERNAL_SERVER_ERROR,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + TransactionErrorDescription.SERVER_ERROR + ", 错误消息: " + UNKNOWN_DAO_ERROR_DESC)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
                    required = true)})
    ReturnResultVO<Boolean> isAppExist(@PathVariable("clientId") String clientId, @RequestBody Map<String, String> clientInfo);


    @RequestMapping(value = "/v2/{client_id}", method = GET)
    @ResponseBody
    @ApiOperation(response = AppDetailsVO.class, nickname = "getAppDetails",
            value = "获取应用详细信息",
            notes = "获取应用详细信息",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "read:apps", description = "")})})
    @ApiResponses(value = {
            @ApiResponse(code = SC_NOT_FOUND,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + TransactionErrorDescription.INVALID_CLIENT + ", 错误消息: " + APP_NOT_FOUND_ERROR_DESC),
            @ApiResponse(code = SC_INTERNAL_SERVER_ERROR,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + TransactionErrorDescription.SERVER_ERROR + ", 错误消息: " + UNKNOWN_DAO_ERROR_DESC)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
                    required = true)})
    ReturnResultVO<AppDetailsVO> getAppDetailsV2(
            @ApiParam(required = true, value = "应用Id")
            @PathVariable("client_id") String clientId);

    @RequestMapping(value = "/getLinkAppCount/{client_id}", method = GET)
    @ResponseBody
    @ApiOperation(response = Map.class, nickname = "getLinkAppCount",
            value = "获取应用模板关联应用的数量",
            notes = "获取应用模板关联应用的数量",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "read:apps", description = "")})})
    @ApiResponses(value = {
            @ApiResponse(code = SC_NOT_FOUND,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + TransactionErrorDescription.INVALID_CLIENT + ", 错误消息: " + APP_NOT_FOUND_ERROR_DESC),
            @ApiResponse(code = SC_INTERNAL_SERVER_ERROR,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + TransactionErrorDescription.SERVER_ERROR + ", 错误消息: " + UNKNOWN_DAO_ERROR_DESC)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
                    required = true)})
    ReturnResultVO<Map<String, Integer>> getLinkAppCount(
            @ApiParam(required = true, value = "模板应用Id")
            @PathVariable("client_id") String clientId);

    /*****************************************************************获取应用登录策略信息******************************************************************************/


    @RequestMapping(value = "/loginPolicy/{client_id}", method = GET)
    @ResponseBody
    @ApiOperation(response = LoginPolicyVO.class,
            value = "查看应用登录策略-暂时不用",
            notes = "查看应用登录策略",
            produces = "application/json",
            authorizations = {@Authorization(value = "uc_auth", scopes = {@AuthorizationScope(scope = "read:apps", description = "")})})
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true),
    })
    @ApiResponses(value = {
            @ApiResponse(code = SC_BAD_REQUEST,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + TransactionErrorDescription.INVALID_CLIENT + ", 错误消息: " + APP_NOT_FOUND_ERROR_DESC),
            @ApiResponse(code = SC_CONFLICT,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + TransactionErrorDescription.CLIENT_ALREADY_EXISTS + ", 错误消息: " + APP_APP_CONFLICT_ERROR_DESC),
            @ApiResponse(code = SC_INTERNAL_SERVER_ERROR,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + TransactionErrorDescription.SERVER_ERROR + ", 错误消息: " + UNKNOWN_DAO_ERROR_DESC)
    })
    ReturnResultVO<LoginPolicyVO> getLoginPolicy(
            @ApiParam(required = true, value = "应用Id", example = "usercenter")
            @PathVariable("client_id") String clientId);

    @RequestMapping(value = "/loginMfaPolicy/{client_id}", method = GET)
    @ResponseBody
    @ApiOperation(response = LoginMfaPolicyDTO.class,
            value = "查看应用登录MFA策略-暂时不用",
            notes = "查看应用登录MFA策略",
            produces = "application/json",
            authorizations = {@Authorization(value = "uc_auth", scopes = {@AuthorizationScope(scope = "read:apps", description = "")})})
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true),
    })
    @ApiResponses(value = {
            @ApiResponse(code = SC_BAD_REQUEST,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + TransactionErrorDescription.INVALID_CLIENT + ", 错误消息: " + APP_NOT_FOUND_ERROR_DESC),
            @ApiResponse(code = SC_CONFLICT,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + TransactionErrorDescription.CLIENT_ALREADY_EXISTS + ", 错误消息: " + APP_APP_CONFLICT_ERROR_DESC),
            @ApiResponse(code = SC_INTERNAL_SERVER_ERROR,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + TransactionErrorDescription.SERVER_ERROR + ", 错误消息: " + UNKNOWN_DAO_ERROR_DESC)
    })
    ReturnResultVO<LoginMfaPolicyDTO> getLoginMfaPolicy(
            @ApiParam(required = true, value = "应用Id", example = "usercenter")
            @PathVariable("client_id") String clientId);

    @RequestMapping(value = "/loginMfaPolicy/{client_id}", method = PATCH)
    @ResponseBody
    @ApiOperation(response = Void.class,
            value = "设置应用MFA认证策略-暂时不用",
            notes = "设置应用认证策略",
            produces = "application/json",
            authorizations = {@Authorization(value = "uc_auth", scopes = {@AuthorizationScope(scope = "update:apps", description = "")})})
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true),
    })
    @ApiResponses(value = {
            @ApiResponse(code = SC_BAD_REQUEST,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + TransactionErrorDescription.INVALID_CLIENT + ", 错误消息: " + APP_NOT_FOUND_ERROR_DESC),
            @ApiResponse(code = SC_CONFLICT,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + TransactionErrorDescription.CLIENT_ALREADY_EXISTS + ", 错误消息: " + APP_APP_CONFLICT_ERROR_DESC),
            @ApiResponse(code = SC_INTERNAL_SERVER_ERROR,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + TransactionErrorDescription.SERVER_ERROR + ", 错误消息: " + UNKNOWN_DAO_ERROR_DESC)
    })
    ReturnResultVO<Void> setLoginMfaPolicy(
            @ApiParam(required = true, value = "应用Id", example = "usercenter")
            @PathVariable("client_id") String clientId,
            @Valid @RequestBody @ApiParam(name = "LoginPolicyDTO", value = "认证策略配置信息")
            LoginMfaPolicyDTO loginMfaPolicyDTO);

    /*****************************************************************重新生成新的client secret******************************************************************************/
    @RequestMapping(value = "/{client_id}/secret", method = PATCH)
    @ResponseBody
    @ApiOperation(response = AppSecretRotationVO.class, nickname = "rotateClientSecret",
            value = "重新生成新的client secret。只有cli,web类型的client才有secret，native/spa类型的没有secret。",
            notes = "新的client secret生成后,老的secret将无效，请在您的应用里使用更新后的secret",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "update:apps", description = "更新app")})})
    @ApiResponses(value = {
            @ApiResponse(code = SC_BAD_REQUEST,
                    response = BaseReturnResult.class,
                    message = "* 错误码: " + APP_CANNOT_HAS_SECRET_ERROR_CODE + ", 错误消息: " + APP_CANNOT_HAS_SECRET_ERROR_DESC),
            @ApiResponse(code = SC_NOT_FOUND,
                    response = BaseReturnResult.class,
                    message = "* 错误码: " + APP_NOT_FOUND_ERROR_CODE + ", 错误消息: " + APP_NOT_FOUND_ERROR_DESC),
            @ApiResponse(code = SC_INTERNAL_SERVER_ERROR,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + UNKNOWN_DAO_ERROR_CODE + ", 错误消息: " + UNKNOWN_DAO_ERROR_DESC)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
                    required = true)})
    ReturnResultVO<AppSecretRotationVO> rotateClientSecret(
            @ApiParam(required = true, value = "应用Id", example = "digitalsee_sag")
            @PathVariable("client_id") String clientId);


    /*****************************************************************重新生成新的对称签名秘钥*****************************************************************************/
    @RequestMapping(value = "/{client_id}/signature_secret", method = PATCH)
    @ResponseBody
    @ApiOperation(nickname = "rotateClientSignatureSecret",
            value = "重新生成新的对称签名秘钥。",
            notes = "新的client signature secret生成后,老的secret将无效，请在您的应用里使用更新后的secret",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "update:apps", description = "更新app")})})
    @ApiResponses(value = {
            @ApiResponse(code = SC_NOT_FOUND,
                    response = BaseReturnResult.class,
                    message = "* 错误码: " + APP_NOT_FOUND_ERROR_CODE + ", 错误消息: " + APP_NOT_FOUND_ERROR_DESC),
            @ApiResponse(code = SC_INTERNAL_SERVER_ERROR,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + UNKNOWN_DAO_ERROR_CODE + ", 错误消息: " + UNKNOWN_DAO_ERROR_DESC)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
                    required = true)})
    ReturnResultVO<AppSignatureSecretRotationVO> rotateClientSignatureSecret(
            @ApiParam(required = true, value = "应用Id", example = "digitalsee_sag")
            @PathVariable("client_id") String clientId);

    /*****************************************************************改变应用的状态******************************************************************************/
    @RequestMapping(value = "/{client_id}/status", method = {PUT}, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation(nickname = "changeAppStatus", value = "改变应用的状态",
            notes = "改变应用的状态。",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "update:apps", description = "更新app")})})
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true),
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = "application/json;charset=UTF-8", required = true)
    })
    @ApiResponses(value = {
            @ApiResponse(code = SC_NOT_FOUND,
                    response = BaseReturnResult.class,
                    message = "* 错误码: " + APP_NOT_FOUND_ERROR_CODE + ", 错误消息: " + APP_NOT_FOUND_ERROR_DESC),
            @ApiResponse(code = SC_INTERNAL_SERVER_ERROR,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + UNKNOWN_DAO_ERROR_CODE + ", 错误消息: " + UNKNOWN_DAO_ERROR_DESC)
    })
    ReturnResultVO<AppDetailsVO> changeAppStatus(
            @ApiParam(required = true, value = "应用id", example = "digitalsee_sag")
            @PathVariable("client_id") String clientId,
            @ApiParam(required = true, value = "新的应用状态") @Valid @RequestBody AppStatusChangeRequestVO status);


    /*****************************************************************删除应用******************************************************************************/
    @RequestMapping(value = "/{client_id}", method = DELETE)
    @ResponseBody
    @ApiOperation(nickname = "deleteApp",
            value = "删除应用",
            notes = "删除应用",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "delete:apps", description = "")})})
    @ApiResponses(value = {
            @ApiResponse(code = SC_NOT_FOUND,
                    response = BaseReturnResult.class,
                    message = "* 错误码: " + APP_NOT_FOUND_ERROR_CODE + ", 错误消息: " + APP_NOT_FOUND_ERROR_DESC),
            @ApiResponse(code = SC_INTERNAL_SERVER_ERROR,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + UNKNOWN_DAO_ERROR_CODE + ", 错误消息: " + UNKNOWN_DAO_ERROR_DESC)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
                    required = true)})
    ReturnResultVO<AppDetailsVO> deleteApp(
            @ApiParam(required = true, value = "应用Id")
            @PathVariable("client_id") String clientId);


    /*****************************************************************更新应用的详细信息*****************************************************************************/
    @RequestMapping(value = "/{client_id}", method = POST)
    @ResponseBody
    @ApiOperation(response = AppDetailsVO.class,
            value = "更新应用的详细信息",
            notes = "更新应用的详细信息",
            produces = "application/json",
            authorizations = {
                    @Authorization(value = "uc_auth",
                            scopes = {
                                    @AuthorizationScope(scope = "update:apps", description = "")
                            }
                    )
            })
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true),
    })
    @ApiResponses(value = {
            @ApiResponse(code = SC_NOT_FOUND,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + APP_NOT_FOUND_ERROR_CODE + "错误消息: " + APP_NOT_FOUND_ERROR_DESC),
            @ApiResponse(code = SC_BAD_REQUEST,
                    response = BaseReturnResult.class,
                    message = "* 错误消息: " + APP_REDIRECT_URIS_INVALID_ERROR_CODE + ", 错误消息: " + APP_REDIRECT_URIS_INVALID_ERROR_DESC
                            + "\r\n" + "* 错误码：" + APP_CLIENT_VALIDATE_FACTORS_ERROR_CODE + ", 错误消息: " + APP_CLIENT_VALIDATE_FACTORS_ERROR_DESC
                            + "\r\n" + "* 错误码：" + APP_PROFILE_UNKNOWN_ERROR_CODE + ", 错误消息: " + APP_PROFILE_UNKNOWN_ERROR_DESC),
            @ApiResponse(code = SC_INTERNAL_SERVER_ERROR,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + UNKNOWN_DAO_ERROR_CODE + ", 错误消息: " + UNKNOWN_DAO_ERROR_DESC)
    })
    ReturnResultVO<AppDetailsVO> updateAppV2(
            @ApiParam(required = true, value = "应用Id", example = "digitalsee_sag")
            @PathVariable("client_id") String clientId,
            @Valid @RequestBody @ApiParam(name = "AppUpdateInVo", value = "更新应用的请求")
                    AppUpdateInVO appUpdateInVO);


    /*****************************************************************更新应用的公开访问配置*****************************************************************************/
    @RequestMapping(value = "/{client_id}/public_access", method = PUT)
    @ResponseBody
    @ApiOperation(value = "更新应用的公开访问配置",
            notes = "更新应用的公开访问配置",
            produces = "application/json",
            authorizations = {
                    @Authorization(value = "uc_auth",
                            scopes = {
                                    @AuthorizationScope(scope = "update:apps_public_access", description = "")
                            }
                    )
            })
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true),
    })
    @ApiResponses(value = {
            @ApiResponse(code = SC_NOT_FOUND,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + APP_NOT_FOUND_ERROR_CODE + "错误消息: " + APP_NOT_FOUND_ERROR_DESC),
            @ApiResponse(code = SC_INTERNAL_SERVER_ERROR,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + UNKNOWN_DAO_ERROR_CODE + ", 错误消息: " + UNKNOWN_DAO_ERROR_DESC)
    })
    ReturnResultVO<Void> updateAppPublicAccess(
            @ApiParam(required = true, value = "应用Id", example = "digitalsee_sag")
            @PathVariable("client_id") String clientId,
            @Valid @RequestBody AppPublicAccessUpdateInVO appPublicAccessUpdateInVO);


    /*****************************************************************获取应用的签名证书****************************************************************************/
    @RequestMapping(value = "/{client_id}/publickey", method = GET)
    @ResponseBody
    @ApiOperation(response = String.class, nickname = "getAppPublicKey",
            value = "获取应用的签名证书",
            notes = "获取应用的签名证书",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "read:app_publickey", description = "")})})
    @ApiResponses(value = {
            @ApiResponse(code = SC_BAD_REQUEST,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + TransactionErrorDescription.INVALID_CLIENT + ", 错误消息: " + APP_NOT_FOUND_ERROR_DESC
                            + "\r\n* 错误码：" + APP_PUBLIC_KEY_FORMAT_ERROR_CODE + ", 错误消息: " + APP_PUBLIC_KEY_FORMAT_ERROR_DESC),
            @ApiResponse(code = SC_INTERNAL_SERVER_ERROR,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + TransactionErrorDescription.SERVER_ERROR + ", 错误消息: " + UNKNOWN_DAO_ERROR_DESC)
    })
    String getAppPublicKey(
            @ApiParam(required = true, value = "应用Id")
            @PathVariable("client_id") String clientId,
            @RequestParam(value = "format", required = false) @ApiParam(
                    value = "验证签名的公共密钥（可选的），默认返回JWK格式", example = "JWK",
                    allowableValues = "JWK, CERT, PEM") String format);


    /*****************************************************************下载应用的签名证书****************************************************************************/
    @RequestMapping(value = "/{client_id}/download_cert", method = GET)
    @ResponseBody
    @ApiOperation(response = String.class, nickname = "downloadAppPublicKey",
            value = "下载应用的签名证书",
            notes = "下载应用的签名证书",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "read:app_publickey", description = "")})})
    @ApiResponses(value = {
            @ApiResponse(code = SC_BAD_REQUEST,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + TransactionErrorDescription.INVALID_CLIENT + ", 错误消息: " + APP_NOT_FOUND_ERROR_DESC),
            @ApiResponse(code = SC_INTERNAL_SERVER_ERROR,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + TransactionErrorDescription.SERVER_ERROR + ", 错误消息: " + UNKNOWN_DAO_ERROR_DESC)
    })
    void downloadAppPublicKey(HttpServletResponse response,
                              @ApiParam(required = true, value = "应用Id")
                              @PathVariable("client_id") String clientId,
                              @RequestParam(value = "filename", required = false, defaultValue = "client.crt")
                              @ApiParam(
                                      value = "用来保存应用证书的文件名称",
                                      example = "client.crt") String certFile) throws IOException;


    /*****************************************************************设置应用是否加入或退出授权白名单****************************************************************************/
    @RequestMapping(value = "/{client_id}/whitelisted", method = {PUT}, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    @ApiOperation(nickname = "changeWhitelisted", value = "设置应用是否加入或退出授权白名单",
            notes = "设置应用是否加入或退出授权白名单",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "update:apps", description = "设置应用是否加入或退出授权白名单")})})
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true),
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = "application/json;charset=UTF-8", required = true)
    })
    @ApiResponses(value = {
            @ApiResponse(code = SC_NOT_FOUND,
                    response = BaseReturnResult.class,
                    message = "* 错误码: " + APP_NOT_FOUND_ERROR_CODE + ", 错误消息: " + APP_NOT_FOUND_ERROR_DESC),
            @ApiResponse(code = SC_INTERNAL_SERVER_ERROR,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + UNKNOWN_DAO_ERROR_CODE + ", 错误消息: " + UNKNOWN_DAO_ERROR_DESC)
    })
    ReturnResultVO<?> changeWhitelisted(
            @ApiParam(required = true, value = "应用id", example = "digitalsee_sag")
            @PathVariable("client_id") String clientId,
            @ApiParam(required = true, value = "新的应用白名单状态") @Valid @RequestBody AppWhitelistedChangeRequestVO status);


    /*****************************************************************给多个用户分配应用****************************************************************************/
    @RequestMapping(value = "/{client_id}/users/add", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(nickname = "entitleUsers",
            value = "给多个用户分配应用",
            notes = "给多个用户分配应用",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "update:user_apps", description = "")})})
    @ApiResponses(value = {
            @ApiResponse(code = SC_NOT_FOUND,
                    response = BaseReturnResult.class,
                    message = "* 错误码: " + APP_NOT_FOUND_ERROR_CODE + ", 错误消息: " + APP_NOT_FOUND_ERROR_DESC),
            @ApiResponse(code = SC_BAD_REQUEST,
                    response = BaseReturnResult.class,
                    message = "* 错误码: " + APP_CLI_TYPE_NOT_ALLOWED_ERROR_CODE + ", 错误消息: " + APP_CLI_TYPE_NOT_ALLOWED_ERROR_DESC
                            + "\n\r* 错误码: " + APP_IS_INTERNAL_ERROR_CODE + ", 错误消息: " + APP_IS_INTERNAL_ERROR_DESC),
            @ApiResponse(code = SC_INTERNAL_SERVER_ERROR,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + UNKNOWN_DAO_ERROR_CODE + ", 错误消息: " + UNKNOWN_DAO_ERROR_DESC)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String",
                    value = HEADER_REQUEST_CONTENT_TYPE,
                    defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
                    required = true)})
    ReturnResultVO<BatchOperationVO<String>> entitleUsers(
            @NotEmpty @PathVariable("client_id") @ApiParam(required = true,
                    value = "应用id") String clientId,
            @RequestBody @Valid AddUsersToAppInVO addUsersToAppInVO);


    /*****************************************************************从应用中已分配的用户列表中移除用户****************************************************************************/
    @RequestMapping(value = "/{client_id}/users/remove", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(nickname = "revokeUsersEntitlement",
            value = "从应用中已分配的用户列表中移除用户",
            notes = "从应用中已分配的用户列表中移除用户",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "update:user_apps", description = "")})})
    @ApiResponses(value = {
            @ApiResponse(code = SC_NOT_FOUND,
                    response = BaseReturnResult.class,
                    message = "* 错误码: " + APP_NOT_FOUND_ERROR_CODE + ", 错误消息: " + APP_NOT_FOUND_ERROR_DESC),
            @ApiResponse(code = SC_BAD_REQUEST,
                    response = BaseReturnResult.class,
                    message = "* 错误码: " + APP_CLI_TYPE_NOT_ALLOWED_ERROR_CODE + ", 错误消息: " + APP_CLI_TYPE_NOT_ALLOWED_ERROR_DESC
                            + "\n\r* 错误码: " + APP_IS_INTERNAL_ERROR_CODE + ", 错误消息: " + APP_IS_INTERNAL_ERROR_DESC),
            @ApiResponse(code = SC_INTERNAL_SERVER_ERROR,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + UNKNOWN_DAO_ERROR_CODE + ", 错误消息: " + UNKNOWN_DAO_ERROR_DESC)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String",
                    value = HEADER_REQUEST_CONTENT_TYPE,
                    defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
                    required = true)})
    ReturnResultVO<BatchOperationVO<String>> revokeUsersEntitlement(
            @NotEmpty @PathVariable("client_id") @ApiParam(required = true,
                    value = "应用id") String clientId,
            @RequestBody @Valid RemoveUsersFromAppInVO removeUsersFromAppInVO);


    /*****************************************************************给多个标签分配应用****************************************************************************/
    @RequestMapping(value = "/{client_id}/tags/add", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(nickname = "entitleTags",
            value = "给多个标签分配应用",
            notes = "给多个标签分配应用",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "update:tag_apps", description = "")})})
    @ApiResponses(value = {
            @ApiResponse(code = SC_NOT_FOUND,
                    response = BaseReturnResult.class,
                    message = "* 错误码: " + TAG_NOT_FOUND_ERROR_CODE + ", 错误消息: " + TAG_NOT_FOUND_ERROR_DESC),
            @ApiResponse(code = SC_BAD_REQUEST,
                    response = BaseReturnResult.class,
                    message = "* 错误码: " + APP_CLI_TYPE_NOT_ALLOWED_ERROR_CODE + ", 错误消息: " + APP_CLI_TYPE_NOT_ALLOWED_ERROR_DESC
                            + "\n\r* 错误码: " + APP_IS_INTERNAL_ERROR_CODE + ", 错误消息: " + APP_IS_INTERNAL_ERROR_DESC),
            @ApiResponse(code = SC_INTERNAL_SERVER_ERROR,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + UNKNOWN_DAO_ERROR_CODE + ", 错误消息: " + UNKNOWN_DAO_ERROR_DESC)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String",
                    value = HEADER_REQUEST_CONTENT_TYPE,
                    defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
                    required = true)})
    ReturnResultVO<BatchOperationVO<String>> entitleTags(
            @NotEmpty @PathVariable("client_id") @ApiParam(required = true,
                    value = "应用id") String clientId,
            @RequestBody @Valid EntitleOrRevokeTagsInVO entitleOrRevokeTagsInVO);


    /*****************************************************************从应用中已分配的用户标签列表中移除标签*********************************************************************/
    @RequestMapping(value = "/{client_id}/tags/remove", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(nickname = "revokeTagsEntitlement",
            value = "从应用中已分配的用户标签列表中移除标签",
            notes = "从应用中已分配的用户标签列表中移除标签",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "update:tag_apps", description = "")})})
    @ApiResponses(value = {
            @ApiResponse(code = SC_NOT_FOUND,
                    response = BaseReturnResult.class,
                    message = "* 错误码: " + TAG_NOT_FOUND_ERROR_CODE + ", 错误消息: " + TAG_NOT_FOUND_ERROR_DESC),
            @ApiResponse(code = SC_BAD_REQUEST,
                    response = BaseReturnResult.class,
                    message = "* 错误码: " + APP_CLI_TYPE_NOT_ALLOWED_ERROR_CODE + ", 错误消息: " + APP_CLI_TYPE_NOT_ALLOWED_ERROR_DESC
                            + "\n\r* 错误码: " + APP_IS_INTERNAL_ERROR_CODE + ", 错误消息: " + APP_IS_INTERNAL_ERROR_DESC),
            @ApiResponse(code = SC_INTERNAL_SERVER_ERROR,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + UNKNOWN_DAO_ERROR_CODE + ", 错误消息: " + UNKNOWN_DAO_ERROR_DESC)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String",
                    value = HEADER_REQUEST_CONTENT_TYPE,
                    defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
                    required = true)})
    ReturnResultVO<BatchOperationVO<String>> revokeTagsEntitlement(
            @NotEmpty @PathVariable("client_id") @ApiParam(required = true,
                    value = "应用id") String clientId,
            @RequestBody @Valid EntitleOrRevokeTagsInVO entitleOrRevokeTagsInVO);


    /*****************************************************************查看已经分配应用的标签列表*********************************************************************/
    @RequestMapping(value = "/{client_id}/tags", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(response = TagVO.class, nickname = "listEntitledUsers",
            value = "查看已经分配应用的标签列表",
            notes = "查看已经分配应用的标签列表，可针对标签名称，描述模糊查询标签。"
                    + "\n\r查询匹配的标准是字符串的子串匹配方式。",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "list:tag_apps", description = "")})})
    @ApiResponses(value = {
            @ApiResponse(code = SC_BAD_REQUEST,
                    response = BaseReturnResult.class,
                    message = "* 错误码: " + USER_ATTR_UNKNOWN_ERROR_CODE + ", 错误消息: " + "Unknown user attribute name/Sort not supported"
                            + "\n\r* 错误码: " + PAGE_SIZE_EXCEED_MAX_ERROR_CODE + ", 错误消息: " + PAGE_SIZE_EXCEED_MAX_ERROR_DESC
                            + "\n\r* 错误码: " + TOO_MANY_ENTRIES_ERROR_CODE + ", 错误消息: " + TOO_MANY_ENTRIES_ERROR_DESC
                            + "\n\r* 错误码: " + APP_NOT_FOUND_ERROR_CODE + ", 错误消息: " + APP_NOT_FOUND_ERROR_DESC
                            + "\n\r* 错误码: " + APP_CLI_TYPE_NOT_ALLOWED_ERROR_CODE + ", 错误消息: " + APP_CLI_TYPE_NOT_ALLOWED_ERROR_DESC
                            + "\n\r* 错误码: " + APP_IS_INTERNAL_ERROR_CODE + ", 错误消息: " + APP_IS_INTERNAL_ERROR_DESC),
            @ApiResponse(code = SC_NOT_FOUND,
                    response = BaseReturnResult.class,
                    message = "* 错误码: " + APP_NOT_FOUND_ERROR_CODE + ", 错误消息: " + APP_NOT_FOUND_ERROR_DESC)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
                    required = true)})
    ReturnResultVO<List<TagVO>> listEntitledTags(
            @NotEmpty @PathVariable("client_id") @ApiParam(required = true,
                    value = "应用id") String clientId,
            @NotEmpty @RequestParam(value = "q", required = false) @ApiParam(
                    value = "作为过滤条件，在子串匹配时过滤标签名称和描述的属性的字符串",
                    example = "name1") String filter);


    /*****************************************************************查看已经分配应用的用户列表*********************************************************************/
    @RequestMapping(value = "/{client_id}/users", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(response = MapUserGetListReturnResult.class, nickname = "listEntitledUsers",
            value = "查看已经分配应用的用户列表",
            notes = "查看已经分配应用的用户列表，可针对用户登录名，全名，电子邮件，移动电话号码和状态属性，模糊查询用户。"
                    + "\n\r查询匹配的标准是字符串的子串匹配方式。"
                    + "\n\r**如果查询参数'q'没有指定，即使指定了返回用户列表的排序方式，API也会使用缺省的'username asc, name asc, email asc'来排序。** "
                    + "\n\r**注意: 即使在查询参数'attrs'中指定了敏感信息（如密码），API也不会返回。**",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "list:user_apps", description = "")})})
    @ApiResponses(value = {
            @ApiResponse(code = SC_BAD_REQUEST,
                    response = BaseReturnResult.class,
                    message = "* 错误码: " + USER_ATTR_UNKNOWN_ERROR_CODE + ", 错误消息: " + "Unknown user attribute name/Sort not supported"
                            + "\n\r* 错误码: " + PAGE_SIZE_EXCEED_MAX_ERROR_CODE + ", 错误消息: " + PAGE_SIZE_EXCEED_MAX_ERROR_DESC
                            + "\n\r* 错误码: " + TOO_MANY_ENTRIES_ERROR_CODE + ", 错误消息: " + TOO_MANY_ENTRIES_ERROR_DESC
                            + "\n\r* 错误码: " + APP_NOT_FOUND_ERROR_CODE + ", 错误消息: " + APP_NOT_FOUND_ERROR_DESC
                            + "\n\r* 错误码: " + APP_CLI_TYPE_NOT_ALLOWED_ERROR_CODE + ", 错误消息: " + APP_CLI_TYPE_NOT_ALLOWED_ERROR_DESC
                            + "\n\r* 错误码: " + APP_IS_INTERNAL_ERROR_CODE + ", 错误消息: " + APP_IS_INTERNAL_ERROR_DESC),
            @ApiResponse(code = SC_NOT_FOUND,
                    response = BaseReturnResult.class,
                    message = "* 错误码: " + APP_NOT_FOUND_ERROR_CODE + ", 错误消息: " + APP_NOT_FOUND_ERROR_DESC)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
                    required = true)})
    ReturnResultVO<QueryPage<Map<String, Object>>> listEntitledUsers(
            @NotEmpty @PathVariable("client_id") @ApiParam(required = true,
                    value = "应用id") String clientId,
            @NotEmpty @RequestParam(value = "search_field", required = false) @ApiParam(
                    value = "指定搜索的字段，用于限定搜索范围，如 'name'、'email' ",
                    example = "name")
            String searchField,
            @NotEmpty @RequestParam(value = "q", required = false) @ApiParam(
                    value = "作为过滤条件，在子串匹配时过滤用户登录名，全名，电子邮件和移动电话的属性的字符串",
                    example = "name1") String filter,

            @ApiParam(
                    value = "要获取的结果页号[1..N], 缺省是："
                            + QueryPage.DEFAULT_PAGE_NUMBER
                            + ". \n\r如果使用了无效的页号，API将使用缺省页号。", example = QueryPage.DEFAULT_PAGE_NUMBER + "")
            @RequestParam(value = "page", required = false) Integer page,

            @ApiParam(
                    value = "获取的每页的记录数[1..N], 缺省是："
                            + QueryPage.DEFAULT_PAGE_SIZE
                            + ". \n\r**每页允许的最大记录数为:" + QueryPage.MAX_PAGE_SIZE
                            + ". \n\r如果使用了无效的记录数，API将使用缺省记录数。", example = QueryPage.DEFAULT_PAGE_SIZE + "")
            @RequestParam(value = "size", required = false) Integer size,

            @ApiParam(
                    value = "排序标准的格式如下：'property1,(asc|desc)'"
                            + "\n\r多列排序也支持，例如： 'sort=property1,asc&sort=property2,desc'")
            @RequestParam(value = "sort", required = false) List<String> sort,

            @NotEmpty @RequestParam(value = "attrs", required = false) @ApiParam(
                    value = "需要返回的用户属性的列表，用逗号分隔，或者用格式类似于'attrs=sub&attrs=username'的方式来表示.\n\r如果不指定参数，所有有效的用户属性都会返回。"
                            + "\n\r**注意：即使用户没有一个属性是'orgs'，但是为了方便客户端的使用，这个额外的属性还是会被返回。**",
                    example = "sub, username, nickname") String... requestAttrs);


    /*****************************************************************给单个用户分配应用*********************************************************************/
    @RequestMapping(value = "/{client_id}/user", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(nickname = "entitleUser",
            value = "给单个用户分配应用",
            notes = "给单个用户分配应用",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "update:user_apps", description = "")})})
    @ApiResponses(value = {
            @ApiResponse(code = SC_NOT_FOUND,
                    response = BaseReturnResult.class,
                    message = "* 错误码: " + APP_NOT_FOUND_ERROR_CODE + ", 错误消息: " + APP_NOT_FOUND_ERROR_DESC
                            + "\n\r* 错误码: " + USER_NOT_FOUND_ERROR_CODE + ", 错误消息: " + USER_NOT_FOUND_ERROR_DESC),
            @ApiResponse(code = SC_BAD_REQUEST,
                    response = BaseReturnResult.class,
                    message = "* 错误码: " + APP_CLI_TYPE_NOT_ALLOWED_ERROR_CODE + ", 错误消息: " + APP_CLI_TYPE_NOT_ALLOWED_ERROR_DESC
                            + "\n\r* 错误码: " + APP_IS_INTERNAL_ERROR_CODE + ", 错误消息: " + APP_IS_INTERNAL_ERROR_DESC),
            @ApiResponse(code = SC_INTERNAL_SERVER_ERROR,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + UNKNOWN_DAO_ERROR_CODE + ", 错误消息: " + UNKNOWN_DAO_ERROR_DESC)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String",
                    value = HEADER_REQUEST_CONTENT_TYPE,
                    defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
                    required = true),
            @ApiImplicitParam(dataType = "String", name = "username", paramType = "query",
                    value = "用户名", required = true, example = "john")})
    ReturnResultVO<Void> entitleUser(
            @NotEmpty @PathVariable("client_id")
            @ApiParam(required = true, value = "应用id") String clientId,
            @NotEmpty @RequestParam(value = "username", required = true)
            @ApiParam(value = "用户名", example = "John") String username);


    /*****************************************************************从应用中已分配的用户列表中移除单个用户*********************************************************************/
    @RequestMapping(value = "/{client_id}/user", method = RequestMethod.DELETE)
    @ResponseBody
    @ApiOperation(nickname = "revokeUserEntitlement",
            value = "从应用中已分配的用户列表中移除单个用户",
            notes = "从应用中已分配的用户列表中移除单个用户",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "update:user_apps", description = "")})})
    @ApiResponses(value = {
            @ApiResponse(code = SC_NOT_FOUND,
                    response = BaseReturnResult.class,
                    message = "* 错误码: " + APP_NOT_FOUND_ERROR_CODE + ", 错误消息: " + APP_NOT_FOUND_ERROR_DESC
                            + "\n\r* 错误码: " + USER_NOT_FOUND_ERROR_CODE + ", 错误消息: " + USER_NOT_FOUND_ERROR_DESC),
            @ApiResponse(code = SC_BAD_REQUEST,
                    response = BaseReturnResult.class,
                    message = "* 错误码: " + APP_CLI_TYPE_NOT_ALLOWED_ERROR_CODE + ", 错误消息: " + APP_CLI_TYPE_NOT_ALLOWED_ERROR_DESC
                            + "\n\r* 错误码: " + APP_IS_INTERNAL_ERROR_CODE + ", 错误消息: " + APP_IS_INTERNAL_ERROR_DESC),
            @ApiResponse(code = SC_INTERNAL_SERVER_ERROR,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + UNKNOWN_DAO_ERROR_CODE + ", 错误消息: " + UNKNOWN_DAO_ERROR_DESC)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String",
                    value = HEADER_REQUEST_CONTENT_TYPE,
                    defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
                    required = true),
            @ApiImplicitParam(dataType = "String", name = "username", paramType = "query",
                    value = "用户名", required = true, example = "john")})
    ReturnResultVO<Void> revokeUserEntitlement(
            @NotEmpty @PathVariable("client_id")
            @ApiParam(required = true, value = "应用id") String clientId,
            @NotEmpty @RequestParam(value = "username", required = true)
            @ApiParam(value = "用户名", example = "John") String username);


    /*****************************************************************给单个组织结构分配应用*********************************************************************/
    @RequestMapping(value = "/{client_id}/orgs/{org_id}", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(nickname = "entitleOrg",
            value = "给单个组织结构分配应用",
            notes = "给单个组织结构分配应用",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "update:org_apps", description = "")})})
    @ApiResponses(value = {
            @ApiResponse(code = SC_NOT_FOUND,
                    response = BaseReturnResult.class,
                    message = "* 错误码: " + APP_NOT_FOUND_ERROR_CODE + ", 错误消息: " + APP_NOT_FOUND_ERROR_DESC
                            + "\n\r* 错误码: " + ORG_NOT_FOUND_ERROR_CODE + ", 错误消息: " + ORG_NOT_FOUND_ERROR_DESC),
            @ApiResponse(code = SC_BAD_REQUEST,
                    response = BaseReturnResult.class,
                    message = "* 错误码: " + APP_CLI_TYPE_NOT_ALLOWED_ERROR_CODE + ", 错误消息: " + APP_CLI_TYPE_NOT_ALLOWED_ERROR_DESC
                            + "\n\r* 错误码: " + ORG_ROOT_CANNOT_MODIFY_ERROR_CODE + ", 错误消息: " + ORG_ROOT_CANNOT_MODIFY_ERROR_DESC
                            + "\n\r* 错误码: " + APP_IS_INTERNAL_ERROR_CODE + ", 错误消息: " + APP_IS_INTERNAL_ERROR_DESC),
            @ApiResponse(code = SC_INTERNAL_SERVER_ERROR,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + UNKNOWN_DAO_ERROR_CODE + ", 错误消息: " + UNKNOWN_DAO_ERROR_DESC)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String",
                    value = HEADER_REQUEST_CONTENT_TYPE,
                    defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
                    required = true)})
    ReturnResultVO<Void> entitleOrg(
            @NotEmpty @PathVariable("client_id") @ApiParam(required = true,
                    value = "应用id") String clientId,
            @NotEmpty @PathVariable("org_id") @ApiParam(required = true,
                    value = "组织结构id") String orgId);


    /*****************************************************************从应用中已分配的组织结构列表中移除单个组织结构**********************************************************/
    @RequestMapping(value = "/{client_id}/orgs/{org_id}", method = RequestMethod.DELETE)
    @ResponseBody
    @ApiOperation(nickname = "revokeOrgEntitlement",
            value = "从应用中已分配的组织结构列表中移除单个组织结构",
            notes = "从应用中已分配的组织结构列表中移除单个组织结构",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "update:org_apps", description = "")})})
    @ApiResponses(value = {
            @ApiResponse(code = SC_NOT_FOUND,
                    response = BaseReturnResult.class,
                    message = "* 错误码: " + APP_NOT_FOUND_ERROR_CODE + ", 错误消息: " + APP_NOT_FOUND_ERROR_DESC
                            + "\n\r* 错误码: " + ORG_NOT_FOUND_ERROR_CODE + ", 错误消息: " + ORG_NOT_FOUND_ERROR_DESC),
            @ApiResponse(code = SC_BAD_REQUEST,
                    response = BaseReturnResult.class,
                    message = "* 错误码: " + APP_CLI_TYPE_NOT_ALLOWED_ERROR_CODE + ", 错误消息: " + APP_CLI_TYPE_NOT_ALLOWED_ERROR_DESC
                            + "\n\r* 错误码: " + ORG_ROOT_CANNOT_MODIFY_ERROR_CODE + ", 错误消息: " + ORG_ROOT_CANNOT_MODIFY_ERROR_DESC
                            + "\n\r* 错误码: " + APP_IS_INTERNAL_ERROR_CODE + ", 错误消息: " + APP_IS_INTERNAL_ERROR_DESC),
            @ApiResponse(code = SC_INTERNAL_SERVER_ERROR,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + UNKNOWN_DAO_ERROR_CODE + ", 错误消息: " + UNKNOWN_DAO_ERROR_DESC)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String",
                    value = HEADER_REQUEST_CONTENT_TYPE,
                    defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
                    required = true)})
    ReturnResultVO<Void> revokeOrgEntitlement(
            @NotEmpty @PathVariable("client_id") @ApiParam(required = true,
                    value = "应用id") String clientId,
            @NotEmpty @PathVariable("org_id") @ApiParam(required = true,
                    value = "组织结构id") String orgId);


    /*****************************************************************列出应用分配的组织结构树**********************************************************/
    @RequestMapping(value = "/{client_id}/orgs", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(
            response = OrgAppInfoChildTreeOutVO.class,
            nickname = "listEntitledOrgTree",
            value = "列出应用分配的组织结构树",
            notes = "列出应用分配的组织结构树",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "list:org_apps", description = "")})})
    @ApiResponses(value = {
            @ApiResponse(code = SC_NOT_FOUND,
                    response = BaseReturnResult.class,
                    message = "* 错误码: " + APP_NOT_FOUND_ERROR_CODE + ", 错误消息: " + APP_NOT_FOUND_ERROR_DESC
            ),
            @ApiResponse(code = SC_BAD_REQUEST,
                    response = BaseReturnResult.class,
                    message = "* 错误码: " + APP_CLI_TYPE_NOT_ALLOWED_ERROR_CODE + ", 错误消息: " + APP_CLI_TYPE_NOT_ALLOWED_ERROR_DESC
                            + "\n\r* 错误码: " + APP_IS_INTERNAL_ERROR_CODE + ", 错误消息: " + APP_IS_INTERNAL_ERROR_DESC),
    })
    @ApiImplicitParams({@ApiImplicitParam(paramType = "header", name = "Authorization",
            dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
            required = true)})
    ReturnResultVO<Object> listEntitledOrgTree(
            @NotEmpty @PathVariable("client_id") @ApiParam(required = true,
                    value = "应用id") String clientId);

    /*****************************************************************列出应用分配的租户列表**********************************************************/
    @RequestMapping(value = "/{client_id}/tenants", method = GET)
    @ResponseBody
    @ApiOperation(value = "获取能使用该应用的租户列表",
            notes = "获取能使用该应用的租户列表",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "read:apps", description = "")})})
    @ApiResponses(value = {
            @ApiResponse(code = SC_NOT_FOUND,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + TransactionErrorDescription.INVALID_CLIENT + ", 错误消息: " + APP_NOT_FOUND_ERROR_DESC),
            @ApiResponse(code = SC_INTERNAL_SERVER_ERROR,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + TransactionErrorDescription.SERVER_ERROR + ", 错误消息: " + UNKNOWN_DAO_ERROR_DESC)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
                    required = true)})
    ReturnResultVO<List<SysTenantVo>> getAssignedTenants(
            @ApiParam(required = true, value = "应用Id")
            @PathVariable("client_id") String clientId);

    /*****************************************************************分配应用的使用权限给租户*********************************************************************/
    @RequestMapping(value = "/{client_id}/tenants", method = {RequestMethod.POST})
    @ResponseBody
    @ApiOperation(nickname = "setAssignedTenants",
            value = "给单个组织结构分配应用",
            notes = "给单个组织结构分配应用",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "update:apps", description = "")})})
    @ApiResponses(value = {
            @ApiResponse(code = SC_NOT_FOUND,
                    response = BaseReturnResult.class,
                    message = "* 错误码: " + APP_NOT_FOUND_ERROR_CODE + ", 错误消息: " + APP_NOT_FOUND_ERROR_DESC
                            + "\n\r* 错误码: " + ORG_NOT_FOUND_ERROR_CODE + ", 错误消息: " + ORG_NOT_FOUND_ERROR_DESC),
            @ApiResponse(code = SC_INTERNAL_SERVER_ERROR,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + UNKNOWN_DAO_ERROR_CODE + ", 错误消息: " + UNKNOWN_DAO_ERROR_DESC)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String",
                    value = HEADER_REQUEST_CONTENT_TYPE,
                    defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
                    required = true)})
    ReturnResultVO<Void> setAssignedTenants(
            @NotEmpty @PathVariable("client_id") @ApiParam(required = true,
                    value = "应用id") String clientId,
            @Valid @RequestBody AppTenantListDto dto);

    /*****************************************************************给应用分配接口访问权限*********************************************************************/
    @RequestMapping(value = "/{client_id}/interface", method = {RequestMethod.POST})
    @ResponseBody
    @ApiOperation(nickname = "setAssignedTenants",
            value = "给应用分配接口访问权限",
            notes = "给应用分配接口访问权限",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "update:apps", description = "")})})
    @ApiResponses(value = {
            @ApiResponse(code = SC_NOT_FOUND,
                    response = BaseReturnResult.class,
                    message = "* 错误码: " + APP_NOT_FOUND_ERROR_CODE + ", 错误消息: " + APP_NOT_FOUND_ERROR_DESC
                            + "\n\r* 错误码: " + PERMISSION_NOT_FOUND_ERROR_DESC + ", 错误消息: " + PERMISSION_NOT_FOUND_ERROR_DESC),
            @ApiResponse(code = SC_INTERNAL_SERVER_ERROR,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + UNKNOWN_DAO_ERROR_CODE + ", 错误消息: " + UNKNOWN_DAO_ERROR_DESC)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String",
                    value = HEADER_REQUEST_CONTENT_TYPE,
                    defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
                    required = true)})
     ReturnResultVO<Void> entitleInterface(
            @NotEmpty @PathVariable("client_id") @ApiParam(required = true,
                    value = "应用id") String clientId,
            @Valid @RequestBody AppInterfaceDto dto);

    /*****************************************************************获取应用的权限*********************************************************************/
    @RequestMapping(value = "/getAppPermissionSets/{client_id}", method = GET)
    @ResponseBody
    @ApiOperation(response = Map.class, nickname = "getAppPermissionSets",
            value = "获取应用的权限组",
            notes = "获取应用的权限组",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "read:apps", description = "")})})
    @ApiResponses(value = {
            @ApiResponse(code = SC_NOT_FOUND,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + TransactionErrorDescription.INVALID_CLIENT + ", 错误消息: " + APP_NOT_FOUND_ERROR_DESC),
            @ApiResponse(code = SC_INTERNAL_SERVER_ERROR,
                    response = BaseReturnResult.class,
                    message = "* 错误码：" + TransactionErrorDescription.SERVER_ERROR + ", 错误消息: " + UNKNOWN_DAO_ERROR_DESC)
    })
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
                    required = true)})
    ReturnResultVO<AppInterfaceDto> getAppPermissionSets(
            @ApiParam(required = true, value = "应用Id")
            @PathVariable("client_id") String clientId);




    /******************************************************应用网关配置****************************************************/
    @RequestMapping(value = "/appGatewayConfig/{client_id}", method = PUT)
    @ResponseBody
    ReturnResultVO<AppGatewayConfig> appGatewayConfig(@PathVariable("client_id") String clientId, @RequestBody AppGatewayConfig appGatewayConfig);

    @RequestMapping(value = "/appGatewayConfig/{client_id}", method = DELETE)
    @ResponseBody
    ReturnResultVO<Void> delGatewayConfig(@PathVariable("client_id") String clientId);

    @RequestMapping(value = "/appGatewayConfig/{client_id}", method = GET)
    @ResponseBody
    ReturnResultVO<AppGatewayConfig> getGatewayConfig(@PathVariable("client_id") String clientId);
}
