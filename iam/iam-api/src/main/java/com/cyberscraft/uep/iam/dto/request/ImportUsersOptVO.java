package com.cyberscraft.uep.iam.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


@ApiModel(value = "import_user_option", description = "Indicate replace exist or not")
public class ImportUsersOptVO {
    @ApiModelProperty(name = "replace", value = "是否替换已经存在的用户？", dataType = "Boolean", required = true, example="true")
    private boolean replace;

    public boolean isReplace() {
        return replace;
    }

    public void setReplace(boolean replace) {
        this.replace = replace;
    }

    @Override
    public String toString() {
        return "ImportUsersOptVO [replace=" + replace + "]";
    }
}
