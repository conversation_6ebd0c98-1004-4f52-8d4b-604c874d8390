package com.cyberscraft.uep.iam.dto.response.ldapserver;

import com.cyberscraft.uep.iam.dto.enums.LdapUserStatus;
import com.fasterxml.jackson.databind.annotation.JsonNaming;

import java.util.List;

/**
 * 用户属性对象，用于查询条件和查询结果
 * @Author: liuanyang
 * @Date: 2021/11/5
 */
@JsonNaming()
public class UserMap {

    private String id;

    /**
     * 用户登录名
     */
    private String username;

    /**
     * email
     */
    private String email;

    /**
     * 手机号码
     */
    private String phoneNumber;

    /**
     * 状态
     */
    private LdapUserStatus status;

    /**
     * 名称
     */
    private String name;

    /**
     * 机构名称路径
     */
    private List<String> trace;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public LdapUserStatus getStatus() {
        return status;
    }

    public void setStatus(LdapUserStatus status) {
        this.status = status;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<String> getTrace() {
        return trace;
    }

    public void setTrace(List<String> trace) {
        this.trace = trace;
    }
}
