package com.cyberscraft.uep.mdm.api.admin.user;

import com.cyberscraft.uep.common.bean.Result;
import com.cyberscraft.uep.common.dto.PageView;
import com.cyberscraft.uep.common.dto.QueryPage;
import com.cyberscraft.uep.mdm.api.constant.UrlConstant;
import com.cyberscraft.uep.mdm.api.dto.user.UserListVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/***
 * 用户微服务对外接口
 * @date 2021-08-28
 * <AUTHOR>
 ***/
@RestController
@RequestMapping(value = UrlConstant.ADMIN_USER_URL_PREFIX, produces = MediaType.APPLICATION_JSON_VALUE)
@Api(value = "用户管理相关API", tags = "Admin Users")
public interface UserApi {
//
//    /****
//     * 获取用户详细信息
//     * @param userId
//     * @return
//     * @throws Exception
//     */
//    @RequestMapping(value = "/{userId}", method = RequestMethod.GET)
//    @ResponseBody
//    @ApiOperation(response = UserDetailVO.class, nickname = "getUserDetail",
//            value = "获取用户详细信息",
//            notes = "获取用户详细信息")
//    Result<UserDetailVO> getUserDetail(
//            @ApiParam(required = true, value = "用户Id", example = "1")
//            @PathVariable("userId") Long userId) throws Exception;
//
//

    /***
     * 获取用户列表
     * @param groupId
     * @param containChildren
     * @param otherInfo
     * @param status 
     * @param order
     * @param sort
     * @param page
     * @param size
     * @return
     */
    @RequestMapping(method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(nickname = "getUsers",
            value = "获取用户列表",
            notes = "根据查询条件获取用户列表，支持分页处理")
    Result<PageView<UserListVO>> getUsers(
            @RequestParam(value = "groupId", required = false)
            @ApiParam(
                    value = "组Id", example = "-2") Long groupId,

            @RequestParam(value = "containChildren", required = false)
            @ApiParam(
                    value = "是否查询子组",
                    example = "false") Boolean containChildren,

            @RequestParam(value = "otherInfo", required = false)
            @ApiParam(
                    value = "用户名/账号/邮箱/电话") String otherInfo,

            @RequestParam(value = "status", required = false)
            @ApiParam(
                    value = "用户状态1-正常 0-锁定",
                    example = "1") Integer status,

            @RequestParam(value = "order", required = false)
            @ApiParam(
                    value = "排序规则(DESC,ASC)",
                    example = "ASC") String order,

            @RequestParam(value = "sort", required = false)
            @ApiParam(
                    value = "排序字段",
                    example = "id") String sort,

            @RequestParam(value = "page", required = false, defaultValue = QueryPage.DEFAULT_PAGE_NUMBER + "")
            @ApiParam(value = "要获取的结果页号[1..N], 缺省是：1", example = "1")
                    Integer page,
            @RequestParam(value = "size", required = false, defaultValue = QueryPage.DEFAULT_PAGE_SIZE + "")
            @ApiParam(value = "获取的每一页的记录数[1..N], 缺省是:50, **每页允许的最大记录数为:100", example = "50")
                    Integer size);
//
//    @RequestMapping(value = "/invite_by_mail", method = RequestMethod.PUT)
//    @ResponseBody
//    @ApiOperation(nickname = "invite_by_mail",
//            value = "发邮件激活邀请",
//            notes = "根据传递的用户ID集合，批量发送激活邀请邮件")
//    Result<Map<String, String>> batchInviteByMail(
//            @ApiParam(required = true, value = "给相关用户发送激活邮件")
//            @RequestBody UserBatchInviteDto dto
//    );
//
//    /**
//     * 根据任务ID获取发送激活结果
//     */
//    @RequestMapping(value = "/tasks/{taskId}", method = RequestMethod.GET)
//    @ApiOperation(value = "根据用户ID获取发送激活邀请结果")
//    @ResponseBody
//    Result<InviteResultVO> getInviteTaskResult(@PathVariable("taskId") String taskId);
//
//    @RequestMapping(value = "/invite_by_sms", method = RequestMethod.PUT)
//    @ResponseBody
//    @ApiOperation(nickname = "invite_by_sms",
//            value = "发短信激活邀请",
//            notes = "根据传递的用户ID集合，批量发送激活短信")
//    Result<Map<String, String>> batchInviteBySms(
//            @ApiParam(required = true, value = "给相关用户发送激活短信")
//            @RequestBody UserBatchInviteDto dto
//    );
//
//    @RequestMapping(value = "/getUserPolicyDeviceNum", method = RequestMethod.GET)
//    @ResponseBody
//    @ApiOperation(nickname = "getUserPolicyDeviceNum",
//            value = "获取用户自定义策略数、所有设备数",
//            notes = "获取用户自定义策略数、所有设备数")
//    Result<Map<String, List<UserPolicyDeviceNumVO>>> getUserPolicyDeviceNum(
//            @RequestParam(value = "loginIds", required = true)
//            @ApiParam(value = "用户登录账号，多个账号以英文逗号分隔") String loginIds
//    );
//
//    @RequestMapping(value = "/syncUser", method = RequestMethod.POST)
//    @ResponseBody
//    @ApiOperation(nickname = "syncUser",
//            value = "手动同步用户和组织数据",
//            notes = "手动同步用户和组织数据")
//    Result<Boolean> syncUser(
//            @RequestParam(value = "connectorId", required = false)
//            @ApiParam(value = "链接器ID") Long connectorId
//    );
//

    /****
     * 查询出第三方平台用户信息对像
     * @param accountType
     * @param userId
     * @param <T>
     * @return
     */
    @RequestMapping(value = "/getThirdPartyUser", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(nickname = "getThirdPartyUser",
            value = "查询出第三方平台用户信息对像",
            notes = "查询出第三方平台用户信息对像")
    <T> Result<T> getThirdPartyUser(
            @RequestParam(value = "tenantId", required = true)
            @ApiParam(value = "租户ID") String tenantId,
            @RequestParam(value = "accountType", required = true)
            @ApiParam(value = "账户类型") String accountType,
            @RequestParam(value = "userId", required = true)
            @ApiParam(value = "第三方平台用户ID") String userId
    );

//
//    /****
//     * 创建测试用户
//     * @return
//     * @throws Exception
//     */
//    @RequestMapping(value = "/create_test_user", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE, consumes = {MediaType.ALL_VALUE})
//    @ResponseBody
//    @ApiOperation(response = String.class, nickname = "创建测试用户",
//            value = "创建测试用户",
//            notes = "创建测试用户")
//    <T> Result<T> crateTestUser(
//            @RequestParam(value = "startLoginId", required = true) Long startLoginId,
//            @RequestParam(value = "maxNum", required = true) Integer maxNum);
}
