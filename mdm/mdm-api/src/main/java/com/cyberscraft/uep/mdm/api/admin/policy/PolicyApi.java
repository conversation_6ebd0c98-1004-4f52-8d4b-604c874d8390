package com.cyberscraft.uep.mdm.api.admin.policy;

import com.cyberscraft.uep.common.bean.Result;
import com.cyberscraft.uep.common.dto.PageView;
import com.cyberscraft.uep.common.dto.QueryPage;
import com.cyberscraft.uep.mdm.api.constant.UrlConstant;
import com.cyberscraft.uep.mdm.api.dto.cmd.CmdTargetDto;
import com.cyberscraft.uep.mdm.api.dto.policy.*;
import com.cyberscraft.uep.mdm.api.dto.user.UserListVO;
import io.swagger.annotations.*;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import static com.cyberscraft.uep.common.constant.UEPSwaggerConstants.*;

/***
 * MDM微服务中，管理员对应的设备对外服务接口
 * @date 2021-09-23
 * <AUTHOR>
 ***/
@RestController
@RequestMapping(value = UrlConstant.ADMIN_POLICY_URL_PREFIX, produces = MediaType.APPLICATION_JSON_VALUE)
@Api(value = "策略管理相关API", tags = "Admin Policies")
public interface PolicyApi {
    @RequestMapping(value = "", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(nickname = "createPolicy",
            value = "创建mdm策略",
            notes = "创建mdm策略")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true),
    })
    Result<String> create(
            @ApiParam(required = true, value = "创建策略请求参数体")
            @RequestBody CreatePolicyDto device);

    @RequestMapping(value = "/{policyId}", method = RequestMethod.PUT)
    @ResponseBody
    @ApiOperation(nickname = "updatePolicy",
            value = "更新mdm策略",
            notes = "更新mdm策略")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true),
    })
    Result<Void> update(
            @ApiParam(required = true, value = "策略Id", example = "a64930c5-bc11-4453-b1b2-187de96e1099")
            @PathVariable("policyId") String policyId,
            @ApiParam(required = true, value = "创建策略请求参数体")
            @RequestBody UpdatePolicyDto device);

    @RequestMapping(value = "/{policyId}", method = RequestMethod.DELETE)
    @ResponseBody
    @ApiOperation(nickname = "deletePolicy",
            value = "删除mdm策略",
            notes = "删除mdm策略")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true),
    })
    Result<PolicyVO> delete(
            @ApiParam(required = true, value = "策略Id", example = "a64930c5-bc11-4453-b1b2-187de96e1099")
            @PathVariable("policyId") String policyId);

    @RequestMapping(value = "/{policyId}/relationships", method = RequestMethod.DELETE)
    @ResponseBody
    @ApiOperation(nickname = "deletePolicyRelationships",
            value = "删除策略下发关系",
            notes = "删除策略下发关系")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true),
    })
    Result<PolicyVO> deletePolicyRelationships(
            @ApiParam(required = true, value = "策略Id", example = "a64930c5-bc11-4453-b1b2-187de96e1099")
            @PathVariable("policyId") String policyId,
            @ApiParam(required = true, value = "删除策略下发的目标对象，可以同时指定多个target")
            @RequestBody CmdTargetDto target);

    @RequestMapping(value = "/{policyId}", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(nickname = "getPolicy",
            value = "查询mdm策略",
            notes = "查询mdm策略")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true),
    })
    Result<PolicyVO> get(
            @ApiParam(required = true, value = "策略Id", example = "a64930c5-bc11-4453-b1b2-187de96e1099")
            @PathVariable("policyId") String policyId);

    @RequestMapping(value = "/userPolicy", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(nickname = "getUserPolicy",
            value = "查询用户策略详情",
            notes = "查询用户策略详情")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true),
    })
    Result<PolicyVO> getUserPolicy(
            @ApiParam(required = false, value = "userId", example = "1")
            @RequestParam(required = false, value = "userId") Long userId,
            @ApiParam(required = false, value = "longinId", example = "zhengdao.zb")
            @RequestParam(required = false, value = "loginId") String loginId,
            @ApiParam(required = true, value = "策略类型", example = "APP_CONFIG",
                    allowableValues = "APP_CONFIG,DEVICE_VIOLATION,SENSITIVE_WORDS,SENSITIVE_URLS,MDM_POLICY,DEVICE_ACCESS_CONFIG")
            @RequestParam("policySubCategory") String policySubCategory);

    @RequestMapping(method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(nickname = "getPolicyList",
            value = "获取策略列表",
            notes = "获取策略列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true),
    })
    Result<PageView<PolicyVO>> getPolicyList(
            @ApiParam(required = true, value = "策略类型", example = "APP_CONFIG",
                    allowableValues = "APP_CONFIG,DEVICE_VIOLATION,SENSITIVE_WORDS,SENSITIVE_URLS,MDM_POLICY,DEVICE_ACCESS_CONFIG")
            @RequestParam(value = "policySubCategory", required = false)
                    String policySubCategory,
            @ApiParam(required = false, value = "策略名称", example = "沙箱应用1")
            @RequestParam(value = "name", required = false)
                    String name,
            @RequestParam(value = "page", required = false, defaultValue = QueryPage.DEFAULT_PAGE_NUMBER + "")
            @ApiParam(value = "要获取的结果页号[1..N], 缺省是：1", example = "1")
                    Integer page,
            @RequestParam(value = "size", required = false, defaultValue = QueryPage.DEFAULT_PAGE_SIZE + "")
            @ApiParam(value = "获取的每一页的记录数[1..N], 缺省是:50, **每页允许的最大记录数为:100", example = "50")
                    Integer size);

    @RequestMapping(value = "/status", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(nickname = "updateStatus",
            value = "批量更新mdm策略状态",
            notes = "批量更新mdm策略状态")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true),
    })
    Result<Void> updateStatus(
            @ApiParam(required = true, value = "批量更新mdm策略状态请求体")
            @RequestBody UpdatePolicyStatusDto dto);

    @RequestMapping(value = "/{policyId}/push", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(nickname = "pushPolicy",
            value = "下发mdm策略",
            notes = "下发mdm策略")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true),
    })
    Result<PolicyVO> push(
            @ApiParam(required = true, value = "策略Id", example = "a64930c5-bc11-4453-b1b2-187de96e1099")
            @PathVariable("policyId") String policyId,
            @ApiParam(required = true, value = "策略下发的目标对象，可以同时指定多个target")
            @RequestBody CmdTargetDto target);

    @RequestMapping(value = "/{policyId}/repush", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(nickname = "pushPolicy",
            value = "对之前下发mdm策略再次推送",
            notes = "对之前下发mdm策略再次推送")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true),
    })
    Result<PolicyVO> repush(
            @ApiParam(required = true, value = "策略Id", example = "a64930c5-bc11-4453-b1b2-187de96e1099")
            @PathVariable("policyId") String policyId);


    @RequestMapping(value = "/policyPushedUsers", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(nickname = "getPolicyPushedUsers",
            value = "查询策略生效范围",
            notes = "查询策略生效范围")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true),
    })
    Result<PageView<UserListVO>> getPolicyPushedUsers(
            @ApiParam(required = true, value = "policyId", example = "c759740c-595c-4b70-86a1-ed5ee68b78e1")
            @RequestParam("policyId") String policyId,
            @ApiParam(required = false, value = "查询字符串", example = "queryStr")
            @RequestParam(value = "q", required = false) String q,
            @RequestParam(value = "page", required = false, defaultValue = QueryPage.DEFAULT_PAGE_NUMBER + "")
            @ApiParam(value = "要获取的结果页号[1..N], 缺省是：1", example = "1") Integer page,
            @RequestParam(value = "size", required = false, defaultValue = QueryPage.DEFAULT_PAGE_SIZE + "")
            @ApiParam(value = "获取的每一页的记录数[1..N], 缺省是:50, **每页允许的最大记录数为:100", example = "50") Integer size);

    @RequestMapping(value = "/policyPushedDevices", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(nickname = "getPolicyPushedDevices",
            value = "查询策略生效状态详情",
            notes = "查询策略生效状态详情")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true),
    })
    Result<PageView<PolicyPushedDeviceListVO>> getPolicyPushedDevices(
            @ApiParam(required = true, value = "policyId", example = "c759740c-595c-4b70-86a1-ed5ee68b78e1")
            @RequestParam("policyId") String policyId,
            @ApiParam(required = true, value = "用户ID", example = "dashan")
            @RequestParam("userId") String userId,
            @ApiParam(required = false, value = "策略生效状态，已生效1  未生效0  不传则查询全部", example = "1", allowableValues = "0,1")
            @RequestParam(value = "devicePolicyStatus", required = false) Integer devicePolicyStatus,
            @ApiParam(value = "要获取的结果页号[1..N], 缺省是：1", example = "1")
            @RequestParam(value = "page", required = false, defaultValue = QueryPage.DEFAULT_PAGE_NUMBER + "") Integer page,
            @ApiParam(value = "获取的每一页的记录数[1..N], 缺省是:50, **每页允许的最大记录数为:100", example = "50")
            @RequestParam(value = "size", required = false, defaultValue = QueryPage.DEFAULT_PAGE_SIZE + "") Integer size);


    @RequestMapping(value = "/getPolicyUsers", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(nickname = "getPolicyUsers",
            value = "查询策略用户",
            notes = "查询策略用户")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX, required = true),
    })
    Result<List<String>> getPolicyUsers(
            @ApiParam(required = true, value = "policyId", example = "login")
            @RequestParam(required = true, value = "policyId") String policyId);
}

