package com.cyberscraft.uep.mdm.api.constant;

/***
 * URL常量定义
 * @date 2021-08-28
 * <AUTHOR>
 ***/
public class UrlConstant {

    /***
     * 自服务用户对应的请求
     */
    public final static String SELF_URL_PREFIX = "/mdm/self";

    /***
     * 基础服务用户对应的请求
     */
    public final static String BASIC_URL_PREFIX = "/mdm/basic";

    /***
     * 基础租户服务用户对应的请求
     */
    public final static String BASIC_TENANT_URL_PREFIX = "/mdm/basic/tenants";
    /***
     * 基础租户服务用户对应的请求
     */
    public final static String BASIC_CONFIG_URL_PREFIX = "/mdm/basic/config";
    /***
     * 基础数据服务对应的请求
     */
    public final static String BASIC_DATA_URL_PREFIX = "/mdm/basic/message";
    /***
     * 基础事件服务对应的请求
     */
    public final static String BASIC_EVENT_URL_PREFIX = "/mdm/basic/event";

    /***
     * 推送对应的API
     */
    public final static String BASIC_PUSH_URL_PREFIX = "/mdm/basic/push";


    /**********************************
     * 管理员对应的请求
     *********************************/
    public final static String ADMIN_URL_PREFIX = "/mdm/admin";

    /***
     * 用户对应的URL前缀
     */
    public final static String ADMIN_USER_URL_PREFIX = ADMIN_URL_PREFIX + "/users";

    /***
     * 设备对应的URL前缀
     */
    public final static String ADMIN_DEVICE_URL_PREFIX = ADMIN_URL_PREFIX + "/devices";

    /***
     * 策略对应的URL前缀
     */
    public final static String ADMIN_POLICY_URL_PREFIX = ADMIN_URL_PREFIX + "/policies";

    /***
     * 策略对应的URL前缀
     */
    public final static String ADMIN_LOG_URL_PREFIX = ADMIN_URL_PREFIX + "/logs";

    /***
     * 组管理对应的URL前缀
     */
    public final static String ADMIN_GROUP_URL_PREFIX = ADMIN_URL_PREFIX + "/groups";

    /***
     * 向客户端发命令对应的URL前缀
     */
    public final static String ADMIN_COMMAND_URL_PREFIX = ADMIN_URL_PREFIX + "/command";

    /***
     * 标签管理对应的URL前缀
     */
    public final static String ADMIN_LABEL_URL_PREFIX = ADMIN_URL_PREFIX + "/label";

    /***
     * 标签管理对应的URL前缀
     */
    public final static String ADMIN_CLIENTAPP_URL_PREFIX = ADMIN_URL_PREFIX + "/clientapps";

    /***
     * 系统配置相关的URL前缀
     */
    public final static String ADMIN_SETTINGS_URL_PREFIX = ADMIN_URL_PREFIX + "/settings";

    /***
     * 证书管理相关的URL前缀
     */
    public final static String ADMIN_CERT_URL_PREFIX = ADMIN_SETTINGS_URL_PREFIX + "/certificate";


    /**********************************
     * 设备及应用客户端对应的请求
     *********************************/
    public final static String APP_URL_PREFIX = "/mdm/app";

    /***
     * 设备及应用客户端对应的指令相关请求
     */
    public final static String APP_COMMAND_URL_PREFIX = APP_URL_PREFIX + "/command";

    /***
     * 设备及应用客户端对应的OAUTH2请求
     */
    public final static String APP_OAUTH2_URL_PREFIX = APP_URL_PREFIX + "/oauth2";

    /***
     * 基础租户服务文件对应的请求
     */
    public final static String BASIC_FILE_URL_PREFIX = APP_URL_PREFIX + "/basic/file";

    /***
     * 设备及应用客户端对应的IOS请求
     */
    public final static String APP_IOS_URL_PREFIX = APP_URL_PREFIX + "/ios";

    /***
     * 设备及应用客户端对应的ANDRIOD请求
     */
    public final static String APP_ANDROID_URL_PREFIX = APP_URL_PREFIX + "/android";

    /***
     * 设备及应用客户端对应的ANDRIOD请求
     */
    public final static String APP_TDOS_URL_PREFIX = APP_URL_PREFIX + "/tdos";

    /***
     * 设备及应用客户端对应的WINDOWS请求
     */
    public final static String APP_WINDOWS_URL_PREFIX = APP_URL_PREFIX + "/windows";


    /***
     * 老接口
     */
    public final static String APP_LEGACY_URL_PREFIX = APP_URL_PREFIX + "/legacy";


}
