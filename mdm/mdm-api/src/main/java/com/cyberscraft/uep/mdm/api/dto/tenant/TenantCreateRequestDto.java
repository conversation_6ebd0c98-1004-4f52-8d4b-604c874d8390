package com.cyberscraft.uep.mdm.api.dto.tenant;

import com.cyberscraft.uep.mdm.api.constant.TenantFromType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/***
 *
 * @date 2021-10-16
 * <AUTHOR>
 ***/
@ApiModel(value = "TenantCreateRequestDto", description = "租户创建MDM服务（初始化租户MDM服务)的请求对像")
public class TenantCreateRequestDto implements Serializable {

    private static final long serialVersionUID = -3323583518508635166L;
    /***
     * 租户ID
     */
    @ApiModelProperty(value="当前要初始化MDM服务的租户ID，必填",required = true)
    @NotEmpty(message = "tenant id is empty")
    private String tenantId;

    /***
     * 租户名称
     */
    @ApiModelProperty(value="当前要初始化MDM服务的租户名称，必填",required = true)
    @NotEmpty(message = "tenant name is empty")
    private String tenantName;

    /***
     * DB前缀
     */
    @ApiModelProperty(value="当前要初始化MDM服务的租户数据库前缀，如果未指定，则以当前服务中配置的为准")
    //@NotEmpty(message = "db prefix is empty")
    private String dbPrefix;

    /***
     * 数据库所在服务器
     */
    @ApiModelProperty(value="当前要初始化MDM服务的租户数据库所在机器，如果未指定，则以当前服务中配置的第一个db host为准")
    //@NotEmpty(message = "db host is empty")
    private String dbHost;

    /***
     * 当前服务所使用的数据库名
     */
    @ApiModelProperty(value="当前要初始化MDM服务的租户数据库名称，如果未指定，则以当前服务中配置的为准")
    //@NotEmpty(message = "db name is empty")
    private String dbName;


    /***
     * 当前服务所使用的数据库名
     */
    @ApiModelProperty(value="当前租户对应的超级管理员密码")
    //@NotEmpty(message = "db name is empty")
    private String adminPwd;

    @ApiModelProperty(value="租户消息来源类型")
    private TenantFromType tenantFromType;

    @ApiModelProperty(value="租户消息通知ID")
    private String messageId;

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getTenantName() {
        return tenantName;
    }

    public void setTenantName(String tenantName) {
        this.tenantName = tenantName;
    }

    public String getDbPrefix() {
        return dbPrefix;
    }

    public void setDbPrefix(String dbPrefix) {
        this.dbPrefix = dbPrefix;
    }

    public String getDbHost() {
        return dbHost;
    }

    public void setDbHost(String dbHost) {
        this.dbHost = dbHost;
    }

    public String getDbName() {
        return dbName;
    }

    public void setDbName(String dbName) {
        this.dbName = dbName;
    }

    public String getAdminPwd() {
        return adminPwd;
    }

    public void setAdminPwd(String adminPwd) {
        this.adminPwd = adminPwd;
    }

    public TenantFromType getTenantFromType() {
        return tenantFromType;
    }

    public void setTenantFromType(TenantFromType tenantFromType) {
        this.tenantFromType = tenantFromType;
    }

    public String getMessageId() {
        return messageId;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }

    @Override
    public String toString() {
        return "TenantCreateRequestDto{" +
                "tenantId='" + tenantId + '\'' +
                ", tenantName='" + tenantName + '\'' +
                ", dbPrefix='" + dbPrefix + '\'' +
                ", dbHost='" + dbHost + '\'' +
                ", dbName='" + dbName + '\'' +
                ", adminPwd='" + adminPwd + '\'' +
                ", tenantFromType=" + tenantFromType +
                ", messageId='" + messageId + '\'' +
                '}';
    }
}
