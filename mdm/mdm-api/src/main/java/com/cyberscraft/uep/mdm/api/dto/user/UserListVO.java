package com.cyberscraft.uep.mdm.api.dto.user;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;


/***
 * 用户列表视图对像
 */
public class UserListVO implements Serializable {
    private String id;
    private String loginId;
    private String name;
    private String mail;
    private String mobile;
    private Number activeDeviceNum;                //已激活设备数量
    private Integer activeDevicePolicyNum;          //已激活设备的策略生效数量(DeviceProfileEntity status=1)
    private Integer status;                         //用户状态 1-正常 0-锁定 -1删除
    private LocalDateTime updateTime;
    private Number registerDeviceNum;
    private String oid;//第三方用户id
    private int policyStatus;//用户策略状态    0全局默认   1自定义
    private List<UserGroupVO> groups;
    private String orgNames;


    public List<UserGroupVO> getGroups() {
        return groups;
    }

    public void setGroups(List<UserGroupVO> groups) {
        this.groups = groups;
    }

    public String getOrgNames() {
        return orgNames;
    }

    public void setOrgNames(String orgNames) {
        this.orgNames = orgNames;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getLoginId() {
        return loginId;
    }

    public void setLoginId(String loginId) {
        this.loginId = loginId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMail() {
        return mail;
    }

    public void setMail(String mail) {
        this.mail = mail;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public Number getActiveDeviceNum() {
        return activeDeviceNum == null ? 0 : activeDeviceNum;
    }

    public void setActiveDeviceNum(Number activeDeviceNum) {
        this.activeDeviceNum = activeDeviceNum;
    }

    public Integer getActiveDevicePolicyNum() {
        return  activeDevicePolicyNum == null ? 0 : activeDevicePolicyNum;
    }

    public void setActiveDevicePolicyNum(Integer activeDevicePolicyNum) {
        this.activeDevicePolicyNum = activeDevicePolicyNum ;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public Number getRegisterDeviceNum() {
        return registerDeviceNum == null ? 0 : registerDeviceNum;
    }

    public void setRegisterDeviceNum(Number registerDeviceNum) {
        this.registerDeviceNum = registerDeviceNum;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public int getPolicyStatus() {
        return policyStatus;
    }

    public void setPolicyStatus(int policyStatus) {
        this.policyStatus = policyStatus;
    }
}
