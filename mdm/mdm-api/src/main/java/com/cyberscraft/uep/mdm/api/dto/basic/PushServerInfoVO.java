package com.cyberscraft.uep.mdm.api.dto.basic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/***
 *
 * @date 2021-10-24
 * <AUTHOR>
 ***/
@ApiModel(value = "PushServerInfoVO", description = "服务端推送相关信息")
public class PushServerInfoVO implements Serializable {


    private static final long serialVersionUID = -3098571453865878374L;
    /***
     * PN服务器类型
     */
    @ApiModelProperty(value = "App推送对应的PN服务器类型")
    private String pushType; // required
    /***
     * PN服务器类型
     */
    @ApiModelProperty(value = "MDM指令推送对应的 PN服务器类型")
    private String mdmPushType; // required
    /***
     * PN服务器地址
     */
    @ApiModelProperty(value = "PN服务器地址")
    private String server; // required
    /***
     * PN服务器端口
     */
    @ApiModelProperty(value = "PN服务器端口")
    private String port; // required
    /***
     * PN服务器端口
     */
    @ApiModelProperty(value = "PN服务器端口")
    private String portNumber;
    /****
     *
     */
    @ApiModelProperty(value = "PN服务器tcp端口")
    private String tcpPort;
    /****
     *
     */
    @ApiModelProperty(value = "Android push type")
    private String androidPushType;

    public String getPushType() {
        return pushType;
    }

    public void setPushType(String pushType) {
        this.pushType = pushType;
    }

    public String getMdmPushType() {
        return mdmPushType;
    }

    public void setMdmPushType(String mdmPushType) {
        this.mdmPushType = mdmPushType;
    }

    public String getPort() {
        return port;
    }

    public void setPort(String port) {
        this.port = port;
    }

    public String getPortNumber() {
        return portNumber;
    }

    public void setPortNumber(String portNumber) {
        this.portNumber = portNumber;
    }

    public String getTcpPort() {
        return tcpPort;
    }

    public void setTcpPort(String tcpPort) {
        this.tcpPort = tcpPort;
    }

    public String getServer() {
        return server;
    }

    public void setServer(String server) {
        this.server = server;
    }

    public String getAndroidPushType() {
        return androidPushType;
    }

    public void setAndroidPushType(String androidPushType) {
        this.androidPushType = androidPushType;
    }
}
