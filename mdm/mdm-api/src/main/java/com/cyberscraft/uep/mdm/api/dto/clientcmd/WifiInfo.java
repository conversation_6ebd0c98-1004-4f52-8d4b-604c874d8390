package com.cyberscraft.uep.mdm.api.dto.clientcmd;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * @Author: liuanyang
 * @Date: 2021/8/2
 */

@ApiModel(value = "WifiInfo", description = "病毒信息")
public class WifiInfo  implements Serializable {

    private static final long serialVersionUID = -1L;

    @ApiModelProperty(name = "bssid", value = "bssid", dataType = "String")
    private String bssid;

    @ApiModelProperty(name = "encryption", value = "加密方式", dataType = "String")
    private String encryption;

    public String getBssid() {
        return bssid;
    }

    public void setBssid(String bssid) {
        this.bssid = bssid;
    }

    public String getEncryption() {
        return encryption;
    }

    public void setEncryption(String encryption) {
        this.encryption = encryption;
    }
}
