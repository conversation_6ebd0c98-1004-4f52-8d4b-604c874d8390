package com.cyberscraft.uep.mdm.api.dto.user;

import com.cyberscraft.uep.common.dto.IQueryDto;

import java.util.List;

/****
 * 从原来的查询条件中拷贝而来
 */
public class UserQueryDTO implements IQueryDto {
    private List<Long> userIds;
    private Long groupId;                           //组ID
    private List<Long> groupIds;                           //组ID
    private Boolean containChildren;                //是否查询子组
    private String q;                       //用户名/账号/邮箱/电话
    private Integer status;                         //用户状态 1-正常 0-锁定
    private String sort;                            //排序字段
    private String order;                            // 排序规则(DESC,ASC)

    public List<Long> getUserIds() {
        return userIds;
    }

    public void setUserIds(List<Long> userIds) {
        this.userIds = userIds;
    }

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }


    public Boolean getContainChildren() {
        return containChildren;
    }

    public void setContainChildren(Boolean containChildren) {
        this.containChildren = containChildren;
    }

    public List<Long> getGroupIds() {
        return groupIds;
    }

    public void setGroupIds(List<Long> groupIds) {
        this.groupIds = groupIds;
    }

    public String getQ() {
        return q;
    }

    public void setQ(String q) {
        this.q = q;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getSort() {
        return sort;
    }

    public void setSort(String sort) {
        this.sort = sort;
    }

    public String getOrder() {
        return order;
    }

    public void setOrder(String order) {
        this.order = order;
    }
}
