package com.cyberscraft.uep.mdm.api.dto.clientcmd;

import com.cyberscraft.uep.common.enums.Platform;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/***
 * 客户端上报设备越狱状态
 * @date 2021-10-23
 * <AUTHOR>
 ***/
@ApiModel(value = "UploadJailbrokenCmdBody", description = "客户端上报设备越狱状态")
public class UploadJailbrokenCmdBody extends EmptyClientCmdBody {

    private static final long serialVersionUID = -3560968297883686159L;
    /***
     * 域状态
     */
    @ApiModelProperty(name = "jailbroken", value = "设备的越狱状态", dataType = "Integer")
    Integer jailbroken;

    /***
     * 设备的开机时长
     */
    @ApiModelProperty(name = "bootTime", value = "设备的开机时长")
    Long bootTime;


    public Integer getJailbroken() {
        return jailbroken;
    }

    public void setJailbroken(Integer jailbroken) {
        this.jailbroken = jailbroken;
    }

    public Long getBootTime() {
        return bootTime;
    }

    public void setBootTime(Long bootTime) {
        this.bootTime = bootTime;
    }
}
