package com.cyberscraft.uep.mdm.api.dto.user;

import java.io.Serializable;

/***
 * 用户列表数据加载明细，主要用于扩展相关逻辑，便于优化处理
 * @date 2021-09-20
 * <AUTHOR>
 ***/
public class UserListLoadDetailDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /***
     * 加载组相关信息
     */
    private boolean isLoadGroup= true;

    /***
     * 加载设备相关信息，主要是指用于列表管理的设备统计信息
     */
    private boolean isLoadDevice= true;

    /***
     * 是否加载密码信息，如果需要的话，则需要用密码进行解密处理
     */
    private boolean isLoadPassword=false;
    
    /**
     * 是否加载用户的风控策略状态信息
     */
    private boolean isLoadPolicyStatus = true;
    
    /**
     * 是否加载用户下设备的风控策略生效信息，用于策略-查看生效范围页面的用户查询
     */
    private boolean isLoadDevicePolicy = false;

    /***
     * 需要加载的策略数据相关的策略ID
     */
    private String policyId;

    public boolean isLoadGroup() {
        return isLoadGroup;
    }

    public void setLoadGroup(boolean loadGroup) {
        isLoadGroup = loadGroup;
    }

    public boolean isLoadDevice() {
        return isLoadDevice;
    }

    public void setLoadDevice(boolean loadDevice) {
        isLoadDevice = loadDevice;
    }

    public boolean isLoadPassword() {
        return isLoadPassword;
    }

    public void setLoadPassword(boolean loadPassword) {
        isLoadPassword = loadPassword;
    }

    public boolean isLoadPolicyStatus() {
        return isLoadPolicyStatus;
    }

    public void setLoadPolicyStatus(boolean isLoadPolicyStatus) {
        this.isLoadPolicyStatus = isLoadPolicyStatus;
    }

    public boolean isLoadDevicePolicy() {
        return isLoadDevicePolicy;
    }

    public void setLoadDevicePolicy(boolean isLoadDevicePolicy) {
        this.isLoadDevicePolicy = isLoadDevicePolicy;
    }

    public String getPolicyId() {
        return policyId;
    }

    public void setPolicyId(String policyId) {
        this.policyId = policyId;
    }
}
