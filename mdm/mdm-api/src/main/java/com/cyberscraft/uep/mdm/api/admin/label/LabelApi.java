package com.cyberscraft.uep.mdm.api.admin.label;

import com.cyberscraft.uep.common.bean.Result;
import com.cyberscraft.uep.common.dto.PageView;
import com.cyberscraft.uep.mdm.api.constant.UrlConstant;
import com.cyberscraft.uep.mdm.api.dto.label.DynaLabelUserGroup;
import com.cyberscraft.uep.mdm.api.dto.label.LabelDetailsVO;
import com.cyberscraft.uep.mdm.api.dto.label.StaticLabelVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

/***
 * MDM微服务中，管理员对标签的操作接口
 * @date 2021-10-17
 * <AUTHOR>
 ***/
@RestController
@RequestMapping(value = UrlConstant.ADMIN_LABEL_URL_PREFIX, produces = MediaType.APPLICATION_JSON_VALUE)
@Api(value = "标签相关API", tags = "Admin Label")
public interface LabelApi {

    @RequestMapping(value = "/get_label", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(nickname = "getAllLabels", value = "获取所有的标签", notes = "获取所有的标签")
    Result<PageView<LabelDetailsVO>> getAllLabels(
            @RequestParam(value = "labelNameOrDesc", required = false) String labelNameOrDesc,
            @RequestParam(value = "isAll", required = false) Integer isAll,
            @RequestParam(value = "sort", required = false) String sort,
            @RequestParam(value = "order", required = false) String order,
            @RequestParam("offset") int offset,
            @RequestParam("limit") int limit);


    @RequestMapping(value = "/dynamic/linkedUsers", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(nickname = "getDynamicLabelLinkedUsers", value = "获取动态标签的用户", notes = "获取动态标签的用户")
    Result<PageView<DynaLabelUserGroup>> getDynamicLabelLinkedUsers(@RequestParam("labelId") String labelId,
                                                                    @RequestParam(value = "otherInfo", required = false) String otherInfo,
                                                                    @RequestParam("offset") int offset,
                                                                    @RequestParam("limit") int limit);

    @RequestMapping(value = "/static/linkedUsers", method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(nickname = "getStaticLabelLinkedUsers", value = "获取静态标签的用户", notes = "获取静态标签的用户")
    Result<PageView<DynaLabelUserGroup>> getStaticLabelLinkedUsers(@RequestParam("labelId") String labelId,
                                                                   @RequestParam(value = "otherInfo", required = false) String otherInfo,
                                                                   @RequestParam(value = "userIds", required = false) String userIds,
                                                                   @RequestParam("offset") int offset,
                                                                   @RequestParam("limit") int limit);

    @RequestMapping(value = "/staticLabel", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(nickname = "addStaticLabel", value = "新增静态标签", notes = "新增静态标签")
    Result<Long> addStaticLabel(@ApiParam(required = true, value = "标签的VO信息")
                                @RequestBody StaticLabelVO VO);

    @RequestMapping(value = "/staticLabel/{labelId}", method = RequestMethod.PUT)
    @ResponseBody
    @ApiOperation(nickname = "updStaticLabel", value = "修改静态标签", notes = "修改静态标签")
    Result<Boolean> updStaticLabel(
            @ApiParam(required = true, value = "设备Id", example = "1")
            @PathVariable("labelId") Long labelId,
            @ApiParam(required = true, value = "标签的VO信息")
            @RequestBody StaticLabelVO VO);
}
