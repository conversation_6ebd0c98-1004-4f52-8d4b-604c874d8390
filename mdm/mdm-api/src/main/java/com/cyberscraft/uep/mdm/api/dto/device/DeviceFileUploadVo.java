package com.cyberscraft.uep.mdm.api.dto.device;

import org.springframework.web.multipart.MultipartFile;

/**
 * <p>
 *     文件对象类
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2019-11-25 15:23
 */
public class DeviceFileUploadVo implements java.io.Serializable {
    private static final long serialVersionUID = -2638339476187495490L;

    private String udid;
    private String userid;
    /**
     * 文件
     */
    private MultipartFile uploadedFile;
    /**
     * 业务类型
     */
    private Integer businessType;

    /**
     * 录屏的批次号
     */
    private String batchNo;

    private String packageName;
    /**
     * 发生时间
     */
    private Long occTime;

    public String getUdid() {
        return udid;
    }

    public void setUdid(String udid) {
        this.udid = udid;
    }

    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    public MultipartFile getUploadedFile() {
        return uploadedFile;
    }

    public void setUploadedFile(MultipartFile uploadedFile) {
        this.uploadedFile = uploadedFile;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public Long getOccTime() {
        return occTime;
    }

    public void setOccTime(Long occTime) {
        this.occTime = occTime;
    }

    @Override
    public String toString() {
        return "DeviceFileUploadVo{" +
                "udid='" + udid + '\'' +
                ", userid='" + userid + '\'' +
                ", uploadedFile=" + uploadedFile +
                ", batchNo=" + batchNo +
                ", businessType=" + businessType +
                ", packageName='" + packageName + '\'' +
                ", occTime=" + occTime +
                '}';
    }
}
