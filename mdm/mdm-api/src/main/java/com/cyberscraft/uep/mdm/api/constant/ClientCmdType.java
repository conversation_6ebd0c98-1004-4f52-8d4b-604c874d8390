package com.cyberscraft.uep.mdm.api.constant;

/***
 *
 * @date 2021-10-22
 * <AUTHOR>
 ***/
public enum ClientCmdType {
    /***
     * 用户设备授权
     */
    AUTH("auth"),
    /***
     * 用户推送指令执行结果
     */
    PUSH_OPEROK("push_oper_ok"),
    /***
     * 拉取指令命令
     */
    PULL("pull"),

    /***
     * 获取用户相关信息
     */
    QUERY_USER_PROFILE("query_user_profile"),

    /***
     * Android 设备激活挑战
     */
    CHALLENGE("challenge"),
    /***
     * Android 设备激活
     */
    ACTIVATE("activate"),
    /***
     * 取消设备MDM激活
     */
    DEACTIVATE_MDM("deactivate_mdm"),
    /***
     * 上报域状态
     */
    UPLOAD_DUAL_STATUS("upload_dual_status"),
    /***
     * 上报越狱状态
     */
    UPLOAD_JAILBROKEN_STATUS("upload_jailbroken"),
    /****
     * 上传违规处理结果
     */
    UPLOAD_VIOLIATION("upload_violiation"),
    /****
     *上传违规处理过程
     */
    UPLOAD_VIOLIATION_PROCESS("upload_violiation_process"),
    /****
     * 上传所有的应用列表
     */
    UPLOAD_ALL_APP("upload_all_app"),
    /***
     * 更改客户端版本
     */
    UPDATE_VERSION("update_version"),
    /***
     * 更改APNS Token信息
     */
    UPDATE_APP_PUSH_TOKEN("update_app_push_token"),
    /***
     * 更改设备信息
     */
    UPDATE_DEVICE("update_device"),
    /***
     * 更改电池息
     */
    UPDATE_BATTERY("update_battery"),
    /***
     * 更新设备流量信息
     */
    UPDATE_NET_TRAFFIC("update_net_traffic"),
    /***
     * 获取服务端信息
     */
    QUERY_SERVER_INFO("query_server_info"),
    /***
     * 获取服务端信息
     */
    QUERY_DEVICE_STATUS("query_device_status"),
    /***
     * 更新网络状态,请求网络状态，对应着原3111
     */
    REQUEST_NET("request_net"),
    /***
     *
     */
    GET_CIRCLEOPER("get_circleoper"),
    /***
     * 生成UDID
     */
    CREATE_UDID("create_udid"),

    /**
     * 安全监测上报
     */
    UPLOAD_RISK("upload_risk"),

    /**
     * Json格式的命令
     */
    JSON_PROTOCOL("json_protocol");

    private String code;

    ClientCmdType(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }
}
