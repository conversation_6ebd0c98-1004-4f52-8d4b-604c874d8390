package com.cyberscraft.uep.mdm.api.dto.clientcmd;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/***
 * 更改版本信息命令
 * @date 2021-10-23
 * <AUTHOR>
 ***/
@ApiModel(value = "UpdateVersionCmdBody", description = "客户端上传MDM版本信息")
public class UpdateVersionCmdBody extends EmptyClientCmdBody {

    private static final long serialVersionUID = -807132979736569810L;
    /***
     * version
     */
    @ApiModelProperty(name = "version", value = "MDM对应的版本号", dataType = "String")
    private String version; // required
    /***
     * size
     */
    @ApiModelProperty(name = "size", value = "文件大小", dataType = "String")
    private String size; // required
    /***
     * updateUrl
     */
    @ApiModelProperty(name = "updateUrl", value = "updateUrl", dataType = "String")
    private String updateUrl; // required
    /***
     * 存储路径
     */
    @ApiModelProperty(name = "storeUrl", value = "存储路径", dataType = "String")
    private String storeUrl; // required
    /***
     * 版本功能说明
     */
    @ApiModelProperty(name = "releaseNote", value = "版本功能说明", dataType = "String")
    private String releaseNote; // required

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getSize() {
        return size;
    }

    public void setSize(String size) {
        this.size = size;
    }

    public String getUpdateUrl() {
        return updateUrl;
    }

    public void setUpdateUrl(String updateUrl) {
        this.updateUrl = updateUrl;
    }

    public String getStoreUrl() {
        return storeUrl;
    }

    public void setStoreUrl(String storeUrl) {
        this.storeUrl = storeUrl;
    }

    public String getReleaseNote() {
        return releaseNote;
    }

    public void setReleaseNote(String releaseNote) {
        this.releaseNote = releaseNote;
    }
}
