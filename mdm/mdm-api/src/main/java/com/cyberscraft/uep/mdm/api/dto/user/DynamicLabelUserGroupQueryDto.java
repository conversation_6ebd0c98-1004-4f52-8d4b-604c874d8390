package com.cyberscraft.uep.mdm.api.dto.user;

import com.cyberscraft.uep.common.dto.IQueryDto;

import java.util.Map;

/***
 * 动态标签的用户组情况
 * @date 2021-10-17
 * <AUTHOR>
 ***/
public class DynamicLabelUserGroupQueryDto implements IQueryDto {
    private Map<String, String> conditions;
    private String otherInfo;

    public Map<String, String> getConditions() {
        return conditions;
    }

    public void setConditions(Map<String, String> conditions) {
        this.conditions = conditions;
    }

    public String getOtherInfo() {
        return otherInfo;
    }

    public void setOtherInfo(String otherInfo) {
        this.otherInfo = otherInfo;
    }

}
