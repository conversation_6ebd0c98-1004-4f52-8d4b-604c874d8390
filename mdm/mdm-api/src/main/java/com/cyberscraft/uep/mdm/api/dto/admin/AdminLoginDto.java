package com.cyberscraft.uep.mdm.api.dto.admin;

import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/***
 *  管理员登录请求对像
 * @date 2021-09-11
 * <AUTHOR>
 ***/
public class AdminLoginDto implements Serializable {

    private static final long serialVersionUID = 1453457650937351515L;
    /***
     * 登录用户名
     */
    @ApiModelProperty(value = "登录用户名")
    @NotNull
    private String loginId;

    /***
     * 密码
     */
    @ApiModelProperty(value = "密码")
    @NotNull
    private String password;

    /***
     * 租户Id
     */
    @ApiModelProperty(value = "租户Id")
    @NotNull
    private String tenantId;

    /***
     *
     */
    @ApiModelProperty(value = "登录时的随机值")
    private String randomKey;

    /***
     * 短信验证码,只有在开启双因子验证的情况下有效
     */
    @ApiModelProperty(value = "登录时的短信验证码")
    private String  verifyCode;

    /***
     * 短信验证码2,只有在开启双因子验证的情况下有效
     */
    @ApiModelProperty(value = "登录时的短信验证码2")
    private String  captcha;

    /***
     * 浏览器信息
     */
    private String userAgent;

    /***
     * 客户端IP
     */
    private transient  String clientIp;

    public String getLoginId() {
        return loginId;
    }

    public void setLoginId(String loginId) {
        this.loginId = loginId;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getClientIp() {
        return clientIp;
    }

    public void setClientIp(String clientIp) {
        this.clientIp = clientIp;
    }

    public String getRandomKey() {
        return randomKey;
    }

    public void setRandomKey(String randomKey) {
        this.randomKey = randomKey;
    }

    public String getVerifyCode() {
        return verifyCode;
    }

    public void setVerifyCode(String verifyCode) {
        this.verifyCode = verifyCode;
    }

    public String getCaptcha() {
        return captcha;
    }

    public void setCaptcha(String captcha) {
        this.captcha = captcha;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }
}
