package com.cyberscraft.uep.mdm.api.dto.clientcmd;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/***
 * 更新设备对应的APNS推送Token信息
 * @date 2021-10-25
 * <AUTHOR>
 ***/
@ApiModel(value = "UpdateAppPushTokenCmdBody", description = "更新设备对应的APNS推送Token信息")
public class UpdateAppPushTokenCmdBody extends EmptyClientCmdBody {

    private static final long serialVersionUID = -2188931698700197685L;
    @ApiModelProperty(name = "token", value = "APNS 对应的token值", dataType = "String")
    private String token; // required
    @ApiModelProperty(name = "type", value = "token类型", dataType = "Integer")
    private Integer type; // required

    @ApiModelProperty(name = "pkgName", value = "token 对应的包名", dataType = "String")
    private String pkgName;


    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getPkgName() {
        return pkgName;
    }

    public void setPkgName(String pkgName) {
        this.pkgName = pkgName;
    }
}
