package mq;

import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.Producer;
import com.aliyun.openservices.ons.api.PropertyKeyConst;
import com.aliyun.openservices.ons.api.bean.ProducerBean;
import com.cyberscraft.uep.common.util.UUIDUtil;
import com.cyberscraft.uep.mq.condition.MqConstant;
import com.cyberscraft.uep.mq.constant.MQConstant;
import com.cyberscraft.uep.mq.enums.MQCodec;
import com.cyberscraft.uep.mq.rocketmq.RocketMQMessageConverter;
import com.cyberscraft.uep.mq.serialization.ISerialization;
import com.cyberscraft.uep.mq.vo.MessageEntry;
import com.cyberscraft.uep.mq.vo.SendResult;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Properties;

/***
 *
 * @date 2021/2/21
 * <AUTHOR>
 ***/
public class TestSendMessage {

    /***
     *
     */
    private final static Logger LOG = LoggerFactory.getLogger(TestSendMessage.class);


    public Properties getRocketMqProp() {
        Properties properties = new Properties();
//        if (USE_GROUP_ID.equalsIgnoreCase(useGroupId)) {
//            properties.setProperty(PropertyKeyConst.GROUP_ID, groupId);
//        } else {
        properties.setProperty(PropertyKeyConst.GROUP_ID, "GID-uem");
//        }
//        properties.setProperty(USE_GROUP_PROPERTY_KEY, useGroupId);
        properties.setProperty(PropertyKeyConst.AccessKey, "LTAI4FoRsv2RqdTUe9G8XCpQ");
        properties.setProperty(PropertyKeyConst.SecretKey, "******************************");
        //properties.setProperty(PropertyKeyConst.NAMESRV_ADDR, "http://1376162780954133.mqrest.cn-beijing.aliyuncs.com");
        //properties.setProperty(PropertyKeyConst.ONSAddr, "http://1376162780954133.mqrest.cn-beijing.aliyuncs.com");
        properties.setProperty(PropertyKeyConst.InstanceName, "MQ_INST_1376162780954133_BcDvjkXA");
        return properties;
    }

    public ProducerBean producerBean() {
        ProducerBean producerBean = new ProducerBean();
        producerBean.setProperties(getRocketMqProp());
        producerBean.start();
        return producerBean;
    }

    public ISerialization serializationBean() {
        return MQCodec.FST.getSerialization();
    }

    public RocketMQMessageConverter converterBean() {

        ISerialization serialization = serializationBean();
        return new RocketMQMessageConverter(serialization, "");
    }

    @Test
    public void run() {
        MessageEntry<String> msg = new MessageEntry();
        msg.setMsg("test");
        msg.setId(UUIDUtil.getUUID());
        SendResult res = send(MQConstant.MDM_TENANT_CREATE_MESSAGE_TOPIC, msg);
    }


    public SendResult send(String destination, MessageEntry message) {
        try {
            LOG.info("===============RocketMQ发送消息开始===============");

            RocketMQMessageConverter sendConvert = converterBean();

            Producer sendProducer = producerBean();
            final Message sendMsg;
            //不进行包装处理
            sendMsg = sendConvert.toMessage(message, destination, MqConstant.DEFAULT_TAG);
            //同步发送
            com.aliyun.openservices.ons.api.SendResult result = sendProducer.send(sendMsg);
            LOG.info("{}", result.toString());
            //异步发送
//            sendProducer.sendAsync(sendMsg, new SendCallback() {
//                @Override
//                public void onSuccess(com.aliyun.openservices.ons.api.SendResult sendResult) {
//                    LOG.info("topic is==>{},msgId is ==>{}", sendResult.getTopic(), sendResult.getMessageId());
//                }
//
//                @Override
//                public void onException(OnExceptionContext context) {
//                    LOG.warn("topic==>{}对应消息==>{}发送失败==>{}", context.getTopic(), context.getMessageId(), context.getException());
//                }
//            });
            LOG.info("===============RocketMQ发送消息完成===============");
            return SendResult.SUCESS;
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }

        return SendResult.FAILURE;
    }

}
