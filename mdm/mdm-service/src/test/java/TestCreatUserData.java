//import com.cyberscraft.uep.common.bean.NamedThreadFactory;
//import com.cyberscraft.uep.common.constant.SysConstant;
//import com.cyberscraft.uep.common.util.DesEncryptUtil;
//import com.cyberscraft.uep.common.util.MD5Util;
//import com.cyberscraft.uep.mdm.api.constant.Constant;
//import com.cyberscraft.uep.mdm.app.MdmApplication;
//import com.cyberscraft.uep.mdm.core.constant.UserTypeConstant;
//import com.cyberscraft.uep.mdm.core.entity.UserEntity;
//import com.cyberscraft.uep.mdm.core.service.user.IUserService;
//import com.cyberscraft.uep.starter.dds.TenantHolder;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit.jupiter.SpringExtension;
//
//import javax.annotation.Resource;
//import java.text.SimpleDateFormat;
//import java.util.Date;
//import java.util.concurrent.CountDownLatch;
//import java.util.concurrent.ExecutorService;
//import java.util.concurrent.Executors;
//import java.util.concurrent.atomic.AtomicLong;
//
///***
// *
// * @date 2021-10-25
// * <AUTHOR>
// ***/
//@ExtendWith(SpringExtension.class)
////@ContextConfiguration(classes = MdmApplication.class)
//@SpringBootTest(classes = MdmApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
//public class TestCreatUserData {
//
//
//    @Resource
//    private IUserService userService;
//
//    private final static Logger LOG = LoggerFactory.getLogger(TestCreatUserData.class);
//
//    /***
//     * 开始时间
//     */
//    private String startDate = "1960-01-01 00:00:00";
//
//    /***
//     * 结束时间
//     */
//    private String endDate = "2018-12-31 23:59:59";
//
//    /***
//     *
//     */
//    private long startBirthdayTime = 0L;//getTime(getDate(startDate));
//
//    /***
//     *
//     */
//    private long endBirthdayTime = 0L;//getTime(getDate(endDate));
//
//    /***
//     * 总时间跨度，用于生成随机数
//     */
//    private int birthdayTimeLen = 0;//(int)(endBirthdayTime -startBirthdayTime);
//
//
//    private final static ExecutorService executorService = Executors.newFixedThreadPool(16, new NamedThreadFactory("cyb-data-"));
//
//    /***
//     * 开始号码
//     */
//    AtomicLong startAdminLoginId = new AtomicLong(12300000000L); //16100500000L+8W//16100456459L
//
//    @Test
//    public void test() {
//
//        startBirthdayTime = getDate(startDate).getTime();
//        endBirthdayTime = getDate(endDate).getTime();
//        birthdayTimeLen = (int) (endBirthdayTime - startBirthdayTime);
//
//        TenantHolder.setTenantCode("mdm");
////        UserEntity user = userService.getUser(1L);
////        System.out.println(user.getLoginId());
//        int maxNum = 2000000;
//        long startTime = System.currentTimeMillis();
//        CountDownLatch latch = new CountDownLatch(maxNum);
//        LOG.info("================开始处理数据，总共{}条================", maxNum);
//        for (int i = 0; i < maxNum; i++) {
//            //addData();
//            executorService.execute(new CreateDataThread(latch));
//        }
//        try {
//            latch.await();
//        } catch (Exception e) {
//            LOG.error(e.getMessage(), e);
//        }
//        long totalTime = System.currentTimeMillis() - startTime;
//        double everyTime = (double) totalTime / maxNum;
//        LOG.info("================处理数据完成，总共{}条,总共花费{}ms===============", maxNum, totalTime);
//        LOG.info("================平均每条数据为{}ms===============", everyTime);
//    }
//
//    // 日期字符串转换为日期
//    public static Date getDate(String strDate) {
//        try {
//            return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(strDate);
//        } catch (Exception e) {
//            LOG.error(e.getMessage(), e);
//        }
//        return null;
//    }
//
//    /***
//     *
//     */
//    private final class CreateDataThread implements Runnable {
//
//        private final CountDownLatch latch;
//
//        public CreateDataThread(CountDownLatch latch) {
//            this.latch = latch;
//        }
//
//        @Override
//        public void run() {
//            try {
//                addData();
//            } catch (Exception e) {
//                LOG.error(e.getMessage(), e);
//            } finally {
//                latch.countDown();
//            }
//        }
//    }
//
//    /***
//     * 增加数据
//     */
//    public void addData() {
//
//
//        TenantHolder.setTenantCode("mdm");
//        UserEntity user = getUserEntity();
//        try {
//            userService.add(user);
//        } catch (Exception e) {
//            LOG.warn("增加用户失败", e);
//        }
//    }
//
//    /****
//     *
//     * @return
//     */
//    UserEntity getUserEntity() {
//        UserEntity obj = new UserEntity();
//        String mobile = String.valueOf(startAdminLoginId.incrementAndGet());
//        obj.setLoginId(mobile);
//        obj.setPassword(encryptPasswordWithMD5("1234abcd"));
////        obj.setCanLoginSelf(Constant.CAN_LOGIN_SELF_USE_PARENT_VALUE);
//        //是否已强制密码修改 0-未修改 1-已修改
////        obj.setPwdModified(SysConstant.TRUE_VALUE);
//        obj.setPlainPassword(DesEncryptUtil.encrypt(mobile));
////        obj.setAllowActivate(0);
////        obj.setAllowInconsistentActivate(1);
//        obj.setName(mobile);
//        obj.setMobile(mobile);
//        obj.setMail(mobile + "@162.com");
////        obj.setAllowActivateNum(9999);
//        obj.setGroupId(Long.valueOf(-2));
//
//        //手工添加用户
//        obj.setType(UserTypeConstant.MANUAL);
//        return obj;
//    }
//
//    public static String encryptPasswordWithMD5(String password) {
//        return MD5Util.md5(Constant.SELF_USER_PASSWORD_PREFIX + password + Constant.SELF_USER_PASSWORD_SUFFIX);
//    }
//}
