package redis;

import com.cyberscraft.uep.common.util.SnowflakeIDUtil;
import com.cyberscraft.uep.mdm.app.MdmApplication;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import javax.annotation.Resource;

/***
 *
 * @date 2021/4/17
 * <AUTHOR>
 ***/
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = MdmApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class TestRedisZset {

    @Resource
    private StringRedisTemplate stringRedisTemplate;


    @Test
    public void run(){

        boolean result= stringRedisTemplate.opsForZSet().add("test_zset_key", "1",1D);

        boolean result2= stringRedisTemplate.opsForZSet().add("test_zset_key", "1",2D);

        System.out.println(result);

        System.out.println(result2);
    }

}
