package pushclient;

import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.common.util.RestAPIUtil;
import com.cyberscraft.uep.common.util.RestTemplateFactory;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/***
 *
 * @date 2021/1/14
 * <AUTHOR>
 ***/
public class TestGetNpnsPushTokenLocal {


    private final static Logger LOG = LoggerFactory.getLogger(TestGetNpnsPushToken.class);

    @Test
    public void run() {
        String token = getTokenFromServer();
        LOG.info("当前的推送token为:{}", token);
    }

    /***
     * 调用RestAPI，获取npns token
     * @return
     */
    private String getTokenFromServer() {
        try {
            String url = "https://192.168.22.83:9443/cps/api/login";
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("username", "11ad5456078bbaf8");
            requestBody.put("password", "uemx2019");
            //if (LOG.isDebugEnabled()) {
            //}
            String body = JsonUtil.obj2Str(requestBody);
            LOG.info("当前获取token参数为:{},{}", url, body);
            String urlVariable = "";
            Map<String, Object> headerParams = null;
            Map res = RestAPIUtil.postForEntity(RestTemplateFactory.getDefaultRestTemplate(), url, requestBody, headerParams, urlVariable);
            if (res != null) {
                return (String) res.get("token");
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
        return null;
    }
}
