import com.cyberscraft.uep.common.util.SnowflakeIDUtil;
import org.junit.Test;

import java.util.HashSet;
import java.util.Set;

/***
 *
 * @date 2021-09-29
 * <AUTHOR>
 ***/
public class TestIdWork {

    @Test
    public void test() {

        Set<Long> data = new HashSet<>();

        int invalidNum = 0;
        int duplicateNum=0;
        int oddNum = 0;
        for (int i = 0; i < 2000000; i++) {
            long id = SnowflakeIDUtil.getShortId();
            if (id > 9007199254740992L) {
                invalidNum++;
                //System.out.println("无效的ID:"+id);
            }
            if (!data.contains(id)) {
                data.add(id);
                //System.out.println("存在重复的ID");
            }
            else{
                duplicateNum++;
            }
            if (id % 2 != 0) {
                oddNum++;
            }
        }
        System.out.println("无效的ID数" + invalidNum);
        System.out.println("重复的ID数" + duplicateNum);
        System.out.println(oddNum);
    }
}
