import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.mdm.api.dto.user.UserListVO;
import com.cyberscraft.uep.mdm.core.service.cmd.impl.ios.model.IOSCommand;
import com.cyberscraft.uep.mdm.core.service.cmd.impl.ios.model.IOSInstallProfileCommand;
import com.cyberscraft.uep.mdm.core.service.cmd.impl.ios.model.RequestType;
import org.junit.Assert;
import org.junit.Test;

/***
 *
 * 测试snake case对应的json字符串
 * @date 2021-09-09
 * <AUTHOR>
 ***/
public class TestJsonWithSnakeCase {

    @Test
    public void run(){

        UserListVO obj= new UserListVO();
        obj.setId("1");
        obj.setActiveDeviceNum(1);
        //obj.setApnPwd("2");

        String jsonStr= JsonUtil.obj2Str(obj);
        Assert.assertTrue(jsonStr.contains("apnPwd"));

        String snakeStr=JsonUtil.obj2Str(obj);
        Assert.assertTrue(snakeStr.contains("apn_pwd"));

        UserListVO revObj2=JsonUtil.str2ObjWithSnake(snakeStr,UserListVO.class);

        //Assert.assertTrue(revObj2.getApnPwd().equalsIgnoreCase(obj.getApnPwd()));


        UserListVO revObj1=JsonUtil.str2Obj(jsonStr,UserListVO.class);

        //Assert.assertTrue(revObj1.getApnPwd().equalsIgnoreCase(obj.getApnPwd()));
    }

    @Test
    public void testInheritance(){

        IOSCommand iosCommand = new IOSCommand(RequestType.DeviceLock);
        IOSInstallProfileCommand iosInstallProfileCommand = new IOSInstallProfileCommand("payload");

        String iosCommandStr = JsonUtil.obj2Str(iosCommand);
        String iosInstallProfileStr = JsonUtil.obj2Str(iosInstallProfileCommand);

        IOSCommand iosCommand2 = JsonUtil.str2Obj(iosInstallProfileStr, IOSCommand.class);

        System.out.println(iosCommandStr);
        System.out.println(iosInstallProfileStr);


        System.out.println(iosCommand2 instanceof IOSInstallProfileCommand);


    }


}
