package provider.govdd;

import com.cyberscraft.uep.account.client.constant.ThirdPartyAccountType;
import com.cyberscraft.uep.account.client.domain.ThirdPartyAccount;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountException;
import com.cyberscraft.uep.account.client.service.IThirdPartyAccountService;
import com.cyberscraft.uep.mdm.app.MdmApplication;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/***
 *
 * @date 2021-11-18
 * <AUTHOR>
 ***/
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = MdmApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class TestGetUsersByUserId {
    /****
     *
     */
    @Resource
    private IThirdPartyAccountService thirdPartyAccountService;

    /****
     * 根据用户ID加载用户对像列表
     * @return
     * @throws ThirdPartyAccountException
     */
    @Test
    public void getUsers() throws ThirdPartyAccountException {
        String tenantId = "mdm";
        List<String> userIds = new ArrayList<>();
        userIds.add("GE_01a632177695487d81d20b09040826b7");
        userIds.add("GE_0342be78b299498a81280288e906b4dd");

        List<ThirdPartyAccount> list = thirdPartyAccountService.getAccountsByUserIds(tenantId, ThirdPartyAccountType.GOV_DINGDING.getCode(), userIds,null);

        System.out.println(list.size());
    }
}
