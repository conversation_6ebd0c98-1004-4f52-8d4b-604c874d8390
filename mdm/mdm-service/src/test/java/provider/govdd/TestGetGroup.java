package provider.govdd;

import com.alibaba.xxpt.gateway.shared.client.http.ExecutableClient;
import com.alibaba.xxpt.gateway.shared.client.http.GetClient;

/***
 *
 * @date 2021-11-18
 * <AUTHOR>
 ***/
public class TestGetGroup {

    public static void main(String[] args) {
        ExecutableClient executableClient = ExecutableClient.getInstance();
        executableClient.setAccessKey("DingGuard_xiong-p2Do102tUfaaYb");
        executableClient.setSecretKey("meNJ0y3K9Ex4a3FdHH72p3YzW67m0jSsRY0x4882");
        executableClient.setDomainName("openplatform.ding.zj.gov.cn");
        executableClient.setProtocal("https");
        executableClient.init();

        String api = "/rpc/vds/server/getRootOrganization";
        GetClient getClient = executableClient.newGetClient(api);
        getClient.addParameter("tenantId", "102");
        String apiResult = getClient.get();

        System.out.println(">>>>>>>>>>>>>>>" + apiResult);

        executableClient.destroy();
    }
}
