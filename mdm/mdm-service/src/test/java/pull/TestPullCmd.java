package pull;

import com.cyberscraft.uep.common.enums.Platform;
import com.cyberscraft.uep.mdm.api.dto.clientcmd.PullCommandRequest;
import com.cyberscraft.uep.mdm.api.dto.clientcmd.PullCommandRequestBody;
import com.cyberscraft.uep.mdm.api.dto.clientcmd.PullCommandRequestHeader;
import com.cyberscraft.uep.mdm.app.MdmApplication;
import com.cyberscraft.uep.mdm.core.service.clientcmd.PullCommandService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;


@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = MdmApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class TestPullCmd {

    @Autowired
    private PullCommandService pullCommandService;

    @Test
    public void pullTest(){
        PullCommandRequest request = new PullCommandRequest();
        request.setPlatform(Platform.ANDROID.getValue());

        PullCommandRequestHeader header = new PullCommandRequestHeader();
        header.setVersion("V2");
        PullCommandRequestBody body = new PullCommandRequestBody();
        body.setUdid("ACC94EA4A2A489234C828BFD78436A6103D43DB8C4D7798656C7E32FCBFA5FC1");
        body.setMaxCmdNum(1577948292L);
        body.setTenantId("57");
        request.setHeader(header);
        request.setBody(body);
        pullCommandService.execute(request);
    }
}
