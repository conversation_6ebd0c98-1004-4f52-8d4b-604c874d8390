package com.cyberscraft.uep.mdm.core.service.device.impl;

import com.cyberscraft.uep.mdm.api.dto.device.DeviceFileUploadVo;
import com.cyberscraft.uep.mdm.app.MdmApplication;
import com.cyberscraft.uep.mdm.core.service.device.ISysUserFileService;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.web.multipart.MultipartFile;


import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;


@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = MdmApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
class SysUserFileServiceImplTest {

    @Autowired
    private ISysUserFileService iSysUserFileService;

    @Test
    void uploadAndSaveFile() throws Exception {
        TenantHolder.setTenantCode("bit");
        DeviceFileUploadVo deviceFileUploadVo = new DeviceFileUploadVo();
        deviceFileUploadVo.setPackageName("com.test");
        // 截屏
        for (int i = 1; i < 5; i++) {
            deviceFileUploadVo.setUploadedFile(createMultipartFile("/Users/<USER>/Desktop/digitalsee/slider/slider" + i + ".png"));
            deviceFileUploadVo.setBusinessType(1);
            deviceFileUploadVo.setOccTime(System.currentTimeMillis());
            iSysUserFileService.uploadAndSaveFile(deviceFileUploadVo, null);
            Thread.sleep(500);
        }

        // 录屏
        for (int i = 5; i < 10; i++) {
            deviceFileUploadVo.setBatchNo("202308090001");
            deviceFileUploadVo.setUploadedFile(createMultipartFile("/Users/<USER>/Desktop/digitalsee/slider/slider" + i + ".png"));
            deviceFileUploadVo.setBusinessType(2);
            deviceFileUploadVo.setOccTime(System.currentTimeMillis());
            iSysUserFileService.uploadAndSaveFile(deviceFileUploadVo, null);
            Thread.sleep(5000);
        }

        for (int i = 10; i < 15; i++) {
            deviceFileUploadVo.setBatchNo("202308090002");
            deviceFileUploadVo.setUploadedFile(createMultipartFile("/Users/<USER>/Desktop/digitalsee/slider/slider" + i + ".png"));
            deviceFileUploadVo.setBusinessType(2);
            deviceFileUploadVo.setOccTime(System.currentTimeMillis());
            iSysUserFileService.uploadAndSaveFile(deviceFileUploadVo, null);
            Thread.sleep(5000);
        }

    }

    public MultipartFile createMultipartFile(String filePath) throws Exception {
        Path path = Paths.get(filePath);
        String name = path.getFileName().toString();
        String originalFileName = path.getFileName().toString();
        String contentType = Files.probeContentType(path);
        byte[] content = Files.readAllBytes(path);
        return new MockMultipartFile(name, originalFileName, contentType, content);
    }
}