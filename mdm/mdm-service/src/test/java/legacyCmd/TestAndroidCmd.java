package legacyCmd;

import com.cyberscraft.uep.mdm.core.domain.clientcmd.LegacyClientCmdRequest;
import com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.constant.LegacyCommandCode;
import org.junit.jupiter.api.Test;

public class TestAndroidCmd extends BaseCmdRequestTest {

    private String url="https://gbd-sec-ding-guard.alibaba-inc.com/iosapp/app";
    //private String url="http://localhost:8091/mdm/app/legacy/andapp";

    String getAuthBody() {
        String res = "<?xml version=\"1.0\" encoding=\"utf-8\" ?><Request Version=\"1.0\"><OperID>" +
                "<ClientUID>eyJhbGciOiJIUzUxMiJ9.eyJyb2xlIjoiMCIsInVkaWRfaWQiOiJERjAwRTIyMkY2MjNBODYxMkU3Q0VFNjZCM0NDMURDMjg3N0UxMUNBREYwOTI1MkNDN0EyQjkxNzFCNDU4QjA2IiwidXNlcl9pZCI6MjY3Mzk0Njg0MzgxMTg0MSwibmFtZSI6IjcxNCIsInRlbmFudElkIjoibWRtIiwiZnVsbG5hbWUiOiLltJTpvpkxNiIsImV4cCI6MTY1MjI2MjU5MSwiYXVkIjoiVUVNIiwiaXNzIjoiaHR0cHM6Ly9tZG0ubmF0aW9uc2t5LmNvbSJ9.whMUtUGoBywRnjnI9L_zvorfjY6016iXzerAxVgYd5s1B-ngAwL2nh_fcVoi8TXZCeqo4F-MutORAflAr8K9kQ</ClientUID><FlowNum />" +
                "<ClientVersion>1.0</ClientVersion>" +
                "<TenantID>57</TenantID>" +
                "<ClientBuildNum>38</ClientBuildNum>" +
                "<Timezone>-1</Timezone></OperID><DeviceUpdate>" +
                "<Maker>IBM </Maker><CpuInfo>Inter I7 core 4</CpuInfo>" +
                "<RamTotal>16GB</RamTotal></DeviceUpdate></Request>";
        return res;
    }

    String getUploadViolationCmdBody() {
        String res = "{\n" +
                "  \"clientVersion\": \"1.0\",\n" +
                "  \"clientVersionNum\": 1,\n" +
                "  \"data\": {\n" +
                "    \"violationProcess\": 2,\n" +
                "    \"violationStatus\": 1,\n" +
                "    \"violationTime\": 1574674361160,\n" +
                "    \"violationType\": 1\n" +
                "  },\n" +
                "  \"flowNum\": \"\",\n" +
                "  \"functionCode\": 12017,\n" +
                "  \"loginId\": \"705\",\n" +
                "  \"platform\": 1,\n" +
                "  \"tenant_id\": \"57\",\n" +
                "  \"udid\": \"eyJhbGciOiJIUzUxMiJ9.eyJyb2xlIjoiMCIsInVkaWRfaWQiOiI1NDg1NzUzNEZBMDI3NDJDQjk4RjEwQjRFMUEwMzJERDU2QkUyQTI3MUVFMDQ1NTE2MTAyOUJEQjg5MjYxQUVBIiwidXNlcl9pZCI6MjY3MzQ1NTMzOTk5OTIzMywibmFtZSI6IjcwNSIsInRlbmFudElkIjoibWRtIiwiZnVsbG5hbWUiOiLltJTpvpk2IiwiZXhwIjoxNjUyNDM0MzAxLCJhdWQiOiJVRU0iLCJpc3MiOiJodHRwczovL21kbS5uYXRpb25za3kuY29tIn0.rsxKyD3xcOXwh5efd-U0Bzu__mpNLrZIHJ8raOaQR-oGhRyslWlt0tarVfM4_V7cX7F08wzFU_yr11CJOqB6sw\"\n" +
                "}";
        return res;
    }

    @Test
    public void testQueryDeviceStatus() {
        LegacyClientCmdRequest request = new LegacyClientCmdRequest();
        request.setEncryptCode(0);
        request.setCommandBody(getAuthBody());
        request.setCompressCode(0);
        request.setProtocalCode(0);
        request.setCommandCode(LegacyCommandCode.QUERY_DEVICE_STATUS.getCmdCode());

        String res = post(url, request);

        System.out.println(res);
    }

    @Test
    public void testUploadDeviceViolation() {
        String requestBody = "{\n" +
                "  \"clientVersion\": \"1.0\",\n" +
                "  \"clientVersionNum\": 1,\n" +
                "  \"data\": {\n" +
                "    \"violationProcess\": 3,\n" +
                "    \"violationStatus\": 1,\n" +
                "    \"violationTime\": 1575083508590,\n" +
                "    \"violationType\": 0\n" +
                "  },\n" +
                "  \"flowNum\": \"348a5e03-783d-40f0-961c-7cab12d04f74\",\n" +
                "  \"functionCode\": 12017,\n" +
                "  \"loginId\": \"706\",\n" +
                "  \"platform\": 2,\n" +
                "  \"tenant_id\": \"57\",\n" +
                "  \"udid\": \"595c9dd1961048979c30b038f7fde680\"\n" +
                "}";

        LegacyClientCmdRequest request = new LegacyClientCmdRequest();
        request.setEncryptCode(0);
        request.setCommandBody(requestBody);
        request.setCompressCode(0);
        request.setProtocalCode(0);
        request.setCommandCode(LegacyCommandCode.JSON_PROTOCOL.getCmdCode());

        String res = post(url, request);

        System.out.println(res);
    }
}
