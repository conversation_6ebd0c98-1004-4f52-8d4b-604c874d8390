package legacyCmd;

import com.cyberscraft.uep.mdm.core.domain.clientcmd.LegacyClientCmdRequest;
import com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.constant.LegacyCommandCode;
import org.junit.jupiter.api.Test;

/***
 *
 * @date 2021-11-22
 * <AUTHOR>
 ***/
public class TestAndroidAuthCmd extends  BaseCmdRequestTest{



    private String url = "http://test-guard.digitalsee.cn/mdm/app/legacy/andapp";
//    private String url="https://gbd-sec-ding-guard.alibaba-inc.com/cgi-bin/dynamic_parser.cgi";

    String getAuthIdTokenBody() {
        String res = "<?xml version='1.0' encoding='UTF-8' ?><Request><OperID><TenantID>57</TenantID>" +
                "<ClientUID>CDCAE615AE095849F9DC9C86CD4685018EB607E622BDF5F6082FAAFC34E86404</ClientUID><FlowNum></FlowNum><Timezone>-1</Timezone>" +
                "<Result>-1</Result><ClientVersion>1.0</ClientVersion>" +
                "<ClientBuildNum>1</ClientBuildNum></OperID><ClientIDInfo>" +
                "<AuthType>3</AuthType>" +
                "<IdToken>f496c4f9e8ed424a9bd45486ca7c2e000002ca01</IdToken>" +
                "<EMMClientId></EMMClientId></ClientIDInfo><MobileIDInfo><ClientOSType>1</ClientOSType><ClientOS>Android</ClientOS><ModelType>1</ModelType><Model>HMA-AL00</Model><OSVersion>9</OSVersion><Jailed>0</Jailed><RamTotal>5.53GB</RamTotal><RamAvailable>3.05GB</RamAvailable><RomAvailable>94.18GB</RomAvailable><RomTotal>109GB</RomTotal><CameraInfo></CameraInfo><Imei></Imei><WifiMac>34:12:F9:6C:6C:A4</WifiMac><Imsi></Imsi><SN>HJS0218B22011127</SN><ClientIP>*************</ClientIP><SdID></SdID><Battery><BatteryLevel>100</BatteryLevel><BatteryState>1</BatteryState></Battery><SpecialTypeFlag>0</SpecialTypeFlag>" +
                "<SSAID>6e1c59b18cc292b1</SSAID></MobileIDInfo></Request>";
        return res;
    }

    String getAuthPasswordBody() {
        String res="<Request><OperID><TenantID>57</TenantID>" +
                "<ClientUID>CDCAE615AE095849F9DC9C86CD4685018EB607E622BDF5F6082FAAFC34E86501</ClientUID>" +
                "<UEM_Version>100191230</UEM_Version><ProtocolVersion>V2</ProtocolVersion>" +
                "<FlowNum></FlowNum><Timezone>-1</Timezone><Result>-1</Result>" +
                "<ClientVersion>1.0</ClientVersion><ClientBuildNum>1</ClientBuildNum></OperID>" +
                "<ClientIDInfo><UserName>12300000001</UserName><UserPassword>1234abcd</UserPassword>" +
                "<EMMClientId></EMMClientId></ClientIDInfo><MobileIDInfo>" +
                "<ClientOSType>1</ClientOSType><ClientOS>Android</ClientOS><ModelType>1</ModelType><Model>MI 9 SE</Model><OSVersion>9</OSVersion><Jailed>0</Jailed><RamTotal>5.49GB</RamTotal><RamAvailable>2.76GB</RamAvailable><RomAvailable>36.24GB</RomAvailable><RomTotal>50.76GB</RomTotal><CameraInfo></CameraInfo><Imei></Imei><WifiMac>60:AB:67:F7:3A:62</WifiMac><Imsi></Imsi><SN>44e7fe28</SN><ClientIP>*************</ClientIP><SdID></SdID><Battery><BatteryLevel>100</BatteryLevel><BatteryState>1</BatteryState></Battery><SpecialTypeFlag>0</SpecialTypeFlag><SSAID>98982104ee7c6fe5</SSAID></MobileIDInfo></Request>";
//        String res = "<?xml version='1.0' encoding='UTF-8' ?>" +
//                "<Request>" +
//                "<OperID><TenantID>57</TenantID>" +
//                "<ClientUID>CDCAE615AE095849F9DC9C86CD4685018EB607E622BDF5F6082FAAFC34E86501</ClientUID>" +
//                "<FlowNum></FlowNum><Timezone>-1</Timezone><Result>-1</Result><ClientVersion>*******</ClientVersion>" +
//                "</OperID>" +
//                "<ClientIDInfo>" +
//                "<AuthType>0</AuthType>" +
//                "<UserName>12300000001</UserName>" +
//                "<UserPassword>1234abcd</UserPassword>" +
//                "<EMMClientId>1111111111111111</EMMClientId>" +
//                "</ClientIDInfo><MobileIDInfo><ClientOSType>1</ClientOSType>" +
//                "<ClientOS>Android</ClientOS><ModelType>1</ModelType>" +
//                "<Model>SM-G9008V</Model><OSVersion>5.0</OSVersion><Jailed>0</Jailed><Imei>15747385290391</Imei>" +
//                "<WifiMac>e2:t0:g1:l2:o1:k8</WifiMac><Imsi>13248</Imsi><SN>MedfieldF2114413248</SN>" +
//                "<ClientIP>*************</ClientIP>" +
//                "</MobileIDInfo>" +
//                "</Request>";
        return res;
    }

//    @Test
    public void testWithIdToken() {
        LegacyClientCmdRequest request = new LegacyClientCmdRequest();
        request.setEncryptCode(0);
        request.setCommandBody(getAuthIdTokenBody());
        request.setCompressCode(0);
        request.setProtocalCode(0);
        request.setCommandCode(LegacyCommandCode.AUTH.getCmdCode());

        String res= post(url,request);

        System.out.println(res);
    }

    @Test
    public void testWithPassword() {
        LegacyClientCmdRequest request = new LegacyClientCmdRequest();
        request.setEncryptCode(0);
        request.setCommandBody(getAuthPasswordBody());
        request.setCompressCode(0);
        request.setProtocalCode(0);
        request.setCommandCode(LegacyCommandCode.AUTH.getCmdCode());

        String res= post(url,request);

        System.out.println(res);
    }
}
