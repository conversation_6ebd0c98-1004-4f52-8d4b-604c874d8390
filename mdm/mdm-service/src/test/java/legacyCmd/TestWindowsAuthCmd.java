package legacyCmd;

import com.cyberscraft.uep.mdm.core.domain.clientcmd.LegacyClientCmdRequest;
import com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.constant.LegacyCommandCode;
import org.junit.jupiter.api.Test;

/***
 *
 * @date 2021-11-21
 * <AUTHOR>
 ***/
public class TestWindowsAuthCmd extends BaseCmdRequestTest {

    //private String url = "http://127.0.0.1:8091/mdm/app/legacy/windowsapp";
    //private String url = "http://10.145.143.48:8091/mdm/app/legacy/windowsapp";

    private String url="https://gbd-sec-ding-guard.alibaba-inc.com/windowsapp";

    @Test
    public void test() {
        LegacyClientCmdRequest request = new LegacyClientCmdRequest();
        request.setEncryptCode(0);
        request.setCommandBody(getAuthBody());
        request.setCompressCode(0);
        request.setProtocalCode(0);
        request.setCommandCode(LegacyCommandCode.AUTH.getCmdCode());

        String res= post(url,request);

        System.out.println(res);
    }

    String getAuthBody() {
        String res = "<?xml version=\"1.0\" encoding=\"utf-8\" ?><Request Version=\"1.0\"><OperID>" +
                "<ClientUID>DF00E222F623A8612E7CEE66B3CC1DC2877E11CADF09252CC7A2B9171B458B06</ClientUID><FlowNum>0</FlowNum>" +
                "<ClientVersion>1.0</ClientVersion>" +
                "<TenantID>57</TenantID>" +
                "<ClientBuildNum>38</ClientBuildNum>" +
                "<Idfa>DF00E222F623A8612E7CEE66B3CC1DC2877E11CADF09252CC7A2B9171B458B06</Idfa></OperID>" +
                "<ClientIDInfo><URLScheme />" +
                "<IdToken>240e22830c0146478b8d6aa8184206000002ca01</IdToken><AuthType>3</AuthType>" +
                "<EMMClientId>90177420-3a89-4dd4-a084-49f786122523</EMMClientId>" +
                "<PkgName>com.alibaba.taurus.ep</PkgName></ClientIDInfo>" +
                "<MobileIDInfo><ClientOSType>5</ClientOSType><ClientOS>windows</ClientOS><Jailed>0</Jailed><OSVersion>12.1.2</OSVersion>" +
                "<UDID>DF00E222F623A8612E7CEE66B3CC1DC2877E11CADF09252CC7A2B9171B458B05</UDID>" +
                "<SN /><Name>lgq ThinkPad</Name><Model>books</Model><ModelType>50</ModelType><WifiMac>02:00:00:00:00:00</WifiMac></MobileIDInfo></Request>";
        return res;
    }
}
