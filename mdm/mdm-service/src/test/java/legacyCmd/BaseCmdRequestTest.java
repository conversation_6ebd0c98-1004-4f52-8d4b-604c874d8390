package legacyCmd;

import com.cyberscraft.uep.mdm.core.domain.clientcmd.LegacyClientCmdRequest;
import com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.constant.LegacyCommandCode;
import com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.decoder.DefaultLegacyDecoder;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;

/***
 *
 * @date 2021-11-21
 * <AUTHOR>
 ***/
public class BaseCmdRequestTest {

    /***
     * 进行数据提交测试
     * @param url
     * @param request
     * @return
     */
    public String post(String url, LegacyClientCmdRequest request) {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);
        RequestConfig requestConfig = RequestConfig.custom().
                setConnectTimeout(180 * 1000).setConnectionRequestTimeout(180 * 1000)
                .setSocketTimeout(180 * 1000).setRedirectsEnabled(true).build();
        httpPost.setConfig(requestConfig);
        httpPost.setHeader("Content-Type", "application/json");
        try {
            ByteArrayOutputStream os = new DefaultLegacyDecoder().encode(request);

            httpPost.setEntity(new ByteArrayEntity(os.toByteArray()));
            //System.out.println("request parameters" + EntityUtils.toString(httpPost.getEntity()));
            System.out.println("httpPost:" + httpPost);
            HttpResponse response = httpClient.execute(httpPost);
            if (response != null && response.getStatusLine().getStatusCode() == 200) {
                String result = EntityUtils.toString(response.getEntity());
                //System.out.println("result:" + result);
                return result;
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (null != httpClient) {
                try {
                    httpClient.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return null;
    }


    /***
     * 进行数据提交测试
     * @param url
     * @param cmdBody
     * @return
     */
    public String post(String url, String cmdBody) {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);
        RequestConfig requestConfig = RequestConfig.custom().
                setConnectTimeout(180 * 1000).setConnectionRequestTimeout(180 * 1000)
                .setSocketTimeout(180 * 1000).setRedirectsEnabled(true).build();
        httpPost.setConfig(requestConfig);
        httpPost.setHeader("Content-Type", "application/json");
        try {
            //ByteArrayOutputStream os = new DefaultLegacyDecoder().encode(request);

            httpPost.setEntity(new ByteArrayEntity(cmdBody.getBytes()));
            //System.out.println("request parameters" + EntityUtils.toString(httpPost.getEntity()));
            //System.out.println("httpPost:" + httpPost);
            HttpResponse response = httpClient.execute(httpPost);
            if (response != null && response.getStatusLine().getStatusCode() == 200) {
                String result = EntityUtils.toString(response.getEntity());
                //System.out.println("result:" + result);
                return result;
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (null != httpClient) {
                try {
                    httpClient.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return null;
    }
}
