package legacyCmd;

import com.cyberscraft.uep.common.enums.Platform;
import com.cyberscraft.uep.mdm.api.constant.ClientCmdType;
import com.cyberscraft.uep.mdm.api.dto.device.DeviceActivatedDto;
import com.cyberscraft.uep.mdm.api.dto.oauth2.AccessTokenRequestDto;
import com.cyberscraft.uep.mdm.app.MdmApplication;
import com.cyberscraft.uep.mdm.core.domain.clientcmd.LegacyClientCmdRequest;
import com.cyberscraft.uep.mdm.core.domain.oauth2.AccessTokenResult;
import com.cyberscraft.uep.mdm.core.service.clientcmd.ILegacyClientCommandService;
import com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.constant.LegacyCommandCode;
import com.cyberscraft.uep.mdm.core.service.oauth2.IAuthorizationService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * @version 1.0
 * @ClassName TestAndroidQueryUserProfile
 * @PackageName legacyCmd
 * @Description TODO
 * <AUTHOR>
 * @date
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = MdmApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class TestAndroidQueryUserProfile extends BaseCmdRequestTest{

    @Resource
    private ILegacyClientCommandService legacyClientCommandService;

    @Resource
    private IAuthorizationService authorizationService;

    @Test
    public void queryProfile() {
        AccessTokenRequestDto dto = new AccessTokenRequestDto();
        dto.setUsername("12300000001");
        dto.setPassword("1234abcd");
        dto.setUdid("0a422c2cd6234e0f928c0a68bed2508c");
        dto.setPlatform(Platform.ANDROID.getValue());
        dto.setTenantId(String.valueOf(57));
        dto.setGrantType("password");

        DeviceActivatedDto deviceActivatedDto = new DeviceActivatedDto();
        deviceActivatedDto.setUdid("0a422c2cd6234e0f928c0a68bed2508c");
        deviceActivatedDto.setDeviceType(1);
        dto.setDevice(deviceActivatedDto);

        AccessTokenResult tokenResult = authorizationService.authorize(dto);
        String accessToken = tokenResult.getAccessToken().getAccessToken();

        LegacyClientCmdRequest cmdRequest = new LegacyClientCmdRequest();
        cmdRequest.setCommandCode(LegacyCommandCode.QUERY_USER_PROFILE.getCmdCode());
        String requestPayload = getAuthBody(accessToken);
        System.out.println(requestPayload);
        cmdRequest.setCommandBody(requestPayload);
        String rtn = legacyClientCommandService.executor(cmdRequest, Platform.ANDROID);
        System.out.println(rtn);
    }

    String getAuthBody(String accessToken) {
        return
                "<?xml version='1.0' encoding='UTF-8' ?><Request><OperID><TenantID>57</TenantID><ClientUID>" + accessToken + "</ClientUID><UEM_Version>100191230</UEM_Version><ProtocolVersion>V2</ProtocolVersion><FlowNum></FlowNum><Timezone>-1</Timezone><Result>-1</Result><ClientVersion>1.0</ClientVersion><ClientBuildNum>1</ClientBuildNum></OperID></Request>";
    }
}
