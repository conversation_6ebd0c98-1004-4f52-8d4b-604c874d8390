package legacyCmd;

import com.cyberscraft.uep.mdm.core.domain.clientcmd.LegacyClientCmdRequest;
import com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.constant.LegacyCommandCode;
import org.junit.jupiter.api.Test;

/***
 *
 * @date 2021-11-25
 * <AUTHOR>
 ***/
public class TestIOSUpdateBatteryCmd extends BaseCmdRequestTest {

    private String url = "http://127.0.0.1:8091/mdm/app/legacy/iosapp/app";
    //private String url = "http://10.145.143.48:8091/mdm/app/legacy/iosapp/app";

    //private String url="https://gbd-sec-ding-guard.alibaba-inc.com/iosapp/app";

    @Test
    public void test() {
        LegacyClientCmdRequest request = new LegacyClientCmdRequest();
        request.setEncryptCode(0);
        request.setCommandBody(getAuthBody());
        request.setCompressCode(0);
        request.setProtocalCode(0);
        request.setCommandCode(LegacyCommandCode.UPDATE_BATTERY.getCmdCode());

        String res = post(url, request);

        System.out.println(res);
    }

    String getAuthBody() {
        String res = "<?xml version=\"1.0\" encoding=\"utf-8\" ?><Request Version=\"1.0\"><OperID>" +
                "<ClientUID>108e2db0bed8451c9f77d2eae0c39b75</ClientUID><FlowNum />" +
                "<ClientVersion>1.0</ClientVersion>" +
                "<UEMVersion>100191230</UEMVersion>" +
                "<TenantID>57</TenantID>" +
                "<ClientBuildNum>38</ClientBuildNum>" +
                "<Timezone>-1</Timezone></OperID><DeviceUpdate>" +
                "<Battery>" +
                "<BatteryLevel>1</BatteryLevel>" +
                "<BatteryStatus>1</BatteryStatus>" +
                "<CpuRatio>20%</CpuRatio>" +
                "<MemoryRatio>80%</MemoryRatio>" +
                "<StorageRatio>70%</StorageRatio>" +
                "</Battery></DeviceUpdate></Request>";
        return res;
    }
}
