package legacyCmd;

import com.cyberscraft.uep.mdm.core.domain.clientcmd.LegacyClientCmdRequest;
import com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.constant.LegacyCommandCode;
import org.junit.jupiter.api.Test;

/***
 *
 * @date 2021-11-25
 * <AUTHOR>
 ***/
public class TestWindowUpdateJailBrokenCmd extends BaseCmdRequestTest {

    private String url = "http://127.0.0.1:8091/mdm/app/legacy/windowsapp";
    //private String url = "http://10.145.143.48:8091/mdm/app/legacy/windowsapp";

    //private String url="https://gbd-sec-ding-guard.alibaba-inc.com/windowsapp";

    @Test
    public void test() {
        LegacyClientCmdRequest request = new LegacyClientCmdRequest();
        request.setEncryptCode(0);
        request.setCommandBody(getAuthBody());
        request.setCompressCode(0);
        request.setProtocalCode(0);
        request.setCommandCode(LegacyCommandCode.UPLOAD_JAILBROKEN_STATUS.getCmdCode());

        String res = post(url, request);

        System.out.println(res);
    }

    String getAuthBody() {
        String res = "<?xml version=\"1.0\" encoding=\"utf-8\" ?><Request Version=\"1.0\"><OperID>" +
                "<ClientUID>eyJhbGciOiJIUzUxMiJ9.eyJyb2xlIjoiMCIsInVkaWRfaWQiOiJERjAwRTIyMkY2MjNBODYxMkU3Q0VFNjZCM0NDMURDMjg3N0UxMUNBREYwOTI1MkNDN0EyQjkxNzFCNDU4QjA2IiwidXNlcl9pZCI6MjY3Mzk0Njg0MzgxMTg0MSwibmFtZSI6IjcxNCIsInRlbmFudElkIjoibWRtIiwiZnVsbG5hbWUiOiLltJTpvpkxNiIsImV4cCI6MTY1MjI2MjU5MSwiYXVkIjoiVUVNIiwiaXNzIjoiaHR0cHM6Ly9tZG0ubmF0aW9uc2t5LmNvbSJ9.whMUtUGoBywRnjnI9L_zvorfjY6016iXzerAxVgYd5s1B-ngAwL2nh_fcVoi8TXZCeqo4F-MutORAflAr8K9kQ</ClientUID><FlowNum />" +
                "<ClientVersion>1.0</ClientVersion>" +
                "<UEMVersion>100191230</UEMVersion>" +
                "<TenantID>57</TenantID>" +
                "<ClientBuildNum>38</ClientBuildNum>" +
                "<Timezone>-1</Timezone></OperID><DeviceUpdate>" +
                "<Jailed>1</Jailed>" +
                "<BootTime>1200</BootTime>" +
                "</DeviceUpdate></Request>";
        return res;
    }
}
