package legacyCmd;

import com.cyberscraft.uep.mdm.core.domain.clientcmd.LegacyClientCmdRequest;
import com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.constant.LegacyCommandCode;
import org.junit.jupiter.api.Test;

/***
 *
 * @date 2021-11-26
 * <AUTHOR>
 ***/
public class TestAndroidGetCircleOperCmd  extends BaseCmdRequestTest {

    private String url = "http://127.0.0.1:8091/mdm/app/legacy/andapp";
    //private String url = "http://10.145.143.48:8091/mdm/app/legacy/andapp";

    //private String url="https://gbd-sec-ding-guard.alibaba-inc.com/andapp";

    @Test
    public void test() {
        LegacyClientCmdRequest request = new LegacyClientCmdRequest();
        request.setEncryptCode(0);
        request.setCommandBody(getAuthBody());
        request.setCompressCode(0);
        request.setProtocalCode(0);
        request.setCommandCode(LegacyCommandCode.GET_CIRCLEOPER.getCmdCode());

        String res = post(url, request);

        System.out.println(res);
    }

    String getAuthBody() {
        String res = "<?xml version=\"1.0\" encoding=\"utf-8\" ?><Request Version=\"1.0\"><OperID>" +
                "<ClientUID>feb9812c3d9f4ec4b60b3298148443fe</ClientUID><FlowNum />" +
                "<ClientVersion>1.0</ClientVersion>" +
                "<UEMVersion>100191230</UEMVersion>" +
                "<TenantID>57</TenantID>" +
                "<ClientBuildNum>38</ClientBuildNum>" +
                "<Timezone>-1</Timezone></OperID>" +
                "</Request>";
        return res;
    }
}
