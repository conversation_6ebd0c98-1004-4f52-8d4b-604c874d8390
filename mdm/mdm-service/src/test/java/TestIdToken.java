import com.cyberscraft.uep.common.util.Base64Util;
import com.cyberscraft.uep.mdm.core.domain.Token;
import com.cyberscraft.uep.mdm.core.exception.MdmException;
import com.dingtalk.api.request.OapiV2UserCreateRequest;
import com.taobao.api.internal.util.json.JSONWriter;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jws;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.jose4j.base64url.SimplePEMEncoder;
import org.jose4j.jwt.JwtClaims;
import org.jose4j.jwt.consumer.JwtConsumer;
import org.jose4j.jwt.consumer.JwtConsumerBuilder;
import org.junit.Test;
import org.springframework.util.AntPathMatcher;

import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.security.*;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.security.spec.X509EncodedKeySpec;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

/***
 *
 * @date 2021-10-08
 * <AUTHOR>
 ***/
public class TestIdToken {

    private static final String HTTP = "http";
    private static final String HTTPS = "https";
    private static SSLConnectionSocketFactory sslsf = null;
    private static PoolingHttpClientConnectionManager cm = null;
    private static SSLContextBuilder builder = null;

    static {
        try {
            builder = new SSLContextBuilder();
            // 全部信任 不做身份鉴定
            builder.loadTrustMaterial(null, new TrustStrategy() {
                @Override
                public boolean isTrusted(X509Certificate[] x509Certificates, String s) throws CertificateException {
                    return true;
                }
            });
            sslsf = new SSLConnectionSocketFactory(builder.build(), new String[]{"SSLv2Hello", "SSLv3", "TLSv1", "TLSv1.2"}, null, NoopHostnameVerifier.INSTANCE);
            Registry<ConnectionSocketFactory> registry = RegistryBuilder.<ConnectionSocketFactory>create()
                    .register(HTTP, new PlainConnectionSocketFactory())
                    .register(HTTPS, sslsf)
                    .build();
            cm = new PoolingHttpClientConnectionManager(registry);
            cm.setMaxTotal(200);//max connection
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static CloseableHttpClient getHttpClient() throws Exception {
        CloseableHttpClient httpClient = HttpClients.custom()
                .setSSLSocketFactory(sslsf)
                .setConnectionManager(cm)
                .setConnectionManagerShared(true)
                .build();
        return httpClient;
    }

    String key = "www.digitalsee.cn,mdm.digitalsee.cn,uemx.digitalsee.cn,ThiSisAsecUrity,dontEllanYonEelse.";


    /***
     *
     */
    private String aud = "UEM";

    /***
     * 平台发布的id token，jwt token中，管理员token对应的iss
     */
    private String adminIss = "https://mdm.amdin.digitalsee.cn";


    /***
     * 签名算法
     */
    private SignatureAlgorithm algorithm = SignatureAlgorithm.HS512;

    /***
     * 签名的key
     */
    Key signingKey = new SecretKeySpec(key.getBytes(), algorithm.getJcaName());

    private JwtConsumer jwtConsumer = new JwtConsumerBuilder()
            .setSkipAllDefaultValidators()
            .setSkipDefaultAudienceValidation()
            .setSkipSignatureVerification()
            .build();

    @Test
    public void run() throws Exception {
        String idToken = createIdToken();
//        "eyJhbGciOiJIUzUxMiJ9.eyJyb2xlIjoiMTAiLCJ1c2VyX2lkIjoiYWRtaW4iLCJuYW1lIjoiYWRtaW4iLCJ0ZW5hbnRJZCI6Inhpb25nLm5hdGlvbnNreSIsImZ1bGxuYW1lIjoiQWRtaW5pc3RyYXRvciIsImV4cCI6MTU3MDUzNDgwMSwiYXVkIjoiVUVNIiwiaXNzIjoiaHR0cHM6Ly9tZG0uYW1kaW4ubmF0aW9uc2t5LmNvbSJ9.ih4giZvoxWnfKqy13WU_Ow7c5NG9afdGZrQ_zNCerlj3M1jx-myyI69YrG7prQFqr9jxnGb4wlteTSCRtDeZLQ";
        System.out.println("idToken:" + idToken);
        JwtClaims claims = jwtConsumer.process(idToken).getJwtClaims();
        String issuer = claims.getIssuer();

        //Map<String, Object> map=extractClaims(idToken);
        //System.out.println(map.size());

    }

    @Test
    public void TestDingdingDept() {
        List<OapiV2UserCreateRequest.DeptTitle> deptTitles = new ArrayList<>();
        OapiV2UserCreateRequest.DeptTitle dept1 = new OapiV2UserCreateRequest.DeptTitle();
        dept1.setDeptId(123L);
        dept1.setTitle("研发");

        OapiV2UserCreateRequest.DeptTitle dept2 = new OapiV2UserCreateRequest.DeptTitle();
        dept2.setDeptId(456L);
        dept2.setTitle("人事");

        deptTitles.add(dept1);
        deptTitles.add(dept2);
        String write = (new JSONWriter(false, false, true)).write(deptTitles);

        System.out.println(write);
    }

    String createIdToken() {
        Token token = new Token();
        token.setLoginId("admin");
        token.setType(30);
        token.setName("admin");
        token.setTenantId("xiong.digitalsee");


        return Jwts.builder() //
                .setClaims(userToClaims(token)) //
                .setExpiration(afterSeconds(12000)) //
                .setAudience(aud)
                .setIssuer(adminIss)
                .signWith(signingKey) //
                .compact();
    }

    ;

    @Test
    public void testUuid() {
        String s = UUID.randomUUID().toString();
        System.out.println(s.replaceAll("-", ""));

        AntPathMatcher matcher = new AntPathMatcher();
        matcher.setTrimTokens(false);
        matcher.setCaseSensitive(true);

        System.out.println(matcher.match("/iam/login/{registrationId}", "/iam/login/iam"));

    }

    @Test
    public void testTenant() {
        String domain = "digitalsee.lego.cn";
        String[] split = domain.split("\\.");
        for (String item : split) {
            System.out.println("item:" + item);
        }

        String serverName = "**************";
        System.out.println(serverName.matches("\\d+(\\.\\d+){3}"));

        String url = "[host]/adm/login/oauth2/code/iam";
        System.out.println(url.replace("[host]", "https://localhost"));
    }

    @Test
    public void testIdTokenByHS256() {
//        ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
//        ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
//        String s2 = "{\"kty\":\"RSA\",\"kid\":\"tc\",\"use\":\"sig\",\"alg\":\"RS256\",\"n\":\"mGk-VyJ8dyvSEa9PlzX2VyOlm252EAYf0I7kLQrmXjDNDjTKqDm4vJP_SbhQjCQSqaSkA19pjVthpg2gFYipwAvwmQB46dkcMa-JuozX7GSFMaj6LPRMMABJP3UrdS4CGe4ZCf9iu-FConf9Gh-AwpV17oXJv81cuAqcB35GA4t3pkU3SxxP-x-68X0lUsdKZnh9W4AGtDRvlLYX4Quz61u_p-dKMWe7h1Q_2y_mnq6s8QqcWF7U-FkIIxgO20S9RSb_RDYgsNlna7CNaKh77jr_ve9pe8rB4K5-sOceyJPJWoVMEF1PtCnjOGz-i56WEWNqw6IYSM9czuYFruq2tw\",\"e\":\"AQAB\",\"d\":\"VAs8-DmpHuIILf5ZSk3UcIrUqJhJ_F4ULyKMipYHIIuSl7HeytNBnqOR0-oSaKT_sZJ7auyzPOLWPwYmb5lz2dX7r7NF-dXiKTfeIAPIka-AjzjZ8-4nERUnTxnig_SB7iCQqCWvIWF_KC44YK5ch3B5nrUh4mVxKn-XOL6WBx-IWAQDLVYTydIZShm7TSL3xmzCT12hnHlNO_lkSllR1cw0g-LJ34xM77y3_9r0-ugJqjGVt3rOc4WlQp-4OS9lQOSx3EpyWbO-t9N5c107iso2ThSzbSjWMdvuOttGqmZTR6OTNjNN0Jpag7j8IJatq5fEm6xRd-kGbIRmWQrksQ\",\"p\":\"x497ygMErPAAy9A0mNhgmJYRSwDwdXatg680fNuxs3ftm3JJGOqoEqrsUO8w-y9fu1dZ02pdlNG8LdwzKHyd_KwQVdzufCb1y08JDVKS8bov68S36OSi-k6sZ-7UVTxz00ZTRO52FVUGHGPZd9X7v1hdjnEtUKh39ZRxgG-AnTk\",\"q\":\"w4QQwkxlv6X8np1VhCmolp3ikb_3Go7riMAQbStohayhne3WB0RsiQwqycNy2oiuqZ0RWCCg-cdHXgwQS8uxw7SqfMTN96_Oy3SrLMLaKhkHDcQcHObU53atmargqK5W57hphBlAFFqP_MP9Wf_11IvN3LHEyGPHY1XkTZ2l428\",\"dp\":\"GcEwzq95CQCoeaVTRG6YeNjVPoLOhbpdRmN0OLD5zBmsNqWXPXn7Wv1oJkHYZnKEvl7Vnu1ZSn2O9bQ3fgAnO5bRPOOYe7lje2ul1WxUvpGy4ZCv04S-a-N_aKJwENgOcrPex0SnRLWGZl-Uj1y0cstqt5lvqbs0UCNqSpMZXDE\",\"dq\":\"nAtBnPGAm88MqkOERzFAfsWQWSdPPwEmd0RjV943U8kOTI_QLIit30u4ssOtXUGNtY3Ik_-muvQTv7n9H6F5RWtiTqZ8hNzqPXPmUplJqiPDf3cvwNlyvzH3KBg7qiVK8zI59NZ6UERfeyupfzVMrNDQAv5WwmDweBXLWy8hgT0\",\"qi\":\"CAAWArmq8WRPsgdMP9N0ajx8q_g5MpK9hmFIvfbTwY443q8fj2fV5ECnnj_6kn_TToEPCZd9Pdk0_aqm5l3gOf2pPah5CW-vlZ8XTsGeNb426zo1fGATK5t-UOwuT6qsVi2ca8Vfqc6BCZPr_RB0AsqFuvpFg4gcsBLp4dAgH0k\"}";
//        Map<String, Object> stringObjectMap = JsonUtil.str2Map(s2);
//        try {
//            RsaJsonWebKey rsaJsonWebKey = new RsaJsonWebKey(stringObjectMap);
//            RSAPrivateKey rsaPrivateKey = rsaJsonWebKey.getRsaPrivateKey();
////            RSAPublicKey rsaPublicKey = rsaJsonWebKey.getRsaPublicKey();
//            String s = Base64.getEncoder().encodeToString(rsaPrivateKey.getEncoded());
//            System.out.println(s);
//
//            byte[] decode = SimplePEMEncoder.decode(s);
//            X509EncodedKeySpec spec = new X509EncodedKeySpec(decode);
//            try {
//                KeyFactory keyFactory = KeyFactory.getInstance("RSA");
//                PrivateKey privateKey = keyFactory.generatePrivate(spec);
//                String s1 = Base64.getEncoder().encodeToString(privateKey.getEncoded());
//                System.out.println(s1);
//            } catch (NoSuchAlgorithmException | InvalidKeySpecException e) {
//                e.printStackTrace();
//            }
//
//        } catch (JoseException e) {
//            e.printStackTrace();
//        }

//        String test1 = "test1";
//        String s = Base64.getEncoder().encodeToString(test1.getBytes());
//        System.out.println(s);
//
//        String s1 = new String(Base64.getDecoder().decode("uBRDzZYnCJYE36Zcj6XhG0MSdO3MWWmKjIhu_Si8xEq_mkAfXH9mrHfrbZcXOXZ3_5EWKgsgrCan8jRKXHkqmSJNG0pfmC3c4FnVdO54GQJO2iKsZknyUoE0wWyjlLm30VueyGhop5sbHWlRsu7WQg6wuueYM4CCKDUzj_rOmLLqrCe-JmZjnXKQqPjaCKohkbGTz1tfUBb9kpgi45zR1nQOQ5Zbj5lxaH4-6bReqqNr3M2KpLBfMJvzAQZp7n5gZM7xUOfOHqG2Jq62knNC5JUO3ienaenn4oApX_AnYS_ZMV1oH4jWEwetdXGFUpW6ttQTfnqFz_0ANvR35pYSrQ"));
//        System.out.println(s1);

//        String idToken = createIdToken("testApp", "Hpip0xqNh+U1Dy6hgArYzM497P++hNTiLMSbm4NVCStl9wehBcZSODSF1Gh/YjUGhyRRu7IQZ+AN5DDXILaeYqVcXzoxrHZ5U/FFXQ85nUN6SXYtnPk3uKzg/iHY3gEkruvhsdVXXuDbI0QQa6HUSSvxTD7N/d0RDBphAMMVxP0=");
        String idToken = null;
        idToken = createIdToken("DF85C6E48D09DFC6245FFBDAF3410115", "bjl419KRcm8SbJQMyCAaLZTBYMDg5YtbkERdmThndp3Waalj+6hKrfv044WxwKJtUYwhnxKPS9/NW4O7U+NDAIGywglH+puRJMSs3RE+y5OGfZdx+6kQC0kfQqvOhPb+Q8vmB+PSCAi9Iwsdef4MuVGOre5mZDNxgGLEUicw6eA=");
        System.out.println(idToken);
    }

    String createIdToken(String clientId, String secret) {
        byte[] decodeSecret = Base64.getDecoder().decode(secret);
        Key signingKey = new SecretKeySpec(decodeSecret, SignatureAlgorithm.HS256.getJcaName());

        Map<String, Object> claims = new HashMap<>();
//        claims.put("user_id","12345678");//第三方用户user_id，如果获取不到，填写"-1"
        claims.put("username", "NONE");//用户登录帐号，如果获取不到，填写"NONE"

        return Jwts.builder()
                .setClaims(claims)
                .setExpiration(new Date(System.currentTimeMillis() + 3600000)) //有效期1个小时
                .setAudience("IAM")
                .setIssuer(clientId)
                .signWith(signingKey)
                .compact();
    }

    /***
     * 转换成token中的claims信息
     * @param token
     * @return
     */
    private Map<String, Object> userToClaims(Token token) {
        Map<String, Object> claims = new HashMap<>();
        claims.put("name", token.getLoginId());
        claims.put("role", String.valueOf(token.getType()));
        claims.put("fullname", token.getName());
        claims.put("tenantId", token.getTenantId());
        claims.put("user_id", token.getLoginId()); // Using by wework in app server
        claims.put("corp_id", token.getCorpId()); // Using by wework in app server
        claims.put("idp", token.getIdp()); // Using by wework in app server
        claims.put("device_id", token.getDeviceId()); // Using by wework in app server
        return claims;
    }

    /***
     *
     * @param seconds
     * @return
     */
    private Date afterSeconds(long seconds) {
        return Date.from(LocalDateTime.now().plusSeconds(seconds).atZone(ZoneId.systemDefault()).toInstant());
    }

    public Map<String, Object> extractClaims(String idToken) throws MdmException {
        Jws<Claims> result = Jwts.parser().setSigningKey(signingKey).parseClaimsJws(idToken);
        // Jwts.parser().setSigningKey(Base64.getEncoder().encodeToString(key.getBytes())).parseClaimsJws(idToken);
        Claims claims = result.getBody();
        Map<String, Object> ret = new HashMap<>();
        for (Map.Entry<String, Object> entry : claims.entrySet()) {
            ret.put(entry.getKey(), entry.getValue());
        }
        return ret;
    }

    /**
     * 根据code获取accessToken和idToken，用于授权码模式
     *
     * @param tokenUrl     iam token接口地址
     * @param clientId     应用key
     * @param clientSecret 应用secret
     * @param redirectUri  重定向uri，用于校验
     * @param code         第三方应用调用iam的authorize接口，用户登录后，通过302跳转返回的授权码
     * @return 包含access_token、id_token的json，例如：
     * {
     * "access_token":"d6ebaca3-0cbb-4bfe-9ffe-f645e42769dd",
     * "token_type":"bearer",
     * "refresh_token":"e460e1da-a5b8-4bf9-9ad2-1677f9efa0ff",
     * "expires_in":71999,
     * "scope":"address email openid phone profile",
     * "id_token":"eyJraWQiOiJhZG0iLCJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOjEzOTY3MDM2MTYxMDg2NTg2ODksInJvbGVzIjpbXSwiYW1yIjpbInB3ZCJdLCJpc3MiOiJodHRwczpcL1wvbGVnby5qaW5hbmNxLmNuOjQ0NiIsIm5vbmNlIjoiNmYtS25CaEJYSDBscmRSSWh2LWNOZWtkS25NZnJaRlREamxPbjlERWF1WSIsInRpZCI6ImRpZ2l0YWxzZWUiLCJjbGllbnRfaWQiOiJhZG0iLCJhdWQiOiJhZG0iLCJncmFudF90eXBlIjoiYXV0aG9yaXphdGlvbl9jb2RlIiwiYXpwIjoiYWRtIiwic2NvcGUiOlsiYWRkcmVzcyIsImVtYWlsIiwib3BlbmlkIiwicGhvbmUiLCJwcm9maWxlIl0sImV4cCI6MTYzOTU0Mjg4OCwiaWF0IjoxNjM5NTQxMDg4LCJ1c2VybmFtZSI6ImFkbWluIn0.f8zGLf7igs9oZzNxTMKRgwbd8rsDC3IIt3zZZXiaevw"
     * }
     */
    public String getToken(String tokenUrl, String clientId, String clientSecret, String redirectUri, String code) throws Exception {
//        CloseableHttpClient httpClient = getHttpClient();
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(tokenUrl);
        List<NameValuePair> pairs = new ArrayList<>();
        pairs.add(new BasicNameValuePair("grant_type", "authorization_code"));
        pairs.add(new BasicNameValuePair("code", code));
        pairs.add(new BasicNameValuePair("client_id", clientId));
        pairs.add(new BasicNameValuePair("client_secret", clientSecret));
        pairs.add(new BasicNameValuePair("redirect_uri", redirectUri));

        try {
            httpPost.setEntity(new UrlEncodedFormEntity(pairs, "UTF-8"));
            HttpResponse response = httpClient.execute(httpPost);
            if (response != null) {
                return EntityUtils.toString(response.getEntity());
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (null != httpClient) {
                try {
                    httpClient.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return null;
    }

    /**
     * 验证并解析jwtIdToken
     *
     * @param jwtIdToken
     * @param signSecret 对称签名密钥，base64编码
     * @return
     */
    public Map<String, Object> verifyIdTokenByHS256(String jwtIdToken, String signSecret) throws Exception {
        SignatureAlgorithm algorithm = SignatureAlgorithm.valueOf("HS256");//.HS256;
        Key signingKey = new SecretKeySpec(Base64Util.decode(signSecret), algorithm.getJcaName());
        JwtConsumer consumer = new JwtConsumerBuilder()
                .setRequireExpirationTime()
                .setSkipDefaultAudienceValidation()
                .setVerificationKey(signingKey)
                .setRelaxVerificationKeyValidation()
                .build();
        JwtClaims jwtClaims = consumer.processToClaims(jwtIdToken);
        return jwtClaims.getClaimsMap();
    }

    /**
     * 验证并解析jwtIdToken
     *
     * @param jwtIdToken
     * @param publicKey  RSA公钥
     * @return
     */
    public Map<String, Object> verifyIdTokenByRS256(String jwtIdToken, String publicKey) throws Exception {
        PublicKey iamPublicKey = null;
        byte[] decode = SimplePEMEncoder.decode(publicKey);
        X509EncodedKeySpec spec = new X509EncodedKeySpec(decode);

        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        iamPublicKey = keyFactory.generatePublic(spec);

        JwtConsumer consumer = new JwtConsumerBuilder()
                .setRequireExpirationTime()
                .setSkipDefaultAudienceValidation()
                .setVerificationKey(iamPublicKey)
                .setRelaxVerificationKeyValidation()
                .build();
        JwtClaims jwtClaims = consumer.processToClaims(jwtIdToken);
        return jwtClaims.getClaimsMap();
    }

    @Test
    public void testCode() {
        String tokenUrl = "http://*************/iam/token";
        String clientId = "E5216AED1DFDC3E6FDE84E9D129753DA";
        String cleintSecret = "ad23665a89115edaf1c126009b6d47a989a1fc331fb81079577bf47339d7ba4d";
        String redirectUri = "https://www.baidu.com";
        String code = "MviYNz3xvAWcgwI0";
        String token = null;
        try {
            token = getToken(tokenUrl, clientId, cleintSecret, redirectUri, code);
        } catch (Exception e) {
            e.printStackTrace();
        }
        System.out.println(token);
    }

    @Test
    public void testHs256VerifyToken() {
//        String jwtToken = "eyJraWQiOiJhZG0iLCJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.I14s7r8jBhB5_5tOoeOv4a4-FcmrQAx-dQPUNGjzeaA";
//        String publicKey = "UTFaH0BM2ZGmc/dAzTqDpFyYxmETILUXY16IHHq++Itw+lcMf5APRDGnVW5vBHqsBOCkVeCEfcnUnUkT1HFdUBxOGq6N1d7lYhu5JyVxff8a433n8cZv0YhS32qta0fjIyK6RNTueVkhwRX8ecAnBgiH8R+voI0pezrfCB88C1k=";
        String jwtToken = "eyJzdWIiOiIxMzk2NzAzNjE2MTA4NjU4Njg5Iiwicm9sZXMiOltdLCJhbXIiOlsicHdkIl0sImlzcyI6Imh0dHBzOlwvXC9sZWdvLmppbmFuY3EuY246NDQ2IiwidGlkIjoiZGlnaXRhbHNlZSIsImNsaWVudF9pZCI6InNzb3dlYmFwcCIsImF1ZCI6InNzb3dlYmFwcCIsImdyYW50X3R5cGUiOiJhdXRob3JpemF0aW9uX2NvZGUiLCJhenAiOiJzc293ZWJhcHAiLCJzY29wZSI6WyJvcGVuaWQiXSwicGhvbmVfbnVtYmVyIjoiMTMyMTIyMjMzMjIiLCJleHAiOjE2NDA2NzcxMDIsImlhdCI6MTY0MDY3NTMwMiwidXNlcm5hbWUiOiJhZG1pbiJ9";
        String publicKey = "kY4ySErRSioL3UVyzsxjNvRuVOCVhnvA5ilri8SWR0YAluyQNYKnB71G6a4S4OB8XJS/rGXrzxI1EOde3QircH2jEsjrKZgerYecYkvrGXVZZq/FYMa6/hxGdzLr90HhkjcwIHu01T97+/ajEB6kdwESLuESKDJEEVhSHpg8ASM=";
        try {
            Map<String, Object> claim = verifyIdTokenByHS256(jwtToken, publicKey);
            System.out.println(claim);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void digestSign() throws Exception {
        MessageDigest md = MessageDigest.getInstance("SHA-1",new BouncyCastleProvider());
        md.update("HelloWorld".getBytes("UTF-8"));
        byte[] result = md.digest();
        System.out.println(Base64.getEncoder().encodeToString(result));
    }

    @Test
    public void testRs256VerifyToken() {
        String jwtToken = "eyJraWQiOiJGNUZCQUI4MUMwRUQ1MUVDN0FGOTFBNzFCRjc3RkY3IiwidHlwIjoiSldUIiwiYWxnIjoiUlMyNTYifQ.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.YAfxTJ1o7CXa9xfTH7C9MCT4tKJ87-6hSZDdlAENUBDxQP2D2G7OGQGFZZYqXoieyD2d-EGSvDv0c1Qm6ISDWamujIPaKXBRLEK0OSusyVx0ccFv3urZo8indgFduo_eSuUMlIa0Nts4xq6AseMpjoYUz6VAvzwsvJuM846bbVsqM8dJk5eM_349_oIp7OOPCdhjTTMQnbPmUGKIHxtvMEBC3hB7ci27QuakIVdtZOvfTeYZyVfhIJx8g_KHQckQcnlwioCojAvD4UcNC-ISyli-9hWEpp2FfybKKJjSaZcmrKUcpcws5ZLJry2GXfvBbTSoF3QTBh3Np6aT8wO57g";
//        String publicKey = "MIIDFDCCAfygAwIBAgIGAXqtTnhxMA0GCSqGSIb3DQEBCwUAMBgxFjAUBgNVBAMTDTE5Mi4xNjguNTAuMTAwHhcNMjEwNzE1MDMxMzU4WhcNMzEwNzE0MDMxMzU4WjAYMRYwFAYDVQQDEw0xOTIuMTY4LjUwLjEwMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAjo6rEzbxrTrodIkn3Qdz9hbTS6cKJh0NyVGiEowpuHK3xzhrul9COplFiv2pfYz3JmBsTmySJ1hQiLfOmmCaOMRqSfoBGFxmYCY5fDH5AEDoxsTO70AClCSdZcnfTA2eIQU88sB3XxdzVWPmuFpA247JxR3BJyAnD0nhPnI2LBeIxjLTAIz3+SmmI/FOV2s9yxCSMUgEMS0uyt/GX12fIHqaicqgadLllPyHQJfRi0iUvrUrihO7c6viWWc+BL7tz1L+2vqKjTivWSlbU9WTGMRXcrESrBBUlAmYl34Z3NytDZ6Kal1BKKUbGXVVRa3tF6xZPDY410n2c0XWNiptCQIDAQABo2QwYjAdBgNVHQ4EFgQUWURV8oSgfjo+vBoECsxq3cvS05cwDwYDVR0TAQH/BAUwAwEB/zALBgNVHQ8EBAMCAbYwIwYDVR0lBBwwGgYIKwYBBQUHAwEGCCsGAQUFBwMCBgRVHSUAMA0GCSqGSIb3DQEBCwUAA4IBAQBfWIl4L98gGJmNaxz2F01n0PhpUpQOLpN9f9HoGL8oS2JgxfA8nhcyH+dymJL3m8P7XXfmoVPAk0OrXhSfVW5hEXMikRFGQRfHxxk4H7QpwSSW5eTZyvk0lIiI3ygYg6TD4NPcBI61GCFvYurJ3UPzxuXAQZEd6POlX2me0Y9LggN7iHewbqiomXMF14lMhstVeK3/HegppvwBG521XsDgyoUFE+Bgn7QFKH5j5Auwxav+tH8QdJyem/eGMpVsOQDM9IcTHFdaDAozFkPvOrJsbTzhqxgBSGXQmptEkpKKBTW7z/XYbL/asGpQT35QauVxlXF7efAkcAEfHIP/koQ2";
        String publicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAjo6rEzbxrTrodIkn3Qdz9hbTS6cKJh0NyVGiEowpuHK3xzhrul9COplFiv2pfYz3JmBsTmySJ1hQiLfOmmCaOMRqSfoBGFxmYCY5fDH5AEDoxsTO70AClCSdZcnfTA2eIQU88sB3XxdzVWPmuFpA247JxR3BJyAnD0nhPnI2LBeIxjLTAIz3+SmmI/FOV2s9yxCSMUgEMS0uyt/GX12fIHqaicqgadLllPyHQJfRi0iUvrUrihO7c6viWWc+BL7tz1L+2vqKjTivWSlbU9WTGMRXcrESrBBUlAmYl34Z3NytDZ6Kal1BKKUbGXVVRa3tF6xZPDY410n2c0XWNiptCQIDAQAB";
        try {
            Map<String, Object> claim = verifyIdTokenByRS256(jwtToken, publicKey);
            System.out.println(claim);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
