package clientcmd;

import com.cyberscraft.uep.account.client.constant.ThirdPartyAccountType;
import com.cyberscraft.uep.mdm.core.constant.GrantType;
import legacyCmd.BaseCmdRequestTest;
import org.junit.jupiter.api.Test;

import java.util.Random;

/***
 *
 * @date 2021/11/5
 * <AUTHOR>
 ***/
public class TestDingDingUserAuthCmd extends BaseCmdRequestTest {

    private String url = "http://127.0.0.1:8091/mdm/app/token";

    /****
     *
     */
    @Test
    public void thirdPartyUserAuth() {

        String userId="015562492128916060";
        String cmdData = getAuthThirdPartyUserBody(userId, ThirdPartyAccountType.DINGDING.getCode());
        String res = post(url, cmdData);
        System.out.println(res);
    }

    String getAuthThirdPartyUserBody(String userId, String accountType) {

        Random rnd = new Random();
        String cmdData = "{\n" +
                "  \"device\": { \n" +
                "    \"clientBuildNum\": \"1.0\",\n" +
                "    \"clientVersion\": \"v1.0\",\n" +
                "    \"deviceName\": \"" + String.valueOf(userId) + "\",\n" +
                "    \"deviceType\": 10,\n" +
                "    \"deviceUuid\": \"" + String.valueOf(userId) + "d1\"\n" +
                "  }, \n" +
                "  \"grantType\": \"" + GrantType.THIRDPARTY_USERID.getCode() + "\", \n" +
                "  \"accountType\": \"" + accountType + "\",  \n" +
                "  \"tenantId\": \"1\",\n" +
                "  \"uemVersion\": \"2020-530\",\n" +
                "  \"username\": \"" + String.valueOf(userId) + "\"\n" +
                "}";
        return cmdData;
    }
}
