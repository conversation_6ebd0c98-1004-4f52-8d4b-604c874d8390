package com.cyberscraft.uep.mdm.app.controller.app.ios;

import com.cyberscraft.uep.common.enums.Platform;
import com.cyberscraft.uep.mdm.api.constant.UrlConstant;
import com.cyberscraft.uep.mdm.core.domain.clientcmd.LegacyClientCmdRequest;
import com.cyberscraft.uep.mdm.core.service.clientcmd.ILegacyClientCommandService;
import com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.constant.LegacyCommandCode;
import com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.decoder.DefaultLegacyDecoder;
import com.cyberscraft.uep.mdm.core.util.WebContextUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.OutputStream;

@RestController
@RequestMapping(value = UrlConstant.APP_LEGACY_URL_PREFIX + "/iosapp")
public class IOSLegacyController {
    private static final Logger logger = LoggerFactory.getLogger(IOSController.class);

    @Resource
    DefaultLegacyDecoder defaultDecoder;

    @Resource
    private ILegacyClientCommandService legacyClientCommandService;
    /***
     * 用于接收及处理原来的xml逻辑
     * 主要步骤如下:第一步，进行编码处理，
     * 第二步根据functionCode，查找对应的处理类，用于生成ClientCmdBody对像
     * 第三步，根据ClientCmdBody及新的指令处理类型，调用clientCommandService.execute(request);得到处理结果
     * 第四步根据第二步中的处理类，输出结果
     */
    @RequestMapping(value = "/auth", method = RequestMethod.POST, produces = MediaType.TEXT_XML_VALUE, consumes = {MediaType.ALL_VALUE})
    public void auth() throws IOException {
        HttpServletRequest request = WebContextUtil.getRequest();
        logger.info("执行xml 上行指令接口");
        LegacyClientCmdRequest cmdRequest = legacyClientCommandService.decode(request);
        if(cmdRequest.getCommandCode().equals(LegacyCommandCode.AUTH.getCmdCode())) {
            String res = legacyClientCommandService.executor(cmdRequest, Platform.IOS);
//            logger.info("执行xml 上行指令接口返回结果: {}", res);
            OutputStream stream = WebContextUtil.getResponse().getOutputStream();
            stream.write(defaultDecoder.assembleResponse(cmdRequest, res).toByteArray());
        }
    }

    @RequestMapping(value = "/app", method = RequestMethod.POST, produces = MediaType.TEXT_XML_VALUE, consumes = {MediaType.ALL_VALUE})
    public void app() throws IOException {
        HttpServletRequest request = WebContextUtil.getRequest();
        logger.info("执行xml 上行指令接口");
        LegacyClientCmdRequest cmdRequest = legacyClientCommandService.decode(request);
        String res = legacyClientCommandService.executor(cmdRequest, Platform.IOS);
//        logger.info("执行xml 上行指令接口返回结果: {}", res);
        OutputStream stream = WebContextUtil.getResponse().getOutputStream();
        stream.write(defaultDecoder.assembleResponse(cmdRequest, res).toByteArray());
    }
}
