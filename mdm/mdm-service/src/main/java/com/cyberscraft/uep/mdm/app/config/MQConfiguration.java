package com.cyberscraft.uep.mdm.app.config;

import com.cyberscraft.emm.vo.CmdMessage;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.iam.dto.domain.PushDataDto;
import com.cyberscraft.uep.iam.dto.enums.DataChangeType;
import com.cyberscraft.uep.iam.dto.enums.PushBusinessType;
import com.cyberscraft.uep.mdm.api.constant.TenantFromType;
import com.cyberscraft.uep.mdm.api.dto.tenant.TenantCreateRequestDto;
import com.cyberscraft.uep.mdm.api.event.Event;
import com.cyberscraft.uep.mdm.api.event.enums.EventBusinessType;
import com.cyberscraft.uep.mdm.api.event.enums.EventChangeType;
import com.cyberscraft.uep.mdm.core.service.basic.ITenantService;
import com.cyberscraft.uep.mdm.core.service.device.IPushTokenInvalidConsumer;
import com.cyberscraft.uep.mdm.core.service.event.EventBuilder;
import com.cyberscraft.uep.mdm.core.service.event.mq.sub.IEventConsumer;
import com.cyberscraft.uep.mq.constant.MQConstant;
import com.cyberscraft.uep.mq.constant.MessageConsumerModelConstant;
import com.cyberscraft.uep.mq.service.DefaultMessageConsumerServiceImpl;
import com.cyberscraft.uep.mq.service.IMessageConsumerFactory;
import com.cyberscraft.uep.mq.service.IMessageConsumerService;
import com.cyberscraft.uep.mq.service.IMessageListener;
import com.cyberscraft.uep.mq.vo.MessageConsumerEntry;
import com.cyberscraft.uep.mq.vo.MessageSubscription;
import com.cyberscraft.uep.push.client.domain.PushTokenInvalidMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/***
 * MQ相关的配置
 * @date 2021-08-28
 * <AUTHOR>
 ***/
@Configuration
//@Order(Ordered.HIGHEST_PRECEDENCE+10003)
public class MQConfiguration {

    /***
     *
     */
    private final static Logger LOG = LoggerFactory.getLogger(MQConfiguration.class);

    @Resource
    private ITenantService tenantService;

    @Resource
    private IEventConsumer eventConsumer;

    @Resource
    private IPushTokenInvalidConsumer pushTokenInvalidConsumer;


    /*********************************************************************************************
     * *******************************                              ******************************
     * ******************************* 以下为消息接收相关配置           *******************************
     * *******************************                              ******************************
     **********************************************************************************************/
    /***
     *
     */
    @Resource
    IMessageConsumerFactory messageConsumerFactory;

    /***
     * 初始化消息消费服务
     * @return
     */
    @Bean(initMethod = "start", destroyMethod = "stop")
    public IMessageConsumerService defaultMessageConsumerService() {
        List<MessageConsumerEntry> enties = new ArrayList<>();
        // MDM创建租户
//        enties.add(getMdmTenantMessageConsumerEntry(MQConstant.MDM_TENANT_CREATE_MESSAGE_TOPIC));
        // 用户删除，删除对应的策略
        enties.add(getEventMessageConsumerEntryUser(MQConstant.QUEUE_PUSH_DATA));
        // 钉钉创建租户
//        enties.add(getGovDDTenantMessageConsumerEntry(MQConstant.DING_TENANT_CREATE_MESSAGE_TOPIC));
        enties.add(getEventMessageConsumerEntry(MQConstant.MDM_CMD_TOPIC));
//        enties.add(getPushTokenInvalidMessageConsumerEntry(MQConstant.PUSH_TOKEN_INVALID_TOPIC));
        DefaultMessageConsumerServiceImpl service = new DefaultMessageConsumerServiceImpl(messageConsumerFactory, enties);
        //service.setMesageListener(cmdMessageLisenter());
        LOG.info("初始化消息消费服务完成");
        return service;
    }


    private MessageConsumerEntry getEventMessageConsumerEntry(String topic) {
        MessageConsumerEntry entry = new MessageConsumerEntry();
        entry.setConcurrentConsumerNum(10);
        entry.setMaxConcurrentConsumerNum(10);
        entry.setMessageModel(MessageConsumerModelConstant.CLUSTERING);
        List<IMessageListener> listeners = new ArrayList<>();
        listeners.add(eventMessageListener());
        entry.setMessageListeners(listeners);

        MessageSubscription subscription = new MessageSubscription();
        subscription.setTopic(topic);
        subscription.setExpression("*");
        entry.setSubscription(subscription);
        return entry;
    }

    private MessageConsumerEntry getEventMessageConsumerEntryUser(String topic) {
        MessageConsumerEntry entry = new MessageConsumerEntry();
        entry.setConcurrentConsumerNum(10);
        entry.setMaxConcurrentConsumerNum(10);
        entry.setMessageModel(MessageConsumerModelConstant.BROADCASTING);
        List<IMessageListener> listeners = new ArrayList<>();
        listeners.add(eventMessageListenerUser());
        entry.setMessageListeners(listeners);

        MessageSubscription subscription = new MessageSubscription();
        subscription.setTopic(topic);
        subscription.setExpression("*");
        entry.setSubscription(subscription);
        return entry;
    }


    /***
     * 定义消息处理业务逻辑
     * @return
     */
    private IMessageListener eventMessageListener() {
        return (entry) -> {
            if (entry == null) {
                LOG.warn("当前接收到的消息为空，跳过");
                return;
            }
            if (entry.getMsg() instanceof CmdMessage) {
                LOG.debug("忽略以前的CmdMessage");
                return;
            }
            if (entry.getMsg() instanceof Event) {
                Event event = (Event) entry.getMsg();
                LOG.info("receive :{}", event);
                try {
                    eventConsumer.consume(event);
                } catch (Exception e) {
                    LOG.error("error while handling event ", e);
                }
            } else {
                LOG.debug("暂不处理其他类型消息");
            }
        };
    }

    /***
     * 定义消息处理业务逻辑
     * @return
     */
    private IMessageListener eventMessageListenerUser() {
        return (entry) -> {
            if (entry == null) {
                LOG.warn("当前接收到的消息为空，跳过");
                return;
            }
            if (entry.getMsg() instanceof Collectors) {
                List<PushDataDto> pushDataDtos = (List<PushDataDto>) entry.getMsg();
                for (PushDataDto pushDataDto : pushDataDtos) {
                    if (PushBusinessType.USER.getValue() == pushDataDto.getBusinessType() && DataChangeType.DELETE.getValue() == pushDataDto.getChangeType()) {
                        Event event = EventBuilder.create()
                                .businessType(EventBusinessType.USER)
                                .changeType(EventChangeType.DELETE)
                                .businessDataId(pushDataDto.getBusinessDataId())
                                .target(pushDataDto)
                                .build();
                        event.setTenantId(pushDataDto.getTcode());
                        try {
                            eventConsumer.consume(event);
                        } catch (Exception e) {
                            LOG.error("error while handling event ", e);
                        }
                    }
                }
            }
            if (entry.getMsg() instanceof PushDataDto) {
                // 删除用户循环删除  发送多次消息
                PushDataDto message = (PushDataDto) entry.getMsg();
                LOG.info("data push consumer receive message:{}", message);
                // 判断发送的事件类型 用户并且是删除的
                if (PushBusinessType.USER.getValue() == message.getBusinessType() && DataChangeType.DELETE.getValue() == message.getChangeType()) {
                    Event event = EventBuilder.create()
                            .businessType(EventBusinessType.USER)
                            .changeType(EventChangeType.DELETE)
                            .businessDataId(message.getBusinessDataId())
                            .target(message)
                            .build();
                    event.setTenantId(message.getTcode());
                    try {
                        eventConsumer.consume(event);
                    } catch (Exception e) {
                        LOG.error("error while handling event ", e);
                    }
                }
            }
        };
    }

    /***
     * 钉钉创建租户消息监听
     * @return
     */
    private IMessageListener GovDDTenantCreateMessageLisenter() {
        return (entry) -> {
            if (entry == null || entry.getMsg() == null) {
                LOG.warn("钉钉租户创建：当前接收到的消息为空，跳过");
                return;
            }

            String message = null;
            if (entry.getMsg() instanceof byte[]) {
                try {
                    message = new String((byte[]) entry.getMsg(), "utf-8");
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                }
            }

            LOG.info("钉钉租户创建：租户创建消费者收到消息：{}", message);

            Map bodyMap = JsonUtil.str2Map(message);
            LOG.info("钉钉租户创建：转成map后的数据：{}", bodyMap.toString());

            TenantCreateRequestDto dto = new TenantCreateRequestDto();
            dto.setTenantId(bodyMap.get("tenantId").toString());
            dto.setTenantName(bodyMap.get("tenantName").toString());
            dto.setTenantFromType(TenantFromType.GOVDD);
            dto.setMessageId(bodyMap.get("messageId").toString());
            LOG.info("钉钉租户创建：receive messageId:{}, tenantId:{}", entry.getId(), dto.getTenantId());

            tenantService.createTenantEvent(dto);
        };
    }

    /***
     * MDM创建租户消费配置
     * @return
     */
    private MessageConsumerEntry getMdmTenantMessageConsumerEntry(String topic) {
        MessageConsumerEntry entry = new MessageConsumerEntry();
        entry.setConcurrentConsumerNum(10);
        entry.setMaxConcurrentConsumerNum(10);
        entry.setMessageModel(MessageConsumerModelConstant.CLUSTERING);
        List<IMessageListener> listeners = new ArrayList<>();
        listeners.add(mdmTenantCreateMessageLisenter());
        entry.setMessageListeners(listeners);

        MessageSubscription subscription = new MessageSubscription();
        subscription.setTopic(topic);
        subscription.setExpression("*");
        entry.setSubscription(subscription);
        return entry;
    }

    /***
     * MDM创建租户消息监听
     * @return
     */
    private IMessageListener mdmTenantCreateMessageLisenter() {
        return (entry) -> {
            if (entry == null) {
                LOG.warn("MDM租户创建：当前接收到的消息为空，跳过");
                return;
            }
            TenantCreateRequestDto dto = (TenantCreateRequestDto) entry.getMsg();
            dto.setTenantFromType(TenantFromType.MDM_ADMIN);
            dto.setMessageId(entry.getId());
            LOG.info("MDM租户创建：receive messageId:{}, tenantId:{}", entry.getId(), dto.getTenantId());
            tenantService.createTenantEvent(dto);
        };
    }


    /***
     * MDM创建租户消费配置
     * @return
     */
    private MessageConsumerEntry getPushTokenInvalidMessageConsumerEntry(String topic) {
        MessageConsumerEntry entry = new MessageConsumerEntry();
        entry.setConcurrentConsumerNum(2);
        entry.setMaxConcurrentConsumerNum(2);
        entry.setMessageModel(MessageConsumerModelConstant.CLUSTERING);
        List<IMessageListener> listeners = new ArrayList<>();
        listeners.add(pushTokenInvalidListener());
        entry.setMessageListeners(listeners);

        MessageSubscription subscription = new MessageSubscription();
        subscription.setTopic(topic);
        subscription.setExpression("*");
        entry.setSubscription(subscription);
        return entry;
    }

    /***
     * MDM创建租户消息监听
     * @return
     */
    private IMessageListener pushTokenInvalidListener() {
        return (entry) -> {
            if (entry == null) {
                LOG.warn("当前接收到的推送Token无效事件为空，跳过处理");
                return;
            }
            PushTokenInvalidMessage dto = (PushTokenInvalidMessage) entry.getMsg();
            LOG.info("当前接收到的推送Token无效事件,用户:{}，设备:{},租户:{}", dto.getLoginId(), dto.getUdid(), dto.getTenantId());
            pushTokenInvalidConsumer.consumer(dto);
            //tenantService.createTenantEvent(dto);
        };
    }
}
