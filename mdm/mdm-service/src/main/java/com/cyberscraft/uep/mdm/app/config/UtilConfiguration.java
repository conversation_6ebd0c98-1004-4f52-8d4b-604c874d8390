package com.cyberscraft.uep.mdm.app.config;

import com.cyberscraft.uep.starter.dds.DDSUtil;
import com.cyberscraft.uep.starter.dds.properties.DDSConfigProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/***
 * 相关工具类的初始化
 * @date 2021-08-31
 * <AUTHOR>
 ***/
@Configuration
public class UtilConfiguration {
//
//    @Bean
//    @ConditionalOnMissingBean
//    public DDSConfigProperties getDDSConfigProperties(){
//        return new DDSConfigProperties();
//    }

    @Autowired
    DDSConfigProperties ddsConfigProperties;

    /***
     *
     * @return
     */
    @Bean("DDSUtil")
    public DDSUtil getDDSUtil()
    {
        DDSUtil obj= new DDSUtil();
        obj.setDdsConfigProperties(ddsConfigProperties);
        return obj;
    }


///***
// *
// * @param  applicationContext
// * @return
// */
//    @Bean("PropertyUtil")
//    public PropertyUtil getMultiTenantUtil(ApplicationContext applicationContext)
//    {
//        PropertyUtil.init(applicationContext);
//        //PropertyUtil propertyUtil = new PropertyUtil();
//        //return propertyUtil;
//        return new PropertyUtil();
//    }

//    /***
//     *
//     * @param inMTenantManageApi
//     * @param inLicenceService
//     * @param inProperties
//     * @param inEnvironment
//     * @return
//     */
//    @Bean("MultiTenantUtil")
//    public MultiTenantUtil getMultiTenantUtil(MtenantManageApi inMTenantManageApi,
//                                              MtenantLicenceApi inLicenceService,
//                                              DDSConfigProperties inProperties,
//                                              Environment inEnvironment)
//    {
//        //MultiTenantUtil multiTenantUtil = new MultiTenantUtil();
//        MultiTenantUtil.init(inMTenantManageApi,inLicenceService,inProperties,inEnvironment);
//        //return multiTenantUtil;
//        return new MultiTenantUtil();
//    }
}
