package com.cyberscraft.uep.mdm.app.controller.app;

import com.cyberscraft.uep.common.bean.Result;
import com.cyberscraft.uep.common.exception.ResponseResult;
import com.cyberscraft.uep.common.constant.SysConstant;
import com.cyberscraft.uep.common.enums.Platform;
import com.cyberscraft.uep.common.util.ValidatorUtil;
import com.cyberscraft.uep.mdm.api.app.BasicApi;
import com.cyberscraft.uep.mdm.api.constant.ClientCmdType;
import com.cyberscraft.uep.mdm.api.dto.basic.ServerInfoVO;
import com.cyberscraft.uep.mdm.api.dto.clientcmd.*;
import com.cyberscraft.uep.mdm.api.dto.device.DeviceActivatedDto;
import com.cyberscraft.uep.mdm.api.dto.device.DeviceInfoVO;
import com.cyberscraft.uep.mdm.api.errors.MdmErrorType;
import com.cyberscraft.uep.mdm.app.controller.BaseController;
import com.cyberscraft.uep.common.constant.DeviceTypeConstant;
import com.cyberscraft.uep.mdm.core.constant.DualStatus;
import com.cyberscraft.uep.mdm.core.domain.oauth2.AccessToken;
import com.cyberscraft.uep.mdm.core.exception.MdmException;
import com.cyberscraft.uep.mdm.core.service.clientcmd.IClientCommandService;
import com.cyberscraft.uep.mdm.core.service.clientcmd.IClientRestRequestService;
import com.cyberscraft.uep.mdm.core.service.clientcmd.PullCommandService;
import com.cyberscraft.uep.mdm.core.service.device.IDeviceBindService;
import com.cyberscraft.uep.mdm.core.util.Oauth2ThreadLocalUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/***
 *
 * @date 2021-10-23
 * <AUTHOR>
 ***/
@RestController
public class BasicController extends BaseController implements BasicApi {

    protected final static Logger DEBUG_LOG = LoggerFactory.getLogger(BasicController.class);

    @Resource
    private IClientCommandService clientCommandService;

    @Resource
    private IDeviceBindService deviceBindService;

    @Resource
    private IClientRestRequestService clientRestRequestService;

    /***
     *
     * @return
     */
    @Override
    public Result<ServerInfoVO> getServerInfo() {
        ServerInfoVO obj = new ServerInfoVO();
        EmptyClientCmdBody cmdBody = new EmptyClientCmdBody();
        AccessToken token = Oauth2ThreadLocalUtil.getToken();
        cmdBody.setUdid(token.getUdid());
        cmdBody.setUserId(token.getUserId());
        //如果未设置，则指定为未越狱
        ClientCmdRequestDto request = new ClientCmdRequestDto();
        request.setType(ClientCmdType.QUERY_SERVER_INFO.getCode());
        request.setCmdBody(cmdBody);
        return clientCommandService.execute(request);
    }

    /***
     * 查询设备状态
     * @return
     */
    @Override
    public Result<DeviceInfoVO> queryDeviceInfo() {
        LOG.debug("=====开始获取设备状态信息=====");
        EmptyClientCmdBody cmdBody = new EmptyClientCmdBody();
        AccessToken token = Oauth2ThreadLocalUtil.getToken();
        cmdBody.setUdid(token.getUdid());
        cmdBody.setUserId(token.getUserId());
        //如果未设置，则指定为未越狱
        ClientCmdRequestDto request = new ClientCmdRequestDto();
        request.setType(ClientCmdType.QUERY_DEVICE_STATUS.getCode());
        request.setCmdBody(cmdBody);
        return clientCommandService.execute(request);
    }


    /****
     * 上传越狱状态
     * @param cmdBody
     * @param <T>
     * @return
     */
    @Override
    public <T> Result<T> uploadJailBrokenStatus(UploadJailbrokenCmdBody cmdBody) {
        String errMsg = ValidatorUtil.validate(cmdBody);
        if (StringUtils.isNotBlank(errMsg)) {
            throw new MdmException(MdmErrorType.REQUEST_INVALID);
        }
        AccessToken token = Oauth2ThreadLocalUtil.getToken();
        cmdBody.setUdid(token.getUdid());
        cmdBody.setUserId(token.getUserId());
        //如果未设置，则指定为未越狱
        if (cmdBody.getJailbroken() == null) {
            cmdBody.setJailbroken(SysConstant.FALSE_VALUE);
        }
        if (cmdBody.getPlatform() == null) {
            cmdBody.setPlatform(Platform.ANDROID.getValue());
        }
        ClientCmdRequestDto request = new ClientCmdRequestDto();
        request.setType(ClientCmdType.UPLOAD_JAILBROKEN_STATUS.getCode());
        request.setPlatform(Platform.findByValue(cmdBody.getPlatform()));
        request.setCmdBody(cmdBody);

        return clientCommandService.execute(request);
    }

    /****
     * 生成设备UDID
     * @param device
     * @return
     */
    @Override
    public Result<String> createUdid(DeviceActivatedDto device) {
        LOG.debug("=====开始生成设备唯一ID=====");
        if (device.getDeviceType() == null) {
            device.setDeviceType(DeviceTypeConstant.ANDROID_PHONE);
        }
        String udid = deviceBindService.createUdid(device, DeviceTypeConstant.getPlatformByDeviceType(device.getDeviceType()));
        LOG.debug("=====本次生成设备唯一ID值为:{}=====", udid);
        return successResult(udid);
    }

    /***
     * 上传域状态
     * @param cmdBody
     * @param <T>
     * @return
     */
    @Override
    public <T> Result<T> uploadDualStatus(UploadDualStatusCmdBody cmdBody) {

        String errMsg = ValidatorUtil.validate(cmdBody);
        if (StringUtils.isNotBlank(errMsg)) {
            throw new MdmException(MdmErrorType.REQUEST_INVALID);
        }
        AccessToken token = Oauth2ThreadLocalUtil.getToken();
        cmdBody.setUdid(token.getUdid());
        cmdBody.setUserId(token.getUserId());
        //如果未设置，则指定为工作域
        if (cmdBody.getDualStatus() == null) {
            cmdBody.setDualStatus(DualStatus.WORK.getCode());
        }
        ClientCmdRequestDto request = new ClientCmdRequestDto();
        request.setType(ClientCmdType.UPLOAD_DUAL_STATUS.getCode());
        request.setCmdBody(cmdBody);
        request.setPlatform(Platform.ANDROID);
        return clientCommandService.execute(request);
    }


    /***
     * 更新系统信息
     * @param cmdBody
     * @param <T>
     * @return
     */
    @Override
    public <T> Result<T> updateVersion(UpdateVersionCmdBody cmdBody) {
        String errMsg = ValidatorUtil.validate(cmdBody);
        if (StringUtils.isNotBlank(errMsg)) {
            throw new MdmException(MdmErrorType.REQUEST_INVALID);
        }
        AccessToken token = Oauth2ThreadLocalUtil.getToken();
        cmdBody.setUdid(token.getUdid());
        cmdBody.setUserId(token.getUserId());
        ClientCmdRequestDto request = new ClientCmdRequestDto();
        request.setType(ClientCmdType.UPDATE_VERSION.getCode());
        request.setPlatform(Platform.ANDROID);
        request.setCmdBody(cmdBody);
        return clientCommandService.execute(request);
    }

    /***
     * 更新APNS对应的Token信息
     * @param cmdBody
     * @param <T>
     * @return
     */
    @Override
    public <T> Result<T> updateAppPushToken(UpdateAppPushTokenCmdBody cmdBody) {
        AccessToken token = Oauth2ThreadLocalUtil.getToken();
        cmdBody.setUdid(token.getUdid());
        cmdBody.setUserId(token.getUserId());
        ClientCmdRequestDto request = new ClientCmdRequestDto();
        request.setType(ClientCmdType.UPDATE_APP_PUSH_TOKEN.getCode());
        //request.setPlatform(Platform.ANDROID);
        request.setCmdBody(cmdBody);
        return clientCommandService.execute(request);
    }

    @Override
    public Result<MdmChallengeVO> getChallenge() {
        return ResponseResult.success(clientRestRequestService.getChallenge());
    }

    /***
     * 更新设备信息
     * @param cmdBody
     * @param <T>
     * @return
     */
    @Override
    public <T> Result<T> updateDevice(UpdateDeviceCmdBody cmdBody) {
        AccessToken token = Oauth2ThreadLocalUtil.getToken();
        cmdBody.setUdid(token.getUdid());
        cmdBody.setUserId(token.getUserId());
        ClientCmdRequestDto request = new ClientCmdRequestDto();
        request.setType(ClientCmdType.UPDATE_DEVICE.getCode());
        //request.setPlatform(Platform.ANDROID);
        request.setCmdBody(cmdBody);
        return clientCommandService.execute(request);
    }

    @Override
    public <T> Result<T> queryUserProfile() {
        EmptyClientCmdBody cmdBody = new EmptyClientCmdBody();
        AccessToken token = Oauth2ThreadLocalUtil.getToken();
        cmdBody.setUdid(token.getUdid());
        cmdBody.setUserId(token.getUserId());
        DEBUG_LOG.info("Query user profile for user {} udid {}", token.getUserId(), token.getUdid());
        ClientCmdRequestDto request = new ClientCmdRequestDto();
        request.setType(ClientCmdType.QUERY_USER_PROFILE.getCode());
        request.setCmdBody(cmdBody);
        return clientCommandService.execute(request);
    }

    @Override
    public <T> Result<T> deActivateMdm(DeviceMdmDeActivateCmdBody cmdBody) {
        AccessToken token = Oauth2ThreadLocalUtil.getToken();
        cmdBody.setUdid(token.getUdid());
        cmdBody.setUserId(token.getUserId());
        ClientCmdRequestDto request = new ClientCmdRequestDto();
        request.setType(ClientCmdType.DEACTIVATE_MDM.getCode());
        //request.setPlatform(Platform.ANDROID);
        request.setCmdBody(cmdBody);
        return clientCommandService.execute(request);
    }

    /****
     * 上报风险检测
     * @param cmdBody
     * @return
     */
    @Override
    public <T> Result<T> uploadRisks(UploadRisksCmdBody cmdBody) {
        AccessToken token = Oauth2ThreadLocalUtil.getToken();
        cmdBody.setUdid(token.getUdid());
        cmdBody.setUserId(token.getUserId());
        ClientCmdRequestDto request = new ClientCmdRequestDto();
        request.setType(ClientCmdType.UPLOAD_RISK.getCode());
        request.setCmdBody(cmdBody);
        return clientCommandService.execute(request);
    }
}
