package com.cyberscraft.uep.mdm.app.controller.basic;

import com.cyberscraft.uep.common.bean.Result;
import com.cyberscraft.uep.common.dto.ReturnResultVO;
import com.cyberscraft.uep.common.util.ValidatorUtil;
import com.cyberscraft.uep.mdm.api.basic.TenantApi;
import com.cyberscraft.uep.mdm.api.dto.tenant.ClientIntegrationDto;
import com.cyberscraft.uep.mdm.api.dto.tenant.ClientIntegrationVO;
import com.cyberscraft.uep.mdm.api.dto.tenant.ClientSignatureSecretRotationVO;
import com.cyberscraft.uep.mdm.api.dto.tenant.TenantCreateRequestDto;
import com.cyberscraft.uep.mdm.api.errors.MdmErrorType;
import com.cyberscraft.uep.mdm.app.controller.BaseController;
import com.cyberscraft.uep.mdm.core.exception.MdmException;
import com.cyberscraft.uep.mdm.core.service.basic.ITenantService;
import com.cyberscraft.uep.mdm.core.service.client.IClientIntegrationService;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/***
 * 主要用于提供租户创建及租户状态查询API接口
 * @date 2021-10-16
 * <AUTHOR>
 ***/
@RestController
public class TenantController extends BaseController implements TenantApi {


    @Resource
    private ITenantService tenantService;

    @Autowired
    private IClientIntegrationService clientIntegrationService;

    @Override
    public Result<Integer> create(TenantCreateRequestDto requestDto) {
        String errMsg = ValidatorUtil.validate(requestDto);
        if (StringUtils.isNotBlank(errMsg)) {
            throw new MdmException(MdmErrorType.REQUEST_INVALID);
        }

        Integer status = tenantService.create(requestDto);
        return successResult(status);
    }

    /***
     * 查询租户的状态，如果redis中不存在，则为未知状态，否则为Redis中对应的状态
     * @param tenant_id
     * @return
     */
    @Override
    public Result<Integer> getStatus(String tenant_id) {
        Integer status = tenantService.status(tenant_id);
        return successResult(status);
    }

    // ===========应用集成信息============

    @Override
    public Result<? extends Object> createClient(ClientIntegrationDto clientIntegrationDto) {
        boolean client = clientIntegrationService.createClient(clientIntegrationDto);
        return successResult(client);
    }

    @Override
    public Result<Boolean> updateClient(String client_id, @Valid ClientIntegrationDto clientIntegrationDto) {
        boolean client = clientIntegrationService.updateClient(client_id, clientIntegrationDto);
        return successResult(client);
    }

    @Override
    public Result<Boolean> deleteClient(String clientId) {
        boolean client = clientIntegrationService.deleteClient(clientId);
        return successResult(client);
    }

    @Override
    public Result<ClientIntegrationVO> getClientByClientId(String clientId) {
        ClientIntegrationVO clientIntegrationDto = clientIntegrationService.getClientByClientId(clientId);
        return successResult(clientIntegrationDto);
    }

    @Override
    public ReturnResultVO<ClientSignatureSecretRotationVO> updateClientSignatureSecret(String clientId) {
        ClientSignatureSecretRotationVO clientIntegrationDto = clientIntegrationService.updateClientSignatureSecret(clientId);
        return new ReturnResultVO<>(clientIntegrationDto);
    }

    @Override
    public Result<List<ClientIntegrationVO>> getClientByTenantOwner() {
        String tenantOwner = TenantHolder.getTenantCode();
        List<ClientIntegrationVO> integrationDtos = clientIntegrationService.getClientByTenantOwner(tenantOwner);
        return successResult(integrationDtos);
    }
}
