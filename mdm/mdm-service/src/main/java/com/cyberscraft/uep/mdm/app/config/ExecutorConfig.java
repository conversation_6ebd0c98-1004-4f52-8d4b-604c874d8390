package com.cyberscraft.uep.mdm.app.config;

import com.cyberscraft.uep.common.util.MDCThreadWrapUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <p>
 *     线程池配置
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2019-08-01 10:23
 */
@Configuration
@EnableAsync
public class ExecutorConfig {
    @Value("${data.pull.job.threadpool.corePoolSize:5}")
    private int dataPullJobCorePoolSize;
    @Value("${data.pull.job.threadpool.MaxPoolSize:10}")
    private int dataPullJobCoreMaxPoolSize;



    @Bean
    public Executor dataPullJobExecutor() {
        ThreadPoolTaskExecutor executor = new MDCThreadWrapUtil.MdcThreadPoolTaskExecutorWrapper();
        executor.setCorePoolSize(dataPullJobCorePoolSize);
        executor.setMaxPoolSize(dataPullJobCoreMaxPoolSize);
        executor.setQueueCapacity(Integer.MAX_VALUE);
        executor.setThreadNamePrefix("data-pull-job-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }
}