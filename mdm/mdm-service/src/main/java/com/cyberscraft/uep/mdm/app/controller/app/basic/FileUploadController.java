package com.cyberscraft.uep.mdm.app.controller.app.basic;

import com.cyberscraft.uep.common.bean.Result;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import com.cyberscraft.uep.mdm.api.constant.UrlConstant;
import com.cyberscraft.uep.mdm.api.dto.device.DeviceFileUploadVo;
import com.cyberscraft.uep.mdm.api.errors.MdmErrorType;
import com.cyberscraft.uep.mdm.app.controller.BaseController;
import com.cyberscraft.uep.mdm.app.controller.app.BasicController;
import com.cyberscraft.uep.mdm.core.exception.MdmException;
import com.cyberscraft.uep.mdm.core.service.device.ISysUserFileService;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;


/**
 * <p>
 * 设备文件上传
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2019-11-25 10:36
 */
@RestController
@RequestMapping(value = UrlConstant.BASIC_FILE_URL_PREFIX, produces = MediaType.APPLICATION_JSON_VALUE)
public class FileUploadController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(FileUploadController.class);

    @Autowired
    private ISysUserFileService sysUserFileService;


    /**
     * 上传文件
     *
     * @return
     * @throws Exception
     */
    @PostMapping(value = "/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Result<?> uploadFile(
            @ApiParam(required = true, name = "occTime", value = "发生时间", example = "0")
            @RequestParam(value = "occTime", required = true, defaultValue = "0") Long occTime,

            @ApiParam(required = true, name = "businessType", value = "业务类型", example = "0")
            @RequestParam(value = "businessType", required = true, defaultValue = "0") Integer businessType,

            @ApiParam(required = true, name = "packageName", value = "包名", example = "0")
            @RequestParam(value = "packageName", required = true, defaultValue = "0") String packageName,

            @ApiParam(name = "batchNo", value = "批次号", example = "0")
            @RequestParam(value = "batchNo", required = false, defaultValue = "0") String batchNo,

            @ApiParam(required = true, name = "uploadedFile", value = "媒体文件")
            @RequestParam(value = "uploadedFile", required = true) MultipartFile uploadedFile) {
        DeviceFileUploadVo vo = new DeviceFileUploadVo();
        vo.setOccTime(occTime);
        vo.setBusinessType(businessType);
        vo.setPackageName(packageName);
        vo.setBatchNo(batchNo);
        vo.setUploadedFile(uploadedFile);
        if (null == uploadedFile) {
            throw new MdmException(MdmErrorType.REQUEST_INVALID);
        }

        String fileName = uploadedFile.getOriginalFilename();
        String tenantId = TenantHolder.getTenantCode();

        logger.info("文件上传租户名:{},设备udid：{},上传文件名：{}", tenantId, vo.getUdid(), fileName);

        Boolean handleResult = sysUserFileService.uploadAndSaveFile(vo, null);
        return successResult(handleResult);

    }

}
