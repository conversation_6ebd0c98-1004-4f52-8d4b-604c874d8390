package com.cyberscraft.uep.mdm.app.controller.basic;

import com.cyberscraft.uep.account.client.constant.ThirdPartyAccountType;
import com.cyberscraft.uep.account.client.domain.Connector;
import com.cyberscraft.uep.account.client.domain.SnsConfig;
import com.cyberscraft.uep.account.client.domain.ThirdPartyCallBackMessage;
import com.cyberscraft.uep.account.client.service.IThirdPartyCallBackConfigService;
import com.cyberscraft.uep.account.client.service.IThirdPartyMessageService;
import com.cyberscraft.uep.common.bean.Result;
import com.cyberscraft.uep.common.enums.ExceptionCodeEnum;
import com.cyberscraft.uep.common.util.RestTemplateFactory;
import com.cyberscraft.uep.mdm.api.basic.MessageApi;
import com.cyberscraft.uep.mdm.api.constant.Constant;
import com.cyberscraft.uep.mdm.app.controller.BaseController;
import com.cyberscraft.uep.mdm.core.domain.Token;
import com.cyberscraft.uep.mdm.core.exception.MdmException;
import com.cyberscraft.uep.mdm.core.service.config.ISysConfiguration;
import com.cyberscraft.uep.mdm.core.util.AdminTokenThreadLocal;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import org.apache.commons.codec.Charsets;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.*;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

/***
 * 数据处理 API
 * @date 2021-11-03
 * <AUTHOR>
 ***/
@RestController
public class MessageController extends BaseController implements MessageApi {
    private final static Logger LOG = LoggerFactory.getLogger(MessageController.class);

    /***
     *
     */
    @Resource
    private ISysConfiguration sysConfiguration;

//    @Resource
//    private IUserGroupEventService userEventService;

    @Resource
    private IThirdPartyCallBackConfigService thirdPartyCallBackConfigService;

    @Resource
    private IThirdPartyMessageService thirdPartyMessageService;

    /***
     * 增加代码转换
     */
    private static HttpMessageConverter httpMessageConverter = new StringHttpMessageConverter(Charsets.UTF_8);

    @Override
    public String reciveData(String tenantId, String api, String method, String data) {
        if (StringUtils.isBlank(tenantId)) {
            throw new MdmException(ExceptionCodeEnum.TENANT_CODE_EMPTY);
        }
        if (StringUtils.isBlank(api)) {
            throw new MdmException(ExceptionCodeEnum.PARAM_INVALID.getCode(), "未指定API参数");
        }
        if (StringUtils.isBlank(method)) {
            throw new MdmException(ExceptionCodeEnum.PARAM_INVALID.getCode(), "未指定method参数");
        }
        String httpUrl = "http://127.0.0.1";
        //进行api地址的组装
        int port = sysConfiguration.getApplicationServerPort();
        if (port != 80 && port != 443) {
            httpUrl += ":" + port;
        }
        if (!api.startsWith("/")) {
            api = "/" + api;
        }
        String requestUrl = httpUrl + api;
        LOG.info("当前租户ID:{},转换后的租户ID为:{},请求的api为:{},请求方式为:{},数据为:{}", tenantId, TenantHolder.getTenantCode(), requestUrl, method, data);

        String mdmTenantId = TenantHolder.getTenantCode();
        if (StringUtils.isBlank(mdmTenantId)) {
            mdmTenantId = tenantId;
        }
        HttpHeaders header = new HttpHeaders();
        header.add("tenantId", mdmTenantId);
        header.add(Constant.DEFAULT_TENANT_HEADEARNAME, mdmTenantId);
        header.setContentType(MediaType.APPLICATION_JSON);
        String access_token = getRequest().getHeader(Constant.DEFAULT_ACCESSTOKEN_HEADERNAME);
        if (StringUtils.isBlank(access_token)) {
            Token adminToken = AdminTokenThreadLocal.get();
            if (adminToken != null) {
                header.add(Constant.DEFAULT_ACCESSTOKEN_HEADERNAME, Constant.DEFAULT_ACCESSTOKEN_SCHEME + " " + adminToken.getToken());
            }
        } else {
            header.add(Constant.DEFAULT_ACCESSTOKEN_HEADERNAME, access_token);
        }

        HttpEntity<String> requestEntity = new HttpEntity<String>(data, header);
        RestTemplate restTemplate = RestTemplateFactory.getDefaultRestTemplate();


        ResponseEntity<String> res = null;
        //restTemplate.exchange();
        //根据不同的请求方式进行数据的转发，如果出错，则报系统错误
        try {
            method = method.toUpperCase();
            switch (method) {
                case "POST":
                    res = restTemplate.postForEntity(requestUrl, requestEntity, String.class);
                    break;
                case "PUT":
                    res = restTemplate.exchange(requestUrl, HttpMethod.PUT, requestEntity, String.class);
                    break;
                case "DELETE":
                    res = restTemplate.exchange(requestUrl, HttpMethod.DELETE, requestEntity, String.class);
                    break;
                default:
                    res = restTemplate.exchange(requestUrl, HttpMethod.GET, requestEntity, String.class);
            }
            if (res == null) {
                throw new MdmException(ExceptionCodeEnum.INNER_ERROR.getCode(), "请求结果无效");
            }
            if (res.getStatusCode().value() != HttpStatus.OK.value()) {
                throw new MdmException(ExceptionCodeEnum.INNER_ERROR.getCode(), "请求结果无效 http status code" + res.getStatusCode());
            }
            return res.getBody();

        } catch (MdmException e) {
            throw e;
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
            throw new MdmException(ExceptionCodeEnum.INNER_ERROR, e);
        }
    }

    /***
     * 接收消息处理事件
     * @param dispatchId
     * @param sourceMessageId
     * @param callbackMessageId
     * @param content
     * @return
     */
    @Override
    public <T> T receivEvent(String dispatchId, String sourceMessageId, String callbackMessageId, String content) {
        LOG.info("收到事件处理请求:dispatchId=>{},sourceMessageId=>{},callbackMessageId=>{},contenant=>{}", dispatchId, sourceMessageId, callbackMessageId, content);
        ThirdPartyCallBackMessage msg = new ThirdPartyCallBackMessage(ThirdPartyAccountType.GOV_DINGDING.getCode(), content);

        SnsConfig snsConfig = null;
        return thirdPartyMessageService.deal(msg, snsConfig);
        //ResponseEntity responseEntity = new ResponseEntity();
//        return JsonUtil.obj2Str(result);
        //return okResponse();
    }

    @Override
    public <T> Result<T> initEvent() {
        //userEventService.registerThirdPartyEventCallBack();
        thirdPartyCallBackConfigService.registerEventCallBack(null, ThirdPartyAccountType.GOV_DINGDING.getCode(), null);
        return successResult(null);
        //return JsonUtil.obj2Str(successResult(null));
    }


    @Override
    public <T> Result<T> removeEventCallBack(String id) {
        //userEventService.removeEventCallBack(id);
        thirdPartyCallBackConfigService.removeEventCallBack(null, ThirdPartyAccountType.GOV_DINGDING.getCode(), id, null);

        return successResult(null);
    }

    @Override
    public <T> Result<T> queryEventCallBack() {
        //T rs = (T) userEventService.getThirdPartyEventCallBacks(ThirdPartyAccountType.GOV_DINGDING.getCode());
        T rs = thirdPartyCallBackConfigService.getEventCallBacks(null, ThirdPartyAccountType.GOV_DINGDING.getCode(), null);

        return successResult(rs);
    }


    @Override
    public <T> Result<T> cleanFailuredEventCallBack() {
        //userEventService.cleanFiluredCallBackEvent();
        thirdPartyCallBackConfigService.cleanFiluredCallBackEvent(null, ThirdPartyAccountType.GOV_DINGDING.getCode(), null);

        return successResult(null);
    }
}
