package com.cyberscraft.uep.mdm.app.controller.admin;

import com.cyberscraft.uep.common.bean.Result;
import com.cyberscraft.uep.common.enums.ExceptionCodeEnum;
import com.cyberscraft.uep.common.util.RemoteUtil;
import com.cyberscraft.uep.common.util.ValidatorUtil;
import com.cyberscraft.uep.mdm.api.admin.AdminAuthorizationApi;
import com.cyberscraft.uep.mdm.api.dto.admin.AdminLoginDto;
import com.cyberscraft.uep.mdm.api.dto.admin.AdminLoginPageInfoVO;
import com.cyberscraft.uep.mdm.api.dto.admin.AdminTokenVO;
import com.cyberscraft.uep.mdm.api.dto.admin.SwaggerLoginDto;
import com.cyberscraft.uep.mdm.core.service.admin.IAdminAuthorizationService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/***
 *
 * @date 2021-09-11
 * <AUTHOR>
 ***/
@RestController
public class AdminAuthorizationController extends BaseAdminCtroller implements AdminAuthorizationApi {


    /***
     * 管理员授权处理接口
     */
    @Resource
    private IAdminAuthorizationService adminAuthorizationService;


    @Override
    public Result<AdminLoginPageInfoVO> getPreAuth(String wenToken, String tenantId) throws Exception {
        AdminLoginPageInfoVO ret=adminAuthorizationService.getPreAuthInfo(wenToken,tenantId);
        return successResult(ret);
    }


    /***
     * 用于获取swagger调用时的管理员访问token
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/swtoken", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    @ApiOperation(response = AdminTokenVO.class, nickname = "getSwaggerAccessToken",
            value = "获取管理员token",
            notes = "获取管理员token")
    public Result<AdminTokenVO> getSwaggerAccessToken(
            @ApiParam(required = true, value = "swagger登录请求参数")
            @RequestBody SwaggerLoginDto swaggerLoginDto) throws Exception {
        //组装请求参数到dto
        AdminLoginDto dto = new AdminLoginDto();
        dto.setLoginId(swaggerLoginDto.getLoginId());
        dto.setPassword(swaggerLoginDto.getPassword());
        dto.setTenantId(swaggerLoginDto.getTenantId());
        HttpServletRequest request = getRequest();
        dto.setClientIp(RemoteUtil.getRealIpAddr(request));
        String errMsg = ValidatorUtil.validate(dto);
        if (StringUtils.isNotBlank(errMsg)) {
            return failureResult(ExceptionCodeEnum.PARAM_INVALID, errMsg);
        }
        //进行登录请求处理
        AdminTokenVO ret = adminAuthorizationService.authorizeWithoutRandomKey(dto);
        return successResult(ret);
    }

    /***
     * 用于管理员登录
     * @param loginId
     * @param password
     * @return
     * @throws Exception
     */
    @Override
    public Result<AdminTokenVO> getAccessToken(String loginId, String password,String randomKey, String tenantId) throws Exception {
        //组装请求参数到dto
        AdminLoginDto dto = new AdminLoginDto();
        dto.setLoginId(loginId);
        dto.setPassword(password);
        dto.setTenantId(tenantId);
        dto.setRandomKey(randomKey);
        HttpServletRequest request = getRequest();
        dto.setClientIp(RemoteUtil.getRealIpAddr(request));

        //设置验证码及相关值
        try {
            dto.setCaptcha(request.getParameter("captcha"));
            dto.setVerifyCode(request.getParameter("verifyCode"));
        }
        catch (Exception e){
            LOG.warn(e.getMessage(),e);
        }
        String errMsg = ValidatorUtil.validate(dto);
        if (StringUtils.isNotBlank(errMsg)) {
            return failureResult(ExceptionCodeEnum.PARAM_INVALID, errMsg);
        }
        //进行登录请求处理
        AdminTokenVO ret = adminAuthorizationService.authorize(dto);
        return successResult(ret);
    }

}
