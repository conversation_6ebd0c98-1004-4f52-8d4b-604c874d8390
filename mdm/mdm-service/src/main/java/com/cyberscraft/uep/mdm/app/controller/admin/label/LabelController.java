//package com.cyberscraft.uep.mdm.app.controller.admin.label;
//
//import com.cyberscraft.uep.common.bean.Result;
//import com.cyberscraft.uep.common.enums.ExceptionCodeEnum;
//import com.cyberscraft.uep.common.dto.PageView;
//import com.cyberscraft.uep.common.dto.Pagination;
//import com.cyberscraft.uep.common.dto.QueryOrderItem;
//import com.cyberscraft.uep.common.util.CollectionUtils;
//import com.cyberscraft.uep.mdm.api.admin.label.LabelApi;
//import com.cyberscraft.uep.mdm.api.dto.label.LabelDetailsVO;
//import com.cyberscraft.uep.mdm.api.dto.label.StaticLabelVO;
//import com.cyberscraft.uep.mdm.api.dto.user.DynamicLabelUserGroupQueryDto;
//import com.cyberscraft.uep.mdm.api.dto.user.LabelQueryDto;
//import com.cyberscraft.uep.mdm.api.dto.user.StaticLabelUserGroupQueryDto;
//import com.cyberscraft.uep.mdm.api.errors.MdmErrorType;
//import com.cyberscraft.uep.mdm.app.controller.admin.BaseAdminCtroller;
//import com.cyberscraft.uep.mdm.api.dto.label.DynaLabelUserGroup;
//import com.cyberscraft.uep.mdm.core.domain.Token;
//import com.cyberscraft.uep.mdm.core.entity.LabelEntity;
//import com.cyberscraft.uep.mdm.core.entity.StaticLabelUserEntity;
//import com.cyberscraft.uep.mdm.core.service.user.IUserLabelService;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.web.bind.annotation.RestController;
//
//import javax.annotation.Resource;
//import java.time.LocalDateTime;
//import java.util.*;
//
///***
// *
// * @date 2021-10-17
// * <AUTHOR>
// ***/
//@RestController
//public class LabelController extends BaseAdminCtroller implements LabelApi {
//
//    @Resource
//    private IUserLabelService iUserLabelService;
//
//    @Override
//    public Result<PageView<LabelDetailsVO>> getAllLabels(String labelNameOrDesc, Integer isAll, String sort, String order, int offset, int limit) {
//        if (StringUtils.isNotBlank(sort)) {
//            String regx = "creator|type|updateTime";
//            if (!sort.matches(regx)) {
//                return failureResult(ExceptionCodeEnum.PARAM_ENUM_INVALID);
//            }
//        }
//
//        if (StringUtils.isNotBlank(order)) {
//            String regx = "DESC|ASC";
//            if (!order.toUpperCase().matches(regx)) {
//                return failureResult(ExceptionCodeEnum.PARAM_ENUM_INVALID);
//            }
//        }
//
//        List<String> childrenAdminList = null;
//        if (!isSuperAdmin()) {
//            childrenAdminList = getCurrentAdmin().getManagedAdminList();
//        }
//
//        //构造查询dto对象
//        Pagination<LabelQueryDto> dtoPagination = new Pagination<>(offset, limit);
//        //构造排序对象
//        List<QueryOrderItem> list = new ArrayList<>();
//        dtoPagination.setOrders(list);
//        QueryOrderItem orderItem = new QueryOrderItem();
//        list.add(orderItem);
//        if (StringUtils.isBlank(sort)) {
//            sort = "updateTime";
//        }
//        orderItem.setColumn(sort);
//        if (order.toLowerCase().equalsIgnoreCase("true")) {
//            orderItem.setAsc(true);
//        } else {
//            orderItem.setAsc(false);
//        }
//        //构造查询条件
//        LabelQueryDto dto = new LabelQueryDto();
//        dto.setLabelNameOrDesc(labelNameOrDesc);
//        dto.setIsAll(isAll);
//        dto.setChildrenAdminList(childrenAdminList);
//        dtoPagination.setQueryDto(dto);
//        //获取结果
//        PageView<LabelDetailsVO> result = iUserLabelService.getAllLabels(dtoPagination);
//        return successResult(result);
//    }
//
//    @Override
//    public Result<PageView<DynaLabelUserGroup>> getStaticLabelLinkedUsers(String labelId, String otherInfo, String userIds, int offset, int limit) {
//        //构造查询dto对象
//        Pagination<StaticLabelUserGroupQueryDto> dtoPagination = new Pagination<>(offset, limit);
//        StaticLabelUserGroupQueryDto dto = new StaticLabelUserGroupQueryDto();
//        dtoPagination.setQueryDto(dto);
//        //根据labelId查询该静态标签的用户组情况
//        if (StringUtils.isNotBlank(labelId) && labelId.matches("\\d+")) {
//            dto.setLabelId(Long.valueOf(labelId));
//            dto.setOtherInfo(otherInfo);
//        } else if (StringUtils.isNotBlank(userIds)) { //根据userIds查询用户组情况
//            List<Long> list = new ArrayList<>();
//            String[] userArray = userIds.split(",");
//            for (String userId : userArray) {
//                list.add(new Long(userId));
//            }
//            dto.setUserIds(list);
//            dto.setOtherInfo(otherInfo);
//        } else {
//            return failureResult(MdmErrorType.PARAM_LABEL_USER_ID_IS_NULL);
//        }
//        PageView<DynaLabelUserGroup> pageView = iUserLabelService.getStaticLabelUserGroup(dtoPagination);
//        return successResult(pageView);
//    }
//
//    @Override
//    public Result<Long> addStaticLabel(StaticLabelVO vo) {
////        if (StringUtils.isBlank(vo.getLabelName())
////                || StringUtils.isEmpty(vo.getLabelCreator())
////                || CollectionUtils.isEmpty(vo.getUserIds())) {
////            return failureResult(MdmErrorType.LABEL_Add_PARAMS_INVALID);
////        }
////        if (!vo.getLabelType().equals("1")
////                &&!vo.getLabelType().equals("2")) {
////            return failureResult(MdmErrorType.LABEL_TYPE_INVALID);
////        }
//        MdmErrorType mdmErrorType = checkValidStaticLabelVo(vo);
//        if (null != mdmErrorType) {
//            return failureResult(mdmErrorType);
//        }
//        Token token = getCurrentAdmin();
//        vo.setLabelCreator(token.getLoginId());
//        LabelEntity labelEntity = genLabelEntity(vo);
//        Set<StaticLabelUserEntity> userEntities = genStaticLabelUserEntitys(vo);
//        Long newLabelId = iUserLabelService.AddStaticLabelWithUsers(labelEntity, userEntities);
//        return successResult(newLabelId);
//    }
//
//    private MdmErrorType checkValidStaticLabelVo(StaticLabelVO vo) {
//        if (StringUtils.isBlank(vo.getLabelName())
//                || StringUtils.isEmpty(vo.getLabelCreator())
//                || CollectionUtils.isEmpty(vo.getUserIds())) {
//            return MdmErrorType.LABEL_Add_PARAMS_INVALID;
//        }
//        if (!vo.getLabelType().equals("1")
//                && !vo.getLabelType().equals("2")) {
//            return MdmErrorType.LABEL_TYPE_INVALID;
//        }
//        return null;
//    }
//
//    @Override
//    public Result<Boolean> updStaticLabel(Long labelId, StaticLabelVO vo) {
//        MdmErrorType mdmErrorType = checkValidStaticLabelVo(vo);
//        if (null != mdmErrorType) {
//            return failureResult(mdmErrorType);
//        }
//        Token token = getCurrentAdmin();
//        LabelEntity existLabel = iUserLabelService.getLabelById(Long.valueOf(labelId));
//        if (existLabel == null) {
//            return failureResult(MdmErrorType.LABEL_NOT_EXIST);
//        } else {
//            //	校验当前编辑标签的用户和标签创建用户是否一致
//            if (!existLabel.getCreator().equals(token.getLoginId())) {
//                return failureResult(MdmErrorType.LABEL_CREATOR_NOT_SAME_ERROE);
//            }
//        }
//        vo.setId(labelId + "");
//        //设置标签创建者 即当前登录系统的用户
//        vo.setLabelCreator(token.getLoginId());
//        LabelEntity labelEntity = genLabelEntity(vo);
//        Set<StaticLabelUserEntity> userEntities = genStaticLabelUserEntitys(vo);
//        boolean bln = iUserLabelService.updStaticLabelWithUsers(labelEntity, userEntities);
//        return successResult(bln);
//    }
//
//    @Override
//    public Result<PageView<DynaLabelUserGroup>> getDynamicLabelLinkedUsers(String labelId, String otherInfo, int offset, int limit) {
//        if (StringUtils.isBlank(labelId) || !labelId.matches("\\d+")) {
//            return failureResult(MdmErrorType.PARAM_LABEL_ID_INVALID);
//        }
//        Map<String, String> conditions = iUserLabelService.getDynamicLabelConditions(Long.valueOf(labelId));
//        if (StringUtils.isEmpty(conditions.get("userids"))
//                && StringUtils.isEmpty(conditions.get("groupids"))) {
//            return failureResult(MdmErrorType.PARAM_LABEL_GROUP_ID_NOT_EXIST);
//        }
//        //构造查询dto对象
//        Pagination<DynamicLabelUserGroupQueryDto> dtoPagination = new Pagination<>(offset, limit);
//        DynamicLabelUserGroupQueryDto dto = new DynamicLabelUserGroupQueryDto();
//        dto.setConditions(conditions);
//        dto.setOtherInfo(otherInfo);
//        dtoPagination.setQueryDto(dto);
//        //获取结果
//        PageView<DynaLabelUserGroup> result = iUserLabelService.getDynamicLabelUserGroup(dtoPagination);
//        return successResult(result);
//    }
//
//    private LabelEntity genLabelEntity(StaticLabelVO vo) {
//        LabelEntity label = new LabelEntity();
//        if (null == vo) {
//            throw new NullPointerException();
//        } else {
//            // 封装labelUser对象
//            if (StringUtils.isNotBlank(vo.getId())) {
//                label.setId(Long.valueOf(vo.getId()));
//            }
//            label.setName(vo.getLabelName());
//            label.setCreator(vo.getLabelCreator());
//            label.setDescription(vo.getLabelDesc());
//            label.setType(Integer.valueOf(vo.getLabelType()));
//            //String createTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
//            label.setCreateTime(LocalDateTime.now());
//            label.setUpdateTime(LocalDateTime.now());
//        }
//        return label;
//    }
//
//    private Set<StaticLabelUserEntity> genStaticLabelUserEntitys(StaticLabelVO vo) {
//        Set<StaticLabelUserEntity> staticLabelUsers = new HashSet<>();
//        if (null == vo) {
//            throw new NullPointerException();
//        } else {
//            StaticLabelUserEntity staticLabelTemp;
//            for (String userId : vo.getUserIds()) {
//                staticLabelTemp = new StaticLabelUserEntity();
//                staticLabelTemp.setLabelId(Long.parseLong(vo.getId()));
//                staticLabelTemp.setUserId(Long.parseLong(userId));
//                staticLabelUsers.add(staticLabelTemp);
//            }
//        }
//        return staticLabelUsers;
//    }
//}
