package com.cyberscraft.uep.mdm.app.config;

import com.cyberscraft.uep.common.constant.SysConstant;
import com.cyberscraft.uep.mdm.api.constant.Constant;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import com.cyberscraft.uep.starter.dds.bean.GeneralAttributes;
import com.cyberscraft.uep.starter.dds.properties.DDSConfigProperties;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpHeaders;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/***
 *
 * @date 2021-08-28
 * <AUTHOR>
 ***/
@Configuration
//@Order(Ordered.HIGHEST_PRECEDENCE + 10001)
public class FeginClientConfiguration {

    private static final String HEADER_AUTHORIZATION = HttpHeaders.AUTHORIZATION;

    /***
     *
     */
    private final static Logger LOG = LoggerFactory.getLogger(FeginClientConfiguration.class);


    @LoadBalanced
    @Bean
    public RestTemplate getRestTemplate() {
        return new RestTemplate();
    }


    @Resource
    private DDSConfigProperties properties;

    @Bean
    public RequestInterceptor headerInterceptor() {
        return (requestTemplate) -> {
            //设置tcode,
            GeneralAttributes att = properties.getGeneral();
            String headerName = att != null ? att.getTenantHeaderName() : null;
            if (StringUtils.isBlank(headerName)) {
                headerName = Constant.DEFAULT_TENANT_HEADEARNAME;
            }
            String tenantCode = TenantHolder.getTenantCode();
//            if(StringUtils.isBlank(tenantCode)){
//                tenantCode = att.getDefaultSchema().trim() + att.getHeaderSeparator() + att.getDefaultDataBase();
//            }
            requestTemplate.header(headerName, tenantCode);
            if (LOG.isInfoEnabled()) {
                LOG.info("设置{}值{}到请求的header中", headerName, tenantCode);
            }

            //增加日志traceID相关信息
            String traceId = MDC.get(SysConstant.LOG_TRACE_ID);
            if (StringUtils.isNotEmpty(traceId)) {
                requestTemplate.header(SysConstant.LOG_TRACE_ID, traceId);
            }
            String rpcId = MDC.get(SysConstant.LOG_RPC_ID);
            if (StringUtils.isNotEmpty(rpcId)) {
                requestTemplate.header(SysConstant.LOG_RPC_ID, rpcId);
            }

            addTokenHeader(requestTemplate);

        };
    }

    private void addTokenHeader(RequestTemplate requestTemplate) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder
                .getRequestAttributes();
        if(attributes == null){
            return;
        }
        HttpServletRequest request = attributes.getRequest();
        if(request == null){
            return;
        }
        String authorization = request.getHeader(HEADER_AUTHORIZATION);
        if(authorization == null){
            return;
        }
        requestTemplate.header(HEADER_AUTHORIZATION, authorization);
    }
}
