package com.cyberscraft.uep.mdm.app.config;

import com.cyberscraft.uep.common.service.impl.DefaultApplicationPostStartServiceImpl;
import com.cyberscraft.uep.common.service.IApplicationPostStartHandle;
import com.cyberscraft.uep.common.service.IApplicationPostStartService;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.List;

/***
 *
 * @date 2021/1/8
 * <AUTHOR>
 ***/
@Configuration
public class ApplicationPostStartHandleAutoCofiguartion {

    @Resource
    private List<IApplicationPostStartHandle> handles;

    //只有在没有启动类注入时启用
    @Bean
    @ConditionalOnMissingBean
    IApplicationPostStartHandle applicationPostStartHandle() {
        return () -> {
        };
    }

    @Bean
    @ConditionalOnMissingBean
    IApplicationPostStartService applicationPostStartService() {
        DefaultApplicationPostStartServiceImpl service= new DefaultApplicationPostStartServiceImpl();
        service.setHandles(handles);
        return service;
    }
}
