<!DOCTYPE html>
<html class="full-hight" lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <style>
        .full-hight {
            height: 100%;
            overflow-y: hidden;
        }
        .flex-one {
            display:flex;
            flex: 1;
            margin-bottom: 5px;
        }
        .center {
            display:flex;
            flex: 1;
            justify-content: center;
            align-items: center;
            margin-bottom: 20px;
        }
        .pull-right {
            justify-content: flex-end;
        }
        .text-input {
            height: 25px;
            border: 2px solid #949494;
            outline: none;
            font-family: Arial,Verdana,Sans-serif;
        }
        .text-input:focus {
            border: 2px solid #0077D3;
        }
        .button {
            font-family: Arial,Verdana,Sans-serif;
            background-color: #0078D7;
            color: #FFFFFF;
            border: none;
            height: 30px;
            width: 80px;
        }
        button[disabled] {
            color: #9B9B9B;
            background-color: #C2C2C2;
        }
        .msg {
            font-family: <PERSON><PERSON>,<PERSON>erdana,Sans-serif;
            color: #5A5A57;
        }
    </style>
</head>
<body class="center full-hight">
<div style="width: 400px">
    <div class="center">
        <img style="height: 64px; width: 83px;"  src="data:image/png;base64,%BASE64IMG%" />
    </div>
    <div>
        <span id="msg" class="msg">请输入企业识别号、用户名和密码</span>
        <form id="form" method="post">
            <input type="hidden" id="wresult" name="wresult" class="flex-one text-input" placeholder="密码" maxlength="16" />
            <div class="flex-one"><input type="text" id="tenantId" name="tenantId" class="flex-one text-input" placeholder="企业识别号" maxlength="50" /></div>
            <div class="flex-one"><input type="text" id="loginId" name="loginId" class="flex-one text-input" placeholder="帐号" maxlength="50" /></div>
            <div class="flex-one"><input type="password" id="password" name="password" class="flex-one text-input" placeholder="密码" maxlength="16" /></div>
        </form>
        <div id="errorMsg" style="display: none; color: red;"><span id="errorText">信息错误，请重新填写</span></div>
        <div class="flex-one">
            <div class="flex-one pull-right">
                <button type="button" id="active" class="button" disabled="disabled">激活</button>
            </div>
        </div>
    </div>
</div>
<script>
    window.onload= function () {
        var activeBtn = document.querySelector("#active"),
                msg = document.querySelector("#msg"),
                form = document.querySelector("#form"),
                language = document.querySelector("#language"),
                errorMsg = document.querySelector("#errorMsg"),
                errorText = document.querySelector("#errorText"),
                wresult = document.querySelector("#wresult"),
                tenantId = document.querySelector("#tenantId"),
                loginId = document.querySelector("#loginId"),
                password = document.querySelector("#password");

        function onInput() {
            errorMsg.style.display = "none";
            if (tenantId.value.trim() === "" || loginId.value.trim() === "" || password.value === "") {
                activeBtn.setAttribute("disabled", "disabled");
            } else {
                activeBtn.removeAttribute("disabled");
            }
        }
        function onSubmit(url) {
            var xmlhttp = new XMLHttpRequest(), data;
            xmlhttp.open("POST", url, true);
            xmlhttp.setRequestHeader("Content-type","application/x-www-form-urlencoded");
            xmlhttp.send("tenantId=" + tenantId.value.trim() + "&loginId=" + loginId.value.trim() + "&password=" + password.value);
            xmlhttp.onreadystatechange = function () {
                if (xmlhttp.readyState === 4) {
                    if (xmlhttp.status === 200) {
                        data = JSON.parse(xmlhttp.responseText);
                        if (data.errorCode === 0) {
                            wresult.value = data.token;
                            form.setAttribute("action", data.forwardUrl);
                            form.submit();
                        } else {
                            errorMsg.style.display = "block";
                        }

                    }
                }
            }
        }
        function getLanguage() {
            return navigator.language.toLocaleLowerCase();
        }
        function setLanguage() {
            if (getLanguage() !== "zh-cn") {
                activeBtn.innerHTML = "Activate";
                tenantId.setAttribute("placeholder", "Tenant id");
                loginId.setAttribute("placeholder", "User name");
                password.setAttribute("placeholder", "Password");
                msg.innerText = "Pls enter your Tenant id User name and Password";
                errorText.innerText = "Information error, please try again";
            }
        }
        activeBtn.onclick = function () {
            onSubmit("/windowsapp/auth");
        };
        tenantId.onkeyup = function () {
            onInput();
        };
        loginId.onkeyup = function () {
            onInput();
        };
        password.onkeyup = function () {
            onInput();
        };
        setLanguage();
    };
</script>
</body>
</html>