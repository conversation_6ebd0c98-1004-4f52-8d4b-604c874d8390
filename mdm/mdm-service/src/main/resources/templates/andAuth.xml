<?xml version="1.0" encoding="utf-8"?>
<Reply Version="1.0" xmlns:th="http://www.thymeleaf.org">
    <OperID ID="6" Version="1.0">
        <TenantID th:text="${andReply.operID.tenantID}"></TenantID>
        <ClientUID th:text="${andReply.operID.clientUID}">${uid}</ClientUID>
        <FlowNum th:text="${andReply.operID.flowNum}">${flowNum}</FlowNum>
        <Timezone>22</Timezone>
        <Result th:text="${andReply.operID.result}">${result}</Result>
        <Message th:text="${andReply.operID.extra}"></Message>
    </OperID>
    <NextAction ID="5" Version="1.0">
        <ExcuteFlag th:text="${andReply.nextAction.excuteFlag}">${ExcuteFlag}</ExcuteFlag>
        <NextFlowNum th:text="${andReply.nextAction.nextFlowNum}">${NextFlowNum}</NextFlowNum>
        <ExcuteType th:text="${andReply.nextAction.excuteType}">${ExcuteType}</ExcuteType>
        <Duty th:text="${andReply.nextAction.duty}">${Duty}</Duty>
    </NextAction>
    <ClientIDInfo ID="4" Version="1.0">
        <UserName th:text="${andReply.clientIDInfo.userName}">${UserName}</UserName>
        <CommonName th:text="${andReply.clientIDInfo.commonName}"></CommonName>
        <GroupId th:text="${andReply.clientIDInfo.groupId}"></GroupId>
        <GroupName th:text="${andReply.clientIDInfo.groupName}"></GroupName>
        <UserPassword th:text="${andReply.clientIDInfo.userPassword}">${UserPassword}</UserPassword>
        <Name th:text="${andReply.clientIDInfo.name}">${Name}</Name>
        <EnablePerm th:text="${andReply.clientIDInfo.enablePerm}">${EnablePerm}</EnablePerm>
        <EnablePermGuide th:text="${andReply.clientIDInfo.enablePermGuide}">${EnablePermGuide}</EnablePermGuide>
        <EMMClientId th:text="${andReply.clientIDInfo.emmClientId}"></EMMClientId>
        <ForceModifyPwd th:text="${andReply.clientIDInfo.forceModifyPwd}"></ForceModifyPwd>
        <IsAdLdapUser th:text="${andReply.clientIDInfo.isAdLdapUser}"></IsAdLdapUser>
        <PwStrength th:text="${andReply.clientIDInfo.pwStrength}"></PwStrength>
        <NeedLockPasswd th:text="${andReply.clientIDInfo.needLockPasswd}"></NeedLockPasswd>
        <ShortUdid th:text="${andReply.clientIDInfo.shortUdid}"></ShortUdid>
        <EmmForbidUnload th:text="${andReply.clientIDInfo.emmForbidUnload}"></EmmForbidUnload>
        <EmmAllowactivate th:text="${andReply.clientIDInfo.emmAllowactivate}"></EmmAllowactivate>
        <UserMobile th:text="${andReply.clientIDInfo.userMobile}"></UserMobile>
        <UserMail th:text="${andReply.clientIDInfo.userMail}"></UserMail>
        <AllowLogout th:text="${andReply.clientIDInfo.allowLogout}"></AllowLogout>
        <GoogleDeviceManagementType th:text="${andReply.clientIDInfo.googleDeviceManagementType}"></GoogleDeviceManagementType>
        <GoogleAuthToken th:text="${andReply.clientIDInfo.googleAuthToken}"></GoogleAuthToken>
    </ClientIDInfo>
    <PushInfo>
        <PushType th:text="${andReply.pushInfo.pushType}">${PushType}</PushType>
        <AppId th:text="${andReply.pushInfo.appId}">${AppId}</AppId>
        <UserName th:text="${andReply.pushInfo.userName}">${UserName}</UserName>
        <UserPassword th:text="${andReply.pushInfo.userPassword}">${UserPassword}</UserPassword>
        <Server th:text="${andReply.pushInfo.server}">${Server}</Server>
        <Port th:text="${andReply.pushInfo.port}">${Port}</Port>
        <PortNumber th:text="${andReply.pushInfo.portNumber}">${PortNumber}</PortNumber>
        <TcpPort th:text="${andReply.pushInfo.tcpPort}">${TcpPort}</TcpPort>
        <AndriodPushType th:text="${andReply.pushInfo.andriodPushType}">${AndriodPushType}</AndriodPushType>
    </PushInfo>
</Reply>