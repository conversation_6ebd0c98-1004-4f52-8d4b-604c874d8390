<?xml version="1.0" encoding="UTF-8"?>
<Reply Version="1.0" xmlns:th="http://www.thymeleaf.org">
    <OperID>
        <!-- 1表示正常，2表示文件夹已不存在 -->
        <Result th:text="${operID.Result}">${Result}</Result>
        <!-- 若文件夹已不存在，则返回文件夹的上级文件夹id，若上级也不存在，则逐步上跃直至根目录 -->
        <Extra th:text="${operID.Extra}">${Extra}</Extra>
    </OperID>
    <!-- 如果VersionCode没变化，可以不传实际内容 -->
    <DocumentList th:attr="versionCode=${documentList_version}">
        <Folder th:each="folder : ${folderList}" th:attr="id=${folder.id},parentId=${folder.parentId},childFileNum=${folder.childFileNum},childFolderNum=${folder.childFolderNum}">
            <CreateTime th:text="${folder.createTime}"/>
            <UpdateTime th:text="${folder.updateTime}"/>
            <Name th:text="${folder.name}"/>
            <IsStar th:text="${folder.isStar}"/>
            <IsLock th:text="${folder.isLock}"/>
            <LockPwd th:text="${folder.lockPwd}"/>
            <Category th:text="${folder.category}"/>
        </Folder>
        <File th:each="file : ${fileList}" th:attr="id=${file.id},ext=${file.ext},size=${file.size},version=${file.version},customVersion=${file.customVersion}">
            <CreateTime th:text="${file.CreateTime}"/>
            <UpdateTime th:text="${file.UpdateTime}"/>
            <Name th:text="${file.Name}"/>
            <FileUrl th:text="${file.FileUrl}"/>
            <FileCRC th:text="${file.FileCRC}"/>
            <PreviewUrl th:text="${file.PreviewUrl}"/>
            <PreviewCRC th:text="${file.PreviewCRC}"/>
            <Admin th:text="${file.Admin}"/>
            <Desc th:text="${file.Desc}"/>
            <IsStar th:text="${file.IsStar}"/>
            <IsLock th:text="${file.IsLock}"/>
            <!-- 加锁密码，建议传32位小写md5值 -->
            <LockPwd th:text="${file.LockPwd}"/>
            <!-- 是否只允许预览 -->
            <PreviewOnly th:text="${file.PreviewOnly}"/>
            <!-- 预览图 -->
            <PreviewImg th:text="${file.PreviewImg}"/>
            <Category th:text="${file.Category}"/>
        </File>
        <AncestorList>
            <Ancestor th:each="ancestor : ${ancestorList}">
           		<id th:text="${ancestor.id}"/>
	            <Name th:text="${ancestor.name}"/>
	            <ParentId th:text="${ancestor.parentId}"/>
            </Ancestor>
        </AncestorList>
	 </DocumentList>
</Reply>


