package com.cyberscraft.uep.mdm.workbench;

import com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.domain.ClientIDInfo;
import com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.domain.DeviceUpdateNew;
import com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.domain.MobileIDInfo;
import com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.domain.OperID;
import com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.parser.AbstractCmdParserDom4j;
import com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.sax.RequestBeanSaxHandler;
import org.dom4j.Element;
import org.openjdk.jmh.annotations.*;

import javax.xml.parsers.SAXParser;
import javax.xml.parsers.SAXParserFactory;
import java.io.ByteArrayInputStream;

/***
 *
 * @date 2021-12-14
 * <AUTHOR>
 ***/
@State(Scope.Thread)
public class TestParseDeviceUpdateCmdDom4j extends AbstractCmdParserDom4j {

    private String xml = "<?xml version='1.0' encoding='UTF-8' ?>" +
            "<Request><OperID><TenantID>57</TenantID>" +
            "<ClientUID>603d05f922b84fa1ac254bf77c4c7957</ClientUID><UEM_Version>100191230</UEM_Version>" +
            "<ProtocolVersion>V2</ProtocolVersion><FlowNum></FlowNum><Timezone>-1</Timezone><Result>-1</Result>" +
            "<ClientVersion>1.0.0</ClientVersion><ClientBuildNum>1</ClientBuildNum></OperID>" +
            "<DeviceUpdate><Battery><BatteryLevel>90</BatteryLevel><BatteryState>1</BatteryState></Battery>" +
            "<Jailed>1</Jailed><UsageStats>0</UsageStats><ClientIP>************</ClientIP><Imsi>/460110012417057</Imsi>" +
            "<SimChanged>0</SimChanged><SdAvailable></SdAvailable>" +
            "<SdTotal></SdTotal><SdID></SdID><Roaming>222</Roaming>" +
            "<SignalStrength>-1</SignalStrength><EncryptionStatus>1</EncryptionStatus>" +
            "<OSVersion>9</OSVersion><SpecialTypeFlag>0</SpecialTypeFlag><ClientOSType>1</ClientOSType>" +
            "<ClientOS>Android</ClientOS><ModelType>1</ModelType>" +
            "<Model>Redmi Note 7 Pro</Model><Maker>Xiaomi</Maker><CpuInfo>armeabi-v7a | 8 core | 1708.0 MHZ</CpuInfo><RamTotal>5.52GB</RamTotal>" +
            "<RamAvailable>2.28GB</RamAvailable><RomTotal>109GB</RomTotal>" +
            "<RomAvailable>70.72GB</RomAvailable><CameraInfo></CameraInfo>" +
            "<BluetoothInfo></BluetoothInfo><WifiMac>20:F4:78:F3:A6:DC</WifiMac><Ecid></Ecid>" +
            "<BtMac></BtMac><BootUp>985</BootUp><Mcnc></Mcnc>" +
            "<ClientSoftInfo>" +
            "<Mobile><MobileNum>/</MobileNum><AllowUnkownSource>1</AllowUnkownSource><Lang>31</Lang><Country>CN</Country><SmsCenter>-1</SmsCenter>" +
            "<CellID>-1</CellID><APN>[type: MOBILE[], state: DISCONNECTED/DISCONNECTED, reason: (unspecified), extra: (none), failover: false, available: true, roaming: false]</APN>" +
            "<BluetoothState>0</BluetoothState><WifiState>1</WifiState><GpsState>1</GpsState><CameraState>1</CameraState>" +
            "<AudioRecordState>1</AudioRecordState><CellularState>1</CellularState>" +
            "</Mobile><Client><Lang>31</Lang><SubCoopID>-1</SubCoopID>" +
            "<ServerIP>-1</ServerIP></Client><SysPlat>" +
            "<ClientOS>1</ClientOS><EditionID>1</EditionID><FirmwareVer>9</FirmwareVer><BasebandVer></BasebandVer>" +
            "<KernelVer>null</KernelVer><BuildNumber>PKQ1.181203.001</BuildNumber></SysPlat>" +
            "</ClientSoftInfo>" +
            "</DeviceUpdate></Request>";

    @Benchmark
    @BenchmarkMode(Mode.SingleShotTime)
    @Fork(1)
    @Measurement(iterations = 20, batchSize = 1)
    @Warmup(iterations = 20, batchSize = 1)
    public long runDom4jParsers() throws InterruptedException {
        parserXML();
        return 0L;
    }

    void parserXML() {
        try {
//            //1、获取解析工厂   SAXParserFactory是protect类型，所以用他的静态方法
//            SAXParserFactory factory = SAXParserFactory.newInstance();
//            //2、从解析工厂获取解析器
//            SAXParser parse = factory.newSAXParser();
//            //3、加载文档Document注册处理器
//            //4、编写处理器
//            RequestBeanSaxHandler handler = new RequestBeanSaxHandler();
//            parse.parse(new ByteArrayInputStream(xml.getBytes()), handler);
//            DeviceUpdateNew deviceUpdate = handler.getDeviceUpdate();
//            OperID obj = handler.getOperID();
//            DeviceUpdateNew updateNew = handler.getDeviceUpdate();
            Element root = getRootElement(xml);
            OperID el = parserObject(root.element("OperID"), OperID.class);
            DeviceUpdateNew updateNew = parserObject(root.element("DeviceUpdate"), DeviceUpdateNew.class);

            LOG.info(updateNew.getBootTime());
            //Assertions.assertTrue(deviceUpdate.getNetTrafficList().getNetTraffic().size() > 0);
            //LOG.info(obj.getClientUID());
        } catch (Exception e) {
           // LOG.error(e.getMessage(), e);
        }
    }

}
