<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>mdm</artifactId>
        <groupId>com.cyberscraft.uep</groupId>
        <version>5.4.1</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>mdm-workbench</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.cyberscraft.uep</groupId>
            <artifactId>mdm-service</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>org.openjdk.jmh</groupId>
            <artifactId>jmh-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.openjdk.jmh</groupId>
            <artifactId>jmh-generator-annprocess</artifactId>
            <scope>provided</scope>
        </dependency>
    </dependencies>

</project>