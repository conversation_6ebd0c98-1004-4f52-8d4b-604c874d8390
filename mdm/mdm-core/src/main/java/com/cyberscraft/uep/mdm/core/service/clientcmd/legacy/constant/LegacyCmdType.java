package com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.constant;

/***
 *
 * @date 2021-11-25
 * <AUTHOR>
 ***/
public enum LegacyCmdType {
    IOS_DEVICE_UPDATE(101),
    IOS_LOCATION(102),
    IOS_LOCK(103),
    IOS_DEL_PWD(104),
    I<PERSON>_SEND_MSG(105),
    IOS_PLAY_SOUND(106),
    IOS_DEVICE_CLEAR(107),
    IOS_EDATA_CLEAR(108),
    IOS_POLICY(109),
    IOS_APP_UPDATE_NOTIFY(110),
    IOS_APP_ADD_NOTIFY(111),
    IOS_DOC_UPDATE_NOTIFY(112),
    IOS_DOC_ADD_NOTIFY(113),
    IOS_APP_INSTALL(114),
    IOS_APP_UNINSTALL(115),
    IOS_DEL_POLICY(116),
    IOS_APP_LIST(117),
    <PERSON><PERSON>_DEVICE_SECURITYINFO(118),
    IOS_POLICY_LIST(119),
    IOS_MANAGED_APP_LIST(120),
    IOS_PUSH_BLACK_WHITE_LIST(121),
    IOS_PUSH_VIOLATION(122),
    IOS_PUSH_APPCONF(123),
    IOS_VIOLATION_FORBID(130),
    IOS_APP_INSTALL_UNACTIVATED_MDM(131),
    IOS_DIAGNOSE_LOG(138),
    IOS_MDM_UPGRADE(140),
    IOS_FENCE_POLICY(143),
    IOS_INSTALL_FENCEPROFILE(144),
    IOS_DEL_FENCEPROFILE(145),
    IOS_PUSH_SERVICE(146),
    IOS_APP_CONF(147),
    IOS_WALLPAPER(148),
    IOS_BLACK_WHITE_POLICY(149),
    AN_DEVICE_UPDATE(201),
    AN_LOCATION(202),
    AN_LOCK(203),
    AN_DEL_PWD(204),
    AN_SEND_MSG(205),
    AN_PLAY_SOUND(206),
    AN_DEVICE_CLEAR(207),
    AN_EDATA_CLEAR(208),
    AN_POLICY(209),
    AN_APP_UPDATE_NOTIFY(210),
    AN_APP_ADD_NOTIFY(211),
    AN_DOC_UPDATE_NOTIFY(212),
    AN_DOC_ADD_NOTIFY(213),
    AN_APP_INSTALL(214),
    AN_APP_UNINSTALL(215),
    AN_DEL_POLICY(216),
    AN_APP_LIST(218),
    AN_DEVICE_ELIMINATED(219),
    AN_UPLOAD_POLICY(220),
    AN_SET_APP_ENABLE(221),
    AN_MEM_SET_CONFIG(222),
    AN_PUSH_BLACK_WHITE_LIST(223),
    AN_PUSH_VIOLATION(224),
    AN_PUSH_APPCONF(225),
    AN_VIOLATION_FORBID(230),
    IOS_DEVICE_INFORMATION(217),
    AN_KNOX_DISABLE(231),
    AN_push_license(232),
    AN_DEVICE_RESTART(233),
    AN_DEVICE_SHUTDOWN(234),
    AN_APP_URL_INSTALL(235),
    AN_APP_URL_UNINSTALL(236),
    AN_PUSH_URL_BLACK_WHITE_LIST(237),
    AN_DIAGNOSE_LOG(238),
    AN_MDM_UPGRADE(240),
    RESEND_WAKEUP(999),
    AN_REMOTE_DESKTOP_CTRL(242),
    AN_PUSH_SERVICE(246),
    AN_SET_DEVICE_CONF(247),
    AN_TRANSMISSION_PIP(250),
    AN_DEVICE_CLEARAPPDATA(226),

    WN_LOCATION(502),
    WN_LOCK(503),
    WN_DEL_PWD(504),
    WN_SEND_MSG(505),
    WN_PLAY_SOUND(506),
    WN_DEVICE_CLEAR(507),
    WN_EDATA_CLEAR(508),
    WN_POLICY(509),
    WN_DEL_POLICY(516),
    WN_EDATA_NOTIFY(550),

    TDOS_DEVICE_UPDATE(601),
    TDOS_LOCATION(602),
    TDOS_LOCK(603),
    TDOS_DEL_PWD(604),//清除密码（解锁）
    TDOS_SEND_MSG(605),
    TDOS_PLAY_SOUND(606),
    TDOS_DEVICE_CLEAR(607),
    TDOS_EDATA_CLEAR(608),
    TDOS_POLICY(609),
    TDOS_APP_INSTALL(614),
    TDOS_APP_UNINSTALL(615),
    TDOS_DEL_POLICY(616),
    TDOS_APP_LIST(618),
    TDOS_PUSH_VIOLATION(624), //推送合规
    TDOS_DEVICE_RESTART(633), //远程重启
    TDOS_DEVICE_SHUTDOWN(634),//远程关机
    TDOS_SET_DEVICE_CONF(647),//流量配额
    TDOS_SCREEN_SHOT(651),//远程截屏
    TDOS_PICTURE(652),//远程拍照
    TDOS_DUALSWITHC(653);//鼎桥双域

    private final int value;

    LegacyCmdType(int value) {
        this.value = value;
    }

    /**
     * Get the integer value of this enum value, as defined in the Thrift IDL.
     */
    public int getValue() {
        return value;
    }

}
