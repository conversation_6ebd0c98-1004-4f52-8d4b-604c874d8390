package com.cyberscraft.uep.mdm.core.service.config.impl;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.cyberscraft.uep.common.constant.SysConstant;
import com.cyberscraft.uep.mdm.api.dto.basic.LocationInfoVO;
import com.cyberscraft.uep.mdm.core.constant.ConfigConstant;
import com.cyberscraft.uep.mdm.core.constant.MapApiType;
import com.cyberscraft.uep.mdm.core.constant.TenantConfigFileConstant;
import com.cyberscraft.uep.mdm.core.dbo.SysConfigDBO;
import com.cyberscraft.uep.mdm.core.domain.basic.LocationCfg;
import com.cyberscraft.uep.mdm.core.entity.SysConfigEntity;
import com.cyberscraft.uep.mdm.core.exception.MdmException;
import com.cyberscraft.uep.mdm.core.service.config.ITenantConfigService;
import com.cyberscraft.uep.mdm.core.service.config.transfer.ILocationTransfer;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/***
 *
 * @date 2021-09-16
 * <AUTHOR>
 ***/
@Service
public class TenantConfigServiceImpl implements ITenantConfigService {

    @Resource
    private SysConfigDBO sysConfigDBO;

    @Resource
    private ILocationTransfer locationTransfer;

    /***
     * 本地文件+Key缓存
     */
    Cache<String, SysConfigEntity> cache = Caffeine.newBuilder()
            .expireAfterWrite(60, TimeUnit.MINUTES)
            .maximumSize(10000)
            .build();

    /***
     * 本地按照文件缓存
     */
    Cache<String, Map<String, String>> cacheFiles = Caffeine.newBuilder()
            .expireAfterWrite(60, TimeUnit.MINUTES)
            .maximumSize(10000)
            .build();

    @Override
    public SysConfigEntity getSystemConfig(final String key, final String configFileName) throws MdmException {
        //return sysConfigDBO.getByFileAndKey(configFileName, key);
        String tenantId = TenantHolder.getTenantCode();

        String cacheKey = tenantId + "-" + configFileName + "-" + key;
        SysConfigEntity obj = cache.get(cacheKey, k -> sysConfigDBO.getByFileAndKey(configFileName, key));
        return obj;
    }


//    @Override
//    public String getConfigValue(String key) throws MdmException {
//
//        return null;
//    }

    @Override
    public String getConfigValue(String key, String configFileName) throws MdmException {
        SysConfigEntity entity = getSystemConfig(key, configFileName);
        return entity != null ? entity.getPropValue() : null;
    }

    @Override
    public Map<String, String> getConfigsByFileName(String configFileName) throws MdmException {
        String tenantId = TenantHolder.getTenantCode();

        String cacheKey = tenantId + "-" + configFileName;
        Map<String, String> retMap = cacheFiles.get(cacheKey, key -> {
            List<SysConfigEntity> list = sysConfigDBO.getListByFile(configFileName);
            if (list == null || list.size() == 0) {
                return null;
            }
            Map<String, String> ret = new HashMap<>(list.size());
            for (SysConfigEntity obj : list) {
                if (StringUtils.isNotBlank(obj.getPropKey())) {
                    ret.put(obj.getPropKey(), obj.getPropValue());
                }
            }
            return ret;
        });
        return retMap;
    }

    @Override
    public boolean ConfigExistFlag(String key, String configFileName) throws MdmException {
        return getSystemConfig(key, configFileName) != null;
    }

    @Override
    public void updateConfigValue(String key, String configFileName, String value) throws MdmException {
        SysConfigEntity obj = getSystemConfig(key, configFileName);
        if (obj != null) {
            obj.setPropValue(value);
            this.sysConfigDBO.updateById(obj);
        }
    }

    @Override
    public void batchUpdateConfigValue(String configFileName, Map<String, String> configMap) throws MdmException {
        List<SysConfigEntity> list = sysConfigDBO.getListByFile(configFileName);
        if (list == null || list.size() == 0) {
            return;
        }
        for (SysConfigEntity obj : list) {
            if (configMap.containsKey(obj.getPropKey())) {
                obj.setPropValue(configMap.get(obj.getPropKey()));
                this.sysConfigDBO.updateById(obj);
            }
        }
    }

    @Override
    public boolean isAuthDoubleFactor() throws MdmException {
        String doubleFactor = getConfigValue(ConfigConstant.AUTH_DOUBLE_FACTOR_CFG_ITEM, TenantConfigFileConstant.SYS_CONF);
        return SysConstant.TRUE.equals(doubleFactor);

//        String doubleFactor = getConfigValue(ConfigConstant.AUTH_DOUBLE_FACTOR_CFG_ITEM);
//        return SysConstant.TRUE.equals(doubleFactor);
    }

    @Override
    public String getLanguage() throws MdmException {
        return getConfigValue(ConfigConstant.LANG_CFG_ITEM, TenantConfigFileConstant.SYS_CONF);
//        return getConfigValue(ConfigConstant.LANG_CFG_ITEM);
    }

    @Override
    public int getAllowRootOrJailbreak() throws MdmException {
        String value = getConfigValue(ConfigConstant.ALLOW_ROOT_CFG_ITEM, TenantConfigFileConstant.SYS_CONF);
        return toInt(value, 0);
    }

    @Override
    public int getAllowMaxActivateNum() throws MdmException {
        String value = getConfigValue(ConfigConstant.ALLOW_MAX_ACTIVE_NUM_CFG_ITEM, TenantConfigFileConstant.SYS_CONF);
        return toInt(value, 1);
    }

    @Override
    public int getAllowInconsistentActivate() throws MdmException {
        String value = getConfigValue(ConfigConstant.ALLOW_INCONSISTENT_ACTIVATE_CFG_ITEM, TenantConfigFileConstant.SYS_CONF);
        return toInt(value, 0);
    }

    @Override
    public int getAllowUnregistActivate() throws MdmException {
        String value = getConfigValue(ConfigConstant.ALLOW_UNREGISTE_ACTIVATE_CFG_ITEM, TenantConfigFileConstant.SYS_CONF);
        return toInt(value, SysConstant.TRUE_VALUE);
    }

    @Override
    public int getRelationshipDefault() throws MdmException {
        String value = getConfigValue(ConfigConstant.RELATIONSHIP_DEFAULT_CFG_ITEM, TenantConfigFileConstant.SYS_CONF);
        return toInt(value, 0);
    }

    /***
     *100米
     */
    private final static int DEFAULT_LOCATION_DISTANCE = 100;
    /***
     * 是否开启定期定位
     */
    private final static int DEFAULT_LOCATION_PERIOD = SysConstant.FALSE_VALUE;
    /***
     *60分钟
     */
    private final static int DEFAULT_LOCATION_TIME = 60;

    @Override
    public LocationCfg getLocationCfg() throws MdmException {
        Map<String, String> map = getConfigsByFileName(TenantConfigFileConstant.SYS_CONF);
        return getLocationCfg(map);
    }

    public LocationCfg getLocationCfg(Map<String, String> map) throws MdmException {
        //Map<String, String> map = getConfigsByFileName(TenantConfigFileConstant.SYS_CONF);
        LocationCfg ret = new LocationCfg();
        //米
        String distance = map != null ? map.get(ConfigConstant.LOCATION_LOCATIONDISTANCE_CFG_ITEM) : null;
        ret.setLocationDistance(toInt(distance, DEFAULT_LOCATION_DISTANCE));
        //分钟
        String time = map != null ? map.get(ConfigConstant.LOCATION_LOCATIONTIME_CFG_ITEM) : null;
        ret.setLocationTime(toInt(time, DEFAULT_LOCATION_TIME));
        //定期定位
        String period = map != null ? map.get(ConfigConstant.LOCATION_MAPPERIOD_CFG_ITEM) : null;
        ret.setMapPeriod(toInt(period, DEFAULT_LOCATION_PERIOD));
        //定期定位
        String mapApi = map != null ? map.get(ConfigConstant.LOCATION_MAPAPI_CFG_ITEM) : null;
        ret.setMapApi(toInt(mapApi, MapApiType.BAIDU.getCode()));

        //是否开启定位
        String enableLocation = map != null ? map.get(ConfigConstant.LOCATION_ENABLED_CFG_ITEM): null;
        ret.setEnabled(toInt(enableLocation, SysConstant.FALSE_VALUE));
        return ret;
    /**/}


    @Override
    public LocationInfoVO getLocationInfo() throws MdmException {
        return locationTransfer.domainToInfoVO(getLocationCfg());
    }

    @Override
    public int getAndroidFenceInterval() throws MdmException {
        String value = getConfigValue(ConfigConstant.ANDROIDFENCE_INTERVAL_CFG_ITEM, TenantConfigFileConstant.SYS_CONF);
        return toInt(value, 10);
    }

    @Override
    public int getIosFenceInterval() throws MdmException {
        String value = getConfigValue(ConfigConstant.IOSFENCE_INTERVAL_CFG_ITEM, TenantConfigFileConstant.SYS_CONF);
        return toInt(value, 10);
    }

    @Override
    public String getEmailServerFeatureId() throws MdmException {
        return getConfigValue(ConfigConstant.EMAILSERVER_FEATUREID_CFG_ITEM, TenantConfigFileConstant.SYS_CONF);
    }

    @Override
    public Map<String, Object> getActiveAndDeviceRelatedConfig() {

        Map<String, String> map = getConfigsByFileName(TenantConfigFileConstant.SYS_CONF);


        //米
        String distance = map != null ? map.get(ConfigConstant.LOCATION_LOCATIONDISTANCE_CFG_ITEM) : null;
        //分钟
        String time = map != null ? map.get(ConfigConstant.LOCATION_LOCATIONTIME_CFG_ITEM) : null;
        //定期定位
        String period = map != null ? map.get(ConfigConstant.LOCATION_MAPPERIOD_CFG_ITEM) : null;
        //定期定位
        String mapApi = map != null ? map.get(ConfigConstant.LOCATION_MAPAPI_CFG_ITEM) : null;

        //是否开启定位

        // 地图引擎类型
        Integer mapType = toInt(mapApi, MapApiType.BAIDU.getCode());//// (pMap.get("sys.conf.location.map.api") != null) ? pMap.get("sys.conf.location.map.api") : Constant.MAP_API_TYPE_BAIDU_MAP;
        // 设备定位设置[频率(分钟)和距离(米)]
        Integer locationDistance = toInt(distance, DEFAULT_LOCATION_DISTANCE); //(pMap.get("sys.conf.location.locationDistance") != null) ? pMap.get("sys.conf.location.locationDistance") : "100";
        Integer locationTime = toInt(time, DEFAULT_LOCATION_TIME);//(pMap.get("sys.conf.location.locationTime") != null) ? pMap.get("sys.conf.location.locationTime") : "60";
        // 定期定位
        Integer periodLocation = toInt(period, DEFAULT_LOCATION_TIME);//(pMap.get("sys.conf.location.map.period") != null) ? pMap.get("sys.conf.location.map.period") : "0";
        Integer locationEnable = toInt(map.get(ConfigConstant.LOCATION_ENABLED_CFG_ITEM), SysConstant.FALSE_VALUE);// Optional.ofNullable(pMap.get("sys.conf.location.enable")).orElse("0");


        // 注册与激活参数
        Integer allowRootOrJailbreak = toInt(map.get(ConfigConstant.ALLOW_ROOT_CFG_ITEM), SysConstant.FALSE_VALUE);// (pMap.get("sys.conf.base.allowRoot") != null) ? pMap.get("sys.conf.base.allowRoot") : "";
        Integer sysAllowUnregistActivate = toInt(map.get(ConfigConstant.ALLOW_UNREGISTE_ACTIVATE_CFG_ITEM), SysConstant.TRUE_VALUE);//tenantConfigService.getAllowUnregistActivate();//  (pMap.get("sys.conf.base.unregist.activate.allow") != null) ? pMap.get("sys.conf.base.unregist.activate.allow") : "";
        Integer sysRelationshipDefault = toInt(map.get(ConfigConstant.RELATIONSHIP_DEFAULT_CFG_ITEM), SysConstant.FALSE_VALUE);//tenantConfigService.getRelationshipDefault();// (pMap.get("sys.conf.base.relationship.default") != null) ? pMap.get("sys.conf.base.relationship.default") : "";
        Integer sysBaseInfoNotSame = toInt(map.get(ConfigConstant.ALLOW_INCONSISTENT_ACTIVATE_CFG_ITEM), SysConstant.TRUE_VALUE);//tenantConfigService.getAllowInconsistentActivate();// (pMap.get("sys.conf.base.info.notSame") != null) ? pMap.get("sys.conf.base.info.notSame") : "";
        Integer allowMaxActivateNum = toInt(map.get(ConfigConstant.ALLOW_MAX_ACTIVE_NUM_CFG_ITEM), 1);//tenantConfigService.getAllowMaxActivateNum();//(pMap.get("sys.conf.base.maxRegNum") != null) ? pMap.get("sys.conf.base.maxRegNum") : "1";


        String language = map.get(ConfigConstant.LANG_CFG_ITEM);//tenantConfigService.getLanguage();//com.cyberscraft.mdm.common.PropertiesUtil.readValue("sysconf.properties", "sys.conf.lang");
        //Android围栏策略更新频率（分钟）
        int androidFenceUpdateCircle = toInt(map.get(ConfigConstant.ANDROIDFENCE_INTERVAL_CFG_ITEM), 10);// tenantConfigService.getAndroidFenceInterval();// (pMap.get("sys.conf.policy.android.fence.update.circle") != null) ? pMap.get("sys.conf.policy.android.fence.update.circle") : "10";
        //Ios围栏策略更新频率（分钟）
        int iosFenceUpdateCircle = toInt(map.get(ConfigConstant.IOSFENCE_INTERVAL_CFG_ITEM), 10);// tenantConfigService.getIosFenceInterval();//(pMap.get("sys.conf.policy.ios.fence.update.circle") != null) ? pMap.get("sys.conf.policy.ios.fence.update.circle") : "10";

        String emailServerFeatureId = map.get(ConfigConstant.EMAILSERVER_FEATUREID_CFG_ITEM);// tenantConfigService.getEmailServerFeatureId();// pMap.get("emailServer.featureID");
        emailServerFeatureId = emailServerFeatureId != null ? emailServerFeatureId : "";


        Map<String, Object> json = new HashMap<>();
        json.put("locationEnable", locationEnable);
        // 地图引擎类型
        json.put("mapViewType", mapType);
        // 设备定位设置[频率(分钟)和距离(米)]
        json.put("locationDistance", locationDistance);
        json.put("locationTime", locationTime);
        // 定期定位
        json.put("periodLocationFlag", periodLocation);


        // 是否允许root/越狱
        json.put("allowRootOrJarlbreak", allowRootOrJailbreak);
        //单用户最大激活设备数量
        json.put("allowMaxActivateNum", allowMaxActivateNum);
        // 是否允许未注册设备激活
        json.put("allowUnregisteredActivate", sysAllowUnregistActivate);
        // 默认设备归属
        json.put("relationshipDefault", sysRelationshipDefault);
        // 设备激活与设备注册时的信息不一致时是否允许激活
        json.put("allowInconsistentActivate", sysBaseInfoNotSame);

        json.put("sysConfLang", language);
        //Android围栏策略更新频率（分钟）
        json.put("circleAnFenceInterval", androidFenceUpdateCircle);
        //Ios围栏策略更新频率（分钟）
        json.put("circleIosFenceInterval", iosFenceUpdateCircle);
        json.put("emailServerFeatureId", emailServerFeatureId);

        return json;
    }

    /***
     *
     * @param value
     * @param defaultValue
     * @return
     */
    private int toInt(String value, int defaultValue) {
        if (StringUtils.isBlank(value)) {
            return defaultValue;
        }
        return NumberUtils.toInt(value, defaultValue);
    }

    /****
     *
     * @param value
     * @param defaultValue
     * @return
     */
    private long toLong(String value, long defaultValue) {
        if (StringUtils.isBlank(value)) {
            return defaultValue;
        }
        return NumberUtils.toLong(value, defaultValue);
    }
}
