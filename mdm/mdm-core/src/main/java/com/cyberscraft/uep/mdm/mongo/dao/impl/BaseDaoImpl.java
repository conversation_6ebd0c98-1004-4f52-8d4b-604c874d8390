package com.cyberscraft.uep.mdm.mongo.dao.impl;

import com.cyberscraft.uep.mdm.mongo.dao.BaseDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import javax.annotation.Resource;
import java.io.Serializable;
import java.lang.reflect.Field;
import java.lang.reflect.Method;

/**
 * @Author: liuanyang
 * @Date: 2021/9/29
 */
public class BaseDaoImpl<T> implements BaseDao<T> {

    private Logger LOG = LoggerFactory.getLogger(this.getClass());

    @Resource
    private MongoTemplate mongoTemplate;

    /**
     * 插入
     *
     * @param entity 实体对象
     */
    @Override
    public T insert(T entity) {
        return mongoTemplate.insert(entity);
    }

    /**
     * 修改或插入
     *
     * @param entity 实体对象
     */
    @Override
    public T save(T entity)
    {
        return mongoTemplate.save(entity);
    }

    /**
     * 根据id修改
     *
     * @param id
     * @param entity
     * @param ignoreNull 是否忽略属性值为null的修改
     */
    @Override
    public void updateById(Serializable id, T entity, Boolean ignoreNull) {
        if(id == null || entity == null){
            return;
        }

        if(!ignoreNull){
            mongoTemplate.save(entity);
        }else {
            Query query = new Query(Criteria.where("id").is(id));
            Update update = new Update();

            Field[] declaredFields = entity.getClass().getDeclaredFields();
            for (Field declaredField : declaredFields) {
                String name = declaredField.getName();
                String getMethodName = "get"+name.substring(0,1).toUpperCase()+name.substring(1);

                try {
                    Method m = entity.getClass().getMethod(getMethodName);
                    Object value = m.invoke(entity);
                    if(value != null){
                        update.set(name, value);
                    }
                } catch (NoSuchMethodException e) {
                    LOG.warn("NoSuchMethod: "+getMethodName);
                } catch (Exception e) {
                    LOG.error("error", e);
                }
            }

            mongoTemplate.upsert(query, update, entity.getClass());
        }
    }

    /**
     * 删除
     *
     * @param entity
     */
    @Override
    public void remove(T entity) {
        mongoTemplate.remove(entity);
    }

    /**
     * 根据 ID 查询
     *
     * @param id 主键ID
     */
    @Override
    public T getById(Serializable id, Class<T> entityClass) {
        Query query = new Query(Criteria.where("id").is(id));
        return mongoTemplate.findOne(query, entityClass);
    }
}
