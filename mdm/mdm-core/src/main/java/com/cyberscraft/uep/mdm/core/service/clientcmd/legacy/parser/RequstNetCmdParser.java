package com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.parser;

import com.cyberscraft.uep.common.bean.Result;
import com.cyberscraft.uep.common.enums.ExceptionCodeEnum;
import com.cyberscraft.uep.common.enums.Platform;
import com.cyberscraft.uep.mdm.api.constant.ClientCmdType;
import com.cyberscraft.uep.mdm.api.dto.clientcmd.ClientCmdRequestDto;
import com.cyberscraft.uep.mdm.api.dto.clientcmd.RequestNetCmdBody;
import com.cyberscraft.uep.mdm.core.domain.clientcmd.LegacyClientCmdRequest;
import com.cyberscraft.uep.mdm.core.domain.oauth2.AccessToken;
import com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.ILegacyCmdParser;
import com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.constant.CodeType;
import com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.constant.LegacyCommandCode;
import com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.constant.TemplatePropertisConstant;
import com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.constant.TemplateURIConstant;
import com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.domain.NextAction;
import com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.domain.OnNetStraCircle;
import com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.domain.OperID;
import com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.domain.Reply;
import com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.sax.RequestBeanSaxHandler;
import org.springframework.stereotype.Component;
import org.thymeleaf.context.Context;

import java.util.HashSet;
import java.util.Set;

/***
 *
 * @date 2021-11-25
 * <AUTHOR>
 ***/
@Component
public class RequstNetCmdParser extends AbstractCmdParser implements ILegacyCmdParser {

    /***
     *
     */
    private final static Set<String> SUPPORTED_CMD_CODES = new HashSet<>();


    static {
        SUPPORTED_CMD_CODES.add(String.valueOf(LegacyCommandCode.REQUEST_NET.getCmdCode()));
    }

    @Override
    public Set<String> getCommandCodes() {
        return SUPPORTED_CMD_CODES;
    }

    @Override
    public boolean isSupported(String cmdCode) {
        return SUPPORTED_CMD_CODES.contains(cmdCode);
    }

    @Override
    public ClientCmdRequestDto parseClientCmdRequestDto(LegacyClientCmdRequest request, Platform platform) {
        RequestNetCmdBody cmdBody = new RequestNetCmdBody();
//        Element root = getRootElement(request.getCommandBody());
//        OnNetStraCircle onNetStraCircle = parserObject(root.getChild("OnNetStraCircle"), OnNetStraCircle.class);
//        OperID operID = getOperIDFromDom(root);

        RequestBeanSaxHandler handler = getRequestBeanHandler(request.getCommandBody());
        OperID operID = handler.getOperID();
        OnNetStraCircle onNetStraCircle = handler.getOnNetStraCircle();

        //验证访问Token
        AccessToken token = validateAccessToken(operID.getClientUID());


        cmdBody.setUdid(token.getUdid());
        cmdBody.setUserId(token.getUserId());
        cmdBody.setUemVersion(operID.getUemVersion());
        cmdBody.setClientVersion(operID.getClientVersion());
        cmdBody.setClientBuildNum(operID.getClientBuildNum());

        if (onNetStraCircle != null) {
            cmdBody.setVersion(onNetStraCircle.getVersion());
        }

        ClientCmdRequestDto cmdRequestDto = new ClientCmdRequestDto();
        cmdRequestDto.setCmdBody(cmdBody);
        cmdRequestDto.setType(ClientCmdType.REQUEST_NET.getCode());
        cmdRequestDto.setPlatform(platform);
        cmdRequestDto.setUdid(token.getUdid());
        return cmdRequestDto;
    }

    @Override
    public String parseOutput(Result result, Platform platform, ClientCmdRequestDto requestDto) {
        Reply reply = new Reply();
        OperID operID = new OperID();
        if (ExceptionCodeEnum.SUCCESS.getCode().equalsIgnoreCase(result.getCode())) {
            operID.setResult(String.valueOf(CodeType.SUCCESS.getValue()));
        } else {
            operID.setResult(String.valueOf(CodeType.FAIL.getValue()));
            operID.setExtra(result.getCode() + result.getMessage());
        }
        reply.setOperID(operID);
        Context context = new Context();
        NextAction nextAction = new NextAction();
//        nextAction.setExcuteFlag(String.valueOf(LegacyCommandCode.GET_CIRCLEOPER.getCmdCode()));
//        nextAction.setExcuteType(String.valueOf(ExcuteType.NOW.getValue()));
//        nextAction.setFunctionCode(LegacyCommandCode.GET_CIRCLEOPER.getCmdCode());
//        nextAction.setNextFlowNum(String.valueOf(ThreadLocalRandom.current().nextInt(1, Integer.MAX_VALUE)));
        context.setVariable(TemplatePropertisConstant.NEXTACTION, nextAction);

        context.setVariable(TemplatePropertisConstant.REPLY, reply);
        String templateName = getTemplateName(platform);
        return legacyContentRender.render(context, templateName);
    }

    /***
     *
     * @param platform
     * @return
     */
    private String getTemplateName(Platform platform) {
        String templateName = TemplateURIConstant.NET_REQUEST;
        return templateName;
    }
}
