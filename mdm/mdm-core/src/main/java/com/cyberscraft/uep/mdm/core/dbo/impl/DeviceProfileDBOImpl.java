package com.cyberscraft.uep.mdm.core.dbo.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cyberscraft.uep.common.dto.PageView;
import com.cyberscraft.uep.common.dto.Pagination;
import com.cyberscraft.uep.common.util.PagingUtil;
import com.cyberscraft.uep.mdm.api.dto.policy.QueryDeviceProfileDto;
import com.cyberscraft.uep.mdm.core.dao.DeviceProfileDao;
import com.cyberscraft.uep.mdm.core.dbo.DeviceProfileDBO;
import com.cyberscraft.uep.mdm.core.domain.device.DevicePolicyCountItem;
import com.cyberscraft.uep.mdm.core.entity.DeviceProfileEntity;
import com.cyberscraft.uep.mdm.core.exception.MdmException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 设备与配置文件策略关联表 服务实现类
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2019-08-28
 */
@Service
public class DeviceProfileDBOImpl extends ServiceImpl<DeviceProfileDao, DeviceProfileEntity> implements DeviceProfileDBO {

    @Resource
    private DeviceProfileDao deviceProfileDao;

    @Override
    public List<DevicePolicyCountItem> countDevicePolicyNums(List<Long> deviceIds) throws MdmException {
        if(deviceIds==null || deviceIds.size()==0){
            return null;
        }
        return deviceProfileDao.countDevicePolicyNums(deviceIds);
    }

    @Override
    public List<DeviceProfileEntity> getByPolicyAndDevices(String policyId, Set<Long> deviceIds) {
        return getByPolicyAndDevicesAndStatus(policyId, deviceIds, null);
    }

    @Override
    public PageView<DeviceProfileEntity> pageDevicePolicy(Pagination<QueryDeviceProfileDto> page) throws MdmException {
        IPage<DeviceProfileEntity> myPage = PagingUtil.toMybatisPage(page);
        String[] selectStr = page.getRequestAttrs() != null ? page.getRequestAttrs().toArray(new String[0]) : new String[0];
        QueryWrapper<DeviceProfileEntity> queryWrapper = new QueryWrapper<DeviceProfileEntity>().select(selectStr);
        queryWrapper.orderByDesc("status");
        LambdaQueryWrapper<DeviceProfileEntity> lambdaQueryWrapper = queryWrapper.lambda();

        QueryDeviceProfileDto queryDto = page.getQueryDto();
        if(queryDto.getPolicyId() != null) {
            lambdaQueryWrapper.eq(DeviceProfileEntity::getPolicyId, queryDto.getPolicyId());
        }

        if(queryDto.getDeviceIds() != null && queryDto.getDeviceIds().size() > 0) {
            lambdaQueryWrapper.in(DeviceProfileEntity::getDeviceId, queryDto.getDeviceIds());
        }

        if(queryDto.getStatus() != null) {
            lambdaQueryWrapper.eq(DeviceProfileEntity::getStatus, queryDto.getStatus());
        }

        IPage<DeviceProfileEntity> temp = this.page(myPage, lambdaQueryWrapper);
        return PagingUtil.toPageView(temp);
    }

    @Override
    public void updateDistributeTime(String policyId, List<Long> deviceIds) {
        LambdaQueryWrapper<DeviceProfileEntity> queryWrapper = new QueryWrapper().lambda();
        queryWrapper.eq(DeviceProfileEntity::getPolicyId, policyId);
        queryWrapper.in(DeviceProfileEntity::getDeviceId, deviceIds);

        DeviceProfileEntity deviceProfileEntity = new DeviceProfileEntity();
        deviceProfileEntity.setStatus(0);
        deviceProfileEntity.setCreateTime(LocalDateTime.now());
        deviceProfileEntity.setUpdateTime(null);
        update(deviceProfileEntity, queryWrapper);
    }

    @Override
    public DeviceProfileEntity getByPolicyIdAndDeviceId(String policyId, Long deviceId) {
        LambdaQueryWrapper<DeviceProfileEntity> deviceProfileQuery = Wrappers.lambdaQuery();
        deviceProfileQuery.eq(DeviceProfileEntity::getPolicyId, policyId);
        deviceProfileQuery.eq(DeviceProfileEntity::getDeviceId, deviceId);
        return this.getOne(deviceProfileQuery);
    }

    @Override
    public List<DeviceProfileEntity> getByPolicyAndDevicesAndStatus(String policyId, Set<Long> deviceIds, Integer status) {
        LambdaQueryWrapper<DeviceProfileEntity> deviceProfileQuery = Wrappers.lambdaQuery();
        if (policyId != null && policyId.length() > 0) {
            deviceProfileQuery.eq(DeviceProfileEntity::getPolicyId, policyId);
        }
        if (deviceIds != null && deviceIds.size() > 0) {
            deviceProfileQuery.in(DeviceProfileEntity::getDeviceId, deviceIds);
        }
        if (status != null) {
            deviceProfileQuery.eq(DeviceProfileEntity::getStatus, status);
        }
        return list(deviceProfileQuery);
    }

    @Override
    public List<Long> getDeviceIdsByPolicyId(String policyId) {
        LambdaQueryWrapper<DeviceProfileEntity> deviceProfileQuery = new LambdaQueryWrapper<DeviceProfileEntity>()
                .select(DeviceProfileEntity::getDeviceId)
                .eq(DeviceProfileEntity::getPolicyId, policyId);
        List<DeviceProfileEntity> list = list(deviceProfileQuery);
        return list.stream().map(e->e.getDeviceId()).collect(Collectors.toList());
    }

    @Override
    public boolean removeByPolicyId(String policyId) {
        return remove(new QueryWrapper<DeviceProfileEntity>().lambda().eq(DeviceProfileEntity::getPolicyId, policyId));
    }
    

    @Override
    public boolean removeByDeviceIds(List<Long> deviceIds) {
        if (deviceIds != null && !deviceIds.isEmpty()) {
            return removeByPolicyId(null, deviceIds);
        }
        return false;
    }

    @Override
    public boolean removeByUdid(String udid) {
        LambdaQueryWrapper<DeviceProfileEntity> queryWrapper = new QueryWrapper<DeviceProfileEntity>()
                .lambda();
        if (StringUtils.isNotEmpty(udid)) {
            queryWrapper.eq(DeviceProfileEntity::getUdid, udid);
            return remove(queryWrapper);
        }

        return false;
    }

    @Override
    public boolean removeByPolicyId(String policyId, List<Long> deviceIds) {
        LambdaQueryWrapper<DeviceProfileEntity> queryWrapper = new QueryWrapper<DeviceProfileEntity>()
                .lambda();
        if (policyId != null && policyId.length() > 0) {
            queryWrapper.eq(DeviceProfileEntity::getPolicyId, policyId);
        }
        if (deviceIds != null && deviceIds.size() > 0) {
            queryWrapper.in(DeviceProfileEntity::getDeviceId, deviceIds);
        }
        return remove(queryWrapper);
    }

    @Override
    public boolean removeByPolicyIdsDeviceIds(List<String> policyIds, Collection<Long> deviceIds) {
        LambdaQueryWrapper<DeviceProfileEntity> queryWrapper = new QueryWrapper<DeviceProfileEntity>()
                .lambda();
        if (policyIds != null && policyIds.size() > 0) {
            queryWrapper.in(DeviceProfileEntity::getPolicyId, policyIds);
        }
        if (deviceIds != null && deviceIds.size() > 0) {
            queryWrapper.in(DeviceProfileEntity::getDeviceId, deviceIds);
        }
        return remove(queryWrapper);
    }

}
