package com.cyberscraft.uep.mdm.core.service.basic.parser;

import com.cyberscraft.uep.common.enums.Platform;
import com.cyberscraft.uep.mdm.api.constant.Constant;
import com.cyberscraft.uep.mdm.core.constant.AppDomainTypeConstant;
import com.cyberscraft.uep.mdm.core.constant.AppTypeConstant;
import com.cyberscraft.uep.mdm.core.constant.LanguageConstant;
import com.cyberscraft.uep.mdm.core.domain.basic.AppInfo;
import com.cyberscraft.uep.mdm.core.domain.basic.AppParseRequest;
import com.cyberscraft.uep.mdm.core.exception.MdmException;
import com.cyberscraft.uep.mdm.core.service.basic.IAppParser;
import com.cyberscraft.uep.mdm.core.util.VersionCodeUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipArchiveInputStream;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.Node;
import org.dom4j.io.SAXReader;
import org.springframework.stereotype.Component;

import java.io.*;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/***
 *
 * @date 2021-12-14
 * <AUTHOR>
 ***/
@Component
public class IOSFileParser extends AbastractFileParser implements IAppParser {


    private final static Set<Integer> SUPPORTED_PLATFORM = new HashSet<>();


    static {
        SUPPORTED_PLATFORM.add(Platform.IOS.getValue());
        SUPPORTED_PLATFORM.add(Platform.MACOS.getValue());
    }

    @Override
    public Set<Integer> getSupportedPlatform() {
        return SUPPORTED_PLATFORM;
    }

    @Override
    public boolean isSupported(Integer platform) {
        return SUPPORTED_PLATFORM.contains(platform);
    }

    @Override
    public AppInfo parse(AppParseRequest request) throws MdmException {
        AppInfo ret = new AppInfo();
        File lecFile = new File(request.getFilePath());
        ret.setSize(getAppSize(lecFile, request.getFilePath()));
        String filePath = lecFile.getParentFile().getPath();
        //ZipArchiveInputStream is = null;
        //boolean convertSucc = true;
        try (ZipArchiveInputStream is = new ZipArchiveInputStream(new BufferedInputStream(new FileInputStream(lecFile)));) {
            //is = new ZipArchiveInputStream(new BufferedInputStream(new FileInputStream(lecFile)));
            ZipArchiveEntry entry = null;
            while ((entry = is.getNextZipEntry()) != null) {
                if ("Info.plist".equalsIgnoreCase(FilenameUtils.getName(entry.getName())) && (StringUtils.countMatches(entry.getName(), "/") <= 2)) {
                    //OutputStream os = null;
                    File listFile = new File(filePath + "\\" + "Info.plist");
                    try (OutputStream os = new BufferedOutputStream(new FileOutputStream(listFile))) {
                        //os = new BufferedOutputStream(new FileOutputStream(listFile));
                        IOUtils.copy(is, os);
                    } catch (Exception e) {
                        LOG.error("There are errors when copy info.plist file", e);
                    }
                    try {
                        //TODO 这 一块暂时未拷贝过来进行实现
                    } catch (Exception e) {
                        //convertSucc = false;
                        LOG.error(e.getMessage(), e);
                    }
                } else if ("InfoPlist.strings".equalsIgnoreCase(FilenameUtils.getName(entry.getName()))
                        && (StringUtils.countMatches(entry.getName(), "/") <= 3)
                        && FilenameUtils.getPath(entry.getName()).indexOf("en.lproj") != -1) {
                    ///OutputStream os = null;
                    File listFile = new File(filePath + "\\" + "InfoPlist.strings");
                    try (OutputStream os = new BufferedOutputStream(new FileOutputStream(listFile))) {
                        //os = new BufferedOutputStream(new FileOutputStream(listFile));
                        IOUtils.copy(is, os);
                    } catch (Exception e) {
                        LOG.error("There are errors when copy InfoPlist.strings file", e);
                    }
                    //TODO 这 一块暂时未拷贝过来进行实现
                } else if (entry.getName().endsWith(".app/embedded.mobileprovision")) {
                    //ByteArrayOutputStream stream = readFileToMemory(is);
                    //String plist = getPlistFromMobileProvisionFile(stream);

//                    if (plist != null) {
//                        NSDictionary rootDict = (NSDictionary) PropertyListParser.parse(plist.getBytes());
//                        if (rootDict.get("ExpirationDate") != null){
//                            NSDate date = (NSDate) rootDict.get("ExpirationDate");
//                            vo.setExpirationDate(date.getDate().getTime());
//                        }
//                    }
                }
            }
        } catch (Exception e) {
            LOG.error("There are errors when unzip info.plist file", e);
        }
        //解释IOS xml文件信息到app 信息中
        parseIosXml(filePath + "\\" + "convert.plist", filePath + "\\" + "convert.strings", request, ret);

        if (builtinPkgName.contains(ret.getPkgName()) || ret.getPkgName().startsWith(Constant.BUILTIN_REMOTE_CTRL_PKGNAME)) {
            ret.setDomainType(AppDomainTypeConstant.APP_DOMAIN_TYPE_BUILD);
        }
        if(ret.getPublishTime()==null){
            ret.setPublishTime(LocalDateTime.now());
        }

        return ret;
    }

    /****
     * 转换IOS对应的XML文件
     * @param plistPath
     * @param infoPlistPath
     * @param ret
     */
    void parseIosXml(String plistPath, String infoPlistPath, AppParseRequest request, AppInfo ret) throws MdmException {

        File file = new File(plistPath);
        File infoPlistFile = new File(infoPlistPath);
        if (file == null) {
            LOG.warn("plistPath 对应的文件为空");
            return;
        }
        String language = request.getLanguage();
        if (StringUtils.isBlank(language)) {
            language = LanguageConstant.ZH_CN;
        }
        try {
            SAXReader saxReader = new SAXReader();
            //取消DTD联网验证 避免链接不到网络时候报错
            saxReader.setValidation(false);

            saxReader.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
            saxReader.setFeature("http://xml.org/sax/features/external-general-entities", false);
            saxReader.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
            saxReader.setFeature("http://apache.org/xml/features/nonvalidating/load-external-dtd", false);

            Document document = saxReader.read(file);
            Element rootEle = document.getRootElement();
            List<Node> es = rootEle.selectNodes("/plist/dict/*");
            boolean iconFlag = false;
            String CFBundleDisplayName = "";
            String CFBundleName = "";
            for (int i = 0; i < es.size(); i++) {
                LOG.debug("########" + es.get(i).getText());
                if ("CFBundleIdentifier".equals(es.get(i).getText())) {
                    ret.setPkgName(es.get(i + 1).getText());
                } else if ("CFBundleShortVersionString".equals(es.get(i).getText())) {

                    ret.setVersion(es.get(i + 1).getText());
                    ret.setVersionCode(VersionCodeUtil.parseVersion(es.get(i + 1).getText()));
//            } else if ("CFBundleVersion".equals(es.get(i).getText())) {
//                vo.setAppVersionCode(VersionCodeUtil.parseVersion(es.get(i + 1).getText()));
                } else if ("CFBundleDisplayName".equals(es.get(i).getText())) {
                    CFBundleDisplayName = es.get(i + 1).getText();
                } else if ("CFBundleName".equals(es.get(i).getText())) {
                    CFBundleName = es.get(i + 1).getText();
                } else if ("CFBundleURLTypes".equals(es.get(i).getText())) {
                    LOG.debug(es.get(i + 1).asXML());
                    List<Node> bundleIconsDict = es.get(i + 1).selectNodes("dict/*");
                    if (CollectionUtils.isNotEmpty(bundleIconsDict)) {
                        for (int j = 0; j < bundleIconsDict.size(); j++) {
                            LOG.info("**********" + bundleIconsDict.get(j).getText());
                            if ("CFBundleURLSchemes".equals(bundleIconsDict.get(j).getText())) {
                                List<Node> urlschemaElemList = bundleIconsDict.get(j + 1).selectNodes("*");
                                if (CollectionUtils.isNotEmpty(urlschemaElemList)) {
                                    String urlScheme = urlschemaElemList.get(0).getText();
                                    //ret.setUrlScheme(urlScheme);
                                    if (urlScheme.endsWith(AppTypeConstant.APP_TYPE_IOS_WRAPP)) {
                                        ret.setDomainType(AppDomainTypeConstant.APP_DOMAIN_TYPE_WRAPP);
                                    }

                                }
                                break;
                            }
                        }
                    }
                } else if ("CFBundleIcons".equals(es.get(i).getText())) {
                    List<Node> bundleIconsDict = es.get(i + 1).selectNodes("*");
                    if (CollectionUtils.isNotEmpty(bundleIconsDict)) {
                        for (int j = 0; j < bundleIconsDict.size(); j++) {
                            if ("CFBundlePrimaryIcon".equals(bundleIconsDict.get(j).getText())) {
                                List<Node> iconElemList = bundleIconsDict.get(j + 1).selectNodes("array/string");
                                if (CollectionUtils.isNotEmpty(iconElemList)) {
                                    List<String> matNameList = new ArrayList<String>();
                                    int index = 0;
                                    if (iconElemList.size() > 1) index = 1;
                                    //如果iconElemList有多个值 时不取第一个图片(因为像素可能低)
                                    for (int z = index; z < iconElemList.size(); z++) {
                                        String eText = iconElemList.get(z).getText();
                                        String eText2x = "";
                                        String eText3x = "";
                                        // 有时候图标名会不带后缀，此处追加后缀
                                        if (eText != null && eText.indexOf('.') < 0) {
                                            eText2x = eText + "@2x" + ".png";
                                            eText3x = eText + "@2x" + ".png";
                                            eText = eText + ".png";
                                        } else if (eText != null && eText.indexOf('.') != -1) {
                                            eText2x = eText.substring(0, eText.indexOf('.')) + "@2x" + ".png";
                                            eText3x = eText.substring(0, eText.indexOf('.')) + "@3x" + ".png";
                                        }
                                        matNameList.add(eText);
                                        matNameList.add(eText2x);
                                        matNameList.add(eText3x);
                                    }
                                    //ret.setIconElemList(matNameList);
                                }
                                break;
                            }
                        }
                    }
                    iconFlag = true;
                } else if (!iconFlag && ("CFBundleIconFile".equals(es.get(i).getText()) || "CFBundleIconFiles".equals(es.get(i).getText()))) {
                    if ("CFBundleIconFile".equals(es.get(i).getText())) {
                        String iconName = es.get(i + 1).getText();
                        // 有时候图标名会不带后缀，此处追加后缀
                        if (iconName != null && iconName.indexOf('.') < 0) {
                            iconName = iconName + ".png";
                        }
                        //ret.setIconName(iconName);
                    } else if ("CFBundleIconFiles".equals(es.get(i).getText())) {
                        List<Node> iconElemList = es.get(i + 1).selectNodes("string");
                        if (CollectionUtils.isNotEmpty(iconElemList)) {
                            List<String> matNameList = new ArrayList<String>();
                            int index = 0;
                            if (iconElemList.size() > 1) index = 1;
                            //不取第一个图片(因为像素可能低)
                            for (int z = index; z < iconElemList.size(); z++) {
                                String eText = iconElemList.get(z).getText();
                                String eText2x = "";
                                String eText3x = "";
                                // 有时候图标名会不带后缀，此处追加后缀
                                if (eText != null && eText.indexOf('.') < 0) {
                                    eText2x = eText + "@2x" + ".png";
                                    eText3x = eText + "@2x" + ".png";
                                    eText = eText + ".png";
                                } else if (eText != null && eText.indexOf('.') != -1) {
                                    eText2x = eText.substring(0, eText.indexOf('.')) + "@2x" + ".png";
                                    eText3x = eText.substring(0, eText.indexOf('.')) + "@3x" + ".png";
                                }
                                matNameList.add(eText);
                                matNameList.add(eText2x);
                                matNameList.add(eText3x);
                            }
                            //ret.setIconElemList(matNameList);
                        }
                    }
                }
            }
            //判断是否为内置应用
            if (builtinPkgName.contains(ret.getPkgName()) || ret.getPkgName().startsWith(Constant.BUILTIN_REMOTE_CTRL_PKGNAME)) {
                ret.setDomainType(AppDomainTypeConstant.APP_DOMAIN_TYPE_BUILD);
            }
            if (LanguageConstant.EN.equalsIgnoreCase(language)) {
                if (infoPlistFile.exists()) {
                    Document infoPlistDocument = saxReader.read(infoPlistFile);
                    Element infoPlistRootEle = infoPlistDocument.getRootElement();
                    List<Node> infoPlistES = infoPlistRootEle.selectNodes("/plist/dict/*");
                    for (int i = 0; i < infoPlistES.size(); i++) {
                        if ("CFBundleDisplayName".equals(infoPlistES.get(i).getText())) {
                            CFBundleDisplayName = infoPlistES.get(i + 1).getText();
                        } else if ("CFBundleName".equals(infoPlistES.get(i).getText())) {
                            CFBundleName = infoPlistES.get(i + 1).getText();
                        }
                    }
                }
            }
            if (StringUtils.isNotEmpty(CFBundleDisplayName)) {
                ret.setAppName(CFBundleDisplayName);
            } else {
                ret.setAppName(CFBundleName);
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }
}
