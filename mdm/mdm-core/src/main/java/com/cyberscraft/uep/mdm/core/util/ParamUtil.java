package com.cyberscraft.uep.mdm.core.util;


import com.cyberscraft.uep.common.enums.ExceptionCodeEnum;
import com.cyberscraft.uep.mdm.core.exception.MdmException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 参数处理工具类
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2019-11-19 11:02
 */
public class ParamUtil {
    private static final Logger logger = LoggerFactory.getLogger(ParamUtil.class);
    //  不合法的 url 参数值
    private static final List<String> invalidParameterValues = new ArrayList<String>() {{
        add("select ");
        add("select%20");
        add("update ");
        add("update%20");
        add("delete ");
        add("delete%20");
        add("where ");
        add("where%20");
        add(" and ");
        add("%20and%20");
        add(" or ");
        add("%20or%20");
        add(" or ");
        add("%20or%20");
        add("drop%20");
        add("drop ");
        add("truncate%20");
        add("truncate ");
        add("exec%20");
        add("exec ");
        add("execute%20");
        add("execute ");
        add("declare%20");
        add("declare ");
        add("alter%20");
        add("alter ");
        add("insert%20");
        add("insert ");
        add("create%20");
        add("create ");
        add("/**");
        add("**/");
    }};


    public static void verifyParam(Object obj) {
        if (obj == null || "".equals(obj)) {
            return;
        }

        for (String filter : invalidParameterValues) {
            if (obj.toString().toUpperCase().contains(filter.toUpperCase())) {
                logger.error("InvalidArgument:{}", obj);
                throw new MdmException(ExceptionCodeEnum.PARAM_INVALID);
            }
        }
    }

}
