package com.cyberscraft.uep.mdm.core.service.device;

import com.cyberscraft.uep.common.enums.Platform;
import com.cyberscraft.uep.mdm.api.dto.device.DeviceActivatedDto;
import com.cyberscraft.uep.mdm.core.exception.MdmException;

/***
 *
 * @date 2021-10-24
 * <AUTHOR>
 ***/
public interface IUdidGenerateService {

    /****
     *
     * @param dto
     * @return
     * @throws MdmException
     */
    String createUdid(DeviceActivatedDto dto, Platform platform) throws MdmException;
}
