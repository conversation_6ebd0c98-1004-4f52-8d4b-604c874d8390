package com.cyberscraft.uep.mdm.core.service.device;

import com.cyberscraft.uep.mdm.core.entity.DeviceSamsungEntity;
import com.cyberscraft.uep.mdm.core.exception.MdmException;

/***
 *
 * @date 2021-11-23
 * <AUTHOR>
 ***/
public interface IDeviceSamsungService {

    /***
     *
     * @param deviceId
     * @return
     * @throws MdmException
     */
    DeviceSamsungEntity getDeviceSamsungByDeviceId(Long deviceId) throws MdmException;

    /***
     *
     * @param obj
     * @throws MdmException
     */
    void add(DeviceSamsungEntity obj) throws MdmException;

    /***
     *
     * @param obj
     * @throws MdmException
     */
    void modify(DeviceSamsungEntity obj) throws MdmException;
}
