package com.cyberscraft.uep.mdm.core.dbo.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cyberscraft.uep.common.dto.Pagination;
import com.cyberscraft.uep.common.util.CollectionUtils;
import com.cyberscraft.uep.common.util.CommonUtil;
import com.cyberscraft.uep.common.util.PagingUtil;
import com.cyberscraft.uep.mdm.api.constant.Constant;
import com.cyberscraft.uep.mdm.api.dto.user.DynamicLabelUserGroupQueryDto;
import com.cyberscraft.uep.mdm.api.dto.user.LabelQueryDto;
import com.cyberscraft.uep.mdm.core.dao.DynamicLabelUserDao;
import com.cyberscraft.uep.mdm.api.dto.label.DynaLabelUserGroup;
import com.cyberscraft.uep.mdm.core.domain.label.LabelUserCountItem;
import com.cyberscraft.uep.mdm.core.entity.DynamicLabelUserEntity;
import com.cyberscraft.uep.mdm.core.entity.LabelEntity;
import com.cyberscraft.uep.mdm.core.dao.LabelDao;
import com.cyberscraft.uep.mdm.core.dbo.LabelDBO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cyberscraft.uep.mdm.core.exception.MdmException;
import com.cyberscraft.uep.mdm.core.util.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 标签表 服务实现类
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2019-08-28
 */
@Service
public class LabelDBOImpl extends ServiceImpl<LabelDao, LabelEntity> implements LabelDBO {

    @Resource
    private LabelDao labelDao;

    @Resource
    private DynamicLabelUserDao dynamicLabelUserDao;


    @Override
    public List<String> getNamesById(List<Long> ids) throws MdmException {
        LambdaQueryWrapper<LabelEntity> queryWrapper = new QueryWrapper("id,name").lambda();
        queryWrapper.in(LabelEntity::getId, ids);
        //如果要排序可以在这里加入排序信息
        List<LabelEntity> list = this.labelDao.selectList(queryWrapper);
        return list != null ? list.stream().map(el -> el.getName()).collect(Collectors.toList()) : null;
    }

    @Override
    public IPage<LabelEntity> getLabelsByQueryDto(Pagination<LabelQueryDto> page) {
        IPage<LabelEntity> myPage = PagingUtil.toMybatisPage(page);
        String selectStr = "id,name,description,creator,type,updatetime";
        //TODO 实现对应的查询条件
        QueryWrapper<LabelEntity> queryWrapper = new QueryWrapper().select(selectStr);
        LabelQueryDto dto = page.getQueryDto();
        if (StringUtils.isNotBlank(dto.getLabelNameOrDesc())) {
            queryWrapper.like("name", dto.getLabelNameOrDesc()).or().like("description", dto.getLabelNameOrDesc());
        }
        if (CollectionUtils.isNotEmpty(dto.getChildrenAdminList())) {  //如果当前管理员不是超级管理员
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < dto.getChildrenAdminList().size(); ++i) {
                if (sb.length() > 0) {
                    sb.append(",");
                }
                sb.append("'" + dto.getChildrenAdminList().get(i) + "'");
            }
            queryWrapper.inSql("creator", sb.toString());
        }
        //获取标签实体集合
        IPage<LabelEntity> labelEntitys = this.page(myPage, queryWrapper);
        return labelEntitys;
    }

    @Override
    public List<LabelUserCountItem> getStaticLabelUserCount(List<Long> staticLabelIds) {
        return labelDao.countStaticLabelUserNums(staticLabelIds);
    }

    @Override
    public List<LabelUserCountItem> getDynamicLabelUserCount(List<Long> dynamicLabelIds) {
        List<LabelUserCountItem> result = new ArrayList<>();
        //获取动态标签的用户数量
        LabelUserCountItem item;
        for (Long labelId : dynamicLabelIds) {
            item = new LabelUserCountItem();
            Map<String, String> map = getDynamicLabelConditions(labelId);
            List<DynaLabelUserGroup> list = getDynamicLabelUserGroup(map, null);
            item.setLabelId(labelId);
            item.setNum(list.size());
            result.add(item);
        }
        return result;
    }


    @Override
    public Map<String, String> getDynamicLabelConditions(Long labelId) {
        String selectStr = "paramName, paramValue";
        QueryWrapper<DynamicLabelUserEntity> queryWrapper = new QueryWrapper().select(selectStr);
        queryWrapper.eq("labelid", labelId);
        List<DynamicLabelUserEntity> list = this.dynamicLabelUserDao.selectList(queryWrapper);
        Map<String, String> result = new HashMap<>();
        if (CollectionUtils.isNotEmpty(list)) {
            for (DynamicLabelUserEntity entity : list) {
                result.put(entity.getParamName(), entity.getParamValue());
            }
        }
        return result;
    }

    @Override
    public List<DynaLabelUserGroup> getDynamicLabelUserGroup(Map<String, String> conditions, String otherInfo) {
        StringBuffer sql = new StringBuffer();
        sql.append("SELECT u.id AS userId, u.name, u.loginId, u.mail, u.mobile, g.name AS groupName,g.id as groupId ")
                .append(" FROM t_user  u, t_group  g ")
                .append(" WHERE ").append("u.groupId = g.id AND u.status <> -1 ");
        sql.append(combineQueryParam(conditions));
        if (StringUtils.isNotBlank(otherInfo)) {
            String escapedParam = CommonUtil.concatSQLLike(otherInfo);
            sql.append(" and (u.name like ").append(escapedParam)
                    .append(" or   u.loginId like ").append(escapedParam);
            sql.append(") ");
        }
        List<DynaLabelUserGroup> result = this.labelDao.executeSql(sql.toString());
        return result;
    }


    @Override
    public IPage<DynaLabelUserGroup> getDynamicLabelUserGroup(Pagination<DynamicLabelUserGroupQueryDto> page) {
        Map<String, String> conditions = page.getQueryDto().getConditions();
        String otherInfo = page.getQueryDto().getOtherInfo();
        //查询全部的记录信息
        List<DynaLabelUserGroup> listAll = getDynamicLabelUserGroup(conditions, otherInfo);
        //组装分页信息
        IPage<DynaLabelUserGroup> result = new Page<>();
        result.setPages(page.getPage());
        result.setSize(page.getSize());
        result.setTotal(listAll.size());
        int fromIndex = (page.getPage() - 1) * page.getSize();
        int toIndex;
        if (listAll.size() < page.getPage() * page.getSize()) {
            toIndex = listAll.size() - 1;
        } else {
            toIndex = page.getPage() * page.getSize() - 1;
        }
        List<DynaLabelUserGroup> subList = listAll.subList(fromIndex, toIndex);
        result.setRecords(subList);
        return result;
    }

    private String combineQueryParam(Map<String, String> conditions) {
        StringBuilder hql = new StringBuilder();

        // 拼装用户ID
        if (StringUtils.isNotBlank(conditions.get("userids"))) {
            ArrayList<String> userids = StringUtil.splitPerThousand(conditions.get("userids"));

            hql.append(" AND ( ");
            hql.append(" ( ");
            for (int i = 0; i < userids.size(); i++) {
                hql.append("u.id IN ( ").append(userids.get(i)).append(")");
                if (i + 1 < userids.size()) {
                    hql.append(" OR ");
                }
            }
            hql.append(" ) ");
        }
        // 拼装组ID
        if (StringUtils.isNotBlank(conditions.get("userids")) && StringUtils.isNotBlank(conditions.get("groupids"))) {
            ArrayList<String> groupids = StringUtil.splitPerThousand(conditions.get("groupids"));
            hql.append(" OR ");
            hql.append("exists ( select 1 from ( select a.groupcode from t_group a where 1=2 ");
            for (int i = 0; i < groupids.size(); i++) {
                hql.append(" or a.id IN ( ").append(groupids.get(i)).append(")");
            }
            hql.append(") t where  g.groupcode like concat(t.groupcode,'%'))");
            hql.append(")");
        } else if (StringUtils.isNotBlank(conditions.get("groupids"))
                && StringUtils.isBlank(conditions.get("userids"))) {
            ArrayList<String> groupids = StringUtil.splitPerThousand(conditions.get("groupids"));
            hql.append(" AND  ");
            hql.append("exists ( select 1 from ( select a.groupcode from t_group a where 1=2 ");
            for (int i = 0; i < groupids.size(); i++) {
                hql.append(" or a.id IN ( ").append(groupids.get(i)).append(")");
            }
            hql.append(") t where  g.groupcode like concat(t.groupcode,'%'))");
        } else if (StringUtils.isNotBlank(conditions.get("userids"))
                && StringUtils.isBlank(conditions.get("groupids"))) {
            hql.append(")");
        }
        if (StringUtils.isNotBlank(conditions.get("operatorname"))
                || StringUtils.isNotBlank(conditions.get("relationship"))
                || StringUtils.isNotBlank(conditions.get("os"))
                || StringUtils.isNotBlank(conditions.get("manufacturer"))
                || StringUtils.isNotBlank(conditions.get("status"))
                || StringUtils.isNotBlank(conditions.get("compliance"))
                || StringUtils.isNotBlank(conditions.get("lastonlinetime"))
                || StringUtils.isNotBlank(conditions.get("version"))) {

            hql.append(" AND EXISTS( SELECT 1 FROM t_device  d ");
            hql.append(" WHERE d.userId = u.id AND d.status <> " + Constant.DEVICE_STATUS_DELETED + " AND (1 =1 ");

            // 拼装运营商
            if (StringUtils.isNotBlank(conditions.get("operatorname"))) {
                // hql.append(" AND EXISTS (select 1 from t_device_using_log AS
                // du where d.id = du.deviceId and du.operatorName=
                // ").append(conditions.get("operatorname")).append(")");
                if (conditions.get("operatorname").equals("000")) {// 中国移动
                    hql.append(" AND (d.imsi like '46000%' or d.imsi like '46002%' or d.imsi like '46007%')");
                } else if (conditions.get("operatorname").equals("001")) {// 中国联通
                    hql.append(" AND d.imsi like '46001%' ");
                } else if (conditions.get("operatorname").equals("002")) {// 中国电信
                    hql.append(" AND d.imsi like '46003%'");
                } else if (conditions.get("operatorname").equals("003")) {// 中国铁通
                    hql.append(" AND d.imsi like '46020%' ");
                }
            }
            // 拼装 设备所属关系 1--公司设备;2--员工设备;3--其他
            if (StringUtils.isNotBlank(conditions.get("relationship"))) {
                hql.append(" AND d.relationship = ").append(Integer.parseInt(conditions.get("relationship")));
            }
            // 拼装 os版本
            if (StringUtils.isNotBlank(conditions.get("os"))) {
//				String os = conditions.get("os").toString();
//				String[] osAndVersion = StringUtils.split(os, "|");
//				if (StringUtils.isNotBlank(osAndVersion[0])) {
//					hql.append(" AND (d.deviceType = '").append(osAndVersion[0]).append("'");
//				}
//				// 拼装版本型号
//				if (StringUtils.isNotBlank(osAndVersion[1])) {
//					hql.append(" AND d.versioNum = '").append(osAndVersion[1] ).append( "')");
//				}
                if (conditions.get("os").equals("1")) {
                    hql.append(" AND d.type =1 ");
                } else if (conditions.get("os").equals("2")) {
                    hql.append(" AND d.type =2 ");
                }

            }
            //拼装 版本号
            if (StringUtils.isNotBlank(conditions.get("version")) && !conditions.get("version").equalsIgnoreCase("all")) {
                String versions = buildStrSql(conditions.get("version"));
                hql.append(" AND d.versioNum in (").append(versions).append(")");
            }
            // 拼装 品牌型号
            if (StringUtils.isNotBlank(conditions.get("manufacturer"))) {
                if (conditions.get("manufacturer").equals("APPLE")) {
                    hql.append(" AND d.type =2 ");
                } else {
                    hql.append(" AND upper(d.manufacturer) = '").append(conditions.get("manufacturer").toUpperCase()).append("'");
                }

            }
            // 拼装设备 设备状态,0--未注册,1--注册未激活,2--激活可用, 3--保留 4--擦除
            // status //设备状态 2 已激活设备，3已丢失设备，4 已擦除设备，5待擦除设备 6,待淘汰设备
            if (StringUtils.isNotBlank(conditions.get("status"))) {
                if (Integer.parseInt(conditions.get("status")) == Constant.DEVICE_STATUS_MISSING) {
                    hql.append(" AND (d.lostFlag = " + Constant.DEVICE_LOST_FLAG_YES + " AND d.status = "
                            + Constant.DEVICE_STATUS_ACTIVATE + ")");
                } else if (Integer.parseInt(conditions.get("status")) == Constant.DEVICE_STATUS_ACTIVATE) {
                    hql.append(" AND (d.lostFlag = " + Constant.DEVICE_LOST_FLAG_NO + " AND d.status = "
                            + Constant.DEVICE_STATUS_ACTIVATE + ")");
                } else {
                    hql.append(" AND d.status = ").append(conditions.get("status"));
                }
            }
            // 拼装合归性
            if (StringUtils.isNotBlank(conditions.get("compliance"))) {
                String value = conditions.get("compliance");
                if ("1".equals(value)) {// 未越狱
                    hql.append(" AND ((d.rootFlag = ").append(0).append(" and d.type = 1) or (d.jailbreakFlag = ")
                            .append(0).append(" and d.type = 2))");
                } else if ("2".equals(value)) {// 越狱
                    hql.append(" AND ((d.rootFlag = ").append(1).append(" and d.type = 1) or (d.jailbreakFlag = ")
                            .append(1).append(" and d.type = 2))");
                } else if ("3".equals(value)) {
                    hql.append(
                            " AND EXISTS (SELECT 1 FROM t_virusscan_latest v where v.deviceid = d.id and v.scanstatus =")
                            .append(0).append(")");
                    // hql.append(" OR v.scanstatus = ").append(0); // 安全设备
                } else if ("4".equals(value)) {
                    hql.append(
                            " AND EXISTS (SELECT 1 FROM t_virusscan_latest v where v.deviceid = d.id and v.scanstatus =")
                            .append(1).append(")");
                    // hql.append(" OR v.scanstatus = ").append(1); // 病毒设备
                } else if ("5".equals(value)) {
                    // hql.append(" AND EXISTS (SELECT 1 FROM t_virusscan_latest
                    // v where v.deviceid = d.id and v.scanstatus NOT IN
                    // (1,2) AND d.type = 1)");
                    hql.append(
                            " AND NOT EXISTS (SELECT 1 FROM t_virusscan_latest v where v.deviceid = d.id) AND d.type = 1 ");
                }
            }
            // 拼装最后在线时间
            if (StringUtils.isNotBlank(conditions.get("lastonlinetime"))) {
                String intervalStr = conditions.get("lastonlinetime");
                long interval = Long.parseLong(intervalStr.substring(0, intervalStr.length() - 1));
                LocalDateTime now = LocalDateTime.now();
                String startTime = null;
                String endTime = now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                if (intervalStr.endsWith("m")) {
                    // 分钟
                    startTime = now.minus(interval, ChronoUnit.MINUTES)
                            .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                } else if (intervalStr.endsWith("h")) {
                    // 小时
                    startTime = now.minus(interval, ChronoUnit.HOURS)
                            .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                } else if (intervalStr.endsWith("d")) {
                    // 天
                    startTime = now.minus(interval, ChronoUnit.DAYS)
                            .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                } else if (intervalStr.endsWith("w")) {
                    // 周
                    startTime = now.minus(interval, ChronoUnit.WEEKS)
                            .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                } else if (intervalStr.endsWith("M")) {
                    // 月
                    startTime = now.minus(interval, ChronoUnit.MONTHS)
                            .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                }
                hql.append(" AND d.lastOnlineTime BETWEEN '").append(startTime).append("'").append(" AND '")
                        .append(endTime).append(" ' ");

            }
            hql.append("))");
        }
        return hql.toString();
    }

    private String buildStrSql(String fieldIds) {
        if (StringUtils.isNotBlank(fieldIds)) {
            StringBuffer buf = new StringBuffer();
            String[] fields = fieldIds.split(",");
            for (int i = 0; i < fields.length; i++) {

                if (i == fields.length - 1) {
                    buf.append("'" + fields[i] + "'");
                } else {
                    buf.append("'" + fields[i] + "',");
                }
            }
            return buf.toString();
        }
        return null;
    }


}
