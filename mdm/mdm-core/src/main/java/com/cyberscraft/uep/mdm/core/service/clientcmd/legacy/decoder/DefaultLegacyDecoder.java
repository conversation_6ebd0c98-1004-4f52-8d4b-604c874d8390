package com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.decoder;

import com.cyberscraft.uep.mdm.core.domain.clientcmd.LegacyClientCmdRequest;
import com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.ILegacyDecoder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.io.ByteArrayOutputStream;
import java.io.EOFException;
import java.io.IOException;
import java.io.InputStream;

/***
 *
 * @date 2021-11-20
 * <AUTHOR>
 ***/
@Component
public class DefaultLegacyDecoder implements ILegacyDecoder {

    /***
     *
     */
    private final static Logger LOG = LoggerFactory.getLogger(DefaultLegacyDecoder.class);

    /***
     * 解码逻辑
     * @param request
     * @return
     */
    @Override
    public LegacyClientCmdRequest decode(HttpServletRequest request) {
        try {
            InputStream inStream = request.getInputStream();
            LegacyClientCmdRequest obj = new LegacyClientCmdRequest();
            obj.setProtocalCode(readRequestBodyHeaderAsByte(inStream));
            obj.setCompressCode(readRequestBodyHeaderAsByte(inStream));
            obj.setEncryptCode(readRequestBodyHeaderAsByte(inStream));
            obj.setCommandCode(readRequestBodyHeaderAsByte(inStream));

            readRequestBodyHeaderAsByte(inStream);
            obj.setCommandBody(readRequestBodyContent(inStream));
            return obj;
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
        return null;
    }

    @Override
    public ByteArrayOutputStream encode(LegacyClientCmdRequest request) {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();

        try {
            baos.write(toLH(request.getProtocalCode()));
            baos.write(toLH(request.getCompressCode()));
            baos.write(toLH(request.getEncryptCode()));
            baos.write(toLH(request.getCommandCode()));
            baos.write(toLH(request.getCommandBody().getBytes().length));
            baos.write(request.getCommandBody().getBytes());
        } catch (IOException e) {
            LOG.error("assembleResponse error:", e);
        } finally {
            try {
                baos.close();
            } catch (IOException e) {
                LOG.error(e.getMessage(), e);
            }
        }
        return baos;
    }

    @Override
    public ByteArrayOutputStream assembleResponse(LegacyClientCmdRequest request, String result) {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();

        try {
            baos.write(toLH(request.getProtocalCode()));
            baos.write(toLH(request.getCompressCode()));
            baos.write(toLH(request.getEncryptCode()));
            baos.write(toLH(request.getCommandCode()));
            if(result != null) {
                baos.write(toLH(result.getBytes().length));
                baos.write(result.getBytes());
            }else {
                baos.write(toLH(0));
            }
        } catch (IOException e) {
            LOG.error("assembleResponse error:",e);
        }finally {
            try {
                baos.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return baos;
    }

    private static byte[] toLH(int n) {
        byte[] b = new byte[4];
        b[0] = (byte) (n & 0xff);
        b[1] = (byte) (n >> 8 & 0xff);
        b[2] = (byte) (n >> 16 & 0xff);
        b[3] = (byte) (n >> 24 & 0xff);
        return b;
    }

    protected static int readRequestBodyHeaderAsByte(InputStream _in) throws IOException {
        int ch1 = _in.read();
        int ch2 = _in.read();
        int ch3 = _in.read();
        int ch4 = _in.read();
        //_in.close();
        if ((ch1 | ch2 | ch3 | ch4) < 0)
            throw new EOFException();
        return ((ch4 << 24) + (ch3 << 16) + (ch2 << 8) + (ch1 << 0));
    }

    protected static String readRequestBodyContent(InputStream _in) {
        String returnString = "";
        ByteArrayOutputStream outSteam = new ByteArrayOutputStream();
        byte[] buffer = new byte[10 * 1024];
        int len = -1;
        try {
            while ((len = _in.read(buffer)) != -1) {
                outSteam.write(buffer, 0, len);
            }
            outSteam.close();
            _in.close();

            returnString = new String(outSteam.toByteArray());
        } catch (IOException e) {
            LOG.error("readRequestBodyContent error:", e);
        }
        return returnString;
    }
}
