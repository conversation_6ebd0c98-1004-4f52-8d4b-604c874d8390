package com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.parser;

import com.cyberscraft.uep.common.enums.ExceptionCodeEnum;
import com.cyberscraft.uep.account.client.constant.ThirdPartyAccountType;
import com.cyberscraft.uep.account.client.provider.govdd.config.GovDDFilterConfig;
import com.cyberscraft.uep.account.client.service.IThirdPartyAccountService;
import com.cyberscraft.uep.common.util.BeanUtil;
import com.cyberscraft.uep.mdm.core.domain.oauth2.AccessToken;
import com.cyberscraft.uep.mdm.core.exception.MdmException;
import com.cyberscraft.uep.mdm.core.service.common.ITemplateContentRender;
import com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.domain.ClientIDInfo;
import com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.domain.MobileIDInfo;
import com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.domain.OperID;
import com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.sax.RequestBeanSaxHandler;
import com.cyberscraft.uep.mdm.core.service.oauth2.IAuthorizationService;
import com.cyberscraft.uep.mdm.core.util.Oauth2ThreadLocalUtil;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.dom4j.Document;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.xml.parsers.SAXParser;
import javax.xml.parsers.SAXParserFactory;
import java.beans.PropertyDescriptor;
import java.io.ByteArrayInputStream;
import java.lang.reflect.Array;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.time.LocalDateTime;
import java.util.*;

/***
 * 主要采用dom4j进行xml的解析
 * @date 2021-12-15
 * <AUTHOR>
 ***/
@Component
public class AbstractCmdParserDom4j {

    @Resource
    protected GovDDFilterConfig govDDFilterConfig;

    @Value("${govdd.enabled:false}")
    protected boolean govDDEnabled = false;

    @Resource
    protected IThirdPartyAccountService thirdPartyAccountService;

    @Resource
    protected ITemplateContentRender legacyContentRender;

    @Resource
    protected IAuthorizationService authorizationService;

    /****
     *
     */
    protected final static Logger LOG = LoggerFactory.getLogger(AbstractCmdParser.class);

    /***
     * 要过虑的字段列表，默认为字段名称的小写
     */
    private final static Set<String> IGNORE_PROPERTIES = new HashSet<>();

    static {
        IGNORE_PROPERTIES.add("serialversionuid");
        IGNORE_PROPERTIES.add("class");
    }

    /***
     * 解释xml得到根结点
     * @param requestBody
     * @return
     */
    protected Element getRootElement(String requestBody) {
        Document doc = null;
        try {

            SAXReader reader = new SAXReader();
            reader.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
            reader.setFeature("http://xml.org/sax/features/external-general-entities", false);
            reader.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
            reader.setFeature("http://apache.org/xml/features/nonvalidating/load-external-dtd", false);

            doc = reader.read(new ByteArrayInputStream(requestBody.getBytes()));//new File(configPath));
            // 获取根节点
            Element rootElt = (Element) doc.getRootElement();
            return rootElt;
        } catch (Exception e) {
            LOG.error("read xml error:" + e.getMessage(), e);
        }
        return null;
    }

    /****
     *
     * @param root
     * @return
     */
    protected ClientIDInfo getClientIDinfoFromDom(Element root) {
        return parserObject(root.element("ClientIDInfo"), ClientIDInfo.class);
    }

    /****
     *
     * @param root
     * @return
     */
    protected OperID getOperIDFromDom(Element root) {
        return parserObject(root.element("OperID"), OperID.class);
    }

    /****
     *
     *
     * @param root
     * @return
     */
    protected MobileIDInfo getMobileIDInfoFromDom(Element root) {
        return parserObject(root.element("MobileIDInfo"), MobileIDInfo.class);
    }

    /****
     * 获取租户ID
     * @param operID
     * @return
     */
    protected String getTenantId(OperID operID) {
        //这里因为做的特殊转换，所以case by case实现，实在不怎么优美
        if (govDDEnabled) {
            if (govDDFilterConfig.isTenantTransfer()) {
                return thirdPartyAccountService.thridPartyTenant2Tenant(operID.getTenantID(), ThirdPartyAccountType.GOV_DINGDING.getCode());
            }
        }
        return operID.getTenantID();
    }

    /***
     * 得到输出的租户ID
     * @param tenantId
     * @return
     */
    protected String getOutTenantId(String tenantId) {
        if (govDDEnabled) {
            if (govDDFilterConfig.isTenantTransfer()) {
                return thirdPartyAccountService.tenant2ThridPartyTenant(tenantId, ThirdPartyAccountType.GOV_DINGDING.getCode());
            }
        }
        return tenantId;
    }

    /***
     *
     * @param root
     * @return
     */
    protected <T> T parserObject(Element root, Class<T> clazzName) {
        try {
            T obj = (T) clazzName.newInstance();
            List<Element> subElments = root.elements();
            if (subElments == null || subElments.size() == 0) {
                return obj;
            }
            Map<String, Field> fieldsMap = BeanUtil.getDeclaredFieldsMap(obj.getClass());
            PropertyDescriptor[] srcPds = BeanUtils.getPropertyDescriptors(obj.getClass());
            if (srcPds == null || srcPds.length == 0) {
                return null;
            }
            for (PropertyDescriptor p : srcPds) {
                String name = p.getName();
                if (IGNORE_PROPERTIES.contains(name.toLowerCase())) {
                    continue;
                }
                Field field = null;
                try {
                    field = fieldsMap.get(name);
                } catch (Exception e) {
                    //如果出错，则代表不是对应的字段,因为该PropertyDescriptor即包括字段也包括方法，跳过就行
                    if (LOG.isTraceEnabled()) {
                        LOG.trace("{}对应的属性，不是字段，进行跳过", name);
                    }
                }
                try {
                    Method writeMethod = p.getWriteMethod();
                    if (!Modifier.isPublic(writeMethod.getDeclaringClass().getModifiers())) {
                        writeMethod.setAccessible(true);
                    }
                    //判断是否是数组类,数据类，只处理对像
                    if (field.getType().isArray()) {
                        List<Element> els = getSubElements(name, subElments);
                        if (els == null || els.size() == 0) {
                            continue;
                        }
//                        List arrayObjects = new ArrayList(els.size());
                        Object arrayObjects = Array.newInstance(field.getType().getComponentType(), els.size());
                        int n = 0;
                        for (Element element1 : els) {
                            Object oneO = parserObject(element1, field.getType().getComponentType());
                            Array.set(arrayObjects, n, oneO);
//                            arrayObjects.add(oneO);
                            n++;
                        }
                        writeMethod.invoke(obj, arrayObjects);

                    } else { //单值类
                        Element propElement = getSubElement(name, subElments);
                        if (propElement == null) {
                            continue;
                        }
                        Object value = null;

                        //如果有子节点,代表是对像,需要重新进行设置
                        if (hasChild(propElement)) {
                            value = parserObject(propElement, field.getType());
                        } else {
                            value = getPropertyValue(field, propElement);
                        }
                        writeMethod.invoke(obj, value);
                    }
                } catch (Exception e) {
                    LOG.warn("写入字段{}的值出错", name);
                }
            }
            return obj;
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
        return null;
    }

    /***
     *
     * @param field
     * @param element
     * @return
     */
    private Object getPropertyValue(Field field, Element element) {

        String val = element.getTextTrim();
        String feildType = field.getType().getSimpleName();
        Object value = null;
        if ("int".equals(feildType) || "Integer".equals(feildType)) {
            value = NumberUtils.toInt(val, 0);
        } else if ("long".equals(feildType) || "Long".equals(feildType)) {
            value = NumberUtils.toLong(val, 0);
        } else if ("double".equals(feildType) || "Double".equals(feildType)) {
            value = NumberUtils.toDouble(val, 0);
        } else if ("float".equals(feildType) || "Float".equals(feildType)) {
            value = NumberUtils.toFloat(val, 0);
        } else {
            value = val;
        }
        return value;
    }

    /***
     *
     * @param name
     * @param subElments
     * @return
     */
    private Element getSubElement(String name, List<Element> subElments) {
        for (Element el : subElments) {
            if (name.toLowerCase().equalsIgnoreCase(el.getName())) {
                return el;
            }
        }
        return null;
    }

    /***
     *
     * @param name
     * @param subElments
     * @return
     */
    private List<Element> getSubElements(String name, List<Element> subElments) {
        List<Element> ret = new ArrayList<>();
        for (Element el : subElments) {
            if (name.toLowerCase().equalsIgnoreCase(el.getName())) {
                ret.add(el);
            }
        }
        return ret;
    }


    /***
     *
     * @param el
     * @return
     */
    boolean hasChild(Element el) {
        List<Element> subElments = el.elements();
        if (subElments == null || subElments.size() == 0) {
            return false;
        }
        return true;
    }

    /****
     * 进行事件解释处理
     * @param requestBody
     * @return
     */
    protected RequestBeanSaxHandler getRequestBeanHandler(String requestBody) {
        try {
            //1、获取解析工厂   SAXParserFactory是protect类型，所以用他的静态方法
            SAXParserFactory factory = SAXParserFactory.newInstance();
            factory.setFeature("http://apache.org/xml/features/disallow-doctype-decl", true);
            factory.setFeature("http://xml.org/sax/features/external-general-entities", false);
            factory.setFeature("http://xml.org/sax/features/external-parameter-entities", false);
            factory.setFeature("http://apache.org/xml/features/nonvalidating/load-external-dtd", false);
            //2、从解析工厂获取解析器
            SAXParser parse = factory.newSAXParser();
            //3、加载文档Document注册处理器
            //4、编写处理器
            RequestBeanSaxHandler handler = new RequestBeanSaxHandler();
            parse.parse(new ByteArrayInputStream(requestBody.getBytes()), handler);
//            DeviceUpdateNew deviceUpdate = handler.getDeviceUpdate();
//            OperID obj = handler.getOperID();
//            Assertions.assertTrue(deviceUpdate.getNetTrafficList().getNetTraffic().size() > 0);
            //LOG.info(obj.getClientUID());
            return handler;
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
        return null;
    }

    /***
     * 验证访问token
     * @param accessToken
     */
    protected AccessToken validateAccessToken(String accessToken) {
        AccessToken token = authorizationService.getAccessToken(accessToken);
        if (token == null) {
            throw new MdmException(ExceptionCodeEnum.ACCESSTOKEN_INVALID.getCode(), "请求token为:" + accessToken);
        }
        //判断是否过期
        if (token.getExpireDate().isBefore(LocalDateTime.now())) {
            throw new MdmException(ExceptionCodeEnum.ACCESSTOKEN_EXPIRED.getCode(), "请求token为:" + accessToken);
        }
        //设置当前数据库为Token对应用户的租户
        if (StringUtils.isNotBlank(token.getTenantId())) {
            TenantHolder.setTenantCode(token.getTenantId());
        }
        Oauth2ThreadLocalUtil.setToken(token);
        LOG.info("当前请求的token:{},对应设备UDID:{},用户:{},租户:{}", accessToken, token.getUdid(), token.getLoginId(), token.getTenantId());
        return token;
    }
}
