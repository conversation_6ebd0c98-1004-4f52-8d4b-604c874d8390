package com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.domain;

import java.io.Serializable;

/***
 *
 * @date 2021-11-22
 * <AUTHOR>
 ***/
public class Battery implements Serializable {

    private String batteryLevel;
    private String batteryState;
    private String batteryStatus;
    private String cpuRatio;//CPU使用率
    private String memoryRatio;//内存使用率
    private String storageRatio;//存储使用率

    public String getBatteryLevel() {
        return batteryLevel;
    }

    public void setBatteryLevel(String batteryLevel) {
        this.batteryLevel = batteryLevel;
    }

    public String getBatteryState() {
        return batteryState;
    }

    public void setBatteryState(String batteryState) {
        this.batteryState = batteryState;
    }

    public String getBatteryStatus() {
        return batteryStatus;
    }

    public void setBatteryStatus(String batteryStatus) {
        this.batteryStatus = batteryStatus;
    }

    public String getCpuRatio() {
        return cpuRatio;
    }

    public void setCpuRatio(String cpuRatio) {
        this.cpuRatio = cpuRatio;
    }

    public String getMemoryRatio() {
        return memoryRatio;
    }

    public void setMemoryRatio(String memoryRatio) {
        this.memoryRatio = memoryRatio;
    }

    public String getStorageRatio() {
        return storageRatio;
    }

    public void setStorageRatio(String storageRatio) {
        this.storageRatio = storageRatio;
    }
}
