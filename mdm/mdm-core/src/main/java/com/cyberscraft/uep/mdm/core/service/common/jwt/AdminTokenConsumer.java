package com.cyberscraft.uep.mdm.core.service.common.jwt;

import com.cyberscraft.uep.mdm.core.exception.MdmException;
import com.cyberscraft.uep.mdm.core.service.config.JwtConfiguration;
import io.jsonwebtoken.SignatureAlgorithm;
import org.apache.commons.lang3.StringUtils;
import org.jose4j.jwt.consumer.JwtConsumer;
import org.jose4j.jwt.consumer.JwtConsumerBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.crypto.spec.SecretKeySpec;
import java.security.Key;

/***
 *
 * @date 2021-10-08
 * <AUTHOR>
 ***/
@Component
public class AdminTokenConsumer extends AbstractTokenConsumer {


    @Autowired
    private JwtConfiguration jwtConfiguration;


    /***
     * 签名算法
     */
    private SignatureAlgorithm algorithm = SignatureAlgorithm.HS512;

    public AdminTokenConsumer(JwtConfiguration jwtConfiguration) {
        this.jwtConfiguration = jwtConfiguration;
        this.initConsumer();
    }

    private void initConsumer() {
        /***
         * 签名的key
         */
        Key signingKey = new SecretKeySpec(jwtConfiguration.getKey().getBytes(), algorithm.getJcaName());
        JwtConsumer temp = new JwtConsumerBuilder()
                .setRequireExpirationTime()
                .setAllowedClockSkewInSeconds(jwtConfiguration.getExpire())
                .setExpectedAudience(jwtConfiguration.getAud())
                .setVerificationKey(signingKey)
                .setRelaxVerificationKeyValidation()
                .build();
        consumer = temp;
    }

    @Override
    public boolean isSupported(String issuer) throws MdmException {
        String adminIss = jwtConfiguration.getAdminIss();
        boolean flag = StringUtils.isNotBlank(issuer) && StringUtils.isNotBlank(adminIss) &&
                issuer.trim().equalsIgnoreCase(adminIss.trim());
        return flag;
    }


}
