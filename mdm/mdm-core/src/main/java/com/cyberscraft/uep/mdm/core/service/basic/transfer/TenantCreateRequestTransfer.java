package com.cyberscraft.uep.mdm.core.service.basic.transfer;

import com.cyberscraft.uep.account.client.domain.ThirdPartyTenantCreateRequest;
import com.cyberscraft.uep.mdm.api.dto.tenant.TenantCreateRequestDto;
import com.cyberscraft.uep.mdm.core.service.device.transfer.IDeviceTransfer;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/***
 *
 * @date 2021/9/17
 * <AUTHOR>
 ***/
@Mapper(componentModel = "spring")
public interface TenantCreateRequestTransfer {

    /***
     *
     */
    TenantCreateRequestTransfer INSTANCE = Mappers.getMapper(TenantCreateRequestTransfer.class);


    /***
     * 转换成DeviceInfo域对像
     * @param  obj
     * @return
     */
    ThirdPartyTenantCreateRequest dto2Domain(TenantCreateRequestDto obj);
}
