package com.cyberscraft.uep.mdm.core.service.admin.impl;

import com.cyberscraft.uep.mdm.core.service.admin.ISysUserPermissionService;
import org.springframework.stereotype.Service;

import java.util.List;

/***
 *
 * @date 2021-09-16
 * <AUTHOR>
 ***/
@Service
public class SysUserPermissionServiceImpl implements ISysUserPermissionService {

    @Override
    public List<String> findFuncCodes(String loginId) {
        return null;
    }

    @Override
    public List<String> findApiCodes(String loginId) {
        return null;
    }
}
