package com.cyberscraft.uep.mdm.core.service.event.mq.sub.impl;

import com.cyberscraft.uep.common.enums.LogTag;
import com.cyberscraft.uep.common.logger.DigitalSeeLoggerFactory;
import com.cyberscraft.uep.mdm.api.event.Event;
import com.cyberscraft.uep.mdm.core.service.event.handler.IEventHandler;
import com.cyberscraft.uep.mdm.core.service.event.mq.sub.IEventConsumer;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;

@Component
public class EventConsumerImpl implements IEventConsumer {
    private final static Logger logger = DigitalSeeLoggerFactory.getLogger(EventConsumerImpl.class, LogTag.MQ);
    /***
     * 当前系统已经支持的事件处理器
     */
    @Resource
    private List<IEventHandler> eventHandlers;

    private final static HashMap<String, IEventHandler> EVENT_HANDLER_MAP = new HashMap<>(128);

    private static String toKey(Integer businessType, Integer changeType){
        return String.format("%d:%d", businessType, changeType);
    }

    @PostConstruct
    private void buildEventHandlerMap(){
        logger.info("current eventHandlers size={}, list={}", eventHandlers.size(), eventHandlers);
        EVENT_HANDLER_MAP.clear();
        for (final IEventHandler eventHandler: eventHandlers) {
            eventHandler.supportedEvents().stream().forEach(e -> {
                IEventHandler oldHandler = EVENT_HANDLER_MAP.put(toKey(e.getLeft().getValue(), e.getRight().getValue()), eventHandler);
                if (oldHandler != null) {
                    logger.error("EventBusinessType {} already registered with {}", e, oldHandler);
                    throw new RuntimeException("can't register 2 event handlers with one event business type");
                }
            });
        }
    }

    @Override
    public void consume(Event event) {
        logger.info("receive {}", event);
        IEventHandler eventHandler = EVENT_HANDLER_MAP.get(toKey(event.getBusinessType(), event.getChangeType()));
        if (eventHandler == null){
            logger.warn("no handler for {}", event);
            return;
        }
        logger.info("set tenantId to thread local, tenantId = {}", event.getTenantId());
        TenantHolder.setTenantCode(event.getTenantId());
        eventHandler.handlerEvent(event);
        TenantHolder.remove();
    }
}
