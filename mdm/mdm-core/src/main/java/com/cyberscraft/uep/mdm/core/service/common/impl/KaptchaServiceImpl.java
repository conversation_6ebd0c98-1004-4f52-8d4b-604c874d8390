package com.cyberscraft.uep.mdm.core.service.common.impl;

import com.cyberscraft.uep.mdm.api.errors.MdmErrorType;
import com.cyberscraft.uep.mdm.core.domain.VerifyCode;
import com.cyberscraft.uep.mdm.core.exception.MdmException;
import com.cyberscraft.uep.mdm.core.service.common.IKaptchaService;
import com.cyberscraft.uep.mdm.core.service.common.IVerifyCodeService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/***
 * 验证码服务接口实现类，从原backend中的KaptchaUtil移植过来
 * @date 2021-09-17
 * <AUTHOR>
 ***/
@Service
public class KaptchaServiceImpl implements IKaptchaService {

    @Resource
    private IVerifyCodeService verifyCodeService;

    @Override
    public boolean checkSmsCaptcha(String verifyCode, String captcha) throws MdmException {
        VerifyCode vo = verifyCodeService.getVerifyCode(verifyCode);
        if (Objects.isNull(vo)) {
            throw new MdmException(MdmErrorType.SMS_CAPTCHA_WRONG);
        }
        // 判断短信验证码是否正确
        String sms1 = captcha;
        String sms0 = vo.getVerifyCode();

        if (null == sms0 || !sms0.equalsIgnoreCase(sms1)) {
            throw new MdmException(MdmErrorType.SMS_CAPTCHA_WRONG);
        }

        verifyCodeService.evict(vo.getKey());
        Date date = vo.getCreateTime();
        Date now = new Date();
        if (date == null || (now.getTime() - date.getTime() > 1000 * 60 * 15)) {
            throw new MdmException(MdmErrorType.SMS_CAPTCHA_TIMEOUT);
        }
        return true;
    }

    @Override
    public boolean checkCaptcha(String verifyCode, String captcha) throws MdmException {
        VerifyCode vo = verifyCodeService.getVerifyCode(verifyCode);
        if (Objects.isNull(vo)) {
            throw new MdmException(MdmErrorType.CAPTCHA_WRONG);
        }

        // 判断验证码是否正确
        String captcha1 = captcha;
        String captcha0 = vo.getVerifyCode();

        if (captcha0 == null || !captcha0.equalsIgnoreCase(captcha1)) {
            throw new MdmException(MdmErrorType.CAPTCHA_WRONG);
        }

        Date date = vo.getCreateTime();
        Date now = new Date();

        verifyCodeService.evict(vo.getKey());
        if (date == null || (now.getTime() - date.getTime() > 1000 * 60 * 2)) {
            throw new MdmException(MdmErrorType.CAPTCHA_TIMEOUT);
        }
        return true;
    }
}
