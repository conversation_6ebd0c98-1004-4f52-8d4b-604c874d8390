package com.cyberscraft.uep.mdm.core.util;


import com.cyberscraft.uep.starter.dds.TenantHolder;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

public abstract class MdmRunnable implements Runnable {

    private final RequestAttributes ra;
    private final String tenantId;
    private Thread thread;

    public MdmRunnable() {
        this.ra = RequestContextHolder.getRequestAttributes();
        //获取租户id
        tenantId = TenantHolder.getTenantCode();
        this.thread = Thread.currentThread();
    }

    /**
     * 执行新线程钱做一些变量初始化工作
     * <p>
     * 子类实现runInternal方法即可。
     */
    @Override
    public final void run() {
        try {
            prepare();
            runInternal();
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            if (Thread.currentThread() != thread) {
                RequestContextHolder.resetRequestAttributes();
            }
            thread = null;
        }
    }

    private void prepare() {
        RequestContextHolder.setRequestAttributes(ra);
        TenantHolder.setTenantCode(tenantId);
    }

    protected abstract void runInternal();
}
