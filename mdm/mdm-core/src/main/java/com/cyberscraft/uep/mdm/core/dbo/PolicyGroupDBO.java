package com.cyberscraft.uep.mdm.core.dbo;

import com.cyberscraft.uep.mdm.core.entity.PolicyGroupEntity;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2019-08-28
 */
public interface PolicyGroupDBO extends IService<PolicyGroupEntity> {

    List<PolicyGroupEntity> getPolicyGroupsByPolicyIdGroupIds(String policyId, List<Long> groupIds);

    List<Long> getGroupIdsByPolicyId(String policyId);

    boolean removeByPolicyId(String policyId);

    List<String> getPolicyIdsByGroupIds(List<Long> groupIds);

}
