//package com.cyberscraft.uep.mdm.core.service.user.event;
//
//import com.cyberscraft.uep.account.client.constant.ThirdPartyEventTagConstant;
//import com.cyberscraft.uep.account.client.domain.EventBody;
//import com.cyberscraft.uep.account.client.domain.GroupEventBody;
//import com.cyberscraft.uep.account.client.domain.ThirdPartyEvent;
//import com.cyberscraft.uep.account.client.domain.ThirdPartyGroup;
//import com.cyberscraft.uep.account.client.event.IThirdPartyEventExecutor;
//import com.cyberscraft.uep.mdm.api.constant.Constant;
//import com.cyberscraft.uep.mdm.core.entity.GroupEntity;
//import com.cyberscraft.uep.starter.dds.TenantHolder;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.stereotype.Component;
//
//import java.util.Arrays;
//import java.util.HashSet;
//import java.util.List;
//import java.util.Set;
//
///***
// * 组移动事件,一般来说主要是更改组的父ID，需要找到新的父组，然后更改当前组及下面子组的代码
// * @date 2021-11-08
// * <AUTHOR>
// ***/
//@Component
//public class GroupMoveEventHandler extends AbstractUserGroupEventHandler implements IThirdPartyEventExecutor {
//
//    @Override
//    public Set<String> getSupportedEventTypes() {
//        return new HashSet<>(Arrays.asList(ThirdPartyEventTagConstant.GROUP_MOVE));
//    }
//
//    @Override
//    public <E extends EventBody> void onEvent(ThirdPartyEvent<E> event) {
//        GroupEventBody eventBody = (GroupEventBody) event.getData();
//        LOG.info("接收到组移动事件，其中租户ID=>{},组编号列表为:{}", event.getTenantId(), eventBody.getCodes());
//        if (eventBody.getCodes() != null && eventBody.getCodes().size() == 0) {
//            return;
//        }
//        TenantHolder.setTenantCode(event.getTenantId());
//        for (String groupId : eventBody.getCodes()) {
//            try {
//                synchronized (groupId) {
//                    GroupEntity groupEntity = groupService.getGroupByThirdGroupId(groupId, String.valueOf(event.getAccountType()));
//                    if (groupEntity == null) {
//                        LOG.error("在组移动事件中，未找到第三方平台对应的组");
//                        continue;
//                    }
//                    ThirdPartyGroup thirdPartyGroup = thirdPartyAccountService.getGroupByCode(event.getTenantId(), String.valueOf(event.getAccountType()), groupId, null);
//                    GroupEntity parentGroup = findGroupByThridTypeGroupCode(event.getTenantId(), thirdPartyGroup.getParentCode(), String.valueOf(event.getAccountType()));
//
//                    //判断是否是未移动的条件为父id相同及当前groupCode为父组织代码-4位
//                    String currentParentGroupCode = groupEntity.getGroupCode();
//                    if (StringUtils.isNotBlank(currentParentGroupCode)) {
//                        currentParentGroupCode = currentParentGroupCode.substring(0, currentParentGroupCode.length() - 4);
//                    }
//                    Long parentGroupId = parentGroup != null ? parentGroup.getId() : Constant.DEFAULT_ROOT_GROUP_PARENTID;
//                    String pGroupCode = parentGroup != null ? parentGroup.getGroupCode() : null;
//
//                    if (parentGroupId.equals(groupEntity.getParentId())
//                            && currentParentGroupCode.equalsIgnoreCase(pGroupCode)) {  //代表着是未进行移动，则不需要进行修改
//                        LOG.info("在组移动事件中，未发现实际的移动");
//                        return;
//                    }
//                    updateGroupCode(groupEntity, parentGroup, String.valueOf(event.getAccountType()));
//                }
//            } catch (Exception e) {
//                LOG.error(e.getMessage(), e);
//            }
//        }
//    }
//
//    /***
//     * 更改组代码，递归更改对应的子组代码
//     * @param group
//     * @param parentGroup
//     */
//    private void updateGroupCode(GroupEntity group, GroupEntity parentGroup, String accountType) {
//        String newGroupCode = null;
//        if (parentGroup == null) {
//            //代表为根组织
//            newGroupCode = getSyncRootGroupCode(accountType);
//        } else {
//            newGroupCode = getNewSubGroupCode(parentGroup.getId(), parentGroup.getGroupCode());
//        }
//        group.setGroupCode(newGroupCode);
//        group.setParentId(parentGroup != null ? parentGroup.getId() : Constant.DEFAULT_ROOT_GROUP_PARENTID);
//        groupService.modify(group);
//        List<GroupEntity> subGroups = this.groupService.getSubGroups(group.getId());
//        if (subGroups != null && subGroups.size() > 0) {
//            for (GroupEntity subGroup : subGroups) {
//                updateGroupCode(subGroup, group, accountType);
//            }
//        }
//    }
//}
