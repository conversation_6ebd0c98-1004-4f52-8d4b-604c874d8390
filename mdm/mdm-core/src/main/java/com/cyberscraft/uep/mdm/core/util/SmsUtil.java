package com.cyberscraft.uep.mdm.core.util;

import org.apache.commons.lang3.RandomUtils;

import java.util.HashMap;
import java.util.Map;

public class SmsUtil {

    public static char[] chars = "abc1defgh2ijklm9nop3qrstu8vwxyzA4BCDEFGHI5JKLMNOP6QRST7UVWXY0Z".toCharArray();

    /**
     * 返回4位的随机字符串
     *
     * @return
     */
    public static String getRandStr() {

        StringBuilder sb = new StringBuilder();

        for (int i = 0; i < 4; i++) {
            sb.append(chars[RandomUtils.nextInt(0, chars.length)]);
        }

        return sb.toString();
    }

    /**
     * 组装短信信息的键值
     *
     * @param industry
     * @param language
     * @return
     */
    public static Map<String, String> getSmsKeys(String industry, String language) {
        Map<String, String> smsKeys = new HashMap<>();

        String smsSignatureKey = "sms.signature.{industry}";
        String inviteSmsTemplateKey = "sms.active.template.{industry}.{language}";
        String smsVerificationTemplateKey = "sms.verification.template.{language}";

        //Map<String, String> propMap = PropertiesUtil.readProperties(new File(Constants.GLOBAL_CONFIG_FILE));

//        boolean  userNoc = Boolean.parseBoolean(null==propMap.get("NOC.use")?"false":propMap.get("NOC.use"));
//        if (userNoc) {
//            industry = "noc";
//        }

        smsSignatureKey = smsSignatureKey.replace("{industry}", industry);
        inviteSmsTemplateKey = inviteSmsTemplateKey.replace("{industry}", industry).replace("{language}", language);
        smsVerificationTemplateKey = smsVerificationTemplateKey.replace("{language}", language);

        smsKeys.put("smsSignatureKey", smsSignatureKey);
        smsKeys.put("inviteSmsTemplateKey", inviteSmsTemplateKey);
        smsKeys.put("smsVerificationTemplateKey", smsVerificationTemplateKey);

        return smsKeys;
    }

    /**
     * 拼接短信内容
     *
     * @param smsContent
     * @param smsSignature
     * @return
     */
    public static String generateSms(String smsContent, String smsSignature) {
        return smsContent + "【" + smsSignature + "】";
    }

    /**
     * 替换短信模板中的变量
     *
     * @param inviteSmsTemplate
     * @param model
     * @return
     */
    public static String replaceAllVariableInTemplate(String inviteSmsTemplate, Map<String, String> model) {
        if (!model.isEmpty()) {
            for (Map.Entry<String, String> entrySet : model.entrySet()) {
                inviteSmsTemplate = inviteSmsTemplate.replace(entrySet.getKey(), entrySet.getValue());
            }
        }

        return inviteSmsTemplate;
    }

    public static void main(String args[]) {

        long start = System.currentTimeMillis();
        for (int i = 0; i < 1000; i++) {
            System.out.println(getRandStr());
        }
        long end = System.currentTimeMillis();

        System.out.println("*********total time " + (end - start));

        Map<String, String> smsKeys = SmsUtil.getSmsKeys("police", "en");

        for (Map.Entry<String, String> map : smsKeys.entrySet()) {
            System.out.println(map.getKey() + "= [" + map.getValue() + "]");
        }
    }

}
