package com.cyberscraft.uep.mdm.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

/**
 * <p>
 * 管理员组关联表
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2019-09-11
 */
@TableName("t_sys_user_group")
public class SysUserGroupEntity implements Serializable {

    private static final long serialVersionUID=1L;

   @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 管理员标识
     */
    private String loginId;

    /**
     * 组标识
     */
    private Long groupId;
    /**
     * 租户ID
     */
    private String tenantId;

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLoginId() {
        return loginId;
    }

    public void setLoginId(String loginId) {
        this.loginId = loginId;
    }

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    @Override
    public String toString() {
        return "SysUserGroupEntity{" +
        "id=" + id +
        ", loginId=" + loginId +
        ", groupId=" + groupId +
        "}";
    }
}
