package com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.parser;

import com.cyberscraft.uep.common.bean.Result;
import com.cyberscraft.uep.common.enums.ExceptionCodeEnum;
import com.cyberscraft.uep.common.enums.Platform;
import com.cyberscraft.uep.mdm.api.constant.ClientCmdType;
import com.cyberscraft.uep.mdm.api.dto.clientcmd.ClientCmdRequestDto;
import com.cyberscraft.uep.mdm.api.dto.clientcmd.EmptyClientCmdBody;
import com.cyberscraft.uep.mdm.api.dto.policy.CircleStrategyVO;
import com.cyberscraft.uep.mdm.core.domain.clientcmd.LegacyClientCmdRequest;
import com.cyberscraft.uep.mdm.core.domain.oauth2.AccessToken;
import com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.ILegacyCmdParser;
import com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.constant.CodeType;
import com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.constant.LegacyCommandCode;
import com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.constant.TemplatePropertisConstant;
import com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.constant.TemplateURIConstant;
import com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.domain.CircleOpero;
import com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.domain.FreNet;
import com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.domain.OperID;
import com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.sax.RequestBeanSaxHandler;
import org.springframework.stereotype.Component;
import org.thymeleaf.context.Context;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/***
 *
 * @date 2021-11-26
 * <AUTHOR>
 ***/
@Component
public class GetCircleOperCmdParser extends AbstractCmdParser implements ILegacyCmdParser {

    /***
     *
     */
    private final static Set<String> SUPPORTED_CMD_CODES = new HashSet<>();


    private static final String[] circleTypelist = {"", "OnNetStraCircle", "BackuContactsCircle", "BackupCallRecordCircle", "BackupShortMessageCircle", "ScanStra"};


    static {
        SUPPORTED_CMD_CODES.add(String.valueOf(LegacyCommandCode.GET_CIRCLEOPER.getCmdCode()));
    }

    @Override
    public Set<String> getCommandCodes() {
        return SUPPORTED_CMD_CODES;
    }

    @Override
    public boolean isSupported(String cmdCode) {
        return SUPPORTED_CMD_CODES.contains(cmdCode);
    }

    @Override
    public ClientCmdRequestDto parseClientCmdRequestDto(LegacyClientCmdRequest request, Platform platform) {
        EmptyClientCmdBody cmdBody = new EmptyClientCmdBody();
        //Element root = getRootElement(request.getCommandBody());
        //OperID operID = getOperIDFromDom(root);

        RequestBeanSaxHandler handler = getRequestBeanHandler(request.getCommandBody());
        OperID operID = handler.getOperID();
        //验证访问Token
        AccessToken token = validateAccessToken(operID.getClientUID());

        cmdBody.setUdid(token.getUdid());
        cmdBody.setUserId(token.getUserId());
        cmdBody.setUemVersion(operID.getUemVersion());
        cmdBody.setClientVersion(operID.getClientVersion());
        cmdBody.setClientBuildNum(operID.getClientBuildNum());
        cmdBody.setPlatform(platform.getValue());

        ClientCmdRequestDto cmdRequestDto = new ClientCmdRequestDto();
        cmdRequestDto.setCmdBody(cmdBody);
        cmdRequestDto.setType(ClientCmdType.GET_CIRCLEOPER.getCode());
        cmdRequestDto.setPlatform(platform);
        cmdRequestDto.setUdid(token.getUdid());
        return cmdRequestDto;
    }

    @Override
    public String parseOutput(Result result, Platform platform, ClientCmdRequestDto requestDto) {
        OperID operID = new OperID();
        List<CircleOpero> circleOperoList = new ArrayList<>();
        if (ExceptionCodeEnum.SUCCESS.getCode().equalsIgnoreCase(result.getCode())) {
            operID.setResult(String.valueOf(CodeType.SUCCESS.getValue()));

            List<CircleStrategyVO> list = (List<CircleStrategyVO>) result.getData();

            for (CircleStrategyVO circleStrategy : list) {
                CircleOpero circleOpero = new CircleOpero();
                FreNet freNet = new FreNet();
                circleOpero.setFreNet(freNet);
                circleOpero.setDownVersion(circleStrategy.getVersion());
                if (circleStrategy.getCircleType() != null && circleStrategy.getCircleType() < circleTypelist.length) {
                    circleOpero.setName(circleTypelist[circleStrategy.getCircleType()]);
                } else {
                    circleOpero.setName("");
                }
                freNet.setIntevalFre(circleStrategy.getInteval());
                freNet.setOnNetBeg(circleStrategy.getOnNetBeg());
                freNet.setPeriod(circleStrategy.getPeriod());
                freNet.setStatus(circleStrategy.getUpdateLocation());
                freNet.setValidDay(circleStrategy.getValidDay());
                freNet.setFenceInterval(circleStrategy.getAnFenceInterval());

                circleOperoList.add(circleOpero);
            }

        } else {
            operID.setResult(String.valueOf(CodeType.FAIL.getValue()));
            operID.setExtra(result.getCode() + result.getMessage());
        }
        //reply.setOperID(operID);
        Context context = new Context();

        context.setVariable(TemplatePropertisConstant.CIRCLEOPEROLIST, circleOperoList);
        context.setVariable(TemplatePropertisConstant.OPERID, operID);
        String templateName = getTemplateName(platform);
        return legacyContentRender.render(context, templateName);
    }

    /***
     *
     * @param platform
     * @return
     */
    private String getTemplateName(Platform platform) {
        String templateName = TemplateURIConstant.CIRCLE_OPERO_XML;
        return templateName;
    }
}
