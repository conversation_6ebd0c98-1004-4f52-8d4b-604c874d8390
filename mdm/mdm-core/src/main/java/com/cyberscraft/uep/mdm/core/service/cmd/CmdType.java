package com.cyberscraft.uep.mdm.core.service.cmd;

import com.cyberscraft.uep.common.enums.Platform;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 原子化的指令处理类型，客户端可识别
 * 以下指令的数字代码，参考自原有EMM系统中的定义，见LegacyCmdType.java文件
 * 在该文件中，iOS平台的指形如1xx，Android平台的指令形如2xx
 * 由此，重新定义原则是，通用的指令，为100以下的，即Android和iOS平台通用的
 * 如果是平台特有的指令，则iOS为1xx，Android为2xx
 */

public enum CmdType {
    // 通用指令定义
    REFRESH_DEVICE("1"),
    DEVICE_LOCATION("2"),
    LOCK_SCREEN("11"),
    UNLOCK_SCREEN("4"),
    SEND_MSG("5"),
    RING("1"),
    CLEAR_DEVICE("7"),
    ERASE_DEVICE("14"),
    WIPE_DEVICE("13"),//擦除设备指令，android使用

    INSTALL_POLICY("-1"),//TODO： 安装策略的cmd实现有误，目前服务器将各策略对应的PolicySubCategory的clientCmd发到了客户端，实际上应该是用这里的cliengtCmd，所以暂时将此处的clientCmd设置为-1，表示有错误
    DELETE_POLICY("24"),
    PUSH_MDM_PAYLOAD("19"),//MDM级别的策略，andorid 使用该clientCmd来安装mdm策略
    DISABLE_MDM("-2"),//取消MDM激活，目前仅有IOS支持

    INFO_QUERY_USER_PROFILE("20"),
    APP_CONFIG("33"),
    UPLOAD_DIAGNOSE_LOG("38"),

    // iOS平台MDM指令定义
    IOS_DEVICE_CLEAR("107"),
    IOS_EDATA_CLEAR("108"),
    IOS_INSTALL_PROFILE("109"),
    IOS_UNINSTALL_PROFILE("116"),
    IOS_PUSH_APPCONF("123"),
    IOS_INSTALL_FENCEPROFILE("144"),
    IOS_DEL_FENCEPROFILE("145"),
    IOS_DOC_ADD_NOTIFY("113"),
    IOS_DOC_UPDATE_NOTIFY("112"),

    CLEAR_APP_DATA("126"),



    WN_WAKE_UP("599"),
    ;

    private final String clientCmd;

    CmdType(String clientCmd) {
        this.clientCmd = clientCmd;
    }

    /**
     * Get the integer value of this enum value, as defined in the Thrift IDL.
     */
    public String getClientCmd() {
        return clientCmd;
    }

    private static List<CmdType> iosMDMWakeUpCmd = new ArrayList<>();
    static {
        Collections.addAll(iosMDMWakeUpCmd, LOCK_SCREEN);
        Collections.addAll(iosMDMWakeUpCmd, WIPE_DEVICE);
        Collections.addAll(iosMDMWakeUpCmd, PUSH_MDM_PAYLOAD);
        Collections.addAll(iosMDMWakeUpCmd, DISABLE_MDM);
    }
    private static List<CmdType> iosAppWakeUpCmd = new ArrayList<>();
    static {
        Collections.addAll(iosAppWakeUpCmd, UPLOAD_DIAGNOSE_LOG);
    }
    
    private static List<CmdType> androidWakeUpCmd = new ArrayList<>();
    static {
        Collections.addAll(androidWakeUpCmd, LOCK_SCREEN);
    }
    private static List<CmdType> windowsMDMCmd = new ArrayList<>();
    static {
        Collections.addAll(windowsMDMCmd, LOCK_SCREEN);
    }
    
    public static boolean isIosMDMWakeUpCmd(Platform platform, CmdType cmdType) {
        return Platform.IOS == platform && iosMDMWakeUpCmd.contains(cmdType);
    }
    
    public static boolean isIosAppWakeUpCmd(Platform platform, CmdType cmdType) {
        return Platform.IOS == platform && iosAppWakeUpCmd.contains(cmdType);
    }
    
    public static boolean isAndroidWakeUpCmd(Platform platform, CmdType cmdType) {
        return Platform.ANDROID == platform && androidWakeUpCmd.contains(cmdType);
    }
    
    public static boolean isWindowsMDMCmd(Platform platform, CmdType cmdType) {
        return Platform.WINDOWS == platform && windowsMDMCmd.contains(cmdType);
    }
}
