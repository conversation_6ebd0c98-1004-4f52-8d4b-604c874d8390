package com.cyberscraft.uep.mdm.core.dbo.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cyberscraft.uep.mdm.api.errors.MdmErrorType;
import com.cyberscraft.uep.mdm.core.dao.DeviceRemoteControlDao;
import com.cyberscraft.uep.mdm.core.dbo.DeviceRemoteControlDBO;
import com.cyberscraft.uep.mdm.core.entity.DeviceRemoteControlEntity;
import com.cyberscraft.uep.mdm.core.exception.MdmException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 设备远程控制指令表 服务实现类
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2019-08-28
 */
@Service
public class DeviceRemoteControlDBOImpl extends ServiceImpl<DeviceRemoteControlDao, DeviceRemoteControlEntity> implements DeviceRemoteControlDBO {


    @Resource
    private DeviceRemoteControlDao deviceRemoteControlDao;

    @Override
    public List<DeviceRemoteControlEntity> getListByDevice(Long deviceId) throws MdmException {
        if (deviceId == null) {
            return null;
        }
        LambdaQueryWrapper<DeviceRemoteControlEntity> queryWrapper = new QueryWrapper().lambda();
        queryWrapper.eq(DeviceRemoteControlEntity::getDeviceId, deviceId);
        return this.deviceRemoteControlDao.selectList(queryWrapper);
    }

    @Override
    public List<DeviceRemoteControlEntity> getListByDevice(List<Long> deviceIds) throws MdmException {
        if (deviceIds == null || deviceIds.size() == 0) {
            return null;
        }
        LambdaQueryWrapper<DeviceRemoteControlEntity> queryWrapper = new QueryWrapper().lambda();
        queryWrapper.in(DeviceRemoteControlEntity::getDeviceId, deviceIds);
        return this.deviceRemoteControlDao.selectList(queryWrapper);
    }

    @Override
    public DeviceRemoteControlEntity getByDevice(Long deviceId) throws MdmException {
        if (deviceId == null) {
            throw new MdmException(MdmErrorType.DEVICE_ID_INVALID);
        }
        LambdaQueryWrapper<DeviceRemoteControlEntity> queryWrapper = new QueryWrapper().lambda();
        queryWrapper.eq(DeviceRemoteControlEntity::getDeviceId, deviceId);
        List<DeviceRemoteControlEntity> list = this.deviceRemoteControlDao.selectList(queryWrapper);
        return list != null && list.size() > 0 ? list.get(0) : null;
    }
}
