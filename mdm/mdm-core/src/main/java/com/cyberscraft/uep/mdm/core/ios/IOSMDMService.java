package com.cyberscraft.uep.mdm.core.ios;

import com.cyberscraft.uep.mdm.core.entity.DeviceEntity;
import com.cyberscraft.uep.mdm.core.entity.PolicyEntity;

import java.io.InputStream;

public interface IOSMDMService {
    /**
     * 返回服务器证书，即服务器的HTTPS证书
     * @return
     */
    byte [] getServerCert();

    /**
     * 生成iOS设备OTA enroll的profile文件
     * @param challenge
     * @return
     */
    byte [] genEnrollProfile(String tid, String challenge);

    /**
     * 从输入流中解析出设备的简要信息
     * @param inStream
     * @return
     */
    IOSDeviceBrief parseDeviceBrief(InputStream inStream);

    /**
     * 生成SECP的profile，适用于激活流程中采用SCEP的情况
     * @param deviceBrief
     * @return
     */
    byte [] genScepProfile(String tid, IOSDeviceBrief deviceBrief);

    /**
     * 生成客户端身份识别证书的profile，适用于激活流程中不采用SCEP，而是采用个人数字证书的情况
     * @param deviceBrief
     * @return
     */
    byte [] genClientIdentProfile(String tid, IOSDeviceBrief deviceBrief);

    /**
     * 生成iOS MDM激活的主Profile
     * @param deviceBrief
     * @return
     */
    byte [] genMdmProfile(String tid, IOSDeviceBrief deviceBrief);

    /**
     * 从输入流中解析出"/checkin"请求的数据，适用于激活流程中的checkin环节
     * @param inStream
     * @return
     */
    IOSMDMCheckinRequest parseCheckInRequest(InputStream inStream);

    /**
     * 从输入流中解析出"/server"请求的数据，适用于iOS设备激活之后的业务处理环节
     * @param inStream
     * @return
     */
    IOSMDMServerRequest parseServerRequest(InputStream inStream);

    /**
     * 处理"/checkin"环节的业务逻辑
     * @param tid
     * @param checkinRequest
     * @return
     */
    int checkin(String tid, IOSMDMCheckinRequest checkinRequest);

    /**
     * 处理"/server"环节的业务逻辑
     * @param tid
     * @param serverRequest
     * @return
     */
    String server(String tid, IOSMDMServerRequest serverRequest);

    void triggerMDMPayload(boolean enableMDM, DeviceEntity entity, PolicyEntity policyEntity);

    void sendDisableMdmEvent(String udid);
}
