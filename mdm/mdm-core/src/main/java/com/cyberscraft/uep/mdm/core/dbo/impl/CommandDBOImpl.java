package com.cyberscraft.uep.mdm.core.dbo.impl;

import com.alibaba.fastjson.JSONObject;
import com.cyberscraft.uep.common.enums.ExceptionCodeEnum;
import com.cyberscraft.uep.common.enums.Platform;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.common.util.MD5Util;
import com.cyberscraft.uep.mdm.api.constant.PolicySubCategory;
import com.cyberscraft.uep.mdm.api.dto.policy.CombinedPolicyPayloadVO;
import com.cyberscraft.uep.mdm.api.dto.policy.PlatformPolicyVO;
import com.cyberscraft.uep.mdm.core.constant.CacheKeyConstant;
import com.cyberscraft.uep.mdm.core.dbo.CommandDBO;
import com.cyberscraft.uep.mdm.core.domain.cmd.CmdForClient;
import com.cyberscraft.uep.mdm.core.domain.cmd.CmdInfoDTO;
import com.cyberscraft.uep.mdm.core.entity.PolicyEntity;
import com.cyberscraft.uep.mdm.core.exception.MdmException;
import com.cyberscraft.uep.mdm.core.service.cmd.CmdType;
import com.cyberscraft.uep.mdm.core.service.cmd.ICommandProfileBuilder;
import com.cyberscraft.uep.mdm.core.service.policy.IPolicyService;
import com.cyberscraft.uep.mdm.core.service.policy.impl.GlobalPolicyServiceImpl;
import com.cyberscraft.uep.mdm.core.service.user.IGlobalPolicyCacheService;
import com.cyberscraft.uep.mdm.core.util.PolicyUtil;
import com.cyberscraft.uep.mdm.core.util.StringUtil;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 指令系統redis操作
 */
@Repository
public class CommandDBOImpl implements CommandDBO {

    private final static Logger log = LoggerFactory.getLogger(CommandDBOImpl.class);
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private IPolicyService policyService;

    @Resource
    private IGlobalPolicyCacheService globalPolicyCacheService;

    @Resource
    private ICommandProfileBuilder commandProfileBuilder;

    //private final static String CacheKeyConstant.PROFILE_KEY = "Q_CACHE_V2-_-ICMDPROF:%s";
    //private final static String CacheKeyConstant.PROFILE_KEYSEND = "Q_CACHE_V2-_-ICMDPROF_SEND:%s";
    public static final String WIN_Q2_PREFIX = "Running_Q2-_-ICMDS:";


    public String popWinCmdFromRunningQ2(String GUID) {
        String key = WIN_Q2_PREFIX + GUID;
        //RList<String> list = redissonClient.getList(key);
        //return list.size() > 0 ? list.remove(0) : null;
        return stringRedisTemplate.execute((RedisConnection connection) -> {
            byte[] rtn = connection.lPop(key.getBytes());
            if (null == rtn)
                return null;
            return new String(rtn);
        });
    }

    /**
     * 在指令中插入cmdnum（取系统时间（纳秒））
     *
     * @param cmd
     * @return
     */
    private static String InsertCmdNumber(String cmd) {
        StringBuffer rCmd = null;
        int indexCmdnum = cmd.indexOf("cmdnum");
        if (indexCmdnum == -1) {
            return null;
        }

        int start = cmd.indexOf(":", indexCmdnum);
        int end = cmd.indexOf(",", start);

        rCmd = new StringBuffer(cmd.substring(0, start + 1));
        rCmd.append(System.nanoTime());
        rCmd.append(cmd.substring(end, cmd.length()));

        return rCmd.toString();

    }

    /**
     * 在指令中插入cmdnum（取系统时间（纳秒））
     *
     * @param cmd
     * @return
     */
    private static String InsertCmdNumber(String cmd, long cmdNum) {
        StringBuilder rCmd = null;
        int indexCmdnum = cmd.indexOf("cmdnum");
        if (indexCmdnum == -1) {
            return null;
        }

        int start = cmd.indexOf(":", indexCmdnum);
        int end = cmd.indexOf(",", start);

        rCmd = new StringBuilder(cmd.substring(0, start + 1));
        rCmd.append(cmdNum);
        rCmd.append(cmd.substring(end, cmd.length()));

        return rCmd.toString();

    }


//    /**
//     * 检验字符串 是否为合法json格式
//     *
//     * @param jsonInString
//     * @return
//     */
//    public final static boolean isJSONValid(String jsonInString) {
//        try {
//            final ObjectMapper mapper = new ObjectMapper();
//            mapper.readTree(jsonInString);
//            return true;
//        } catch (IOException e) {
//            return false;
//        }
//    }


    /**
     * 截取指令的cmdNum
     *
     * @param cmd
     * @return
     */
    private long getCmdNumber(String cmd) {
        long cmdno = -1;
        int indexCmdnum = cmd.indexOf("cmdnum");
        if (indexCmdnum == -1) {
            return -1;
        }

        int start = cmd.indexOf(":", indexCmdnum);
        int end = cmd.indexOf(",", start);

        String sCmdno = cmd.substring(start + 1, end);
        if (sCmdno != null && !sCmdno.trim().equals("")) {
            cmdno = Long.parseLong(sCmdno.trim());
        }

        return cmdno;

    }

    @Override
    public List<String> getGlobalPolicyCommand(String tenantId, String policySubCategory) {
        if (StringUtils.isBlank(tenantId)) {
            return null;
        }
        HashOperations<String, String, String> hashOper = stringRedisTemplate.opsForHash();
        String key = CacheKeyConstant.getGlobalProfileKey(tenantId);
        // policySubCategory 为空拉取所有全局默认策略
        if (StringUtils.isBlank(policySubCategory)) {
            return hashOper.values(key);
        } else {
            return Arrays.asList(hashOper.get(key, policySubCategory));
        }
    }

    @Override
    public List<String> fullSyncDevicePolicies(Map<String, String> oldDeviceMd5Map, Map<String, String> globalMd5Map, String udid, Long userId, Integer platform) {
        String tenantId = TenantHolder.getTenantCode();
        if (StringUtil.isEmpty(tenantId)){
            log.error("tenantId is empty");
            throw new MdmException(ExceptionCodeEnum.TENANT_CODE_EMPTY);
        }

        Map<String, PolicyEntity> userPolicyMap = policyService.getUserPolicies(userId);

        Map<String, String> userPolicyMd5Map = new HashMap<>();
        for (Map.Entry<String, PolicyEntity> entry: userPolicyMap.entrySet()){
            userPolicyMd5Map.put(entry.getKey(), MD5Util.md5(entry.getValue().getContent()));
        }

        Map<String, String> mergedMd5Map = globalMd5Map;
        //合并两个md5 map，以userPolicyMd5Map优先级更高
        mergedMd5Map.putAll(userPolicyMd5Map);

        List<String> commands = Lists.newArrayList();

        for (Map.Entry<String, String> entry: mergedMd5Map.entrySet()){
            if (oldDeviceMd5Map.get(entry.getKey()) == null){
                //服务器有该类型，但是客户端没有，需要拉取最新的
                log.info("pull new policy {}: tenant {} device {} user {}", entry.getKey(), tenantId, udid, userId);
                addCommand(commands, tenantId, platform, entry.getKey(), userPolicyMap.get(entry.getKey()));
            }else if (oldDeviceMd5Map.get(entry.getKey()).equals(entry.getValue())){
                //服务器有该类型，客户端有，checksum相同，无须拉取
                log.info("pull new policy {}: tenant {} device {} user {}, no changes, not returned", entry.getKey(), tenantId, udid, userId);
            }else{
                //服务器有该类型，客户端有，checksum不同，需要拉取
                log.info("pull updated policy {}: tenant {} device {} user {}", entry.getKey(), tenantId, udid, userId);
                addCommand(commands, tenantId, platform, entry.getKey(), userPolicyMap.get(entry.getKey()));
            }
        }
        return commands;
    }

    private void addCommand(List<String> commands, String tenantId, Integer platform, String policySubCategory, PolicyEntity policy) {
        if (policy  == null) {//该policySubCategory无自定义策略，使用全局策略
            String policyContent = globalPolicyCacheService.getPolicy(tenantId, policySubCategory);
            if (policyContent != null){
                commands.add(policyContent);
            }else{
                log.info("tenant {} policySubCategory {} global policy not found", tenantId, policySubCategory);
            }
            return;
        }

        CombinedPolicyPayloadVO vo = PolicyUtil.toVO(policy);
        Platform platformEnum = Platform.findByValue(platform);
        PlatformPolicyVO platformPolicyVO = vo.getPlatformPolicyVO(platformEnum);

        //使用用户自定义策略
        CmdInfoDTO cmdInfo = new CmdInfoDTO();
        cmdInfo.setFlowNum(policy.getId());
        cmdInfo.setTenantId(TenantHolder.getTenantCode());
        cmdInfo.setCmdType(CmdType.INSTALL_POLICY);
        cmdInfo.setPolicyId(policy.getId());
        cmdInfo.setPolicySubCategory(PolicySubCategory.findNameByValue(policy.getSubCategory()));
        cmdInfo.setCustomized(platformPolicyVO.isCustomized());
        cmdInfo.setIsGlobalPolicy("false");
        cmdInfo.setCmdBody(vo.getClientPayloadAsString(platformEnum));
        String policyProfile = commandProfileBuilder.buildCmdProfile(platformEnum, cmdInfo);

        @SuppressWarnings("unchecked")
        CmdForClient<JSONObject> cmd = JsonUtil.str2Obj(policyProfile, CmdForClient.class);
        cmd.getHeader().setCmdnum(GlobalPolicyServiceImpl.NEW_PULL_CMD_NUM);
        String command = JsonUtil.obj2Str(cmd);
        log.debug("add {} command {}", policySubCategory, command);
        commands.add(command);
    }
}
