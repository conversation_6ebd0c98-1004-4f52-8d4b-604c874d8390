package com.cyberscraft.uep.mdm.core.service.cmd.impl.ios;

import com.cyberscraft.uep.common.enums.Platform;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.mdm.core.domain.cmd.CmdInfoDTO;
import com.cyberscraft.uep.mdm.core.service.cmd.CmdType;
import com.cyberscraft.uep.mdm.core.service.cmd.impl.common.CommonProfileServiceImpl;
import com.cyberscraft.uep.mdm.core.service.cmd.impl.ios.model.IOSCommand;
import com.cyberscraft.uep.mdm.core.service.cmd.impl.ios.model.RequestType;
import org.springframework.stereotype.Service;

/**
 * 生成ios设备锁定plist
 */
@Service
public class IosWipeDeviceProfileServiceImpl extends CommonProfileServiceImpl {
    @Override
    public String genProfile(CmdInfoDTO cmdInfo) {
        //所有的commandUUID，都需要在获取时，根据redis zset score生成
        IOSCommand command = new IOSCommand(RequestType.EraseDevice);
        return JsonUtil.obj2Str(command);
    }
    
    @Override
    public String getServiceKey() {
        return CmdType.WIPE_DEVICE.name()+"_"+Platform.IOS.getValue();
    }
}
