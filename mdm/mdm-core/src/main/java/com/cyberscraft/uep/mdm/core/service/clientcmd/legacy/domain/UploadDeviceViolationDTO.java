package com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.domain;

import java.io.Serializable;

public class UploadDeviceViolationDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private int violationStatus;
    private int violationType;
    private int violationProcess;
    private long violationTime;

    public int getViolationStatus() {
        return violationStatus;
    }

    public void setViolationStatus(int violationStatus) {
        this.violationStatus = violationStatus;
    }

    public int getViolationType() {
        return violationType;
    }

    public void setViolationType(int violationType) {
        this.violationType = violationType;
    }

    public int getViolationProcess() {
        return violationProcess;
    }

    public void setViolationProcess(int violationProcess) {
        this.violationProcess = violationProcess;
    }

    public long getViolationTime() {
        return violationTime;
    }

    public void setViolationTime(long violationTime) {
        this.violationTime = violationTime;
    }

}
