package com.cyberscraft.uep.mdm.core.constant;

/***
 * 定义平台所用的Token类型
 * @date 2021-09-29
 * <AUTHOR>
 ***/
public enum  TokenType {
    /***
     * 前端APP使用的Token
     */
    APP(0),
    /***
     * 自服务平台使用的Token
     */
    SELF(1),
    /***
     * 管理平台使用的Token
     */
    ADMIN(2);

    private Integer code;

    TokenType(Integer code) {
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }

}
