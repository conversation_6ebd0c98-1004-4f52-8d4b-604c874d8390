package com.cyberscraft.uep.mdm.core.dbo;

import java.util.List;
import java.util.Map;

public interface CommandDBO {

    public String popWinCmdFromRunningQ2(String GUID);
//
//    public List<String> getAllCommand(final String udid, long maxCmdNum);
//
//    public boolean addSendQCommand(final List<String> list, final String udid);
//
//    public List<String> removeSendCommand(final String udid, long maxSendNum);

//    public List<Object> deleteSendCommand(final String udid, long removeIndex);

//    public long getSendCommand(final String udid, long maxSendNum, List<String> cmdList);

    /**
     * 获取全局默认策略指令，每个租户tenantId的全局默认策略不同，<br>
     * 根据policySubCategory按类型获取，如果policySubCategory为空(null或空字符串)则返回所有全局默认策略
     * @param tenantId 租户ID
     * @param policySubCategory PolicySubCategory定义的策略类型，如 APP_CONFIG / DEVICE_VIOLATION / SENSITIVE_WORDS 等
     * @return
     */
    public List<String> getGlobalPolicyCommand(String tenantId, String policySubCategory);

//    public List<String> retrieveAllCommand(final String udid, long maxSendNum);

    //获取设备或用户下所有的策略
    List<String> fullSyncDevicePolicies(Map<String, String> oldMd5Map, Map<String, String> globalMd5Map, String udid, Long userId, Integer platform);
}
