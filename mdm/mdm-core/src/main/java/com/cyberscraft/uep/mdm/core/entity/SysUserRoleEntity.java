package com.cyberscraft.uep.mdm.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

/**
 * <p>
 * 系统用户角色表
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2019-09-11
 */
@TableName("t_sys_user_role")
public class SysUserRoleEntity implements Serializable {

    private static final long serialVersionUID=1L;

   @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户名
     */
    private String loginId;

    /**
     * 角色id
     */
    private Long roleId;

    /**
     * 租户ID
     */
    private String tenantId;

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLoginId() {
        return loginId;
    }

    public void setLoginId(String loginId) {
        this.loginId = loginId;
    }

    public Long getRoleId() {
        return roleId;
    }

    public void setRoleId(Long roleId) {
        this.roleId = roleId;
    }

    @Override
    public String toString() {
        return "SysUserRoleEntity{" +
        "id=" + id +
        ", loginId=" + loginId +
        ", roleId=" + roleId +
        "}";
    }
}
