package com.cyberscraft.uep.mdm.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 设备apn的token表
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2019-09-11
 */
@TableName("t_device_apn_token")
public class DeviceApnsTokenEntity implements Serializable {

    private static final long serialVersionUID=1L;

   @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 设备id
     */
    private Long deviceId;

    /**
     * 设备udid
     */
    private String udid;

    /**
     * apns的token
     */
    private String token;

    /**
     * apn的类型: 0 com.cyb.mdm 1 com.cyb.mcm
     */
    private Integer type;

    /**
     * 该token对应的app包名
     */
    private String pkgName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    /**
     * 租户ID
     */
    private String tenantId;

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(Long deviceId) {
        this.deviceId = deviceId;
    }

    public String getUdid() {
        return udid;
    }

    public void setUdid(String udid) {
        this.udid = udid;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getPkgName() {
        return pkgName;
    }

    public void setPkgName(String pkgName) {
        this.pkgName = pkgName;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "DeviceApnTokenEntity{" +
        "id=" + id +
        ", deviceId=" + deviceId +
        ", udid=" + udid +
        ", token=" + token +
        ", type=" + type +
        ", pkgName=" + pkgName +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
