package com.cyberscraft.uep.mdm.core.service.device.impl;

import com.cyberscraft.uep.common.enums.Platform;
import com.cyberscraft.uep.common.util.UUIDUtil;
import com.cyberscraft.uep.mdm.api.constant.Constant;
import com.cyberscraft.uep.mdm.api.dto.device.DeviceActivatedDto;
import com.cyberscraft.uep.mdm.core.exception.MdmException;
import com.cyberscraft.uep.mdm.core.service.device.IUdidGenerateService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.List;

/***
 *
 * @date 2021-10-24
 * <AUTHOR>
 ***/
@Service
public class UdidGenerateServiceImpl implements IUdidGenerateService {

    private final static Logger LOG = LoggerFactory.getLogger(UdidGenerateServiceImpl.class);

    @Override
    public String createUdid(DeviceActivatedDto deviceDto, Platform platform) throws MdmException {

        //如果传入了DeviceUuid，则用DeviceUuid值生成
        //如果传入了ssaid，则用ssaid，
        //如果传入了Imei，并且andrio版本大于6,则用该Imei值加一个空的wifi生成，
        //如果传入了Imei,wifiMac，则用imei,wifiMac生成
        //如果传入了Imei，则Imei值加一个空的wifi生成，
        //否则用serialNum，+（iemi,bluetoothMac,wifiMac）三个值中不为空的值用_连接起来的值，生成
        //String udid = null;
        //新增加逻辑，如果指定了extra，则代表客户端生成了udid
        if (StringUtils.isNotEmpty(deviceDto.getDeviceUuid())) {
            return deviceDto.getDeviceUuid();
            //return getUdid(deviceDto.getDeviceUuid(), "");
        }
        //适配Android Q
        if (StringUtils.isNotEmpty(deviceDto.getSsAid())) {
            return getUdid(deviceDto.getSsAid(), "");
        }
        if (StringUtils.isNotBlank(deviceDto.getImei()) && StringUtils.isNotBlank(deviceDto.getWifiMac())) {
            deviceDto.setImei(formatImei(deviceDto.getImei()));
            return getUdid(deviceDto.getImei(), deviceDto.getWifiMac());
        }

        if (StringUtils.isNotBlank(deviceDto.getImei())) {
            deviceDto.setImei(formatImei(deviceDto.getImei()));
            return getUdid(deviceDto.getImei(), Constant.NULL_MAC_ADDR);
        }
        deviceDto.setImei(formatImei(deviceDto.getImei()));
        return createUdid(deviceDto.getImei(), deviceDto.getBluetoothMac(), deviceDto.getWifiMac(), deviceDto.getSerialNum());

//TODO 熊祥众于2019-10-24日进行调整，去掉原来的对数据库的判断
        // MDMTFI-3153 【升级】设备重新激活后在设备列表不应显示两条记录 leiwuluan 2016-06-29
//        if (platform.equals(Platform.ANDROID)) {
//
//            // 安卓大于或等于6.0
//            if (StringUtils.isNotEmpty(deviceDto.getOsVersion()) && "6.0".compareToIgnoreCase(deviceDto.getOsVersion()) < 1) {
//                String nullMac = Constant.NULL_MAC_ADDR; //"02:00:00:00:00:00";
//                String tmpUdid = getUdid(deviceDto.getImei(), nullMac);
//
//                //DeviceEntity deviceTemp = deviceService.getNormalDeviceByUdid(tmpUdid);
//
////                LOG.debug("db has device ({}), udid={}", (deviceTemp == null), tmpUdid);
////                if (deviceTemp != null) {
//                    udid = tmpUdid;
////                }
//            }
//            if (udid == null) {
//                String tmpUdid = getUdid(deviceDto.getImei(), deviceDto.getWifiMac());
////                DeviceEntity tmpDevice = deviceService.getNormalDeviceByUdid(tmpUdid);
////
////                LOG.debug("imei_compare tmpUdid={}, imei={}", tmpUdid, deviceDto.getImei());
////
////                // leiwuluan 2017-08-30
////                // uem 4.4版本中imei多个时同时上传并使用逗号隔开。而4.4以下版本的就上传一个imei
////                // ume 升级到4.3时需要做udid兼容处理
////                if (tmpDevice == null && null != deviceDto.getImei()) {
////                    String imeiTemp = deviceDto.getImei().replaceAll("/", ",") + "," + deviceDto.getImei();
////                    List<String> imeiList = Arrays.asList(imeiTemp.split(","));
////                    List<DeviceEntity> devList = deviceService.getDevicesByImei(imeiList);
////                    if (devList != null && devList.size() > 1) {
////                        for (DeviceEntity dev : devList) {
////                            if (dev.getStatus() != DeviceStatusConstant.REGEISTED) {
////                                tmpDevice = dev;
////                                break;
////                            }
////                        }
////                    } else if (devList != null && devList.size() == 1) {
////                        tmpDevice = devList.get(0);
////                    }
////
////                    if (null != tmpDevice) {
////                        tmpUdid = tmpDevice.getUdid();
////                        if (StringUtils.isEmpty(tmpUdid)) {
////                            tmpUdid = createUdid(deviceDto.getImei(), deviceDto.getBluetoothMac(), deviceDto.getWifiMac(), deviceDto.getSerialNum());
////                        }
////                        LOG.debug("imei_compare tmpUdid={}, imei={}， dbImei={}", tmpUdid, imeiTemp, tmpDevice.getImei());
////                    }
////                }
////
////                if (tmpDevice != null) {
////                    udid = tmpUdid;
////                } else {
////                    udid = createUdid(deviceDto.getImei(), deviceDto.getBluetoothMac(), deviceDto.getWifiMac(), deviceDto.getSerialNum());
////                    LOG.debug("db has ({}), udid={}, bluetoothMac={},wifiMac={}, serialNum={}",
////                            (tmpDevice == null), tmpUdid, deviceDto.getBluetoothMac(), deviceDto.getWifiMac(), deviceDto.getSerialNum());
////                }
//            }
//        } else {
//            udid = getUdid(deviceDto.getImei(), deviceDto.getWifiMac());
//        }
//        return udid;
    }

    /***
     * 主要用于解决原来的历史问题多个imei值乱序的问题主要用于解决原来的历史问题多个imei值乱序的问题
     */
    private String formatImei(String imei) {
        if (StringUtils.isBlank(imei)) {
            return imei;
        }
        List<String> imeiList = Arrays.asList(imei.split("/"));
        if (imeiList.size() == 1) {
            return imei;
        }
        imeiList.sort((e1, e2) -> e1.compareToIgnoreCase(e2));
        StringBuilder sb = new StringBuilder();
        for (String temp : imeiList) {
            if (sb.length() > 0) {
                sb.append("/");
            }
            sb.append(temp);
        }
        return sb.toString();
    }

    /***
     *
     * @param imei
     * @param bluetoothMac
     * @param wifiMac
     * @param serialNum
     * @param args
     * @return
     */
    private String createUdid(String imei, String bluetoothMac, String wifiMac, String serialNum, String... args) {
        StringBuilder str2 = new StringBuilder();
        if (StringUtils.isNotEmpty(imei)) {
            str2.append("_").append(imei);
        }
        if (StringUtils.isNotEmpty(bluetoothMac)) {
            str2.append("_").append(bluetoothMac);
        }
        if (StringUtils.isNotEmpty(wifiMac)) {
            str2.append("_").append(wifiMac);
        }
        for (String arg : args) {
            if (StringUtils.isNotEmpty(arg)) {
                str2.append("_").append(arg);
            }
        }
        String udid = getUdid(serialNum, str2.toString());
        return udid;
    }

    /****
     * 生成UDID
     * @param imei
     * @param mac
     * @return
     */
    public String getUdid(String imei, String mac) {
        StringBuffer buf = new StringBuffer();
        MessageDigest mdi;
        String content = imei + mac;
        //如果没有任何硬件信息，取uuid后计算udid
        if (StringUtils.isEmpty(imei) && StringUtils.isEmpty(mac)) {
            content = UUIDUtil.getUUID();
        }
        try {
            mdi = MessageDigest.getInstance("SHA-1");
            mdi.update(content.getBytes());
            byte[] bytes = mdi.digest();
            for (int i = 0; i < bytes.length; i++) {
                String s = Integer.toHexString(bytes[i] & 0xff);
                if (s.length() == 1) {
                    buf.append("0");
                }
                buf.append(s);
            }
            return buf.toString();
        } catch (NoSuchAlgorithmException e) {
            LOG.error("getUdid error,imei:{}, mac:{}", imei, mac, e);
        }
        return null;
    }
}
