package com.cyberscraft.uep.mdm.core.domain.basic;

import java.io.Serializable;

/***
 * 系统定位地图配置
 * @date 2021-10-25
 * <AUTHOR>
 ***/
public class LocationCfg implements Serializable {

    /***
     * 是否允许开启定位
     */
    private Integer enabled=0;

    /***
     * 地图引擎类型
     */
    private Integer mapApi;

    /***
     * 定位周期
     */
    private Integer mapPeriod;

    /***
     *定位距离
     */
    private Integer locationDistance;

    /***
     * 定位频率单位为分钟
     */
    private Integer locationTime;


    public Integer getMapApi() {
        return mapApi;
    }

    public void setMapApi(Integer mapApi) {
        this.mapApi = mapApi;
    }

    public Integer getMapPeriod() {
        return mapPeriod;
    }

    public void setMapPeriod(Integer mapPeriod) {
        this.mapPeriod = mapPeriod;
    }

    public Integer getLocationDistance() {
        return locationDistance;
    }

    public void setLocationDistance(Integer locationDistance) {
        this.locationDistance = locationDistance;
    }

    public Integer getLocationTime() {
        return locationTime;
    }

    public void setLocationTime(Integer locationTime) {
        this.locationTime = locationTime;
    }

    public Integer getEnabled() {
        return enabled;
    }

    public void setEnabled(Integer enabled) {
        this.enabled = enabled;
    }
}
