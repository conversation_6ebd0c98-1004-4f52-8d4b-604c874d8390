package com.cyberscraft.uep.mdm.core.service.clientcmd.executor;

import com.cyberscraft.uep.common.constant.SysConstant;
import com.cyberscraft.uep.common.enums.Platform;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.mdm.api.constant.ClientCmdType;
import com.cyberscraft.uep.mdm.api.constant.PolicySubCategory;
import com.cyberscraft.uep.mdm.api.dto.clientcmd.ClientCmdBody;
import com.cyberscraft.uep.mdm.api.dto.clientcmd.ClientCmdRequestDto;
import com.cyberscraft.uep.mdm.api.dto.clientcmd.ProcessInfo;
import com.cyberscraft.uep.mdm.api.dto.clientcmd.UploadRisksCmdBody;
import com.cyberscraft.uep.mdm.api.dto.policy.BaselineBlacklistDto;
import com.cyberscraft.uep.mdm.api.dto.policy.SecurityBaselineDto;
import com.cyberscraft.uep.mdm.api.errors.MdmErrorType;
import com.cyberscraft.uep.mdm.core.constant.CacheKeyConstant;
import com.cyberscraft.uep.mdm.core.dbo.PolicyGlobalDBO;
import com.cyberscraft.uep.mdm.core.entity.DeviceEntity;
import com.cyberscraft.uep.mdm.core.entity.PolicyGlobalEntity;
import com.cyberscraft.uep.mdm.core.exception.MdmException;
import com.cyberscraft.uep.mdm.core.service.clientcmd.IClientCommandExecutor;
import com.cyberscraft.uep.mdm.core.service.risks.IRisksService;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: liuanyang
 * @Date: 2021/8/7
 */

@Component
public class UploadRiskCmdExecutor extends AbstractClientCmdExecutor implements IClientCommandExecutor {

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private PolicyGlobalDBO policyGlobalDBO;

    @Autowired(required = false)
    private IRisksService risksService;

    /****
     * 用于判断是否是指定指令的执行器
     * @param cmdCode
     * @return
     * @throws MdmException
     */
    @Override
    public boolean isSupport(String cmdCode) throws MdmException {
        return ClientCmdType.UPLOAD_RISK.getCode().equalsIgnoreCase(cmdCode);
    }

    /***
     * 执行客户端指令请求
     * @return
     * @throws Exception
     * @param requestDto
     */
    @Override
    public ClientCmdBody parseCmd(ClientCmdRequestDto requestDto) throws MdmException {
        UploadRisksCmdBody cmdBody = JsonUtil.str2ObjWithSnake(requestDto.getBody(), UploadRisksCmdBody.class);
        cmdBody.setUdid(requestDto.getUdid());
        cmdBody.setUserId(requestDto.getUserId());
        return cmdBody;
    }

    /***
     * 执行客户端指令请求
     * @param requestCmd
     * @param platform
     * @return
     * @throws Exception
     */
    @Override
    @Transactional
    public <T> T execute(ClientCmdBody requestCmd, Platform platform) throws MdmException {
        UploadRisksCmdBody cmdBody = (UploadRisksCmdBody) requestCmd;
        LOG.info("用户{}上传设备{}对应的风险检测", cmdBody.getUserId(), cmdBody.getUdid());

        DeviceEntity device = appDeviceService.getDeviceByUserAndUdid(cmdBody.getUserId(), cmdBody.getUdid());
        if (device == null) {
            throw new MdmException(MdmErrorType.DEVICE_UDID_INVALID);
        }

        //记录上报的数据-----start
        cmdBody.setId(device.getId());
        try {
//            risksService.updateDeviceRisks(cmdBody);
        } catch (Exception e) {
            LOG.warn("save risk body fail");
        }
        //记录上报的数据-----end

        device.setUpdateTime(LocalDateTime.now());
        device.setLastOnlineTime(LocalDateTime.now());
        //device.setJailbreakFlag(null);
        device.setUemVersion(cmdBody.getUemVersion());

        if (StringUtils.isNotBlank(cmdBody.getClientVersion())) {
            device.setClientVersion(cmdBody.getClientVersion());
        }
        if (StringUtils.isNotBlank(cmdBody.getClientBuildNum())) {
            device.setClientBuildNum(cmdBody.getClientBuildNum());
        }

        appDeviceService.modify(device);

        List<Integer> listeningPorts = new ArrayList<>();
        List<String> packageNames = new ArrayList<>();
//        Map<String,Object> packageNames=new HashMap<>();
        List<String> processNames = new ArrayList<>();

        PolicyGlobalEntity policyGlobalEntity = policyGlobalDBO.getBySubCategory(PolicySubCategory.SECURITY_BASELINE.getValue());
        if (policyGlobalEntity != null) {
            String content = policyGlobalEntity.getContent();
            SecurityBaselineDto securityBaselineDto = JsonUtil.str2Obj(content, SecurityBaselineDto.class);
            BaselineBlacklistDto blacklist = securityBaselineDto.getBlacklist();
            listeningPorts = blacklist.getListeningPorts();
            packageNames = blacklist.getPackageNames();
            processNames = blacklist.getProcessNames();
        }

        List<Map<String, Object>> up_installedAppList = cmdBody.getInstalledAppList();
        List<ProcessInfo> up_processList = cmdBody.getProcessList();
        List<Integer> up_listeningPorts = cmdBody.getListeningPorts();

        List<String> str_processList = new ArrayList<>();
        if (up_installedAppList == null) {
            up_installedAppList = new ArrayList<>();
        }
        if (up_processList != null) {
            str_processList = up_processList.stream().map(el -> el.getProcessName()).collect(Collectors.toList());
        }
        if (up_listeningPorts == null) {
            up_listeningPorts = new ArrayList<>();
        }
        packageNames.retainAll(up_installedAppList.stream().map(el -> el.get("app_name")).collect(Collectors.toList()));
        processNames.retainAll(str_processList);
        listeningPorts.retainAll(up_listeningPorts);

        Map<String, Object> map = new HashMap<>();
        map.put("packageNames", packageNames);
        map.put("processNames", processNames);
        map.put("listeningPorts", listeningPorts);

        String deviceZSBKey = CacheKeyConstant.getDeviceZSBKey(TenantHolder.getTenantCode(), cmdBody.getUdid());

        stringRedisTemplate.opsForValue().set(deviceZSBKey, JsonUtil.obj2Str(map));

        LOG.info("用户{}上传设备{}对应的风险检测完成", cmdBody.getUserId(), cmdBody.getUdid());
        return (T) SysConstant.TRUE;
    }
}
