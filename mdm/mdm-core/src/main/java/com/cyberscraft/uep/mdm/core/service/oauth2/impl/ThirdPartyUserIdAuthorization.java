package com.cyberscraft.uep.mdm.core.service.oauth2.impl;

import com.cyberscraft.uep.iam.dto.enums.UserStatus;
import com.cyberscraft.uep.mdm.api.dto.oauth2.AccessTokenRequestDto;
import com.cyberscraft.uep.mdm.api.errors.MdmErrorType;
import com.cyberscraft.uep.mdm.core.constant.GrantType;
import com.cyberscraft.uep.mdm.core.domain.user.UserInfo;
import com.cyberscraft.uep.mdm.core.exception.MdmException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/***
 *
 * @date 2021/10/19
 * <AUTHOR>
 ***/
@Component
public class ThirdPartyUserIdAuthorization extends AbstractAuthorization {

    @Override
    protected void validation(AccessTokenRequestDto dto) throws MdmException {
        if (StringUtils.isBlank(dto.getUsername())) {
            throw new MdmException(MdmErrorType.REQUEST_INVALID);
        }
        if (StringUtils.isBlank(dto.getAccountType())) {
            throw new MdmException(MdmErrorType.REQUEST_INVALID);
        }
    }

    @Override
    protected UserInfo getUser(AccessTokenRequestDto dto) throws MdmException {
        if (StringUtils.isBlank(dto.getUsername())) {
            throw new MdmException(MdmErrorType.REQUEST_INVALID);
        }
        if (StringUtils.isBlank(dto.getAccountType())) {
            throw new MdmException(MdmErrorType.REQUEST_INVALID);
        }
        UserInfo user = userService.getUserByThirdPartyUserId(dto.getUsername(), dto.getAccountType());
        if (user == null) {
            throw new MdmException(MdmErrorType.USER_NAME_INVALID.getCode(), "type=" + dto.getAccountType() + ",uid=" + dto.getUsername() + " is invalid");
        }
        if (UserStatus.isInactive(user.getStatus())) {
            throw new MdmException(MdmErrorType.USER_STATUS_INVALID.getCode(), "type=" + dto.getAccountType() + ",uid=" + dto.getUsername() + " status is invalid");
        }
        return user;
    }

    @Override
    public boolean isSupport(String grantType) {
        return GrantType.THIRDPARTY_USERID.getCode().equalsIgnoreCase(grantType);
    }
}
