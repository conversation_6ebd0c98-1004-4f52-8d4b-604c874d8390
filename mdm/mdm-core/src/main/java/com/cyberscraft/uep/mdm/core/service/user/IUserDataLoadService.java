package com.cyberscraft.uep.mdm.core.service.user;

import com.cyberscraft.uep.common.dto.PageView;
import com.cyberscraft.uep.common.dto.Pagination;
import com.cyberscraft.uep.mdm.api.dto.user.UserListLoadDetailDTO;
import com.cyberscraft.uep.mdm.api.dto.user.UserListVO;
import com.cyberscraft.uep.mdm.api.dto.user.UserQueryDTO;

import java.util.List;

/***
 * 用户扩展信息加载服务，用于加载用户相关的扩展信息，如策略生效状态，设备状态等
 * @date 2021/9/27
 * <AUTHOR>
 ***/
public interface IUserDataLoadService {


    /***
     * 查询用户分页
     * @param page
     * @param load
     * @return
     */
    PageView<UserListVO> getUsers(Pagination<UserQueryDTO> page, UserListLoadDetailDTO load);


    /***
     *
     * @param list
     * @param load
     * @return
     */
    List<UserListVO> reloadUserListData(List<UserListVO> list, UserListLoadDetailDTO load);
}
