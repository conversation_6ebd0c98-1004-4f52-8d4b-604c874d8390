package com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.domain;

import java.io.Serializable;

/***
 *
 * @date 2021-11-22
 * <AUTHOR>
 ***/
public class NetTraffic implements Serializable {

    private String wifiTolKB;
    private String celuarTolKB;
    private String date;

    public NetTraffic(){
        wifiTolKB = "0";
        celuarTolKB = "0";
        date = "";
    }

    public String getWifiTolKB() {
        return wifiTolKB;
    }

    public void setWifiTolKB(String wifiTolKB) {
        this.wifiTolKB = wifiTolKB;
    }

    public String getCeluarTolKB() {
        return celuarTolKB;
    }

    public void setCeluarTolKB(String celuarTolKB) {
        this.celuarTolKB = celuarTolKB;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }
}
