package com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.constant;

/***
 *
 * @date 2021-11-25
 * <AUTHOR>
 ***/
public enum ExcuteType {
    NOW(1),
    LAZY(2);

    private final int value;

    ExcuteType(int value) {
        this.value = value;
    }

    /**
     * Get the integer value of this enum value, as defined in the Thrift IDL.
     */
    public int getValue() {
        return value;
    }
}
