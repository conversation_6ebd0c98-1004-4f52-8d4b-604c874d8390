package com.cyberscraft.uep.mdm.core.service.config;

import com.cyberscraft.uep.mdm.core.domain.basic.NetworkCfg;
import com.cyberscraft.uep.mdm.core.exception.MdmException;

/***
 * 系统服务相关配置
 * @date 2021-09-16
 * <AUTHOR>
 ***/
public interface ISysConfiguration {

    /***
     * 得到系统配置的Push Server类型
     * 配置项名称：sys.conf.push.type
     * @return
     * @throws MdmException
     */
    String getPushServerType() throws MdmException;

    /***
     * 得到系统配置的Push Server类型
     * 配置项名称：sys.conf.push.mdmType
     * @return
     * @throws MdmException
     */
    String getMdmPushServerType() throws MdmException;

    /***
     * 得到当前应用对应的端口号
     * @return
     */
    int getApplicationServerPort();

    /***
     * 获取系统配置语言项
     * @return
     * @throws MdmException
     */
    String getLanguage() throws MdmException;

    /***
     * 激活邀请二维码中，客户端的下载地址
     * @return
     * @throws MdmException
     */
    String getDownLoadUrl() throws MdmException;

    /****
     * 得到可以操作的文件临时目录根目录
     * @return
     * @throws MdmException
     */
    String getTempFileRootPath() throws MdmException;

    String getIosAppStoreUse() throws MdmException;

    String getIosAppStoreAdd() throws MdmException;

    //
    String getProxyIp() throws MdmException;

    String getProxyPort() throws MdmException;

    String getHostname() throws MdmException;

    /***
     * 得到文件系统服务器名称
     * @return
     * @throws MdmException
     */
    String getFileServerHostname() throws MdmException;

    /***
     * 得到网络相关配置
     * @return
     * @throws MdmException
     */
    Integer getMdmHeartTime() throws MdmException;

    /***
     * 得到网络相关配置
     * @return
     * @throws MdmException
     */
    NetworkCfg getNetWorkCfg() throws MdmException;

    /***
     *策略自动更新周期(单位小时)
     * @return
     * @throws MdmException
     */
    Integer getPolicyUpdateTimeInterval() throws MdmException;

    /***
     * 允许删除激活数据
     * @return
     * @throws MdmException
     */
    Integer getAllowRemoveActiveData() throws MdmException;

    /***
     * 激活时是否显示License
     * @return
     * @throws MdmException
     */
    Integer getShowLicenseWhenActive() throws MdmException;

    /****
     * 用户号码通话白名单【启用/禁用】
     * @return
     * @throws MdmException
     */
    Integer getEnableContactsWhitelist() throws MdmException;

    /***
     * 全局通话白名单
     * @return
     * @throws MdmException
     */
    Integer getEnableDictContactsWhitelist() throws MdmException;


    /***
     * 亲情号通话白名单
     * @return
     * @throws MdmException
     */
    Integer getEnableFamilyNumberContactsWhitelist() throws MdmException;

    /***
     * 是否异步激活设备
     * @return
     * @throws MdmException
     */
    Boolean getEnableAsyncDeviceActivate() throws MdmException;
}
