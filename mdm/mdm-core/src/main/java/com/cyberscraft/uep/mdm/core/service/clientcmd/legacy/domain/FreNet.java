package com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.domain;

import java.io.Serializable;

/***
 *
 * @date 2021-11-21
 * <AUTHOR>
 ***/
public class FreNet implements Serializable {
    private String period;
    private String validDay;
    private String onNetBeg;
    private String intevalFre;
    private int status;
    private int fenceInterval;

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public String getValidDay() {
        return validDay;
    }

    public void setValidDay(String validDay) {
        this.validDay = validDay;
    }

    public String getOnNetBeg() {
        return onNetBeg;
    }

    public void setOnNetBeg(String onNetBeg) {
        this.onNetBeg = onNetBeg;
    }

    public String getIntevalFre() {
        return intevalFre;
    }

    public void setIntevalFre(String intevalFre) {
        this.intevalFre = intevalFre;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public int getFenceInterval() {
        return fenceInterval;
    }

    public void setFenceInterval(int fenceInterval) {
        this.fenceInterval = fenceInterval;
    }
}
