package com.cyberscraft.uep.mdm.core.dbo.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cyberscraft.uep.mdm.core.dao.VirusscanLatestDao;
import com.cyberscraft.uep.mdm.core.dbo.VirusscanLatestDBO;
import com.cyberscraft.uep.mdm.core.entity.VirusscanLatestEntity;
import com.cyberscraft.uep.mdm.core.exception.MdmException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 最新一次病毒扫描记录表 服务实现类
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2019-08-28
 */
@Service
public class VirusscanLatestDBOImpl extends ServiceImpl<VirusscanLatestDao, VirusscanLatestEntity> implements VirusscanLatestDBO {

    @Resource
    private VirusscanLatestDao virusscanLatestDao;

    @Override
    public List<VirusscanLatestEntity> getListByDevice(Long deviceId) throws MdmException {
        if (deviceId == null) {
            return null;
        }
        LambdaQueryWrapper<VirusscanLatestEntity> queryWrapper = new QueryWrapper().lambda();
        queryWrapper.eq(VirusscanLatestEntity::getDeviceId, deviceId);
        return this.virusscanLatestDao.selectList(queryWrapper);
    }

    @Override
    public List<VirusscanLatestEntity> getListByDevice(List<Long> deviceIds) throws MdmException {
        if (deviceIds == null || deviceIds.size() == 0) {
            return null;
        }
        LambdaQueryWrapper<VirusscanLatestEntity> queryWrapper = new QueryWrapper().lambda();
        queryWrapper.in(VirusscanLatestEntity::getDeviceId, deviceIds);
        return this.virusscanLatestDao.selectList(queryWrapper);
    }
}
