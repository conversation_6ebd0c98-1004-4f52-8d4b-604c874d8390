package com.cyberscraft.uep.mdm.core.dbo;

import com.cyberscraft.uep.mdm.core.entity.ViolationDeviceEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cyberscraft.uep.mdm.core.exception.MdmException;

/**
 * <p>
 * 违规设备表 服务类
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2019-08-28
 */
public interface ViolationDeviceDBO extends IService<ViolationDeviceEntity> {

    /***
     * 删除设备对应的违规信息
     * @param deviceId
     * @throws MdmException
     */
    void deleteByDevice(Long deviceId) throws MdmException;
}
