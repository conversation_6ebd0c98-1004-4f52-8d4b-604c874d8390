package com.cyberscraft.uep.mdm.core.util;

import org.apache.commons.lang3.StringUtils;

/**
 * Created by <PERSON> on 13/4/2015.
 */
public class MyStringUtils extends StringUtils {

    public static final String ZERO = "0";
    public static final String MINUS = "-";
    public static final String TRUE = "TRUE";
    public static final String STR_1001 = "1001";
    public static final String STR_2004 = "2004";
    public static final String STR_2007 = "2007";
    public static final String CLIENTID_HEADER = "<ClientUID>";
    public static final String CLIENTID_TAIL = "</ClientUID>";
    public static final String REDIS_CHANNEL = "MONITOR_INFO";
    public static final String JSON_KEY_VALUE_TMP = "'%s':'%s'";

    public static String stringEmpty2Zero(String para){
        if(StringUtils.isEmpty(para)){
            return ZERO;
        }
        return para;
    }
    public static int string2Int(String param){
        if (param == null || param.length() == 0) {
            return 0;
        }
        if(MINUS.equalsIgnoreCase(String.valueOf(param.charAt(0)))){
            if(StringUtils.isNumeric(param.substring(1))){
                return Integer.valueOf(MINUS + param.substring(1));
            }else{
                return 0;
            }
        }else{
            if(StringUtils.isNumeric(param)){
                return Integer.valueOf(param);
            }else{
                return 0;
            }
        }
    }
    
    public static int stringBoolean2Int(String param){
    	if (param == null || param.length() == 0) {
    		return 0;
    	}
    		if(StringUtils.isNumeric(param)){
    			return Integer.valueOf(param);
    		}else{
    			if(param.toUpperCase().equals(TRUE)){
    				return 1;
    			}
    			return 0;
    		}

    }

    public static int boolean2Int(Object param){
        if (param == null ||"null".equalsIgnoreCase(param.toString())) {
            return 0;
        } if (param instanceof Boolean){
            if ((Boolean)param){
                return 1;
            }else {
                return 0;
            }
        } else  {
            return 0;
        }

    }
//    public static ChannelBuffer string2ChannelBuffer(String param){
//        ChannelBuffer channelBuffer;
//        try {
//            channelBuffer = ChannelBuffers.buffer(ChannelBuffers.LITTLE_ENDIAN, param.getBytes().length);
//            channelBuffer.writeBytes(param.getBytes());
//        }catch (Exception ex){
//            throw new ChallengeDecodeFailException("Request protocol flag error. Decode fails");
//        }
//        return channelBuffer;
//    }

    /**
     * 传入键值对，生成 key:value 形式的字符
     * @param key
     * @param value
     * @return String key:value
     */
    public static String addJsonPairAndSymbol(String key, String value, String Symbol){
    	if(null != key && null != value){
	    	String jsonString = JSON_KEY_VALUE_TMP;
	    	return String.format(jsonString, key,value)+Symbol;
    	}else{
    		return "";
    	}
    }
	/**
	 * 传入键值对，生成 key:value 形式的字符
	 * @param key
	 * @param value
	 * @return String key:value
	 */
    public static String addJsonPair(String key, String value){
    	String jsonString = JSON_KEY_VALUE_TMP;
    	return String.format(jsonString, key,value);
    }
    
    public static boolean isEmptyOrNullString(String str){
    	return StringUtils.isEmpty(str) || "null".toUpperCase().equals(str.toUpperCase());
    }

    public static String toStringNullToEmptyString(Object obj){
    	try{
	    	if(obj == null||"null".equalsIgnoreCase(obj.toString())){
	    		return "";
	    	}
	    	String str = obj.toString();
	    	return StringUtils.isBlank(str)? "":str;
    	}catch(Exception e){
    		return "";
    	}
    }
}
