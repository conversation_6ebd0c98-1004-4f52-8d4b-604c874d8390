package com.cyberscraft.uep.mdm.core.service.clientcmd.executor;

import com.cyberscraft.uep.common.constant.SysConstant;
import com.cyberscraft.uep.common.enums.Platform;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.mdm.api.constant.ClientCmdType;
import com.cyberscraft.uep.mdm.api.dto.clientcmd.ClientCmdBody;
import com.cyberscraft.uep.mdm.api.dto.clientcmd.ClientCmdRequestDto;
import com.cyberscraft.uep.mdm.api.dto.clientcmd.UpdateBatteryCmdBody;
import com.cyberscraft.uep.mdm.api.dto.clientcmd.UploadViolationCmdBody;
import com.cyberscraft.uep.mdm.core.exception.MdmException;
import com.cyberscraft.uep.mdm.core.service.clientcmd.IClientCommandExecutor;
import com.cyberscraft.uep.mdm.core.service.device.IViolationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class UploadDeviceViolationCmdExecutor extends AbstractClientCmdExecutor implements IClientCommandExecutor {
    private final static Logger logger = LoggerFactory.getLogger(UploadDeviceViolationCmdExecutor.class);

    @Resource
    private IViolationService violationService;

    private final static String SUPPORTED_CMD_TYPE = ClientCmdType.UPLOAD_VIOLIATION.getCode();

    @Override
    public boolean isSupport(String cmdCode) throws MdmException {
        return SUPPORTED_CMD_TYPE.equalsIgnoreCase(cmdCode);
    }

    @Override
    public ClientCmdBody parseCmd(ClientCmdRequestDto requestDto) throws MdmException {
        UploadViolationCmdBody cmdBody = JsonUtil.str2ObjWithSnake(requestDto.getBody(), UploadViolationCmdBody.class);
        cmdBody.setUdid(requestDto.getUdid());
        cmdBody.setUserId(requestDto.getUserId());
        return cmdBody;
    }

    @Override
    public <T> T execute(ClientCmdBody request, Platform platform) throws MdmException {
        UploadViolationCmdBody cmdBody = (UploadViolationCmdBody)request;
        violationService.uploadDeviceViolation(cmdBody);
        return (T) SysConstant.TRUE;
    }
}
