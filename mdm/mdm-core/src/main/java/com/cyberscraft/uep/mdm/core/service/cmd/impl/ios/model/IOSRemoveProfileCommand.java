package com.cyberscraft.uep.mdm.core.service.cmd.impl.ios.model;

import com.alibaba.fastjson.annotation.JSONType;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.dd.plist.NSDictionary;

@JSONType(typeName = "RemoveProfile", serialzeFeatures = SerializerFeature.WriteClassName)
public class IOSRemoveProfileCommand extends IOSCommand{
    private String identifier;

    public IOSRemoveProfileCommand(String identifier) {
        super(RequestType.RemoveProfile);
        this.identifier = identifier;
    }

    public String getIdentifier() {
        return identifier;
    }

    public void setIdentifier(String payload) {
        this.identifier = identifier;
    }

    @Override
    public NSDictionary toCmdDictionary(){
        /**
         *      标准品的逻辑
         *      IOSDelPolicyCmd cmd = (IOSDelPolicyCmd) baseCmd;
         * 		NSDictionary cdict = new NSDictionary();
         * 		cdict.put("CommandUUID", cmd.getUuid());
         *
         * 		NSDictionary cmddict = new NSDictionary();
         * 		cmddict.put("RequestType", "RemoveProfile");
         * 		cmddict.put("Identifier", cmd.getPayloadIdentifier());
         * 		cdict.put("Command", cmddict);
         * 		String cmdProfile = cdict.toXMLPropertyList();
         * 		log.debug("del policy Profile:"+ cmdProfile);
         */
        NSDictionary cmddict = super.toCmdDictionary();
        cmddict.put("Identifier", identifier);
        return cmddict;
    }
}
