package com.cyberscraft.uep.mdm.core.service.cmd;

import com.cyberscraft.uep.common.enums.Platform;
import com.cyberscraft.uep.mdm.core.domain.cmd.CmdInfoDTO;

/**
 * 指令构造接口
 * 
 * <AUTHOR>
 * @date Created：2019-10-11
 */
public interface ICommandProfileBuilder {
    String ANDROID_WAKE_UP_CMD =
            "{\"header\":{\"version\":\"V2\", \"cmdnum\":0, \"cmd\":[\"-1\"], \"flownum\":\"%s\"}, \"body\":{}, \"aps\":{}}";
    String IOS_APP_WAKE_UP_CMD =
            "{\"header\":{\"version\":\"V2\",\"cmdnum\":0,\"cmd\":[\"-1\"],\"flownum\":\"%s\"},\"body\":{}," +
                    "\"aps\":" +
                    "   {\"content-available\":1," +
                    "   \"alert\":\"UEM notification\"}" +
                    "}";
    String IOS_MDM_WAKE_UP_CMD =
            "{\"header\":{\"version\":\"V2\",\"cmdnum\":0,\"cmd\":[\"-1\"],\"flownum\":\"%s\"},"
            + "\"body\":{},\"aps\":{\"content-available\":1,\"alert\":\"\"}}";
    String WINDOWS_WAKE_UP_CMD =
            "{\"header\":{\"version\":\"V2\",\"cmdnum\":0,\"cmd\":[\"-1\"],\"flownum\":\"%s\"},\"body\":{}}";

    String INFO_NOTIFY = "{\"header\":{\"version\":\"V2\",\"cmdnum\":0,\"cmd\":[\"%s\"],\"flownum\":\"%s\"},\"body\":{}}";
    String IOS_INFO_NOTIFY = "{\"header\":{\"version\":\"V2\",\"cmdnum\":0,\"cmd\":[\"%s\"],\"flownum\":\"%s\"},\"body\":{}," +
            "\"aps\":" +
            "   {\"content-available\":1," +
            "   \"alert\":\"UEM notification\"}" +
            "}";

    /**
     * 构造非策略类型的指令profile:不需要唤醒的指令profile不用存缓存，直接推送给设备；需要唤醒指令profile构造后要存入缓存
     * @param platform
     * @param cmdInfo
     * @return
     */
    String buildCmdProfile(Platform platform, CmdInfoDTO cmdInfo);
    
    /**
     * 构造唤醒指令profile，生成唤醒指令。
     * 唤醒指令分为：Androd唤醒，IOS MDM唤醒(唤醒苹果MDM系统), IOS客户端唤醒(唤醒EMM客户端). 
     * 对于IOS MDM 唤醒,生成的指令为空
     * @param platform
     * @param cmdInfo
     * @return
     */
    String buildWakeUpCmdProfile(Platform platform, CmdInfoDTO cmdInfo);

    /**
     * 构造一次性指令profile。
     * @param platform
     * @param cmdInfo
     * @return
     */
    String buildActionNotifyProfile(Platform platform, CmdInfoDTO cmdInfo);

}
