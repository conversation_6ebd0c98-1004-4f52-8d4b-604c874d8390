package com.cyberscraft.uep.mdm.core.service.admin.impl;

import com.cyberscraft.uep.mdm.core.constant.CacheNameConstant;
import com.cyberscraft.uep.mdm.core.domain.sysuser.AdminGroupId;
import com.cyberscraft.uep.mdm.core.exception.MdmException;
import com.cyberscraft.uep.mdm.core.service.admin.IAdminGroupIdCacheService;
import com.cyberscraft.uep.mdm.core.service.common.ICacheService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/***
 *
 * @date 2021-09-25
 * <AUTHOR>
 ***/
@Service
public class AdminGroupIdCacheServiceImpl implements IAdminGroupIdCacheService {


    /***
     * 系统中的缓存接口
     */
    @Resource
    private ICacheService cacheService;

    @Override
    public void saveAdminGroupId(AdminGroupId obj) throws MdmException {
        cacheService.save(CacheNameConstant.ADMIN_GROUPID_CACHE, obj.getLoginId(), obj);
    }

    @Override
    public AdminGroupId getAdminGroupId(String loginId) throws MdmException {
        return cacheService.get(CacheNameConstant.ADMIN_GROUPID_CACHE, loginId, AdminGroupId.class);
    }

    @Override
    public void clear() {
        cacheService.clear(CacheNameConstant.ADMIN_GROUPID_CACHE);
    }
}
