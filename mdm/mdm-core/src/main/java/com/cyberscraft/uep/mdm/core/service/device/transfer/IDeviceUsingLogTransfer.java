package com.cyberscraft.uep.mdm.core.service.device.transfer;

import com.cyberscraft.uep.mdm.api.dto.device.DeviceUsingLogVO;
import com.cyberscraft.uep.mdm.core.dbo.DeviceUsingLogDBO;
import com.cyberscraft.uep.mdm.core.entity.DeviceUsingLogEntity;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/***
 *
 * @date 2021-09-25
 * <AUTHOR>
 ***/
@Mapper(componentModel = "spring")
public interface IDeviceUsingLogTransfer {


    /***
     *
     */
    IDeviceUsingLogTransfer INSTANCE = Mappers.getMapper(IDeviceUsingLogTransfer.class);

    /***
     *
     * @param po
     * @return
     */
    DeviceUsingLogVO entityToVo(DeviceUsingLogEntity po);

    /***
     *
     * @param po
     * @return
     */
    List<DeviceUsingLogVO> entityToVo(List<DeviceUsingLogEntity> po);

    /***
     *
     * @param po
     * @return
     */
    DeviceUsingLogEntity voToEntity(DeviceUsingLogVO po);

    /***
     *
     * @param po
     * @return
     */
    List<DeviceUsingLogEntity> voToEntity(List<DeviceUsingLogVO> po);
}
