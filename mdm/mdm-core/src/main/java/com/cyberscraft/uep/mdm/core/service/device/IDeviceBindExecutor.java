package com.cyberscraft.uep.mdm.core.service.device;

import com.cyberscraft.uep.common.enums.Platform;
import com.cyberscraft.uep.mdm.api.dto.device.DeviceActivatedDto;
import com.cyberscraft.uep.mdm.core.domain.user.UserInfo;
import com.cyberscraft.uep.mdm.core.domain.user.UserOrGroupConfig;
import com.cyberscraft.uep.mdm.core.entity.DeviceEntity;
import com.cyberscraft.uep.mdm.core.exception.MdmException;

/***
 * 设备激活的执行器，主要对应于原来的IosUserGetDBO,及androidUserGetDBO
 * @date 2021-10-10
 * <AUTHOR>
 ***/
public interface IDeviceBindExecutor {

    /****
     * 支持激活设备的平台
     * @param platform
     * @return
     * @throws MdmException
     */
    boolean isSupport(Platform platform) throws MdmException;

    /***
     * 绑定对应的设备到用户
     * @param user
     * @param deviceDto
     * @return
     * @throws MdmException
     */
    DeviceEntity bindDeviceToUser(UserInfo user, UserOrGroupConfig userConfig, <PERSON><PERSON>ActivatedDto deviceDto, Platform platform) throws MdmException;

    /****
     * 进行设备信息的写入操作处理
     * @param deviceEntity
     * @return
     * @throws MdmException
     */
    DeviceEntity bindDeviceToUser(DeviceEntity deviceEntity) throws MdmException;
}
