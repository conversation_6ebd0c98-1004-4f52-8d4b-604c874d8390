package com.cyberscraft.uep.mdm.core.service.tenant;

import com.cyberscraft.uep.account.client.constant.ThirdPartyEventTagConstant;
import com.cyberscraft.uep.account.client.domain.EventBody;
import com.cyberscraft.uep.account.client.domain.ThirdPartyEvent;
import com.cyberscraft.uep.account.client.event.IThirdPartyEventExecutor;
import com.cyberscraft.uep.mdm.api.dto.tenant.TenantCreateRequestDto;
import com.cyberscraft.uep.mdm.core.service.basic.ITenantService;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * <p>
 * 租户创建处理
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2019-11-21 17:13
 */
@Component
public class TenantEventHander implements IThirdPartyEventExecutor {

    /***
     *
     */
    private final static Logger LOG = LoggerFactory.getLogger(TenantEventHander.class);

    @Autowired
    private ITenantService tenantService;

    @Override
    public Set<String> getSupportedEventTypes() {
        return new HashSet<>(Arrays.asList(ThirdPartyEventTagConstant.TENANT_CREATE));
    }

    @Override
    public <E extends EventBody> void onEvent(ThirdPartyEvent<E> event) {
        LOG.info("接收到租户创建事件，其中租户ID=>{}", event.getTenantId());
        if (StringUtils.isBlank(event.getTenantId())) {
            return;
        }
        TenantHolder.setTenantCode(event.getTenantId());
        TenantCreateRequestDto tenantCreateRequestDto = new TenantCreateRequestDto();
        tenantCreateRequestDto.setTenantId(event.getTenantId());
        tenantService.create(tenantCreateRequestDto);
    }

}
