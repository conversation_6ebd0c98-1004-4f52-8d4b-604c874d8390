package com.cyberscraft.uep.mdm.core.service.cmd;

import java.util.List;

import com.cyberscraft.uep.mdm.core.service.cmd.impl.CompletedCmd;

public interface ICommandBaseService {
    /**
     * 根据指令类型设置推送的token
     * @return
     */
    boolean setCmdToken(CompletedCmd cmd);

    /**
     * 是否是唤醒指令
     * @return
     */
    boolean isWakeUpCmd(CompletedCmd cmd);
//
//    /**
//     * 是否是唤醒IOS客户端指令
//     * @return
//     */
//    boolean isWakeupIosAppCmd(CompletedCmd completedCmd);
//
//    /**
//     * 是否是唤醒IOS MDM指令
//     * @return
//     */
//    boolean isWakeupIosMDMCmd(CompletedCmd completedCmd);
//    /**
//     * 是否是windows mdm　指令
//     * @param cmd
//     * @return
//     */
//	boolean isWindowsMDMCmd(CompletedCmd cmd);
    /**
     * 是否是tdos　指令
     * @param cmd
     * @return
     */
//	boolean isTDOSCmd(CompletedCmd cmd);
//
//    ApnType getApnType(CompletedCmd cmd);

    String getDeviceApnToken(String udid, CmdType cmdType);

    String getWakeUpCmd(CompletedCmd baseCmd);

    /**
     * 调用方保证cmdList为相同类型指令
     */
    void sendToPN(List<CompletedCmd> cmdList);
    
    /**
     * 通过PN发送通知
     * @param cmdList
     */
    void sendToPN(CompletedCmd cmdList);

}
