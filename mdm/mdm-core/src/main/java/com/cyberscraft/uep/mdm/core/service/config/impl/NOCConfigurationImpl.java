package com.cyberscraft.uep.mdm.core.service.config.impl;

import com.cyberscraft.uep.config.client.IRawConsulConfigClient;
import com.cyberscraft.uep.config.client.NOCConfiguration;
import com.cyberscraft.uep.common.domain.NOC;
import com.cyberscraft.uep.mdm.core.service.config.INOCConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/***
 * 从原来的com.cyberscraft.emm.config.client.model.PN移值而来
 *  * @see com.cyberscraft.emm.config.client.model.NOC
 * @date 2021-06-06
 * <AUTHOR>
 ***/
@Component
@RefreshScope
public class NOCConfigurationImpl implements INOCConfiguration {

    private  final static Logger LOG = LoggerFactory.getLogger(NOCConfigurationImpl.class);

    @Resource
    private IRawConsulConfigClient rawConsulConfigClient;

    @Resource
    private NOCConfiguration nocConfiguration;

    private volatile NOC currentNOC = null;

    private final static Object LOCK= new Object();

    /***
     * 得到NOC对像,原来代码是ThreadLocal相关的，应该是跟ThreadLocal无关，可以做成一个静态对像
     * @return
     */
    @Override
    public NOC getNOC() {
        if(currentNOC==null) {
            synchronized (LOCK) {
                if(currentNOC==null) {
                    NOC noc = new NOC();
                    BeanUtils.copyProperties(this, noc);
                    currentNOC = noc;
                }
            }
        }
        return currentNOC;
    }

    /***
     * 保存NOC配置到配置中心
     * @param noc
     */
    @Override
    public void save(NOC noc) {
        synchronized (LOCK) {
            //再给当前对像设置新的值
            BeanUtils.copyProperties(noc,this);
            //调用rest服务，进行配置的保存
            rawConsulConfigClient.saveServiceProperties(nocConfiguration.getPropertiesNameValuePairs());
            //如果再次升级时，去掉PropertiesUtil工具获取属性的方式后，则可以去掉下面的代码
            currentNOC = null;
        }
    }

    @Override
    public Map<String, String> getPropertiesMap() {
        return nocConfiguration.getPropertiesMap();
    }

    /***
     * 用于刷新对应的配置到PropertyUtil中，并且清空当前的NOC对像，以便于获取NOC时，返回对应的最新对像
     * @throws Exception
     */
    public void afterPropertiesSet() throws Exception {
        try {
            Map<String, String> propertiesMap = getPropertiesMap();
            //environment.getProperty().get;
            if(LOG.isDebugEnabled()) {
                for (String key:propertiesMap.keySet()  ) {
                    LOG.info("{}对应的值为{}", key, propertiesMap.get(key));
                }
            }
            afterPropertiesSetCallback();
        }
        catch (Exception e){
            LOG.error(e.getMessage(),e);
        }
    }

    /***
     * 模板方式，用于扩展afterPropertiesSet，方便某些特殊的类做特殊的处理，如PNConfiguration,NOCConfiguration等
     */
    private void afterPropertiesSetCallback() {
        //threadLocalNOC.set(null);
        synchronized (LOCK) {
            currentNOC=null;
        }
    }
}
