package com.cyberscraft.uep.mdm.core.util;

import com.cyberscraft.uep.common.enums.ExceptionCodeEnum;
import com.cyberscraft.uep.mdm.core.exception.MdmException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.time.Duration;

/**
 * <p>
 *     redis分布式锁简易实现，
 *     目前仅实现以下功能：
 *     1、加锁，同时设置超时时间
 *     2、解锁，且只有加锁的客户端才能解锁
 *     尚未支持的功能：
 *     1、超时时间的续租，前期建议设置一个稍长于业务执行时间的超时时间
 *     2、可重入的支持
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2020-01-03 12:01
 */
@Component
public class RedisLock {

    private static final Logger logger = LoggerFactory.getLogger(RedisLock.class);

    @Autowired
    private RedisTemplate redisTemplate;
    //统一锁前缀
    private static final String lockPrefix = "LOCK_";
    //默认过期时间
    private static final Long DEFAULT_EXPIRE_TIME = 1000 * 5L;
    //默认超时时间
    private static final Long DEFAULT_TIMEOUT = 1000 * 5L;

    /**
     * 获取锁
     * @param key
     * @param requestId  客户端请求标识
     * @param expireTime 在redis中的过期时间，单位：毫秒
     * @param timeOut    获取锁的超时时间,单秒：毫秒
     * @return
     */
    public boolean getLock(String key,String requestId, Long expireTime, Long timeOut) {
        if(expireTime == null || "".equals(expireTime)){
            expireTime = DEFAULT_EXPIRE_TIME;
        }
        if(timeOut == null || "".equals(timeOut)){
            timeOut = DEFAULT_TIMEOUT;
        }

        checkParam(key,requestId);

        //在timeOut内如果获取不到锁会一直尝试重试(借鉴自旋的思想)，获取锁的时间超过timeOut，就返回null，
        Long startTime = System.currentTimeMillis();
        Long endTime = startTime;
        while (endTime - startTime < timeOut) {
            Boolean lock = redisTemplate.opsForValue().setIfAbsent(lockPrefix + key, requestId, Duration.ofMillis(expireTime));
            if (lock) {
                return true;
            }
            endTime = System.currentTimeMillis();
        }
        return false;
    }

    /**
     * 解锁
     * @param key
     * @param requestId
     * @return
     */
    public Boolean unLock(String key,String requestId) {
        checkParam(key,requestId);

        key = lockPrefix + key;
        Object redisValue = redisTemplate.opsForValue().get(key);
        if(redisValue == null){
            return true;
        }
        //判断是否为当前线程的锁
        if (requestId.equals(redisValue.toString())) {
            return redisTemplate.delete(key);
        }
        return false;
    }

    private void checkParam(String key,String requestId){
        if(StringUtil.isEmpty(key)){
            logger.warn("参数【key】不能为空");
            throw new MdmException(ExceptionCodeEnum.PARAM_INVALID);
        }

        if(StringUtil.isEmpty(requestId)){
            logger.warn("参数【requestId】不能为空");
            throw new MdmException(ExceptionCodeEnum.PARAM_INVALID);
        }
    }
}