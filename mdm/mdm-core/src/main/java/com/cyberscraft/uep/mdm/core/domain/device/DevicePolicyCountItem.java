package com.cyberscraft.uep.mdm.core.domain.device;

import java.io.Serializable;

/***
 * 设备策略数量统计项对像
 * @date 2021-09-24
 * <AUTHOR>
 ***/
public class DevicePolicyCountItem implements Serializable {

    /****
     *
     */
    private static final long serialVersionUID = -2257375820896306869L;

    /***
     * 设备Id
     */
    private Long deviceId;
    /***
     * 当前状态对应的数量
     */
    private Integer count;
    /**
     * Prolfie的状态
     */
    private Integer status;
    /***
     * 策略类型
     */
    private Integer type;

    public Long getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(Long deviceId) {
        this.deviceId = deviceId;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}
