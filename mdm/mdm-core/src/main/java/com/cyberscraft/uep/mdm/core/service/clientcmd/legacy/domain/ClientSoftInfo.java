package com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.domain;

import java.io.Serializable;

/***
 *
 * @date 2021-11-23
 * <AUTHOR>
 ***/
public class ClientSoftInfo implements Serializable {

    private Mobile mobile;
    private Client client;
    private SysPlat sysPlat;

    public Mobile getMobile() {
        return mobile;
    }

    public void setMobile(Mobile mobile) {
        this.mobile = mobile;
    }

    public Client getClient() {
        return client;
    }

    public void setClient(Client client) {
        this.client = client;
    }

    public SysPlat getSysPlat() {
        return sysPlat;
    }

    public void setSysPlat(SysPlat sysPlat) {
        this.sysPlat = sysPlat;
    }
}
