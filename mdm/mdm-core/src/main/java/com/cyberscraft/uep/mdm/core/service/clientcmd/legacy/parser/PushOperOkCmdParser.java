package com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.parser;

import com.cyberscraft.uep.common.bean.Result;
import com.cyberscraft.uep.common.enums.ExceptionCodeEnum;
import com.cyberscraft.uep.common.enums.Platform;
import com.cyberscraft.uep.mdm.api.constant.ClientCmdType;
import com.cyberscraft.uep.mdm.api.dto.clientcmd.ClientCmdRequestDto;
import com.cyberscraft.uep.mdm.api.dto.clientcmd.PushOperOkCmdBody;
import com.cyberscraft.uep.mdm.core.domain.clientcmd.LegacyClientCmdRequest;
import com.cyberscraft.uep.mdm.core.domain.oauth2.AccessToken;
import com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.ILegacyCmdParser;
import com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.constant.*;
import com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.domain.OperID;
import com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.domain.Push;
import com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.domain.Reply;
import com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.sax.RequestBeanSaxHandler;
import org.springframework.stereotype.Component;
import org.thymeleaf.context.Context;

import java.util.HashSet;
import java.util.Set;

/***
 * 推送基础指令结果
 * @date 2021-11-25
 * <AUTHOR>
 ***/
@Component
public class PushOperOkCmdParser extends AbstractCmdParser implements ILegacyCmdParser {

    /***
     *
     */
    private final static Set<String> SUPPORTED_CMD_CODES = new HashSet<>();


    static {
        SUPPORTED_CMD_CODES.add(String.valueOf(LegacyCommandCode.PUSH_OPEROK_CMD.getCmdCode()));
        SUPPORTED_CMD_CODES.add(String.valueOf(LegacyCommandCode.IOS_CMD_CALLBACK.getCmdCode()));
    }

    @Override
    public Set<String> getCommandCodes() {
        return SUPPORTED_CMD_CODES;
    }

    @Override
    public boolean isSupported(String cmdCode) {
        return SUPPORTED_CMD_CODES.contains(cmdCode);
    }

    @Override
    public ClientCmdRequestDto parseClientCmdRequestDto(LegacyClientCmdRequest request, Platform platform) {
        PushOperOkCmdBody cmdBody = new PushOperOkCmdBody();
        //Element root = getRootElement(request.getCommandBody());
        //Push push = parserObject(root.getChild("Push"), Push.class);
        //OperID operID = getOperIDFromDom(root);
        RequestBeanSaxHandler handler = getRequestBeanHandler(request.getCommandBody());
        OperID operID = handler.getOperID();
        Push push = handler.getPush();
        //MobileIDInfo mobileIDInfo = handler.getMobileIDInfo();

        //验证访问Token
        AccessToken token = validateAccessToken(operID.getClientUID());


        cmdBody.setUdid(token.getUdid());
        cmdBody.setUserId(token.getUserId());
        cmdBody.setUemVersion(operID.getUemVersion());
        cmdBody.setClientVersion(operID.getClientVersion());
        cmdBody.setClientBuildNum(operID.getClientBuildNum());

        if (push != null) {
            cmdBody.setCmdCode(String.valueOf(push.getCmd()));
            cmdBody.setFlowNum(push.getFlowNum());
            cmdBody.setResult(push.getResult());
            cmdBody.setWeakUpCmd(isWeakUpCmd(push.getCmd()));
        }
        cmdBody.setPlatform(platform.getValue());

        ClientCmdRequestDto cmdRequestDto = new ClientCmdRequestDto();
        cmdRequestDto.setCmdBody(cmdBody);
        cmdRequestDto.setType(ClientCmdType.PUSH_OPEROK.getCode());
        cmdRequestDto.setPlatform(platform);
        cmdRequestDto.setUdid(token.getUdid());
        return cmdRequestDto;
    }

    @Override
    public String parseOutput(Result result, Platform platform, ClientCmdRequestDto requestDto) {
        Reply reply = new Reply();
        OperID operID = new OperID();
        if (ExceptionCodeEnum.SUCCESS.getCode().equalsIgnoreCase(result.getCode())) {
            operID.setResult(String.valueOf(CodeType.SUCCESS.getValue()));
        } else {
            operID.setResult(String.valueOf(CodeType.FAIL.getValue()));
            operID.setExtra(result.getCode() + result.getMessage());
        }
        reply.setOperID(operID);
        Context context = new Context();

        context.setVariable(TemplatePropertisConstant.REPLY, reply);
        String templateName = getTemplateName(platform);
        return legacyContentRender.render(context, templateName);
    }

    /***
     *
     * @param platform
     * @return
     */
    private String getTemplateName(Platform platform) {
        String templateName = TemplateURIConstant.NO_INFO_XML;
        return templateName;
    }

    /****
     * 是否是唤醒指令
     * @param aCmdType
     * @return
     */
    private boolean isWeakUpCmd(int aCmdType) {

        if (LegacyCmdType.IOS_LOCK.getValue() == aCmdType
                || LegacyCmdType.IOS_WALLPAPER.getValue() == aCmdType
                || LegacyCmdType.IOS_DEL_PWD.getValue() == aCmdType
                || LegacyCmdType.IOS_DEVICE_CLEAR.getValue() == aCmdType
                || LegacyCmdType.IOS_POLICY.getValue() == aCmdType
                || LegacyCmdType.IOS_DEL_POLICY.getValue() == aCmdType
                || LegacyCmdType.IOS_APP_INSTALL.getValue() == aCmdType
                || LegacyCmdType.IOS_APP_LIST.getValue() == aCmdType
                || LegacyCmdType.IOS_MANAGED_APP_LIST.getValue() == aCmdType
                || LegacyCmdType.IOS_APP_UNINSTALL.getValue() == aCmdType
                || LegacyCmdType.IOS_DEVICE_SECURITYINFO.getValue() == aCmdType
                || LegacyCmdType.IOS_DEVICE_INFORMATION.getValue() == aCmdType
                || LegacyCmdType.IOS_PUSH_APPCONF.getValue() == aCmdType
                || LegacyCmdType.IOS_POLICY_LIST.getValue() == aCmdType
                || LegacyCmdType.RESEND_WAKEUP.getValue() == aCmdType
                || LegacyCmdType.IOS_INSTALL_FENCEPROFILE.getValue() == aCmdType
                || LegacyCmdType.IOS_PUSH_VIOLATION.getValue() == aCmdType
                || LegacyCmdType.IOS_VIOLATION_FORBID.getValue() == aCmdType
                || LegacyCmdType.IOS_BLACK_WHITE_POLICY.getValue() == aCmdType


                || LegacyCmdType.AN_DEVICE_CLEAR.getValue() == aCmdType
                || LegacyCmdType.AN_EDATA_CLEAR.getValue() == aCmdType
                || LegacyCmdType.AN_push_license.getValue() == aCmdType
                || LegacyCmdType.AN_POLICY.getValue() == aCmdType
                || LegacyCmdType.AN_DEL_POLICY.getValue() == aCmdType
                || LegacyCmdType.AN_APP_INSTALL.getValue() == aCmdType
                || LegacyCmdType.AN_APP_URL_INSTALL.getValue() == aCmdType
                || LegacyCmdType.AN_PUSH_BLACK_WHITE_LIST.getValue() == aCmdType
                || LegacyCmdType.AN_PUSH_URL_BLACK_WHITE_LIST.getValue() == aCmdType
                || LegacyCmdType.AN_PUSH_VIOLATION.getValue() == aCmdType
                || LegacyCmdType.AN_PUSH_APPCONF.getValue() == aCmdType
                || LegacyCmdType.AN_APP_UNINSTALL.getValue() == aCmdType
                || LegacyCmdType.AN_APP_URL_UNINSTALL.getValue() == aCmdType
                || LegacyCmdType.RESEND_WAKEUP.getValue() == aCmdType
                || LegacyCmdType.AN_LOCK.getValue() == aCmdType
                || LegacyCmdType.AN_KNOX_DISABLE.getValue() == aCmdType

        ) {
            return true;
        }
        return false;
    }
}
