package com.cyberscraft.uep.mdm.core.service.admin.impl;

import com.cyberscraft.uep.mdm.api.errors.MdmErrorType;
import com.cyberscraft.uep.mdm.core.dbo.SysUserGroupDBO;
import com.cyberscraft.uep.mdm.core.domain.sysuser.AdminGroupId;
import com.cyberscraft.uep.mdm.core.exception.MdmException;
import com.cyberscraft.uep.mdm.core.service.admin.IAdminGroupIdCacheService;
import com.cyberscraft.uep.mdm.core.service.admin.ISysUserGroupService;
import com.cyberscraft.uep.mdm.core.service.user.IUserServiceNew;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/***
 *
 * @date 2021-09-16
 * <AUTHOR>
 ***/
@Service
public class SysUserGroupServiceImpl implements ISysUserGroupService {

    @Resource
    private SysUserGroupDBO sysUserGroupDBO;

    @Resource
    private IUserServiceNew userService;

    @Resource
    private IAdminGroupIdCacheService adminGroupIdCacheService;

    @Override
    public List<Long> getGroupIdsByLoginId(String loginId) {
        if(StringUtils.isBlank(loginId)){
            throw new MdmException(MdmErrorType.USER_LOGINID_INVALID);
        }
        synchronized (loginId){
            AdminGroupId obj= adminGroupIdCacheService.getAdminGroupId(loginId);
            if(obj!=null){
                return obj.getGroupIds();
            }
            List<Long> ids= sysUserGroupDBO.getGroupIdsByLoginId(loginId);
            if(ids==null || ids.size()==0){
                return null;
            }
            List<Long> allIds= userService.getGroupAndSubGroupIdsById(ids);
            obj= new AdminGroupId();
            obj.setGroupIds(allIds);
            obj.setLoginId(loginId);
            adminGroupIdCacheService.saveAdminGroupId(obj);
            return obj.getGroupIds();
        }

    }
}
