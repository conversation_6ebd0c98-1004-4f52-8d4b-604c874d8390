package com.cyberscraft.uep.mdm.core.service.oauth2.impl;

import com.cyberscraft.uep.common.enums.ExceptionCodeEnum;
import com.cyberscraft.uep.mdm.api.dto.device.DeviceActivatedDto;
import com.cyberscraft.uep.mdm.api.dto.oauth2.AccessTokenRequestDto;
import com.cyberscraft.uep.mdm.api.errors.MdmErrorType;
import com.cyberscraft.uep.mdm.core.constant.GrantType;
import com.cyberscraft.uep.mdm.core.domain.oauth2.RefreshToken;
import com.cyberscraft.uep.mdm.core.domain.user.UserInfo;
import com.cyberscraft.uep.mdm.core.exception.MdmException;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/***
 *
 * @date 2021-09-29
 * <AUTHOR>
 ***/
@Service
public class RefreshTokenAuthorization extends AbstractAuthorization {

    @Override
    public boolean isSupport(String grantType) {
        return GrantType.REFRESH_TOKEN.getCode().equals(grantType);
    }

    @Override
    protected void validation(AccessTokenRequestDto dto) throws MdmException {
        if (StringUtils.isBlank(dto.getRefreshToken())) {
            throw new MdmException(MdmErrorType.REQUEST_INVALID);
        }
        dto.setBindDevice(false);
    }

    @Override
    protected UserInfo getUser(AccessTokenRequestDto dto) throws MdmException {

        String refreshToken = dto.getRefreshToken();
        RefreshToken token = refreshTokenService.getToken(refreshToken);
        if (token == null) {
            throw new MdmException(ExceptionCodeEnum.REFRESHTOKEN_INVALID);
        }
        //String loginId = token.getUserId();
//        if (!token.getUdid().equals(dto.getUdid())) {
//            throw new MdmException(MdmErrorType.DEVICE_UDID_INVALID);
//        }
        TenantHolder.setTenantCode(token.getTenantId());
        UserInfo userEntity = userService.getUser(token.getUserId());
        if (userEntity == null) {
            throw new MdmException(MdmErrorType.USER_NAME_INVALID);
        }
        DeviceActivatedDto device = dto.getDevice();
        if (device == null) {
            device = new DeviceActivatedDto();
        }
        dto.setTenantId(token.getTenantId());
        dto.setUdid(token.getUdid());
        device.setUdid(token.getUdid());
        dto.setDevice(device);
        return userEntity;
    }

    /***
     * 清除相关Token，保证Refresh Token只有一次有效
     * @param dto
     */
    @Override
    protected void authorizedSuccess(AccessTokenRequestDto dto) {
        try {
            RefreshToken token = refreshTokenService.getToken(dto.getRefreshToken());
            if (token != null) {
                accessTokenService.remove(token.getAccessToken());
                refreshTokenService.remove(token.getRefreshToken());
            }
        } catch (Exception e) {
            LOG.error(e.getMessage(), e);
        }
    }
}
