package com.cyberscraft.uep.mdm.core.service.device.impl;

import com.cyberscraft.uep.common.constant.SysConstant;
import com.cyberscraft.uep.common.enums.Platform;
import com.cyberscraft.uep.common.util.RandomUtil;
import com.cyberscraft.uep.common.util.SnowflakeIDUtil;
import com.cyberscraft.uep.mdm.api.dto.device.DeviceActivatedDto;
import com.cyberscraft.uep.mdm.api.errors.MdmErrorType;
import com.cyberscraft.uep.mdm.core.constant.DeviceStatusConstant;
import com.cyberscraft.uep.mdm.core.constant.ThreadPoolNameConstant;
import com.cyberscraft.uep.mdm.core.domain.user.UserInfo;
import com.cyberscraft.uep.mdm.core.domain.user.UserOrGroupConfig;
import com.cyberscraft.uep.mdm.core.entity.DeviceEntity;
import com.cyberscraft.uep.mdm.core.exception.MdmException;
import com.cyberscraft.uep.mdm.core.service.clientcmd.executor.AbstractClientCmdExecutor;
import com.cyberscraft.uep.mdm.core.service.device.IDeviceBindExecutor;
import com.cyberscraft.uep.mdm.core.service.device.IUdidGenerateService;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

/***
 *
 * @date 2021-10-10
 * <AUTHOR>
 ***/
@Component
public abstract class AbstractDeviceBindExecutor extends AbstractClientCmdExecutor implements IDeviceBindExecutor {

    /***
     *
     */
    protected final static Logger LOG = LoggerFactory.getLogger(AbstractDeviceBindExecutor.class);

    @Resource
    private IUdidGenerateService udidGenerateService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    private final static String DEVICE_LOGIN_FLAG_KEY_PREFIX = "udid_login_";
//
//    @Resource
//    protected ISmcsReportService smcsReportService;

    @Resource(name = ThreadPoolNameConstant.AUTH_POOL_NAME)
    private ExecutorService authExcutor;

    /***
     * 设备设备的登录标志
     * @param udid
     * @return
     */
    protected boolean setLoginDeviceFlag(String udid) {
        if (StringUtils.isBlank(udid)) {
            return false;
        }
        synchronized (udid) {
            return stringRedisTemplate.opsForValue().setIfAbsent(DEVICE_LOGIN_FLAG_KEY_PREFIX + udid, "1", 30, TimeUnit.SECONDS);
        }
    }

    /***
     *  清除设备的登录标志
     * @param udid
     */
    protected void cleanDeviceLoginFlag(String udid) {
        stringRedisTemplate.delete(DEVICE_LOGIN_FLAG_KEY_PREFIX + udid);
    }

    @Override
    public DeviceEntity bindDeviceToUser(UserInfo user, UserOrGroupConfig userConfig, DeviceActivatedDto deviceDto, Platform platform) throws MdmException {

        if (StringUtils.isNotEmpty(deviceDto.getDeviceUuid())) {
            deviceDto.setUdid(null);
        }
        if (StringUtils.isNotEmpty(deviceDto.getSsAid())) {
            deviceDto.setUdid(null);
        }
        // 生成udid
        if (StringUtils.isEmpty(deviceDto.getUdid())) {
            deviceDto.setUdid(getDeviceUdid(deviceDto, platform));
        }
        if (StringUtils.isEmpty(deviceDto.getUdid())) {
            LOG.warn("[ACTIVATE][DEVICE] UDID IS EMPTY! USER:" + user.getUsername());
            throw new MdmException(MdmErrorType.DEVICE_UDID_INVALID);
        }

        //TODO 2020-05-14优化，这段逻辑变成异步的，写入redis，
        // redis中增加一个数据库入库标志，后续每次请求时，从redis中读取数据，
        // 如果数据入库标志为未入库，则再次触发写入，这里有一个并发问题（入库的并发因为是分开写的）
        if (sysConfiguration.getEnableAsyncDeviceActivate()) {
            return asyncBindDeviceToUser(user, userConfig, deviceDto, platform);
        } else {
            return syncBindDeviceToUser(user, userConfig, deviceDto, platform);
        }
    }

    /***
     * 原来的同步激活逻辑
     * @param user
     * @param userConfig
     * @param deviceDto
     * @param platform
     * @return
     * @throws MdmException
     */
    @Transactional
    DeviceEntity syncBindDeviceToUser(UserInfo user, UserOrGroupConfig userConfig, DeviceActivatedDto deviceDto, Platform platform) throws MdmException {
        if (!setLoginDeviceFlag(deviceDto.getUdid())) {
            throw new MdmException(MdmErrorType.DEVICE_LOGIN_DUPLICAT);
        }

        try {
            //如果当前用户对应的已经激活状态的设备存在，则直接返回
            //5、验证对应的已经激活的设备是否存在
            //LOG.info("[BIND DEVICE]验证对应的已经激活的设备是否存在,设备UDID:{}", deviceDto.getUdid());
            // 2016-02-18, cuilong added, 为了支持“激活设备与注册信息不一致时允许激活”选项而增加，与iOS保持一致。
            DeviceEntity device = getDeviceByUser(user, deviceDto, platform);
            boolean existFlag = false;
            if (device != null && device.getStatus() == DeviceStatusConstant.LOGIN_ONLY) {
//                if (device != null && (device.getStatus() == DeviceStatusConstant.ACTIVATED ||
//                        device.getStatus() == DeviceStatusConstant.LOGIN_ONLY)) {
                //已经有激活的设备信息
                //LOG.info("Use_existed_device_item,Udid:" + device.getUdid());
                //return device;
                existFlag = true;
            }
            checkPreBindState(user, deviceDto, platform);
            DeviceEntity ret = bindToUser(user, userConfig, device, deviceDto, platform);
            LOG.info("auth sync finished bind device  udid is :{},loginId:{}", ret.getUdid(), ret.getLoginId());
            return ret;
        } catch (Exception e) {
            throw e;
        } finally {
            cleanDeviceLoginFlag(deviceDto.getUdid());
        }
    }

    /***
     * 主要逻辑为直接根据当前用户信息，设备信息生成一个设备信息对像，存入redis，并且返回
     * 用register状态标识为待写入
     * @param user
     * @param userConfig
     * @param deviceDto
     * @param platform
     * @return
     * @throws MdmException
     */
    DeviceEntity asyncBindDeviceToUser(UserInfo user, UserOrGroupConfig userConfig, DeviceActivatedDto deviceDto, Platform platform) throws MdmException {
        //验证绑定标志，主要是越狱标志
        checkPreBindState(user, deviceDto, platform);
        DeviceEntity device = getNewDevice(user, deviceDto, platform);

        setDeviceInfoFromDeviceDto(device, deviceDto, user, DeviceStatusConstant.REGEISTED, platform);
//        device.setOfflineStatus(SysConstant.FALSE_VALUE);
//
//        device.setHostId(user.getEmmClientId());

//        // 保存客户端的EMM版本号
        device.setClientVersion(deviceDto.getClientVersion());
        device.setClientBuildNum(deviceDto.getClientBuildNum());
        if (StringUtils.isNotBlank(deviceDto.getUemVersion())) {
            device.setUemVersion(deviceDto.getUemVersion());
        }
        if (StringUtils.isNotBlank(deviceDto.getClientFinger())) {
            device.setClientFinger(deviceDto.getClientFinger());
        }
        //TODO 熊祥众2020-05-27 因为以下相关属性暂时未使用，所以进行屏蔽
//        // 是否启用设备权限引导，初始置为1，启用。
//        //if (isNewDeviceBecauseNotRegistered) {
//        device.setRelationship(userConfig.getRelationship());
////            // 如果是新增的设备，是否启用设备权限配置向导，取决于用户级别的设置
//        device.setEnablePerm(userConfig.getEnableDevicePerm());
//        device.setEnablePermissionGuide(userConfig.getEnablePermissionGuide());
//        //}

        String shortUdid = RandomUtil.getRandomString(6);
        device.setShortUdid(shortUdid);
        //设置challenge值
//        device.setChallenge(null);

        if (platform == Platform.WINDOWS8) {
            device.setDeviceType(30);
        }
        if (Platform.IOS.equals(platform) || Platform.MACOS.equals(platform)) {
            device.setManufacturer("APPLE");
        }

        String tenantId = TenantHolder.getTenantCode();
        device.setTenantId(tenantId);
        deviceCacheService.saveDevice(device);

//        TODO 测试性能压力做数据转发处理
        // 这一段逻辑可以改成发送MQ，通过MQ进行异步线程处理
        authExcutor.submit(() -> {
            try {
                LOG.info("async bind thread is runing,tenantId :{}", tenantId);
                TenantHolder.setTenantCode(tenantId);
                bindDeviceToUser(device);
            } catch (Exception e) {
                LOG.error("async bind device to user has error,user:{} ,udid:{}, error:{}", e.getMessage(), device.getUserId(), device.getUdid());
            }
        });
        device.setStatus(DeviceStatusConstant.LOGIN_ONLY);

        //清空Policy的数据
        commandProcessService.cleanPolicy(device);
        LOG.info(" auth async finished save device to redis udid is :{},loginId is:{}", device.getUdid(), device.getLoginId());
        return device;
    }

    //public DeviceEntity bindDeviceToUser(DeviceEntity device) throws MdmException;

    /***
     * 绑定到用户
     * @param user
     * @param device
     * @param deviceDto
     * @param platform
     * @return
     * @throws MdmException
     */
    protected abstract DeviceEntity bindToUser(UserInfo user, UserOrGroupConfig userConfig, DeviceEntity device, DeviceActivatedDto deviceDto, Platform platform) throws MdmException;

    /***
     *
     * @param user
     * @param deviceDto
     * @param platform
     */
    protected void checkPreBindState(UserInfo user, DeviceActivatedDto deviceDto, Platform platform) {
        if (deviceDto == null) {
            LOG.warn("激活时，用户{}的设备信息为空", user.getUsername());
            throw new MdmException(MdmErrorType.DEVICE_INVALID);
        }
        //如果是不允许越狱，但是设备是越狱的，则报错
        if (0 == tenantConfigService.getAllowRootOrJailbreak() && deviceDto.getJailbreak() != null && 1 == deviceDto.getJailbreak()) {
            LOG.warn("激活时，用户{}的设备{}已经越狱，系统为不允许越狱", user.getUsername(), deviceDto.getUdid());
            throw new MdmException(MdmErrorType.DEVICE_BINDED_JAILBREAK);
        }
        //判断是否超过当前用户允许的最大激活设备数量
        //TODO 因为系统中暂时未进行设置，所以暂时不考虑
//        if (!existFlag) {
//            int activedDeviceNum = deviceService.getActivedDeviceNumByUser(user.getId());
//            int maxNum = tenantConfigService.getAllowMaxActivateNum();
//            if (activedDeviceNum >= maxNum) {
//                LOG.warn("激活时，用户{}，已经激活设备数为{}，已经超过允许的最大设备数{}", user.getLoginId(), activedDeviceNum, maxNum);
//                throw new MdmException(MdmErrorType.DEVICE_BINDED_ACTIVATENUMMAX);
//            }
//        }
    }

    /***
     * 根据设备信息，生成UDID
     * @param deviceDto
     * @param platform
     * @return
     */
    protected String getDeviceUdid(DeviceActivatedDto deviceDto, Platform platform) {

        return udidGenerateService.createUdid(deviceDto, platform);
    }


    /***
     * 查找用户对应的设备信息
     * @param user
     * @param deviceDto
     * @param platform
     * @return
     */
    protected DeviceEntity getDeviceByUser(UserInfo user, DeviceActivatedDto deviceDto, Platform platform) {

        //TODO 熊祥众于2019-11-29进行调整，因为不需要查询其它设备，用户+udid为唯一，只要对应的Udid+userId存在即可，但是不需要考虑状态
        DeviceEntity device = null;
        try {
//            String udid = deviceDto.getUdid();
//            String idfa = deviceDto.getIdfa();
            return deviceService.getDeviceByUserAndUdid(user.getId(), deviceDto.getUdid());
//            List<DeviceEntity> list = deviceService.getDevicesByUserAndPlatformDeviceType(user.getId(), platform.getValue(), deviceDto.getDeviceType());
//            LOG.debug("getDeviceByUser: userDeviceList.size = :{}", list.size());
//
//            if (!list.isEmpty()) {
//                for (DeviceEntity td : list) {
//                    // 原来只取激活状态的设备，现在是取所有状态，即只要原来有设备，则用原来的设备 跟崔龙讨论后，去掉对应的状态判断
////                    if (td.getStatus() == DeviceStatusConstant.ACTIVATED
////                            || td.getStatus() == DeviceStatusConstant.LOGIN_ONLY) {
//                    boolean useAlreadyActived = false;
//                    if (Platform.IOS.getValue() == td.getType() && StringUtils.isNotEmpty(idfa) && idfa.equals(td.getIdfa())) {
//                        useAlreadyActived = true;
//                    } else if (StringUtils.isNotEmpty(udid) && udid.equals(td.getUdid())) {
//                        useAlreadyActived = true;
//                    } else if (StringUtils.isNotEmpty(deviceDto.getSsAid())) {
//                        if (StringUtils.isNotEmpty(td.getImei()) && td.getImei().equals(deviceDto.getImei())) {
//                            useAlreadyActived = true;
//                        }
//                    }
//                    if (useAlreadyActived) {
//                        // 该用户已经有激活数据，直接返回
//                        LOG.info("##Android Userget 3001 successfully! Device aready activated! User:{}, device udid:{}", user.getName(), deviceDto.getUdid());
//                        return td;
//                    }
////                    }
//                }
//                // 查找非淘汰、非激活最匹配的数据
//                //device = getBestDeviceItemForUser(user, deviceDto, list);
//            }
        } catch (Exception e) {
            LOG.error("Get_device_BY_user_ERROR,userid:{}", user.getId(), e);
        }
        return device;

    }


    /***
     * 根据请求的DeviceDto对像中的信息，设置设备信息
     * @param deviceDto
     * @param deviceDto
     * @param deviceStatus
     * @param platform
     */
    protected void setDeviceInfoFromDeviceDto(DeviceEntity device, DeviceActivatedDto deviceDto, UserInfo user, int deviceStatus, Platform platform) {

        if (StringUtils.isNotEmpty(deviceDto.getUdid())) {
            //LOG.info("########setDeviceInfoFromDeviceDto deviceDto udid is" + deviceDto.getUdid());
            device.setUdid(deviceDto.getUdid());
            //LOG.info("########setDeviceInfoFromDeviceDto device udid is" + device.getUdid());
        } else {
            String udid = getDeviceUdid(deviceDto, platform);
            device.setUdid(udid);
            deviceDto.setUdid(udid);
            //LOG.info("########setDeviceInfoFromDeviceDto device.udid is empty, deviceDb udid is" + device.getUdid());
        }

        device.setStatus(deviceStatus);
        device.setType(platform.getValue());
        LOG.info("setDeviceInfoFromDeviceDto_udid:{}, type:{}", device.getUdid(), device.getType());

        if (StringUtils.isNotBlank(deviceDto.getIdfa())) {
            device.setIdfa(deviceDto.getIdfa());
        }
        if (deviceDto.getDeviceType() > 4) {
            device.setDeviceType(deviceDto.getDeviceType());
        }
        if (!StringUtils.isBlank(deviceDto.getIp())) {
            device.setIp(deviceDto.getIp());
        }

        if (!StringUtils.isBlank(deviceDto.getImei())) {
            device.setImei(deviceDto.getImei());
        }
        if (!StringUtils.isBlank(deviceDto.getImsi())) {
            device.setImsi(deviceDto.getImsi());
        }
        if (!StringUtils.isBlank(deviceDto.getSerialNum())) {
            device.setSerialNum(deviceDto.getSerialNum());
        }
//        device.setJailbreakFlag(deviceDto.getJailbreak());
        device.setRootFlag(deviceDto.getJailbreak());

        device.setOnlineFlag(SysConstant.TRUE_VALUE);
//        device.setOfflineStatus(SysConstant.FALSE_VALUE);
        device.setRegistTime(LocalDateTime.now());
        device.setLastOnlineTime(LocalDateTime.now());
        device.setActivateTime(LocalDateTime.now());
        device.setUpdateTime(LocalDateTime.now());
//        device.setLostFlag(0);

        if (!StringUtils.isBlank(deviceDto.getOs())) {
            device.setOs(deviceDto.getOs());
        }
        if (!StringUtils.isBlank(deviceDto.getOsVersion())) {
            device.setVersionNum(deviceDto.getOsVersion());
        }
        if (!StringUtils.isBlank(deviceDto.getWifiMac())) {
            device.setWifiMac(deviceDto.getWifiMac().toUpperCase());
        }
        if (!StringUtils.isBlank(deviceDto.getBluetoothMac())) {
            device.setBluetoothMac(deviceDto.getBluetoothMac());
        }
        if (!StringUtils.isBlank(deviceDto.getModel())) {
            device.setModel(deviceDto.getModel());
        }
//        if (deviceDto.getSpecialTypeFlag() != null) {
//            device.setSpecialTypeFlag(deviceDto.getSpecialTypeFlag());
//        }
        if (!StringUtils.isBlank(deviceDto.getClientBuildNum())) {
            device.setClientBuildNum(deviceDto.getClientBuildNum());
        }
        if (!StringUtils.isBlank(deviceDto.getClientVersion())) {
            device.setClientVersion(deviceDto.getClientVersion());
        }
        handleDeviceName(user.getUsername(), device, deviceDto);

        device.setUserId(user.getId());
        //TODO 因为需要对多组的支持，所以该逻辑暂时屏蔽，待统一重新考虑
        device.setGroupId(user.getGroups()!=null && user.getGroups().size()>0?user.getGroups().get(0).getGroupId():null);
        device.setLoginId(user.getUsername());
        device.setUserName(user.getName());
        device.setOpenUserId(user.getConnectorUid());
        //return device;
    }


    /***
     * 根据请求的DeviceDto对像中的信息，设置设备信息
     * @param deviceDto
     * @param deviceDto
     */
    protected void setDeviceInfoFromDeviceDto(DeviceEntity device, DeviceEntity deviceDto) {


        device.setUdid(deviceDto.getUdid());
        //device.setStatus(deviceDto.getStatus());
        //device.setLostFlag(SysConstant.FALSE_VALUE);
        device.setType(deviceDto.getType());
        device.setOnlineFlag(SysConstant.TRUE_VALUE);
        device.setRegistTime(LocalDateTime.now());
        device.setLastOnlineTime(LocalDateTime.now());
        device.setActivateTime(LocalDateTime.now());
        device.setUpdateTime(LocalDateTime.now());
        device.setDeviceType(deviceDto.getDeviceType());
//        device.setInitSimInfo(deviceDto.getInitSimInfo());
        // 记录激活时imsi，以备SIM卡变更合规使用
        device.setShortUdid(deviceDto.getShortUdid());

        LOG.info("setDeviceInfoFromDeviceDto_udid:{}, type:{}", device.getUdid(), device.getType());

        if (StringUtils.isNotBlank(deviceDto.getIdfa())) {
            device.setIdfa(deviceDto.getIdfa());
        }
        if (deviceDto.getDeviceType() > 4) {
            device.setDeviceType(deviceDto.getDeviceType());
        }
        if (!StringUtils.isBlank(deviceDto.getIp())) {
            device.setIp(deviceDto.getIp());
        }

        if (!StringUtils.isBlank(deviceDto.getImei())) {
            device.setImei(deviceDto.getImei());
        }
        if (!StringUtils.isBlank(deviceDto.getImsi())) {
            device.setImsi(deviceDto.getImsi());
        }
        if (!StringUtils.isBlank(deviceDto.getSerialNum())) {
            device.setSerialNum(deviceDto.getSerialNum());
        }

        if (!StringUtils.isBlank(deviceDto.getOs())) {
            device.setOs(deviceDto.getOs());
        }
        if (!StringUtils.isBlank(deviceDto.getVersionNum())) {
            device.setVersionNum(deviceDto.getVersionNum());
        }
        if (!StringUtils.isBlank(deviceDto.getWifiMac())) {
            device.setWifiMac(deviceDto.getWifiMac().toUpperCase());
        }
        if (!StringUtils.isBlank(deviceDto.getBluetoothMac())) {
            device.setBluetoothMac(deviceDto.getBluetoothMac());
        }
        if (!StringUtils.isBlank(deviceDto.getModel())) {
            device.setModel(deviceDto.getModel());
        }
//        if (deviceDto.getSpecialTypeFlag() != null) {
//            device.setSpecialTypeFlag(deviceDto.getSpecialTypeFlag());
//        }
        if (!StringUtils.isBlank(deviceDto.getClientBuildNum())) {
            device.setClientBuildNum(deviceDto.getClientBuildNum());
        }
        if (!StringUtils.isBlank(deviceDto.getClientVersion())) {
            device.setClientVersion(deviceDto.getClientVersion());
        }
        device.setDeviceName(deviceDto.getDeviceName());
        if (StringUtils.isNotBlank(deviceDto.getManufacturer())) {
            device.setManufacturer(deviceDto.getManufacturer());
        }
        device.setUserId(deviceDto.getUserId());
        device.setGroupId(deviceDto.getGroupId());
        device.setLoginId(deviceDto.getLoginId());
        device.setUserName(deviceDto.getUserName());
        device.setOpenUserId(deviceDto.getOpenUserId());
        //return device;
    }

    /****
     * 处理设备名称
     * @param loginId
     * @param deviceDB
     * @param device
     */
    protected void handleDeviceName(String loginId, DeviceEntity deviceDB, DeviceActivatedDto device) {
        if (!StringUtils.isBlank(deviceDB.getDeviceName())) {
            return;
        }

        Platform platform = Platform.findByValue(deviceDB.getType());
        if (platform == Platform.ANDROID) {
            deviceDB.setDeviceName(loginId + "_Android");
        } else if (platform == Platform.IOS) {
            if (null != device.getDeviceName()) {
                deviceDB.setDeviceName(loginId + "_" + device.getDeviceName());
            } else {
                deviceDB.setDeviceName(loginId + "_IOS");
            }
        } else if (platform == Platform.WINDOWS8) {
            deviceDB.setDeviceName(loginId + "_" + device.getDeviceName() + "_Windows8");
        } else if (platform == Platform.WINDOWSPHONE8) {
            deviceDB.setDeviceName(loginId + "_" + device.getDeviceName() + "_WindowsPhone8");
        } else if (platform == Platform.WINDOWS) {
            deviceDB.setDeviceName(loginId + "_" + device.getDeviceName() + "_Windows10");
        } else if (platform == Platform.TDOS) {
            deviceDB.setDeviceName(loginId + "_TDOS");
        }
    }


    /***
     * 生成新的用户设备绑定信息
     * @param userDB
     * @param deviceDto
     * @param platform
     * @return
     */
    protected DeviceEntity getNewDevice(UserInfo userDB, DeviceActivatedDto deviceDto, Platform platform) {

        // 插入一条注册设备信息
        DeviceEntity device = new DeviceEntity();
        device.setId(SnowflakeIDUtil.getId());
        device.setUserId(userDB.getId());
        device.setIdfa(deviceDto.getIdfa());
        device.setType(platform.getValue());
        device.setStatus(DeviceStatusConstant.REGEISTED);//
        device.setRegistTime(LocalDateTime.now());//
        device.setUdid(deviceDto.getUdid());
        device.setDeviceType(deviceDto.getDeviceType());
        /*
         * zhouwensi 2017-01-16
         * jailbreakFlag=0 或 rootFlag=0 也需要更新数据库中的记录，不能出现null
         */
        device.setRootFlag(deviceDto.getJailbreak());
//        device.setJailbreakFlag(deviceDto.getJailbreak());


        device.setLoginId(userDB.getUsername());


        return device;
    }


    /****
     * 保存信息
     * @param device
     * @param deviceDto
     * @param platform
     * @param isNewDeviceBecauseNotRegistered
     */
    void saveDevice(DeviceEntity device, DeviceActivatedDto deviceDto, Platform platform, boolean isNewDeviceBecauseNotRegistered) {
//
//        if (deviceDto.getSpecialTypeFlag() == SpecialTypeFlag.SamsungKnox.getValue()) {
//            // KNOX双域，直接设置knoxstatus为201（未创建）
//            device.setKnoxStatus(KnoxStatus.KnoxUncreated.getValue());
//        } else if (deviceDto.getSpecialTypeFlag() == SpecialTypeFlag.ChinaMobileKnox.getValue()) {
//            // 移动双域，直接设置knoxstatus为101（已创建）
//            device.setKnoxStatus(KnoxStatus.KnoxCreated.getValue());
//        } else if (deviceDto.getSpecialTypeFlag() == SpecialTypeFlag.TdPlugin.getValue()) {
//            device.setKnoxStatus(KnoxStatus.TdPluginApproved.getValue());
//        } else {
//            device.setKnoxStatus(KnoxStatus.Unsupport.getValue());
//        }

        if (platform == Platform.WINDOWS8) {
            device.setDeviceType(30);
        }
        if (Platform.IOS.equals(platform) || Platform.MACOS.equals(platform)) {
            device.setManufacturer("APPLE");
        }

        // 记录激活时imsi，以备SIM卡变更合规使用
//        if (StringUtils.isEmpty(deviceDto.getImsi())) {
//            device.setInitSimInfo(Constant.SIMINFO_EMPTY);
//        } else {
//            device.setInitSimInfo(deviceDto.getImsi());
//        }
        // 为安卓mdm客户端生成 防卸载6位的唯一标识密码
        String shortUdid = RandomUtil.getRandomString(6);
        device.setShortUdid(shortUdid);
        //设置challenge值
//        device.setChallenge(null);
        //device.setJailbreakFlag(null);

        device.setStatus(DeviceStatusConstant.LOGIN_ONLY);
//        device.setLostFlag(SysConstant.FALSE_VALUE);


        if (isNewDeviceBecauseNotRegistered) {
            this.deviceService.save(device);
        } else {
            LOG.info("#########updateDeviceDb udid is " + device.getUdid());
            this.deviceService.modify(device);
        }
        //公安版本
//        if ("public_security" .equals(sysConfiguration.getSysConfLicenseType())) {
//            //上报设备绑定到集中管控平台
//            LOG.info("######Report user and device  bindRelation to smcs, udid= " + device.getUdid() + ",userid is " + device.getUserId());
//            //TODO 暂时屏蔽smcsReportService.reportUserDeviceBind(device.getUserId(), device.getUdid());
//        }
    }

}
