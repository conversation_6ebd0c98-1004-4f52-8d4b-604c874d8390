package com.cyberscraft.uep.mdm.core.service.admin.impl;

import com.cyberscraft.uep.mdm.core.service.admin.IAuthorityService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/***
 *
 * @date 2021-09-16
 * <AUTHOR>
 ***/
@Service
public class AuthorityServiceImpl implements IAuthorityService {

    @Override
    public int procIpLog(String ip, int type) {
        return 0;
    }

    @Override
    public void addIpFailedLog(String ip, int type) {

    }

    @Override
    public void delIpFailedLog(String ip, int type) {

    }

    @Override
    public long ipUnlockTime(String clientIp) {
        return 0;
    }
}
