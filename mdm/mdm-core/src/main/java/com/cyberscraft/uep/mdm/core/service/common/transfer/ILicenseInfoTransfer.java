package com.cyberscraft.uep.mdm.core.service.common.transfer;

import com.cyberscraft.uep.mdm.api.dto.common.LicenseInfoVO;
import com.cyberscraft.uep.mdm.core.domain.LicenseInfo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/***
 *
 * @date 2021-09-16
 * <AUTHOR>
 ***/
@Mapper(componentModel = "spring")
public interface ILicenseInfoTransfer {

    /***
     *
     */
    ILicenseInfoTransfer INSTANCE = Mappers.getMapper( ILicenseInfoTransfer.class );

    /***
     * 转换成VO
     * @param domain
     * @return
     */
    LicenseInfoVO domainToVO(LicenseInfo domain);


    /***
     * 转换成VO
     * @param vo
     * @return
     */
    LicenseInfo voToDomain(LicenseInfoVO vo);
}
