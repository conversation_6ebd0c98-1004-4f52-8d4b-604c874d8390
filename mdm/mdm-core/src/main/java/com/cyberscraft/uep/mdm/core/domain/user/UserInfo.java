package com.cyberscraft.uep.mdm.core.domain.user;

import java.io.Serializable;
import java.util.List;

/***
 *
 * @date 2021/9/22
 * <AUTHOR>
 ***/
public class UserInfo implements Serializable {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 用户的电子邮件
     */
    private String email;

    /**
     * 用户的全名，即包括姓和名
     */
    private String name;

    /**
     * 用户的显示名，即昵称，缺省同用户登录名
     */
    private String nickname;

    /**
     * 用户的移动电话号码
     */
    private String phoneNumber;

    /**
     * 用户登录名
     */
    private String username;


    /**
     * 数据来源ID，同步或导入时使用
     */
    private Long connectorId;

    /**
     * 同步源的用户登录名
     */
    private String connectorUid;

    /**
     * 同步源的连接器类型
     */
    private Integer connectorType;

    /**
     * 同步源的用户实际sub id，通常唯一，且不可变，而connnectorUid是可变的
     */
    private String connectorSubId;

    /***
     * 用户所在组ID
     */
    private List<UserGroup> groups;

    /***
     *
     */
    private String tenantId;

    /**
     * 用户的状态， ['ACTIVE', 'INACTIVE']stringEnum:"ACTIVE", "INACTIVE"
     */
    private Integer status;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public Long getConnectorId() {
        return connectorId;
    }

    public void setConnectorId(Long connectorId) {
        this.connectorId = connectorId;
    }

    public String getConnectorUid() {
        return connectorUid;
    }

    public void setConnectorUid(String connectorUid) {
        this.connectorUid = connectorUid;
    }

    public String getConnectorSubId() {
        return connectorSubId;
    }

    public void setConnectorSubId(String connectorSubId) {
        this.connectorSubId = connectorSubId;
    }

    public List<UserGroup> getGroups() {
        return groups;
    }

    public void setGroups(List<UserGroup> groups) {
        this.groups = groups;
    }

    public Integer getConnectorType() {
        return connectorType;
    }

    public void setConnectorType(Integer connectorType) {
        this.connectorType = connectorType;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
