package com.cyberscraft.uep.mdm.core.service.config;

/***
 * Oauth2对应的配置类
 * @date 2021-09-29
 * <AUTHOR>
 ***/
public interface IOauth2Configuration {

    /***
     * 得到访问Token过期时间
     * @return
     */
    Integer getAccessTokenExpiresIn();

    /***
     * 得到刷新Token的过期时间
     * @return
     */
    Integer getRefreshTokenExpiresIn();

    /***
     * 获取当前的token生成策略
     * @return
     */
    Integer getTokenPolicy();
}
