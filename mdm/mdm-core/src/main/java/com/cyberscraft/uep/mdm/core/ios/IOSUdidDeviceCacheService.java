package com.cyberscraft.uep.mdm.core.ios;

import com.cyberscraft.uep.mdm.core.exception.MdmException;

/***
 * IOS中udid,设备对应的设备缓存接口,默认缓存有效值为7天
 * @date 2021/1/16
 * <AUTHOR>
 ***/
public interface IOSUdidDeviceCacheService {

    /****
     *
     * @param dudid
     * @return
     * @throws MdmException
     */
    Long getDeviceId(String dudid) throws MdmException;

    /****
     *
     * @param dudid
     * @param deviceId
     * @throws MdmException
     */
    void saveDeviceId(String dudid,Long deviceId) throws MdmException;
}
