package com.cyberscraft.uep.mdm.core.dbo.impl;

import com.cyberscraft.uep.mdm.api.errors.MdmErrorType;
import com.cyberscraft.uep.mdm.core.entity.ViolateDistDeviceEntity;
import com.cyberscraft.uep.mdm.core.dao.ViolateDistDeviceDao;
import com.cyberscraft.uep.mdm.core.dbo.ViolateDistDeviceDBO;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cyberscraft.uep.mdm.core.exception.MdmException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 违规分发设备表 服务实现类
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2019-08-28
 */
@Service
public class ViolateDistDeviceDBOImpl extends ServiceImpl<ViolateDistDeviceDao, ViolateDistDeviceEntity> implements ViolateDistDeviceDBO {

    @Resource
    private ViolateDistDeviceDao violateDistDeviceDao;

    @Override
    public void deleteByDevice(Long deviceId) throws MdmException {
        if(deviceId==null){
            throw new MdmException(MdmErrorType.DEVICE_ID_INVALID);
        }
        violateDistDeviceDao.deleteByDevice(deviceId);
    }
}
