package com.cyberscraft.uep.mdm.core.service.clientcmd.legacy.domain;

import java.io.Serializable;

/***
 *
 * @date 2021-11-22
 * <AUTHOR>
 ***/
public class DeviceLocation implements Serializable {

    private String longitude;
    private String latitude;
    private String flowNum;
    private String locatedTime;
    private String location;

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getFlowNum() {
        return flowNum;
    }

    public void setFlowNum(String flowNum) {
        this.flowNum = flowNum;
    }

    public String getLocatedTime() {
        return locatedTime;
    }

    public void setLocatedTime(String locatedTime) {
        this.locatedTime = locatedTime;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }
}
