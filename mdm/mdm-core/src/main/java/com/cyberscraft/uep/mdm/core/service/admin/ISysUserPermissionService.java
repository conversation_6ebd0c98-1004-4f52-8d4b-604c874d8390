package com.cyberscraft.uep.mdm.core.service.admin;

import java.util.List;

/***
 * 系统用户权限对应管理接口
 * @date 2021-09-16
 * <AUTHOR>
 ***/
public interface ISysUserPermissionService {


    /**
     * 获取管理员功能列表
     * @param loginId
     * @return
     */
    List<String> findFuncCodes(String loginId);

    /**
     * 获取管理员api列表
     * @param loginId
     * @return
     */
    List<String> findApiCodes(String loginId);
}
