package com.cyberscraft.uep.mdm.core.dbo;

import com.cyberscraft.uep.mdm.core.entity.TempDeviceIosCtrlEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cyberscraft.uep.mdm.core.exception.MdmException;

/**
 * <p>
 * ios设备远程控制指令临时表 服务类
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2019-08-28
 */
public interface TempDeviceIosCtrlDBO extends IService<TempDeviceIosCtrlEntity> {

    /****
     *
     * @param udid
     * @return
     * @throws MdmException
     */
    TempDeviceIosCtrlEntity getByUdid(String udid) throws MdmException;

    /****
     *
     * @param udid
     * @throws MdmException
     */
    void deleteByUdid(String udid) throws MdmException;
}
