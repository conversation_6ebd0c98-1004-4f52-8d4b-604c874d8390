package com.cyberscraft.uep.mdm.core.dbo.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cyberscraft.uep.common.dto.PageView;
import com.cyberscraft.uep.common.util.PagingUtil;
import com.cyberscraft.uep.mdm.core.dao.DeviceViolationLogDao;
import com.cyberscraft.uep.mdm.core.dbo.DeviceViolationLogDBO;
import com.cyberscraft.uep.mdm.core.entity.DeviceViolationLogEntity;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2019-08-28
 */
@Service
public class DeviceViolationLogDBOImpl extends ServiceImpl<DeviceViolationLogDao, DeviceViolationLogEntity> implements DeviceViolationLogDBO {
    @Override
    public PageView<DeviceViolationLogEntity> getPageList(LocalDateTime startTime, LocalDateTime endTime, Integer page, Integer size) {
        IPage<DeviceViolationLogEntity> myPage = new Page<>(page, size, true);
        LambdaQueryWrapper<DeviceViolationLogEntity> queryWrapper  = new QueryWrapper<DeviceViolationLogEntity>().lambda();

        if(startTime != null){
            queryWrapper.ge(DeviceViolationLogEntity::getViolationTime,startTime);
        }
        if(endTime != null){
            queryWrapper.le(DeviceViolationLogEntity::getViolationTime,endTime);
        }
        queryWrapper.orderByDesc(DeviceViolationLogEntity::getViolationTime);
        IPage<DeviceViolationLogEntity> resultPage = this.getBaseMapper().selectPage(myPage,queryWrapper);
        return PagingUtil.toPageView(resultPage);
    }
}
