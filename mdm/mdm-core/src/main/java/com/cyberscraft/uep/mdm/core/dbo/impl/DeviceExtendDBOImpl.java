package com.cyberscraft.uep.mdm.core.dbo.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cyberscraft.uep.mdm.core.dao.DeviceExtendDao;
import com.cyberscraft.uep.mdm.core.dbo.DeviceExtendDBO;
import com.cyberscraft.uep.mdm.core.entity.DeviceExtendEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * 设备信息表 服务实现类
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2019-08-28
 */
@Service
public class DeviceExtendDBOImpl extends ServiceImpl<DeviceExtendDao, DeviceExtendEntity> implements DeviceExtendDBO {

    @Resource
    private DeviceExtendDao deviceExtendDao;


//    @Override
//    public void updateDualStatus(Long id, Integer status) throws MdmException {
//        if (id == null) {
//            throw new MdmException(MdmErrorType.DEVICE_ID_INVALID);
//        }
//        if (status == null) {
//            throw new MdmException(ExceptionCodeEnum.PARAM_INVALID);
//        }
//        LambdaUpdateWrapper<DeviceExtendEntity> updateWrapper = new UpdateWrapper<DeviceExtendEntity>()
//                .lambda()
//                .eq(DeviceExtendEntity::getId, id)
//                .set(DeviceExtendEntity::getDualStatus, status)
//                .set(DeviceExtendEntity::getUpdateTime, LocalDateTime.now())
//                ;
//        this.update(updateWrapper);
//    }
}
