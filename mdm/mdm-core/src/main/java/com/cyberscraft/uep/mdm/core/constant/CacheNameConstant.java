package com.cyberscraft.uep.mdm.core.constant;

/***
 * 相关缓存名称常量
 * @date 2021-09-16
 * <AUTHOR>
 ***/
public class CacheNameConstant {

    /***
     * 管理员Token对应的cache名称
     */
    public final static String ADMIN_TOKEN_CACHE = "apiToken";

    /***
     * AccessToken对应的cache名称
     */
    public final static String ACCESS_TOKEN_CACHE = "accessToken";


    /***
     * 用户设备对应的AccessToken对应的cache名称
     */
    public final static String USER_DEVICE_ACCESS_TOKEN_CACHE = "userDeviceAcessToken";

    /***
     * Refresh Token对应的cache名称
     */
    public final static String REFRESH_TOKEN_CACHE = "refreshToken";


    /***
     * 用户设备对应的Refresh Token对应的cache名称
     */
    public final static String USER_DEVICE_REFRESH_TOKEN_CACHE = "userDeviceRefreshToken";

    /***
     * 验证码对应的cache名称
     */
    public final static String VERIFY_CODE_CACHE = "verifyCode";

    /***
     * 管理员组ID cache缓存名称
     */
    public final static String ADMIN_GROUPID_CACHE = "adminGroupId";

    /***
     * 组cache缓存名称
     */
    public final static String GROUP_CACHE = "group";


    /***
     * dudidDeviceId对应的cache名称
     */
    public final static String DUDID_DEVICEID_CACHE = "dudidDeviceId";

    /***
     * DeviceId对应的cache名称
     */
    public final static String DEVICEID_CACHE = "device";


    /***
     * DeviceId对应的Challenge cache名称
     */
    public final static String DEVICEID_CHALLENGE_CACHE = "deviceChallenge";


    /***
     * CHALLENGE对应的DeviceId cache名称
     */
    public final static String CHALLENGE_DEVICEID_CACHE = "challengeDevice";
}
