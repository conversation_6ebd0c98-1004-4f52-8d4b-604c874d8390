package com.cyberscraft.uep.mdm.core.service.clientcmd.impl;

import com.cyberscraft.uep.common.bean.Result;
import com.cyberscraft.uep.common.exception.ResponseResult;
import com.cyberscraft.uep.common.enums.Platform;
import com.cyberscraft.uep.mdm.api.dto.clientcmd.ClientCmdBody;
import com.cyberscraft.uep.mdm.api.dto.clientcmd.ClientCmdRequestDto;
import com.cyberscraft.uep.mdm.core.exception.MdmException;
import com.cyberscraft.uep.mdm.core.service.clientcmd.IClientCommandExecutor;
import com.cyberscraft.uep.mdm.core.service.clientcmd.IClientCommandService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

/***
 *
 * 客户端指令执行器服务实现类
 * @date 2021-09-27
 * <AUTHOR>
 ***/
@Service
public class ClientCommandServiceImpl implements IClientCommandService {

    /***
     *
     */
    private final static ConcurrentHashMap<String, IClientCommandExecutor> CMD_EXECUTOR_MAP = new ConcurrentHashMap<>(128);

    /***
     * 默认指令实现类
     */
    private final static IClientCommandExecutor EMPTY_CMD_EXECUTOR = new IClientCommandExecutor() {
        @Override
        public boolean isSupport(String cmdCode) throws MdmException {
            return false;
        }

        @Override
        public <T> T execute(ClientCmdBody request, Platform platform) throws MdmException {
            return null;
        }

        @Override
        public ClientCmdBody parseCmd(ClientCmdRequestDto requestDto) throws MdmException {
            return null;
        }
    };

    /****
     *
     */
    private final static Logger LOG = LoggerFactory.getLogger(ClientCommandServiceImpl.class);

    /***
     * 当前系统已经支持的指令执行器列表
     */
    @Resource
    private List<IClientCommandExecutor> executors;

    @Override
    public <T> Result<T> execute(ClientCmdRequestDto request) throws MdmException {
        LOG.debug("开始执行对应的指令{}", request.getType());
        IClientCommandExecutor executor = getExecutor(request);
        if (executor == null || executor == EMPTY_CMD_EXECUTOR) {
            LOG.info("未找到指令{}对应的执行器，本次执行结果", request.getType());
            return null;
        }

        ClientCmdBody cmdBody = request.getCmdBody();
        if (cmdBody == null && StringUtils.isNotBlank(request.getBody())) {
            cmdBody = executor.parseCmd(request);
        }
        T rs = executor.execute(cmdBody, request.getPlatform());
        LOG.debug("执行对应的指令{}完成", request.getType());
        return ResponseResult.success(rs);
    }

    /***
     *
     * @param requestDto
     * @return
     */
    private IClientCommandExecutor getExecutor(ClientCmdRequestDto requestDto) {
        if (requestDto == null) {
            return null;
        }
        String cmdCode = requestDto.getType();
        IClientCommandExecutor executor = CMD_EXECUTOR_MAP.get(cmdCode);
        if (executor != null) {
            return executor;
        }
        synchronized (cmdCode) {
            executor = CMD_EXECUTOR_MAP.get(cmdCode);
            if (executor != null) {
                return executor;
            }
            if (this.executors != null && this.executors.size() > 0) {
                for (IClientCommandExecutor el : this.executors) {
                    if (el.isSupport(cmdCode)) {
                        CMD_EXECUTOR_MAP.put(cmdCode, el);
                        return el;
                    }
                }
            }
            CMD_EXECUTOR_MAP.put(cmdCode, EMPTY_CMD_EXECUTOR);
            return EMPTY_CMD_EXECUTOR;
        }
    }

}
