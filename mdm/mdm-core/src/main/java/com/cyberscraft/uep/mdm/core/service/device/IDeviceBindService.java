package com.cyberscraft.uep.mdm.core.service.device;

import com.cyberscraft.uep.common.enums.Platform;
import com.cyberscraft.uep.mdm.api.dto.device.DeviceActivatedDto;
import com.cyberscraft.uep.mdm.core.domain.user.UserInfo;
import com.cyberscraft.uep.mdm.core.domain.user.UserOrGroupConfig;
import com.cyberscraft.uep.mdm.core.entity.DeviceEntity;
import com.cyberscraft.uep.mdm.core.exception.MdmException;

/***
 * 设备激活服务业务接口
 * @date 2021-10-10
 * <AUTHOR>
 ***/
public interface IDeviceBindService {

    /***
     * 绑定设备到当前登录用户
     * @param user
     * @param deviceDto
     * @param platform
     * @return
     * @throws MdmException
     */
    DeviceEntity bindDeviceToUser(UserInfo user, UserOrGroupConfig userConfig, DeviceActivatedDto deviceDto, Platform platform) throws MdmException;

    /***
     * 绑定设备到当前登录用户（主要用于异步写入设备数据到数据库)
     * @param deviceEntity
     * @return
     * @throws MdmException
     */
    DeviceEntity bindDeviceToUser(DeviceEntity deviceEntity) throws MdmException;

    /****
     *
     * @param dto
     * @return
     * @throws MdmException
     */
    String createUdid(DeviceActivatedDto dto, Platform platform) throws MdmException;

}
