package com.cyberscraft.uep.mdm.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

/**
 * <p>
 * 设备型号信息表
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2019-09-11
 */
@TableName("t_device_model")
public class DeviceModelEntity implements Serializable {

    private static final long serialVersionUID=1L;

   @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 设备型号
     */
    private String model;

    /**
     * 设备类型  1--android;2--ios
     */
    private Integer type;

    /**
     * 设备图片路径
     */
    private String picturePath;

    /**
     * 租户ID
     */
    private String tenantId;

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getPicturePath() {
        return picturePath;
    }

    public void setPicturePath(String picturePath) {
        this.picturePath = picturePath;
    }

    @Override
    public String toString() {
        return "DeviceModelEntity{" +
        "id=" + id +
        ", model=" + model +
        ", type=" + type +
        ", picturePath=" + picturePath +
        "}";
    }
}
