package com.cyberscraft.uep.mdm.core.dao;

import com.cyberscraft.uep.mdm.core.entity.ViolateDistDeviceEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cyberscraft.uep.mdm.core.exception.MdmException;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 违规分发设备表 Mapper 接口
 * </p>
 *
 * <AUTHOR> CodeGenerator
 * @since 2019-08-28
 */
public interface ViolateDistDeviceDao extends BaseMapper<ViolateDistDeviceEntity> {

    /****
     *
     * @param deviceId
     * @throws MdmException
     */
    void deleteByDevice(@Param("deviceId") Long deviceId) throws MdmException;

}
