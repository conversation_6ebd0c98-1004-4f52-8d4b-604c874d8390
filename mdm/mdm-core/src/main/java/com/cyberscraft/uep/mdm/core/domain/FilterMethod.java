package com.cyberscraft.uep.mdm.core.domain;

import com.thoughtworks.xstream.annotations.XStreamAsAttribute;

import java.io.Serializable;

/***
 *
 * @date 2021-09-19
 * <AUTHOR>
 ***/
public class FilterMethod implements Serializable {

    private static final long serialVersionUID = -3468066695813302873L;
    @XStreamAsAttribute
    private String url;
    @XStreamAsAttribute
    private String type = "ignore";
    @XStreamAsAttribute
    private String rule = "equals";

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getRule() {
        return rule;
    }

    public void setRule(String rule) {
        this.rule = rule;
    }
}
