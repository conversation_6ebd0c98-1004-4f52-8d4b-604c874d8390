package com.cyberscraft.uep.mdm.core.service.basic.impl;

import com.cyberscraft.uep.common.util.UUIDUtil;
import com.cyberscraft.uep.mdm.api.dto.tenant.TenantCreateRequestDto;
import com.cyberscraft.uep.mq.constant.MQConstant;
import com.cyberscraft.uep.mdm.core.exception.MdmException;
import com.cyberscraft.uep.mdm.core.service.basic.ITenantMessageSendService;
import com.cyberscraft.uep.mq.service.IMessageSendClient;
import com.cyberscraft.uep.mq.vo.MessageEntry;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/***
 *
 * @date 2021-10-21
 * <AUTHOR>
 ***/
@Service
public class TenantMessageSendServiceImpl implements ITenantMessageSendService {

    @Resource
    IMessageSendClient messageSendClient;

    @Override
    public void send(TenantCreateRequestDto dto) throws MdmException {
        MessageEntry<TenantCreateRequestDto> msg = new MessageEntry<>();
        msg.setId(UUIDUtil.getUUID());
        msg.setMsg(dto);
        messageSendClient.send(MQConstant.MDM_TENANT_CREATE_MESSAGE_TOPIC, msg);
    }
}
