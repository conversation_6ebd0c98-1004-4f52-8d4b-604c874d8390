package com.cyberscraft.uep.mdm.core.service.event.handler.impl.policy;

import com.cyberscraft.uep.common.enums.LogTag;
import com.cyberscraft.uep.common.enums.Platform;
import com.cyberscraft.uep.common.logger.DigitalSeeLoggerFactory;
import com.cyberscraft.uep.iam.constants.RedisKeyConstants;
import com.cyberscraft.uep.mdm.api.constant.PolicyStatus;
import com.cyberscraft.uep.mdm.api.constant.PolicySubCategory;
import com.cyberscraft.uep.mdm.api.dto.cmd.CmdTargetDto;
import com.cyberscraft.uep.mdm.api.dto.policy.CombinedPolicyPayloadVO;
import com.cyberscraft.uep.mdm.api.dto.policy.PlatformPolicyVO;
import com.cyberscraft.uep.mdm.api.event.Event;
import com.cyberscraft.uep.mdm.api.event.enums.EventBusinessType;
import com.cyberscraft.uep.mdm.api.event.enums.EventChangeType;
import com.cyberscraft.uep.mdm.core.constant.PolicyConstant;
import com.cyberscraft.uep.mdm.core.domain.cmd.CmdInfoDTO;
import com.cyberscraft.uep.mdm.core.entity.*;
import com.cyberscraft.uep.mdm.core.exception.MdmException;
import com.cyberscraft.uep.mdm.core.service.cmd.CmdType;
import com.cyberscraft.uep.mdm.core.service.user.IUserServiceNew;
import com.cyberscraft.uep.mdm.core.util.PolicyUtil;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.tuple.Pair;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.stream.Collectors;

@Component
public class PushPolicyEventHandler extends PolicyEventHandler {
    private final static Logger logger = DigitalSeeLoggerFactory.getLogger(PushPolicyEventHandler.class, LogTag.POLICY);


    @Resource
    private IUserServiceNew userService;

    @Resource
    private RedissonClient redissonClient;

    /****
     * 同步锁的自然过期时间，单位毫秒
     */
    private final static Long PUSH_POLICY_LOCK_EXPIRE = 180 * 1000L;

    /***
     *  PUSH_POLICY 同步使用的key
     */
    public final static String PUSH_POLICY_LOCK_KEY = "MDM:PUSH_POLICY_LOCK:%s:%s";

    @Override
    public void handlerEvent(Event event) {
        logger.info("receive {}", event);
        Assert.isNull(event.getContent(), "payload must not be null");

        PolicyEntity policy = getPolicy(event);
        String policyId = policy.getId();
        String policyContent = policy.getContent();
        logger.info("policy id: {}, policy name: {}, policy subCatagory: {}, content: {}",
                policyId, policy.getName(), policy.getSubCategory(), policyContent);
        CmdTargetDto targetDto = (CmdTargetDto) event.getTarget();

        Map<Long, DeviceEntity> deviceMap = createGroupAndUserRelationships(targetDto, policy);
        CombinedPolicyPayloadVO vo = PolicyUtil.toVO(policy);

        for (PlatformPolicyVO platformPolicyVO : vo.getPlatforms()) {
            Platform platform = platformPolicyVO.getPlatform();

            List<DeviceEntity> currentDistributeDevices = createDevicesRelationships(deviceMap, policyId, platform);
            logger.info("policy id = {}, policy name = {}, platform = {}, currentDistributeDevices size = {}",
                    policyId, policy.getName(), platform, currentDistributeDevices.size());

            if (currentDistributeDevices.size() == 0) {
                continue;
            }

            if (policy.getStatus() != PolicyStatus.ENABLED.getValue()) {
                /*
                 *  管理平台的策略操作限制是只有启用状态的策略才能下发，但是存在这种情况：
                 *  已启用的策略下发给用户A，然后再禁用策略，然后在用户A下激活设备，
                 *  此时需要创建策略和设备的关联关系，但不需要下发指令。
                 */
                continue;
            }

            triggerMDMAction(policy, CmdType.INSTALL_POLICY, platformPolicyVO, currentDistributeDevices);

            CmdInfoDTO cmdInfo = new CmdInfoDTO();
            cmdInfo.setFlowNum(policyId);//TODO
            cmdInfo.setTenantId(event.getTenantId());
            cmdInfo.setCmdType(CmdType.INSTALL_POLICY);
            cmdInfo.setPolicyId(policyId);
            cmdInfo.setPolicySubCategory(PolicySubCategory.findNameByValue(policy.getSubCategory()));
            cmdInfo.setCustomized(platformPolicyVO.isCustomized());
            cmdInfo.setCmdBody(vo.getClientPayloadAsString(platform));

            String policyProfile;
            try {
                policyProfile = commandProfileBuilder.buildCmdProfile(platform, cmdInfo);
            } catch (MdmException e) {
                logger.error("Fail to build profile for platform {}", platform.getValue());
                continue;
            }
            for (DeviceEntity device : currentDistributeDevices) {
                commandProcessService.processCmd(device, cmdInfo, policyProfile);
            }
            commandProcessService.batchWakeUpDevice(platform, currentDistributeDevices, cmdInfo);
        }
    }

    /**
     * 创建策略和要下发的组、用户的对应关系，并查询出所有需要推送的设备
     *
     * @param targetDto
     * @return 返回当前需要推送策略的所有设备（不含数据库中已存在设备-策略下发关系的数据）
     */
    private Map<Long, DeviceEntity> createGroupAndUserRelationships(CmdTargetDto targetDto, PolicyEntity policy) {
        // 设置锁定
        String tenantId = TenantHolder.getTenantCode();
        String key = String.format(PUSH_POLICY_LOCK_KEY, tenantId, policy.getId());
        RLock lock = redissonClient.getLock(key);
        // 找到所有需要推送策略的设备
        Map<Long, DeviceEntity> allDeviceMap = new HashMap<>(128);
        try {
            lock.lock(PUSH_POLICY_LOCK_EXPIRE, TimeUnit.MILLISECONDS);
            // 业务完成进行解锁
            String policyId = policy.getId();
            // 所有需要建立下发关系的用户
            Set<Long> allUserIds = new HashSet<>(128);
            // 建立策略和用户组的下发关系
            List<String> groupIds = targetDto.getGroups();
            if (groupIds != null && !groupIds.isEmpty()) {
                List<Long> longGroupIds = groupIds.stream().map(Long::parseLong).collect(Collectors.toList());
                // 查找所有组和子组的ID，查询数据库，如果库里没有则持久化
                List<Long> allGroupIds = userService.getGroupAndSubGroupIdsById(longGroupIds);
                List<Long> dbGroupIds = policyGroupDBO.getGroupIdsByPolicyId(policyId);
                List<Long> groupNotFoundInDb = allGroupIds.stream().filter(groupId -> !dbGroupIds.contains(groupId)).collect(Collectors.toList());
                for (Long groupId : groupNotFoundInDb) {
                    policyGroupDBO.save(createPolicyGroup(policyId, groupId, longGroupIds));
                }

                // 查询组和子组下的所有有效用户id
                allUserIds.addAll(userService.getValidUserIdsByGroupIds(longGroupIds, true));
            }

            // 查找策略直接推送的用户id
            Set<Long> directUserIds = new HashSet<>();
            List<String> userIds = targetDto.getUsers();
            if (userIds != null && !userIds.isEmpty()) {
                directUserIds.addAll(userIds.stream().map(Long::parseLong).collect(Collectors.toSet()));
            }
            // 根据用户loginId查找用户
            List<String> userLoginIds = targetDto.getUserLoginIds();
            if (userLoginIds != null && !userLoginIds.isEmpty()) {
                directUserIds.addAll(userService.getValidUserIdsByUserNames(userLoginIds));
            }
            allUserIds.addAll(directUserIds);
            logger.info("allUserIds = {}", allUserIds);

            List<String> otherPolicyIds = new ArrayList<>();
            // 建立策略和用户的下发关系
            if (!allUserIds.isEmpty()) {
                // 策略覆盖原则，给用户下发新的策略时要删除以前的同类型策略
                // 查询出目标用户已经下发过的策略
                List<PolicyUserEntity> allUserPolicys = policyUserDBO.getByUserIds(allUserIds);

                // 过滤出与当前策略相同类型的其他策略
                List<PolicyUserEntity> otherUserPolicys = allUserPolicys.stream()
                        .filter(pu -> pu.getSubCategory() != null && pu.getSubCategory() == policy.getSubCategory())
                        .filter(pu -> !pu.getPolicyId().equals(policyId))
                        .collect(Collectors.toList());

                // 删除相同类型的其他策略与人的关联关系
                if (!otherUserPolicys.isEmpty()) {
                    policyUserDBO.removeByIds(otherUserPolicys.stream().map(PolicyUserEntity::getId).collect(Collectors.toList()));
                }
                otherPolicyIds.addAll(otherUserPolicys.stream().map(PolicyUserEntity::getPolicyId).collect(Collectors.toList()));

                logger.info("otherPolicyIds: {}", otherPolicyIds);

                // 查找当前策略未下发过的用户，创建下发关系
                List<Long> currentDbUserIds = allUserPolicys.stream()
                        .filter(pu -> pu.getPolicyId().equals(policyId))
                        .map(PolicyUserEntity::getUserId)
                        .collect(Collectors.toList());
                logger.info("currentDbUserIds size: {}", currentDbUserIds.size());
                List<Long> userNotFoundInDb = allUserIds.stream().filter(userId -> !currentDbUserIds.contains(userId)).collect(Collectors.toList());
                if (!userNotFoundInDb.isEmpty()) {
                    logger.info("userNotFoundInDb size: {}", userNotFoundInDb.size());
                    for (Long userId : userNotFoundInDb) {
                        int customFlag = directUserIds.contains(userId) ? 1 : 0;
                        policyUserDBO.save(createPolicyUser(policy, userId, customFlag));
                    }

                }
                // 查出所有待下发用户的设备（因为有先下发策略给用户、后激活设备的情况，所以要全查出来）
                List<DeviceEntity> devicesInUsers = deviceService.getActivedDevicesByUserIds(allUserIds);
                logger.info("devicesInUsers size: {}", devicesInUsers.size());
                devicesInUsers.forEach(d -> allDeviceMap.put(d.getId(), d));
            }

            // 查找当前直接推送的所有设备
            List<String> deviceIds = targetDto.getDevices();
            if (deviceIds != null && !deviceIds.isEmpty()) {
                List<Long> longDeviceIds = deviceIds.stream().map(Long::parseLong).collect(Collectors.toList());
                List<DeviceEntity> devicesDirect = deviceService.getActivedDevicesByDeviceIds(longDeviceIds);
                devicesDirect.forEach(d -> allDeviceMap.put(d.getId(), d));
            }
            // 查找数据库中设备-策略关系的数据，并从allDeviceMap中去除
            if (allDeviceMap.size() > 0) {
                List<DeviceProfileEntity> deviceProfiles = deviceProfileDBO.getByPolicyAndDevices(policyId, allDeviceMap.keySet());
                Set<Long> dbProfileDeviceIds = deviceProfiles.stream().map(DeviceProfileEntity::getDeviceId).collect(Collectors.toSet());
                dbProfileDeviceIds.forEach(deviceId -> allDeviceMap.remove(deviceId));
                if (otherPolicyIds != null && !otherPolicyIds.isEmpty()) {
                    deviceProfileDBO.removeByPolicyIdsDeviceIds(otherPolicyIds, allDeviceMap.keySet());
                }
            }
            logger.info("allDeviceMap size = {}", allDeviceMap.size());
            return allDeviceMap;
        } finally {
            // 解锁
            if (lock != null) {
                lock.unlock();
            }
        }
    }


    /**
     * 创建策略和设备的下发关系，因为下发给设备时要判断设备的平台类型(ios,android等)，所以和用户、组的关系创建分开处理
     *
     * @param deviceMap
     * @param policyId
     * @param platform
     * @return 返回当前待下发的设备列表，已经下发过策略的设备不再重新下发
     */
    private List<DeviceEntity> createDevicesRelationships(Map<Long, DeviceEntity> deviceMap, String policyId, Platform platform) {
        // 查询与策略平台类型一致的设备
        List<DeviceEntity> devicesByPlatform = deviceMap.values().stream()
                .filter(device -> device.getType() == platform.getValue())
                .collect(Collectors.toList());
        // 创建设备-策略下发关系
        devicesByPlatform.forEach(device -> {
            deviceProfileDBO.save(createDeviceProfile(policyId, device));
        });
        return devicesByPlatform;
    }

    private PolicyGroupEntity createPolicyGroup(String policyId, Long groupId, List<Long> originalGroupIds) {
        PolicyGroupEntity policyGroup = new PolicyGroupEntity();
        policyGroup.setPolicyId(policyId);
        policyGroup.setGroupId(groupId);
        // 组和策略的关系是当前推送指定的还是从父级组继承的，0继承    1直接指定
        policyGroup.setCustomFlag(originalGroupIds.contains(groupId) ? 1 : 0);
        return policyGroup;
    }

    private PolicyUserEntity createPolicyUser(PolicyEntity policy, Long userId, int customFlag) {
        PolicyUserEntity policyUser = new PolicyUserEntity();
        policyUser.setPolicyId(policy.getId());
        policyUser.setSubCategory(policy.getSubCategory());
        policyUser.setUserId(userId);
        // 用户和策略的关系是当前推送指定的还是从组继承的，0继承    1直接指定
        policyUser.setCustomFlag(customFlag);
        return policyUser;
    }

    private DeviceProfileEntity createDeviceProfile(String policyId, DeviceEntity device) {
        DeviceProfileEntity deviceProfile = new DeviceProfileEntity();
        deviceProfile.setPolicyId(policyId);
        deviceProfile.setDeviceId(device.getId());
        deviceProfile.setUdid(device.getUdid());
        LocalDateTime now = LocalDateTime.now();
        deviceProfile.setCreateTime(now);
        deviceProfile.setStatus(PolicyConstant.DEVICE_POLICY_STAUTS_PUSHED);
        return deviceProfile;
    }

    @SuppressWarnings("unchecked")
    @Override
    public Set<Pair<EventBusinessType, EventChangeType>> supportedEvents() {
        return Sets.newHashSet(
                Pair.of(EventBusinessType.POLICY, EventChangeType.PUSH));
    }
}