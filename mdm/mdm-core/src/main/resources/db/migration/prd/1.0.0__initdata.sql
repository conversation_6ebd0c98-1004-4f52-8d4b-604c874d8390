-- ----------------------------
-- 设备表
-- ----------------------------

DROP TABLE IF EXISTS `t_mdm_device`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_mdm_device` (
        `id` bigint(20) NOT NULL AUTO_INCREMENT,
        `userid` bigint(20) DEFAULT NULL COMMENT '所属用户用户id',
        `shortudid` varchar(10) DEFAULT NULL COMMENT 'unique device short identifier',
        `udid` char(64) DEFAULT NULL COMMENT 'unique device identifier',
        `devicetype` int(11) NOT NULL DEFAULT '10' COMMENT '设备类型 10-android phone 11-android pad;20-iphone 21-ipad 22-i<PERSON><PERSON> 23- ipodtouch',
        `type` int(11) NOT NULL DEFAULT '1' COMMENT '平台1--android;2--ios',
        `rootflag` int(11) DEFAULT '0' COMMENT '是否破解root, 0--未破解 ;1--破解 ',
        `lastonlinetime` datetime DEFAULT NULL COMMENT '最后在线时间',
        `onlineflag` int(11) DEFAULT '0' COMMENT '设备是否在线 0--在线;1--不在线',
        `status` int(11) DEFAULT '1' COMMENT '设备状态,0--未注册,1--注册未激活,2--激活可用, 3--保留 4--擦除',
        `registtime` datetime DEFAULT NULL COMMENT '注册时间',
        `activatetime` datetime DEFAULT NULL COMMENT '激活时间',
        `updatetime` datetime DEFAULT NULL COMMENT '设备信息更新时间',
        `devicename` varchar(100) DEFAULT NULL COMMENT '设备名称',
        `userName` varchar(150) DEFAULT NULL COMMENT '用户名',
        `loginId` varchar(50) DEFAULT NULL COMMENT '用户登录名',
        `openUserId` varchar(50) DEFAULT NULL COMMENT '第三方平台用户Id',
        `groupId` bigint(20) DEFAULT NULL COMMENT '所属用户组Id',
        `dudid` varchar(64) comment '设备的系统UDID值,设备MDM激活后得到的设备唯一ID,原来的UDID为应用生成的UMID',
        `activateMdmTime` datetime comment '设备的MDM激活时间',
        `mdmStatus` int(11) DEFAULT 0 comment '设备的MDM激活状态',
        `dualstatus` int(11) DEFAULT NULL COMMENT '双域状态（0-工作域 1-生活域）',
        `uemVersion` varchar(50) DEFAULT NULL COMMENT 'UEM版本号',
        `clientversion` varchar(30) DEFAULT '' COMMENT 'EMM 客户端版本号',
        `ip` varchar(20) DEFAULT NULL COMMENT 'ip地址',
        `clientbuildnum` varchar(50) DEFAULT NULL COMMENT 'EMM客户端Build号',
        `tenantId` varchar(64) NOT NULL COMMENT '租户ID',

        `idfa` char(64) DEFAULT NULL COMMENT 'unique device identifier(IOS7)',
        `imei` varchar(50) DEFAULT NULL COMMENT 'imei:international mobile equipment identity',
        `imsi` varchar(50) DEFAULT NULL COMMENT 'imsi:international mobile subscriber identity',
        `serialnum` varchar(50) DEFAULT NULL COMMENT '设备序列号',
        `manufacturer` varchar(200) DEFAULT NULL COMMENT '设备厂商',
        `model` varchar(100) DEFAULT NULL COMMENT '设备型号,例如：MC319LL',
        `os` varchar(100) DEFAULT NULL COMMENT '操作系统平台',
        `versionNum` varchar(100) DEFAULT NULL COMMENT ' 版本号',
        `oscoreversion` varchar(50) DEFAULT NULL COMMENT '系统内核版本号',
        `cpu` varchar(100) DEFAULT NULL,
        `ram` varchar(100) DEFAULT NULL,
        `camera` varchar(100) DEFAULT NULL COMMENT '摄像头',
        `wifimac` varchar(30) DEFAULT NULL COMMENT 'wifi mac地址',
        `bluetoothmac` varchar(30) DEFAULT NULL COMMENT '蓝牙 mac地址',
        `productname` varchar(100) DEFAULT NULL COMMENT '设备型号码 ,例如：iPhone3,1',


        PRIMARY KEY (`id`),
        KEY `t_mdm_evice_idx_1` (`userid`,`tenantId`,`type`,`deviceType`,`udid`,`status`,`groupId`,`loginId`,`userName`,`openUserId`),
        KEY `t_mdm_device_idx_2` (`type`,`tenantId`,`status`,`onlineflag`,`lastonlinetime`),
        KEY `t_mdm_device_idx_3` (`udid`,`tenantId`,`userid`,`status`),
        KEY `t_mdm_device_idx_4` (`groupId`,`tenantId`,`type`,`devicetype`,`status`,`userid`,`loginId`,`userName`,`openUserId`),
        KEY `t_mdm_device_idx_5` (`loginId`,`tenantId`,`type`,`devicetype`,`status`,`groupId`,`userName`,`openUserId`),
        KEY `t_mdm_device_idx_6` (`dudid`,`tenantId`,`status`,`mdmStatus`,`activateMdmTime`,`groupId`,`userId`,`loginId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备信息表';
/*!40101 SET character_set_client = @saved_cs_client */;



-- ----------------------------
-- 设备表扩展表
-- ----------------------------
DROP TABLE IF EXISTS `t_mdm_device_extend`;
/*!40101 SET @saved_cs_client = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_mdm_device_extend` (
       `id` bigint(20) NOT NULL AUTO_INCREMENT,
       `buytime` date DEFAULT NULL COMMENT '购买时间',
       `warranty` int(11) DEFAULT NULL COMMENT '保修期',
       `relationship` int(11) NOT NULL DEFAULT '1' COMMENT '设备所属关系 1--公司设备;2--员工设备;3--其他',
       `enableperm` int(11) DEFAULT '1' COMMENT '是否启用权限配置引导，1 -- 启用；0 -- 不启用',
       `devicename` varchar(100) DEFAULT NULL COMMENT '设备名称',
       `devicenumber` varchar(50) DEFAULT NULL COMMENT '设备编号',
       `initsiminfo` varchar(50) DEFAULT NULL COMMENT '激活时SIM卡信息，Android设备为IMSI，iOS设备为ICCID',
       `initsdserialnum` varchar(100) DEFAULT NULL COMMENT '激活时的SD卡序列号',
       `iccid` varchar(50) DEFAULT NULL,
       `phonecode` varchar(100) DEFAULT NULL COMMENT '手机号码',
       `romcapacity` varchar(100) DEFAULT NULL COMMENT 'rom 总容量',
       `romavailablecapacity` varchar(100) DEFAULT NULL COMMENT 'rom 可用空间',
       `sdcapacity` varchar(100) DEFAULT NULL COMMENT 'sd卡容量',
       `sdavailablecapacity` varchar(100) DEFAULT NULL COMMENT 'sd卡可用空间',
       `sdserialnum` varchar(100) DEFAULT NULL COMMENT 'sd卡序列号',
       `powerstatus` varchar(100) DEFAULT NULL COMMENT '电源状态',
       `boottime` bigint(20) DEFAULT NULL COMMENT '开机时长',
       `capacity` varchar(100) DEFAULT NULL COMMENT '存储容量(ios)',
       `capacitystatus` varchar(100) DEFAULT NULL COMMENT '存储容量状态(ios)',
       `availablecapacity` varchar(100) DEFAULT NULL COMMENT '可用空间(ios)',
       `ip` varchar(20) DEFAULT NULL COMMENT 'ip地址',
       `encryptionlevel` int(11) DEFAULT NULL COMMENT '加密级别',
       `token` varchar(200) DEFAULT NULL COMMENT 'ios mdm发送唤醒使用',
       `pushmagic` varchar(200) DEFAULT NULL COMMENT 'ios mdm发送唤醒使用',
       `unlocktoken` varchar(4000) DEFAULT NULL COMMENT 'ios mdm解除锁屏使用',
       `sendregistnoticeflag` int(11) DEFAULT '0' COMMENT '是否发送注册通知信息,0--否;1--是',
       `allowactrootorjailbreakflag` int(11) DEFAULT '0' COMMENT '是否允许root/越狱设备激活,0--否;1--是',
       `remark` varchar(500) DEFAULT NULL COMMENT '备注',
       `roamingflag` int(11) DEFAULT '0' COMMENT '设备漫游开关 0--否;1--是',
       `updatetime` datetime DEFAULT NULL COMMENT '设备信息更新时间',
       `apnstoken` varchar(200) DEFAULT NULL COMMENT 'apns的设备token',
       `lostflag` int(11) DEFAULT '0' COMMENT '是否丢失：0 --未丢失，1 --丢失',
       `dataencryptionflag` int(11) DEFAULT '0' COMMENT '是否开启数据加密.0--否;1--是',
       `specialtypeflag` int(11) DEFAULT '0' COMMENT '特殊设备类型标识.0--普通设备;100--Samsung safe设备;101--Samsung KNOX设备;301--华为设备',
       `knoxstatus` int(11) DEFAULT '0' COMMENT 'knox状态,0--不支持;201--支持未注入;202--SAFE license已注入;203--KNOX license已注入;101--已创建;501--锁定;301--华为插件已激活;302--移除华为插件',
       `offlinestatus` int(11) NOT NULL DEFAULT '0' COMMENT '设备是否失联：0未失联，1失联',
       `offlinethreshold` varchar(5) NOT NULL DEFAULT '0' COMMENT '设备失联违规天数',
       `emmloginagain` int(11) DEFAULT '0' COMMENT '自服务是否修改密码 0-未修改 1-已修改',
       `hostid` varchar(50) DEFAULT NULL COMMENT 'hostId proxy激活时的hostId',
       `usagestats` int(2) DEFAULT '0' COMMENT '应用使用量统计状态 0 未启用，1 启用',
       `flowupdate` int(2) DEFAULT '0' COMMENT '流量是否有更新 0 未更新，1 有更新',
       `flowquota` int(10) DEFAULT '0' COMMENT '流量配额',
       `bitlocker` int(11) DEFAULT NULL COMMENT 'BitLocker加密状态 0:未加密 1:整个硬盘加密 2:系统分区加密',
       `meid` varchar(50) DEFAULT NULL COMMENT '副卡对应的设备识别码,(如无副卡,可为空)',
       `resolution` varchar(50) DEFAULT NULL COMMENT '屏幕分辨率,(字符串,长*高,如640*480)',
       `ossoftwareversion` varchar(50) DEFAULT NULL COMMENT '系统软件版本号',
       `safetyosversion` varchar(50) DEFAULT NULL COMMENT '安全加固双操作系统版本,(如无,可为空)',
       `patchlevel` varchar(50) DEFAULT NULL COMMENT '系统安全补丁程序级别(如无,可为空)',
       `iccid2` varchar(50) DEFAULT NULL COMMENT 'ICCID(SIM卡2的ICCID)',
       `imsi2` varchar(50) DEFAULT NULL COMMENT 'IMSI(SIM卡2的IMSI,如无卡2，可为空)',
       `networktype` varchar(50) DEFAULT NULL COMMENT '支持的移动网络制式',
       `wlanadapterchip` varchar(50) DEFAULT NULL COMMENT '无线网卡芯片型号',
       `btadapterchip` varchar(50) DEFAULT NULL COMMENT '蓝牙芯片型号',
       `nfcchip` varchar(50) DEFAULT NULL COMMENT 'NFC芯片型号',
       `locatorchip` varchar(50) DEFAULT NULL COMMENT '定位芯片型号',
       `clientbuildnum` varchar(50) DEFAULT NULL COMMENT 'EMM客户端Build号',
       `cpuratio` varchar(30) DEFAULT NULL COMMENT 'CPU占用率',
       `memoryratio` varchar(30) DEFAULT NULL COMMENT '内存占用率',
       `storageratio` varchar(30) DEFAULT NULL COMMENT '存储占用率',
       `bluetoothstate` int(11) DEFAULT NULL COMMENT '蓝牙状态，1-开启,0 -关闭',
       `wifistate` int(11) DEFAULT NULL COMMENT 'wifi状态，1-开启 0-关闭',
       `camerastate` int(11) DEFAULT NULL COMMENT '相机状态，1-开启 0-关闭',
       `microphonestate` int(11) DEFAULT NULL COMMENT '麦克风状态，1-开启 0-关闭',
       `mobiledatastate` int(11) DEFAULT NULL COMMENT '移动数据状态，1-开启 0-关闭',
       `apn` varchar(500) DEFAULT NULL COMMENT 'apn集合，status:0 未连接 ，1连接',
       `buildnumber` varchar(200) DEFAULT NULL COMMENT '内核版本',
       `gpsstate` int(11) DEFAULT NULL COMMENT 'GPS状态：0-关闭 1-开启',
       `syncflag` int(11) DEFAULT '0' COMMENT '是否上报成功 1-上报绑定成功 10-上报绑定失败 2-上报解绑成功 20-上报解绑失败',
       `dualstatus` int(11) DEFAULT NULL COMMENT '双域状态（0-工作域 1-生活域）',
       `simoper` int(11) DEFAULT NULL COMMENT 'sim卡操作 1-更新sim卡 0-撤销更新sim卡',
       `tpmreport` int(11) DEFAULT NULL COMMENT '可信度 2-不安全 1-安全',
       `systemintegrity` int(11) DEFAULT NULL COMMENT '系统完整性 2-不完整 1-完整',
       `idp` varchar(20) DEFAULT NULL COMMENT '第三方idp名称，idp_device_id对应',
       `idpdeviceid` varchar(100) DEFAULT NULL COMMENT 'device_id对应的idp',
       `offlineTime` timestamp NULL DEFAULT NULL COMMENT '失联时间',
       `enablepermissionguide` int(11) DEFAULT '1' COMMENT '强管控权限向导，1 -- 强管控；0 -- 弱管控',
                                       PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备扩展信息表';



--
-- 全局策略表
--

DROP TABLE IF EXISTS `t_mdm_policy_global`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_mdm_policy_global` (
  `id` varchar(100) NOT NULL COMMENT 'uuid',
  `name` varchar(200) NOT NULL COMMENT '名称',
  `category` int(11) DEFAULT NULL COMMENT '策略大的分类，配置策略、限制策略、围栏策略、合规策略等',
  `sub_category` int(11) DEFAULT NULL COMMENT '策略小的分类，WiFi配置策略、VPN配置策略等',
  `update_by` varchar(200) DEFAULT NULL COMMENT '更新人姓名',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `content` longtext COMMENT '扩展存放json',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='全局默认策略表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- 策略表
--

DROP TABLE IF EXISTS `t_mdm_policy`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_mdm_policy` (
  `id` varchar(100) NOT NULL COMMENT 'uuid',
  `name` varchar(200) NOT NULL COMMENT '名称',
  `category` int(11) DEFAULT NULL COMMENT '策略大的分类，配置策略、限制策略、围栏策略、合规策略等',
  `sub_category` int(11) DEFAULT NULL COMMENT '策略小的分类，WiFi配置策略、VPN配置策略等',
  `create_by` varchar(200) DEFAULT NULL COMMENT '创建人姓名',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_by` varchar(200) DEFAULT NULL COMMENT '更新人姓名',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `status` int(11) DEFAULT '0' COMMENT '策略状态,-1--删除;0--禁用;1--启用',
  `content` longtext COMMENT '扩展存放json',
  `deleted` tinyint(2)  default '0' comment '逻辑删除标志',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `t_mdm_policy_idx_1` (`status`,`deleted`,`tenantId`,`sub_category`,`update_time`,`category`,`create_time`),
  KEY `t_mdm_policy_idx_2` (`deleted`,`status`,`tenantId`,`sub_category`,`update_time`,`create_time`,`category`)

) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;


--
-- 策略下方-用户关联表
--

DROP TABLE IF EXISTS `t_policy_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_policy_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `policyid` varchar(100) NOT NULL COMMENT '策略id',
  `userid` bigint(20) NOT NULL COMMENT '用户id',
  `customflag` int(11) DEFAULT '0' COMMENT '自定义标识: 0-继承 ;1-自定义',
  `sub_category` int(11) DEFAULT '0' COMMENT '策略子类型',
  `exceptionflag` int(11) DEFAULT '0' COMMENT '例外标识: 0-添加 ;1-排除',
  `pushid` bigint(20) DEFAULT NULL,
  `deleted` tinyint(2)  default '0' comment '逻辑删除标志',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `t_policy_user_idx_1` (`userid`,`tenantId`,`policyid`,`deleted`,`sub_category`,`pushid`),
  KEY `t_policy_user_idx_2` (`policyid`,`tenantId`,`userid`,`deleted`,`sub_category`,`pushid`),
  KEY `t_policy_user_idx_3` (`sub_category`,`tenantId`,`userid`,`deleted`,`policyid`,`pushid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;


--
-- 策略下方-设备关联表
--

DROP TABLE IF EXISTS `t_policy_device`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_policy_device` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `policyId` varchar(100) NOT NULL COMMENT '策略id',
  `deviceId` bigint(20) NOT NULL COMMENT '设备id',
  `customflag` int(11) DEFAULT '0' COMMENT '自定义标识: 0-继承 ;1-自定义',
  `exceptionflag` int(11) DEFAULT '0' COMMENT '例外标识: 0-添加 ;1-排除',
  `pushId` bigint(20) DEFAULT NULL,
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `t_policy_device_ibfk_1` (`policyId`),
  KEY `t_policy_device_ibfk_2` (`deviceId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='策略按设备下发关联表';
/*!40101 SET character_set_client = @saved_cs_client */;


--
-- 策略下方-组关系表
--

DROP TABLE IF EXISTS `t_policy_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_policy_group` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `policyid` varchar(100) NOT NULL COMMENT '策略id',
  `groupid` bigint(20) NOT NULL,
  `customflag` int(11) NOT NULL DEFAULT '0' COMMENT '自定义标识: 0-继承 ;1-自定义',
  `pushid` bigint(20) DEFAULT NULL,
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `policyid` (`policyid`),
  KEY `groupid` (`groupid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;


--
-- 设备-策略关联表（设备实际安装使用）
--

DROP TABLE IF EXISTS `t_device_profile`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_device_profile` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `deviceid` bigint(20) NOT NULL,
  `udid` char(64) DEFAULT NULL,
  `policyid` varchar(100) NOT NULL COMMENT '配置文件uuid',
  `createtime` datetime DEFAULT NULL COMMENT '下发时间',
  `updatetime` datetime DEFAULT NULL COMMENT '更新时间',
  `status` int(11) DEFAULT NULL COMMENT '执行状态,0--已分发;1--已安装;21--安装失败',
  `fencestatus` int(11) DEFAULT '0' COMMENT '围栏状态：0、围栏外；1、围栏内',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `t_device_profile_idx_1` (`deviceId`,`tenantId`,`status`,`policyId`),
  KEY `t_device_profile_idx_2` (`udid`,`tenantId`,`deviceId`,`status`,`policyId`),
  KEY `t_device_profile_idx_3` (`policyId`,`tenantId`,`status`,`deviceId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备与配置文件策略关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- 违规策略表
--

DROP TABLE IF EXISTS `t_violations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_violations` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `violationname` varchar(200) NOT NULL COMMENT '违规名称',
  `condid` int(11) NOT NULL COMMENT '违规 条件字典表id',
  `violationtype` int(11) NOT NULL COMMENT '违规类型.1--系统类型;2--配置策略类;3--应用类型',
  `condvalue` varchar(200) DEFAULT NULL COMMENT '违规 条件参数值',
  `platformtypes` varchar(30) NOT NULL DEFAULT '1,2,3,4' COMMENT '适用平台 1--android;2--ios;3--windows 8;4--windows phone8,多个平台以逗号分隔',
  `relationship` varchar(50) DEFAULT '1,2,3' COMMENT '设备所属关系  1--公司设备;2--员工设备;3--其他,多种关系使用逗号分隔',
  `useralarmways` varchar(50) DEFAULT NULL COMMENT '违规用户告警通知方式  1--邮件;2--push消息;3--短信,多种通知方式使用逗号分隔',
  `alarmId` varchar(500) DEFAULT NULL COMMENT '管理员告警id 多个管理员告警使用逗号分隔',
  `processid` int(11) NOT NULL COMMENT '违规 处理字典表id',
  `graceperiod` int(11) DEFAULT NULL COMMENT '违规处理宽限时间(分钟)',
  `status` int(11) DEFAULT '0' COMMENT '违规策略状态,0--禁用;1--启用',
  `createby` varchar(50) NOT NULL COMMENT '管理者id',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `updatetime` datetime DEFAULT NULL COMMENT '变更时间',
  `lockpass` varchar(20) DEFAULT NULL COMMENT '锁屏密码',
  `gentype` int(11) DEFAULT NULL COMMENT '生成方式 0-自动 1-手动',
  `apppkglist` varchar(2000) DEFAULT NULL COMMENT '沙箱应用包名json',
  `oldapppkglist` varchar(2000) DEFAULT NULL COMMENT '保存更新前的沙箱应用包名json',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='违规策略表';
/*!40101 SET character_set_client = @saved_cs_client */;


--
-- 违规 条件字典表
--

DROP TABLE IF EXISTS `t_violation_cond_dict`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_violation_cond_dict` (
  `condid` int(11) NOT NULL COMMENT 'id',
  `violationtype` int(11) NOT NULL COMMENT '违规类型.1--系统类型;2--配置策略类;3--应用类型',
  `title` varchar(200) NOT NULL COMMENT '标题.',
  PRIMARY KEY (`condid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='违规条件字典表';
/*!40101 SET character_set_client = @saved_cs_client */;


--
-- 违规处理字典表
--

DROP TABLE IF EXISTS `t_violate_process_dict`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_violate_process_dict` (
  `processid` int(11) NOT NULL COMMENT 'id',
  `title` varchar(200) NOT NULL COMMENT '标题',
  PRIMARY KEY (`processid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='违规处理字典表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- 违规策略分发用户组表
--

DROP TABLE IF EXISTS `t_violations_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_violations_group` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `violationid` bigint(20) NOT NULL COMMENT '违规 id',
  `groupid` bigint(20) NOT NULL COMMENT '用户组id',
  `relationships` varchar(500) NOT NULL COMMENT '设备所属关系 1 企业 2 员工 3 其他 ',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `groupid` (`groupid`),
  KEY `violationid` (`violationid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='违规策略分发用户组表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- 违规策略分发用户表
--

DROP TABLE IF EXISTS `t_violations_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_violations_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `violationid` bigint(20) NOT NULL COMMENT '违规 id',
  `userid` bigint(20) NOT NULL COMMENT '用户id',
  `relationships` varchar(500) NOT NULL COMMENT '设备所属关系 1 企业 2 员工 3 其他 ',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `userid` (`userid`),
  KEY `violationid` (`violationid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='违规策略分发用户表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- 违规策略分发设备表
--

DROP TABLE IF EXISTS `t_violations_device`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_violations_device` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `violationid` bigint(20) NOT NULL COMMENT '违规 id',
  `deviceid` bigint(20) NOT NULL COMMENT '设备id',
  `relationships` varchar(500) NOT NULL COMMENT '设备所属关系 1 企业 2 员工 3 其他 ',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `deviceid` (`deviceid`),
  KEY `violationid` (`violationid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='违规策略分发设备表';


--
-- 违规设备表
--

DROP TABLE IF EXISTS `t_violation_device`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_violation_device` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `violationid` bigint(20) NOT NULL COMMENT '违规id',
  `deviceid` bigint(20) NOT NULL COMMENT '设备id',
  `condid` int(11) NOT NULL COMMENT '违规 条件字典表id',
  `violationtype` int(11) NOT NULL COMMENT '违规类型.1--系统类型;2--配置策略类;3--应用类型',
  `condtitle` varchar(200) NOT NULL COMMENT '违规 条件标题',
  `appstrategyid` bigint(20) DEFAULT NULL COMMENT '应用策略id.黑名单id;白名单id',
  `policyid` varchar(100) DEFAULT NULL COMMENT '配置策略id',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `updatetime` datetime DEFAULT NULL COMMENT '创建时间',
  `adminalarmids` varchar(500) DEFAULT NULL COMMENT '管理员告警id多个管理员告警使用逗号分隔',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `violationid` (`violationid`),
  KEY `deviceid` (`deviceid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='违规设备表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- 违规设备应用违规明细表
--

DROP TABLE IF EXISTS `t_violation_device_app`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_violation_device_app` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `violationid` bigint(20) NOT NULL COMMENT '违规id',
  `deviceid` bigint(20) NOT NULL COMMENT '设备id',
  `appstrategyid` bigint(20) DEFAULT NULL COMMENT '应用策略id.黑名单id;白名单id',
  `packagename` varchar(200) NOT NULL COMMENT '应用包名',
  `appversion` varchar(50) DEFAULT NULL COMMENT '应用版本',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='违规设备应用违规明细表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- 违规设备处理结果表
--

DROP TABLE IF EXISTS `t_violation_device_proc`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_violation_device_proc` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `uuid` varchar(50) NOT NULL COMMENT 'uuid',
  `violationid` bigint(20) NOT NULL COMMENT '违规id',
  `deviceid` bigint(20) NOT NULL COMMENT '设备id',
  `procstrategyid` bigint(20) DEFAULT NULL COMMENT '违规 处理策略id',
  `processid` int(11) NOT NULL COMMENT '违规 处理字典表id',
  `processtime` datetime DEFAULT NULL COMMENT '处理时间',
  `processstatus` int(11) DEFAULT NULL COMMENT '处理状态.0--未处理;1---成功;-1--处理失败',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `updatetime` datetime DEFAULT NULL COMMENT '修改时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `deviceid` (`deviceid`),
  KEY `t_violation_device_proc_ibfk_1` (`violationid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='违规设备处理结果表';
/*!40101 SET character_set_client = @saved_cs_client */;


-- 推送日志记录信息
DROP TABLE IF EXISTS `t_push_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_push_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `pushMode` int(11) NOT NULL COMMENT '推送模式0应用，1设备',
  `pushType` varchar(40) NOT NULL COMMENT '推送类型,govdd政务钉钉推送，npns(NPNS服务推送),ios(苹果推送)',
  `sender` varchar(100) COMMENT '发送方（推送方)',
  `payload` text  COMMENT '推送对应的payload,是指业务中对应的payload',
  `pushPayload` text  COMMENT '推送至客户端对应的payload,一般ios中会跟payload不一致',
  `isBatch` int(11) NOT NULL COMMENT '0单个推送，1批量推送',
  `pushTime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '推送时间',
  `result` varchar(100) COMMENT '推送结果',
  `errorMsg` varchar(2000) COMMENT '推送结果消息，error msg',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `t_push_log_idx_1` (`pushTime`,`pushMode`,`pushType`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='记录的是推送记录信息';



-- 推送日志记录接收者信息
DROP TABLE IF EXISTS `t_push_log_receiver`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_push_log_receiver` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `logId` int(11) NOT NULL COMMENT '推送记录ID',
  `userId` bigint(20) NOT NULL COMMENT '推送的目标用户ID',
  `loginId` varchar(40) COMMENT '推送的目标用户loginId',
  `udid` varchar(64) COMMENT '推送的目标用户设备umid',
  `dudid` varchar(64) COMMENT '推送的目标用户设备udid',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `t_push_log_receiver_idx_1` (`logId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='推送日志记录接收者信息';


-- 记录的是推送失败记录信息
DROP TABLE IF EXISTS `t_push_failure_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_push_failure_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `pushMode` int(11) NOT NULL COMMENT '推送模式0应用，1设备',
  `pushType` varchar(40) COMMENT '推送类型,govdd政务钉钉推送，npns(NPNS服务推送),ios(苹果推送)',
  `sender` varchar(100) COMMENT '发送方（推送方)',
  `payload` text  COMMENT '推送对应的payload,是指业务中对应的payload',
  `pushPayload` text  COMMENT '推送至客户端对应的payload,一般ios中会跟payload不一致',
  `isBatch` int(11) NOT NULL COMMENT '0单个推送，1批量推送',
  `receivers` text COMMENT '接收方，以json方式存储,是指业务中对应的receivers',
  `pushReceivers` text COMMENT '推送时，具体的接收方，以json方式存储',
  `pushTime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '推送时间',
  `result` varchar(100) COMMENT '推送结果',
  `errorMsg` varchar(2000) COMMENT '推送结果消息，error msg',
  `failureTimes` int(11) COMMENT '失败次数',
  `nextPushTime` datetime COMMENT '下一推送时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `t_push_failure_record_idx_1` (`nextPushTime`,`pushMode`,`pushType`,`pushTime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='记录的是推送失败记录信息';


-- 推送token信息表
DROP TABLE IF EXISTS `t_push_token`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_push_token` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `userid` bigint(20) DEFAULT NULL COMMENT '所属用户用户id',
  `deviceId` bigint(20) DEFAULT NULL COMMENT '设备ID',
  `udid` char(64) DEFAULT NULL COMMENT 'unique device identifier',
  `dudid` varchar(64) comment '设备的系统UDID值,设备MDM激活后得到的设备唯一ID,原来的UDID为应用生成的UMID',
  `activateMdmTime` datetime comment '设备的MDM激活时间',
  `type` int(11) NOT NULL DEFAULT '1' COMMENT '平台1--android;2--ios',
  `devicetype` int(11) NOT NULL DEFAULT '10' COMMENT '设备类型 10-android phone 11-android pad;20-iphone 21-ipad 22-ipadmini 23- ipodtouch',
  `activatetime` datetime DEFAULT NULL COMMENT '激活时间',
  `token` varchar(200) DEFAULT NULL COMMENT 'ios mdm发送唤醒使用',
  `pushmagic` varchar(200) DEFAULT NULL COMMENT 'ios mdm发送唤醒使用',
  `unlocktoken` varchar(4000) DEFAULT NULL COMMENT 'ios mdm解除锁屏使用',
  `userName` varchar(150) DEFAULT NULL COMMENT '用户名',
  `loginId` varchar(50) DEFAULT NULL COMMENT '用户登录名',
  `status` int(11) NOT NULL DEFAULT '1' COMMENT '1有效，0无效',
  `expireTime` datetime comment '设备的MDM激活时间',
  `nextCheckTime` datetime comment '设备的MDM下一检查时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `t_push_token_idx_1` (`userid`,`tenantId`,`type`,`deviceType`,`udid`,`loginId`,`userName`),
  KEY `t_push_token_idx_2` (`type`,`tenantId`,`devicetype`,`userid`,`udid`,`loginId`),
  KEY `t_push_token_idx_3` (`udid`,`tenantId`,`loginId`,`userid`,`devicetype`,`type`),
  KEY `t_push_token_idx_4` (`loginId`,`tenantId`,`type`,`devicetype`,`udid`,`userid`,`userName`),
  KEY `t_push_token_idx_5` (`dudid`,`tenantId`,`userId`,`loginId`,`type`,`deviceType`),
  KEY `t_push_token_idx_6` (`status`,`nextCheckTime`,`tenantId`)

) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='推送token信息表';
/*!40101 SET character_set_client = @saved_cs_client */;



-- NPNS客户端应用级推送token(NPNS推送Token）信息表
DROP TABLE IF EXISTS `t_apns_token`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_apns_token` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `userid` bigint(20) DEFAULT NULL COMMENT '所属用户用户id',
  `deviceId` bigint(20) DEFAULT NULL COMMENT '设备ID',
  `udid` char(64) DEFAULT NULL COMMENT 'unique device identifier',
  `type` int(11) NOT NULL DEFAULT '1' COMMENT '平台1--android;2--ios',
  `devicetype` int(11) NOT NULL DEFAULT '10' COMMENT '设备类型 10-android phone 11-android pad;20-iphone 21-ipad 22-ipadmini 23- ipodtouch',
  `activatetime` datetime DEFAULT NULL COMMENT '激活时间',
  `token` varchar(200) DEFAULT NULL COMMENT 'npns对应的token值',
  `tokenType` int DEFAULT NULL COMMENT 'token类型，暂时支持0:mdm,1:mcm',
  `pkgName` varchar(200) DEFAULT NULL COMMENT 'token对应的应用包名',
  `userName` varchar(150) DEFAULT NULL COMMENT '用户名',
  `loginId` varchar(50) DEFAULT NULL COMMENT '用户登录名',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `t_npns_token_idx_1` (`userid`,`tenantId`,`tokenType`,`type`,`deviceType`,`udid`,`loginId`),
  KEY `t_npns_token_idx_2` (`type`,`tenantId`,`devicetype`,`tokenType`,`userid`,`udid`,`loginId`),
  KEY `t_npns_token_idx_3` (`udid`,`tenantId`,`loginId`,`tokenType`,`userid`,`devicetype`,`type`),
  KEY `t_push_token_idx_4` (`loginId`,`tenantId`,`udid`,`tokenType`,`type`,`devicetype`,`userid`)

) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='NPNS客户端应用级推送token(NPNS推送Token）信息表';
/*!40101 SET character_set_client = @saved_cs_client */;




-- 推送证书信息
DROP TABLE IF EXISTS `t_push_credential`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_push_credential` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `pushType` varchar(40) NOT NULL COMMENT '推送类型,govdd政务钉钉推送，npns(NPNS服务推送),ios(苹果推送)',
  `credential` text  COMMENT '证书内容对应Base64字符串',
  `config` varchar(2000)  COMMENT '证书附加的配置信息，如密码、topic等',
  PRIMARY KEY (`id`),
  KEY `t_push_credential_idx_1` (`pushType`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='记录的是推送记录信息';



-- scep生成的客户端证书表
DROP TABLE IF EXISTS `t_scep_certificate`;
CREATE TABLE `t_scep_certificate` (
     `id` bigint(20) NOT NULL,
     `createTime` datetime comment '证书生成时间',
     `expireTime` datetime comment '证书过期时间',
     `revoked` int(1)  COMMENT '证书是否被revoke',
     `serialNumber` varchar(64)  COMMENT '证书序列号',
     `issuer` varchar(64)  COMMENT '证书的签发者CN',
     `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
     PRIMARY KEY (`id`),
     KEY `t_scep_certificate_idx_1` (`serialNumber`,`issuer`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='scep生成的客户端证书表';

DROP TABLE IF EXISTS `t_certificate`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_certificate` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '证书名称',
  `type` varchar(20) NOT NULL COMMENT '证书类型',
  `filename` varchar(200) NOT NULL COMMENT '证书文件名称',
  `content` text COMMENT '证书实体内容',
  `config` varchar(2000)  COMMENT '证书附加的配置信息，如密码、topic等',
  `createTime` datetime NOT NULL COMMENT '导入时间',
  `updateTime` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='证书信息管理表';



DROP TABLE IF EXISTS `t_sys_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_sys_config` (
                                `id` varchar(64) NOT NULL,
                                `prop_key` varchar(256) DEFAULT NULL,
                                `prop_value` varchar(2048) DEFAULT NULL,
                                `note` varchar(1024) DEFAULT NULL,
                                `config_file_name` varchar(128) DEFAULT NULL,
                                `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
                                PRIMARY KEY (`id`,`tenantId`) USING BTREE,
                                KEY `t_sys_config_idx_1` (`prop_key`(255),`config_file_name`,`tenantId`),
                                KEY `t_sys_config_idx_2` (`config_file_name`,`tenantId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_sys_dictionary`
--

DROP TABLE IF EXISTS `t_sys_dictionary`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_sys_dictionary` (
                                    `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                    `dictname` varchar(50) NOT NULL,
                                    `description` varchar(255) NOT NULL,
                                    `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
                                    PRIMARY KEY (`id`,`tenantId`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1000 DEFAULT CHARSET=utf8 COMMENT='系统字典表,系统版本、运营商、手机品牌 ';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_sys_dictionary_value`
--

DROP TABLE IF EXISTS `t_sys_dictionary_value`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_sys_dictionary_value` (
                                          `id` bigint(20) NOT NULL AUTO_INCREMENT,
                                          `dictkey` varchar(50) NOT NULL,
                                          `dictvalue` varchar(50) NOT NULL,
                                          `status` int(11) NOT NULL COMMENT '启用禁用  1:启用 0:禁用',
                                          `dicttype` bigint(20) NOT NULL COMMENT '关联t_sys_dictionary',
                                          `fromtype` int(11) NOT NULL COMMENT '数据来源 1:预置数据 2:管理员添加 ',
                                          `creator` varchar(50) DEFAULT NULL,
                                          `createtime` datetime DEFAULT NULL,
                                          `updater` varchar(50) DEFAULT NULL,
                                          `updatetime` datetime DEFAULT NULL,
                                          `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
                                          PRIMARY KEY (`id`),
                                          KEY `t_sys_dictionary_value_ibfk_1` (`dicttype`)
) ENGINE=InnoDB AUTO_INCREMENT=349 DEFAULT CHARSET=utf8 COMMENT='字典值表';
/*!40101 SET character_set_client = @saved_cs_client */;


DROP TABLE IF EXISTS `t_sys_tenant`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_sys_tenant` (
                                `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
                                `enName` varchar(256) DEFAULT NULL COMMENT '租户英文标识',
                                `zhName` varchar(64) DEFAULT NULL COMMENT '租户名称',
                                `address` varchar(256) DEFAULT NULL COMMENT '租户地址',
                                `contacts` varchar(64) DEFAULT NULL COMMENT '联系人',
                                `phone` varchar(32) DEFAULT NULL COMMENT '租户电话',
                                `email` varchar(64) DEFAULT NULL COMMENT '租户email',
                                `createTime` datetime DEFAULT NULL COMMENT '创建时间',
                                `updateTime` datetime DEFAULT NULL COMMENT '更新时间',
                                `status` int(11) DEFAULT NULL COMMENT '租户状态',
                                `expiredTime` datetime DEFAULT NULL COMMENT '租户过期时间',
                                `tenantType` int(11) DEFAULT NULL COMMENT '客户类型。0-正式 1-试用',
                                PRIMARY KEY (`tenantId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='租户信息表';
/*!40101 SET character_set_client = @saved_cs_client */;


--
-- Table structure for table `t_sys_user_file`
--

DROP TABLE IF EXISTS `t_sys_user_file`;
CREATE TABLE `t_sys_user_file` (
    `id` bigint(20) NOT NULL,
    `deviceId` bigint(20) DEFAULT NULL COMMENT '设备ID',
    `userId` bigint(20) NOT NULL COMMENT '用户ID',
    `businessType` tinyint(3) NOT NULL COMMENT '业务类型，1：截屏日志',
    `filePath` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '文件路径',
    `fileName` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '文件名',
    `fileExt` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '文件扩展名',
    `dfsType` tinyint(3) NOT NULL COMMENT '文件系统类型，1：本地，2：OSS',
    `createTime` datetime DEFAULT NULL COMMENT '创建时间',
    `updateTime` datetime DEFAULT NULL COMMENT '更新时间',
    `tenantId` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '租户ID',
    `packageName` varchar(64) COLLATE utf8_bin DEFAULT NULL COMMENT '应用包名',
    `deviceName` varchar(64) COLLATE utf8_bin DEFAULT NULL COMMENT '设备名称',
    `userName` varchar(64) COLLATE utf8_bin DEFAULT NULL COMMENT '用户名称',
    `loginId` varchar(32) COLLATE utf8_bin DEFAULT NULL COMMENT '用户登录ID',
    `occTime` datetime DEFAULT NULL COMMENT '发生时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin ROW_FORMAT=DYNAMIC COMMENT='用户文件表'


-- ---------------------------
-- 定义一些常用的存储过程
-- ---------------------------

-- ---------------------------------------------------------------------------------------------
-- 增加列
-- 如：CALL AddColumn('table_name', 'column_name', 'varchar(255)', '','NOT NULL', '应用主页地址');
-- ----------------------------------------------------------------------------------------------

DELIMITER $$
DROP PROCEDURE IF EXISTS `AddColumn` $$
CREATE PROCEDURE `AddColumn`
(
    given_table     VARCHAR(64),
    given_column    VARCHAR(64),
    given_type      VARCHAR(64),
    given_charset   VARCHAR(64),
    given_nullable  VARCHAR(64),
    given_comment   VARCHAR(64)
)
BEGIN
    DECLARE columnIsThere INTEGER;
    SELECT COUNT(1) INTO columnIsThere FROM information_schema.columns
	WHERE table_name = given_table
	AND COLUMN_NAME=given_column;

    IF columnIsThere = 0 THEN
	SET @alertStmt = 'ALTER TABLE ';
        SET @sqlstmt = CONCAT(@alertStmt, given_table,' ADD COLUMN ',
                              given_column,' ', given_type, ' ', given_charset, ' ', given_nullable,' COMMENT \'', given_comment, '\'');
        PREPARE st FROM @sqlstmt;
        EXECUTE st;
        DEALLOCATE PREPARE st;
    END IF;
END $$
DELIMITER ;


-- ---------------------------------------------------------------------------------------------
-- 删除列
-- 如：CALL DropColumn('table_name', 'column_name');
-- ----------------------------------------------------------------------------------------------

DELIMITER $$
DROP PROCEDURE IF EXISTS `DropColumn` $$
CREATE PROCEDURE `DropColumn`
(
    given_table     VARCHAR(64),
    given_column    VARCHAR(64)
)
BEGIN
    DECLARE columnIsThere INTEGER;
    SELECT COUNT(1) INTO columnIsThere FROM information_schema.columns
	WHERE table_name = given_table
	AND COLUMN_NAME=given_column;

    IF columnIsThere > 0 THEN
	SET @alertStmt = 'ALTER TABLE ';
        SET @sqlstmt = CONCAT(@alertStmt, given_table,' DROP COLUMN ', given_column);
        PREPARE st FROM @sqlstmt;
        EXECUTE st;
        DEALLOCATE PREPARE st;
    END IF;
END $$
DELIMITER ;


-- ---------------------------------------------------------------------------------------------
-- 创建索引
-- 如：CALL CreateIndex(1, 'table_name', 'index_name', 'column_name');
-- ----------------------------------------------------------------------------------------------

DELIMITER $$
DROP PROCEDURE IF EXISTS `CreateIndex` $$
CREATE PROCEDURE `CreateIndex`
(
    is_unique INTEGER,
    given_table    VARCHAR(64),
    given_index    VARCHAR(64),
    given_columns  VARCHAR(64)
)
BEGIN
    DECLARE IndexIsThere INTEGER;

    SELECT COUNT(1) INTO IndexIsThere
    FROM INFORMATION_SCHEMA.STATISTICS
    WHERE table_name   = given_table
      AND   index_name   = given_index;

    IF IndexIsThere = 0 THEN
        SET @createStmt = 'CREATE INDEX ';
        IF is_unique = 1 THEN
            set @createStmt = 'CREATE UNIQUE INDEX ';
        END IF;
        SET @sqlstmt = CONCAT(@createStmt,given_index,' ON ',
                              given_table,' (',given_columns,')', ' USING BTREE');
        PREPARE st FROM @sqlstmt;
        EXECUTE st;
        DEALLOCATE PREPARE st;
    ELSE
        SELECT CONCAT('Index ',given_index,' already exists on Table ',
                      given_table) CreateindexErrorMessage;
    END IF;
END $$
DELIMITER ;

-- ---------------------------------------------------------------------------------------------
-- 删除索引
-- 如：CALL DropIndex('table_name', 'index_name');
-- ----------------------------------------------------------------------------------------------

DELIMITER $$
DROP PROCEDURE IF EXISTS `DropIndex` $$
CREATE PROCEDURE `DropIndex`
(
    given_table     VARCHAR(64),
    given_index    VARCHAR(64)
)
BEGIN
    DECLARE IndexIsThere INTEGER;
    SELECT COUNT(1) INTO IndexIsThere
    FROM INFORMATION_SCHEMA.STATISTICS
    WHERE table_name   = given_table
      AND   index_name   = given_index;

    IF IndexIsThere > 0 THEN
	SET @alertStmt = 'ALTER TABLE ';
        SET @sqlstmt = CONCAT(@alertStmt, given_table,' DROP INDEX ', given_index);
        PREPARE st FROM @sqlstmt;
        EXECUTE st;
        DEALLOCATE PREPARE st;
    END IF;
END $$
DELIMITER ;

-- ----------------------------
-- 全局级别初始化数据
-- ----------------------------
INSERT INTO t_violation_cond_dict (`condid`, `violationtype`, `title`) VALUES (101, 1, 'violation.cond.dict.sys.os.version.low');
INSERT INTO t_violation_cond_dict (`condid`, `violationtype`, `title`) VALUES (102, 1, 'violation.cond.dict.sys.jailbreak.root');
INSERT INTO t_violation_cond_dict (`condid`, `violationtype`, `title`) VALUES (103, 1, 'violation.cond.dict.sys.data.no.cipher');
INSERT INTO t_violation_cond_dict (`condid`, `violationtype`, `title`) VALUES (104, 1, 'violation.cond.dict.sys.sim.modify');
INSERT INTO t_violation_cond_dict (`condid`, `violationtype`, `title`) VALUES (105, 1, 'violation.cond.dict.sys.sd.modify');
INSERT INTO t_violation_cond_dict (`condid`, `violationtype`, `title`) VALUES (106, 1, 'violation.cond.dict.sys.strategy.not.update');
INSERT INTO t_violation_cond_dict (`condid`, `violationtype`, `title`) VALUES (107, 1, 'violation.cond.dict.sys.not.flowStatistics');
INSERT INTO t_violation_cond_dict (`condid`, `violationtype`, `title`) VALUES (108, 1, 'violation.cond.dict.sys.flow.threshold');
INSERT INTO t_violation_cond_dict (`condid`, `violationtype`, `title`) VALUES (109, 1, 'violation.cond.dict.sys.completeness');
INSERT INTO t_violation_cond_dict (`condid`, `violationtype`, `title`) VALUES (201, 2, 'violation.cond.dict.conf.not.effect');
INSERT INTO t_violation_cond_dict (`condid`, `violationtype`, `title`) VALUES (301, 3, 'violation.cond.dict.app.blacklist');
INSERT INTO t_violation_cond_dict (`condid`, `violationtype`, `title`) VALUES (302, 3, 'violation.cond.dict.app.whitelist');
INSERT INTO t_violation_cond_dict (`condid`, `violationtype`, `title`) VALUES (303, 3, 'violation.cond.dict.app.force');
INSERT INTO t_violation_cond_dict (`condid`, `violationtype`, `title`) VALUES (401, 1, 'violation.cond.dict.sys.device.offline');

-- ----------------------------
-- Records of t_violate_process_dict-全局
-- ----------------------------
INSERT INTO t_violate_process_dict (`processid`,`title`) VALUES (1, 'violation.process.dict.lock.device');
INSERT INTO t_violate_process_dict (`processid`,`title`) VALUES (2, 'violation.process.dict.company.wipe');
INSERT INTO t_violate_process_dict (`processid`,`title`) VALUES (3, 'violation.process.dict.all.wipe');
INSERT INTO t_violate_process_dict (`processid`,`title`) VALUES (8, 'violation.process.dict.forbid.appstore');
INSERT INTO t_violate_process_dict (`processid`,`title`) VALUES (9, 'violation.process.dict.forbid.mcm');
INSERT INTO t_violate_process_dict (`processid`,`title`) VALUES (10, 'violation.process.dict.forbid.sd');
INSERT INTO t_violate_process_dict (`processid`,`title`) VALUES (11, 'violation.process.dict.forbid.camera');
INSERT INTO t_violate_process_dict (`processid`,`title`) VALUES (12, 'violation.process.dict.forbid.mdm');
INSERT INTO t_violate_process_dict (`processid`,`title`) VALUES (13, 'violation.process.dict.forbid.sag');
INSERT INTO t_violate_process_dict (`processid`,`title`) VALUES (14, 'violation.process.dict.forbid.mobile.data');
INSERT INTO t_violate_process_dict (`processid`,`title`) VALUES (15, 'violation.process.dict.lock.screen');
INSERT INTO t_violate_process_dict (`processid`,`title`) VALUES (16, 'violation.process.dict.close.device');
INSERT INTO t_violate_process_dict (`processid`,`title`) VALUES (17, 'violation.process.dict.forbid.wrappingApp');
INSERT INTO t_violate_process_dict (`processid`,`title`) VALUES (-1, 'violation.process.dict.noOper');

-- ----------------------------
-- Records of t_certificate-全局
-- ----------------------------
insert into t_certificate (`config`, `content`, `updateTime`, `filename`, `type`, `createTime`, `name`) values ( null, '-----BEGIN CERTIFICATE-----\nMIIDnTCCAoWgAwIBAgIJAOFv8yGRi7zUMA0GCSqGSIb3DQEBCwUAMD0xCzAJBgNV\nBAoMAk5RMQwwCgYDVQQLDANEZXYxCzAJBgNVBAYTAkNOMRMwEQYDVQQDDApJbnRl\ncm5hbENBMB4XDTE5MTAxNzEwNDEzMVoXDTI5MTAxNjEwNDEzMVowPTELMAkGA1UE\nCgwCTlExDDAKBgNVBAsMA0RldjELMAkGA1UEBhMCQ04xEzARBgNVBAMMCkludGVy\nbmFsQ0EwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQDN6VFQPrgXNVlE\nlQRgPNH5uYiBtc6Z3TCn4VOyxGRbnkdj6d+0CCVyNp9g30EzT+aG4B5UlwB4fWg+\nwJ12Ix1j7fYjf4g64o4+8q0tgc0lxn4qgCemnFPk7zUa0IbIVCOSyxj6qgYR9wh1\n+sgSULWLv7hjp9x7h6ppTs6oX2UxBres6BQdenxm+h1BY6FSw0QTUeUnCDV2uIYk\n3vPEIBFQ057jJICEkdOC2hJRpbdKkDqN6EwwruCgPBZoZUscowFjONPDV79YgO6N\nvEYnSgCxyE35JthZnCp5g6d3Y+9spSEYdlglw1ZNNX1AHpf+xOSX3jJZcvRPkVyN\nsy3yPEFVAgMBAAGjgZ8wgZwwHQYDVR0OBBYEFFEudTI+cQcycDnWKVTK9XSwX1Wq\nMG0GA1UdIwRmMGSAFFEudTI+cQcycDnWKVTK9XSwX1WqoUGkPzA9MQswCQYDVQQK\nDAJOUTEMMAoGA1UECwwDRGV2MQswCQYDVQQGEwJDTjETMBEGA1UEAwwKSW50ZXJu\nYWxDQYIJAOFv8yGRi7zUMAwGA1UdEwQFMAMBAf8wDQYJKoZIhvcNAQELBQADggEB\nAC7wUQNMp5L/KbU1JIlveqCkwjtzG8+jv1EkNg+OcmMHjbA9nrMG+viBZJ3krYGr\ndNHp8ADtuxZkc4tAhvY/G/QjlDV4Y71Zh8+faCKaNWBg2JEEKUbCXJuZxq3dcUmd\nbfjbjiFw9guxKFhUwQiMuwj9EXFzy2IagSKvGghi+kYrVyump2CMA2C8N7u4BaZo\nPKQEmxUxWj8Cia549+rrcprurL/FogxfJji3qW4zfCt8LrDnW+n3O/4xAKfk1k3M\nvgqDtHlbujuiHZTJeA3LeWXH0XVbHxBXrJuLYJgUee2s0TkUxtRk1bdEK0brVft7\nahH1NrQLF2DD+d842v9sbR4=\n-----END CERTIFICATE-----', '2020-02-10 15:53:36', 'SCEPRootCA.pem', 'SERVER_CERT', '2020-02-10 15:53:34', '服务器证书');
insert into t_certificate (`config`, `content`, `updateTime`, `filename`, `type`, `createTime`, `name`) values ( '{\"password\":\"Nsky@0!9\"}', 'MIIJkQIBAzCCCVcGCSqGSIb3DQEHAaCCCUgEgglEMIIJQDCCA/cGCSqGSIb3DQEHBqCCA+gwggPk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\nCAA=', '2020-02-07 14:39:04', 'mdmclient.cert', 'CLIENT_CERT', '2020-02-07 14:39:04', '客户端证书');
insert into t_certificate (`config`, `content`, `updateTime`, `filename`, `type`, `createTime`, `name`) values ( null, '-----BEGIN CERTIFICATE-----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\nCrrCyPrYxmD/iU99y5nT\n-----END CERTIFICATE-----', '2020-02-10 15:53:36', 'digitalsee.cn.cert', 'SIGNER_CERT', '2020-02-10 15:53:34', '签名者证书');
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
insert into t_certificate (`config`, `content`, `updateTime`, `filename`, `type`, `createTime`, `name`) values ( null, '-----BEGIN CERTIFICATE-----\nMIIEizCCA3OgAwIBAgIQBUb+GCP34ZQdo5/OFMRhczANBgkqhkiG9w0BAQsFADBh\nMQswCQYDVQQGEwJVUzEVMBMGA1UEChMMRGlnaUNlcnQgSW5jMRkwFwYDVQQLExB3\nd3cuZGlnaWNlcnQuY29tMSAwHgYDVQQDExdEaWdpQ2VydCBHbG9iYWwgUm9vdCBD\nQTAeFw0xNzExMDYxMjIzNDVaFw0yNzExMDYxMjIzNDVaMF4xCzAJBgNVBAYTAlVT\nMRUwEwYDVQQKEwxEaWdpQ2VydCBJbmMxGTAXBgNVBAsTEHd3dy5kaWdpY2VydC5j\nb20xHTAbBgNVBAMTFEdlb1RydXN0IFJTQSBDQSAyMDE4MIIBIjANBgkqhkiG9w0B\nAQEFAAOCAQ8AMIIBCgKCAQEAv4rRY03hGOqHXegWPI9/tr6HFzekDPgxP59FVEAh\n150Hm8oDI0q9m+2FAmM/n4W57Cjv8oYi2/hNVEHFtEJ/zzMXAQ6CkFLTxzSkwaEB\n2jKgQK0fWeQz/KDDlqxobNPomXOMJhB3y7c/OTLo0lko7geG4gk7hfiqafapa59Y\nrXLIW4dmrgjgdPstU0Nigz2PhUwRl9we/FAwuIMIMl5cXMThdSBK66XWdS3cLX18\n4ND+fHWhTkAChJrZDVouoKzzNYoq6tZaWmyOLKv23v14RyZ5eqoi6qnmcRID0/i6\nU9J5nL1krPYbY7tNjzgC+PBXXcWqJVoMXcUw/iBTGWzpwwIDAQABo4IBQDCCATww\nHQYDVR0OBBYEFJBY/7CcdahRVHex7fKjQxY4nmzFMB8GA1UdIwQYMBaAFAPeUDVW\n0Uy7ZvCj4hsbw5eyPdFVMA4GA1UdDwEB/wQEAwIBhjAdBgNVHSUEFjAUBggrBgEF\nBQcDAQYIKwYBBQUHAwIwEgYDVR0TAQH/BAgwBgEB/wIBADA0BggrBgEFBQcBAQQo\nMCYwJAYIKwYBBQUHMAGGGGh0dHA6Ly9vY3NwLmRpZ2ljZXJ0LmNvbTBCBgNVHR8E\nOzA5MDegNaAzhjFodHRwOi8vY3JsMy5kaWdpY2VydC5jb20vRGlnaUNlcnRHbG9i\nYWxSb290Q0EuY3JsMD0GA1UdIAQ2MDQwMgYEVR0gADAqMCgGCCsGAQUFBwIBFhxo\ndHRwczovL3d3dy5kaWdpY2VydC5jb20vQ1BTMA0GCSqGSIb3DQEBCwUAA4IBAQAw\n8YdVPYQI/C5earp80s3VLOO+AtpdiXft9OlWwJLwKlUtRfccKj8QW/Pp4b7h6QAl\nufejwQMb455OjpIbCZVS+awY/R8pAYsXCnM09GcSVe4ivMswyoCZP/vPEn/LPRhH\nhdgUPk8MlD979RGoUWz7qGAwqJChi28uRds3thx+vRZZIbEyZ62No0tJPzsSGSz8\nnQ//jP8BIwrzBAUH5WcBAbmvgWfrKcuv+PyGPqRcc4T55TlzrBnzAzZ3oClo9fTv\nO9PuiHMKrC6V6mgi0s2sa/gbXlPCD9Z24XUMxJElwIVTDuKB0Q4YMMlnpN/QChJ4\nB0AFsQ+DU0NCO+f78Xf7\n-----END CERTIFICATE-----', '2020-02-10 16:08:59', 'SCEPRootCA.pem', 'SIGNER_ROOT_CA', '2020-02-10 16:08:57', '签名者根证书');
