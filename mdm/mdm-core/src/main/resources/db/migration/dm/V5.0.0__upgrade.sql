CREATE TABLE "uemx"."t_apns_token"
(
"id" BIGINT NOT NULL,
"userid" BIGINT,
"deviceId" BIGINT,
"udid" CHAR(256),
"type" INT DEFAULT 1 NOT NULL,
"devicetype" INT DEFAULT 10 NOT NULL,
"activatetime" TIMESTAMP(0),
"token" VARCHAR(800),
"tokenType" INT,
"pkgName" VARCHAR(800),
"userName" VARCHAR(600),
"loginId" VARCHAR(200),
"tenantId" VARCHAR(256) NOT NULL,
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT ON TABLE "uemx"."t_apns_token" IS 'NPNS客户端应用级推送token(NPNS推送Token）信息表';

COMMENT ON COLUMN "uemx"."t_apns_token"."activatetime" IS '激活时间';

COMMENT ON COLUMN "uemx"."t_apns_token"."deviceId" IS '设备ID';

COMMENT ON COLUMN "uemx"."t_apns_token"."devicetype" IS '设备类型 10-android phone 11-android pad;20-iphone 21-ipad 22-ipadmini 23- ipodtouch';

COMMENT ON COLUMN "uemx"."t_apns_token"."loginId" IS '用户登录名';

COMMENT ON COLUMN "uemx"."t_apns_token"."pkgName" IS 'token对应的应用包名';

COMMENT ON COLUMN "uemx"."t_apns_token"."tenantId" IS '租户ID';

COMMENT ON COLUMN "uemx"."t_apns_token"."token" IS 'npns对应的token值';

COMMENT ON COLUMN "uemx"."t_apns_token"."tokenType" IS 'token类型，暂时支持0:mdm,1:mcm';

COMMENT ON COLUMN "uemx"."t_apns_token"."type" IS '平台1--android;2--ios';

COMMENT ON COLUMN "uemx"."t_apns_token"."udid" IS 'unique device identifier';

COMMENT ON COLUMN "uemx"."t_apns_token"."userid" IS '所属用户用户id';

COMMENT ON COLUMN "uemx"."t_apns_token"."userName" IS '用户名';

CREATE OR REPLACE  INDEX "uemx"."t_npns_token_idx_1" ON "uemx"."t_apns_token"("userid" ASC,"tenantId" ASC,"tokenType" ASC,"type" ASC,"devicetype" ASC,"udid" ASC,"loginId" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE OR REPLACE  INDEX "uemx"."t_npns_token_idx_2" ON "uemx"."t_apns_token"("type" ASC,"tenantId" ASC,"devicetype" ASC,"tokenType" ASC,"userid" ASC,"udid" ASC,"loginId" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE OR REPLACE  INDEX "uemx"."t_push_token_idx_4" ON "uemx"."t_apns_token"("loginId" ASC,"tenantId" ASC,"udid" ASC,"tokenType" ASC,"type" ASC,"devicetype" ASC,"userid" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE OR REPLACE  INDEX "uemx"."t_npns_token_idx_3" ON "uemx"."t_apns_token"("udid" ASC,"tenantId" ASC,"loginId" ASC,"tokenType" ASC,"userid" ASC,"devicetype" ASC,"type" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE TABLE "uemx"."t_certificate"
(
"id" BIGINT NOT NULL,
"name" VARCHAR(400) NOT NULL,
"type" VARCHAR(80) NOT NULL,
"filename" VARCHAR(800) NOT NULL,
"content" TEXT,
"config" VARCHAR(8000),
"createTime" TIMESTAMP(0) NOT NULL,
"updateTime" TIMESTAMP(0) NOT NULL,
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT ON TABLE "uemx"."t_certificate" IS '证书信息管理表';

COMMENT ON COLUMN "uemx"."t_certificate"."config" IS '证书附加的配置信息，如密码、topic等';

COMMENT ON COLUMN "uemx"."t_certificate"."content" IS '证书实体内容';

COMMENT ON COLUMN "uemx"."t_certificate"."createTime" IS '导入时间';

COMMENT ON COLUMN "uemx"."t_certificate"."filename" IS '证书文件名称';

COMMENT ON COLUMN "uemx"."t_certificate"."name" IS '证书名称';

COMMENT ON COLUMN "uemx"."t_certificate"."type" IS '证书类型';

COMMENT ON COLUMN "uemx"."t_certificate"."updateTime" IS '更新时间';

CREATE TABLE "uemx"."t_client_integration"
(
"id" BIGINT NOT NULL,
"clientId" VARCHAR(256) NOT NULL,
"packageName" VARCHAR(256) NOT NULL,
"clientName" VARCHAR(400) NOT NULL,
"description" TEXT,
"logoUri" TEXT,
"status" TINYINT DEFAULT 0,
"clientNameEn" VARCHAR(400),
"appOs" TINYINT,
"clientIdIssuedAt" TIMESTAMP(0),
"updateTime" TIMESTAMP(0),
"createBy" VARCHAR(256),
"updateBy" VARCHAR(256),
"tenantOwner" VARCHAR(256) NOT NULL,
"signingAlg" VARCHAR(80),
"signatureSecret" VARCHAR(1020),
"publicKeyPem" TEXT,
"privateKeyPem" TEXT,
"licenceCode" VARCHAR(128),
NOT CLUSTER PRIMARY KEY("id"),
CONSTRAINT "clientId_index" UNIQUE("clientId")) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT ON TABLE "uemx"."t_client_integration" IS 'MDM-应用集成信息表';

COMMENT ON COLUMN "uemx"."t_client_integration"."appOs" IS '应用操作系统，1：android ，2：ios，5：windows，7：macos';

COMMENT ON COLUMN "uemx"."t_client_integration"."clientId" IS '应用的唯一ID';

COMMENT ON COLUMN "uemx"."t_client_integration"."clientIdIssuedAt" IS '创建时间';

COMMENT ON COLUMN "uemx"."t_client_integration"."clientName" IS '应用的中文名称';

COMMENT ON COLUMN "uemx"."t_client_integration"."clientNameEn" IS '应用的英文名称';

COMMENT ON COLUMN "uemx"."t_client_integration"."createBy" IS '创建人';

COMMENT ON COLUMN "uemx"."t_client_integration"."description" IS '应用简介';

COMMENT ON COLUMN "uemx"."t_client_integration"."id" IS '主键ID';

COMMENT ON COLUMN "uemx"."t_client_integration"."licenceCode" IS '授权码';

COMMENT ON COLUMN "uemx"."t_client_integration"."logoUri" IS '应用图标';

COMMENT ON COLUMN "uemx"."t_client_integration"."packageName" IS '应用包名';

COMMENT ON COLUMN "uemx"."t_client_integration"."privateKeyPem" IS '验证签名的私有密钥(PEM格式)';

COMMENT ON COLUMN "uemx"."t_client_integration"."publicKeyPem" IS '验证签名的公共密钥(PEM格式)';

COMMENT ON COLUMN "uemx"."t_client_integration"."signatureSecret" IS '对称签名的secret base64值';

COMMENT ON COLUMN "uemx"."t_client_integration"."signingAlg" IS '签名算法，none表示不进行签名，RS256非对称，HS256对称';

COMMENT ON COLUMN "uemx"."t_client_integration"."status" IS '应用状态:0-无效 1-有效';

COMMENT ON COLUMN "uemx"."t_client_integration"."tenantOwner" IS '应用所属的租户ID';

COMMENT ON COLUMN "uemx"."t_client_integration"."updateBy" IS '更新人';

COMMENT ON COLUMN "uemx"."t_client_integration"."updateTime" IS '更新时间';

CREATE TABLE "uemx"."t_device_profile"
(
"id" BIGINT NOT NULL,
"deviceid" BIGINT NOT NULL,
"udid" CHAR(256),
"policyid" VARCHAR(400) NOT NULL,
"createtime" TIMESTAMP(0),
"updatetime" TIMESTAMP(0),
"status" INT,
"fencestatus" INT DEFAULT 0,
"tenantId" VARCHAR(256) NOT NULL,
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT ON TABLE "uemx"."t_device_profile" IS '设备与配置文件策略关联表';

COMMENT ON COLUMN "uemx"."t_device_profile"."createtime" IS '下发时间';

COMMENT ON COLUMN "uemx"."t_device_profile"."fencestatus" IS '围栏状态：0、围栏外；1、围栏内';

COMMENT ON COLUMN "uemx"."t_device_profile"."policyid" IS '配置文件uuid';

COMMENT ON COLUMN "uemx"."t_device_profile"."status" IS '执行状态,0--已分发;1--已安装;21--安装失败';

COMMENT ON COLUMN "uemx"."t_device_profile"."tenantId" IS '租户ID';

COMMENT ON COLUMN "uemx"."t_device_profile"."updatetime" IS '更新时间';

CREATE OR REPLACE  INDEX "uemx"."t_device_profile_idx_1" ON "uemx"."t_device_profile"("deviceid" ASC,"tenantId" ASC,"status" ASC,"policyid" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE OR REPLACE  INDEX "uemx"."t_device_profile_idx_3" ON "uemx"."t_device_profile"("policyid" ASC,"tenantId" ASC,"status" ASC,"deviceid" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE OR REPLACE  INDEX "uemx"."t_device_profile_idx_2" ON "uemx"."t_device_profile"("udid" ASC,"tenantId" ASC,"deviceid" ASC,"status" ASC,"policyid" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE TABLE "uemx"."t_device_using_log"
(
"id" BIGINT NOT NULL,
"deviceid" BIGINT NOT NULL,
"imsi" VARCHAR(200),
"operatorname" VARCHAR(800),
"phonecode" VARCHAR(400),
"simchangeinfo" VARCHAR(800),
"simcardflow" INT,
"wlanflow" INT,
"createtime" TIMESTAMP(0),
"tenantId" VARCHAR(256) NOT NULL,
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT ON TABLE "uemx"."t_device_using_log" IS '设备使用日志表';

COMMENT ON COLUMN "uemx"."t_device_using_log"."createtime" IS '日志创建时间';

COMMENT ON COLUMN "uemx"."t_device_using_log"."imsi" IS 'imsi:international mobile subscriber identity';

COMMENT ON COLUMN "uemx"."t_device_using_log"."operatorname" IS '运营商名称';

COMMENT ON COLUMN "uemx"."t_device_using_log"."phonecode" IS '手机号码';

COMMENT ON COLUMN "uemx"."t_device_using_log"."simcardflow" IS 'sim卡流量(kb)';

COMMENT ON COLUMN "uemx"."t_device_using_log"."simchangeinfo" IS 'sim变更信息';

COMMENT ON COLUMN "uemx"."t_device_using_log"."tenantId" IS '租户ID';

COMMENT ON COLUMN "uemx"."t_device_using_log"."wlanflow" IS 'wlan流量(kb)';

CREATE OR REPLACE  INDEX "uemx"."t_device_usercheckin_index" ON "uemx"."t_device_using_log"("deviceid" ASC,"tenantId" ASC,"createtime" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE TABLE "uemx"."t_mdm_device"
(
"id" BIGINT NOT NULL,
"userid" BIGINT,
"shortudid" VARCHAR(40),
"udid" CHAR(256),
"devicetype" INT DEFAULT 10 NOT NULL,
"type" INT DEFAULT 1 NOT NULL,
"rootflag" INT DEFAULT 0,
"lastonlinetime" TIMESTAMP(0),
"onlineflag" INT DEFAULT 0,
"status" INT DEFAULT 1,
"registtime" TIMESTAMP(0),
"activatetime" TIMESTAMP(0),
"updatetime" TIMESTAMP(0),
"devicename" VARCHAR(400),
"userName" VARCHAR(600),
"loginId" VARCHAR(200),
"openUserId" VARCHAR(200),
"groupId" BIGINT,
"dudid" VARCHAR(256),
"activateMdmTime" TIMESTAMP(0),
"mdmStatus" INT DEFAULT 0,
"dualstatus" INT,
"uemVersion" VARCHAR(200),
"clientversion" VARCHAR(120) DEFAULT '',
"ip" VARCHAR(80),
"clientbuildnum" VARCHAR(200),
"tenantId" VARCHAR(256) NOT NULL,
"idfa" CHAR(256),
"imei" VARCHAR(200),
"imsi" VARCHAR(200),
"serialnum" VARCHAR(200),
"manufacturer" VARCHAR(800),
"model" VARCHAR(400),
"os" VARCHAR(400),
"versionNum" VARCHAR(400),
"oscoreversion" VARCHAR(200),
"cpu" VARCHAR(400),
"ram" VARCHAR(400),
"camera" VARCHAR(400),
"wifimac" VARCHAR(120),
"bluetoothmac" VARCHAR(120),
"productname" VARCHAR(400),
"clientfinger" VARCHAR(400),
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT ON TABLE "uemx"."t_mdm_device" IS '设备信息表';

COMMENT ON COLUMN "uemx"."t_mdm_device"."activateMdmTime" IS '设备的MDM激活时间';

COMMENT ON COLUMN "uemx"."t_mdm_device"."activatetime" IS '激活时间';

COMMENT ON COLUMN "uemx"."t_mdm_device"."bluetoothmac" IS '蓝牙 mac地址';

COMMENT ON COLUMN "uemx"."t_mdm_device"."camera" IS '摄像头';

COMMENT ON COLUMN "uemx"."t_mdm_device"."clientbuildnum" IS 'EMM客户端Build号';

COMMENT ON COLUMN "uemx"."t_mdm_device"."clientfinger" IS '客户端签名证书指纹';

COMMENT ON COLUMN "uemx"."t_mdm_device"."clientversion" IS 'EMM 客户端版本号';

COMMENT ON COLUMN "uemx"."t_mdm_device"."devicename" IS '设备名称';

COMMENT ON COLUMN "uemx"."t_mdm_device"."devicetype" IS '设备类型 10-android phone 11-android pad;20-iphone 21-ipad 22-ipadmini 23- ipodtouch';

COMMENT ON COLUMN "uemx"."t_mdm_device"."dualstatus" IS '双域状态（0-工作域 1-生活域）';

COMMENT ON COLUMN "uemx"."t_mdm_device"."dudid" IS '设备的系统UDID值,设备MDM激活后得到的设备唯一ID,原来的UDID为应用生成的UMID';

COMMENT ON COLUMN "uemx"."t_mdm_device"."groupId" IS '所属用户组Id';

COMMENT ON COLUMN "uemx"."t_mdm_device"."idfa" IS 'unique device identifier(IOS7)';

COMMENT ON COLUMN "uemx"."t_mdm_device"."imei" IS 'imei:international mobile equipment identity';

COMMENT ON COLUMN "uemx"."t_mdm_device"."imsi" IS 'imsi:international mobile subscriber identity';

COMMENT ON COLUMN "uemx"."t_mdm_device"."ip" IS 'ip地址';

COMMENT ON COLUMN "uemx"."t_mdm_device"."lastonlinetime" IS '最后在线时间';

COMMENT ON COLUMN "uemx"."t_mdm_device"."loginId" IS '用户登录名';

COMMENT ON COLUMN "uemx"."t_mdm_device"."manufacturer" IS '设备厂商';

COMMENT ON COLUMN "uemx"."t_mdm_device"."mdmStatus" IS '设备的MDM激活状态';

COMMENT ON COLUMN "uemx"."t_mdm_device"."model" IS '设备型号,例如：MC319LL';

COMMENT ON COLUMN "uemx"."t_mdm_device"."onlineflag" IS '设备是否在线 0--在线;1--不在线';

COMMENT ON COLUMN "uemx"."t_mdm_device"."openUserId" IS '第三方平台用户Id';

COMMENT ON COLUMN "uemx"."t_mdm_device"."os" IS '操作系统平台';

COMMENT ON COLUMN "uemx"."t_mdm_device"."oscoreversion" IS '系统内核版本号';

COMMENT ON COLUMN "uemx"."t_mdm_device"."productname" IS '设备型号码 ,例如：iPhone3,1';

COMMENT ON COLUMN "uemx"."t_mdm_device"."registtime" IS '注册时间';

COMMENT ON COLUMN "uemx"."t_mdm_device"."rootflag" IS '是否破解root, 0--未破解 ;1--破解 ';

COMMENT ON COLUMN "uemx"."t_mdm_device"."serialnum" IS '设备序列号';

COMMENT ON COLUMN "uemx"."t_mdm_device"."shortudid" IS 'unique device short identifier';

COMMENT ON COLUMN "uemx"."t_mdm_device"."status" IS '设备状态,0--未注册,1--注册未激活,2--激活可用, 3--保留 4--擦除';

COMMENT ON COLUMN "uemx"."t_mdm_device"."tenantId" IS '租户ID';

COMMENT ON COLUMN "uemx"."t_mdm_device"."type" IS '平台1--android;2--ios';

COMMENT ON COLUMN "uemx"."t_mdm_device"."udid" IS 'unique device identifier';

COMMENT ON COLUMN "uemx"."t_mdm_device"."uemVersion" IS 'UEM版本号';

COMMENT ON COLUMN "uemx"."t_mdm_device"."updatetime" IS '设备信息更新时间';

COMMENT ON COLUMN "uemx"."t_mdm_device"."userid" IS '所属用户用户id';

COMMENT ON COLUMN "uemx"."t_mdm_device"."userName" IS '用户名';

COMMENT ON COLUMN "uemx"."t_mdm_device"."versionNum" IS ' 版本号';

COMMENT ON COLUMN "uemx"."t_mdm_device"."wifimac" IS 'wifi mac地址';

CREATE OR REPLACE  INDEX "uemx"."t_mdm_evice_idx_1" ON "uemx"."t_mdm_device"("userid" ASC,"tenantId" ASC,"type" ASC,"devicetype" ASC,"udid" ASC,"status" ASC,"groupId" ASC,"loginId" ASC,"userName" ASC,"openUserId" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE OR REPLACE  INDEX "uemx"."t_mdm_device_idx_5" ON "uemx"."t_mdm_device"("loginId" ASC,"tenantId" ASC,"type" ASC,"devicetype" ASC,"status" ASC,"groupId" ASC,"userName" ASC,"openUserId" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE OR REPLACE  INDEX "uemx"."t_mdm_device_idx_6" ON "uemx"."t_mdm_device"("dudid" ASC,"tenantId" ASC,"status" ASC,"mdmStatus" ASC,"activateMdmTime" ASC,"groupId" ASC,"userid" ASC,"loginId" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE OR REPLACE  INDEX "uemx"."t_mdm_device_idx_2" ON "uemx"."t_mdm_device"("type" ASC,"tenantId" ASC,"status" ASC,"onlineflag" ASC,"lastonlinetime" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE OR REPLACE  INDEX "uemx"."t_mdm_device_idx_3" ON "uemx"."t_mdm_device"("udid" ASC,"tenantId" ASC,"userid" ASC,"status" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE OR REPLACE  INDEX "uemx"."t_mdm_device_idx_4" ON "uemx"."t_mdm_device"("groupId" ASC,"tenantId" ASC,"type" ASC,"devicetype" ASC,"status" ASC,"userid" ASC,"loginId" ASC,"userName" ASC,"openUserId" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE TABLE "uemx"."t_mdm_device_extend"
(
"id" BIGINT NOT NULL,
"buytime" DATE,
"warranty" INT,
"relationship" INT DEFAULT 1 NOT NULL,
"enableperm" INT DEFAULT 1,
"devicename" VARCHAR(400),
"devicenumber" VARCHAR(200),
"initsiminfo" VARCHAR(200),
"initsdserialnum" VARCHAR(400),
"iccid" VARCHAR(200),
"phonecode" VARCHAR(400),
"romcapacity" VARCHAR(400),
"romavailablecapacity" VARCHAR(400),
"sdcapacity" VARCHAR(400),
"sdavailablecapacity" VARCHAR(400),
"sdserialnum" VARCHAR(400),
"powerstatus" VARCHAR(400),
"boottime" BIGINT,
"capacity" VARCHAR(400),
"capacitystatus" VARCHAR(400),
"availablecapacity" VARCHAR(400),
"ip" VARCHAR(80),
"encryptionlevel" INT,
"token" VARCHAR(800),
"pushmagic" VARCHAR(800),
"unlocktoken" VARCHAR(16000),
"sendregistnoticeflag" INT DEFAULT 0,
"allowactrootorjailbreakflag" INT DEFAULT 0,
"remark" VARCHAR(2000),
"roamingflag" INT DEFAULT 0,
"updatetime" TIMESTAMP(0),
"apnstoken" VARCHAR(800),
"lostflag" INT DEFAULT 0,
"dataencryptionflag" INT DEFAULT 0,
"specialtypeflag" INT DEFAULT 0,
"knoxstatus" INT DEFAULT 0,
"offlinestatus" INT DEFAULT 0 NOT NULL,
"offlinethreshold" VARCHAR(20) DEFAULT '0' NOT NULL,
"emmloginagain" INT DEFAULT 0,
"hostid" VARCHAR(200),
"usagestats" INT DEFAULT 0,
"flowupdate" INT DEFAULT 0,
"flowquota" INT DEFAULT 0,
"bitlocker" INT,
"meid" VARCHAR(200),
"resolution" VARCHAR(200),
"ossoftwareversion" VARCHAR(200),
"safetyosversion" VARCHAR(200),
"patchlevel" VARCHAR(200),
"iccid2" VARCHAR(200),
"imsi2" VARCHAR(200),
"networktype" VARCHAR(200),
"wlanadapterchip" VARCHAR(200),
"btadapterchip" VARCHAR(200),
"nfcchip" VARCHAR(200),
"locatorchip" VARCHAR(200),
"clientbuildnum" VARCHAR(200),
"cpuratio" VARCHAR(120),
"memoryratio" VARCHAR(120),
"storageratio" VARCHAR(120),
"bluetoothstate" INT,
"wifistate" INT,
"camerastate" INT,
"microphonestate" INT,
"mobiledatastate" INT,
"apn" VARCHAR(2000),
"buildnumber" VARCHAR(800),
"gpsstate" INT,
"syncflag" INT DEFAULT 0,
"dualstatus" INT,
"simoper" INT,
"tpmreport" INT,
"systemintegrity" INT,
"idp" VARCHAR(80),
"idpdeviceid" VARCHAR(400),
"offlineTime" TIMESTAMP(0),
"enablepermissionguide" INT DEFAULT 1,
"tenantId" VARCHAR(256) NOT NULL,
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT ON TABLE "uemx"."t_mdm_device_extend" IS '设备扩展信息表';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."allowactrootorjailbreakflag" IS '是否允许root/越狱设备激活,0--否;1--是';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."apn" IS 'apn集合，status:0 未连接 ，1连接';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."apnstoken" IS 'apns的设备token';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."availablecapacity" IS '可用空间(ios)';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."bitlocker" IS 'BitLocker加密状态 0:未加密 1:整个硬盘加密 2:系统分区加密';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."bluetoothstate" IS '蓝牙状态，1-开启,0 -关闭';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."boottime" IS '开机时长';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."btadapterchip" IS '蓝牙芯片型号';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."buildnumber" IS '内核版本';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."buytime" IS '购买时间';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."camerastate" IS '相机状态，1-开启 0-关闭';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."capacity" IS '存储容量(ios)';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."capacitystatus" IS '存储容量状态(ios)';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."clientbuildnum" IS 'EMM客户端Build号';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."cpuratio" IS 'CPU占用率';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."dataencryptionflag" IS '是否开启数据加密.0--否;1--是';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."devicename" IS '设备名称';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."devicenumber" IS '设备编号';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."dualstatus" IS '双域状态（0-工作域 1-生活域）';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."emmloginagain" IS '自服务是否修改密码 0-未修改 1-已修改';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."enableperm" IS '是否启用权限配置引导，1 -- 启用；0 -- 不启用';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."enablepermissionguide" IS '强管控权限向导，1 -- 强管控；0 -- 弱管控';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."encryptionlevel" IS '加密级别';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."flowquota" IS '流量配额';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."flowupdate" IS '流量是否有更新 0 未更新，1 有更新';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."gpsstate" IS 'GPS状态：0-关闭 1-开启';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."hostid" IS 'hostId proxy激活时的hostId';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."iccid2" IS 'ICCID(SIM卡2的ICCID)';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."idp" IS '第三方idp名称，idp_device_id对应';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."idpdeviceid" IS 'device_id对应的idp';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."imsi2" IS 'IMSI(SIM卡2的IMSI,如无卡2，可为空)';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."initsdserialnum" IS '激活时的SD卡序列号';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."initsiminfo" IS '激活时SIM卡信息，Android设备为IMSI，iOS设备为ICCID';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."ip" IS 'ip地址';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."knoxstatus" IS 'knox状态,0--不支持;201--支持未注入;202--SAFE license已注入;203--KNOX license已注入;101--已创建;501--锁定;301--华为插件已激活;302--移除华为插件';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."locatorchip" IS '定位芯片型号';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."lostflag" IS '是否丢失：0 --未丢失，1 --丢失';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."meid" IS '副卡对应的设备识别码,(如无副卡,可为空)';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."memoryratio" IS '内存占用率';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."microphonestate" IS '麦克风状态，1-开启 0-关闭';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."mobiledatastate" IS '移动数据状态，1-开启 0-关闭';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."networktype" IS '支持的移动网络制式';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."nfcchip" IS 'NFC芯片型号';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."offlinestatus" IS '设备是否失联：0未失联，1失联';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."offlinethreshold" IS '设备失联违规天数';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."offlineTime" IS '失联时间';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."ossoftwareversion" IS '系统软件版本号';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."patchlevel" IS '系统安全补丁程序级别(如无,可为空)';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."phonecode" IS '手机号码';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."powerstatus" IS '电源状态';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."pushmagic" IS 'ios mdm发送唤醒使用';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."relationship" IS '设备所属关系 1--公司设备;2--员工设备;3--其他';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."remark" IS '备注';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."resolution" IS '屏幕分辨率,(字符串,长*高,如640*480)';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."roamingflag" IS '设备漫游开关 0--否;1--是';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."romavailablecapacity" IS 'rom 可用空间';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."romcapacity" IS 'rom 总容量';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."safetyosversion" IS '安全加固双操作系统版本,(如无,可为空)';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."sdavailablecapacity" IS 'sd卡可用空间';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."sdcapacity" IS 'sd卡容量';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."sdserialnum" IS 'sd卡序列号';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."sendregistnoticeflag" IS '是否发送注册通知信息,0--否;1--是';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."simoper" IS 'sim卡操作 1-更新sim卡 0-撤销更新sim卡';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."specialtypeflag" IS '特殊设备类型标识.0--普通设备;100--Samsung safe设备;101--Samsung KNOX设备;301--华为设备';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."storageratio" IS '存储占用率';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."syncflag" IS '是否上报成功 1-上报绑定成功 10-上报绑定失败 2-上报解绑成功 20-上报解绑失败';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."systemintegrity" IS '系统完整性 2-不完整 1-完整';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."tenantId" IS '租户ID';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."token" IS 'ios mdm发送唤醒使用';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."tpmreport" IS '可信度 2-不安全 1-安全';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."unlocktoken" IS 'ios mdm解除锁屏使用';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."updatetime" IS '设备信息更新时间';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."usagestats" IS '应用使用量统计状态 0 未启用，1 启用';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."warranty" IS '保修期';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."wifistate" IS 'wifi状态，1-开启 0-关闭';

COMMENT ON COLUMN "uemx"."t_mdm_device_extend"."wlanadapterchip" IS '无线网卡芯片型号';

CREATE TABLE "uemx"."t_mdm_policy"
(
"id" VARCHAR(400) NOT NULL,
"name" VARCHAR(800) NOT NULL,
"category" INT,
"sub_category" INT,
"create_by" VARCHAR(800),
"create_time" TIMESTAMP(0) NOT NULL,
"update_by" VARCHAR(800),
"update_time" TIMESTAMP(0),
"status" INT DEFAULT 0,
"content" CLOB,
"deleted" TINYINT DEFAULT 0,
"tenantId" VARCHAR(256) NOT NULL,
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT ON COLUMN "uemx"."t_mdm_policy"."category" IS '策略大的分类，配置策略、限制策略、围栏策略、合规策略等';

COMMENT ON COLUMN "uemx"."t_mdm_policy"."content" IS '扩展存放json';

COMMENT ON COLUMN "uemx"."t_mdm_policy"."create_by" IS '创建人姓名';

COMMENT ON COLUMN "uemx"."t_mdm_policy"."create_time" IS '创建时间';

COMMENT ON COLUMN "uemx"."t_mdm_policy"."deleted" IS '逻辑删除标志';

COMMENT ON COLUMN "uemx"."t_mdm_policy"."id" IS 'uuid';

COMMENT ON COLUMN "uemx"."t_mdm_policy"."name" IS '名称';

COMMENT ON COLUMN "uemx"."t_mdm_policy"."status" IS '策略状态,-1--删除;0--禁用;1--启用';

COMMENT ON COLUMN "uemx"."t_mdm_policy"."sub_category" IS '策略小的分类，WiFi配置策略、VPN配置策略等';

COMMENT ON COLUMN "uemx"."t_mdm_policy"."tenantId" IS '租户ID';

COMMENT ON COLUMN "uemx"."t_mdm_policy"."update_by" IS '更新人姓名';

COMMENT ON COLUMN "uemx"."t_mdm_policy"."update_time" IS '更新时间';

CREATE OR REPLACE  INDEX "uemx"."t_mdm_policy_idx_1" ON "uemx"."t_mdm_policy"("status" ASC,"deleted" ASC,"tenantId" ASC,"sub_category" ASC,"update_time" ASC,"category" ASC,"create_time" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE OR REPLACE  INDEX "uemx"."t_mdm_policy_idx_2" ON "uemx"."t_mdm_policy"("deleted" ASC,"status" ASC,"tenantId" ASC,"sub_category" ASC,"update_time" ASC,"create_time" ASC,"category" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE TABLE "uemx"."t_mdm_policy_global"
(
"id" VARCHAR(400) NOT NULL,
"name" VARCHAR(800) NOT NULL,
"category" INT,
"sub_category" INT,
"update_by" VARCHAR(800),
"update_time" TIMESTAMP(0),
"content" CLOB,
"tenantId" VARCHAR(256) NOT NULL,
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT ON TABLE "uemx"."t_mdm_policy_global" IS '全局默认策略表';

COMMENT ON COLUMN "uemx"."t_mdm_policy_global"."category" IS '策略大的分类，配置策略、限制策略、围栏策略、合规策略等';

COMMENT ON COLUMN "uemx"."t_mdm_policy_global"."content" IS '扩展存放json';

COMMENT ON COLUMN "uemx"."t_mdm_policy_global"."id" IS 'uuid';

COMMENT ON COLUMN "uemx"."t_mdm_policy_global"."name" IS '名称';

COMMENT ON COLUMN "uemx"."t_mdm_policy_global"."sub_category" IS '策略小的分类，WiFi配置策略、VPN配置策略等';

COMMENT ON COLUMN "uemx"."t_mdm_policy_global"."tenantId" IS '租户ID';

COMMENT ON COLUMN "uemx"."t_mdm_policy_global"."update_by" IS '更新人姓名';

COMMENT ON COLUMN "uemx"."t_mdm_policy_global"."update_time" IS '更新时间';

CREATE TABLE "uemx"."t_policy_device"
(
"id" BIGINT NOT NULL,
"policyId" VARCHAR(400) NOT NULL,
"deviceId" BIGINT NOT NULL,
"customflag" INT DEFAULT 0,
"exceptionflag" INT DEFAULT 0,
"pushId" BIGINT,
"tenantId" VARCHAR(256) NOT NULL,
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT ON TABLE "uemx"."t_policy_device" IS '策略按设备下发关联表';

COMMENT ON COLUMN "uemx"."t_policy_device"."customflag" IS '自定义标识: 0-继承 ;1-自定义';

COMMENT ON COLUMN "uemx"."t_policy_device"."deviceId" IS '设备id';

COMMENT ON COLUMN "uemx"."t_policy_device"."exceptionflag" IS '例外标识: 0-添加 ;1-排除';

COMMENT ON COLUMN "uemx"."t_policy_device"."policyId" IS '策略id';

COMMENT ON COLUMN "uemx"."t_policy_device"."tenantId" IS '租户ID';

CREATE OR REPLACE  INDEX "uemx"."t_policy_device_ibfk_2" ON "uemx"."t_policy_device"("deviceId" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE OR REPLACE  INDEX "uemx"."t_policy_device_ibfk_1" ON "uemx"."t_policy_device"("policyId" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE TABLE "uemx"."t_policy_group"
(
"id" BIGINT NOT NULL,
"policyid" VARCHAR(400) NOT NULL,
"groupid" BIGINT NOT NULL,
"customflag" INT DEFAULT 0 NOT NULL,
"pushid" BIGINT,
"tenantId" VARCHAR(256) NOT NULL,
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT ON COLUMN "uemx"."t_policy_group"."customflag" IS '自定义标识: 0-继承 ;1-自定义';

COMMENT ON COLUMN "uemx"."t_policy_group"."policyid" IS '策略id';

COMMENT ON COLUMN "uemx"."t_policy_group"."tenantId" IS '租户ID';

CREATE OR REPLACE  INDEX "uemx"."groupid" ON "uemx"."t_policy_group"("groupid" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE OR REPLACE  INDEX "uemx"."policyid" ON "uemx"."t_policy_group"("policyid" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE TABLE "uemx"."t_policy_user"
(
"id" BIGINT NOT NULL,
"policyid" VARCHAR(400) NOT NULL,
"userid" BIGINT NOT NULL,
"customflag" INT DEFAULT 0,
"sub_category" INT DEFAULT 0,
"exceptionflag" INT DEFAULT 0,
"pushid" BIGINT,
"deleted" TINYINT DEFAULT 0,
"tenantId" VARCHAR(256) NOT NULL,
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT ON COLUMN "uemx"."t_policy_user"."customflag" IS '自定义标识: 0-继承 ;1-自定义';

COMMENT ON COLUMN "uemx"."t_policy_user"."deleted" IS '逻辑删除标志';

COMMENT ON COLUMN "uemx"."t_policy_user"."exceptionflag" IS '例外标识: 0-添加 ;1-排除';

COMMENT ON COLUMN "uemx"."t_policy_user"."policyid" IS '策略id';

COMMENT ON COLUMN "uemx"."t_policy_user"."sub_category" IS '策略子类型';

COMMENT ON COLUMN "uemx"."t_policy_user"."tenantId" IS '租户ID';

COMMENT ON COLUMN "uemx"."t_policy_user"."userid" IS '用户id';

CREATE OR REPLACE  INDEX "uemx"."t_policy_user_idx_3" ON "uemx"."t_policy_user"("sub_category" ASC,"tenantId" ASC,"userid" ASC,"deleted" ASC,"policyid" ASC,"pushid" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE OR REPLACE  INDEX "uemx"."t_policy_user_idx_2" ON "uemx"."t_policy_user"("policyid" ASC,"tenantId" ASC,"userid" ASC,"deleted" ASC,"sub_category" ASC,"pushid" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE OR REPLACE  INDEX "uemx"."t_policy_user_idx_1" ON "uemx"."t_policy_user"("userid" ASC,"tenantId" ASC,"policyid" ASC,"deleted" ASC,"sub_category" ASC,"pushid" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE TABLE "uemx"."t_push_credential"
(
"id" BIGINT NOT NULL,
"pushType" VARCHAR(160) NOT NULL,
"credential" TEXT,
"config" VARCHAR(8000),
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT ON TABLE "uemx"."t_push_credential" IS '记录的是推送记录信息';

COMMENT ON COLUMN "uemx"."t_push_credential"."config" IS '证书附加的配置信息，如密码、topic等';

COMMENT ON COLUMN "uemx"."t_push_credential"."credential" IS '证书内容对应Base64字符串';

COMMENT ON COLUMN "uemx"."t_push_credential"."pushType" IS '推送类型,govdd政务钉钉推送，npns(NPNS服务推送),ios(苹果推送)';

CREATE OR REPLACE  INDEX "uemx"."t_push_credential_idx_1" ON "uemx"."t_push_credential"("pushType" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE TABLE "uemx"."t_push_failure_record"
(
"id" BIGINT NOT NULL,
"pushMode" INT NOT NULL,
"pushType" VARCHAR(160),
"sender" VARCHAR(400),
"payload" TEXT,
"pushPayload" TEXT,
"isBatch" INT NOT NULL,
"receivers" TEXT,
"pushReceivers" TEXT,
"pushTime" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP(),
"result" VARCHAR(400),
"errorMsg" VARCHAR(8000),
"failureTimes" INT,
"nextPushTime" TIMESTAMP(0),
"tenantId" VARCHAR(256) NOT NULL,
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT ON TABLE "uemx"."t_push_failure_record" IS '记录的是推送失败记录信息';

COMMENT ON COLUMN "uemx"."t_push_failure_record"."errorMsg" IS '推送结果消息，error msg';

COMMENT ON COLUMN "uemx"."t_push_failure_record"."failureTimes" IS '失败次数';

COMMENT ON COLUMN "uemx"."t_push_failure_record"."isBatch" IS '0单个推送，1批量推送';

COMMENT ON COLUMN "uemx"."t_push_failure_record"."nextPushTime" IS '下一推送时间';

COMMENT ON COLUMN "uemx"."t_push_failure_record"."payload" IS '推送对应的payload,是指业务中对应的payload';

COMMENT ON COLUMN "uemx"."t_push_failure_record"."pushMode" IS '推送模式0应用，1设备';

COMMENT ON COLUMN "uemx"."t_push_failure_record"."pushPayload" IS '推送至客户端对应的payload,一般ios中会跟payload不一致';

COMMENT ON COLUMN "uemx"."t_push_failure_record"."pushReceivers" IS '推送时，具体的接收方，以json方式存储';

COMMENT ON COLUMN "uemx"."t_push_failure_record"."pushTime" IS '推送时间';

COMMENT ON COLUMN "uemx"."t_push_failure_record"."pushType" IS '推送类型,govdd政务钉钉推送，npns(NPNS服务推送),ios(苹果推送)';

COMMENT ON COLUMN "uemx"."t_push_failure_record"."receivers" IS '接收方，以json方式存储,是指业务中对应的receivers';

COMMENT ON COLUMN "uemx"."t_push_failure_record"."result" IS '推送结果';

COMMENT ON COLUMN "uemx"."t_push_failure_record"."sender" IS '发送方（推送方)';

COMMENT ON COLUMN "uemx"."t_push_failure_record"."tenantId" IS '租户ID';

CREATE OR REPLACE  INDEX "uemx"."t_push_failure_record_idx_1" ON "uemx"."t_push_failure_record"("nextPushTime" ASC,"pushMode" ASC,"pushType" ASC,"pushTime" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE TABLE "uemx"."t_push_log"
(
"id" BIGINT NOT NULL,
"pushMode" INT NOT NULL,
"pushType" VARCHAR(160) NOT NULL,
"sender" VARCHAR(400),
"payload" TEXT,
"pushPayload" TEXT,
"isBatch" INT NOT NULL,
"pushTime" TIMESTAMP(0) DEFAULT CURRENT_TIMESTAMP(),
"result" VARCHAR(400),
"errorMsg" VARCHAR(8000),
"tenantId" VARCHAR(256) NOT NULL,
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT ON TABLE "uemx"."t_push_log" IS '记录的是推送记录信息';

COMMENT ON COLUMN "uemx"."t_push_log"."errorMsg" IS '推送结果消息，error msg';

COMMENT ON COLUMN "uemx"."t_push_log"."isBatch" IS '0单个推送，1批量推送';

COMMENT ON COLUMN "uemx"."t_push_log"."payload" IS '推送对应的payload,是指业务中对应的payload';

COMMENT ON COLUMN "uemx"."t_push_log"."pushMode" IS '推送模式0应用，1设备';

COMMENT ON COLUMN "uemx"."t_push_log"."pushPayload" IS '推送至客户端对应的payload,一般ios中会跟payload不一致';

COMMENT ON COLUMN "uemx"."t_push_log"."pushTime" IS '推送时间';

COMMENT ON COLUMN "uemx"."t_push_log"."pushType" IS '推送类型,govdd政务钉钉推送，npns(NPNS服务推送),ios(苹果推送)';

COMMENT ON COLUMN "uemx"."t_push_log"."result" IS '推送结果';

COMMENT ON COLUMN "uemx"."t_push_log"."sender" IS '发送方（推送方)';

COMMENT ON COLUMN "uemx"."t_push_log"."tenantId" IS '租户ID';

CREATE OR REPLACE  INDEX "uemx"."t_push_log_idx_1" ON "uemx"."t_push_log"("pushTime" ASC,"pushMode" ASC,"pushType" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE TABLE "uemx"."t_push_log_receiver"
(
"id" BIGINT NOT NULL,
"logId" INT NOT NULL,
"userId" BIGINT NOT NULL,
"loginId" VARCHAR(160),
"udid" VARCHAR(256),
"dudid" VARCHAR(256),
"tenantId" VARCHAR(256) NOT NULL,
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT ON TABLE "uemx"."t_push_log_receiver" IS '推送日志记录接收者信息';

COMMENT ON COLUMN "uemx"."t_push_log_receiver"."dudid" IS '推送的目标用户设备udid';

COMMENT ON COLUMN "uemx"."t_push_log_receiver"."logId" IS '推送记录ID';

COMMENT ON COLUMN "uemx"."t_push_log_receiver"."loginId" IS '推送的目标用户loginId';

COMMENT ON COLUMN "uemx"."t_push_log_receiver"."tenantId" IS '租户ID';

COMMENT ON COLUMN "uemx"."t_push_log_receiver"."udid" IS '推送的目标用户设备umid';

COMMENT ON COLUMN "uemx"."t_push_log_receiver"."userId" IS '推送的目标用户ID';

CREATE OR REPLACE  INDEX "uemx"."t_push_log_receiver_idx_1" ON "uemx"."t_push_log_receiver"("logId" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE TABLE "uemx"."t_push_token"
(
"id" BIGINT NOT NULL,
"userid" BIGINT,
"deviceId" BIGINT,
"udid" CHAR(256),
"dudid" VARCHAR(256),
"activateMdmTime" TIMESTAMP(0),
"type" INT DEFAULT 1 NOT NULL,
"devicetype" INT DEFAULT 10 NOT NULL,
"activatetime" TIMESTAMP(0),
"token" VARCHAR(800),
"pushmagic" VARCHAR(800),
"unlocktoken" VARCHAR(16000),
"userName" VARCHAR(600),
"loginId" VARCHAR(200),
"status" INT DEFAULT 1 NOT NULL,
"expireTime" TIMESTAMP(0),
"nextCheckTime" TIMESTAMP(0),
"tenantId" VARCHAR(256) NOT NULL,
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT ON TABLE "uemx"."t_push_token" IS '推送token信息表';

COMMENT ON COLUMN "uemx"."t_push_token"."activateMdmTime" IS '设备的MDM激活时间';

COMMENT ON COLUMN "uemx"."t_push_token"."activatetime" IS '激活时间';

COMMENT ON COLUMN "uemx"."t_push_token"."deviceId" IS '设备ID';

COMMENT ON COLUMN "uemx"."t_push_token"."devicetype" IS '设备类型 10-android phone 11-android pad;20-iphone 21-ipad 22-ipadmini 23- ipodtouch';

COMMENT ON COLUMN "uemx"."t_push_token"."dudid" IS '设备的系统UDID值,设备MDM激活后得到的设备唯一ID,原来的UDID为应用生成的UMID';

COMMENT ON COLUMN "uemx"."t_push_token"."expireTime" IS '设备的MDM激活时间';

COMMENT ON COLUMN "uemx"."t_push_token"."loginId" IS '用户登录名';

COMMENT ON COLUMN "uemx"."t_push_token"."nextCheckTime" IS '设备的MDM下一检查时间';

COMMENT ON COLUMN "uemx"."t_push_token"."pushmagic" IS 'ios mdm发送唤醒使用';

COMMENT ON COLUMN "uemx"."t_push_token"."status" IS '1有效，0无效';

COMMENT ON COLUMN "uemx"."t_push_token"."tenantId" IS '租户ID';

COMMENT ON COLUMN "uemx"."t_push_token"."token" IS 'ios mdm发送唤醒使用';

COMMENT ON COLUMN "uemx"."t_push_token"."type" IS '平台1--android;2--ios';

COMMENT ON COLUMN "uemx"."t_push_token"."udid" IS 'unique device identifier';

COMMENT ON COLUMN "uemx"."t_push_token"."unlocktoken" IS 'ios mdm解除锁屏使用';

COMMENT ON COLUMN "uemx"."t_push_token"."userid" IS '所属用户用户id';

COMMENT ON COLUMN "uemx"."t_push_token"."userName" IS '用户名';

CREATE OR REPLACE  INDEX "uemx"."INDEX1818089969509700" ON "uemx"."t_push_token"("loginId" ASC,"tenantId" ASC,"type" ASC,"devicetype" ASC,"udid" ASC,"userid" ASC,"userName" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE OR REPLACE  INDEX "uemx"."t_push_token_idx_6" ON "uemx"."t_push_token"("status" ASC,"nextCheckTime" ASC,"tenantId" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE OR REPLACE  INDEX "uemx"."t_push_token_idx_2" ON "uemx"."t_push_token"("type" ASC,"tenantId" ASC,"devicetype" ASC,"userid" ASC,"udid" ASC,"loginId" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE OR REPLACE  INDEX "uemx"."t_push_token_idx_5" ON "uemx"."t_push_token"("dudid" ASC,"tenantId" ASC,"userid" ASC,"loginId" ASC,"type" ASC,"devicetype" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE OR REPLACE  INDEX "uemx"."t_push_token_idx_1" ON "uemx"."t_push_token"("userid" ASC,"tenantId" ASC,"type" ASC,"devicetype" ASC,"udid" ASC,"loginId" ASC,"userName" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE OR REPLACE  INDEX "uemx"."t_push_token_idx_3" ON "uemx"."t_push_token"("udid" ASC,"tenantId" ASC,"loginId" ASC,"userid" ASC,"devicetype" ASC,"type" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE TABLE "uemx"."t_scep_certificate"
(
"id" BIGINT NOT NULL,
"createTime" TIMESTAMP(0),
"expireTime" TIMESTAMP(0),
"revoked" INT,
"serialNumber" VARCHAR(256),
"issuer" VARCHAR(256),
"tenantId" VARCHAR(256) NOT NULL,
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT ON TABLE "uemx"."t_scep_certificate" IS 'scep生成的客户端证书表';

COMMENT ON COLUMN "uemx"."t_scep_certificate"."createTime" IS '证书生成时间';

COMMENT ON COLUMN "uemx"."t_scep_certificate"."expireTime" IS '证书过期时间';

COMMENT ON COLUMN "uemx"."t_scep_certificate"."issuer" IS '证书的签发者CN';

COMMENT ON COLUMN "uemx"."t_scep_certificate"."revoked" IS '证书是否被revoke';

COMMENT ON COLUMN "uemx"."t_scep_certificate"."serialNumber" IS '证书序列号';

COMMENT ON COLUMN "uemx"."t_scep_certificate"."tenantId" IS '租户ID';

CREATE OR REPLACE  INDEX "uemx"."t_scep_certificate_idx_1" ON "uemx"."t_scep_certificate"("serialNumber" ASC,"issuer" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE TABLE "uemx"."t_sys_config"
(
"id" VARCHAR(256) NOT NULL,
"prop_key" VARCHAR(1024),
"prop_value" VARCHAR(8192),
"note" VARCHAR(4096),
"config_file_name" VARCHAR(512),
"tenantId" VARCHAR(256) NOT NULL,
NOT CLUSTER PRIMARY KEY("id", "tenantId")) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT ON COLUMN "uemx"."t_sys_config"."tenantId" IS '租户ID';

CREATE OR REPLACE  INDEX "uemx"."t_sys_config_idx_2" ON "uemx"."t_sys_config"("config_file_name" ASC,"tenantId" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE OR REPLACE  INDEX "uemx"."t_sys_config_idx_1" ON "uemx"."t_sys_config"("prop_key" ASC,"config_file_name" ASC,"tenantId" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE TABLE "uemx"."t_sys_dictionary"
(
"id" BIGINT NOT NULL,
"dictname" VARCHAR(200) NOT NULL,
"description" VARCHAR(1020) NOT NULL,
"tenantId" VARCHAR(256) NOT NULL,
NOT CLUSTER PRIMARY KEY("id", "tenantId")) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT ON TABLE "uemx"."t_sys_dictionary" IS '系统字典表,系统版本、运营商、手机品牌 ';

COMMENT ON COLUMN "uemx"."t_sys_dictionary"."tenantId" IS '租户ID';

CREATE TABLE "uemx"."t_sys_dictionary_value"
(
"id" BIGINT NOT NULL,
"dictkey" VARCHAR(200) NOT NULL,
"dictvalue" VARCHAR(200) NOT NULL,
"status" INT NOT NULL,
"dicttype" BIGINT NOT NULL,
"fromtype" INT NOT NULL,
"creator" VARCHAR(200),
"createtime" TIMESTAMP(0),
"updater" VARCHAR(200),
"updatetime" TIMESTAMP(0),
"tenantId" VARCHAR(256) NOT NULL,
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT ON TABLE "uemx"."t_sys_dictionary_value" IS '字典值表';

COMMENT ON COLUMN "uemx"."t_sys_dictionary_value"."dicttype" IS '关联t_sys_dictionary';

COMMENT ON COLUMN "uemx"."t_sys_dictionary_value"."fromtype" IS '数据来源 1:预置数据 2:管理员添加 ';

COMMENT ON COLUMN "uemx"."t_sys_dictionary_value"."status" IS '启用禁用  1:启用 0:禁用';

COMMENT ON COLUMN "uemx"."t_sys_dictionary_value"."tenantId" IS '租户ID';

CREATE OR REPLACE  INDEX "uemx"."t_sys_dictionary_value_ibfk_1" ON "uemx"."t_sys_dictionary_value"("dicttype" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE TABLE "uemx"."t_sys_tenant"
(
"tenantId" VARCHAR(256) NOT NULL,
"enName" VARCHAR(1024),
"zhName" VARCHAR(256),
"address" VARCHAR(1024),
"contacts" VARCHAR(256),
"phone" VARCHAR(128),
"email" VARCHAR(256),
"createTime" TIMESTAMP(0),
"updateTime" TIMESTAMP(0),
"status" INT,
"expiredTime" TIMESTAMP(0),
"tenantType" INT,
NOT CLUSTER PRIMARY KEY("tenantId")) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT ON TABLE "uemx"."t_sys_tenant" IS '租户信息表';

COMMENT ON COLUMN "uemx"."t_sys_tenant"."address" IS '租户地址';

COMMENT ON COLUMN "uemx"."t_sys_tenant"."contacts" IS '联系人';

COMMENT ON COLUMN "uemx"."t_sys_tenant"."createTime" IS '创建时间';

COMMENT ON COLUMN "uemx"."t_sys_tenant"."email" IS '租户email';

COMMENT ON COLUMN "uemx"."t_sys_tenant"."enName" IS '租户英文标识';

COMMENT ON COLUMN "uemx"."t_sys_tenant"."expiredTime" IS '租户过期时间';

COMMENT ON COLUMN "uemx"."t_sys_tenant"."phone" IS '租户电话';

COMMENT ON COLUMN "uemx"."t_sys_tenant"."status" IS '租户状态';

COMMENT ON COLUMN "uemx"."t_sys_tenant"."tenantId" IS '租户ID';

COMMENT ON COLUMN "uemx"."t_sys_tenant"."tenantType" IS '客户类型。0-正式 1-试用';

COMMENT ON COLUMN "uemx"."t_sys_tenant"."updateTime" IS '更新时间';

COMMENT ON COLUMN "uemx"."t_sys_tenant"."zhName" IS '租户名称';

CREATE TABLE "uemx"."t_sys_user"
(
"loginid" VARCHAR(200) NOT NULL,
"password" VARCHAR(200) DEFAULT '',
"name" VARCHAR(200) NOT NULL,
"telephone" VARCHAR(80),
"email" VARCHAR(200),
"description" VARCHAR(800),
"flag" INT,
"parentid" VARCHAR(200),
"needchangepwd" INT DEFAULT 0,
"syncconnectorid" BIGINT,
"syncbatchno" BIGINT,
"syncuid" VARCHAR(400),
"guardids" VARCHAR(800),
"tenantId" VARCHAR(256) NOT NULL,
NOT CLUSTER PRIMARY KEY("loginid", "tenantId")) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT ON TABLE "uemx"."t_sys_user" IS '系统用户表';

COMMENT ON COLUMN "uemx"."t_sys_user"."description" IS '描述';

COMMENT ON COLUMN "uemx"."t_sys_user"."email" IS '电子信箱';

COMMENT ON COLUMN "uemx"."t_sys_user"."flag" IS '系统用户标识,10--超级管理员,20--管理员,21--业务管理员 11--审核员';

COMMENT ON COLUMN "uemx"."t_sys_user"."guardids" IS '可显示门禁权限';

COMMENT ON COLUMN "uemx"."t_sys_user"."loginid" IS '用户名';

COMMENT ON COLUMN "uemx"."t_sys_user"."name" IS '真实姓名';

COMMENT ON COLUMN "uemx"."t_sys_user"."needchangepwd" IS '登录后是否需要修改密码依据 0-否 1-是';

COMMENT ON COLUMN "uemx"."t_sys_user"."parentid" IS '父管理员id';

COMMENT ON COLUMN "uemx"."t_sys_user"."password" IS '密码';

COMMENT ON COLUMN "uemx"."t_sys_user"."syncbatchno" IS 'connector同步批次号';

COMMENT ON COLUMN "uemx"."t_sys_user"."syncconnectorid" IS 't_connector表中connector id';

COMMENT ON COLUMN "uemx"."t_sys_user"."syncuid" IS '同步connector的第三方账户uid';

COMMENT ON COLUMN "uemx"."t_sys_user"."telephone" IS '电话';

COMMENT ON COLUMN "uemx"."t_sys_user"."tenantId" IS '租户ID';

CREATE OR REPLACE  INDEX "uemx"."parentid_index" ON "uemx"."t_sys_user"("parentid" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE OR REPLACE  INDEX "uemx"."loginid_index" ON "uemx"."t_sys_user"("loginid" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE TABLE "uemx"."t_sys_user_file"
(
"id" BIGINT NOT NULL,
"deviceId" BIGINT,
"userId" BIGINT NOT NULL,
"businessType" TINYINT NOT NULL,
"filePath" VARCHAR(1020) NOT NULL,
"fileName" VARCHAR(256) NOT NULL,
"fileExt" VARCHAR(80),
"dfsType" TINYINT NOT NULL,
"createTime" TIMESTAMP(0),
"updateTime" TIMESTAMP(0),
"tenantId" VARCHAR(256),
"packageName" VARCHAR(256),
"deviceName" VARCHAR(256),
"userName" VARCHAR(256),
"loginId" VARCHAR(128),
"occTime" TIMESTAMP(0),
"ocrContent" TEXT,
"ocrStatus" TINYINT DEFAULT 0,
"batchNo" VARCHAR(128),
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT ON TABLE "uemx"."t_sys_user_file" IS '用户文件表';

COMMENT ON COLUMN "uemx"."t_sys_user_file"."batchNo" IS '录屏审计上传的批次';

COMMENT ON COLUMN "uemx"."t_sys_user_file"."businessType" IS '业务类型，1：截屏日志';

COMMENT ON COLUMN "uemx"."t_sys_user_file"."createTime" IS '创建时间';

COMMENT ON COLUMN "uemx"."t_sys_user_file"."deviceId" IS '设备ID';

COMMENT ON COLUMN "uemx"."t_sys_user_file"."deviceName" IS '设备名称';

COMMENT ON COLUMN "uemx"."t_sys_user_file"."dfsType" IS '文件系统类型，1：本地，2：OSS';

COMMENT ON COLUMN "uemx"."t_sys_user_file"."fileExt" IS '文件扩展名';

COMMENT ON COLUMN "uemx"."t_sys_user_file"."fileName" IS '文件名';

COMMENT ON COLUMN "uemx"."t_sys_user_file"."filePath" IS '文件路径';

COMMENT ON COLUMN "uemx"."t_sys_user_file"."loginId" IS '用户登录ID';

COMMENT ON COLUMN "uemx"."t_sys_user_file"."occTime" IS '发生时间';

COMMENT ON COLUMN "uemx"."t_sys_user_file"."ocrContent" IS 'ocr识别内容';

COMMENT ON COLUMN "uemx"."t_sys_user_file"."ocrStatus" IS 'ocr识别状态 0 未识别 1 已识别';

COMMENT ON COLUMN "uemx"."t_sys_user_file"."packageName" IS '应用包名';

COMMENT ON COLUMN "uemx"."t_sys_user_file"."tenantId" IS '租户ID';

COMMENT ON COLUMN "uemx"."t_sys_user_file"."updateTime" IS '更新时间';

COMMENT ON COLUMN "uemx"."t_sys_user_file"."userId" IS '用户ID';

COMMENT ON COLUMN "uemx"."t_sys_user_file"."userName" IS '用户名称';

CREATE TABLE "uemx"."t_sys_user_file_package_log"
(
"id" BIGINT NOT NULL,
"createBy" VARCHAR(80),
"updateTime" TIMESTAMP(0),
"status" INT,
"path" VARCHAR(1020),
"failReason" TEXT,
"tenantId" VARCHAR(256),
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT ON TABLE "uemx"."t_sys_user_file_package_log" IS 'MDM-截屏审计日至打包记录';

COMMENT ON COLUMN "uemx"."t_sys_user_file_package_log"."createBy" IS '创建者';

COMMENT ON COLUMN "uemx"."t_sys_user_file_package_log"."failReason" IS '失败原因';

COMMENT ON COLUMN "uemx"."t_sys_user_file_package_log"."id" IS '主键ID';

COMMENT ON COLUMN "uemx"."t_sys_user_file_package_log"."path" IS '路径';

COMMENT ON COLUMN "uemx"."t_sys_user_file_package_log"."status" IS '状态 0：打包失败，1：打包成功 2：打包中';

COMMENT ON COLUMN "uemx"."t_sys_user_file_package_log"."tenantId" IS '租户ID';

COMMENT ON COLUMN "uemx"."t_sys_user_file_package_log"."updateTime" IS '打包时间';

CREATE TABLE "uemx"."t_violate_process_dict"
(
"processid" INT NOT NULL,
"title" VARCHAR(800) NOT NULL,
NOT CLUSTER PRIMARY KEY("processid")) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT ON TABLE "uemx"."t_violate_process_dict" IS '违规处理字典表';

COMMENT ON COLUMN "uemx"."t_violate_process_dict"."processid" IS 'id';

COMMENT ON COLUMN "uemx"."t_violate_process_dict"."title" IS '标题';

CREATE TABLE "uemx"."t_violation_cond_dict"
(
"condid" INT NOT NULL,
"violationtype" INT NOT NULL,
"title" VARCHAR(800) NOT NULL,
NOT CLUSTER PRIMARY KEY("condid")) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT ON TABLE "uemx"."t_violation_cond_dict" IS '违规条件字典表';

COMMENT ON COLUMN "uemx"."t_violation_cond_dict"."condid" IS 'id';

COMMENT ON COLUMN "uemx"."t_violation_cond_dict"."title" IS '标题.';

COMMENT ON COLUMN "uemx"."t_violation_cond_dict"."violationtype" IS '违规类型.1--系统类型;2--配置策略类;3--应用类型';

CREATE TABLE "uemx"."t_violation_device"
(
"id" BIGINT NOT NULL,
"violationid" BIGINT NOT NULL,
"deviceid" BIGINT NOT NULL,
"condid" INT NOT NULL,
"violationtype" INT NOT NULL,
"condtitle" VARCHAR(800) NOT NULL,
"appstrategyid" BIGINT,
"policyid" VARCHAR(400),
"createtime" TIMESTAMP(0),
"updatetime" TIMESTAMP(0),
"adminalarmids" VARCHAR(2000),
"tenantId" VARCHAR(256) NOT NULL,
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT ON TABLE "uemx"."t_violation_device" IS '违规设备表';

COMMENT ON COLUMN "uemx"."t_violation_device"."adminalarmids" IS '管理员告警id多个管理员告警使用逗号分隔';

COMMENT ON COLUMN "uemx"."t_violation_device"."appstrategyid" IS '应用策略id.黑名单id;白名单id';

COMMENT ON COLUMN "uemx"."t_violation_device"."condid" IS '违规 条件字典表id';

COMMENT ON COLUMN "uemx"."t_violation_device"."condtitle" IS '违规 条件标题';

COMMENT ON COLUMN "uemx"."t_violation_device"."createtime" IS '创建时间';

COMMENT ON COLUMN "uemx"."t_violation_device"."deviceid" IS '设备id';

COMMENT ON COLUMN "uemx"."t_violation_device"."id" IS 'id';

COMMENT ON COLUMN "uemx"."t_violation_device"."policyid" IS '配置策略id';

COMMENT ON COLUMN "uemx"."t_violation_device"."tenantId" IS '租户ID';

COMMENT ON COLUMN "uemx"."t_violation_device"."updatetime" IS '创建时间';

COMMENT ON COLUMN "uemx"."t_violation_device"."violationid" IS '违规id';

COMMENT ON COLUMN "uemx"."t_violation_device"."violationtype" IS '违规类型.1--系统类型;2--配置策略类;3--应用类型';

CREATE OR REPLACE  INDEX "uemx"."deviceid" ON "uemx"."t_violation_device"("deviceid" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE OR REPLACE  INDEX "uemx"."violationid" ON "uemx"."t_violation_device"("violationid" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE TABLE "uemx"."t_violation_device_app"
(
"id" BIGINT NOT NULL,
"violationid" BIGINT NOT NULL,
"deviceid" BIGINT NOT NULL,
"appstrategyid" BIGINT,
"packagename" VARCHAR(800) NOT NULL,
"appversion" VARCHAR(200),
"tenantId" VARCHAR(256) NOT NULL,
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT ON TABLE "uemx"."t_violation_device_app" IS '违规设备应用违规明细表';

COMMENT ON COLUMN "uemx"."t_violation_device_app"."appstrategyid" IS '应用策略id.黑名单id;白名单id';

COMMENT ON COLUMN "uemx"."t_violation_device_app"."appversion" IS '应用版本';

COMMENT ON COLUMN "uemx"."t_violation_device_app"."deviceid" IS '设备id';

COMMENT ON COLUMN "uemx"."t_violation_device_app"."id" IS 'id';

COMMENT ON COLUMN "uemx"."t_violation_device_app"."packagename" IS '应用包名';

COMMENT ON COLUMN "uemx"."t_violation_device_app"."tenantId" IS '租户ID';

COMMENT ON COLUMN "uemx"."t_violation_device_app"."violationid" IS '违规id';

CREATE TABLE "uemx"."t_violation_device_proc"
(
"id" BIGINT NOT NULL,
"uuid" VARCHAR(200) NOT NULL,
"violationid" BIGINT NOT NULL,
"deviceid" BIGINT NOT NULL,
"procstrategyid" BIGINT,
"processid" INT NOT NULL,
"processtime" TIMESTAMP(0),
"processstatus" INT,
"createtime" TIMESTAMP(0),
"updatetime" TIMESTAMP(0),
"tenantId" VARCHAR(256) NOT NULL,
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT ON TABLE "uemx"."t_violation_device_proc" IS '违规设备处理结果表';

COMMENT ON COLUMN "uemx"."t_violation_device_proc"."createtime" IS '创建时间';

COMMENT ON COLUMN "uemx"."t_violation_device_proc"."deviceid" IS '设备id';

COMMENT ON COLUMN "uemx"."t_violation_device_proc"."id" IS 'id';

COMMENT ON COLUMN "uemx"."t_violation_device_proc"."processid" IS '违规 处理字典表id';

COMMENT ON COLUMN "uemx"."t_violation_device_proc"."processstatus" IS '处理状态.0--未处理;1---成功;-1--处理失败';

COMMENT ON COLUMN "uemx"."t_violation_device_proc"."processtime" IS '处理时间';

COMMENT ON COLUMN "uemx"."t_violation_device_proc"."procstrategyid" IS '违规 处理策略id';

COMMENT ON COLUMN "uemx"."t_violation_device_proc"."tenantId" IS '租户ID';

COMMENT ON COLUMN "uemx"."t_violation_device_proc"."updatetime" IS '修改时间';

COMMENT ON COLUMN "uemx"."t_violation_device_proc"."uuid" IS 'uuid';

COMMENT ON COLUMN "uemx"."t_violation_device_proc"."violationid" IS '违规id';

CREATE OR REPLACE  INDEX "uemx"."t_violation_device_proc_ibfk_1" ON "uemx"."t_violation_device_proc"("violationid" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE OR REPLACE  INDEX "uemx"."INDEX1818090259773500" ON "uemx"."t_violation_device_proc"("deviceid" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE TABLE "uemx"."t_violations"
(
"id" BIGINT NOT NULL,
"violationname" VARCHAR(800) NOT NULL,
"condid" INT NOT NULL,
"violationtype" INT NOT NULL,
"condvalue" VARCHAR(800),
"platformtypes" VARCHAR(120) DEFAULT '1,2,3,4' NOT NULL,
"relationship" VARCHAR(200) DEFAULT '1,2,3',
"useralarmways" VARCHAR(200),
"alarmId" VARCHAR(2000),
"processid" INT NOT NULL,
"graceperiod" INT,
"status" INT DEFAULT 0,
"createby" VARCHAR(200) NOT NULL,
"createtime" TIMESTAMP(0),
"updatetime" TIMESTAMP(0),
"lockpass" VARCHAR(80),
"gentype" INT,
"apppkglist" VARCHAR(8000),
"oldapppkglist" VARCHAR(8000),
"tenantId" VARCHAR(256) NOT NULL,
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT ON TABLE "uemx"."t_violations" IS '违规策略表';

COMMENT ON COLUMN "uemx"."t_violations"."alarmId" IS '管理员告警id 多个管理员告警使用逗号分隔';

COMMENT ON COLUMN "uemx"."t_violations"."apppkglist" IS '沙箱应用包名json';

COMMENT ON COLUMN "uemx"."t_violations"."condid" IS '违规 条件字典表id';

COMMENT ON COLUMN "uemx"."t_violations"."condvalue" IS '违规 条件参数值';

COMMENT ON COLUMN "uemx"."t_violations"."createby" IS '管理者id';

COMMENT ON COLUMN "uemx"."t_violations"."createtime" IS '创建时间';

COMMENT ON COLUMN "uemx"."t_violations"."gentype" IS '生成方式 0-自动 1-手动';

COMMENT ON COLUMN "uemx"."t_violations"."graceperiod" IS '违规处理宽限时间(分钟)';

COMMENT ON COLUMN "uemx"."t_violations"."id" IS 'id';

COMMENT ON COLUMN "uemx"."t_violations"."lockpass" IS '锁屏密码';

COMMENT ON COLUMN "uemx"."t_violations"."oldapppkglist" IS '保存更新前的沙箱应用包名json';

COMMENT ON COLUMN "uemx"."t_violations"."platformtypes" IS '适用平台 1--android;2--ios;3--windows 8;4--windows phone8,多个平台以逗号分隔';

COMMENT ON COLUMN "uemx"."t_violations"."processid" IS '违规 处理字典表id';

COMMENT ON COLUMN "uemx"."t_violations"."relationship" IS '设备所属关系  1--公司设备;2--员工设备;3--其他,多种关系使用逗号分隔';

COMMENT ON COLUMN "uemx"."t_violations"."status" IS '违规策略状态,0--禁用;1--启用';

COMMENT ON COLUMN "uemx"."t_violations"."tenantId" IS '租户ID';

COMMENT ON COLUMN "uemx"."t_violations"."updatetime" IS '变更时间';

COMMENT ON COLUMN "uemx"."t_violations"."useralarmways" IS '违规用户告警通知方式  1--邮件;2--push消息;3--短信,多种通知方式使用逗号分隔';

COMMENT ON COLUMN "uemx"."t_violations"."violationname" IS '违规名称';

COMMENT ON COLUMN "uemx"."t_violations"."violationtype" IS '违规类型.1--系统类型;2--配置策略类;3--应用类型';

CREATE TABLE "uemx"."t_violations_device"
(
"id" BIGINT NOT NULL,
"violationid" BIGINT NOT NULL,
"deviceid" BIGINT NOT NULL,
"relationships" VARCHAR(2000) NOT NULL,
"tenantId" VARCHAR(256) NOT NULL,
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT ON TABLE "uemx"."t_violations_device" IS '违规策略分发设备表';

COMMENT ON COLUMN "uemx"."t_violations_device"."deviceid" IS '设备id';

COMMENT ON COLUMN "uemx"."t_violations_device"."relationships" IS '设备所属关系 1 企业 2 员工 3 其他 ';

COMMENT ON COLUMN "uemx"."t_violations_device"."tenantId" IS '租户ID';

COMMENT ON COLUMN "uemx"."t_violations_device"."violationid" IS '违规 id';

CREATE OR REPLACE  INDEX "uemx"."INDEX1818090281650300" ON "uemx"."t_violations_device"("violationid" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE OR REPLACE  INDEX "uemx"."INDEX1818090301051100" ON "uemx"."t_violations_device"("deviceid" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE TABLE "uemx"."t_violations_group"
(
"id" BIGINT NOT NULL,
"violationid" BIGINT NOT NULL,
"groupid" BIGINT NOT NULL,
"relationships" VARCHAR(2000) NOT NULL,
"tenantId" VARCHAR(256) NOT NULL,
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT ON TABLE "uemx"."t_violations_group" IS '违规策略分发用户组表';

COMMENT ON COLUMN "uemx"."t_violations_group"."groupid" IS '用户组id';

COMMENT ON COLUMN "uemx"."t_violations_group"."relationships" IS '设备所属关系 1 企业 2 员工 3 其他 ';

COMMENT ON COLUMN "uemx"."t_violations_group"."tenantId" IS '租户ID';

COMMENT ON COLUMN "uemx"."t_violations_group"."violationid" IS '违规 id';

CREATE OR REPLACE  INDEX "uemx"."INDEX1818090347493100" ON "uemx"."t_violations_group"("groupid" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE OR REPLACE  INDEX "uemx"."INDEX1818090323022400" ON "uemx"."t_violations_group"("violationid" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE TABLE "uemx"."t_violations_user"
(
"id" BIGINT NOT NULL,
"violationid" BIGINT NOT NULL,
"userid" BIGINT NOT NULL,
"relationships" VARCHAR(2000) NOT NULL,
"tenantId" VARCHAR(256) NOT NULL,
NOT CLUSTER PRIMARY KEY("id")) STORAGE(ON "MAIN", CLUSTERBTR);

COMMENT ON TABLE "uemx"."t_violations_user" IS '违规策略分发用户表';

COMMENT ON COLUMN "uemx"."t_violations_user"."relationships" IS '设备所属关系 1 企业 2 员工 3 其他 ';

COMMENT ON COLUMN "uemx"."t_violations_user"."tenantId" IS '租户ID';

COMMENT ON COLUMN "uemx"."t_violations_user"."userid" IS '用户id';

COMMENT ON COLUMN "uemx"."t_violations_user"."violationid" IS '违规 id';

CREATE OR REPLACE  INDEX "uemx"."INDEX1818090396592300" ON "uemx"."t_violations_user"("violationid" ASC) STORAGE(ON "MAIN", CLUSTERBTR);

CREATE OR REPLACE  INDEX "uemx"."userid" ON "uemx"."t_violations_user"("userid" ASC) STORAGE(ON "MAIN", CLUSTERBTR);


INSERT INTO "uemx"."t_certificate"("id","name","type","filename","content","config","createTime","updateTime")
VALUES(1,'服务器证书','SERVER_CERT','SCEPRootCA.pem','-----BEGIN CERTIFICATE-----
MIIDnTCCAoWgAwIBAgIJAOFv8yGRi7zUMA0GCSqGSIb3DQEBCwUAMD0xCzAJBgNV
BAoMAk5RMQwwCgYDVQQLDANEZXYxCzAJBgNVBAYTAkNOMRMwEQYDVQQDDApJbnRl
cm5hbENBMB4XDTE5MTAxNzEwNDEzMVoXDTI5MTAxNjEwNDEzMVowPTELMAkGA1UE
CgwCTlExDDAKBgNVBAsMA0RldjELMAkGA1UEBhMCQ04xEzARBgNVBAMMCkludGVy
bmFsQ0EwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQDN6VFQPrgXNVlE
lQRgPNH5uYiBtc6Z3TCn4VOyxGRbnkdj6d+0CCVyNp9g30EzT+aG4B5UlwB4fWg+
wJ12Ix1j7fYjf4g64o4+8q0tgc0lxn4qgCemnFPk7zUa0IbIVCOSyxj6qgYR9wh1
+sgSULWLv7hjp9x7h6ppTs6oX2UxBres6BQdenxm+h1BY6FSw0QTUeUnCDV2uIYk
3vPEIBFQ057jJICEkdOC2hJRpbdKkDqN6EwwruCgPBZoZUscowFjONPDV79YgO6N
vEYnSgCxyE35JthZnCp5g6d3Y+9spSEYdlglw1ZNNX1AHpf+xOSX3jJZcvRPkVyN
sy3yPEFVAgMBAAGjgZ8wgZwwHQYDVR0OBBYEFFEudTI+cQcycDnWKVTK9XSwX1Wq
MG0GA1UdIwRmMGSAFFEudTI+cQcycDnWKVTK9XSwX1WqoUGkPzA9MQswCQYDVQQK
DAJOUTEMMAoGA1UECwwDRGV2MQswCQYDVQQGEwJDTjETMBEGA1UEAwwKSW50ZXJu
YWxDQYIJAOFv8yGRi7zUMAwGA1UdEwQFMAMBAf8wDQYJKoZIhvcNAQELBQADggEB
AC7wUQNMp5L/KbU1JIlveqCkwjtzG8+jv1EkNg+OcmMHjbA9nrMG+viBZJ3krYGr
dNHp8ADtuxZkc4tAhvY/G/QjlDV4Y71Zh8+faCKaNWBg2JEEKUbCXJuZxq3dcUmd
bfjbjiFw9guxKFhUwQiMuwj9EXFzy2IagSKvGghi+kYrVyump2CMA2C8N7u4BaZo
PKQEmxUxWj8Cia549+rrcprurL/FogxfJji3qW4zfCt8LrDnW+n3O/4xAKfk1k3M
vgqDtHlbujuiHZTJeA3LeWXH0XVbHxBXrJuLYJgUee2s0TkUxtRk1bdEK0brVft7
ahH1NrQLF2DD+d842v9sbR4=
-----END CERTIFICATE-----',null,'2020-02-10 15:53:34.0','2020-02-10 15:53:36.0');
INSERT INTO "uemx"."t_certificate"("id","name","type","filename","content","config","createTime","updateTime")
VALUES(2,'客户端证书','CLIENT_CERT','mdmclient.cert','MIIJkQIBAzCCCVcGCSqGSIb3DQEHAaCCCUgEgglEMIIJQDCCA/cGCSqGSIb3DQEHBqCCA+gwggPk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','{"password":"Nsky@0!9"}','2020-02-07 14:39:04.0','2020-02-07 14:39:04.0');
INSERT INTO "uemx"."t_certificate"("id","name","type","filename","content","config","createTime","updateTime")
VALUES(3,'签名者证书','SIGNER_CERT','digitalsee.cn.cert','-----BEGIN CERTIFICATE-----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-----END CERTIFICATE-----',null,'2020-02-10 15:53:34.0','2020-02-10 15:53:36.0');
INSERT INTO "uemx"."t_certificate"("id","name","type","filename","content","config","createTime","updateTime")
VALUES(4,'签名者私钥','SIGNER_KEY','digitalsee.cn.pkcs8.key','***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',null,'2020-02-10 15:54:27.0','2020-02-10 15:54:30.0');
INSERT INTO "uemx"."t_certificate"("id","name","type","filename","content","config","createTime","updateTime")
VALUES(5,'签名者根证书','SIGNER_ROOT_CA','SCEPRootCA.pem','-----BEGIN CERTIFICATE-----
MIIEizCCA3OgAwIBAgIQBUb+GCP34ZQdo5/OFMRhczANBgkqhkiG9w0BAQsFADBh
MQswCQYDVQQGEwJVUzEVMBMGA1UEChMMRGlnaUNlcnQgSW5jMRkwFwYDVQQLExB3
d3cuZGlnaWNlcnQuY29tMSAwHgYDVQQDExdEaWdpQ2VydCBHbG9iYWwgUm9vdCBD
QTAeFw0xNzExMDYxMjIzNDVaFw0yNzExMDYxMjIzNDVaMF4xCzAJBgNVBAYTAlVT
MRUwEwYDVQQKEwxEaWdpQ2VydCBJbmMxGTAXBgNVBAsTEHd3dy5kaWdpY2VydC5j
b20xHTAbBgNVBAMTFEdlb1RydXN0IFJTQSBDQSAyMDE4MIIBIjANBgkqhkiG9w0B
AQEFAAOCAQ8AMIIBCgKCAQEAv4rRY03hGOqHXegWPI9/tr6HFzekDPgxP59FVEAh
150Hm8oDI0q9m+2FAmM/n4W57Cjv8oYi2/hNVEHFtEJ/zzMXAQ6CkFLTxzSkwaEB
2jKgQK0fWeQz/KDDlqxobNPomXOMJhB3y7c/OTLo0lko7geG4gk7hfiqafapa59Y
rXLIW4dmrgjgdPstU0Nigz2PhUwRl9we/FAwuIMIMl5cXMThdSBK66XWdS3cLX18
4ND+fHWhTkAChJrZDVouoKzzNYoq6tZaWmyOLKv23v14RyZ5eqoi6qnmcRID0/i6
U9J5nL1krPYbY7tNjzgC+PBXXcWqJVoMXcUw/iBTGWzpwwIDAQABo4IBQDCCATww
HQYDVR0OBBYEFJBY/7CcdahRVHex7fKjQxY4nmzFMB8GA1UdIwQYMBaAFAPeUDVW
0Uy7ZvCj4hsbw5eyPdFVMA4GA1UdDwEB/wQEAwIBhjAdBgNVHSUEFjAUBggrBgEF
BQcDAQYIKwYBBQUHAwIwEgYDVR0TAQH/BAgwBgEB/wIBADA0BggrBgEFBQcBAQQo
MCYwJAYIKwYBBQUHMAGGGGh0dHA6Ly9vY3NwLmRpZ2ljZXJ0LmNvbTBCBgNVHR8E
OzA5MDegNaAzhjFodHRwOi8vY3JsMy5kaWdpY2VydC5jb20vRGlnaUNlcnRHbG9i
YWxSb290Q0EuY3JsMD0GA1UdIAQ2MDQwMgYEVR0gADAqMCgGCCsGAQUFBwIBFhxo
dHRwczovL3d3dy5kaWdpY2VydC5jb20vQ1BTMA0GCSqGSIb3DQEBCwUAA4IBAQAw
8YdVPYQI/C5earp80s3VLOO+AtpdiXft9OlWwJLwKlUtRfccKj8QW/Pp4b7h6QAl
ufejwQMb455OjpIbCZVS+awY/R8pAYsXCnM09GcSVe4ivMswyoCZP/vPEn/LPRhH
hdgUPk8MlD979RGoUWz7qGAwqJChi28uRds3thx+vRZZIbEyZ62No0tJPzsSGSz8
nQ//jP8BIwrzBAUH5WcBAbmvgWfrKcuv+PyGPqRcc4T55TlzrBnzAzZ3oClo9fTv
O9PuiHMKrC6V6mgi0s2sa/gbXlPCD9Z24XUMxJElwIVTDuKB0Q4YMMlnpN/QChJ4
B0AFsQ+DU0NCO+f78Xf7
-----END CERTIFICATE-----',null,'2020-02-10 16:08:57.0','2020-02-10 16:08:59.0');

INSERT INTO "uemx"."t_device_profile"("id","deviceid","udid","policyid","createtime","updatetime","status","fencestatus","tenantId")
VALUES(1864269172738318338,1864256648640638978,'7a119a6f1908d57763d012097f8f2c9c02ec5f95                                                                                                                                                                                                                        ','061add4f-213f-4a6d-b60d-96ecf209fef4','2024-12-04 19:42:23.0',null,0,0,'bit');

INSERT INTO "uemx"."t_mdm_device_extend"("id","buytime","warranty","relationship","enableperm","devicename","devicenumber","initsiminfo","initsdserialnum","iccid","phonecode","romcapacity","romavailablecapacity","sdcapacity","sdavailablecapacity","sdserialnum","powerstatus","boottime","capacity","capacitystatus","availablecapacity","ip","encryptionlevel","token","pushmagic","unlocktoken","sendregistnoticeflag","allowactrootorjailbreakflag","remark","roamingflag","updatetime","apnstoken","lostflag","dataencryptionflag","specialtypeflag","knoxstatus","offlinestatus","offlinethreshold","emmloginagain","hostid","usagestats","flowupdate","flowquota","bitlocker","meid","resolution","ossoftwareversion","safetyosversion","patchlevel","iccid2","imsi2","networktype","wlanadapterchip","btadapterchip","nfcchip","locatorchip","clientbuildnum","cpuratio","memoryratio","storageratio","bluetoothstate","wifistate","camerastate","microphonestate","mobiledatastate","apn","buildnumber","gpsstate","syncflag","dualstatus","simoper","tpmreport","systemintegrity","idp","idpdeviceid","offlineTime","enablepermissionguide","tenantId")
VALUES(1864256648640638978,null,null,1,1,null,null,null,null,null,'+8613681520453/','51.49GB','29.99GB',null,null,null,'99',6688,null,null,null,null,null,null,null,null,0,0,null,202,'2024-12-04 18:33:21.0',null,0,0,0,0,0,'0',0,null,0,0,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,1,1,1,1,1,null,'PKQ1.190118.001',1,0,null,null,null,null,null,null,null,1,'bit');
INSERT INTO "uemx"."t_mdm_device_extend"("id","buytime","warranty","relationship","enableperm","devicename","devicenumber","initsiminfo","initsdserialnum","iccid","phonecode","romcapacity","romavailablecapacity","sdcapacity","sdavailablecapacity","sdserialnum","powerstatus","boottime","capacity","capacitystatus","availablecapacity","ip","encryptionlevel","token","pushmagic","unlocktoken","sendregistnoticeflag","allowactrootorjailbreakflag","remark","roamingflag","updatetime","apnstoken","lostflag","dataencryptionflag","specialtypeflag","knoxstatus","offlinestatus","offlinethreshold","emmloginagain","hostid","usagestats","flowupdate","flowquota","bitlocker","meid","resolution","ossoftwareversion","safetyosversion","patchlevel","iccid2","imsi2","networktype","wlanadapterchip","btadapterchip","nfcchip","locatorchip","clientbuildnum","cpuratio","memoryratio","storageratio","bluetoothstate","wifistate","camerastate","microphonestate","mobiledatastate","apn","buildnumber","gpsstate","syncflag","dualstatus","simoper","tpmreport","systemintegrity","idp","idpdeviceid","offlineTime","enablepermissionguide","tenantId")
VALUES(1864277934351757314,null,null,1,1,null,null,null,null,null,null,'466GB','387GB',null,null,null,'100',1290055,null,null,null,null,null,null,null,null,0,0,null,12,'2024-12-04 20:21:59.0',null,0,0,0,0,0,'0',0,null,0,0,0,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,'UKQ1.240523.001 release-keys',null,0,null,null,null,null,null,null,null,1,'bit');

INSERT INTO "uemx"."t_sys_user"("loginid","password","name","telephone","email","description","flag","parentid","needchangepwd","syncconnectorid","syncbatchno","syncuid","guardids","tenantId")
VALUES('admin','1bc2ae89ada7eee62d9c149ea744f7d8','admin',null,null,null,10,null,0,null,null,null,null,'uep1');

INSERT INTO "uemx"."t_violate_process_dict"("processid","title")
VALUES(-1,'violation.process.dict.noOper');
INSERT INTO "uemx"."t_violate_process_dict"("processid","title")
VALUES(1,'violation.process.dict.lock.device');
INSERT INTO "uemx"."t_violate_process_dict"("processid","title")
VALUES(2,'violation.process.dict.company.wipe');
INSERT INTO "uemx"."t_violate_process_dict"("processid","title")
VALUES(3,'violation.process.dict.all.wipe');
INSERT INTO "uemx"."t_violate_process_dict"("processid","title")
VALUES(8,'violation.process.dict.forbid.appstore');
INSERT INTO "uemx"."t_violate_process_dict"("processid","title")
VALUES(9,'violation.process.dict.forbid.mcm');
INSERT INTO "uemx"."t_violate_process_dict"("processid","title")
VALUES(10,'violation.process.dict.forbid.sd');
INSERT INTO "uemx"."t_violate_process_dict"("processid","title")
VALUES(11,'violation.process.dict.forbid.camera');
INSERT INTO "uemx"."t_violate_process_dict"("processid","title")
VALUES(12,'violation.process.dict.forbid.mdm');
INSERT INTO "uemx"."t_violate_process_dict"("processid","title")
VALUES(13,'violation.process.dict.forbid.sag');
INSERT INTO "uemx"."t_violate_process_dict"("processid","title")
VALUES(14,'violation.process.dict.forbid.mobile.data');
INSERT INTO "uemx"."t_violate_process_dict"("processid","title")
VALUES(15,'violation.process.dict.lock.screen');
INSERT INTO "uemx"."t_violate_process_dict"("processid","title")
VALUES(16,'violation.process.dict.close.device');
INSERT INTO "uemx"."t_violate_process_dict"("processid","title")
VALUES(17,'violation.process.dict.forbid.wrappingApp');

INSERT INTO "uemx"."t_violation_cond_dict"("condid","violationtype","title")
VALUES(101,1,'violation.cond.dict.sys.os.version.low');
INSERT INTO "uemx"."t_violation_cond_dict"("condid","violationtype","title")
VALUES(102,1,'violation.cond.dict.sys.jailbreak.root');
INSERT INTO "uemx"."t_violation_cond_dict"("condid","violationtype","title")
VALUES(103,1,'violation.cond.dict.sys.data.no.cipher');
INSERT INTO "uemx"."t_violation_cond_dict"("condid","violationtype","title")
VALUES(104,1,'violation.cond.dict.sys.sim.modify');
INSERT INTO "uemx"."t_violation_cond_dict"("condid","violationtype","title")
VALUES(105,1,'violation.cond.dict.sys.sd.modify');
INSERT INTO "uemx"."t_violation_cond_dict"("condid","violationtype","title")
VALUES(106,1,'violation.cond.dict.sys.strategy.not.update');
INSERT INTO "uemx"."t_violation_cond_dict"("condid","violationtype","title")
VALUES(107,1,'violation.cond.dict.sys.not.flowStatistics');
INSERT INTO "uemx"."t_violation_cond_dict"("condid","violationtype","title")
VALUES(108,1,'violation.cond.dict.sys.flow.threshold');
INSERT INTO "uemx"."t_violation_cond_dict"("condid","violationtype","title")
VALUES(109,1,'violation.cond.dict.sys.completeness');
INSERT INTO "uemx"."t_violation_cond_dict"("condid","violationtype","title")
VALUES(201,2,'violation.cond.dict.conf.not.effect');
INSERT INTO "uemx"."t_violation_cond_dict"("condid","violationtype","title")
VALUES(301,3,'violation.cond.dict.app.blacklist');
INSERT INTO "uemx"."t_violation_cond_dict"("condid","violationtype","title")
VALUES(302,3,'violation.cond.dict.app.whitelist');
INSERT INTO "uemx"."t_violation_cond_dict"("condid","violationtype","title")
VALUES(303,3,'violation.cond.dict.app.force');
INSERT INTO "uemx"."t_violation_cond_dict"("condid","violationtype","title")
VALUES(401,1,'violation.cond.dict.sys.device.offline');

