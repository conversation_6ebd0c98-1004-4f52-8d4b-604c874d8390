-- 临时增加t_sys_user表，用于admin登录mock管理平台
--
-- Table structure for table `t_sys_user`
--

DROP TABLE IF EXISTS `t_sys_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_sys_user` (
  `loginid` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(50) DEFAULT ' ' COMMENT '密码',
  `name` varchar(50) NOT NULL COMMENT '真实姓名',
  `telephone` varchar(20) DEFAULT NULL COMMENT '电话',
  `email` varchar(50) DEFAULT NULL COMMENT '电子信箱',
  `description` varchar(200) DEFAULT NULL COMMENT '描述',
  `flag` int(11) DEFAULT NULL COMMENT '系统用户标识,10--超级管理员,20--管理员,21--业务管理员 11--审核员',
  `parentid` varchar(50) DEFAULT NULL COMMENT '父管理员id',
  `needchangepwd` int(11) DEFAULT '0' COMMENT '登录后是否需要修改密码依据 0-否 1-是',
  `syncconnectorid` bigint(20) DEFAULT NULL COMMENT 't_connector表中connector id',
  `syncbatchno` bigint(20) DEFAULT NULL COMMENT 'connector同步批次号',
  `syncuid` varchar(100) DEFAULT NULL COMMENT '同步connector的第三方账户uid',
  `guardids` varchar(200) DEFAULT NULL COMMENT '可显示门禁权限',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`loginid`,`tenantId`) USING BTREE,
  KEY `loginid_index` (`loginid`),
  KEY `parentid_index` (`parentid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='系统用户表';
/*!40101 SET character_set_client = @saved_cs_client */;

-- 初始化管理员账号admin，仅用于mock管理平台
insert into t_sys_user (description, syncuid, parentid, guardids, loginid, tenantId, needchangepwd, password, syncconnectorid, flag, syncbatchno, telephone, email, name)
values ( null, null, null, null, 'admin', 'uep1', '0', '1bc2ae89ada7eee62d9c149ea744f7d8', null, '10', null, null, null, 'admin');