<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cyberscraft.uep.mdm.core.dao.UserDao">

    <select id="validUserWhenNotSuperAdmin"
            resultType="String">
        select concat(a.id,'') from
        t_user a, t_sys_user_group b,t_group g1, t_group g2
        where a.groupId = g1.id and b.groupId = g2.id and g1.status = 1 and g2.status = 1
        and g1.groupCode like concat(g2.groupCode, '%')
        and b.loginId = #{loginId}
        and a.status != -1
        and ( a.id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
        )
    </select>

    <update id="updateStatusByIds">
        update t_device
        set status=#{status,jdbcType=INTEGER}
        where id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="updateUserGroupIdByGroupId">
        update t_device
        set groupId = #{newGroupId,jdbcType=INTEGER}
        where groupId = #{groupId,jdbcType=INTEGER}
    </update>

</mapper>
