<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cyberscraft.uep.mdm.core.dao.GroupDao">

    <select id="countGroupChildNodeNums"
            resultType="com.cyberscraft.uep.mdm.core.domain.group.GroupChildNodeNumCountItem">
        select parentid,count(*) as num from t_group where parentId in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by parentid;
    </select>

</mapper>
