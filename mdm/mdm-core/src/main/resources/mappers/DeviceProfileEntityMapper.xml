<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cyberscraft.uep.mdm.core.dao.DeviceProfileDao">

    <select id="countDevicePolicyNums" resultType="com.cyberscraft.uep.mdm.core.domain.device.DevicePolicyCountItem">
        select count(*) as count,
        deviceId,
        status,
        type
        from(
            select pro.deviceId,pro.status,
            policy.type
            from t_device_profile pro,t_policy policy
            where pro.policyid=policy.id
            and pro.deviceId in
            <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
                #{item}
            </foreach>
        ) a group by deviceId,status,type;
    </select>
</mapper>
