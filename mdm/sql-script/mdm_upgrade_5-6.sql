DROP TABLE IF EXISTS `t_mdm_device`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_mdm_device` (
        `id` bigint(20) NOT NULL AUTO_INCREMENT,
        `userid` bigint(20) DEFAULT NULL COMMENT '所属用户用户id',
        `shortUdid` varchar(10) DEFAULT NULL COMMENT 'unique device short identifier',
        `udid` char(64) DEFAULT NULL COMMENT 'unique device identifier',
        `deviceType` int(11) NOT NULL DEFAULT '10' COMMENT '设备类型 10-android phone 11-android pad;20-iphone 21-ipad 22-ipadmini 23- ipodtouch',
        `type` int(11) NOT NULL DEFAULT '1' COMMENT '平台1--android;2--ios',
        `rootflag` int(11) DEFAULT '0' COMMENT '是否破解root, 0--未破解 ;1--破解 ',
        `lastonlinetime` datetime DEFAULT NULL COMMENT '最后在线时间',
        `onlineflag` int(11) DEFAULT '0' COMMENT '设备是否在线 0--在线;1--不在线',
        `status` int(11) DEFAULT '1' COMMENT '设备状态,0--未注册,1--注册未激活,2--激活可用, 3--保留 4--擦除',
        `registtime` datetime DEFAULT NULL COMMENT '注册时间',
        `activatetime` datetime DEFAULT NULL COMMENT '激活时间',
        `updatetime` datetime DEFAULT NULL COMMENT '设备信息更新时间',
        `devicename` varchar(100) DEFAULT NULL COMMENT '设备名称',
        `userName` varchar(150) DEFAULT NULL COMMENT '用户名',
        `loginId` varchar(50) DEFAULT NULL COMMENT '用户登录名',
        `openUserId` varchar(50) DEFAULT NULL COMMENT '第三方平台用户Id',
        `groupId` bigint(20) DEFAULT NULL COMMENT '所属用户组Id',
        `dudid` varchar(64) comment '设备的系统UDID值,设备MDM激活后得到的设备唯一ID,原来的UDID为应用生成的UMID',
        `activateMdmTime` datetime comment '设备的MDM激活时间',
        `mdmStatus` int(11) DEFAULT 0 comment '设备的MDM激活状态',
        `dualstatus` int(11) DEFAULT NULL COMMENT '双域状态（0-工作域 1-生活域）',
        `uemVersion` varchar(50) DEFAULT NULL COMMENT 'UEM版本号',
        `clientversion` varchar(30) DEFAULT '' COMMENT 'EMM 客户端版本号',
        `ip` varchar(20) DEFAULT NULL COMMENT 'ip地址',
        `clientbuildnum` varchar(50) DEFAULT NULL COMMENT 'EMM客户端Build号',
        `tenantId` varchar(64) NOT NULL COMMENT '租户ID',

        `idfa` char(64) DEFAULT NULL COMMENT 'unique device identifier(IOS7)',
        `imei` varchar(50) DEFAULT NULL COMMENT 'imei:international mobile equipment identity',
        `imsi` varchar(50) DEFAULT NULL COMMENT 'imsi:international mobile subscriber identity',
        `serialnum` varchar(50) DEFAULT NULL COMMENT '设备序列号',
        `manufacturer` varchar(200) DEFAULT NULL COMMENT '设备厂商',
        `model` varchar(100) DEFAULT NULL COMMENT '设备型号,例如：MC319LL',
        `os` varchar(100) DEFAULT NULL COMMENT '操作系统平台',
        `versionNum` varchar(100) DEFAULT NULL COMMENT ' 版本号',
        `oscoreversion` varchar(50) DEFAULT NULL COMMENT '系统内核版本号',
        `cpu` varchar(100) DEFAULT NULL,
        `ram` varchar(100) DEFAULT NULL,
        `camera` varchar(100) DEFAULT NULL COMMENT '摄像头',
        `wifimac` varchar(30) DEFAULT NULL COMMENT 'wifi mac地址',
        `bluetoothmac` varchar(30) DEFAULT NULL COMMENT '蓝牙 mac地址',
        `productname` varchar(100) DEFAULT NULL COMMENT '设备型号码 ,例如：iPhone3,1',


        PRIMARY KEY (`id`),
        KEY `t_mdm_evice_idx_1` (`userid`,`tenantId`,`type`,`deviceType`,`udid`,`status`,`groupId`,`loginId`,`userName`,`openUserId`),
        KEY `t_mdm_device_idx_2` (`type`,`tenantId`,`status`,`onlineflag`,`lastonlinetime`),
        KEY `t_mdm_device_idx_3` (`udid`,`tenantId`,`userid`,`status`),
        KEY `t_mdm_device_idx_4` (`groupId`,`tenantId`,`type`,`devicetype`,`status`,`userid`,`loginId`,`userName`,`openUserId`),
        KEY `t_mdm_device_idx_5` (`loginId`,`tenantId`,`type`,`devicetype`,`status`,`groupId`,`userName`,`openUserId`),
        KEY `t_mdm_device_idx_6` (`dudid`,`tenantId`,`status`,`mdmStatus`,`activateMdmTime`,`groupId`,`userId`,`loginId`)
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8 COMMENT='设备信息表';
/*!40101 SET character_set_client = @saved_cs_client */;




DROP TABLE IF EXISTS `t_mdm_device_extend`;
/*!40101 SET @saved_cs_client = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_mdm_device_extend` (
       `id` bigint(20) NOT NULL AUTO_INCREMENT,
       `buytime` date DEFAULT NULL COMMENT '购买时间',
       `warranty` int(11) DEFAULT NULL COMMENT '保修期',
       `relationship` int(11) NOT NULL DEFAULT '1' COMMENT '设备所属关系 1--公司设备;2--员工设备;3--其他',
       `enableperm` int(11) DEFAULT '1' COMMENT '是否启用权限配置引导，1 -- 启用；0 -- 不启用',
       `devicename` varchar(100) DEFAULT NULL COMMENT '设备名称',
       `devicenumber` varchar(50) DEFAULT NULL COMMENT '设备编号',
       `initsiminfo` varchar(50) DEFAULT NULL COMMENT '激活时SIM卡信息，Android设备为IMSI，iOS设备为ICCID',
       `initsdserialnum` varchar(100) DEFAULT NULL COMMENT '激活时的SD卡序列号',
       `iccid` varchar(50) DEFAULT NULL,
       `phonecode` varchar(100) DEFAULT NULL COMMENT '手机号码',
       `romcapacity` varchar(100) DEFAULT NULL COMMENT 'rom 总容量',
       `romavailablecapacity` varchar(100) DEFAULT NULL COMMENT 'rom 可用空间',
       `sdcapacity` varchar(100) DEFAULT NULL COMMENT 'sd卡容量',
       `sdavailablecapacity` varchar(100) DEFAULT NULL COMMENT 'sd卡可用空间',
       `sdserialnum` varchar(100) DEFAULT NULL COMMENT 'sd卡序列号',
       `powerstatus` varchar(100) DEFAULT NULL COMMENT '电源状态',
       `boottime` bigint(20) DEFAULT NULL COMMENT '开机时长',
       `capacity` varchar(100) DEFAULT NULL COMMENT '存储容量(ios)',
       `capacitystatus` varchar(100) DEFAULT NULL COMMENT '存储容量状态(ios)',
       `availablecapacity` varchar(100) DEFAULT NULL COMMENT '可用空间(ios)',
       `ip` varchar(20) DEFAULT NULL COMMENT 'ip地址',
       `encryptionlevel` int(11) DEFAULT NULL COMMENT '加密级别',
       `token` varchar(200) DEFAULT NULL COMMENT 'ios mdm发送唤醒使用',
       `pushmagic` varchar(200) DEFAULT NULL COMMENT 'ios mdm发送唤醒使用',
       `unlocktoken` varchar(4000) DEFAULT NULL COMMENT 'ios mdm解除锁屏使用',
       `sendregistnoticeflag` int(11) DEFAULT '0' COMMENT '是否发送注册通知信息,0--否;1--是',
       `allowactrootorjailbreakflag` int(11) DEFAULT '0' COMMENT '是否允许root/越狱设备激活,0--否;1--是',
       `remark` varchar(500) DEFAULT NULL COMMENT '备注',
       `roamingflag` int(11) DEFAULT '0' COMMENT '设备漫游开关 0--否;1--是',
       `updatetime` datetime DEFAULT NULL COMMENT '设备信息更新时间',
       `apnstoken` varchar(200) DEFAULT NULL COMMENT 'apns的设备token',
       `lostflag` int(11) DEFAULT '0' COMMENT '是否丢失：0 --未丢失，1 --丢失',
       `dataencryptionflag` int(11) DEFAULT '0' COMMENT '是否开启数据加密.0--否;1--是',
       `specialtypeflag` int(11) DEFAULT '0' COMMENT '特殊设备类型标识.0--普通设备;100--Samsung safe设备;101--Samsung KNOX设备;301--华为设备',
       `knoxstatus` int(11) DEFAULT '0' COMMENT 'knox状态,0--不支持;201--支持未注入;202--SAFE license已注入;203--KNOX license已注入;101--已创建;501--锁定;301--华为插件已激活;302--移除华为插件',
       `offlinestatus` int(11) NOT NULL DEFAULT '0' COMMENT '设备是否失联：0未失联，1失联',
       `offlinethreshold` varchar(5) NOT NULL DEFAULT '0' COMMENT '设备失联违规天数',
       `emmloginagain` int(11) DEFAULT '0' COMMENT '自服务是否修改密码 0-未修改 1-已修改',
       `hostid` varchar(50) DEFAULT NULL COMMENT 'hostId proxy激活时的hostId',
       `usagestats` int(2) DEFAULT '0' COMMENT '应用使用量统计状态 0 未启用，1 启用',
       `flowupdate` int(2) DEFAULT '0' COMMENT '流量是否有更新 0 未更新，1 有更新',
       `flowquota` int(10) DEFAULT '0' COMMENT '流量配额',
       `bitlocker` int(11) DEFAULT NULL COMMENT 'BitLocker加密状态 0:未加密 1:整个硬盘加密 2:系统分区加密',
       `meid` varchar(50) DEFAULT NULL COMMENT '副卡对应的设备识别码,(如无副卡,可为空)',
       `resolution` varchar(50) DEFAULT NULL COMMENT '屏幕分辨率,(字符串,长*高,如640*480)',
       `ossoftwareversion` varchar(50) DEFAULT NULL COMMENT '系统软件版本号',
       `safetyosversion` varchar(50) DEFAULT NULL COMMENT '安全加固双操作系统版本,(如无,可为空)',
       `patchlevel` varchar(50) DEFAULT NULL COMMENT '系统安全补丁程序级别(如无,可为空)',
       `iccid2` varchar(50) DEFAULT NULL COMMENT 'ICCID(SIM卡2的ICCID)',
       `imsi2` varchar(50) DEFAULT NULL COMMENT 'IMSI(SIM卡2的IMSI,如无卡2，可为空)',
       `networktype` varchar(50) DEFAULT NULL COMMENT '支持的移动网络制式',
       `wlanadapterchip` varchar(50) DEFAULT NULL COMMENT '无线网卡芯片型号',
       `btadapterchip` varchar(50) DEFAULT NULL COMMENT '蓝牙芯片型号',
       `nfcchip` varchar(50) DEFAULT NULL COMMENT 'NFC芯片型号',
       `locatorchip` varchar(50) DEFAULT NULL COMMENT '定位芯片型号',
       `clientbuildnum` varchar(50) DEFAULT NULL COMMENT 'EMM客户端Build号',
       `cpuratio` varchar(30) DEFAULT NULL COMMENT 'CPU占用率',
       `memoryratio` varchar(30) DEFAULT NULL COMMENT '内存占用率',
       `storageratio` varchar(30) DEFAULT NULL COMMENT '存储占用率',
       `bluetoothstate` int(11) DEFAULT NULL COMMENT '蓝牙状态，1-开启,0 -关闭',
       `wifistate` int(11) DEFAULT NULL COMMENT 'wifi状态，1-开启 0-关闭',
       `camerastate` int(11) DEFAULT NULL COMMENT '相机状态，1-开启 0-关闭',
       `microphonestate` int(11) DEFAULT NULL COMMENT '麦克风状态，1-开启 0-关闭',
       `mobiledatastate` int(11) DEFAULT NULL COMMENT '移动数据状态，1-开启 0-关闭',
       `apn` varchar(500) DEFAULT NULL COMMENT 'apn集合，status:0 未连接 ，1连接',
       `buildnumber` varchar(200) DEFAULT NULL COMMENT '内核版本',
       `gpsstate` int(11) DEFAULT NULL COMMENT 'GPS状态：0-关闭 1-开启',
       `syncflag` int(11) DEFAULT '0' COMMENT '是否上报成功 1-上报绑定成功 10-上报绑定失败 2-上报解绑成功 20-上报解绑失败',
       `dualstatus` int(11) DEFAULT NULL COMMENT '双域状态（0-工作域 1-生活域）',
       `simoper` int(11) DEFAULT NULL COMMENT 'sim卡操作 1-更新sim卡 0-撤销更新sim卡',
       `tpmreport` int(11) DEFAULT NULL COMMENT '可信度 2-不安全 1-安全',
       `systemintegrity` int(11) DEFAULT NULL COMMENT '系统完整性 2-不完整 1-完整',
       `idp` varchar(20) DEFAULT NULL COMMENT '第三方idp名称，idp_device_id对应',
       `idpdeviceid` varchar(100) DEFAULT NULL COMMENT 'device_id对应的idp',
       `offlineTime` timestamp NULL DEFAULT NULL COMMENT '失联时间',
       `enablepermissionguide` int(11) DEFAULT '1' COMMENT '强管控权限向导，1 -- 强管控；0 -- 弱管控',

       `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
                                       PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8 COMMENT='设备扩展信息表';