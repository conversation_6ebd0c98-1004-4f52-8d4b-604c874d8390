-- MySQL dump 10.13  Distrib 5.7.25, for macos10.14 (x86_64)
--
-- Host: localhost    Database: mdm_tenant
-- ------------------------------------------------------
-- Server version	5.7.25

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `t_accessweb_restriction`
--

DROP TABLE IF EXISTS `t_accessweb_restriction`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_accessweb_restriction` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `policyid` varchar(100) DEFAULT NULL,
  `userid` bigint(20) NOT NULL,
  `loginid` varchar(50) NOT NULL DEFAULT '',
  `deviceid` bigint(20) NOT NULL,
  `username` varchar(100) NOT NULL DEFAULT '',
  `devicename` varchar(100) NOT NULL DEFAULT '',
  `policyname` varchar(100) DEFAULT NULL,
  `groupid` bigint(20) NOT NULL,
  `relationship` int(11) NOT NULL,
  `url` varchar(2000) NOT NULL DEFAULT '',
  `title` varchar(150) DEFAULT NULL,
  `accesstime` datetime NOT NULL,
  `groupname` varchar(100) DEFAULT NULL COMMENT '组名称',
  `appname` varchar(100) DEFAULT NULL COMMENT '应用名称',
  `hostname` varchar(100) DEFAULT NULL,
  `updatestatus` int(11) NOT NULL COMMENT '更新状态(0：未更新，1：已更新)',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_admin_list_custom`
--

DROP TABLE IF EXISTS `t_admin_list_custom`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_admin_list_custom` (
  `id` varchar(50) NOT NULL COMMENT 'uuid',
  `loginid` varchar(50) NOT NULL COMMENT '用户名',
  `listid` varchar(50) NOT NULL COMMENT '列表id',
  `fieldname` varchar(50) NOT NULL COMMENT '字段名',
  `priorityno` int(11) NOT NULL COMMENT '优先级序号',
  `displayflag` int(11) NOT NULL COMMENT '是否显示标识,0---否;1---是',
  `orderbyflag` int(11) DEFAULT '0' COMMENT '是否排序标识,0--否;1---是',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `loginid` (`loginid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='管理平台自定义列表字段表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_admin_list_default`
--

DROP TABLE IF EXISTS `t_admin_list_default`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_admin_list_default` (
  `id` varchar(50) NOT NULL COMMENT 'uuid',
  `listid` varchar(50) NOT NULL COMMENT '列表id',
  `fieldname` varchar(50) NOT NULL COMMENT '字段名',
  `priorityno` int(11) NOT NULL COMMENT '优先级序号',
  `displayflag` int(11) NOT NULL COMMENT '是否显示标识,0---否;1---是',
  `orderbyflag` int(11) DEFAULT '0' COMMENT '是否排序标识,0--否;1---是',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='管理平台默认列表字段表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_api_dict`
--

DROP TABLE IF EXISTS `t_api_dict`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_api_dict` (
  `code` varchar(50) NOT NULL COMMENT '功能码',
  `descr` varchar(100) DEFAULT NULL COMMENT '描述',
  `url` varchar(200) DEFAULT NULL,
  `method` varchar(50) DEFAULT NULL COMMENT 'url请求类型：POST PUT DELETE GET *',
  `matchrule` int(11) DEFAULT '0' COMMENT '匹配规则 0-全部匹配 1-开始匹配 2-正则匹配',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_api_token`
--

DROP TABLE IF EXISTS `t_api_token`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_api_token` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `loginid` varchar(50) DEFAULT NULL,
  `accesstoken` varchar(50) DEFAULT NULL,
  `expiredate` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_app_category`
--

DROP TABLE IF EXISTS `t_app_category`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_app_category` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(200) DEFAULT NULL COMMENT '分类名称',
  `description` varchar(200) DEFAULT NULL COMMENT '分类描述',
  `parentid` int(10) unsigned DEFAULT NULL COMMENT '父节点',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `updatetime` datetime DEFAULT NULL COMMENT '修改时间',
  `type` int(10) unsigned DEFAULT '0' COMMENT '分类标识: 0 内部分类 1 外部分类 2 内置 默认0',
  `loginid` varchar(50) DEFAULT NULL COMMENT '管理员id',
  `sort` int(11) DEFAULT '999' COMMENT '排序',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COMMENT='应用分类表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_app_download_log`
--

DROP TABLE IF EXISTS `t_app_download_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_app_download_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `appid` bigint(20) NOT NULL COMMENT '应用id',
  `name` varchar(200) NOT NULL COMMENT '应用名称',
  `deviceid` bigint(20) DEFAULT NULL COMMENT '设备id',
  `downloadtime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '下载时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `idx_download_log` (`downloadtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='记录应用的下载日志';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_app_push`
--

DROP TABLE IF EXISTS `t_app_push`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_app_push` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '推送id',
  `appstoreid` bigint(20) NOT NULL COMMENT '应用id',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  `appconf` varchar(2000) DEFAULT NULL COMMENT '应用配置',
  `loginid` varchar(50) DEFAULT NULL COMMENT '用户id',
  `distributemode` int(11) DEFAULT '0' COMMENT '推送模式字段（必装-0、按需-1）默认0',
  `devicerelationship` varchar(50) DEFAULT NULL COMMENT '设备所属关系（企业-1、员工-2、其他-3、全部-0）默认全部',
  `delapp` int(11) DEFAULT '0' COMMENT '是否在淘汰设备时删除该应用(不删除-0、删除-1)默认不删除',
  `wifi` int(11) DEFAULT '0' COMMENT '只在wifi情况下下载，0:所有情况下载  默认0',
  `starttime` datetime DEFAULT NULL COMMENT '开始检测时间',
  `distributetype` int(11) DEFAULT '1' COMMENT '分发类型: 0 推送安装包 1 推送消息 2 不推送 ',
  `essentialapp` int(11) DEFAULT '0' COMMENT '是否开启合规检测 (是-1 否-0) 默认0',
  `preventbackup` int(11) DEFAULT '0' COMMENT '是否防止应用程序备份(是-1、否-0)默认否',
  `isinputknox` int(11) DEFAULT '0' COMMENT '是否放入KNOX容器内 (0：否 1：是 默认否)',
  `appconfid` bigint(20) DEFAULT NULL COMMENT '应用配置ID',
  `isuemcontainer` int(11) DEFAULT '0' COMMENT '是否放入UEM容器,为了升级考虑设置默认值为0',
  `showicononsysdesktop` int(11) DEFAULT '0' COMMENT '是否在系统桌面显示图标',
  `preventuninstall` int(11) DEFAULT '0' COMMENT '应用防卸载，1-开启 0-关闭',
  `allowdemotion` int(11) DEFAULT '0' COMMENT '是否允许降级，1-允许 0-不允许',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='应用推送';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_app_push_device`
--

DROP TABLE IF EXISTS `t_app_push_device`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_app_push_device` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `deviceId` bigint(20) DEFAULT NULL COMMENT '设备id',
  `appStoreId` bigint(20) DEFAULT NULL COMMENT '应用id',
  `essentialApp` int(11) DEFAULT '0' COMMENT '是否系统客户端应用：必装应用： 1 必装 默认 0',
  `appPushId` bigint(20) DEFAULT NULL COMMENT '应用推送ID',
  `customflag` int(11) DEFAULT NULL COMMENT '自定义标识: 0-继承 ;1-自定义',
  `exceptionflag` int(11) DEFAULT NULL COMMENT '例外标识: 0-添加 ;1-排除',
  `appconf` varchar(255) DEFAULT NULL COMMENT '应用配置',
  `createtime` varchar(255) DEFAULT NULL,
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='应用推送分发设备流水表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_app_push_user`
--

DROP TABLE IF EXISTS `t_app_push_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_app_push_user` (
  `id` varchar(100) NOT NULL DEFAULT '' COMMENT 'uuid',
  `userid` bigint(20) DEFAULT NULL COMMENT '用户id',
  `appstoreid` bigint(20) DEFAULT NULL COMMENT '应用id',
  `essentialapp` int(11) DEFAULT '0' COMMENT '是否系统客户端应用：必装应用： 1 必装 默认 0',
  `apppushid` bigint(20) DEFAULT NULL COMMENT '应用推送ID',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='应用推送分发用户流水表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_app_push_user_group`
--

DROP TABLE IF EXISTS `t_app_push_user_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_app_push_user_group` (
  `id` varchar(100) NOT NULL DEFAULT '' COMMENT 'uuid',
  `groupid` bigint(20) DEFAULT NULL COMMENT '用户组',
  `appstoreid` bigint(20) DEFAULT NULL COMMENT '应用id',
  `apppushid` bigint(20) DEFAULT NULL COMMENT '应用推送ID',
  `essentialapp` int(11) DEFAULT '0' COMMENT '是否系统客户端应用：必装应用： 1 必装 默认 0',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='应用推送分发用户组流水表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_app_schedule`
--

DROP TABLE IF EXISTS `t_app_schedule`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_app_schedule` (
  `id` varchar(50) NOT NULL COMMENT 'uuid',
  `appid` bigint(20) NOT NULL COMMENT '应用id',
  `userids` varchar(2000) DEFAULT NULL COMMENT '用户ids',
  `groupids` varchar(2000) DEFAULT NULL COMMENT '用户组ids',
  `jobname` varchar(100) NOT NULL COMMENT 'job name',
  `jobgroup` varchar(100) NOT NULL COMMENT 'job group',
  `triggername` varchar(100) NOT NULL COMMENT 'trigger name',
  `triggergroup` varchar(100) NOT NULL COMMENT 'trigger group',
  `cronexpression` varchar(200) NOT NULL COMMENT 'cron表达式',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `appid` (`appid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='应用定时分发排程表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_app_store`
--

DROP TABLE IF EXISTS `t_app_store`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_app_store` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(200) NOT NULL COMMENT '名称',
  `appuuid` varchar(36) DEFAULT NULL COMMENT '应用唯一标识',
  `pkgname` varchar(200) DEFAULT NULL COMMENT '应用id',
  `version` varchar(50) DEFAULT NULL COMMENT '版本',
  `icon` varchar(200) DEFAULT NULL COMMENT '图标',
  `price` varchar(50) DEFAULT NULL COMMENT '价格',
  `appsize` int(10) DEFAULT NULL COMMENT '大小',
  `brief` varchar(2000) DEFAULT NULL COMMENT '软件简介',
  `company` varchar(200) DEFAULT NULL COMMENT '开发商',
  `itunesstoreid` bigint(20) DEFAULT NULL COMMENT '苹果应用商店应用id',
  `platform` int(11) NOT NULL DEFAULT '1' COMMENT '适合平台  0：表示其它，1：表示android, 2:表示ios，默认是1 ',
  `source` varchar(1000) DEFAULT NULL COMMENT '应用安装包（上传.ipa或.apk文件）',
  `downloadcount` int(11) DEFAULT '0' COMMENT '下载量',
  `distributetype` int(11) DEFAULT '1' COMMENT '分发类型: 0 推送安装包 1 推送消息 2 不推送 ',
  `type` int(11) NOT NULL DEFAULT '1' COMMENT '应用分类: 1 企业 2 外部 默认1',
  `status` int(10) NOT NULL DEFAULT '0' COMMENT '是否可用: 0 可用 1 不可用 0 默认',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  `wraptime` datetime DEFAULT NULL COMMENT 'wrapping开始时间',
  `updatetime` datetime NOT NULL COMMENT '更新时间',
  `osversion` varchar(200) DEFAULT NULL COMMENT '要求系统版本',
  `loginid` varchar(50) DEFAULT NULL COMMENT '用户id',
  `modeltype` varchar(20) DEFAULT '20,21,22,23' COMMENT 'MDM4.0.1开始设备型号允许多选,20-iphone 21-ipad 22-ipad mini 23-itouch',
  `description` varchar(2000) DEFAULT NULL COMMENT '描述',
  `customversion` varchar(50) DEFAULT NULL COMMENT '自定义版本',
  `distributemode` int(11) DEFAULT '0' COMMENT '推送模式字段（自动-0、按需-1）默认0',
  `devicerelationship` varchar(50) DEFAULT NULL COMMENT '设备所属关系（企业-1、员工-2、其他-3、全部-0）默认全部',
  `delapp` int(11) DEFAULT '0' COMMENT '是否在淘汰设备时删除该应用(不删除-0、删除-1)默认不删除',
  `preventbackup` int(11) DEFAULT '0' COMMENT '是否防止应用程序备份(是-1、否-0)默认否',
  `wifi` int(11) DEFAULT '0' COMMENT '只在wifi情况下下载，0:所有情况下载  默认0',
  `checkflag` int(11) DEFAULT NULL COMMENT '是否定时检查(0 否 1 是)',
  `starttime` varchar(50) DEFAULT NULL COMMENT '开始检测时间',
  `checkperiod` int(11) DEFAULT NULL COMMENT '检测周期(单位分钟)',
  `flag` int(11) DEFAULT '1' COMMENT '是否系统客户端应用：0否 1是 默认0',
  `clienttype` int(11) DEFAULT '0' COMMENT '客户端类型： 1 mcm 2 mdm 3 mem 4安全浏览器',
  `filename` varchar(200) DEFAULT NULL COMMENT '文件名',
  `appconf` varchar(2000) DEFAULT NULL COMMENT '应用配置',
  `urlscheme` varchar(255) DEFAULT NULL COMMENT '用于IOS判断应用是否已经安装',
  `versioncode` varchar(20) DEFAULT NULL COMMENT '应用版本编号',
  `domaintype` int(11) DEFAULT '0' COMMENT '应用类型（普通应用-0 双域应用-1 沙箱应用-2 内置应用-3）默认普通应用',
  `facetid` varchar(200) DEFAULT NULL COMMENT '上传应用时，ids生成的facetid',
  `uuWrapper` int(11) DEFAULT NULL,
  `filehash` varchar(32) DEFAULT NULL COMMENT '文件md5',
  `showWrapperIcon` int(11) NOT NULL DEFAULT '1' COMMENT '是否显示沙箱应用图标 0否   1是 默认1',
  `screenshots` text COMMENT '应用详情中的预览图',
  `launchcount` int(11) unsigned DEFAULT '0' COMMENT '应用版本使用量',
  `setupcount` int(11) unsigned DEFAULT '0' COMMENT '应用版本安装量',
  `appsourcetype` int(11) NOT NULL DEFAULT '0' COMMENT '应用市场类型: 0 非google Play应用 1 google Play应用',
  `fromAppId` bigint(20) DEFAULT NULL COMMENT '关联AppId，用于wrapping的原包',
  `wrapjson` varchar(2000) DEFAULT NULL COMMENT 'wrapping应用json指令',
  `demotionappid` bigint(20) DEFAULT NULL COMMENT '降级关联的appId',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `index_unique_pkg_ver_plat` (`pkgname`,`version`,`platform`,`domaintype`,`tenantId`) USING BTREE,
  UNIQUE KEY `index_unique_pkg_vercode_plat` (`pkgname`,`versioncode`,`platform`,`domaintype`,`tenantId`) USING BTREE,
  KEY `appuuid_index` (`appuuid`),
  KEY `fk_t_app_store_loginid` (`loginid`),
  KEY `pkgname_index` (`pkgname`),
  KEY `platform_index` (`platform`),
  KEY `type_index` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='应用商店';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_app_store_appinfo`
--

DROP TABLE IF EXISTS `t_app_store_appinfo`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_app_store_appinfo` (
  `id` bigint(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `app_version` varchar(255) DEFAULT NULL COMMENT '应用版本',
  `app_name` varchar(255) DEFAULT NULL COMMENT '应用名称',
  `app_package` varchar(255) DEFAULT NULL COMMENT '应用包名',
  `app_icon` varchar(255) DEFAULT NULL COMMENT '应用icon',
  `app_size` int(11) DEFAULT NULL COMMENT '应用大小',
  `app_type` varchar(255) DEFAULT NULL COMMENT '应用类型',
  `app_from` varchar(255) DEFAULT NULL COMMENT '应用来源',
  `app_url` varchar(2000) DEFAULT NULL COMMENT '应用下载地址',
  `app_desc` varchar(255) DEFAULT NULL COMMENT '应用描述',
  `platform` varchar(128) DEFAULT '' COMMENT '应用平台',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4409 DEFAULT CHARSET=utf8 COMMENT='应用信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_app_store_category`
--

DROP TABLE IF EXISTS `t_app_store_category`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_app_store_category` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `appstoreid` bigint(20) NOT NULL COMMENT '应用商店id',
  `appuuid` varchar(36) DEFAULT NULL COMMENT '应用唯一标识',
  `appcategoryid` int(11) NOT NULL COMMENT '应用分类id',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `appstoreid` (`appstoreid`,`appcategoryid`),
  UNIQUE KEY `index_unique_appstoreid_appcategoryid` (`appstoreid`,`appcategoryid`),
  UNIQUE KEY `categoryappuuid` (`appuuid`,`appcategoryid`),
  KEY `appcategoryid` (`appcategoryid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='应用商店分类表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_app_store_conf`
--

DROP TABLE IF EXISTS `t_app_store_conf`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_app_store_conf` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '配置id',
  `name` varchar(200) NOT NULL COMMENT '名称',
  `description` varchar(2000) DEFAULT NULL COMMENT '描述',
  `loginid` varchar(50) DEFAULT NULL COMMENT '用户id',
  `type` int(11) NOT NULL COMMENT '1--普通策略;2--wrapping策略;3--安全桌面;4--安全容器;5--安全浏览器;6--杀毒;7--远程控制;',
  `appconf` text COMMENT '应用配置-推送时使用',
  `jsonconf` text COMMENT '应用配置-编辑时使用',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  `updatetime` datetime NOT NULL COMMENT '更新时间',
  `copynum` int(11) DEFAULT '1' COMMENT '应用策略复制编号',
  `flag` int(11) DEFAULT '0' COMMENT '是否是默认应用策略：0否 1是 默认0',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8 COMMENT='应用配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_app_store_del`
--

DROP TABLE IF EXISTS `t_app_store_del`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_app_store_del` (
  `id` bigint(20) NOT NULL,
  `name` varchar(200) NOT NULL COMMENT '名称',
  `appuuid` varchar(36) DEFAULT NULL COMMENT '应用唯一标识',
  `pkgname` varchar(200) DEFAULT NULL COMMENT '应用id',
  `version` varchar(50) DEFAULT NULL COMMENT '版本',
  `icon` varchar(200) DEFAULT NULL COMMENT '图标',
  `price` varchar(50) DEFAULT NULL COMMENT '价格',
  `appsize` int(10) DEFAULT NULL COMMENT '大小',
  `brief` varchar(2000) DEFAULT NULL COMMENT '软件简介',
  `company` varchar(200) DEFAULT NULL COMMENT '开发商',
  `itunesstoreid` bigint(20) DEFAULT NULL COMMENT '苹果应用商店应用id',
  `platform` int(11) NOT NULL DEFAULT '1' COMMENT '适合平台  0：表示其它，1：表示android, 2:表示ios，默认是1 ',
  `source` varchar(1000) DEFAULT NULL COMMENT '应用安装包（上传.ipa或.apk文件）',
  `downloadcount` int(11) DEFAULT '0' COMMENT '下载量',
  `distributetype` int(11) DEFAULT '1' COMMENT '分发类型: 0 推送安装包 1 推送消息 2 不推送 ',
  `type` int(11) NOT NULL DEFAULT '1' COMMENT '应用分类: 1 企业 2 外部 默认1',
  `status` int(10) NOT NULL DEFAULT '0' COMMENT '是否可用: 0 可用 1 不可用 0 默认',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  `updatetime` datetime NOT NULL COMMENT '更新时间',
  `osversion` varchar(200) DEFAULT NULL COMMENT '要求系统版本',
  `loginid` varchar(50) DEFAULT NULL COMMENT '用户id',
  `modeltype` varchar(20) DEFAULT '20,21,22,23' COMMENT 'MDM4.0.1开始设备型号允许多选,20-iphone 21-ipad 22-ipad mini 23-itouch',
  `description` varchar(2000) DEFAULT NULL COMMENT '描述',
  `customversion` varchar(50) DEFAULT NULL COMMENT '自定义版本',
  `distributemode` int(11) DEFAULT '0' COMMENT '推送模式字段（自动-0、按需-1）默认0',
  `devicerelationship` varchar(50) DEFAULT NULL COMMENT '设备所属关系（企业-1、员工-2、其他-3、全部-0）默认全部',
  `delapp` int(11) DEFAULT '0' COMMENT '是否在淘汰设备时删除该应用(不删除-0、删除-1)默认不删除',
  `preventbackup` int(11) DEFAULT '0' COMMENT '是否防止应用程序备份(是-1、否-0)默认否',
  `wifi` int(11) DEFAULT '0' COMMENT '只在wifi情况下下载，0:所有情况下载  默认0',
  `checkflag` int(11) DEFAULT NULL COMMENT '是否定时检查(0 否 1 是)',
  `starttime` varchar(50) DEFAULT NULL COMMENT '开始检测时间',
  `checkperiod` int(11) DEFAULT NULL COMMENT '检测周期(单位分钟)',
  `flag` int(11) DEFAULT '1' COMMENT '是否系统客户端应用：0否 1是 默认0',
  `clienttype` int(11) DEFAULT '0' COMMENT '客户端类型： 1 mcm 2 mdm 3 mem 4安全浏览器',
  `filename` varchar(200) DEFAULT NULL COMMENT '文件名',
  `appconf` varchar(2000) DEFAULT NULL COMMENT '应用配置',
  `urlscheme` varchar(255) DEFAULT NULL COMMENT '用于IOS判断应用是否已经安装',
  `versioncode` varchar(20) DEFAULT NULL COMMENT '应用版本编号',
  `domaintype` int(11) DEFAULT '0' COMMENT '应用类型（普通应用-0 双域应用-1 沙箱应用-2 内置应用-3）默认普通应用',
  `facetid` varchar(200) DEFAULT NULL COMMENT '上传应用时，ids生成的facetid',
  `uuWrapper` int(11) DEFAULT NULL,
  `filehash` varchar(32) DEFAULT NULL COMMENT '文件md5',
  `showWrapperIcon` int(11) NOT NULL DEFAULT '1' COMMENT '是否显示沙箱应用图标 0否   1是 默认1',
  `screenshots` text COMMENT '应用详情中的预览图',
  `launchcount` int(11) unsigned DEFAULT '0' COMMENT '应用版本使用量',
  `setupcount` int(11) unsigned DEFAULT '0' COMMENT '应用版本安装量',
  `appsourcetype` int(11) NOT NULL DEFAULT '0' COMMENT '应用市场类型: 0 非google Play应用 1 google Play应用',
  `fromAppId` bigint(20) DEFAULT NULL COMMENT '关联AppId，用于wrapping的原包',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='应用商店逻辑删除记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_app_store_device`
--

DROP TABLE IF EXISTS `t_app_store_device`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_app_store_device` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'uuid',
  `deviceid` bigint(20) NOT NULL COMMENT '设备id',
  `appstoreid` bigint(20) NOT NULL COMMENT '应用id',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `updatetime` datetime DEFAULT NULL COMMENT '更新时间',
  `status` int(4) DEFAULT NULL COMMENT '1:未分发，2:已分发，3:正在下载，4:下载失败，5:等待安装，6:正在安装，7:已安装，8:用户拒绝，9:用户卸载/已删除，10:安装失败，11:更新',
  `safecontainerstatus` int(4) DEFAULT '1' COMMENT '安全容器的状态  1：启用，0：禁用；默认为1',
  `appConfStatus` int(4) DEFAULT '0' COMMENT '应用配置执行状态,0--未生效;1--已生效',
  `enabled` int(4) DEFAULT '1' COMMENT '1 启用 2 禁用 默认启用',
  `essentialapp` int(4) DEFAULT '0' COMMENT '是否系统客户端应用：必装应用： 1 必装 默认 0',
  `apppushid` bigint(20) DEFAULT NULL COMMENT '应用推送ID',
  `appconf` varchar(2000) DEFAULT NULL COMMENT '应用配置',
  `isinputknox` int(11) DEFAULT '0' COMMENT '是否放入KNOX容器内 (0：否 1：是 默认否)',
  `isuemcontainer` int(11) DEFAULT '0' COMMENT '是否放入UEM容器',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `appstoreid` (`appstoreid`),
  KEY `deviceid` (`deviceid`),
  KEY `idx_t_app_store_device_status` (`status`),
  KEY `idx_t_app_store_device_apppushid` (`apppushid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='应用分发设备关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_app_store_user`
--

DROP TABLE IF EXISTS `t_app_store_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_app_store_user` (
  `id` varchar(100) NOT NULL DEFAULT '' COMMENT 'uuid',
  `userid` bigint(20) DEFAULT NULL COMMENT '用户id',
  `appstoreid` bigint(20) DEFAULT NULL COMMENT '应用id',
  `customflag` int(11) DEFAULT '0' COMMENT '自定义标识: 0-继承 ;1-自定义',
  `exceptionflag` int(11) DEFAULT '0' COMMENT '例外标识: 0-添加 ;1-排除',
  `essentialapp` int(11) DEFAULT '0' COMMENT '是否系统客户端应用：必装应用： 1 必装 默认 0',
  `apppushid` bigint(20) DEFAULT NULL COMMENT '应用推送ID',
  `appconf` varchar(2000) DEFAULT NULL COMMENT '应用配置',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `appstoreid` (`appstoreid`),
  KEY `userid` (`userid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='应用分发用户关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_app_store_user_group`
--

DROP TABLE IF EXISTS `t_app_store_user_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_app_store_user_group` (
  `id` varchar(100) NOT NULL DEFAULT '' COMMENT 'uuid',
  `groupid` bigint(20) DEFAULT NULL COMMENT '用户组',
  `appstoreid` bigint(20) DEFAULT NULL COMMENT '应用id',
  `customflag` int(11) NOT NULL DEFAULT '0' COMMENT '自定义标识: 0-继承 ;1-自定义',
  `essentialapp` int(11) DEFAULT '0' COMMENT '是否系统客户端应用：必装应用： 1 必装 默认 0',
  `apppushid` bigint(20) DEFAULT NULL COMMENT '应用推送ID',
  `appconf` varchar(2000) DEFAULT NULL COMMENT '应用配置',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `appstoreid` (`appstoreid`),
  KEY `groupid` (`groupid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='应用分发用户组关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_app_strategy`
--

DROP TABLE IF EXISTS `t_app_strategy`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_app_strategy` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(200) DEFAULT NULL COMMENT '名称',
  `description` varchar(200) DEFAULT NULL COMMENT '描述',
  `type` int(11) NOT NULL COMMENT '1--白名单;2--黑名单',
  `platformtype` int(11) NOT NULL COMMENT '平台1--android;2--ios;3--windows8;4--windows phone8',
  `pushdevicetype` varchar(50) NOT NULL COMMENT '推送设备所属关系：0-全部；1-公司；2-员工；3-其他',
  `irrepushflag` int(11) NOT NULL COMMENT '违规推送标识：0-不推送；1-推送',
  `status` int(11) NOT NULL COMMENT '应用策略的状态标志：0-禁用；1-启用；默认禁用',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  `loginid` varchar(100) DEFAULT NULL COMMENT '创建人',
  `updatetime` datetime NOT NULL COMMENT '更新时间',
  `updateby` varchar(100) DEFAULT NULL COMMENT '修改人',
  `disttype` int(1) DEFAULT '0' COMMENT '默认为0 0-用户／用户组 1-标签',
  `copynum` int(11) DEFAULT '1' COMMENT '黑白名单复制编号',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='应用策略表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_app_strategy_device`
--

DROP TABLE IF EXISTS `t_app_strategy_device`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_app_strategy_device` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `deviceid` bigint(20) NOT NULL,
  `udid` char(64) DEFAULT NULL,
  `strategyid` bigint(20) DEFAULT NULL COMMENT '应用策略id',
  `createtime` datetime DEFAULT NULL COMMENT '下发时间',
  `updatetime` datetime DEFAULT NULL COMMENT '更新时间',
  `status` int(11) DEFAULT NULL COMMENT '执行状态,0--未生效;1--已生效',
  `strategypushid` bigint(20) DEFAULT NULL COMMENT '应用策略推送ID',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `deviceid` (`deviceid`),
  KEY `strategyid` (`strategyid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备与应用策略关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_app_strategy_group`
--

DROP TABLE IF EXISTS `t_app_strategy_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_app_strategy_group` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `strategyid` bigint(20) NOT NULL COMMENT '应用策略id',
  `groupid` bigint(20) NOT NULL COMMENT '用户组id',
  `customflag` int(11) DEFAULT NULL COMMENT '自定义标识: 0-继承 ;1-自定义',
  `strategypushid` bigint(20) DEFAULT NULL COMMENT '应用策略推送ID',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `groupid_index` (`groupid`),
  KEY `strategyid_index` (`strategyid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='应用策略用户组表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_app_strategy_irre_log`
--

DROP TABLE IF EXISTS `t_app_strategy_irre_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_app_strategy_irre_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `userid` bigint(20) NOT NULL COMMENT '用户id',
  `deviceid` bigint(20) NOT NULL COMMENT '设备id',
  `strategyid` bigint(20) NOT NULL COMMENT '应用策略id',
  `packagename` varchar(200) NOT NULL COMMENT '应用包名',
  `appname` varchar(200) DEFAULT NULL COMMENT '应用名称',
  `appversion` varchar(50) DEFAULT NULL COMMENT '应用版本',
  `createtime` timestamp NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `updatetime` timestamp NULL DEFAULT NULL,
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `userid` (`userid`),
  KEY `deviceid` (`deviceid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_app_strategy_label`
--

DROP TABLE IF EXISTS `t_app_strategy_label`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_app_strategy_label` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `strategyid` bigint(20) NOT NULL COMMENT '应用策略id',
  `labelid` bigint(20) NOT NULL COMMENT '标签id',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `strategyid` (`strategyid`),
  KEY `labelid` (`labelid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='应用策略标签表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_app_strategy_push`
--

DROP TABLE IF EXISTS `t_app_strategy_push`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_app_strategy_push` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '推送id',
  `appstrategyid` bigint(20) NOT NULL COMMENT '应用策略id',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  `pushdevicetype` varchar(50) NOT NULL COMMENT '推送设备所属关系：0-全部；1-公司；2-员工；3-其他',
  `irrepushflag` int(11) DEFAULT '1' COMMENT '违规推送标识：0-不推送；1-推送',
  `loginid` varchar(50) DEFAULT NULL COMMENT '用户id',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='应用策略推送';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_app_strategy_store`
--

DROP TABLE IF EXISTS `t_app_strategy_store`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_app_strategy_store` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `strategyid` bigint(20) NOT NULL COMMENT '应用策略id',
  `packagename` varchar(200) NOT NULL COMMENT '应用包名',
  `appname` varchar(200) NOT NULL COMMENT '名称',
  `appversion` varchar(50) DEFAULT NULL COMMENT '版本',
  `description` varchar(200) DEFAULT NULL COMMENT '描述',
  `ordernum` int(11) DEFAULT NULL,
  `icon` varchar(2000) DEFAULT NULL,
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `strategyid` (`strategyid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='应用商店应用策略关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_app_strategy_user`
--

DROP TABLE IF EXISTS `t_app_strategy_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_app_strategy_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `userid` bigint(20) DEFAULT NULL,
  `strategyid` bigint(20) DEFAULT NULL,
  `customflag` int(11) DEFAULT '0' COMMENT '自定义标识: 0-继承 ;1-自定义',
  `exceptionflag` int(11) DEFAULT '0' COMMENT '例外标识: 0-添加 ;1-排除',
  `strategypushid` bigint(20) DEFAULT NULL COMMENT '应用策略推送ID',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `userid_index` (`userid`),
  KEY `strategyid_index` (`strategyid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='应用策略用户表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_app_time_group`
--

DROP TABLE IF EXISTS `t_app_time_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_app_time_group` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `appid` bigint(20) NOT NULL COMMENT '应用id',
  `groupid` bigint(20) NOT NULL COMMENT '用户组id',
  `starttime` datetime DEFAULT NULL COMMENT '开始时间',
  `customflag` int(5) DEFAULT '0' COMMENT '自定义标识: 0-继承 ;1-自定义',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `appid` (`appid`),
  KEY `groupid` (`groupid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='应用定时下发的中间表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_app_traffic`
--

DROP TABLE IF EXISTS `t_app_traffic`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_app_traffic` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `loginid` varchar(50) NOT NULL COMMENT '用户帐号',
  `udid` char(64) NOT NULL COMMENT '设备udid',
  `endtime` datetime NOT NULL COMMENT '统计截止时间',
  `sim1num` bigint(20) DEFAULT '0' COMMENT 'sim1日流量',
  `sim2num` bigint(20) DEFAULT '0' COMMENT 'sim2日流量',
  `wifinum` bigint(20) DEFAULT '0' COMMENT 'wifi日流量',
  `apppackage` varchar(100) NOT NULL COMMENT 'app包名',
  `appname` varchar(200) NOT NULL COMMENT 'app名称',
  `username` varchar(50) DEFAULT NULL COMMENT '用户名称',
  `devicename` varchar(50) DEFAULT NULL COMMENT '设备名称',
  `groupid` bigint(20) DEFAULT NULL COMMENT '组ID',
  `relationship` int(11) DEFAULT NULL COMMENT '所属关系',
  `platformtype` int(11) DEFAULT NULL COMMENT '平台类型',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `idx_app_traffic_udid` (`udid`),
  KEY `idx_app_traffic_pkgname` (`apppackage`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_apple_dep_device`
--

DROP TABLE IF EXISTS `t_apple_dep_device`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_apple_dep_device` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `userid` bigint(20) DEFAULT NULL COMMENT '用户id',
  `uuid` varchar(50) DEFAULT NULL COMMENT 'UUID',
  `serialnumber` varchar(50) NOT NULL COMMENT '序列号',
  `devicefamily` varchar(20) DEFAULT NULL COMMENT '设备类型:iPad iPhone iPod Mac AppleTV',
  `assettag` varchar(100) DEFAULT NULL COMMENT '资产编号:当从苹果购买设备时才有',
  `model` varchar(100) DEFAULT NULL COMMENT '型号',
  `os` varchar(100) DEFAULT NULL COMMENT '操作系统',
  `color` varchar(20) DEFAULT NULL COMMENT '颜色',
  `description` varchar(200) DEFAULT NULL COMMENT '描述',
  `status` int(11) DEFAULT '1' COMMENT '设备状态: -1-删除;1-有效;',
  `profileuuid` varchar(50) DEFAULT NULL COMMENT '策略id',
  `profileassigntime` datetime DEFAULT NULL COMMENT '策略创建时间',
  `profilepushtime` datetime DEFAULT NULL COMMENT '策略推送时间',
  `profilestatus` varchar(20) DEFAULT NULL COMMENT '策略状态:empty assigned pushed removed',
  `deviceassignedby` varchar(100) DEFAULT NULL COMMENT '设备所有人',
  `deviceassigneddate` datetime DEFAULT NULL COMMENT '设备注册时间',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `updatetime` datetime DEFAULT NULL COMMENT '更新时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `index_unique_key` (`serialnumber`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='apple Dep 设备表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_apple_dep_profile`
--

DROP TABLE IF EXISTS `t_apple_dep_profile`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_apple_dep_profile` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `uuid` varchar(40) NOT NULL COMMENT '策略uuid',
  `description` varchar(50) DEFAULT NULL COMMENT '描述',
  `profilename` varchar(100) NOT NULL COMMENT '策略名称',
  `url` varchar(1000) NOT NULL COMMENT 'MDM服务器URL',
  `supportphonenumber` varchar(50) DEFAULT NULL COMMENT '联系电话',
  `supportemailaddress` varchar(200) DEFAULT NULL COMMENT '邮件地址',
  `orgmagic` varchar(200) DEFAULT NULL COMMENT '组织标识',
  `department` varchar(100) DEFAULT NULL COMMENT '部门名称',
  `language` varchar(15) DEFAULT NULL COMMENT 'tvOS语言',
  `region` varchar(15) DEFAULT NULL COMMENT 'tvOS国家码',
  `anchorcerts` varchar(1024) DEFAULT NULL COMMENT '受信证书文件名',
  `supervisinghostcerts` varchar(1024) DEFAULT NULL COMMENT '监管模式匹配证书文件名',
  `skipsetupitems` varchar(1000) DEFAULT NULL COMMENT '开机跳过设置项',
  `allowpairing` int(11) NOT NULL DEFAULT '1' COMMENT '证书匹配 0-不匹配 1-匹配',
  `issupervised` int(11) NOT NULL DEFAULT '1' COMMENT '是否开启监管模式 0-不开启 1-开启',
  `ismultiuser` int(11) NOT NULL DEFAULT '0' COMMENT '只对Apple School Manager生效,用户设备共享 0-不共享 1-共享',
  `ismandatory` int(11) NOT NULL DEFAULT '0' COMMENT '策略强制生效 0-非强制 1-强制',
  `awaitdeviceconfigured` int(11) NOT NULL DEFAULT '0' COMMENT '监管模式下有效 0-不等待MDM配置确认 1-等待MDM配置确认',
  `ismdmremovable` int(11) NOT NULL DEFAULT '1' COMMENT '策略是否可移除 0-不可移除 1-可移除',
  `autoadvancesetup` int(11) NOT NULL DEFAULT '0' COMMENT 'tvOS自动配置选项 0-不开启 1-开启',
  `loginId` varchar(50) NOT NULL COMMENT '创建人',
  `status` int(11) NOT NULL DEFAULT '1' COMMENT '策略状态 0-无效 1-有效',
  `creator` varchar(200) DEFAULT NULL COMMENT '创建人姓名',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  `updatetime` datetime DEFAULT NULL COMMENT '更新时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `idx_uuid` (`uuid`),
  KEY `idx_profile_name` (`profilename`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='apple Dep 策略表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_apple_device_profile`
--

DROP TABLE IF EXISTS `t_apple_device_profile`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_apple_device_profile` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `serialnumber` varchar(50) NOT NULL COMMENT 'dep设备序列号',
  `depprofileuuid` varchar(40) NOT NULL COMMENT '策略uuid',
  `createtime` datetime DEFAULT NULL COMMENT '分配时间',
  `updatetime` datetime DEFAULT NULL COMMENT '更新时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `index_unique_serial_number` (`serialnumber`),
  KEY `idx_profile_uuid` (`depprofileuuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='apple Dep 设备-策略表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_client`
--

DROP TABLE IF EXISTS `t_client`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_client` (
  `id` varchar(100) NOT NULL COMMENT '主键',
  `hostId` varchar(100) NOT NULL COMMENT 'client注册时发送过来的Host Id',
  `appId` varchar(200) NOT NULL COMMENT 'client对应的APP ID',
  `tenantId` varchar(100) NOT NULL COMMENT 'client注册时发送过来的Tenant Id',
  `appVersion` varchar(50) DEFAULT NULL COMMENT 'client对应的APP版本',
  `platform` varchar(100) DEFAULT NULL COMMENT 'client对应的平台，例如Android或者iOS',
  `userId` bigint(20) NOT NULL COMMENT 'client所属user的id',
  `status` smallint(6) NOT NULL COMMENT '状态 1 - INSTALLED, 2 - ACTIVE, 3 - SUSPENDED, 0 - UNINSTALLED',
  `token` varchar(500) DEFAULT NULL COMMENT 'client注册后生成的token',
  `tokenMD5` varchar(200) DEFAULT NULL COMMENT 'client注册后生成的token md5值',
  `pnsToken` varchar(500) DEFAULT NULL COMMENT 'client订阅时发送过来的pnsToken',
  `pnsTokenType` varchar(50) DEFAULT NULL COMMENT 'client订阅时发送过来的pnsToken类型',
  `createdDatetime` datetime NOT NULL COMMENT '创建时间',
  `updatedDatetime` datetime NOT NULL COMMENT '修改时间',
  `udid` varchar(64) DEFAULT NULL COMMENT 'unique device identifier',
  PRIMARY KEY (`id`),
  UNIQUE KEY `client_hostId_appId_tenantId` (`hostId`,`appId`,`tenantId`),
  KEY `client_user_id` (`userId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='client表, 用于存储client在BeMail Console中的注册信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_client_layout`
--

DROP TABLE IF EXISTS `t_client_layout`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_client_layout` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) DEFAULT NULL COMMENT '模版名称',
  `type` varchar(50) DEFAULT NULL COMMENT '模版类型，可取值：portal，workbench, apprecommend',
  `comments` varchar(500) DEFAULT NULL COMMENT '模版备注',
  `template` longtext COMMENT '模版内容',
  `builtin` int(11) DEFAULT NULL COMMENT '是否为内建模板，1：是；0：否',
  `distributed` int(11) DEFAULT NULL COMMENT '是否下发，0：未下发；1：已下发',
  `createdby` varchar(100) DEFAULT NULL COMMENT '创建者',
  `createtime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updatetime` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8 COMMENT='客户端布局模板表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_client_layout_group`
--

DROP TABLE IF EXISTS `t_client_layout_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_client_layout_group` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `layoutid` bigint(20) DEFAULT NULL COMMENT '布局的id',
  `groupid` bigint(20) DEFAULT NULL COMMENT '组id',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_client_policy`
--

DROP TABLE IF EXISTS `t_client_policy`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_client_policy` (
  `id` varchar(100) NOT NULL COMMENT '主键',
  `clientId` varchar(100) NOT NULL COMMENT '对应的client id',
  `policyId` varchar(100) NOT NULL COMMENT '对应的policy id',
  `status` smallint(6) NOT NULL COMMENT '状态 0 - 等待下发, 1 - 下发中, 2 - 已下发, 3 - 已启用, 4 - 启用失败',
  `distributeDatetime` datetime NOT NULL COMMENT '下发时间',
  `tryTimes` smallint(6) NOT NULL COMMENT '已经尝试下发的次数',
  `changeTime` bigint(20) NOT NULL COMMENT 'client下发的policy变更的时间戳',
  `createdDatetime` datetime NOT NULL COMMENT '创建时间',
  `updatedDatetime` datetime NOT NULL COMMENT '修改时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `client_policy__clientId` (`clientId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='client_policy, 用于存储client被下发的policy信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_command_log`
--

DROP TABLE IF EXISTS `t_command_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_command_log` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `logtime` varchar(12) DEFAULT NULL COMMENT '日志时间',
  `num` bigint(11) DEFAULT NULL COMMENT '指令总和',
  `createtime` datetime DEFAULT NULL,
  `updatetime` datetime DEFAULT NULL,
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `logtime` (`logtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='指令发送量日志';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_compliance_schedule`
--

DROP TABLE IF EXISTS `t_compliance_schedule`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_compliance_schedule` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `tasklevel` int(4) NOT NULL COMMENT '任务级别',
  `taskcode` varchar(50) NOT NULL COMMENT '任务编号',
  `platformtype` int(11) NOT NULL COMMENT '平台 0:default 1:android 2:ios 3: windows8 4: windowsphone8',
  `udid` char(64) NOT NULL COMMENT '设备udid',
  `deviceid` bigint(20) NOT NULL COMMENT '设备id',
  `violationid` bigint(20) NOT NULL COMMENT '违规id',
  `condid` int(11) NOT NULL COMMENT '违规 条件字典表id',
  `procstrategyid` bigint(20) DEFAULT NULL COMMENT '违规 处理策略id',
  `processid` int(11) DEFAULT NULL COMMENT '违规 处理字典表id',
  `graceperiod` int(11) DEFAULT NULL COMMENT '违规处理宽限时间(分钟)',
  `packagenames` varchar(500) DEFAULT NULL COMMENT '违规报名',
  `deviceprocid` bigint(20) DEFAULT NULL COMMENT 'violationdeviceprocid',
  `procuuid` varchar(50) DEFAULT NULL COMMENT 'violationdeviceproc.uuid',
  `policyid` varchar(100) DEFAULT NULL COMMENT '配置策略id',
  `actioncode` varchar(50) NOT NULL COMMENT '动作编码号',
  `measurecode` varchar(50) NOT NULL DEFAULT '' COMMENT '措施编码号',
  `jobname` varchar(50) NOT NULL COMMENT '工作名称',
  `jobgroup` varchar(50) NOT NULL COMMENT '工作组',
  `triggername` varchar(50) NOT NULL COMMENT '触发器名称',
  `triggergroup` varchar(50) NOT NULL COMMENT '触发器组',
  `cronexpression` varchar(100) NOT NULL COMMENT '时间表达式',
  `taskstatus` char(1) NOT NULL DEFAULT '1' COMMENT '任务状态:1-未执行，2-已执行, 3-已合规',
  `reserved1` varchar(100) DEFAULT NULL COMMENT '保留字段1',
  `reserved2` varchar(100) DEFAULT NULL COMMENT '保留字段2',
  `remark` varchar(100) DEFAULT NULL COMMENT '描述',
  `createdate` datetime NOT NULL COMMENT '创建时间',
  `modifydate` datetime NOT NULL COMMENT '修改时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='违规计划任务表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_config`
--

DROP TABLE IF EXISTS `t_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `k` varchar(255) NOT NULL COMMENT 'config key',
  `v` varchar(255) NOT NULL COMMENT 'config value',
  `description` varchar(255) DEFAULT NULL COMMENT 'short description for this config item.',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `k` (`k`,`tenantId`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8 COMMENT='config table';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_connector`
--

DROP TABLE IF EXISTS `t_connector`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_connector` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT 'connector name',
  `type` varchar(20) NOT NULL COMMENT 'wework or dingding',
  `config` text DEFAULT NULL COMMENT '配置信息，json格式',
  `overrideorignore` varchar(100) DEFAULT NULL COMMENT '导入冲突处理方式 忽略或者覆盖',
  `autosyncinterval` int(5) DEFAULT '0' COMMENT '自动同步周期（小时） 0-不自动同步',
  `mountgroupid` bigint(20) DEFAULT '0' COMMENT '导入到的目标组id',
  `lastsyncstarttime` datetime DEFAULT NULL COMMENT '上次同步开始时间',
  `lastsyncendtime` datetime DEFAULT NULL COMMENT '上次同步结束时间',
  `lastsyncstatus` varchar(100) DEFAULT NULL COMMENT '上次同步状态',
  `lastsyncstats` text COMMENT '上次同步统计信息，例如增加多少用户，更新多少，删除多少，增加多少组，更新多少，删除多少',
  `syncbatchno` bigint(20) DEFAULT NULL COMMENT '上次同步批次号',
  `scheduleType` int(11) DEFAULT NULL COMMENT '定时器类型，0按指定时间间隔，1,按照指定时间运行',
  `scheduleTime` varchar(20) DEFAULT NULL COMMENT '定时运行时间如12:00代表每天的12运行一次',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=52 DEFAULT CHARSET=utf8 COMMENT='connector配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_contact`
--

DROP TABLE IF EXISTS `t_contact`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_contact` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `display` varchar(100) DEFAULT NULL COMMENT '联系人名称(全名)',
  `family` varchar(100) DEFAULT NULL,
  `given` varchar(100) DEFAULT NULL,
  `middle` varchar(100) DEFAULT NULL,
  `userid` bigint(20) DEFAULT NULL COMMENT '用户id',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  `updatetime` datetime NOT NULL COMMENT '修改时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='通讯录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_contact_data`
--

DROP TABLE IF EXISTS `t_contact_data`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_contact_data` (
  `id` bigint(20) NOT NULL,
  `contactid` bigint(20) NOT NULL COMMENT '联系人id',
  `category` int(11) DEFAULT '0' COMMENT '联系方式分类: 0 tel 1 email 2 addr 3 org 默认 0',
  `data` varchar(200) DEFAULT NULL COMMENT '数据',
  `type` varchar(100) DEFAULT NULL COMMENT '数据类型',
  `title` varchar(200) DEFAULT NULL COMMENT '标题',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  `updatetime` datetime NOT NULL COMMENT '修改时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `contactid` (`contactid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='联系人联系方式详情表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_contact_log`
--

DROP TABLE IF EXISTS `t_contact_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_contact_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `opertype` int(11) NOT NULL DEFAULT '0' COMMENT '操作类型: 0 user add 1 user del 2 user modified 3 data add 4 data del 5 data modified 默认0',
  `userid` bigint(20) NOT NULL COMMENT '用户id',
  `display` varchar(100) DEFAULT NULL COMMENT '全名',
  `family` varchar(100) DEFAULT NULL COMMENT '姓',
  `given` varchar(100) DEFAULT NULL COMMENT '名',
  `middle` varchar(100) DEFAULT NULL COMMENT '中间姓名',
  `predisplay` varchar(100) DEFAULT NULL COMMENT '之前全名',
  `prefamily` varchar(100) DEFAULT NULL COMMENT '之前姓',
  `pregiven` varchar(100) DEFAULT NULL COMMENT '之前名',
  `premiddle` varchar(100) DEFAULT NULL COMMENT '之前中间姓名',
  `category` int(11) NOT NULL DEFAULT '0' COMMENT '联系方式分类: 0 tel 1 email 2 addr 3 org 默认 0',
  `data` varchar(200) DEFAULT NULL COMMENT '数据',
  `type` varchar(100) DEFAULT NULL COMMENT '数据类型',
  `title` varchar(200) DEFAULT NULL COMMENT '标题',
  `predata` varchar(200) DEFAULT NULL COMMENT '之前数据',
  `pretype` varchar(100) DEFAULT NULL COMMENT '之前数据类型',
  `pretitle` varchar(200) DEFAULT NULL COMMENT '之前标题',
  `version` varchar(50) DEFAULT NULL COMMENT '版本名称 eg：20110608001 临时版本 :00000000001',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `column_21` char(10) DEFAULT NULL,
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='通讯录日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_contact_version`
--

DROP TABLE IF EXISTS `t_contact_version`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_contact_version` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `version` varchar(45) NOT NULL COMMENT '版本名称 eg：20110608001',
  `version_no` int(11) NOT NULL COMMENT '版本号',
  `actived` int(11) NOT NULL DEFAULT '0' COMMENT '是否为当前版本:0否1是默认0',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  `updatetime` datetime NOT NULL COMMENT '修改时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='通讯录版本';
/*!40101 SET character_set_client = @saved_cs_client */;


DROP TABLE IF EXISTS `t_mdm_device`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_mdm_device` (
        `id` bigint(20) NOT NULL AUTO_INCREMENT,
        `userid` bigint(20) DEFAULT NULL COMMENT '所属用户用户id',
        `shortUdid` varchar(10) DEFAULT NULL COMMENT 'unique device short identifier',
        `udid` char(64) DEFAULT NULL COMMENT 'unique device identifier',
        `deviceType` int(11) NOT NULL DEFAULT '10' COMMENT '设备类型 10-android phone 11-android pad;20-iphone 21-ipad 22-ipadmini 23- ipodtouch',
        `type` int(11) NOT NULL DEFAULT '1' COMMENT '平台1--android;2--ios',
        `rootflag` int(11) DEFAULT '0' COMMENT '是否破解root, 0--未破解 ;1--破解 ',
        `lastonlinetime` datetime DEFAULT NULL COMMENT '最后在线时间',
        `onlineflag` int(11) DEFAULT '0' COMMENT '设备是否在线 0--在线;1--不在线',
        `status` int(11) DEFAULT '1' COMMENT '设备状态,0--未注册,1--注册未激活,2--激活可用, 3--保留 4--擦除',
        `registtime` datetime DEFAULT NULL COMMENT '注册时间',
        `activatetime` datetime DEFAULT NULL COMMENT '激活时间',
        `updatetime` datetime DEFAULT NULL COMMENT '设备信息更新时间',
        `devicename` varchar(100) DEFAULT NULL COMMENT '设备名称',
        `userName` varchar(150) DEFAULT NULL COMMENT '用户名',
        `loginId` varchar(50) DEFAULT NULL COMMENT '用户登录名',
        `openUserId` varchar(50) DEFAULT NULL COMMENT '第三方平台用户Id',
        `groupId` bigint(20) DEFAULT NULL COMMENT '所属用户组Id',
        `dudid` varchar(64) comment '设备的系统UDID值,设备MDM激活后得到的设备唯一ID,原来的UDID为应用生成的UMID',
        `activateMdmTime` datetime comment '设备的MDM激活时间',
        `mdmStatus` int(11) DEFAULT 0 comment '设备的MDM激活状态',
        `dualstatus` int(11) DEFAULT NULL COMMENT '双域状态（0-工作域 1-生活域）',
        `uemVersion` varchar(50) DEFAULT NULL COMMENT 'UEM版本号',
        `clientversion` varchar(30) DEFAULT '' COMMENT 'EMM 客户端版本号',
        `ip` varchar(20) DEFAULT NULL COMMENT 'ip地址',
        `clientbuildnum` varchar(50) DEFAULT NULL COMMENT 'EMM客户端Build号',
        `tenantId` varchar(64) NOT NULL COMMENT '租户ID',

        `idfa` char(64) DEFAULT NULL COMMENT 'unique device identifier(IOS7)',
        `imei` varchar(50) DEFAULT NULL COMMENT 'imei:international mobile equipment identity',
        `imsi` varchar(50) DEFAULT NULL COMMENT 'imsi:international mobile subscriber identity',
        `serialnum` varchar(50) DEFAULT NULL COMMENT '设备序列号',
        `manufacturer` varchar(200) DEFAULT NULL COMMENT '设备厂商',
        `model` varchar(100) DEFAULT NULL COMMENT '设备型号,例如：MC319LL',
        `os` varchar(100) DEFAULT NULL COMMENT '操作系统平台',
        `versionNum` varchar(100) DEFAULT NULL COMMENT ' 版本号',
        `oscoreversion` varchar(50) DEFAULT NULL COMMENT '系统内核版本号',
        `cpu` varchar(100) DEFAULT NULL,
        `ram` varchar(100) DEFAULT NULL,
        `camera` varchar(100) DEFAULT NULL COMMENT '摄像头',
        `wifimac` varchar(30) DEFAULT NULL COMMENT 'wifi mac地址',
        `bluetoothmac` varchar(30) DEFAULT NULL COMMENT '蓝牙 mac地址',
        `productname` varchar(100) DEFAULT NULL COMMENT '设备型号码 ,例如：iPhone3,1',


        PRIMARY KEY (`id`),
        KEY `t_mdm_evice_idx_1` (`userid`,`tenantId`,`type`,`deviceType`,`udid`,`status`,`groupId`,`loginId`,`userName`,`openUserId`),
        KEY `t_mdm_device_idx_2` (`type`,`tenantId`,`status`,`onlineflag`,`lastonlinetime`),
        KEY `t_mdm_device_idx_3` (`udid`,`tenantId`,`userid`,`status`),
        KEY `t_mdm_device_idx_4` (`groupId`,`tenantId`,`type`,`devicetype`,`status`,`userid`,`loginId`,`userName`,`openUserId`),
        KEY `t_mdm_device_idx_5` (`loginId`,`tenantId`,`type`,`devicetype`,`status`,`groupId`,`userName`,`openUserId`),
        KEY `t_mdm_device_idx_6` (`dudid`,`tenantId`,`status`,`mdmStatus`,`activateMdmTime`,`groupId`,`userId`,`loginId`)
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8 COMMENT='设备信息表';
/*!40101 SET character_set_client = @saved_cs_client */;




DROP TABLE IF EXISTS `t_mdm_device_extend`;
/*!40101 SET @saved_cs_client = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_mdm_device_extend` (
       `id` bigint(20) NOT NULL AUTO_INCREMENT,
       `buytime` date DEFAULT NULL COMMENT '购买时间',
       `warranty` int(11) DEFAULT NULL COMMENT '保修期',
       `relationship` int(11) NOT NULL DEFAULT '1' COMMENT '设备所属关系 1--公司设备;2--员工设备;3--其他',
       `enableperm` int(11) DEFAULT '1' COMMENT '是否启用权限配置引导，1 -- 启用；0 -- 不启用',
       `devicename` varchar(100) DEFAULT NULL COMMENT '设备名称',
       `devicenumber` varchar(50) DEFAULT NULL COMMENT '设备编号',
       `initsiminfo` varchar(50) DEFAULT NULL COMMENT '激活时SIM卡信息，Android设备为IMSI，iOS设备为ICCID',
       `initsdserialnum` varchar(100) DEFAULT NULL COMMENT '激活时的SD卡序列号',
       `iccid` varchar(50) DEFAULT NULL,
       `phonecode` varchar(100) DEFAULT NULL COMMENT '手机号码',
       `romcapacity` varchar(100) DEFAULT NULL COMMENT 'rom 总容量',
       `romavailablecapacity` varchar(100) DEFAULT NULL COMMENT 'rom 可用空间',
       `sdcapacity` varchar(100) DEFAULT NULL COMMENT 'sd卡容量',
       `sdavailablecapacity` varchar(100) DEFAULT NULL COMMENT 'sd卡可用空间',
       `sdserialnum` varchar(100) DEFAULT NULL COMMENT 'sd卡序列号',
       `powerstatus` varchar(100) DEFAULT NULL COMMENT '电源状态',
       `boottime` bigint(20) DEFAULT NULL COMMENT '开机时长',
       `capacity` varchar(100) DEFAULT NULL COMMENT '存储容量(ios)',
       `capacitystatus` varchar(100) DEFAULT NULL COMMENT '存储容量状态(ios)',
       `availablecapacity` varchar(100) DEFAULT NULL COMMENT '可用空间(ios)',
       `ip` varchar(20) DEFAULT NULL COMMENT 'ip地址',
       `encryptionlevel` int(11) DEFAULT NULL COMMENT '加密级别',
       `token` varchar(200) DEFAULT NULL COMMENT 'ios mdm发送唤醒使用',
       `pushmagic` varchar(200) DEFAULT NULL COMMENT 'ios mdm发送唤醒使用',
       `unlocktoken` varchar(4000) DEFAULT NULL COMMENT 'ios mdm解除锁屏使用',
       `sendregistnoticeflag` int(11) DEFAULT '0' COMMENT '是否发送注册通知信息,0--否;1--是',
       `allowactrootorjailbreakflag` int(11) DEFAULT '0' COMMENT '是否允许root/越狱设备激活,0--否;1--是',
       `remark` varchar(500) DEFAULT NULL COMMENT '备注',
       `roamingflag` int(11) DEFAULT '0' COMMENT '设备漫游开关 0--否;1--是',
       `updatetime` datetime DEFAULT NULL COMMENT '设备信息更新时间',
       `apnstoken` varchar(200) DEFAULT NULL COMMENT 'apns的设备token',
       `lostflag` int(11) DEFAULT '0' COMMENT '是否丢失：0 --未丢失，1 --丢失',
       `dataencryptionflag` int(11) DEFAULT '0' COMMENT '是否开启数据加密.0--否;1--是',
       `specialtypeflag` int(11) DEFAULT '0' COMMENT '特殊设备类型标识.0--普通设备;100--Samsung safe设备;101--Samsung KNOX设备;301--华为设备',
       `knoxstatus` int(11) DEFAULT '0' COMMENT 'knox状态,0--不支持;201--支持未注入;202--SAFE license已注入;203--KNOX license已注入;101--已创建;501--锁定;301--华为插件已激活;302--移除华为插件',
       `offlinestatus` int(11) NOT NULL DEFAULT '0' COMMENT '设备是否失联：0未失联，1失联',
       `offlinethreshold` varchar(5) NOT NULL DEFAULT '0' COMMENT '设备失联违规天数',
       `emmloginagain` int(11) DEFAULT '0' COMMENT '自服务是否修改密码 0-未修改 1-已修改',
       `hostid` varchar(50) DEFAULT NULL COMMENT 'hostId proxy激活时的hostId',
       `usagestats` int(2) DEFAULT '0' COMMENT '应用使用量统计状态 0 未启用，1 启用',
       `flowupdate` int(2) DEFAULT '0' COMMENT '流量是否有更新 0 未更新，1 有更新',
       `flowquota` int(10) DEFAULT '0' COMMENT '流量配额',
       `bitlocker` int(11) DEFAULT NULL COMMENT 'BitLocker加密状态 0:未加密 1:整个硬盘加密 2:系统分区加密',
       `meid` varchar(50) DEFAULT NULL COMMENT '副卡对应的设备识别码,(如无副卡,可为空)',
       `resolution` varchar(50) DEFAULT NULL COMMENT '屏幕分辨率,(字符串,长*高,如640*480)',
       `ossoftwareversion` varchar(50) DEFAULT NULL COMMENT '系统软件版本号',
       `safetyosversion` varchar(50) DEFAULT NULL COMMENT '安全加固双操作系统版本,(如无,可为空)',
       `patchlevel` varchar(50) DEFAULT NULL COMMENT '系统安全补丁程序级别(如无,可为空)',
       `iccid2` varchar(50) DEFAULT NULL COMMENT 'ICCID(SIM卡2的ICCID)',
       `imsi2` varchar(50) DEFAULT NULL COMMENT 'IMSI(SIM卡2的IMSI,如无卡2，可为空)',
       `networktype` varchar(50) DEFAULT NULL COMMENT '支持的移动网络制式',
       `wlanadapterchip` varchar(50) DEFAULT NULL COMMENT '无线网卡芯片型号',
       `btadapterchip` varchar(50) DEFAULT NULL COMMENT '蓝牙芯片型号',
       `nfcchip` varchar(50) DEFAULT NULL COMMENT 'NFC芯片型号',
       `locatorchip` varchar(50) DEFAULT NULL COMMENT '定位芯片型号',
       `clientbuildnum` varchar(50) DEFAULT NULL COMMENT 'EMM客户端Build号',
       `cpuratio` varchar(30) DEFAULT NULL COMMENT 'CPU占用率',
       `memoryratio` varchar(30) DEFAULT NULL COMMENT '内存占用率',
       `storageratio` varchar(30) DEFAULT NULL COMMENT '存储占用率',
       `bluetoothstate` int(11) DEFAULT NULL COMMENT '蓝牙状态，1-开启,0 -关闭',
       `wifistate` int(11) DEFAULT NULL COMMENT 'wifi状态，1-开启 0-关闭',
       `camerastate` int(11) DEFAULT NULL COMMENT '相机状态，1-开启 0-关闭',
       `microphonestate` int(11) DEFAULT NULL COMMENT '麦克风状态，1-开启 0-关闭',
       `mobiledatastate` int(11) DEFAULT NULL COMMENT '移动数据状态，1-开启 0-关闭',
       `apn` varchar(500) DEFAULT NULL COMMENT 'apn集合，status:0 未连接 ，1连接',
       `buildnumber` varchar(200) DEFAULT NULL COMMENT '内核版本',
       `gpsstate` int(11) DEFAULT NULL COMMENT 'GPS状态：0-关闭 1-开启',
       `syncflag` int(11) DEFAULT '0' COMMENT '是否上报成功 1-上报绑定成功 10-上报绑定失败 2-上报解绑成功 20-上报解绑失败',
       `dualstatus` int(11) DEFAULT NULL COMMENT '双域状态（0-工作域 1-生活域）',
       `simoper` int(11) DEFAULT NULL COMMENT 'sim卡操作 1-更新sim卡 0-撤销更新sim卡',
       `tpmreport` int(11) DEFAULT NULL COMMENT '可信度 2-不安全 1-安全',
       `systemintegrity` int(11) DEFAULT NULL COMMENT '系统完整性 2-不完整 1-完整',
       `idp` varchar(20) DEFAULT NULL COMMENT '第三方idp名称，idp_device_id对应',
       `idpdeviceid` varchar(100) DEFAULT NULL COMMENT 'device_id对应的idp',
       `offlineTime` timestamp NULL DEFAULT NULL COMMENT '失联时间',
       `enablepermissionguide` int(11) DEFAULT '1' COMMENT '强管控权限向导，1 -- 强管控；0 -- 弱管控',

       `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
       PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8 COMMENT='设备扩展信息表';
--
-- Table structure for table `t_device_accessweb`
--

DROP TABLE IF EXISTS `t_device_accessweb`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_device_accessweb` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `userid` bigint(20) NOT NULL,
  `loginid` varchar(50) NOT NULL DEFAULT '',
  `deviceid` bigint(20) NOT NULL,
  `username` varchar(100) NOT NULL DEFAULT '',
  `devicename` varchar(100) NOT NULL DEFAULT '',
  `groupid` bigint(20) NOT NULL,
  `relationship` int(11) NOT NULL,
  `url` varchar(2000) NOT NULL DEFAULT '',
  `title` varchar(150) DEFAULT NULL,
  `accesstime` datetime NOT NULL,
  `groupname` varchar(100) DEFAULT NULL COMMENT '组名称',
  `appname` varchar(100) DEFAULT NULL COMMENT '应用名称',
  `hostname` varchar(100) DEFAULT NULL,
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_device_apn`
--

DROP TABLE IF EXISTS `t_device_apn`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_device_apn` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `apnid` int(11) NOT NULL COMMENT 'APN ID',
  `description` varchar(500) DEFAULT NULL COMMENT 'APN描述',
  `apnname` varchar(50) DEFAULT NULL COMMENT 'APN名称',
  `apntype` varchar(50) DEFAULT NULL COMMENT 'APN类',
  `networkcode` varchar(50) DEFAULT NULL COMMENT '运营商网络码',
  `mcc` varchar(50) DEFAULT NULL COMMENT 'MCC',
  `mnc` varchar(50) DEFAULT NULL COMMENT 'MNC',
  `proxy` varchar(50) DEFAULT NULL COMMENT '代理',
  `port` varchar(50) DEFAULT NULL COMMENT '端口',
  `mmsproxy` varchar(50) DEFAULT NULL COMMENT '彩信代理',
  `mmsport` varchar(50) DEFAULT NULL COMMENT '彩信端口',
  `username` varchar(50) DEFAULT NULL COMMENT '用户名',
  `server` varchar(50) DEFAULT NULL COMMENT '服务器',
  `password` varchar(50) DEFAULT NULL COMMENT '密码',
  `mmsc` varchar(50) DEFAULT NULL COMMENT 'MMSC',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `updatetime` datetime DEFAULT NULL COMMENT '更新时间',
  `deviceid` bigint(20) NOT NULL COMMENT '关联设备外键',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `fk_t_device_apn_deviceid` (`deviceid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_device_apn_token`
--

DROP TABLE IF EXISTS `t_device_apn_token`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_device_apn_token` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `deviceid` bigint(20) NOT NULL COMMENT '设备id',
  `udid` char(64) DEFAULT NULL COMMENT '设备udid',
  `token` varchar(260) DEFAULT NULL COMMENT 'apns的token',
  `type` int(10) unsigned NOT NULL DEFAULT '0' COMMENT 'apn的类型: 0 com.cyb.mdm 1 com.cyb.mcm',
  `pkgname` varchar(200) DEFAULT NULL COMMENT '该token对应的app包名',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `updatetime` datetime DEFAULT NULL COMMENT '更新时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `fk_t_device_apn_token_deviceid` (`deviceid`),
  KEY `index_type_and_udid` (`type`,`udid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备apn的token表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_device_app`
--

DROP TABLE IF EXISTS `t_device_app`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_device_app` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `deviceid` bigint(20) NOT NULL,
  `name` varchar(255) NOT NULL,
  `packagename` varchar(255) DEFAULT NULL,
  `publickey` varchar(2048) DEFAULT NULL,
  `versionname` varchar(255) DEFAULT NULL,
  `versioncode` varchar(255) DEFAULT NULL,
  `scantime` datetime DEFAULT NULL COMMENT 'scan time',
  `certsha1` varchar(50) DEFAULT NULL COMMENT '证书sha1',
  `type` int(11) DEFAULT NULL COMMENT '类型: (1 系统，2 其他）',
  `appsize` int(10) DEFAULT NULL,
  `appid` bigint(20) unsigned DEFAULT NULL,
  `version` varchar(100) DEFAULT NULL,
  `appdatasize` int(11) DEFAULT NULL,
  `userid` bigint(20) DEFAULT NULL COMMENT '用户id',
  `status` int(10) unsigned DEFAULT NULL COMMENT '应用状态: 1 已安装  3 已删除 2 应用更新',
  `itunesstoreid` bigint(20) DEFAULT NULL COMMENT '苹果应用商店应用id',
  `appflag` int(11) DEFAULT '1' COMMENT '应用标识: 0--非企业管理;1--企业管理',
  `firstinstalltime` datetime DEFAULT NULL COMMENT '初次安装时间',
  `lastupdatetime` datetime DEFAULT NULL COMMENT '最后更新时间',
  `developer` varchar(100) DEFAULT NULL COMMENT '开发者',
  `simsendflow` varchar(30) DEFAULT NULL COMMENT '数据网络的发送量',
  `simrecvflow` varchar(30) DEFAULT NULL COMMENT '数据网络的接收量',
  `wifisendflow` varchar(30) DEFAULT NULL COMMENT 'WiFi无线网络的发送量',
  `wifirecvflow` varchar(30) DEFAULT NULL COMMENT 'WiFi无线网络的接收量',
  `powerconsumption` varchar(30) DEFAULT NULL COMMENT '耗电量（毫安时）',
  `runningtime` varchar(30) DEFAULT NULL COMMENT '运行时间（毫秒）',
  `exceptioncount` varchar(30) DEFAULT NULL COMMENT '异常次数',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `index_union_unique` (`packagename`,`deviceid`),
  KEY `deviceid_index` (`deviceid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备应用信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_device_app_action`
--

DROP TABLE IF EXISTS `t_device_app_action`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_device_app_action` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `deviceid` bigint(20) NOT NULL COMMENT '关联设备id',
  `appname` varchar(50) NOT NULL DEFAULT '' COMMENT '应用名称',
  `pkgname` varchar(50) NOT NULL DEFAULT '' COMMENT '包名称',
  `versionname` varchar(50) NOT NULL DEFAULT '' COMMENT '大版本号',
  `versioncode` varchar(50) NOT NULL DEFAULT '' COMMENT '小版本号',
  `status` int(11) DEFAULT NULL COMMENT '状态（打开-1 关闭-0）',
  `opertime` datetime DEFAULT NULL COMMENT '操作时间',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `updatetime` datetime DEFAULT NULL COMMENT '更新时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备应用行为表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_device_app_crash`
--

DROP TABLE IF EXISTS `t_device_app_crash`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_device_app_crash` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `deviceid` bigint(20) NOT NULL COMMENT '设备id',
  `userid` bigint(20) NOT NULL COMMENT '用户id',
  `apppkg` varchar(4000) NOT NULL COMMENT 'app package name',
  `appname` varchar(200) NOT NULL COMMENT 'app name',
  `version` varchar(200) NOT NULL COMMENT 'app version',
  `platformtype` int(11) NOT NULL COMMENT '设备平台',
  `filepath` varchar(200) NOT NULL COMMENT '文件路径',
  `crashtime` datetime DEFAULT NULL COMMENT '应用崩溃时间',
  `createtime` datetime DEFAULT NULL,
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='应用崩溃报表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_device_app_startup`
--

DROP TABLE IF EXISTS `t_device_app_startup`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_device_app_startup` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `deviceid` bigint(20) NOT NULL COMMENT '设备id',
  `userid` bigint(20) NOT NULL COMMENT '用户id',
  `apppkg` varchar(4000) NOT NULL COMMENT 'app package name',
  `appname` varchar(200) NOT NULL COMMENT 'app name',
  `version` varchar(200) NOT NULL COMMENT 'app version',
  `platformtype` int(11) NOT NULL COMMENT '设备平台',
  `startuptime` datetime DEFAULT NULL COMMENT '应用启动时间',
  `createtime` datetime DEFAULT NULL,
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='应用启动报表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_device_appusage`
--

DROP TABLE IF EXISTS `t_device_appusage`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_device_appusage` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `userid` bigint(20) NOT NULL,
  `loginid` varchar(50) NOT NULL DEFAULT '',
  `deviceid` bigint(20) NOT NULL,
  `username` varchar(100) NOT NULL DEFAULT '',
  `devicename` varchar(100) NOT NULL DEFAULT '',
  `groupid` bigint(20) NOT NULL,
  `relationship` int(11) NOT NULL,
  `pkgname` varchar(100) NOT NULL DEFAULT '',
  `appname` varchar(100) NOT NULL,
  `version` varchar(100) DEFAULT NULL,
  `vendor` varchar(100) DEFAULT NULL,
  `platform` int(4) NOT NULL,
  `usagecount` int(11) NOT NULL,
  `usageduration` int(11) NOT NULL,
  `mobiledata` int(11) NOT NULL,
  `wifidata` int(11) NOT NULL,
  `occureddate` datetime NOT NULL,
  `createtime` datetime DEFAULT CURRENT_TIMESTAMP,
  `updatetime` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `t_device_appusage_index` (`userid`,`deviceid`,`pkgname`,`version`,`occureddate`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_device_area`
--

DROP TABLE IF EXISTS `t_device_area`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_device_area` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `deviceid` bigint(20) NOT NULL COMMENT '设备ID',
  `udid` char(64) NOT NULL COMMENT '设备udid',
  `userid` bigint(20) NOT NULL COMMENT '用户ID',
  `guarddata` varchar(50) NOT NULL COMMENT '门禁信息',
  `updatetime` datetime DEFAULT NULL COMMENT '进出门禁刷卡时间',
  `status` int(11) DEFAULT '-1' COMMENT '所处状态  1 区外   0 区内',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `t_device_area_ibfk_1` (`deviceid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备大屏统计表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_device_browserhistory`
--

DROP TABLE IF EXISTS `t_device_browserhistory`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_device_browserhistory` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `userid` bigint(20) NOT NULL,
  `loginid` varchar(50) NOT NULL DEFAULT '',
  `deviceid` bigint(20) NOT NULL,
  `username` varchar(100) NOT NULL DEFAULT '',
  `devicename` varchar(100) NOT NULL DEFAULT '',
  `groupid` bigint(20) NOT NULL,
  `relationship` int(11) NOT NULL,
  `url` varchar(2000) NOT NULL DEFAULT '',
  `title` varchar(150) DEFAULT NULL,
  `accesstime` datetime NOT NULL,
  `groupname` varchar(100) DEFAULT NULL COMMENT '组名称',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_device_call`
--

DROP TABLE IF EXISTS `t_device_call`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_device_call` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '通话id',
  `udid` char(64) NOT NULL DEFAULT '' COMMENT '设备id',
  `callType` int(1) DEFAULT '0' COMMENT '通话类型，0呼出，1呼入',
  `originalNo` varchar(20) DEFAULT NULL COMMENT '主叫电话号码',
  `terminalNo` varchar(20) DEFAULT NULL COMMENT '被叫电话号码',
  `startTime` datetime NOT NULL COMMENT '通话开始时间',
  `duration` int(11) DEFAULT '0' COMMENT '通话时长(秒)，0表示未接通',
  `location` varchar(100) DEFAULT NULL COMMENT '通话地点',
  `loginid` varchar(50) DEFAULT NULL COMMENT '用户帐号',
  `username` varchar(50) DEFAULT NULL COMMENT '用户名称',
  `devicename` varchar(50) DEFAULT NULL COMMENT '设备名称',
  `groupid` bigint(20) DEFAULT NULL COMMENT '组ID',
  `relationship` int(11) DEFAULT NULL COMMENT '所属关系',
  `platformtype` int(11) DEFAULT NULL COMMENT '平台类型',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_device_callover_log`
--

DROP TABLE IF EXISTS `t_device_callover_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_device_callover_log` (
  `id` bigint(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '系统ID',
  `loginid` varchar(50) NOT NULL COMMENT '用户ID',
  `udid` char(64) NOT NULL COMMENT '设备udid',
  `username` varchar(150) DEFAULT '' COMMENT '用户名',
  `mobile` varchar(50) DEFAULT NULL COMMENT '手机',
  `createtime` datetime NOT NULL COMMENT '上报时间',
  `deviceid` bigint(20) NOT NULL,
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='点名表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_device_cert`
--

DROP TABLE IF EXISTS `t_device_cert`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_device_cert` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `content1` varchar(1000) DEFAULT NULL COMMENT '密码模块返回的数字证书字符串',
  `content2` varchar(1000) DEFAULT NULL COMMENT '证书的json信息',
  `deviceid` bigint(20) NOT NULL COMMENT '关联设备外键',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `fk_t_device_cert_deviceid` (`deviceid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_device_check`
--

DROP TABLE IF EXISTS `t_device_check`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_device_check` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `manufacturer` varchar(200) DEFAULT NULL COMMENT '设备厂商',
  `model` varchar(100) DEFAULT NULL COMMENT '设备型号',
  `udid` varchar(64) DEFAULT NULL COMMENT 'unique device identifier',
  `imei` varchar(50) DEFAULT NULL COMMENT 'imei:international mobile equipment identity',
  `imsi` varchar(50) DEFAULT NULL COMMENT 'imsi:international mobile subscriber identity',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备信息验证表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_device_check_device`
--

DROP TABLE IF EXISTS `t_device_check_device`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_device_check_device` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `checkid` bigint(20) NOT NULL COMMENT '验证信息id',
  `deviceid` bigint(20) NOT NULL COMMENT '设备id',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `t_device_check_device_checkid` (`checkid`),
  KEY `t_device_check_device_deviceid` (`deviceid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备验证与设备关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_device_cmd_send_state`
--

DROP TABLE IF EXISTS `t_device_cmd_send_state`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_device_cmd_send_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `udid` char(64) DEFAULT NULL,
  `uuid` varchar(100) DEFAULT NULL COMMENT '指令uuid',
  `opertype` int(6) DEFAULT NULL COMMENT '操作类型 0:锁机;21--清除密码;22--设备更新,23--设备定位,24--播放铃声,25--设备擦除,26--选择性擦除',
  `status` int(2) DEFAULT NULL COMMENT '执行状态,0:指令下发未执行;1:执行成功;21--执行失败',
  `paramcontent` varchar(200) DEFAULT NULL COMMENT '指令参数',
  `createtime` datetime DEFAULT NULL COMMENT '指令下发时间',
  `updatetime` datetime DEFAULT NULL COMMENT '执行完成更新时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备远程控制指令表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_device_collect`
--

DROP TABLE IF EXISTS `t_device_collect`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_device_collect` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `type` int(11) DEFAULT NULL COMMENT '设备类型：1：安卓，2：iphone，3：其他',
  `manufacturer` varchar(200) DEFAULT NULL COMMENT '设备厂商',
  `model` varchar(100) DEFAULT NULL COMMENT '设备型号,例如：MC319LL',
  `versionnum` varchar(100) DEFAULT NULL COMMENT ' 版本号',
  `updatetime` datetime DEFAULT NULL COMMENT '更新时间',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='采集设备信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_device_cryptomodule`
--

DROP TABLE IF EXISTS `t_device_cryptomodule`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_device_cryptomodule` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `result` varchar(50) DEFAULT NULL COMMENT '0:查询成功 1:终端密码模块连接异常 2:终端密码模块状态异常 3:终端密码模块信息读取错误',
  `moduletype` varchar(50) DEFAULT NULL COMMENT '终端密码模块类型，包括但不限于tfcard、usbkey',
  `manufacture` varchar(50) DEFAULT NULL COMMENT '终端密码模块生产厂商',
  `moduleid` varchar(50) DEFAULT NULL COMMENT '终端密码模块硬件编号',
  `deviceid` bigint(20) NOT NULL COMMENT '关联设备外键',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `fk_t_device_cryptomodule_deviceid` (`deviceid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_device_diagnose_log`
--

DROP TABLE IF EXISTS `t_device_diagnose_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_device_diagnose_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `filename` varchar(50) DEFAULT NULL COMMENT 'id转换为字符串格式加上压缩扩展名',
  `deviceid` bigint(20) NOT NULL COMMENT '设备id',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  `updatetime` datetime DEFAULT NULL COMMENT '修改时间',
  `begintime` datetime NOT NULL COMMENT '起始时间',
  `endtime` datetime NOT NULL COMMENT '结束时间',
  `wifi` int(11) NOT NULL DEFAULT '0' COMMENT '是否只在wifi情况下下载 0:所有情况下载, 1:仅在wifi下下载  默认0',
  `devicename` varchar(100) DEFAULT NULL COMMENT '设备名称',
  `userid` bigint(20) DEFAULT NULL COMMENT '用户id',
  `groupid` bigint(20) DEFAULT NULL COMMENT '用户组id',
  `platform` int(11) NOT NULL DEFAULT '1' COMMENT '适合平台  0:表示其它，1:表示android, 2:表示ios，默认是1 ',
  `triggermode` int(11) NOT NULL DEFAULT '0' COMMENT '触发模式  0:server请求, 1:client上报，默认是0 ',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '日志上传状态  0:等待上传, 1:上传成功, 2:上传失败，3:wifi限制，默认是0',
  `path` varchar(500) DEFAULT NULL COMMENT '文件相对路径',
  `operator` varchar(50) DEFAULT NULL COMMENT '操作者',
  `type` int(11) DEFAULT '0' COMMENT '日志类型 0-设备日志，1-拍照，2-截屏',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `deviceid` (`deviceid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='远程诊断 日志上传表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_device_history`
--

DROP TABLE IF EXISTS `t_device_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_device_history` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `udid` char(64) NOT NULL DEFAULT '' COMMENT '设备id',
  `loginid` varchar(50) DEFAULT NULL COMMENT '用户帐号',
  `username` varchar(50) DEFAULT NULL COMMENT '用户名称',
  `devicename` varchar(50) DEFAULT NULL COMMENT '设备名称',
  `groupid` bigint(20) DEFAULT NULL COMMENT '组ID',
  `relationship` int(11) DEFAULT NULL COMMENT '所属关系',
  `platformtype` int(11) DEFAULT NULL COMMENT '平台类型',
  `productname` varchar(100) DEFAULT NULL COMMENT '设备型号码 ,例如：iPhone3,1',
  `model` varchar(100) DEFAULT NULL COMMENT '设备型号,例如：MC319LL',
  `os` varchar(100) DEFAULT NULL COMMENT '操作系统平台',
  `versionnum` varchar(100) DEFAULT NULL COMMENT ' 版本号',
  `serialnum` varchar(50) DEFAULT NULL COMMENT '设备序列号',
  `registtime` datetime DEFAULT NULL COMMENT '注册时间',
  `activatetime` datetime DEFAULT NULL COMMENT '激活时间',
  `status` int(11) DEFAULT NULL COMMENT '设备状态,0--淘汰,1--注册未激活,2--激活可用, 3--保留 4--擦除，5待擦除，6待淘汰',
  `rootflag` int(11) DEFAULT '0' COMMENT '是否破解root, 0--未破解 ;1--破解 ',
  `jailbreakflag` int(11) DEFAULT '0' COMMENT '设备是否越狱 0--否;1--是',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_device_model`
--

DROP TABLE IF EXISTS `t_device_model`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_device_model` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `model` varchar(100) DEFAULT NULL COMMENT '设备型号',
  `type` int(11) NOT NULL DEFAULT '1' COMMENT '设备类型  1--android;2--ios',
  `picturepath` varchar(200) NOT NULL COMMENT '设备图片路径',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `model` (`model`,`tenantId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备型号信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_device_msg`
--

DROP TABLE IF EXISTS `t_device_msg`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_device_msg` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '短信id',
  `udid` char(64) NOT NULL DEFAULT '' COMMENT '设备id',
  `msgType` int(1) DEFAULT '0' COMMENT '短信状态，0发送，1接收',
  `smsType` int(1) DEFAULT '0' COMMENT '短信类型，0短信，1彩信',
  `originalNo` varchar(20) DEFAULT NULL COMMENT '发送方电话号码',
  `terminalNo` varchar(20) DEFAULT NULL COMMENT '接收方电话号码',
  `happenTime` datetime NOT NULL COMMENT '发送或接收的时间',
  `content` varchar(500) DEFAULT NULL COMMENT '短信内容',
  `loginid` varchar(50) DEFAULT NULL COMMENT '用户帐号',
  `username` varchar(50) DEFAULT NULL COMMENT '用户名称',
  `devicename` varchar(50) DEFAULT NULL COMMENT '设备名称',
  `groupid` bigint(20) DEFAULT NULL COMMENT '组ID',
  `relationship` int(11) DEFAULT NULL COMMENT '所属关系',
  `platformtype` int(11) DEFAULT NULL COMMENT '平台类型',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_device_network_log`
--

DROP TABLE IF EXISTS `t_device_network_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_device_network_log` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '系统ID',
  `userid` bigint(20) NOT NULL COMMENT '用户ID',
  `loginid` varchar(50) NOT NULL,
  `username` varchar(150) DEFAULT '' COMMENT '用户名',
  `mobile` varchar(50) DEFAULT NULL COMMENT '手机',
  `udid` char(64) NOT NULL COMMENT '设备udid',
  `deviceid` bigint(20) NOT NULL,
  `devicename` varchar(150) DEFAULT '' COMMENT '设备名称',
  `createtime` datetime NOT NULL COMMENT '上报时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备联网异常表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_device_oper_log`
--

DROP TABLE IF EXISTS `t_device_oper_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_device_oper_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `userid` bigint(20) NOT NULL COMMENT '用户id',
  `loginid` varchar(50) NOT NULL COMMENT '用户名',
  `name` varchar(50) NOT NULL COMMENT '真实姓名',
  `opertype` varchar(100) NOT NULL COMMENT '动作,0--注册;1--激活;-1--淘汰',
  `opertime` datetime NOT NULL COMMENT '操作时间',
  `devicename` varchar(100) DEFAULT NULL COMMENT '设备名称',
  `serialnum` varchar(50) DEFAULT NULL COMMENT '设备序列号',
  `remarks` varchar(500) DEFAULT NULL COMMENT '备注',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8 COMMENT='设备操作(注册、激活、淘汰)日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_device_profile`
--

DROP TABLE IF EXISTS `t_device_profile`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_device_profile` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `deviceid` bigint(20) NOT NULL,
  `udid` char(64) DEFAULT NULL,
  `policyid` varchar(100) NOT NULL COMMENT '配置文件uuid',
  `createtime` datetime DEFAULT NULL COMMENT '下发时间',
  `updatetime` datetime DEFAULT NULL COMMENT '更新时间',
  `status` int(11) DEFAULT NULL COMMENT '执行状态,0--已分发;1--已安装;21--安装失败',
  `fencestatus` int(11) DEFAULT '0' COMMENT '围栏状态：0、围栏外；1、围栏内',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `t_device_profile_idx_1` (`deviceId`,`tenantId`,`status`,`policyId`),
  KEY `t_device_profile_idx_2` (`udid`,`tenantId`,`deviceId`,`status`,`policyId`),
  KEY `t_device_profile_idx_3` (`policyId`,`tenantId`,`status`,`deviceId`)
) ENGINE=InnoDB AUTO_INCREMENT=1196004355109838852 DEFAULT CHARSET=utf8 COMMENT='设备与配置文件策略关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_device_remotecontrol`
--

DROP TABLE IF EXISTS `t_device_remotecontrol`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_device_remotecontrol` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `deviceid` bigint(20) NOT NULL,
  `udid` char(64) DEFAULT NULL,
  `uuid` varchar(100) DEFAULT NULL COMMENT '指令uuid',
  `paramcontent` varchar(500) DEFAULT NULL COMMENT '指令参数',
  `opertype` int(11) NOT NULL COMMENT '操作类型 0:锁机;21--清除密码;22--设备更新,23--设备定位,24--播放铃声,25--设备擦除,26--选择性擦除',
  `createtime` datetime DEFAULT NULL COMMENT '指令下发时间',
  `updatetime` datetime DEFAULT NULL COMMENT '执行完成更新时间',
  `status` int(11) DEFAULT NULL COMMENT '执行状态,0:指令下发未执行;1:执行成功;21--执行失败',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `deviceid` (`deviceid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备远程控制指令表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_device_samsung`
--

DROP TABLE IF EXISTS `t_device_samsung`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_device_samsung` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `deviceid` bigint(20) NOT NULL COMMENT '设备id',
  `bluetoothstatus` tinyint(4) DEFAULT '0' COMMENT '蓝牙状态 0--禁止;1--启用',
  `camerastatus` tinyint(4) DEFAULT '0' COMMENT '摄像头状态  0--禁止;1--启用',
  `microphonestatus` tinyint(4) DEFAULT '0' COMMENT '麦克风状态 0--禁止;1--启用',
  `wifistatus` tinyint(4) DEFAULT '0' COMMENT 'wi-fi状态 0--禁止;1--启用',
  `nfcstatus` tinyint(4) DEFAULT '0' COMMENT 'nfc状态 0--禁止;1--启用',
  `sdcardstatus` tinyint(4) DEFAULT '0' COMMENT 'sd卡状态 0--禁止;1--启用',
  `gpsstatus` tinyint(4) DEFAULT '0' COMMENT 'gps状态 0--关闭;1--开启',
  `cellulardatastatus` tinyint(4) DEFAULT '0' COMMENT '蜂窝数据状态 0--禁止;1--启用',
  `roamingdatastatus` tinyint(4) DEFAULT '0' COMMENT '数据漫游状态 0--禁止;1--启用',
  `roamingpushstatus` tinyint(4) DEFAULT '0' COMMENT 'push消息状态 0--禁止;1--启用',
  `roamingsyncstatus` tinyint(4) DEFAULT '0' COMMENT '漫游同步状态 0--禁止;1--启用',
  `roamingvoicecallsstatus` tinyint(4) DEFAULT '0' COMMENT '语音漫游状态 0--禁止;1--启用',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `deviceid` (`deviceid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_device_securityinfo`
--

DROP TABLE IF EXISTS `t_device_securityinfo`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_device_securityinfo` (
  `id` bigint(11) NOT NULL AUTO_INCREMENT,
  `deviceid` bigint(20) NOT NULL,
  `hardwareencryptioncaps` int(2) NOT NULL DEFAULT '0' COMMENT '1--block-level encryption.2--file-level encryption.',
  `passcodepresent` int(2) NOT NULL DEFAULT '0' COMMENT '0--false,1--true 是否受密码保护',
  `passcodecompliant` int(2) NOT NULL DEFAULT '0' COMMENT '0--false,1--true 其它帐号是否需要用户的密码',
  `passcodecompliantwithprofiles` int(2) NOT NULL DEFAULT '0' COMMENT '0--false,1--true 配置文件是否需要用户的密码',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `deviceid` (`deviceid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_device_status`
--

DROP TABLE IF EXISTS `t_device_status`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_device_status` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `udid` char(64) NOT NULL COMMENT 'unique device identifier',
  `componenttype` int(5) DEFAULT NULL COMMENT '业务类型：1-mem;2-mcm;3-mam',
  `status` int(5) DEFAULT NULL COMMENT '设备状态：0-暂停;1-禁用',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `updatetime` datetime DEFAULT NULL COMMENT '修改时间',
  `deviceid` bigint(20) DEFAULT NULL COMMENT '设备id',
  `violationid` bigint(20) DEFAULT NULL COMMENT '违规 id',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `deviceid` (`deviceid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备状态表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_device_sysevent_log`
--

DROP TABLE IF EXISTS `t_device_sysevent_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_device_sysevent_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `udid` char(64) NOT NULL COMMENT '1.System Time 2.GPS On/Off',
  `eventType` int(11) NOT NULL,
  `eventResult` varchar(50) NOT NULL COMMENT '修改后的结果',
  `createTime` datetime DEFAULT NULL,
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_device_tmp`
--

DROP TABLE IF EXISTS `t_device_tmp`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_device_tmp` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `uuid` varchar(50) DEFAULT NULL,
  `userid` bigint(20) NOT NULL COMMENT '所属用户用户id',
  `udid` char(64) DEFAULT NULL COMMENT 'unique device identifier',
  `imei` varchar(50) DEFAULT NULL COMMENT 'imei:international mobile equipment identity',
  `imsi` varchar(50) DEFAULT NULL COMMENT 'imsi:international mobile subscriber identity',
  `buytime` date DEFAULT NULL COMMENT '购买时间',
  `warranty` int(11) DEFAULT NULL COMMENT '保修期',
  `devicetype` int(11) NOT NULL DEFAULT '10' COMMENT '设备类型 10--android phone/android pad;20--iphone/ipod touch;21-ipad/ipad mini',
  `type` int(11) NOT NULL DEFAULT '1' COMMENT '平台1--android;2--ios',
  `relationship` int(11) NOT NULL DEFAULT '1' COMMENT '设备所属关系  1--公司设备;2--员工设备;3--其他',
  `devicename` varchar(100) DEFAULT NULL COMMENT '设备名称',
  `devicenumber` varchar(50) DEFAULT NULL COMMENT '设备编号',
  `iccid` varchar(50) DEFAULT NULL,
  `serialnum` varchar(50) DEFAULT NULL COMMENT '设备序列号',
  `manufacturer` varchar(200) DEFAULT NULL COMMENT '设备厂商',
  `model` varchar(100) DEFAULT NULL COMMENT '设备型号',
  `os` varchar(100) DEFAULT NULL COMMENT '操作系统平台',
  `versionum` varchar(100) DEFAULT NULL COMMENT ' 版本号',
  `phonecode` varchar(100) DEFAULT NULL COMMENT '手机号码',
  `cpu` varchar(100) DEFAULT NULL,
  `ram` varchar(100) DEFAULT NULL,
  `romcapacity` varchar(100) DEFAULT NULL COMMENT 'rom 总容量',
  `romavailablecapacity` varchar(100) DEFAULT NULL COMMENT 'rom 可用空间',
  `sdcapacity` varchar(100) DEFAULT NULL COMMENT 'sd卡容量',
  `sdavailablecapacity` varchar(100) DEFAULT NULL COMMENT 'sd卡可用空间',
  `sdserialnum` varchar(100) DEFAULT NULL COMMENT 'sd卡序列号',
  `camera` varchar(100) DEFAULT NULL COMMENT '摄像头',
  `wifimac` varchar(30) DEFAULT NULL COMMENT 'wifi mac地址',
  `bluetoothmac` varchar(30) DEFAULT NULL COMMENT '蓝牙 mac地址',
  `powerstatus` varchar(100) DEFAULT NULL COMMENT '电源状态',
  `boottime` bigint(20) DEFAULT NULL COMMENT '开机时长',
  `capacity` varchar(100) DEFAULT NULL COMMENT '存储容量(ios)',
  `capacitystatus` varchar(100) DEFAULT NULL COMMENT '存储容量状态(ios)',
  `availablecapacity` varchar(100) DEFAULT NULL COMMENT '可用空间(ios)',
  `ip` varchar(20) DEFAULT NULL COMMENT 'ip地址',
  `encryptionlevel` int(11) DEFAULT NULL COMMENT '加密级别',
  `rootflag` int(11) DEFAULT '0' COMMENT '是否破解root, 0--未破解 ;1--破解 ',
  `lastonlinetime` datetime DEFAULT NULL COMMENT '最后在线时间',
  `onlineflag` int(11) DEFAULT '0' COMMENT '设备是否在线 0--在线;1--不在线',
  `jailbreakflag` int(11) DEFAULT '0' COMMENT '设备是否越狱 0--否;1--是',
  `status` int(11) DEFAULT NULL COMMENT '设备状态,0--淘汰,1--注册未激活,2--激活可用, 3--保留 4--擦除，5待擦除，6待淘汰',
  `registtime` datetime DEFAULT NULL COMMENT '注册时间',
  `activatetime` datetime DEFAULT NULL COMMENT '激活时间',
  `token` varchar(200) DEFAULT NULL COMMENT 'ios mdm发送唤醒使用',
  `pushmagic` varchar(200) DEFAULT NULL COMMENT 'ios mdm发送唤醒使用',
  `unlocktoken` varchar(4000) DEFAULT NULL COMMENT 'ios mdm解除锁屏使用',
  `challenge` varchar(200) DEFAULT NULL COMMENT 'ios mdm激活使用',
  `sendregistnoticeflag` int(11) DEFAULT '0' COMMENT '是否发送注册通知信息,0--否;1--是',
  `allowactrootorjailbreakflag` int(11) DEFAULT '0' COMMENT '是否允许root/越狱设备激活,0--否;1--是',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `roamingflag` int(11) DEFAULT '0' COMMENT '设备漫游开关 0--否;1--是',
  `updatetime` datetime DEFAULT NULL COMMENT '设备信息更新时间',
  `apnstoken` varchar(200) DEFAULT NULL COMMENT 'apns的设备token',
  `lostflag` int(11) DEFAULT '0' COMMENT '是否丢失：0 --未丢失，1 --丢失',
  `dataencryptionflag` int(11) DEFAULT '0' COMMENT '是否开启数据加密.0--否;1--是',
  `specialtypeflag` int(11) DEFAULT '0' COMMENT '特殊设备类型标识.0--普通设备;100--samsung safe设备;101--samsung knox设备',
  `pauseflag` int(11) DEFAULT '0' COMMENT '设备暂停标识：0 正常   / 1 暂停',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备信息表uuid udid转换表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_device_traffic`
--

DROP TABLE IF EXISTS `t_device_traffic`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_device_traffic` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `loginId` varchar(50) NOT NULL DEFAULT '' COMMENT '用户帐号',
  `udid` char(64) NOT NULL COMMENT '设备udid',
  `endtime` datetime NOT NULL COMMENT '统计截止时间',
  `simTraffic` bigint(20) DEFAULT '0' COMMENT 'sim1日流量',
  `wifiTraffic` bigint(20) DEFAULT '0' COMMENT 'wifi日流量',
  `mobile` varchar(20) DEFAULT NULL COMMENT '手机号',
  `username` varchar(50) DEFAULT NULL COMMENT '用户名称',
  `devicename` varchar(50) DEFAULT NULL COMMENT '设备名称',
  `groupid` bigint(20) DEFAULT NULL COMMENT '组ID',
  `relationship` int(11) DEFAULT NULL COMMENT '所属关系',
  `platformtype` int(11) DEFAULT NULL COMMENT '平台类型',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_device_usercheckin`
--

DROP TABLE IF EXISTS `t_device_usercheckin`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_device_usercheckin` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `userid` bigint(20) NOT NULL,
  `loginid` varchar(50) NOT NULL DEFAULT '',
  `deviceid` bigint(20) NOT NULL,
  `username` varchar(100) NOT NULL DEFAULT '',
  `mobile` varchar(40) DEFAULT NULL,
  `devicename` varchar(100) NOT NULL DEFAULT '',
  `groupname` varchar(100) DEFAULT NULL,
  `groupid` bigint(20) NOT NULL,
  `relationship` int(11) NOT NULL,
  `longitude` double(12,7) DEFAULT NULL,
  `latitude` double(12,7) DEFAULT NULL,
  `occureddate` datetime NOT NULL,
  `createtime` datetime DEFAULT CURRENT_TIMESTAMP,
  `updatetime` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `t_device_usercheckin_index` (`userid`,`deviceid`,`occureddate`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_device_using_log`
--

DROP TABLE IF EXISTS `t_device_using_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_device_using_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `deviceid` bigint(20) NOT NULL,
  `imsi` varchar(50) DEFAULT NULL COMMENT 'imsi:international mobile subscriber identity',
  `operatorname` varchar(200) DEFAULT NULL COMMENT '运营商名称',
  `phonecode` varchar(100) DEFAULT NULL COMMENT '手机号码',
  `simchangeinfo` varchar(200) DEFAULT NULL COMMENT 'sim变更信息',
  `simcardflow` int(11) DEFAULT NULL COMMENT 'sim卡流量(kb)',
  `wlanflow` int(11) DEFAULT NULL COMMENT 'wlan流量(kb)',
  `createtime` datetime DEFAULT NULL COMMENT '日志创建时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `t_device_usercheckin_index` (`deviceId`,`tenantId`,`createTime`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 COMMENT='设备使用日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_device_wechat`
--

DROP TABLE IF EXISTS `t_device_wechat`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_device_wechat` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `deviceid` bigint(20) DEFAULT NULL COMMENT '设备ID',
  `version` varchar(50) DEFAULT NULL COMMENT '版本',
  `imei` varchar(50) DEFAULT NULL COMMENT 'imei:international mobile equipment identity',
  `account` varchar(100) DEFAULT NULL COMMENT '微信账号',
  `name` varchar(100) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '微信姓名',
  `headportrait` varchar(255) DEFAULT NULL COMMENT '微信头像',
  `autograph` varchar(100) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '微信个性签名',
  `province` varchar(100) DEFAULT NULL COMMENT '微信省份',
  `area` varchar(100) DEFAULT NULL COMMENT '微信地区',
  `qrcode` varchar(255) DEFAULT NULL COMMENT '微信二维码',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `updatetime` datetime DEFAULT NULL COMMENT '更新时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备微信账号信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_device_wechat_linkman`
--

DROP TABLE IF EXISTS `t_device_wechat_linkman`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_device_wechat_linkman` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `devicewechatid` bigint(20) DEFAULT NULL COMMENT '本机微信',
  `version` varchar(50) DEFAULT NULL COMMENT '版本',
  `account` varchar(100) DEFAULT NULL COMMENT '联系人账号',
  `name` varchar(100) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '联系人姓名',
  `headportrait` varchar(255) DEFAULT NULL COMMENT '联系人头像',
  `autograph` varchar(100) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '联系人个性签名',
  `province` varchar(100) DEFAULT NULL COMMENT '联系人省份',
  `area` varchar(100) DEFAULT NULL COMMENT '联系人地区',
  `source` varchar(100) DEFAULT NULL COMMENT '联系人来源',
  `qrcode` varchar(255) DEFAULT NULL COMMENT '联系人二维码',
  `sex` int(1) DEFAULT NULL COMMENT '联系人性别',
  `remark` varchar(255) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '联系人备注',
  `blacklist` int(1) DEFAULT NULL COMMENT '是否加入黑名单',
  `starfriend` int(1) DEFAULT NULL COMMENT '是否星标朋友',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `updatetime` datetime DEFAULT NULL COMMENT '更新时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备微信联系人信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_device_wechat_log`
--

DROP TABLE IF EXISTS `t_device_wechat_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_device_wechat_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `deviceid` bigint(20) NOT NULL COMMENT '设备id',
  `imei` varchar(50) DEFAULT NULL COMMENT 'imei:international mobile equipment identity',
  `account` varchar(100) DEFAULT NULL COMMENT '微信账号',
  `name` varchar(100) DEFAULT NULL COMMENT '微信姓名',
  `type` int(1) DEFAULT NULL COMMENT '登录登出类型(1:登录,0:登出)',
  `logintime` datetime DEFAULT NULL COMMENT '登录时间',
  `logouttime` datetime DEFAULT NULL COMMENT '登出时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='微信审计操作日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_device_wrapapp`
--

DROP TABLE IF EXISTS `t_device_wrapapp`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_device_wrapapp` (
  `deviceid` bigint(20) NOT NULL,
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `updatetime` datetime DEFAULT NULL COMMENT '更新时间',
  `forbiddenapp` varchar(3000) DEFAULT NULL COMMENT '启用APP包名',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`deviceid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='iOS设备wrappingapp配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_doc_group_version`
--

DROP TABLE IF EXISTS `t_doc_group_version`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_doc_group_version` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `groupid` bigint(20) NOT NULL COMMENT '组id',
  `version` bigint(20) unsigned NOT NULL,
  `updatetime` varchar(45) NOT NULL,
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `index_unique_groupid` (`groupid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='组文档版本表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_document`
--

DROP TABLE IF EXISTS `t_document`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_document` (
  `id` bigint(20) NOT NULL,
  `name` varchar(100) DEFAULT NULL COMMENT '文件名',
  `url` varchar(200) DEFAULT NULL COMMENT 'url路径',
  `icon` varchar(200) DEFAULT NULL COMMENT '图标',
  `description` varchar(500) DEFAULT NULL COMMENT '描述',
  `type` int(11) NOT NULL DEFAULT '0' COMMENT '类型: 0 pdf, 1 numbers, 2 pages, 3 keynote, 4 word, 5 powerpoint,  6 excel,  7 html,  8 xml, 9 text, 10image',
  `tagid` int(11) DEFAULT NULL COMMENT '标签id',
  `status` int(11) DEFAULT NULL COMMENT '状态',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  `updatetime` datetime NOT NULL COMMENT '修改时间',
  `createby` varchar(50) DEFAULT NULL COMMENT '创建人',
  `updateby` varchar(50) DEFAULT NULL COMMENT '修改人',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `tagid` (`tagid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='文档管理表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_document_log`
--

DROP TABLE IF EXISTS `t_document_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_document_log` (
  `id` bigint(20) NOT NULL,
  `docid` bigint(20) NOT NULL COMMENT '文档id',
  `deviceid` bigint(20) NOT NULL COMMENT '设备id',
  `opertype` int(11) NOT NULL DEFAULT '0' COMMENT '操作类型: 0 预览、1 下载、2 分享 默认0',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `docid` (`docid`),
  KEY `deviceid` (`deviceid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='文档操作记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_document_oper_log`
--

DROP TABLE IF EXISTS `t_document_oper_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_document_oper_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `fileid` bigint(20) DEFAULT NULL COMMENT '文件id',
  `opertype` int(5) DEFAULT NULL COMMENT '操作类型: 0  预览   , 1  下载   , 2  上传',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `operby` varchar(100) DEFAULT NULL COMMENT '操作人',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='文件操作日志';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_document_statistics`
--

DROP TABLE IF EXISTS `t_document_statistics`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_document_statistics` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `baseymd` date DEFAULT NULL COMMENT '日期: 格式 2013-04-19',
  `fileid` bigint(20) DEFAULT NULL COMMENT '文件id',
  `opertype` int(5) DEFAULT NULL COMMENT '操作类型: 0 预览  , 1 下载  , 2 上传',
  `opercount` bigint(50) DEFAULT NULL COMMENT '操作次数',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='统计信息表(供dashboard统计用)';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_document_tag`
--

DROP TABLE IF EXISTS `t_document_tag`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_document_tag` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) DEFAULT NULL COMMENT '标签名称',
  `description` varchar(500) DEFAULT NULL COMMENT '描述',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  `updatetime` datetime NOT NULL COMMENT '修改时间',
  `type` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '标签类型: 0 内置标签 1 自定义',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='文档标签表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_document_tag_group`
--

DROP TABLE IF EXISTS `t_document_tag_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_document_tag_group` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tagid` int(11) NOT NULL,
  `groupid` bigint(20) NOT NULL,
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `tagid` (`tagid`,`groupid`),
  KEY `groupid` (`groupid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='文档标签表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_document_tag_link`
--

DROP TABLE IF EXISTS `t_document_tag_link`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_document_tag_link` (
  `id` bigint(20) NOT NULL,
  `docid` bigint(20) DEFAULT NULL COMMENT '文档id',
  `tagid` int(11) DEFAULT NULL COMMENT '标签id',
  `distribute` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '是否推送通知:  0 分发 1 不分发 默认 0 ',
  `opertype` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '操作类型: 0 预览、1 下载、2 分享 默认0',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `docid` (`docid`,`tagid`),
  KEY `tagid` (`tagid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_document_tag_user`
--

DROP TABLE IF EXISTS `t_document_tag_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_document_tag_user` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tagid` int(11) NOT NULL,
  `userid` bigint(20) NOT NULL,
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `tagid` (`tagid`,`userid`),
  KEY `userid` (`userid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='文档标签用户关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_document_user_version`
--

DROP TABLE IF EXISTS `t_document_user_version`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_document_user_version` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `userid` bigint(20) NOT NULL COMMENT '用户id',
  `version` bigint(20) unsigned NOT NULL,
  `updatetime` varchar(45) NOT NULL,
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `index_unique_userid` (`userid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户文档版本表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_dynamic_label_user`
--

DROP TABLE IF EXISTS `t_dynamic_label_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_dynamic_label_user` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `labelid` bigint(20) NOT NULL COMMENT '标签Id',
  `paramname` varchar(100) NOT NULL COMMENT '标签参数名称:1:选择用户、用户组 2:设备归属 3:OS版本 4:品牌型号 5:运营商 6:运营商 7:设备状态 8:上次在线时间',
  `paramvalue` varchar(1000) NOT NULL COMMENT '标签参数值',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `id_UNIQUE` (`id`),
  KEY `t_dynamic_label_user_idfk_idx` (`labelid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='动态标签用户表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_emm_client_login`
--

DROP TABLE IF EXISTS `t_emm_client_login`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_emm_client_login` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `emm_client_id` varchar(50) NOT NULL,
  `loginid` varchar(50) NOT NULL,
  `status` varchar(10) NOT NULL COMMENT 'logout，login',
  `last_login_time` datetime DEFAULT NULL,
  `last_logout_time` datetime DEFAULT NULL,
  `create_time` datetime NOT NULL,
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `idx_t_emm_client_login_loginid` (`loginid`)
) ENGINE=InnoDB AUTO_INCREMENT=32 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_emm_grant`
--

DROP TABLE IF EXISTS `t_emm_grant`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_emm_grant` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `pincode_id` bigint(20) unsigned NOT NULL,
  `host_id` varchar(50) NOT NULL,
  `credential` varchar(200) NOT NULL,
  `create_time` datetime NOT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `status` varchar(20) NOT NULL DEFAULT 'ACTIVATION',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `host_id` (`host_id`),
  KEY `pincode_fk` (`pincode_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_emm_pincode`
--

DROP TABLE IF EXISTS `t_emm_pincode`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_emm_pincode` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `pincode` varchar(20) NOT NULL,
  `principle` varchar(50) NOT NULL,
  `pincode_mode` varchar(20) NOT NULL,
  `proxy_record_id` varchar(50) NOT NULL,
  `expire_time` datetime NOT NULL,
  `status` varchar(10) NOT NULL COMMENT '"CREATED", "USED"',
  `create_time` datetime NOT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `proxy_record_id` (`proxy_record_id`),
  KEY `tEmmPincode_principle_mode` (`principle`,`pincode_mode`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_emm_token`
--

DROP TABLE IF EXISTS `t_emm_token`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_emm_token` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `host_id` varchar(50) NOT NULL,
  `app_id` varchar(50) NOT NULL,
  `app_pass_token` varchar(50) NOT NULL,
  `expire_time` datetime NOT NULL,
  `create_time` datetime NOT NULL,
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `tEmmPassToken_hostId_appId` (`host_id`,`app_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_entrance_guard`
--

DROP TABLE IF EXISTS `t_entrance_guard`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_entrance_guard` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `guardname` varchar(50) NOT NULL COMMENT '门禁名称',
  `guarddata` varchar(50) NOT NULL COMMENT '门禁信息',
  `guardgroupid` bigint(20) NOT NULL COMMENT '门禁组id',
  `status` int(11) NOT NULL DEFAULT '1' COMMENT '门禁状态 1-有效 0-无效 -1-删除',
  `loginid` varchar(50) NOT NULL COMMENT '创建人',
  `switchdomain` int(11) DEFAULT NULL COMMENT '是否切换域  0-不切换  1-切换',
  `opendoor` int(11) DEFAULT NULL COMMENT '是否开门  0-不开门  1-开门',
  `enterorleave` int(11) DEFAULT NULL COMMENT '进门-0  出门-1',
  `guardcontrollerip` varchar(50) DEFAULT NULL COMMENT '门禁控制器IP',
  `guardcontrollerport` varchar(10) DEFAULT NULL COMMENT '门禁控制器端口',
  `guardcontrollersn` varchar(50) DEFAULT NULL COMMENT '门禁控制器序列号',
  `guardcontrollerdoorid` varchar(20) DEFAULT NULL COMMENT '门禁对应的门编号',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `updatetime` datetime DEFAULT NULL COMMENT '更新时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uindex_name_data` (`guardname`,`guarddata`),
  KEY `status_index` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='门禁信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_entrance_guard_device`
--

DROP TABLE IF EXISTS `t_entrance_guard_device`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_entrance_guard_device` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `deviceid` bigint(20) NOT NULL COMMENT '设备id',
  `guardid` bigint(20) NOT NULL COMMENT '门禁id',
  `guardpushid` bigint(20) DEFAULT NULL COMMENT '门禁信息推送ID',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `updatetime` datetime DEFAULT NULL COMMENT '更新时间',
  `status` int(2) DEFAULT '0' COMMENT '执行状态,0--已分发;1--已应用',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `idx_tegd_guardid` (`guardid`),
  KEY `idx_tegd_deviceid` (`deviceid`),
  KEY `idx_tegd_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='门禁信息分发设备关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_entrance_guard_group`
--

DROP TABLE IF EXISTS `t_entrance_guard_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_entrance_guard_group` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `guardgroupname` varchar(25) NOT NULL COMMENT '门禁组组名',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `status` int(11) NOT NULL DEFAULT '1' COMMENT '门禁组状态 1-有效 0-无效  -1-删除',
  `loginid` varchar(50) NOT NULL COMMENT '创建人',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `updatetime` datetime DEFAULT NULL COMMENT '更新时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uindex_guardgroupname` (`guardgroupname`),
  KEY `status_index` (`status`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COMMENT='门禁组表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_entrance_guard_log`
--

DROP TABLE IF EXISTS `t_entrance_guard_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_entrance_guard_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `deviceid` bigint(20) DEFAULT NULL COMMENT '设备ID',
  `udid` char(64) DEFAULT NULL COMMENT '设备udid',
  `userid` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `guarddata` varchar(50) NOT NULL COMMENT '门禁信息',
  `switchdomain` int(11) DEFAULT '0' COMMENT '是否切换域  0-不切换  1-切换',
  `opendoor` int(11) DEFAULT '0' COMMENT '是否开门  0-不开门  1-开门',
  `result` int(11) DEFAULT '-1' COMMENT '是否成功开门  -1 失败   0 成功',
  `oper` varchar(10) NOT NULL COMMENT '进出标识(门禁信息后2位)00进 01 出',
  `opertime` datetime DEFAULT NULL COMMENT '进出门禁刷卡时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `idx_tegl_deviceid` (`deviceid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='门禁日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_entrance_guard_pu`
--

DROP TABLE IF EXISTS `t_entrance_guard_pu`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_entrance_guard_pu` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `guardid` bigint(20) DEFAULT NULL COMMENT '门禁id',
  `userid` bigint(20) DEFAULT NULL COMMENT '用户',
  `guardpushid` bigint(20) DEFAULT NULL COMMENT '门禁信息推送ID',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `guardpushid` (`guardpushid`),
  KEY `userid` (`userid`),
  KEY `tegpu_ibfk_1` (`guardid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='门禁信息分发用户关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_entrance_guard_pug`
--

DROP TABLE IF EXISTS `t_entrance_guard_pug`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_entrance_guard_pug` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `guardid` bigint(20) DEFAULT NULL COMMENT '门禁id',
  `groupid` bigint(20) DEFAULT NULL COMMENT '用户组',
  `guardpushid` bigint(20) DEFAULT NULL COMMENT '门禁信息推送ID',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `guardid` (`guardid`),
  KEY `groupid` (`groupid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='门禁信息分发用户组关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_entrance_guard_push`
--

DROP TABLE IF EXISTS `t_entrance_guard_push`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_entrance_guard_push` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '推送id',
  `guardids` varchar(255) DEFAULT NULL COMMENT '门禁ids',
  `guardgroupid` bigint(20) DEFAULT NULL COMMENT '门禁组id',
  `loginid` varchar(50) DEFAULT NULL COMMENT '创建人',
  `devicerelationship` varchar(50) NOT NULL DEFAULT '0' COMMENT '推送设备所属关系（企业-1、员工-2、其他-3、全部-0）默认全部',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='门禁信息推送表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_exception`
--

DROP TABLE IF EXISTS `t_exception`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_exception` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `deviceid` bigint(20) NOT NULL COMMENT '设备id',
  `exceptiontype` int(11) NOT NULL COMMENT '异常类型.201—Android设备异常退出激活;',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='异常记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_fence`
--

DROP TABLE IF EXISTS `t_fence`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_fence` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `name` varchar(200) NOT NULL COMMENT '围栏名称',
  `type` int(1) NOT NULL COMMENT '围栏类型 1-时间围栏 2- 地理围栏',
  `descr` varchar(200) DEFAULT NULL COMMENT '描述',
  `timezone` varchar(100) DEFAULT NULL COMMENT '时区',
  `begindate` date DEFAULT NULL COMMENT '开始日期',
  `enddate` date DEFAULT NULL COMMENT '结束日期',
  `longitude` double(10,6) DEFAULT NULL COMMENT '经度',
  `latitude` double(10,6) DEFAULT NULL COMMENT '纬度',
  `distance` float(7,3) DEFAULT NULL COMMENT '距离范围',
  `unit` int(1) DEFAULT NULL COMMENT '距离单位 1-km 2- mile',
  `creator` varchar(50) NOT NULL COMMENT '创建人',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `updatetime` datetime DEFAULT NULL COMMENT '变更时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='围栏信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_fence_inout_log`
--

DROP TABLE IF EXISTS `t_fence_inout_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_fence_inout_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `udid` char(64) NOT NULL COMMENT '设备的唯一标识',
  `policyId` varchar(50) NOT NULL COMMENT '策略id',
  `eventType` varchar(10) NOT NULL COMMENT '时间类型（1：进围栏；2：出围栏）',
  `startTime` varchar(50) DEFAULT NULL COMMENT '围栏配置的开始时间',
  `endTime` varchar(50) DEFAULT NULL COMMENT '围栏配置的结束时间',
  `longitude` double(10,6) DEFAULT NULL COMMENT '经度',
  `latitude` double(10,6) DEFAULT NULL COMMENT '纬度',
  `distance` float(4,2) DEFAULT NULL COMMENT '半径',
  `unit` varchar(20) DEFAULT NULL COMMENT '单位',
  `occurTime` varchar(50) NOT NULL COMMENT '发生时间',
  `createTime` datetime NOT NULL COMMENT '记录创建的时间',
  `fenceType` int(2) DEFAULT NULL COMMENT '策略类型',
  `effectiveDate` varchar(50) DEFAULT NULL,
  `expiredDate` varchar(50) DEFAULT NULL,
  `type` int(4) DEFAULT NULL,
  `day` varchar(50) DEFAULT NULL,
  `updatestatus` int(11) NOT NULL DEFAULT '0' COMMENT '更新状态(0：未更新，1：已更新)',
  `inouttype` int(11) DEFAULT NULL COMMENT '引起进出围栏的原因：0-设备移动;1-删除;2-下发',
  `isgeofencetype` int(11) DEFAULT NULL COMMENT '引起进出围栏的原因是不是地里围栏：0-是，1-否',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='进出围栏日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_fence_trigger_log`
--

DROP TABLE IF EXISTS `t_fence_trigger_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_fence_trigger_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `deviceUdid` char(64) NOT NULL COMMENT '设备的唯一标识',
  `policyId` varchar(50) NOT NULL,
  `function` varchar(50) DEFAULT NULL COMMENT '功能项',
  `expection` int(4) NOT NULL COMMENT '事件类型（1：启用；2：禁用）',
  `result` int(4) NOT NULL COMMENT '1：成功;0：失败',
  `createTime` datetime NOT NULL,
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_file`
--

DROP TABLE IF EXISTS `t_file`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_file` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '文件id',
  `filename` varchar(300) DEFAULT NULL COMMENT '文件名称',
  `fileurl` varchar(300) DEFAULT NULL COMMENT '文件url路径',
  `filepdfurl` varchar(300) DEFAULT NULL COMMENT '文档转成pdf格式后的url路径',
  `fileicon` varchar(300) DEFAULT NULL COMMENT '图标url',
  `filetype` int(5) NOT NULL COMMENT '类型: (microsoft office系列: 10 word , 11 excel , 12 powerpoint ..),(20 pdf ..),(text : 30 txt , 31 csv ...),( image: 40 png , 41 jpg , 42 gif ..),(50 html) ..',
  `filedir` varchar(500) DEFAULT NULL COMMENT '文件所在文件夹逻辑位置(例如: /a/b)',
  `filedirids` varchar(500) DEFAULT NULL COMMENT '文件所属文件夹全路径id串(1,2,3,4,5)',
  `filesize` bigint(50) DEFAULT NULL COMMENT '文件大小(单位: 字节)',
  `filesource` int(5) DEFAULT NULL COMMENT '文件来源: 0 管理员上传  , 1 ftp导入  , 2 共享目录导入  , 3 sharepoint , 4 其它',
  `filemd5` varchar(50) DEFAULT NULL COMMENT '文档md5值(判断内容是否有改动)',
  `customversion` varchar(100) DEFAULT NULL COMMENT '用户文件版本',
  `versioncode` int(11) DEFAULT NULL COMMENT '版本号(后台标记)',
  `status` int(5) DEFAULT NULL COMMENT '文件状态: 0 禁用   , 1 启用  ',
  `isdelete` int(5) DEFAULT NULL COMMENT '是否删除: 0 否   , 1 是',
  `convertflag` int(5) DEFAULT NULL COMMENT 'pdf转换状态: 0 未转换  , 1 转换中  , 2  转换完成   , 3  转换失败',
  `folderid` bigint(20) DEFAULT NULL COMMENT '文件所在的文件夹(如果不在任何文件夹下则为-1)',
  `starflag` int(5) DEFAULT NULL COMMENT '是否是星标文档: 0  否  , 1  是',
  `lockflag` int(5) DEFAULT NULL COMMENT '是否给文件加锁: 0  否   , 1  是',
  `lockpassword` varchar(200) DEFAULT NULL COMMENT '文件锁密码(md5加密后值)',
  `description` varchar(500) DEFAULT NULL COMMENT '文件描述',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `updatetime` datetime DEFAULT NULL COMMENT '修改时间',
  `createby` varchar(100) DEFAULT NULL COMMENT '创建人',
  `updateby` varchar(100) DEFAULT NULL COMMENT '修改人',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `index_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='文件信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_file_dist_device`
--

DROP TABLE IF EXISTS `t_file_dist_device`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_file_dist_device` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `filedistributeid` bigint(20) DEFAULT NULL COMMENT '文件分发条件id',
  `deviceId` bigint(20) DEFAULT NULL COMMENT '设备id',
  `fileId` bigint(20) DEFAULT NULL COMMENT '文件id',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `t_file_dist_device_ibfk_1` (`filedistributeid`),
  KEY `t_file_dist_device_ibfk_2` (`deviceId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='文档分发设备关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_file_dist_group`
--

DROP TABLE IF EXISTS `t_file_dist_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_file_dist_group` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `filedistributeid` bigint(20) DEFAULT NULL COMMENT '文件分发条件id',
  `groupid` bigint(20) DEFAULT NULL COMMENT '用户组id',
  `fileid` bigint(20) DEFAULT NULL COMMENT '文件id',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `filedistributeid` (`filedistributeid`),
  KEY `groupid` (`groupid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='文件分发条件用户组关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_file_dist_user`
--

DROP TABLE IF EXISTS `t_file_dist_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_file_dist_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `filedistributeid` bigint(20) DEFAULT NULL COMMENT '文件分发条件id',
  `userid` bigint(20) DEFAULT NULL COMMENT '用户id',
  `fileid` bigint(20) DEFAULT NULL COMMENT '文件id',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `filedistributeid` (`filedistributeid`),
  KEY `userid` (`userid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='文档分发用户关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_file_distribute`
--

DROP TABLE IF EXISTS `t_file_distribute`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_file_distribute` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `fileid` bigint(20) DEFAULT NULL COMMENT '文件id',
  `relationship` varchar(50) DEFAULT NULL COMMENT '设备所属关系: 逗号分割的字符串(1,2,3)',
  `distributetype` int(5) DEFAULT NULL COMMENT '是否推送通知:  0  不通知  , 1  apns通知',
  `authopertype` int(5) DEFAULT NULL COMMENT '文档操作权限: 0   预览  , 1  预览下载  ',
  `labelid` bigint(20) DEFAULT NULL COMMENT '标签id',
  `createtime` datetime DEFAULT NULL COMMENT '分发时间',
  `creator` varchar(20) DEFAULT NULL COMMENT '创建者',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `fileid` (`fileid`),
  KEY `t_file_distribute_ibfk_2` (`labelid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='文档分发条件信息表(每一条记录都是对一个文档的分发条件)';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_file_user_open`
--

DROP TABLE IF EXISTS `t_file_user_open`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_file_user_open` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `fileid` bigint(20) DEFAULT NULL COMMENT '文件id',
  `userid` bigint(20) DEFAULT NULL COMMENT '用户id',
  `lastopentime` datetime DEFAULT NULL COMMENT '最近打开时间',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `fileid` (`fileid`),
  KEY `userid` (`userid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户最近打开文件';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_file_user_star`
--

DROP TABLE IF EXISTS `t_file_user_star`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_file_user_star` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `fileid` bigint(20) DEFAULT NULL COMMENT '加星文件id',
  `userid` bigint(20) DEFAULT NULL COMMENT '哪个用户id对该文件加星',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `fileid` (`fileid`),
  KEY `userid` (`userid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户星标文件关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_folder`
--

DROP TABLE IF EXISTS `t_folder`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_folder` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '文件夹标识id',
  `foldername` varchar(300) DEFAULT NULL COMMENT '文件夹名称',
  `folderurl` varchar(300) DEFAULT NULL COMMENT '文件夹url路径',
  `foldertype` int(5) NOT NULL COMMENT '文件夹类型: 0 自定义  , 1 导入生成',
  `folderdir` varchar(500) DEFAULT NULL COMMENT '当前文件夹的父文件夹逻辑位置(例如: /a/b)',
  `folderdirids` varchar(500) DEFAULT NULL COMMENT '文件夹所属文件夹全路径id串(1,2,3,4,5)',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `parentfolderid` bigint(20) DEFAULT NULL COMMENT '父目录id( 如果无父目录则为 -1 )',
  `importserverid` bigint(20) DEFAULT NULL COMMENT '如果文件夹是导入生成, 是则需要关联一个 importserverid 获得服务器信息',
  `isdelete` int(5) DEFAULT NULL COMMENT '是否删除: 0 否   , 1 是',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `updatetime` datetime DEFAULT NULL COMMENT '修改时间',
  `createby` varchar(100) DEFAULT NULL COMMENT '创建人',
  `updateby` varchar(100) DEFAULT NULL COMMENT '修改人',
  `toplevel` int(11) DEFAULT '0' COMMENT '是否是管理员的根目录',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='文件夹信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_folder_dist_group`
--

DROP TABLE IF EXISTS `t_folder_dist_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_folder_dist_group` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `folderdistributeid` bigint(20) DEFAULT NULL COMMENT '文件夹分发条件id',
  `groupid` bigint(20) DEFAULT NULL COMMENT '用户组id',
  `customflag` int(5) DEFAULT '0' COMMENT '自定义标识: 0-继承 ;1-自定义',
  `exceptionflag` int(5) DEFAULT '0' COMMENT '例外标识: 0-添加 ;1-排除',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `folderdistributeid` (`folderdistributeid`),
  KEY `groupid` (`groupid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='文件夹分发条件用户组关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_folder_dist_user`
--

DROP TABLE IF EXISTS `t_folder_dist_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_folder_dist_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `folderdistributeid` bigint(20) DEFAULT NULL COMMENT '文件夹分发条件id',
  `userid` bigint(20) DEFAULT NULL COMMENT '用户id',
  `customflag` int(5) DEFAULT '0' COMMENT '自定义标识: 0-继承 ;1-自定义',
  `exceptionflag` int(5) DEFAULT '0' COMMENT '例外标识: 0-添加 ;1-排除',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `folderdistributeid` (`folderdistributeid`),
  KEY `userid` (`userid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='文档分发用户关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_folder_distribute`
--

DROP TABLE IF EXISTS `t_folder_distribute`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_folder_distribute` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分发条件id',
  `folderid` bigint(20) DEFAULT NULL COMMENT '文件夹id',
  `description` varchar(500) DEFAULT NULL COMMENT '分发条件描述',
  `usertagid` bigint(20) DEFAULT NULL COMMENT '用户标签id',
  `timetagid` bigint(20) DEFAULT NULL COMMENT '时间标签id',
  `positiontagid` bigint(20) DEFAULT NULL COMMENT '位置标签id',
  `relationship` varchar(50) DEFAULT NULL COMMENT '设备所属关系: 逗号分割的字符串(1,2,3)',
  `distributetype` int(5) DEFAULT NULL COMMENT '是否推送通知:  0  不通知  , 1  apns通知',
  `authopertype` int(5) DEFAULT NULL COMMENT '文档操作权限: 0   预览  , 1   预览下载  ',
  `labelid` bigint(20) DEFAULT NULL COMMENT '标签id',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `folderid` (`folderid`),
  KEY `t_folder_distribute_ibfk_2` (`labelid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='文件夹分发条件信息表(每一条记录都是对一个文件夹的分发条件)';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_google_layout_page`
--

DROP TABLE IF EXISTS `t_google_layout_page`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_google_layout_page` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `orderinlayout` int(11) DEFAULT NULL COMMENT 'page页序号',
  `pageid` varchar(100) DEFAULT NULL COMMENT 'id from Google',
  `pagename` varchar(100) DEFAULT NULL COMMENT 'page页名称',
  `clusterids` varchar(1000) DEFAULT NULL COMMENT '应用群集id',
  `clusternames` varchar(1000) DEFAULT NULL COMMENT '应用群集名称',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `updatetime` datetime DEFAULT NULL COMMENT '更新时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Google Play store layout page';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_google_play_product`
--

DROP TABLE IF EXISTS `t_google_play_product`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_google_play_product` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `productid` varchar(100) DEFAULT NULL COMMENT 'google product 主键',
  `smalliconurl` varchar(255) DEFAULT NULL COMMENT '小图标',
  `iconurl` varchar(255) DEFAULT NULL COMMENT '图标',
  `title` varchar(255) DEFAULT NULL COMMENT '标题',
  `lastupdatedtime` datetime DEFAULT NULL COMMENT '最新更新时间',
  `authorname` varchar(100) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '产品发行方',
  `minandroidsdkversion` int(11) DEFAULT NULL COMMENT 'android最小版本',
  `kind` varchar(50) DEFAULT NULL COMMENT 'google 资源类型',
  `versioncode` int(10) DEFAULT NULL COMMENT '版本号',
  `versionstring` varchar(50) DEFAULT NULL COMMENT '版本号',
  `detailsurl` varchar(255) DEFAULT NULL COMMENT '详情地址',
  `workdetailsurl` varchar(255) DEFAULT NULL COMMENT '链接地址',
  `mcmid` varchar(20) DEFAULT NULL COMMENT '生效的配置',
  `appid` bigint(20) DEFAULT NULL COMMENT '应用商店appid',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_google_user_product`
--

DROP TABLE IF EXISTS `t_google_user_product`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_google_user_product` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `behavior` varchar(100) DEFAULT NULL COMMENT 'productSetBehavior:includeAll,allApproved,whitelist',
  `userids` longtext COMMENT '用户id',
  `productids` longtext COMMENT 'productSet',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `updatetime` datetime DEFAULT NULL COMMENT '更新时间',
  `loginid` varchar(50) NOT NULL COMMENT '创建人',
  `creator` varchar(200) DEFAULT NULL COMMENT '创建人姓名',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='Google Play user Available ProductSet';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_group`
--

DROP TABLE IF EXISTS `t_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_group` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '组标识',
  `guid` varchar(100) DEFAULT NULL COMMENT 'ad/ldap同步时的唯一标识',
  `name` varchar(100) NOT NULL COMMENT '组名',
  `parentid` bigint(20) DEFAULT NULL COMMENT '上级组标识',
  `canloginself` int(11) NOT NULL DEFAULT '1' COMMENT '是否可登录自服务平台 0-否 1-是',
  `canregistdevice` int(11) NOT NULL DEFAULT '1' COMMENT '是否能够进行设备注册0-否 1-是',
  `caneliminatedevice` int(11) NOT NULL DEFAULT '1' COMMENT '是否能够进行设备淘汰0-否 1-是',
  `allowactivatenum` int(11) NOT NULL DEFAULT '1' COMMENT '允许激活的设备数量',
  `type` int(11) NOT NULL DEFAULT '0' COMMENT '0-自定义 1-ad/ldap同步',
  `status` int(11) NOT NULL DEFAULT '1' COMMENT '组状态 1-有效 0-无效  -1-删除',
  `descr` varchar(600) DEFAULT NULL COMMENT '描述',
  `creattime` datetime DEFAULT NULL COMMENT '创建时间',
  `updatetime` datetime DEFAULT NULL COMMENT '更新时间',
  `allowunregisteredactivate` int(11) DEFAULT '1' COMMENT '是否允许未注册设备激活,0-否 1-是',
  `allowinconsistentactivate` int(11) DEFAULT '1' COMMENT '是否允许设备激活与设备注册时的信息不一致时激活,0-否 1-是',
  `syncbactchno` bigint(20) DEFAULT '0' COMMENT 'ladp自动同步批次号',
  `groupcode` varchar(500) DEFAULT NULL COMMENT '组层级编码',
  `forcemodifypwd` int(11) DEFAULT '0' COMMENT '强制初次登录修改密码 0-否 1-是',
  `maxpwdage` int(11) DEFAULT '0' COMMENT '用户密码有效期 0-永久',
  `maxfailedattempts` int(11) DEFAULT '5' COMMENT '用户密码输入尝试次数 0-不限',
  `lockinterval` int(11) DEFAULT '0' COMMENT '密码输入超过次数后锁定时长 -1 从组继承配置 0-永久锁定 N 锁定多少分钟',
  `ldapconfigid` bigint(20) DEFAULT NULL COMMENT '关联的LDAP配置',
  `needlockpasswd` int(11) DEFAULT '0' COMMENT '是否需要解锁密码,0-否 1-是',
  `forbidunload` int(11) DEFAULT '1' COMMENT '是否防止卸载，0-否 1-是',
  `passwordstrength` int(11) DEFAULT '2' COMMENT '1：弱（可以是纯字母或数字，6-16位）、2：中等（至少包含字母数字，8-16位）、3：复杂（至少包含大小写的字母以及数字，8-16位）',
  `allowactivate` int(11) NOT NULL DEFAULT '1' COMMENT '是否允许android激活设备管理器,0--不激活;1--强制激活;2--按需激活',
  `iosallowactivate` int(11) NOT NULL DEFAULT '1' COMMENT '是否允许ios激活设备管理器,0--不激活;1--强制激活;2--按需激活',
  `allowlogout` int(11) DEFAULT '1' COMMENT '允许客户端显示登出按钮，1表示允许显示，0不允许显示',
  `thirdgroupid` varchar(300) DEFAULT NULL COMMENT '同步第三方服务中组织id',
  `syncflag` int(11) DEFAULT '0' COMMENT '是否上报成功  1-上报成功 2-上报失败',
  `issyncgoogle` int(11) NOT NULL DEFAULT '0' COMMENT '是否需要同步到Google Play Enterprise; 0-否;1-是',
  `googleenterpriseid` varchar(100) DEFAULT NULL COMMENT 'Google Play Enterprise id',
  `gpath` varchar(500) DEFAULT NULL COMMENT '用户组全路径',
  `syncconnectorid` bigint(20) DEFAULT NULL COMMENT 't_connector表中connector id',
  `relationship` int(11) DEFAULT '1' COMMENT '默认设备归属，1--公司设备;2--员工设备;3--其他',
  `enabledeviceperm` int(11) DEFAULT '1' COMMENT '默认是否启用设备端权限引导，1 -- 启用；0 -- 不启用',
  `enablepermissionguide` int(11) DEFAULT '1' COMMENT '强管控权限向导，1 -- 强管控；0 -- 弱管控',
  `importtime` datetime DEFAULT NULL COMMENT '第三方系统导入的时间，暂用于iam导入',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `t_group_idx_1` (`name`,`tenantId`,`status`),
  KEY `t_group_idx_2` (`groupcode`,`tenantId`,`status`),
  KEY `t_group_idx_3` (`parentId`,`tenantId`,`status`),
  KEY `t_group_idx_4` (`guid`,`tenantId`,`status`),
  KEY `t_group_idx_5` (`thirdgroupid`,`tenantId`,`type`,`status`)
) ENGINE=InnoDB AUTO_INCREMENT=2674220854022147 DEFAULT CHARSET=utf8 COMMENT='设备用户组表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_group_center`
--

DROP TABLE IF EXISTS `t_group_center`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_group_center` (
  `id` varchar(100) NOT NULL COMMENT '组标识',
  `name` varchar(100) NOT NULL COMMENT '组名',
  `parentid` varchar(100) DEFAULT NULL COMMENT '上级组标识',
  `type` int(11) NOT NULL DEFAULT '0' COMMENT '0-无变化(默认)，1-新增 2-删除 3-基本信息变更 4-组织机构变更',
  `grage` int(11) NOT NULL DEFAULT '0' COMMENT '级别 根组级别为1',
  `displayflag` int(11) DEFAULT NULL COMMENT '是否显示 为null或1展示，0 不展示',
  `note` varchar(2000) DEFAULT NULL COMMENT '备注',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `updatetime` datetime DEFAULT NULL COMMENT '更新时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `parentid_index` (`parentid`),
  KEY `type_index` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户组中间表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_group_map`
--

DROP TABLE IF EXISTS `t_group_map`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_group_map` (
  `groupid` bigint(20) NOT NULL COMMENT '组id',
  `hrgroupid` varchar(500) NOT NULL COMMENT '层级组id',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`groupid`),
  KEY `hrgroupid_index` (`hrgroupid`(255))
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='组层级关系映射表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_illegal_smssend_log`
--

DROP TABLE IF EXISTS `t_illegal_smssend_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_illegal_smssend_log` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `alermid` bigint(20) NOT NULL,
  `deviceids` varchar(100) DEFAULT NULL,
  `policyid` varchar(100) NOT NULL DEFAULT '',
  `mobiles` text COMMENT '告警接收手机号集合',
  `emails` text COMMENT '告警接收邮件集合',
  `description` varchar(1000) DEFAULT NULL COMMENT '描述信息',
  `createtime` datetime NOT NULL,
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='告警发送信息记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_illegal_time`
--

DROP TABLE IF EXISTS `t_illegal_time`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_illegal_time` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `userid` bigint(20) NOT NULL COMMENT '用户编号',
  `deviceid` bigint(20) NOT NULL COMMENT '设备编号',
  `type` int(11) NOT NULL COMMENT '策略类型',
  `illegaldate` varchar(16) NOT NULL COMMENT '违规日期',
  `num` int(11) NOT NULL COMMENT '违规数量',
  `updatetime` datetime DEFAULT NULL COMMENT '最后更新时间',
  `username` varchar(100) DEFAULT NULL,
  `groupid` bigint(20) DEFAULT NULL,
  `groupname` varchar(100) DEFAULT NULL,
  `mobile` varchar(20) DEFAULT NULL,
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='违规次数表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_import_server`
--

DROP TABLE IF EXISTS `t_import_server`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_import_server` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `servertype` int(5) NOT NULL COMMENT '0 ftp服务器   , 1 共享目录服务器   , 2 sharepoint',
  `hostname` varchar(300) DEFAULT NULL COMMENT '所导入的服务器hostname',
  `servername` varchar(300) DEFAULT NULL COMMENT '所导入的服务器名称',
  `authname` varchar(300) DEFAULT NULL COMMENT '登录所导入的服务器账号',
  `authpassword` varchar(300) DEFAULT NULL COMMENT '登录所导入的服务器密码',
  `rootpath` varchar(300) DEFAULT NULL COMMENT '所要导入的根目录',
  `syncfrequency` int(11) DEFAULT NULL COMMENT '同步频率',
  `filecount` int(11) DEFAULT NULL COMMENT '导入的文档数量',
  `filetotalsize` bigint(50) DEFAULT NULL COMMENT '导入的文档总大小',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `updatetime` datetime DEFAULT NULL COMMENT '修改时间',
  `createby` varchar(100) DEFAULT NULL COMMENT '创建人',
  `updateby` varchar(100) DEFAULT NULL COMMENT '修改人',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='导入服务器信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ind_strategy_max_set`
--

DROP TABLE IF EXISTS `t_ind_strategy_max_set`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_ind_strategy_max_set` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `strategyid` varchar(100) DEFAULT NULL COMMENT '策略ID',
  `strategytype` int(1) DEFAULT NULL COMMENT '策略类型 1 配置策略 2 限制策略 3 合规策略 4 围栏策略',
  `loginid` varchar(50) DEFAULT NULL COMMENT '管理员id',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='首页策略显示设置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_index_app_max_set`
--

DROP TABLE IF EXISTS `t_index_app_max_set`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_index_app_max_set` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `appid` bigint(20) DEFAULT NULL COMMENT '应用ID',
  `loginid` varchar(50) DEFAULT NULL COMMENT '管理员id',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='首页应用显示表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_index_app_set`
--

DROP TABLE IF EXISTS `t_index_app_set`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_index_app_set` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `appid` bigint(20) DEFAULT NULL COMMENT '应用ID',
  `loginid` varchar(50) DEFAULT NULL COMMENT '管理员id',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='首页应用显示表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_index_chart_def`
--

DROP TABLE IF EXISTS `t_index_chart_def`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_index_chart_def` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `identifier` varchar(20) DEFAULT NULL COMMENT '标识符',
  `remark` varchar(200) DEFAULT NULL COMMENT '描述',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='首页图表默认表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_index_chart_set`
--

DROP TABLE IF EXISTS `t_index_chart_set`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_index_chart_set` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `identifier` varchar(20) DEFAULT NULL COMMENT '标识符',
  `remark` varchar(200) DEFAULT NULL COMMENT '描述',
  `loginid` varchar(50) DEFAULT NULL COMMENT '管理员id',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='首页图表设置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_index_shortcut_def`
--

DROP TABLE IF EXISTS `t_index_shortcut_def`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_index_shortcut_def` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `identifier` varchar(20) DEFAULT NULL COMMENT '功能标识符',
  `url` varchar(200) DEFAULT NULL COMMENT '功能地址',
  `classname` varchar(20) DEFAULT NULL COMMENT '功能图标class名称',
  `remark` varchar(200) DEFAULT NULL COMMENT '功能描述',
  `hoverclassname` varchar(50) DEFAULT NULL COMMENT 'hoverClass名称',
  `menuCode` varchar(20) DEFAULT NULL COMMENT '菜单code',
  `funcCode` varchar(20) DEFAULT NULL COMMENT '权限编码',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8 COMMENT='首页快捷功能默认表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_index_shortcut_set`
--

DROP TABLE IF EXISTS `t_index_shortcut_set`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_index_shortcut_set` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `identifier` varchar(20) DEFAULT NULL COMMENT '功能标识符',
  `url` varchar(200) DEFAULT NULL COMMENT '功能地址',
  `classname` varchar(20) DEFAULT NULL COMMENT '功能图标class名称',
  `remark` varchar(200) DEFAULT NULL COMMENT '功能描述',
  `loginid` varchar(50) DEFAULT NULL COMMENT '管理员id',
  `hoverclassname` varchar(50) DEFAULT NULL COMMENT 'hoverClass名称',
  `menuCode` varchar(20) DEFAULT NULL COMMENT '菜单code',
  `funcCode` varchar(20) DEFAULT NULL COMMENT '权限编码',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='首页快捷功能设置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_index_strategy_set`
--

DROP TABLE IF EXISTS `t_index_strategy_set`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_index_strategy_set` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `strategyid` varchar(100) DEFAULT NULL COMMENT '策略ID',
  `strategytype` int(1) DEFAULT NULL COMMENT '策略类型 1 配置策略 2 限制策略 3 合规策略 4 围栏策略',
  `loginid` varchar(50) DEFAULT NULL COMMENT '管理员id',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='首页策略显示设置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ios_buildin_app`
--

DROP TABLE IF EXISTS `t_ios_buildin_app`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_ios_buildin_app` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(200) NOT NULL COMMENT '名称',
  `pkgname` varchar(200) DEFAULT NULL COMMENT '应用id',
  `modeltype` varchar(20) DEFAULT '20,21,22,23' COMMENT 'MDM4.0.1开始设备型号允许多选,20-iphone 21-ipad 22-ipad mini 23-itouch',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=89 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='IOS内置应用';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ios_model_dict`
--

DROP TABLE IF EXISTS `t_ios_model_dict`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_ios_model_dict` (
  `model` varchar(200) NOT NULL COMMENT '型号(如：mc134ch)',
  `generation` varchar(200) DEFAULT NULL COMMENT '第几代(iphone 3g , iphone 4, ipad 2)',
  `variant` varchar(200) DEFAULT NULL COMMENT 'wifi , gsm ,cdma',
  `identifier` varchar(200) DEFAULT NULL COMMENT '标识: ipad1,1 ',
  `color` varchar(200) DEFAULT NULL COMMENT '颜色',
  `storage` varchar(200) DEFAULT NULL COMMENT '存储: 8g , 16g , 32g ,64g',
  PRIMARY KEY (`model`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='ios型号字典表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_label`
--

DROP TABLE IF EXISTS `t_label`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_label` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '标签id',
  `name` varchar(100) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL COMMENT '标签名称',
  `type` int(11) NOT NULL COMMENT '标签类型（静态-1、动态-2）',
  `description` varchar(200) DEFAULT NULL COMMENT '标签描述',
  `creator` varchar(50) NOT NULL COMMENT '创建者',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  `updatetime` datetime NOT NULL COMMENT '更新时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='标签表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_label_device`
--

DROP TABLE IF EXISTS `t_label_device`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_label_device` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '设备标签id',
  `name` varchar(100) NOT NULL COMMENT '设备标签名称',
  `type` int(11) NOT NULL COMMENT '设备标签类型（静态-1、动态-2）',
  `description` varchar(200) DEFAULT NULL COMMENT '设备标签描述',
  `creator` varchar(50) NOT NULL COMMENT '创建者',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  `updatetime` datetime NOT NULL COMMENT '更新时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备标签表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_last_location`
--

DROP TABLE IF EXISTS `t_last_location`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_last_location` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `deviceid` bigint(20) NOT NULL,
  `udid` char(64) DEFAULT NULL,
  `createtime` datetime DEFAULT NULL COMMENT '指令下发时间',
  `updatetime` datetime DEFAULT NULL COMMENT '执行完成更新时间',
  `longitude` double(10,6) DEFAULT NULL COMMENT '经度',
  `latitude` double(10,6) DEFAULT NULL COMMENT '纬度',
  `location` varchar(200) DEFAULT NULL COMMENT '定位地点',
  `locatedtime` datetime DEFAULT NULL COMMENT '定位时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `idx_tll_deviceid` (`deviceid`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8 COMMENT='设备最近定位表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_ldap_config`
--

DROP TABLE IF EXISTS `t_ldap_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_ldap_config` (
  `id` bigint(20) NOT NULL,
  `type` varchar(10) NOT NULL COMMENT 'ad or ldap',
  `rootname` varchar(100) NOT NULL COMMENT '导入到的根组名称',
  `url` varchar(100) NOT NULL COMMENT '地址+端口',
  `base` varchar(100) NOT NULL COMMENT 'baseDN',
  `domain` varchar(100) DEFAULT NULL COMMENT '域（仅ad需要）',
  `username` varchar(100) NOT NULL COMMENT '登录用户名',
  `password` varchar(100) NOT NULL COMMENT '登录密码',
  `loginfilter` varchar(100) DEFAULT NULL COMMENT '账号名匹配模式',
  `conflicthandle` int(5) DEFAULT '0' COMMENT '导入冲突处理方式 0-不导入 1-直接覆盖',
  `ldapverify` int(5) DEFAULT '0' COMMENT '是否采用ldap认证 0-否 1-是',
  `autosyncinterval` int(5) DEFAULT '0' COMMENT '自动同步周期（小时） 0-不自动同步',
  `parentgroupid` bigint(20) DEFAULT '0' COMMENT '导入到的目标组id',
  `parentgroupname` varchar(100) DEFAULT NULL COMMENT '导入到的目标组名称',
  `lastsyncstarttime` datetime DEFAULT NULL COMMENT '上次同步开始时间',
  `lastsynctime` datetime DEFAULT NULL COMMENT '上次同步结束时间',
  `lastsyncresult` varchar(100) DEFAULT NULL COMMENT '上次同步结果',
  `loginid` varchar(50) DEFAULT NULL COMMENT 't_user中loginid对应的ldap属性',
  `name` varchar(50) DEFAULT NULL COMMENT 't_user中name对应的ldap属性',
  `mail` varchar(50) DEFAULT NULL COMMENT 't_user中mail对应的ldap属性',
  `mobile` varchar(50) DEFAULT NULL COMMENT 't_user中mobile对应的ldap属性',
  `syncflag` int(5) DEFAULT '0' COMMENT '导入完成标志 0-导入完成 1-正在导入',
  `userfilter` varchar(100) DEFAULT NULL COMMENT '用户匹配模式',
  `oufilter` varchar(100) DEFAULT NULL COMMENT '用户组匹配模式',
  `lastsyncusers` int(10) DEFAULT NULL COMMENT '上次导入用户数',
  `lastsyncugroups` int(10) DEFAULT NULL COMMENT '上次导入用户组数',
  `groupname` varchar(100) DEFAULT NULL COMMENT '组名',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='ldap配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_location_history`
--

DROP TABLE IF EXISTS `t_location_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_location_history` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `deviceid` bigint(20) NOT NULL,
  `udid` char(64) DEFAULT NULL,
  `uuid` varchar(100) DEFAULT NULL COMMENT '指令uuid',
  `createtime` datetime DEFAULT NULL COMMENT '指令下发时间',
  `updatetime` datetime DEFAULT NULL COMMENT '执行完成更新时间',
  `status` int(11) DEFAULT NULL COMMENT '执行状态,0:指令下发未执行;1:执行成功;21--执行失败',
  `longitude` double(10,6) DEFAULT NULL COMMENT '经度',
  `latitude` double(10,6) DEFAULT NULL COMMENT '纬度',
  `location` varchar(200) DEFAULT NULL COMMENT '定位地点',
  `locatedtime` datetime DEFAULT NULL COMMENT '定位时间',
  `dayflag` int(11) DEFAULT NULL COMMENT '日期标记',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  KEY `idx_tlh_id` (`id`),
  KEY `idx_tlh_deviceid` (`deviceid`),
  KEY `idx_tlh_dayflag` (`dayflag`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8 COMMENT='设备定位历史表'
/*!50100 PARTITION BY HASH (dayflag)
PARTITIONS 60 */;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_malware_list`
--

DROP TABLE IF EXISTS `t_malware_list`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_malware_list` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `pkgname` varchar(200) DEFAULT NULL COMMENT '应用包名',
  `uuid` varchar(200) DEFAULT NULL COMMENT 'UUID',
  `version` varchar(50) DEFAULT NULL COMMENT '版本',
  `name` varchar(200) DEFAULT NULL COMMENT '名称',
  `scanid` bigint(20) NOT NULL COMMENT '扫描ID(t_virusscan_history的ID)',
  `handlestatus` int(10) DEFAULT '0' COMMENT '危险应用处理状态(0为未处理，1为删除， 2为忽略)',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `malwareid` varchar(40) DEFAULT NULL COMMENT '病毒序列',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='危险应用表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_mdm_log_device_violation`
--

DROP TABLE IF EXISTS `t_mdm_log_device_violation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_mdm_log_device_violation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户id',
  `login_id` varchar(50) DEFAULT NULL COMMENT '用户账号',
  `user_name` varchar(50) DEFAULT NULL COMMENT '用户姓名',
  `device_id` bigint(20) DEFAULT NULL COMMENT '设备id',
  `udid` varchar(80) DEFAULT NULL COMMENT '设备udid',
  `device_name` varchar(80) DEFAULT NULL COMMENT '设备名称',
  `model` varchar(50) DEFAULT NULL COMMENT '设备型号',
  `violation_type` int(11) DEFAULT NULL COMMENT '违规类型，例如 0设备ROOT  1系统版本过低',
  `violation_status` int(11) DEFAULT NULL COMMENT '违规状态，0合规  1违规',
  `violation_process` int(11) DEFAULT NULL COMMENT '违规处理结果，0无  1擦除数据  2禁止程序访问内网',
  `violation_time` datetime DEFAULT NULL COMMENT '违规发生时间',
  `description` varchar(200) DEFAULT NULL COMMENT '备注信息',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `t_mdm_log_device_violation` (`violation_time`,`tenantId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备违规及合规日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_mdm_policy`
--

DROP TABLE IF EXISTS `t_mdm_policy`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_mdm_policy` (
  `id` varchar(100) NOT NULL COMMENT 'uuid',
  `name` varchar(200) NOT NULL COMMENT '名称',
  `category` int(11) DEFAULT NULL COMMENT '策略大的分类，配置策略、限制策略、围栏策略、合规策略等',
  `sub_category` int(11) DEFAULT NULL COMMENT '策略小的分类，WiFi配置策略、VPN配置策略等',
  `create_by` varchar(200) DEFAULT NULL COMMENT '创建人姓名',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_by` varchar(200) DEFAULT NULL COMMENT '更新人姓名',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `status` int(11) DEFAULT '0' COMMENT '策略状态,-1--删除;0--禁用;1--启用',
  `content` longtext COMMENT '扩展存放json',
  `deleted` tinyint(2)  default '0' comment '逻辑删除标志',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `t_mdm_policy_idx_1` (`status`,`deleted`,`tenantId`,`sub_category`,`update_time`,`category`,`create_time`),
  KEY `t_mdm_policy_idx_2` (`deleted`,`status`,`tenantId`,`sub_category`,`update_time`,`create_time`,`category`)

) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_mdm_policy_global`
--

DROP TABLE IF EXISTS `t_mdm_policy_global`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_mdm_policy_global` (
  `id` varchar(100) NOT NULL COMMENT 'uuid',
  `name` varchar(200) NOT NULL COMMENT '名称',
  `category` int(11) DEFAULT NULL COMMENT '策略大的分类，配置策略、限制策略、围栏策略、合规策略等',
  `sub_category` int(11) DEFAULT NULL COMMENT '策略小的分类，WiFi配置策略、VPN配置策略等',
  `update_by` varchar(200) DEFAULT NULL COMMENT '更新人姓名',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `content` longtext COMMENT '扩展存放json',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='全局默认策略表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_mem_task`
--

DROP TABLE IF EXISTS `t_mem_task`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_mem_task` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '任务id',
  `type` int(5) DEFAULT NULL COMMENT '任务类型',
  `times` int(5) DEFAULT NULL COMMENT '执行次数',
  `priority` int(5) DEFAULT NULL COMMENT '优先级',
  `failcause` varchar(300) DEFAULT NULL COMMENT '失败原因',
  `loginid` varchar(50) DEFAULT NULL COMMENT '用户名',
  `udid` char(64) DEFAULT NULL COMMENT '设备唯一标识',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `updatetime` datetime DEFAULT NULL COMMENT '修改时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='mem任务信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_notification`
--

DROP TABLE IF EXISTS `t_notification`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_notification` (
  `id` varchar(100) NOT NULL COMMENT '主键',
  `clientId` varchar(100) NOT NULL COMMENT '通知所对应的clientId',
  `message` longtext NOT NULL COMMENT '通知的具体内容',
  `status` smallint(6) NOT NULL COMMENT '状态 0 - SENDING, -1 - TIMEOUT, -2 - REFUSED, 2000 - SUCCESS, 5001 - ERROR',
  `type` varchar(100) NOT NULL COMMENT '类型 pf - Profile变更， pl - 策略下发',
  `silent` smallint(6) NOT NULL COMMENT '1 - 静默 2 - 非静默',
  `event` varchar(100) NOT NULL COMMENT '触发下发消息的事件',
  `comments` varchar(500) DEFAULT NULL COMMENT '关于通知的一些备注信息',
  `createdDatetime` datetime NOT NULL COMMENT '创建时间',
  `updatedDatetime` datetime NOT NULL COMMENT '修改时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `notification_client_id` (`clientId`),
  KEY `notification_type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='notification表, 用于存储BeMail Console向Client发送的推送消息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_notifications_logon`
--

DROP TABLE IF EXISTS `t_notifications_logon`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_notifications_logon` (
  `notification_id` bigint(20) unsigned NOT NULL COMMENT '客户端程序负责维护',
  `create_time` datetime NOT NULL COMMENT '插入时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '标记逻辑删除，0:未删除，1:删除',
  `read` tinyint(4) NOT NULL DEFAULT '0' COMMENT '已读未读：0:未定义;1:未读;2:已读',
  `sender` varchar(255) NOT NULL COMMENT '可以理解为发件人名称',
  `received` varchar(50) NOT NULL COMMENT 'reference t_sys_user.loginid',
  `content` mediumtext NOT NULL COMMENT '极限内容按5000设备bigint(20)长度计算确定的datatype',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`notification_id`),
  KEY `idx_t_notifications_logon_create_time` (`create_time`),
  KEY `idx_t_notifications_logon_create_time_deleted_received` (`deleted`,`received`,`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='通知中心消息历史纪录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_perm_template`
--

DROP TABLE IF EXISTS `t_perm_template`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_perm_template` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '模板名称',
  `description` varchar(200) DEFAULT NULL COMMENT '描述',
  `sysflag` int(5) DEFAULT '0' COMMENT '是否系统内置 0-否 1-是',
  `loginid` varchar(50) DEFAULT NULL COMMENT '管理员id',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `loginid` (`loginid`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8 COMMENT='权限模板';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_perm_template_api`
--

DROP TABLE IF EXISTS `t_perm_template_api`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_perm_template_api` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `templateid` bigint(20) NOT NULL COMMENT '权限模板id',
  `apicode` varchar(50) DEFAULT NULL,
  `funccode` varchar(50) DEFAULT NULL,
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `t_perm_template_api_fk` (`templateid`)
) ENGINE=InnoDB AUTO_INCREMENT=510 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_perm_template_menu`
--

DROP TABLE IF EXISTS `t_perm_template_menu`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_perm_template_menu` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `templateid` bigint(20) NOT NULL COMMENT '权限模板id',
  `code` varchar(50) NOT NULL COMMENT '菜单编号',
  `authority` int(5) DEFAULT '0' COMMENT '菜单对应的权限 0-查询 1-管理',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `t_perm_template_menu_ibfk_1` (`templateid`),
  KEY `t_perm_template_menu_ibfk_2` (`code`)
) ENGINE=InnoDB AUTO_INCREMENT=33 DEFAULT CHARSET=utf8 COMMENT='权限模板对应的菜单权限';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_policy`
--

DROP TABLE IF EXISTS `t_policy`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_policy` (
  `id` varchar(100) NOT NULL COMMENT 'uuid',
  `payloaddisplayname` varchar(200) NOT NULL COMMENT '名称',
  `payloadidentifier` varchar(200) DEFAULT NULL COMMENT '标识符',
  `payloadtype` varchar(100) DEFAULT NULL,
  `payloadorganization` varchar(200) DEFAULT NULL COMMENT '机构',
  `payloaddescription` varchar(1000) DEFAULT NULL COMMENT '描述',
  `payloadremovaltype` int(11) DEFAULT NULL COMMENT '安全性      总是--0;鉴定--1;永不--2',
  `removalpassword` varchar(50) DEFAULT NULL COMMENT '授权密码',
  `removalpasswordpayloaduuid` varchar(100) DEFAULT NULL,
  `removalpasswordpayloadtype` varchar(100) DEFAULT NULL,
  `removalpwdpayloadidentifier` varchar(200) DEFAULT NULL,
  `loginid` varchar(50) NOT NULL COMMENT '创建人',
  `creator` varchar(200) DEFAULT NULL COMMENT '创建人姓名',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  `updatetime` datetime DEFAULT NULL COMMENT '更新时间',
  `status` int(11) DEFAULT '0' COMMENT '策略状态,-1--删除;0--禁用;1--启用',
  `type` int(11) DEFAULT '0' COMMENT '默认为0 5-Android限制策略 6-IOS限制策略 7-KNOX限制策略 8-SAFE 限制策略 9-Android配置策略 10-IOS配置策略 11-KNOX配置策略 12-SAFE配置策略 13-Android时间围栏策略 14-IOS时间围栏策略 15-Android地理围栏策略 16-IOS地理围栏策略 17-华为限制策略',
  `fenceid` bigint(20) DEFAULT NULL COMMENT '关联的围栏ID',
  `priority` int(11) DEFAULT NULL COMMENT '策略优先级',
  `platformtype` int(5) NOT NULL DEFAULT '1' COMMENT '适用平台 1--android;2--ios',
  `policytype` int(11) NOT NULL DEFAULT '0' COMMENT '策略类型,0--自定义;10--内置默认;11--默认可修改',
  `minversion` varchar(50) DEFAULT NULL COMMENT '最低操作系统版本,如:5.0.0(ios),''''--任意',
  `devicetype` int(11) DEFAULT '0' COMMENT '设备类型 10-android phone 11-android pad;20-iphone 21-ipad 22-ipadmini 23- ipodtouch',
  `issuedobjectflag` int(11) DEFAULT '0' COMMENT '分发对象标识  0--自定义;1--所有员工',
  `relationship` varchar(50) DEFAULT '1,2,3' COMMENT '设备所属关系  1--公司设备;2--员工设备;3--其他,多种关系使用逗号分隔',
  `ownerid` varchar(50) NOT NULL COMMENT '管理者id',
  `extensionjson` longtext COMMENT '扩展存放json',
  `policypushtime` varchar(50) DEFAULT NULL COMMENT '策略推送时间',
  `deletedelaytime` varchar(50) DEFAULT NULL COMMENT '延迟删除时间',
  `specialtypeflag` int(11) DEFAULT '0' COMMENT '策略适用的特殊设备类型标识.0--普通设备;100--SamSung SAFE设备;101--SamSung KNOX设备;301--华为设备',
  `cmds` varchar(100) DEFAULT NULL COMMENT '命令码集合',
  `disttype` int(1) DEFAULT '0' COMMENT '0-用户／用户组 1-标签',
  `originJson` longtext COMMENT '传入原始参数json',
  `policyitem` int(11) DEFAULT NULL COMMENT '二进制表示的策略项目配置情况,例如二进制0101表示该策略的第1,3项目配置,其他未配置',
  `copynum` int(11) DEFAULT '1' COMMENT '策略复制编号',
  `oldwrappapp` longtext COMMENT '保存更新前的沙箱应用',
  `alarmid` bigint(20) DEFAULT NULL COMMENT '告警',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `fk_t_policy_fenceid_t_fence` (`fenceid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_policy_device`
--

DROP TABLE IF EXISTS `t_policy_device`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_policy_device` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `policyId` varchar(100) NOT NULL COMMENT '策略id',
  `deviceId` bigint(20) NOT NULL COMMENT '设备id',
  `customflag` int(11) DEFAULT '0' COMMENT '自定义标识: 0-继承 ;1-自定义',
  `exceptionflag` int(11) DEFAULT '0' COMMENT '例外标识: 0-添加 ;1-排除',
  `pushId` bigint(20) DEFAULT NULL,
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `t_policy_device_ibfk_1` (`policyId`),
  KEY `t_policy_device_ibfk_2` (`deviceId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='策略按设备下发关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_policy_group`
--

DROP TABLE IF EXISTS `t_policy_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_policy_group` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `policyid` varchar(100) NOT NULL COMMENT '策略id',
  `groupid` bigint(20) NOT NULL,
  `customflag` int(11) NOT NULL DEFAULT '0' COMMENT '自定义标识: 0-继承 ;1-自定义',
  `pushid` bigint(20) DEFAULT NULL,
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `policyid` (`policyid`),
  KEY `groupid` (`groupid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_policy_mdm`
--

DROP TABLE IF EXISTS `t_policy_mdm`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_policy_mdm` (
  `id` varchar(100) NOT NULL COMMENT 'uuid',
  `uuid` varchar(100) NOT NULL,
  `payloadidentifier` varchar(200) DEFAULT NULL COMMENT '标识符',
  `payloadtype` varchar(100) DEFAULT NULL,
  `serverurl` varchar(100) NOT NULL COMMENT '服务器url',
  `checkinurl` varchar(100) NOT NULL COMMENT '登记url',
  `topic` varchar(100) NOT NULL DEFAULT ' ' COMMENT '主题',
  `identitycertificateuuid` varchar(100) NOT NULL COMMENT '身份',
  `signmessage` tinyint(4) DEFAULT NULL COMMENT '给信息签名 0-false;1-true',
  `checkoutwhenremoved` tinyint(4) DEFAULT NULL COMMENT '移除时检查 0-false;1-true',
  `usedevelopmentapns` tinyint(4) DEFAULT NULL COMMENT '使用开发apns服务器 0-false;1-true',
  `accessrights` int(11) DEFAULT NULL COMMENT '访问权限选项全选，包括"在设备中查询"、"添加/移除"、安全性',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `uuid` (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_policy_push`
--

DROP TABLE IF EXISTS `t_policy_push`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_policy_push` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '推送id',
  `policyid` varchar(100) NOT NULL COMMENT '策略id',
  `relationship` varchar(50) NOT NULL COMMENT '推送设备所属关系：0-全部；1-公司；2-员工；3-其他',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `idx_policy_id_push` (`policyid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='策略推送表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_policy_scep`
--

DROP TABLE IF EXISTS `t_policy_scep`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_policy_scep` (
  `id` varchar(100) NOT NULL COMMENT 'uuid',
  `uuid` varchar(100) NOT NULL,
  `payloadidentifier` varchar(200) DEFAULT NULL COMMENT '标识符',
  `payloadtype` varchar(100) DEFAULT NULL,
  `url` varchar(100) NOT NULL COMMENT '服务器url',
  `name` varchar(200) NOT NULL DEFAULT '' COMMENT '名称',
  `subject` varchar(100) NOT NULL COMMENT '主题',
  `challenge` varchar(100) DEFAULT '' COMMENT '口令',
  `keytype` varchar(50) DEFAULT NULL COMMENT 'currently always rsa',
  `keyusage` int(11) DEFAULT '0' COMMENT '用做数字签名(1),用于密匙加密(4)',
  `subjectaltnametype` int(11) DEFAULT '0' COMMENT '主题备用名称类型   0--无,1--rfc 822 name,2--dns 名称,3--统一资源标识符',
  `ntprincipalname` varchar(100) DEFAULT NULL COMMENT 'nt 主题名称 ',
  `rfc822name` varchar(100) DEFAULT NULL COMMENT '主题备用名称值(主题备用名称类型  为1--rfc 822 name)',
  `dnsname` varchar(100) DEFAULT NULL COMMENT '主题备用名称值(主题备用名称类型 为2--dns 名称)',
  `uniformresourceidentifier` varchar(100) DEFAULT NULL COMMENT '主题备用名称值(主题备用名称类型 为3--统一资源标识符)',
  `cafingerprint` varchar(100) DEFAULT NULL COMMENT '指纹',
  `cafingerprintsha1` varchar(100) DEFAULT NULL COMMENT '指纹sha-1显示值',
  `retries` int(11) DEFAULT NULL COMMENT '重试次数',
  `retrydelay` int(11) DEFAULT NULL COMMENT 'retrydelay,重试间隔时间(秒)',
  `keysize` int(11) DEFAULT NULL COMMENT '密匙大小(位)',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `uuid` (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_policy_user`
--

DROP TABLE IF EXISTS `t_policy_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_policy_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `policyid` varchar(100) NOT NULL COMMENT '策略id',
  `userid` bigint(20) NOT NULL COMMENT '用户id',
  `customflag` int(11) DEFAULT '0' COMMENT '自定义标识: 0-继承 ;1-自定义',
  `sub_category` int(11) DEFAULT '0' COMMENT '策略子类型',
  `exceptionflag` int(11) DEFAULT '0' COMMENT '例外标识: 0-添加 ;1-排除',
  `pushid` bigint(20) DEFAULT NULL,
  `deleted` tinyint(2)  default '0' comment '逻辑删除标志',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `t_policy_user_idx_1` (`userid`,`tenantId`,`policyid`,`deleted`,`sub_category`,`pushid`),
  KEY `t_policy_user_idx_2` (`policyid`,`tenantId`,`userid`,`deleted`,`sub_category`,`pushid`),
  KEY `t_policy_user_idx_3` (`sub_category`,`tenantId`,`userid`,`deleted`,`policyid`,`pushid`)
) ENGINE=InnoDB AUTO_INCREMENT=1195738335241605123 DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_push_info`
--

DROP TABLE IF EXISTS `t_push_info`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_push_info` (
  `id` bigint(10) NOT NULL AUTO_INCREMENT COMMENT '主键id，自增长',
  `udid` char(64) NOT NULL COMMENT '设备udid',
  `uuid` varchar(50) NOT NULL COMMENT 'uuid',
  `status` int(2) DEFAULT '0' COMMENT '0-未读，1-已读，10-已删除',
  `source` int(2) DEFAULT '0' COMMENT '消息来源,0-mdm,1-mcm,2-platform,...',
  `content` varchar(512) DEFAULT NULL COMMENT '消息内容',
  `resourceuri` varchar(300) DEFAULT NULL COMMENT '资源uri',
  `createtime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updatetime` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '上次更新时间',
  `domaintype` int(11) DEFAULT '0' COMMENT '是否企业域应用：0否 1是 默认0',
  `infotype` int(11) DEFAULT '0' COMMENT '0: 系统消息  1：手工通知消息',
  `userid` bigint(20) DEFAULT NULL COMMENT '用户id',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `idx_t_push_info_udid` (`udid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_remote_desktop_log`
--

DROP TABLE IF EXISTS `t_remote_desktop_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_remote_desktop_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `deviceid` bigint(20) NOT NULL,
  `udid` char(64) DEFAULT NULL,
  `uuid` varchar(100) DEFAULT NULL COMMENT '指令uuid',
  `createtime` datetime DEFAULT NULL COMMENT '指令下发时间',
  `updatetime` datetime DEFAULT NULL COMMENT '执行完成更新时间',
  `status` int(11) DEFAULT '0' COMMENT '执行状态,0：结果未上报1：成功2：RC APP未安装3：RC APP启动失败4：用户授权拒绝5：用户拒绝6：连接RC服务器失败7：连接已占用',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `deviceid` (`deviceid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备远程控制指令表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_report`
--

DROP TABLE IF EXISTS `t_report`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_report` (
  `id` varchar(50) NOT NULL COMMENT 'uuid',
  `reportname` varchar(200) NOT NULL COMMENT '报表名称',
  `reporttype` varchar(50) NOT NULL COMMENT '报表类别',
  `description` varchar(200) DEFAULT NULL COMMENT '报表描述',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='报表表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_report_chats_content`
--

DROP TABLE IF EXISTS `t_report_chats_content`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_report_chats_content` (
  `id` bigint(50) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `deviceid` varchar(50) NOT NULL COMMENT '设备id',
  `userid` bigint(20) NOT NULL COMMENT '用户id',
  `appid` bigint(20) DEFAULT NULL COMMENT 'APP id',
  `appPkg` varchar(4000) NOT NULL COMMENT 'app package name',
  `appName` varchar(200) NOT NULL COMMENT 'app name',
  `msgType` int(11) DEFAULT NULL COMMENT '1发送，0接受',
  `contacts` varchar(255) DEFAULT NULL COMMENT '联系人',
  `keyword` varchar(255) DEFAULT NULL COMMENT '关键字',
  `chatContent` varchar(13000) DEFAULT NULL COMMENT '聊天内容',
  `occurredTime` timestamp NULL DEFAULT NULL COMMENT '聊天发生时间',
  `collectionTime` timestamp NULL DEFAULT NULL COMMENT '收集时间',
  `reportTime` timestamp NULL DEFAULT NULL COMMENT '上报时间',
  `updateTime` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `contenttype` int(11) DEFAULT '1' COMMENT '1:聊天审计日志 2:网页关键字过滤日志',
  `updatestatus` int(11) NOT NULL DEFAULT '0' COMMENT '更新状态(0：未更新，1：已更新)',
  `policyid` varchar(100) DEFAULT NULL,
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='聊天内容报表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_report_chats_file`
--

DROP TABLE IF EXISTS `t_report_chats_file`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_report_chats_file` (
  `id` bigint(50) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `deviceid` varchar(50) NOT NULL COMMENT '设备id',
  `userid` bigint(20) NOT NULL COMMENT '用户id',
  `appid` bigint(20) DEFAULT NULL COMMENT 'APP id',
  `appPkg` varchar(200) DEFAULT NULL COMMENT 'app package name',
  `appName` varchar(200) DEFAULT NULL COMMENT 'app name',
  `chatsType` varchar(255) DEFAULT NULL COMMENT '信息类型',
  `filePath` varchar(255) NOT NULL COMMENT '日志路径',
  `originFileName` varchar(255) NOT NULL COMMENT '文件名',
  `fileType` varchar(255) NOT NULL COMMENT '文件类型',
  `fileSize` varchar(255) NOT NULL COMMENT '文件大小',
  `collectionTime` timestamp NULL DEFAULT NULL COMMENT '日志收集时间',
  `reportTime` timestamp NULL DEFAULT NULL COMMENT '上报时间',
  `updateTime` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `audittype` int(11) DEFAULT '1' COMMENT '1:聊天审计日志 2:网页发帖搜索日志',
  `account` varchar(100) DEFAULT NULL COMMENT '微信账号',
  `version` varchar(50) DEFAULT NULL COMMENT '版本',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='聊天日志文件报表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_report_schedule`
--

DROP TABLE IF EXISTS `t_report_schedule`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_report_schedule` (
  `id` varchar(50) NOT NULL COMMENT 'uuid',
  `subsid` varchar(50) NOT NULL COMMENT '订阅id',
  `reportid` varchar(50) NOT NULL COMMENT '报表id',
  `jobname` varchar(100) NOT NULL COMMENT 'job name',
  `jobgroup` varchar(100) NOT NULL COMMENT 'job group',
  `triggername` varchar(100) NOT NULL COMMENT 'trigger name',
  `triggergroup` varchar(100) NOT NULL COMMENT 'trigger group',
  `cronexpression` varchar(200) NOT NULL COMMENT 'cron表达式',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `subsid` (`subsid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='报表订阅排程表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_report_subs_param`
--

DROP TABLE IF EXISTS `t_report_subs_param`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_report_subs_param` (
  `id` varchar(50) NOT NULL COMMENT 'uuid',
  `subsid` varchar(50) NOT NULL COMMENT '订阅id',
  `reportid` varchar(50) NOT NULL COMMENT '报表id',
  `paramname` varchar(100) NOT NULL COMMENT '参数名',
  `paramvalue` varchar(500) NOT NULL COMMENT '参数数值',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `subsid` (`subsid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='报表订阅之报表参数表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_report_subscription`
--

DROP TABLE IF EXISTS `t_report_subscription`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_report_subscription` (
  `id` varchar(50) NOT NULL COMMENT 'uuid',
  `reportid` varchar(50) NOT NULL COMMENT '报表id',
  `creater` varchar(50) NOT NULL COMMENT '订阅创建人',
  `subsname` varchar(200) NOT NULL COMMENT '订阅名称',
  `reportdoctype` int(11) NOT NULL COMMENT '报表文档类型,0--excel;1--pdf',
  `incomingmail` varchar(200) NOT NULL COMMENT '接收邮件地址',
  `cycletype` int(11) NOT NULL COMMENT '订阅周期类型,0--日报;1--周报;2--月报',
  `reportcycle` int(11) DEFAULT '0' COMMENT '报表周期，0:关注所有；1：关注本期',
  `substime` varchar(100) NOT NULL COMMENT '订阅的时间,日报时间如hh:mm;周报为本周周几;月报为本月第几天',
  `description` varchar(200) DEFAULT NULL COMMENT '订阅描述',
  `lastexetime` datetime DEFAULT NULL COMMENT '上次执行时间',
  `lastexestatus` int(11) DEFAULT NULL COMMENT '上次执行状态,0--成功;-1--失败',
  `filepath` varchar(300) DEFAULT NULL COMMENT '报表文档路径',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `reportid` (`reportid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='报表订阅表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_reset_password`
--

DROP TABLE IF EXISTS `t_reset_password`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_reset_password` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `userid` varchar(100) NOT NULL COMMENT '用户id',
  `token` varchar(200) NOT NULL COMMENT '重置密码token',
  `status` int(11) NOT NULL COMMENT '链接是否有效 1-有效 0-无效 -1-删除',
  `creattime` datetime DEFAULT NULL COMMENT '创建时间',
  `updatetime` datetime DEFAULT NULL COMMENT '更新时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='密码重置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_restrictions_samsung`
--

DROP TABLE IF EXISTS `t_restrictions_samsung`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_restrictions_samsung` (
  `id` varchar(100) NOT NULL COMMENT 'uuid',
  `uuid` varchar(100) NOT NULL,
  `allowappfromwhitelistuninstall` tinyint(4) DEFAULT '0' COMMENT '允许删除企业应用（应用商店中的必装应用） 0-不允许；1-允许',
  `allowbluetooth` tinyint(4) DEFAULT '0' COMMENT '是否启用蓝牙 0-不启用；1-启用',
  `allowcamera` tinyint(4) DEFAULT '0' COMMENT '是否启用相机 0-不启用；1-启用',
  `allowmicrophone` tinyint(4) DEFAULT '0' COMMENT '是否启用麦克风 0-不启用；1-启用',
  `allowwifi` tinyint(4) DEFAULT '0' COMMENT '启用wifi 0-不启用；1-启用',
  `allownfc` tinyint(4) DEFAULT '0' COMMENT '是否启用nfc 0-不启用；1-启用',
  `allowmocklocation` tinyint(4) DEFAULT '0' COMMENT '是否启用gps 0-不启用；1-启用',
  `allowsdcard` tinyint(4) DEFAULT '0' COMMENT '是否启用sd卡 0-不启用；1-启用',
  `allowusb` tinyint(4) DEFAULT '0' COMMENT 'usb连接 0-不启用；1-启用',
  `allowvpn` tinyint(4) DEFAULT '0' COMMENT '允许vpn 0-不启用；1-启用',
  `allowotaupgrade` tinyint(4) DEFAULT '0' COMMENT '允许os升级 0-不启用；1-启用',
  `allow3gdata` tinyint(4) DEFAULT '0' COMMENT '允许3g数据 0-不启用；1-启用',
  `allowsettingschanges` tinyint(4) DEFAULT '0' COMMENT '是否允许访问“设置”选项 0-不启用；1-启用',
  `allowbrowser` tinyint(4) DEFAULT '0' COMMENT '允许使用原生浏览器 0-不启用；1-启用',
  `allowroamingvoicecalls` tinyint(4) DEFAULT '0' COMMENT '允许语音漫游 0-不启用；1-启用',
  `allowroamingdata` tinyint(4) DEFAULT '0' COMMENT '允许数据漫游 0-不启用；1-启用',
  `allowroamingmsg` tinyint(4) DEFAULT '0' COMMENT '允许发送短信 0-不启用；1-启用',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `uuid` (`uuid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='safe设备(samsung)限制策略';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_server`
--

DROP TABLE IF EXISTS `t_server`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_server` (
  `id` varchar(100) NOT NULL COMMENT '主键',
  `name` varchar(100) NOT NULL COMMENT '名称',
  `version` varchar(30) NOT NULL COMMENT '版本',
  `type` varchar(20) NOT NULL COMMENT '类型 BMNS - BeMail Notification Server',
  `url` varchar(200) DEFAULT NULL COMMENT 'RESTful Base URL',
  `authToken` varchar(500) DEFAULT NULL COMMENT '回调Server URL的Auth Token',
  `bcAuthToken` varchar(500) DEFAULT NULL COMMENT '访问BeMail Console的Auth Token',
  `lastCheckTimestamp` bigint(30) DEFAULT NULL COMMENT 'Server上一次响应的时间戳',
  `createdDatetime` datetime NOT NULL COMMENT '创建时间',
  `updatedDatetime` datetime NOT NULL COMMENT '修改时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `server_type_name_version` (`type`,`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='server 表, 用于存储其他server在BeMail Console中的注册信息';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_static_label_device`
--

DROP TABLE IF EXISTS `t_static_label_device`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_static_label_device` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `labelId` bigint(20) NOT NULL COMMENT '设备标签id',
  `deviceId` bigint(20) NOT NULL COMMENT '设备id',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `index_unique_labelid_deviceid` (`labelId`,`deviceId`),
  KEY `t_static_label_device_ibfk_2` (`deviceId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='静态设备标签关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_static_label_user`
--

DROP TABLE IF EXISTS `t_static_label_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_static_label_user` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'uuid',
  `labelid` bigint(20) NOT NULL COMMENT '标签id',
  `userid` bigint(20) NOT NULL COMMENT '用户id',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `index_unique_labelid_deviceid` (`labelid`,`userid`),
  UNIQUE KEY `id_UNIQUE` (`id`),
  KEY `t_static_label_ibfk_2_idx` (`userid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='静态标签用户表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_sys_config`
--

DROP TABLE IF EXISTS `t_sys_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_sys_config` (
  `id` varchar(64) NOT NULL,
  `prop_key` varchar(256) DEFAULT NULL,
  `prop_value` varchar(2048) DEFAULT NULL,
  `note` varchar(1024) DEFAULT NULL,
  `config_file_name` varchar(128) DEFAULT NULL,
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`,`tenantId`) USING BTREE,
  KEY `t_sys_config_idx_1` (`prop_key`(255),`config_file_name`,`tenantId`),
  KEY `t_sys_config_idx_2` (`config_file_name`,`tenantId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_sys_dictionary`
--

DROP TABLE IF EXISTS `t_sys_dictionary`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_sys_dictionary` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `dictname` varchar(50) NOT NULL,
  `description` varchar(255) NOT NULL,
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`,`tenantId`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1000 DEFAULT CHARSET=utf8 COMMENT='系统字典表,系统版本、运营商、手机品牌 ';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_sys_dictionary_value`
--

DROP TABLE IF EXISTS `t_sys_dictionary_value`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_sys_dictionary_value` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `dictkey` varchar(50) NOT NULL,
  `dictvalue` varchar(50) NOT NULL,
  `status` int(11) NOT NULL COMMENT '启用禁用  1:启用 0:禁用',
  `dicttype` bigint(20) NOT NULL COMMENT '关联t_sys_dictionary',
  `fromtype` int(11) NOT NULL COMMENT '数据来源 1:预置数据 2:管理员添加 ',
  `creator` varchar(50) DEFAULT NULL,
  `createtime` datetime DEFAULT NULL,
  `updater` varchar(50) DEFAULT NULL,
  `updatetime` datetime DEFAULT NULL,
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `t_sys_dictionary_value_ibfk_1` (`dicttype`)
) ENGINE=InnoDB AUTO_INCREMENT=349 DEFAULT CHARSET=utf8 COMMENT='字典值表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_sys_log`
--

DROP TABLE IF EXISTS `t_sys_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_sys_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `userid` bigint(20) DEFAULT NULL COMMENT '用户id',
  `loginid` varchar(50) DEFAULT NULL COMMENT '用户名',
  `name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `ip` varchar(20) DEFAULT NULL COMMENT 'ip地址',
  `category` int(2) DEFAULT NULL COMMENT '日志分类：1-设备；2-用户；3-应用；4-登录；5-内容 ',
  `opertype` varchar(100) DEFAULT NULL COMMENT '操作类型',
  `opertime` datetime DEFAULT NULL COMMENT '操作时间',
  `operdesc` varchar(500) DEFAULT NULL COMMENT '操作描述',
  `optlevel` varchar(50) DEFAULT NULL COMMENT '严重程度',
  `optaffected` longtext COMMENT '影响谁/事',
  `optcontent` varchar(1000) DEFAULT NULL COMMENT '日志数据',
  `optids` longtext COMMENT '影响的设备id列表',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_sys_menu`
--

DROP TABLE IF EXISTS `t_sys_menu`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_sys_menu` (
  `code` varchar(50) NOT NULL COMMENT '菜单编号',
  `parentcode` varchar(50) DEFAULT NULL COMMENT '父菜单编号',
  `type` int(11) DEFAULT NULL COMMENT '类型,1--菜单子节点;0--菜单叶节点',
  `menulevel` int(11) DEFAULT NULL COMMENT '层级',
  `title` varchar(100) DEFAULT NULL COMMENT '标题',
  `rootpath` varchar(100) DEFAULT NULL COMMENT '模块根路径',
  `url` varchar(250) DEFAULT ' ' COMMENT 'url',
  `templateflag` bigint(20) DEFAULT NULL,
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_sys_role`
--

DROP TABLE IF EXISTS `t_sys_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_sys_role` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '角色名称',
  `description` varchar(200) DEFAULT NULL COMMENT '描述',
  `sysflag` int(11) DEFAULT '0' COMMENT '是否系统内置 0-否 1-是',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='系统角色表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_sys_role_menu`
--

DROP TABLE IF EXISTS `t_sys_role_menu`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_sys_role_menu` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `roleid` bigint(20) NOT NULL COMMENT '角色id',
  `code` varchar(50) NOT NULL COMMENT '菜单编号',
  `authority` int(5) DEFAULT '0' COMMENT '权限 0-查询 1-管理',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `roleid` (`roleid`),
  KEY `code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_sys_tenant`
--

DROP TABLE IF EXISTS `t_sys_tenant`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_sys_tenant` (
                                `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
                                `enName` varchar(256) DEFAULT NULL COMMENT '租户英文标识',
                                `zhName` varchar(64) DEFAULT NULL COMMENT '租户名称',
                                `address` varchar(256) DEFAULT NULL COMMENT '租户地址',
                                `contacts` varchar(64) DEFAULT NULL COMMENT '联系人',
                                `phone` varchar(32) DEFAULT NULL COMMENT '租户电话',
                                `email` varchar(64) DEFAULT NULL COMMENT '租户email',
                                `createTime` datetime DEFAULT NULL COMMENT '创建时间',
                                `updateTime` datetime DEFAULT NULL COMMENT '更新时间',
                                `status` int(11) DEFAULT NULL COMMENT '租户状态',
                                `expiredTime` datetime DEFAULT NULL COMMENT '租户过期时间',
                                `tenantType` int(11) DEFAULT NULL COMMENT '客户类型。0-正式 1-试用',
                                PRIMARY KEY (`tenantId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='租户信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_sys_user`
--

DROP TABLE IF EXISTS `t_sys_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_sys_user` (
  `loginid` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(50) DEFAULT ' ' COMMENT '密码',
  `name` varchar(50) NOT NULL COMMENT '真实姓名',
  `telephone` varchar(20) DEFAULT NULL COMMENT '电话',
  `email` varchar(50) DEFAULT NULL COMMENT '电子信箱',
  `description` varchar(200) DEFAULT NULL COMMENT '描述',
  `flag` int(11) DEFAULT NULL COMMENT '系统用户标识,10--超级管理员,20--管理员,21--业务管理员 11--审核员',
  `parentid` varchar(50) DEFAULT NULL COMMENT '父管理员id',
  `needchangepwd` int(11) DEFAULT '0' COMMENT '登录后是否需要修改密码依据 0-否 1-是',
  `syncconnectorid` bigint(20) DEFAULT NULL COMMENT 't_connector表中connector id',
  `syncbatchno` bigint(20) DEFAULT NULL COMMENT 'connector同步批次号',
  `syncuid` varchar(100) DEFAULT NULL COMMENT '同步connector的第三方账户uid',
  `guardids` varchar(200) DEFAULT NULL COMMENT '可显示门禁权限',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`loginid`,`tenantId`) USING BTREE,
  KEY `loginid_index` (`loginid`),
  KEY `parentid_index` (`parentid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='系统用户表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_sys_user_api`
--

DROP TABLE IF EXISTS `t_sys_user_api`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_sys_user_api` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `loginid` varchar(50) NOT NULL,
  `apicode` varchar(50) DEFAULT NULL,
  `funccode` varchar(50) DEFAULT NULL,
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `t_sys_user_api_fk` (`loginid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_sys_user_group`
--

DROP TABLE IF EXISTS `t_sys_user_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_sys_user_group` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `loginid` varchar(50) NOT NULL COMMENT '管理员标识',
  `groupid` bigint(20) NOT NULL COMMENT '组标识',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `sys_user_group_ibfk_1` (`loginid`),
  KEY `sys_user_group_ibfk_2` (`groupid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='管理员组关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_sys_user_perm`
--

DROP TABLE IF EXISTS `t_sys_user_perm`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_sys_user_perm` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `loginid` varchar(50) NOT NULL COMMENT '管理员标识',
  `code` varchar(50) NOT NULL COMMENT '菜单编号',
  `authority` int(5) DEFAULT '0' COMMENT '菜单对应的权限 0-查询 1-管理',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `t_sys_user_perm_ibfk_1` (`loginid`),
  KEY `t_sys_user_perm_ibfk_2` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='管理员权限表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_sys_user_role`
--

DROP TABLE IF EXISTS `t_sys_user_role`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_sys_user_role` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `loginid` varchar(50) NOT NULL COMMENT '用户名',
  `roleid` bigint(20) NOT NULL COMMENT '角色id',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `loginid` (`loginid`),
  KEY `roleid` (`roleid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='系统用户角色表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_sysconf_setlicense`
--

DROP TABLE IF EXISTS `t_sysconf_setlicense`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_sysconf_setlicense` (
  `id` varchar(100) NOT NULL COMMENT 'uuid',
  `issueDate` varchar(100) DEFAULT NULL COMMENT '颁发日期',
  `expireDate` varchar(100) DEFAULT NULL COMMENT '截止日期',
  `licenseValue` varchar(256) DEFAULT NULL COMMENT '实际License',
  `maxUserCounts` varchar(16) DEFAULT NULL COMMENT '可用的最大用户数',
  `usedCounts` varchar(16) DEFAULT NULL COMMENT '已用的License数',
  `licenseType` tinyint(4) DEFAULT '0' COMMENT '暂时只有KNOX,SAFE两种,默认值为0；0表示SAFE，1表示KNOX',
  `userId` varchar(100) DEFAULT NULL COMMENT '用户ID',
  `groupId` varchar(100) DEFAULT NULL COMMENT '组ID',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='浏览器黑名单and例外配置与策略关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_tag`
--

DROP TABLE IF EXISTS `t_tag`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_tag` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '标签id',
  `tagname` varchar(100) DEFAULT NULL COMMENT '标签名称',
  `tagtype` int(5) NOT NULL COMMENT '标签类型: 0 用户标签  , 1 时间标签   , 2 位置标签',
  `status` int(5) DEFAULT NULL COMMENT '标签状态: 0 禁用   , 1 启用',
  `description` varchar(500) DEFAULT NULL COMMENT '描述',
  `isdefault` int(5) DEFAULT NULL COMMENT '是否是系统默认标签: 0  否  , 1  是',
  `isalluser` int(5) DEFAULT NULL COMMENT '是否为全部用户标签: 0 否  , 1 是',
  `createtime` datetime NOT NULL COMMENT '创建时间',
  `updatetime` datetime NOT NULL COMMENT '修改时间',
  `createby` varchar(100) DEFAULT NULL COMMENT '创建人',
  `updateby` varchar(100) DEFAULT NULL COMMENT '修改人',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='标签信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_tag_group`
--

DROP TABLE IF EXISTS `t_tag_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_tag_group` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `tagid` bigint(20) DEFAULT NULL COMMENT '标签id',
  `groupid` bigint(20) DEFAULT NULL COMMENT '用户组id',
  `customflag` int(5) DEFAULT '0' COMMENT '自定义标识: 0-继承 ;1-自定义',
  `exceptionflag` int(5) DEFAULT '0' COMMENT '例外标识: 0-添加 ;1-排除',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `tagid` (`tagid`),
  KEY `groupid` (`groupid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='标签授权的用户组关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_tag_position`
--

DROP TABLE IF EXISTS `t_tag_position`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_tag_position` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `tagid` bigint(20) DEFAULT NULL COMMENT '位置标签id',
  `minlongitude` double(10,6) DEFAULT NULL COMMENT '位置标签的最小经度值',
  `minlatitude` double(10,6) DEFAULT NULL COMMENT '位置标签的最小纬度值',
  `maxlongitude` double(10,6) DEFAULT NULL COMMENT '位置标签的最大经度值',
  `maxlatitude` double(10,6) DEFAULT NULL COMMENT '位置标签的最大纬度值',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `tagid` (`tagid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='位置标签关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_tag_time`
--

DROP TABLE IF EXISTS `t_tag_time`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_tag_time` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `tagid` bigint(20) DEFAULT NULL COMMENT '时间标签id',
  `mindatetime` datetime DEFAULT NULL COMMENT '时间标签的开始值',
  `maxdatetime` datetime DEFAULT NULL COMMENT '时间标签的结束值',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `tagid` (`tagid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='时间标签关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_tag_user`
--

DROP TABLE IF EXISTS `t_tag_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_tag_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `tagid` bigint(20) DEFAULT NULL COMMENT '标签id',
  `userid` bigint(20) DEFAULT NULL COMMENT '用户id',
  `customflag` int(5) DEFAULT '0' COMMENT '自定义标识: 0-继承 ;1-自定义',
  `exceptionflag` int(5) DEFAULT '0' COMMENT '例外标识: 0-添加 ;1-排除',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `tagid` (`tagid`),
  KEY `userid` (`userid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='标签授权的用户关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_temp_device_ios_ctrl`
--

DROP TABLE IF EXISTS `t_temp_device_ios_ctrl`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_temp_device_ios_ctrl` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `udid` char(64) DEFAULT NULL,
  `uuid` varchar(100) DEFAULT NULL COMMENT '指令uuid',
  `opertype` int(11) NOT NULL COMMENT '操作类型 0:锁机;21--清除密码;22--设备更新,23--设备定位,24--播放铃声,25--设备擦除,26--选择性擦除',
  `paramcontent` varchar(500) DEFAULT NULL COMMENT '指令参数',
  `createtime` datetime DEFAULT NULL COMMENT '指令下发时间',
  `retrytime` datetime DEFAULT NULL COMMENT '重试时间',
  `retrytimes` int(11) DEFAULT NULL COMMENT '重试次数',
  `status` int(11) DEFAULT NULL COMMENT '执行状态.0:指令下发未执行;1:执行成功;21--执行失败',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='ios设备远程控制指令临时表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_time_fence_record`
--

DROP TABLE IF EXISTS `t_time_fence_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_time_fence_record` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `udid` char(64) NOT NULL DEFAULT '' COMMENT '设备id',
  `policyid` varchar(50) NOT NULL DEFAULT '' COMMENT '时间围栏策略id',
  `status` int(11) NOT NULL DEFAULT '2' COMMENT '1:出围栏，2:进围栏',
  `create_time` datetime NOT NULL,
  `sendcmd` int(11) DEFAULT '0' COMMENT '判断在围栏外时是否发送过沙箱指令 0未发送，1已发送',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `udid` (`udid`,`policyid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='时间围栏记录';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_time_unit`
--

DROP TABLE IF EXISTS `t_time_unit`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_time_unit` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `fenceid` bigint(20) NOT NULL COMMENT '围栏表外键',
  `type` int(1) NOT NULL COMMENT '时间单元类型 1-每天，2-每周，3-工作日，4-特定日志',
  `value` varchar(50) DEFAULT NULL COMMENT '时间单元类型具体值',
  `begintime` varchar(5) NOT NULL COMMENT '开始时间',
  `endtime` varchar(5) NOT NULL COMMENT '结束时间',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `updatetime` datetime DEFAULT NULL COMMENT '变更时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `fenceid` (`fenceid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='时间单元表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_user`
--

DROP TABLE IF EXISTS `t_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `loginid` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(50) DEFAULT '' COMMENT '登录密码',
  `name` varchar(150) NOT NULL COMMENT '姓名',
  `mail` varchar(200) DEFAULT '<EMAIL>' COMMENT '邮件地址',
  `mobile` varchar(50) DEFAULT NULL COMMENT '联系电话',
  `type` int(11) NOT NULL COMMENT '用户来源 0-手工录入 1-文件导入 2-ad导入 3-ad同步',
  `canloginself` int(11) NOT NULL DEFAULT '1' COMMENT '是否可登录自服务平台 0-否 1-是',
  `canregistdevice` int(11) NOT NULL DEFAULT '1' COMMENT '是否能够进行设备注册0-否 1-是',
  `caneliminatedevice` int(11) NOT NULL DEFAULT '1' COMMENT '是否能够进行设备淘汰0-否 1-是',
  `allowactivatenum` int(11) NOT NULL DEFAULT '1' COMMENT '允许激活的设备数量',
  `autosyncontactsflag` int(11) DEFAULT '1' COMMENT '是否开启通讯录自动同步,0--否;1--是',
  `conflict` int(11) NOT NULL DEFAULT '0' COMMENT '0-不冲突   1-冲突',
  `status` int(11) NOT NULL DEFAULT '1' COMMENT '用户状态 1-有效 0-无效',
  `creattime` datetime DEFAULT NULL COMMENT '创建时间',
  `updatetime` datetime DEFAULT NULL COMMENT '更新时间',
  `allowunregisteredactivate` int(11) DEFAULT '1' COMMENT '是否允许未注册设备激活,0-否 1-是',
  `allowinconsistentactivate` int(11) DEFAULT '1' COMMENT '是否允许设备激活与设备注册时的信息不一致时激活,0-否 1-是',
  `guid` varchar(100) DEFAULT NULL COMMENT 'ad/ldap同步时的唯一标识',
  `syncbactchno` bigint(20) DEFAULT '0' COMMENT 'ladp自动同步批次号',
  `groupid` bigint(20) DEFAULT NULL COMMENT '组id',
  `forcemodifypwd` int(11) DEFAULT '1' COMMENT '强制初次登录修改密码 0-否 1-是',
  `maxpwdage` int(11) DEFAULT '0' COMMENT '用户密码有效期 0-永久',
  `maxfailedattempts` int(11) DEFAULT '5' COMMENT '用户密码输入尝试次数 0-不限',
  `pwdstarttime` datetime DEFAULT NULL COMMENT '密码有效期计算的开始时间，即用户首次登陆或者修改密码时间',
  `lockinterval` int(11) DEFAULT '0' COMMENT '密码输入超过次数后锁定时长 -1 从组继承配置 0 永久锁定 N 锁定多少分钟',
  `bemail` varchar(200) DEFAULT NULL COMMENT 'bemail邮箱',
  `pwdmodified` int(11) DEFAULT '0' COMMENT '是否已强制密码修改 0-未修改 1-已修改',
  `bmPermission` int(11) NOT NULL DEFAULT '-1' COMMENT '开通BeMail标识 -1-未初始化, 1-使用中, 0-已关闭',
  `ldapconfigid` bigint(20) DEFAULT NULL COMMENT '关联的LDAP配置',
  `emmLoginAgain` int(11) DEFAULT '0' COMMENT '自服务是否修改密码 0-未修改 1-已修改',
  `resetpwd` int(11) DEFAULT '1' COMMENT '发送激活邀请时是否重置用户密码 0-否 1-是',
  `needlockpasswd` int(11) DEFAULT '1' COMMENT '是否需要解锁密码,0-否 1-是',
  `forbidunload` int(11) DEFAULT '1' COMMENT '是否防止卸载，0-否 1-是',
  `passwordstrength` int(11) DEFAULT '2' COMMENT '1：弱（可以是纯字母或数字，6-16位）、2：中等（至少包含字母数字，8-16位）、3：复杂（至少包含大小写的字母以及数字，8-16位）',
  `custom1` varchar(255) DEFAULT NULL COMMENT '自定义1',
  `custom2` varchar(255) DEFAULT NULL COMMENT '自定义2',
  `custom3` varchar(255) DEFAULT NULL COMMENT '自定义3',
  `custom4` varchar(255) DEFAULT NULL COMMENT '自定义4',
  `custom5` varchar(255) DEFAULT NULL COMMENT '自定义5',
  `lockoper` int(11) DEFAULT '1' COMMENT '管理员对用户锁操作 0：锁定 1：解锁',
  `allowactivate` int(11) NOT NULL DEFAULT '1' COMMENT '是否允许android激活设备管理器,0--不激活;1--强制激活;2--按需激活',
  `iosallowactivate` int(11) NOT NULL DEFAULT '1' COMMENT '是否允许ios激活设备管理器,0--不激活;1--强制激活;2--按需激活',
  `plainpassword` varchar(50) DEFAULT '' COMMENT '可逆登录密码',
  `allowlogout` int(11) DEFAULT '1' COMMENT '允许客户端显示登出按钮，1表示允许显示，0不允许显示',
  `syncflag` int(11) DEFAULT '0' COMMENT '是否上报成功  1-上报成功 2-上报失败',
  `identifier` varchar(50) DEFAULT NULL COMMENT '身份证号',
  `policetype` varchar(50) DEFAULT NULL COMMENT '警种',
  `oid` varchar(50) DEFAULT NULL COMMENT '第三方用户id',
  `avatar` varchar(100) DEFAULT NULL COMMENT '用户头像',
  `locationstatus` int(11) NOT NULL DEFAULT '1' COMMENT '定位状态：1启用，0禁用',
  `googleuserid` varchar(32) DEFAULT NULL COMMENT 'Google用户ID',
  `googleuserkind` varchar(32) DEFAULT NULL COMMENT 'Google用户类型',
  `googleusermanagetype` varchar(32) DEFAULT NULL COMMENT 'google用户管理类型',
  `googleuseraccounttype` varchar(32) DEFAULT NULL COMMENT 'google用户账户类型',
  `googleusermail` varchar(200) DEFAULT NULL COMMENT 'Google用户邮箱',
  `googleuseridentifier` varchar(100) DEFAULT NULL COMMENT 'Google用户标识',
  `googleusername` varchar(100) DEFAULT NULL COMMENT 'Google用户展示名称',
  `googleusertype` int(11) NOT NULL DEFAULT '0' COMMENT 'Google用户类型码 0-不添加google帐户 1-用户帐户类型 2-设备帐户类型',
  `googledevicetype` int(11) NOT NULL DEFAULT '0' COMMENT 'Googel android设备激活类型 0-deviceOwner 1-workProfile',
  `syncconnectorid` bigint(20) DEFAULT NULL COMMENT 't_connector表中connector id',
  `relationship` int(11) DEFAULT '1' COMMENT '默认设备归属，1--公司设备;2--员工设备;3--其他',
  `enabledeviceperm` int(11) DEFAULT '1' COMMENT '默认是否启用设备端权限引导，1 -- 启用；0 -- 不启用',
  `enablepermissionguide` int(11) DEFAULT '1' COMMENT '强管控权限向导，1 -- 强管控；0 -- 弱管控',
  `importtime` datetime DEFAULT NULL COMMENT '第三方系统导入的时间，暂用于iam导入',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `t_user_idx_1` (`oid`,`tenantId`,`groupid`,`status`),
  KEY `t_user_idx_2` (`loginId`,`tenantId`,`groupid`,`status`),
  KEY `t_user_idx_3` (`groupid`,`tenantId`,`status`,`loginId`),
  KEY `t_user_idx_4` (`guid`,`tenantId`,`status`,`loginId`),
  UNIQUE KEY `t_user_idx_5` (`loginId`,`tenantId`)
) ENGINE=InnoDB AUTO_INCREMENT=2686685292331011 DEFAULT CHARSET=utf8 COMMENT='设备用户表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_user_app_permission`
--

DROP TABLE IF EXISTS `t_user_app_permission`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_user_app_permission` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `pkgname` varchar(200) NOT NULL COMMENT '应用的包名',
  `app_username` varchar(200) NOT NULL COMMENT '名称',
  `userId` bigint(20) NOT NULL COMMENT '用户ID',
  `permission` int(11) DEFAULT NULL COMMENT '-1-未开通 0-禁用 1-启用 ...',
  `user_app_extra_id` bigint(20) DEFAULT NULL COMMENT '附加表t_user_app_extra的id(暂时不引入该表)',
  `reserved_string` varchar(200) DEFAULT NULL COMMENT '保留字符型字段',
  `reserved_string2` varchar(200) DEFAULT NULL COMMENT '保留字符型字段2',
  `reserved_long` bigint(20) DEFAULT NULL COMMENT '保留整形字段',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `FK_user` (`userId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_user_back_thread_log`
--

DROP TABLE IF EXISTS `t_user_back_thread_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_user_back_thread_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `type` int(11) DEFAULT NULL COMMENT '操作类型 1-移动用户(批量) 2-删除用户 3-新增组 4-移动组 5-删除组',
  `result` int(11) DEFAULT NULL COMMENT '操作结果 0-成功 1-失败',
  `destid` bigint(20) DEFAULT NULL COMMENT '被操作目标对象ID',
  `oldparentid` bigint(20) DEFAULT NULL COMMENT '移动前所属组',
  `newparentid` bigint(20) DEFAULT NULL COMMENT '移动后所属组',
  `policyresult` varchar(10) DEFAULT NULL COMMENT '调用策略接口结果 0-成功 1-失败 --忽略',
  `appresult` varchar(10) DEFAULT NULL COMMENT '调用应用接口结果 0-成功 1-失败 --忽略',
  `violationresult` varchar(10) DEFAULT NULL COMMENT '调用合规接口结果 0-成功 1-失败 --忽略',
  `deviceresult` varchar(10) DEFAULT NULL COMMENT '调用设备接口结果 0-成功 1-失败 --忽略',
  `mcmresult` varchar(10) DEFAULT NULL COMMENT '调用文档接口结果 0-成功 1-失败 --忽略',
  `whiteblackresult` varchar(10) DEFAULT NULL COMMENT '调用黑白名单接口结果 0-成功 1-失败 --忽略',
  `permissionresult` varchar(10) DEFAULT NULL COMMENT '调用权限接口结果 0-成功 1-失败 --忽略',
  `labelresult` varchar(10) DEFAULT NULL COMMENT '调用标签接口结果 0-成功 1-失败 --忽略',
  `reportresult` varchar(10) DEFAULT NULL COMMENT '调用报表接口结果 0-成功 1-失败 --忽略',
  `loginid` varchar(50) DEFAULT NULL COMMENT '操作账户',
  `starttime` datetime DEFAULT NULL COMMENT '操作开始时间',
  `endtime` datetime DEFAULT NULL COMMENT '操作结束时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='内部接口执行的日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_user_center`
--

DROP TABLE IF EXISTS `t_user_center`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_user_center` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `opertype` int(11) NOT NULL COMMENT '操作类型：1-导入用户 2-导入设备 3-同时导入用户及设备',
  `loginid` varchar(50) DEFAULT NULL COMMENT '用户名',
  `password` varchar(50) DEFAULT NULL COMMENT '密码',
  `name` varchar(50) DEFAULT NULL COMMENT '姓名',
  `mail` varchar(200) DEFAULT NULL COMMENT '邮件地址',
  `mobile` varchar(50) DEFAULT NULL COMMENT '联系电话',
  `ldapgroup` varchar(200) DEFAULT NULL COMMENT 'ldap组名',
  `devicetype` int(11) DEFAULT NULL COMMENT '设备类型 10-android phone 11-android pad;20-iphone 21-ipad 22-ipadmini 23- ipodtouch',
  `model` varchar(100) DEFAULT NULL COMMENT '设备型号',
  `relationship` int(11) DEFAULT NULL COMMENT '设备所属关系  1--公司设备;2--员工设备;3--其他',
  `enabledeviceperm` int(11) DEFAULT '1' COMMENT '默认是否启用设备端权限引导，1 -- 启用；0 -- 不启用',
  `devicenumber` varchar(50) DEFAULT NULL COMMENT '设备编号',
  `serialnum` varchar(50) DEFAULT NULL COMMENT '设备序列号',
  `imei` varchar(50) DEFAULT NULL,
  `wifimac` varchar(30) DEFAULT NULL,
  `versionnumber` varchar(100) DEFAULT NULL COMMENT ' 版本号',
  `type` int(11) NOT NULL COMMENT '用户来源 1-文件导入 2-ad导入',
  `conflict` int(11) NOT NULL COMMENT '冲突原因 1-帐号冲突 0-不冲突',
  `devicename` varchar(100) DEFAULT NULL COMMENT '设备名称',
  `deviceplatform` int(11) DEFAULT NULL COMMENT '平台1--android;2--ios',
  `displaygroup` varchar(200) DEFAULT NULL COMMENT '组显示',
  `sim` varchar(200) DEFAULT NULL COMMENT 'sim卡串号',
  `password1` varchar(50) DEFAULT NULL,
  `producttype` varchar(100) DEFAULT NULL,
  `imsi` varchar(50) DEFAULT NULL,
  `sdserialnum` varchar(100) DEFAULT NULL,
  `custom1` varchar(255) DEFAULT NULL COMMENT '自定义1',
  `custom2` varchar(255) DEFAULT NULL COMMENT '自定义2',
  `custom3` varchar(255) DEFAULT NULL COMMENT '自定义3',
  `custom4` varchar(255) DEFAULT NULL COMMENT '自定义4',
  `custom5` varchar(255) DEFAULT NULL COMMENT '自定义5',
  `pincode` varchar(45) DEFAULT NULL COMMENT '邀请码',
  `pwdtype` int(11) DEFAULT '1' COMMENT '密码生成方式 0-手动,1-自动',
  `plainpassword` varchar(50) DEFAULT '' COMMENT '可逆登录密码',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备用户导入中间表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_user_custom_field`
--

DROP TABLE IF EXISTS `t_user_custom_field`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_user_custom_field` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `field` varchar(100) NOT NULL COMMENT '自定义字段key',
  `name` varchar(200) DEFAULT '' COMMENT '自定义字段名称',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_user_family_phone`
--

DROP TABLE IF EXISTS `t_user_family_phone`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_user_family_phone` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `userid` bigint(20) NOT NULL COMMENT '用户id',
  `name` varchar(50) NOT NULL COMMENT '名称',
  `phone` varchar(20) NOT NULL COMMENT '手机号码',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `type` int(11) NOT NULL DEFAULT '1' COMMENT '0:导入;1手动录入',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `updatetime` datetime DEFAULT NULL COMMENT '更新时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uindex_userid_phone` (`userid`,`phone`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户亲情号表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_user_pin`
--

DROP TABLE IF EXISTS `t_user_pin`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_user_pin` (
  `id` bigint(20) NOT NULL COMMENT '用户id',
  `pincode` varchar(45) NOT NULL COMMENT '邀请码',
  `pinexpiredate` datetime NOT NULL COMMENT 'pincode失效截止日期',
  `pincodesendstatus` int(11) NOT NULL DEFAULT '0' COMMENT 'pincode邀请邮件发送状态：0、邮件未发送；1、邮件发送成功；2、邮件发送失败',
  `gentype` int(11) DEFAULT '0' COMMENT '生成方式 0-自动 1-手动',
  `synstatus` int(11) DEFAULT '1' COMMENT '同步状态 0-未同步，1-同步',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_user_signin_log`
--

DROP TABLE IF EXISTS `t_user_signin_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_user_signin_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `userId` bigint(20) NOT NULL COMMENT '用户ID',
  `failedAttemptTimes` int(11) DEFAULT '0' COMMENT '密码输错次数',
  `lastFailedTime` datetime DEFAULT NULL COMMENT '上次密码输错时间',
  `lockStatus` int(11) DEFAULT '0' COMMENT '是否由于密码输错次数超限被锁定，1 锁定，0 正常',
  `lockExpireTime` datetime DEFAULT NULL COMMENT '密码锁定何时将失效，用户可以进行正常使用',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `t_user_signin_log_idx_1` (`userid`,`tenantId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_user_vacation`
--

DROP TABLE IF EXISTS `t_user_vacation`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_user_vacation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `userId` bigint(20) NOT NULL COMMENT '用户ID',
  `startTime` datetime NOT NULL COMMENT '请假创建时间',
  `endTime` datetime NOT NULL COMMENT '请假结束时间',
  `description` text COMMENT '描述',
  `status` int(11) DEFAULT '-1' COMMENT '请假状态  -1 已删除   0 未开始 1请假中 2 已完成',
  `createdTime` datetime NOT NULL COMMENT '创建时间',
  `updateTime` datetime DEFAULT NULL COMMENT '更新时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户请假表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_version`
--

DROP TABLE IF EXISTS `t_version`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_version` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pkgname` varchar(200) NOT NULL,
  `name` varchar(200) NOT NULL,
  `version` varchar(50) NOT NULL,
  `pkgsize` varchar(20) NOT NULL DEFAULT '0',
  `ostype` int(10) NOT NULL DEFAULT '1' COMMENT '1:android,2:ios',
  `apptype` int(10) NOT NULL DEFAULT '0' COMMENT '0:mdm,1:mcm',
  `downloadtype` int(10) NOT NULL DEFAULT '1' COMMENT '1、server下载；2、store下载；0两者都行',
  `source` varchar(200) NOT NULL COMMENT 'apk相对路径',
  `storeurl` varchar(200) DEFAULT NULL,
  `releasenote` varchar(2000) NOT NULL COMMENT '版本功能说明',
  `releasetime` datetime NOT NULL COMMENT '版本发布时间',
  `delsign` int(10) NOT NULL DEFAULT '0' COMMENT '是否删除标识,0:未删除,1:删除',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='版本表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_violate_dist_device`
--

DROP TABLE IF EXISTS `t_violate_dist_device`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_violate_dist_device` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `violationid` bigint(20) NOT NULL COMMENT '违规id',
  `deviceid` bigint(20) NOT NULL COMMENT '设备id',
  `condid` int(11) NOT NULL COMMENT '违规 条件字典表id',
  `condvalue` varchar(200) DEFAULT NULL COMMENT '违规 条件参数值',
  `useralarmways` varchar(50) DEFAULT NULL COMMENT '违规用户告警通知方式  1--邮件;2--push消息;3--短信,多种通知方式使用逗号分隔',
  `adminalarmids` varchar(500) DEFAULT NULL COMMENT '管理员告警id 多个管理员告警使用逗号分隔',
  `violationprocess` varchar(500) NOT NULL COMMENT '违规处理',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '执行状态,0--已分发;1--已生效',
  `createtime` datetime NOT NULL,
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `violationid` (`violationid`),
  KEY `deviceid` (`deviceid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='违规分发设备表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_violate_process_dict`
--

DROP TABLE IF EXISTS `t_violate_process_dict`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_violate_process_dict` (
  `processid` int(11) NOT NULL COMMENT 'id',
  `title` varchar(200) NOT NULL COMMENT '标题',
  PRIMARY KEY (`processid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='违规 处理字典表 ';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_violation_admin_alarm`
--

DROP TABLE IF EXISTS `t_violation_admin_alarm`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_violation_admin_alarm` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `alarmname` varchar(200) NOT NULL COMMENT '告警名称',
  `reportcycle` int(11) NOT NULL COMMENT '报告周期(小时)',
  `email` varchar(1000) DEFAULT NULL COMMENT '接收告警邮件地址',
  `phone` varchar(1000) DEFAULT NULL COMMENT '接收告警的手机号',
  `language` varchar(10) NOT NULL DEFAULT 'zh_CN' COMMENT '语言环境',
  `ownerid` varchar(50) NOT NULL COMMENT '管理者id',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `updatetime` datetime DEFAULT NULL COMMENT '变更时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_violation_cond_dict`
--

DROP TABLE IF EXISTS `t_violation_cond_dict`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_violation_cond_dict` (
  `condid` int(11) NOT NULL COMMENT 'id',
  `violationtype` int(11) NOT NULL COMMENT '违规类型.1--系统类型;2--配置策略类;3--应用类型',
  `title` varchar(200) NOT NULL COMMENT '标题.',
  PRIMARY KEY (`condid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='违规 条件字典表 ';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_violation_device`
--

DROP TABLE IF EXISTS `t_violation_device`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_violation_device` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `violationid` bigint(20) NOT NULL COMMENT '违规id',
  `deviceid` bigint(20) NOT NULL COMMENT '设备id',
  `condid` int(11) NOT NULL COMMENT '违规 条件字典表id',
  `violationtype` int(11) NOT NULL COMMENT '违规类型.1--系统类型;2--配置策略类;3--应用类型',
  `condtitle` varchar(200) NOT NULL COMMENT '违规 条件标题',
  `appstrategyid` bigint(20) DEFAULT NULL COMMENT '应用策略id.黑名单id;白名单id',
  `policyid` varchar(100) DEFAULT NULL COMMENT '配置策略id',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `updatetime` datetime DEFAULT NULL COMMENT '创建时间',
  `adminalarmids` varchar(500) DEFAULT NULL COMMENT '管理员告警id多个管理员告警使用逗号分隔',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `violationid` (`violationid`),
  KEY `deviceid` (`deviceid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='违规设备表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_violation_device_app`
--

DROP TABLE IF EXISTS `t_violation_device_app`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_violation_device_app` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `violationid` bigint(20) NOT NULL COMMENT '违规id',
  `deviceid` bigint(20) NOT NULL COMMENT '设备id',
  `appstrategyid` bigint(20) DEFAULT NULL COMMENT '应用策略id.黑名单id;白名单id',
  `packagename` varchar(200) NOT NULL COMMENT '应用包名',
  `appversion` varchar(50) DEFAULT NULL COMMENT '应用版本',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='违规设备应用违规明细表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_violation_device_proc`
--

DROP TABLE IF EXISTS `t_violation_device_proc`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_violation_device_proc` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `uuid` varchar(50) NOT NULL COMMENT 'uuid',
  `violationid` bigint(20) NOT NULL COMMENT '违规id',
  `deviceid` bigint(20) NOT NULL COMMENT '设备id',
  `procstrategyid` bigint(20) DEFAULT NULL COMMENT '违规 处理策略id',
  `processid` int(11) NOT NULL COMMENT '违规 处理字典表id',
  `processtime` datetime DEFAULT NULL COMMENT '处理时间',
  `processstatus` int(11) DEFAULT NULL COMMENT '处理状态.0--未处理;1---成功;-1--处理失败',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `updatetime` datetime DEFAULT NULL COMMENT '修改时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `deviceid` (`deviceid`),
  KEY `t_violation_device_proc_ibfk_1` (`violationid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='违规设备处理结果表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_violation_device_proc_his`
--

DROP TABLE IF EXISTS `t_violation_device_proc_his`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_violation_device_proc_his` (
  `id` bigint(20) NOT NULL COMMENT 'id',
  `uuid` varchar(50) NOT NULL COMMENT 'uuid',
  `violationid` bigint(20) NOT NULL COMMENT '违规id',
  `deviceid` bigint(20) NOT NULL COMMENT '设备id',
  `procstrategyid` bigint(20) DEFAULT NULL COMMENT '违规 处理策略id',
  `processid` int(11) NOT NULL COMMENT '违规 处理字典表id',
  `processtime` datetime DEFAULT NULL COMMENT '处理时间',
  `processstatus` int(11) DEFAULT NULL COMMENT '处理状态.0--未处理;1---成功;-1--处理失败',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `updatetime` datetime DEFAULT NULL COMMENT '修改时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `deviceid` (`deviceid`),
  KEY `t_violation_device_proc_ibfk_1` (`violationid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='违规设备处理结果历史表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_violations`
--

DROP TABLE IF EXISTS `t_violations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_violations` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `violationname` varchar(200) NOT NULL COMMENT '违规名称',
  `condid` int(11) NOT NULL COMMENT '违规 条件字典表id',
  `violationtype` int(11) NOT NULL COMMENT '违规类型.1--系统类型;2--配置策略类;3--应用类型',
  `condvalue` varchar(200) DEFAULT NULL COMMENT '违规 条件参数值',
  `platformtypes` varchar(30) NOT NULL DEFAULT '1,2,3,4' COMMENT '适用平台 1--android;2--ios;3--windows 8;4--windows phone8,多个平台以逗号分隔',
  `relationship` varchar(50) DEFAULT '1,2,3' COMMENT '设备所属关系  1--公司设备;2--员工设备;3--其他,多种关系使用逗号分隔',
  `useralarmways` varchar(50) DEFAULT NULL COMMENT '违规用户告警通知方式  1--邮件;2--push消息;3--短信,多种通知方式使用逗号分隔',
  `alarmId` varchar(500) DEFAULT NULL COMMENT '管理员告警id 多个管理员告警使用逗号分隔',
  `processid` int(11) NOT NULL COMMENT '违规 处理字典表id',
  `graceperiod` int(11) DEFAULT NULL COMMENT '违规处理宽限时间(分钟)',
  `status` int(11) DEFAULT '0' COMMENT '违规策略状态,0--禁用;1--启用',
  `createby` varchar(50) NOT NULL COMMENT '管理者id',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `updatetime` datetime DEFAULT NULL COMMENT '变更时间',
  `lockpass` varchar(20) DEFAULT NULL COMMENT '锁屏密码',
  `gentype` int(11) DEFAULT NULL COMMENT '生成方式 0-自动 1-手动',
  `apppkglist` varchar(2000) DEFAULT NULL COMMENT '沙箱应用包名json',
  `oldapppkglist` varchar(2000) DEFAULT NULL COMMENT '保存更新前的沙箱应用包名json',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='合规策略表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_violations_device`
--

DROP TABLE IF EXISTS `t_violations_device`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_violations_device` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `violationid` bigint(20) NOT NULL COMMENT '违规 id',
  `deviceid` bigint(20) NOT NULL COMMENT '设备id',
  `relationships` varchar(500) NOT NULL COMMENT '设备所属关系 1 企业 2 员工 3 其他 ',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `deviceid` (`deviceid`),
  KEY `violationid` (`violationid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='合规策略分发设备表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_violations_group`
--

DROP TABLE IF EXISTS `t_violations_group`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_violations_group` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `violationid` bigint(20) NOT NULL COMMENT '违规 id',
  `groupid` bigint(20) NOT NULL COMMENT '用户组id',
  `relationships` varchar(500) NOT NULL COMMENT '设备所属关系 1 企业 2 员工 3 其他 ',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `groupid` (`groupid`),
  KEY `violationid` (`violationid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='违规策略分发用户组表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_violations_user`
--

DROP TABLE IF EXISTS `t_violations_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_violations_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `violationid` bigint(20) NOT NULL COMMENT '违规 id',
  `userid` bigint(20) NOT NULL COMMENT '用户id',
  `relationships` varchar(500) NOT NULL COMMENT '设备所属关系 1 企业 2 员工 3 其他 ',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `userid` (`userid`),
  KEY `violationid` (`violationid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='违规策略分发用户表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_virusscan_history`
--

DROP TABLE IF EXISTS `t_virusscan_history`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_virusscan_history` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `deviceid` bigint(20) NOT NULL COMMENT '设备ID',
  `totalcount` int(10) NOT NULL COMMENT '扫描应用总数',
  `malwarecount` int(10) NOT NULL COMMENT '危险应用数',
  `scanstatus` int(10) NOT NULL COMMENT '扫描状态(0 危险 / 1 安全 )',
  `scantime` datetime DEFAULT NULL COMMENT '扫描时间',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='病毒扫描历史记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_virusscan_latest`
--

DROP TABLE IF EXISTS `t_virusscan_latest`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_virusscan_latest` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `deviceid` bigint(20) NOT NULL COMMENT '设备ID',
  `totalcount` int(10) NOT NULL COMMENT '扫描应用总数',
  `malwarecount` int(10) NOT NULL COMMENT '危险应用数',
  `scanstatus` int(10) NOT NULL COMMENT '扫描状态(0 危险 / 1 安全 )',
  `scantime` datetime DEFAULT NULL COMMENT '扫描时间',
  `scanid` bigint(20) NOT NULL COMMENT '最近一次扫描ID(t_virusscan_history的ID)',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='最新一次病毒扫描记录表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Table structure for table `t_wechat_linkman_his`
--

DROP TABLE IF EXISTS `t_wechat_linkman_his`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_wechat_linkman_his` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `devicewechatid` bigint(20) DEFAULT NULL COMMENT '本机微信',
  `version` varchar(50) DEFAULT NULL COMMENT '版本',
  `account` varchar(100) DEFAULT NULL COMMENT '联系人账号',
  `name` varchar(100) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '联系人姓名',
  `headportrait` varchar(255) DEFAULT NULL COMMENT '联系人头像',
  `autograph` varchar(100) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '联系人个性签名',
  `province` varchar(100) DEFAULT NULL COMMENT '联系人省份',
  `area` varchar(100) DEFAULT NULL COMMENT '联系人地区',
  `source` varchar(100) DEFAULT NULL COMMENT '联系人来源',
  `qrcode` varchar(255) DEFAULT NULL COMMENT '联系人二维码',
  `sex` int(1) DEFAULT NULL COMMENT '联系人性别',
  `deletefriend` int(1) DEFAULT NULL COMMENT '是否已删除好友 0-新添加 1-已删除 2-已更新',
  `remark` varchar(255) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '联系人备注',
  `blacklist` int(1) DEFAULT NULL COMMENT '是否加入黑名单',
  `starfriend` int(1) DEFAULT NULL COMMENT '是否星标朋友',
  `createtime` datetime DEFAULT NULL COMMENT '创建时间',
  `updatetime` datetime DEFAULT NULL COMMENT '更新时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='设备微信联系人历史信息表';


-- 用户文件表 --
DROP TABLE IF EXISTS `t_sys_user_file`;
CREATE TABLE `t_sys_user_file` (
  `id` bigint(20) NOT NULL,
  `deviceId` bigint(20) DEFAULT NULL COMMENT '设备ID',
  `userId` bigint(20) NOT NULL COMMENT '用户ID',
  `businessType` tinyint(3) NOT NULL COMMENT '业务类型，1：截屏日志',
  `filePath` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '文件路径',
  `fileName` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '文件名',
  `fileExt` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '文件扩展名',
  `dfsType` tinyint(3) NOT NULL COMMENT '文件系统类型，1：本地，2：OSS',
  `createTime` datetime DEFAULT NULL COMMENT '创建时间',
  `updateTime` datetime DEFAULT NULL COMMENT '更新时间',
  `tenantId` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT '租户ID',
  `packageName` varchar(64) COLLATE utf8_bin DEFAULT NULL COMMENT '应用包名',
  `deviceName` varchar(64) COLLATE utf8_bin DEFAULT NULL COMMENT '设备名称',
  `userName` varchar(64) COLLATE utf8_bin DEFAULT NULL COMMENT '用户名称',
  `loginId` varchar(32) COLLATE utf8_bin DEFAULT NULL COMMENT '用户登录ID',
  `occTime` datetime DEFAULT NULL COMMENT '发生时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='用户文件表';

-- VPP 应用商店表 --
DROP TABLE IF EXISTS `t_vpp_store`;
CREATE TABLE t_vpp_store (
 `id` bigint(20) NOT NULL AUTO_INCREMENT,
 `adamid` bigint(20) NOT NULL COMMENT '苹果应用商店应用id',
 `producttypename` varchar(50) COMMENT 'productTypeName',
 `producttypeid` int(11) COMMENT 'productTypeId',
 `assignedcount` int(11) COMMENT '已授权license个数',
 `availablecount` int(11) COMMENT '可用license个数',
 `pricingparam` varchar(30) COMMENT 'pricingParam',
 `retiredcount` int(11) COMMENT '已收回license个数',
 `totalcount` int(11) COMMENT 'license总数',
 `irrevocable` varchar(10) COMMENT 'license是否可回收',
 `deviceassignable` varchar(10) COMMENT '是否可直接授权给设备',
 `name` varchar(200) DEFAULT NULL COMMENT '名称',
 `pkgname` varchar(200) DEFAULT NULL COMMENT '应用id',
 `version` varchar(50) DEFAULT NULL COMMENT '版本',
 `icon` varchar(200) DEFAULT NULL COMMENT '图标',
 `price` varchar(50) DEFAULT NULL COMMENT '价格',
 `appsize` int(11) DEFAULT NULL COMMENT '大小',
 `brief` varchar(2000) DEFAULT NULL COMMENT '软件简介',
 `company` varchar(200) DEFAULT NULL COMMENT '开发商',
 `createtime` datetime DEFAULT NULL COMMENT '创建时间',
 `updatetime` datetime DEFAULT NULL COMMENT '更新时间',
 `osversion` varchar(200) DEFAULT NULL COMMENT '要求系统版本',
 `description` varchar(2000) DEFAULT NULL COMMENT '描述',
 `b2bcustomapp` varchar(10) COMMENT '自定app',
 `source` varchar(1000) COMMENT '应用源地址',
 `modeltype` varchar(20) COMMENT '支持设备型号',
 `conflict` int(11) DEFAULT 0 COMMENT '与应用商店是否有冲突：0、无冲突，1、有冲突',
 `tenantId` varchar(64) DEFAULT NULL COMMENT '租户ID',
 PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='VPP应用';


-- VPP 应用兑换码 --
DROP TABLE IF EXISTS `t_vpp_cdkey`;
CREATE TABLE t_vpp_cdkey (
 `id` bigint(20) NOT NULL AUTO_INCREMENT,
 `orderid` varchar(50) COMMENT '订单号',
 `loginid` varchar(50) COMMENT '用户longinid',
 `adamid` bigint(20) NOT NULL COMMENT '苹果应用商店应用id',
 `vppappid` bigint(20) NOT NULL COMMENT 'vppappid',
 `status` int(11) DEFAULT 0 COMMENT '0：未使用，1已使用',
 `cdkey` varchar(50) COMMENT '兑换码',
 `inviteurl` varchar(500) COMMENT 'inviteUrl',
 `createtime` datetime NOT NULL COMMENT '创建时间',
 `updatetime` datetime NOT NULL COMMENT '更新时间',
 `tenantId` varchar(64) DEFAULT NULL COMMENT '租户ID',
 PRIMARY KEY (`id`),
 KEY `t_vpp_cdkey_idx_1` (`adamid`,`status`,`tenantId`,`createTime`),
 KEY `t_vpp_cdkey_idx_2` (`cdkey`,`tenantId`),
 KEY `t_vpp_cdkey_idx_3` (`orderId`,`tenantId`),
 KEY `t_vpp_cdkey_idx_4` (`logind`,`adamid`,`status`,`tenantId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='VPP应用兑换码';

-- adm 定制配置表
DROP TABLE IF EXISTS `t_adm_cust`;
CREATE TABLE `t_adm_cust` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '标签id',
  `cust_title` varchar(255) DEFAULT NULL COMMENT '标题',
  `cust_color` varchar(255) DEFAULT NULL COMMENT '按钮颜色',
  `cust_picture` text COMMENT '图片',
  `user_id` varchar(255) DEFAULT NULL COMMENT '所属用户',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '修改时间',
  `my_use` int(20) DEFAULT NULL COMMENT '是否启用',
  `tenantId` varchar(64) DEFAULT NULL COMMENT '租户ID',
  `message_info` varchar(1024) DEFAULT NULL COMMENT '启用提示信息',
  PRIMARY KEY (`id`),
  KEY `t_adm_cust_idx_1` (`tenantId`,`my_use`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='adm配置信息表';

-- 客户端应用信息表 --
DROP TABLE IF EXISTS `t_client_app`;
CREATE TABLE t_client_app (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `appName` varchar(200)  CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci COMMENT '应用名称',
  `pkgName` varchar(200) COMMENT '应用的包名',
  `publishTime` datetime NOT NULL COMMENT '应用的打包或者发布时间，一般会从apk包中解释出来',
  `platform` int(11) NOT NULL COMMENT '平台类型，参考系统平台类型定义',
  `version` varchar(50) COMMENT '版本信息',
  `size` int(11) DEFAULT 0 COMMENT '应用的包大小',
  `status` int(11) DEFAULT 0 COMMENT '1当前活动的有效的应用，0上传的历史版本应用或者未启用的版本',
  `filePath` varchar(500) COMMENT '文件路径（或者OSS中的Key)',
  `rootPath` varchar(500) COMMENT '文件根目录，或者OSS中的bucketName',
  `checksum` varchar(100) COMMENT '文件的MDM值',
  `createTime` datetime NOT NULL COMMENT '创建时间',
  `updateTime` datetime NOT NULL COMMENT '更新时间',
  `comment` varchar(2000) COMMENT '应用说明描述',
  `tenantId` varchar(64) DEFAULT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `t_client_app_idx_1` (`platform`,`pkgName`,`version`,`tenantId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='客户端应用信息表';

-- 推送日志记录信息
DROP TABLE IF EXISTS `t_push_log`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_push_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `pushMode` int(11) NOT NULL COMMENT '推送模式0应用，1设备',
  `pushType` varchar(40) NOT NULL COMMENT '推送类型,govdd政务钉钉推送，npns(NPNS服务推送),ios(苹果推送)',
  `sender` varchar(100) COMMENT '发送方（推送方)',
  `payload` text  COMMENT '推送对应的payload,是指业务中对应的payload',
  `pushPayload` text  COMMENT '推送至客户端对应的payload,一般ios中会跟payload不一致',
  `isBatch` int(11) NOT NULL COMMENT '0单个推送，1批量推送',
  `pushTime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '推送时间',
  `result` varchar(100) COMMENT '推送结果',
  `errorMsg` varchar(2000) COMMENT '推送结果消息，error msg',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `t_push_log_idx_1` (`pushTime`,`pushMode`,`pushType`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='记录的是推送记录信息';



-- 推送日志记录接收者信息
DROP TABLE IF EXISTS `t_push_log_receiver`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_push_log_receiver` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `logId` int(11) NOT NULL COMMENT '推送记录ID',
  `userId` bigint(20) NOT NULL COMMENT '推送的目标用户ID',
  `loginId` varchar(40) COMMENT '推送的目标用户loginId',
  `udid` varchar(64) COMMENT '推送的目标用户设备umid',
  `dudid` varchar(64) COMMENT '推送的目标用户设备udid',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `t_push_log_receiver_idx_1` (`logId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='推送日志记录接收者信息';


-- 记录的是推送失败记录信息
DROP TABLE IF EXISTS `t_push_failure_record`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_push_failure_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `pushMode` int(11) NOT NULL COMMENT '推送模式0应用，1设备',
  `pushType` varchar(40) COMMENT '推送类型,govdd政务钉钉推送，npns(NPNS服务推送),ios(苹果推送)',
  `sender` varchar(100) COMMENT '发送方（推送方)',
  `payload` text  COMMENT '推送对应的payload,是指业务中对应的payload',
  `pushPayload` text  COMMENT '推送至客户端对应的payload,一般ios中会跟payload不一致',
  `isBatch` int(11) NOT NULL COMMENT '0单个推送，1批量推送',
  `receivers` text COMMENT '接收方，以json方式存储,是指业务中对应的receivers',
  `pushReceivers` text COMMENT '推送时，具体的接收方，以json方式存储',
  `pushTime` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '推送时间',
  `result` varchar(100) COMMENT '推送结果',
  `errorMsg` varchar(2000) COMMENT '推送结果消息，error msg',
  `failureTimes` int(11) COMMENT '失败次数',
  `nextPushTime` datetime COMMENT '下一推送时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `t_push_failure_record_idx_1` (`nextPushTime`,`pushMode`,`pushType`,`pushTime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='记录的是推送失败记录信息';


-- 推送token信息表
DROP TABLE IF EXISTS `t_push_token`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_push_token` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `userid` bigint(20) DEFAULT NULL COMMENT '所属用户用户id',
  `deviceId` bigint(20) DEFAULT NULL COMMENT '设备ID',
  `udid` char(64) DEFAULT NULL COMMENT 'unique device identifier',
  `dudid` varchar(64) comment '设备的系统UDID值,设备MDM激活后得到的设备唯一ID,原来的UDID为应用生成的UMID',
  `activateMdmTime` datetime comment '设备的MDM激活时间',
  `type` int(11) NOT NULL DEFAULT '1' COMMENT '平台1--android;2--ios',
  `devicetype` int(11) NOT NULL DEFAULT '10' COMMENT '设备类型 10-android phone 11-android pad;20-iphone 21-ipad 22-ipadmini 23- ipodtouch',
  `activatetime` datetime DEFAULT NULL COMMENT '激活时间',
  `token` varchar(200) DEFAULT NULL COMMENT 'ios mdm发送唤醒使用',
  `pushmagic` varchar(200) DEFAULT NULL COMMENT 'ios mdm发送唤醒使用',
  `unlocktoken` varchar(4000) DEFAULT NULL COMMENT 'ios mdm解除锁屏使用',
  `userName` varchar(150) DEFAULT NULL COMMENT '用户名',
  `loginId` varchar(50) DEFAULT NULL COMMENT '用户登录名',
  `status` int(11) NOT NULL DEFAULT '1' COMMENT '1有效，0无效',
  `expireTime` datetime comment '设备的MDM激活时间',
  `nextCheckTime` datetime comment '设备的MDM下一检查时间',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `t_push_token_idx_1` (`userid`,`tenantId`,`type`,`deviceType`,`udid`,`loginId`,`userName`),
  KEY `t_push_token_idx_2` (`type`,`tenantId`,`devicetype`,`userid`,`udid`,`loginId`),
  KEY `t_push_token_idx_3` (`udid`,`tenantId`,`loginId`,`userid`,`devicetype`,`type`),
  KEY `t_push_token_idx_4` (`loginId`,`tenantId`,`type`,`devicetype`,`udid`,`userid`,`userName`),
  KEY `t_push_token_idx_5` (`dudid`,`tenantId`,`userId`,`loginId`,`type`,`deviceType`),
  KEY `t_push_token_idx_6` (`status`,`nextCheckTime`,`tenantId`)

) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8 COMMENT='推送token信息表';
/*!40101 SET character_set_client = @saved_cs_client */;



-- NPNS客户端应用级推送token(NPNS推送Token）信息表
DROP TABLE IF EXISTS `t_apns_token`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_apns_token` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `userid` bigint(20) DEFAULT NULL COMMENT '所属用户用户id',
  `deviceId` bigint(20) DEFAULT NULL COMMENT '设备ID',
  `udid` char(64) DEFAULT NULL COMMENT 'unique device identifier',
  `type` int(11) NOT NULL DEFAULT '1' COMMENT '平台1--android;2--ios',
  `devicetype` int(11) NOT NULL DEFAULT '10' COMMENT '设备类型 10-android phone 11-android pad;20-iphone 21-ipad 22-ipadmini 23- ipodtouch',
  `activatetime` datetime DEFAULT NULL COMMENT '激活时间',
  `token` varchar(200) DEFAULT NULL COMMENT 'npns对应的token值',
  `tokenType` int DEFAULT NULL COMMENT 'token类型，暂时支持0:mdm,1:mcm',
  `pkgName` varchar(200) DEFAULT NULL COMMENT 'token对应的应用包名',
  `userName` varchar(150) DEFAULT NULL COMMENT '用户名',
  `loginId` varchar(50) DEFAULT NULL COMMENT '用户登录名',
  `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`id`),
  KEY `t_npns_token_idx_1` (`userid`,`tenantId`,`tokenType`,`type`,`deviceType`,`udid`,`loginId`),
  KEY `t_npns_token_idx_2` (`type`,`tenantId`,`devicetype`,`tokenType`,`userid`,`udid`,`loginId`),
  KEY `t_npns_token_idx_3` (`udid`,`tenantId`,`loginId`,`tokenType`,`userid`,`devicetype`,`type`),
  KEY `t_push_token_idx_4` (`loginId`,`tenantId`,`udid`,`tokenType`,`type`,`devicetype`,`userid`)

) ENGINE=InnoDB AUTO_INCREMENT=20 DEFAULT CHARSET=utf8 COMMENT='NPNS客户端应用级推送token(NPNS推送Token）信息表';
/*!40101 SET character_set_client = @saved_cs_client */;




-- 推送证书信息
DROP TABLE IF EXISTS `t_push_credential`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_push_credential` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `pushType` varchar(40) NOT NULL COMMENT '推送类型,govdd政务钉钉推送，npns(NPNS服务推送),ios(苹果推送)',
  `credential` text  COMMENT '证书内容对应Base64字符串',
  `config` varchar(2000)  COMMENT '证书附加的配置信息，如密码、topic等',
  PRIMARY KEY (`id`),
  KEY `t_push_credential_idx_1` (`pushType`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='记录的是推送记录信息';


-- configuration table, each config item is unique and identified by key
-- NOTE: not support one-key/multi-value.
DROP TABLE IF EXISTS `t_config`;
CREATE TABLE `t_config` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `k` varchar(255) NOT NULL COMMENT 'config key',
    `v` varchar(255) NOT NULL COMMENT 'config value',
    `description` varchar(255) COMMENT 'short description for this config item.',
    PRIMARY KEY (`id`),
    UNIQUE KEY `k` (`k`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='config table';

INSERT INTO `t_config`(`k`,`v`, `description`) VALUES ('db.version','135', 'current db version.');

-- scep生成的客户端证书表
DROP TABLE IF EXISTS `t_scep_certificate`;
CREATE TABLE `t_scep_certificate` (
     `id` bigint(20) NOT NULL,
     `createTime` datetime comment '证书生成时间',
     `expireTime` datetime comment '证书过期时间',
     `revoked` int(1)  COMMENT '证书是否被revoke',
     `serialNumber` varchar(64)  COMMENT '证书序列号',
     `issuer` varchar(64)  COMMENT '证书的签发者CN',
     `tenantId` varchar(64) NOT NULL COMMENT '租户ID',
     PRIMARY KEY (`id`),
     KEY `t_scep_certificate_idx_1` (`serialNumber`,`issuer`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='scep生成的客户端证书表';

DROP TABLE IF EXISTS `t_certificate`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `t_certificate` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '证书名称',
  `type` varchar(20) NOT NULL COMMENT '证书类型',
  `filename` varchar(200) NOT NULL COMMENT '证书文件名称',
  `content` text COMMENT '证书实体内容',
  `config` varchar(2000)  COMMENT '证书附加的配置信息，如密码、topic等',
  `createTime` datetime NOT NULL COMMENT '导入时间',
  `updateTime` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='证书信息管理表';

/*!40101 SET character_set_client = @saved_cs_client */;

/*!40101 SET character_set_client = @saved_cs_client */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2019-11-22 15:00:10


-- ----------------------------
-- 全局级别初始化数据
-- ----------------------------
INSERT INTO t_violation_cond_dict (`condid`, `violationtype`, `title`) VALUES (101, 1, 'violation.cond.dict.sys.os.version.low');
INSERT INTO t_violation_cond_dict (`condid`, `violationtype`, `title`) VALUES (102, 1, 'violation.cond.dict.sys.jailbreak.root');
INSERT INTO t_violation_cond_dict (`condid`, `violationtype`, `title`) VALUES (103, 1, 'violation.cond.dict.sys.data.no.cipher');
INSERT INTO t_violation_cond_dict (`condid`, `violationtype`, `title`) VALUES (104, 1, 'violation.cond.dict.sys.sim.modify');
INSERT INTO t_violation_cond_dict (`condid`, `violationtype`, `title`) VALUES (105, 1, 'violation.cond.dict.sys.sd.modify');
INSERT INTO t_violation_cond_dict (`condid`, `violationtype`, `title`) VALUES (106, 1, 'violation.cond.dict.sys.strategy.not.update');
INSERT INTO t_violation_cond_dict (`condid`, `violationtype`, `title`) VALUES (107, 1, 'violation.cond.dict.sys.not.flowStatistics');
INSERT INTO t_violation_cond_dict (`condid`, `violationtype`, `title`) VALUES (108, 1, 'violation.cond.dict.sys.flow.threshold');
INSERT INTO t_violation_cond_dict (`condid`, `violationtype`, `title`) VALUES (109, 1, 'violation.cond.dict.sys.completeness');
INSERT INTO t_violation_cond_dict (`condid`, `violationtype`, `title`) VALUES (201, 2, 'violation.cond.dict.conf.not.effect');
INSERT INTO t_violation_cond_dict (`condid`, `violationtype`, `title`) VALUES (301, 3, 'violation.cond.dict.app.blacklist');
INSERT INTO t_violation_cond_dict (`condid`, `violationtype`, `title`) VALUES (302, 3, 'violation.cond.dict.app.whitelist');
INSERT INTO t_violation_cond_dict (`condid`, `violationtype`, `title`) VALUES (303, 3, 'violation.cond.dict.app.force');
INSERT INTO t_violation_cond_dict (`condid`, `violationtype`, `title`) VALUES (401, 1, 'violation.cond.dict.sys.device.offline');

-- ----------------------------
-- Records of t_violate_process_dict-全局
-- ----------------------------
INSERT INTO t_violate_process_dict (`processid`,`title`) VALUES (1, 'violation.process.dict.lock.device');
INSERT INTO t_violate_process_dict (`processid`,`title`) VALUES (2, 'violation.process.dict.company.wipe');
INSERT INTO t_violate_process_dict (`processid`,`title`) VALUES (3, 'violation.process.dict.all.wipe');
INSERT INTO t_violate_process_dict (`processid`,`title`) VALUES (8, 'violation.process.dict.forbid.appstore');
INSERT INTO t_violate_process_dict (`processid`,`title`) VALUES (9, 'violation.process.dict.forbid.mcm');
INSERT INTO t_violate_process_dict (`processid`,`title`) VALUES (10, 'violation.process.dict.forbid.sd');
INSERT INTO t_violate_process_dict (`processid`,`title`) VALUES (11, 'violation.process.dict.forbid.camera');
INSERT INTO t_violate_process_dict (`processid`,`title`) VALUES (12, 'violation.process.dict.forbid.mdm');
INSERT INTO t_violate_process_dict (`processid`,`title`) VALUES (13, 'violation.process.dict.forbid.sag');
INSERT INTO t_violate_process_dict (`processid`,`title`) VALUES (14, 'violation.process.dict.forbid.mobile.data');
INSERT INTO t_violate_process_dict (`processid`,`title`) VALUES (15, 'violation.process.dict.lock.screen');
INSERT INTO t_violate_process_dict (`processid`,`title`) VALUES (16, 'violation.process.dict.close.device');
INSERT INTO t_violate_process_dict (`processid`,`title`) VALUES (17, 'violation.process.dict.forbid.wrappingApp');
INSERT INTO t_violate_process_dict (`processid`,`title`) VALUES (-1, 'violation.process.dict.noOper');

-- ----------------------------
-- Records of t_group-根组和默认组属于全局级别
-- ----------------------------
INSERT INTO t_group(`id`, `guid`, `name`, `parentid`, `canloginself`, `canregistdevice`, `caneliminatedevice`, `allowactivatenum`, `type`, `status`, `descr`, `creattime`, `updatetime`, `allowunregisteredactivate`, `allowinconsistentactivate`, `syncbactchno`, `groupcode`, `forcemodifypwd`, `maxpwdage`, `maxfailedattempts`, `lockinterval`, `ldapconfigid`, `needlockpasswd`, `forbidunload`, `passwordstrength`, `allowactivate`, `iosallowactivate`, `allowlogout`, `thirdgroupid`, `syncflag`, `issyncgoogle`, `googleenterpriseid`, `gpath`, `syncconnectorid`, `relationship`, `enabledeviceperm`, `enablepermissionguide`, `importtime`, `tenantId`) VALUES (-2, 'Default', 'Default', -1, 1, 1, 1, 10, 0, 1, 'Default Group', '2019-11-16 13:34:53', NULL, 1, 1, 0, '00010001', 0, 0, 5, 0, NULL, 0, 1, 2, 0, 0, 1, '-2', 0, 0, NULL, 'Default', NULL, 1, 1, 1, NULL, '');
INSERT INTO t_group(`id`, `guid`, `name`, `parentid`, `canloginself`, `canregistdevice`, `caneliminatedevice`, `allowactivatenum`, `type`, `status`, `descr`, `creattime`, `updatetime`, `allowunregisteredactivate`, `allowinconsistentactivate`, `syncbactchno`, `groupcode`, `forcemodifypwd`, `maxpwdage`, `maxfailedattempts`, `lockinterval`, `ldapconfigid`, `needlockpasswd`, `forbidunload`, `passwordstrength`, `allowactivate`, `iosallowactivate`, `allowlogout`, `thirdgroupid`, `syncflag`, `issyncgoogle`, `googleenterpriseid`, `gpath`, `syncconnectorid`, `relationship`, `enabledeviceperm`, `enablepermissionguide`, `importtime`, `tenantId`) VALUES (-1, NULL, 'ROOT', 0, 1, 1, 1, 10, 0, 1, 'Custom Root Group', '2019-11-16 13:34:53', NULL, 1, 1, 0, '0001', 0, 0, 5, 0, NULL, 0, 1, 2, 0, 0, 1, '-1', 0, 0, NULL, 'ROOT', NULL, 1, 1, 1, NULL, '');


-- ----------------------------
-- Records of t_certificate-全局
-- ----------------------------
insert into t_certificate (`config`, `content`, `updateTime`, `filename`, `type`, `createTime`, `name`) values ( null, '-----BEGIN CERTIFICATE-----\nMIIDnTCCAoWgAwIBAgIJAOFv8yGRi7zUMA0GCSqGSIb3DQEBCwUAMD0xCzAJBgNV\nBAoMAk5RMQwwCgYDVQQLDANEZXYxCzAJBgNVBAYTAkNOMRMwEQYDVQQDDApJbnRl\ncm5hbENBMB4XDTE5MTAxNzEwNDEzMVoXDTI5MTAxNjEwNDEzMVowPTELMAkGA1UE\nCgwCTlExDDAKBgNVBAsMA0RldjELMAkGA1UEBhMCQ04xEzARBgNVBAMMCkludGVy\nbmFsQ0EwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQDN6VFQPrgXNVlE\nlQRgPNH5uYiBtc6Z3TCn4VOyxGRbnkdj6d+0CCVyNp9g30EzT+aG4B5UlwB4fWg+\nwJ12Ix1j7fYjf4g64o4+8q0tgc0lxn4qgCemnFPk7zUa0IbIVCOSyxj6qgYR9wh1\n+sgSULWLv7hjp9x7h6ppTs6oX2UxBres6BQdenxm+h1BY6FSw0QTUeUnCDV2uIYk\n3vPEIBFQ057jJICEkdOC2hJRpbdKkDqN6EwwruCgPBZoZUscowFjONPDV79YgO6N\nvEYnSgCxyE35JthZnCp5g6d3Y+9spSEYdlglw1ZNNX1AHpf+xOSX3jJZcvRPkVyN\nsy3yPEFVAgMBAAGjgZ8wgZwwHQYDVR0OBBYEFFEudTI+cQcycDnWKVTK9XSwX1Wq\nMG0GA1UdIwRmMGSAFFEudTI+cQcycDnWKVTK9XSwX1WqoUGkPzA9MQswCQYDVQQK\nDAJOUTEMMAoGA1UECwwDRGV2MQswCQYDVQQGEwJDTjETMBEGA1UEAwwKSW50ZXJu\nYWxDQYIJAOFv8yGRi7zUMAwGA1UdEwQFMAMBAf8wDQYJKoZIhvcNAQELBQADggEB\nAC7wUQNMp5L/KbU1JIlveqCkwjtzG8+jv1EkNg+OcmMHjbA9nrMG+viBZJ3krYGr\ndNHp8ADtuxZkc4tAhvY/G/QjlDV4Y71Zh8+faCKaNWBg2JEEKUbCXJuZxq3dcUmd\nbfjbjiFw9guxKFhUwQiMuwj9EXFzy2IagSKvGghi+kYrVyump2CMA2C8N7u4BaZo\nPKQEmxUxWj8Cia549+rrcprurL/FogxfJji3qW4zfCt8LrDnW+n3O/4xAKfk1k3M\nvgqDtHlbujuiHZTJeA3LeWXH0XVbHxBXrJuLYJgUee2s0TkUxtRk1bdEK0brVft7\nahH1NrQLF2DD+d842v9sbR4=\n-----END CERTIFICATE-----', '2020-02-10 15:53:36', 'SCEPRootCA.pem', 'SERVER_CERT', '2020-02-10 15:53:34', '服务器证书');
insert into t_certificate (`config`, `content`, `updateTime`, `filename`, `type`, `createTime`, `name`) values ( '{\"password\":\"Nsky@0!9\"}', 'MIIJkQIBAzCCCVcGCSqGSIb3DQEHAaCCCUgEgglEMIIJQDCCA/cGCSqGSIb3DQEHBqCCA+gwggPk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\nCAA=', '2020-02-07 14:39:04', 'mdmclient.cert', 'CLIENT_CERT', '2020-02-07 14:39:04', '客户端证书');
insert into t_certificate (`config`, `content`, `updateTime`, `filename`, `type`, `createTime`, `name`) values ( null, '-----BEGIN CERTIFICATE-----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\nCrrCyPrYxmD/iU99y5nT\n-----END CERTIFICATE-----', '2020-02-10 15:53:36', 'digitalsee.cn.cert', 'SIGNER_CERT', '2020-02-10 15:53:34', '签名者证书');
***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
insert into t_certificate (`config`, `content`, `updateTime`, `filename`, `type`, `createTime`, `name`) values ( null, '-----BEGIN CERTIFICATE-----\nMIIEizCCA3OgAwIBAgIQBUb+GCP34ZQdo5/OFMRhczANBgkqhkiG9w0BAQsFADBh\nMQswCQYDVQQGEwJVUzEVMBMGA1UEChMMRGlnaUNlcnQgSW5jMRkwFwYDVQQLExB3\nd3cuZGlnaWNlcnQuY29tMSAwHgYDVQQDExdEaWdpQ2VydCBHbG9iYWwgUm9vdCBD\nQTAeFw0xNzExMDYxMjIzNDVaFw0yNzExMDYxMjIzNDVaMF4xCzAJBgNVBAYTAlVT\nMRUwEwYDVQQKEwxEaWdpQ2VydCBJbmMxGTAXBgNVBAsTEHd3dy5kaWdpY2VydC5j\nb20xHTAbBgNVBAMTFEdlb1RydXN0IFJTQSBDQSAyMDE4MIIBIjANBgkqhkiG9w0B\nAQEFAAOCAQ8AMIIBCgKCAQEAv4rRY03hGOqHXegWPI9/tr6HFzekDPgxP59FVEAh\n150Hm8oDI0q9m+2FAmM/n4W57Cjv8oYi2/hNVEHFtEJ/zzMXAQ6CkFLTxzSkwaEB\n2jKgQK0fWeQz/KDDlqxobNPomXOMJhB3y7c/OTLo0lko7geG4gk7hfiqafapa59Y\nrXLIW4dmrgjgdPstU0Nigz2PhUwRl9we/FAwuIMIMl5cXMThdSBK66XWdS3cLX18\n4ND+fHWhTkAChJrZDVouoKzzNYoq6tZaWmyOLKv23v14RyZ5eqoi6qnmcRID0/i6\nU9J5nL1krPYbY7tNjzgC+PBXXcWqJVoMXcUw/iBTGWzpwwIDAQABo4IBQDCCATww\nHQYDVR0OBBYEFJBY/7CcdahRVHex7fKjQxY4nmzFMB8GA1UdIwQYMBaAFAPeUDVW\n0Uy7ZvCj4hsbw5eyPdFVMA4GA1UdDwEB/wQEAwIBhjAdBgNVHSUEFjAUBggrBgEF\nBQcDAQYIKwYBBQUHAwIwEgYDVR0TAQH/BAgwBgEB/wIBADA0BggrBgEFBQcBAQQo\nMCYwJAYIKwYBBQUHMAGGGGh0dHA6Ly9vY3NwLmRpZ2ljZXJ0LmNvbTBCBgNVHR8E\nOzA5MDegNaAzhjFodHRwOi8vY3JsMy5kaWdpY2VydC5jb20vRGlnaUNlcnRHbG9i\nYWxSb290Q0EuY3JsMD0GA1UdIAQ2MDQwMgYEVR0gADAqMCgGCCsGAQUFBwIBFhxo\ndHRwczovL3d3dy5kaWdpY2VydC5jb20vQ1BTMA0GCSqGSIb3DQEBCwUAA4IBAQAw\n8YdVPYQI/C5earp80s3VLOO+AtpdiXft9OlWwJLwKlUtRfccKj8QW/Pp4b7h6QAl\nufejwQMb455OjpIbCZVS+awY/R8pAYsXCnM09GcSVe4ivMswyoCZP/vPEn/LPRhH\nhdgUPk8MlD979RGoUWz7qGAwqJChi28uRds3thx+vRZZIbEyZ62No0tJPzsSGSz8\nnQ//jP8BIwrzBAUH5WcBAbmvgWfrKcuv+PyGPqRcc4T55TlzrBnzAzZ3oClo9fTv\nO9PuiHMKrC6V6mgi0s2sa/gbXlPCD9Z24XUMxJElwIVTDuKB0Q4YMMlnpN/QChJ4\nB0AFsQ+DU0NCO+f78Xf7\n-----END CERTIFICATE-----', '2020-02-10 16:08:59', 'SCEPRootCA.pem', 'SIGNER_ROOT_CA', '2020-02-10 16:08:57', '签名者根证书');
