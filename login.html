<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录 - 数字化企业管理平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            max-width: 400px;
            width: 100%;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            padding: 40px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .page-title {
            font-size: 28px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 8px;
            text-align: center;
        }
        
        .page-subtitle {
            font-size: 14px;
            color: #8c8c8c;
            text-align: center;
            margin-bottom: 32px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            color: #262626;
            font-weight: 500;
        }
        
        .form-control {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid #d9d9d9;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
            background: white;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #1890ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }
        
        .remember-forgot {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
            font-size: 13px;
        }
        
        .remember-me {
            display: flex;
            align-items: center;
        }
        
        .remember-me input {
            margin-right: 6px;
        }
        
        .forgot-password {
            color: #1890ff;
            text-decoration: none;
        }
        
        .forgot-password:hover {
            text-decoration: underline;
        }
        
        .btn-login {
            width: 100%;
            padding: 14px;
            background: linear-gradient(135deg, #1890ff, #40a9ff);
            border: none;
            border-radius: 8px;
            color: white;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .btn-login:hover {
            background: linear-gradient(135deg, #40a9ff, #69c0ff);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(24, 144, 255, 0.4);
        }
        
        .btn-login:active {
            transform: translateY(0);
        }
        
        .register-link {
            text-align: center;
            margin-top: 24px;
            padding-top: 24px;
            border-top: 1px solid #f0f0f0;
        }
        
        .register-link a {
            color: #1890ff;
            text-decoration: none;
            font-size: 14px;
        }
        
        .register-link a:hover {
            text-decoration: underline;
        }
        
        .demo-notice {
            background: #e6f7ff;
            border: 1px solid #91d5ff;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 24px;
            font-size: 13px;
            color: #0050b3;
        }
        
        .demo-notice strong {
            display: block;
            margin-bottom: 8px;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .login-container {
                padding: 24px;
                margin: 10px;
            }
            
            .page-title {
                font-size: 24px;
            }
        }
        
        @media (max-width: 480px) {
            body {
                padding: 10px;
            }
            
            .login-container {
                padding: 20px;
            }
            
            .page-title {
                font-size: 22px;
            }
        }
    </style>
</head>

<body>
    <div class="login-container">
        <h1 class="page-title">用户登录</h1>
        <p class="page-subtitle">欢迎回到数字化企业管理平台</p>

        <div class="demo-notice">
            <strong>演示说明：</strong>
            这是一个演示登录页面。在实际项目中，这里会连接到后端认证系统。
            您可以输入任意用户名和密码进行演示。
        </div>

        <form id="loginForm">
            <div class="form-group">
                <label class="form-label">用户名或邮箱</label>
                <input type="text" class="form-control" id="username" name="username" required>
            </div>

            <div class="form-group">
                <label class="form-label">密码</label>
                <input type="password" class="form-control" id="password" name="password" required>
            </div>

            <div class="remember-forgot">
                <label class="remember-me">
                    <input type="checkbox" id="rememberMe" name="rememberMe">
                    记住我
                </label>
                <a href="#" class="forgot-password">忘记密码？</a>
            </div>

            <button type="submit" class="btn-login" id="loginBtn">
                立即登录
            </button>
        </form>

        <div class="register-link">
            还没有账户？<a href="user-registration.html">立即注册</a>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('loginForm');
            const loginBtn = document.getElementById('loginBtn');
            
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                
                const username = document.getElementById('username').value.trim();
                const password = document.getElementById('password').value.trim();
                
                if (!username || !password) {
                    alert('请输入用户名和密码');
                    return;
                }
                
                loginBtn.disabled = true;
                loginBtn.textContent = '登录中...';
                
                // 模拟登录请求
                setTimeout(() => {
                    alert('登录成功！欢迎使用数字化企业管理平台。');
                    // 在实际项目中，这里会跳转到主页面
                    // window.location.href = 'dashboard.html';
                    loginBtn.disabled = false;
                    loginBtn.textContent = '立即登录';
                }, 1500);
            });
        });
    </script>
</body>

</html>
