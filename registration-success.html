<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册成功 - 数字化企业管理平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .success-container {
            max-width: 500px;
            width: 100%;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            padding: 40px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            text-align: center;
        }
        
        .success-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 24px;
            background: linear-gradient(135deg, #52c41a, #73d13d);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: successPulse 2s ease-in-out infinite;
        }
        
        .success-icon::before {
            content: '✓';
            color: white;
            font-size: 36px;
            font-weight: bold;
        }
        
        @keyframes successPulse {
            0%, 100% {
                transform: scale(1);
                box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.4);
            }
            50% {
                transform: scale(1.05);
                box-shadow: 0 0 0 20px rgba(82, 196, 26, 0);
            }
        }
        
        .success-title {
            font-size: 28px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 16px;
        }
        
        .success-message {
            font-size: 16px;
            color: #595959;
            line-height: 1.6;
            margin-bottom: 32px;
        }
        
        .info-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 24px;
            margin-bottom: 32px;
            text-align: left;
        }
        
        .info-title {
            font-size: 16px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
        }
        
        .info-title::before {
            content: 'ℹ️';
            margin-right: 8px;
        }
        
        .info-list {
            list-style: none;
            padding: 0;
        }
        
        .info-list li {
            font-size: 14px;
            color: #595959;
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }
        
        .info-list li::before {
            content: '•';
            color: #1890ff;
            position: absolute;
            left: 0;
            font-weight: bold;
        }
        
        .user-info {
            background: white;
            border: 1px solid #e8e8e8;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 24px;
        }
        
        .user-info-title {
            font-size: 14px;
            font-weight: 600;
            color: #262626;
            margin-bottom: 12px;
        }
        
        .user-detail {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .user-detail:last-child {
            border-bottom: none;
        }
        
        .user-label {
            font-size: 13px;
            color: #8c8c8c;
        }
        
        .user-value {
            font-size: 13px;
            color: #262626;
            font-weight: 500;
        }
        
        .btn-group {
            display: flex;
            gap: 12px;
            margin-top: 24px;
        }
        
        .btn {
            flex: 1;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            border: 1px solid;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #1890ff, #40a9ff);
            border-color: #1890ff;
            color: white;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #40a9ff, #69c0ff);
            border-color: #40a9ff;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(24, 144, 255, 0.4);
            text-decoration: none;
            color: white;
        }
        
        .btn-secondary {
            background: white;
            border-color: #d9d9d9;
            color: #595959;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }
        
        .btn-secondary:hover {
            border-color: #40a9ff;
            color: #40a9ff;
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
            text-decoration: none;
        }
        
        .countdown {
            font-size: 12px;
            color: #8c8c8c;
            margin-top: 16px;
        }
        
        .countdown-number {
            color: #1890ff;
            font-weight: 600;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .success-container {
                padding: 24px;
                margin: 10px;
            }
            
            .btn-group {
                flex-direction: column;
            }
            
            .success-title {
                font-size: 24px;
            }
        }
        
        @media (max-width: 480px) {
            body {
                padding: 10px;
            }
            
            .success-container {
                padding: 20px;
            }
            
            .success-title {
                font-size: 22px;
            }
            
            .success-icon {
                width: 60px;
                height: 60px;
            }
            
            .success-icon::before {
                font-size: 28px;
            }
        }
    </style>
</head>

<body>
    <div class="success-container">
        <div class="success-icon"></div>
        
        <h1 class="success-title">注册成功！</h1>
        <p class="success-message">
            恭喜您成功注册数字化企业管理平台账户！<br>
            我们已向您的邮箱发送了验证邮件，请及时查收并完成邮箱验证。
        </p>
        
        <div class="user-info">
            <div class="user-info-title">注册信息</div>
            <div class="user-detail">
                <span class="user-label">用户名</span>
                <span class="user-value" id="displayUsername">-</span>
            </div>
            <div class="user-detail">
                <span class="user-label">邮箱地址</span>
                <span class="user-value" id="displayEmail">-</span>
            </div>
            <div class="user-detail">
                <span class="user-label">注册时间</span>
                <span class="user-value" id="displayTime">-</span>
            </div>
        </div>
        
        <div class="info-card">
            <div class="info-title">接下来您需要：</div>
            <ul class="info-list">
                <li>检查您的邮箱（包括垃圾邮件文件夹）</li>
                <li>点击邮件中的验证链接完成邮箱验证</li>
                <li>验证完成后即可正常使用所有功能</li>
                <li>如未收到邮件，请检查邮箱地址是否正确</li>
            </ul>
        </div>
        
        <div class="btn-group">
            <a href="login.html" class="btn btn-primary">立即登录</a>
            <a href="user-registration.html" class="btn btn-secondary">重新注册</a>
        </div>
        
        <div class="countdown">
            <span id="countdownText">页面将在 <span class="countdown-number" id="countdownNumber">10</span> 秒后自动跳转到登录页面</span>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 从URL参数或localStorage获取用户信息
            const urlParams = new URLSearchParams(window.location.search);
            const username = urlParams.get('username') || localStorage.getItem('registeredUsername') || '新用户';
            const email = urlParams.get('email') || localStorage.getItem('registeredEmail') || '<EMAIL>';
            
            // 显示用户信息
            document.getElementById('displayUsername').textContent = username;
            document.getElementById('displayEmail').textContent = email;
            document.getElementById('displayTime').textContent = new Date().toLocaleString('zh-CN');
            
            // 倒计时自动跳转
            let countdown = 10;
            const countdownElement = document.getElementById('countdownNumber');
            const countdownTextElement = document.getElementById('countdownText');
            
            const timer = setInterval(() => {
                countdown--;
                countdownElement.textContent = countdown;
                
                if (countdown <= 0) {
                    clearInterval(timer);
                    countdownTextElement.textContent = '正在跳转到登录页面...';
                    window.location.href = 'login.html';
                }
            }, 1000);
            
            // 清理localStorage中的注册信息
            localStorage.removeItem('registeredUsername');
            localStorage.removeItem('registeredEmail');
        });
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 从URL参数或localStorage获取用户信息
            const urlParams = new URLSearchParams(window.location.search);
            const username = urlParams.get('username') || localStorage.getItem('registeredUsername') || '新用户';
            const email = urlParams.get('email') || localStorage.getItem('registeredEmail') || '<EMAIL>';

            // 显示用户信息
            document.getElementById('displayUsername').textContent = username;
            document.getElementById('displayEmail').textContent = email;
            document.getElementById('displayTime').textContent = new Date().toLocaleString('zh-CN');

            // 倒计时自动跳转
            let countdown = 10;
            const countdownElement = document.getElementById('countdownNumber');
            const countdownTextElement = document.getElementById('countdownText');

            const timer = setInterval(() => {
                countdown--;
                countdownElement.textContent = countdown;

                if (countdown <= 0) {
                    clearInterval(timer);
                    countdownTextElement.textContent = '正在跳转到登录页面...';
                    window.location.href = 'login.html';
                }
            }, 1000);

            // 清理localStorage中的注册信息
            localStorage.removeItem('registeredUsername');
            localStorage.removeItem('registeredEmail');
        });
    </script>
</body>

</html>
