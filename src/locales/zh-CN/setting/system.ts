pwd_must_change_for_admin_reset_pwd_tip: `启用后，当管理员为用户重置密码时，用户首次登录必须修改密码才能继续使用系统。

适用场景：提高密码安全性，确保临时密码及时更换
注意事项：AD/LDAP同步用户也需要修改密码，但可能需要在原系统操作
建议设置：建议启用，有助于提升账户安全性`,

user_field_tip: `设置用户重置密码时使用的身份识别字段，系统将根据此字段查找用户并发送重置验证信息。

可选字段：手机号、邮箱、用户名、工号等
使用说明：用户输入对应字段信息后，系统会发送验证码或重置链接
建议设置：选择用户常用且已验证的联系方式，如手机号和邮箱`,

pwd_max_age_tip: `设置密码的有效使用天数，超过期限后用户需要修改密码才能继续使用系统。

有效范围：1-3650天，设置为0表示永不过期
提醒机制：配合"密码到期提醒"功能，可在到期前通知用户
建议设置：根据安全要求设置，一般建议90-180天`,

pwd_expire_warning_tip: `在密码即将到期前的指定天数内，通过邮件、短信和系统消息提醒用户及时修改密码。

提醒方式：邮件 + 短信 + 站内消息
提醒时机：密码到期前N天开始提醒
建议设置：7-14天，给用户充足的处理时间`,

pwd_in_history_tip: `记住用户最近使用过的密码，防止重复使用相同密码，提高账户安全性。

工作原理：用户修改密码时，系统会检查新密码是否与最近N个历史密码重复
记录数量：最多记录15个历史密码
建议设置：3-5个历史密码，既保证安全又不影响用户体验`,

forget_password_attrs_tip: `设置用户忘记密码时可以使用的身份验证方式，用户可通过这些方式重置密码。

验证方式：手机验证码、邮箱重置链接等
安全保护：系统会对发送频率进行限制，防止恶意操作
建议设置：包含用户的主要联系方式，确保用户能够正常找回密码`,

identity_verify_attrs_tip: `设置用户登录后需要额外验证身份的字段，通过验证这些信息来确认用户身份可信。

验证时机：用户完成密码登录后，系统会要求验证指定的联系方式
验证方式：发送验证码到手机或邮箱进行确认
特殊说明：管理员用户和从企业工作台登录的用户会跳过此验证
建议设置：根据安全需求选择，避免设置过多影响登录体验`,

执行机制：
• 管理员重置密码时：用户密码状态设为TEMP
• 用户登录验证：检查密码状态，如为TEMP则设置Redis标记"IS_UPDATE_PASSWORD"
• 登录流程拦截：检测到标记后跳转到密码修改页面
• 密码修改完成：清除Redis标记，密码状态改为NORMAL

例外情况：
• SCIM协议同步的用户：不受此限制，密码状态不会设为TEMP
• JIT(即时供应)用户：不受此限制，密码状态不会设为TEMP
• 注意：AD/LDAP数据源同步用户(BY_SYNC_DS)在当前实现中仍受此限制

取值范围：启用/禁用
配置建议：建议启用以提高密码安全性，确保管理员重置的密码得到及时更换
安全考量：防止管理员设置的临时密码被长期使用，降低密码泄露风险
依赖条件：需要"管理员重置用户密码"功能启用
注意事项：启用后会影响用户首次登录体验，需要提前告知用户`,

执行机制：
• 用户输入重置信息：根据配置的字段类型接收用户输入
• 用户身份匹配：系统根据配置字段在数据库中查找对应用户
• 验证信息发送：向用户的邮箱或手机发送重置密码的验证码或链接
• 身份验证完成：用户通过验证后可设置新密码

字段类型处理：
• 手机号码(phone_number)：支持中国大陆手机号格式，自动格式化处理
• 邮箱地址(email)：标准邮箱格式验证
• 用户名(username)：系统内唯一用户标识
• 工号(job_number)：企业内部员工编号

同步用户处理：
• AD/LDAP同步用户：可正常使用重置密码功能，但密码修改可能需要在源系统进行
• 外部连接器用户：通过connector_user_id字段进行匹配

取值范围：phone_number, email, username, job_number等用户属性字段
配置建议：建议配置多个字段以提供更多重置方式，优先选择已验证的联系方式
安全考量：确保配置的字段具有唯一性，避免身份混淆
注意事项：字段必须在用户属性中存在且有值才能生效`,

执行机制：
• 密码创建/修改时：计算过期时间(当前时间 + 有效期天数)，存储在pwd_expiration_time字段
• 登录验证时：检查当前时间是否超过过期时间
• 过期处理：密码过期后用户登录时会被要求修改密码
• 系统限制：最大有效期不超过3650天(10年)

同步用户处理：
• AD/LDAP同步用户：密码有效期仍然生效，但密码修改可能需要在AD/LDAP服务器上进行
• SCIM/JIT用户：密码有效期正常生效
• 外部用户：根据来源系统的密码策略执行

取值范围：1-3650天，0表示永不过期
配置建议：根据安全策略设置，一般建议90-180天
安全考量：定期强制密码更换可降低密码泄露风险
依赖条件：需要配合"密码到期提醒"功能使用
注意事项：过期提醒功能需要配合"密码到期提醒"配置使用`,

执行机制：
• 定时任务扫描：系统定时检查所有用户的密码过期时间
• 提醒条件判断：当前时间 + 提醒天数 >= 密码过期时间
• 多渠道通知：同时发送邮件、短信和站内消息提醒
• 防重复发送：使用Redis锁机制防止重复发送提醒

消息发送逻辑：
• 邮件提醒：发送到用户绑定的邮箱地址
• 短信提醒：发送到用户绑定的手机号码
• 站内消息：通过消息服务推送系统通知

同步用户处理：
• 所有类型用户均会收到提醒，包括AD/LDAP同步用户
• 提醒内容会说明密码修改的具体操作方式

取值范围：1-365天，建议设置为7-30天
配置建议：建议设置为7-14天，给用户充足的修改时间
技术实现：通过定时任务扫描pwd_expiration_time字段
依赖条件：需要"密码有效期"功能启用且大于0
注意事项：需要确保邮件和短信服务配置正确`,

执行机制：
• 密码存储：每次密码修改时，将旧密码的MD5值存储在user_status表的history_passwd字段
• 密码验证：用户设置新密码时，检查新密码MD5是否与历史记录中的任何一个匹配
• 历史管理：最多保存15个历史密码，超出时删除最早的记录
• 匹配检查：从最近的N个历史密码中检查重复(N为配置值)

同步用户处理：
• AD/LDAP同步用户：历史密码检查仍然生效，但实际密码修改在AD/LDAP服务器上进行
• SCIM/JIT用户：历史密码检查正常生效
• 注意：同步用户的密码修改可能绕过本地历史检查

验证时机：
• 管理员重置密码时：检查新密码是否在历史记录中
• 用户自助修改密码时：检查新密码是否重复使用
• 忘记密码重置时：检查重置的新密码

取值范围：0-15个历史密码，0表示不检查历史密码
配置建议：建议设置为3-5个，平衡安全性和用户体验
安全考量：防止用户在多个常用密码间循环使用
技术实现：使用MD5加密存储，不可逆向解密
注意事项：启用后用户修改密码时会有额外验证步骤`,

执行机制：
• 用户输入验证：用户输入登录标识，系统判断输入类型(邮箱/手机/用户名等)
• 身份匹配：根据配置的属性字段在数据库中查找对应用户
• 验证方式选择：根据用户信息和配置选择邮箱或短信验证
• 重置流程：发送验证码或重置链接，用户验证后设置新密码

验证流程分支：
• 邮箱验证：发送重置密码链接到用户邮箱
• 短信验证：发送验证码到用户手机，验证后跳转重置页面
• 用户名验证：根据用户绑定的邮箱或手机选择验证方式

安全机制：
• 频率限制：防止短时间内重复发送验证信息
• 用户隐私保护：对于不存在的用户也返回相同的成功响应
• 验证码有效期：限制验证码的使用时间

取值范围：phone_number, email等已验证的联系方式字段
默认配置：["phone_number", "email"]
配置建议：建议包含用户的主要联系方式，确保字段已通过验证
安全考量：只配置已验证的联系方式，避免安全风险
注意事项：配置的字段必须是用户可访问且已验证的联系方式`,

执行机制：
• 登录后检查：用户完成基础认证后，检查是否需要身份验证
• 可信属性判断：查询用户已验证的可信属性列表
• 验证流程：对未验证的属性发送验证码，用户验证后标记为可信
• 验证记录：将验证通过的属性记录到用户信任表中

验证逻辑：
• 属性有效性：只对用户已填写且非空的属性进行验证
• 可信状态：已验证的属性不会重复要求验证
• 掩码显示：验证页面对敏感信息进行掩码处理
• 多属性支持：可同时配置多个属性进行验证

特殊用户处理：
• admin用户：跳过身份验证检查
• 工作台访问：从企业工作台(钉钉/企微/飞书)访问时跳过验证
• 同步用户：所有类型用户均需要进行身份验证

验证方式：
• 手机号验证：发送短信验证码
• 邮箱验证：发送邮件验证码
• 其他属性：根据属性类型选择合适的验证方式

取值范围：phone_number, email等用户联系方式属性
配置建议：根据安全需求选择关键联系方式进行验证
安全考量：验证通过的属性会被标记为可信，降低后续验证频率
依赖条件：需要用户已填写相应的联系方式信息
注意事项：过多的身份验证会影响用户登录体验，需要平衡安全性和便利性`, 