package com.cyberscraft.uep.acm.listener.dingtalk;

import com.cyberscraft.uep.acm.constants.ThreadPoolNameConstant;
import com.cyberscraft.uep.acm.listener.EventListener;
import com.cyberscraft.uep.acm.listener.EventSource;
import com.cyberscraft.uep.acm.service.AcmStreamTenantService;
import com.cyberscraft.uep.common.domain.auth.Account;
import com.cyberscraft.uep.common.domain.auth.DingTalkParam;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import com.dingtalk.open.app.api.GenericEventListener;
import com.dingtalk.open.app.api.OpenDingTalkClient;
import com.dingtalk.open.app.api.OpenDingTalkStreamClientBuilder;
import com.dingtalk.open.app.api.message.GenericOpenDingTalkEvent;
import com.dingtalk.open.app.api.security.AuthClientCredential;
import com.dingtalk.open.app.stream.protocol.event.EventAckStatus;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;

/**
 * @Description 钉钉事件监听
 * <AUTHOR>
 * @Date 2024/10/10 10:55
 */
@Component
public class DingTalkEventSource implements EventSource {

    private static final Logger logger = LoggerFactory.getLogger(DingTalkEventSource.class);

    private final Map<String, OpenDingTalkClient> clientMap = new ConcurrentHashMap<>();

    private final Map<String, EventListener> listenerMap = new ConcurrentHashMap<>();


    @Resource(name = ThreadPoolNameConstant.COMMON_POOL_NAME)
    private ExecutorService commonPoolExecutor;

    @Autowired
    private RedissonClient redissonClient;

    @Autowired
    private AcmStreamTenantService acmStreamTenantService;

    public DingTalkEventSource() {
    }

    @Override
    public void connect(Account account) {
        DingTalkParam dingTalkParam = (DingTalkParam) account;
        try {
            if (clientMap.get(dingTalkParam.getAppKey()) == null) {
                if (StringUtils.isBlank(dingTalkParam.getCorpId())) {
                    throw new RuntimeException("corpId is empty");
                }
                OpenDingTalkClient build = OpenDingTalkStreamClientBuilder
                        .custom()
                        .credential(new AuthClientCredential(dingTalkParam.getAppKey(), dingTalkParam.getAppSecret()))
                        //注册事件监听
                        .registerAllEventListener(new GenericEventListener() {
                            public EventAckStatus onEvent(GenericOpenDingTalkEvent event) {
                                try {
                                    logger.info("receive dingtalk event: {}", JsonUtil.obj2Str(event));
                                    commonPoolExecutor.submit(() -> {
                                        String tenantId = (String) redissonClient.getBucket(dingTalkParam.getCorpId()).get();
                                        if (StringUtils.isBlank(tenantId)) {
                                            tenantId = acmStreamTenantService.getTenantId(dingTalkParam.getCorpId());
                                        }
                                        String str = JsonUtil.obj2Str(event);
                                        TenantHolder.setTenantCode(tenantId);
                                        logger.info("receive dingtalk event: {}, tenantId: {}", str, tenantId);
                                        onEventReceived(dingTalkParam.getAppKey(), event.getEventId(), event);

                                    });
                                    //消费成功
                                    return EventAckStatus.SUCCESS;
                                } catch (Exception e) {
                                    //消费失败
                                    return EventAckStatus.LATER;
                                }
                            }
                        })
                        .build();
                build.start();
                clientMap.put(dingTalkParam.getAppKey(), build);
                logger.info(" appKey {} DingTalkEventSource register success", dingTalkParam.getAppKey());
            }
        } catch (Exception e) {
            logger.error(" appKey {} DingTalkEventSource register failed", dingTalkParam.getAppKey(), e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public void checkConnections() {

    }

    @Override
    public void disconnect() {
        try {
            for (Map.Entry<String, OpenDingTalkClient> clientEntry : clientMap.entrySet()) {
                OpenDingTalkClient client = clientEntry.getValue();
                client.stop();
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public void disconnect(String key) {
        try {

            OpenDingTalkClient openDingTalkClient = clientMap.get(key);
            if (openDingTalkClient != null) {
                openDingTalkClient.stop();
            }
            //移除连接
            clientMap.remove(key);
        } catch (Exception e) {
            logger.error("disconnect error", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public void registerEventListener(String key, EventListener listener) {
        listenerMap.putIfAbsent(key, listener);
    }

    private void onEventReceived(String key, String eventId, Object event) {
        EventListener eventListener = listenerMap.get(key);
        if (eventListener != null) {
            eventListener.onEvent(key, eventId, event);
        }
    }
}
