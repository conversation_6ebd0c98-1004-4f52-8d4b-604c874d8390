package com.cyberscraft.uep.acm.auditlog;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/1/5 4:18 下午
 */
public enum AuditEventSubType {

    // For FLOW
    CREATE_FLOW(1401, "创建连接流"),
    UPDATE_FLOW(1402, "更新连接流"),
    DELETE_FLOW(1403, "删除连接流"),
    COPY_FLOW(1404, "复制连接流"),
    RETRY_FLOW(1405, "重试连接流"),
    CREATE_FLOW_NODE(1406, "创建连接流节点"),
    UPDATE_FLOW_NODE(1407, "更新连接流节点"),
    DELETE_FLOW_NODE(1408, "删除连接流节点"),
    MOVE_FLOW_NODE(1409, "移动连接流节点"),
    CREATE_FLOW_ACCOUNT_INSTANCE(1410, "创建连接流账户实例"),
    UPDATE_FLOW_ACCOUNT_INSTANCE(1411, "更新连接流账户实例"),
    DELETE_FLOW_ACCOUNT_INSTANCE(1412, "删除连接流账户实例"),
    ON_FLOW(1413, "发布连接流"),
    OFF_FLOW(1414, "下线连接流"),

    COPY_FLOW_NODE(1415, "向下复制连接流节点"),
    CLOSE_FLOW_NODE(1416, "启停用连接流节点"),
    UPDATE_FLOW_NODE_NOTE(1417, "更新连接流节点备注"),

    // For FLOW TEMPLATE
    ENABLE_FLOW_TEMPLATE(1501, "启用连接流模板"),
    ;

    public static final int CREATE_FLOW_CODE = 1401;
    public static final int UPDATE_FLOW_CODE = 1402;
    public static final int DELETE_FLOW_CODE = 1403;
    public static final int COPY_FLOW_CODE = 1404;
    public static final int RETRY_FLOW_CODE = 1405;
    public static final int CREATE_FLOW_NODE_CODE = 1406;
    public static final int UPDATE_FLOW_NODE_CODE = 1407;
    public static final int DELETE_FLOW_NODE_CODE = 1408;
    public static final int MOVE_FLOW_NODE_CODE = 1409;
    public static final int CREATE_FLOW_ACCOUNT_INSTANCE_CODE = 1410;
    public static final int UPDATE_FLOW_ACCOUNT_INSTANCE_CODE = 1411;
    public static final int DELETE_FLOW_ACCOUNT_INSTANCE_CODE = 1412;
    public static final int ON_FLOW_CODE = 1413;
    public static final int OFF_FLOW_CODE = 1414;
    public static final int COPY_FLOW_NODE_CODE = 1415;
    public static final int CLOSE_FLOW_NODE_CODE = 1416;
    public static final int UPDATE_FLOW_NODE_NOTE_CODE = 1417;

    public static final int FORCED_ON_FLOW_CODE = 1418;

    public static final int ENABLE_FLOW_TEMPLATE_CODE = 1501;

    private int code;

    private String desc;

    AuditEventSubType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int code() {
        return code;
    }

    public String desc() {
        return desc;
    }
}