package com.cyberscraft.uep.acm.task;

import com.cyberscraft.uep.acm.dbo.FlowInstanceDBO;
import com.cyberscraft.uep.acm.entity.AppFlowLogEntity;
import com.cyberscraft.uep.acm.service.IAppFlowLogService;
import com.cyberscraft.uep.acm.service.IAppFlowService;
import com.cyberscraft.uep.common.config.ServerConfig;
import com.cyberscraft.uep.iam.constants.RedisKeyConstants;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import com.cyberscraft.uep.task.Task;
import com.cyberscraft.uep.task.monitor.RedisTaskMonitor;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/29 8:54 下午
 */
@Component
public class FlowTaskMonitor extends RedisTaskMonitor {

    @Autowired
    private IAppFlowService flowService;

    @Resource
    private FlowInstanceDBO flowInstanceDBO;

    @Resource
    private IAppFlowLogService appFlowLogService;

    @Autowired
    private ServerConfig serverConfig;

    public FlowTaskMonitor() {
        super("TASK:FLOW");
    }

    @Autowired
    @Override
    public void setRedissonClient(RedissonClient redissonClient) {
        super.setRedissonClient(redissonClient);
    }

    @Override
    public void resumeTask(Task task, Boolean isDead, String msg) {
        if (task instanceof FlowTask) {
            FlowTask flowTask = (FlowTask) task;
            String taskId = flowTask.getId();
            Long flowId = flowTask.getFlowId();
            String tenantId = flowTask.getTenantId();

            TenantHolder.setTenantCode(tenantId);

//            FlowInstanceEntity flowEntity = flowInstanceDBO.getById(flowId);
//            flowEntity.setStatus(RunStatus.FAILED.name());
//            flowEntity.setOpStatus(OpStatus.ON_WARN.name());
//            flowEntity.setErrorMsg("服务被中断");
//            flowEntity.setRunStartTime(DateUtil.long2date(flowTask.getCreateTime()));
//            flowEntity.setRunEndTime(LocalDateTime.now());
//            flowEntity.setUpdateTime(LocalDateTime.now());
//            flowInstanceDBO.updateById(flowEntity);

            if (Long.valueOf(taskId) != 0L) {
                AppFlowLogEntity appFlowLogEntity = appFlowLogService.queryFlowLog(Long.valueOf(taskId));
                if (appFlowLogEntity != null && appFlowLogEntity.getStatus() == 3) {
                    appFlowLogEntity.setStatus(0);
                    appFlowLogEntity.setEndTime(LocalDateTime.now());
                    appFlowLogEntity.setErrorMsg(msg);
                    appFlowLogService.updateFlowLog(appFlowLogEntity);

                    if (isDead) {
                        String lockKey = String.format(RedisKeyConstants.API_FLOW_SCHEDULE_LOCK_KEY, tenantId, flowId);
                        this.getRedissonClient().getBucket(lockKey).delete();

                        flowService.retryFlow(Long.valueOf(taskId));
                    }
                }
            }
        }
    }

    @Override
    public String getTenant(Task task) {
        FlowTask flowTask = (FlowTask) task;
        return flowTask.getTenantId();
    }

    @Override
    public List<String> getTenantWhiteList() {
        return serverConfig.getWhiteTenantList();
    }

    @Override
    public List<String> getTenantBlackList() {
        return serverConfig.getBlackTenantList();
    }

}
