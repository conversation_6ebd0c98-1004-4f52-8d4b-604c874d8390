package com.cyberscraft.uep.acm.dbo.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cyberscraft.uep.acm.dao.AppFlowLogDao;
import com.cyberscraft.uep.acm.dbo.AppFlowLogDBO;
import com.cyberscraft.uep.acm.dto.FlowLogDto;
import com.cyberscraft.uep.acm.entity.AppFlowLogEntity;
import com.cyberscraft.uep.common.dto.QueryPage;
import com.cyberscraft.uep.common.util.DateUtil;
import com.cyberscraft.uep.common.util.PagingUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * @Author：xhquan
 * @Project：uep-services
 * @since：2023/7/13 14:38
 */
@Service
public class AppFlowLogDBOImpl extends ServiceImpl<AppFlowLogDao, AppFlowLogEntity> implements AppFlowLogDBO {

    @Override
    public QueryPage<AppFlowLogEntity> queryFlowLog(FlowLogDto flowLogDto) {
        QueryPage<AppFlowLogEntity> queryPage = new QueryPage<>(flowLogDto.getPage(), flowLogDto.getSize(), null);
        LambdaQueryWrapper<AppFlowLogEntity> queryWrapper = new QueryWrapper().lambda();

        if (StringUtils.isNotBlank(flowLogDto.getFilter())) {
            queryWrapper.and(el -> el.eq(AppFlowLogEntity::getFlowName, flowLogDto.getFilter()).or().eq(AppFlowLogEntity::getId, flowLogDto.getFilter()));
        }
        if (flowLogDto.getFlowId() != null) {
            queryWrapper.eq(AppFlowLogEntity::getFlowId, flowLogDto.getFlowId());
        }
        if (flowLogDto.getStatus() != null) {
            queryWrapper.eq(AppFlowLogEntity::getStatus, flowLogDto.getStatus());
        }
        if (flowLogDto.getType() != null) {
            queryWrapper.eq(AppFlowLogEntity::getType, flowLogDto.getType());
        }
        if (StringUtils.isNotBlank(flowLogDto.getStartTime())) {
            queryWrapper.ge(AppFlowLogEntity::getStartTime, DateUtil.long2date(Long.valueOf(flowLogDto.getStartTime())));
        }
        if (StringUtils.isNotBlank(flowLogDto.getEndTime())) {
            queryWrapper.le(AppFlowLogEntity::getStartTime, DateUtil.long2date(Long.valueOf(flowLogDto.getEndTime())));
        }

        queryWrapper.orderByDesc(AppFlowLogEntity::getStartTime);
        IPage<AppFlowLogEntity> mybatisPage = PagingUtil.toMybatisPage(queryPage);
        mybatisPage = page(mybatisPage, queryWrapper);
        PagingUtil.fromMybatisPageToQueryPage(mybatisPage, queryPage);

        return queryPage;
    }
}
