package com.cyberscraft.uep.acm.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cyberscraft.uep.acm.entity.FlowNodeEntity;
import com.cyberscraft.uep.acm.query.FlowProxyBo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/10/19 3:45 下午
 */
public interface FlowNodeDao extends BaseMapper<FlowNodeEntity> {

    List<FlowNodeEntity> listFlowNodes(@Param("flowId") Long flowId);

    FlowNodeEntity getFlowNode(@Param("nodeId") Long nodeId);

    List<FlowProxyBo> getFlowNodeByProxy(@Param("ids") List<Long> ids, @Param("accountIds") List<Long> accountIds);

}
