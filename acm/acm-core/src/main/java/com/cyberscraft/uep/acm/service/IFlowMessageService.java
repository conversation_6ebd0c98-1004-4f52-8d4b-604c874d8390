package com.cyberscraft.uep.acm.service;

import com.cyberscraft.uep.acm.entity.FlowMessageEntity;

import java.util.List;
import java.util.Set;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/24 11:37 上午
 */
public interface IFlowMessageService {

    /**
     * 持久化连接流webhook事件信息
     * @param tenantId
     * @param flowId
     * @param taskId
     * @param data
     * @param isSync 是否同步连接流，同步连接流不用持久化
     * @param recordSuccessLog 是否记录节点成功执行的日志
     * @return 该租户待执行的连接流总数
     */
    int saveFlowMessage(String tenantId, Long flowId, Long taskId, Object data, boolean isSync, boolean recordSuccessLog);

    /**
     * 获取一个待执行的webhook事件消息
     * @param tenantId
     * @return
     */
    FlowMessageEntity getHeadMessage(String tenantId);

    /**
     * 标记webhook事件消息已执行
     * @param messageId
     */
    void flagFlowMessageDone(Long messageId);

    FlowMessageEntity getFlowMessage(Long messageId);

    /**
     * 删除事件消息
     * @param messageId
     */
    void delInvalidFlowMessage(Long messageId);

    /**
     * 获取长时间未被执行的消息ID列表
     * @return
     */
    List<Long> listWaitMessages();

    /**
     * 获取待执行的租户code列表
     * @return
     */
    Set<String> listWaitTenants();

    /**
     * 批量删除已执行的过期消息
     * @param expiredHour
     * @param limit
     */
    int batchDelExpiredDoneMessages(int expiredHour, int limit);

}
