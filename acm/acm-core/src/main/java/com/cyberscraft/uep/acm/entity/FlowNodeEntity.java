package com.cyberscraft.uep.acm.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/10/19 3:14 下午
 */
@TableName("acm_flow_node")
public class FlowNodeEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 父节点ID
     */
    private Long parentId;

    /**
     * 同级下一节点ID
     */
    private Long nextId;

    /**
     * 节点类型
     */
    private String nodeType;

    @TableField(exist = false)
    private String protocol;

    /**
     * 连接器ID
     */
    private Long connectorId;

    /**
     * 连接器名称
     */
    @TableField(exist = false)
    private String connectorName;

    /**
     * 执行动作模型ID
     */
    private Long actionId;

    /**
     * 执行动作名称
     */
    @TableField(exist = false)
    private String actionName;

    /**
     * 执行动作key
     */
    private String actionKey;


    /**
     * 帐号实例ID
     */
    private Long accountId;

    /**
     * 帐号模型ID
     */
    @TableField(exist = false)
    private Long accountSchemaId;

    /**
     * 连接流ID
     */
    private Long flowId;

    /**
     * 描述
     */
    private String description;

    /**
     * 调试状态，参见RunStatus类
     */
    private String status;

    /**
     * 参数配置
     */
    private String config;

    /**
     * 入参
     */
    private String input;

    /**
     * 出参
     */
    private String output;

    /**
     * 上一次执行失败的错误描述
     */
    private String errorMsg;

    /**
     * 上一次运行开始时间
     */
    private LocalDateTime runStartTime;

    /**
     * 上一次运行结束时间
     */
    private LocalDateTime runEndTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 租户id
     */
    private String tenantId;
    /**
     * 网络代理ID
     */
    private Long proxyId;

    /**
     * 节点备注
     */
    private String note;

    /**
     * 启停用，0启用，1停用
     */
    private String closed;

    /**
     * 错误处理配置
     */
    private String errorHandler;

    @TableField(exist = false)
    private FlowNodeEntity nextEntity;

    @TableField(exist = false)
    private FlowNodeEntity prevEntity;

    @TableField(exist = false)
    private FlowInstanceEntity flowEntity;

    @TableField(exist = false)
    private String icon;

    @TableField(exist = false)
    private Boolean isWebhookTrigger;

    @TableField(exist = false)
    private String sampleOutput;

    @TableField(exist = false)
    private String nodeStruct;

    /**
     * 版本类型
     */
    @TableField(exist = false)
    private String verType;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getNodeType() {
        return nodeType;
    }

    public void setNodeType(String nodeType) {
        this.nodeType = nodeType;
    }

    public String getProtocol() {
        return protocol;
    }

    public void setProtocol(String protocol) {
        this.protocol = protocol;
    }

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public Long getAccountSchemaId() {
        return accountSchemaId;
    }

    public void setAccountSchemaId(Long accountSchemaId) {
        this.accountSchemaId = accountSchemaId;
    }

    public Long getFlowId() {
        return flowId;
    }

    public void setFlowId(Long flowId) {
        this.flowId = flowId;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Long getNextId() {
        return nextId;
    }

    public void setNextId(Long nextId) {
        this.nextId = nextId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getConfig() {
        return config;
    }

    public void setConfig(String config) {
        this.config = config;
    }

    public String getSampleOutput() {
        return sampleOutput;
    }

    public void setSampleOutput(String sampleOutput) {
        this.sampleOutput = sampleOutput;
    }

    public String getNodeStruct() {
        return nodeStruct;
    }

    public void setNodeStruct(String nodeStruct) {
        this.nodeStruct = nodeStruct;
    }

    public Long getConnectorId() {
        return connectorId;
    }

    public void setConnectorId(Long connectorId) {
        this.connectorId = connectorId;
    }

    public String getConnectorName() {
        return connectorName;
    }

    public void setConnectorName(String connectorName) {
        this.connectorName = connectorName;
    }

    public String getActionName() {
        return actionName;
    }

    public void setActionName(String actionName) {
        this.actionName = actionName;
    }

    public Long getActionId() {
        return actionId;
    }

    public void setActionId(Long actionId) {
        this.actionId = actionId;
    }

    public String getActionKey() {
        return actionKey;
    }

    public void setActionKey(String actionKey) {
        this.actionKey = actionKey;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getInput() {
        return input;
    }

    public void setInput(String input) {
        this.input = input;
    }

    public String getOutput() {
        return output;
    }

    public void setOutput(String output) {
        this.output = output;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public LocalDateTime getRunStartTime() {
        return runStartTime;
    }

    public void setRunStartTime(LocalDateTime runStartTime) {
        this.runStartTime = runStartTime;
    }

    public LocalDateTime getRunEndTime() {
        return runEndTime;
    }

    public void setRunEndTime(LocalDateTime runEndTime) {
        this.runEndTime = runEndTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public Long getProxyId() {
        return proxyId;
    }

    public void setProxyId(Long proxyId) {
        this.proxyId = proxyId;
    }

    public FlowNodeEntity getNextEntity() {
        return nextEntity;
    }

    public void setNextEntity(FlowNodeEntity nextEntity) {
        this.nextEntity = nextEntity;
    }

    public FlowNodeEntity getPrevEntity() {
        return prevEntity;
    }

    public void setPrevEntity(FlowNodeEntity prevEntity) {
        this.prevEntity = prevEntity;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public FlowInstanceEntity getFlowEntity() {
        return flowEntity;
    }

    public void setFlowEntity(FlowInstanceEntity flowEntity) {
        this.flowEntity = flowEntity;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public String getClosed() {
        return closed;
    }

    public void setClosed(String closed) {
        this.closed = closed;
    }

    public String getErrorHandler() {
        return errorHandler;
    }

    public void setErrorHandler(String errorHandler) {
        this.errorHandler = errorHandler;
    }

    public Boolean getWebhookTrigger() {
        return isWebhookTrigger;
    }

    public void setWebhookTrigger(Boolean webhookTrigger) {
        isWebhookTrigger = webhookTrigger;
    }

    public String getVerType() {
        return verType;
    }

    public void setVerType(String verType) {
        this.verType = verType;
    }
}
