package com.cyberscraft.uep.acm.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.cyberscraft.uep.acm.dbo.AcmStreamTenantDBO;
import com.cyberscraft.uep.acm.entity.AcmStreamTenantEntity;
import com.cyberscraft.uep.acm.service.AcmStreamTenantService;
import com.cyberscraft.uep.dataconnect.exception.FlowException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024/11/7 20:52
 */
@Service
public class AcmStreamTenantServiceImpl implements AcmStreamTenantService {

    @Autowired
    private AcmStreamTenantDBO acmStreamTenantDBO;

    @Override
    public String getTenantId(String thirdId) {
        if (StringUtils.isBlank(thirdId)) {
            throw new FlowException("thirdId is blank");
        }
        QueryWrapper<AcmStreamTenantEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AcmStreamTenantEntity::getThirdId, thirdId);
        AcmStreamTenantEntity one = acmStreamTenantDBO.getOne(queryWrapper);
        if (one != null) {
            return one.getTenantId();
        }
        throw new FlowException("thirdId is error");
    }

    @Override
    public boolean saveTenant(String thirdId) {
        QueryWrapper<AcmStreamTenantEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AcmStreamTenantEntity::getThirdId, thirdId);
        AcmStreamTenantEntity one = acmStreamTenantDBO.getOne(queryWrapper);
        if (one == null) {
            AcmStreamTenantEntity entity = new AcmStreamTenantEntity();
            entity.setThirdId(thirdId);
            return acmStreamTenantDBO.save(entity);
        }
        return true;
    }
}
