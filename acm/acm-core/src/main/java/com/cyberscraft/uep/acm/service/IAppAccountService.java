package com.cyberscraft.uep.acm.service;

import com.cyberscraft.uep.acm.dto.AppAccountDto;
import com.cyberscraft.uep.acm.dto.AppAccountSchema;
import com.cyberscraft.uep.acm.entity.AppAccountEntity;
import com.cyberscraft.uep.common.dto.QueryPage;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022/10/20 12:01 下午
 */
public interface IAppAccountService {

    /**
     * 获取帐号详情
     *
     * @param id
     * @return
     */
    AppAccountSchema getById(Long id);

    /**
     * 创建帐号
     *
     * @param appAccountSchema
     * @return
     */
    AppAccountSchema createAccount(AppAccountSchema appAccountSchema);

    /**
     * 修改帐号
     *
     * @param appAccountSchema
     * @return
     */
    AppAccountSchema updateAccount(Long id, AppAccountSchema appAccountSchema);

    /**
     * 按id删除帐号
     *
     * @param id
     * @return
     */
    Void deleteAccount(Long id);

    /**
     * 获取帐号列表
     *
     * @param page 页码
     * @param size 每页条数
     * @return
     */
    QueryPage<AppAccountSchema> queryAccounts(Integer page, Integer size);

    /**
     * 根据账号ID查询连接流账号模型
     *
     * @param accountIds 连接流账号ID
     * @return
     */
    List<AppAccountEntity> queryAccounts(List<Long> accountIds);


    /**
     * 查询帐号实例列表
     *
     * @param linkId
     * @return
     */
    List<AppAccountDto> queryAccountByLinkId(Long linkId);

    /**
     * 根据id获取帐号实例详情
     *
     * @param id
     * @return
     */
    AppAccountDto getAccountInstanceById(Long id);

    /**
     * 测试帐号实例
     *
     * @param appAccountDto
     * @return
     */
    Boolean testAccountInstance(AppAccountDto appAccountDto);

    /**
     * 创建帐号实例
     *
     * @param appAccountDto
     * @return
     */
    AppAccountDto createAccountInstance(AppAccountDto appAccountDto);

    /**
     * 修改帐号实例
     *
     * @param id
     * @param appAccountDto
     * @return
     */
    AppAccountDto updateAccountInstance(Long id, AppAccountDto appAccountDto);

    /**
     * 删除帐号实例
     *
     * @param id
     * @return
     */
    AppAccountDto deleteAccountInstance(Long id);


    /**
     * 查看指定代理下的所有账号
     *
     * @param groupId
     * @return
     */
    List<AppAccountDto> getAccountsByProxyId(Long groupId);

}
