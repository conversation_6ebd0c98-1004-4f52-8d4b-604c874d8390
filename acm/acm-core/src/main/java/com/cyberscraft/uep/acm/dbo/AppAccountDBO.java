package com.cyberscraft.uep.acm.dbo;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cyberscraft.uep.acm.entity.AppAccountEntity;
import com.cyberscraft.uep.common.dto.QueryPage;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2023/7/13 5:12 下午
 */
public interface AppAccountDBO extends IService<AppAccountEntity> {

    QueryPage<AppAccountEntity> queryAccounts(QueryPage<AppAccountEntity> queryPage);

    List<AppAccountEntity> queryAccounts(List<Long> ids);
}
