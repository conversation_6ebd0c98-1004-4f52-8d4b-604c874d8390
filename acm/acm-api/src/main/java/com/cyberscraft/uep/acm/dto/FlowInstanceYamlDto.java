package com.cyberscraft.uep.acm.dto;

import java.util.List;

/**
 * @Description 连接流导入导出yaml实体类
 * <AUTHOR>
 * @Date 2024/4/19 11:13
 */
public class FlowInstanceYamlDto {

    /**
     * 类型
     */
    private String type;

    /**
     * 连接流信息
     */
    private FlowDto flowDto;

    /**
     * 连接流中的所有节点信息
     */
    private List<FlowNodeDto> nodes;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public FlowDto getFlowDto() {
        return flowDto;
    }

    public void setFlowDto(FlowDto flowDto) {
        this.flowDto = flowDto;
    }

    public List<FlowNodeDto> getNodes() {
        return nodes;
    }

    public void setNodes(List<FlowNodeDto> nodes) {
        this.nodes = nodes;
    }
}
