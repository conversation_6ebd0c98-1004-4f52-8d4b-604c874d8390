package com.cyberscraft.uep.acm.cal.dto;

/**
 * @Description 钉钉的日程循环规则
 * <AUTHOR>
 * @Date 2024/10/15 12:11
 */
public class DingTalkRecurrence {

    private Pattern pattern;

    private Range range;

    public Pattern getPattern() {
        return pattern;
    }

    public void setPattern(Pattern pattern) {
        this.pattern = pattern;
    }

    public Range getRange() {
        return range;
    }

    public void setRange(Range range) {
        this.range = range;
    }
   public static class Pattern {
        private String type;

        private String daysOfWeek;

        private Integer dayOfMonth;

        private String index;

        private Integer interval;

        private String firstDayOfWeek;

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

       public String getDaysOfWeek() {
           return daysOfWeek;
       }

       public void setDaysOfWeek(String daysOfWeek) {
           this.daysOfWeek = daysOfWeek;
       }

       public Integer getDayOfMonth() {
           return dayOfMonth;
       }

       public void setDayOfMonth(Integer dayOfMonth) {
           this.dayOfMonth = dayOfMonth;
       }

       public String getIndex() {
            return index;
        }

        public void setIndex(String index) {
            this.index = index;
        }

       public Integer getInterval() {
           return interval;
       }

       public void setInterval(Integer interval) {
           this.interval = interval;
       }

       public String getFirstDayOfWeek() {
            return firstDayOfWeek;
        }

        public void setFirstDayOfWeek(String firstDayOfWeek) {
            this.firstDayOfWeek = firstDayOfWeek;
        }
    }

    public static class Range {

        private String type;

        private String endDate;

        private String numberOfOccurrences;

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public String getEndDate() {
            return endDate;
        }

        public void setEndDate(String endDate) {
            this.endDate = endDate;
        }

        public String getNumberOfOccurrences() {
            return numberOfOccurrences;
        }

        public void setNumberOfOccurrences(String numberOfOccurrences) {
            this.numberOfOccurrences = numberOfOccurrences;
        }
    }
}