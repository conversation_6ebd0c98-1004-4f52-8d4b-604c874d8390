package com.cyberscraft.uep.acm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@ApiModel(value = "QueryAppLinksDto",
        description = "连接器列表请求信息")
public class QueryAppConnectorDto implements Serializable {

    private static final long serialVersionUID = 3557920914407151042L;

    @ApiModelProperty(value = "页码", required = true, dataType = "Integer", example = "1")
    Integer page;

    @ApiModelProperty(value = "分页大小", required = true, dataType = "Integer", example = "10")
    private Integer size;

    @ApiModelProperty(value = "是否为触发事件", required = false, dataType = "Boolean")
    private Boolean trigger;

    @ApiModelProperty(value = "连接器名称", required = false, dataType = "String", example = "testLink")
    private String name;

    @ApiModelProperty(value = "应用创建类型：BuiltIn(内置应用)，Market(连接市场)，Custom(自建应用)", required = true)
    private String buildType;

    @ApiModelProperty(value = "应用部署类型：SaasApp(SAAS)，PrivateApp(私有化)，MixApp(混合部署)", required = true)
    private String deploymentType;

    @ApiModelProperty(value = "应用领域分类：LOGIC_COMPONENT(逻辑组件), AI(AI), DATA_PROTOCOL(数据/协议)...详情参看AppTypeEnum")
    private String appDomainCategory;

    @ApiModelProperty(value = "版本类型", dataType = "String")
    private String verType;


    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    public Boolean getTrigger() {
        return trigger;
    }

    public void setTrigger(Boolean trigger) {
        this.trigger = trigger;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getBuildType() {
        return buildType;
    }

    public void setBuildType(String buildType) {
        this.buildType = buildType;
    }

    public String getDeploymentType() {
        return deploymentType;
    }

    public void setDeploymentType(String deploymentType) {
        this.deploymentType = deploymentType;
    }

    public String getAppDomainCategory() {
        return appDomainCategory;
    }

    public void setAppDomainCategory(String appDomainCategory) {
        this.appDomainCategory = appDomainCategory;
    }

    public String getVerType() {
        return verType;
    }

    public void setVerType(String verType) {
        this.verType = verType;
    }
}
