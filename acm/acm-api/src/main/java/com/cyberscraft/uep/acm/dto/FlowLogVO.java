package com.cyberscraft.uep.acm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Author：xhquan
 * @Project：uep-services
 * @since：2023/7/13 14:12
 */
@ApiModel(value = "FlowLogVO", description = "连接流日志信息")
public class FlowLogVO implements Serializable {

    private static final long serialVersionUID = 3557920914407901042L;

    @ApiModelProperty(value = "链接流日志唯一ID", hidden = true)
    private Long id;

    @ApiModelProperty(value = "连接流Id", dataType = "String", example = "testFlow")
    private String flowId;

    @ApiModelProperty(value = "连接流名称", dataType = "String", example = "testFlow")
    private String flowName;

    @ApiModelProperty(value = "状态：0 失败、1 成功 、 2 警告", dataType = "int")
    private Integer status;

    @ApiModelProperty(value = "类型：0 定时计划任务、1 手动触发", dataType = "int")
    private Integer type;

    @ApiModelProperty(value = "开始时间", dataType = "date")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "结束时间", dataType = "date")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "错误信息", dataType = "string")
    private String errorMsg;

    @ApiModelProperty(value = "异常节点数量", dataType = "int")
    private int errorCount;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getFlowId() {
        return flowId;
    }

    public void setFlowId(String flowId) {
        this.flowId = flowId;
    }

    public String getFlowName() {
        return flowName;
    }

    public void setFlowName(String flowName) {
        this.flowName = flowName;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public LocalDateTime getStartTime() {
        return startTime;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public LocalDateTime getEndTime() {
        return endTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public int getErrorCount() {
        return errorCount;
    }

    public void setErrorCount(int errorCount) {
        this.errorCount = errorCount;
    }

}
