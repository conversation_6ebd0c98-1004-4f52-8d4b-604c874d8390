package com.cyberscraft.uep.acm.dto;

import com.cyberscraft.uep.acm.dto.config.NodeEnum;
import com.cyberscraft.uep.acm.dto.config.ProtocolEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Size;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 触发事件配置
 * </p>
 *
 * <AUTHOR>
 * @since 2022/10/18 4:29 下午
 */
@ApiModel(value = "FlowNodeDto",
        description = "连接流节点配置信息")
public class FlowNodeDto {

    @ApiModelProperty(value = "节点的唯一ID", hidden = true)
    private Long id;

    @ApiModelProperty(value = "节点的唯一ID", hidden = true)
    private Long nodeId;

    @ApiModelProperty(value = "节点的序号")
    private Integer seq;

    @ApiModelProperty(value = "父节点ID", required = false, dataType = "long")
    private Long parentId;

    @ApiModelProperty(value = "同级的前一个节点ID，在同级插入时，要求传该值，第一个节点时该值为null", required = false, dataType = "long")
    private Long prevId;

    @ApiModelProperty(value = "下一个节点ID", required = false, dataType = "long")
    private Long nextId;

    @ApiModelProperty(value = "节点类型", required = true, dataType = "String")
    private NodeEnum nodeType;

    @ApiModelProperty(value = "节点协议类型", hidden = true)
    private ProtocolEnum protocol;

    @ApiModelProperty(value = "描述", dataType = "String")
    private String description;

    @ApiModelProperty(value = "连接器ID", required = true, dataType = "long")
    private Long connectorId;

    @ApiModelProperty(value = "应用名称", required = true, dataType = "String")
    private String connectorName;

    @ApiModelProperty(value = "应用图标", dataType = "String")
    private String icon;

    @ApiModelProperty(value = "执行动作模型ID", required = true, dataType = "long")
    private Long actionId;

    @ApiModelProperty(value = "动作名称", required = true, dataType = "String")
    private String actionName;

    @ApiModelProperty(value = "动作key", required = true, dataType = "String")
    private String actionKey;

    @ApiModelProperty(value = "帐号实例ID", required = true, dataType = "long")
    private Long accountId;

    @ApiModelProperty(value = "帐号模型ID", required = false, dataType = "long")
    private Long accountSchemaId;

    @ApiModelProperty(value = "连接流ID", required = true, dataType = "long")
    private Long flowId;

    @ApiModelProperty(value = "连接流名称", required = false, dataType = "String")
    private String flowName;

    @ApiModelProperty(value = "调试状态，参见RunStatus类", required = true, dataType = "String")
    private String status;

    @ApiModelProperty(value = "参数属性", required = true, dataType = "domain")
    private Map<String, Object> properties;

    @ApiModelProperty(value = "输入参数", required = true, dataType = "String")
    private String input;

    @ApiModelProperty(value = "输出参数", required = true, dataType = "String")
    private String output;

    @ApiModelProperty(value = "输出样例", required = false, dataType = "String")
    private String sampleOutput;

    @ApiModelProperty(value = "节点结构", required = false, dataType = "String")
    private String nodeStruct;

    @ApiModelProperty(value = "上一次执行失败的错误描述", dataType = "String")
    private String errorMsg;

    @ApiModelProperty(value = "上一次运行开始时间", dataType = "long")
    private Long runStartTime;

    @ApiModelProperty(value = "上一次运行结束时间", dataType = "long")
    private Long runEndTime;

    @ApiModelProperty(value = "创建时间", dataType = "long")
    private Long createTime;

    @ApiModelProperty(value = "创建时间", dataType = "long")
    private Long updateTime;

    @ApiModelProperty(value = "子节点", dataType = "domain")
    private List<FlowNodeDto> children;

    @ApiModelProperty(value = "网络代理ID", dataType = "long")
    private Long proxyId;

    @ApiModelProperty(value = "节点备注", dataType = "String")
    @Size(max = 1000, message = "备注长度不能超过1000个字符")
    private String note;

    @ApiModelProperty(value = "启停用，0启用，1停用", dataType = "String")
    private String closed;

    @ApiModelProperty(value = "版本", dataType = "String")
    private String verType;

    @ApiModelProperty(value = "错误处理", dataType = "domain")
    private ErrorHandlerDto errorHandler;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getNodeId() {
        return nodeId;
    }

    public void setNodeId(Long nodeId) {
        this.nodeId = nodeId;
    }

    public Integer getSeq() {
        return seq;
    }

    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    public NodeEnum getNodeType() {
        return nodeType;
    }

    public void setNodeType(NodeEnum nodeType) {
        this.nodeType = nodeType;
    }

    public ProtocolEnum getProtocol() {
        return protocol;
    }

    public void setProtocol(ProtocolEnum protocol) {
        this.protocol = protocol;
    }

    public String getActionKey() {
        return actionKey;
    }

    public void setActionKey(String actionKey) {
        this.actionKey = actionKey;
    }

    public String getActionName() {
        return actionName;
    }

    public void setActionName(String actionName) {
        this.actionName = actionName;
    }

    public String getConnectorName() {
        return connectorName;
    }

    public void setConnectorName(String connectorName) {
        this.connectorName = connectorName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public Long getAccountSchemaId() {
        return accountSchemaId;
    }

    public void setAccountSchemaId(Long accountSchemaId) {
        this.accountSchemaId = accountSchemaId;
    }

    public Long getFlowId() {
        return flowId;
    }

    public void setFlowId(Long flowId) {
        this.flowId = flowId;
    }

    public String getFlowName() {
        return flowName;
    }

    public void setFlowName(String flowName) {
        this.flowName = flowName;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Long getPrevId() {
        return prevId;
    }

    public void setPrevId(Long prevId) {
        this.prevId = prevId;
    }

    public Long getNextId() {
        return nextId;
    }

    public void setNextId(Long nextId) {
        this.nextId = nextId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getConnectorId() {
        return connectorId;
    }

    public void setConnectorId(Long connectorId) {
        this.connectorId = connectorId;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public Long getActionId() {
        return actionId;
    }

    public void setActionId(Long actionId) {
        this.actionId = actionId;
    }

    public Map<String, Object> getProperties() {
        return properties;
    }

    public void setProperties(Map<String, Object> properties) {
        this.properties = properties;
    }

    public List<FlowNodeDto> getChildren() {
        return children;
    }

    public void setChildren(List<FlowNodeDto> children) {
        this.children = children;
    }

    public String getInput() {
        return input;
    }

    public void setInput(String input) {
        this.input = input;
    }

    public String getOutput() {
        return output;
    }

    public void setOutput(String output) {
        this.output = output;
    }

    public String getSampleOutput() {
        return sampleOutput;
    }

    public void setSampleOutput(String sampleOutput) {
        this.sampleOutput = sampleOutput;
    }

    public String getNodeStruct() {
        return nodeStruct;
    }

    public void setNodeStruct(String nodeStruct) {
        this.nodeStruct = nodeStruct;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public Long getRunStartTime() {
        return runStartTime;
    }

    public void setRunStartTime(Long runStartTime) {
        this.runStartTime = runStartTime;
    }

    public Long getRunEndTime() {
        return runEndTime;
    }

    public void setRunEndTime(Long runEndTime) {
        this.runEndTime = runEndTime;
    }

    public Long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Long createTime) {
        this.createTime = createTime;
    }

    public Long getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    public Long getProxyId() {
        return proxyId;
    }

    public void setProxyId(Long proxyId) {
        this.proxyId = proxyId;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public String getClosed() {
        return closed;
    }

    public void setClosed(String closed) {
        this.closed = closed;
    }

    public String getVerType() {
        return verType;
    }

    public void setVerType(String verType) {
        this.verType = verType;
    }

    public ErrorHandlerDto getErrorHandler() {
        return errorHandler;
    }

    public void setErrorHandler(ErrorHandlerDto errorHandler) {
        this.errorHandler = errorHandler;
    }
}
