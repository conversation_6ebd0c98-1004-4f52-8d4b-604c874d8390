package com.cyberscraft.uep.acm.dto;

import com.cyberscraft.uep.acm.dto.config.AppActionCategory;
import com.cyberscraft.uep.acm.dto.config.BuildTypeEnum;
import com.cyberscraft.uep.acm.dto.config.DeploymentStrategyEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * APP连接器模型参数信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024/1/3 3:52 下午
 */
@ApiModel(value = "AppConnectorSchema",
        description = "连接器模型参数信息")
public class AppConnectorSchema {

    @ApiModelProperty(value = "连接器的唯一ID", hidden = true)
    private Long id;

    @ApiModelProperty(value = "连接器名称", required = true, dataType = "String", example = "testLink")
    @NotBlank(message = "could not be empty")
    private String name;

    @ApiModelProperty(value = "功能描述", dataType = "String")
    private String description;

    @ApiModelProperty(value = "应用渠道：BuiltIn(内置应用)，Market(连接市场)，Custom(自建应用)", required = true)
    private BuildTypeEnum buildType;

    @ApiModelProperty(value = "应用领域分类：LOGIC_COMPONENT(逻辑组件), AI(AI/大模型), DATA_PROTOCOL(数据/协议)...详情参看AppTypeEnum", required = true)
    private Set<String> appDomainCategory;

    @ApiModelProperty(value = "应用动作分类")
    private List<AppActionCategory> appActionCategory;

    @ApiModelProperty(value = "部署模式：SaasApp(SAAS)，PrivateApp(私有化)，MixApp(混合部署)", required = true)
    private DeploymentStrategyEnum deploymentType;

    @ApiModelProperty(value = "应用包名", hidden = true, dataType = "String")
    private String appPackage;

    @ApiModelProperty(value = "版本名称", required = true, dataType = "String")
    private String verName;

    @ApiModelProperty(value = "版本号", required = true, dataType = "Integer")
    private Integer verNumber;

    @ApiModelProperty(value = "版本类型", required = true, dataType = "String")
    private String verType;

    @ApiModelProperty(value = "图标", dataType = "String")
    private String icon;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public BuildTypeEnum getBuildType() {
        return buildType;
    }

    public void setBuildType(BuildTypeEnum buildType) {
        this.buildType = buildType;
    }

    public Set<String> getAppDomainCategory() {
        return appDomainCategory;
    }

    public void setAppDomainCategory(Set<String> appDomainCategory) {
        this.appDomainCategory = appDomainCategory;
    }

    public List<AppActionCategory> getAppActionCategory() {
        return appActionCategory;
    }

    public void setAppActionCategory(List<AppActionCategory> appActionCategory) {
        this.appActionCategory = appActionCategory;
    }

    public DeploymentStrategyEnum getDeploymentType() {
        return deploymentType;
    }

    public void setDeploymentType(DeploymentStrategyEnum deploymentType) {
        this.deploymentType = deploymentType;
    }

    public String getAppPackage() {
        return appPackage;
    }

    public void setAppPackage(String appPackage) {
        this.appPackage = appPackage;
    }

    public String getVerName() {
        return verName;
    }

    public void setVerName(String verName) {
        this.verName = verName;
    }

    public Integer getVerNumber() {
        return verNumber;
    }

    public void setVerNumber(Integer verNumber) {
        this.verNumber = verNumber;
    }

    public String getVerType() {
        return verType;
    }

    public void setVerType(String verType) {
        this.verType = verType;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }
}