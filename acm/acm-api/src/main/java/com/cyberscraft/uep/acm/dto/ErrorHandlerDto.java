package com.cyberscraft.uep.acm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/11 4:19 下午
 */
@ApiModel(value = "ErrorHandlerDto",
        description = "连接流节点运行错误处理配置")
public class ErrorHandlerDto {

    /**
     * 节点执行失败时是否重试，重试成功后不算失败
     */
    @ApiModelProperty(value = "节点执行失败时是否重试，重试成功后不算失败")
    private boolean retry = false;

    /**
     * 重试次数，范围为1-10次
     */
    @ApiModelProperty(value = "重试次数，范围为1-10次")
    private int retryTimes = 3;

    /**
     * 重试间隔时长，范围为1-60秒
     */
    @ApiModelProperty(value = "重试间隔时长，范围为1-60秒")
    private int retryInterval = 5;

    /**
     * 退避系数，范围为1-5
     */
    @ApiModelProperty(value = "退避系数，范围为1-5")
    private float stepFactor = 1.5f;

    /**
     * 最大间隔时长，范围为1-300秒
     */
    @ApiModelProperty(value = "最大间隔时长，范围为1-300秒")
    private int maxRetryInterval = 60;

    /**
     * 节点执行失败（包括重试）后处理方式：1、终止连接流；2、忽略失败并继续执行；
     */
    @ApiModelProperty(value = "节点执行失败（包括重试）后处理方式：1、终止连接流；2、忽略失败并继续执行")
    private int processType = 1;

    /**
     * 节点失败后告警方式：0、不告警；1、webhook方式
     */
    @ApiModelProperty(value = "节点失败后告警方式：0、不告警；1、webhook方式")
    private int warnType = 0;

    /**
     * webhook告警通知的url
     */
    @ApiModelProperty(value = "webhook告警通知的url")
    private String notifyUrl;

    @ApiModelProperty(value = "是否应用到全部动作节点")
    private Boolean applyAll = false;

    public boolean isRetry() {
        return retry;
    }

    public void setRetry(boolean retry) {
        this.retry = retry;
    }

    public int getRetryTimes() {
        return retryTimes;
    }

    public void setRetryTimes(int retryTimes) {
        this.retryTimes = retryTimes;
    }

    public int getRetryInterval() {
        return retryInterval;
    }

    public void setRetryInterval(int retryInterval) {
        this.retryInterval = retryInterval;
    }

    public float getStepFactor() {
        return stepFactor;
    }

    public void setStepFactor(float stepFactor) {
        this.stepFactor = stepFactor;
    }

    public int getMaxRetryInterval() {
        return maxRetryInterval;
    }

    public void setMaxRetryInterval(int maxRetryInterval) {
        this.maxRetryInterval = maxRetryInterval;
    }

    public int getProcessType() {
        return processType;
    }

    public void setProcessType(int processType) {
        this.processType = processType;
    }

    public int getWarnType() {
        return warnType;
    }

    public void setWarnType(int warnType) {
        this.warnType = warnType;
    }

    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }

    public Boolean getApplyAll() {
        return applyAll;
    }

    public void setApplyAll(Boolean applyAll) {
        this.applyAll = applyAll;
    }
}
