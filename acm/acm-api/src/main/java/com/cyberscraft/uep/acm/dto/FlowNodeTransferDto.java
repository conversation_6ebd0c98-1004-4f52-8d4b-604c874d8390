package com.cyberscraft.uep.acm.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/8/6 14:32
 * @Version 1.0
 * @Description 导入或导出连接流节点请求参数
 */
@ApiModel(value = "FlowNodeTransferDto", description = "导出亮连接流节点信息")
public class FlowNodeTransferDto implements Serializable {

    @ApiModelProperty(value = "连接流ID", required = true, dataType = "long")
    private Long flowId;

    @ApiModelProperty(value = "同级的前一个节点ID，在同级插入时，要求传该值，第一个节点时该值为null", required = false, dataType = "long")
    private Long prevId;

    @ApiModelProperty(value = "开始节点ID", required = false, dataType = "long")
    private Long startNodeId;

    @ApiModelProperty(value = "结束节点ID", required = false, dataType = "long")
    private Long endNodeId;

    @ApiModelProperty(value = "父节点ID", required = false, dataType = "long")
    private Long parentId;

    @ApiModelProperty(value = "节点列表", required = true)
    private List<FlowNodeDto> nodes;

    @ApiModelProperty(value = "上传的文件流", example = "1")
    private MultipartFile file;

    public Long getFlowId() {
        return flowId;
    }

    public void setFlowId(Long flowId) {
        this.flowId = flowId;
    }

    public Long getPrevId() {
        return prevId;
    }

    public void setPrevId(Long prevId) {
        this.prevId = prevId;
    }

    public Long getStartNodeId() {
        return startNodeId;
    }

    public void setStartNodeId(Long startNodeId) {
        this.startNodeId = startNodeId;
    }

    public Long getEndNodeId() {
        return endNodeId;
    }

    public void setEndNodeId(Long endNodeId) {
        this.endNodeId = endNodeId;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public List<FlowNodeDto> getNodes() {
        return nodes;
    }

    public void setNodes(List<FlowNodeDto> nodes) {
        this.nodes = nodes;
    }

    public MultipartFile getFile() {
        return file;
    }

    public void setFile(MultipartFile file) {
        this.file = file;
    }
}
