package com.cyberscraft.uep.acm.flow.action.handler;

import com.cyberscraft.uep.common.bean.Result;
import com.cyberscraft.uep.common.domain.Param;
import com.cyberscraft.uep.common.enums.ExceptionCodeEnum;
import com.cyberscraft.uep.dataconnect.constant.Constants;
import com.cyberscraft.uep.dataconnect.context.NodeRunContext;
import com.cyberscraft.uep.dataconnect.domain.ProtocolType;
import com.cyberscraft.uep.dataconnect.domain.node.Node;
import com.cyberscraft.uep.dataconnect.domain.node.NodeOutput;
import com.cyberscraft.uep.dataconnect.domain.node.NodeParam;
import com.cyberscraft.uep.dataconnect.exception.FlowException;
import com.cyberscraft.uep.dataconnect.handler.IActionHandler;
import com.cyberscraft.uep.dataconnect.tools.ContextHolder;
import com.cyberscraft.uep.dataconnect.tools.NodeContextHolder;
import com.cyberscraft.uep.iam.api.inner.InnerUserOrgApi;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Description 通讯录同步触发器
 * <AUTHOR>
 * @Date 2025/2/7 15:32
 */
@Service
public class UserPushTriggerHandler implements IActionHandler {

    @Resource
    private InnerUserOrgApi innerUserOrgApi;


    @Override
    public boolean isSupported(ProtocolType protocolType) {
        return ProtocolType.USER_PUSH == protocolType;
    }

    @Override
    public void run(String taskId, Node node) {
        NodeRunContext nodeRunContext = NodeContextHolder.get(node.getNodeId());
        Param inputParam = (Param) nodeRunContext.getInputValue();

        String connectorId = node.getFlowId();
        Result<Integer> result = innerUserOrgApi.getPushUserAndOrgCount(Long.valueOf(connectorId), Long.valueOf(taskId));
        if (!ExceptionCodeEnum.SUCCESS.getCode().equals(result.getCode())) {
            //此处无需抛出异常，因为iam失败时已经维护了task的状态
            throw new FlowException(result.getMessage());
        } else {
            ContextHolder.put(Constants.PUSH_TOTAL, result.getData());
        }

        Param input = node.getInput();
        if (input instanceof NodeParam) {
            node.setOutput(new NodeOutput(((NodeParam) input).getParam(), result.getData()));
        } else {
            node.setOutput(new NodeOutput(inputParam.toParam()));
        }
    }

    @Override
    public void skipRun(String taskId, Node node) {
        String message = "连接流运行中断";
        innerUserOrgApi.failPushTask(Long.valueOf(taskId), message);

        throw new FlowException(message);
    }
}
