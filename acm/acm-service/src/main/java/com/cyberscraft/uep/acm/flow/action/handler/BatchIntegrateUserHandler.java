package com.cyberscraft.uep.acm.flow.action.handler;

import com.cyberscraft.uep.acm.constants.ThreadPoolNameConstant;
import com.cyberscraft.uep.acm.flow.action.param.SyncCounter;
import com.cyberscraft.uep.common.util.CollectionUtils;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.dataconnect.constant.Constants;
import com.cyberscraft.uep.dataconnect.context.NodeRunContext;
import com.cyberscraft.uep.dataconnect.domain.ErrorExecEnum;
import com.cyberscraft.uep.dataconnect.domain.ProtocolType;
import com.cyberscraft.uep.dataconnect.domain.loop.LoopOutput;
import com.cyberscraft.uep.dataconnect.domain.loop.LoopType;
import com.cyberscraft.uep.dataconnect.domain.node.Node;
import com.cyberscraft.uep.dataconnect.domain.node.NodeOutput;
import com.cyberscraft.uep.dataconnect.domain.node.RunStatus;
import com.cyberscraft.uep.dataconnect.exception.FlowException;
import com.cyberscraft.uep.dataconnect.handler.IActionHandler;
import com.cyberscraft.uep.dataconnect.service.IFlowLogService;
import com.cyberscraft.uep.dataconnect.service.IFlowService;
import com.cyberscraft.uep.dataconnect.tools.ContextHolder;
import com.cyberscraft.uep.dataconnect.tools.NodeContextHolder;
import com.cyberscraft.uep.dataconnect.tools.NodeUtils;
import com.cyberscraft.uep.iam.api.inner.InnerUserOrgApi;
import com.cyberscraft.uep.iam.dto.domain.SyncProgress;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/12/2 10:54 上午
 */
@Service
public class BatchIntegrateUserHandler implements IActionHandler {
    @Autowired
    private IFlowService flowService;

    @Autowired
    private IFlowLogService flowLogService;

    @Resource
    private InnerUserOrgApi innerUserOrgApi;

    @Resource(name = ThreadPoolNameConstant.PARALLEL_POOL_NAME)
    private ExecutorService parallelPoolExecutor;

    @Override
    public boolean isSupported(ProtocolType protocolType) {
        return ProtocolType.LOOP_EXT_USER == protocolType;
    }

    private final static Logger logger = LoggerFactory.getLogger(BatchIntegrateUserHandler.class);

    @Override
    public void run(String taskId, Node node) {
        NodeRunContext nodeRunContext = NodeContextHolder.get(node.getNodeId());
        Map<String,Object> param = (Map) nodeRunContext.getInputValue();
        LocalDateTime startTime = LocalDateTime.now();
        boolean logSaved = true;
        try {
            final Object items = param.get("ext_user");

            final List<Object> itemList = new ArrayList<>();
            if (items instanceof Collection) {
                itemList.addAll((Collection)items);
            } else if (items != null) {
                itemList.add(items);
            }

            Object currObject = null;
            int index = 0;

            Boolean isDebug = ContextHolder.get(Constants.IS_DEBUG) != null && (Boolean) ContextHolder.get(Constants.IS_DEBUG);

            if (CollectionUtils.isEmpty(itemList)) {
                //更新context
                node.setOutput(new NodeOutput(new LoopOutput(index, currObject, LoopType.INNER)));
                ContextHolder.put("N" + node.getNodeId(), node.getOutput().getValue());

                flowLogService.saveNodeRunLog(taskId, node, RunStatus.SUCCESS, Constants.SUCCESS_MSG, startTime, LocalDateTime.now(), logSaved);

                if (isDebug) {
                    NodeUtils.debugForTrue(node, flowLogService);
                }
                return;
            }

            Integer total = (Integer) ContextHolder.get(Constants.SYNC_PREDICT_TOTAL);
            ErrorExecEnum errorExec = ErrorExecEnum.END;
            String tenantCode = TenantHolder.getTenantCode();
            String run_type = (String) param.get("run_type");
            List<Node> children = node.getChildren();

            SyncProgress syncProgress = new SyncProgress(0, 95, itemList.size(), total);
            SyncCounter syncCounter = new SyncCounter();
            ContextHolder.put(Constants.CURRENT_SYNC_COUNTER, syncCounter);

            int latchNum = itemList.size();
            if (isDebug) latchNum = 1;
            CountDownLatch latch = new CountDownLatch(latchNum);

            for (Object item : itemList) {
                startTime = LocalDateTime.now();
                currObject = item;
                //更新context
                node.setOutput(new NodeOutput(new LoopOutput(index, currObject, LoopType.INNER)));
                ContextHolder.put("N" + node.getNodeId(), node.getOutput().getValue());
                flowLogService.saveNodeRunLog(taskId, node, RunStatus.SUCCESS, Constants.SUCCESS_MSG, startTime, LocalDateTime.now(), logSaved);
                logSaved = false;

                if ("并行".equals(run_type)) {
//                    logger.warn("loop user:{}", JsonUtil.obj2Str(ContextHolder.getContext()));
                    Map contextClone = (Map) ((HashMap) ContextHolder.getContext()).clone();
                    Map nodeContextClone = (Map) ((HashMap) NodeContextHolder.getContext()).clone();
                    parallelPoolExecutor.submit(() -> {
                        try {
                            TenantHolder.setTenantCode(tenantCode);
                            //执行全部子节点
                            RunStatus runStatus = NodeUtils.successExecChildren(taskId, children, contextClone, flowService, nodeContextClone, node);
                            if (runStatus == RunStatus.FAILED) {
                                node.setRunStatus(RunStatus.FAILED);
                            } else if (node.getRunStatus() != RunStatus.FAILED && runStatus == RunStatus.WARNING) {
                                node.setRunStatus(RunStatus.WARNING);
                            }
                        } catch (Exception e) {
                            node.setRunStatus(RunStatus.WARNING);
                        } finally {
                            latch.countDown();
                            ContextHolder.clean();
                            NodeContextHolder.clean();
                        }
                    });
                    index++;
                } else {
                    latch.countDown();
                    RunStatus childStatus = NodeUtils.successExecChildren(taskId, children, ContextHolder.getContext(), flowService, NodeContextHolder.getContext(), node);
                    if (NodeUtils.handleLoopStatus(node, childStatus, errorExec)) {
                        break;
                    }
                    index++;
                }

                if (isDebug) {
                    break;
                }
            }

            if ("并行".equals(run_type)) {
                latch.await();
            }
            if (isDebug) return;

            syncProgress.setSyncStats(syncCounter.toSyncUserStats());
            logger.info("sync progress:{}", JsonUtil.obj2Str(syncProgress));
            innerUserOrgApi.updateSyncTask(Long.valueOf(taskId), syncProgress);
        } catch (Exception e) {
            innerUserOrgApi.failSyncTask(Long.valueOf(taskId), e.getMessage());
            throw new FlowException(e.getMessage());
        }
    }
}
