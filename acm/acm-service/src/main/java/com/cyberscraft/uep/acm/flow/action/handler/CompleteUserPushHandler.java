package com.cyberscraft.uep.acm.flow.action.handler;

import com.cyberscraft.uep.common.bean.Result;
import com.cyberscraft.uep.dataconnect.domain.ProtocolType;
import com.cyberscraft.uep.dataconnect.domain.node.Node;
import com.cyberscraft.uep.dataconnect.domain.node.NodeOutput;
import com.cyberscraft.uep.dataconnect.handler.IActionHandler;
import com.cyberscraft.uep.iam.api.inner.InnerUserOrgApi;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @Description 完成通讯录同步
 * <AUTHOR>
 * @Date 2025/2/8 18:33
 */
@Service
public class CompleteUserPushHandler implements IActionHandler {

    @Resource
    private InnerUserOrgApi innerUserOrgApi;

    @Override
    public boolean isSupported(ProtocolType protocolType) {
        return ProtocolType.COMPLETE_USER_PUSH == protocolType;
    }

    @Override
    public void run(String taskId, Node node) {
        Result<Void> result = innerUserOrgApi.completePushTask(Long.valueOf(taskId));
        node.setOutput(new NodeOutput(result));
    }
}
