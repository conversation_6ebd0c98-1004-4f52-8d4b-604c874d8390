package com.cyberscraft.uep.acm.configuration;

import com.cyberscraft.uep.acm.constants.ComConstants;
import com.cyberscraft.uep.common.constant.SysConstant;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import com.cyberscraft.uep.starter.dds.bean.GeneralAttributes;
import com.cyberscraft.uep.starter.dds.properties.DDSConfigProperties;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpHeaders;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Collection;
import java.util.Enumeration;
import java.util.Map;

/***
 *
 * @date 2021-08-28
 * <AUTHOR>
 ***/
@Configuration
//@Order(Ordered.HIGHEST_PRECEDENCE + 10001)
public class FeginClientConfiguration {

    private static final String HEADER_AUTHORIZATION = HttpHeaders.AUTHORIZATION;

    /***
     *
     */
    private final static Logger LOG = LoggerFactory.getLogger(FeginClientConfiguration.class);


    @LoadBalanced
    @Bean
    public RestTemplate getRestTemplate() {
        return new RestTemplate();
    }

    @Resource
    private DDSConfigProperties properties;

    @Bean
    public RequestInterceptor headerInterceptor() {
        return (requestTemplate) -> {

            addHeader(requestTemplate);

            //设置tcode,
            GeneralAttributes att = properties.getGeneral();
            String headerName = att != null ? att.getTenantHeaderName() : null;
            if (StringUtils.isBlank(headerName)) {
                headerName = ComConstants.DEFAULT_TENANT_HEADEARNAME;
            }
            String tenantCode = TenantHolder.getTenantCode();
            if (StringUtils.isNotBlank(tenantCode)) {
                requestTemplate.removeHeader(headerName);
                requestTemplate.header(headerName, tenantCode);
            }

            if (LOG.isInfoEnabled()) {
                LOG.info("设置{}值{}到请求的header中", headerName, tenantCode);
            }

            //增加日志traceID相关信息
            String traceId = MDC.get(SysConstant.LOG_TRACE_ID);
            if (StringUtils.isNotEmpty(traceId)) {
                requestTemplate.header(SysConstant.LOG_TRACE_ID, traceId);
            }
            String rpcId = MDC.get(SysConstant.LOG_RPC_ID);
            if (StringUtils.isNotEmpty(rpcId)) {
                requestTemplate.header(SysConstant.LOG_RPC_ID, rpcId);
            }

        };
    }

    private void addHeader(RequestTemplate requestTemplate) {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder
                .getRequestAttributes();
        if(attributes == null){
            return;
        }
        HttpServletRequest request = attributes.getRequest();
        if(request == null){
            return;
        }

        Enumeration<String> headerNames = request.getHeaderNames();

        if(headerNames == null){
            return;
        }

        Map<String, Collection<String>> headers = requestTemplate.headers();

        while (headerNames.hasMoreElements()) {
            String name = headerNames.nextElement();
            if (!headers.containsKey(name)) {
                String values = request.getHeader(name);
                requestTemplate.header(name, values);
            }
        }

    }
}
