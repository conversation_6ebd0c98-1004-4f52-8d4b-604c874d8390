package com.cyberscraft.uep.acm.filters;

import com.cyberscraft.uep.common.config.ServerConfig;
import com.cyberscraft.uep.starter.dds.properties.DDSConfigProperties;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.LocaleResolver;
import org.springframework.web.servlet.i18n.SessionLocaleResolver;

import javax.annotation.Resource;
import java.util.Locale;

@Configuration
public class FilterConfig {

    @Resource
    DDSConfigProperties ddsConfigProperties;

    @Resource
    private ServerConfig serverConfig;

    @Bean
    public LocaleResolver localeResolver() {
        SessionLocaleResolver sessionLocaleResolver = new SessionLocaleResolver();
        sessionLocaleResolver.setDefaultLocale(Locale.US);
        return sessionLocaleResolver;
    }
    
    @Bean
    public FilterRegistrationBean correlationHeaderFilter() {
        FilterRegistrationBean filterRegBean = new FilterRegistrationBean();
        filterRegBean.setFilter(new SetTenantCodeFilter(serverConfig.getSingleTenant(), serverConfig.getSingleTenantId(), ddsConfigProperties.getGeneral().getTenantHeaderName()));
        filterRegBean.addUrlPatterns("/*");
        filterRegBean.setOrder(FilterRegistrationBean.HIGHEST_PRECEDENCE- 104);
        return filterRegBean;
    }
}
