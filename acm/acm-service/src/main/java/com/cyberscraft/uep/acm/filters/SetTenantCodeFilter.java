package com.cyberscraft.uep.acm.filters;

import com.cyberscraft.uep.starter.dds.TenantHolder;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.core.OAuth2AuthenticatedPrincipal;
import org.springframework.web.filter.GenericFilterBean;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

public class SetTenantCodeFilter extends GenericFilterBean {

    private final static Logger logger = LoggerFactory.getLogger(SetTenantCodeFilter.class);

    private String tenantHeaderName;

    private Boolean isSingleTenant;

    private String singleTenantId;

    public SetTenantCodeFilter(Boolean isSingleTenant, String singleTenantId, String tenantHeaderName) {
        this.isSingleTenant = isSingleTenant;
        this.singleTenantId = singleTenantId;
        this.tenantHeaderName = tenantHeaderName;
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        String tenantCodeValue = parseTenantFromAuthentication();
        logger.info("request tenant code is:{}", tenantCodeValue);
        if (StringUtils.isNotEmpty(tenantCodeValue)) {
            request.setAttribute(tenantHeaderName, tenantCodeValue);
            TenantHolder.setTenantCode(tenantCodeValue);
        } else {
            tenantCodeValue = getTenantCode((HttpServletRequest) request);
            if (StringUtils.isNotEmpty(tenantCodeValue)) {
                request.setAttribute(tenantHeaderName, tenantCodeValue);
                TenantHolder.setTenantCode(tenantCodeValue);
            }
        }
        logger.info("this tenant is {}", tenantCodeValue);

        try {
            chain.doFilter(request, response);
        } finally {
            TenantHolder.remove();
        }
    }

    private String getTenantCode(HttpServletRequest httpServletRequest) {
        logger.debug("request class:{}", httpServletRequest.getClass().getName());

        String host = httpServletRequest.getHeader("host");
        logger.debug("Header host: " + host);

        logger.debug("getTeanantCode isSingleTenant:{},tenantCode:{},host:{}", isSingleTenant, singleTenantId, httpServletRequest.getServerName());

        String tenantCodeValue = httpServletRequest.getHeader(tenantHeaderName);
        if (StringUtils.isBlank(tenantCodeValue)) {
            tenantCodeValue = httpServletRequest.getParameter(tenantHeaderName);
        }
        logger.info("request tenantId:{},{}", tenantCodeValue, httpServletRequest.getRequestURI());

        if (StringUtils.isBlank(tenantCodeValue)) {
            if (isSingleTenant) {
                return singleTenantId;
            }
            String serverName = httpServletRequest.getServerName();
            if (!serverName.matches("\\d+(\\.\\d+){3}")) {
                String[] split = serverName.split("\\.");
                tenantCodeValue = split[0];
            }
        }
        return tenantCodeValue;
    }

    private String parseTenantFromAuthentication() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null && !authentication.isAuthenticated()) {
            return null;
        }
        if (authentication.getPrincipal() == null) {
            return null;
        }
        Object principal = authentication.getPrincipal();
        if (principal instanceof OAuth2AuthenticatedPrincipal) {
            OAuth2AuthenticatedPrincipal oauth2Principal = (OAuth2AuthenticatedPrincipal) authentication.getPrincipal();
            return oauth2Principal.getAttribute("tenant_id");
        }
        return null;
    }
}
