package com.cyberscraft.uep.acm.flow.action.handler;

import com.cyberscraft.uep.common.bean.Result;
import com.cyberscraft.uep.dataconnect.domain.ProtocolType;
import com.cyberscraft.uep.dataconnect.domain.node.Node;
import com.cyberscraft.uep.dataconnect.domain.node.NodeOutput;
import com.cyberscraft.uep.dataconnect.handler.IActionHandler;
import com.cyberscraft.uep.iam.api.inner.InnerUserOrgApi;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/10/11 11:58 上午
 */
@Service
public class CompleteUserSyncHandler implements IActionHandler {

    @Resource
    private InnerUserOrgApi innerUserOrgApi;

    @Override
    public boolean isSupported(ProtocolType protocolType) {
        return ProtocolType.COMPLETE_USER_SYNC == protocolType;
    }

    @Override
    public void run(String taskId, Node node) {
        Result<Void> result = innerUserOrgApi.completeSyncTask(Long.valueOf(taskId));
        node.setOutput(new NodeOutput(result));

//            SyncProgress syncProgress = new SyncProgress(95, 100, 1,1);
//            innerUserOrgApi.updateSyncTask(Long.valueOf(taskId), syncProgress);

    }
}
