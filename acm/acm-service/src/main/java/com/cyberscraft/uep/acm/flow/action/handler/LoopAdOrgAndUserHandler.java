package com.cyberscraft.uep.acm.flow.action.handler;

import com.cyberscraft.uep.account.client.service.IAuthenticateService;
import com.cyberscraft.uep.common.domain.auth.AccountParam;
import com.cyberscraft.uep.common.domain.auth.LdapAccount;
import com.cyberscraft.uep.dataconnect.constant.Constants;
import com.cyberscraft.uep.dataconnect.context.NodeRunContext;
import com.cyberscraft.uep.dataconnect.domain.ErrorExecEnum;
import com.cyberscraft.uep.dataconnect.domain.ProtocolType;
import com.cyberscraft.uep.dataconnect.domain.loop.LoopOutput;
import com.cyberscraft.uep.dataconnect.domain.loop.LoopType;
import com.cyberscraft.uep.dataconnect.domain.node.Node;
import com.cyberscraft.uep.dataconnect.domain.node.NodeOutput;
import com.cyberscraft.uep.dataconnect.domain.node.RunStatus;
import com.cyberscraft.uep.dataconnect.exception.FlowException;
import com.cyberscraft.uep.dataconnect.handler.IActionHandler;
import com.cyberscraft.uep.dataconnect.service.IFlowLogService;
import com.cyberscraft.uep.dataconnect.service.IFlowService;
import com.cyberscraft.uep.dataconnect.tools.*;
import org.ldaptive.*;
import org.ldaptive.control.PagedResultsControl;
import org.ldaptive.control.ResponseControl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/1/20 7:08 下午
 */
@Service
public class LoopAdOrgAndUserHandler implements IActionHandler {
    @Autowired
    private IFlowService flowService;

    @Autowired
    private IFlowLogService flowLogService;

    private IAuthenticateService authenticateService;

    @Resource
    public void setAuthenticateService(IAuthenticateService authenticateService) {
        this.authenticateService = authenticateService;
    }

    @Override
    public boolean isSupported(ProtocolType protocolType) {
        return ProtocolType.LOOP_AD == protocolType;
    }

    @Override
    public void run(String taskId, Node node) {
        NodeRunContext nodeRunContext = NodeContextHolder.get(node.getNodeId());
        Map<String,Object> param = (Map) nodeRunContext.getInputValue();
        LocalDateTime startTime = LocalDateTime.now();
        boolean logSaved = true;
        Connection connection = null;
        try {
            AccountParam accountParam = node.getAccount();
            String base_dn = (String) param.get("base_dn");
            String dept_filter = (String) param.get("dept_filter");
            String user_filter = (String) param.get("user_filter");
            String output_org = (String) param.get("output_org");
            String output_user = (String) param.get("output_user");

            String dept_id_name = "objectGUID";
            String dept_pid_name = "parentObjectGUID";
            String user_dept_name = "department_ids";

            LdapAccount ldapAccount = (LdapAccount)authenticateService.getAccount(accountParam, null, null);
            connection = LdapActionUtil.getConnection(ldapAccount);

            SearchRequest searchRequest = new SearchRequest(base_dn, LdapActionUtil.createPresenceFilter("objectclass"));
            searchRequest.setSearchScope(org.ldaptive.SearchScope.OBJECT);
            searchRequest.setBinaryAttributes("objectGUID");

            LdapEntry entry = LdapActionUtil.querySingleEntry(connection, searchRequest);

            int index = 0;
            Boolean isDebug = ContextHolder.get(Constants.IS_DEBUG) != null && (Boolean) ContextHolder.get(Constants.IS_DEBUG);
            ErrorExecEnum errorExec = ErrorExecEnum.END;
            List<Node> children = node.getChildren();

            Map<String, Object> rootMap = new HashMap<>();
            rootMap.put("${root}$", true);

            for (LdapAttribute attribute : entry.getAttributes()) {
                if (attribute.getName().equals("objectGUID")) {
                    final String strGuid = LdapActionUtil.byte2String(attribute.getBinaryValue());
                    rootMap.put(attribute.getName(), strGuid);
                } else {
                    rootMap.put(attribute.getName(), attribute.getStringValue());
                }
            }
            rootMap.put("dn", entry.getDn());

            final Map<String, Object> currObject = new HashMap<>();
            currObject.put("dept", Arrays.asList(rootMap));
            currObject.put("user", Collections.EMPTY_LIST);
            //更新context
            node.setOutput(new NodeOutput(new LoopOutput(index, currObject, LoopType.INNER)));
            ContextHolder.put("N" + node.getNodeId(), node.getOutput().getValue());
            flowLogService.saveNodeRunLog(taskId, node, RunStatus.SUCCESS, Constants.SUCCESS_MSG, startTime, LocalDateTime.now(), logSaved);
            logSaved = false;

            //执行根节点输出
            RunStatus childStatus = NodeUtils.successExecChildren(taskId, children, ContextHolder.getContext(), flowService, NodeContextHolder.getContext(), node);
            if (NodeUtils.handleLoopStatus(node, childStatus, errorExec)) {
                return;
            }
            index++;

            LinkedList<Pair<String,String>> deptDnList = new LinkedList<>();
            deptDnList.add(new Pair(rootMap.get(dept_id_name), rootMap.get("dn")));

            while (deptDnList.size() > 0) {
                startTime = LocalDateTime.now();
                Pair<String, String> parentPair = deptDnList.removeFirst();
                final List<Map<String, Object>> childDeptList = searchSubDept(connection, dept_id_name, dept_pid_name, parentPair, dept_filter, deptDnList, "是".equals(output_org));
                final List<Map<String, Object>> childUserList = searchSubUser(connection, user_dept_name, parentPair, user_filter, rootMap.get(dept_id_name).toString(), "是".equals(output_org), "是".equals(output_user));

                currObject.put("dept", childDeptList);
                currObject.put("user", childUserList);

                //更新context
                node.setOutput(new NodeOutput(new LoopOutput(index, currObject, LoopType.INNER)));
                ContextHolder.put("N" + node.getNodeId(), node.getOutput().getValue());
                flowLogService.saveNodeRunLog(taskId, node, RunStatus.SUCCESS, Constants.SUCCESS_MSG, startTime, LocalDateTime.now(), logSaved);
                logSaved = false;

                //执行全部子节点
                childStatus = NodeUtils.successExecChildren(taskId, children, ContextHolder.getContext(), flowService, NodeContextHolder.getContext(), node);
                if (NodeUtils.handleLoopStatus(node, childStatus, errorExec)) {
                    break;
                }
                index++;

                if (isDebug) {
                    break;
                }
            }
        } finally {
            if (connection != null) {
                connection.close();
            }
        }
    }

    private List<Map<String, Object>> searchSubDept(Connection connection, String deptIdName, String deptPidName, Pair<String,String> parentPair, String deptFilter, LinkedList<Pair<String,String>> deptDnList, boolean outputOrg) {
        String parentId = parentPair.getFirst();
        String parentDn = parentPair.getSecond();
        SearchRequest searchRequest = new SearchRequest(parentDn, deptFilter);
        searchRequest.setSearchScope(SearchScope.ONELEVEL);
        searchRequest.setBinaryAttributes("objectGUID");

        Collection<LdapEntry> ldapEntries = LdapActionUtil.queryEntries(connection, searchRequest);
        ldapEntries = ldapEntries.stream().filter(e -> !e.getDn().equalsIgnoreCase(parentDn)).collect(Collectors.toList());

        List<Map<String, Object>> subOrgs = new ArrayList<>();
        for (LdapEntry ldapEntry : ldapEntries) {
            Map<String, Object> entryMap = new HashMap<>();
            for (LdapAttribute attribute : ldapEntry.getAttributes()) {
                if (attribute.getName().equals("objectGUID")) {
                    final String strGuid = LdapActionUtil.byte2String(attribute.getBinaryValue());
                    entryMap.put(attribute.getName(), strGuid);
                } else {
                    entryMap.put(attribute.getName(), attribute.getStringValue());
                }
            }
            entryMap.put("dn", ldapEntry.getDn());
            entryMap.put(deptPidName, parentId);

            deptDnList.add(new Pair(entryMap.get(deptIdName), ldapEntry.getDn()));

            if (outputOrg) {
                subOrgs.add(entryMap);
            }
        }

        return subOrgs;
    }

    private List<Map<String, Object>> searchSubUser(Connection connection, String userDeptName, Pair<String,String> deptPair, String userFilter, String rootDeptId, boolean containOrg, boolean outputUser) {
        try {
            List<Map<String, Object>> userList = new ArrayList<>();
            if (!outputUser) return userList;

            Collection<LdapEntry> ldapEntries = new ArrayList<>();
            String deptId = deptPair.getFirst();
            String deptDn = deptPair.getSecond();

            SearchRequest searchRequest = new SearchRequest(deptDn, userFilter);
            searchRequest.setSearchScope(SearchScope.ONELEVEL);
            searchRequest.setBinaryAttributes("objectGUID");

            PagedResultsControl pagedResultsControl = new PagedResultsControl(500);
            searchRequest.setControls(pagedResultsControl);

            byte[] cookie = null;
            do {
                SearchOperation search = new SearchOperation(connection);
                Response<SearchResult> response = search.execute(searchRequest);

                ResponseControl[] responseControls = response.getControls();
                for (ResponseControl control : responseControls) {
                    if (control instanceof PagedResultsControl) {
                        cookie = ((PagedResultsControl) control).getCookie();
                        searchRequest.setControls(new PagedResultsControl(500, cookie, false));
                        break;
                    }
                }

                final Collection<LdapEntry> pageResult = response.getResult().getEntries();
                ldapEntries.addAll(pageResult);

            } while (cookie != null && cookie.length > 0);

            for (LdapEntry ldapEntry : ldapEntries) {

                Map<String, Object> entryMap = new HashMap<>();
                for (LdapAttribute attribute : ldapEntry.getAttributes()) {
                    if (attribute.getName().equals("objectGUID")) {
                        final String strGuid = LdapActionUtil.byte2String(attribute.getBinaryValue());
                        entryMap.put(attribute.getName(), strGuid);
                    } else {
                        entryMap.put(attribute.getName(), attribute.getStringValue());
                    }
                }
                if (containOrg) {
                    entryMap.put(userDeptName, Arrays.asList(deptId));
                } else {
                    entryMap.put(userDeptName, Arrays.asList(rootDeptId));
                }
                entryMap.put("dn", ldapEntry.getDn());
                userList.add(entryMap);
            }
            return userList;
        } catch (Exception e) {
            throw new FlowException(e.getMessage());
        }
    }
}
