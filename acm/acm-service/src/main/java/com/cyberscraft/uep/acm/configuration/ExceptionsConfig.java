package com.cyberscraft.uep.acm.configuration;

import com.cyberscraft.uep.common.dto.ReturnResultVO;
import com.cyberscraft.uep.dataconnect.exception.FlowException;
import com.cyberscraft.uep.iam.errors.TransactionErrorDescription;
import com.cyberscraft.uep.iam.errors.TransactionErrorType;
import com.fasterxml.jackson.databind.exc.InvalidFormatException;
import com.fasterxml.jackson.databind.exc.UnrecognizedPropertyException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.TypeMismatchException;
import org.springframework.core.annotation.Order;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.security.oauth2.common.exceptions.OAuth2Exception;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.context.request.async.AsyncRequestTimeoutException;
import org.springframework.web.multipart.support.MissingServletRequestPartException;
import org.springframework.web.servlet.NoHandlerFoundException;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.sql.SQLIntegrityConstraintViolationException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by Liu Yang on 1/9/2016.
 * UC exception config.
 * Some init a exception handler, and others override exist method.
 */
@Order(Integer.MAX_VALUE-1)
@ControllerAdvice
public class ExceptionsConfig extends ResponseEntityExceptionHandler {
    private static final Logger logger = LoggerFactory.getLogger(ExceptionsConfig.class);


    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseBody
    public ResponseEntity<?> handleConstraintViolationException(ConstraintViolationException exception) {
        List<String> errorMessageList = exception.getConstraintViolations().stream()
                .map(ConstraintViolation::getMessage).collect(Collectors.toList());
        logger.warn(errorMessageList.toString());

        return new ResponseEntity<>(
                new ReturnResultVO<>(false, errorMessageList.toString(),
                        TransactionErrorType.INVALID_REQUEST_PARAM.getErrorCode()),
                HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(DuplicateKeyException.class)
    @ResponseBody
    public ResponseEntity<?> handleDuplicatedKeyException(DuplicateKeyException exception) {
        int returnErrorCode = TransactionErrorType.UNKNOWN_DAO_ERROR.getErrorCode();
        String returnErrorMsg = null;
        if (exception.getCause() != null) {
            if (exception.getCause() instanceof SQLIntegrityConstraintViolationException) {
                SQLIntegrityConstraintViolationException sqlEx =
                        (SQLIntegrityConstraintViolationException) exception.getCause();
                int errorCode = sqlEx.getErrorCode();
                String errorMsg = sqlEx.getMessage();

                if (errorCode == 1062 && errorMsg.indexOf("phone_number_index") > 0) {
                    returnErrorCode = TransactionErrorType.USER_MOBILE_ALREADY_EXIST_ERROR.getErrorCode();
                    returnErrorMsg = TransactionErrorType.USER_MOBILE_ALREADY_EXIST_ERROR.getDesc();
                }
                if (errorCode == 1062 && errorMsg.indexOf("email_index") > 0) {
                    returnErrorCode = TransactionErrorType.USER_EMAIL_ALREADY_EXIST_ERROR.getErrorCode();
                    returnErrorMsg = TransactionErrorType.USER_EMAIL_ALREADY_EXIST_ERROR.getDesc();
                }
                if (errorCode == 1062 && errorMsg.indexOf("id_card_index") > 0) {
                    returnErrorCode = TransactionErrorType.USER_ID_CARD_ALREADY_EXIST_ERROR.getErrorCode();
                    returnErrorMsg = TransactionErrorType.USER_ID_CARD_ALREADY_EXIST_ERROR.getDesc();
                }
            } else {
                returnErrorMsg = exception.getCause().getMessage();
            }
        }
        if (null == returnErrorMsg) {
            returnErrorMsg = TransactionErrorType.UNKNOWN_DAO_ERROR.getDesc();
        }
        return new ResponseEntity<>(
                new ReturnResultVO<>(false, returnErrorMsg, returnErrorCode),
                HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(OAuth2Exception.class)
    public ResponseEntity<?> handleException(OAuth2Exception ex) throws Exception {
        logger.info("OAuth2Exception error: " + ex.getClass().getSimpleName() + ", " + ex.getMessage());
        return new ResponseEntity<>(new ReturnResultVO<>(false, ex.getMessage(), TransactionErrorType.INVALID_REQUEST_PARAM.getErrorCode()), HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(ServletException.class)
    @ResponseBody
    ResponseEntity<?> handleControllerServletException(HttpServletRequest request, Throwable ex) {
        logger.error("ServletException", ex);

        HttpStatus status = getStatus(request);
        return new ResponseEntity<>(new ReturnResultVO<>(false, ex.getMessage(),
                TransactionErrorType.UNKNOWN_ERROR.getErrorCode()), status);
    }


    @ExceptionHandler(SocketTimeoutException.class)
    public ResponseEntity<?> handleException(SocketTimeoutException ex) throws Exception {
        logger.info("SocketTimeoutException error: " + ex.getClass().getSimpleName() + ", " + ex.getMessage());
        return new ResponseEntity<>(new ReturnResultVO<>(false, ex.getMessage(), TransactionErrorType.REQUEST_TIMEOUT_ERROR.getErrorCode()), HttpStatus.REQUEST_TIMEOUT);
    }

    @ExceptionHandler(ConnectException.class)
    public ResponseEntity<?> handleException(ConnectException ex) throws Exception {
        logger.info("ConnectException error: " + ex.getClass().getSimpleName() + ", " + ex.getMessage());
        return new ResponseEntity<>(new ReturnResultVO<>(false, ex.getMessage(), TransactionErrorType.DS_DESTINATION_CONNECT.getErrorCode()), HttpStatus.SERVICE_UNAVAILABLE);
    }

    @ExceptionHandler(FlowException.class)
    public ResponseEntity<?> handleException(FlowException ex) throws Exception {
        logger.info("FlowException error: " + ex.getClass().getSimpleName() + ", " + ex.getMessage());
        return new ResponseEntity<>(new ReturnResultVO<>(false, ex.getMessage(), TransactionErrorDescription.DATA_FLOW_COMMON_ERROR_CODE), HttpStatus.BAD_REQUEST);
    }

    @Override
    public ResponseEntity<Object> handleHttpMessageNotReadable(
            HttpMessageNotReadableException ex, HttpHeaders headers, HttpStatus status, WebRequest request) {

        String errorMessage = TransactionErrorType.INVALID_REQUEST_PARAM.getDesc();
        logger.warn(ex.getMessage());
        if (ex.getCause() != null && ex.getCause() instanceof UnrecognizedPropertyException) {
            UnrecognizedPropertyException unrecognizedPropertyException = (UnrecognizedPropertyException) ex.getCause();
            errorMessage = String.format("Unrecognized property name: %s", unrecognizedPropertyException.getPropertyName());
        }

        if (ex.getCause() != null && ex.getCause() instanceof InvalidFormatException) {
            InvalidFormatException invalidFormatException = (InvalidFormatException) ex.getCause();
            errorMessage = String.format("Invalid property value: %s, required type %s", invalidFormatException.getValue(), invalidFormatException.getTargetType().getSimpleName());
        }
        return new ResponseEntity<>(new ReturnResultVO<>(false, errorMessage,
                TransactionErrorType.INVALID_REQUEST_PARAM.getErrorCode()), status);
    }

    @Override
    protected ResponseEntity<Object> handleAsyncRequestTimeoutException(
            AsyncRequestTimeoutException ex, HttpHeaders headers, HttpStatus status, WebRequest request) {
        logger.warn(ex.getMessage());
        return new ResponseEntity<>(new ReturnResultVO<>(false, TransactionErrorType.REQUEST_TIMEOUT_ERROR.getDesc(),
                TransactionErrorType.REQUEST_TIMEOUT_ERROR.getErrorCode()), headers, status);
    }

    @Override
    protected ResponseEntity<Object> handleMissingServletRequestParameter(MissingServletRequestParameterException ex,
                                                                          HttpHeaders headers, HttpStatus status, WebRequest request) {
        logger.warn(ex.getMessage());
        return new ResponseEntity<>(new ReturnResultVO<>(false, ex.getMessage(),
                TransactionErrorType.INVALID_REQUEST_PARAM.getErrorCode()), status);
    }

    @Override
    protected ResponseEntity<Object> handleMissingServletRequestPart(MissingServletRequestPartException ex,
                                                                     HttpHeaders headers, HttpStatus status, WebRequest request) {
        logger.warn(ex.getMessage());
        return new ResponseEntity<>(new ReturnResultVO<>(false, ex.getMessage(),
                TransactionErrorType.INVALID_REQUEST_PARAM.getErrorCode()), status);
    }

    @Override
    protected ResponseEntity<Object> handleTypeMismatch(TypeMismatchException ex, HttpHeaders headers,
                                                        HttpStatus status, WebRequest request) {
        String message;
        if (ex.getCause() != null) {
            message = ex.getCause().getMessage();
        } else {
            message = TransactionErrorDescription.INVALID_REQUEST_PARAM_ERROR_DESC;
        }

        logger.warn(message);
        return new ResponseEntity<>(new ReturnResultVO<>(false, message,
                TransactionErrorType.INVALID_REQUEST_PARAM.getErrorCode()), status);
    }


    @Override
    protected ResponseEntity<Object> handleHttpRequestMethodNotSupported(
            HttpRequestMethodNotSupportedException ex, HttpHeaders headers, HttpStatus status,
            WebRequest request) {
        logger.info("HttpRequestMethodNotSupportedException", ex);
        return new ResponseEntity<>(new ReturnResultVO<>(false, ex.getMessage(),
                TransactionErrorType.UNKNOWN_ERROR.getErrorCode()), status);
    }

    @Override
    protected ResponseEntity<Object> handleNoHandlerFoundException(NoHandlerFoundException ex,
                                                                   HttpHeaders headers, HttpStatus status, WebRequest request) {
        logger.info("NoHandlerFoundException", ex);
        return new ResponseEntity<>(new ReturnResultVO<>(false, ex.getMessage(),
                TransactionErrorType.UNKNOWN_ERROR.getErrorCode()), status);
    }

    @Override
    protected ResponseEntity<Object> handleMethodArgumentNotValid(
            MethodArgumentNotValidException ex, HttpHeaders headers, HttpStatus status,
            WebRequest request) {
        String errorMessage =
                String.format("%s: %s", ex.getBindingResult().getFieldError().getField(),
                        ex.getBindingResult().getFieldError().getDefaultMessage());
        logger.warn(errorMessage);
        return new ResponseEntity<>(new ReturnResultVO<>(false, errorMessage,
                TransactionErrorType.INVALID_REQUEST_PARAM.getErrorCode()), status);
    }

    private HttpStatus getStatus(HttpServletRequest request) {
        HttpStatus httpStatus =
                (HttpStatus) request.getAttribute("javax.servlet.error.status_code");
        if (httpStatus == null) {
            return HttpStatus.INTERNAL_SERVER_ERROR;
        }
        return httpStatus;
    }
}
