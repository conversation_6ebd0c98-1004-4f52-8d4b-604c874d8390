package com.cyberscraft.uep.acm.monitor;

import com.cyberscraft.uep.acm.constants.ThreadPoolNameConstant;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/10/21 12:15 下午
 */
@Service
public class ThreadPoolMonitor {

    @Resource(name = ThreadPoolNameConstant.PARALLEL_POOL_NAME)
    private ThreadPoolExecutor parallelPoolExecutor;

    @Resource(name = ThreadPoolNameConstant.COMMON_POOL_NAME)
    private ThreadPoolExecutor commonPoolExecutor;

    @Resource(name = ThreadPoolNameConstant.SCHEDULE_API_FLOW_POOL_NAME)
    private ThreadPoolTaskScheduler threadPoolTaskScheduler;

    private final static Logger logger = LoggerFactory.getLogger(ThreadPoolMonitor.class);

    public void run() {
        new Thread(()->{
            while (true) {
                int size = parallelPoolExecutor.getQueue().size();
                int remainingCapacity = parallelPoolExecutor.getQueue().remainingCapacity();
                int activeCount = parallelPoolExecutor.getActiveCount();
                long completedTaskCount = parallelPoolExecutor.getCompletedTaskCount();
                logger.info("parallel pool queue wait for run count: {}", size);
//                logger.info("parallel pool queue remaining capacity: {}", remainingCapacity);
                logger.info("parallel pool queue active threads count: {}", activeCount);
//                logger.info("parallel pool queue completed task count: {}", completedTaskCount);


                size = commonPoolExecutor.getQueue().size();
                remainingCapacity = commonPoolExecutor.getQueue().remainingCapacity();
                activeCount = commonPoolExecutor.getActiveCount();
                completedTaskCount = commonPoolExecutor.getCompletedTaskCount();
                logger.info("common pool queue wait for run count: {}", size);
//                logger.info("common pool queue remaining capacity: {}", remainingCapacity);
                logger.info("common pool queue active threads count: {}", activeCount);
//                logger.info("common pool queue completed task count: {}", completedTaskCount);

                ScheduledThreadPoolExecutor scheduledThreadPoolExecutor = threadPoolTaskScheduler.getScheduledThreadPoolExecutor();
                size = scheduledThreadPoolExecutor.getQueue().size();
                remainingCapacity = scheduledThreadPoolExecutor.getQueue().remainingCapacity();
                activeCount = threadPoolTaskScheduler.getActiveCount();
                completedTaskCount = scheduledThreadPoolExecutor.getCompletedTaskCount();
                logger.info("schedule pool queue wait for run count: {}", size);
//                logger.info("schedule pool queue remaining capacity: {}", remainingCapacity);
                logger.info("schedule pool queue active threads count: {}", activeCount);
//                logger.info("schedule pool queue completed task count: {}", completedTaskCount);

                try {
                    Thread.sleep(60000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }

        }).start();
    }
}
