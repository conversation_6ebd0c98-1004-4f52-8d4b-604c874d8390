package com.cyberscraft.uep.acm.flow.action.handler;

import com.cyberscraft.uep.account.client.constant.RrsOrgAttr;
import com.cyberscraft.uep.account.client.constant.RrsUserAttr;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountErrorType;
import com.cyberscraft.uep.account.client.exception.ThirdPartyAccountException;
import com.cyberscraft.uep.account.client.service.IAuthenticateService;
import com.cyberscraft.uep.common.bean.RestApiResponse;
import com.cyberscraft.uep.common.domain.auth.Account;
import com.cyberscraft.uep.common.domain.auth.AccountParam;
import com.cyberscraft.uep.common.domain.auth.AuthorizeCredential;
import com.cyberscraft.uep.common.domain.auth.OAuth2Param;
import com.cyberscraft.uep.common.exception.UnauthorizedException;
import com.cyberscraft.uep.common.util.JsonUtil;
import com.cyberscraft.uep.common.util.RestAPIUtil;
import com.cyberscraft.uep.common.util.WebUrlUtil;
import com.cyberscraft.uep.dataconnect.constant.Constants;
import com.cyberscraft.uep.dataconnect.context.NodeRunContext;
import com.cyberscraft.uep.dataconnect.domain.ErrorExecEnum;
import com.cyberscraft.uep.dataconnect.domain.ProtocolType;
import com.cyberscraft.uep.dataconnect.domain.loop.LoopOutput;
import com.cyberscraft.uep.dataconnect.domain.loop.LoopType;
import com.cyberscraft.uep.dataconnect.domain.node.Node;
import com.cyberscraft.uep.dataconnect.domain.node.NodeOutput;
import com.cyberscraft.uep.dataconnect.domain.node.RunStatus;
import com.cyberscraft.uep.dataconnect.handler.IActionHandler;
import com.cyberscraft.uep.dataconnect.service.IFlowLogService;
import com.cyberscraft.uep.dataconnect.service.IFlowService;
import com.cyberscraft.uep.dataconnect.tools.ContextHolder;
import com.cyberscraft.uep.dataconnect.tools.NodeContextHolder;
import com.cyberscraft.uep.dataconnect.tools.NodeUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;

/**
 * <AUTHOR>
 * @Date 2024/12/10 12:01
 * @Version 1.0
 * @Description 遍历睿人事所有部门和用户
 */
@Service
public class LoopRrsOrgAndUserHandler implements IActionHandler {
    private final static Logger logger = LoggerFactory.getLogger(LoopRrsOrgAndUserHandler.class);

    @Autowired
    private IFlowService flowService;

    @Autowired
    private IFlowLogService flowLogService;

    @Autowired
    private IAuthenticateService authenticateService;

    /**
     * 获取下级组织列表
     */
    private String getOrgChildListApi = "/open/api/v1/org/getOrgChildList";

    /**
     * 分页获取部门下全部员工
     */
    private String getAllUserApi = "/open/api/v1/employee/list";

    /**
     * 获取员工兼岗信息
     */
    private String getUserPtjApi = "/open/api/v1/ptj/info/detail";


    @Resource
    public void setAuthenticateService(IAuthenticateService authenticateService) {
        this.authenticateService = authenticateService;
    }

    @Override
    public boolean isSupported(ProtocolType protocolType) {
        return ProtocolType.LOOP_RRS_ALL == protocolType;
    }

    @Override
    public void run(String taskId, Node node) {
        NodeRunContext nodeRunContext = NodeContextHolder.get(node.getNodeId());
        Map<String,Object> param = (Map) nodeRunContext.getInputValue();
        LocalDateTime startTime = LocalDateTime.now();
        boolean logSaved = true;

        AccountParam accountParam = node.getAccount();
        Account account = authenticateService.getAccount(accountParam, null, null);
        String authProtocol = accountParam.getAuthProtocol();
        String accountId = accountParam.getAccountId();
        String deptId = (String) param.get("dept_id");
        String corpId = (String) param.get("corp_id");

        int index = 0;
        Boolean isDebug = ContextHolder.get(Constants.IS_DEBUG) != null && (Boolean) ContextHolder.get(Constants.IS_DEBUG);
        ErrorExecEnum errorExec = ErrorExecEnum.END;
        List<Node> children = node.getChildren();

        final List<Map<String, Object>> childUserList = new ArrayList<>();
        Integer pageNum = 0;
        Integer pageSize = 100;
        Boolean hasNext = true;
        while (hasNext) {
            startTime = LocalDateTime.now();
            // 只有首次获取部门
            List<Map<String, Object>> childDeptList = new ArrayList<>();
            if (index == 0) {
                childDeptList = listSubDept(authProtocol, account, accountId, corpId, deptId);
            } else {
                pageNum++;
                hasNext = listSubUser(pageNum, pageSize, authProtocol, account, accountId, corpId, deptId, childUserList);
            }

            final Map<String, Object> currObject = new HashMap<>();
            currObject.put("dept", childDeptList);
            currObject.put("user", childUserList);

            //更新context
            node.setOutput(new NodeOutput(new LoopOutput(index, currObject, LoopType.INNER)));
            ContextHolder.put("N" + node.getNodeId(), node.getOutput().getValue());
            flowLogService.saveNodeRunLog(taskId, node, RunStatus.SUCCESS, Constants.SUCCESS_MSG, startTime, LocalDateTime.now(), logSaved);
            logSaved = false;


            //执行全部子节点
            RunStatus childStatus = NodeUtils.successExecChildren(taskId, children, ContextHolder.getContext(), flowService, NodeContextHolder.getContext(), node);
            if (NodeUtils.handleLoopStatus(node, childStatus, errorExec)) {
                break;
            }
            index++;

            if (isDebug) {
                break;
            }
        }
    }

    /**
     * 获取指定部门全部子部门列表
     *
     * @param authProtocol 认证协议
     * @param account      账户信息
     * @param accountId    认证账号id
     * @param corpId       企业id
     * @param deptParentId 查询部门
     * @return 子部门列表
     */
    private List<Map<String, Object>> listSubDept(String authProtocol, Account account, String accountId, String corpId, String deptParentId) {
        List<Map<String, Object>> externalOrgs = new ArrayList<>();
        List<String> parentOrgFindList = new ArrayList<>();
        parentOrgFindList.add(deptParentId);

        while (parentOrgFindList.size() > 0) {
            List<Map<String, Object>> orgs = new ArrayList<>();
            for (String parentOrg : parentOrgFindList) {
                List<Map<String, Object>> subOrgs = getExternalOrgList(authProtocol, account, accountId, corpId, Arrays.asList(parentOrg));
                orgs.addAll(subOrgs);
                externalOrgs.addAll(subOrgs);
            }
            parentOrgFindList.clear();
            for (Map<String, Object> subOrg : orgs) {
                parentOrgFindList.add(subOrg.get(RrsOrgAttr.orgId.getAttrName()).toString());
            }
        }
        return externalOrgs;
    }


    /**
     * 获取指定部门全部子用户列表
     *
     * @param page          页码
     * @param pageSize      页码
     * @param authProtocol  认证协议
     * @param account       账户信息
     * @param accountId     认证账号id
     * @param corpId        企业id
     * @param deptId        查询部门
     * @param childUserList 子部门列表
     * @return 是否还有下一页
     */
    private Boolean listSubUser(Integer page, Integer pageSize, String authProtocol, Account account, String accountId, String corpId, String deptId, List<Map<String, Object>> childUserList) {
        List<String> orgIds = Arrays.asList(deptId);
        Map<String, Object> params = new HashMap<>();
        params.put("corpId", corpId);
        params.put("page", page);
        params.put("pageSize", pageSize);
        params.put("orgIds", orgIds);
        Map<String, Object> response = httpRequest(authProtocol, account, accountId, getAllUserApi, params);

        Map data = (Map) response.get("data");
        if (data == null || data.isEmpty()) {
            return false;
        }
        List<Map<String, Object>> list = (List<Map<String, Object>>) data.get("list");
        for (Map<String, Object> map : list) {
            Map<String, Object> ptjParams = new HashMap<>();
            ptjParams.put("corpId", corpId);
            ptjParams.put(RrsUserAttr.empId.getAttrName(), map.get(RrsUserAttr.empId.getAttrName()).toString());
            Map<String, Object> ptjResponse = httpRequest(authProtocol, account, accountId, getUserPtjApi, ptjParams);
            List ptjData = (List) ptjResponse.get("data");
            if (CollectionUtils.isNotEmpty(ptjData)) {
                map.put(RrsUserAttr.ptjDepartment_source.getAttrName(), ptjData);
            }
            childUserList.add(map);
        }
        Integer total = Integer.valueOf(data.get("total").toString());
        return total > page * pageSize;
    }

    /**
     * 获取当前部门的子部门列表（不包括下一级）
     */
    private List<Map<String, Object>> getExternalOrgList(String authProtocol, Account account, String accountId, String corpId, List<String> parentOrgFindList) {
        List<Map<String, Object>> subOrgs = new ArrayList<>();
        Integer page = 1;
        Integer pageSize = 100;

        Map<String, Object> params = new HashMap<>();
        while (true) {
            params.put("corpId", corpId);
            params.put("page", page);
            params.put("pageSize", pageSize);
            params.put("parentOrgIdList", parentOrgFindList);
            Map<String, Object> response = httpRequest(authProtocol, account, accountId, getOrgChildListApi, params);

            Map data = (Map) response.get("data");
            if (data == null || data.isEmpty()) {
                return subOrgs;
            }
            List<Map<String, Object>> list = (List<Map<String, Object>>) data.get("list");
            for (Map<String, Object> map : list) {
                Map<String, Object> dataMap = (Map<String, Object>) map.get("dataMap");
                subOrgs.add(dataMap);
            }
            Integer total = Integer.valueOf(data.get("total").toString());
            if (total <= page * pageSize) {
                return subOrgs;
            }
            page++;
        }
    }


    private Map<String, Object> httpRequest(String authProtocol, Account account, String accountId, String api, Map<String, Object> params) {
        AuthorizeCredential authorizeCredential = authenticateService.auth(authProtocol, account, accountId, false);
        OAuth2Param oAuth2Param = (OAuth2Param) account;
        String authUrl = oAuth2Param.getEndpoint();
        String host = WebUrlUtil.getOrigin(authUrl);
        String endpoint = host + api;
        RestApiResponse response;
        Map<String, Object> resp;
        try {
            response = RestAPIUtil.modifyEntityForString(endpoint, HttpMethod.POST.name(), params, authorizeCredential.getHead(), new HashMap<>(), null);
            checkCredential(response, api);
        } catch (UnauthorizedException e) {
            logger.error("ruiRenShi UnauthorizedException:{}", e.getMessage());
            try {
                Thread.sleep(1000);
            } catch (InterruptedException interruptedException) {

            }

            //重新获取Authorization
            authorizeCredential = authenticateService.auth(authProtocol, account, accountId, true);
            response = RestAPIUtil.modifyEntityForString(endpoint, HttpMethod.POST.name(), params, authorizeCredential.getHead(), new HashMap<>(), null);
        }

        return checkResponse(response, api);
    }

    /**
     * 校验鉴权返回结果
     */
    private void checkCredential(RestApiResponse apiResponse, String api) throws UnauthorizedException {
        if (apiResponse.getHttpStatus() == 403) {
            String res = apiResponse.getBody().toString();
            logger.error("ruiRenShi response httpStatus 403 error: {}, api: {}", res, api);
            throw new UnauthorizedException();
        }
        Map<String, Object> response = null;
        Object responseBody = apiResponse.getBody();
        if (responseBody instanceof String) {
            response = JsonUtil.str2Map(responseBody.toString());
        } else {
            response = JsonUtil.obj2Map(responseBody);
        }
        String error = String.valueOf(response.get("error"));
        String errorDescription = String.valueOf(response.get("error_description"));
        String message = String.valueOf(response.get("message"));
        if (StringUtils.isNotBlank(error) && "invalid_token".equals(error)) {
            logger.error("ruiRenShi response error:{}, {}, {}, api: {}", error, errorDescription, message, api);
            throw new UnauthorizedException();
        }
    }


    /**
     * 校验返回结果
     */
    private Map<String, Object> checkResponse(RestApiResponse apiResponse, String api) {
        try {
            Thread.sleep(50L);
        } catch (Exception e) {
            logger.error("ruiRenShi sleep error", e);
        }
        Map<String, Object> response = null;
        Object responseBody = apiResponse.getBody();
        if (responseBody instanceof String) {
            response = JsonUtil.str2Map(responseBody.toString());
        } else {
            response = JsonUtil.obj2Map(responseBody);
        }

        String success = String.valueOf(response.get("success"));
        String errorCode = String.valueOf(response.get("errorCode"));
        String errorMsg = String.valueOf(response.get("errorMsg"));

        if (!"true".equalsIgnoreCase(success)) {
            logger.error("ruiRenShi response error:{}, {}, {}, api: {}", errorCode, errorMsg, response, api);
            if (StringUtils.isNotBlank(errorCode)) {
                throw new ThirdPartyAccountException(errorCode, errorMsg);
            } else {
                throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.SERVER_RESPONSE_INVALID.getCode(), "调用睿人事接口错误，服务器未正确返回结果");
            }
        } else {
            Object obj = response.get("data");
            if (obj instanceof Map) {
                Map<String, Object> data = (Map) obj;
                if (data != null) {
                    Object errorMsgObj = data.get("errorMsg");
                    if (errorMsgObj != null) {
                        throw new ThirdPartyAccountException(ThirdPartyAccountErrorType.SERVER_RESPONSE_INVALID.getCode(), errorMsgObj.toString());
                    }
                }
            }
        }
        return response;
    }

}
