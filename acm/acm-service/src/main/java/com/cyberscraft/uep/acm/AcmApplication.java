package com.cyberscraft.uep.acm;

import com.cyberscraft.uep.acm.configuration.FeginClientConfiguration;
import com.cyberscraft.uep.base.oauth2.client.EnableDigitalSeeOauth2Client;
import com.cyberscraft.uep.iam.api.UserSelfApi;
import com.cyberscraft.uep.iam.api.inner.AuditCollectApi;
import com.cyberscraft.uep.iam.api.inner.InnerUserOrgApi;
import com.cyberscraft.uep.mq.EnableMQ;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.jdbc.DataSourceHealthContributorAutoConfiguration;
import org.springframework.boot.actuate.autoconfigure.jms.JmsHealthContributorAutoConfiguration;
import org.springframework.boot.actuate.autoconfigure.ldap.LdapHealthContributorAutoConfiguration;
import org.springframework.boot.actuate.autoconfigure.mail.MailHealthContributorAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.jpa.JpaRepositoriesAutoConfiguration;
import org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration;
import org.springframework.boot.autoconfigure.ldap.LdapAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/1/12 3:05 下午
 */
@SpringBootApplication(exclude = {FreeMarkerAutoConfiguration.class
        , HibernateJpaAutoConfiguration.class
        , JpaRepositoriesAutoConfiguration.class
        , LdapAutoConfiguration.class
        , MailHealthContributorAutoConfiguration.class
        , LdapHealthContributorAutoConfiguration.class
        , JmsHealthContributorAutoConfiguration.class
        , DataSourceHealthContributorAutoConfiguration.class
        , MongoAutoConfiguration.class
},
        scanBasePackages = {
                "com.cyberscraft.uep.acm",
                "com.cyberscraft.uep.account.client",
                "com.cyberscraft.uep.dataconnect",
                "com.cyberscraft.uep.common.config",
                "com.cyberscraft.uep.common.auditlog",
                "com.cyberscraft.uep.dds.common",
                "com.cyberscraft.uep.proxy.meta",
                "com.cyberscraft.uep.common.exception"
        }
)
@EnableScheduling
@EnableTransactionManagement
@EnableDiscoveryClient
@EnableFeignClients(defaultConfiguration = FeginClientConfiguration.class,
        basePackageClasses = {
                UserSelfApi.class,
                InnerUserOrgApi.class,
                AuditCollectApi.class,
        })
@EnableMQ
@EnableDigitalSeeOauth2Client
public class AcmApplication {
        private static final Logger LOG = LoggerFactory.getLogger(AcmApplication.class);


        public static void main(String[] args) {
                System.setProperty("rocketmq.client.logUseSlf4j", "true");
                SpringApplication.run(AcmApplication.class, args);
                LOG.info("============================================================");
                LOG.info("===========ACM service is success started===============");
                LOG.info("============================================================");
        }
}
