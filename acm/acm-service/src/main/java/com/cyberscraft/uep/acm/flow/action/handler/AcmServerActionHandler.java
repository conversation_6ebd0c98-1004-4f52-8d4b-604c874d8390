package com.cyberscraft.uep.acm.flow.action.handler;

import com.cyberscraft.uep.acm.cal.EWSController;
import com.cyberscraft.uep.common.domain.Param;
import com.cyberscraft.uep.common.domain.auth.ExchangeParam;
import com.cyberscraft.uep.common.util.MappingParser;
import com.cyberscraft.uep.dataconnect.context.NodeRunContext;
import com.cyberscraft.uep.dataconnect.domain.ProtocolType;
import com.cyberscraft.uep.dataconnect.domain.action.AcmAction;
import com.cyberscraft.uep.dataconnect.domain.node.Node;
import com.cyberscraft.uep.dataconnect.domain.node.NodeOutput;
import com.cyberscraft.uep.dataconnect.handler.IActionHandler;
import com.cyberscraft.uep.dataconnect.tools.NodeContextHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;

/**
 * @Description 这个位置主要是连接流调用服务内部的一些业务逻辑
 * <AUTHOR>
 * @Date 2024/10/12 18:03
 */
@Service
public class AcmServerActionHandler implements IActionHandler {

    @Autowired
    private EWSController ewsController;

    @Override
    public boolean isSupported(ProtocolType protocolType) {
        return ProtocolType.ACM == protocolType;
    }

    @Override
    public void run(String taskId, Node node) {
        NodeRunContext nodeRunContext = NodeContextHolder.get(node.getNodeId());
        Param inputParam = (Param) nodeRunContext.getInputValue();
        AcmAction acmAction = (AcmAction) inputParam;

        Object result = new Object();
        switch (acmAction.getKey()) {
            case "CREATE_CAL":
                ExchangeParam exchangeParam = MappingParser.schema2Obj(new HashMap<>(), node.getAccount().getAccount(), ExchangeParam.class);
                exchangeParam.setAccountId(node.getAccount().getAccountId());
                acmAction.setAccount(exchangeParam);
                result = ewsController.createCal(acmAction);
                break;
            case "UPDATE_CAL":
                exchangeParam = MappingParser.schema2Obj(new HashMap<>(), node.getAccount().getAccount(), ExchangeParam.class);
                exchangeParam.setAccountId(node.getAccount().getAccountId());
                acmAction.setAccount(exchangeParam);
                result = ewsController.updateCal(acmAction);
                break;
            case "CANCEL_CAL":
                exchangeParam = MappingParser.schema2Obj(new HashMap<>(), node.getAccount().getAccount(), ExchangeParam.class);
                exchangeParam.setAccountId(node.getAccount().getAccountId());
                acmAction.setAccount(exchangeParam);
                result = ewsController.cancelCal(acmAction);
                break;
            case "CHANGE_STATUS":
                exchangeParam = MappingParser.schema2Obj(new HashMap<>(), node.getAccount().getAccount(), ExchangeParam.class);
                exchangeParam.setAccountId(node.getAccount().getAccountId());
                acmAction.setAccount(exchangeParam);
                result = ewsController.changeItemStatus(acmAction);
                break;
            case "IS_CREATE_EWS_CAL":
                result = ewsController.isCreateEwsCal(acmAction);
                break;
            case "CREATE_MAPPING":
                result = ewsController.createDingExchangeLink(acmAction);
                break;
            case "GET_EVENT_EXCHANGE_ID":
                result = ewsController.getEventMappingByExchangeId(acmAction);
                break;
            case "GET_EVENT_TING_ID":
                result = ewsController.getEventMappingByDingTalkId(acmAction);
                break;

        }
        node.setOutput(new NodeOutput(result));
    }
}
