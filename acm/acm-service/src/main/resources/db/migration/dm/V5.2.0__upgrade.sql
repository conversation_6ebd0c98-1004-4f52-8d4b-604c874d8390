INSERT INTO "acm"."acm_app_connector" ("id", "name", "description", "icon", "build_type", "app_domain_category", "app_action_category", "app_package", "deployment_type", "ver_name", "ver_number", "ver_type", "create_time", "create_by", "update_time", "update_by", "tenant_id") VALUES (39, '通讯录同步', NULL, NULL, 'BuiltIn', 'DS_AREA', NULL, '1881898965186723842', 'SaasApp', '1', 1, 'RELEASE', '2025-01-22 10:57:32', 'admin', '2025-02-20 16:35:41', 'admin', 'iam');

-- 通讯录同步流化动作
INSERT INTO "acm"."acm_app_action" ("id", "action_key", "is_trigger", "name", "description", "connector_id", "account_schema_id", "protocol", "action_type", "status", "config_schema", "create_time", "create_by", "update_time", "update_by", "tenant_id", "sample_output", "node_struct") VALUES (151, '1887778362112516090', 1, '启动通讯录同步', '根据接收到的参数，实时启动通讯录集成流程', 39, NULL, 'USER_PUSH', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"异步：webhook接收到数据后，将直接返回接收成功的结果以及流的信息。 同步：Webhook接收到数据后，将等待连接流执行完成并将最后的执行结果返回给调用方（最长等待时间为10s，超时将返回为空）。请查看接口文档获得更多详细信息\",\"display_name\":\"连接流执行方式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"type\",\"option_values\":[\"同步\",\"异步\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"异步\",\"value_type\":\"FIX_VALUE\"}],\"type\":\"OBJECT\",\"ui_id\":\"1887778854628663297\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2025-02-07 16:20:09', 'admin', '2025-02-07 17:50:32', 'admin', 'iam', NULL, 'SINGLE');
INSERT INTO "acm"."acm_app_action" ("id", "action_key", "is_trigger", "name", "description", "connector_id", "account_schema_id", "protocol", "action_type", "status", "config_schema", "create_time", "create_by", "update_time", "update_by", "tenant_id", "sample_output", "node_struct") VALUES (152, '1882032927489998852', 0, '遍历部门数据', '依照部门层级结构，逐一遍历各层级部门', 39, NULL, 'LOOP_DIGITAL_ORG', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"填写需要同步到下游真实存在的部门ID，如果不填写则使用创建连接器的值\",\"display_name\":\"下游根部门ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"thirdRootId\",\"option_values\":[],\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1890231663756222466\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2025-01-22 19:49:51', 'admin', '2025-02-14 10:48:42', 'admin', 'iam', NULL, 'LOOP');
INSERT INTO "acm"."acm_app_action" ("id", "action_key", "is_trigger", "name", "description", "connector_id", "account_schema_id", "protocol", "action_type", "status", "config_schema", "create_time", "create_by", "update_time", "update_by", "tenant_id", "sample_output", "node_struct") VALUES (155, '1881899198532632579', 0, '遍历用户数据', '依照部门层级结构，逐一遍历各层级部门的直属用户', 39, NULL, 'LOOP_DIGITAL_USER', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[],\"type\":\"OBJECT\",\"ui_id\":\"1882614466460438529\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2025-01-22 10:58:28', 'admin', '2025-02-07 17:50:45', 'admin', 'iam', NULL, 'LOOP');
INSERT INTO "acm"."acm_app_action" ("id", "action_key", "is_trigger", "name", "description", "connector_id", "account_schema_id", "protocol", "action_type", "status", "config_schema", "create_time", "create_by", "update_time", "update_by", "tenant_id", "sample_output", "node_struct") VALUES (158, '1887801420277784579', 0, '遍历待删除用户数据', '遍历符合删除阈值控制的所有用户数据', 39, NULL, 'LOOP_DELETE_USER', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[],\"type\":\"OBJECT\",\"ui_id\":\"1887801487747358722\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2025-02-07 17:51:47', 'admin', '2025-02-07 17:52:03', 'admin', 'iam', NULL, 'LOOP');
INSERT INTO "acm"."acm_app_action" ("id", "action_key", "is_trigger", "name", "description", "connector_id", "account_schema_id", "protocol", "action_type", "status", "config_schema", "create_time", "create_by", "update_time", "update_by", "tenant_id", "sample_output", "node_struct") VALUES (159, '1887799845358575619', 0, '删除第三方用户信息', '删除三方用户数据', 39, NULL, 'DELETE_USER', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"请选择创建的请求类型\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619266\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619267\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619268\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"路径传惨使用该选项。使用 {} 标识地址参数\",\"display_name\":\"Path参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1738921709264\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"header\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619269\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Query参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619270\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"按照三方接口文档提交即可\",\"display_name\":\"Body参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body\",\"page_control\":\"TEXTAREA\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619271\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"请求返回的类型。默认是JSON 如果返回字符串需要修改\",\"display_name\":\"返回类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"responseType\",\"option_values\":[\"JSON\",\"STRING\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1739868613926\",\"update\":true,\"value\":\"JSON\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"用于判断请求是否成功。一般情况为 return response.body.code == 0; 根据实际情况编写\",\"display_name\":\"请求成功判断\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"successCondition\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619123\",\"update\":true,\"value\":\"\",\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1892493229843193858\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2025-02-07 17:45:31', 'admin', '2025-02-20 16:35:21', 'admin', 'iam', NULL, 'SINGLE');
INSERT INTO "acm"."acm_app_action" ("id", "action_key", "is_trigger", "name", "description", "connector_id", "account_schema_id", "protocol", "action_type", "status", "config_schema", "create_time", "create_by", "update_time", "update_by", "tenant_id", "sample_output", "node_struct") VALUES (160, '1887799845358575620', 0, '删除第三方用户信息（内置鉴权）', '删除三方用户数据', 39, 35, 'DELETE_USER', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"请选择创建的请求类型\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619266\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619267\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619268\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"路径传惨使用该选项。使用 {} 标识地址参数\",\"display_name\":\"Path参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1738921709264\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"header\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619269\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Query参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619270\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"按照三方接口文档提交即可\",\"display_name\":\"Body参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body\",\"page_control\":\"TEXTAREA\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619271\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"请求返回的类型。默认是JSON 如果返回字符串需要修改\",\"display_name\":\"返回类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"responseType\",\"option_values\":[\"JSON\",\"STRING\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1739868576176\",\"update\":true,\"value\":\"JSON\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"用于判断请求是否成功。一般情况为 return response.body.code == 0; 根据实际情况编写\",\"display_name\":\"请求成功判断\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"successCondition\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619123\",\"update\":true,\"value\":\"\",\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1892493306687037441\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2025-02-07 17:45:31', 'admin', '2025-02-20 16:35:40', 'admin', 'iam', NULL, 'SINGLE');
INSERT INTO "acm"."acm_app_action" ("id", "action_key", "is_trigger", "name", "description", "connector_id", "account_schema_id", "protocol", "action_type", "status", "config_schema", "create_time", "create_by", "update_time", "update_by", "tenant_id", "sample_output", "node_struct") VALUES (161, '1887801760695885827', 0, '遍历待删除部门数据', '遍历符合删除阈值控制的所有部门数据', 39, NULL, 'LOOP_DELETE_ORG', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[],\"type\":\"OBJECT\",\"ui_id\":\"1887802026677673986\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2025-02-07 17:53:08', 'admin', '2025-02-07 17:54:11', 'admin', 'iam', NULL, 'LOOP');
INSERT INTO "acm"."acm_app_action" ("id", "action_key", "is_trigger", "name", "description", "connector_id", "account_schema_id", "protocol", "action_type", "status", "config_schema", "create_time", "create_by", "update_time", "update_by", "tenant_id", "sample_output", "node_struct") VALUES (162, '1887799285637095430', 0, '删除第三方部门信息', '删除三方部门数据', 39, NULL, 'DELETE_ORG', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"请选择请求类型\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619266\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619267\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619268\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"路径传惨使用该选项。使用 {} 标识地址参数\",\"display_name\":\"Path参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1738921774703\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"header\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619269\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Query参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619270\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"按照三方接口文档需要格式提交即可\",\"display_name\":\"Body参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body\",\"page_control\":\"TEXTAREA\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619271\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"请求返回的类型。默认是JSON 如果返回字符串需要修改\",\"display_name\":\"返回类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"responseType\",\"option_values\":[\"JSON\",\"STRING\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1739868515827\",\"update\":true,\"value\":\"JSON\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"用于判断请求是否成功。一般情况为 return response.body.code == 0; 根据实际情况编写\",\"display_name\":\"请求成功判断\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"successCondition\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619123\",\"update\":true,\"value\":\"\",\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1892492654116249601\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2025-02-07 17:43:18', 'admin', '2025-02-20 16:34:34', 'admin', 'iam', NULL, 'SINGLE');
INSERT INTO "acm"."acm_app_action" ("id", "action_key", "is_trigger", "name", "description", "connector_id", "account_schema_id", "protocol", "action_type", "status", "config_schema", "create_time", "create_by", "update_time", "update_by", "tenant_id", "sample_output", "node_struct") VALUES (163, '1887799285637095431', 0, '删除第三方部门信息（内置鉴权）', '删除三方部门数据', 39, 35, 'DELETE_ORG', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"请选择创建的请求类型\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619266\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619267\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619268\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"路径传惨使用该选项。使用 {} 标识地址参数\",\"display_name\":\"Path参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1738921774703\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"header\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619269\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Query参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619270\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"按照三方接口文档提交即可\",\"display_name\":\"Body参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body\",\"page_control\":\"TEXTAREA\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619271\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"请求返回的类型。默认是JSON 如果返回字符串需要修改\",\"display_name\":\"返回类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"responseType\",\"option_values\":[\"JSON\",\"STRING\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1739868390755\",\"update\":true,\"value\":\"JSON\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"用于判断请求是否成功。一般情况为 return response.body.code == 0; 根据实际情况编写\",\"display_name\":\"请求成功判断\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"successCondition\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619123\",\"update\":true,\"value\":\"\",\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1892493144006762498\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2025-02-07 17:43:18', 'admin', '2025-02-20 16:35:01', 'admin', 'iam', NULL, 'SINGLE');
INSERT INTO "acm"."acm_app_action" ("id", "action_key", "is_trigger", "name", "description", "connector_id", "account_schema_id", "protocol", "action_type", "status", "config_schema", "create_time", "create_by", "update_time", "update_by", "tenant_id", "sample_output", "node_struct") VALUES (164, '1888176546500485123', 0, '完成通讯录同步', '完成通讯录同步，删除缓存、锁等内容', 39, NULL, 'COMPLETE_USER_PUSH', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[],\"type\":\"OBJECT\",\"ui_id\":\"1888176624174800897\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2025-02-08 18:42:24', 'admin', '2025-02-08 18:42:42', 'admin', 'iam', NULL, 'SINGLE');

INSERT INTO "acm"."acm_app_action" ("id", "action_key", "is_trigger", "name", "description", "connector_id", "account_schema_id", "protocol", "action_type", "status", "config_schema", "create_time", "create_by", "update_time", "update_by", "tenant_id", "sample_output", "node_struct") VALUES (153, '1882311767479209989', 0, '创建或更新第三方部门信息', '用于三方系统创建和更新部门接口不相同的情况', 39, NULL, 'PUSH_ORG', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"请选择创建的请求类型\",\"display_name\":\"创建请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"createMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619266\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"创建的地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"createPath\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619267\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"更新的请求有类型和创建类型不一致时必填\",\"display_name\":\"更新请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"updateMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1737612672096\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"更新地址和创建地址不一致时必填\",\"display_name\":\"更新的地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"updatePath\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1737546450938\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619268\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"路径传惨使用该选项。使用 {} 标识地址参数\",\"display_name\":\"Path参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1739864096037\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"header\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619269\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Query参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619270\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"发起请求时的参数数据结构如下：\\n{\\n    \\\"k1\\\": \\\"v1\\\",\\n    \\\"k2\\\": \\\"v2\\\"\\n}\\n根据实际情况修改\\n提交时的数据结构：确保提交的数据结构与上述示例完全一致。\",\"display_name\":\"请求体\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body\",\"page_control\":\"TEXTAREA\",\"required\":true,\"type\":\"OBJECT\",\"ui_id\":\"1737546057702\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"与请求体不一致时配置\\n发起请求时的参数数据结构如下：\\n{\\n    \\\"k1\\\": \\\"v1\\\",\\n    \\\"k2\\\": \\\"v2\\\"\\n}\\n根据实际情况修改\\n提交时的数据结构：确保提交的数据结构与上述示例完全一致。\",\"display_name\":\"更新请求体\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"updateBody\",\"page_control\":\"TEXTAREA\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1741261577807\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"请求返回的类型。默认是JSON 如果返回字符串需要修改\",\"display_name\":\"返回类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"responseType\",\"option_values\":[\"JSON\",\"STRING\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1739868315362\",\"update\":true,\"value\":\"JSON\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"此输入框用于填写解析ID的表达式，帮助集成平台与第三方系统之间建立数据关联\\n 1、选择前置节点：如果前置节点已获取，请直接选择。\\n 2、编写表达式：根据返回结果编写解析ID的表达式。例如，若需从响应体中提取id字段，则应填写 return response.body.data.id;\",\"display_name\":\"解析Id的表达式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"idExpress\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1737545743752\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"用于在更新时判断部门是否存在，并决定是否创建新部门 \\n编写表达式：根据部门不存在的结果编写表达式，以判断是否需要创建新部门。 \\n示例：如果部门不存在则创建新部门，可以编写表达式 return response.body.code == 1002;\",\"display_name\":\"用户不存在检查\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"createExpress\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1737545820983\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"用于在创建时如果部门已经存在是否更新部门 \\n编写表达式：根据部门存在的结果编写表达式，以判断是否需要更新新部门。 \\n示例：如果部门存在则更新部门，可以编写表达式 return response.body.code == 1002;\",\"display_name\":\"部门存在检查\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"updateExpress\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1737545909620\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"用于判断请求是否成功。一般情况为 return response.body.code == 0; 根据实际情况编写\",\"display_name\":\"判断请求成功表达式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"successCondition\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"FIX_VALUE\"}],\"type\":\"OBJECT\",\"ui_id\":\"1897615211895013378\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2025-01-23 14:17:52', 'admin', '2025-03-06 19:48:17', 'admin', 'iam', NULL, 'SINGLE');
INSERT INTO "acm"."acm_app_action" ("id", "action_key", "is_trigger", "name", "description", "connector_id", "account_schema_id", "protocol", "action_type", "status", "config_schema", "create_time", "create_by", "update_time", "update_by", "tenant_id", "sample_output", "node_struct") VALUES (154, '1882311767479209990', 0, '创建或更新第三方部门信息（内置鉴权）', '用于三方系统创建和更新部门接口不相同的情况', 39, 35, 'PUSH_ORG', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"请选择创建的请求类型\",\"display_name\":\"创建请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"createMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619266\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"创建的地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"createPath\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619267\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"更新的请求有类型和创建类型不一致时必填\",\"display_name\":\"更新请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"updateMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1737612672096\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"更新地址和创建地址不一致时必填\",\"display_name\":\"更新的地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"updatePath\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1737546450938\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619268\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"路径传惨使用该选项。使用 {} 标识地址参数\",\"display_name\":\"Path参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1739864070929\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"header\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619269\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Query参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619270\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"发起请求时的参数数据结构如下：\\n{\\n    \\\"k1\\\": \\\"v1\\\",\\n    \\\"k2\\\": \\\"v2\\\"\\n}\\n根据实际情况修改\\n提交时的数据结构：确保提交的数据结构与上述示例完全一致。\",\"display_name\":\"请求体\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body\",\"page_control\":\"TEXTAREA\",\"required\":true,\"type\":\"OBJECT\",\"ui_id\":\"1737546057702\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"与请求体不一致时配置\\n发起请求时的参数数据结构如下：\\n{\\n    \\\"k1\\\": \\\"v1\\\",\\n    \\\"k2\\\": \\\"v2\\\"\\n}\\n根据实际情况修改\\n提交时的数据结构：确保提交的数据结构与上述示例完全一致。\",\"display_name\":\"更新请求体\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"updateBody\",\"page_control\":\"TEXTAREA\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1741261717093\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"请求返回的类型。默认是JSON 如果返回字符串需要修改\",\"display_name\":\"返回类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"responseType\",\"option_values\":[\"JSON\",\"STRING\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1739868211408\",\"update\":true,\"value\":\"JSON\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"此输入框用于填写解析Id的表达式，帮助集成平台与第三方系统之间建立数据关联\\n1、选择前置节点：如果前置节点已获取，请直接选择。\\n2、编写表达式：根据返回结果编写解析ID的表达式。例如，若需从响应体中提取id字段，则应填写 return response.body.data.id;\",\"display_name\":\"解析Id的表达式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"idExpress\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1737545743752\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"用于在更新时判断部门是否存在，并决定是否创建新部门 \\n编写表达式：根据部门不存在的结果编写表达式，以判断是否需要创建新部门。 \\n示例：如果部门不存在则创建新部门，可以编写表达式 return response.body.code == 1002;\",\"display_name\":\"部门不存在检查\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"createExpress\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1737545820983\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"用于在创建时如果部门已经存在是否更新部门 \\n编写表达式：根据部门存在的结果编写表达式，以判断是否需要更新新部门。 \\n示例：如果部门存在则更新部门，可以编写表达式 return response.body.code == 1002;\",\"display_name\":\"部门存在检查\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"updateExpress\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1737545909620\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"用于判断请求是否成功。一般情况为 return response.body.code == 0; 根据实际情况编写\",\"display_name\":\"判断请求成功表达式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"successCondition\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"FIX_VALUE\"}],\"type\":\"OBJECT\",\"ui_id\":\"1897615436512575489\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2025-01-23 14:17:52', 'admin', '2025-03-06 19:49:20', 'admin', 'iam', NULL, 'SINGLE');

INSERT INTO "acm"."acm_app_action" ("id", "action_key", "is_trigger", "name", "description", "connector_id", "account_schema_id", "protocol", "action_type", "status", "config_schema", "create_time", "create_by", "update_time", "update_by", "tenant_id", "sample_output", "node_struct") VALUES (156, '1882031726140669955', 0, '创建或更新第三方用户信息', '用于三方系统创建和更新用户接口不相同的情况', 39, NULL, 'PUSH_USER', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"请选择创建的请求类型\",\"display_name\":\"创建请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"createMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619266\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"创建的地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"createPath\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619267\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"更新的请求有类型和创建类型不一致时必填\",\"display_name\":\"更新请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"updateMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1737612672096\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"更新地址和创建地址不一致时必填\",\"display_name\":\"更新的地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"updatePath\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1737546450938\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619268\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"路径传惨使用该选项。使用 {} 标识地址参数\",\"display_name\":\"Path参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1739864036452\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"header\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619269\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Query参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619270\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"发起请求时的参数数据结构如下：\\n{\\n    \\\"k1\\\": \\\"v1\\\",\\n    \\\"k2\\\": \\\"v2\\\"\\n}\\n根据实际情况修改\\n提交时的数据结构：确保提交的数据结构与上述示例完全一致。\",\"display_name\":\"请求体\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body\",\"page_control\":\"TEXTAREA\",\"required\":true,\"type\":\"OBJECT\",\"ui_id\":\"1737546057702\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"与更新体不一致时配置\\n发起请求时的参数数据结构如下：\\n{\\n    \\\"k1\\\": \\\"v1\\\",\\n    \\\"k2\\\": \\\"v2\\\"\\n}\\n根据实际情况修改\\n提交时的数据结构：确保提交的数据结构与上述示例完全一致。\",\"display_name\":\"更新请求体\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"updateBody\",\"page_control\":\"TEXTAREA\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1741261444544\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"请求返回的类型。默认是JSON 如果返回字符串需要修改\",\"display_name\":\"请求返回类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"responseType\",\"option_values\":[\"JSON\",\"STRING\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1739868147024\",\"update\":true,\"value\":\"JSON\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"此输入框用于填写解析ID的表达式，帮助集成平台与第三方系统之间建立数据关联 \\n1、选择前置节点：如果前置节点已获取，请直接选择。\\n2、编写表达式：根据返回结果编写解析ID的表达式。例如，若需从响应体中提取id字段，则应填写 return response.body.data.id;\",\"display_name\":\"解析Id的表达式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"idExpress\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1737545743752\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"此输入框用于填写解析unionid的表达式，帮助集成平台与第三方系统之间建立数据关联 \\n1、选择前置节点：如果前置节点已获取，请直接选择。 \\n2、编写表达式：根据返回结果编写解析openid的表达式。例如，若需从响应体中提取id字段，则应填写 return response.body.data.unionid;\",\"display_name\":\"解析UnionId的表达式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"unionIdExpress\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1738840918861\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"此输入框用于填写解析openid的表达式，帮助集成平台与第三方系统之间建立数据关联 \\n1、选择前置节点：如果前置节点已获取，请直接选择。 \\n2、编写表达式：根据返回结果编写解析openid的表达式。例如，若需从响应体中提取id字段，则应填写 return response.body.data.openid;\",\"display_name\":\"解析OpenId的表达式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"openIdExpress\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1738840909114\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"用于在更新时判断用户是否存在，并决定是否创建新用户 \\n编写表达式：根据用户不存在的结果编写表达式，以判断是否需要创建新用户。 \\n示例：如果用户不存在则创建新用户，可以编写表达式 return response.body.code == 1002;\",\"display_name\":\"用户不存在检查\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"createExpress\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1737545820983\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"用于在创建时如果用户已经存在是否更新用户 \\n编写表达式：根据用户存在的结果编写表达式，以判断是否需要更新新用户。 示例：如果用户存在则更新用户，可以编写表达式 return response.body.code == 1002;\",\"display_name\":\"用户存在检查\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"updateExpress\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1737545909620\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"用于判断请求是否成功。一般情况为 return response.body.code == 0; 根据实际情况编写\",\"display_name\":\"判断请求成功表达式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"successCondition\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"FIX_VALUE\"}],\"type\":\"OBJECT\",\"ui_id\":\"1897614541414547457\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2025-01-22 19:45:05', 'admin', '2025-03-06 19:45:53', 'admin', 'iam', NULL, 'SINGLE');
INSERT INTO "acm"."acm_app_action" ("id", "action_key", "is_trigger", "name", "description", "connector_id", "account_schema_id", "protocol", "action_type", "status", "config_schema", "create_time", "create_by", "update_time", "update_by", "tenant_id", "sample_output", "node_struct") VALUES (157, '1882031726140669950', 0, '创建或更新第三方用户信息（内置鉴权）', '用于三方系统创建和更新用户接口不相同的情况', 39, 35, 'PUSH_USER', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"请选择创建的请求类型\",\"display_name\":\"创建请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"createMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619266\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"创建的地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"createPath\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619267\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"更新的请求有类型和创建类型不一致时必填\",\"display_name\":\"更新请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"updateMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1737612672096\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"更新地址和创建地址不一致时必填\",\"display_name\":\"更新的地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"updatePath\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1737546450938\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619268\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"路径传惨使用该选项。使用 {} 标识地址参数\",\"display_name\":\"Path参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1739863993469\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"header\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619269\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Query参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619270\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"发起请求时的参数数据结构如下：\\n{\\n    \\\"k1\\\": \\\"v1\\\",\\n    \\\"k2\\\": \\\"v2\\\"\\n}\\n根据实际情况修改。\\n提交时的数据结构：确保提交的数据结构与上述示例完全一致。\",\"display_name\":\"请求体\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body\",\"page_control\":\"TEXTAREA\",\"required\":true,\"type\":\"OBJECT\",\"ui_id\":\"1737546057702\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"与请求体数据不一致时使用\\n发起请求时的参数数据结构如下：\\n{\\n    \\\"k1\\\": \\\"v1\\\",\\n    \\\"k2\\\": \\\"v2\\\"\\n}\\n根据实际情况修改。\\n提交时的数据结构：确保提交的数据结构与上述示例完全一致。\",\"display_name\":\"更新请求体\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"updateBody\",\"page_control\":\"TEXTAREA\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1741261237792\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"请求返回的类型。默认是JSON 如果返回字符串需要修改\",\"display_name\":\"返回类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"responseType\",\"option_values\":[\"JSON\",\"STRING\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1739868095014\",\"update\":true,\"value\":\"JSON\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"此输入框用于填写解析ID的表达式，帮助集成平台与第三方系统之间建立数据关联\\n1、选择前置节点：如果前置节点已获取，请直接选择。\\n2、编写表达式：根据返回结果编写解析ID的表达式。例如，若需从响应体中提取id字段，则应填写 return response.body.data.id;\",\"display_name\":\"解析Id的表达式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"idExpress\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1737545743752\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"此输入框用于填写解析unionid的表达式，帮助集成平台与第三方系统之间建立数据关联\\n1、选择前置节点：如果前置节点已获取，请直接选择。\\n2、编写表达式：根据返回结果编写解析openid的表达式。例如，若需从响应体中提取id字段，则应填写 return response.body.data.unionid;\",\"display_name\":\"解析UnionId的表达式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"unionIdExpress\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1738840918861\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"此输入框用于填写解析openid的表达式，帮助集成平台与第三方系统之间建立数据关联\\n1、选择前置节点：如果前置节点已获取，请直接选择。\\n2、编写表达式：根据返回结果编写解析openid的表达式。例如，若需从响应体中提取id字段，则应填写 return response.body.data.openid;\",\"display_name\":\"解析OpenId的表达式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"openIdExpress\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1738840909114\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"用于在更新时判断用户是否存在，并决定是否创建新用户\\n编写表达式：根据用户不存在的结果编写表达式，以判断是否需要创建新用户。\\n示例：例如，如果用户不存在则创建新用户，可以编写表达式 return response.body.code == 1002;\\n\",\"display_name\":\"用户不存在检查\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"createExpress\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1737545820983\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"用于在创建时如果用户已经存在是否更新用户\\n编写表达式：根据用户存在的结果编写表达式，以判断是否需要更新新用户。\\n示例：例如，如果用户存在则更新用户，可以编写表达式 return response.body.code == 1002;\\n\",\"display_name\":\"用户存在检查\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"updateExpress\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1737545909620\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"用于判断请求是否成功。一般情况为 return response.body.code == 0;\\n根据实际情况编写\",\"display_name\":\"判断请求成功表达式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"successCondition\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"FIX_VALUE\"}],\"type\":\"OBJECT\",\"ui_id\":\"1897614072701075458\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2025-01-22 19:45:05', 'admin', '2025-03-06 19:45:44', 'admin', 'iam', NULL, 'SINGLE');

-- 通讯录同步流化模板
INSERT INTO "acm"."acm_flow_instance" ("id", "name", "description", "flow_type", "status", "op_status", "error_msg", "run_start_time", "run_end_time", "api_key", "category", "create_time", "create_by", "update_time", "update_by", "tenant_id", "template", "is_deleted") VALUES (1892486974506704898, 'Http通讯录同步', NULL, NULL, 'IDLE', 'ONLINE', NULL, NULL, NULL, 'adf236cb203345088478543ac4e04087', '通讯录同步', '2025-02-20 16:10:30', 'admin', '2025-02-20 16:41:22', 'admin', 'iam', 1, 0);
INSERT INTO "acm"."acm_flow_node" ("id", "node_type", "parent_id", "next_id", "connector_id", "action_id", "action_key", "account_id", "flow_id", "description", "config", "input", "output", "status", "error_msg", "run_start_time", "run_end_time", "proxy_id", "create_time", "create_by", "update_time", "update_by", "tenant_id", "closed", "note") VALUES (1892486974661894145, 'StartEventNode', 0, 1892486974787723266, 39, 151, '1887778362112516090', NULL, 1892486974506704898, NULL, '{\"ui_id\":\"1887778854628663297\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"*************\",\"name\":\"type\",\"type\":\"STRING\",\"description\":\"异步：webhook接收到数据后，将直接返回接收成功的结果以及流的信息。 同步：Webhook接收到数据后，将等待连接流执行完成并将最后的执行结果返回给调用方（最长等待时间为10s，超时将返回为空）。请查看接口文档获得更多详细信息\",\"display_name\":\"连接流执行方式\",\"multi_valued\":false,\"value\":\"异步\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"同步\",\"异步\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, NULL, 'SUCCESS', NULL, NULL, NULL, NULL, '2025-02-20 16:10:30', 'admin', '2025-02-20 16:10:41', 'admin', 'iam', '0', NULL);
INSERT INTO "acm"."acm_flow_node" ("id", "node_type", "parent_id", "next_id", "connector_id", "action_id", "action_key", "account_id", "flow_id", "description", "config", "input", "output", "status", "error_msg", "run_start_time", "run_end_time", "proxy_id", "create_time", "create_by", "update_time", "update_by", "tenant_id", "closed", "note") VALUES (1892486974787723266, 'ActionNode', 0, 1892487796724506625, 39, 152, '1882032927489998852', NULL, 1892486974506704898, NULL, '{\"ui_id\":\"1890231663756222466\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"*************\",\"name\":\"thirdRootId\",\"type\":\"STRING\",\"description\":\"填写需要同步到下游真实存在的部门ID，如果不填写则使用创建连接器的值\",\"display_name\":\"下游根部门ID\",\"multi_valued\":false,\"value\":\"\",\"update\":true,\"required\":false,\"page_control\":\"TEXT\",\"option_values\":[],\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, NULL, 'SUCCESS', NULL, NULL, NULL, NULL, '2025-02-20 16:10:30', 'admin', '2025-02-20 16:10:57', 'admin', 'iam', '0', NULL);
INSERT INTO "acm"."acm_flow_node" ("id", "node_type", "parent_id", "next_id", "connector_id", "action_id", "action_key", "account_id", "flow_id", "description", "config", "input", "output", "status", "error_msg", "run_start_time", "run_end_time", "proxy_id", "create_time", "create_by", "update_time", "update_by", "tenant_id", "closed", "note") VALUES (1892486974984855554, 'EndNode', 0, NULL, NULL, NULL, NULL, NULL, 1892486974506704898, NULL, NULL, NULL, NULL, 'SUCCESS', NULL, NULL, NULL, NULL, '2025-02-20 16:10:30', 'admin', '2025-02-20 16:10:30', 'admin', 'iam', '0', NULL);
INSERT INTO "acm"."acm_flow_node" ("id", "node_type", "parent_id", "next_id", "connector_id", "action_id", "action_key", "account_id", "flow_id", "description", "config", "input", "output", "status", "error_msg", "run_start_time", "run_end_time", "proxy_id", "create_time", "create_by", "update_time", "update_by", "tenant_id", "closed", "note") VALUES (1892487796724506625, 'ActionNode', 0, 1892490769701707778, 39, 155, '1881899198532632579', NULL, 1892486974506704898, NULL, '{\"ui_id\":\"1882614466460438529\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, NULL, 'SUCCESS', NULL, NULL, NULL, NULL, '2025-02-20 16:13:46', 'admin', '2025-02-20 16:13:54', 'admin', 'iam', '0', NULL);
INSERT INTO "acm"."acm_flow_node" ("id", "node_type", "parent_id", "next_id", "connector_id", "action_id", "action_key", "account_id", "flow_id", "description", "config", "input", "output", "status", "error_msg", "run_start_time", "run_end_time", "proxy_id", "create_time", "create_by", "update_time", "update_by", "tenant_id", "closed", "note") VALUES (1892490769701707778, 'ActionNode', 0, 1892493486511792130, 39, 158, '1887801420277784579', NULL, 1892486974506704898, NULL, '{\"ui_id\":\"1887801487747358722\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, NULL, 'SUCCESS', NULL, NULL, NULL, NULL, '2025-02-20 16:25:35', 'admin', '2025-02-20 16:25:50', 'admin', 'iam', '0', NULL);
INSERT INTO "acm"."acm_flow_node" ("id", "node_type", "parent_id", "next_id", "connector_id", "action_id", "action_key", "account_id", "flow_id", "description", "config", "input", "output", "status", "error_msg", "run_start_time", "run_end_time", "proxy_id", "create_time", "create_by", "update_time", "update_by", "tenant_id", "closed", "note") VALUES (1892490849338957826, 'ActionNode', 1892490769701707778, NULL, 39, 159, '1887799845358575619', NULL, 1892486974506704898, NULL, '{\"ui_id\":\"1892493229843193858\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1699309157332619266\",\"name\":\"httpMethod\",\"type\":\"STRING\",\"description\":\"请选择创建的请求类型\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"value\":\"POST\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1699309157332619267\",\"name\":\"endpoint\",\"type\":\"STRING\",\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"value\":\"https://ip:port/path\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1699309157332619268\",\"name\":\"contentType\",\"type\":\"STRING\",\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"value\":\"application/json; charset=UTF-8\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readonly\"},{\"ui_id\":\"1738921709264\",\"name\":\"path\",\"type\":\"OBJECT\",\"description\":\"路径传惨使用该选项。使用 {} 标识地址参数\",\"display_name\":\"Path参数\",\"multi_valued\":false,\"value\":{},\"update\":true,\"required\":false,\"page_control\":\"EXTEND_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1699309157332619269\",\"name\":\"header\",\"type\":\"OBJECT\",\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"value\":{},\"update\":true,\"required\":false,\"page_control\":\"EXTEND_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1699309157332619270\",\"name\":\"query\",\"type\":\"OBJECT\",\"description\":\"\",\"display_name\":\"Query参数\",\"multi_valued\":false,\"value\":{},\"update\":true,\"required\":false,\"page_control\":\"EXTEND_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1699309157332619271\",\"name\":\"body\",\"type\":\"OBJECT\",\"description\":\"按照三方接口文档提交即可\",\"display_name\":\"Body参数\",\"multi_valued\":false,\"value\":\"\",\"update\":true,\"required\":false,\"page_control\":\"TEXTAREA\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1739868613926\",\"name\":\"responseType\",\"type\":\"STRING\",\"description\":\"请求返回的类型。默认是JSON 如果返回字符串需要修改\",\"display_name\":\"返回类型\",\"multi_valued\":false,\"value\":\"JSON\",\"update\":true,\"required\":false,\"page_control\":\"SELECT\",\"option_values\":[\"JSON\",\"STRING\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1699309157332619123\",\"name\":\"successCondition\",\"type\":\"STRING\",\"description\":\"用于判断请求是否成功。一般情况为 return response.body.code == 0; 根据实际情况编写\",\"display_name\":\"删除成功判断\",\"multi_valued\":false,\"value\":\"return response.body.code == 0;\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, NULL, 'SUCCESS', NULL, NULL, NULL, NULL, '2025-02-20 16:25:54', 'admin', '2025-02-20 16:36:19', 'admin', 'iam', '0', NULL);
INSERT INTO "acm"."acm_flow_node" ("id", "node_type", "parent_id", "next_id", "connector_id", "action_id", "action_key", "account_id", "flow_id", "description", "config", "input", "output", "status", "error_msg", "run_start_time", "run_end_time", "proxy_id", "create_time", "create_by", "update_time", "update_by", "tenant_id", "closed", "note") VALUES (1892493486511792130, 'ActionNode', 0, 1892493750388039681, 39, 161, '1887801760695885827', NULL, 1892486974506704898, NULL, '{\"ui_id\":\"1887802026677673986\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, NULL, 'SUCCESS', NULL, NULL, NULL, NULL, '2025-02-20 16:36:23', 'admin', '2025-02-20 16:36:38', 'admin', 'iam', '0', NULL);
INSERT INTO "acm"."acm_flow_node" ("id", "node_type", "parent_id", "next_id", "connector_id", "action_id", "action_key", "account_id", "flow_id", "description", "config", "input", "output", "status", "error_msg", "run_start_time", "run_end_time", "proxy_id", "create_time", "create_by", "update_time", "update_by", "tenant_id", "closed", "note") VALUES (1892493562009264129, 'ActionNode', 1892493486511792130, NULL, 39, 162, '1887799285637095430', NULL, 1892486974506704898, NULL, '{\"ui_id\":\"1892492654116249601\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1699309157332619266\",\"name\":\"httpMethod\",\"type\":\"STRING\",\"description\":\"请选择请求类型\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"value\":\"POST\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1699309157332619267\",\"name\":\"endpoint\",\"type\":\"STRING\",\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"value\":\"https://ip:port/path\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1699309157332619268\",\"name\":\"contentType\",\"type\":\"STRING\",\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"value\":\"application/json; charset=UTF-8\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readonly\"},{\"ui_id\":\"1738921774703\",\"name\":\"path\",\"type\":\"OBJECT\",\"description\":\"路径传惨使用该选项。使用 {} 标识地址参数\",\"display_name\":\"Path参数\",\"multi_valued\":false,\"value\":{},\"update\":true,\"required\":false,\"page_control\":\"EXTEND_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1699309157332619269\",\"name\":\"header\",\"type\":\"OBJECT\",\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"value\":{},\"update\":true,\"required\":false,\"page_control\":\"EXTEND_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1699309157332619270\",\"name\":\"query\",\"type\":\"OBJECT\",\"description\":\"\",\"display_name\":\"Query参数\",\"multi_valued\":false,\"value\":{},\"update\":true,\"required\":false,\"page_control\":\"EXTEND_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1699309157332619271\",\"name\":\"body\",\"type\":\"OBJECT\",\"description\":\"按照三方接口文档需要格式提交即可\",\"display_name\":\"Body参数\",\"multi_valued\":false,\"value\":\"\",\"update\":true,\"required\":false,\"page_control\":\"TEXTAREA\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1739868515827\",\"name\":\"responseType\",\"type\":\"STRING\",\"description\":\"请求返回的类型。默认是JSON 如果返回字符串需要修改\",\"display_name\":\"返回类型\",\"multi_valued\":false,\"value\":\"JSON\",\"update\":true,\"required\":false,\"page_control\":\"SELECT\",\"option_values\":[\"JSON\",\"STRING\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1699309157332619123\",\"name\":\"successCondition\",\"type\":\"STRING\",\"description\":\"用于判断请求是否成功。一般情况为 return response.body.code == 0; 根据实际情况编写\",\"display_name\":\"删除成功判断\",\"multi_valued\":false,\"value\":\"return response.body.code == 0;\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, NULL, 'SUCCESS', NULL, NULL, NULL, NULL, '2025-02-20 16:36:41', 'admin', '2025-02-20 16:37:23', 'admin', 'iam', '0', NULL);
INSERT INTO "acm"."acm_flow_node" ("id", "node_type", "parent_id", "next_id", "connector_id", "action_id", "action_key", "account_id", "flow_id", "description", "config", "input", "output", "status", "error_msg", "run_start_time", "run_end_time", "proxy_id", "create_time", "create_by", "update_time", "update_by", "tenant_id", "closed", "note") VALUES (1892493750388039681, 'ActionNode', 0, 1892486974984855554, 39, 164, '1888176546500485123', NULL, 1892486974506704898, NULL, '{\"ui_id\":\"1888176624174800897\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, NULL, 'SUCCESS', NULL, NULL, NULL, NULL, '2025-02-20 16:37:26', 'admin', '2025-02-20 16:37:32', 'admin', 'iam', '0', NULL);
INSERT INTO "acm"."acm_flow_node" ("id", "node_type", "parent_id", "next_id", "connector_id", "action_id", "action_key", "account_id", "flow_id", "description", "config", "input", "output", "status", "error_msg", "run_start_time", "run_end_time", "proxy_id", "create_time", "create_by", "update_time", "update_by", "tenant_id", "closed", "note") VALUES (1897502635532378114, 'ActionNode', 1892486974787723266, NULL, 39, 153, '1882311767479209989', NULL, 1892486974506704898, NULL, '{\"ui_id\":\"1897279361290248194\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1699309157332619266\",\"name\":\"createMethod\",\"type\":\"STRING\",\"description\":\"请选择创建的请求类型\",\"display_name\":\"创建请求类型\",\"multi_valued\":false,\"value\":\"POST\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1699309157332619267\",\"name\":\"createPath\",\"type\":\"STRING\",\"description\":\"请填写完整的接口地址\",\"display_name\":\"创建的地址\",\"multi_valued\":false,\"value\":\"https://ip:port/path\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1737612672096\",\"name\":\"updateMethod\",\"type\":\"STRING\",\"description\":\"更新的请求有类型和创建类型不一致时必填\",\"display_name\":\"更新请求类型\",\"multi_valued\":false,\"update\":true,\"required\":false,\"page_control\":\"SELECT\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1737546450938\",\"name\":\"updatePath\",\"type\":\"STRING\",\"description\":\"更新地址和创建地址不一致时必填\",\"display_name\":\"更新的地址\",\"multi_valued\":false,\"update\":true,\"required\":false,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1699309157332619268\",\"name\":\"contentType\",\"type\":\"STRING\",\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"value\":\"application/json; charset=UTF-8\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readonly\"},{\"ui_id\":\"1739864096037\",\"name\":\"path\",\"type\":\"OBJECT\",\"description\":\"路径传惨使用该选项。使用 {} 标识地址参数\",\"display_name\":\"Path参数\",\"multi_valued\":false,\"value\":{},\"update\":true,\"required\":false,\"page_control\":\"EXTEND_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1699309157332619269\",\"name\":\"header\",\"type\":\"OBJECT\",\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"value\":{},\"update\":true,\"required\":false,\"page_control\":\"EXTEND_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1699309157332619270\",\"name\":\"query\",\"type\":\"OBJECT\",\"description\":\"\",\"display_name\":\"Query参数\",\"multi_valued\":false,\"value\":{},\"update\":true,\"required\":false,\"page_control\":\"EXTEND_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1737546057702\",\"name\":\"body\",\"type\":\"OBJECT\",\"description\":\"发起请求时的参数数据结构如下：\\n       {\\n        \\\"k1\\\": \\\"v1\\\",\\n        \\\"k2\\\": \\\"v2\\\"\\n      }\\n 可根据实际情况修改。 \\n提交时的数据结构：确保提交的数据结构与上述示例完全一致。\",\"display_name\":\"请求体\",\"multi_valued\":false,\"value\":\"{}\",\"update\":true,\"required\":true,\"page_control\":\"TEXTAREA\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1741181591680\",\"name\":\"updateBody\",\"type\":\"OBJECT\",\"description\":\"更新参数和创建参数不一致的情况\\n发起请求时的参数数据结构如下：\\n       {\\n        \\\"k1\\\": \\\"v1\\\",\\n        \\\"k2\\\": \\\"v2\\\"\\n      }\\n可根据实际情况修改。\\n提交时的数据结构：确保提交的数据结构与上述示例完全一致。\",\"display_name\":\"更新请求体\",\"multi_valued\":false,\"update\":true,\"required\":false,\"page_control\":\"TEXTAREA\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1739868315362\",\"name\":\"responseType\",\"type\":\"STRING\",\"description\":\"请求返回的类型。默认是JSON 如果返回字符串需要修改\",\"display_name\":\"返回类型\",\"multi_valued\":false,\"value\":\"JSON\",\"update\":true,\"required\":false,\"page_control\":\"SELECT\",\"option_values\":[\"JSON\",\"STRING\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1737545743752\",\"name\":\"idExpress\",\"type\":\"STRING\",\"description\":\"此输入框用于填写解析ID的表达式，帮助集成平台与第三方系统之间建立数据关联\\n 1、选择前置节点：如果前置节点已获取，请直接选择。\\n 2、编写表达式：根据返回结果编写解析ID的表达式。例如，若需从响应体中提取id字段，则应填写 return response.body.data.id;\",\"display_name\":\"解析Id的表达式\",\"multi_valued\":false,\"value\":\"return response.body.data.id;\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1737545820983\",\"name\":\"createExpress\",\"type\":\"STRING\",\"description\":\"用于在更新时判断部门是否存在，并决定是否创建新部门 \\n编写表达式：根据部门不存在的结果编写表达式，以判断是否需要创建新部门。 \\n示例：如果部门不存在则创建新部门，可以编写表达式 return response.body.code == 1002;\",\"display_name\":\"部门不存在检查\",\"multi_valued\":false,\"value\":\"return response.body.code == 0;\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1737545909620\",\"name\":\"updateExpress\",\"type\":\"STRING\",\"description\":\"用于在创建时如果部门已经存在是否更新部门 \\n编写表达式：根据部门存在的结果编写表达式，以判断是否需要更新新部门。 \\n示例：如果部门存在则更新部门，可以编写表达式 return response.body.code == 1002;\",\"display_name\":\"部门存在检查\",\"multi_valued\":false,\"value\":\"return response.body.code == 0;\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"*************\",\"name\":\"successCondition\",\"type\":\"STRING\",\"description\":\"用于判断请求是否成功。一般情况为 return response.body.code == 0; 根据实际情况编写\",\"display_name\":\"判断请求成功表达式\",\"multi_valued\":false,\"value\":\"return response.body.code == 0;\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, NULL, 'SUCCESS', NULL, NULL, NULL, NULL, '2025-03-06 12:20:57', 'admin', '2025-03-06 12:21:49', 'admin', 'iam', '0', NULL);
INSERT INTO "acm"."acm_flow_node" ("id", "node_type", "parent_id", "next_id", "connector_id", "action_id", "action_key", "account_id", "flow_id", "description", "config", "input", "output", "status", "error_msg", "run_start_time", "run_end_time", "proxy_id", "create_time", "create_by", "update_time", "update_by", "tenant_id", "closed", "note") VALUES (1897502900025188353, 'ActionNode', 1892487796724506625, NULL, 39, 156, '1882031726140669955', NULL, 1892486974506704898, NULL, '{\"ui_id\":\"1897279141248671745\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1699309157332619266\",\"name\":\"createMethod\",\"type\":\"STRING\",\"description\":\"请选择创建的请求类型\",\"display_name\":\"创建请求类型\",\"multi_valued\":false,\"value\":\"POST\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1699309157332619267\",\"name\":\"createPath\",\"type\":\"STRING\",\"description\":\"请填写完整的接口地址\",\"display_name\":\"创建的地址\",\"multi_valued\":false,\"value\":\"https://ip:port/path\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1737612672096\",\"name\":\"updateMethod\",\"type\":\"STRING\",\"description\":\"更新的请求有类型和创建类型不一致时必填\",\"display_name\":\"更新请求类型\",\"multi_valued\":false,\"update\":true,\"required\":false,\"page_control\":\"SELECT\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1737546450938\",\"name\":\"updatePath\",\"type\":\"STRING\",\"description\":\"更新地址和创建地址不一致时必填\",\"display_name\":\"更新的地址\",\"multi_valued\":false,\"update\":true,\"required\":false,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1699309157332619268\",\"name\":\"contentType\",\"type\":\"STRING\",\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"value\":\"application/json; charset=UTF-8\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readonly\"},{\"ui_id\":\"1739863993469\",\"name\":\"path\",\"type\":\"OBJECT\",\"description\":\"路径传惨使用该选项。使用 {} 标识地址参数\",\"display_name\":\"Path参数\",\"multi_valued\":false,\"value\":{},\"update\":true,\"required\":false,\"page_control\":\"EXTEND_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1699309157332619269\",\"name\":\"header\",\"type\":\"OBJECT\",\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"value\":{},\"update\":true,\"required\":false,\"page_control\":\"EXTEND_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1699309157332619270\",\"name\":\"query\",\"type\":\"OBJECT\",\"description\":\"\",\"display_name\":\"Query参数\",\"multi_valued\":false,\"value\":{},\"update\":true,\"required\":false,\"page_control\":\"EXTEND_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1737546057702\",\"name\":\"body\",\"type\":\"OBJECT\",\"description\":\"发起请求时的参数数据结构如下：\\n       {\\n        \\\"k1\\\": \\\"v1\\\",\\n        \\\"k2\\\": \\\"v2\\\"\\n      }\\n 可根据实际情况修改。 \\n提交时的数据结构：确保提交的数据结构与上述示例完全一致。\",\"display_name\":\"请求体\",\"multi_valued\":false,\"value\":\"{}\",\"update\":true,\"required\":true,\"page_control\":\"TEXTAREA\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1741181517181\",\"name\":\"updateBody\",\"type\":\"OBJECT\",\"description\":\"更新参数和创建参数不一致的情况\\n发起请求时的参数数据结构如下：\\n       {\\n        \\\"k1\\\": \\\"v1\\\",\\n        \\\"k2\\\": \\\"v2\\\"\\n      }\\n可根据实际情况修改。\\n提交时的数据结构：确保提交的数据结构与上述示例完全一致。\",\"display_name\":\"更新请求体\",\"multi_valued\":false,\"update\":true,\"required\":false,\"page_control\":\"TEXTAREA\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1739868095014\",\"name\":\"responseType\",\"type\":\"STRING\",\"description\":\"请求返回的类型。默认是JSON 如果返回字符串需要修改\",\"display_name\":\"返回类型\",\"multi_valued\":false,\"value\":\"JSON\",\"update\":true,\"required\":false,\"page_control\":\"SELECT\",\"option_values\":[\"JSON\",\"STRING\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1737545743752\",\"name\":\"idExpress\",\"type\":\"STRING\",\"description\":\"此输入框用于填写解析ID的表达式，帮助集成平台与第三方系统之间建立数据关联\\n1、选择前置节点：如果前置节点已获取，请直接选择。\\n2、编写表达式：根据返回结果编写解析ID的表达式。例如，若需从响应体中提取id字段，则应填写 return response.body.data.id;\",\"display_name\":\"解析Id的表达式\",\"multi_valued\":false,\"value\":\"return response.body.data.id;\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1738840918861\",\"name\":\"unionIdExpress\",\"type\":\"STRING\",\"description\":\"此输入框用于填写解析unionid的表达式，帮助集成平台与第三方系统之间建立数据关联\\n1、选择前置节点：如果前置节点已获取，请直接选择。\\n2、编写表达式：根据返回结果编写解析openid的表达式。例如，若需从响应体中提取id字段，则应填写 return response.body.data.unionid;\",\"display_name\":\"解析UnionId的表达式\",\"multi_valued\":false,\"update\":true,\"required\":false,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1738840909114\",\"name\":\"openIdExpress\",\"type\":\"STRING\",\"description\":\"此输入框用于填写解析openid的表达式，帮助集成平台与第三方系统之间建立数据关联\\n1、选择前置节点：如果前置节点已获取，请直接选择。\\n2、编写表达式：根据返回结果编写解析openid的表达式。例如，若需从响应体中提取id字段，则应填写 return response.body.data.openid;\",\"display_name\":\"解析OpenId的表达式\",\"multi_valued\":false,\"update\":true,\"required\":false,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1737545820983\",\"name\":\"createExpress\",\"type\":\"STRING\",\"description\":\"用于在更新时判断用户是否存在，并决定是否创建新用户\\n编写表达式：根据用户不存在的结果编写表达式，以判断是否需要创建新用户。\\n示例：例如，如果用户不存在则创建新用户，可以编写表达式 return response.body.code == 1002;\\n\",\"display_name\":\"用户不存在检查\",\"multi_valued\":false,\"value\":\"return response.body.code == 20;\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1737545909620\",\"name\":\"updateExpress\",\"type\":\"STRING\",\"description\":\"用于在创建时如果用户已经存在是否更新用户\\n编写表达式：根据用户存在的结果编写表达式，以判断是否需要更新新用户。\\n示例：例如，如果用户存在则更新用户，可以编写表达式 return response.body.code == 1002;\\n\",\"display_name\":\"用户存在检查\",\"multi_valued\":false,\"value\":\"return response.body.code == 10;\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"*************\",\"name\":\"successCondition\",\"type\":\"STRING\",\"description\":\"用于判断请求是否成功。一般情况为 return response.body.code == 0;\\n根据实际情况编写\",\"display_name\":\"判断请求成功表达式\",\"multi_valued\":false,\"value\":\"return response.body.code == 0;\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, NULL, 'SUCCESS', NULL, NULL, NULL, NULL, '2025-03-06 12:22:00', 'admin', '2025-03-06 12:23:06', 'admin', 'iam', '0', NULL);


INSERT INTO "acm"."acm_app_account" ("id", "name", "description", "auth_type", "auth_schema", "create_time", "create_by", "update_time", "update_by", "tenant_id") VALUES (35, ' HTTP 账号', NULL, 'ADAPT', '{\"description\":\"HTTP 账号\",\"displayName\":\"HTTP 账号\",\"multiValued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"subParams\":[{\"description\":\"输入获取Token的地址\",\"displayName\":\"Access Token URL\",\"multiValued\":false,\"name\":\"endpoint\",\"type\":\"STRING\",\"required\":true,\"page_control\":\"TEXT\",\"valueType\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请求的query参数：\\n{\\n        \\\"k1\\\":\\\"v1\\\",\\n        \\\"k2\\\": \\\"v2\\\"\\n    }\",\"displayName\":\"Query参数\",\"multiValued\":false,\"name\":\"query\",\"type\":\"OBJECT\",\"required\":false,\"page_control\":\"FIXED_OBJ\",\"valueType\":\"JS_EXP\"},{\"description\":\"请求的Header参数：\\n{\\n        \\\"k1\\\":\\\"v1\\\",\\n        \\\"k2\\\": \\\"v2\\\"\\n    }\",\"displayName\":\"Header参数\",\"multiValued\":false,\"name\":\"header\",\"type\":\"OBJECT\",\"required\":false,\"page_control\":\"FIXED_OBJ\",\"valueType\":\"JS_EXP\"},{\"description\":\"请求的Body参数：\\n{\\n        \\\"k1\\\":\\\"v1\\\",\\n        \\\"k2\\\": \\\"v2\\\"\\n    }\",\"displayName\":\"Body参数\",\"multiValued\":false,\"name\":\"body\",\"type\":\"OBJECT\",\"required\":false,\"page_control\":\"FIXED_OBJ\",\"valueType\":\"JS_EXP\"},{\"description\":\"将获取到的token方法到header还是token\",\"displayName\":\"Token放入header\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"putHeader\",\"option_values\":[\"TRUE\",\"FALSE\"],\"value\":\"TRUE\",\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"token的key\",\"displayName\":\"Token放入Header的Key\",\"multiValued\":false,\"name\":\"key\",\"type\":\"STRING\",\"value\":\"access-token\",\"page_control\":\"TEXT\",\"required\":true,\"valueType\":\"FIX_VALUE\"},{\"description\":\"将获取到的Token通过Query传参\",\"displayName\":\"Token放入query\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"putQuery\",\"option_values\":[\"TRUE\",\"FALSE\"],\"value\":\"TRUE\",\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"token的key\",\"displayName\":\"Token放入Query的Key\",\"multiValued\":false,\"name\":\"queryKey\",\"type\":\"STRING\",\"value\":\"access-token\",\"page_control\":\"TEXT\",\"required\":true,\"valueType\":\"FIX_VALUE\"},{\"description\":\"从请求的返回信息中获取token的表达式。以return response 开头，后面根据接口文档补充\",\"displayName\":\"Token表达式\",\"multiValued\":false,\"name\":\"tokenExpression\",\"type\":\"STRING\",\"value\":\"return response.data;\",\"required\":true,\"page_control\":\"TEXT\",\"valueType\":\"FIX_VALUE\"},{\"description\":\"从请求的返回信息中获取token的表达式，以return response 开头，后面根据接口文档补充\",\"displayName\":\"Token过期表达式\",\"multiValued\":false,\"name\":\"tokenExpireExpression\",\"type\":\"STRING\",\"value\":\"return response.data;\",\"required\":true,\"page_control\":\"TEXT\",\"valueType\":\"FIX_VALUE\"}],\"type\":\"OBJECT\",\"valueType\":\"JS_EXP\"}', '2024-08-29 20:05:52', NULL, '2024-08-29 20:05:52', NULL, 'iam');

delete from "acm"."acm_app_connector"  where id = 30;
delete from"acm"."acm_app_action"  where connector_id = 30;



insert into "acm"."acm_app_action"(id,action_key,is_trigger,name,description,connector_id,account_schema_id,protocol,node_struct,config_schema,create_time,update_time,tenant_id) values(86,'http-loop',0,'自循环的HTTP请求','自循环HTTP请求，内置response变量，用于分页查询等场景',4,null,'LOOP_HTTP','LOOP','',now(),now(),'iam');
update "acm"."acm_app_action" set config_schema='{"description":"自循环的HTTP请求","display_name":"自循环的HTTP请求","multi_valued":false,"mutability":"readWrite","name":"root","page_control":"FIXED_OBJ","required":true,"sub_params":[{"description":"","display_name":"请求类型","multi_valued":false,"mutability":"readWrite","name":"httpMethod","option_values":["GET","POST","PUT","PATCH","DELETE"],"page_control":"SELECT","required":true,"type":"STRING","value_type":"FIX_VALUE"},{"description":"请填写完整的接口地址","display_name":"请求地址","multi_valued":false,"mutability":"readWrite","name":"endpoint","page_control":"TEXT","required":true,"type":"STRING","value_type":"ADAPTOR"},{"description":"","display_name":"应答编码","multi_valued":false,"mutability":"readWrite","name":"encoding","option_values":["UTF8","GBK"],"page_control":"SELECT","required":true,"type":"STRING","value_type":"FIX_VALUE"},{"description":"","display_name":"Header参数","multi_valued":false,"mutability":"readWrite","name":"header","page_control":"EXTEND_OBJ","required":false,"type":"OBJECT","value_type":"JS_EXP"},{"description":"","display_name":"Path参数","multi_valued":false,"mutability":"readWrite","name":"path","page_control":"EXTEND_OBJ","required":false,"type":"OBJECT","value_type":"JS_EXP"},{"description":"","display_name":"Query参数","multi_valued":false,"mutability":"readWrite","name":"query","page_control":"EXTEND_OBJ","required":false,"type":"OBJECT","value_type":"JS_EXP"},{"description":"Body参数传字符串时需用引号括起来","display_name":"Body参数","multi_valued":false,"mutability":"readWrite","name":"body","page_control":"TEXTAREA","required":false,"type":"OBJECT","value_type":"JS_EXP"},{"description":"循环执行的条件表达式，true继续循环，false结束循环","display_name":"循环条件","multi_valued":false,"mutability":"readWrite","name":"loop","page_control":"TEXT","required":true,"type":"BOOLEAN","value_type":"JS_EXP"}],"type":"OBJECT","value_type":"JS_EXP"}' where id=86;

insert into "acm"."acm_app_action"(id,action_key,is_trigger,name,description,connector_id,account_schema_id,protocol,node_struct,config_schema,create_time,update_time,tenant_id) values(112,'level-loop',0,'自循环的HTTP请求，并按层级输出','自循环HTTP请求，并按层级输出，内置response变量，用于部门分页查询等场景',4,null,'LEVEL_HTTP','LOOP','',now(),now(),'iam');
update "acm"."acm_app_action" set config_schema='{"description":"自循环的HTTP请求","display_name":"自循环的HTTP请求","multi_valued":false,"mutability":"readWrite","name":"root","page_control":"FIXED_OBJ","required":true,"sub_params":[{"description":"","display_name":"请求类型","multi_valued":false,"mutability":"readWrite","name":"httpMethod","option_values":["GET","POST","PUT","PATCH","DELETE"],"page_control":"SELECT","required":true,"type":"STRING","value_type":"FIX_VALUE"},{"description":"请填写完整的接口地址","display_name":"请求地址","multi_valued":false,"mutability":"readWrite","name":"endpoint","page_control":"TEXT","required":true,"type":"STRING","value_type":"ADAPTOR"},{"description":"","display_name":"应答编码","multi_valued":false,"mutability":"readWrite","name":"encoding","option_values":["UTF8","GBK"],"page_control":"SELECT","required":true,"type":"STRING","value_type":"FIX_VALUE"},{"description":"","display_name":"Header参数","multi_valued":false,"mutability":"readWrite","name":"header","page_control":"EXTEND_OBJ","required":false,"type":"OBJECT","value_type":"JS_EXP"},{"description":"","display_name":"Path参数","multi_valued":false,"mutability":"readWrite","name":"path","page_control":"EXTEND_OBJ","required":false,"type":"OBJECT","value_type":"JS_EXP"},{"description":"","display_name":"Query参数","multi_valued":false,"mutability":"readWrite","name":"query","page_control":"EXTEND_OBJ","required":false,"type":"OBJECT","value_type":"JS_EXP"},{"description":"Body参数传字符串时需用引号括起来","display_name":"Body参数","multi_valued":false,"mutability":"readWrite","name":"body","page_control":"TEXTAREA","required":false,"type":"OBJECT","value_type":"JS_EXP"},{"description":"循环执行的条件表达式，true继续循环，false结束循环","display_name":"循环条件","multi_valued":false,"mutability":"readWrite","name":"loop","page_control":"TEXT","required":true,"type":"BOOLEAN","value_type":"JS_EXP"},{"description":"通过js表达式从应答结果response中提取要输出的对象列表","display_name":"列表提取","multi_valued":true,"mutability":"readWrite","name":"levelList","page_control":"TEXT","required":true,"type":"OBJECT","value_type":"ADAPTOR"},{"description":"唯一标识该对象的字段名","display_name":"ID字段名","multi_valued":false,"mutability":"readWrite","name":"objId","page_control":"TEXT","required":true,"type":"STRING","value_type":"FIX_VALUE"},{"description":"该对象的父ID字段名","display_name":"父ID字段名","multi_valued":false,"mutability":"readWrite","name":"objPid","page_control":"TEXT","required":true,"type":"STRING","value_type":"FIX_VALUE"}],"type":"OBJECT","value_type":"JS_EXP"}' where id=112;

insert into "acm"."acm_app_account"(id,name,auth_type,auth_schema,create_time,update_time,tenant_id) values (34, 'AD/LDAP认证', 'LDAP', '{"description":"AD/LDAP认证配置","displayName":"AD/LDAP认证配置","multiValued":false,"mutability":"readWrite","name":"root","subParams":[{"description":"服务器地址：IP或域名。","displayName":"服务器地址","multiValued":false,"name":"host","type":"STRING","required":true,"page_control":"TEXT","valueType":"FIX_VALUE"},{"description":"示例：389，请填写AD/LDAP端口。","displayName":"端口","multiValued":false,"name":"port","type":"NUMBER","required":true,"page_control":"TEXT","valueType":"FIX_VALUE"},{"description":"是否开启TLS。","displayName":"TLS","multiValued":false,"name":"tls","type":"STRING","required":true,"option_values":["关闭","开启"],"page_control":"SELECT","valueType":"FIX_VALUE"},{"description":"","displayName":"BaseDN","multiValued":false,"name":"baseDn","type":"STRING","required":true,"page_control":"TEXT","valueType":"FIX_VALUE"},{"description":"管理员账号的DN。","displayName":"管理员账号","multiValued":false,"name":"username","type":"STRING","required":true,"page_control":"TEXT","valueType":"FIX_VALUE"},{"description":"管理员密码。","displayName":"密码","multiValued":false,"name":"password","type":"STRING","required":true,"page_control":"PASSWORD","valueType":"FIX_VALUE"}],"type":"OBJECT","valueType":"JS_EXP"}', now(), now(), 'iam');
insert into "acm"."acm_app_connector"(id, name, description, build_type, app_package, app_domain_category, ver_name, ver_number, create_time, update_time, tenant_id) values (36,'Windows AD','Windows AD','BuiltIn','ad','DATA_PROTOCOL','1.0',1,now(),now(),'iam');
insert into "acm"."acm_app_action"(id,action_key,is_trigger,name,description,connector_id,account_schema_id,protocol,node_struct,config_schema,create_time,update_time,tenant_id) values(118,'ad-search',0,'遍历AD部门和用户','根据BaseDN遍历全部部门和用户',36,34,'LOOP_AD','LOOP','',now(),now(),'iam');
update "acm"."acm_app_action" set config_schema='{"display_name":"root","multi_valued":false,"mutability":"readWrite","name":"root","page_control":"FIXED_OBJ","required":false,"type":"OBJECT","value_type":"JS_EXP","sub_params":[
           {"description":"根节点的BaseDN","display_name":"BaseDN","multi_valued":false,"mutability":"readWrite","name":"base_dn","page_control":"TEXT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"},
           {"description":"部门搜索条件","display_name":"部门搜索条件","multi_valued":false,"mutability":"readWrite","name":"dept_filter","value":"(objectclass=organizationalUnit)","page_control":"TEXT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"},
           {"description":"用户搜索条件","display_name":"用户搜索条件","multi_valued":false,"mutability":"readWrite","name":"user_filter","value":"(&(&(objectclass=user)(objectCategory=person))(!(userAccountControl:1.2.840.113556.1.4.803:=2)))","page_control":"TEXT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"},
           {"description":"遍历时是否输出部门信息","display_name":"输出部门","multi_valued":false,"mutability":"readWrite","name":"output_org","option_values":["是","否"],"page_control":"SELECT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"},
           {"description":"遍历时是否输出用户信息","display_name":"输出用户","multi_valued":false,"mutability":"readWrite","name":"output_user","option_values":["是","否"],"page_control":"SELECT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"}
           ]}' where id=118;

insert into "acm"."acm_app_connector"(id, name, description, build_type, app_package, app_domain_category, ver_name, ver_number, create_time, update_time, tenant_id) values (35,'LDAP','LDAP','BuiltIn','ldap','DATA_PROTOCOL','1.0',1,now(),now(),'iam');
insert into "acm"."acm_app_action"(id,action_key,is_trigger,name,description,connector_id,account_schema_id,protocol,node_struct,config_schema,create_time,update_time,tenant_id) values(119,'search-dept-byDN',0,'按DN逐级遍历部门','适用于通过DN确定部门上下级关系的场景',35,34,'LOOP_DEPT_BY_DN','LOOP','',now(),now(),'iam');
update "acm"."acm_app_action" set config_schema='{"display_name":"root","multi_valued":false,"mutability":"readWrite","name":"root","page_control":"FIXED_OBJ","required":false,"type":"OBJECT","value_type":"JS_EXP","sub_params":[
           {"description":"搜索根节点的BaseDN","display_name":"BaseDN","multi_valued":false,"mutability":"readWrite","name":"base_dn","page_control":"TEXT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"},
           {"description":"部门搜索条件","display_name":"部门搜索条件","multi_valued":false,"mutability":"readWrite","name":"dept_filter","value":"(objectclass=organizationalUnit)","page_control":"TEXT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"},
           {"description":"作为部门ID的属性名，一般用entryUUID","display_name":"部门ID属性名","multi_valued":false,"mutability":"readWrite","name":"dept_id_name","value":"entryUUID","page_control":"TEXT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"},
           {"description":"作为部门父ID的逻辑属性名，一般用parentEntryUUID","display_name":"部门父ID属性名","multi_valued":false,"mutability":"readWrite","name":"dept_pid_name","value":"parentEntryUUID","page_control":"TEXT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"}
           ]}' where id=119;

insert into "acm"."acm_app_action"(id,action_key,is_trigger,name,description,connector_id,account_schema_id,protocol,node_struct,config_schema,create_time,update_time,tenant_id) values(120,'search-all-dept',0,'按BaseDN一次性搜索全部部门','适用于通过指定部门父ID属性确定父部门的场景',35,34,'LOOP_LDAP_DEPT','LOOP','',now(),now(),'iam');
update "acm"."acm_app_action" set config_schema='{"display_name":"root","multi_valued":false,"mutability":"readWrite","name":"root","page_control":"FIXED_OBJ","required":false,"type":"OBJECT","value_type":"JS_EXP","sub_params":[
           {"description":"搜索根节点的BaseDN","display_name":"BaseDN","multi_valued":false,"mutability":"readWrite","name":"base_dn","page_control":"TEXT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"},
           {"description":"部门搜索条件","display_name":"部门搜索条件","multi_valued":false,"mutability":"readWrite","name":"dept_filter","value":"(objectclass=organizationalUnit)","page_control":"TEXT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"},
           {"description":"作为部门ID的属性名，一般用entryUUID","display_name":"部门ID属性名","multi_valued":false,"mutability":"readWrite","name":"dept_id_name","value":"entryUUID","page_control":"TEXT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"},
           {"description":"作为部门父ID的属性名，一般用parentEntryUUID","display_name":"部门父ID属性名","multi_valued":false,"mutability":"readWrite","name":"dept_pid_name","value":"parentEntryUUID","page_control":"TEXT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"}
           ]}' where id=120;

insert into "acm"."acm_app_action"(id,action_key,is_trigger,name,description,connector_id,account_schema_id,protocol,node_struct,config_schema,create_time,update_time,tenant_id) values(121,'search-all-user',0,'按BaseDN一次性搜索全部用户','适用于用户不直接挂在部门DN下的场景',35,34,'LOOP_LDAP_USER','LOOP','',now(),now(),'iam');
update "acm"."acm_app_action" set config_schema='{"display_name":"root","multi_valued":false,"mutability":"readWrite","name":"root","page_control":"FIXED_OBJ","required":false,"type":"OBJECT","value_type":"JS_EXP","sub_params":[
           {"description":"搜索根节点的BaseDN","display_name":"BaseDN","multi_valued":false,"mutability":"readWrite","name":"base_dn","page_control":"TEXT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"},
           {"description":"用户搜索条件","display_name":"用户搜索条件","multi_valued":false,"mutability":"readWrite","name":"user_filter","value":"(objectclass=organizationalPerson)","page_control":"TEXT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"}
           ]}' where id=121;

insert into "acm"."acm_app_action"(id,action_key,is_trigger,name,description,connector_id,account_schema_id,protocol,node_struct,config_schema,create_time,update_time,tenant_id) values(122,'search-all-byDN',0,'按DN逐级遍历部门和用户','适用于通过DN确定部门上下级、部门和用户关系的场景',35,34,'LOOP_ALL_BY_DN','LOOP','',now(),now(),'iam');
update "acm"."acm_app_action" set config_schema='{"display_name":"root","multi_valued":false,"mutability":"readWrite","name":"root","page_control":"FIXED_OBJ","required":false,"type":"OBJECT","value_type":"JS_EXP","sub_params":[
           {"description":"根节点的BaseDN","display_name":"BaseDN","multi_valued":false,"mutability":"readWrite","name":"base_dn","page_control":"TEXT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"},
           {"description":"部门搜索条件","display_name":"部门搜索条件","multi_valued":false,"mutability":"readWrite","name":"dept_filter","value":"(objectclass=organizationalUnit)","page_control":"TEXT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"},
           {"description":"用户搜索条件","display_name":"用户搜索条件","multi_valued":false,"mutability":"readWrite","name":"user_filter","value":"(objectclass=organizationalPerson)","page_control":"TEXT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"},
           {"description":"遍历时是否输出部门信息","display_name":"输出部门","multi_valued":false,"mutability":"readWrite","name":"output_org","option_values":["是","否"],"page_control":"SELECT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"},
           {"description":"遍历时是否输出用户信息","display_name":"输出用户","multi_valued":false,"mutability":"readWrite","name":"output_user","option_values":["是","否"],"page_control":"SELECT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"},
           {"description":"作为部门ID的属性名，一般用entryUUID","display_name":"部门ID属性名","multi_valued":false,"mutability":"readWrite","name":"dept_id_name","value":"entryUUID","page_control":"TEXT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"},
           {"description":"作为部门父ID的逻辑属性名，一般用parentEntryUUID","display_name":"部门父ID属性名","multi_valued":false,"mutability":"readWrite","name":"dept_pid_name","value":"parentEntryUUID","page_control":"TEXT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"},
           {"description":"作为用户所属部门的逻辑属性名，用来记录用户所属的部门ID","display_name":"用户所属部门ID属性名","multi_valued":false,"mutability":"readWrite","name":"user_dept_name","value":"department_ids","page_control":"TEXT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"}
           ]}' where id=122;


-- AD通讯录集成模版
INSERT INTO "acm"."acm_flow_instance" ("id", "name", "description", "flow_type", "status", "error_msg", "run_start_time", "run_end_time", "create_time", "create_by", "update_time", "update_by", "tenant_id", "op_status", "api_key", "category", "template", "is_deleted") VALUES (1887751465322188801, 'AD通讯录集成', 'AD通讯录集成模版', 1, 'IDLE', NULL, NULL, NULL, '2025-02-07 14:33:17', 'anonymousUser', '2025-02-07 18:08:17', 'admin', 'iam', 'ONLINE', '1c31d595e02a41c8a2ab307e56d66dda', '通讯录集成', 1, 0);
INSERT INTO "acm"."acm_flow_node" ("id", "node_type", "next_id", "action_id", "action_key", "account_id", "flow_id", "description", "config", "input", "output", "status", "error_msg", "run_start_time", "run_end_time", "create_time", "create_by", "update_time", "update_by", "tenant_id", "connector_id", "parent_id", "proxy_id", "closed", "note") VALUES (1887751466108653569, 'StartEventNode', 1887770122504630274, 81, 'user_sync', NULL, 1887751465322188801, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1851605414348050433\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1851605414348050434\",\"option_values\":[\"异步\",\"同步\"],\"description\":\"异步：
webhook接收到数据后，将直接返回接收成功的结果以及流的信息。\\n同步：Webhook接收到数据后，将等待连接流执行完成并将最后的执行结果返回给调用方（最长等待时间为10s，超时将返回为空）。请查看接口文档获得更多详细信息\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"连接流执行方式\",\"required\":true,\"name\":\"type\",\"mutability\":\"readWrite\",\"value\":\"异步\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1851605414348050435\",\"name\":\"value.org_total\",\"description\":\"预估总的外部部门个数，用于估算集成进度\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"预估部门个数\",\"value\":\"300\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1851605414348050436\",\"name\":\"value.user_total\",\"description\":\"预估总的外部用户个数，用于估算集成进度\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"预估用户个数\",\"value\":\"1000\",\"required\":true,\"page_control\":\"TEXT\"}],\"name\":\"root\",\"description\":\"启动通讯录集成\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"启动通讯录集成\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', NULL, '{\"hook_url\":null,\"sync\":false,\"type\":\"异步\",\"value\":{\"org_total\":300,\"user_total\":1000}}', 'SUCCESS', 'OK', '2025-02-07 14:54:54', '2025-02-07 14:54:54', '2025-02-07 14:33:17', 'anonymousUser', '2025-02-07 15:47:10', 'admin', 'iam', 31, 0, NULL, '0', NULL);
INSERT INTO "acm"."acm_flow_node" ("id", "node_type", "next_id", "action_id", "action_key", "account_id", "flow_id", "description", "config", "input", "output", "status", "error_msg", "run_start_time", "run_end_time", "create_time", "create_by", "update_time", "update_by", "tenant_id", "connector_id", "parent_id", "proxy_id", "closed", "note") VALUES (1887751466108653571, 'EndNode', NULL, NULL, NULL, NULL, 1887751465322188801, NULL, NULL, NULL, NULL, 'IDLE', NULL, NULL, NULL, '2025-02-07 14:33:17', 'anonymousUser', '2025-02-07 14:33:17', 'anonymousUser', 'iam', NULL, 0, NULL, '0', NULL);
INSERT INTO "acm"."acm_flow_node" ("id", "node_type", "next_id", "action_id", "action_key", "account_id", "flow_id", "description", "config", "input", "output", "status", "error_msg", "run_start_time", "run_end_time", "create_time", "create_by", "update_time", "update_by", "tenant_id", "connector_id", "parent_id", "proxy_id", "closed", "note") VALUES (1887751466108653572, 'ActionNode', 1887751466108653571, 89, 'complete_user_sync', NULL, 1887751465322188801, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1851607623072731137\",\"sub_params\":[],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', NULL, '{\"code\":\"0\",\"data\":null,\"message\":\"SUCCESS\"}', 'INVALID', 'OK', '2024-12-09 13:52:38', '2024-12-09 13:52:38', '2025-02-07 14:33:17', 'anonymousUser', '2025-02-07 14:33:17', 'anonymousUser', 'iam', 31, 0, NULL, '0', NULL);
INSERT INTO "acm"."acm_flow_node" ("id", "node_type", "next_id", "action_id", "action_key", "account_id", "flow_id", "description", "config", "input", "output", "status", "error_msg", "run_start_time", "run_end_time", "create_time", "create_by", "update_time", "update_by", "tenant_id", "connector_id", "parent_id", "proxy_id", "closed", "note") VALUES (1887770122504630274, 'ActionNode', 1887751466108653572, 118, 'ad-search', 1882350888685322241, 1887751465322188801, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1887770134112776193\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1887770134112776194\",\"name\":\"base_dn\",\"description\":\"根节点的BaseDN\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"BaseDN\",\"value\":\"OU=vic_sso1,DC=digital,DC=com\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1887770134112776195\",\"name\":\"dept_filter\",\"description\":\"部门搜索条件\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"部门搜索条件\",\"value\":\"(objectclass=organizationalUnit)\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1887770134112776196\",\"name\":\"user_filter\",\"description\":\"用户搜索条件\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"用户搜索条件\",\"value\":\"(&(&(objectclass=user)(objectCategory=person))(!(userAccountControl:1.2.840.113556.1.4.803:=2)))\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1887770134112776197\",\"option_values\":[\"是\",\"否\"],\"description\":\"遍历时是
否输出部门信息\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"输出部门\",\"required\":true,\"name\":\"output_org\",\"mutability\":\"readWrite\",\"value\":\"是\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1887770134112776198\",\"option_values\":[\"是\",\"否\"],\"description\":\"遍历时是否输出用户
信息\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"输出用户\",\"required\":true,\"name\":\"output_user\",\"mutability\":\"readWrite\",\"value\":\"是\",\"page_control\":\"SELECT\"}],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', NULL, '{\"current\":{\"dept\":[{\"whenCreated\":\"20250117102934.0Z\",\"uSNChanged\":\"825887\",\"ou\":\"子部门\",\"instanceType\":\"4\",\"objectClass\":\"top\",\"distinguishedName\":\"OU=子部门,OU=vic_sso1,DC=digital,DC=com\",\"dn\":\"OU=子部门,OU=vic_sso1,DC=digital,DC=com\",\"whenChanged\":\"20250117102934.0Z\",\"parentObjectGUID\":\"8dcbb744-**************-7db4a0f830c0\",\"objectGUID\":\"bddc54ad-b007-465a-87c0-43413ad2374b\",\"name\":\"子部门\",\"dSCorePropagationData\":\"**************.0Z\",\"uSNCreated\":\"825887\",\"objectCategory\":\"CN=Organizational-Unit,CN=Schema,CN=Configuration,DC=digital,DC=com\"},{\"whenCreated\":\"**************.0Z\",\"uSNChanged\":\"827643\",\"ou\":\"AD210-Proxy\",\"instanceType\":\"4\",\"objectClass\":\"top\",\"distinguishedName\":\"OU=AD210-Proxy,OU=vic_sso1,DC=digital,DC=com\",\"dn\":\"OU=AD210-Proxy,OU=vic_sso1,DC=digital,DC=com\",\"whenChanged\":\"**************.0Z\",\"parentObjectGUID\":\"8dcbb744-**************-7db4a0f830c0\",\"objectGUID\":\"2f0f3557-15ce-4d03-871c-27d1617aae8b\",\"name\":\"AD210-Proxy\",\"dSCorePropagationData\":\"**************.0Z\",\"uSNCreated\":\"827643\",\"objectCategory\":\"CN=Organizational-Unit,CN=Schema,CN=Configuration,DC=digital,DC=com\"}],\"user\":[{\"lastLogoff\":\"0\",\"logonCount\":\"0\",\"accountExpires\":\"9223372036854775807\",\"displayName\":\"vicuser1\",\"distinguishedName\":\"CN=vicuser1,OU=vic_sso1,DC=digital,DC=com\",\"dn\":\"CN=vicuser1,OU=vic_sso1,DC=digital,DC=com\",\"countryCode\":\"0\",\"objectGUID\":\"8fb3b990-2dd1-43d7-8192-ab22346fe7a9\",\"dSCorePropagationData\":\"**************.0Z\",\"uSNCreated\":\"346426\",\"codePage\":\"0\",\"userAccountControl\":\"512\",\"userPrincipalName\":\"<EMAIL>\",\"lastLogon\":\"0\",\"whenCreated\":\"**************.0Z\",\"badPasswordTime\":\"0\",\"uSNChanged\":\"827513\",\"sAMAccountName\":\"vicuser1\",\"givenName\":\"vicuser1\",\"instanceType\":\"4\",\"objectClass\":\"top\",\"cn\":\"vicuser1\",\"department_ids\":[\"8dcbb744-**************-7db4a0f830c0\"],\"whenChanged\":\"**************.0Z\",\"employeeType\":\"foreign engineer\",\"primaryGroupID\":\"513\",\"sAMAccountType\":\"*********\",\"name\":\"vicuser1\",\"objectSid\":\"\\u0001\\u0005\\u0000\\u0000\\u0000\\u0000\\u0000\\u0005\\u0015\\u0000\\u0000\\u0000\\u000E???\\u0019ʜԋ\\u001E??\\u0013b\\u0000\\u0000\",\"badPwdCount\":\"0\",\"pwdLastSet\":\"133812467526747472\",\"objectCategory\":\"CN=Person,CN=Schema,CN=Configuration,DC=digital,DC=com\"},{\"lastLogoff\":\"0\",\"logonCount\":\"0\",\"accountExpires\":\"9223372036854775807\",\"displayName\":\"ad210e\",\"distinguishedName\":\"CN=ad210e,OU=vic_sso1,DC=digital,DC=com\",\"dn\":\"CN=ad210e,OU=vic_sso1,DC=digital,DC=com\",\"countryCode\":\"0\",\"objectGUID\":\"cfde6c58-38e3-4d12-a6e4-09eecdf81b2d\",\"dSCorePropagationData\":\"**************.0Z\",\"uSNCreated\":\"692632\",\"sn\":\"ad210e\",\"codePage\":\"0\",\"userAccountControl\":\"512\",\"userPrincipalName\":\"<EMAIL>\",\"lastLogon\":\"0\",\"whenCreated\":\"**************.0Z\",\"badPasswordTime\":\"0\",\"uSNChanged\":\"827511\",\"sAMAccountName\":\"ad210e\",\"instanceType\":\"4\",\"objectClass\":\"top\",\"cn\":\"ad210e\",\"department_ids\":[\"8dcbb744-**************-7db4a0f830c0\"],\"whenChanged\":\"**************.0Z\",\"employeeType\":\"正式外部员工\",\"primaryGroupID\":\"513\",\"sAMAccountType\":\"*********\",\"name\":\"ad210e\",\"objectSid\":\"\\u0001\\u0005\\u0000\\u0000\\u0000\\u0000\\u0000\\u0005\\u0015\\u0000\\u0000\\u0000\\u000E???\\u0019ʜԋ\\u001E???}\\u0000\\u0000\",\"badPwdCount\":\"0\",\"pwdLastSet\":\"133812994176001046\",\"objectCategory\":\"CN=Person,CN=Schema,CN=Configuration,DC=digital,DC=com\"},{\"lastLogoff\":\"0\",\"logonCount\":\"0\",\"accountExpires\":\"9223372036854775807\",\"displayName\":\"ad210f\",\"distinguishedName\":\"CN=ad210f,OU=vic_sso1,DC=digital,DC=com\",\"dn\":\"CN=ad210f,OU=vic_sso1,DC=digital,DC=com\",\"countryCode\":\"0\",\"objectGUID\":\"0285f49a-0e9f-4f27-a45d-2b3d0cd1226d\",\"dSCorePropagationData\":\"**************.0Z\",\"uSNCreated\":\"692639\",\"sn\":\"ad210f\",\"codePage\":\"0\",\"userAccountControl\":\"512\",\"userPrincipalName\":\"<EMAIL>\",\"lastLogon\":\"0\",\"whenCreated\":\"**************.0Z\",\"badPasswordTime\":\"0\",\"uSNChanged\":\"692644\",\"sAMAccountName\":\"ad210f\",\"instanceType\":\"4\",\"objectClass\":\"top\",\"cn\":\"ad210f\",\"department_ids\":[\"8dcbb744-**************-7db4a0f830c0\"],\"whenChanged\":\"**************.0Z\",\"primaryGroupID\":\"513\",\"sAMAccountType\":\"*********\",\"name\":\"ad210f\",\"objectSid\":\"\\u0001\\u0005\\u0000\\u0000\\u0000\\u0000\\u0000\\u0005\\u0015\\u0000\\u0000\\u0000\\u000E???\\u0019ʜԋ\\u001E???}\\u0000\\u0000\",\"badPwdCount\":\"0\",\"pwdLastSet\":\"133812994424282870\",\"objectCategory\":\"CN=Person,CN=Schema,CN=Configuration,DC=digital,DC=com\"}]},\"index\":1,\"type\":\"INNER\"}', 'SUCCESS', 'OK', '2025-02-07 16:24:17', '2025-02-07 16:24:32', '2025-02-07 15:47:25', 'admin', '2025-02-07 17:42:38', 'admin', 'iam', 36, 0, NULL, '0', NULL);
INSERT INTO "acm"."acm_flow_node" ("id", "node_type", "next_id", "action_id", "action_key", "account_id", "flow_id", "description", "config", "input", "output", "status", "error_msg", "run_start_time", "run_end_time", "create_time", "create_by", "update_time", "update_by", "tenant_id", "connector_id", "parent_id", "proxy_id", "closed", "note") VALUES (1887770556405379074, 'ActionNode', 1887798166594252801, 84, 'loop_ext_org', NULL, 1887751465322188801, NULL, '{\"ui_id\":\"1887770583566004227\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1887770583566004228\",\"name\":\"ext_org\",\"type\":\"OBJECT\",\"description\":\"支持一次集成单个或
多个外部部门\",\"display_name\":\"外部部门\",\"multi_valued\":true,\"value\":\"{{N1887770122504630274.current.dept}}\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1887770583566004229\",\"name\":\"run_type\",\"type\":\"STRING\",\"description\":\"并行：子流程按多线程并发执
行；串行：子流程按单线程顺序执行\",\"display_name\":\"执行方式\",\"multi_valued\":false,\"value\":\"并行\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"并行\",\"串行\"],\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, '{\"current\":{\"whenCreated\":\"20250117102934.0Z\",\"uSNChanged\":\"825887\",\"ou\":\"子部门\",\"instanceType\":\"4\",\"objectClass\":\"top\",\"distinguishedName\":\"OU=子部门,OU=vic_sso1,DC=digital,DC=com\",\"dn\":\"OU=子部门,OU=vic_sso1,DC=digital,DC=com\",\"whenChanged\":\"20250117102934.0Z\",\"parentObjectGUID\":\"8dcbb744-**************-7db4a0f830c0\",\"objectGUID\":\"bddc54ad-b007-465a-87c0-43413ad2374b\",\"name\":\"子部门\",\"dSCorePropagationData\":\"**************.0Z\",\"uSNCreated\":\"825887\",\"objectCategory\":\"CN=Organizational-Unit,CN=Schema,CN=Configuration,DC=digital,DC=com\"},\"index\":0,\"type\":\"INNER\"}', 'SUCCESS', 'OK', '2025-02-07 16:24:29', '2025-02-07 16:24:32', '2025-02-07 15:49:08', 'admin', '2025-02-07 16:24:32', 'admin', 'iam', 31, 1887770122504630274, NULL, '0', NULL);
INSERT INTO "acm"."acm_flow_node" ("id", "node_type", "next_id", "action_id", "action_key", "account_id", "flow_id", "description", "config", "input", "output", "status", "error_msg", "run_start_time", "run_end_time", "create_time", "create_by", "update_time", "update_by", "tenant_id", "connector_id", "parent_id", "proxy_id", "closed", "note") VALUES (1887770723607113729, 'ActionNode', NULL, 85, 'sync_update_org', NULL, 1887751465322188801, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1887770743645810698\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"ADAPTOR\",\"ui_id\":\"1887770743645810699\",\"name\":\"ext_org\",\"description\":\"待集成的外部部门对象（json）信息\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"外部部门对象\",\"value\":\"{{N1887770556405379074.current}}\",\"required\":true,\"page_control\":\"TEXT\"}],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', NULL, '{\"code\":\"0\",\"data\":[{\"external_id\":\"bddc54ad-b007-465a-87c0-43413ad2374b\",\"flag\":\"CREATE\",\"local\":{\"manager\":\"\",\"name\":\"子部门\",\"description\":\"子部门\",\"connector_parent_org_id\":\"8dcbb744-**************-7db4a0f830c0\",\"id\":\"1887779463266074626\",\"connector_org_id\":\"bddc54ad-b007-465a-87c0-43413ad2374b\"},\"message\":\"\"}],\"message\":\"SUCCESS\"}', 'SUCCESS', 'OK', '2025-02-07 16:24:29', '2025-02-07 16:24:32', '2025-02-07 15:49:48', 'admin', '2025-02-07 16:24:32', 'admin', 'iam', 31, 1887770556405379074, NULL, '0', NULL);
INSERT INTO "acm"."acm_flow_node" ("id", "node_type", "next_id", "action_id", "action_key", "account_id", "flow_id", "description", "config", "input", "output", "status", "error_msg", "run_start_time", "run_end_time", "create_time", "create_by", "update_time", "update_by", "tenant_id", "connector_id", "parent_id", "proxy_id", "closed", "note") VALUES (1887798166594252801, 'ActionNode', NULL, 87, 'loop_ext_user', NULL, 1887751465322188801, NULL, '{\"ui_id\":\"1887798201824718854\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1887798201824718855\",\"name\":\"ext_user\",\"type\":\"OBJECT\",\"description\":\"支持一次读入单个或多个外部用户>（json）对象\",\"display_name\":\"外部用户\",\"multi_valued\":true,\"value\":\"{{N1887770122504630274.current.user}}\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1887798201824718856\",\"name\":\"run_type\",\"type\":\"STRING\",\"description\":\"并行：子流程按多线程并发执
行；串行：子流程按单线程顺序执行\",\"display_name\":\"执行方式\",\"multi_valued\":false,\"value\":\"并行\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"并行\",\"串行\"],\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, '{\"current\":{\"lastLogoff\":\"0\",\"logonCount\":\"0\",\"accountExpires\":\"9223372036854775807\",\"displayName\":\"vicuser1\",\"distinguishedName\":\"CN=vicuser1,OU=vic_sso1,DC=digital,DC=com\",\"dn\":\"CN=vicuser1,OU=vic_sso1,DC=digital,DC=com\",\"countryCode\":\"0\",\"objectGUID\":\"8fb3b990-2dd1-43d7-8192-ab22346fe7a9\",\"dSCorePropagationData\":\"**************.0Z\",\"uSNCreated\":\"346426\",\"codePage\":\"0\",\"userAccountControl\":\"512\",\"userPrincipalName\":\"<EMAIL>\",\"lastLogon\":\"0\",\"whenCreated\":\"**************.0Z\",\"badPasswordTime\":\"0\",\"uSNChanged\":\"827513\",\"sAMAccountName\":\"vicuser1\",\"givenName\":\"vicuser1\",\"instanceType\":\"4\",\"objectClass\":\"top\",\"cn\":\"vicuser1\",\"department_ids\":[\"8dcbb744-**************-7db4a0f830c0\"],\"whenChanged\":\"**************.0Z\",\"employeeType\":\"foreign engineer\",\"primaryGroupID\":\"513\",\"sAMAccountType\":\"*********\",\"name\":\"vicuser1\",\"objectSid\":\"\\u0001\\u0005\\u0000\\u0000\\u0000\\u0000\\u0000\\u0005\\u0015\\u0000\\u0000\\u0000\\u000E???\\u0019ʜԋ\\u001E??\\u0013b\\u0000\\u0000\",\"badPwdCount\":\"0\",\"pwdLastSet\":\"133812467526747472\",\"objectCategory\":\"CN=Person,CN=Schema,CN=Configuration,DC=digital,DC=com\"},\"index\":0,\"type\":\"INNER\"}', 'SUCCESS', 'OK', '2025-02-07 17:39:27', '2025-02-07 17:39:27', '2025-02-07 17:38:51', 'admin', '2025-02-07 17:39:29', 'admin', 'iam', 31, 1887770122504630274, NULL, '0', NULL);
INSERT INTO "acm"."acm_flow_node" ("id", "node_type", "next_id", "action_id", "action_key", "account_id", "flow_id", "description", "config", "input", "output", "status", "error_msg", "run_start_time", "run_end_time", "create_time", "create_by", "update_time", "update_by", "tenant_id", "connector_id", "parent_id", "proxy_id", "closed", "note") VALUES (1887798348803207170, 'ActionNode', NULL, 88, 'sync_update_user', NULL, 1887751465322188801, NULL, '{\"ui_id\":\"1887798373245923341\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1887798373245923342\",\"name\":\"ext_user\",\"type\":\"OBJECT\",\"description\":\"待集成的外部用户对象（json）
信息\",\"display_name\":\"外部用户对象\",\"multi_valued\":false,\"value\":\"{{N1887798166594252801.current}}\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, '{\"code\":\"0\",\"data\":[{\"external_id\":\"8fb3b990-2dd1-43d7-8192-ab22346fe7a9\",\"flag\":\"CREATE\",\"local\":{\"sub\":\"1887798782509256706\",\"connector_org_ids\":[\"8dcbb744-**************-7db4a0f830c0\"],\"manager\":\"\",\"name\":\"vicuser1\",\"nickname\":\"vicuser1\",\"username\":\"vicuser1\"},\"message\":\"\"}],\"message\":\"SUCCESS\"}', 'SUCCESS', 'OK', '2025-02-07 17:41:17', '2025-02-07 17:41:18', '2025-02-07 17:39:35', 'admin', '2025-02-07 17:41:22', 'admin', 'iam', 31, 1887798166594252801, NULL, '0', NULL);

-- LDAP通讯录集成模版
INSERT INTO "acm"."acm_flow_instance" ("id", "name", "description", "flow_type", "status", "error_msg", "run_start_time", "run_end_time", "create_time", "create_by", "update_time", "update_by", "tenant_id", "op_status", "api_key", "category", "template", "is_deleted") VALUES (1887769664310771714, 'LDAP通讯录集成', 'LDAP通讯录集成模版', 1, 'IDLE', NULL, NULL, NULL, '2025-02-07 15:45:36', 'anonymousUser', '2025-02-07 18:08:29', 'admin', 'iam', 'ONLINE', '8a0a2e39baca48b4966d9e8e5d477631', '通讯录集成', 1, 0);
INSERT INTO "acm"."acm_flow_node" ("id", "node_type", "next_id", "action_id", "action_key", "account_id", "flow_id", "description", "config", "input", "output", "status", "error_msg", "run_start_time", "run_end_time", "create_time", "create_by", "update_time", "update_by", "tenant_id", "connector_id", "parent_id", "proxy_id", "closed", "note") VALUES (1887769666871504898, 'StartEventNode', 1887769666871504903, 81, 'user_sync', NULL, 1887769664310771714, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1851605414348050433\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1851605414348050434\",\"option_values\":[\"异步\",\"同步\"],\"description\":\"异步：
webhook接收到数据后，将直接返回接收成功的结果以及流的信息。\\n同步：Webhook接收到数据后，将等待连接流执行完成并将最后的执行结果返回给调用方（最长等待时间为10s，超时将返回为空）。请查看接口文档获得更多详细信息\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"连接流执行方式\",\"required\":true,\"name\":\"type\",\"mutability\":\"readWrite\",\"value\":\"异步\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1851605414348050435\",\"name\":\"value.org_total\",\"description\":\"预估总的外部部门个数，用于估算集成进度\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"预估部门个数\",\"value\":\"300\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1851605414348050436\",\"name\":\"value.user_total\",\"description\":\"预估总的外部用户个数，用于估算集成进度\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"预估用户个数\",\"value\":\"1000\",\"required\":true,\"page_control\":\"TEXT\"}],\"name\":\"root\",\"description\":\"启动通讯录集成\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"启动通讯录集成\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', NULL, '{\"hook_url\":null,\"sync\":false,\"type\":\"异步\",\"value\":{\"org_total\":300,\"user_total\":1000}}', 'SUCCESS', 'OK', '2025-02-07 17:43:44', '2025-02-07 17:43:44', '2025-02-07 15:45:36', 'anonymousUser', '2025-02-07 17:43:46', 'admin', 'iam', 31, 0, NULL, '0', NULL);
INSERT INTO "acm"."acm_flow_node" ("id", "node_type", "next_id", "action_id", "action_key", "account_id", "flow_id", "description", "config", "input", "output", "status", "error_msg", "run_start_time", "run_end_time", "create_time", "create_by", "update_time", "update_by", "tenant_id", "connector_id", "parent_id", "proxy_id", "closed", "note") VALUES (1887769666871504900, 'EndNode', NULL, NULL, NULL, NULL, 1887769664310771714, NULL, NULL, NULL, NULL, 'IDLE', NULL, NULL, NULL, '2025-02-07 15:45:36', 'anonymousUser', '2025-02-07 15:45:36', 'anonymousUser', 'iam', NULL, 0, NULL, '0', NULL);
INSERT INTO "acm"."acm_flow_node" ("id", "node_type", "next_id", "action_id", "action_key", "account_id", "flow_id", "description", "config", "input", "output", "status", "error_msg", "run_start_time", "run_end_time", "create_time", "create_by", "update_time", "update_by", "tenant_id", "connector_id", "parent_id", "proxy_id", "closed", "note") VALUES (1887769666871504901, 'ActionNode', 1887769666871504900, 89, 'complete_user_sync', NULL, 1887769664310771714, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1851607623072731137\",\"sub_params\":[],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', NULL, '{\"code\":\"0\",\"data\":null,\"message\":\"SUCCESS\"}', 'INVALID', 'OK', '2024-12-09 13:52:38', '2024-12-09 13:52:38', '2025-02-07 15:45:36', 'anonymousUser', '2025-02-07 15:45:36', 'anonymousUser', 'iam', 31, 0, NULL, '0', NULL);
INSERT INTO "acm"."acm_flow_node" ("id", "node_type", "next_id", "action_id", "action_key", "account_id", "flow_id", "description", "config", "input", "output", "status", "error_msg", "run_start_time", "run_end_time", "create_time", "create_by", "update_time", "update_by", "tenant_id", "connector_id", "parent_id", "proxy_id", "closed", "note") VALUES (1887769666871504903, 'ActionNode', 1887800063673757697, 82, 'record_ext_root_org', NULL, 1887769664310771714, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1865972729377787914\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"ADAPTOR\",\"ui_id\":\"1865972729377787915\",\"name\":\"root_id\",\"description\":\"外部部门的根部门ID\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"外部根部门ID\",\"value\":\"8dcbb744-**************-7db4a0f830c0\",\"required\":true,\"page_control\":\"TEXT\"}],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', NULL, '{\"code\":\"0\",\"data\":{\"external_id\":\"8dcbb744-**************-7db4a0f830c0\",\"flag\":\"UPDATE\",\"local\":\"1887769664478543873\",\"message\":\"\"},\"message\":\"SUCCESS\"}', 'SUCCESS', 'OK', '2025-02-07 17:45:25', '2025-02-07 17:45:25', '2025-02-07 15:45:36', 'anonymousUser', '2025-02-07 17:46:11', 'admin', 'iam', 31, 0, NULL, '0', NULL);
INSERT INTO "acm"."acm_flow_node" ("id", "node_type", "next_id", "action_id", "action_key", "account_id", "flow_id", "description", "config", "input", "output", "status", "error_msg", "run_start_time", "run_end_time", "create_time", "create_by", "update_time", "update_by", "tenant_id", "connector_id", "parent_id", "proxy_id", "closed", "note") VALUES (1887800063673757697, 'ActionNode', 1887800504583188482, 120, 'search-all-dept', 1882388529564815362, 1887769664310771714, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1887800106625937418\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1887800106625937419\",\"name\":\"base_dn\",\"description\":\"搜索>根节点的BaseDN\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"BaseDN\",\"value\":\"OU=vic_sso1,DC=digital,DC=com\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1887800106625937420\",\"name\":\"dept_filter\",\"description\":\"部门搜索条件\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"部门搜索条件\",\"value\":\"(objectclass=organizationalUnit)\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1887800106625937421\",\"name\":\"dept_id_name\",\"description\":\"作为部门ID的属性名，一般用entryUUID\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"部门ID属性名\",\"value\":\"entryUUID\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1887800106625937422\",\"name\":\"dept_pid_name\",\"description\":\"作为部门父ID的属性名，一般用parentEntryUUID\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"部门父ID属性名\",\"value\":\"parentEntryUUID\",\"required\":true,\"page_control\":\"TEXT\"}],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', NULL, '{\n  \"current\": {\n    \"dept\": [\n      {\n        \"whenCreated\": \"20250117102934.0Z\",\n        \"uSNChanged\": \"825887\",\n        \"ou\": \"子部门\",\n        \"instanceType\": \"4\",\n        \"objectClass\": \"top\",\n        \"distinguishedName\": \"OU=子部门,OU=vic_sso1,DC=digital,DC=com\",\n        \"dn\": \"OU=子部门,OU=vic_sso1,DC=digital,DC=com\",\n        \"whenChanged\": \"20250117102934.0Z\",\n        \"parentObjectGUID\": \"8dcbb744-**************-7db4a0f830c0\",\n        \"objectGUID\": \"bddc54ad-b007-465a-87c0-43413ad2374b\",\n        \"name\": \"子部门\",\n        \"dSCorePropagationData\": \"**************.0Z\",\n        \"uSNCreated\": \"825887\",\n        \"objectCategory\": \"CN=Organizational-Unit,CN=Schema,CN=Configuration,DC=digital,DC=com\"\n      },\n      {\n        \"whenCreated\": \"**************.0Z\",\n        \"uSNChanged\": \"827643\",\n        \"ou\": \"AD210-Proxy\",\n        \"instanceType\": \"4\",\n        \"objectClass\": \"top\",\n        \"distinguishedName\": \"OU=AD210-Proxy,OU=vic_sso1,DC=digital,DC=com\",\n        \"dn\": \"OU=AD210-Proxy,OU=vic_sso1,DC=digital,DC=com\",\n        \"whenChanged\": \"**************.0Z\",\n        \"parentObjectGUID\": \"8dcbb744-**************-7db4a0f830c0\",\n        \"objectGUID\": \"2f0f3557-15ce-4d03-871c-27d1617aae8b\",\n        \"name\": \"AD210-Proxy\",\n        \"dSCorePropagationData\": \"**************.0Z\",\n        \"uSNCreated\": \"827643\",\n        \"objectCategory\": \"CN=Organizational-Unit,CN=Schema,CN=Configuration,DC=digital,DC=com\"\n      }\n    ]\n  },\n  \"index\": 1,\n  \"type\": \"INNER\"\n}', 'IDLE', NULL, NULL, NULL, '2025-02-07 17:46:23', 'admin', '2025-02-07 17:55:25', 'admin', 'iam', 35, 0, NULL, '0', NULL);
INSERT INTO "acm"."acm_flow_node" ("id", "node_type", "next_id", "action_id", "action_key", "account_id", "flow_id", "description", "config", "input", "output", "status", "error_msg", "run_start_time", "run_end_time", "create_time", "create_by", "update_time", "update_by", "tenant_id", "connector_id", "parent_id", "proxy_id", "closed", "note") VALUES (1887800504583188482, 'ActionNode', 1887769666871504901, 121, 'search-all-user', 1882388529564815362, 1887769664310771714, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1887800528761663504\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1887800528761663505\",\"name\":\"base_dn\",\"description\":\"搜索>根节点的BaseDN\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"BaseDN\",\"value\":\"OU=vic_sso1,DC=digital,DC=com\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1887800528761663506\",\"name\":\"user_filter\",\"description\":\"用户搜索条件\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"用户搜索条件\",\"value\":\"(objectclass=organizationalPerson)\",\"required\":true,\"page_control\":\"TEXT\"}],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', NULL, '{\n  \"current\": {\n    \"user\": [\n      {\n        \"lastLogoff\": \"0\",\n        \"logonCount\": \"0\",\n        \"accountExpires\": \"9223372036854775807\",\n        \"displayName\": \"vicuser1\",\n        \"distinguishedName\": \"CN=vicuser1,OU=vic_sso1,DC=digital,DC=com\",\n        \"dn\": \"CN=vicuser1,OU=vic_sso1,DC=digital,DC=com\",\n        \"countryCode\": \"0\",\n        \"objectGUID\": \"8fb3b990-2dd1-43d7-8192-ab22346fe7a9\",\n        \"dSCorePropagationData\": \"**************.0Z\",\n        \"uSNCreated\": \"346426\",\n        \"codePage\": \"0\",\n        \"userAccountControl\": \"512\",\n        \"userPrincipalName\": \"<EMAIL>\",\n        \"lastLogon\": \"0\",\n        \"whenCreated\": \"**************.0Z\",\n        \"badPasswordTime\": \"0\",\n        \"uSNChanged\": \"827513\",\n        \"sAMAccountName\": \"vicuser1\",\n        \"givenName\": \"vicuser1\",\n        \"instanceType\": \"4\",\n        \"objectClass\": \"top\",\n        \"cn\": \"vicuser1\",\n        \"department_ids\": [\n          \"8dcbb744-**************-7db4a0f830c0\"\n        ],\n        \"whenChanged\": \"**************.0Z\",\n        \"employeeType\": \"foreign engineer\",\n        \"primaryGroupID\": \"513\",\n        \"sAMAccountType\": \"*********\",\n        \"name\": \"vicuser1\",\n        \"objectSid\": \"\\u0001\\u0005\\u0000\\u0000\\u0000\\u0000\\u0000\\u0005\\u0015\\u0000\\u0000\\u0000\\u000e???\\u0019ʜԋ\\u001e??\\u0013b\\u0000\\u0000\",\n        \"badPwdCount\": \"0\",\n        \"pwdLastSet\": \"133812467526747472\",\n        \"objectCategory\": \"CN=Person,CN=Schema,CN=Configuration,DC=digital,DC=com\"\n      },\n      {\n        \"lastLogoff\": \"0\",\n        \"logonCount\": \"0\",\n        \"accountExpires\": \"9223372036854775807\",\n        \"displayName\": \"ad210e\",\n        \"distinguishedName\": \"CN=ad210e,OU=vic_sso1,DC=digital,DC=com\",\n        \"dn\": \"CN=ad210e,OU=vic_sso1,DC=digital,DC=com\",\n        \"countryCode\": \"0\",\n        \"objectGUID\": \"cfde6c58-38e3-4d12-a6e4-09eecdf81b2d\",\n        \"dSCorePropagationData\": \"**************.0Z\",\n        \"uSNCreated\": \"692632\",\n        \"sn\": \"ad210e\",\n        \"codePage\": \"0\",\n        \"userAccountControl\": \"512\",\n        \"userPrincipalName\": \"<EMAIL>\",\n        \"lastLogon\": \"0\",\n        \"whenCreated\": \"**************.0Z\",\n        \"badPasswordTime\": \"0\",\n        \"uSNChanged\": \"827511\",\n        \"sAMAccountName\": \"ad210e\",\n        \"instanceType\": \"4\",\n        \"objectClass\": \"top\",\n        \"cn\": \"ad210e\",\n        \"department_ids\": [\n          \"8dcbb744-**************-7db4a0f830c0\"\n        ],\n        \"whenChanged\": \"**************.0Z\",\n        \"employeeType\": \"正式外部员工\",\n        \"primaryGroupID\": \"513\",\n        \"sAMAccountType\": \"*********\",\n        \"name\": \"ad210e\",\n        \"objectSid\": \"\\u0001\\u0005\\u0000\\u0000\\u0000\\u0000\\u0000\\u0005\\u0015\\u0000\\u0000\\u0000\\u000e???\\u0019ʜԋ\\u001e???}\\u0000\\u0000\",\n        \"badPwdCount\": \"0\",\n        \"pwdLastSet\": \"133812994176001046\",\n        \"objectCategory\": \"CN=Person,CN=Schema,CN=Configuration,DC=digital,DC=com\"\n      },\n      {\n        \"lastLogoff\": \"0\",\n        \"logonCount\": \"0\",\n        \"accountExpires\": \"9223372036854775807\",\n        \"displayName\": \"ad210f\",\n        \"distinguishedName\": \"CN=ad210f,OU=vic_sso1,DC=digital,DC=com\",\n        \"dn\": \"CN=ad210f,OU=vic_sso1,DC=digital,DC=com\",\n        \"countryCode\": \"0\",\n        \"objectGUID\": \"0285f49a-0e9f-4f27-a45d-2b3d0cd1226d\",\n        \"dSCorePropagationData\": \"**************.0Z\",\n        \"uSNCreated\": \"692639\",\n        \"sn\": \"ad210f\",\n        \"codePage\": \"0\",\n        \"userAccountControl\": \"512\",\n        \"userPrincipalName\": \"<EMAIL>\",\n        \"lastLogon\": \"0\",\n        \"whenCreated\": \"**************.0Z\",\n        \"badPasswordTime\": \"0\",\n        \"uSNChanged\": \"692644\",\n        \"sAMAccountName\": \"ad210f\",\n        \"instanceType\": \"4\",\n        \"objectClass\": \"top\",\n        \"cn\": \"ad210f\",\n        \"department_ids\": [\n          \"8dcbb744-**************-7db4a0f830c0\"\n        ],\n        \"whenChanged\": \"**************.0Z\",\n        \"primaryGroupID\": \"513\",\n        \"sAMAccountType\": \"*********\",\n        \"name\": \"ad210f\",\n        \"objectSid\": \"\\u0001\\u0005\\u0000\\u0000\\u0000\\u0000\\u0000\\u0005\\u0015\\u0000\\u0000\\u0000\\u000e???\\u0019ʜԋ\\u001e???}\\u0000\\u0000\",\n        \"badPwdCount\": \"0\",\n        \"pwdLastSet\": \"133812994424282870\",\n        \"objectCategory\": \"CN=Person,CN=Schema,CN=Configuration,DC=digital,DC=com\"\n      }\n    ]\n  },\n  \"index\": 1,\n  \"type\": \"INNER\"\n}', 'IDLE', NULL, NULL, NULL, '2025-02-07 17:48:09', 'admin', '2025-02-07 17:59:02', 'admin', 'iam', 35, 0, NULL, '0', NULL);
INSERT INTO "acm"."acm_flow_node" ("id", "node_type", "next_id", "action_id", "action_key", "account_id", "flow_id", "description", "config", "input", "output", "status", "error_msg", "run_start_time", "run_end_time", "create_time", "create_by", "update_time", "update_by", "tenant_id", "connector_id", "parent_id", "proxy_id", "closed", "note") VALUES (1887801014493114370, 'ActionNode', NULL, 84, 'loop_ext_org', NULL, 1887769664310771714, NULL, '{\"ui_id\":\"1887801118157844482\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1887801118157844483\",\"name\":\"ext_org\",\"type\":\"OBJECT\",\"description\":\"支持一次集成单个或多个外部部门\",\"display_name\":\"外部部门\",\"multi_valued\":true,\"value\":\"{{N1887800063673757697.current.dept}}\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1887801118157844484\",\"name\":\"run_type\",\"type\":\"STRING\",\"description\":\"并行：子流程按多线程并发执行；串行：子流>程按单线程顺序执行\",\"display_name\":\"执行方式\",\"multi_valued\":false,\"value\":\"并行\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"并行\",\"串行\"],\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, '{\"current\":{\"whenCreated\":\"20250117102934.0Z\",\"uSNChanged\":\"825887\",\"ou\":\"子部门\",\"instanceType\":\"4\",\"objectClass\":\"top\",\"distinguishedName\":\"OU=子部门,OU=vic_sso1,DC=digital,DC=com\",\"dn\":\"OU=子部门,OU=vic_sso1,DC=digital,DC=com\",\"whenChanged\":\"20250117102934.0Z\",\"parentObjectGUID\":\"8dcbb744-**************-7db4a0f830c0\",\"objectGUID\":\"bddc54ad-b007-465a-87c0-43413ad2374b\",\"name\":\"子部门\",\"dSCorePropagationData\":\"**************.0Z\",\"uSNCreated\":\"825887\",\"objectCategory\":\"CN=Organizational-Unit,CN=Schema,CN=Configuration,DC=digital,DC=com\"},\"index\":0,\"type\":\"INNER\"}', 'SUCCESS', 'OK', '2025-02-07 17:55:46', '2025-02-07 17:55:46', '2025-02-07 17:50:10', 'admin', '2025-02-07 17:55:48', 'admin', 'iam', 31, 1887800063673757697, NULL, '0', NULL);
INSERT INTO "acm"."acm_flow_node" ("id", "node_type", "next_id", "action_id", "action_key", "account_id", "flow_id", "description", "config", "input", "output", "status", "error_msg", "run_start_time", "run_end_time", "create_time", "create_by", "update_time", "update_by", "tenant_id", "connector_id", "parent_id", "proxy_id", "closed", "note") VALUES (1887802504565747714, 'ActionNode', NULL, 85, 'sync_update_org', NULL, 1887769664310771714, NULL, '{\"ui_id\":\"1887802531495686154\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1887802531495686155\",\"name\":\"ext_org\",\"type\":\"OBJECT\",\"description\":\"待集成的外部部门对象（json）信
息\",\"display_name\":\"外部部门对象\",\"multi_valued\":false,\"value\":\"{{N1887801014493114370.current}}\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, '{\"code\":\"1\",\"data\":null,\"message\":null}', 'FAILED', 'OK', '2025-02-07 17:56:25', '2025-02-07 17:56:25', '2025-02-07 17:56:05', 'admin', '2025-02-07 17:56:27', 'admin', 'iam', 31, 1887801014493114370, NULL, '0', NULL);
INSERT INTO "acm"."acm_flow_node" ("id", "node_type", "next_id", "action_id", "action_key", "account_id", "flow_id", "description", "config", "input", "output", "status", "error_msg", "run_start_time", "run_end_time", "create_time", "create_by", "update_time", "update_by", "tenant_id", "connector_id", "parent_id", "proxy_id", "closed", "note") VALUES (1887802887753166850, 'ActionNode', NULL, 87, 'loop_ext_user', NULL, 1887769664310771714, NULL, '{\"ui_id\":\"1887802912338489349\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1887802912338489350\",\"name\":\"ext_user\",\"type\":\"OBJECT\",\"description\":\"支持一次读入单个或多个外部用户>（json）对象\",\"display_name\":\"外部用户\",\"multi_valued\":true,\"value\":\"{{N1887800504583188482.current.user}}\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1887802912338489351\",\"name\":\"run_type\",\"type\":\"STRING\",\"description\":\"并行：子流程按多线程并发执
行；串行：子流程按单线程顺序执行\",\"display_name\":\"执行方式\",\"multi_valued\":false,\"value\":\"并行\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"并行\",\"串行\"],\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, '{\"current\":{\"lastLogoff\":\"0\",\"logonCount\":\"0\",\"accountExpires\":\"9223372036854775807\",\"displayName\":\"vicuser1\",\"distinguishedName\":\"CN=vicuser1,OU=vic_sso1,DC=digital,DC=com\",\"dn\":\"CN=vicuser1,OU=vic_sso1,DC=digital,DC=com\",\"countryCode\":\"0\",\"objectGUID\":\"8fb3b990-2dd1-43d7-8192-ab22346fe7a9\",\"dSCorePropagationData\":\"**************.0Z\",\"uSNCreated\":\"346426\",\"codePage\":\"0\",\"userAccountControl\":\"512\",\"userPrincipalName\":\"<EMAIL>\",\"lastLogon\":\"0\",\"whenCreated\":\"**************.0Z\",\"badPasswordTime\":\"0\",\"uSNChanged\":\"827513\",\"sAMAccountName\":\"vicuser1\",\"givenName\":\"vicuser1\",\"instanceType\":\"4\",\"objectClass\":\"top\",\"cn\":\"vicuser1\",\"department_ids\":[\"8dcbb744-**************-7db4a0f830c0\"],\"whenChanged\":\"**************.0Z\",\"employeeType\":\"foreign engineer\",\"primaryGroupID\":\"513\",\"sAMAccountType\":\"*********\",\"name\":\"vicuser1\",\"objectSid\":\"\\u0001\\u0005\\u0000\\u0000\\u0000\\u0000\\u0000\\u0005\\u0015\\u0000\\u0000\\u0000\\u000E???\\u0019ʜԋ\\u001E??\\u0013b\\u0000\\u0000\",\"badPwdCount\":\"0\",\"pwdLastSet\":\"133812467526747472\",\"objectCategory\":\"CN=Person,CN=Schema,CN=Configuration,DC=digital,DC=com\"},\"index\":0,\"type\":\"INNER\"}', 'SUCCESS', 'OK', '2025-02-07 17:57:57', '2025-02-07 17:57:57', '2025-02-07 17:57:37', 'admin', '2025-02-07 17:57:59', 'admin', 'iam', 31, 1887800504583188482, NULL, '0', NULL);
INSERT INTO "acm"."acm_flow_node" ("id", "node_type", "next_id", "action_id", "action_key", "account_id", "flow_id", "description", "config", "input", "output", "status", "error_msg", "run_start_time", "run_end_time", "create_time", "create_by", "update_time", "update_by", "tenant_id", "connector_id", "parent_id", "proxy_id", "closed", "note") VALUES (1887802995953627138, 'ActionNode', NULL, 88, 'sync_update_user', NULL, 1887769664310771714, NULL, '{\"ui_id\":\"1887803015493201932\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1887803015493201933\",\"name\":\"ext_user\",\"type\":\"OBJECT\",\"description\":\"待集成的外部用户对象（json）
信息\",\"display_name\":\"外部用户对象\",\"multi_valued\":false,\"value\":\"{{N1887802887753166850.current}}\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, NULL, 'INVALID', NULL, NULL, NULL, '2025-02-07 17:58:03', 'admin', '2025-02-07 17:58:17', 'admin', 'iam', 31, 1887802887753166850, NULL, '0', NULL);
