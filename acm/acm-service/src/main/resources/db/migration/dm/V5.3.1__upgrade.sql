UPDATE "acm"."acm_app_action" SET "config_schema" = '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"请复制此Webhook地址，它支持常见的GET/POST/PUT/PATCH/DELETE等请求方式,请根据需求选择合适的方法进行API调用。\",\"display_name\":\"webhook地址\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"hookUrl\",\"page_control\":\"LABEL\",\"required\":true,\"ui_id\":\"1898991617057046530\",\"update\":true,\"value\":\"{{server}}/acm/flows/start/1/{{flowId}}/{{apiKey}}\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"异步：webhook接收到数据后，将直接返回接收成功的结果以及流的信息。同步：Webhook接收到数据后，将等待连接流执行完成并将最后的执行结果返回给调用方（最长等待时间为10s，超时将返回为空）。请查看接口文档获得更多详细信息\",\"display_name\":\"连接流执行方式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"type\",\"option_values\":[\"异步\",\"同步\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1898991617057046531\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"请按实际情况输入json格式的测试数据\",\"display_name\":\"测试数据\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"value\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1898991617057046532\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"设置可访问的IP白名单，多个IP以, 分隔\",\"display_name\":\"IP白名单\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"ipList\",\"page_control\":\"TEXTAREA\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"FIX_VALUE\"}],\"type\":\"OBJECT\",\"ui_id\":\"1900034121575604226\",\"update\":true,\"value_type\":\"JS_EXP\"}'  WHERE "id" = 1;

INSERT INTO "acm"."acm_app_action" ("id", "action_key", "is_trigger", "name", "description", "connector_id", "account_schema_id", "protocol", "action_type", "status", "config_schema", "create_time", "create_by", "update_time", "update_by", "tenant_id", "sample_output", "node_struct") VALUES (861, '1898942974505598978', 1, '短信Webhook', NULL, 1, NULL, 'WEBHOOK', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"请复制此Webhook地址，它支持常见的GET/POST/PUT/PATCH/DELETE等请求方式,请根据需求选择合适的方法进行API调用。\",\"display_name\":\"webHook地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"hookUrl\",\"option_values\":[],\"page_control\":\"LABEL\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619266\",\"update\":true,\"value\":\"{{server}}/acm/flows/start/1/{{flowId}}/{{apiKey}}\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"异步：webhook接收到数据后，将直接返回接收成功的结果以及流的信息。同步：Webhook接收到数据后，将等待连接流执行完成并将最后的执行结果返回给调用方（最长等待时间为10s，超时将返回为空）。请查看接口文档获得更多详细信息\",\"display_name\":\"连接流执行方式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"type\",\"option_values\":[\"同步\",\"异步\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1741589948996\",\"update\":true,\"value\":\"同步\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请按实际情况输入json格式的测试数据\",\"display_name\":\"测试数据\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"value\",\"page_control\":\"TEXTAREA\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619267\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"设置可访问的IP白名单，多个IP以, 分隔\",\"display_name\":\"IP白名单\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"ipList\",\"page_control\":\"TEXTAREA\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1741774074249\",\"update\":true,\"value_type\":\"FIX_VALUE\"}],\"type\":\"OBJECT\",\"ui_id\":\"1900035553427103745\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2025-03-10 11:44:20', 'admin', '2025-03-13 12:05:52', 'admin', 'iam', NULL, 'SINGLE');

-- 短信模板 改为使用yaml导入
-- INSERT INTO "acm"."acm_flow_instance" ("id", "name", "description", "flow_type", "status", "op_status", "error_msg", "run_start_time", "run_end_time", "api_key", "category", "create_time", "create_by", "update_time", "update_by", "tenant_id", "template", "is_deleted") VALUES (1899654617574858753, '中国联通Http短信', NULL, 1, 'IDLE', 'ONLINE', 'OK', now(), now(), '58ef07fd6ceb456d95806ad78ccef59a', '短信集成', now(), 'admin', now(), 'admin', 'iam', 1, 0);
-- INSERT INTO "acm"."acm_flow_node" ("id", "node_type", "parent_id", "next_id", "connector_id", "action_id", "action_key", "account_id", "flow_id", "description", "config", "input", "output", "status", "error_msg", "run_start_time", "run_end_time", "proxy_id", "create_time", "create_by", "update_time", "update_by", "tenant_id", "closed", "note") VALUES (1899654617612607489, 'StartEventNode', 0, 1901833944131325954, 1, 861, '1898942974505598978', NULL, 1899654617574858753, NULL, '{\"ui_id\":\"1900035553427103745\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1699309157332619266\",\"name\":\"hookUrl\",\"type\":\"STRING\",\"description\":\"请复制此Webhook地址，它支持常见的GET/POST/PUT/PATCH/DELETE等请求方式,请根据需求选择合适的方法进行API调用。\",\"display_name\":\"webHook地址\",\"multi_valued\":false,\"value\":\"https://192-168-50-20-9lxgxdd3nny8td.ztna-dingtalk.com/acm/flows/start/1/1899654617574858753/58ef07fd6ceb456d95806ad78ccef59a\",\"update\":true,\"required\":true,\"page_control\":\"LABEL\",\"option_values\":[],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1741589948996\",\"name\":\"type\",\"type\":\"STRING\",\"description\":\"异步：webhook接收到数据后，将直接返回接收成功的结果以及流的信息。同步：Webhook接收到数据后，将等待连接流执行完成并将最后的执行结果返回给调用方（最长等待时间为10s，超时将返回为空）。请查看接口文档获得更多详细信息\",\"display_name\":\"连接流执行方式\",\"multi_valued\":false,\"value\":\"同步\",\"update\":true,\"required\":true,\"page_control\":\"HIDDEN\",\"option_values\":[\"同步\",\"异步\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1699309157332619267\",\"name\":\"value\",\"type\":\"OBJECT\",\"description\":\"请按实际情况输入json格式的测试数据\",\"display_name\":\"测试数据\",\"multi_valued\":false,\"update\":true,\"required\":false,\"page_control\":\"TEXTAREA\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1741774074249\",\"name\":\"ipList\",\"type\":\"STRING\",\"description\":\"设置可访问的IP白名单，多个IP以, 分隔\",\"display_name\":\"IP白名单\",\"multi_valued\":false,\"update\":true,\"required\":false,\"page_control\":\"TEXTAREA\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', '{\"hook_url\":\"https://192-168-50-20-9lxgxdd3nny8td.ztna-dingtalk.com/acm/flows/start/1/1899654617574858753/58ef07fd6ceb456d95806ad78ccef59a\",\"ip_list\":null,\"sync\":true,\"type\":\"同步\",\"value\":null}', '{\"hook_url\":\"https://192-168-50-20-9lxgxdd3nny8td.ztna-dingtalk.com/acm/flows/start/1/1899654617574858753/58ef07fd6ceb456d95806ad78ccef59a\",\"ip_list\":null,\"sync\":true,\"type\":\"同步\",\"value\":null}', 'SUCCESS', 'OK', '2025-03-18 11:11:22', '2025-03-18 11:11:22', NULL, '2025-03-14 11:42:12', 'admin', '2025-03-18 11:11:24', 'admin', 'iam', '0', NULL);
-- INSERT INTO "acm"."acm_flow_node" ("id", "node_type", "parent_id", "next_id", "connector_id", "action_id", "action_key", "account_id", "flow_id", "description", "config", "input", "output", "status", "error_msg", "run_start_time", "run_end_time", "proxy_id", "create_time", "create_by", "update_time", "update_by", "tenant_id", "closed", "note") VALUES (1899654617688104961, 'EndNode', 0, NULL, NULL, NULL, NULL, NULL, 1899654617574858753, NULL, NULL, NULL, NULL, 'IDLE', NULL, NULL, NULL, NULL,  now(),'admin',now(), 'admin', 'iam', '0', NULL);
-- INSERT INTO "acm"."acm_flow_node" ("id", "node_type", "parent_id", "next_id", "connector_id", "action_id", "action_key", "account_id", "flow_id", "description", "config", "input", "output", "status", "error_msg", "run_start_time", "run_end_time", "proxy_id", "create_time", "create_by", "update_time", "update_by", "tenant_id", "closed", "note") VALUES (1899662076519571458, 'ActionNode', 0, 1899662361925181441, 4, 41, 'http-text', NULL, 1899654617574858753, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1847173036443410433\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1802916934316052483\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"请求类型\",\"required\":true,\"name\":\"httpMethod\",\"mutability\":\"readWrite\",\"value\":\"POST\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"ADAPTOR\",\"ui_id\":\"1802916934316052484\",\"name\":\"endpoint\",\"description\":\"请填写完整的接口地址\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"请求地址\",\"value\":\"var time = {{N1900027041449435138.value.time}};\\nvar sign = {{N1900027796474486785.result}};\\nvar content = {{N1899654617612607489.value.content}};\\n\\nreturn  \\\"http://************:8888/v2sms.aspx?userid=373&action=send&timestamp=\\\"+time+\\\"&sign=\\\"+sign+\\\"&mobile=***********&content=【中国合格评定国家认可委员会】\\\"+content+\\\"&sendTime=&extno=\\\"\\n\\n\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1802916934316052485\",\"option_values\":[\"UTF8\",\"GBK\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"应答编码\",\"required\":true,\"name\":\"encoding\",\"mutability\":\"readWrite\",\"value\":\"UTF8\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1802916934316052486\",\"name\":\"responseType\",\"description\":\"\",\"update\":true,\"mutability\":\"readonly\",\"type\":\"STRING\",\"display_name\":\"应答类型\",\"value\":\"STRING\",\"required\":true,\"page_control\":\"HIDDEN\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1802916934316052487\",\"name\":\"header\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"Header参数\",\"value\":{},\"required\":false,\"page_control\":\"EXTEND_OBJ\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1718683877009\",\"name\":\"path\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"Path参数\",\"value\":{},\"required\":false,\"page_control\":\"EXTEND_OBJ\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1802916934316052488\",\"name\":\"query\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"Query参数\",\"value\":{},\"required\":false,\"page_control\":\"EXTEND_OBJ\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1802916934316052489\",\"name\":\"body\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"Body参数\",\"value\":{},\"required\":false,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', '{\"account\":null,\"account_id\":null,\"auth_protocol\":null,\"body\":{},\"content_type\":null,\"encoding\":\"UTF8\",\"endpoint\":\"http://************:8888/v2sms.aspx?userid=37322&action=send&timestamp=************&sign=db225c2126184c9d0ef63ce9bb54036a&mobile=***********&content=%E3%80%90%E4%B8%AD%E5%9B%BD%E5%90%88%E6%A0%BC%E8%AF%84%E5%AE%9A%E5%9B%BD%E5%AE%B6%E8%AE%A4%E5%8F%AF%E5%A7%94%E5%91%98%E4%BC%9A%E3%80%91%E6%B5%8B%E8%AF%95%E7%9F%AD%E4%BF%A1&sendTime=&extno=\",\"header\":{},\"http_method\":\"POST\",\"path\":{},\"proxy_ip\":null,\"query\":{},\"response_type\":\"STRING\",\"success_condition\":null,\"update_body\":null}', '{\"status_code\":200,\"header\":{\"X-Frame-Options\":\"SAMEORIGIN\",\"Cache-Control\":\"private\",\"Server\":\"Microsoft-IIS/10.0\",\"X-AspNet-Version\":\"2.0.50727\",\"Content-Length\":\"217\",\"Date\":\"Wed, 12 Mar 2025 03:22:06 GMT\",\"Content-Type\":\"text/xml; charset=UTF-8\"},\"body\":\"<?xml version=\\\"1.0\\\" encoding=\\\"utf-8\\\" ?><returnsms>\\n <returnstatus>Faild</returnstatus>\\n <message>企业id错误</message>\\n <remainpoint>0</remainpoint>\\n <taskID>0</taskID>\\n <successCounts>0</successCounts></returnsms>\"}', 'SUCCESS', 'OK', '2025-03-14 11:42:12', '2025-03-14 11:42:12', NULL,  now(),'admin',now(), 'admin', 'iam', '0', NULL);
-- INSERT INTO "acm"."acm_flow_node" ("id", "node_type", "parent_id", "next_id", "connector_id", "action_id", "action_key", "account_id", "flow_id", "description", "config", "input", "output", "status", "error_msg", "run_start_time", "run_end_time", "proxy_id", "create_time", "create_by", "update_time", "update_by", "tenant_id", "closed", "note") VALUES (1899662361925181441, 'ActionNode', 0, 1899667819897479169, 23, 42, 'xml_trans_json', NULL, 1899654617574858753, NULL, '{\"ui_id\":\"1772511280870039554\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1699309157332619266\",\"name\":\"xmlParam\",\"type\":\"STRING\",\"description\":\"\",\"display_name\":\"XML参数\",\"multi_valued\":false,\"value\":\"{{N1899662076519571458.body}}\",\"update\":true,\"required\":true,\"page_control\":\"TEXTAREA\",\"option_values\":[],\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', '{\"xml_param\":\"<?xml version=\\\"1.0\\\" encoding=\\\"utf-8\\\" ?><returnsms>\\n <returnstatus>Faild</returnstatus>\\n <message>企业id错误</message>\\n <remainpoint>0</remainpoint>\\n <taskID>0</taskID>\\n <successCounts>0</successCounts></returnsms>\"}', '{\"code\":200,\"msg\":\"OK\",\"value\":{\"returnsms\":{\"returnstatus\":\"Faild\",\"successCounts\":\"0\",\"message\":\"企业id错误\",\"remainpoint\":\"0\",\"taskID\":\"0\"}}}', 'SUCCESS', 'OK', '2025-03-14 11:42:12', '2025-03-14 11:42:12', NULL,  now(),'admin',now(), 'admin', 'iam', '0', NULL);
-- INSERT INTO "acm"."acm_flow_node" ("id", "node_type", "parent_id", "next_id", "connector_id", "action_id", "action_key", "account_id", "flow_id", "description", "config", "input", "output", "status", "error_msg", "run_start_time", "run_end_time", "proxy_id", "create_time", "create_by", "update_time", "update_by", "tenant_id", "closed", "note") VALUES (1899667819897479169, 'ActionNode', 0, 1899654617688104961, 3, 4, 'set_var', NULL, 1899654617574858753, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1899667831778263044\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1899667831778263045\",\"name\":\"varPath\",\"description\":\"选择前面步骤已经创建的变量\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"选择要更新的变量\",\"value\":\"res\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1899667831778263046\",\"name\":\"value\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"设置新的值\",\"value\":\"var status = {{N1899662361925181441.value.returnsms.returnstatus}};\\nreturn status == \\\"Success\\\";\",\"required\":true,\"page_control\":\"JSON\"}],\"name\":\"root\",\"description\":\"更新变量的值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"更新变量\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"input\":{\"value\":false,\"var_path\":\"res\"}}', '{\"code\":200,\"msg\":\"OK\",\"value\":false}', 'SUCCESS', 'OK', '2025-03-14 11:42:12', '2025-03-14 11:42:12', NULL,  now(),'admin',now(), 'admin', 'iam', '0', NULL);
-- INSERT INTO "acm"."acm_flow_node" ("id", "node_type", "parent_id", "next_id", "connector_id", "action_id", "action_key", "account_id", "flow_id", "description", "config", "input", "output", "status", "error_msg", "run_start_time", "run_end_time", "proxy_id", "create_time", "create_by", "update_time", "update_by", "tenant_id", "closed", "note") VALUES (1900027041449435138, 'ActionNode', 0, 1900027796474486785, 3, 4, 'set_var', NULL, 1899654617574858753, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1900027052805931012\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1900027052805931013\",\"name\":\"varPath\",\"description\":\"选择前面步骤已经创建的变量\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"选择要更新的变量\",\"value\":\"res\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1900027052805931014\",\"name\":\"value\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"设置新的值\",\"value\":\"const now = new Date();\\nconst year = now.getFullYear();\\nconst month = String(now.getMonth() + 1).padStart(2, ''0'');\\nconst day = String(now.getDate()).padStart(2, ''0'');\\nconst hours = String(now.getHours()).padStart(2, ''0'');\\nconst minutes = String(now.getMinutes()).padStart(2, ''0'');\\nconst seconds = String(now.getSeconds()).padStart(2, ''0'');\\n\\nvar time = "${year}${month}${day}${hours}${minutes}${seconds}";\\n\\n\\nvar res = {};\\nres.time = time;\\nres.sign = {{N1901833944131325954.vars.account}}+{{N1901833944131325954.vars.password}}+time;\\nreturn  res;\\n\\n\",\"required\":true,\"page_control\":\"JSON\"}],\"name\":\"root\",\"description\":\"更新变量的值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"更新变量\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"input\":{\"value\":{\"sign\":\"10**************\",\"time\":\"**************\"},\"var_path\":\"res\"}}', '{\"code\":200,\"msg\":\"OK\",\"value\":{\"sign\":\"H10**************\",\"time\":\"**************\"}}', 'SUCCESS', 'OK', '2025-03-18 11:13:55', '2025-03-18 11:13:55', NULL,  now(),'admin',now(), 'admin', 'iam', '0', NULL);
-- INSERT INTO "acm"."acm_flow_node" ("id", "node_type", "parent_id", "next_id", "connector_id", "action_id", "action_key", "account_id", "flow_id", "description", "config", "input", "output", "status", "error_msg", "run_start_time", "run_end_time", "proxy_id", "create_time", "create_by", "update_time", "update_by", "tenant_id", "closed", "note") VALUES (1900027796474486785, 'ActionNode', 0, 1899662076519571458, 26, 59, 'signature', NULL, 1899654617574858753, NULL, '{\"ui_id\":\"1872494833556455425\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"*************\",\"name\":\"alg\",\"type\":\"STRING\",\"display_name\":\"加密算法\",\"multi_valued\":false,\"value\":\"MD5\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"SHA-1\",\"SHA-224\",\"SHA-256\",\"SHA-384\",\"SHA-512\",\"MD5\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"*************\",\"name\":\"content\",\"type\":\"STRING\",\"description\":\"输入或粘贴需要加密的文字或数据\",\"display_name\":\"输入待加密内容\",\"multi_valued\":false,\"value\":\"{{N1900027041449435138.value.sign}}\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1735272391773\",\"name\":\"contentEncode\",\"type\":\"STRING\",\"description\":\"选择输入内容的编码方式，如 Base64、Hex、HEX、PlainText\",\"display_name\":\"选择待解密内容编码\",\"multi_valued\":false,\"value\":\"PlainText\",\"update\":true,\"required\":false,\"page_control\":\"SELECT\",\"option_values\":[\"Base64\",\"Hex\",\"HEX\",\"PlainText\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1712918687195\",\"name\":\"encode\",\"type\":\"STRING\",\"description\":\"输出编码格式\",\"display_name\":\"选择输出编码格式\",\"multi_valued\":false,\"value\":\"Hex\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"Base64\",\"Hex\",\"HEX\",\"PlainText\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', '{\"alg\":\"MD5\",\"content\":\"H0313113457\",\"content_encode\":\"PlainText\",\"encode\":\"Hex\",\"key_encode\":\"PlainText\",\"secret\":null,\"type\":null}', '{\"result\":\"da02e3e93b8a06103dcd64c866d68753\"}', 'SUCCESS', 'OK', '2025-03-14 11:42:12', '2025-03-14 11:42:12', NULL,  now(),'admin',now(), 'admin', 'iam', '0', NULL);
-- INSERT INTO "acm"."acm_flow_node" ("id", "node_type", "parent_id", "next_id", "connector_id", "action_id", "action_key", "account_id", "flow_id", "description", "config", "input", "output", "status", "error_msg", "run_start_time", "run_end_time", "proxy_id", "create_time", "create_by", "update_time", "update_by", "tenant_id", "closed", "note") VALUES (1901833944131325954, 'ActionNode', 0, 1900027041449435138, 3, 3, 'def_var', NULL, 1899654617574858753, NULL, '{\"ui_id\":\"1901833959149486082\",\"name\":\"root\",\"type\":\"OBJECT\",\"description\":\"创建一个变量，并设置初始值\",\"display_name\":\"创建变量\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1901833959149486083\",\"name\":\"vars\",\"type\":\"OBJECT\",\"description\":\"添加变量名及其初始值(js表达式)\",\"display_name\":\"添加变量\",\"multi_valued\":false,\"value\":{\"account\":\"\",\"password\":\"\"},\"update\":true,\"required\":true,\"page_control\":\"EXTEND_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":true,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', '{\"vars\":{\"password\":\"\",\"account\":\"\"}}', '{\"vars\":{\"password\":\"\",\"account\":\"\"}}', 'SUCCESS', 'OK', '2025-03-18 11:13:50', '2025-03-18 11:13:50', NULL,  now(),'admin',now(), 'admin', 'iam', '0', NULL);
--
-- INSERT INTO "acm"."acm_flow_instance" ("id", "name", "description", "flow_type", "status", "op_status", "error_msg", "run_start_time", "run_end_time", "api_key", "category", "create_time", "create_by", "update_time", "update_by", "tenant_id", "template", "is_deleted") VALUES (1899789288744869890, '浙江移动Http短信', NULL, 1, 'SUCCESS', 'ONLINE', 'OK', now(), now(), '2a054fe639544caebad18acd2ca024a4', '短信集成', now(), 'admin', now(), 'admin', 'iam', 1, 0);
-- INSERT INTO "acm"."acm_flow_node" ("id", "node_type", "parent_id", "next_id", "connector_id", "action_id", "action_key", "account_id", "flow_id", "description", "config", "input", "output", "status", "error_msg", "run_start_time", "run_end_time", "proxy_id", "create_time", "create_by", "update_time", "update_by", "tenant_id", "closed", "note") VALUES (1899789288770035714, 'StartEventNode', 0, 1901834500803547138, 1, 861, '1898942974505598978', NULL, 1899789288744869890, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1899764551543504898\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1699309157332619266\",\"option_values\":[],\"description\":\"请复制此Webhook地址，它支持常见的GET/POST/PUT/PATCH/DELETE等请求方式,请根据需求选择合适的方法进行API调用。\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"webHook地址\",\"required\":true,\"name\":\"hookUrl\",\"mutability\":\"readWrite\",\"value\":\"https://192-168-50-20-9lxgxdd3nny8td.ztna-dingtalk.com/acm/flows/start/1/1899789288744869890/2a054fe639544caebad18acd2ca024a4\",\"page_control\":\"LABEL\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1741589948996\",\"option_values\":[\"同步\",\"异步\"],\"description\":\"异步：webhook接收到数据后，将直接返回接收成功的结果以及流的信息。同步：Webhook接收到数据后，将等待连接流执行完成并将最后的执行结果返回给调用方（最长等待时间为10s，超时将返回为空）。请查看接口文档获得更多详细信息\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"连接流执行方式\",\"required\":true,\"name\":\"type\",\"mutability\":\"readWrite\",\"value\":\"同步\",\"page_control\":\"HIDDEN\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1699309157332619267\",\"name\":\"value\",\"description\":\"请按实际情况输入json格式的测试数据\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"测试数据\",\"value\":\"{\\n    \\\"mobile\\\": \\\"***********\\\",\\n    \\\"title\\\": \\\"管理员重置密码通知\\\",\\n    \\\"config\\\": {},\\n    \\\"content\\\": \\\"管理员已为您重置账号（user1）的密码，新密码为：Sdjkfjh&*78456。请及时登录并修改密码。\\\"\\n  }\",\"required\":false,\"page_control\":\"TEXTAREA\"},{\"multi_valued\":true,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1741774074249\",\"name\":\"ipList\",\"description\":\"设置可访问的IP白名单，多个IP以, 分隔\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"IP白名单\",\"value\":\"\",\"required\":false,\"page_control\":\"TEXTAREA\"}],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', '{\"hook_url\":\"https://192-168-50-20-9lxgxdd3nny8td.ztna-dingtalk.com/acm/flows/start/1/1899789288744869890/2a054fe639544caebad18acd2ca024a4\",\"sync\":true,\"type\":\"同步\",\"value\":{\"mobile\":\"***********\",\"title\":\"管理员重置密码通知\",\"config\":{},\"content\":\"管理员已为您重置账号（user1）的密码，新密码为：Sdjkfjh&*78456。请及时登录并修改密码。\"}}', '{\"hook_url\":\"https://192-168-50-20-9lxgxdd3nny8td.ztna-dingtalk.com/acm/flows/start/1/1899789288744869890/2a054fe639544caebad18acd2ca024a4\",\"sync\":true,\"type\":\"同步\",\"value\":{\"mobile\":\"***********\",\"title\":\"管理员重置密码通知\",\"config\":{},\"content\":\"管理员已为您重置账号（user1）的密码，新密码为：Sdjkfjh&*78456。请及时登录并修改密码。\"}}', 'SUCCESS', 'OK', '2025-03-13 10:12:00', '2025-03-13 10:12:00', NULL, '2025-03-14 11:42:12', 'admin', '2025-03-14 11:42:12', 'admin', 'iam', '0', NULL);
-- INSERT INTO "acm"."acm_flow_node" ("id", "node_type", "parent_id", "next_id", "connector_id", "action_id", "action_key", "account_id", "flow_id", "description", "config", "input", "output", "status", "error_msg", "run_start_time", "run_end_time", "proxy_id", "create_time", "create_by", "update_time", "update_by", "tenant_id", "closed", "note") VALUES (1899789288811978753, 'ActionNode', 0, 1900007313775452161, 4, 5, 'http-json', NULL, 1899789288744869890, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1847173075261693954\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1802915904329203714\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"请求类型\",\"required\":true,\"name\":\"httpMethod\",\"mutability\":\"readWrite\",\"value\":\"POST\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"ADAPTOR\",\"ui_id\":\"1802915904329203715\",\"name\":\"endpoint\",\"description\":\"请填写完整的接口地址\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"请求地址\",\"value\":\"https://api.openmas.net:18443/yunmas_api/smsApi/batchSendMessage\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1802915904329203716\",\"name\":\"contentType\",\"description\":\"\",\"update\":true,\"mutability\":\"readonly\",\"type\":\"STRING\",\"display_name\":\"请求体类型\",\"value\":\"application/json; charset=UTF-8\",\"required\":true,\"page_control\":\"LABEL\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1802915904329203717\",\"name\":\"header\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"Header参数\",\"value\":{},\"required\":false,\"page_control\":\"EXTEND_OBJ\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1718683627745\",\"name\":\"path\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"Path参数\",\"value\":{},\"required\":false,\"page_control\":\"EXTEND_OBJ\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1802915904329203718\",\"name\":\"query\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"Query参数\",\"value\":{},\"required\":false,\"page_control\":\"EXTEND_OBJ\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1802915904329203719\",\"name\":\"body\",\"description\":\"请输入json格式的请求参数body\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"Body参数\",\"value\":\"var mobiles = [];\\nmobiles.push({{N1899789288770035714.value.mobile}});\\nvar res = {};\\nres.applicationId = {{N1901834500803547138.vars.applicationId}};\\nres.password = {{N1901834500803547138.vars.password}};\\nres.requestTime = {{N18997**************.value.time}};\\nres.sign = {{N1899792264779718657.result}};\\nres.funCode = {{N1901834500803547138.vars.funCode}};\\nres.extendCode = {{N1901834500803547138.vars.extendCode}};\\nres.mobiles = mobiles;\\nres.content = \\\"{{N1899789288770035714.value.content}}\\\";\\nreturn res;\\n\",\"required\":false,\"page_control\":\"JSON\"}],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', '{\"account\":null,\"account_id\":null,\"auth_protocol\":null,\"body\":{\"requestTime\":\"**************\",\"password\":\"\",\"extendCode\":0,\"mobiles\":[\"***********\"],\"sign\":\"7108103943d0601690284a62316e17ba\",\"applicationId\":\"\",\"funCode\":,\"content\":\"管理员已为您重置账号（user1）的密码，新密码为：Sdjkfjh&*78456。请及时登录并修改密码。\"},\"content_type\":\"application/json; charset=UTF-8\",\"encoding\":\"UTF8\",\"endpoint\":\"https://api.openmas.net:18443/yunmas_api/smsApi/batchSendMessage\",\"header\":{\"Content-Type\":\"application/json; charset=UTF-8\"},\"http_method\":\"POST\",\"path\":{},\"proxy_ip\":null,\"query\":{},\"response_type\":\"JSON\",\"success_condition\":null,\"update_body\":null}', '{\"status_code\":200,\"header\":{\"Transfer-Encoding\":\"chunked\",\"Keep-Alive\":\"timeout=20\",\"Server\":\"\",\"x-frame-options\":\"SAMEORIGIN\",\"Connection\":\"keep-alive\",\"Date\":\"Tue, 18 Mar 2025 03:16:19 GMT\",\"Content-Type\":\"application/json;charset=UTF-8\"},\"body\":{\"resultMap\":null,\"resultCode\":1025,\"resultMsg\":\"请求时间错误。\"}}', 'SUCCESS', 'OK', '2025-03-18 11:16:19', '2025-03-18 11:16:20', NULL,  now(),'admin',now(), 'admin', 'iam', '0', NULL);
-- INSERT INTO "acm"."acm_flow_node" ("id", "node_type", "parent_id", "next_id", "connector_id", "action_id", "action_key", "account_id", "flow_id", "description", "config", "input", "output", "status", "error_msg", "run_start_time", "run_end_time", "proxy_id", "create_time", "create_by", "update_time", "update_by", "tenant_id", "closed", "note") VALUES (1899789288849727490, 'EndNode', 0, NULL, NULL, NULL, NULL, NULL, 1899789288744869890, NULL, NULL, NULL, NULL, 'IDLE', NULL, NULL, NULL, NULL,  now(),'admin',now(), 'admin', 'iam', '0', NULL);
-- INSERT INTO "acm"."acm_flow_node" ("id", "node_type", "parent_id", "next_id", "connector_id", "action_id", "action_key", "account_id", "flow_id", "description", "config", "input", "output", "status", "error_msg", "run_start_time", "run_end_time", "proxy_id", "create_time", "create_by", "update_time", "update_by", "tenant_id", "closed", "note") VALUES (18997**************, 'ActionNode', 0, 1899792264779718657, 3, 4, 'set_var', NULL, 1899789288744869890, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1899789794794356741\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1899789794794356743\",\"name\":\"varPath\",\"description\":\"选择前面步骤已经创建的变量\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"选择要更新的变量\",\"value\":\"time\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1899789794794356745\",\"name\":\"value\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"设置新的值\",\"value\":\"const now = new Date();\\nconst year = now.getFullYear();\\nconst month = String(now.getMonth() + 1).padStart(2, ''0'');\\nconst day = String(now.getDate()).padStart(2, ''0'');\\nconst hours = String(now.getHours()).padStart(2, ''0'');\\nconst minutes = String(now.getMinutes()).padStart(2, ''0'');\\nconst seconds = String(now.getSeconds()).padStart(2, ''0'');\\n\\nvar time = "${year}${month}${day}${hours}${minutes}${seconds}";\\n\\n\\nvar res = {};\\nres.time = time;\\nres.sign = {{N1901834500803547138.vars.applicationId}}+{{N1901834500803547138.vars.password}}+time+{{N1901834500803547138.vars.sign}};\\nreturn  res;\\n\\n\\n\\n\\n\",\"required\":true,\"page_control\":\"JSON\"}],\"name\":\"root\",\"description\":\"更新变量的值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"更新变量\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"input\":{\"value\":{\"sign\":\"\",\"time\":\"**************\"},\"var_path\":\"time\"}}', '{\"code\":200,\"msg\":\"OK\",\"value\":{\"sign\":\"\",\"time\":\"**************\"}}', 'SUCCESS', 'OK', '2025-03-18 11:17:37', '2025-03-18 11:17:37', NULL,  now(),'admin',now(), 'admin', 'iam', '0', NULL);
-- INSERT INTO "acm"."acm_flow_node" ("id", "node_type", "parent_id", "next_id", "connector_id", "action_id", "action_key", "account_id", "flow_id", "description", "config", "input", "output", "status", "error_msg", "run_start_time", "run_end_time", "proxy_id", "create_time", "create_by", "update_time", "update_by", "tenant_id", "closed", "note") VALUES (1899792264779718657, 'ActionNode', 0, 1899789288811978753, 26, 59, 'signature', NULL, 1899789288744869890, NULL, '{\"ui_id\":\"1872494833556455425\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"*************\",\"name\":\"alg\",\"type\":\"STRING\",\"display_name\":\"加密算法\",\"multi_valued\":false,\"value\":\"MD5\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"SHA-1\",\"SHA-224\",\"SHA-256\",\"SHA-384\",\"SHA-512\",\"MD5\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"*************\",\"name\":\"content\",\"type\":\"STRING\",\"description\":\"输入或粘贴需要加密的文字或数据\",\"display_name\":\"输入待加密内容\",\"multi_valued\":false,\"value\":\"{{N18997**************.value}}\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1735272391773\",\"name\":\"contentEncode\",\"type\":\"STRING\",\"description\":\"选择输入内容的编码方式，如 Base64、Hex、HEX、PlainText\",\"display_name\":\"选择待解密内容编码\",\"multi_valued\":false,\"value\":\"PlainText\",\"update\":true,\"required\":false,\"page_control\":\"SELECT\",\"option_values\":[\"Base64\",\"Hex\",\"HEX\",\"PlainText\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1712918687195\",\"name\":\"encode\",\"type\":\"STRING\",\"description\":\"输出编码格式\",\"display_name\":\"选择输出编码格式\",\"multi_valued\":false,\"value\":\"Hex\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"Base64\",\"Hex\",\"HEX\",\"PlainText\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', '{\"alg\":\"MD5\",\"content\":\"\",\"content_encode\":\"PlainText\",\"encode\":\"Hex\",\"key_encode\":\"PlainText\",\"secret\":null,\"type\":null}', '{\"result\":\"7108103943d0601690284a62316e17ba\"}', 'SUCCESS', 'OK', '2025-03-12 20:00:57', '2025-03-12 20:00:57', NULL,  now(),'admin',now(), 'admin', 'iam', '0', NULL);
-- INSERT INTO "acm"."acm_flow_node" ("id", "node_type", "parent_id", "next_id", "connector_id", "action_id", "action_key", "account_id", "flow_id", "description", "config", "input", "output", "status", "error_msg", "run_start_time", "run_end_time", "proxy_id", "create_time", "create_by", "update_time", "update_by", "tenant_id", "closed", "note") VALUES (1900007313775452161, 'ActionNode', 0, 1899789288849727490, 3, 4, 'set_var', NULL, 1899789288744869890, NULL, '{\"ui_id\":\"1900018246726270980\",\"name\":\"root\",\"type\":\"OBJECT\",\"description\":\"更新变量的值\",\"display_name\":\"更新变量\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1900018246726270981\",\"name\":\"varPath\",\"type\":\"STRING\",\"description\":\"选择前面步骤已经创建的变量\",\"display_name\":\"选择要更新的变量\",\"multi_valued\":false,\"value\":\"res\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1900018246726270982\",\"name\":\"value\",\"type\":\"OBJECT\",\"description\":\"\",\"display_name\":\"设置新的值\",\"multi_valued\":false,\"value\":\"var resultCode = {{N1899789288811978753.body.resultCode}};\\nreturn resultCode == 0;\",\"update\":true,\"required\":true,\"page_control\":\"JSON\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":true,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', '{\"input\":{\"value\":false,\"var_path\":\"res\"}}', '{\"code\":200,\"msg\":\"OK\",\"value\":false}', 'SUCCESS', 'OK', '2025-03-13 10:57:49', '2025-03-13 10:57:49', NULL, now(),'admin',now(), 'admin', 'iam', '0', NULL);
-- INSERT INTO "acm"."acm_flow_node" ("id", "node_type", "parent_id", "next_id", "connector_id", "action_id", "action_key", "account_id", "flow_id", "description", "config", "input", "output", "status", "error_msg", "run_start_time", "run_end_time", "proxy_id", "create_time", "create_by", "update_time", "update_by", "tenant_id", "closed", "note") VALUES (1901834500803547138, 'ActionNode', 0, 18997**************, 3, 3, 'def_var', NULL, 1899789288744869890, NULL, '{\"ui_id\":\"1901834519789518850\",\"name\":\"root\",\"type\":\"OBJECT\",\"description\":\"创建一个变量，并设置初始值\",\"display_name\":\"创建变量\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1901834519789518851\",\"name\":\"vars\",\"type\":\"OBJECT\",\"description\":\"添加变量名及其初始值(js表达式)\",\"display_name\":\"添加变量\",\"multi_valued\":false,\"value\":{\"applicationId\":\"\",\"password\":\"\",\"funCode\":\"\",\"extendCode\":\"\",\"sign\":\"\"},\"update\":true,\"required\":true,\"page_control\":\"EXTEND_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":true,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', '{\"vars\":{\"password\":\"\",\"extendCode\":0,\"sign\":\"\",\"applicationId\":\"\",\"funCode\":}}', '{\"vars\":{\"password\":\"\",\"extendCode\":0,\"sign\":\"\",\"applicationId\":\"\",\"funCode\":1002}}', 'SUCCESS', 'OK', '2025-03-18 11:17:17', '2025-03-18 11:17:17', NULL,  now(),'admin',now(), 'admin', 'iam', '0', NULL);

-- 多维表执行动作
INSERT INTO "acm"."acm_app_connector" ("id", "name", "description", "icon", "build_type", "app_domain_category", "app_action_category", "app_package", "deployment_type", "ver_name", "ver_number", "ver_type", "create_time", "create_by", "update_time", "update_by", "tenant_id") VALUES (40, '钉钉多维表', NULL, NULL, 'BuiltIn', 'COLLABORATION', NULL, '1899286402024161281', 'SaasApp', '1.0', 1, 'RELEASE', now(), 'admin', now(), 'admin', 'iam');
INSERT INTO "acm"."acm_app_action" ("id", "action_key", "is_trigger", "name", "description", "connector_id", "account_schema_id", "protocol", "action_type", "status", "config_schema", "create_time", "create_by", "update_time", "update_by", "tenant_id", "sample_output", "node_struct") VALUES (165, '1899305893403639810', 0, '创建数据表', NULL, 40, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619266\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619267\",\"update\":true,\"value\":\"https://api.dingtalk.com/v1.0/notable/bases/{baseId}/sheets\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619268\",\"update\":true,\"value\":\"application/json; charset=UTF-8\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"创建多维表必须配置操作的多维表id\",\"display_name\":\"多维表ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path.baseId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1741664968358\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"header\",\"page_control\":\"HIDDEN\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619269\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"创建多维表的Id，钉钉的Unionid\",\"display_name\":\"创建人\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.operatorId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619270\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\",\"display_name\":\"多维表名称\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.name\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619271\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"传递多维表每一列的定义\",\"display_name\":\"多维表列表\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.fields\",\"page_control\":\"TEXTAREA\",\"required\":true,\"type\":\"OBJECT\",\"ui_id\":\"1741665452526\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"预期响应\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"successCondition\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619123\",\"update\":true,\"value\":\"[{\\\"path\\\":\\\"status_code\\\",\\\"op\\\":\\\"eq\\\",\\\"value\\\":\\\"200\\\"}]\",\"value_type\":\"JS_EXP\"}],\"type\":\"OBJECT\",\"ui_id\":\"1900063398916431873\",\"update\":true,\"value_type\":\"JS_EXP\"}', now(), 'admin', now(), 'admin', 'iam', NULL, 'SINGLE');
INSERT INTO "acm"."acm_app_action" ("id", "action_key", "is_trigger", "name", "description", "connector_id", "account_schema_id", "protocol", "action_type", "status", "config_schema", "create_time", "create_by", "update_time", "update_by", "tenant_id", "sample_output", "node_struct") VALUES (166, '1899336868632281090', 0, '获取所有数据表', NULL, 40, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619266\",\"update\":true,\"value\":\"GET\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619267\",\"update\":true,\"value\":\"https://api.dingtalk.com/v1.0/notable/bases/{baseId}/sheets\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619268\",\"update\":true,\"value\":\"application/json; charset=UTF-8\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"多维表ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path.baseId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1741672241506\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"header\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619269\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"操作人的Id，使用钉钉的Unionid\",\"display_name\":\"操作人\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.operatorId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619270\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\",\"display_name\":\"Body参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619271\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"预期响应\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"successCondition\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619123\",\"update\":true,\"value\":\"[{\\\"path\\\":\\\"status_code\\\",\\\"op\\\":\\\"eq\\\",\\\"value\\\":\\\"200\\\"}]\",\"value_type\":\"JS_EXP\"}],\"type\":\"OBJECT\",\"ui_id\":\"1900063982528667650\",\"update\":true,\"value_type\":\"JS_EXP\"}', now(), 'admin', now(), 'admin', 'iam', NULL, 'SINGLE');
INSERT INTO "acm"."acm_app_action" ("id", "action_key", "is_trigger", "name", "description", "connector_id", "account_schema_id", "protocol", "action_type", "status", "config_schema", "create_time", "create_by", "update_time", "update_by", "tenant_id", "sample_output", "node_struct") VALUES (167, '1899340656768270338', 0, '获取数据表', NULL, 40, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619266\",\"update\":true,\"value\":\"GET\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619267\",\"update\":true,\"value\":\"https://api.dingtalk.com/v1.0/notable/bases/{baseId}/sheets/{sheetId}\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619268\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"多维表Id\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path.baseId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1741673153215\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"数据表Id\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path.sheetId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1741673189150\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"header\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619269\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"操作人Id 钉钉的Unionid\",\"display_name\":\"操作人\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.operatorId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1741673241314\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\",\"display_name\":\"Body参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619271\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"预期响应\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"successCondition\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619123\",\"update\":true,\"value\":\"[{\\\"path\\\":\\\"status_code\\\",\\\"op\\\":\\\"eq\\\",\\\"value\\\":\\\"200\\\"}]\",\"value_type\":\"JS_EXP\"}],\"type\":\"OBJECT\",\"ui_id\":\"1900064045732634625\",\"update\":true,\"value_type\":\"JS_EXP\"}', now(), 'admin', now(), 'admin', 'iam', NULL, 'SINGLE');
INSERT INTO "acm"."acm_app_action" ("id", "action_key", "is_trigger", "name", "description", "connector_id", "account_schema_id", "protocol", "action_type", "status", "config_schema", "create_time", "create_by", "update_time", "update_by", "tenant_id", "sample_output", "node_struct") VALUES (168, '1899341939617120258', 0, '更新数据表', NULL, 40, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619266\",\"update\":true,\"value\":\"PUT\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619267\",\"update\":true,\"value\":\"https://api.dingtalk.com/v1.0/notable/bases/{baseId}/sheets/{sheetId}\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619268\",\"update\":true,\"value\":\"application/json; charset=UTF-8\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"多维表ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path.baseId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1741673433151\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"数据表ID/名称\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path.sheetId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1741673463181\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"header\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619269\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"操作人Id，钉钉的Unionid\",\"display_name\":\"操作人\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.operatorId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619270\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"修改的内容，请求体是Json对象\\n{\\n     \\\"name\\\": \\\"Sheet1\\\"\\n}\",\"display_name\":\"Body参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body\",\"page_control\":\"EXTEND_OBJ\",\"required\":true,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619271\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"预期响应\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"successCondition\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619123\",\"update\":true,\"value\":\"[{\\\"path\\\":\\\"status_code\\\",\\\"op\\\":\\\"eq\\\",\\\"value\\\":\\\"200\\\"}]\",\"value_type\":\"JS_EXP\"}],\"type\":\"OBJECT\",\"ui_id\":\"1900064386498863106\",\"update\":true,\"value_type\":\"JS_EXP\"}', now(), 'admin', now(), 'admin', 'iam', NULL, 'SINGLE');
INSERT INTO "acm"."acm_app_action" ("id", "action_key", "is_trigger", "name", "description", "connector_id", "account_schema_id", "protocol", "action_type", "status", "config_schema", "create_time", "create_by", "update_time", "update_by", "tenant_id", "sample_output", "node_struct") VALUES (169, '1899347824905109505', 0, '删除数据表', NULL, 40, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619266\",\"update\":true,\"value\":\"DELETE\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619267\",\"update\":true,\"value\":\"https://api.dingtalk.com/v1.0/notable/bases/{baseId}/sheets/{sheetId}\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619268\",\"update\":true,\"value\":\"application/json; charset=UTF-8\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"多维表ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path.baseId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1741674891310\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"数据表ID/名称\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path.sheetId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1741674938544\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"header\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619269\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"操作人Id，钉钉的Unionid\",\"display_name\":\"操作人\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.operatorId\",\"option_values\":[],\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619270\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\",\"display_name\":\"预期响应\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"successCondition\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619123\",\"update\":true,\"value\":\"[{\\\"path\\\":\\\"status_code\\\",\\\"op\\\":\\\"eq\\\",\\\"value\\\":\\\"200\\\"}]\",\"value_type\":\"JS_EXP\"}],\"type\":\"OBJECT\",\"ui_id\":\"1900064814846353410\",\"update\":true,\"value_type\":\"JS_EXP\"}', now(), 'admin', now(), 'admin', 'iam', NULL, 'SINGLE');
INSERT INTO "acm"."acm_app_action" ("id", "action_key", "is_trigger", "name", "description", "connector_id", "account_schema_id", "protocol", "action_type", "status", "config_schema", "create_time", "create_by", "update_time", "update_by", "tenant_id", "sample_output", "node_struct") VALUES (170, '1899351428214898690', 0, '新增数据表多行记录', NULL, 40, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619266\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619267\",\"update\":true,\"value\":\"https://api.dingtalk.com/v1.0/notable/bases/{baseId}/sheets/{sheetId}/records\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619268\",\"update\":true,\"value\":\"application/json; charset=UTF-8\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"多维表ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path.baseId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1741675816658\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"数据表ID/名称\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path.sheetId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1741675875990\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\",\"display_name\":\"操作人\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.operatorId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619270\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"需要一个数组，每一个元素表示一行记录\\n[{\\n    \\\"fields\\\": {\\n      \\\"多行文本\\\": \\\"多行文本内容\\\",\\n      \\\"数字\\\": 100,\\n      \\\"单选\\\": \\\"选项1\\\",\\n      \\\"多选\\\": [\\\"选项1\\\"]\\n    }\\n  }]\",\"display_name\":\"内容\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.records\",\"page_control\":\"TEXTAREA\",\"required\":true,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619271\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"预期响应\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"successCondition\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619123\",\"update\":true,\"value\":\"[{\\\"path\\\":\\\"status_code\\\",\\\"op\\\":\\\"eq\\\",\\\"value\\\":\\\"200\\\"}]\",\"value_type\":\"JS_EXP\"}],\"type\":\"OBJECT\",\"ui_id\":\"1900064757086593026\",\"update\":true,\"value_type\":\"JS_EXP\"}', now(), 'admin', now(), 'admin', 'iam', NULL, 'SINGLE');
INSERT INTO "acm"."acm_app_action" ("id", "action_key", "is_trigger", "name", "description", "connector_id", "account_schema_id", "protocol", "action_type", "status", "config_schema", "create_time", "create_by", "update_time", "update_by", "tenant_id", "sample_output", "node_struct") VALUES (171, '1899354640087072770', 0, '获取数据表多行记录', NULL, 40, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619266\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619267\",\"update\":true,\"value\":\"https://api.dingtalk.com/v1.0/notable/bases/{baseId}/sheets/{sheetId}/records/list\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619268\",\"update\":true,\"value\":\"application/json; charset=UTF-8\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"多维表ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path.baseId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1741676476580\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"数据表ID/名称\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path.sheetId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1741676523529\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"操作人Id，钉钉的UnionId\",\"display_name\":\"操作人\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.operatorId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619270\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"每次拉取的行数，最大值为100\",\"display_name\":\"行数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.maxResults\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"NUMBER\",\"ui_id\":\"1699309157332619271\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"数据拉取的起始位置。首次调用时不传，后续调用时使用服务端返回的nextToken参数。\",\"display_name\":\"标记\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.nextToken\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1741676696418\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\",\"display_name\":\"预期响应\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"successCondition\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619123\",\"update\":true,\"value\":\"[{\\\"path\\\":\\\"status_code\\\",\\\"op\\\":\\\"eq\\\",\\\"value\\\":\\\"200\\\"}]\",\"value_type\":\"JS_EXP\"}],\"type\":\"OBJECT\",\"ui_id\":\"1900065032442650626\",\"update\":true,\"value_type\":\"JS_EXP\"}', now(), 'admin', now(), 'admin', 'iam', NULL, 'SINGLE');
INSERT INTO "acm"."acm_app_action" ("id", "action_key", "is_trigger", "name", "description", "connector_id", "account_schema_id", "protocol", "action_type", "status", "config_schema", "create_time", "create_by", "update_time", "update_by", "tenant_id", "sample_output", "node_struct") VALUES (172, '1899356335999369217', 0, '获取数据表单行记录', NULL, 40, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619266\",\"update\":true,\"value\":\"GET\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619267\",\"update\":true,\"value\":\"https://api.dingtalk.com/v1.0/notable/bases/{baseId}/sheets/{sheetId}/records/{recordId}\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619268\",\"update\":true,\"value\":\"application/json; charset=UTF-8\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"多维表ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path.baseId\",\"option_values\":[],\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1741676895961\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"数据表ID/名称\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path.sheetId\",\"option_values\":[],\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1741676963500\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"记录ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path.recordId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1741677010317\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"操作人Id，钉钉的UnionId\",\"display_name\":\"操作人\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.operatorId\",\"option_values\":[],\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619270\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\",\"display_name\":\"预期响应\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"successCondition\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619123\",\"update\":true,\"value\":\"[{\\\"path\\\":\\\"status_code\\\",\\\"op\\\":\\\"eq\\\",\\\"value\\\":\\\"200\\\"}]\",\"value_type\":\"JS_EXP\"}],\"type\":\"OBJECT\",\"ui_id\":\"1900065129578536962\",\"update\":true,\"value_type\":\"JS_EXP\"}', now(), 'admin', now(), 'admin', 'iam', NULL, 'SINGLE');
INSERT INTO "acm"."acm_app_action" ("id", "action_key", "is_trigger", "name", "description", "connector_id", "account_schema_id", "protocol", "action_type", "status", "config_schema", "create_time", "create_by", "update_time", "update_by", "tenant_id", "sample_output", "node_struct") VALUES (173, '1899358430454431746', 0, '更新数据表多行记录', NULL, 40, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619266\",\"update\":true,\"value\":\"PUT\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619267\",\"update\":true,\"value\":\"https://api.dingtalk.com/v1.0/notable/bases/{baseId}/sheets/{sheetId}/records\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619268\",\"update\":true,\"value\":\"application/json; charset=UTF-8\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"多维表ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path.baseId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1741677401822\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"数据表ID/名称\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path.sheetId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1741677441805\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\",\"display_name\":\"操作人\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.operatorId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619270\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"传递数组 每一个元素表示一行记录。\\n数据结构如下：\\n{\\n    \\\"id\\\": \\\"xxx\\\",\\n    \\\"fields\\\": {\\n      \\\"多行文本\\\": \\\"多行文本内容\\\",\\n      \\\"数字\\\": 100,\\n      \\\"单选\\\": \\\"选项1\\\",\\n      \\\"多选\\\": [\\\"选项1\\\"]\\n    }\\n  }]\",\"display_name\":\"更新记录\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.records\",\"page_control\":\"TEXTAREA\",\"required\":true,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619271\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"预期响应\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"successCondition\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619123\",\"update\":true,\"value\":\"[{\\\"path\\\":\\\"status_code\\\",\\\"op\\\":\\\"eq\\\",\\\"value\\\":\\\"200\\\"}]\",\"value_type\":\"JS_EXP\"}],\"type\":\"OBJECT\",\"ui_id\":\"1900065250747785217\",\"update\":true,\"value_type\":\"JS_EXP\"}', now(), 'admin', now(), 'admin', 'iam', NULL, 'SINGLE');
INSERT INTO "acm"."acm_app_action" ("id", "action_key", "is_trigger", "name", "description", "connector_id", "account_schema_id", "protocol", "action_type", "status", "config_schema", "create_time", "create_by", "update_time", "update_by", "tenant_id", "sample_output", "node_struct") VALUES (174, '1899359939741171713', 0, '删除数据表多条记录', NULL, 40, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619266\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619267\",\"update\":true,\"value\":\"https://api.dingtalk.com/v1.0/notable/bases/{baseId}/sheets/{sheetId}/records/delete\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619268\",\"update\":true,\"value\":\"application/json; charset=UTF-8\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"多维表ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path.baseId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1741677820413\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"数据表ID/名称\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path.sheetId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1741677873716\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"操作人ID，钉钉的UnionId\",\"display_name\":\"操作人\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.operatorId\",\"option_values\":[],\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619270\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"需要删除的记录ID数组\",\"display_name\":\"记录ID\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.recordIds\",\"page_control\":\"TEXTAREA\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619271\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"预期响应\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"successCondition\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619123\",\"update\":true,\"value\":\"[{\\\"path\\\":\\\"status_code\\\",\\\"op\\\":\\\"eq\\\",\\\"value\\\":\\\"200\\\"}]\",\"value_type\":\"JS_EXP\"}],\"type\":\"OBJECT\",\"ui_id\":\"1900065297258422274\",\"update\":true,\"value_type\":\"JS_EXP\"}', now(), 'admin', now(), 'admin', 'iam', NULL, 'SINGLE');


alter table "acm"."acm_flow_log" add column error_msg text;
COMMENT ON COLUMN "acm"."acm_flow_log"."error_msg" IS '连接流运行错误信息';

CREATE TABLE "acm"."acm_flow_message"
(
    "id" BIGINT NOT NULL,
    "flow_id" BIGINT NOT NULL,
    "task_id" BIGINT NOT NULL,
    "query" LONGVARCHAR NULL,
    "header" LONGVARCHAR NULL,
    "body" LONGVARCHAR NULL,
    "body_is_text" TINYINT DEFAULT 0,
    "record_success_log" TINYINT DEFAULT 1,
    "status" TINYINT DEFAULT 1,
    "create_time" TIMESTAMP(0) NULL,
    "update_time" TIMESTAMP(0) NULL,
    "tenant_id" VARCHAR(4096) NOT NULL
);

ALTER TABLE "acm"."acm_flow_message" ADD CONSTRAINT  NOT CLUSTER  PRIMARY KEY("id") ;
CREATE INDEX "acm"."status_index" ON "acm"."acm_flow_message"("status");
CREATE INDEX "acm"."tenant_index" ON "acm"."acm_flow_message"("tenant_id");

COMMENT ON TABLE "acm"."acm_flow_message" IS '连接流消息记录表';
COMMENT ON COLUMN "acm"."acm_flow_message"."id" IS '主键';
COMMENT ON COLUMN "acm"."acm_flow_message"."flow_id" IS '连接流ID';
COMMENT ON COLUMN "acm"."acm_flow_message"."task_id" IS '任务ID';
COMMENT ON COLUMN "acm"."acm_flow_message"."query" IS 'query参数，json字符串';
COMMENT ON COLUMN "acm"."acm_flow_message"."header" IS 'header参数，json字符串';
COMMENT ON COLUMN "acm"."acm_flow_message"."body" IS 'body参数，字符串或json字符串';
COMMENT ON COLUMN "acm"."acm_flow_message"."body_is_text" IS 'body是否为普通文本：0、json字符串；1、普通文本';
COMMENT ON COLUMN "acm"."acm_flow_message"."record_success_log" IS '是否记录成功日志：0、不记录；1、记录';
COMMENT ON COLUMN "acm"."acm_flow_message"."status" IS '执行状态：0、未执行；1、已执行';
COMMENT ON COLUMN "acm"."acm_flow_message"."create_time" IS '创建时间';
COMMENT ON COLUMN "acm"."acm_flow_message"."update_time" IS '更新时间';
COMMENT ON COLUMN "acm"."acm_flow_message"."tenant_id" IS '租户ID';

