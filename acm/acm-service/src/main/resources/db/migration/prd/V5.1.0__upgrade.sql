-- 宜搭
INSERT IGNORE INTO acm.acm_app_connector (id, name, description, icon, build_type, app_domain_category, app_action_category, app_package, ver_name, ver_number, ver_type, create_time, create_by, update_time, update_by, tenant_id, deployment_type) VALUES (1698903860229296130, '宜搭', NULL, NULL, 'Market', 'LOW_CODE_PLATFORM', NULL, '1698903860227158017', '1.0', 1, 'RELEASE', '2023-09-05 11:40:19', 'admin', '2024-05-31 14:41:09', 'admin', 'iam', 'SaasApp');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1698903982090604546, '1698903982088466433', 0, '查询表单实例数据', '调用本接口查询表单实例数据', 1698903860229296130, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://api.dingtalk.com/v1.0/yida/forms/instances/search\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"表单ID\",\"display_name\":\"表单ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.formUuid\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698904879677911041\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"应用秘钥\",\"display_name\":\"应用秘钥\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.systemToken\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698904879677911043\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"语言。取值：\\n\\nzh_CN：中文（默认值）\\nen_US：英文\",\"display_name\":\"语言\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.language\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1698904879682105348\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"根据表单内组件值查询\",\"display_name\":\"根据表单内组件值查询\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.searchFieldJson\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1698904879682105349\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"用户userid\",\"display_name\":\"用户userid\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.userId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698904879682105350\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"应用编码\",\"display_name\":\"应用编码\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.appType\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698904879682105351\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"根据数据提交人工号查询\",\"display_name\":\"人工号查询\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.originatorId\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1698904879682105352\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"分页参数，当前页\",\"display_name\":\"当前页\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.currentPage\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"NUMBER\",\"ui_id\":\"1698904879682105353\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"分页参数，每页显示条数\",\"display_name\":\"每页显示条数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.pageSize\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"NUMBER\",\"ui_id\":\"1693885760821\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"查询创建数据列表的开始时间\",\"display_name\":\"开始时间\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.createFromTimeGMT\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1698904879682105355\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\\t\\n查询创建数据列表的结束时间\",\"display_name\":\"结束时间\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.createToTimeGMT\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1693885896645\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"查询修改数据列表的开始时间\",\"display_name\":\"修改开始时间\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.modifiedFromTimeGMT\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1693885992285\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"查询修改数据列表的结束时间\",\"display_name\":\"修改结束时间\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.modifiedToTimeGMT\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1693886070065\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"指定排序字段\",\"display_name\":\"指定排序字段\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.dynamicOrder\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1813527849050685442\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-05 11:40:48', 'admin', '2024-09-05 16:28:19', 'linkadmin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1698909114371002370, '1698909114368864258', 0, '查询表单数据', '调用本接口通过表单实例ID查询表单数据', 1698903860229296130, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"GET\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://oapi.dingtalk.com/v1.0/yida/forms/instances/{id}\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"表单实例ID\",\"display_name\":\"表单实例ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path.id\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1693886674564\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"用户userId\",\"display_name\":\"用户userId\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.userId\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1693883316728\",\"update\":true,\"value\":\"\",\"value_type\":\"ADAPTOR\"},{\"description\":\"应用编码\",\"display_name\":\"应用编码\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.appType\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"父任务ID\",\"display_name\":\"父任务ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.parentTaskId\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"应用秘钥\",\"display_name\":\"应用秘钥\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.systemToken\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1693886822364\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"语言，取值：\\n\\nzh_CN：中文（默认值）\\nen_US：英文\",\"display_name\":\"语言\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.language\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1704042617641185281\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-05 12:01:12', 'admin', '2023-09-19 15:59:54', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1698930246184718337, '1698930246182580225', 0, '更新表单数据', '调用本接口更新表单数据', 1698903860229296130, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"PUT\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://api.dingtalk.com/v1.0/yida/forms/instances\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\\t\\n使用最新的表单版本进行更新\",\"display_name\":\"最新的表单版本\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.useLatestVersion\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"BOOLEAN\",\"ui_id\":\"1698930737385910274\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"应用ID\",\"display_name\":\"应用ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.appType\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1698930737385910275\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"更新的表单数据\",\"display_name\":\"更新的表单数据\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.updateFormDataJson\",\"page_control\":\"JSON\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698930737385910276\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"应用秘钥\",\"display_name\":\"应用秘钥\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.systemToken\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698930737385910277\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\\t\\n语言，取值：\\n\\nzh_CN：中文（默认值）\\nen_US：英文\",\"display_name\":\"语言\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.language\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1698930737385910278\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"流程实例ID\",\"display_name\":\"流程实例ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.formInstanceId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698930737385910279\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"用户的userid\",\"display_name\":\"用户的userid\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.userId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698930737385910280\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1813527756192989185\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-05 13:25:10', 'admin', '2024-07-17 18:54:45', 'linkadmin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1698932103867125761, '1698932103864987650', 0, '删除表单数据', '调用本接口删除表单数据', 1698903860229296130, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"DELETE\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://oapi.dingtalk.com/v1.0/yida/forms/instances\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"应用编码\",\"display_name\":\"应用编码\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.appType\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698930737385910275\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"应用秘钥\",\"display_name\":\"应用秘钥\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.systemToken\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698930737385910277\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\\t\\n语言，取值：\\n\\nzh_CN：中文（默认值）\\nen_US：英文\",\"display_name\":\"语言\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.language\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1698930737385910278\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"流程实例ID\",\"display_name\":\"流程实例ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.formInstanceId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698930737385910279\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"用户的userid\",\"display_name\":\"用户的userid\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.userId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698930737385910280\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1704043002372108289\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-05 13:32:33', 'admin', '2023-09-19 16:01:30', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1698933296941092865, '1698933296938954754', 0, '发起宜搭审批流程', '调用本接口发起宜搭审批流程到钉钉开放平台', 1698903860229296130, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://oapi.dingtalk.com/v1.0/yida/processes/instances/start\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"应用编码\",\"display_name\":\"应用编码\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.appType\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698930737385910275\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"应用秘钥\",\"display_name\":\"应用秘钥\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.systemToken\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698930737385910277\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\\t\\n语言，取值：\\n\\nzh_CN：中文（默认值）\\nen_US：英文\",\"display_name\":\"语言\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.language\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1698930737385910278\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"用户的userid\",\"display_name\":\"用户的userid\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.userId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698930737385910280\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"表单唯一编码\",\"display_name\":\"表单唯一编码\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.formUuid\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698934054669332481\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"流程编码\",\"display_name\":\"流程编码\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.processCode\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1698934054669332482\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"表单数据\",\"display_name\":\"表单数据\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.formDataJson\",\"page_control\":\"JSON\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698934054669332483\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"发起人所在部门ID\",\"display_name\":\"发起人所在部门ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.departmentId\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1698934054669332484\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1704043237488013313\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-05 13:37:17', 'admin', '2023-09-19 16:02:22', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1698934709125500930, '1698934709123362818', 0, '获取流程实例', '用于获取宜搭的流程实例信息，包括实例Id、创建时间、发起人等信息', 1698903860229296130, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://oapi.dingtalk.com/v1.0/yida/processes/instances\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"分页页码\",\"display_name\":\"分页页码\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.pageNumber\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1693892736127\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"分页大小\",\"display_name\":\"分页大小\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.pageSize\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1693892735383\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"应用ID\",\"display_name\":\"应用ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.appType\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698930737385910275\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"应用秘钥\",\"display_name\":\"应用秘钥\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.systemToken\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698930737385910277\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\\t\\n语言，取值：\\n\\nzh_CN：中文（默认值）\\nen_US：英文\",\"display_name\":\"语言\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.language\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1698930737385910278\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"用户的userid\",\"display_name\":\"用户的userid\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.userId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698930737385910280\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"表单唯一编码\",\"display_name\":\"表单唯一编码\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.formUuid\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698934054669332481\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"流程审批结果\",\"display_name\":\"流程审批结果\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.approvedResult\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1698935986406371330\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"修改时间起始值\",\"display_name\":\"修改时间起始值\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.modifiedToTimeGMT\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1698935986406371331\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"修改时间终止值\",\"display_name\":\"修改时间终止值\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.modifiedFromTimeGMT\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1698935986406371332\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"查询过滤条件，支持2种模式的过滤规则。\",\"display_name\":\"过滤条件\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.searchFieldJson\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1698935986406371333\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"根据流程发起人工号查询\",\"display_name\":\"人工号\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.originatorId\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1698935986406371334\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"实例状态\",\"display_name\":\"实例状态\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.instanceStatus\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1698935986406371336\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"创建时间起始值\",\"display_name\":\"创建时间起始值\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.createFromTimeGMT\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1698935986406371337\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"创建时间终止值\",\"display_name\":\"创建时间终止值\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.createToTimeGMT\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1693893267044\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"任务ID\",\"display_name\":\"任务ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.taskId\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1698935986406371338\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"排序规则\",\"display_name\":\"排序规则\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.orderConfigJson\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1704043669711040514\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-05 13:42:54', 'admin', '2023-09-19 16:04:05', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1730471674221416449, '1730471674217787394', 0, '保存表单数据', '将数据保存到宜搭对应应用的表单中', 1698903860229296130, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619266\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619267\",\"update\":true,\"value\":\"https://api.dingtalk.com/v1.0/yida/forms/instances\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619268\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"应用编码\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.appType\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619269\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"应用秘钥\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.systemToken\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1701411842251\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"用户userId\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.userId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1701411923983\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"语言\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.language\",\"option_values\":[\"zh_CN\",\"en_US\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701412000558\",\"update\":true,\"value\":\"zh_CN\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"表单ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.formUuid\",\"option_values\":[],\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1701412069438\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"表单数据\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.formDataJson\",\"option_values\":[],\"page_control\":\"JSON\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"JS_EXP\"}],\"type\":\"OBJECT\",\"ui_id\":\"1795860406370070529\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-12-01 14:19:32', 'admin', '2024-07-17 18:54:07', 'linkadmin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1750095889026621442, '1750095889028988930', 0, '更新表单信息（新）', '调用本接口更新表单数据', 1698903860229296130, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619266\",\"update\":true,\"value\":\"PUT\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619267\",\"update\":true,\"value\":\"https://api.dingtalk.com/v1.0/yida/forms/instances\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619268\",\"update\":true,\"value\":\"application/json\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"Body参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body\",\"page_control\":\"TEXTAREA\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619271\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"断言该接口的响应结果。例：[{\\\"path\\\":\\\"响应参数名\\\",\\\"op\\\":\\\"运算符\\\",\\\"value\\\":\\\"节点响应结果\\\"},\\\"path\\\":\\\"响应参数名2\\\",\\\"op\\\":\\\"运算符2\\\",\\\"value\\\":\\\"节点响应结果2\\\"]。；运算符示例：\\\"eq\\\"表示等于\\\"neq\\\"表示不等于；\\\"ne\\\"表示不相等；\\\"contains\\\"表示包含\",\"display_name\":\"预期响应\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"successCondition\",\"page_control\":\"HIDDEN\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619123\",\"update\":true,\"value\":\"[{\\\"path\\\":\\\"status_code\\\",\\\"op\\\":\\\"eq\\\",\\\"value\\\":\\\"200\\\"}]\",\"value_type\":\"JS_EXP\"}],\"type\":\"OBJECT\",\"ui_id\":\"1750108366227750914\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2024-01-24 17:59:10', 'admin', '2024-01-24 18:48:45', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (61, '1790333639631601666', 0, '获取组织内已完成的审批任务', '获取组织内已完成的审批任务', 1698903860229296130, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619266\",\"update\":true,\"value\":\"GET\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619267\",\"update\":true,\"value\":\"https://api.dingtalk.com/v1.0/yida/tasks/completedTasks/{corpId}/{userId}\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[],\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619268\",\"update\":true,\"value\":\"application/json; charset=UTF-8\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"组织的corpId\",\"display_name\":\"组织的corpId\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path.corpId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619269\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"用户userid\",\"display_name\":\"用户userid\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path.userId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1715684266254\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"分页大小\",\"display_name\":\"分页大小\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.pageSize\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"NUMBER\",\"ui_id\":\"***********32619270\",\"update\":true,\"value\":\"10\",\"value_type\":\"ADAPTOR\"},{\"description\":\"分页页码\",\"display_name\":\"分页页码\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.pageNumber\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"NUMBER\",\"ui_id\":\"***********32619271\",\"update\":true,\"value\":\"1\",\"value_type\":\"ADAPTOR\"},{\"description\":\"语言\",\"display_name\":\"语言\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.language\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1715684316812\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"表单中组件数据模糊搜索\",\"display_name\":\"表单中组件数据模糊搜索\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.keyword\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1715684404269\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"应用标识\",\"display_name\":\"应用标识\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.appTypes\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1715684413795\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"流程code\",\"display_name\":\"流程code\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.processCodes\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1715684432824\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"创建时间起始值\",\"display_name\":\"创建时间起始值\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.createFromTimeGMT\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"NUMBER\",\"ui_id\":\"1715684449778\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"创建时间终止值\",\"display_name\":\"创建时间终止值\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.createToTimeGMT\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"NUMBER\",\"ui_id\":\"1715684465892\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"验权token。\\n\\n校验方式如下：md5(corpId + userId + code)。md5取32位大写值\",\"display_name\":\"验权token\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.token\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1790339259541782529\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2024-05-14 18:49:37', 'admin', '2024-05-14 19:11:57', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (62, '1790333639631601667', 0, '查询企业下用户待办列表', '查询企业下用户待办列表', 1698903860229296130, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619266\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619267\",\"update\":true,\"value\":\"https://api.dingtalk.com/v1.0/todo/users/{unionId}/org/tasks/query\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[],\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619268\",\"update\":true,\"value\":\"application/json; charset=UTF-8\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"用户的unionId\",\"display_name\":\"组织的unionId\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path.unionId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619269\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"分页游标\",\"display_name\":\"分页游标\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.nextToken\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"***********32619270\",\"update\":true,\"value\":\"10\",\"value_type\":\"ADAPTOR\"},{\"description\":\"待办完成状态\",\"display_name\":\"待办完成状态\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.isDone\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"BOOLEAN\",\"ui_id\":\"***********32619271\",\"update\":true,\"value\":\"1\",\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1790347048934735873\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2024-05-14 18:49:37', 'admin', '2024-05-14 19:42:54', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (64, '1790333639631601669', 0, '获取任务列表（组织维度）', '获取任务列表（组织维度）', 1698903860229296130, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619266\",\"update\":true,\"value\":\"GET\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619267\",\"update\":true,\"value\":\"https://api.dingtalk.com/v1.0/yida/corpTasks\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[],\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619268\",\"update\":true,\"value\":\"application/json; charset=UTF-8\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"组织的corpId\",\"display_name\":\"组织的corpId\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.corpId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619269\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"用户userid\",\"display_name\":\"用户userid\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.userId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1715684266254\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"分页大小\",\"display_name\":\"分页大小\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.pageSize\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"NUMBER\",\"ui_id\":\"***********32619270\",\"update\":true,\"value\":\"10\",\"value_type\":\"ADAPTOR\"},{\"description\":\"分页页码\",\"display_name\":\"分页页码\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.pageNumber\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"NUMBER\",\"ui_id\":\"***********32619271\",\"update\":true,\"value\":\"1\",\"value_type\":\"ADAPTOR\"},{\"description\":\"语言\",\"display_name\":\"语言\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.language\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1715684316812\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"表单中组件数据模糊搜索\",\"display_name\":\"表单中组件数据模糊搜索\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.keyword\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1715684404269\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"应用标识\",\"display_name\":\"应用标识\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.appTypes\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1715684413795\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"流程code\",\"display_name\":\"流程code\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.processCodes\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1715684432824\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"创建时间起始值\",\"display_name\":\"创建时间起始值\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.createFromTimeGMT\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"NUMBER\",\"ui_id\":\"1715684449778\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"创建时间终止值\",\"display_name\":\"创建时间终止值\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.createToTimeGMT\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"NUMBER\",\"ui_id\":\"1715684465892\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"验权token。\\n\\n校验方式如下：md5(corpId + userId + code)。md5取32位大写值\",\"display_name\":\"验权token\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.token\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1790713339155030017\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2024-05-14 18:49:37', 'admin', '2024-05-15 19:58:25', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1796390323347341313, '1796390323341086721', 0, '根据流程实例ID获取流程实例', '根据流程实例ID获取流程实例', 1698903860229296130, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619266\",\"update\":true,\"value\":\"GET\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"option_values\":[],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619267\",\"update\":true,\"value\":\"https://api.dingtalk.com/v1.0/yida/processes/instancesInfos/{id}\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619268\",\"update\":true,\"value\":\"application/json\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"header\",\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"OBJECT\",\"ui_id\":\"***********32619269\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"PATH参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path\",\"page_control\":\"EXTEND_OBJ\",\"required\":true,\"type\":\"OBJECT\",\"ui_id\":\"***********32619270\",\"update\":true,\"value\":\"ID\",\"value_type\":\"ADAPTOR\"},{\"display_name\":\"Query\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query\",\"page_control\":\"EXTEND_OBJ\",\"required\":true,\"type\":\"OBJECT\",\"ui_id\":\"1717130805801\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\",\"display_name\":\"Body参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body\",\"page_control\":\"HIDDEN\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619271\",\"update\":true,\"value_type\":\"JS_EXP\"}],\"type\":\"OBJECT\",\"ui_id\":\"1796431700129075202\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2024-05-31 11:56:43', 'admin', '2024-08-13 14:46:56', 'linkadmin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1831608062483202049, '1831608062481383426', 0, '获取审批记录 ', '获取审批记录 ', 1698903860229296130, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619266\",\"update\":true,\"value\":\"GET\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619267\",\"update\":true,\"value\":\"https://api.dingtalk.com/v1.0/yida/processes/operationRecords\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619268\",\"update\":true,\"value\":\"application/json; charset=UTF-8\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"应用ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.appType\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619270\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"应用秘钥\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.systemToken\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1725524686854\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"用户id\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.userId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1725524738226\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"语言，取值：\\n\\nzh_CN：中文（默认值）\\nen_US：英文\",\"display_name\":\"语言\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.language\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1725524738146\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"流程实例ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.processInstanceId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1831610158719025154\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2024-09-05 16:19:26', 'linkadmin', '2024-09-05 16:28:14', 'linkadmin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1846099179815923714, '1846099179813851137', 0, '通过高级查询条件获取表单实例数据（包括子表单组件数据）', '使用筛选条件获取表单实例详情。', 1698903860229296130, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619266\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619267\",\"update\":true,\"value\":\"https://api.dingtalk.com/v2.0/yida/forms/instances/advances/queryAll\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619268\",\"update\":true,\"value\":\"application/json\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"页码\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.pageNumber\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"NUMBER\",\"ui_id\":\"1846099390204325889\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"最大值100\",\"display_name\":\"每页最大条目数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.pageSize\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"NUMBER\",\"ui_id\":\"1728979789795\",\"update\":true,\"value\":\"\",\"value_type\":\"ADAPTOR\"},{\"display_name\":\"表单编码\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.formUuid\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1846099390204325890\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"应用密钥\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.systemToken\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1728979754794\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"检索条件\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.searchCondition\",\"page_control\":\"TEXTAREA\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1846099390204325891\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"格式yyyy-MM-dd HH:mm:ss\",\"display_name\":\"修改的截止时间\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.modifiedToTimeGMT\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1846099390204325892\",\"update\":true,\"value\":\"\",\"value_type\":\"ADAPTOR\"},{\"description\":\"格式yyyy-MM-dd HH:mm:ss\",\"display_name\":\"修改开始时间\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.modifiedFromTimeGMT\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1846099390204325894\",\"update\":true,\"value\":\"\",\"value_type\":\"ADAPTOR\"},{\"display_name\":\"用户userId。\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.userId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1846099390204325896\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"应用编码\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.appType\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1846099390204325897\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"排序规则\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.orderConfigJson\",\"page_control\":\"TEXTAREA\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1846099390204325898\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"display_name\":\"表单提交人的userId\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.originatorId\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1846099390204325899\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"格式yyyy-MM-dd HH:mm:ss\",\"display_name\":\"创建的截止时间\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.createToTimeGMT\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1846099390204325900\",\"update\":true,\"value\":\"\",\"value_type\":\"ADAPTOR\"},{\"description\":\"格式yyyy-MM-dd HH:mm:ss\",\"display_name\":\"创建的开始时间\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.createFromTimeGMT\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1846099390204325901\",\"update\":true,\"value\":\"\",\"value_type\":\"ADAPTOR\"},{\"display_name\":\"是否使用组件别名\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.useAlias\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"BOOLEAN\",\"ui_id\":\"1846099390204325902\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1846106707785695234\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2024-10-15 16:01:58', 'linkadmin', '2024-10-15 16:31:57', 'linkadmin', 'iam', NULL, 'SINGLE');

-- 金蝶云星空
INSERT IGNORE INTO acm.acm_app_connector (id, name, description, icon, build_type, app_domain_category, app_action_category, app_package, ver_name, ver_number, ver_type, create_time, create_by, update_time, update_by, tenant_id, deployment_type) VALUES (1729027738628558849, '金蝶云星空', '金蝶云星空', NULL, 'Market', 'CRM_ERP', NULL, '1729027738546819074', '1.0', 1, 'RELEASE', '2023-11-27 14:41:51', 'admin', '2023-11-30 15:06:19', 'admin', 'iam', 'SaasApp');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1729031168805867522, '1729031168808013825', 0, '提交付款申请单', NULL, 1729027738628558849, 14, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"POST\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619266\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619267\",\"update\":true,\"value\":\"https://openapi.open.kingdee.com/cross/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Submit.common.kdsvc\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619268\",\"update\":true,\"value\":\"applicationjson; charset=UTF-8\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"header\",\"page_control\":\"HIDDEN\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619269\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Query参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query\",\"page_control\":\"HIDDEN\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619270\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"display_name\":\"业务对象表单Id\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"formid\",\"option_values\":[],\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1701069894115\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\",\"display_name\":\"创建者组织内码\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.CreateOrgId\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"NUMBER\",\"ui_id\":\"***********32619271\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"单据编码集合，数组类型，格式：[No1,No2,...]（使用编码时必录）\",\"display_name\":\"单据编码集合\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.Numbers\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701070062240\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"单据内码集合，字符串类型，格式：\\\"Id1,Id2,...\\\"（使用内码时必录）\",\"display_name\":\"单据内码集合\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.Ids\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701070159764\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"工作流发起员工岗位内码，整型（非必录） 注（员工身兼多岗时不传参默认取第一个岗位）\",\"display_name\":\"工作流发起员工岗位内码\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.SelectedPostId\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"NUMBER\",\"ui_id\":\"1701070186165\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"是否启用网控，布尔类型，默认false（非必录）\",\"display_name\":\"是否启用网控\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.NetworkCtrl\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701070262473\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"是否允许忽略交互，布尔类型，默认true（非必录）\",\"display_name\":\"是否允许忽略交互\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.IgnoreInterationFlag\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1730137667827429377\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-11-27 14:55:29', 'admin', '2023-12-01 18:56:49', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1729042628613279746, '1729042628611231745', 0, '提交物料', NULL, 1729027738628558849, 14, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"POST\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619266\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619267\",\"update\":true,\"value\":\" https://openapi.open.kingdee.com/cross/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Submit.common.kdsvc\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619268\",\"update\":true,\"value\":\"application/json; charset=UTF-8\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"业务对象表单Id，字符串类型（必录）\",\"display_name\":\"业务对象表单Id\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"formid\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1701070967705\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"创建者组织内码（非必录）\",\"display_name\":\"Body参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.CreateOrgId\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"NUMBER\",\"ui_id\":\"***********32619271\",\"update\":true,\"value\":\"\",\"value_type\":\"ADAPTOR\"},{\"description\":\"单据编码集合，数组类型，格式：[No1,No2,...]（使用编码时必录）\",\"display_name\":\"单据编码集合\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.Numbers\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701071110453\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"单据内码集合，字符串类型，格式：\\\"Id1,Id2,...\\\"（使用内码时必录）\",\"display_name\":\"单据内码集合\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.Ids\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701071157739\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\\t\\n工作流发起员工岗位内码，整型（非必录） 注（员工身兼多岗时不传参默认取第一个岗位）\",\"display_name\":\"工作流发起员工岗位内码\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.SelectedPostId\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"NUMBER\",\"ui_id\":\"1701071237319\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"是否启用网控，布尔类型，默认false（非必录）\",\"display_name\":\"是否启用网控\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.NetworkCtrl\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701071287439\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"是否允许忽略交互，布尔类型，默认true（非必录）\",\"display_name\":\"是否允许忽略交互\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.IgnoreInterationFlag\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1730137784898842626\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-11-27 15:41:01', 'admin', '2023-11-30 16:12:47', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1729046294560804865, '1729046294571339777', 0, '提交供应商', NULL, 1729027738628558849, 14, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"POST\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619266\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619267\",\"update\":true,\"value\":\"https://openapi.open.kingdee.com/cross/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Submit.common.kdsvc\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619268\",\"update\":true,\"value\":\"application/json; charset=UTF-8\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"业务对象表单Id，字符串类型（必录）\",\"display_name\":\"业务对象表单Id\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"formid\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1701073859656\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"创建者组织内码（非必录）\",\"display_name\":\"创建者组织内码\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.CreateOrgId\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"***********32619271\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"单据编码集合，数组类型，格式：[No1,No2,...]（使用编码时必录）\",\"display_name\":\"单据编码集合\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.Numbers\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701073962903\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"单据内码集合，字符串类型，格式：\\\"Id1,Id2,...\\\"（使用内码时必录）\",\"display_name\":\"单据内码集合\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.Ids\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701074004266\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"工作流发起员工岗位内码，整型（非必录） 注（员工身兼多岗时不传参默认取第一个岗位）\",\"display_name\":\"工作流发起员工岗位内码\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.SelectedPostId\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701074085540\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"是否启用网控，布尔类型，默认false（非必录）\",\"display_name\":\"是否启用网控\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.NetworkCtrl\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701074107581\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"是否允许忽略交互，布尔类型，默认true（非必录）\",\"display_name\":\"是否允许忽略交互\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.IgnoreInterationFlag\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1730138255717851137\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-11-27 15:55:35', 'admin', '2023-11-30 16:14:39', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1729071538797318146, '1729071538799464449', 0, '提交凭证', NULL, 1729027738628558849, 14, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"POST\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619266\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619267\",\"update\":true,\"value\":\" https://openapi.open.kingdee.com/cross/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Submit.common.kdsvc\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619268\",\"update\":true,\"value\":\"applicationjson; charset=UTF-8\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"业务对象表单Id，字符串类型（必录）\",\"display_name\":\"业务对象表单Id\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"formid\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1701078066193\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\\t\\n创建者组织内码（非必录）\",\"display_name\":\"创建者组织内码\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.CreateOrgId\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"***********32619271\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"单据编码集合，数组类型，格式：[No1,No2,...]（使用编码时必录）\",\"display_name\":\"单据编码集合\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.Numbers\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701078145327\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"单据内码集合，字符串类型，格式：\\\"Id1,Id2,...\\\"（使用内码时必录）\",\"display_name\":\"单据内码集合\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.Ids\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701078169142\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\\t\\n工作流发起员工岗位内码，整型（非必录） 注（员工身兼多岗时不传参默认取第一个岗位）\",\"display_name\":\"工作流发起员工岗位内码\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.SelectedPostId\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701078283390\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"是否启用网控，布尔类型，默认false（非必录）\",\"display_name\":\"是否启用网控\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.NetworkCtrl\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701078328808\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"是否允许忽略交互，布尔类型，默认true（非必录）\",\"display_name\":\"是否允许忽略交互\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.IgnoreInterationFlag\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1730138449716994049\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-11-27 17:35:54', 'admin', '2023-11-30 16:15:25', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1729075331387658241, '1729075331389804546', 0, '提交销售订单', NULL, 1729027738628558849, 14, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619266\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619267\",\"update\":true,\"value\":\" https://openapi.open.kingdee.com/cross/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Submit.common.kdsvc\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619268\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"header\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619269\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Query参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619270\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Body参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619271\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"断言该接口的响应结果。例：[{\\\"path\\\":\\\"响应参数名\\\",\\\"op\\\":\\\"运算符\\\",\\\"value\\\":\\\"节点响应结果\\\"},\\\"path\\\":\\\"响应参数名2\\\",\\\"op\\\":\\\"运算符2\\\",\\\"value\\\":\\\"节点响应结果2\\\"]。；运算符示例：\\\"eq\\\"表示等于\\\"neq\\\"表示不等于；\\\"ne\\\"表示不相等；\\\"contains\\\"表示包含\",\"display_name\":\"预期响应\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"successCondition\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619123\",\"update\":true,\"value\":\"[{\\\"path\\\":\\\"status_code\\\",\\\"op\\\":\\\"eq\\\",\\\"value\\\":\\\"200\\\"},{\\\"path\\\":\\\"body.errcode\\\",\\\"op\\\":\\\"eq\\\",\\\"value\\\":\\\"0\\\"}]\",\"value_type\":\"JS_EXP\"}],\"type\":\"OBJECT\",\"ui_id\":\"1730138385724502018\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-11-27 17:50:58', 'admin', '2023-11-30 16:15:10', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1729081614060785665, '1729081614062931970', 0, '提交采购订单', NULL, 1729027738628558849, 14, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"POST\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619266\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619267\",\"update\":true,\"value\":\" https://openapi.open.kingdee.com/cross/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Submit.common.kdsvc\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619268\",\"update\":true,\"value\":\"application/ison; charset=UTF-8\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"业务对象表单Id，字符串类型（必录）\",\"display_name\":\"业务对象表单Id\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"formid\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1701080215178\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"创建者组织内码（非必录）\",\"display_name\":\"创建者组织内码\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.CreateOrgId\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701080255630\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"单据编码集合，数组类型，格式：[No1,No2,...]（使用编码时必录）\",\"display_name\":\"单据编码集合\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.Numbers\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701080649849\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"单据内码集合，字符串类型，格式：\\\"Id1,Id2,...\\\"（使用内码时必录）\",\"display_name\":\"单据内码集合\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.Ids\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701080682791\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"工作流发起员工岗位内码，整型（非必录） 注（员工身兼多岗时不传参默认取第一个岗位）\",\"display_name\":\"工作流发起员工岗位内码\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.SelectedPostId\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701080710577\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"是否启用网控，布尔类型，默认false（非必录）\",\"display_name\":\"是否启用网控\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.NetworkCtrl\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701080736114\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"是否允许忽略交互，布尔类型，默认true（非必录）\",\"display_name\":\"是否允许忽略交互\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.IgnoreInterationFlag\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1730138560513732609\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-11-27 18:15:56', 'admin', '2023-11-30 16:15:52', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1730123055722229762, '1730123055723732993', 0, '提交费用申请', NULL, 1729027738628558849, 14, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"POST\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619266\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619267\",\"update\":true,\"value\":\"https://openapi.open.kingdee.com/cross/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Submit.common.kdsvc\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619268\",\"update\":true,\"value\":\"application/json; charset=UTF-8\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"header\",\"page_control\":\"HIDDEN\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619269\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Query参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query\",\"page_control\":\"HIDDEN\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619270\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Body参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body\",\"page_control\":\"HIDDEN\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619271\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\\t\\n业务对象表单Id，字符串类型（必录）\",\"display_name\":\"业务对象表单Id\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"formid\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1701328604390\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"JSON格式数据（详情参考JSON格式数据）（必录）\",\"display_name\":\"JSON格式数据（详情参考JSON格式数据）（必录）\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.data\",\"page_control\":\"HIDDEN\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1701328650757\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\\t\\n创建者组织内码（非必录）\",\"display_name\":\"创建者组织内码\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.CreateOrgId\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701328715882\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"单据编码集合，数组类型，格式：[No1,No2,...]（使用编码时必录）\",\"display_name\":\"单据编码集合\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.Numbers\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701328759021\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"单据内码集合，字符串类型，格式：\\\"Id1,Id2,...\\\"（使用内码时必录）\",\"display_name\":\"单据内码集合\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.Ids\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701328786504\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"工作流发起员工岗位内码，整型（非必录） 注（员工身兼多岗时不传参默认取第一个岗位）\",\"display_name\":\"工作流发起员工岗位内码\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.SelectedPostId\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701328811683\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"是否启用网控，布尔类型，默认false（非必录）\",\"display_name\":\"是否启用网控\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.NetworkCtrl\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701328840453\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"是否允许忽略交互，布尔类型，默认true（非必录）\",\"display_name\":\"是否允许忽略交互\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.IgnoreInterationFlag\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1730138766319841282\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-11-30 15:14:15', 'admin', '2023-12-01 18:53:10', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1730482536424288258, '1730482536362045441', 0, '获取销售订单', '根据指定条件查询金蝶云星空的销售订单', 1729027738628558849, 14, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619266\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请修改为实际使用的host\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619267\",\"update\":true,\"value\":\"https://openapi.open.kingdee.com/cross/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.ExecuteBillQuery.common.kdsvc\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619268\",\"update\":true,\"value\":\"application/json\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"业务对象表单ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.data.FormId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619269\",\"update\":true,\"value\":\"SAL_SaleOrder\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"字符串类型，格式：\\\"key1,key2,...\\\"（必录） 注（查询单据体内码,需加单据体Key和下划线,如：FEntryKey_FEntryId）\",\"display_name\":\"需要查询的字段key集合\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.data.FieldKeys\",\"page_control\":\"TEXTAREA\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619270\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"数组类型，如：[{\\\"Left\\\":\\\"(\\\",\\\"FieldName\\\":\\\"Field1\\\",\\\"Compare\\\":\\\"=\\\",\\\"Value\\\":\\\"111\\\",\\\"Right\\\":\\\")\\\",\\\"Logic\\\":\\\"AND\\\"},{\\\"Left\\\":\\\"(\\\",\\\"FieldName\\\":\\\"Field2\\\",\\\"Compare\\\":\\\"=\\\",\\\"Value\\\":\\\"222\\\",\\\"Right\\\":\\\")\\\",\\\"Logic\\\":\\\"\\\"}]\",\"display_name\":\"过滤条件\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.data.FilterString\",\"page_control\":\"TEXTAREA\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619271\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"排序字段\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.data.OrderString\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"***********32619123\",\"update\":true,\"value\":\"\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"返回总行数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.data.TopRowCount\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"NUMBER\",\"ui_id\":\"1701414940804\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"开始行索引\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.data.StartRow\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"NUMBER\",\"ui_id\":\"1701414985980\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"最大行数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.data.Limit\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"NUMBER\",\"ui_id\":\"1701414988520\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"表单所在的子系统内码\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.data.SubSystemId\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"FIX_VALUE\"}],\"type\":\"OBJECT\",\"ui_id\":\"1730514394785275905\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-12-01 15:02:42', 'admin', '2024-08-13 10:30:37', 'linkadmin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (*********6212511745, '*********6214035458', 0, '查询费用申请单', NULL, 1729027738628558849, 14, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619266\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619267\",\"update\":true,\"value\":\"https://openapi.open.kingdee.com/cross/Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.ExecuteBillQuery.common.kdsvc\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619268\",\"update\":true,\"value\":\"application/json; charset=UTF-8\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"返回总行数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.TopRowCount\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"NUMBER\",\"ui_id\":\"1730548282165321730\",\"update\":true,\"value\":\"100\",\"value_type\":\"ADAPTOR\"},{\"description\":\"格式：\\\"key1,key2,...\\\"（必录） 注（查询单据体内码,需加单据体Key和下划线,如：FEntryKey_FEntryId）\",\"display_name\":\"需查询的字段key集合\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.FieldKeys\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1730548282165321731\",\"update\":true,\"value\":\"key1,key2\",\"value_type\":\"ADAPTOR\"},{\"display_name\":\"业务对象表单Id\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.FormId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1730548282165321732\",\"update\":true,\"value\":\"ER_ExpenseRequest\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"不能超过10000\",\"display_name\":\"最大行数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.Limit\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"NUMBER\",\"ui_id\":\"1730548282165321733\",\"update\":true,\"value\":\"1000\",\"value_type\":\"ADAPTOR\"},{\"display_name\":\"开始行索引\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.StartRow\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"NUMBER\",\"ui_id\":\"1730548282165321734\",\"update\":true,\"value\":\"0\",\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1730550888270950401\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-12-01 18:52:54', 'admin', '2023-12-01 19:34:19', 'admin', 'iam', NULL, 'SINGLE');

-- 用友高级版
INSERT IGNORE INTO acm.acm_app_connector (id, name, description, icon, build_type, app_domain_category, app_action_category, app_package, ver_name, ver_number, ver_type, create_time, create_by, update_time, update_by, tenant_id, deployment_type) VALUES (1729326744034381826, '用友BIP高级版', NULL, NULL, 'Market', 'CRM_ERP', NULL, '1729326744036528129', '1.0.0', 1, 'RELEASE', '2023-11-28 10:30:00', 'admin', '2024-06-06 10:08:23', 'admin', 'iam', 'SaasApp');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1729326892290445313, '1729326892292591618', 0, '新增采购订单', NULL, 1729326744034381826, 7, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"POST\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619266\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619267\",\"update\":true,\"value\":\"http://ip:port/nccloud/api/pu/order/operation/save\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619268\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"header\",\"page_control\":\"HIDDEN\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619269\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Query参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query\",\"page_control\":\"HIDDEN\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619270\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Body参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body\",\"page_control\":\"HIDDEN\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619271\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"display_name\":\"请求参数合集\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.data\",\"option_values\":[],\"page_control\":\"JSON\",\"required\":true,\"type\":\"OBJECT\",\"ui_id\":\"1701139070027\",\"update\":true,\"value\":\"[     {       \\\"po_order\\\": {         \\\"pk_dept\\\": \\\"TH01\\\",         \\\"pk_org\\\": \\\"T20\\\",         \\\"pk_supplier\\\": \\\"T20\\\",         \\\"vtrantypecode\\\": \\\"21-01\\\"       },       \\\"po_order_b\\\": [         {           \\\"nastnum\\\": \\\"1\\\",           \\\"nqtorigprice\\\": \\\"1\\\",           \\\"nqtorigtaxprice\\\": \\\"1\\\",           \\\"pk_material\\\": \\\"001\\\"         }       ]     }   ]\",\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1729339496696205314\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-11-28 10:30:35', 'admin', '2024-05-28 19:20:18', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1729336372164071425, '1729336372166217730', 0, '销售订单新增保存', NULL, 1729326744034381826, 15, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"POST\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619266\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619267\",\"update\":true,\"value\":\"http://ip:port/nccloud/api/so/saleorder/operation/save\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619268\",\"update\":true,\"value\":\"application/json; charset=UTF-8\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"header\",\"page_control\":\"HIDDEN\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619269\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Query参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query\",\"page_control\":\"HIDDEN\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619270\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Body参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body\",\"page_control\":\"HIDDEN\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619271\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"display_name\":\"基本参数\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.array\",\"option_values\":[],\"page_control\":\"JSON\",\"required\":true,\"type\":\"OBJECT\",\"ui_id\":\"1701141509447\",\"update\":true,\"value\":\"[     {       \\\"so_saleorder\\\": {         \\\"ccustomervid\\\": \\\"T20\\\",         \\\"cdeptvid\\\": \\\"DB01\\\",         \\\"vtrantypecode\\\": \\\"30-01\\\",         \\\"ccustomerid\\\": \\\"T20\\\",         \\\"pk_org\\\": \\\"T200501\\\"       },       \\\"so_saleorder_b\\\": [         {           \\\"cqtunitid\\\": \\\"MTR\\\",           \\\"norigtaxprice\\\": \\\"100\\\",           \\\"cmaterialvid\\\": \\\"001\\\",           \\\"castunitid\\\": \\\"MTR\\\",           \\\"nqtorigtaxprice\\\": \\\"10\\\",           \\\"nastnum\\\": \\\"10\\\"         }       ]     }   ]\",\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1729342203439960066\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-11-28 11:08:15', 'admin', '2024-05-22 18:41:42', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1729339692039573506, '1729339692045914113', 0, '新增采购入库单', NULL, 1729326744034381826, 15, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"POST\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619266\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619267\",\"update\":true,\"value\":\"http://ip:port/nccloud/api/ic/purchasein/operation/save\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619268\",\"update\":true,\"value\":\"application/jison; charset=UTF-8\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"header\",\"page_control\":\"HIDDEN\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619269\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Query参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query\",\"page_control\":\"HIDDEN\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619270\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Body参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body\",\"page_control\":\"HIDDEN\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619271\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"display_name\":\"采购入库单表头\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.ic_purchasein_h\",\"option_values\":[],\"page_control\":\"JSON\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701141819035\",\"update\":true,\"value\":\"{     \\\"dbilldate\\\": \\\"2022-04-29 10:05:05\\\",     \\\"ctrantypeid\\\": \\\"45-01\\\",     \\\"cwarehouseid\\\": \\\"11\\\",     \\\"vtrantypecode\\\": \\\"45-01\\\",     \\\"cvendorid\\\": \\\"T20\\\",     \\\"cbiztype\\\": \\\"PU02\\\",     \\\"pk_org\\\": \\\"T20\\\"   }\",\"value_type\":\"ADAPTOR\"},{\"display_name\":\"采购入库单表体\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.ic_purchasein_b\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1701142218014\",\"update\":true,\"value\":\"[     {       \\\"nshouldassistnum\\\": \\\"2\\\",       \\\"cmaterialvid\\\": \\\"0102003\\\",       \\\"castunitid\\\": \\\"EA\\\",       \\\"nnum\\\": \\\"10\\\"     }   ]\",\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1729342513889759233\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-11-28 11:21:27', 'admin', '2024-05-22 18:48:19', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1729342739616665601, '1729342739610423297', 0, '删除采购入库单', NULL, 1729326744034381826, 15, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"POST\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619266\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619267\",\"update\":true,\"value\":\"http://ip:port/nccloud/api/ic/purchasein/operation/delete\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619268\",\"update\":true,\"value\":\"applicationjison; charset=UTF-8\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"header\",\"page_control\":\"HIDDEN\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619269\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Query参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query\",\"page_control\":\"HIDDEN\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619270\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Body参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body\",\"page_control\":\"HIDDEN\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619271\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"display_name\":\"采购入库单表头主键\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.cgeneralhid\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"OBJECT\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"[ 0:\\\"1002AA1000000000GQZO\\\" ]\",\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1729343344504557569\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-11-28 11:33:33', 'admin', '2023-11-28 11:35:58', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1729344031063838722, '1729344031065985026', 0, '查询采购合同', NULL, 1729326744034381826, 15, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"POST\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619266\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619267\",\"update\":true,\"value\":\"http://ip:port/nccloud/api/ct/purdaily/queryvo\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619268\",\"update\":true,\"value\":\"application/json; charset=UTF-8\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"header\",\"page_control\":\"HIDDEN\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619269\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Query参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query\",\"page_control\":\"HIDDEN\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619270\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Body参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body\",\"page_control\":\"HIDDEN\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619271\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"display_name\":\"参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.ct_pu\",\"page_control\":\"JSON\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1701142801586\",\"update\":true,\"value\":\"\",\"value_type\":\"ADAPTOR\"},{\"display_name\":\"参数\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.ct_pu_b\",\"page_control\":\"JSON\",\"required\":true,\"type\":\"OBJECT\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1729345121408864257\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-11-28 11:38:41', 'admin', '2023-11-28 11:43:01', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1729345831909232642, '1729345831911378945', 0, '采购合同审批', NULL, 1729326744034381826, 15, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"POST\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619266\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619267\",\"update\":true,\"value\":\"http://ip:port/nccloud/api/ct/purdaily/operation/approve\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619268\",\"update\":true,\"value\":\"application/json; charset=UTF-8\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"header\",\"page_control\":\"HIDDEN\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619269\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Query参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query\",\"page_control\":\"HIDDEN\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619270\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Body参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body\",\"page_control\":\"HIDDEN\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619271\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"display_name\":\"入参不需要加key值，详细见示例\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.pk_ct_pu\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"OBJECT\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1729346341699670018\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-11-28 11:45:51', 'admin', '2023-11-28 11:47:52', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1729381456972648449, '1729381456978989058', 0, '查询销售合同', NULL, 1729326744034381826, 15, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"POST\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619266\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619267\",\"update\":true,\"value\":\"http://ip:port/nccloud/api/ct/saledaily/queryvo\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619268\",\"update\":true,\"value\":\"application/json; charset=UTF-8\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"header\",\"page_control\":\"HIDDEN\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619269\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Query参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query\",\"page_control\":\"HIDDEN\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619270\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Body参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body\",\"page_control\":\"HIDDEN\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619271\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"display_name\":\"销售合同表头主键\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.pk_ct_sale\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701151793633\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"销售组织最新版本\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.pk_org\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1701151840978\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"合同类型\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.ctrantypeid\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701151898711\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"合同编码\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.vbillcode\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701151927492\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"合同名称\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.ctname\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701151959543\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"客户编码\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.pk_customer.code\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701151997247\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"客户名称\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.pk_customer.name\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701152030288\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"人员\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.personnelid\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701152053381\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"部门最新版本\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.depid\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701152095234\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"币种\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.corigcurrencyid\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701152169060\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"合同状态\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.fstatusflag\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701152400185\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"项目\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.cprojectid\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701152427953\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"合同签订日期\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.subscribedate\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701152455411\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"计划生效日期\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.valdate\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701152484482\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"计划终止日期\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.invallidate\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701152569314\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"实际生效日期\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.actualvalidate\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701152613951\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"实际终止日期\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.actualinvalidate\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701152648379\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"制单人\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.billmaker\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701152674507\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"表体物料最新版本编码\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.pk_ct_sale_b.pk_srcmaterial.code\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701152799667\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"表体物料最新版本名称\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.pk_ct_sale_b.pk_srcmaterial.name\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701152828935\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"表体物料分类名称\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.pk_ct_sale_b.pk_marbasclass.name\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701152845864\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"表体物料分类编码\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.pk_ct_sale_b.pk_marbasclass.code\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1729387008937250817\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-11-28 14:07:24', 'admin', '2023-11-28 14:29:28', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1729387492089565185, '1729387492087517185', 0, '新增凭证', NULL, 1729326744034381826, 15, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"POST\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619266\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619267\",\"update\":true,\"value\":\"http://ip:port/nccloud/api/fip/service/add\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619268\",\"update\":true,\"value\":\"application/ison; charset=UTF-8\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"header\",\"page_control\":\"HIDDEN\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619269\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Query参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query\",\"page_control\":\"HIDDEN\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619270\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Body参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body\",\"page_control\":\"HIDDEN\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619271\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"display_name\":\"参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.ufinterface\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701153151803\",\"update\":true,\"value\":\"{     \\\"billtype\\\": \\\"QC\\\",     \\\"sender\\\": \\\"QC\\\",     \\\"bill\\\": {       \\\"billhead\\\": {         \\\"pk_group\\\": \\\"T2\\\",         \\\"billMoney\\\": \\\"100000.00000000\\\",         \\\"messageType\\\": \\\"0\\\",         \\\"bodies\\\": [           {             \\\"item\\\": {               \\\"interestEndDate\\\": \\\"2019-05-29 00:00:00\\\",               \\\"rate\\\": \\\"3.600000000\\\",               \\\"operator\\\": \\\"ncc001\\\"             }           }         ],         \\\"pk_operator\\\": \\\"ncc001\\\",         \\\"billExplain\\\": \\\"贷款\\\",         \\\"billCode\\\": \\\"20190701000000000001\\\",         \\\"relationID\\\": \\\"********************\\\",         \\\"settleDate\\\": \\\"2019-05-29 10:18:08\\\",         \\\"listType\\\": \\\"1\\\",         \\\"pk_org\\\": \\\"T20\\\",         \\\"primaryKey\\\": \\\"********************\\\"       }     },     \\\"groupcode\\\": \\\"T2\\\"   }\",\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1729388073267056641\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-11-28 14:31:23', 'admin', '2023-11-28 14:33:42', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1729388434075717633, '1729388434077863937', 0, '根据PK查凭证信息', NULL, 1729326744034381826, 15, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"POST\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619266\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619267\",\"update\":true,\"value\":\"http://ip:port/nccloud/api/gl/voucher/getVoucherByPK\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619268\",\"update\":true,\"value\":\"application/json; charset=UTF-8\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"header\",\"page_control\":\"HIDDEN\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619269\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Query参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query\",\"page_control\":\"HIDDEN\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619270\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Body参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619271\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"display_name\":\"凭证主键\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.pk_voucher\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1729388895052845058\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-11-28 14:35:08', 'admin', '2023-11-28 14:36:58', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1729398130161569793, '1729398130067197953', 0, '应收单新增', NULL, 1729326744034381826, 15, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619266\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619267\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619268\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"header\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619269\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Query参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619270\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Body参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619271\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"断言该接口的响应结果。例：[{\\\"path\\\":\\\"响应参数名\\\",\\\"op\\\":\\\"运算符\\\",\\\"value\\\":\\\"节点响应结果\\\"},\\\"path\\\":\\\"响应参数名2\\\",\\\"op\\\":\\\"运算符2\\\",\\\"value\\\":\\\"节点响应结果2\\\"]。；运算符示例：\\\"eq\\\"表示等于\\\"neq\\\"表示不等于；\\\"ne\\\"表示不相等；\\\"contains\\\"表示包含\",\"display_name\":\"预期响应\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"successCondition\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619123\",\"update\":true,\"value\":\"[{\\\"path\\\":\\\"status_code\\\",\\\"op\\\":\\\"eq\\\",\\\"value\\\":\\\"200\\\"},{\\\"path\\\":\\\"body.errcode\\\",\\\"op\\\":\\\"eq\\\",\\\"value\\\":\\\"0\\\"}]\",\"value_type\":\"JS_EXP\"}],\"type\":\"OBJECT\",\"ui_id\":\"1729398130134306818\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-11-28 15:13:40', 'admin', '2023-11-28 15:17:03', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1793230699974709249, '1793230699968450562', 0, '新增/修改个人银行账户', '新增/修改个人银行账户，id相同时修改', 1729326744034381826, 7, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619266\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619267\",\"update\":true,\"value\":\"http://ip:端口/nccloud/api/uapbd/bankmanage/psnbankacc/add\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619268\",\"update\":true,\"value\":\"application/json\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"header\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619269\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Query参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query\",\"page_control\":\"HIDDEN\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619270\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Body参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"OBJECT\",\"ui_id\":\"***********32619271\",\"update\":true,\"value_type\":\"JS_EXP\"}],\"type\":\"OBJECT\",\"ui_id\":\"1798537357661683713\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2024-05-22 18:41:30', 'admin', '2024-11-14 19:49:01', 'linkadmin', 'iam', NULL, 'SINGLE');

-- 钉钉其余动作
INSERT IGNORE INTO acm.acm_app_connector (id, name, description, icon, build_type, app_domain_category, app_action_category, app_package, ver_name, ver_number, ver_type, create_time, create_by, update_time, update_by, tenant_id, deployment_type) VALUES (1698653512688324609, '钉钉机器人', NULL, NULL, 'Market', 'COLLABORATION', NULL, '1698653512690380801', '1.0', 1, 'RELEASE', '2023-09-04 19:05:32', 'admin', '2023-09-06 11:12:23', 'admin', 'iam', 'SaasApp');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1698654080311873538, '1698654080309735426', 0, '发送DING消息', '可使用企业内机器人发送DING消息，可发送应用内DING、短信DING、电话DING。', 1698653512688324609, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://api.dingtalk.com/v1.0/robot/ding/send\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"接收人userId列表。\\n\\n应用内DING消息，每次接收人不能超过200个。\\n\\n短信DING和电话DING，每次接收人不能超过20个。\",\"display_name\":\"接收人userId列表\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.receiverUserIdList\",\"page_control\":\"JSON\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698655128227229697\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"发DING消息的机器人ID\",\"display_name\":\"机器人ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.robotCode\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698655128227229698\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"DING消息内容\",\"display_name\":\"内容\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.content\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698655128227229699\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"DING消息类型。\\n\\n1：应用内DING\\n\\n2：短信DING\\n\\n3：电话DING\",\"display_name\":\"消息类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.remindType\",\"option_values\":[\"1\",\"2\",\"3\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"NUMBER\",\"ui_id\":\"1698655128227229700\",\"update\":true,\"value_type\":\"FIX_VALUE\"}],\"type\":\"OBJECT\",\"ui_id\":\"1819272796202463233\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-04 19:07:47', 'admin', '2024-08-06 17:17:15', 'linkadmin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1698656384557633537, '1698656384555495426', 0, '发送群聊消息', '使用机器人发送群聊消息', 1698653512688324609, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://api.dingtalk.com/v1.0/robot/groupMessages/send\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"消息模板参数\",\"display_name\":\"消息模板参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.msgParam\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698655128227229697\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"发DING消息的机器人ID\",\"display_name\":\"机器人ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.robotCode\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698655128227229698\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"消息模板key\",\"display_name\":\"消息模板key\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.msgKey\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698655128227229699\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"群id\\n\",\"display_name\":\"群id\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.openConversationId\",\"option_values\":[],\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698655128227229700\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1819272752229380098\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-04 19:16:56', 'admin', '2024-08-02 15:26:16', 'linkadmin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1698657692689747969, '1698657692687609857', 0, '人与人会话中机器人发送普通消息', '人与人会话中机器人发送普通消息', 1698653512688324609, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://api.dingtalk.com/v1.0/robot/privateChatMessages/send\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"酷应用编码\",\"display_name\":\"酷应用编码\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.coolAppCode\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1698658123862061057\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"消息模板参数\",\"display_name\":\"消息模板参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.msgParam\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698658123862061058\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"消息模板Key\",\"display_name\":\"消息模板Key\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.msgKey\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698658123862061059\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"机器人编码，该参数使用企业内部应用的robotCode\",\"display_name\":\"机器人编码\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.robotCode\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1698658123862061060\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"会话ID\",\"display_name\":\"会话ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.openConversationId\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1698658123862061061\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1819272717701869570\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-04 19:22:08', 'admin', '2024-08-02 15:26:08', 'linkadmin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1698659519862788098, '1698659519869038594', 0, '自定义机器人发送群消息', '自定义机器人发送群消息', 1698653512688324609, NULL, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://oapi.dingtalk.com/robot/send\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"消息类型，自定义机器人可发送的消息类型\",\"display_name\":\"消息类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.msgtype\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698658123862061057\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"文本类型消息\",\"display_name\":\"文本类型消息\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.text\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1698658123862061058\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"被@的群成员信息\",\"display_name\":\"被@的群成员信息\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.at\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1698658123862061059\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"链接类型消息\",\"display_name\":\"链接类型消息\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.link\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1698658123862061060\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"markdown类型消息\",\"display_name\":\"markdown类型消息\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.markdown\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1698658123862061061\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"actionCard类型消息\",\"display_name\":\"actionCard类型消息\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.actionCard\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1693827308036\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"feedCard类型消息\",\"display_name\":\"feedCard类型消息\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.feedCard\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1693827341032\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1819272850367705090\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-04 19:29:24', 'admin', '2024-08-06 17:17:33', 'linkadmin', 'iam', NULL, 'SINGLE');

INSERT IGNORE INTO acm.acm_app_connector (id, name, description, icon, build_type, app_domain_category, app_action_category, app_package, ver_name, ver_number, ver_type, create_time, create_by, update_time, update_by, tenant_id, deployment_type) VALUES (1698661994879635457, '钉钉考勤', NULL, NULL, 'Market', 'COLLABORATION', NULL, '1698661994877497345', '1.0', 1, 'RELEASE', '2023-09-04 19:39:14', 'admin', '2023-09-06 11:12:07', 'admin', 'iam', 'SaasApp');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1698662310895276034, '1698662310893137921', 0, '上传打卡记录', '将三方考勤系统的刷卡或刷脸记录上传到钉钉考勤，做为钉钉打卡流水', 1698661994879635457, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://oapi.dingtalk.com/topapi/attendance/record/upload\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"考勤机名称\",\"display_name\":\"考勤机名称\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.device_name\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698662723931418626\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"考勤机ID\",\"display_name\":\"考勤机ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.device_id\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698662723931418627\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"员工打卡的时间\",\"display_name\":\"打卡时间\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.user_check_time\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"NUMBER\",\"ui_id\":\"1698662723931418628\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"打卡备注图片地址，必须是公网可访问的地址\",\"display_name\":\"图片地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.photo_url\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1698662723931418629\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"上传打卡记录的员工userId\",\"display_name\":\"员工userId\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.userid\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698662723931418630\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1704051437482356737\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-04 19:40:29', 'admin', '2024-08-02 16:18:33', 'linkadmin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1698663850863349761, '1698663850861211650', 0, '获取打卡结果', '获取企业内员工的实际打卡结果', 1698661994879635457, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://oapi.dingtalk.com/attendance/list\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"查询考勤打卡记录的起始工作日\",\"display_name\":\"起始工作日\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.workDateFrom\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698664317322342401\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"表示获取考勤数据的起始点。第一次传0，如果还有多余数据，下次获取传的offset值为之前的offset+limit，0、1、2...依次递增\",\"display_name\":\"考勤数据的起始点\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.offset\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"NUMBER\",\"ui_id\":\"1698664317322342402\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"员工在企业内的userId列表，最大值50\",\"display_name\":\"userId列表\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.userIdList\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698664317322342403\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"表示获取考勤数据的条数，最大值50\",\"display_name\":\"考勤数据的条数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.limit\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"NUMBER\",\"ui_id\":\"1698664317322342404\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"是否为海外企业使用：\\n\\ntrue：海外平台使用\\n\\nfalse（默认）：国内平台使用\",\"display_name\":\"是否为海外企业使用\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.isI18n\",\"option_values\":[\"true\",\"false\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"BOOLEAN\",\"ui_id\":\"1698664317322342405\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"查询考勤打卡记录的结束工作日\",\"display_name\":\"结束工作日\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.workDateTo\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698664317322342406\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1704051624724475906\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-04 19:46:36', 'admin', '2023-09-19 16:36:20', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1698665867786702849, '1698665867784564737', 0, '获取打卡详情', '获取企业内员工的实际打卡详情', 1698661994879635457, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://oapi.dingtalk.com/attendance/listRecord\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"查询考勤打卡记录的起始工作日\",\"display_name\":\"起始工作日\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.checkDateFrom\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698664317322342401\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"员工在企业内的userId列表，最大值50\",\"display_name\":\"userId列表\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.userIds\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698664317322342403\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"是否为海外企业使用：\\n\\ntrue：海外平台使用\\n\\nfalse（默认）：国内平台使用\",\"display_name\":\"是否为海外企业使用\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.isI18n\",\"option_values\":[\"true\",\"false\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"BOOLEAN\",\"ui_id\":\"1698664317322342405\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"查询考勤打卡记录的结束工作日\",\"display_name\":\"结束工作日\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.checkDateFrom\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698664317322342406\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1704051923031764993\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-04 19:54:37', 'admin', '2024-03-28 14:34:05', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1698667943866519553, '1698667943864381442', 0, '获取用户考勤数据', '获取指定用户当天的考勤数据', 1698661994879635457, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://oapi.dingtalk.com/topapi/attendance/getupdatedata\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"查询日期\",\"display_name\":\"查询日期\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.work_date\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698668240049352705\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"用户的userId\",\"display_name\":\"用户的userId\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.userid\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698668240049352706\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1704052032452767746\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-04 20:02:52', 'admin', '2024-08-02 16:18:50', 'linkadmin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1773236632473690114, '1773236632471846913', 0, '获取考勤报表列值', '获取钉钉智能考勤报表的列值数据，其中包含了一定时间段内报表某一列的所有数据，以及相关的列信息', 1698661994879635457, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619266\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619267\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"Body参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619271\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"断言该接口的响应结果。例：[{\\\"path\\\":\\\"响应参数名\\\",\\\"op\\\":\\\"运算符\\\",\\\"value\\\":\\\"节点响应结果\\\"},\\\"path\\\":\\\"响应参数名2\\\",\\\"op\\\":\\\"运算符2\\\",\\\"value\\\":\\\"节点响应结果2\\\"]。；运算符示例：\\\"eq\\\"表示等于\\\"neq\\\"表示不等于；\\\"ne\\\"表示不相等；\\\"contains\\\"表示包含\",\"display_name\":\"预期响应\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"successCondition\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619123\",\"update\":true,\"value\":\"[{\\\"path\\\":\\\"status_code\\\",\\\"op\\\":\\\"eq\\\",\\\"value\\\":\\\"200\\\"},{\\\"path\\\":\\\"body.errcode\\\",\\\"op\\\":\\\"eq\\\",\\\"value\\\":\\\"0\\\"}]\",\"value_type\":\"JS_EXP\"},{\"description\":\"报表列ID，多值用英文逗号分隔，最大长度20。\",\"display_name\":\"报表列ID，多值用英文逗号分隔，最大长度20\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.column_id_list\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1773237352650625026\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"开始时间\",\"display_name\":\"开始时间\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.from_date\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1773237352650625027\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"结束时间，结束时间减去开始时间必须在31天以内\",\"display_name\":\"结束时间\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.to_date\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1773237352650625028\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"用户的userId\",\"display_name\":\"用户的userId\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.userid\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1773237352650625029\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1773237874979885058\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2024-03-28 14:32:13', 'admin', '2024-03-28 14:37:09', 'admin', 'iam', NULL, 'SINGLE');

INSERT IGNORE INTO acm.acm_app_connector (id, name, description, icon, build_type, app_domain_category, app_action_category, app_package, ver_name, ver_number, ver_type, create_time, create_by, update_time, update_by, tenant_id, deployment_type) VALUES (1698670219905585154, '钉钉OA审批', '钉钉为企业提供的官方应用，可以快速建立审批流程，如请假、出差等。', NULL, 'Market', 'COLLABORATION', NULL, '1698670219903447042', '1.0', 1, 'RELEASE', '2023-09-04 20:11:55', 'admin', '2023-11-01 10:22:45', 'admin', 'iam', 'SaasApp');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1698670445852741634, '1698670445850603522', 0, '发起审批实例', '用于发起OA审批实例', 1698670219905585154, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://oapi.dingtalk.com/topapi/processinstance/create\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"应用标识AgentId\",\"display_name\":\"AgentId\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.agent_id\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"NUMBER\",\"ui_id\":\"1698864021117153281\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"审批流的唯一码。\\n\\nprocess_code在审批模板编辑页面的URL中获取\",\"display_name\":\"审批流的唯一码\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.process_code\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698864021117153282\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"审批发起人的userId\",\"display_name\":\"发起人的userId\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.originator_user_id\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698864021117153283\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"审批发起人所在的部门ID\",\"display_name\":\"部门ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.dept_id\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"NUMBER\",\"ui_id\":\"1698864021117153284\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"审批流表单参数，最大列表长度200。\\n\\n仅支持下表列举的表单控件。\",\"display_name\":\"表单内容\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.form_component_values\",\"page_control\":\"JSON\",\"required\":true,\"type\":\"OBJECT\",\"ui_id\":\"1698864021117153285\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"抄送时间点\",\"display_name\":\"抄送时间点\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.cc_position\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1698864206274703362\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"审批人userid列表，最大列表长度20。\\n\\n多个审批人用逗号分隔，按传入的顺序依次审批。\",\"display_name\":\"审批人列表\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.approvers\",\"page_control\":\"TEXTAREA\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1698864206274703364\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"审批人列表，最大列表长度20。\\n\\n支持会签/或签，优先级高于approvers变量。\",\"display_name\":\"审批人列表v2\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.approvers_v2\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1693877589856\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\\t\\n抄送人 userId\",\"display_name\":\"抄送人 userId\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.cc_list\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1698864206274703365\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1719549554404208641\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-04 20:12:49', 'admin', '2024-11-27 16:59:55', 'linkadmin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1698868738650132482, '1698868738647994370', 0, '获取单个审批实例详情', '获取单个审批实例详情', 1698670219905585154, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"GET\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://api.dingtalk.com/v1.0/workflow/processInstances\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\\t\\n审批实例ID\",\"display_name\":\"审批实例ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.processInstanceId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1749733801664299009\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-05 09:20:45', 'admin', '2024-10-28 18:14:52', 'linkadmin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1698873277352628225, '1698873277354684418', 0, '撤销审批实例', '撤销审批实例', 1698670219905585154, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://oapi.dingtalk.com/topapi/process/instance/terminate\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"终止审批请求\",\"display_name\":\"审批实例ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.request\",\"page_control\":\"JSON\",\"required\":true,\"type\":\"OBJECT\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1704046563109711873\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-05 09:38:48', 'admin', '2024-06-18 12:12:40', 'linkadmin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1698874130406952962, '1698874130409009154', 0, '同意或拒绝审批任务', '根据指定模板ID、实例ID、审批节点ID和审批人，对单个审批任务进行处理', 1698670219905585154, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://oapi.dingtalk.com/topapi/process/instance/execute\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请求参数\",\"display_name\":\"请求参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.request\",\"page_control\":\"JSON\",\"required\":true,\"type\":\"OBJECT\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1704046648501547010\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-05 09:42:11', 'admin', '2024-08-02 16:18:42', 'linkadmin', 'iam', NULL, 'SINGLE');

INSERT IGNORE INTO acm.acm_app_connector (id, name, description, icon, build_type, app_domain_category, app_action_category, app_package, ver_name, ver_number, ver_type, create_time, create_by, update_time, update_by, tenant_id, deployment_type) VALUES (1698876928578146305, '钉钉CRM', NULL, NULL, 'Market', 'CRM_ERP', NULL, '1698876928576008194', '1.0', 1, 'RELEASE', '2023-09-05 09:53:18', 'admin', '2023-09-06 11:11:43', 'admin', 'iam', 'SaasApp');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1698877410163937282, '1698877410161799169', 0, '创建个人或企业客户数据', '添加CRM个人客户或企业客户', 1698876928578146305, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://oapi.dingtalk.com/v1.0/crm/personalCustomers\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"记录创建人的昵称\",\"display_name\":\"创建人的昵称\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.creatorNick\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1698877982357139458\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"关系类型。\\n\\ncrm_customer：企业客户\\n\\ncrm_customer_personal：个人客户\",\"display_name\":\"关系类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.relationType\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1698877982357139459\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"记录创建人的用户userId\",\"display_name\":\"用户userId\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.creatorUserId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698877982357139460\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"是否跳过查重字段。\\n\\ntrue：是\\nfalse：否\",\"display_name\":\"是否跳过查重字段\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.skipDuplicateCheck\",\"option_values\":[\"true\",\"false\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"BOOLEAN\",\"ui_id\":\"1698877982357139461\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"客户数据内容，JSON格式字符串\",\"display_name\":\"客户数据内容\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.data\",\"page_control\":\"JSON\",\"required\":true,\"type\":\"OBJECT\",\"ui_id\":\"1698877982357139462\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\\t\\n取值。\\n\\npublicDraw：公海领取客户\\npublicAssign：公海分配客户\\n其余场景：不用传\",\"display_name\":\"取值\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.action\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1698877982357139463\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"权限\",\"display_name\":\"权限\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.permission\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1698877982357139464\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"扩展数据内容\",\"display_name\":\"扩展数据内容\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.extendData\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1698877982357139465\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1704045195686916097\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-05 09:55:13', 'admin', '2024-08-02 16:18:55', 'linkadmin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1698880442859638785, '1698880442857500674', 0, '更新个人或企业客户数据', '更新个人或企业客户数据', 1698876928578146305, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"PUT\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://oapi.dingtalk.com/v1.0/crm/personalCustomers\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"客户数据ID\",\"display_name\":\"客户数据ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.instanceId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1693880322797\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"记录修改人的昵称\",\"display_name\":\"昵称\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.modifierNick\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1698877982357139458\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"关系类型。\\n\\ncrm_customer：企业客户\\n\\ncrm_customer_personal：个人客户\",\"display_name\":\"关系类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.relationType\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1698877982357139459\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"记录修改人的用户userId\",\"display_name\":\"用户userId\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.modifierUserId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698877982357139460\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"是否跳过查重字段。\\n\\ntrue：是\\nfalse：否\",\"display_name\":\"是否跳过查重字段\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.skipDuplicateCheck\",\"option_values\":[\"true\",\"false\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"BOOLEAN\",\"ui_id\":\"1698877982357139461\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"客户数据内容，JSON格式字符串\",\"display_name\":\"客户数据内容\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.data\",\"page_control\":\"JSON\",\"required\":true,\"type\":\"OBJECT\",\"ui_id\":\"1698877982357139462\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\\t\\n取值。\\n\\npublicDraw：公海领取客户\\npublicAssign：公海分配客户\\n其余场景：不用传\",\"display_name\":\"取值\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.action\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1698877982357139463\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"权限\",\"display_name\":\"权限\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.permission\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1698877982357139464\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"扩展数据内容\",\"display_name\":\"扩展数据内容\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.extendData\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1698877982357139465\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1704045427778727937\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-05 10:07:16', 'admin', '2024-08-02 16:19:01', 'linkadmin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1698885012532875266, '1698885012530737153', 0, '删除个人或企业客户数据', '删除CRM个人客户或企业客户数据', 1698876928578146305, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"DELETE\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://oapi.dingtalk.com/v1.0/crm/personalCustomers/{dataId}\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"客户数据ID\",\"display_name\":\"客户数据ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path.dataId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1693880902248\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"关系类型。\\n\\ncrm_customer：企业客户\\n\\ncrm_customer_personal：个人客户\",\"display_name\":\"关系类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.relationType\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1693880949748\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"操作人用户userId\",\"display_name\":\"操作人用户userId\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.currentOperatorUserId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1704045543877062657\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-05 10:25:25', 'admin', '2023-09-19 16:11:32', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1698886315636019201, '1698886315633881090', 0, '查询个人或企业客户数据', '根据指定条件查询CRM个人客户或企业客户数据', 1698876928578146305, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"GET\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://oapi.dingtalk.com/v1.0/crm/personalCustomers\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"用户userId\",\"display_name\":\"用户userId\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.currentOperatorUserId\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1693880902248\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"关系类型。\\n\\ncrm_customer：企业客户\\n\\ncrm_customer_personal：个人客户\",\"display_name\":\"关系类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.relationType\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1693880949748\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"分页游标，获取下一页时传入上一页返回的nextToken\",\"display_name\":\"nextToken\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.nextToken\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"每页条数，最大值100\",\"display_name\":\"每页条数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.maxResults\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"NUMBER\",\"ui_id\":\"1693881452209\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"查询条件\",\"display_name\":\"查询条件\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.queryDsl\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1693881517874\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1704045721279344642\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-05 10:30:36', 'admin', '2023-09-19 16:12:14', 'admin', 'iam', NULL, 'SINGLE');

INSERT IGNORE INTO acm.acm_app_connector (id, name, description, icon, build_type, app_domain_category, app_action_category, app_package, ver_name, ver_number, ver_type, create_time, create_by, update_time, update_by, tenant_id, deployment_type) VALUES (1698890508144857090, '钉钉OCR', '进行OCR文字识别，即识别一张图片上的文字', NULL, 'Market', 'DATA_SERVICE', NULL, '1698890508142718978', '1.0', 1, 'RELEASE', '2023-09-05 10:47:16', 'admin', '2023-09-06 11:11:32', 'admin', 'iam', 'SaasApp');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1698890683580010498, '1698890683577872385', 0, 'OCR文字识别', '进行OCR文字识别，即识别一张图片上的文字', 1698890508144857090, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://oapi.dingtalk.com/topapi/ocr/structured/recognize\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"识别图片类型\",\"display_name\":\"识别图片类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.type\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"识别图片地址，最大长度1000\",\"display_name\":\"地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.image_url\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1693882218435\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1704044931613536258\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-05 10:47:57', 'admin', '2024-08-02 16:19:10', 'linkadmin', 'iam', NULL, 'SINGLE');

INSERT IGNORE INTO acm.acm_app_connector (id, name, description, icon, build_type, app_domain_category, app_action_category, app_package, ver_name, ver_number, ver_type, create_time, create_by, update_time, update_by, tenant_id, deployment_type) VALUES (1698891681040027650, '钉钉ASR', NULL, NULL, 'Market', 'DATA_SERVICE', NULL, '1698891681058861057', '1.0', 1, 'RELEASE', '2023-09-05 10:51:55', 'admin', '2023-09-06 11:11:13', 'admin', 'iam', 'SaasApp');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1698891825785458689, '1698891825783320577', 0, 'ASR 一句话语音识别', '调用本接口，识别一段60秒内的语音。\n\n调用本接口，可根据音频的media_id进行语音识别。', 1698891681040027650, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://oapi.dingtalk.com/topapi/asr/voice/translate\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"音频的mediaId\",\"display_name\":\"音频的mediaId\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.media_id\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1704044813900394498\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-05 10:52:30', 'admin', '2023-09-19 16:08:38', 'admin', 'iam', NULL, 'SINGLE');
-- 飞书
INSERT IGNORE INTO acm.acm_app_connector (id, name, description, icon, build_type, app_domain_category, app_action_category, app_package, ver_name, ver_number, ver_type, create_time, create_by, update_time, update_by, tenant_id, deployment_type) VALUES (1698998146199310338, '飞书任务', NULL, NULL, 'Market', 'COLLABORATION', NULL, '1698998146197172225', '1.0', 1, 'RELEASE', '2023-09-05 17:54:59', 'admin', '2023-09-06 11:07:26', 'admin', 'iam', 'SaasApp');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1698998869469286401, '1698998869471342593', 0, '创建任务', '该接口可以创建一个任务', 1698998146199310338, 13, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"LABEL\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://open.feishu.cn/open-apis/task/v2/tasks\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\\t\\n用户 ID 类型\",\"display_name\":\"用户 ID 类型\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"query.user_id_type\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1698996620955299844\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"任务标题\",\"display_name\":\"任务标题\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.summary\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699003696360271874\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"任务提醒\",\"display_name\":\"任务提醒\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.reminders\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699003696360271875\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\\t\\n任务关联的第三方平台来源信息，用于来源信息在飞书任务界面的展示\",\"display_name\":\"来源\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.origin\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699003696360271876\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"任务的开始时间\",\"display_name\":\"任务的开始时间\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.start\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699003696360271890\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"任务摘要\",\"display_name\":\"任务摘要\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.description\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1699003696360271892\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"任务所在清单的信息\",\"display_name\":\"任务所在清单的信息\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.tasklists\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699003696360271893\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\\t\\n任务自定义完成配置\",\"display_name\":\"任务自定义完成配置\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.custom_complete\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699003696360271894\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\\t\\n幂等token\",\"display_name\":\"幂等token\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.client_token\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1699003696364466187\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"任务截止时间\",\"display_name\":\"任务截止时间\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.due\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699003696364466188\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\\t\\n调用者可以传入的任意附带到任务上的数据。\",\"display_name\":\"数据\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.extra\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1699003696364466190\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\\t\\n任务的完成时刻时间戳(ms)\",\"display_name\":\"任务时间\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.completed_at\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1693909535054\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"任务成员列表，包括负责人和关注人。\",\"display_name\":\"任务成员列表\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.members\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699003696364466191\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"重复任务规则\",\"display_name\":\"重复任务规则\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.repeat_rule\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1699003696364466192\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1699270298880258050\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-05 17:57:51', 'admin', '2023-09-06 11:56:25', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1698999034292850689, '1698999034286518274', 0, '获取任务详情', '该接口用于获取任务详情，包括任务标题、描述、时间、成员等信息', 1698998146199310338, 13, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"LABEL\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"GET\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://open.feishu.cn/open-apis/task/v2/tasks/{task_guid}\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"要更新的任务全局唯一ID\",\"display_name\":\"任务全局唯一ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path.task_guid\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1693908632566\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"\\t\\n用户 ID 类型\",\"display_name\":\"用户 ID 类型\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"query.user_id_type\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1698996620955299844\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1699270332594073601\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-05 17:58:30', 'admin', '2023-09-06 11:56:33', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1698999184448933890, '1698999184446795778', 0, '更新任务', '该接口用于修改任务的标题、描述、截止时间等信息', 1698998146199310338, 13, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"LABEL\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"PATCH\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://open.feishu.cn/open-apis/task/v2/tasks/{task_guid}\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"要更新的任务全局唯一ID\",\"display_name\":\"任务全局唯一ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path.task_guid\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1693908632566\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"\\t\\n用户 ID 类型\",\"display_name\":\"用户 ID 类型\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"query.user_id_type\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1698996620955299844\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"要更新的任务数据\",\"display_name\":\"任务数据\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.task\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1698996620955299847\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\\t\\n设置需要修改的字段\",\"display_name\":\"修改的字段\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.update_fields\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1699000936923475970\",\"update\":true,\"value\":\"\",\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1699270376562962434\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-05 17:59:06', 'admin', '2023-09-06 11:56:43', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1698999363893841922, '1698999363895898114', 0, '创建评论', '为一个任务创建评论，或者回复该任务的某个评论', 1698998146199310338, 13, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"LABEL\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://open.feishu.cn/open-apis/task/v2/comments\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\\t\\n用户 ID 类型\",\"display_name\":\"用户 ID 类型\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"query.user_id_type\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1698996620955299844\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\\t\\n评论内容。不允许为空，最长3000个utf8字符。\",\"display_name\":\"评论内容\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.content\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698996620955299847\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"评论归属的资源类型，目前只支持任务“task”，默认为\\\"task\\\"。\",\"display_name\":\"资源类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.resource_type\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1699000936923475970\",\"update\":true,\"value\":\"task\",\"value_type\":\"ADAPTOR\"},{\"description\":\"\\t\\n评论归属的资源ID。当归属资源类型为\\\"task\\\"时，这里应填写任务的GUID。\",\"display_name\":\"资源ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.resource_id\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1699000936923475971\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\\t\\n回复给评论的评论ID\",\"display_name\":\"评论ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.reply_to_comment_id\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1699000936923475972\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1699270409748295681\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-05 17:59:49', 'admin', '2023-09-19 15:38:09', 'admin', 'iam', NULL, 'SINGLE');

INSERT IGNORE INTO acm.acm_app_connector (id, name, description, icon, build_type, app_domain_category, app_action_category, app_package, ver_name, ver_number, ver_type, create_time, create_by, update_time, update_by, tenant_id, deployment_type) VALUES (1699008875178610690, '飞书审批', NULL, NULL, 'Market', 'COLLABORATION', NULL, '1699008875176472577', '1.0', 1, 'RELEASE', '2023-09-05 18:37:37', 'admin', '2023-09-06 11:07:14', 'admin', 'iam', 'SaasApp');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1699009007613759489, '1699009007611621377', 0, '创建审批实例', '创建一个审批实例，调用方需对审批定义的表单有详细了解，将按照定义的表单结构，将表单 Value 通过接口传入', 1699008875178610690, 13, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://open.feishu.cn/open-apis/approval/v4/instances\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"审批定义 code\",\"display_name\":\"审批定义 code\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.approval_code\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699012778441912321\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"控件值\",\"display_name\":\"控件值\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.form\",\"page_control\":\"JSON\",\"required\":true,\"type\":\"OBJECT\",\"ui_id\":\"1699012778441912322\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"发起审批用户\",\"display_name\":\"发起审批用户\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.user_id\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1699012778441912323\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\\t\\n发起审批用户 open id\",\"display_name\":\"发起审批用户 open id\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.open_id\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1699012778441912324\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"发起审批用户部门id\",\"display_name\":\"发起审批用户部门id\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.department_id\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1693911330811\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"节点的审批人\",\"display_name\":\"节点的审批人\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.node_approver_user_id_list\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699012778441912325\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"审批人发起人自选 open id\",\"display_name\":\"审批人发起人自选 open id\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.node_approver_open_id_list\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699012778441912326\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"节点的抄送人\",\"display_name\":\"节点的抄送人\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.node_cc_user_id_list\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699012778441912327\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\\t\\n抄送人发起人自选 open id\",\"display_name\":\"\\t 抄送人发起人自选 open id\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.node_cc_open_id_list\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699012778441912328\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"审批实例 uuid\",\"display_name\":\"审批实例 uuid\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.uuid\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1693911533912\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"可配置是否可以重新提交，适用于审批人退回场景，提单人在同一实例重新提交单据\",\"display_name\":\"重新提交\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.allow_resubmit\",\"option_values\":[\"false\",\"true\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"BOOLEAN\",\"ui_id\":\"1693911552214\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\\t\\n可配置是否可以再次提交，适用于周期性提单场景，按照当前表单内容再次发起一个新实例\",\"display_name\":\"再次提交\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.allow_submit_again\",\"option_values\":[\"false\",\"true\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"BOOLEAN\",\"ui_id\":\"1693911570938\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"配置bot是否取消通知结果\",\"display_name\":\"配置bot是否取消通知结果\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.cancel_bot_notification\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1693911572214\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"配置是否可以禁止撤销\",\"display_name\":\"禁止撤销\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.forbid_revoke\",\"option_values\":[\"false\",\"true\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"BOOLEAN\",\"ui_id\":\"1693911573335\",\"update\":true,\"value\":\"false\",\"value_type\":\"ADAPTOR\"},{\"description\":\"\\t\\n国际化文案。目前只支单行、多行文本的值。\\n\\n\",\"display_name\":\"\\t 国际化文案\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.i18n_resources\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1702251338804572162\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-05 18:38:08', 'admin', '2023-09-19 15:36:36', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1699009125364649985, '1699009125362511873', 0, '获取单个审批实例详情', '通过审批实例 Instance Code 获取审批实例详情', 1699008875178610690, 13, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"LABEL\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"GET\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://open.feishu.cn/open-apis/approval/v4/instances/{instance_id}\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"审批实例 Code, 若在创建的时候传了uuid, 也可以通过传uuid获取\",\"display_name\":\"审批实例 Code\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path.instance_id\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1693910890563\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\\t\\n语言\\n\\n示例值：\\\"zh-CN\\\"\\n\\n可选值有：\\n\\nzh-CN：中文\\n\\nen-US：英文\\n\\nja-JP：日文\",\"display_name\":\"语言\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.locale\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1698996620955299844\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"发起审批用户id\",\"display_name\":\"发起审批用户id\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.user_id\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1699009860020023299\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\\t\\n用户 ID 类型\",\"display_name\":\"用户 ID 类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.user_id_type\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1699009860020023300\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1704036853501472769\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-05 18:38:36', 'admin', '2023-09-19 15:37:00', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1699009223859490818, '1699009223857352706', 0, '撤回审批实例', '对于状态为“审批中”的单个审批实例进行撤销操作，撤销后审批流程结束', 1699008875178610690, 13, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"LABEL\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://open.feishu.cn/open-apis/approval/v4/instances/cancel\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\\t\\n用户 ID 类型\",\"display_name\":\"用户 ID 类型\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"query.user_id_type\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1698996620955299844\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"审批定义 Code\",\"display_name\":\"审批定义 Code\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.approval_code\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699009860020023297\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"根据user_id_type填写操作用户id\",\"display_name\":\"操作用户id\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.user_id\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699009860020023299\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"审批实例 Code\",\"display_name\":\"审批实例 Code\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.instance_code\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699009860020023300\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1704037098255888385\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-05 18:39:00', 'admin', '2023-09-19 15:37:58', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1699009326317948930, '1699009326320005121', 0, '同意审批任务', '对于单个审批任务进行同意操作。同意后审批流程会流转到下一个审批人', 1699008875178610690, 13, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"LABEL\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://open.feishu.cn/open-apis/approval/v4/tasks/approve\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\\t\\n用户 ID 类型\",\"display_name\":\"用户 ID 类型\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"query.user_id_type\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1698996620955299844\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"审批定义 Code\",\"display_name\":\"审批定义 Code\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.approval_code\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699009860020023297\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\\t\\njson 数组\",\"display_name\":\"控件值\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.form\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699009860020023298\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"根据user_id_type填写操作用户id\",\"display_name\":\"操作用户id\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.user_id\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699009860020023299\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"审批实例 Code\",\"display_name\":\"审批实例 Code\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.instance_code\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699009860020023300\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\\t\\n意见\",\"display_name\":\" 意见\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.comment\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1699009860020023301\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\\t\\n任务 ID， 审批实例详情task_list中id\",\"display_name\":\"任务 ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.task_id\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699009860020023302\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1699270775940395009\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-05 18:39:24', 'admin', '2023-09-06 11:58:19', 'admin', 'iam', NULL, 'SINGLE');

INSERT IGNORE INTO acm.acm_app_connector (id, name, description, icon, build_type, app_domain_category, app_action_category, app_package, ver_name, ver_number, ver_type, create_time, create_by, update_time, update_by, tenant_id, deployment_type) VALUES (1699016400934391809, '飞书日历', NULL, NULL, 'Market', 'COLLABORATION', NULL, '1699016400932253698', '1.0', 1, 'RELEASE', '2023-09-05 19:07:31', 'admin', '2023-09-06 11:07:01', 'admin', 'iam', 'SaasApp');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1699016646498308097, '1699016646500364290', 0, '创建日程', NULL, 1699016400934391809, 13, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://open.feishu.cn/open-apis/calendar/v4/calendars/{calendar_id}/events\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"日历ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path.calendar_id\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1694575373800\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\",\"display_name\":\"创建日程的幂\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.idempotency_key\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1694575335408\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\",\"display_name\":\"用户 ID 类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.user_id_type\",\"option_values\":[\"open_id\",\"union_id\",\"user_id\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1694575539569\",\"update\":true,\"value\":\"\",\"value_type\":\"ADAPTOR\"},{\"display_name\":\"预期响应\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"successCondition\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1694570979667\",\"update\":true,\"value\":\"[{\\\"path\\\":\\\"status_code\\\",\\\"op\\\":\\\"eq\\\",\\\"value\\\":\\\"200\\\"},{\\\"path\\\":\\\"body.code\\\",\\\"op\\\":\\\"eq\\\",\\\"value\\\":\\\"0\\\"}]\",\"value_type\":\"ADAPTOR\"},{\"description\":\"日程标题\",\"display_name\":\"日程标题\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.summary\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701798807699795970\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"日程提醒列表\",\"display_name\":\"日程提醒列表\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.reminders\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1701798807699795971\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"参与人权限\",\"display_name\":\"参与人权限\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.attendee_ability\",\"option_values\":[\"none\",\"can_see_others\",\"can_invite_others\",\"can_modify_event\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701798807699795972\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"更新日程是否给日程参与人发送bot通知，默认为true\",\"display_name\":\"发送bot通知\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.need_notification\",\"option_values\":[\"false\",\"true\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"BOOLEAN\",\"ui_id\":\"1701798807699795973\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"视频会议URL\",\"display_name\":\"视频会议URL\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.vchat.meeting_url\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701798807699795974\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"指定主持人\",\"display_name\":\"指定主持人\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.vchat.meeting_settings.assign_hosts\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701798807699795975\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"设置自动录制\",\"display_name\":\"设置自动录制\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.vchat.meeting_settings.auto_record\",\"option_values\":[\"false\",\"true\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"BOOLEAN\",\"ui_id\":\"1701798807699795976\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\\t\\n设置会议 owner\",\"display_name\":\"\\t 设置会议 owner\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.vchat.meeting_settings.owner_id\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701798807699795977\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"允许日程参与者发起会议\",\"display_name\":\"允许日程参与者发起会议\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.vchat.meeting_settings.allow_attendees_start\",\"option_values\":[\"false\",\"true\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"BOOLEAN\",\"ui_id\":\"1701798807699795978\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"设置入会范围\",\"display_name\":\"设置入会范围\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.vchat.meeting_settings.join_meeting_permission\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701798807699795979\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"开启等候室\",\"display_name\":\"开启等候室\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.vchat.meeting_settings.open_lobby\",\"option_values\":[\"false\",\"true\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"BOOLEAN\",\"ui_id\":\"1701798807699795980\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"第三方视频会议文案，可以为空，为空展示默认文案\",\"display_name\":\"\\t 第三方视频会议文案\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.vchat.description\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701798807699795981\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"视频会议类型；可以为空，为空会在首次添加日程参与人时，自动生成飞书视频会议URL。若无需视频会议时需显式传入no_meeting。\",\"display_name\":\"视频会议类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.vchat.vc_type\",\"option_values\":[\"vc\",\"third_party\",\"no_meeting\",\"lark_live\",\"unknown\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701798807699795982\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\\t\\n第三方视频会议icon类型；可以为空，为空展示默认icon。\",\"display_name\":\"视频会议icon类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.vchat.icon_type\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701798807699795983\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\\t\\n日程公开范围，新建日程默认为Default；仅新建日程时对所有参与人生效，之后修改该属性仅对当前身份生效\",\"display_name\":\"\\t 日程公开范围\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.visibility\",\"option_values\":[\"default\",\"public\",\"private\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701798807699795984\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\\t\\n日程颜色，颜色RGB值的int32表示。仅对当前身份生效；客户端展示时会映射到色板上最接近的一种颜色；值为0或-1时默认跟随日历颜色。\",\"display_name\":\"日程颜色\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.color\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"NUMBER\",\"ui_id\":\"1701798807699795985\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"日程结束时间\",\"display_name\":\"结束时间\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.end_time\",\"page_control\":\"JSON\",\"required\":true,\"type\":\"OBJECT\",\"ui_id\":\"1701798807699795986\",\"update\":true,\"value\":\"    \\\"end_time\\\": {         \\\"date\\\": \\\"2023-09-17\\\",         \\\"timestamp\\\": \\\"\\\",         \\\"timezone\\\": \\\"Asia/Shanghai\\\"\",\"value_type\":\"ADAPTOR\"},{\"description\":\"\\t\\n日程描述；目前不支持编辑富文本描述，如果日程描述通过客户端编辑过，更新描述会导致富文本格式丢失\",\"display_name\":\"日程描述\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.description\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701798807699795989\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\\t\\n重复日程的重复性规则\",\"display_name\":\"\\t 重复日程的重复性规则\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.recurrence\",\"page_control\":\"HIDDEN\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701798807699795990\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"日程开始时间\",\"display_name\":\"日程开始时间\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.start_time\",\"page_control\":\"JSON\",\"required\":true,\"type\":\"OBJECT\",\"ui_id\":\"1701798807699795991\",\"update\":true,\"value\":\"    \\\"start_time\\\": {         \\\"date\\\": \\\"2023-09-17\\\",         \\\"timestamp\\\": \\\"\\\",         \\\"timezone\\\": \\\"Asia/Shanghai\\\"     }\",\"value_type\":\"ADAPTOR\"},{\"description\":\"日程自定义信息；控制日程详情页的ui展示\",\"display_name\":\"日程自定义信息\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.schemas\",\"page_control\":\"HIDDEN\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1701798807699795994\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"地点地址\",\"display_name\":\"地点地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.location.address\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701798807699795995\",\"update\":true,\"value\":\"\",\"value_type\":\"ADAPTOR\"},{\"description\":\"\\t\\n地点坐标纬度信息，对于国内的地点，采用GCJ-02标准，海外地点采用WGS84标准\",\"display_name\":\"地点坐标纬度\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.location.latitude\",\"page_control\":\"HIDDEN\",\"required\":false,\"type\":\"NUMBER\",\"ui_id\":\"1701798807699795996\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"地点名称\",\"display_name\":\"地点名称\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.location.name\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701798807699795997\",\"update\":true,\"value\":\"\",\"value_type\":\"ADAPTOR\"},{\"description\":\"地点坐标经度信息，对于国内的地点，采用GCJ-02标准，海外地点采用WGS84标准\",\"display_name\":\"地点坐标经度\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.location.longitude\",\"page_control\":\"HIDDEN\",\"required\":false,\"type\":\"NUMBER\",\"ui_id\":\"1701798807699795998\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\\t\\n日程占用的忙闲状态，新建日程默认为Busy；仅新建日程时对所有参与人生效，之后修改该属性仅对当前身份生效\",\"display_name\":\"忙闲状态\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.free_busy_status\",\"option_values\":[\"busy\",\"free\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1701798807699795999\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1704036231830122497\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-05 19:08:29', 'admin', '2023-09-19 15:34:32', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1699016817856598018, '1699016817858654210', 0, '更新日程', NULL, 1699016400934391809, 13, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"PATCH\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://open.feishu.cn/open-apis/calendar/v4/calendars/{calendar_id}/events/{event_id}\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"日历ID\",\"display_name\":\"日历ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path.calendar_id\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698996620955299844\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"日程ID\",\"display_name\":\"日程ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path.calendar_id\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699009860020023297\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"用户 ID 类型\",\"display_name\":\"用户 ID 类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.user_id_type\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1699009860020023299\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"预期响应\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"successCondition\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1694571048806\",\"update\":true,\"value\":\"[{\\\"path\\\":\\\"status_code\\\",\\\"op\\\":\\\"eq\\\",\\\"value\\\":\\\"200\\\"},{\\\"path\\\":\\\"body.code\\\",\\\"op\\\":\\\"eq\\\",\\\"value\\\":\\\"0\\\"}]\",\"value_type\":\"FIX_VALUE\"}],\"type\":\"OBJECT\",\"ui_id\":\"1701780435855552514\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-05 19:09:10', 'admin', '2023-09-19 15:34:57', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1699016908118020097, '1699016908115881986', 0, '删除日程', NULL, 1699016400934391809, 13, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"DELETE\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://open.feishu.cn/open-apis/calendar/v4/calendars/{calendar_id}/events/{event_id}\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"日历ID\",\"display_name\":\"日历ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path.calendar_id\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698996620955299844\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"日程ID\",\"display_name\":\"日程ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path.event_id\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1693912748489\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\\t\\n删除日程是否给日程参与人发送bot通知\",\"display_name\":\"发送bot通知\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.need_notification\",\"option_values\":[\"false\",\"true\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"BOOLEAN\",\"ui_id\":\"1699009860020023297\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"预期响应\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"successCondition\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1694571085769\",\"update\":true,\"value\":\"[{\\\"path\\\":\\\"status_code\\\",\\\"op\\\":\\\"eq\\\",\\\"value\\\":\\\"200\\\"},{\\\"path\\\":\\\"body.code\\\",\\\"op\\\":\\\"eq\\\",\\\"value\\\":\\\"0\\\"}]\",\"value_type\":\"FIX_VALUE\"}],\"type\":\"OBJECT\",\"ui_id\":\"1704036484679544833\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-05 19:09:32', 'admin', '2023-09-19 15:35:32', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1699016999146999809, '1699016999144861698', 0, '获取日程', NULL, 1699016400934391809, 13, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"GET\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://open.feishu.cn/open-apis/calendar/v4/calendars/{calendar_id}/events/{event_id}\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"日历ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path.calendar_id\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1693910890563\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\",\"display_name\":\"日程ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path.event_id\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1693912492233\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\",\"display_name\":\"返回会前设置\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.need_meeting_settings\",\"option_values\":[\"false\",\"true\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"BOOLEAN\",\"ui_id\":\"1698996620955299844\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\",\"display_name\":\"用户 ID 类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.user_id_type\",\"option_values\":[\"open_id\",\"union_id\",\"user_id\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1699009860020023300\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"预期响应\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"successCondition\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1694571121285\",\"update\":true,\"value\":\"[{\\\"path\\\":\\\"status_code\\\",\\\"op\\\":\\\"eq\\\",\\\"value\\\":\\\"200\\\"},{\\\"path\\\":\\\"body.code\\\",\\\"op\\\":\\\"eq\\\",\\\"value\\\":\\\"0\\\"}]\",\"value_type\":\"FIX_VALUE\"}],\"type\":\"OBJECT\",\"ui_id\":\"1704036632730087426\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-05 19:09:53', 'admin', '2023-09-19 15:36:07', 'admin', 'iam', NULL, 'SINGLE');

INSERT IGNORE INTO acm.acm_app_connector (id, name, description, icon, build_type, app_domain_category, app_action_category, app_package, ver_name, ver_number, ver_type, create_time, create_by, update_time, update_by, tenant_id, deployment_type) VALUES (1699024881854828545, '飞书多维表格', NULL, NULL, 'Market', 'COLLABORATION', NULL, '1699024881852690434', '1.0', 1, 'RELEASE', '2023-09-05 19:41:13', 'admin', '2023-09-06 11:06:50', 'admin', 'iam', 'SaasApp');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1699024999433752578, '1699024999431614466', 0, '创建多维表格', '指定目录下创建多维表格', 1699024881854828545, 13, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://open.feishu.cn/open-apis/bitable/v1/apps\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"多维表格App归属文件夹\",\"display_name\":\"归属文件夹\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.folder_token\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1699025366819090434\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"多维表格App名字\",\"display_name\":\"多维表格App名字\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.name\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1699025366819090435\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"预期响应\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"successCondition\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1694569527072\",\"update\":true,\"value\":\"[{\\\"path\\\":\\\"status_code\\\",\\\"op\\\":\\\"eq\\\",\\\"value\\\":\\\"200\\\"},{\\\"path\\\":\\\"body.code\\\",\\\"op\\\":\\\"eq\\\",\\\"value\\\":\\\"0\\\"}]\",\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1704035380239609857\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-05 19:41:41', 'admin', '2023-09-19 15:31:09', 'admin', 'iam', NULL, 'SINGLE');

INSERT IGNORE INTO acm.acm_app_connector (id, name, description, icon, build_type, app_domain_category, app_action_category, app_package, ver_name, ver_number, ver_type, create_time, create_by, update_time, update_by, tenant_id, deployment_type) VALUES (1699025730102480897, '飞书OCR', NULL, NULL, 'Market', 'DATA_SERVICE', NULL, '1699025730100342786', '1.0', 1, 'RELEASE', '2023-09-05 19:44:35', 'admin', '2023-09-06 11:06:39', 'admin', 'iam', 'SaasApp');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1699025871945453570, '1699025871947509762', 0, '基础图片识别', NULL, 1699025730102480897, 13, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://open.feishu.cn/open-apis/optical_char_recognition/v1/image/basic_recognize\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"图片数据\",\"display_name\":\"图片数据\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.image\",\"page_control\":\"TEXTAREA\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1699025366819090435\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"预期响应\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"successCondition\",\"page_control\":\"TEXTAREA\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1694567710554\",\"update\":true,\"value\":\"[{\\\"path\\\":\\\"status_code\\\",\\\"op\\\":\\\"eq\\\",\\\"value\\\":\\\"200\\\"},{\\\"path\\\":\\\"body.code\\\",\\\"op\\\":\\\"eq\\\",\\\"value\\\":\\\"0\\\"}]\",\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1704035250602061826\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-05 19:45:09', 'admin', '2023-09-19 15:30:38', 'admin', 'iam', NULL, 'SINGLE');

INSERT IGNORE INTO acm.acm_app_connector (id, name, description, icon, build_type, app_domain_category, app_action_category, app_package, ver_name, ver_number, ver_type, create_time, create_by, update_time, update_by, tenant_id, deployment_type) VALUES (1699026452546179074, '飞书ASR', NULL, NULL, 'Market', 'DATA_SERVICE', NULL, '1699026452544040962', '1.0', 1, 'RELEASE', '2023-09-05 19:47:27', 'admin', '2023-09-06 11:59:39', 'admin', 'iam', 'SaasApp');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1699026674647158785, '1699026674649214977', 0, '识别语音文件', '语音文件识别接口，上传整段语音文件进行一次性识别。接口适合 60 秒以内音频识别', 1699026452546179074, 13, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://open.feishu.cn/open-apis/speech_to_text/v1/speech/file_recognize\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"语音资源\",\"display_name\":\"语音资源\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.speech.speech\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699025366819090435\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"仅包含字母数字和下划线的 16 位字符串作为文件的标识，用户生成\",\"display_name\":\"文件的标识\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.config.file_id\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1694566911681\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"语音格式，目前仅支持：pcm\",\"display_name\":\"语音格式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.config.format\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1694566990792\",\"update\":true,\"value\":\"pcm\",\"value_type\":\"ADAPTOR\"},{\"description\":\"引擎类型，目前仅支持：16k_auto 中英混合\",\"display_name\":\"引擎类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.config.engine_type\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1694566991621\",\"update\":true,\"value\":\"16k_auto\",\"value_type\":\"ADAPTOR\"},{\"display_name\":\"预期响应\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"successCondition\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1694567486812\",\"update\":true,\"value\":\"[{\\\"path\\\":\\\"status_code\\\",\\\"op\\\":\\\"eq\\\",\\\"value\\\":\\\"200\\\"},{\\\"path\\\":\\\"body.code\\\",\\\"op\\\":\\\"eq\\\",\\\"value\\\":\\\"0\\\"}]\",\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1704035128715587586\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-05 19:48:20', 'admin', '2024-04-08 11:44:12', 'admin', 'iam', NULL, 'SINGLE');

-- 金智CRM
INSERT IGNORE INTO acm.acm_app_connector (id, name, description, icon, build_type, app_domain_category, app_action_category, app_package, ver_name, ver_number, ver_type, create_time, create_by, update_time, update_by, tenant_id, deployment_type) VALUES (1698938901101662209, '金智CRM', NULL, NULL, 'Market', 'CRM_ERP', NULL, '1698938901099524097', '1.0', 1, 'RELEASE', '2023-09-05 13:59:33', 'admin', '2024-03-21 17:53:28', 'admin', 'iam', 'SaasApp');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1698939022740672514, '1698939022738534401', 0, '新增或编辑客户资料', '新增或编辑客户资料', 1698938901101662209, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://oapi.dingtalk.com/v1.0/jzcrm/customers\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"数据类型，固定值148\",\"display_name\":\"数据类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.datatype\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"NUMBER\",\"ui_id\":\"1698940225627172868\",\"update\":true,\"value\":\"148\",\"value_type\":\"ADAPTOR\"},{\"description\":\"时间戳，单位：秒\",\"display_name\":\"时间戳\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.stamp\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"NUMBER\",\"ui_id\":\"1698940225627172869\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"数据ID\",\"display_name\":\"数据ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.msgid\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"NUMBER\",\"ui_id\":\"1698940225627172870\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"编辑数据\",\"display_name\":\"编辑数据\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.data\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1704041724514480130\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-05 14:00:02', 'admin', '2024-04-08 11:43:57', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1698941363510431745, '1698941363512487937', 0, '新增或编辑销售机会', '调用本接口新增或编辑销售机会', 1698938901101662209, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://oapi.dingtalk.com/v1.0/jzcrm/sales\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"数据类型，固定值148\",\"display_name\":\"数据类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.datatype\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"NUMBER\",\"ui_id\":\"1698940225627172868\",\"update\":true,\"value\":\"158\",\"value_type\":\"ADAPTOR\"},{\"description\":\"时间戳，单位：秒\",\"display_name\":\"时间戳\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.stamp\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"NUMBER\",\"ui_id\":\"1698940225627172869\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"数据ID\",\"display_name\":\"数据ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.msgid\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"NUMBER\",\"ui_id\":\"1698940225627172870\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"编辑数据\",\"display_name\":\"编辑数据\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.data\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1704041892081119234\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-05 14:09:21', 'admin', '2023-09-19 15:57:01', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1698942070275821569, '1698942070273683458', 0, '新增或编辑报价', '调用本接口新增或编辑报价记录', 1698938901101662209, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://oapi.dingtalk.com/v1.0/jzcrm/quotationRecords\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"数据类型，固定值148\",\"display_name\":\"数据类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.datatype\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"NUMBER\",\"ui_id\":\"1698940225627172868\",\"update\":true,\"value\":\"161\",\"value_type\":\"ADAPTOR\"},{\"description\":\"时间戳，单位：秒\",\"display_name\":\"时间戳\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.stamp\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"NUMBER\",\"ui_id\":\"1698940225627172869\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"数据ID\",\"display_name\":\"数据ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.msgid\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"NUMBER\",\"ui_id\":\"1698940225627172870\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"编辑数据\",\"display_name\":\"编辑数据\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.data\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1704042034037338113\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-05 14:12:09', 'admin', '2023-09-19 15:57:35', 'admin', 'iam', NULL, 'SINGLE');

-- 云身份
INSERT IGNORE INTO acm.acm_app_connector (id, name, description, icon, build_type, app_domain_category, app_action_category, app_package, ver_name, ver_number, ver_type, create_time, create_by, update_time, update_by, tenant_id, deployment_type) VALUES (1698982786456735746, '云身份连接器', '获取集成平台的信息', NULL, 'BuiltIn', 'DS_AREA', NULL, '1698982786454597633', '1.0', 1, 'RELEASE', '2023-09-05 16:53:57', 'admin', '2024-10-23 10:03:49', 'admin', 'iam', 'MixApp');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1698984422084300801, '1698984422086356994', 0, '查询用户信息', '根据手机号|登录名|工号查询用户信息', 1698982786456735746, 2, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"GET\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://IP/iam/api/users\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"租户ID\",\"display_name\":\"租户ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"header.tcode\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1693904975310\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"用户属性\",\"display_name\":\"用户属性\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.attrs\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1693905025334\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"姓名/用户名/手机/邮箱（模糊查询）\",\"display_name\":\"查询条件\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.q\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1693905072096\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"部门id\",\"display_name\":\"部门id\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.org_id\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1693905073320\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"页码\",\"display_name\":\"页码\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.page\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1693905074827\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"每页条数\",\"display_name\":\"每页条数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.size\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1693905076660\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"是否查询子组/子部门下的用户：\\n如果true，则查询子组/子部门下的用户，\\n如果false,则不查询子组/子部门下的用户\",\"display_name\":\"是否查询子部门\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.return_users_in_sub_org\",\"option_values\":[\"true\",\"false\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1771060912148566017\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-05 17:00:26', 'admin', '2024-08-16 10:39:11', 'linkadmin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1698988408942219265, '1698988408940081153', 0, '根据登录名查询用户信息', NULL, 1698982786456735746, 2, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"LABEL\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"GET\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://IP/iam/api/user\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"租户ID\",\"display_name\":\"租户ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"header.tcode\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1693904975310\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"不指定则返回所有属性\",\"display_name\":\"用户属性\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.attrs\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1693905025334\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"用户username\",\"display_name\":\"用户账号\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.username\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1693905072096\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"[{\\\"path\\\":\\\"status_code\\\",\\\"op\\\":\\\"eq\\\",\\\"value\\\":\\\"200\\\"}]\",\"display_name\":\"预期响应\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"successCondition\\t\",\"page_control\":\"HIDDEN\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1777184065467822082\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-05 17:16:17', 'admin', '2024-08-16 10:41:48', 'linkadmin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1844991603493040129, '1844991603419291650', 0, '获取中间用户信息', '根据用户ID获取中间表的部分用户信息', 1698982786456735746, 2, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619266\",\"update\":true,\"value\":\"GET\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619267\",\"update\":true,\"value\":\"http://IP/iam/api/linkUser/byIds\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619268\",\"update\":true,\"value\":\"application/json; charset=UTF-8\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"header\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619269\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"必传参数 ids多个用\\\",\\\"隔开；syncDirection 同步方向 多个用“,”隔开\",\"display_name\":\"Query参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619270\",\"update\":true,\"value_type\":\"JS_EXP\"}],\"type\":\"OBJECT\",\"ui_id\":\"1855945392192045057\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2024-10-12 14:40:51', 'admin', '2024-11-11 20:07:18', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1844992823418941442, '1844992823395524610', 0, '根据指定条件查询用户信息', '根据指定条件查询指定属性的用户信息', 1698982786456735746, 2, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619266\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619267\",\"update\":true,\"value\":\"http://IP/iam/api/users/mapSearch\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619268\",\"update\":true,\"value\":\"application/json; charset=UTF-8\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"header\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619269\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"attrs 查询指定属性 多个用“,”隔开\",\"display_name\":\"Query参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619270\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Body参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619271\",\"update\":true,\"value_type\":\"JS_EXP\"}],\"type\":\"OBJECT\",\"ui_id\":\"1856210571895062530\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2024-10-12 14:45:42', 'admin', '2024-11-12 13:41:02', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1848551860316041217, '1848551860263378946', 0, '根据外部UnionId获取用户', NULL, 1698982786456735746, 2, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619266\",\"update\":true,\"value\":\"GET\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619267\",\"update\":true,\"value\":\"http://IP/iam/api/linkUser/byUnionIds\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619268\",\"update\":true,\"value\":\"application/json; charset=UTF-8\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"header\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619269\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"必传参数 unionIds多个用\\\",\\\"隔开\",\"display_name\":\"Query参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619270\",\"update\":true,\"value_type\":\"JS_EXP\"}],\"type\":\"OBJECT\",\"ui_id\":\"1855945266044157954\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2024-10-22 10:28:03', 'admin', '2024-11-11 20:06:48', 'admin', 'iam', NULL, 'SINGLE');

-- Teambition
INSERT IGNORE INTO acm.acm_app_connector (id, name, description, icon, build_type, app_domain_category, app_action_category, app_package, ver_name, ver_number, ver_type, create_time, create_by, update_time, update_by, tenant_id, deployment_type) VALUES (1698893745929441282, 'Teambition', NULL, NULL, 'Market', 'COLLABORATION', NULL, '1698893745927303170', '1.0', 1, 'RELEASE', '2023-09-05 11:00:08', 'admin', '2023-09-06 11:10:51', 'admin', 'iam', 'SaasApp');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1698894784443305985, '1698894784441167873', 0, '创建项目', '调用本接口创建项目', 1698893745929441282, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://oapi.dingtalk.com/v1.0/project/users/{userId}/projects\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"操作者userId\",\"display_name\":\"操作者userId\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path.userId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1693883316728\",\"update\":true,\"value\":\"\",\"value_type\":\"ADAPTOR\"},{\"description\":\"项目名称\",\"display_name\":\"项目名称\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.name\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1704043824053039106\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-05 11:04:15', 'admin', '2023-09-19 16:04:42', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1698897565480767489, '1698897565474435074', 0, '创建项目任务', '调用本接口，创建一个钉钉项目任务', 1698893745929441282, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://oapi.dingtalk.com/v1.0/project/users/{userId}/tasks\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"操作者userId\",\"display_name\":\"操作者userId\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path.userId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1693883316728\",\"update\":true,\"value\":\"\",\"value_type\":\"ADAPTOR\"},{\"description\":\"任务备注\",\"display_name\":\"任务备注\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.note\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1698898148176506881\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"任务执行者userId\",\"display_name\":\"任务执行者userId\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.executorId\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1698898148176506882\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"任务截止时间，格式：YYYY-MM-DDTHH:mm:ssZ（ISO 8601/RFC 3339）\",\"display_name\":\"任务截止时间\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.dueDate\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1698898148176506883\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"任务优先级\",\"display_name\":\"任务优先级\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.priority\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"NUMBER\",\"ui_id\":\"1698898148176506884\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"项目id\",\"display_name\":\"项目id\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.projectId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698898148176506885\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"自定义字段列表\",\"display_name\":\"自定义字段列表\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.customfields\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1698898148176506886\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"任务标题\",\"display_name\":\"任务标题\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.content\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1698898148176506887\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"任务列表ID\",\"display_name\":\"任务列表ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.stageId\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1693884262431\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"父任务id\",\"display_name\":\"父任务id\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.parentTaskId\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1693884262435\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"任务类型id\",\"display_name\":\"任务类型id\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.scenariofieldconfigId\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1693884288938\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"任务开始时间\",\"display_name\":\"任务开始时间\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.startDate\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1693884290314\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"任务的可见性规则\",\"display_name\":\"任务的可见性规则\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.visible\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1704044188932321282\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-05 11:15:18', 'admin', '2023-09-19 16:06:16', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1698900611833122818, '1698900611830984705', 0, '删除任务', '删除任务', 1698893745929441282, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"DELETE\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://oapi.dingtalk.com/v1.0/project/users/{userId}/tasks/{taskId}\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"操作者userId\",\"display_name\":\"操作者userId\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path.userId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1693883316728\",\"update\":true,\"value\":\"\",\"value_type\":\"ADAPTOR\"},{\"description\":\"任务ID\",\"display_name\":\"任务ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path.taskId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1704044349486084098\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-05 11:27:25', 'admin', '2023-09-19 16:06:47', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1698901214902734849, '1698901214900596737', 0, '获取任务详情', '调用本接口获取任务详情', 1698893745929441282, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"GET\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://oapi.dingtalk.com/v1.0/project/users/{userId}/tasks\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"操作者userId\",\"display_name\":\"操作者userId\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path.userId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1693883316728\",\"update\":true,\"value\":\"\",\"value_type\":\"ADAPTOR\"},{\"description\":\"任务ID\",\"display_name\":\"任务ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.taskId\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"父任务ID\",\"display_name\":\"父任务ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.parentTaskId\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1704044471427084289\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-05 11:29:48', 'admin', '2023-09-19 16:07:22', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (1698902486754119682, '1698902486751981570', 0, '更新任务工作流状态', '调用本接口更新任务工作流状态', 1698893745929441282, 6, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"PUT\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"https://oapi.dingtalk.com/v1.0/project/users/{userId}/tasks/{taskId}/taskflowStatuses\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"操作者userId\",\"display_name\":\"操作者userId\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path.userId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1693883316728\",\"update\":true,\"value\":\"\",\"value_type\":\"ADAPTOR\"},{\"description\":\"任务ID\",\"display_name\":\"任务ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path.taskId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"任务状态ID\",\"display_name\":\"任务状态ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.taskflowStatusId\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\\t\\n任务流转说明\",\"display_name\":\"\\t 任务流转说明\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.tfsUpdateNote\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1704044641619357698\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-09-05 11:34:52', 'admin', '2023-09-19 16:08:01', 'admin', 'iam', NULL, 'SINGLE');



insert into acm_app_action(id,action_key,is_trigger,name,description,connector_id,account_schema_id,protocol,node_struct,config_schema,create_time,update_time,tenant_id) values(86,'http-loop',0,'自循环的HTTP请求','自循环HTTP请求，内置response变量，用于分页查询等场景',4,null,'LOOP_HTTP','LOOP','',now(),now(),'iam');
update acm_app_action set config_schema='{"description":"自循环的HTTP请求","display_name":"自循环的HTTP请求","multi_valued":false,"mutability":"readWrite","name":"root","page_control":"FIXED_OBJ","required":true,"sub_params":[{"description":"","display_name":"请求类型","multi_valued":false,"mutability":"readWrite","name":"httpMethod","option_values":["GET","POST","PUT","PATCH","DELETE"],"page_control":"SELECT","required":true,"type":"STRING","value_type":"FIX_VALUE"},{"description":"请填写完整的接口地址","display_name":"请求地址","multi_valued":false,"mutability":"readWrite","name":"endpoint","page_control":"TEXT","required":true,"type":"STRING","value_type":"ADAPTOR"},{"description":"","display_name":"应答编码","multi_valued":false,"mutability":"readWrite","name":"encoding","option_values":["UTF8","GBK"],"page_control":"SELECT","required":true,"type":"STRING","value_type":"FIX_VALUE"},{"description":"","display_name":"Header参数","multi_valued":false,"mutability":"readWrite","name":"header","page_control":"EXTEND_OBJ","required":false,"type":"OBJECT","value_type":"JS_EXP"},{"description":"","display_name":"Path参数","multi_valued":false,"mutability":"readWrite","name":"path","page_control":"EXTEND_OBJ","required":false,"type":"OBJECT","value_type":"JS_EXP"},{"description":"","display_name":"Query参数","multi_valued":false,"mutability":"readWrite","name":"query","page_control":"EXTEND_OBJ","required":false,"type":"OBJECT","value_type":"JS_EXP"},{"description":"Body参数传字符串时需用引号括起来","display_name":"Body参数","multi_valued":false,"mutability":"readWrite","name":"body","page_control":"TEXTAREA","required":false,"type":"OBJECT","value_type":"JS_EXP"},{"description":"循环执行的条件表达式，true继续循环，false结束循环","display_name":"循环条件","multi_valued":false,"mutability":"readWrite","name":"loop","page_control":"TEXT","required":true,"type":"BOOLEAN","value_type":"JS_EXP"}],"type":"OBJECT","value_type":"JS_EXP"}' where id=86;

insert into acm_app_action(id,action_key,is_trigger,name,description,connector_id,account_schema_id,protocol,node_struct,config_schema,create_time,update_time,tenant_id) values(112,'level-loop',0,'自循环的HTTP请求，并按层级输出','自循环HTTP请求，并按层级输出，内置response变量，用于部门分页查询等场景',4,null,'LEVEL_HTTP','LOOP','',now(),now(),'iam');
update acm_app_action set config_schema='{"description":"自循环的HTTP请求","display_name":"自循环的HTTP请求","multi_valued":false,"mutability":"readWrite","name":"root","page_control":"FIXED_OBJ","required":true,"sub_params":[{"description":"","display_name":"请求类型","multi_valued":false,"mutability":"readWrite","name":"httpMethod","option_values":["GET","POST","PUT","PATCH","DELETE"],"page_control":"SELECT","required":true,"type":"STRING","value_type":"FIX_VALUE"},{"description":"请填写完整的接口地址","display_name":"请求地址","multi_valued":false,"mutability":"readWrite","name":"endpoint","page_control":"TEXT","required":true,"type":"STRING","value_type":"ADAPTOR"},{"description":"","display_name":"应答编码","multi_valued":false,"mutability":"readWrite","name":"encoding","option_values":["UTF8","GBK"],"page_control":"SELECT","required":true,"type":"STRING","value_type":"FIX_VALUE"},{"description":"","display_name":"Header参数","multi_valued":false,"mutability":"readWrite","name":"header","page_control":"EXTEND_OBJ","required":false,"type":"OBJECT","value_type":"JS_EXP"},{"description":"","display_name":"Path参数","multi_valued":false,"mutability":"readWrite","name":"path","page_control":"EXTEND_OBJ","required":false,"type":"OBJECT","value_type":"JS_EXP"},{"description":"","display_name":"Query参数","multi_valued":false,"mutability":"readWrite","name":"query","page_control":"EXTEND_OBJ","required":false,"type":"OBJECT","value_type":"JS_EXP"},{"description":"Body参数传字符串时需用引号括起来","display_name":"Body参数","multi_valued":false,"mutability":"readWrite","name":"body","page_control":"TEXTAREA","required":false,"type":"OBJECT","value_type":"JS_EXP"},{"description":"循环执行的条件表达式，true继续循环，false结束循环","display_name":"循环条件","multi_valued":false,"mutability":"readWrite","name":"loop","page_control":"TEXT","required":true,"type":"BOOLEAN","value_type":"JS_EXP"},{"description":"通过js表达式从应答结果response中提取要输出的对象列表","display_name":"列表提取","multi_valued":true,"mutability":"readWrite","name":"levelList","page_control":"TEXT","required":true,"type":"OBJECT","value_type":"ADAPTOR"},{"description":"唯一标识该对象的字段名","display_name":"ID字段名","multi_valued":false,"mutability":"readWrite","name":"objId","page_control":"TEXT","required":true,"type":"STRING","value_type":"FIX_VALUE"},{"description":"该对象的父ID字段名","display_name":"父ID字段名","multi_valued":false,"mutability":"readWrite","name":"objPid","page_control":"TEXT","required":true,"type":"STRING","value_type":"FIX_VALUE"}],"type":"OBJECT","value_type":"JS_EXP"}' where id=112;

insert into acm_app_account(id,name,auth_type,auth_schema,create_time,update_time,tenant_id) values (34, 'AD/LDAP认证', 'LDAP', '{"description":"AD/LDAP认证配置","displayName":"AD/LDAP认证配置","multiValued":false,"mutability":"readWrite","name":"root","subParams":[{"description":"服务器地址：IP或域名。","displayName":"服务器地址","multiValued":false,"name":"host","type":"STRING","required":true,"page_control":"TEXT","valueType":"FIX_VALUE"},{"description":"示例：389，请填写AD/LDAP端口。","displayName":"端口","multiValued":false,"name":"port","type":"NUMBER","required":true,"page_control":"TEXT","valueType":"FIX_VALUE"},{"description":"是否开启TLS。","displayName":"TLS","multiValued":false,"name":"tls","type":"STRING","required":true,"option_values":["关闭","开启"],"page_control":"SELECT","valueType":"FIX_VALUE"},{"description":"","displayName":"BaseDN","multiValued":false,"name":"baseDn","type":"STRING","required":true,"page_control":"TEXT","valueType":"FIX_VALUE"},{"description":"管理员账号的DN。","displayName":"管理员账号","multiValued":false,"name":"username","type":"STRING","required":true,"page_control":"TEXT","valueType":"FIX_VALUE"},{"description":"管理员密码。","displayName":"密码","multiValued":false,"name":"password","type":"STRING","required":true,"page_control":"PASSWORD","valueType":"FIX_VALUE"}],"type":"OBJECT","valueType":"JS_EXP"}', now(), now(), 'iam');
insert into acm_app_connector(id, name, description, build_type, app_package, app_domain_category, ver_name, ver_number, create_time, update_time, tenant_id) values (36,'Windows AD','Windows AD','BuiltIn','ad','DATA_PROTOCOL','1.0',1,now(),now(),'iam');
insert into acm_app_action(id,action_key,is_trigger,name,description,connector_id,account_schema_id,protocol,node_struct,config_schema,create_time,update_time,tenant_id) values(118,'ad-search',0,'遍历AD部门和用户','根据BaseDN遍历全部部门和用户',36,34,'LOOP_AD','LOOP','',now(),now(),'iam');
update acm_app_action set config_schema='{"display_name":"root","multi_valued":false,"mutability":"readWrite","name":"root","page_control":"FIXED_OBJ","required":false,"type":"OBJECT","value_type":"JS_EXP","sub_params":[
           {"description":"根节点的BaseDN","display_name":"BaseDN","multi_valued":false,"mutability":"readWrite","name":"base_dn","page_control":"TEXT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"},
           {"description":"部门搜索条件","display_name":"部门搜索条件","multi_valued":false,"mutability":"readWrite","name":"dept_filter","value":"(objectclass=organizationalUnit)","page_control":"TEXT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"},
           {"description":"用户搜索条件","display_name":"用户搜索条件","multi_valued":false,"mutability":"readWrite","name":"user_filter","value":"(&(&(objectclass=user)(objectCategory=person))(!(userAccountControl:1.2.840.113556.1.4.803:=2)))","page_control":"TEXT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"},
           {"description":"遍历时是否输出部门信息","display_name":"输出部门","multi_valued":false,"mutability":"readWrite","name":"output_org","option_values":["是","否"],"page_control":"SELECT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"},
           {"description":"遍历时是否输出用户信息","display_name":"输出用户","multi_valued":false,"mutability":"readWrite","name":"output_user","option_values":["是","否"],"page_control":"SELECT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"}
           ]}' where id=118;

insert into acm_app_connector(id, name, description, build_type, app_package, app_domain_category, ver_name, ver_number, create_time, update_time, tenant_id) values (35,'LDAP','LDAP','BuiltIn','ldap','DATA_PROTOCOL','1.0',1,now(),now(),'iam');
insert into acm_app_action(id,action_key,is_trigger,name,description,connector_id,account_schema_id,protocol,node_struct,config_schema,create_time,update_time,tenant_id) values(119,'search-dept-byDN',0,'按DN逐级遍历部门','适用于通过DN确定部门上下级关系的场景',35,34,'LOOP_DEPT_BY_DN','LOOP','',now(),now(),'iam');
update acm_app_action set config_schema='{"display_name":"root","multi_valued":false,"mutability":"readWrite","name":"root","page_control":"FIXED_OBJ","required":false,"type":"OBJECT","value_type":"JS_EXP","sub_params":[
           {"description":"搜索根节点的BaseDN","display_name":"BaseDN","multi_valued":false,"mutability":"readWrite","name":"base_dn","page_control":"TEXT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"},
           {"description":"部门搜索条件","display_name":"部门搜索条件","multi_valued":false,"mutability":"readWrite","name":"dept_filter","value":"(objectclass=organizationalUnit)","page_control":"TEXT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"},
           {"description":"作为部门ID的属性名，一般用entryUUID","display_name":"部门ID属性名","multi_valued":false,"mutability":"readWrite","name":"dept_id_name","value":"entryUUID","page_control":"TEXT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"},
           {"description":"作为部门父ID的逻辑属性名，一般用parentEntryUUID","display_name":"部门父ID属性名","multi_valued":false,"mutability":"readWrite","name":"dept_pid_name","value":"parentEntryUUID","page_control":"TEXT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"}
           ]}' where id=119;

insert into acm_app_action(id,action_key,is_trigger,name,description,connector_id,account_schema_id,protocol,node_struct,config_schema,create_time,update_time,tenant_id) values(120,'search-all-dept',0,'按BaseDN一次性搜索全部部门','适用于通过指定部门父ID属性确定父部门的场景',35,34,'LOOP_LDAP_DEPT','LOOP','',now(),now(),'iam');
update acm_app_action set config_schema='{"display_name":"root","multi_valued":false,"mutability":"readWrite","name":"root","page_control":"FIXED_OBJ","required":false,"type":"OBJECT","value_type":"JS_EXP","sub_params":[
           {"description":"搜索根节点的BaseDN","display_name":"BaseDN","multi_valued":false,"mutability":"readWrite","name":"base_dn","page_control":"TEXT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"},
           {"description":"部门搜索条件","display_name":"部门搜索条件","multi_valued":false,"mutability":"readWrite","name":"dept_filter","value":"(objectclass=organizationalUnit)","page_control":"TEXT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"},
           {"description":"作为部门ID的属性名，一般用entryUUID","display_name":"部门ID属性名","multi_valued":false,"mutability":"readWrite","name":"dept_id_name","value":"entryUUID","page_control":"TEXT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"},
           {"description":"作为部门父ID的属性名，一般用parentEntryUUID","display_name":"部门父ID属性名","multi_valued":false,"mutability":"readWrite","name":"dept_pid_name","value":"parentEntryUUID","page_control":"TEXT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"}
           ]}' where id=120;

insert into acm_app_action(id,action_key,is_trigger,name,description,connector_id,account_schema_id,protocol,node_struct,config_schema,create_time,update_time,tenant_id) values(121,'search-all-user',0,'按BaseDN一次性搜索全部用户','适用于用户不直接挂在部门DN下的场景',35,34,'LOOP_LDAP_USER','LOOP','',now(),now(),'iam');
update acm_app_action set config_schema='{"display_name":"root","multi_valued":false,"mutability":"readWrite","name":"root","page_control":"FIXED_OBJ","required":false,"type":"OBJECT","value_type":"JS_EXP","sub_params":[
           {"description":"搜索根节点的BaseDN","display_name":"BaseDN","multi_valued":false,"mutability":"readWrite","name":"base_dn","page_control":"TEXT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"},
           {"description":"用户搜索条件","display_name":"用户搜索条件","multi_valued":false,"mutability":"readWrite","name":"user_filter","value":"(objectclass=organizationalPerson)","page_control":"TEXT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"}
           ]}' where id=121;

insert into acm_app_action(id,action_key,is_trigger,name,description,connector_id,account_schema_id,protocol,node_struct,config_schema,create_time,update_time,tenant_id) values(122,'search-all-byDN',0,'按DN逐级遍历部门和用户','适用于通过DN确定部门上下级、部门和用户关系的场景',35,34,'LOOP_ALL_BY_DN','LOOP','',now(),now(),'iam');
update acm_app_action set config_schema='{"display_name":"root","multi_valued":false,"mutability":"readWrite","name":"root","page_control":"FIXED_OBJ","required":false,"type":"OBJECT","value_type":"JS_EXP","sub_params":[
           {"description":"根节点的BaseDN","display_name":"BaseDN","multi_valued":false,"mutability":"readWrite","name":"base_dn","page_control":"TEXT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"},
           {"description":"部门搜索条件","display_name":"部门搜索条件","multi_valued":false,"mutability":"readWrite","name":"dept_filter","value":"(objectclass=organizationalUnit)","page_control":"TEXT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"},
           {"description":"用户搜索条件","display_name":"用户搜索条件","multi_valued":false,"mutability":"readWrite","name":"user_filter","value":"(objectclass=organizationalPerson)","page_control":"TEXT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"},
           {"description":"遍历时是否输出部门信息","display_name":"输出部门","multi_valued":false,"mutability":"readWrite","name":"output_org","option_values":["是","否"],"page_control":"SELECT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"},
           {"description":"遍历时是否输出用户信息","display_name":"输出用户","multi_valued":false,"mutability":"readWrite","name":"output_user","option_values":["是","否"],"page_control":"SELECT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"},
           {"description":"作为部门ID的属性名，一般用entryUUID","display_name":"部门ID属性名","multi_valued":false,"mutability":"readWrite","name":"dept_id_name","value":"entryUUID","page_control":"TEXT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"},
           {"description":"作为部门父ID的逻辑属性名，一般用parentEntryUUID","display_name":"部门父ID属性名","multi_valued":false,"mutability":"readWrite","name":"dept_pid_name","value":"parentEntryUUID","page_control":"TEXT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"},
           {"description":"作为用户所属部门的逻辑属性名，用来记录用户所属的部门ID","display_name":"用户所属部门ID属性名","multi_valued":false,"mutability":"readWrite","name":"user_dept_name","value":"department_ids","page_control":"TEXT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"}
           ]}' where id=122;


-- AD通讯录集成模版
INSERT INTO `acm`.`acm_flow_instance` (`id`, `name`, `description`, `flow_type`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `op_status`, `api_key`, `category`, `template`, `is_deleted`) VALUES (1887751465322188801, 'AD通讯录集成', 'AD通讯录集成模版', 1, 'IDLE', NULL, NULL, NULL, '2025-02-07 14:33:17', 'anonymousUser', '2025-02-07 18:08:17', 'admin', 'iam', 'ONLINE', '1c31d595e02a41c8a2ab307e56d66dda', '通讯录集成', 1, 0);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1887751466108653569, 'StartEventNode', 1887770122504630274, 81, 'user_sync', NULL, 1887751465322188801, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1851605414348050433\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1851605414348050434\",\"option_values\":[\"异步\",\"同步\"],\"description\":\"异步：
webhook接收到数据后，将直接返回接收成功的结果以及流的信息。\\n同步：Webhook接收到数据后，将等待连接流执行完成并将最后的执行结果返回给调用方（最长等待时间为10s，超时将返回为空）。请查看接口文档获得更多详细信息\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"连接流执行方式\",\"required\":true,\"name\":\"type\",\"mutability\":\"readWrite\",\"value\":\"异步\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1851605414348050435\",\"name\":\"value.org_total\",\"description\":\"预估总的外部部门个数，用于估算集成进度\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"预估部门个数\",\"value\":\"300\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1851605414348050436\",\"name\":\"value.user_total\",\"description\":\"预估总的外部用户个数，用于估算集成进度\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"预估用户个数\",\"value\":\"1000\",\"required\":true,\"page_control\":\"TEXT\"}],\"name\":\"root\",\"description\":\"启动通讯录集成\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"启动通讯录集成\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', NULL, '{\"hook_url\":null,\"sync\":false,\"type\":\"异步\",\"value\":{\"org_total\":300,\"user_total\":1000}}', 'SUCCESS', 'OK', '2025-02-07 14:54:54', '2025-02-07 14:54:54', '2025-02-07 14:33:17', 'anonymousUser', '2025-02-07 15:47:10', 'admin', 'iam', 31, 0, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1887751466108653571, 'EndNode', NULL, NULL, NULL, NULL, 1887751465322188801, NULL, NULL, NULL, NULL, 'IDLE', NULL, NULL, NULL, '2025-02-07 14:33:17', 'anonymousUser', '2025-02-07 14:33:17', 'anonymousUser', 'iam', NULL, 0, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1887751466108653572, 'ActionNode', 1887751466108653571, 89, 'complete_user_sync', NULL, 1887751465322188801, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1851607623072731137\",\"sub_params\":[],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', NULL, '{\"code\":\"0\",\"data\":null,\"message\":\"SUCCESS\"}', 'INVALID', 'OK', '2024-12-09 13:52:38', '2024-12-09 13:52:38', '2025-02-07 14:33:17', 'anonymousUser', '2025-02-07 14:33:17', 'anonymousUser', 'iam', 31, 0, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1887770122504630274, 'ActionNode', 1887751466108653572, 118, 'ad-search', 1882350888685322241, 1887751465322188801, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1887770134112776193\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1887770134112776194\",\"name\":\"base_dn\",\"description\":\"根节点的BaseDN\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"BaseDN\",\"value\":\"OU=vic_sso1,DC=digital,DC=com\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1887770134112776195\",\"name\":\"dept_filter\",\"description\":\"部门搜索条件\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"部门搜索条件\",\"value\":\"(objectclass=organizationalUnit)\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1887770134112776196\",\"name\":\"user_filter\",\"description\":\"用户搜索条件\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"用户搜索条件\",\"value\":\"(&(&(objectclass=user)(objectCategory=person))(!(userAccountControl:1.2.840.113556.1.4.803:=2)))\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1887770134112776197\",\"option_values\":[\"是\",\"否\"],\"description\":\"遍历时是
否输出部门信息\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"输出部门\",\"required\":true,\"name\":\"output_org\",\"mutability\":\"readWrite\",\"value\":\"是\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1887770134112776198\",\"option_values\":[\"是\",\"否\"],\"description\":\"遍历时是否输出用户
信息\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"输出用户\",\"required\":true,\"name\":\"output_user\",\"mutability\":\"readWrite\",\"value\":\"是\",\"page_control\":\"SELECT\"}],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', NULL, '{\"current\":{\"dept\":[{\"whenCreated\":\"20250117102934.0Z\",\"uSNChanged\":\"825887\",\"ou\":\"子部门\",\"instanceType\":\"4\",\"objectClass\":\"top\",\"distinguishedName\":\"OU=子部门,OU=vic_sso1,DC=digital,DC=com\",\"dn\":\"OU=子部门,OU=vic_sso1,DC=digital,DC=com\",\"whenChanged\":\"20250117102934.0Z\",\"parentObjectGUID\":\"8dcbb744-**************-7db4a0f830c0\",\"objectGUID\":\"bddc54ad-b007-465a-87c0-43413ad2374b\",\"name\":\"子部门\",\"dSCorePropagationData\":\"**************.0Z\",\"uSNCreated\":\"825887\",\"objectCategory\":\"CN=Organizational-Unit,CN=Schema,CN=Configuration,DC=digital,DC=com\"},{\"whenCreated\":\"**************.0Z\",\"uSNChanged\":\"827643\",\"ou\":\"AD210-Proxy\",\"instanceType\":\"4\",\"objectClass\":\"top\",\"distinguishedName\":\"OU=AD210-Proxy,OU=vic_sso1,DC=digital,DC=com\",\"dn\":\"OU=AD210-Proxy,OU=vic_sso1,DC=digital,DC=com\",\"whenChanged\":\"**************.0Z\",\"parentObjectGUID\":\"8dcbb744-**************-7db4a0f830c0\",\"objectGUID\":\"2f0f3557-15ce-4d03-871c-27d1617aae8b\",\"name\":\"AD210-Proxy\",\"dSCorePropagationData\":\"**************.0Z\",\"uSNCreated\":\"827643\",\"objectCategory\":\"CN=Organizational-Unit,CN=Schema,CN=Configuration,DC=digital,DC=com\"}],\"user\":[{\"lastLogoff\":\"0\",\"logonCount\":\"0\",\"accountExpires\":\"9223372036854775807\",\"displayName\":\"vicuser1\",\"distinguishedName\":\"CN=vicuser1,OU=vic_sso1,DC=digital,DC=com\",\"dn\":\"CN=vicuser1,OU=vic_sso1,DC=digital,DC=com\",\"countryCode\":\"0\",\"objectGUID\":\"8fb3b990-2dd1-43d7-8192-ab22346fe7a9\",\"dSCorePropagationData\":\"**************.0Z\",\"uSNCreated\":\"346426\",\"codePage\":\"0\",\"userAccountControl\":\"512\",\"userPrincipalName\":\"<EMAIL>\",\"lastLogon\":\"0\",\"whenCreated\":\"**************.0Z\",\"badPasswordTime\":\"0\",\"uSNChanged\":\"827513\",\"sAMAccountName\":\"vicuser1\",\"givenName\":\"vicuser1\",\"instanceType\":\"4\",\"objectClass\":\"top\",\"cn\":\"vicuser1\",\"department_ids\":[\"8dcbb744-**************-7db4a0f830c0\"],\"whenChanged\":\"**************.0Z\",\"employeeType\":\"foreign engineer\",\"primaryGroupID\":\"513\",\"sAMAccountType\":\"*********\",\"name\":\"vicuser1\",\"objectSid\":\"\\u0001\\u0005\\u0000\\u0000\\u0000\\u0000\\u0000\\u0005\\u0015\\u0000\\u0000\\u0000\\u000E???\\u0019ʜԋ\\u001E??\\u0013b\\u0000\\u0000\",\"badPwdCount\":\"0\",\"pwdLastSet\":\"133812467526747472\",\"objectCategory\":\"CN=Person,CN=Schema,CN=Configuration,DC=digital,DC=com\"},{\"lastLogoff\":\"0\",\"logonCount\":\"0\",\"accountExpires\":\"9223372036854775807\",\"displayName\":\"ad210e\",\"distinguishedName\":\"CN=ad210e,OU=vic_sso1,DC=digital,DC=com\",\"dn\":\"CN=ad210e,OU=vic_sso1,DC=digital,DC=com\",\"countryCode\":\"0\",\"objectGUID\":\"cfde6c58-38e3-4d12-a6e4-09eecdf81b2d\",\"dSCorePropagationData\":\"**************.0Z\",\"uSNCreated\":\"692632\",\"sn\":\"ad210e\",\"codePage\":\"0\",\"userAccountControl\":\"512\",\"userPrincipalName\":\"<EMAIL>\",\"lastLogon\":\"0\",\"whenCreated\":\"**************.0Z\",\"badPasswordTime\":\"0\",\"uSNChanged\":\"827511\",\"sAMAccountName\":\"ad210e\",\"instanceType\":\"4\",\"objectClass\":\"top\",\"cn\":\"ad210e\",\"department_ids\":[\"8dcbb744-**************-7db4a0f830c0\"],\"whenChanged\":\"**************.0Z\",\"employeeType\":\"正式外部员工\",\"primaryGroupID\":\"513\",\"sAMAccountType\":\"*********\",\"name\":\"ad210e\",\"objectSid\":\"\\u0001\\u0005\\u0000\\u0000\\u0000\\u0000\\u0000\\u0005\\u0015\\u0000\\u0000\\u0000\\u000E???\\u0019ʜԋ\\u001E???}\\u0000\\u0000\",\"badPwdCount\":\"0\",\"pwdLastSet\":\"133812994176001046\",\"objectCategory\":\"CN=Person,CN=Schema,CN=Configuration,DC=digital,DC=com\"},{\"lastLogoff\":\"0\",\"logonCount\":\"0\",\"accountExpires\":\"9223372036854775807\",\"displayName\":\"ad210f\",\"distinguishedName\":\"CN=ad210f,OU=vic_sso1,DC=digital,DC=com\",\"dn\":\"CN=ad210f,OU=vic_sso1,DC=digital,DC=com\",\"countryCode\":\"0\",\"objectGUID\":\"0285f49a-0e9f-4f27-a45d-2b3d0cd1226d\",\"dSCorePropagationData\":\"**************.0Z\",\"uSNCreated\":\"692639\",\"sn\":\"ad210f\",\"codePage\":\"0\",\"userAccountControl\":\"512\",\"userPrincipalName\":\"<EMAIL>\",\"lastLogon\":\"0\",\"whenCreated\":\"**************.0Z\",\"badPasswordTime\":\"0\",\"uSNChanged\":\"692644\",\"sAMAccountName\":\"ad210f\",\"instanceType\":\"4\",\"objectClass\":\"top\",\"cn\":\"ad210f\",\"department_ids\":[\"8dcbb744-**************-7db4a0f830c0\"],\"whenChanged\":\"**************.0Z\",\"primaryGroupID\":\"513\",\"sAMAccountType\":\"*********\",\"name\":\"ad210f\",\"objectSid\":\"\\u0001\\u0005\\u0000\\u0000\\u0000\\u0000\\u0000\\u0005\\u0015\\u0000\\u0000\\u0000\\u000E???\\u0019ʜԋ\\u001E???}\\u0000\\u0000\",\"badPwdCount\":\"0\",\"pwdLastSet\":\"133812994424282870\",\"objectCategory\":\"CN=Person,CN=Schema,CN=Configuration,DC=digital,DC=com\"}]},\"index\":1,\"type\":\"INNER\"}', 'SUCCESS', 'OK', '2025-02-07 16:24:17', '2025-02-07 16:24:32', '2025-02-07 15:47:25', 'admin', '2025-02-07 17:42:38', 'admin', 'iam', 36, 0, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1887770556405379074, 'ActionNode', 1887798166594252801, 84, 'loop_ext_org', NULL, 1887751465322188801, NULL, '{\"ui_id\":\"1887770583566004227\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1887770583566004228\",\"name\":\"ext_org\",\"type\":\"OBJECT\",\"description\":\"支持一次集成单个或
多个外部部门\",\"display_name\":\"外部部门\",\"multi_valued\":true,\"value\":\"{{N1887770122504630274.current.dept}}\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1887770583566004229\",\"name\":\"run_type\",\"type\":\"STRING\",\"description\":\"并行：子流程按多线程并发执
行；串行：子流程按单线程顺序执行\",\"display_name\":\"执行方式\",\"multi_valued\":false,\"value\":\"并行\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"并行\",\"串行\"],\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, '{\"current\":{\"whenCreated\":\"20250117102934.0Z\",\"uSNChanged\":\"825887\",\"ou\":\"子部门\",\"instanceType\":\"4\",\"objectClass\":\"top\",\"distinguishedName\":\"OU=子部门,OU=vic_sso1,DC=digital,DC=com\",\"dn\":\"OU=子部门,OU=vic_sso1,DC=digital,DC=com\",\"whenChanged\":\"20250117102934.0Z\",\"parentObjectGUID\":\"8dcbb744-**************-7db4a0f830c0\",\"objectGUID\":\"bddc54ad-b007-465a-87c0-43413ad2374b\",\"name\":\"子部门\",\"dSCorePropagationData\":\"**************.0Z\",\"uSNCreated\":\"825887\",\"objectCategory\":\"CN=Organizational-Unit,CN=Schema,CN=Configuration,DC=digital,DC=com\"},\"index\":0,\"type\":\"INNER\"}', 'SUCCESS', 'OK', '2025-02-07 16:24:29', '2025-02-07 16:24:32', '2025-02-07 15:49:08', 'admin', '2025-02-07 16:24:32', 'admin', 'iam', 31, 1887770122504630274, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1887770723607113729, 'ActionNode', NULL, 85, 'sync_update_org', NULL, 1887751465322188801, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1887770743645810698\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"ADAPTOR\",\"ui_id\":\"1887770743645810699\",\"name\":\"ext_org\",\"description\":\"待集成的外部部门对象（json）信息\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"外部部门对象\",\"value\":\"{{N1887770556405379074.current}}\",\"required\":true,\"page_control\":\"TEXT\"}],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', NULL, '{\"code\":\"0\",\"data\":[{\"external_id\":\"bddc54ad-b007-465a-87c0-43413ad2374b\",\"flag\":\"CREATE\",\"local\":{\"manager\":\"\",\"name\":\"子部门\",\"description\":\"子部门\",\"connector_parent_org_id\":\"8dcbb744-**************-7db4a0f830c0\",\"id\":\"1887779463266074626\",\"connector_org_id\":\"bddc54ad-b007-465a-87c0-43413ad2374b\"},\"message\":\"\"}],\"message\":\"SUCCESS\"}', 'SUCCESS', 'OK', '2025-02-07 16:24:29', '2025-02-07 16:24:32', '2025-02-07 15:49:48', 'admin', '2025-02-07 16:24:32', 'admin', 'iam', 31, 1887770556405379074, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1887798166594252801, 'ActionNode', NULL, 87, 'loop_ext_user', NULL, 1887751465322188801, NULL, '{\"ui_id\":\"1887798201824718854\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1887798201824718855\",\"name\":\"ext_user\",\"type\":\"OBJECT\",\"description\":\"支持一次读入单个或多个外部用户>（json）对象\",\"display_name\":\"外部用户\",\"multi_valued\":true,\"value\":\"{{N1887770122504630274.current.user}}\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1887798201824718856\",\"name\":\"run_type\",\"type\":\"STRING\",\"description\":\"并行：子流程按多线程并发执
行；串行：子流程按单线程顺序执行\",\"display_name\":\"执行方式\",\"multi_valued\":false,\"value\":\"并行\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"并行\",\"串行\"],\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, '{\"current\":{\"lastLogoff\":\"0\",\"logonCount\":\"0\",\"accountExpires\":\"9223372036854775807\",\"displayName\":\"vicuser1\",\"distinguishedName\":\"CN=vicuser1,OU=vic_sso1,DC=digital,DC=com\",\"dn\":\"CN=vicuser1,OU=vic_sso1,DC=digital,DC=com\",\"countryCode\":\"0\",\"objectGUID\":\"8fb3b990-2dd1-43d7-8192-ab22346fe7a9\",\"dSCorePropagationData\":\"**************.0Z\",\"uSNCreated\":\"346426\",\"codePage\":\"0\",\"userAccountControl\":\"512\",\"userPrincipalName\":\"<EMAIL>\",\"lastLogon\":\"0\",\"whenCreated\":\"**************.0Z\",\"badPasswordTime\":\"0\",\"uSNChanged\":\"827513\",\"sAMAccountName\":\"vicuser1\",\"givenName\":\"vicuser1\",\"instanceType\":\"4\",\"objectClass\":\"top\",\"cn\":\"vicuser1\",\"department_ids\":[\"8dcbb744-**************-7db4a0f830c0\"],\"whenChanged\":\"**************.0Z\",\"employeeType\":\"foreign engineer\",\"primaryGroupID\":\"513\",\"sAMAccountType\":\"*********\",\"name\":\"vicuser1\",\"objectSid\":\"\\u0001\\u0005\\u0000\\u0000\\u0000\\u0000\\u0000\\u0005\\u0015\\u0000\\u0000\\u0000\\u000E???\\u0019ʜԋ\\u001E??\\u0013b\\u0000\\u0000\",\"badPwdCount\":\"0\",\"pwdLastSet\":\"133812467526747472\",\"objectCategory\":\"CN=Person,CN=Schema,CN=Configuration,DC=digital,DC=com\"},\"index\":0,\"type\":\"INNER\"}', 'SUCCESS', 'OK', '2025-02-07 17:39:27', '2025-02-07 17:39:27', '2025-02-07 17:38:51', 'admin', '2025-02-07 17:39:29', 'admin', 'iam', 31, 1887770122504630274, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1887798348803207170, 'ActionNode', NULL, 88, 'sync_update_user', NULL, 1887751465322188801, NULL, '{\"ui_id\":\"1887798373245923341\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1887798373245923342\",\"name\":\"ext_user\",\"type\":\"OBJECT\",\"description\":\"待集成的外部用户对象（json）
信息\",\"display_name\":\"外部用户对象\",\"multi_valued\":false,\"value\":\"{{N1887798166594252801.current}}\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, '{\"code\":\"0\",\"data\":[{\"external_id\":\"8fb3b990-2dd1-43d7-8192-ab22346fe7a9\",\"flag\":\"CREATE\",\"local\":{\"sub\":\"1887798782509256706\",\"connector_org_ids\":[\"8dcbb744-**************-7db4a0f830c0\"],\"manager\":\"\",\"name\":\"vicuser1\",\"nickname\":\"vicuser1\",\"username\":\"vicuser1\"},\"message\":\"\"}],\"message\":\"SUCCESS\"}', 'SUCCESS', 'OK', '2025-02-07 17:41:17', '2025-02-07 17:41:18', '2025-02-07 17:39:35', 'admin', '2025-02-07 17:41:22', 'admin', 'iam', 31, 1887798166594252801, NULL, '0', NULL);

-- LDAP通讯录集成模版
INSERT INTO `acm`.`acm_flow_instance` (`id`, `name`, `description`, `flow_type`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `op_status`, `api_key`, `category`, `template`, `is_deleted`) VALUES (1887769664310771714, 'LDAP通讯录集成', 'LDAP通讯录集成模版', 1, 'IDLE', NULL, NULL, NULL, '2025-02-07 15:45:36', 'anonymousUser', '2025-02-07 18:08:29', 'admin', 'iam', 'ONLINE', '8a0a2e39baca48b4966d9e8e5d477631', '通讯录集成', 1, 0);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1887769666871504898, 'StartEventNode', 1887769666871504903, 81, 'user_sync', NULL, 1887769664310771714, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1851605414348050433\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1851605414348050434\",\"option_values\":[\"异步\",\"同步\"],\"description\":\"异步：
webhook接收到数据后，将直接返回接收成功的结果以及流的信息。\\n同步：Webhook接收到数据后，将等待连接流执行完成并将最后的执行结果返回给调用方（最长等待时间为10s，超时将返回为空）。请查看接口文档获得更多详细信息\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"连接流执行方式\",\"required\":true,\"name\":\"type\",\"mutability\":\"readWrite\",\"value\":\"异步\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1851605414348050435\",\"name\":\"value.org_total\",\"description\":\"预估总的外部部门个数，用于估算集成进度\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"预估部门个数\",\"value\":\"300\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1851605414348050436\",\"name\":\"value.user_total\",\"description\":\"预估总的外部用户个数，用于估算集成进度\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"预估用户个数\",\"value\":\"1000\",\"required\":true,\"page_control\":\"TEXT\"}],\"name\":\"root\",\"description\":\"启动通讯录集成\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"启动通讯录集成\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', NULL, '{\"hook_url\":null,\"sync\":false,\"type\":\"异步\",\"value\":{\"org_total\":300,\"user_total\":1000}}', 'SUCCESS', 'OK', '2025-02-07 17:43:44', '2025-02-07 17:43:44', '2025-02-07 15:45:36', 'anonymousUser', '2025-02-07 17:43:46', 'admin', 'iam', 31, 0, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1887769666871504900, 'EndNode', NULL, NULL, NULL, NULL, 1887769664310771714, NULL, NULL, NULL, NULL, 'IDLE', NULL, NULL, NULL, '2025-02-07 15:45:36', 'anonymousUser', '2025-02-07 15:45:36', 'anonymousUser', 'iam', NULL, 0, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1887769666871504901, 'ActionNode', 1887769666871504900, 89, 'complete_user_sync', NULL, 1887769664310771714, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1851607623072731137\",\"sub_params\":[],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', NULL, '{\"code\":\"0\",\"data\":null,\"message\":\"SUCCESS\"}', 'INVALID', 'OK', '2024-12-09 13:52:38', '2024-12-09 13:52:38', '2025-02-07 15:45:36', 'anonymousUser', '2025-02-07 15:45:36', 'anonymousUser', 'iam', 31, 0, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1887769666871504903, 'ActionNode', 1887800063673757697, 82, 'record_ext_root_org', NULL, 1887769664310771714, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1865972729377787914\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"ADAPTOR\",\"ui_id\":\"1865972729377787915\",\"name\":\"root_id\",\"description\":\"外部部门的根部门ID\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"外部根部门ID\",\"value\":\"8dcbb744-**************-7db4a0f830c0\",\"required\":true,\"page_control\":\"TEXT\"}],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', NULL, '{\"code\":\"0\",\"data\":{\"external_id\":\"8dcbb744-**************-7db4a0f830c0\",\"flag\":\"UPDATE\",\"local\":\"1887769664478543873\",\"message\":\"\"},\"message\":\"SUCCESS\"}', 'SUCCESS', 'OK', '2025-02-07 17:45:25', '2025-02-07 17:45:25', '2025-02-07 15:45:36', 'anonymousUser', '2025-02-07 17:46:11', 'admin', 'iam', 31, 0, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1887800063673757697, 'ActionNode', 1887800504583188482, 120, 'search-all-dept', 1882388529564815362, 1887769664310771714, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1887800106625937418\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1887800106625937419\",\"name\":\"base_dn\",\"description\":\"搜索>根节点的BaseDN\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"BaseDN\",\"value\":\"OU=vic_sso1,DC=digital,DC=com\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1887800106625937420\",\"name\":\"dept_filter\",\"description\":\"部门搜索条件\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"部门搜索条件\",\"value\":\"(objectclass=organizationalUnit)\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1887800106625937421\",\"name\":\"dept_id_name\",\"description\":\"作为部门ID的属性名，一般用entryUUID\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"部门ID属性名\",\"value\":\"entryUUID\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1887800106625937422\",\"name\":\"dept_pid_name\",\"description\":\"作为部门父ID的属性名，一般用parentEntryUUID\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"部门父ID属性名\",\"value\":\"parentEntryUUID\",\"required\":true,\"page_control\":\"TEXT\"}],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', NULL, '{\n  \"current\": {\n    \"dept\": [\n      {\n        \"whenCreated\": \"20250117102934.0Z\",\n        \"uSNChanged\": \"825887\",\n        \"ou\": \"子部门\",\n        \"instanceType\": \"4\",\n        \"objectClass\": \"top\",\n        \"distinguishedName\": \"OU=子部门,OU=vic_sso1,DC=digital,DC=com\",\n        \"dn\": \"OU=子部门,OU=vic_sso1,DC=digital,DC=com\",\n        \"whenChanged\": \"20250117102934.0Z\",\n        \"parentObjectGUID\": \"8dcbb744-**************-7db4a0f830c0\",\n        \"objectGUID\": \"bddc54ad-b007-465a-87c0-43413ad2374b\",\n        \"name\": \"子部门\",\n        \"dSCorePropagationData\": \"**************.0Z\",\n        \"uSNCreated\": \"825887\",\n        \"objectCategory\": \"CN=Organizational-Unit,CN=Schema,CN=Configuration,DC=digital,DC=com\"\n      },\n      {\n        \"whenCreated\": \"**************.0Z\",\n        \"uSNChanged\": \"827643\",\n        \"ou\": \"AD210-Proxy\",\n        \"instanceType\": \"4\",\n        \"objectClass\": \"top\",\n        \"distinguishedName\": \"OU=AD210-Proxy,OU=vic_sso1,DC=digital,DC=com\",\n        \"dn\": \"OU=AD210-Proxy,OU=vic_sso1,DC=digital,DC=com\",\n        \"whenChanged\": \"**************.0Z\",\n        \"parentObjectGUID\": \"8dcbb744-**************-7db4a0f830c0\",\n        \"objectGUID\": \"2f0f3557-15ce-4d03-871c-27d1617aae8b\",\n        \"name\": \"AD210-Proxy\",\n        \"dSCorePropagationData\": \"**************.0Z\",\n        \"uSNCreated\": \"827643\",\n        \"objectCategory\": \"CN=Organizational-Unit,CN=Schema,CN=Configuration,DC=digital,DC=com\"\n      }\n    ]\n  },\n  \"index\": 1,\n  \"type\": \"INNER\"\n}', 'IDLE', NULL, NULL, NULL, '2025-02-07 17:46:23', 'admin', '2025-02-07 17:55:25', 'admin', 'iam', 35, 0, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1887800504583188482, 'ActionNode', 1887769666871504901, 121, 'search-all-user', 1882388529564815362, 1887769664310771714, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1887800528761663504\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1887800528761663505\",\"name\":\"base_dn\",\"description\":\"搜索>根节点的BaseDN\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"BaseDN\",\"value\":\"OU=vic_sso1,DC=digital,DC=com\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1887800528761663506\",\"name\":\"user_filter\",\"description\":\"用户搜索条件\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"用户搜索条件\",\"value\":\"(objectclass=organizationalPerson)\",\"required\":true,\"page_control\":\"TEXT\"}],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', NULL, '{\n  \"current\": {\n    \"user\": [\n      {\n        \"lastLogoff\": \"0\",\n        \"logonCount\": \"0\",\n        \"accountExpires\": \"9223372036854775807\",\n        \"displayName\": \"vicuser1\",\n        \"distinguishedName\": \"CN=vicuser1,OU=vic_sso1,DC=digital,DC=com\",\n        \"dn\": \"CN=vicuser1,OU=vic_sso1,DC=digital,DC=com\",\n        \"countryCode\": \"0\",\n        \"objectGUID\": \"8fb3b990-2dd1-43d7-8192-ab22346fe7a9\",\n        \"dSCorePropagationData\": \"**************.0Z\",\n        \"uSNCreated\": \"346426\",\n        \"codePage\": \"0\",\n        \"userAccountControl\": \"512\",\n        \"userPrincipalName\": \"<EMAIL>\",\n        \"lastLogon\": \"0\",\n        \"whenCreated\": \"**************.0Z\",\n        \"badPasswordTime\": \"0\",\n        \"uSNChanged\": \"827513\",\n        \"sAMAccountName\": \"vicuser1\",\n        \"givenName\": \"vicuser1\",\n        \"instanceType\": \"4\",\n        \"objectClass\": \"top\",\n        \"cn\": \"vicuser1\",\n        \"department_ids\": [\n          \"8dcbb744-**************-7db4a0f830c0\"\n        ],\n        \"whenChanged\": \"**************.0Z\",\n        \"employeeType\": \"foreign engineer\",\n        \"primaryGroupID\": \"513\",\n        \"sAMAccountType\": \"*********\",\n        \"name\": \"vicuser1\",\n        \"objectSid\": \"\\u0001\\u0005\\u0000\\u0000\\u0000\\u0000\\u0000\\u0005\\u0015\\u0000\\u0000\\u0000\\u000e???\\u0019ʜԋ\\u001e??\\u0013b\\u0000\\u0000\",\n        \"badPwdCount\": \"0\",\n        \"pwdLastSet\": \"133812467526747472\",\n        \"objectCategory\": \"CN=Person,CN=Schema,CN=Configuration,DC=digital,DC=com\"\n      },\n      {\n        \"lastLogoff\": \"0\",\n        \"logonCount\": \"0\",\n        \"accountExpires\": \"9223372036854775807\",\n        \"displayName\": \"ad210e\",\n        \"distinguishedName\": \"CN=ad210e,OU=vic_sso1,DC=digital,DC=com\",\n        \"dn\": \"CN=ad210e,OU=vic_sso1,DC=digital,DC=com\",\n        \"countryCode\": \"0\",\n        \"objectGUID\": \"cfde6c58-38e3-4d12-a6e4-09eecdf81b2d\",\n        \"dSCorePropagationData\": \"**************.0Z\",\n        \"uSNCreated\": \"692632\",\n        \"sn\": \"ad210e\",\n        \"codePage\": \"0\",\n        \"userAccountControl\": \"512\",\n        \"userPrincipalName\": \"<EMAIL>\",\n        \"lastLogon\": \"0\",\n        \"whenCreated\": \"**************.0Z\",\n        \"badPasswordTime\": \"0\",\n        \"uSNChanged\": \"827511\",\n        \"sAMAccountName\": \"ad210e\",\n        \"instanceType\": \"4\",\n        \"objectClass\": \"top\",\n        \"cn\": \"ad210e\",\n        \"department_ids\": [\n          \"8dcbb744-**************-7db4a0f830c0\"\n        ],\n        \"whenChanged\": \"**************.0Z\",\n        \"employeeType\": \"正式外部员工\",\n        \"primaryGroupID\": \"513\",\n        \"sAMAccountType\": \"*********\",\n        \"name\": \"ad210e\",\n        \"objectSid\": \"\\u0001\\u0005\\u0000\\u0000\\u0000\\u0000\\u0000\\u0005\\u0015\\u0000\\u0000\\u0000\\u000e???\\u0019ʜԋ\\u001e???}\\u0000\\u0000\",\n        \"badPwdCount\": \"0\",\n        \"pwdLastSet\": \"133812994176001046\",\n        \"objectCategory\": \"CN=Person,CN=Schema,CN=Configuration,DC=digital,DC=com\"\n      },\n      {\n        \"lastLogoff\": \"0\",\n        \"logonCount\": \"0\",\n        \"accountExpires\": \"9223372036854775807\",\n        \"displayName\": \"ad210f\",\n        \"distinguishedName\": \"CN=ad210f,OU=vic_sso1,DC=digital,DC=com\",\n        \"dn\": \"CN=ad210f,OU=vic_sso1,DC=digital,DC=com\",\n        \"countryCode\": \"0\",\n        \"objectGUID\": \"0285f49a-0e9f-4f27-a45d-2b3d0cd1226d\",\n        \"dSCorePropagationData\": \"**************.0Z\",\n        \"uSNCreated\": \"692639\",\n        \"sn\": \"ad210f\",\n        \"codePage\": \"0\",\n        \"userAccountControl\": \"512\",\n        \"userPrincipalName\": \"<EMAIL>\",\n        \"lastLogon\": \"0\",\n        \"whenCreated\": \"**************.0Z\",\n        \"badPasswordTime\": \"0\",\n        \"uSNChanged\": \"692644\",\n        \"sAMAccountName\": \"ad210f\",\n        \"instanceType\": \"4\",\n        \"objectClass\": \"top\",\n        \"cn\": \"ad210f\",\n        \"department_ids\": [\n          \"8dcbb744-**************-7db4a0f830c0\"\n        ],\n        \"whenChanged\": \"**************.0Z\",\n        \"primaryGroupID\": \"513\",\n        \"sAMAccountType\": \"*********\",\n        \"name\": \"ad210f\",\n        \"objectSid\": \"\\u0001\\u0005\\u0000\\u0000\\u0000\\u0000\\u0000\\u0005\\u0015\\u0000\\u0000\\u0000\\u000e???\\u0019ʜԋ\\u001e???}\\u0000\\u0000\",\n        \"badPwdCount\": \"0\",\n        \"pwdLastSet\": \"133812994424282870\",\n        \"objectCategory\": \"CN=Person,CN=Schema,CN=Configuration,DC=digital,DC=com\"\n      }\n    ]\n  },\n  \"index\": 1,\n  \"type\": \"INNER\"\n}', 'IDLE', NULL, NULL, NULL, '2025-02-07 17:48:09', 'admin', '2025-02-07 17:59:02', 'admin', 'iam', 35, 0, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1887801014493114370, 'ActionNode', NULL, 84, 'loop_ext_org', NULL, 1887769664310771714, NULL, '{\"ui_id\":\"1887801118157844482\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1887801118157844483\",\"name\":\"ext_org\",\"type\":\"OBJECT\",\"description\":\"支持一次集成单个或多个外部部门\",\"display_name\":\"外部部门\",\"multi_valued\":true,\"value\":\"{{N1887800063673757697.current.dept}}\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1887801118157844484\",\"name\":\"run_type\",\"type\":\"STRING\",\"description\":\"并行：子流程按多线程并发执行；串行：子流>程按单线程顺序执行\",\"display_name\":\"执行方式\",\"multi_valued\":false,\"value\":\"并行\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"并行\",\"串行\"],\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, '{\"current\":{\"whenCreated\":\"20250117102934.0Z\",\"uSNChanged\":\"825887\",\"ou\":\"子部门\",\"instanceType\":\"4\",\"objectClass\":\"top\",\"distinguishedName\":\"OU=子部门,OU=vic_sso1,DC=digital,DC=com\",\"dn\":\"OU=子部门,OU=vic_sso1,DC=digital,DC=com\",\"whenChanged\":\"20250117102934.0Z\",\"parentObjectGUID\":\"8dcbb744-**************-7db4a0f830c0\",\"objectGUID\":\"bddc54ad-b007-465a-87c0-43413ad2374b\",\"name\":\"子部门\",\"dSCorePropagationData\":\"**************.0Z\",\"uSNCreated\":\"825887\",\"objectCategory\":\"CN=Organizational-Unit,CN=Schema,CN=Configuration,DC=digital,DC=com\"},\"index\":0,\"type\":\"INNER\"}', 'SUCCESS', 'OK', '2025-02-07 17:55:46', '2025-02-07 17:55:46', '2025-02-07 17:50:10', 'admin', '2025-02-07 17:55:48', 'admin', 'iam', 31, 1887800063673757697, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1887802504565747714, 'ActionNode', NULL, 85, 'sync_update_org', NULL, 1887769664310771714, NULL, '{\"ui_id\":\"1887802531495686154\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1887802531495686155\",\"name\":\"ext_org\",\"type\":\"OBJECT\",\"description\":\"待集成的外部部门对象（json）信
息\",\"display_name\":\"外部部门对象\",\"multi_valued\":false,\"value\":\"{{N1887801014493114370.current}}\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, '{\"code\":\"1\",\"data\":null,\"message\":null}', 'FAILED', 'OK', '2025-02-07 17:56:25', '2025-02-07 17:56:25', '2025-02-07 17:56:05', 'admin', '2025-02-07 17:56:27', 'admin', 'iam', 31, 1887801014493114370, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1887802887753166850, 'ActionNode', NULL, 87, 'loop_ext_user', NULL, 1887769664310771714, NULL, '{\"ui_id\":\"1887802912338489349\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1887802912338489350\",\"name\":\"ext_user\",\"type\":\"OBJECT\",\"description\":\"支持一次读入单个或多个外部用户>（json）对象\",\"display_name\":\"外部用户\",\"multi_valued\":true,\"value\":\"{{N1887800504583188482.current.user}}\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1887802912338489351\",\"name\":\"run_type\",\"type\":\"STRING\",\"description\":\"并行：子流程按多线程并发执
行；串行：子流程按单线程顺序执行\",\"display_name\":\"执行方式\",\"multi_valued\":false,\"value\":\"并行\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"并行\",\"串行\"],\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, '{\"current\":{\"lastLogoff\":\"0\",\"logonCount\":\"0\",\"accountExpires\":\"9223372036854775807\",\"displayName\":\"vicuser1\",\"distinguishedName\":\"CN=vicuser1,OU=vic_sso1,DC=digital,DC=com\",\"dn\":\"CN=vicuser1,OU=vic_sso1,DC=digital,DC=com\",\"countryCode\":\"0\",\"objectGUID\":\"8fb3b990-2dd1-43d7-8192-ab22346fe7a9\",\"dSCorePropagationData\":\"**************.0Z\",\"uSNCreated\":\"346426\",\"codePage\":\"0\",\"userAccountControl\":\"512\",\"userPrincipalName\":\"<EMAIL>\",\"lastLogon\":\"0\",\"whenCreated\":\"**************.0Z\",\"badPasswordTime\":\"0\",\"uSNChanged\":\"827513\",\"sAMAccountName\":\"vicuser1\",\"givenName\":\"vicuser1\",\"instanceType\":\"4\",\"objectClass\":\"top\",\"cn\":\"vicuser1\",\"department_ids\":[\"8dcbb744-**************-7db4a0f830c0\"],\"whenChanged\":\"**************.0Z\",\"employeeType\":\"foreign engineer\",\"primaryGroupID\":\"513\",\"sAMAccountType\":\"*********\",\"name\":\"vicuser1\",\"objectSid\":\"\\u0001\\u0005\\u0000\\u0000\\u0000\\u0000\\u0000\\u0005\\u0015\\u0000\\u0000\\u0000\\u000E???\\u0019ʜԋ\\u001E??\\u0013b\\u0000\\u0000\",\"badPwdCount\":\"0\",\"pwdLastSet\":\"133812467526747472\",\"objectCategory\":\"CN=Person,CN=Schema,CN=Configuration,DC=digital,DC=com\"},\"index\":0,\"type\":\"INNER\"}', 'SUCCESS', 'OK', '2025-02-07 17:57:57', '2025-02-07 17:57:57', '2025-02-07 17:57:37', 'admin', '2025-02-07 17:57:59', 'admin', 'iam', 31, 1887800504583188482, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1887802995953627138, 'ActionNode', NULL, 88, 'sync_update_user', NULL, 1887769664310771714, NULL, '{\"ui_id\":\"1887803015493201932\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1887803015493201933\",\"name\":\"ext_user\",\"type\":\"OBJECT\",\"description\":\"待集成的外部用户对象（json）
信息\",\"display_name\":\"外部用户对象\",\"multi_valued\":false,\"value\":\"{{N1887802887753166850.current}}\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, NULL, 'INVALID', NULL, NULL, NULL, '2025-02-07 17:58:03', 'admin', '2025-02-07 17:58:17', 'admin', 'iam', 31, 1887802887753166850, NULL, '0', NULL);


-- 企企通通用动作
INSERT IGNORE INTO acm.acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, action_type, status, config_schema, create_time, create_by, update_time, update_by, tenant_id, sample_output, node_struct) VALUES (123, '1881962722709708801', 0, '通用请求', '', 16, 22, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619266\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619267\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"***********32619268\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"header\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619269\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Query参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619270\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Body参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body\",\"page_control\":\"TEXTAREA\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619271\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"断言该接口的响应结果。例：[{\\\"path\\\":\\\"响应参数名\\\",\\\"op\\\":\\\"运算符\\\",\\\"value\\\":\\\"节点响应结果\\\"},\\\"path\\\":\\\"响应参数名2\\\",\\\"op\\\":\\\"运算符2\\\",\\\"value\\\":\\\"节点响应结果2\\\"]。；运算符示例：\\\"eq\\\"表示等于\\\"neq\\\"表示不等于；\\\"ne\\\"表示不相等；\\\"contains\\\"表示包含\",\"display_name\":\"预期响应\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"successCondition\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"***********32619123\",\"update\":true,\"value\":\"[{\\\"path\\\":\\\"status_code\\\",\\\"op\\\":\\\"eq\\\",\\\"value\\\":\\\"200\\\"},{\\\"path\\\":\\\"body.errcode\\\",\\\"op\\\":\\\"eq\\\",\\\"value\\\":\\\"0\\\"}]\",\"value_type\":\"JS_EXP\"}],\"type\":\"OBJECT\",\"ui_id\":\"1881962888162418690\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2025-01-22 15:10:53', 'admin', '2025-01-22 15:12:19', 'admin', 'iam', NULL, 'SINGLE');
