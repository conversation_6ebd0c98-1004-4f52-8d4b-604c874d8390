-- 钉钉考勤记录同步到mysql
INSERT INTO acm.acm_flow_instance (id, name, description, flow_type, status, op_status, error_msg, run_start_time, run_end_time, api_key, category, create_time, create_by, update_time, update_by, tenant_id) VALUES (1820705947778134017, '钉钉考勤记录同步到mysql', null, 2, 'IDLE', 'ONLINE', null, null, null, 'edbc32aad9fc48d6ae140824202f579a', '人事', '2024-08-06 14:18:20', 'admin', '2024-08-08 16:52:08', 'admin', 'iam');
INSERT INTO acm.acm_flow_node (id, node_type, parent_id, next_id, connector_id, action_id, account_id, flow_id, description, config, input, output, status, error_msg, run_start_time, run_end_time, proxy_id, create_time, create_by, update_time, update_by, tenant_id, closed, note) VALUES (1820705947803299842, 'StartEventNode', 0, 1820710510673375233, 2, 2, null, 1820705947778134017, null, '{"multi_valued":false,"value_type":"JS_EXP","ui_id":"1820710324214296577","sub_params":[{"multi_valued":false,"value_type":"FIX_VALUE","ui_id":"1820710324214296578","name":"startDate","description":"定时执行生效的开始日期","update":true,"mutability":"readWrite","type":"STRING","display_name":"生效开始日期","value":"2024-08-06","required":true,"page_control":"DATE"},{"multi_valued":false,"value_type":"FIX_VALUE","ui_id":"1820710324214296579","name":"endDate","description":"定时执行生效的结束日期","update":true,"mutability":"readWrite","type":"STRING","display_name":"生效结束日期","value":"2033-08-06","required":true,"page_control":"DATE"},{"multi_valued":false,"value_type":"FIX_VALUE","ui_id":"1820710324214296580","name":"cycle","description":"cron表达式：秒 分 时 天 月 周","update":true,"mutability":"readWrite","type":"STRING","display_name":"crontab表达式","value":"0 0 12 * * *","required":true,"page_control":"TEXT"}],"name":"root","description":"配置crontab，自定义触发时间","update":true,"mutability":"readWrite","type":"OBJECT","display_name":"自定义执行","required":true,"page_control":"FIXED_OBJ"}', '{"cycle":"0 0 12 * * *","end_date":"2033-08-06","start_date":"2024-08-06"}', '{"cycle":"0 0 12 * * *","end_date":"2033-08-06","start_date":"2024-08-06"}', 'SUCCESS', 'OK', '2024-08-08 16:52:08', '2024-08-08 16:52:08', null, '2024-08-06 14:18:20', 'admin', '2024-08-08 16:52:08', 'admin', 'iam', '0', null);
INSERT INTO acm.acm_flow_node (id, node_type, parent_id, next_id, connector_id, action_id, account_id, flow_id, description, config, input, output, status, error_msg, run_start_time, run_end_time, proxy_id, create_time, create_by, update_time, update_by, tenant_id, closed, note) VALUES (1820705947836854273, 'ActionNode', 0, 1820714201367293954, 1698661994879635457, 1698667943866519553, 1820710311066447874, 1820705947778134017, null, '{"multi_valued":false,"value_type":"JS_EXP","ui_id":"1704052032452767746","sub_params":[{"multi_valued":false,"value_type":"FIX_VALUE","ui_id":"*************","option_values":[],"name":"httpMethod","update":true,"mutability":"readWrite","type":"STRING","display_name":"请求类型","value":"POST","required":true,"page_control":"HIDDEN"},{"multi_valued":false,"value_type":"FIX_VALUE","ui_id":"*************","name":"endpoint","update":true,"mutability":"readWrite","type":"STRING","display_name":"请求地址","value":"https://oapi.dingtalk.com/topapi/attendance/getupdatedata","required":true,"page_control":"TEXT"},{"multi_valued":false,"value_type":"ADAPTOR","ui_id":"1698668240049352705","name":"body.work_date","description":"查询日期","update":true,"mutability":"readWrite","type":"STRING","display_name":"查询日期","value":"{{N1820705947803299842.start_date}}","required":true,"page_control":"TEXT"},{"multi_valued":false,"value_type":"ADAPTOR","ui_id":"1698668240049352706","name":"body.userid","description":"用户的userId","update":true,"mutability":"readWrite","type":"STRING","display_name":"用户的userId","value":"{{N1820710510673375233.body.result.userid}}","required":true,"page_control":"TEXT"}],"name":"root","update":true,"mutability":"readWrite","type":"OBJECT","display_name":"root","required":false,"page_control":"FIXED_OBJ"}', '{"account":{"aes_key":null,"agent_id":"**********","app_key":"dingelozbav4e5zxaixc","app_secret":"rqp4h6EMDzifrKTJigJwExJ8ibEsbWR6C8K6VyeN8Rm4ZawPwGYEPQ1jOSopeEH2","corp_id":"dingd2a976881f424c2da1320dcb25e91351","proxy_ip":null,"real_ip":"oapi.dingtalk.com","token":null},"account_id":"1820710311066447874","auth_protocol":"DINGTALK","body":{"work_date":"2024-08-06","userid":"352559383320843011"},"content_type":null,"encoding":"UTF8","endpoint":"https://oapi.dingtalk.com/topapi/attendance/getupdatedata","header":null,"http_method":"POST","path":null,"proxy_ip":null,"query":null,"response_type":"JSON","success_condition":null}', '{"status_code":200,"header":{"Transfer-Encoding":"chunked","Server":"DingTalk/1.0.0","Cache-Control":"no-cache","Connection":"keep-alive","Date":"Tue, 06 Aug 2024 08:11:09 GMT","Application-Host":"***********","Content-Type":"application/json;charset=UTF-8"},"body":{"errcode":0,"result":{"approve_list":[],"attendance_result_list":[],"corpId":"dingd2a976881f424c2da1320dcb25e91351","work_date":"2024-08-06 00:00:00","userid":"352559383320843011","check_record_list":[]},"success":true,"errmsg":"ok","request_id":"16kcmy2r53m73"}}', 'SUCCESS', 'OK', '2024-08-06 16:11:09', '2024-08-06 16:11:09', null, '2024-08-06 14:18:20', 'admin', '2024-08-06 16:11:09', 'admin', 'iam', '0', null);
INSERT INTO acm.acm_flow_node (id, node_type, parent_id, next_id, connector_id, action_id, account_id, flow_id, description, config, input, output, status, error_msg, run_start_time, run_end_time, proxy_id, create_time, create_by, update_time, update_by, tenant_id, closed, note) VALUES (1820705947874603010, 'EndNode', 0, null, null, null, null, 1820705947778134017, null, null, null, null, 'IDLE', null, null, null, null, '2024-08-06 14:18:20', 'admin', '2024-08-06 14:18:20', 'admin', 'iam', '0', null);
INSERT INTO acm.acm_flow_node (id, node_type, parent_id, next_id, connector_id, action_id, account_id, flow_id, description, config, input, output, status, error_msg, run_start_time, run_end_time, proxy_id, create_time, create_by, update_time, update_by, tenant_id, closed, note) VALUES (1820710510673375233, 'ActionNode', 0, 1820705947836854273, 1731512369023430658, 1731522967236059137, 1820710749803229186, 1820705947778134017, null, '{"ui_id":"1731577864289017858","name":"root","type":"OBJECT","display_name":"root","multi_valued":false,"sub_params":[{"ui_id":"1699309157332619266","name":"httpMethod","type":"STRING","description":"","display_name":"请求类型","multi_valued":false,"value":"POST","update":true,"required":true,"page_control":"HIDDEN","option_values":["GET","POST","PUT","PATCH","DELETE"],"value_type":"FIX_VALUE","mutability":"readWrite"},{"ui_id":"1699309157332619267","name":"endpoint","type":"STRING","description":"请填写完整的接口地址","display_name":"请求地址","multi_valued":false,"value":"https://oapi.dingtalk.com/topapi/v2/user/get","update":true,"required":true,"page_control":"HIDDEN","value_type":"FIX_VALUE","mutability":"readWrite"},{"ui_id":"1699309157332619268","name":"contentType","type":"STRING","description":"","display_name":"请求体类型","multi_valued":false,"update":true,"required":true,"page_control":"HIDDEN","option_values":[],"value_type":"FIX_VALUE","mutability":"readonly"},{"ui_id":"1699309157332619271","name":"body","type":"OBJECT","description":"","display_name":"Body参数","multi_valued":false,"update":true,"required":false,"page_control":"HIDDEN","value_type":"JS_EXP","mutability":"readWrite"},{"ui_id":"1701675143696","name":"body.userid","type":"STRING","display_name":"Userid","multi_valued":false,"value":"352559383320843011","update":true,"required":true,"page_control":"TEXT","value_type":"ADAPTOR","mutability":"readWrite"},{"ui_id":"1699309157332619123","name":"successCondition","type":"OBJECT","description":"断言该接口的响应结果。例：[{\\"path\\":\\"响应参数名\\",\\"op\\":\\"运算符\\",\\"value\\":\\"节点响应结果\\"},\\"path\\":\\"响应参数名2\\",\\"op\\":\\"运算符2\\",\\"value\\":\\"节点响应结果2\\"]。；运算符示例：\\"eq\\"表示等于\\"neq\\"表示不等于；\\"ne\\"表示不相等；\\"contains\\"表示包含","display_name":"预期响应","multi_valued":true,"value":"[{\\"path\\":\\"status_code\\",\\"op\\":\\"eq\\",\\"value\\":\\"200\\"},{\\"path\\":\\"body.errcode\\",\\"op\\":\\"eq\\",\\"value\\":\\"0\\"}]","update":true,"required":false,"page_control":"HIDDEN","value_type":"JS_EXP","mutability":"readWrite"}],"update":true,"required":false,"page_control":"FIXED_OBJ","value_type":"JS_EXP","mutability":"readWrite"}', '{"account":{"aes_key":null,"agent_id":"**********","app_key":"dingelozbav4e5zxaixc","app_secret":"rqp4h6EMDzifrKTJigJwExJ8ibEsbWR6C8K6VyeN8Rm4ZawPwGYEPQ1jOSopeEH2","corp_id":"dingd2a976881f424c2da1320dcb25e91351","proxy_ip":null,"real_ip":"oapi.dingtalk.com","token":null},"account_id":"1820710749803229186","auth_protocol":"DINGTALK","body":{"userid":"352559383320843011"},"content_type":null,"encoding":"UTF8","endpoint":"https://oapi.dingtalk.com/topapi/v2/user/get","header":null,"http_method":"POST","path":null,"proxy_ip":null,"query":null,"response_type":"JSON","success_condition":[{"op":"eq","path":"status_code","value":"200"},{"op":"eq","path":"body.errcode","value":"0"}]}', '{"status_code":200,"header":{"Transfer-Encoding":"chunked","Server":"DingTalk/1.0.0","Cache-Control":"no-cache","Connection":"keep-alive","Date":"Tue, 06 Aug 2024 07:53:31 GMT","Application-Host":"************","Content-Type":"application/json;charset=UTF-8"},"body":{"errcode":0,"result":{"extension":"{\\"员工类型\\":\\"全职\\",\\"员工状态\\":\\"正式\\"}","boss":false,"unionid":"u2EYiiAqIdQGqMPFQIUMQrQiEiE","role_list":[{"group_name":"默认","name":"主管理员","id":**********}],"exclusive_account":false,"admin":true,"title":"","userid":"352559383320843011","work_place":"","dept_order_list":[{"dept_id":*********,"order":176200735189300512}],"real_authed":true,"dept_id_list":[*********],"email":"","leader_in_dept":[{"leader":false,"dept_id":*********}],"create_time":"2023-12-15T11:31:17.000Z","mobile":"***********","active":true,"telephone":"","avatar":"https://static-legacy.dingtalk.com/media/lQDPM53ABmH3sIPNAnDNAnCwT1b55wZ2OdQGeupYJCWeAA_624_624.jpg","hide_mobile":false,"dept_position_list":[{"dept_id":*********}],"senior":false,"name":"刘严杰","union_emp_ext":{},"state_code":"86"},"errmsg":"ok","request_id":"16ke3y919beto"}}', 'SUCCESS', 'OK', '2024-08-06 15:53:31', '2024-08-06 15:53:31', null, '2024-08-06 14:36:28', 'admin', '2024-08-06 15:53:31', 'admin', 'iam', '0', null);
INSERT INTO acm.acm_flow_node (id, node_type, parent_id, next_id, connector_id, action_id, account_id, flow_id, description, config, input, output, status, error_msg, run_start_time, run_end_time, proxy_id, create_time, create_by, update_time, update_by, tenant_id, closed, note) VALUES (1820714201367293954, 'ActionNode', 0, 1820705947874603010, 5, 7, 1820725295183933442, 1820705947778134017, null, '{"ui_id":"1820734622685589506","name":"root","type":"OBJECT","description":"自定义Sql操作数据库","display_name":"自定义Sql","multi_valued":false,"sub_params":[{"ui_id":"1820734622685589507","name":"opType","type":"STRING","description":"","display_name":"操作类型","multi_valued":false,"value":"INSERT","update":true,"required":true,"page_control":"SELECT","option_values":["INSERT","SELECT","UPDATE","DELETE"],"value_type":"FIX_VALUE","mutability":"readWrite"},{"ui_id":"1820734622685589508","name":"sql","type":"STRING","description":"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入","display_name":"Sql语句","multi_valued":false,"value":"INSERT INTO users (id, name, email)\\nVALUES (:id, :name, :email);\\n","update":true,"required":true,"page_control":"TEXTAREA","value_type":"FIX_VALUE","mutability":"readWrite"},{"ui_id":"1820734622685589509","name":"limit","type":"NUMBER","description":"查询结果最大返回行数，最大返回100条","display_name":"查询结果最大返回行数","multi_valued":false,"value":"100","update":true,"required":true,"page_control":"TEXT","value_type":"FIX_VALUE","mutability":"readWrite"},{"ui_id":"1820734622685589510","name":"input","type":"OBJECT","description":"","display_name":"输入参数","multi_valued":false,"value":{"id":"3","name":"liuyanjie","email":"2024/8/6"},"update":true,"required":false,"page_control":"EXTEND_OBJ","value_type":"JS_EXP","mutability":"readWrite"}],"update":true,"required":true,"page_control":"FIXED_OBJ","value_type":"JS_EXP","mutability":"readWrite"}', '{"account":{"data_base_url":"****************************************************************************************","dbname":"test","encode":"utf8","file_name":null,"host":"*************","instance":null,"password":"Cyb2021@dgsee","port":"3306","proxy_ip":null,"real_ip":"*************","type":"MYSQL","username":"uep"},"account_id":"1820725295183933442","input":{"name":"liuyanjie","id":"3","email":"2024/8/6"},"limit":100,"op_type":"INSERT","sql":"INSERT INTO users (id, name, email)\\nVALUES (:id, :name, :email);\\n"}', '{"result":true,"total":1,"data":null}', 'SUCCESS', 'OK', '2024-08-06 16:20:26', '2024-08-06 16:20:26', null, '2024-08-06 14:51:07', 'admin', '2024-08-06 16:21:07', 'admin', 'iam', '0', null);

-- 当宜搭有数据新增的时候在Teambition创建任务
INSERT INTO acm.acm_flow_instance (id, name, description, flow_type, status, op_status, error_msg, run_start_time, run_end_time, api_key, category, create_time, create_by, update_time, update_by, tenant_id) VALUES (1819196666402680834, '宜搭数据新增同步到Teambition创建任务', null, 1, 'IDLE', 'ONLINE', 'OK', '2024-08-06 13:32:26', '2024-08-06 13:32:27', '8553cb6e5c5948aebf6e773d82939ec1', '协同', '2024-08-02 10:20:59', 'admin', '2024-08-07 15:32:14', 'admin', 'iam');
INSERT INTO acm.acm_flow_node (id, node_type, parent_id, next_id, connector_id, action_id, account_id, flow_id, description, config, input, output, status, error_msg, run_start_time, run_end_time, proxy_id, create_time, create_by, update_time, update_by, tenant_id, closed, note) VALUES (1819196666436235265, 'StartEventNode', 0, 1820693447921741826, 1, 1, null, 1819196666402680834, null, '{"multi_valued":false,"value_type":"JS_EXP","sub_params":[{"multi_valued":false,"value_type":"FIX_VALUE","ui_id":"1819197777875169283","name":"hookUrl","description":"请复制此Webhook地址，作为接口调用目标地址；目前仅支持POST方法且content-type为json的调用方式。","update":true,"mutability":"readonly","display_name":"webhook地址","value":"http://**************/acm/flows/start/1/1819196666402680834/8553cb6e5c5948aebf6e773d82939ec1","page_control":"LABEL","required":true},{"multi_valued":false,"value_type":"FIX_VALUE","option_values":["异步","同步"],"ui_id":"1819197777875169284","description":"异步：webhook接收到数据后，将直接返回接收成功的结果以及流的信息。\\n同步：Webhook接收到数据后，将等待连接流执行完成并将最后的执行结果返回给调用方（最长等待时间为10s，超时将返回为空）。请查看接口文档获得更多详细信息","update":true,"display_name":"连接流执行方式","type":"STRING","required":true,"name":"type","mutability":"readWrite","value":"异步","page_control":"SELECT"},{"multi_valued":false,"value_type":"JS_EXP","ui_id":"1819197777875169285","name":"value","description":"请按实际情况输入json格式的测试数据","update":true,"mutability":"readWrite","display_name":"测试数据","type":"OBJECT","value":"{\\"name\\":\\"花粉\\",\\"userid\\":\\"225558436220163554\\"}","page_control":"JSON","required":false}],"ui_id":"1819197777875169282","name":"root","description":"实时触发流程","update":true,"mutability":"readWrite","display_name":"接收数据","type":"OBJECT","page_control":"FIXED_OBJ","required":true}', '{"hook_url":"http://**************/acm/flows/start/1/1819196666402680834/8553cb6e5c5948aebf6e773d82939ec1","sync":false,"type":"异步","value":{"name":"花粉","userid":"225558436220163554"}}', '{"hook_url":"http://**************/acm/flows/start/1/1819196666402680834/8553cb6e5c5948aebf6e773d82939ec1","sync":false,"type":"异步","value":{"name":"花粉","userid":"225558436220163554"}}', 'SUCCESS', 'OK', '2024-08-07 15:32:14', '2024-08-07 15:32:14', null, '2024-08-02 10:20:59', 'admin', '2024-08-07 15:32:14', 'admin', 'iam', '0', null);
INSERT INTO acm.acm_flow_node (id, node_type, parent_id, next_id, connector_id, action_id, account_id, flow_id, description, config, input, output, status, error_msg, run_start_time, run_end_time, proxy_id, create_time, create_by, update_time, update_by, tenant_id, closed, note) VALUES (1819196666532704257, 'EndNode', 0, null, null, null, null, 1819196666402680834, null, null, null, null, 'IDLE', null, null, null, null, '2024-08-02 10:20:59', 'admin', '2024-08-02 10:20:59', 'admin', 'iam', '0', null);
INSERT INTO acm.acm_flow_node (id, node_type, parent_id, next_id, connector_id, action_id, account_id, flow_id, description, config, input, output, status, error_msg, run_start_time, run_end_time, proxy_id, create_time, create_by, update_time, update_by, tenant_id, closed, note) VALUES (1820693447921741826, 'ActionNode', 0, 1819196666532704257, 1698893745929441282, 1698894784443305985, 1820693148834312194, 1819196666402680834, null, '{"ui_id":"1704043824053039106","name":"root","type":"OBJECT","display_name":"root","multi_valued":false,"sub_params":[{"ui_id":"*************","name":"httpMethod","type":"STRING","display_name":"请求类型","multi_valued":false,"value":"POST","update":true,"required":true,"page_control":"HIDDEN","option_values":[],"value_type":"FIX_VALUE","mutability":"readWrite"},{"ui_id":"*************","name":"endpoint","type":"STRING","display_name":"请求地址","multi_valued":false,"value":"https://api.dingtalk.com/v1.0/project/users/{userId}/projects","update":true,"required":true,"page_control":"TEXT","value_type":"FIX_VALUE","mutability":"readWrite"},{"ui_id":"1693883316728","name":"path.userId","type":"STRING","description":"操作者userId","display_name":"操作者userId","multi_valued":false,"value":"{{N1819196666436235265.value.userid}}","update":true,"required":true,"page_control":"TEXT","value_type":"ADAPTOR","mutability":"readWrite"},{"ui_id":"*************","name":"body.name","type":"STRING","description":"项目名称","display_name":"项目名称","multi_valued":false,"value":"{{N1819196666436235265.value.name}}","update":true,"required":false,"page_control":"TEXT","value_type":"ADAPTOR","mutability":"readWrite"}],"update":true,"required":false,"page_control":"FIXED_OBJ","value_type":"JS_EXP","mutability":"readWrite"}', '{"account":{"aes_key":null,"agent_id":"**********","app_key":"dinggl8faoyhh2hoa4cw","app_secret":"LGDJQQELRzJimWb7HgMl89dRi_2TCETHgKDFL81Y4G0bXaKH402DPlIMQZ9cPhtO","corp_id":"dingd2a976881f424c2da1320dcb25e91351","proxy_ip":null,"real_ip":"oapi.dingtalk.com","token":null},"account_id":"1820693148834312194","auth_protocol":"DINGTALK","body":{"name":"花粉"},"content_type":null,"encoding":"UTF8","endpoint":"https://api.dingtalk.com/v1.0/project/users/{userId}/projects","header":null,"http_method":"POST","path":{"userId":"225558436220163554"},"proxy_ip":null,"query":null,"response_type":"JSON","success_condition":null}', '{"status_code":200,"header":{"Transfer-Encoding":"chunked","Access-Control-Expose-Headers":"*","Server":"DingTalk/1.0.0","Access-Control-Allow-Origin":"*","x-acs-request-id":"65E4C3FF-82BB-7F9C-A0A1-6EBA7797C95D","Connection":"keep-alive","Date":"Tue, 06 Aug 2024 05:32:26 GMT","Access-Control-Allow-Headers":"X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token","Content-Type":"application/json;charset=utf-8","x-acs-trace-id":"36a344184eea33d213c0aabb334c5fc2"},"body":{"result":{"sourceId":"6294691653c2ef524482040e","isSuspended":false,"visibility":"project","created":"2024-08-06T05:32:26.458Z","isArchived":false,"normalType":"taskflow","customFields":[],"creatorId":"225558436220163554","rootCollectionId":"66b1b56afbc9cb5265f15300","defaultCollectionId":"66b1b56afbc9cb5265f15301","uniqueIdPrefix":"","isTemplate":false,"name":"花粉","logo":"https://tcs-ga.teambition.net/thumbnail/111tef9a0fbcb8a22b1ea0de47d85b9a52e0/w/600/h/300","projectId":"66b1b56afbc9cb5265f152ff","updated":"2024-08-06T05:32:26.458Z"}}}', 'INVALID', 'OK', '2024-08-06 13:32:26', '2024-08-06 13:32:27', null, '2024-08-06 13:28:39', 'admin', '2024-08-06 13:32:27', 'admin', 'iam', '0', null);


-- 当宜搭修改任务标题的时候在Teambition修改任务标题
INSERT INTO acm.acm_flow_instance (id, name, description, flow_type, status, op_status, error_msg, run_start_time, run_end_time, api_key, category, create_time, create_by, update_time, update_by, tenant_id) VALUES (1821489867117408257, '宜搭数据更新同步到Teambition', null, 1, 'IDLE', 'ONLINE', 'OK', '2024-08-08 19:29:40', '2024-08-08 19:29:40', '7007924a78124dfa90f7e789f85548bc', '协同', '2024-08-08 18:13:21', 'admin', '2024-08-08 19:29:40', 'admin', 'iam');
INSERT INTO acm.acm_flow_node (id, node_type, parent_id, next_id, connector_id, action_id, account_id, flow_id, description, config, input, output, status, error_msg, run_start_time, run_end_time, proxy_id, create_time, create_by, update_time, update_by, tenant_id, closed, note) VALUES (1821489867146768385, 'StartEventNode', 0, 1821505636702203906, 1, 1, null, 1821489867117408257, null, '{"description":"实时触发流程","display_name":"接收数据","multi_valued":false,"mutability":"readWrite","name":"root","page_control":"FIXED_OBJ","required":true,"sub_params":[{"description":"请复制此Webhook地址，作为接口调用目标地址；目前仅支持POST方法且content-type为json的调用方式。","display_name":"webhook地址","multi_valued":false,"mutability":"readonly","name":"hookUrl","page_control":"LABEL","required":true,"ui_id":"1821490015464452099","update":true,"value":"http://**************/acm/flows/start/1/1821489867117408257/7007924a78124dfa90f7e789f85548bc","value_type":"FIX_VALUE"},{"description":"异步：webhook接收到数据后，将直接返回接收成功的结果以及流的信息。\\n同步：Webhook接收到数据后，将等待连接流执行完成并将最后的执行结果返回给调用方（最长等待时间为10s，超时将返回为空）。请查看接口文档获得更多详细信息","display_name":"连接流执行方式","multi_valued":false,"mutability":"readWrite","name":"type","option_values":["异步","同步"],"page_control":"SELECT","required":true,"type":"STRING","ui_id":"1821490015464452100","update":true,"value":"异步","value_type":"FIX_VALUE"},{"description":"请按实际情况输入json格式的测试数据","display_name":"测试数据","multi_valued":false,"mutability":"readWrite","name":"value","page_control":"JSON","required":false,"type":"OBJECT","ui_id":"1821490015464452101","update":true,"value":"{\\"userId\\":\\"010616354816790819\\",\\"taskId\\":\\"66b4963e542c18e1d7c3b14b\\",\\"content\\":\\"我去你的\\"}","value_type":"JS_EXP"}],"type":"OBJECT","ui_id":"1821490015464452098","update":true,"value_type":"JS_EXP"}', '{"hook_url":"http://**************/acm/flows/start/1/1821489867117408257/7007924a78124dfa90f7e789f85548bc","sync":false,"type":"异步","value":{"userId":"010616354816790819","taskId":"66b4963e542c18e1d7c3b14b","content":"我去你的"}}', '{"hook_url":"http://**************/acm/flows/start/1/1821489867117408257/7007924a78124dfa90f7e789f85548bc","sync":false,"type":"异步","value":{"userId":"010616354816790819","taskId":"66b4963e542c18e1d7c3b14b","content":"我去你的"}}', 'SUCCESS', 'OK', '2024-08-08 19:29:40', '2024-08-08 19:29:40', null, '2024-08-08 18:13:21', 'admin', '2024-08-08 19:29:40', 'admin', 'iam', '0', null);
INSERT INTO acm.acm_flow_node (id, node_type, parent_id, next_id, connector_id, action_id, account_id, flow_id, description, config, input, output, status, error_msg, run_start_time, run_end_time, proxy_id, create_time, create_by, update_time, update_by, tenant_id, closed, note) VALUES (1821489867205488641, 'EndNode', 0, null, null, null, null, 1821489867117408257, null, null, null, null, 'IDLE', null, null, null, null, '2024-08-08 18:13:21', 'admin', '2024-08-08 18:13:21', 'admin', 'iam', '0', null);
INSERT INTO acm.acm_flow_node (id, node_type, parent_id, next_id, connector_id, action_id, account_id, flow_id, description, config, input, output, status, error_msg, run_start_time, run_end_time, proxy_id, create_time, create_by, update_time, update_by, tenant_id, closed, note) VALUES (1821505636702203906, 'ActionNode', 0, 1821489867205488641, 24, 74, 1821498517340925954, 1821489867117408257, null, '{"multi_valued":false,"value_type":"JS_EXP","ui_id":"1821502810620817409","sub_params":[{"multi_valued":false,"value_type":"FIX_VALUE","ui_id":"1699309157332619266","option_values":["GET","POST","PUT","PATCH","DELETE"],"description":"","update":true,"type":"STRING","display_name":"请求类型","required":true,"name":"httpMethod","mutability":"readWrite","value":"PUT","page_control":"HIDDEN"},{"multi_valued":false,"value_type":"FIX_VALUE","ui_id":"1699309157332619267","name":"endpoint","description":"请填写完整的接口地址","update":true,"mutability":"readWrite","type":"STRING","display_name":"请求地址","value":"https://api.dingtalk.com/v1.0/project/users/{userId}/tasks/{taskId}/contents","required":true,"page_control":"HIDDEN"},{"multi_valued":false,"value_type":"FIX_VALUE","ui_id":"1699309157332619268","option_values":[],"description":"","update":true,"type":"STRING","display_name":"请求体类型","required":true,"name":"contentType","mutability":"readonly","value":"application/json","page_control":"HIDDEN"},{"multi_valued":false,"value_type":"ADAPTOR","ui_id":"1699309157332619270","name":"path.userId","description":"","update":true,"mutability":"readWrite","type":"STRING","display_name":"操作者userId","value":"{{N1821489867146768385.value.userId}}","required":true,"page_control":"TEXT"},{"multi_valued":false,"value_type":"ADAPTOR","ui_id":"1723113690925","name":"path.taskId","update":true,"mutability":"readWrite","type":"STRING","display_name":"任务ID","value":"{{N1821489867146768385.value.taskId}}","required":true,"page_control":"TEXT"},{"multi_valued":false,"value_type":"ADAPTOR","ui_id":"1699309157332619271","name":"body.content","description":"","update":true,"mutability":"readWrite","type":"STRING","display_name":"任务标题","value":"{{N1821489867146768385.value.content}}","required":false,"page_control":"TEXT"}],"name":"root","update":true,"mutability":"readWrite","type":"OBJECT","display_name":"root","required":false,"page_control":"FIXED_OBJ"}', '{"account":{"aes_key":null,"agent_id":"**********","app_key":"dingvifmmqjhgbjo50rc","app_secret":"SRM29zqaZdCOkDZ3qPtB_QZRL5NyPbv_qwnRSZXfV-S1b4i1hv4mbM9drLIrVrFj","corp_id":"dingd2a976881f424c2da1320dcb25e91351","proxy_ip":null,"real_ip":"oapi.dingtalk.com","token":null},"account_id":"1821498517340925954","auth_protocol":"DINGTALK","body":{"content":"我去你的"},"content_type":"application/json","encoding":"UTF8","endpoint":"https://api.dingtalk.com/v1.0/project/users/{userId}/tasks/{taskId}/contents","header":null,"http_method":"PUT","path":{"userId":"010616354816790819","taskId":"66b4963e542c18e1d7c3b14b"},"proxy_ip":null,"query":null,"response_type":"JSON","success_condition":null}', '{"status_code":200,"header":{"Transfer-Encoding":"chunked","Access-Control-Expose-Headers":"*","Server":"DingTalk/1.0.0","Access-Control-Allow-Origin":"*","x-acs-request-id":"77A5103F-9C69-7097-9B9F-3D6873E2E338","Connection":"keep-alive","Date":"Thu, 08 Aug 2024 11:29:40 GMT","Access-Control-Allow-Headers":"X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token","Content-Type":"application/json;charset=utf-8","x-acs-trace-id":"a0593c3d9fdd1d73e663a6d672c178c3"},"body":{"result":{"updated":"2024-08-08T11:29:40.359Z","content":"我去你的"}}}', 'SUCCESS', 'OK', '2024-08-08 19:29:40', '2024-08-08 19:29:40', null, '2024-08-08 19:16:00', 'admin', '2024-08-08 19:29:40', 'admin', 'iam', '0', null);

ALTER TABLE acm_flow_instance ADD COLUMN template TINYINT DEFAULT 0 COMMENT '是否为模版，1是，0否';

UPDATE acm_flow_instance SET template = 1 where tenant_id = 'iam';

-- 新增睿人事
INSERT INTO acm_app_connector (id, name, description, icon, build_type, app_package, deployment_type, ver_name, ver_number, create_time, create_by, update_time, update_by, tenant_id) VALUES (29, '睿人事', '睿人事', NULL, 'BuiltIn', 'rrs', 'SaasApp', '1.0', 1, now(), 'admin', now(), 'admin', 'iam');
INSERT INTO acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, status, config_schema, create_time, create_by, update_time, update_by, tenant_id) VALUES (75, 'rrs_sync_user', 0, '同步员工的生态对接信息', '同步员工的钉钉信息至睿人事', 29, 2, 'HTTP', 1, '{"display_name":"root","multi_valued":false,"mutability":"readWrite","name":"root","page_control":"FIXED_OBJ","required":false,"sub_params":[{"description":"","display_name":"请求类型","multi_valued":false,"mutability":"readWrite","name":"httpMethod","option_values":[],"page_control":"HIDDEN","required":true,"type":"STRING","ui_id":"1699309157332619266","update":true,"value":"POST","value_type":"FIX_VALUE"},{"description":"请填写完整的接口地址","display_name":"请求地址","multi_valued":false,"mutability":"readWrite","name":"endpoint","page_control":"TEXT","required":true,"type":"STRING","ui_id":"1699309157332619267","update":true,"value":"https://ip:port/open/api/v1/ecology/sync/emp/info","value_type":"FIX_VALUE"},{"description":"","display_name":"请求体类型","multi_valued":false,"mutability":"readonly","name":"contentType","option_values":[],"page_control":"HIDDEN","required":true,"type":"STRING","ui_id":"1699309157332619268","update":true,"value":"application/json; charset=UTF-8","value_type":"FIX_VALUE"},{"description":"","display_name":"员工的empId","multi_valued":false,"mutability":"readWrite","name":"body.empId","page_control":"TEXT","required":true,"type":"STRING","ui_id":"1699309157332619271","update":true,"value":"","value_type":"ADAPTOR"},{"display_name":"企业id","multi_valued":false,"mutability":"readWrite","name":"body.corpId","page_control":"TEXT","required":true,"type":"STRING","ui_id":"1723627884936","update":true,"value_type":"ADAPTOR"},{"description":"账号类型为个人时必填","display_name":"钉钉的手机号","multi_valued":false,"mutability":"readWrite","name":"body.dingTalkMobile","page_control":"TEXT","required":false,"type":"STRING","ui_id":"*************","update":true,"value_type":"ADAPTOR"},{"display_name":"钉钉的userId","multi_valued":false,"mutability":"readWrite","name":"body.userId","page_control":"TEXT","required":true,"type":"STRING","ui_id":"*************","update":true,"value_type":"ADAPTOR"},{"description":"专属SSO账号：sync_sso_account","display_name":"账号类型","multi_valued":false,"mutability":"readWrite","name":"body.dingSyncType","page_control":"TEXT","required":true,"type":"STRING","ui_id":"*************","update":true,"value_type":"ADAPTOR"}],"type":"OBJECT","ui_id":"1823660491346780162","update":true,"value_type":"JS_EXP"}', now(), 'admin', now(), 'admin', 'iam');
INSERT INTO acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, status, config_schema, create_time, create_by, update_time, update_by, tenant_id) VALUES (76, 'rrs_get_ptj', 0, '查询员工兼岗信息', '查询员工兼岗信息', 29, 2, 'HTTP', 1, '{"display_name":"root","multi_valued":false,"mutability":"readWrite","name":"root","page_control":"FIXED_OBJ","required":false,"sub_params":[{"description":"","display_name":"请求类型","multi_valued":false,"mutability":"readWrite","name":"httpMethod","option_values":[],"page_control":"HIDDEN","required":true,"type":"STRING","ui_id":"1699309157332619266","update":true,"value":"POST","value_type":"FIX_VALUE"},{"description":"请填写完整的接口地址","display_name":"请求地址","multi_valued":false,"mutability":"readWrite","name":"endpoint","page_control":"TEXT","required":true,"type":"STRING","ui_id":"1699309157332619267","update":true,"value":"https://ip:port/open/api/v1/ptj/info/detail","value_type":"FIX_VALUE"},{"description":"","display_name":"请求体类型","multi_valued":false,"mutability":"readonly","name":"contentType","option_values":[],"page_control":"HIDDEN","required":true,"type":"STRING","ui_id":"1699309157332619268","update":true,"value":"application/json; charset=UTF-8","value_type":"FIX_VALUE"},{"description":"","display_name":"员工的empId","multi_valued":false,"mutability":"readWrite","name":"body.empId","page_control":"TEXT","required":true,"type":"STRING","ui_id":"1699309157332619271","update":true,"value":"","value_type":"ADAPTOR"},{"display_name":"企业id","multi_valued":false,"mutability":"readWrite","name":"body.corpId","page_control":"TEXT","required":true,"type":"STRING","ui_id":"1723627884936","update":true,"value_type":"ADAPTOR"},{"description":"兼岗开始日期 用于获取当前生效的兼岗记录","display_name":"开始时间","multi_valued":false,"mutability":"readWrite","name":"body.ptjStartDate","page_control":"TEXT","required":false,"type":"STRING","ui_id":"*************","update":true,"value_type":"ADAPTOR"},{"description":"兼岗开始日期 用于获取当前生效的兼岗记录\\n\\n","display_name":"结束时间","multi_valued":false,"mutability":"readWrite","name":"body.ptjStartDate","page_control":"TEXT","required":false,"type":"STRING","ui_id":"*************","update":true,"value_type":"ADAPTOR"},{"description":"兼岗部门 用于获取某部门下的兼岗员工","display_name":"兼岗部门","multi_valued":false,"mutability":"readWrite","name":"body.ptjDepartment","page_control":"TEXT","required":false,"type":"STRING","ui_id":"*************","update":true,"value_type":"ADAPTOR"}],"type":"OBJECT","ui_id":"1823976763259330561","update":true,"value_type":"JS_EXP"}', now(), 'admin', now(), 'admin', 'iam');
-- 修改oauth2账户配置
UPDATE acm_app_account SET auth_schema = '{"description":"OAuth 2.0认证","displayName":"OAuth 2.0认证","multiValued":false,"mutability":"readWrite","name":"root","subParams":[{"description":"","displayName":"Grant Type","multiValued":false,"name":"grantType","type":"STRING","value":"client_credentials","required":true,"page_control":"LABEL","valueType":"FIX_VALUE"},{"description":"","displayName":"Client Authentication","multiValued":false,"name":"authMethod","type":"STRING","required":true,"page_control":"SELECT","option_values":["client_secret_basic","client_secret_post"],"valueType":"FIX_VALUE"},{"description":"","displayName":"应用标识名","multiValued":false,"name":"clientIdKey","value":"client_id","type":"STRING","required":true,"page_control":"TEXT","valueType":"FIX_VALUE"},{"description":"","displayName":"应用标识值","multiValued":false,"name":"clientIdValue","type":"STRING","required":true,"page_control":"TEXT","valueType":"FIX_VALUE"},{"descriptionadisplayName":"应用密钥名","multiValued":false,"name":"clientSecretKey","value":"client_secret","type":"STRING","required":true,"page_control":"TEXT","valueType":"FIX_VALUE"},{"description":"","displayName":"应用密钥值","multiValued":false,"name":"clientSecretValue","type":"STRING","required":true,"page_control":"TEXT","valueType":"FIX_VALUE"},{"description":"","displayName":"Access Token URL","multiValued":false,"name":"endpoint","type":"STRING","required":true,"page_control":"TEXT","valueType":"FIX_VALUE"},{"description":"","displayName":"授权范围","multiValued":false,"name":"scope","type":"STRING","required":false,"page_control":"TEXT","valueType":"FIX_VALUE"},{"description":"","displayName":"请求类型","multiValued":false,"name":"contentType","type":"STRING","required":true,"page_control":"SELECT","option_values":["application/x-www-form-urlencoded","application/json; charset=utf-8"],"valueType":"FIX_VALUE"}],"type":"OBJECT","valueType":"JS_EXP"}' WHERE id = 2;

-- 修改webhook文案
UPDATE acm_app_action SET config_schema = '{"description":"实时触发流程","display_name":"接收数据","multi_valued":false,"mutability":"readWrite","name":"root","page_control":"FIXED_OBJ","required":true,"sub_params":[{"description":"请复制此Webhook地址，它支持常见的GET/POST/PUT/PATCH/DELETE等请求方式,请根据需求选择合适的方法进行API调用。","display_name":"webhook地址","multi_valued":false,"mutability":"readonly","name":"hookUrl","page_control":"LABEL","required":true,"value":"{{server}}/acm/flows/start/1/{{flowId}}/{{apiKey}}","value_type":"FIX_VALUE"},{"description":"异步：webhook接收到数据后，将直接返回接收成功的结果以及流的信息。同步：Webhook接收到数据后，将等待连接流执行完成并将最后的执行结果返回给调用方（最长等待时间为10s，超时将返回为空）。请查看接口文档获得更多详细信息","display_name":"连接流执行方式","multi_valued":false,"mutability":"readWrite","name":"type","option_values":["异步","同步"],"page_control":"SELECT","required":true,"type":"STRING","value_type":"FIX_VALUE"},{"description":"请按实际情况输入json格式的测试数据","display_name":"测试数据","multi_valued":false,"mutability":"readWrite","name":"value","page_control":"JSON","required":false,"type":"OBJECT","value_type":"JS_EXP"}],"type":"OBJECT","value_type":"JS_EXP"}' WHERE id = 1;

ALTER TABLE acm_flow_node ADD COLUMN action_key VARCHAR(100) COMMENT 'action_key' AFTER action_id;
ALTER TABLE acm_flow_node_shadow ADD COLUMN action_key VARCHAR(100) COMMENT 'action_key' AFTER action_id;

insert into acm_app_action(id,action_key,is_trigger,name,description,connector_id,account_schema_id,protocol,config_schema,create_time,update_time,tenant_id)
values(77,'ding_event_approval',1,'钉钉事件通知-审批','当发生在钉钉订阅的审批事件时，实时触发流程',6,6,'WEBHOOK_CRONTAB',
       '{"description":"当接收到新数据时，实时触发流程","display_name":"接收数据","multi_valued":false,"mutability":"readWrite","name":"root","page_control":"FIXED_OBJ","required":true,"sub_params":[
       {"description":"登录 钉钉开发者平台将复制的Webhook地址配置到已创建的企业内部应用-事件订阅下，并在事件订阅列表区域开启要订阅的事件。","display_name":"Webhook URL","multi_valued":false,"mutability":"readonly","name":"hookUrl","page_control":"LABEL","required":true,"value":"{{server}}/acm/flows/start/8/{{flowId}}/{{apiKey}}","value_type":"FIX_VALUE"},{"description":"最近一次收到的钉钉事件通知数据。","display_name":"事件通知数据","multi_valued":false,"mutability":"readonly","name":"value","page_control":"JSON","required":false,"type":"OBJECT","value_type":"JS_EXP"},
       {"description":"定时执行生效的开始日期","display_name":"生效开始日期","multi_valued":false,"mutability":"readWrite","name":"startDate","page_control":"HIDDEN","required":true,"type":"STRING","value":"2020-01-01","value_type":"FIX_VALUE","visible": false},{"description":"定时执行生效的结束日期","display_name":"生效结束日期","multi_valued":false,"mutability":"readWrite","name":"endDate","page_control":"HIDDEN","required":true,"type":"STRING","value_type":"FIX_VALUE","value":"2200-01-01","visible": false},{"description":"cron表达式：秒 分 时 天 月 周","display_name":"crontab表达式","multi_valued":false,"mutability":"readWrite","name":"cycle","page_control":"HIDDEN","required":true,"type":"STRING","value_type":"FIX_VALUE","value":"0/10 * * * * *","visible":false}
       ],"type":"OBJECT","value_type":"JS_EXP"}'
          ,now(),now(),'iam');
insert into acm_app_action(id,action_key,is_trigger,name,description,connector_id,account_schema_id,protocol,config_schema,create_time,update_time,tenant_id)
values(78,'ding_event_todo',1,'钉钉事件通知-待办','当发生在钉钉订阅的待办事件时，实时触发流程',6,6,'WEBHOOK_CRONTAB',
       '{"description":"当接收到新数据时，实时触发流程","display_name":"接收数据","multi_valued":false,"mutability":"readWrite","name":"root","page_control":"FIXED_OBJ","required":true,"sub_params":[
       {"description":"登录 钉钉开发者平台将复制的Webhook地址配置到已创建的企业内部应用-事件订阅下，并在事件订阅列表区域开启要订阅的事件。","display_name":"Webhook URL","multi_valued":false,"mutability":"readonly","name":"hookUrl","page_control":"LABEL","required":true,"value":"{{server}}/acm/flows/start/8/{{flowId}}/{{apiKey}}","value_type":"FIX_VALUE"},{"description":"最近一次收到的钉钉事件通知数据。","display_name":"事件通知数据","multi_valued":false,"mutability":"readonly","name":"value","page_control":"JSON","required":false,"type":"OBJECT","value_type":"JS_EXP"},
       {"description":"定时执行生效的开始日期","display_name":"生效开始日期","multi_valued":false,"mutability":"readWrite","name":"startDate","page_control":"HIDDEN","required":true,"type":"STRING","value":"2020-01-01","value_type":"FIX_VALUE","visible": false},{"description":"定时执行生效的结束日期","display_name":"生效结束日期","multi_valued":false,"mutability":"readWrite","name":"endDate","page_control":"HIDDEN","required":true,"type":"STRING","value_type":"FIX_VALUE","value":"2200-01-01","visible": false},{"description":"cron表达式：秒 分 时 天 月 周","display_name":"crontab表达式","multi_valued":false,"mutability":"readWrite","name":"cycle","page_control":"HIDDEN","required":true,"type":"STRING","value_type":"FIX_VALUE","value":"0/10 * * * * *","visible":false}
       ],"type":"OBJECT","value_type":"JS_EXP"}'
          ,now(),now(),'iam');











-- 读取Dashboard数据渲染
INSERT INTO `acm`.`acm_flow_instance` (`id`, `name`, `description`, `flow_type`, `status`, `op_status`, `error_msg`, `run_start_time`, `run_end_time`, `api_key`, `category`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `template`) VALUES (1824052833100910593, 'Dashboard用户部门统计', 'Dashboard用户部门统计', 2, 'IDLE', 'ONLINE', NULL, NULL, NULL, 'b8ed104ca8394de88193fee0d1c96e21', '协同', now(), 'admin', now(), 'admin', 'iam', 0);
INSERT INTO `acm`.`acm_flow_instance` (`id`, `name`, `description`, `flow_type`, `status`, `op_status`, `error_msg`, `run_start_time`, `run_end_time`, `api_key`, `category`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `template`) VALUES (1824053226413379586, 'Dashboard连接流应用统计', 'Dashboard连接流应用统计', 2, 'IDLE', 'ONLINE', NULL, NULL, NULL, '2e1c3c619ca444dd966a5077f824e458', '协同', now(), 'admin', now(), 'admin', 'iam', 0);
INSERT INTO `acm`.`acm_flow_instance` (`id`, `name`, `description`, `flow_type`, `status`, `op_status`, `error_msg`, `run_start_time`, `run_end_time`, `api_key`, `category`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `template`) VALUES (1824265087104761857, 'Dashboard用户状态统计图', 'Dashboard用户状态统计图', 2, 'IDLE', 'ONLINE', NULL, NULL, NULL, '95fa78d06ff14c7986e74fa14b05dd0a', '协同', now(), 'admin', now(), 'admin', 'iam', 0);
INSERT INTO `acm`.`acm_flow_instance` (`id`, `name`, `description`, `flow_type`, `status`, `op_status`, `error_msg`, `run_start_time`, `run_end_time`, `api_key`, `category`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `template`) VALUES (1824272013301563394, 'Dashboard用户登录趋势统计', 'Dashboard用户登录趋势统计', 2, 'IDLE', 'ONLINE', NULL, NULL, NULL, '56f34737b1f04600b2c465d364217bda', '协同', now(), 'admin', now(), 'admin', 'iam', 0);
INSERT INTO `acm`.`acm_flow_instance` (`id`, `name`, `description`, `flow_type`, `status`, `op_status`, `error_msg`, `run_start_time`, `run_end_time`, `api_key`, `category`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `template`) VALUES (1824272226372206593, 'Dashboard应用Top3登录趋势统计', 'Dashboard应用Top3登录趋势统计', 2, 'IDLE', 'ONLINE', NULL, NULL, NULL, '0a3795ec589b41359468710e93f56d47', '协同', now(), 'admin', now(), 'admin', 'iam', 0);
INSERT INTO `acm`.`acm_flow_instance` (`id`, `name`, `description`, `flow_type`, `status`, `op_status`, `error_msg`, `run_start_time`, `run_end_time`, `api_key`, `category`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `template`) VALUES (1824272437412806658, 'Dashboard应用认证源统计', 'Dashboard应用认证源统计', 2, 'IDLE', 'ONLINE', NULL, NULL, NULL, '5f55ca655279491db1af24102056d1b8', '协同', now(), 'admin', now(), 'admin', 'iam', 0);
INSERT INTO `acm`.`acm_flow_instance` (`id`, `name`, `description`, `flow_type`, `status`, `op_status`, `error_msg`, `run_start_time`, `run_end_time`, `api_key`, `category`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `template`) VALUES (1824369959049281537, 'Dashboard用户部门数据获取', 'Dashboard用户部门数据获取', 1, 'IDLE', 'ONLINE', NULL, NULL, NULL, 'e28e5a28cdf3408eb80a9bd65d359d20', '协同', now(), 'admin', now(), 'admin', 'iam', 0);
INSERT INTO `acm`.`acm_flow_instance` (`id`, `name`, `description`, `flow_type`, `status`, `op_status`, `error_msg`, `run_start_time`, `run_end_time`, `api_key`, `category`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `template`) VALUES (1824380134007160833, 'Dashboard连接流应用统计数据获取', 'Dashboard连接流应用统计数据获取', 1, 'IDLE', 'ONLINE', NULL, NULL, NULL, '7637e00658f34d838e8da6cc99086966', '协同', now(), 'admin', now(), 'admin', 'iam', 0);
INSERT INTO `acm`.`acm_flow_instance` (`id`, `name`, `description`, `flow_type`, `status`, `op_status`, `error_msg`, `run_start_time`, `run_end_time`, `api_key`, `category`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `template`) VALUES (1824381134206058498, 'Dashboard用户状态数据获取', 'Dashboard用户状态数据获取', 1, 'IDLE', 'ONLINE', NULL, NULL, NULL, '2131733a6615451099e9966d6fffcd5d', '协同', now(), 'admin', now(), 'admin', 'iam', 0);
INSERT INTO `acm`.`acm_flow_instance` (`id`, `name`, `description`, `flow_type`, `status`, `op_status`, `error_msg`, `run_start_time`, `run_end_time`, `api_key`, `category`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `template`) VALUES (1824381737745432577, 'Dashboard用户登录趋势数据', 'Dashboard用户登录趋势数据', 1, 'IDLE', 'ONLINE', NULL, NULL, NULL, '6a435009486140b2801f18f10bd7aaea', '协同', now(), 'admin', now(), 'admin', 'iam', 0);
INSERT INTO `acm`.`acm_flow_instance` (`id`, `name`, `description`, `flow_type`, `status`, `op_status`, `error_msg`, `run_start_time`, `run_end_time`, `api_key`, `category`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `template`) VALUES (1824382296946819073, 'Dashboard应用Top3登录趋势数据', 'Dashboard应用Top3登录趋势数据', 1, 'IDLE', 'ONLINE', NULL, NULL, NULL, 'd74e3218769945cba71876459c439819', '协同', now(), 'admin', now(), 'admin', 'iam', 0);
INSERT INTO `acm`.`acm_flow_instance` (`id`, `name`, `description`, `flow_type`, `status`, `op_status`, `error_msg`, `run_start_time`, `run_end_time`, `api_key`, `category`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `template`) VALUES (1824382978630270977, 'Dashboard应用认证源统计数据', 'Dashboard应用认证源统计数据', 1, 'IDLE', 'ONLINE', NULL, NULL, NULL, 'bf0665f9997d42e7a5b7b6757e073197', '协同', now(), 'admin', now(), 'admin', 'iam', 0);

-- 用户部门信息统计
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824052844836573185, 'StartEventNode', 0, 1824052862737862657, 2, 2, NULL, NULL, 1824052833100910593, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824407712493117441\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824407712493117442\",\"name\":\"startDate\",\"description\":\"定时执行生效的开始日期\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"生效开始日期\",\"value\":\"2024-08-01\",\"required\":true,\"page_control\":\"DATE\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824407712493117443\",\"name\":\"endDate\",\"description\":\"定时执行生效的结束日期\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"生效结束日期\",\"value\":\"2029-08-31\",\"required\":true,\"page_control\":\"DATE\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824407712493117444\",\"name\":\"cycle\",\"description\":\"cron表达式：秒 分 时 天 月 周\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"crontab表达式\",\"value\":\"* */30 * * * *\",\"required\":true,\"page_control\":\"TEXT\"}],\"name\":\"root\",\"description\":\"配置crontab，自定义触发时间\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"自定义执行\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"cycle\":\"* */30 * * * *\",\"end_date\":\"2029-08-31\",\"start_date\":\"2024-08-01\"}', '{\"cycle\":\"* */30 * * * *\",\"end_date\":\"2029-08-31\",\"start_date\":\"2024-08-01\"}', 'SUCCESS', 'OK', '2024-08-28 15:54:31', '2024-08-28 15:54:31', NULL, '2024-08-15 19:57:39', 'admin', '2024-08-28 15:54:31', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824052862737862657, 'ActionNode', 0, 1824065266519527425, 5, 7, NULL, 1822874457954340865, 1824052833100910593, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824064382563426306\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824064382563426307\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"操作类型\",\"required\":true,\"name\":\"opType\",\"mutability\":\"readWrite\",\"value\":\"SELECT\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824064382563426308\",\"name\":\"sql\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"Sql语句\",\"value\":\"SELECT\\n\\tcount( 1 ) AS userTotal,\\n\\ttenant_id \\nFROM\\n\\tiam_user \\nWHERE\\n\\tusername != \'admin\' \\nGROUP BY\\n\\ttenant_id\",\"required\":true,\"page_control\":\"TEXTAREA\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824064382563426309\",\"name\":\"limit\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"查询结果最大返回行数\",\"value\":\"2000\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824064382563426310\",\"name\":\"input\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"输入参数\",\"value\":{},\"required\":false,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"自定义Sql操作数据库\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"自定义Sql\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"account\":{\"data_base_url\":\"jdbc:mysql://*************:3306/iam?useUnicode=true&useSSL=false&characterEncoding=utf8\",\"dbname\":\"iam\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"1822874457954340865\",\"input\":{},\"limit\":2000,\"op_type\":\"SELECT\",\"sql\":\"SELECT\\n\\tcount( 1 ) AS userTotal,\\n\\ttenant_id \\nFROM\\n\\tiam_user \\nWHERE\\n\\tusername != \'admin\' \\nGROUP BY\\n\\ttenant_id\"}', '{\"result\":true,\"total\":3,\"data\":[{\"tenant_id\":\"bit\",\"userTotal\":5354},{\"tenant_id\":\"iam\",\"userTotal\":1},{\"tenant_id\":\"vic\",\"userTotal\":1}]}', 'SUCCESS', 'OK', '2024-08-28 15:54:31', '2024-08-28 15:54:31', NULL, '2024-08-15 19:57:42', 'admin', '2024-08-28 15:54:31', 'admin', 'iam', '0', '获取各租户的用户总数');
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824052862876274690, 'EndNode', 0, NULL, NULL, NULL, NULL, NULL, 1824052833100910593, NULL, NULL, NULL, NULL, 'IDLE', NULL, NULL, NULL, NULL, '2024-08-15 19:57:46', 'admin', '2024-08-15 19:57:46', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824065266519527425, 'ActionNode', 0, 1824066125538152450, 5, 7, NULL, 1822874457954340865, 1824052833100910593, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824065286092005377\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824065286092005378\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"操作类型\",\"required\":true,\"name\":\"opType\",\"mutability\":\"readWrite\",\"value\":\"SELECT\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824065286092005379\",\"name\":\"sql\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"Sql语句\",\"value\":\"SELECT\\n\\tcount( 1 ) AS orgTotal,\\n\\ttenant_id \\nFROM\\n\\tiam_org \\nGROUP BY\\n\\ttenant_id\",\"required\":true,\"page_control\":\"TEXTAREA\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824065286092005380\",\"name\":\"limit\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"查询结果最大返回行数\",\"value\":\"2000\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824065286092005381\",\"name\":\"input\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"输入参数\",\"value\":{},\"required\":false,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"自定义Sql操作数据库\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"自定义Sql\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"account\":{\"data_base_url\":\"jdbc:mysql://*************:3306/iam?useUnicode=true&useSSL=false&characterEncoding=utf8\",\"dbname\":\"iam\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"1822874457954340865\",\"input\":{},\"limit\":2000,\"op_type\":\"SELECT\",\"sql\":\"SELECT\\n\\tcount( 1 ) AS orgTotal,\\n\\ttenant_id \\nFROM\\n\\tiam_org \\nGROUP BY\\n\\ttenant_id\"}', '{\"result\":true,\"total\":3,\"data\":[{\"tenant_id\":\"bit\",\"orgTotal\":142},{\"tenant_id\":\"iam\",\"orgTotal\":2},{\"tenant_id\":\"vic\",\"orgTotal\":3}]}', 'SUCCESS', 'OK', '2024-08-28 15:54:31', '2024-08-28 15:54:31', NULL, '2024-08-15 20:47:04', 'admin', '2024-08-28 15:54:31', 'admin', 'iam', '0', '获取个租户的部门总数');
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824066125538152450, 'ActionNode', 0, 1824263003940765698, 5, 7, NULL, 1822874457954340865, 1824052833100910593, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824066137384722433\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824066137384722434\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"操作类型\",\"required\":true,\"name\":\"opType\",\"mutability\":\"readWrite\",\"value\":\"SELECT\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824066137384722435\",\"name\":\"sql\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"Sql语句\",\"value\":\"SELECT\\n    CASE\\n        WHEN DATEDIFF(CURDATE(), DATE(occur_time)) BETWEEN 7 AND 30 THEN \'last30days\'\\n        WHEN DATEDIFF(CURDATE(), DATE(occur_time)) BETWEEN 1 AND 7 THEN \'last7days\'\\n        WHEN DATEDIFF(CURDATE(), DATE(occur_time)) = 0 THEN \'today\'\\n    END AS timePeriod,\\n    COUNT(DISTINCT operator) AS loginCount,\\n\\t\\ttenant_id\\nFROM\\n    iam_auditlog\\nWHERE\\n    event_subtype in (101,104)\\n    AND operator != \'admin\'\\n    AND DATEDIFF(CURDATE(), DATE(occur_time)) <= 30\\nGROUP BY\\n    timePeriod,\\n\\ttenant_id\",\"required\":true,\"page_control\":\"TEXTAREA\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824066137384722436\",\"name\":\"limit\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"查询结果最大返回行数\",\"value\":\"6000\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824066137384722437\",\"name\":\"input\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"输入参数\",\"value\":{},\"required\":false,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"自定义Sql操作数据库\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"自定义Sql\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"account\":{\"data_base_url\":\"jdbc:mysql://*************:3306/iam?useUnicode=true&useSSL=false&characterEncoding=utf8\",\"dbname\":\"iam\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"1822874457954340865\",\"input\":{},\"limit\":6000,\"op_type\":\"SELECT\",\"sql\":\"SELECT\\n    CASE\\n        WHEN DATEDIFF(CURDATE(), DATE(occur_time)) BETWEEN 7 AND 30 THEN \'last30days\'\\n        WHEN DATEDIFF(CURDATE(), DATE(occur_time)) BETWEEN 1 AND 7 THEN \'last7days\'\\n        WHEN DATEDIFF(CURDATE(), DATE(occur_time)) = 0 THEN \'today\'\\n    END AS timePeriod,\\n    COUNT(DISTINCT operator) AS loginCount,\\n\\t\\ttenant_id\\nFROM\\n    iam_auditlog\\nWHERE\\n    event_subtype in (101,104)\\n    AND operator != \'admin\'\\n    AND DATEDIFF(CURDATE(), DATE(occur_time)) <= 30\\nGROUP BY\\n    timePeriod,\\n\\ttenant_id\"}', '{\"result\":true,\"total\":2,\"data\":[{\"timePeriod\":\"last30days\",\"tenant_id\":\"bit\",\"loginCount\":2},{\"timePeriod\":\"today\",\"tenant_id\":\"iam\",\"loginCount\":1}]}', 'SUCCESS', 'OK', '2024-08-28 15:54:31', '2024-08-28 15:54:31', NULL, '2024-08-15 20:50:28', 'admin', '2024-08-28 15:54:31', 'admin', 'iam', '0', '获取各租户活跃的用户数');
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824263003940765698, 'ActionNode', 0, 1824264138365779969, 5, 7, NULL, 1822874457954340865, 1824052833100910593, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824263015065886722\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824263015065886723\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"操作类型\",\"required\":true,\"name\":\"opType\",\"mutability\":\"readWrite\",\"value\":\"SELECT\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824263015065886724\",\"name\":\"sql\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"Sql语句\",\"value\":\"SELECT\\n    CASE\\n        WHEN DATEDIFF(CURDATE(), DATE(create_time)) BETWEEN 7 AND 30 THEN \'last30days\'\\n        WHEN DATEDIFF(CURDATE(), DATE(create_time)) BETWEEN 1 AND 7 THEN \'last7days\'\\n        WHEN DATEDIFF(CURDATE(), DATE(create_time)) = 0 THEN \'today\'\\n    END AS timePeriod,\\n    COUNT(1) AS userCount,\\n    tenant_id\\nFROM\\n    iam_user\\nWHERE\\n  username !=\'admin\'\\n   AND TIMESTAMPDIFF(DAY,DATE(create_time),now()) <= 30\\n   AND TIMESTAMPDIFF(DAY,DATE(create_time),now()) >= 0\\nGROUP BY\\n    timePeriod,\\n    tenant_id\",\"required\":true,\"page_control\":\"TEXTAREA\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824263015065886725\",\"name\":\"limit\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"查询结果最大返回行数\",\"value\":\"6000\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824263015065886726\",\"name\":\"input\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"输入参数\",\"value\":{},\"required\":false,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"自定义Sql操作数据库\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"自定义Sql\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"account\":{\"data_base_url\":\"jdbc:mysql://*************:3306/iam?useUnicode=true&useSSL=false&characterEncoding=utf8\",\"dbname\":\"iam\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"1822874457954340865\",\"input\":{},\"limit\":6000,\"op_type\":\"SELECT\",\"sql\":\"SELECT\\n    CASE\\n        WHEN DATEDIFF(CURDATE(), DATE(create_time)) BETWEEN 7 AND 30 THEN \'last30days\'\\n        WHEN DATEDIFF(CURDATE(), DATE(create_time)) BETWEEN 1 AND 7 THEN \'last7days\'\\n        WHEN DATEDIFF(CURDATE(), DATE(create_time)) = 0 THEN \'today\'\\n    END AS timePeriod,\\n    COUNT(1) AS userCount,\\n    tenant_id\\nFROM\\n    iam_user\\nWHERE\\n  username !=\'admin\'\\n   AND TIMESTAMPDIFF(DAY,DATE(create_time),now()) <= 30\\n   AND TIMESTAMPDIFF(DAY,DATE(create_time),now()) >= 0\\nGROUP BY\\n    timePeriod,\\n    tenant_id\"}', '{\"result\":true,\"total\":5,\"data\":[{\"timePeriod\":\"last30days\",\"userCount\":1,\"tenant_id\":\"iam\"},{\"timePeriod\":\"last30days\",\"userCount\":5049,\"tenant_id\":\"bit\"},{\"timePeriod\":\"last30days\",\"userCount\":1,\"tenant_id\":\"vic\"},{\"timePeriod\":\"last7days\",\"userCount\":5,\"tenant_id\":\"bit\"},{\"timePeriod\":\"today\",\"userCount\":300,\"tenant_id\":\"bit\"}]}', 'SUCCESS', 'OK', '2024-08-28 15:54:31', '2024-08-28 15:54:31', NULL, '2024-08-16 09:52:48', 'admin', '2024-08-28 15:54:31', 'admin', 'iam', '0', '获取各租户今天7天30天新增用户数');
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824264138365779969, 'ActionNode', 0, 1824280446583095297, 5, 7, NULL, 1822874457954340865, 1824052833100910593, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824264153907503106\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824264153907503107\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"操作类型\",\"required\":true,\"name\":\"opType\",\"mutability\":\"readWrite\",\"value\":\"SELECT\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824264153907503108\",\"name\":\"sql\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"Sql语句\",\"value\":\"SELECT\\n    CASE\\n        WHEN DATEDIFF(CURDATE(), DATE(occur_time)) BETWEEN 7 AND 30 THEN \'last30days\'\\n        WHEN DATEDIFF(CURDATE(), DATE(occur_time)) BETWEEN 1 AND 7 THEN \'last7days\'\\n        WHEN DATEDIFF(CURDATE(), DATE(occur_time)) = 0 THEN \'today\'\\n    END AS timePeriod,\\n    COUNT(1) AS authCount,\\n    tenant_id\\nFROM\\n    iam_auditlog\\nWHERE\\n    event_subtype in (101,104)\\n    AND operator != \'admin\'\\n    AND TIMESTAMPDIFF(DAY,DATE(occur_time),now()) <= 30\\n    AND TIMESTAMPDIFF(DAY,DATE(occur_time),now()) >= 0\\nGROUP BY\\n    timePeriod,\\n    tenant_id\",\"required\":true,\"page_control\":\"TEXTAREA\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824264153907503109\",\"name\":\"limit\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"查询结果最大返回行数\",\"value\":\"6000\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824264153911697409\",\"name\":\"input\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"输入参数\",\"value\":{},\"required\":false,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"自定义Sql操作数据库\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"自定义Sql\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"account\":{\"data_base_url\":\"jdbc:mysql://*************:3306/iam?useUnicode=true&useSSL=false&characterEncoding=utf8\",\"dbname\":\"iam\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"1822874457954340865\",\"input\":{},\"limit\":6000,\"op_type\":\"SELECT\",\"sql\":\"SELECT\\n    CASE\\n        WHEN DATEDIFF(CURDATE(), DATE(occur_time)) BETWEEN 7 AND 30 THEN \'last30days\'\\n        WHEN DATEDIFF(CURDATE(), DATE(occur_time)) BETWEEN 1 AND 7 THEN \'last7days\'\\n        WHEN DATEDIFF(CURDATE(), DATE(occur_time)) = 0 THEN \'today\'\\n    END AS timePeriod,\\n    COUNT(1) AS authCount,\\n    tenant_id\\nFROM\\n    iam_auditlog\\nWHERE\\n    event_subtype in (101,104)\\n    AND operator != \'admin\'\\n    AND TIMESTAMPDIFF(DAY,DATE(occur_time),now()) <= 30\\n    AND TIMESTAMPDIFF(DAY,DATE(occur_time),now()) >= 0\\nGROUP BY\\n    timePeriod,\\n    tenant_id\"}', '{\"result\":true,\"total\":2,\"data\":[{\"timePeriod\":\"last30days\",\"tenant_id\":\"bit\",\"authCount\":5},{\"timePeriod\":\"today\",\"tenant_id\":\"iam\",\"authCount\":4}]}', 'SUCCESS', 'OK', '2024-08-28 15:54:31', '2024-08-28 15:54:31', NULL, '2024-08-16 09:57:18', 'admin', '2024-08-28 15:54:31', 'admin', 'iam', '0', '获取各租户今天7天30天总登录数');
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824280446583095297, 'ActionNode', 0, 1824280524316131330, 3, 3, NULL, NULL, 1824052833100910593, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824280458593214466\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824280458593214467\",\"name\":\"vars\",\"description\":\"添加变量名及其初始值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"添加变量\",\"value\":{\"userOrg\":\"\"},\"required\":true,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"创建一个变量，并设置初始值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"创建变量\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"vars\":{\"userOrg\":\"\"}}', '{\"vars\":{\"userOrg\":\"\"}}', 'SUCCESS', 'OK', '2024-08-28 15:54:31', '2024-08-28 15:54:31', NULL, '2024-08-16 11:02:07', 'admin', '2024-08-28 15:54:31', 'admin', 'iam', '0', '接收统计数据计算结果');
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824280524316131330, 'ActionNode', 0, 1824322002929238017, 3, 4, 'set_var', NULL, 1824052833100910593, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824280540889653251\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824280540889653252\",\"name\":\"varPath\",\"description\":\"选择前面步骤已经创建的变量\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"选择要更新的变量\",\"value\":\"{{N1824280446583095297.vars.userOrg}}\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824280540889653253\",\"name\":\"value\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"设置新的值\",\"value\":\"var userTotal = {{N1824052862737862657.data}};\\nvar orgTotal = {{N1824065266519527425.data}};\\nvar loginUsers = {{N1824066125538152450.data}};\\nvar addUsers = {{N1824263003940765698.data}};\\nvar loginCounts = {{N1824264138365779969.data}};\\n\\nconst userTotalData = {};\\nuserTotal.forEach(item => {\\n  const tenantId = item.tenant_id;\\n  if (!userTotalData[tenantId]) {\\n    userTotalData[tenantId] = { userTotal: item.userTotal };\\n  }\\n});\\n\\nconst orgTotaldData = {};\\norgTotal.forEach(item => {\\n  const tenantId = item.tenant_id;\\n  if (!orgTotaldData[tenantId]) {\\n    orgTotaldData[tenantId] = { orgTotal: item.orgTotal };\\n  }\\n});\\n\\nconst loginUserData = {};\\nloginUsers.forEach(item => {\\n  const tenantId = item.tenant_id;\\n  if (!loginUserData[tenantId]) {\\n    loginUserData[tenantId] = [item];\\n  } else {\\n    loginUserData[tenantId].push(item);\\n  }\\n});\\n\\nconst addUserData = {};\\naddUsers.forEach(item => {\\n  const tenantId = item.tenant_id;\\n  if (!addUserData[tenantId]) {\\n    addUserData[tenantId] = [item];\\n  } else {\\n    addUserData[tenantId].push(item);\\n  }\\n});\\n\\nconst loginCountData = {};\\nloginCounts.forEach(item => {\\n  const tenantId = item.tenant_id;\\n  if (!loginCountData[tenantId]) {\\n    loginCountData[tenantId] = [item];\\n  } else {\\n    loginCountData[tenantId].push(item);\\n  }\\n});\\n\\nconst tenantIds = Array.from(new Set(userTotal.map(item => item.tenant_id)));\\n\\nfunction calculateTotal(tenantData, timePeriods, key) {\\n  let total = 0;\\n\\n  tenantData.forEach(item => {\\n    if (timePeriods.includes(item.timePeriod)) {\\n      total += Number(item[key]);\\n    }\\n  });\\n\\n  return Number(total);\\n}\\n\\nvar resultArr = [];\\n\\n\\nfunction calculateProperty(data, periods, field) {\\n  return data ? calculateTotal(data, periods, field) : 0;\\n}\\n\\nfor (let tenantId of tenantIds) {\\n  const user = {\\n    total: Number(userTotalData[tenantId]?.userTotal) || 0,\\n    active: {\\n      day: calculateProperty(loginUserData[tenantId], [\'today\'], \'loginCount\'),\\n      week: calculateProperty(loginUserData[tenantId], [\'last7days\', \'today\'], \'loginCount\'),\\n      month: calculateProperty(loginUserData[tenantId], [\'last30days\', \'last7days\', \'today\'], \'loginCount\')\\n    },\\n    create: {\\n      day: calculateProperty(addUserData[tenantId], [\'today\'], \'userCount\'),\\n      week: calculateProperty(addUserData[tenantId], [\'last7days\', \'today\'], \'userCount\'),\\n      month: calculateProperty(addUserData[tenantId], [\'last30days\', \'last7days\', \'today\'], \'userCount\')\\n    },\\n    login: {\\n      day: calculateProperty(loginCountData[tenantId], [\'today\'], \'authCount\'),\\n      week: calculateProperty(loginCountData[tenantId], [\'last7days\', \'today\'], \'authCount\'),\\n      month: calculateProperty(loginCountData[tenantId], [\'last30days\', \'last7days\', \'today\'], \'authCount\')\\n    }\\n  };\\n\\n  const org = {\\n    total: Number(orgTotaldData[tenantId]?.orgTotal) - 1 || 0\\n  };\\n\\n  resultArr.push({\\n    user,\\n    org,\\n    tenantId\\n  });\\n}\\n\\n\\nreturn resultArr;\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\",\"required\":true,\"page_control\":\"JSON\"}],\"name\":\"root\",\"description\":\"更新变量的值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"更新变量\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"input\":{\"value\":[{\"org\":{\"total\":141},\"tenantId\":\"bit\",\"user\":{\"total\":5354,\"active\":{\"week\":0,\"month\":2,\"day\":0},\"create\":{\"week\":305,\"month\":5354,\"day\":300},\"login\":{\"week\":0,\"month\":5,\"day\":0}}},{\"org\":{\"total\":1},\"tenantId\":\"iam\",\"user\":{\"total\":1,\"active\":{\"week\":1,\"month\":1,\"day\":1},\"create\":{\"week\":0,\"month\":1,\"day\":0},\"login\":{\"week\":4,\"month\":4,\"day\":4}}},{\"org\":{\"total\":2},\"tenantId\":\"vic\",\"user\":{\"total\":1,\"active\":{\"week\":0,\"month\":0,\"day\":0},\"create\":{\"week\":0,\"month\":1,\"day\":0},\"login\":{\"week\":0,\"month\":0,\"day\":0}}}],\"var_path\":\"N1824280446583095297.vars.userOrg\"}}', '{\"code\":200,\"msg\":\"OK\",\"value\":[{\"org\":{\"total\":141},\"tenantId\":\"bit\",\"user\":{\"total\":5354,\"active\":{\"week\":0,\"month\":2,\"day\":0},\"create\":{\"week\":305,\"month\":5354,\"day\":300},\"login\":{\"week\":0,\"month\":5,\"day\":0}}},{\"org\":{\"total\":1},\"tenantId\":\"iam\",\"user\":{\"total\":1,\"active\":{\"week\":1,\"month\":1,\"day\":1},\"create\":{\"week\":0,\"month\":1,\"day\":0},\"login\":{\"week\":4,\"month\":4,\"day\":4}}},{\"org\":{\"total\":2},\"tenantId\":\"vic\",\"user\":{\"total\":1,\"active\":{\"week\":0,\"month\":0,\"day\":0},\"create\":{\"week\":0,\"month\":1,\"day\":0},\"login\":{\"week\":0,\"month\":0,\"day\":0}}}]}', 'SUCCESS', 'OK', '2024-08-28 15:54:31', '2024-08-28 15:54:31', NULL, '2024-08-16 11:02:25', 'admin', '2024-08-28 15:54:31', 'linkadmin', 'iam', '0', '查询的数据计算');
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824298431087558657, 'ActionNode', 1824384275437764610, 1824299431487782914, 5, 7, NULL, **********252201473, 1824052833100910593, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824298459807784961\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824298459807784962\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"操作类型\",\"required\":true,\"name\":\"opType\",\"mutability\":\"readWrite\",\"value\":\"SELECT\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824298459807784963\",\"name\":\"sql\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"Sql语句\",\"value\":\"SELECT\\n\\tcount(*) AS num \\nFROM\\n\\tacm_dashboard \\nWHERE\\n\\ttype = \'userorg\' and tenant_id = :tenantId;\",\"required\":true,\"page_control\":\"TEXTAREA\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824298459807784964\",\"name\":\"limit\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"查询结果最大返回行数\",\"value\":\"1\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824298459807784965\",\"name\":\"input\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"输入参数\",\"value\":{\"tenantId\":\"{{N1824384275437764610.current}}\"},\"required\":false,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"自定义Sql操作数据库\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"自定义Sql\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"account\":{\"data_base_url\":\"***************************************************************************************\",\"dbname\":\"acm\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"**********252201473\",\"input\":{\"tenantId\":\"bit\"},\"limit\":1,\"op_type\":\"SELECT\",\"sql\":\"SELECT\\n\\tcount(*) AS num \\nFROM\\n\\tacm_dashboard \\nWHERE\\n\\ttype = \'userorg\' and tenant_id = :tenantId;\"}', '{\"result\":true,\"total\":1,\"data\":[{\"num\":1}]}', 'SUCCESS', 'OK', '2024-08-28 15:54:31', '2024-08-28 15:54:31', NULL, '2024-08-16 12:13:34', 'admin', '2024-08-28 15:54:31', 'admin', 'iam', '0', '查看当前是否存在用户部门模块的Dashboard数据');
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824299431487782914, 'ConditionNode', 1824384275437764610, 1824371883983159298, NULL, NULL, NULL, NULL, 1824052833100910593, NULL, '{\"or_list\":[{\"and_list\":[{\"query\":\"{{N1824298431087558657.data}}[0].num\",\"operator\":\"eq\",\"value\":\"0\"}]}]}', '{\"or_list\":[{\"and_list\":[{\"operator\":\"eq\",\"query\":\"{{N1824298431087558657.data}}[0].num\",\"value\":\"0\"}]}]}', '{\"result\":false}', 'SUCCESS', 'OK', '2024-08-28 15:54:31', '2024-08-28 15:54:31', NULL, '2024-08-16 12:17:33', 'admin', '2024-08-28 15:54:31', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824322002929238017, 'ActionNode', 0, 1824322082881060865, 3, 3, NULL, NULL, 1824052833100910593, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824322028843474945\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824322028843474946\",\"name\":\"vars\",\"description\":\"添加变量名及其初始值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"添加变量\",\"value\":{\"tenantIds\":\"\"},\"required\":true,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"创建一个变量，并设置初始值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"创建变量\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"vars\":{\"tenantIds\":\"\"}}', '{\"vars\":{\"tenantIds\":\"\"}}', 'SUCCESS', 'OK', '2024-08-28 15:54:31', '2024-08-28 15:54:31', NULL, '2024-08-16 13:47:14', 'admin', '2024-08-28 15:54:31', 'admin', 'iam', '0', '接收租户列表');
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824322082881060865, 'ActionNode', 0, 1824384275437764610, 3, 4, NULL, NULL, 1824052833100910593, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824322096459849731\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824322096459849732\",\"name\":\"varPath\",\"description\":\"选择前面步骤已经创建的变量\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"选择要更新的变量\",\"value\":\"{{N1824322002929238017.vars.tenantIds}}\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824322096459849733\",\"name\":\"value\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"设置新的值\",\"value\":\"var userTotal = {{N1824052862737862657.data}};\\nconst tenantIds   = Array.from(new Set(userTotal.map(item => item.tenant_id)));\\nreturn tenantIds;\",\"required\":true,\"page_control\":\"JSON\"}],\"name\":\"root\",\"description\":\"更新变量的值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"更新变量\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"input\":{\"value\":[\"bit\",\"iam\",\"vic\"],\"var_path\":\"N1824322002929238017.vars.tenantIds\"}}', '{\"code\":200,\"msg\":\"OK\",\"value\":[\"bit\",\"iam\",\"vic\"]}', 'SUCCESS', 'OK', '2024-08-28 15:54:31', '2024-08-28 15:54:31', NULL, '2024-08-16 13:47:33', 'admin', '2024-08-28 15:54:31', 'admin', 'iam', '0', '计算出租户列表');
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824322376335540226, 'ActionNode', 1824299431487782914, NULL, 5, 7, NULL, **********252201473, 1824052833100910593, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824322431756705793\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824322431756705794\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"操作类型\",\"required\":true,\"name\":\"opType\",\"mutability\":\"readWrite\",\"value\":\"INSERT\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824322431756705795\",\"name\":\"sql\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"Sql语句\",\"value\":\"INSERT INTO `acm`.`acm_dashboard` (`tenant_id`, `dashboard`, `type`, `update_time`) VALUES ( :tenantId , :content , \'userOrg\', now());\",\"required\":true,\"page_control\":\"TEXTAREA\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824322431756705796\",\"name\":\"limit\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"查询结果最大返回行数\",\"value\":\"2\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824322431756705797\",\"name\":\"input\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"输入参数\",\"value\":{\"tenantId\":\"{{N1824384275437764610.current}}\",\"content\":\"{{N1824323410109841409.value}}\"},\"required\":false,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"自定义Sql操作数据库\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"自定义Sql\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"account\":{\"data_base_url\":\"***************************************************************************************\",\"dbname\":\"acm\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"**********252201473\",\"input\":{\"tenantId\":\"bit\",\"content\":\"{\\\"org\\\":{\\\"total\\\":0},\\\"tenantId\\\":\\\"bit\\\",\\\"user\\\":{\\\"total\\\":0,\\\"active\\\":{\\\"week\\\":0,\\\"month\\\":0,\\\"day\\\":1},\\\"create\\\":{\\\"week\\\":0,\\\"month\\\":0,\\\"day\\\":0},\\\"login\\\":{\\\"week\\\":0,\\\"month\\\":0,\\\"day\\\":0}}}\"},\"limit\":2,\"op_type\":\"INSERT\",\"sql\":\"INSERT INTO `acm`.`acm_dashboard` (`tenant_id`, `dashboard`, `type`, `update_time`) VALUES ( :tenantId , :content , \'userOrg\', now());\"}', '{\"result\":true,\"total\":1,\"data\":null}', 'SUCCESS', 'OK', '2024-08-16 17:56:29', '2024-08-16 17:56:29', NULL, '2024-08-16 13:48:43', 'admin', '2024-08-16 17:56:29', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824323410109841409, 'ActionNode', 1824384275437764610, 1824298431087558657, 3, 4, NULL, NULL, 1824052833100910593, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824323447818461187\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824323447818461188\",\"name\":\"varPath\",\"description\":\"选择前面步骤已经创建的变量\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"选择要更新的变量\",\"value\":\"{{N1824280446583095297.vars.userOrg}}\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824323447818461189\",\"name\":\"value\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"设置新的值\",\"value\":\"var allUserOrg = {{N1824280524316131330.value}};\\nvar tenantId = {{N1824384275437764610.current}};\\n\\nconst allData = {};\\nallUserOrg.forEach(item => {\\n  const tenantId = item.tenantId;\\n  if (!allData[tenantId]) {\\n    allData[tenantId] = [];\\n  }\\n  allData[tenantId].push(item);\\n});\\n\\nreturn JSON.stringify(allData[tenantId][0]);\\n\\n\\n\",\"required\":true,\"page_control\":\"JSON\"}],\"name\":\"root\",\"description\":\"更新变量的值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"更新变量\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"input\":{\"value\":\"{\\\"org\\\":{\\\"total\\\":141},\\\"tenantId\\\":\\\"bit\\\",\\\"user\\\":{\\\"total\\\":5354,\\\"active\\\":{\\\"week\\\":0,\\\"month\\\":2,\\\"day\\\":0},\\\"create\\\":{\\\"week\\\":305,\\\"month\\\":5354,\\\"day\\\":300},\\\"login\\\":{\\\"week\\\":0,\\\"month\\\":5,\\\"day\\\":0}}}\",\"var_path\":\"N1824280446583095297.vars.userOrg\"}}', '{\"code\":200,\"msg\":\"OK\",\"value\":\"{\\\"org\\\":{\\\"total\\\":141},\\\"tenantId\\\":\\\"bit\\\",\\\"user\\\":{\\\"total\\\":5354,\\\"active\\\":{\\\"week\\\":0,\\\"month\\\":2,\\\"day\\\":0},\\\"create\\\":{\\\"week\\\":305,\\\"month\\\":5354,\\\"day\\\":300},\\\"login\\\":{\\\"week\\\":0,\\\"month\\\":5,\\\"day\\\":0}}}\"}', 'SUCCESS', 'OK', '2024-08-28 15:54:31', '2024-08-28 15:54:31', NULL, '2024-08-16 13:52:50', 'admin', '2024-08-28 15:54:31', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824324995405758466, 'ActionNode', 1824371883983159298, 1824325595258339330, 5, 7, NULL, **********252201473, 1824052833100910593, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824325006816092162\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824325006816092163\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"操作类型\",\"required\":true,\"name\":\"opType\",\"mutability\":\"readWrite\",\"value\":\"DELETE\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824325006816092164\",\"name\":\"sql\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"Sql语句\",\"value\":\"DELETE \\nFROM\\n\\tacm_dashboard \\nWHERE\\n\\ttype = \'userOrg\' and tenant_id = :tenantId ;\",\"required\":true,\"page_control\":\"TEXTAREA\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824325006816092165\",\"name\":\"limit\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"查询结果最大返回行数\",\"value\":\"2000\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824325006816092166\",\"name\":\"input\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"输入参数\",\"value\":{\"tenantId\":\"{{N1824384275437764610.current}}\"},\"required\":false,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"自定义Sql操作数据库\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"自定义Sql\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"account\":{\"data_base_url\":\"***************************************************************************************\",\"dbname\":\"acm\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"**********252201473\",\"input\":{\"tenantId\":\"bit\"},\"limit\":2000,\"op_type\":\"DELETE\",\"sql\":\"DELETE \\nFROM\\n\\tacm_dashboard \\nWHERE\\n\\ttype = \'userOrg\' and tenant_id = :tenantId ;\"}', '{\"result\":true,\"total\":1,\"data\":null}', 'SUCCESS', 'OK', '2024-08-28 15:54:31', '2024-08-28 15:54:31', NULL, '2024-08-16 13:59:08', 'admin', '2024-08-28 15:54:31', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824325595258339330, 'ActionNode', 1824371883983159298, NULL, 5, 7, NULL, **********252201473, 1824052833100910593, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824325608719687682\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824325608719687683\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"操作类型\",\"required\":true,\"name\":\"opType\",\"mutability\":\"readWrite\",\"value\":\"INSERT\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824325608719687684\",\"name\":\"sql\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"Sql语句\",\"value\":\"INSERT INTO `acm`.`acm_dashboard` (`tenant_id`, `dashboard`, `type`, `update_time`) VALUES ( :tenantId , :content , \'userOrg\', now());\",\"required\":true,\"page_control\":\"TEXTAREA\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824325608719687685\",\"name\":\"limit\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"查询结果最大返回行数\",\"value\":\"12\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824325608719687686\",\"name\":\"input\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"输入参数\",\"value\":{\"tenantId\":\"{{N1824384275437764610.current}}\",\"content\":\"{{N1824323410109841409.value}}\"},\"required\":false,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"自定义Sql操作数据库\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"自定义Sql\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"account\":{\"data_base_url\":\"***************************************************************************************\",\"dbname\":\"acm\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"**********252201473\",\"input\":{\"tenantId\":\"bit\",\"content\":\"{\\\"org\\\":{\\\"total\\\":141},\\\"tenantId\\\":\\\"bit\\\",\\\"user\\\":{\\\"total\\\":5354,\\\"active\\\":{\\\"week\\\":0,\\\"month\\\":2,\\\"day\\\":0},\\\"create\\\":{\\\"week\\\":305,\\\"month\\\":5354,\\\"day\\\":300},\\\"login\\\":{\\\"week\\\":0,\\\"month\\\":5,\\\"day\\\":0}}}\"},\"limit\":12,\"op_type\":\"INSERT\",\"sql\":\"INSERT INTO `acm`.`acm_dashboard` (`tenant_id`, `dashboard`, `type`, `update_time`) VALUES ( :tenantId , :content , \'userOrg\', now());\"}', '{\"result\":true,\"total\":1,\"data\":null}', 'SUCCESS', 'OK', '2024-08-28 15:54:31', '2024-08-28 15:54:31', NULL, '2024-08-16 14:01:31', 'admin', '2024-08-28 15:54:31', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824371883983159298, 'ConditionNode', 1824384275437764610, NULL, NULL, NULL, NULL, NULL, 1824052833100910593, NULL, '{\"or_list\":[{\"and_list\":[{\"query\":\"{{N1824298431087558657.data}}[0].num\",\"operator\":\"ne\",\"value\":\"0\"}]}]}', '{\"or_list\":[{\"and_list\":[{\"operator\":\"ne\",\"query\":\"{{N1824298431087558657.data}}[0].num\",\"value\":\"0\"}]}]}', '{\"result\":true}', 'SUCCESS', 'OK', '2024-08-28 15:54:31', '2024-08-28 15:54:31', NULL, '2024-08-16 17:05:27', 'admin', '2024-08-28 15:54:31', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824384275437764610, 'LoopNode', 0, 1824052862876274690, NULL, NULL, NULL, NULL, 1824052833100910593, NULL, '{\"type\":\"ARRAY\",\"asc\":true,\"errorExec\":\"CONTINUE\",\"expression\":\"{{N1824322082881060865.value}}\"}', '{\"asc\":true,\"end\":null,\"error_exec\":\"CONTINUE\",\"expression\":\"{{N1824322082881060865.value}}\",\"start\":null,\"type\":\"ARRAY\"}', '{\"current\":\"bit\",\"index\":0,\"type\":\"ARRAY\"}', 'SUCCESS', 'OK', '2024-08-28 15:54:31', '2024-08-28 15:54:31', NULL, '2024-08-16 17:54:41', 'admin', '2024-08-28 15:54:31', 'admin', 'iam', '0', NULL);



-- 应用连接流统计
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824053226480488449, 'StartEventNode', 0, 1824053226555985922, 2, 2, NULL, 1824053226413379586, NULL, '{\"ui_id\":\"1826099237880860674\",\"name\":\"root\",\"type\":\"OBJECT\",\"description\":\"配置crontab，自定义触发时间\",\"display_name\":\"自定义执行\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1826099237880860675\",\"name\":\"startDate\",\"type\":\"STRING\",\"description\":\"定时执行生效的开始日期\",\"display_name\":\"生效开始日期\",\"multi_valued\":false,\"value\":\"2024-08-01\",\"update\":true,\"required\":true,\"page_control\":\"DATE\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1826099237880860676\",\"name\":\"endDate\",\"type\":\"STRING\",\"description\":\"定时执行生效的结束日期\",\"display_name\":\"生效结束日期\",\"multi_valued\":false,\"value\":\"2030-08-31\",\"update\":true,\"required\":true,\"page_control\":\"DATE\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1826099237880860677\",\"name\":\"cycle\",\"type\":\"STRING\",\"description\":\"cron表达式：秒 分 时 天 月 周\",\"display_name\":\"crontab表达式\",\"multi_valued\":false,\"value\":\"* */30 * * * * \",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":true,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', '{\"cycle\":\"* */30 * * * * \",\"end_date\":\"2030-08-31\",\"start_date\":\"2024-08-01\"}', '{\"cycle\":\"* */30 * * * * \",\"end_date\":\"2030-08-31\",\"start_date\":\"2024-08-01\"}', 'SUCCESS', 'OK', '2024-08-21 19:26:14', '2024-08-21 19:26:14', NULL, '2024-08-15 19:59:13', 'admin', '2024-08-21 19:26:14', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824053226555985922, 'ActionNode', 0, 1824269746053758977, 5, 7, 1822874457954340865, 1824053226413379586, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824268951402749953\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824268951402749954\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"操作类型\",\"required\":true,\"name\":\"opType\",\"mutability\":\"readWrite\",\"value\":\"SELECT\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824268951402749955\",\"name\":\"sql\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"Sql语句\",\"value\":\"SELECT sum(t.total_count) activeConnectorCount,\\ntenant_id\\nFROM\\n  (SELECT count(1) total_count,tenant_id\\n   FROM iam_connector\\n   WHERE `status` = 1 \\n\\t GROUP BY tenant_id\\n   UNION ALL SELECT count(1) total_count,tenant_id\\n   FROM iam_push_connector\\n   WHERE `status` = 1 \\n\\t GROUP BY tenant_id) t;\",\"required\":true,\"page_control\":\"TEXTAREA\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824268951402749956\",\"name\":\"limit\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"查询结果最大返回行数\",\"value\":\"2000\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824268951402749957\",\"name\":\"input\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"输入参数\",\"value\":{},\"required\":false,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"自定义Sql操作数据库\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"自定义Sql\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"account\":{\"data_base_url\":\"***************************************************************************************\",\"dbname\":\"iam\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"1822874457954340865\",\"input\":{},\"limit\":2000,\"op_type\":\"SELECT\",\"sql\":\"SELECT sum(t.total_count) activeConnectorCount,\\ntenant_id\\nFROM\\n  (SELECT count(1) total_count,tenant_id\\n   FROM iam_connector\\n   WHERE `status` = 1 \\n\\t GROUP BY tenant_id\\n   UNION ALL SELECT count(1) total_count,tenant_id\\n   FROM iam_push_connector\\n   WHERE `status` = 1 \\n\\t GROUP BY tenant_id) t;\"}', '{\"result\":true,\"total\":1,\"data\":[{\"activeConnectorCount\":1,\"tenant_id\":\"bit\"}]}', 'SUCCESS', 'OK', '2024-08-21 19:26:14', '2024-08-21 19:26:14', NULL, '2024-08-15 19:59:13', 'admin', '2024-08-21 19:26:14', 'admin', 'iam', '0', '获取各租户活跃的集成统计连接器数');
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824053226656649217, 'EndNode', 0, NULL, NULL, NULL, NULL, 1824053226413379586, NULL, NULL, NULL, NULL, 'IDLE', NULL, NULL, NULL, NULL, '2024-08-15 19:59:13', 'admin', '2024-08-15 19:59:13', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824269746053758977, 'ActionNode', 0, 1824271320054415362, 5, 7, **********252201473, 1824053226413379586, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824269762505646081\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824269762505646082\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"操作类型\",\"required\":true,\"name\":\"opType\",\"mutability\":\"readWrite\",\"value\":\"SELECT\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824269762505646083\",\"name\":\"sql\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"Sql语句\",\"value\":\"SELECT\\n\\tcount(1) AS onLineFlows,\\n\\ttenant_id\\nFROM\\n\\tacm_flow_instance \\nWHERE\\n\\top_status = \'ONLINE\' \\n\\tAND template = 0\\nGROUP BY\\n\\ttenant_id\",\"required\":true,\"page_control\":\"TEXTAREA\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824269762505646084\",\"name\":\"limit\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"查询结果最大返回行数\",\"value\":\"2000\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824269762505646085\",\"name\":\"input\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"输入参数\",\"value\":{},\"required\":false,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"自定义Sql操作数据库\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"自定义Sql\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"account\":{\"data_base_url\":\"***************************************************************************************\",\"dbname\":\"acm\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"**********252201473\",\"input\":{},\"limit\":2000,\"op_type\":\"SELECT\",\"sql\":\"SELECT\\n\\tcount(1) AS onLineFlows,\\n\\ttenant_id\\nFROM\\n\\tacm_flow_instance \\nWHERE\\n\\top_status = \'ONLINE\' \\n\\tAND template = 0\\nGROUP BY\\n\\ttenant_id\"}', '{\"result\":true,\"total\":2,\"data\":[{\"tenant_id\":\"iam\",\"onLineFlows\":10},{\"tenant_id\":\"bit\",\"onLineFlows\":1}]}', 'SUCCESS', 'OK', '2024-08-21 19:26:14', '2024-08-21 19:26:14', NULL, '2024-08-16 10:19:35', 'admin', '2024-08-21 19:26:14', 'admin', 'iam', '0', '获取各租户的已上线连接数');
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824271320054415362, 'ActionNode', 0, 1824271751463747585, 5, 7, **********252201473, 1824053226413379586, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824271331389251586\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824271331389251587\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"操作类型\",\"required\":true,\"name\":\"opType\",\"mutability\":\"readWrite\",\"value\":\"SELECT\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824271331389251588\",\"name\":\"sql\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"Sql语句\",\"value\":\"SELECT\\n\\tcount(1) AS excepFlows,\\n\\ttenant_id\\nFROM\\n\\tacm_flow_instance \\nWHERE\\n\\top_status = \'ONLINE\' \\n\\tAND `status` IN (\'FAILED\',\'WARNING\')\\n\\tAND template = 0\\nGROUP BY\\n\\t tenant_id\",\"required\":true,\"page_control\":\"TEXTAREA\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824271331389251589\",\"name\":\"limit\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"查询结果最大返回行数\",\"value\":\"2000\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824271331389251590\",\"name\":\"input\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"输入参数\",\"value\":{},\"required\":false,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"自定义Sql操作数据库\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"自定义Sql\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"account\":{\"data_base_url\":\"***************************************************************************************\",\"dbname\":\"acm\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"**********252201473\",\"input\":{},\"limit\":2000,\"op_type\":\"SELECT\",\"sql\":\"SELECT\\n\\tcount(1) AS excepFlows,\\n\\ttenant_id\\nFROM\\n\\tacm_flow_instance \\nWHERE\\n\\top_status = \'ONLINE\' \\n\\tAND `status` IN (\'FAILED\',\'WARNING\')\\n\\tAND template = 0\\nGROUP BY\\n\\t tenant_id\"}', '{\"result\":true,\"total\":0,\"data\":[]}', 'SUCCESS', 'OK', '2024-08-21 19:26:14', '2024-08-21 19:26:14', NULL, '2024-08-16 10:25:51', 'admin', '2024-08-21 19:26:14', 'admin', 'iam', '0', '获取各租户的异常流数量');
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824271751463747585, 'ActionNode', 0, 1824330560869679105, 5, 7, 1822874457954340865, 1824053226413379586, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824271761548681218\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824271761548681219\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"操作类型\",\"required\":true,\"name\":\"opType\",\"mutability\":\"readWrite\",\"value\":\"SELECT\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824271761548681220\",\"name\":\"sql\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"Sql语句\",\"value\":\"SELECT\\n\\tcount(1) activeApps,\\n\\ttenant_owner\\nFROM\\n\\tiam_app \\nWHERE\\n\\tSTATUS = 1 \\n\\tAND id > 1000\\nGROUP BY \\n\\ttenant_owner\",\"required\":true,\"page_control\":\"TEXTAREA\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824271761548681221\",\"name\":\"limit\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"查询结果最大返回行数\",\"value\":\"2000\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824271761548681222\",\"name\":\"input\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"输入参数\",\"value\":{},\"required\":false,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"自定义Sql操作数据库\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"自定义Sql\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"account\":{\"data_base_url\":\"***************************************************************************************\",\"dbname\":\"iam\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"1822874457954340865\",\"input\":{},\"limit\":2000,\"op_type\":\"SELECT\",\"sql\":\"SELECT\\n\\tcount(1) activeApps,\\n\\ttenant_owner\\nFROM\\n\\tiam_app \\nWHERE\\n\\tSTATUS = 1 \\n\\tAND id > 1000\\nGROUP BY \\n\\ttenant_owner\"}', '{\"result\":true,\"total\":1,\"data\":[{\"tenant_owner\":\"bit\",\"activeApps\":2}]}', 'SUCCESS', 'OK', '2024-08-21 19:26:14', '2024-08-21 19:26:14', NULL, '2024-08-16 10:27:34', 'admin', '2024-08-21 19:26:14', 'admin', 'iam', '0', '获取各租户的系统连接数');
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824328204849430529, 'ActionNode', 0, 1824328314278821890, 3, 3, NULL, 1824053226413379586, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824328229308243969\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824328229308243970\",\"name\":\"vars\",\"description\":\"添加变量名及其初始值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"添加变量\",\"value\":{\"flowApp\":\"\"},\"required\":true,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"创建一个变量，并设置初始值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"创建变量\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"vars\":{\"flowApp\":\"\"}}', '{\"vars\":{\"flowApp\":\"\"}}', 'SUCCESS', 'OK', '2024-08-21 19:26:14', '2024-08-21 19:26:15', NULL, '2024-08-16 14:11:53', 'admin', '2024-08-21 19:26:15', 'admin', 'iam', '0', '接收连接流应用统计信息');
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824328314278821890, 'ActionNode', 0, 1824374467900915713, 3, 4, NULL, 1824053226413379586, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824329546168705028\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824329546168705029\",\"name\":\"varPath\",\"description\":\"选择前面步骤已经创建的变量\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"选择要更新的变量\",\"value\":\"{{N1824328204849430529.vars.flowApp}}\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824329546168705030\",\"name\":\"value\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"设置新的值\",\"value\":\"var activeConnector = {{N1824053226555985922.data}};\\nvar onlineFlows = {{N1824269746053758977.data}};\\nvar exceptionFlows = {{N1824271320054415362.data}};\\nvar apps = {{N1824271751463747585.data}};\\n\\nconst connectorData = {};\\nactiveConnector.forEach(item => {\\n  const tenantId = item.tenant_id;\\n  if (!connectorData[tenantId]) {\\n    connectorData[tenantId] = [];\\n  }\\n  connectorData[tenantId].push(item);\\n});\\n\\nconst onlineFlowData = {};\\nonlineFlows.forEach(item => {\\n  const tenantId = item.tenant_id;\\n  if (!onlineFlowData[tenantId]) {\\n    onlineFlowData[tenantId] = [];\\n  }\\n  onlineFlowData[tenantId].push(item);\\n});\\n\\nconst exceptionData = {};\\nexceptionFlows.forEach(item => {\\n  const tenantId = item.tenant_id;\\n  if (!exceptionData[tenantId]) {\\n    exceptionData[tenantId] = [];\\n  }\\n  exceptionData[tenantId].push(item);\\n});\\n\\nconst appData = {};\\napps.forEach(item => {\\n  const tenantId = item.tenant_owner;\\n  if (!appData[tenantId]) {\\n    appData[tenantId] = [];\\n  }\\n  appData[tenantId].push(item);\\n});\\n\\nvar resultArr = [];\\nfor (let tenantId of {{N1824335947773689858.value}}) {\\n  const link = {\\n    active: Number(connectorData[tenantId]?.[0]?.activeConnectorCount) || 0,\\n    system: Number(appData[tenantId]?.[0]?.activeApps) || 0,\\n    flow: Number(onlineFlowData[tenantId]?.[0]?.onLineFlows) || 0,\\n    expection: Number(exceptionData[tenantId]?.[0]?.excepFlows) || 0,\\n    tenantId: tenantId\\n  };\\n\\n  resultArr.push(link);\\n}\\n\\nreturn resultArr;\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\",\"required\":true,\"page_control\":\"JSON\"}],\"name\":\"root\",\"description\":\"更新变量的值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"更新变量\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"input\":{\"value\":[{\"system\":0,\"expection\":0,\"tenantId\":\"admin\",\"active\":0,\"flow\":0},{\"system\":2,\"expection\":0,\"tenantId\":\"bit\",\"active\":1,\"flow\":1},{\"system\":0,\"expection\":0,\"tenantId\":\"iam\",\"active\":0,\"flow\":10}],\"var_path\":\"N1824328204849430529.vars.flowApp\"}}', '{\"code\":200,\"msg\":\"OK\",\"value\":[{\"system\":0,\"expection\":0,\"tenantId\":\"admin\",\"active\":0,\"flow\":0},{\"system\":2,\"expection\":0,\"tenantId\":\"bit\",\"active\":1,\"flow\":1},{\"system\":0,\"expection\":0,\"tenantId\":\"iam\",\"active\":0,\"flow\":10}]}', 'SUCCESS', 'OK', '2024-08-21 19:26:15', '2024-08-21 19:26:15', NULL, '2024-08-16 14:12:19', 'admin', '2024-08-21 19:26:15', 'admin', 'iam', '0', '计算连接流应用统计信息');
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824330560869679105, 'ActionNode', 0, 1824335862121807873, 5, 7, 1822874457954340865, 1824053226413379586, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824330577032159233\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824330577032159234\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"操作类型\",\"required\":true,\"name\":\"opType\",\"mutability\":\"readWrite\",\"value\":\"SELECT\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824330577032159235\",\"name\":\"sql\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"Sql语句\",\"value\":\"select tenant_id from iam_sys_tenant;\",\"required\":true,\"page_control\":\"TEXTAREA\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824330577032159236\",\"name\":\"limit\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"查询结果最大返回行数\",\"value\":\"1000\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824330577032159237\",\"name\":\"input\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"输入参数\",\"value\":{},\"required\":false,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"自定义Sql操作数据库\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"自定义Sql\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"account\":{\"data_base_url\":\"***************************************************************************************\",\"dbname\":\"iam\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"1822874457954340865\",\"input\":{},\"limit\":1000,\"op_type\":\"SELECT\",\"sql\":\"select tenant_id from iam_sys_tenant;\"}', '{\"result\":true,\"total\":3,\"data\":[{\"tenant_id\":\"admin\"},{\"tenant_id\":\"bit\"},{\"tenant_id\":\"iam\"}]}', 'SUCCESS', 'OK', '2024-08-21 19:26:14', '2024-08-21 19:26:14', NULL, '2024-08-16 14:21:15', 'admin', '2024-08-21 19:26:14', 'admin', 'iam', '0', '获取所有租户ID');
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824335862121807873, 'ActionNode', 0, 1824335947773689858, 3, 3, NULL, 1824053226413379586, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824335870784872449\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824335870784872450\",\"name\":\"vars\",\"description\":\"添加变量名及其初始值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"添加变量\",\"value\":{\"allTenant\":\"\"},\"required\":true,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"创建一个变量，并设置初始值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"创建变量\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"vars\":{\"allTenant\":\"\"}}', '{\"vars\":{\"allTenant\":\"\"}}', 'SUCCESS', 'OK', '2024-08-21 19:26:14', '2024-08-21 19:26:14', NULL, '2024-08-16 14:42:19', 'admin', '2024-08-21 19:26:14', 'admin', 'iam', '0', '接收所有租户信息');
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824335947773689858, 'ActionNode', 0, 1824328204849430529, 3, 4, NULL, 1824053226413379586, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824335955807608836\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824335955807608837\",\"name\":\"varPath\",\"description\":\"选择前面步骤已经创建的变量\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"选择要更新的变量\",\"value\":\"{{N1824335862121807873.vars.allTenant}}\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824335955807608838\",\"name\":\"value\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"设置新的值\",\"value\":\"var allTenant = {{N1824330560869679105.data}};\\n\\nconst tenantIds   = Array.from(new Set(allTenant.map(item => item.tenant_id)));\\n\\nreturn tenantIds;\",\"required\":true,\"page_control\":\"JSON\"}],\"name\":\"root\",\"description\":\"更新变量的值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"更新变量\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"input\":{\"value\":[\"admin\",\"bit\",\"iam\"],\"var_path\":\"N1824335862121807873.vars.allTenant\"}}', '{\"code\":200,\"msg\":\"OK\",\"value\":[\"admin\",\"bit\",\"iam\"]}', 'SUCCESS', 'OK', '2024-08-21 19:26:14', '2024-08-21 19:26:14', NULL, '2024-08-16 14:42:39', 'admin', '2024-08-21 19:26:14', 'admin', 'iam', '0', '计算租户列表');
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824336670171250690, 'ActionNode', 1824374467900915713, 1824336792565235714, 5, 7, **********252201473, 1824053226413379586, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824336680340070401\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824336680340070402\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"操作类型\",\"required\":true,\"name\":\"opType\",\"mutability\":\"readWrite\",\"value\":\"SELECT\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824336680340070403\",\"name\":\"sql\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"Sql语句\",\"value\":\"SELECT\\n\\tcount(*) AS num \\nFROM\\n\\tacm_dashboard \\nWHERE\\n\\ttype = \'flowApp\' and tenant_id = :tenantId ;\",\"required\":true,\"page_control\":\"TEXTAREA\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824336680340070404\",\"name\":\"limit\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"查询结果最大返回行数\",\"value\":\"5\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824336680340070405\",\"name\":\"input\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"输入参数\",\"value\":{\"tenantId\":\"{{N1824374467900915713.current}}\"},\"required\":false,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"自定义Sql操作数据库\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"自定义Sql\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"account\":{\"data_base_url\":\"***************************************************************************************\",\"dbname\":\"acm\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"**********252201473\",\"input\":{\"tenantId\":\"admin\"},\"limit\":5,\"op_type\":\"SELECT\",\"sql\":\"SELECT\\n\\tcount(*) AS num \\nFROM\\n\\tacm_dashboard \\nWHERE\\n\\ttype = \'flowApp\' and tenant_id = :tenantId ;\"}', '{\"result\":true,\"total\":1,\"data\":[{\"num\":1}]}', 'SUCCESS', 'OK', '2024-08-21 19:26:15', '2024-08-21 19:26:15', NULL, '2024-08-16 14:45:31', 'admin', '2024-08-21 19:26:15', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824336792565235714, 'ConditionNode', 1824374467900915713, 1824375245701038081, NULL, NULL, NULL, 1824053226413379586, NULL, '{\"or_list\":[{\"and_list\":[{\"query\":\"{{N1824336670171250690.data}}[0].num\",\"operator\":\"eq\",\"value\":\"0\"}]}]}', '{\"or_list\":[{\"and_list\":[{\"operator\":\"eq\",\"query\":\"{{N1824336670171250690.data}}[0].num\",\"value\":\"0\"}]}]}', '{\"result\":false}', 'SUCCESS', 'OK', '2024-08-21 19:26:15', '2024-08-21 19:26:15', NULL, '2024-08-16 14:46:01', 'admin', '2024-08-21 19:26:15', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824337106202705921, 'ActionNode', 1824374467900915713, 1824336670171250690, 3, 4, NULL, 1824053226413379586, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824337118640644099\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824337118640644100\",\"name\":\"varPath\",\"description\":\"选择前面步骤已经创建的变量\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"选择要更新的变量\",\"value\":\"{{N1824328204849430529.vars.flowApp}}\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824337118640644101\",\"name\":\"value\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"设置新的值\",\"value\":\"var flowAppData = {{N1824328314278821890.value}};\\nvar tenantId = {{N1824374467900915713.current}};\\n\\nconst allData = {};\\nflowAppData.forEach(item => {\\n  const tenantId = item.tenantId;\\n  if (!allData[tenantId]) {\\n    allData[tenantId] = [];\\n  }\\n  allData[tenantId].push(item);\\n});\\n\\nreturn JSON.stringify(allData[tenantId][0]);\\n\",\"required\":true,\"page_control\":\"JSON\"}],\"name\":\"root\",\"description\":\"更新变量的值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"更新变量\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"input\":{\"value\":\"{\\\"system\\\":0,\\\"expection\\\":0,\\\"tenantId\\\":\\\"admin\\\",\\\"active\\\":0,\\\"flow\\\":0}\",\"var_path\":\"N1824328204849430529.vars.flowApp\"}}', '{\"code\":200,\"msg\":\"OK\",\"value\":\"{\\\"system\\\":0,\\\"expection\\\":0,\\\"tenantId\\\":\\\"admin\\\",\\\"active\\\":0,\\\"flow\\\":0}\"}', 'SUCCESS', 'OK', '2024-08-21 19:26:15', '2024-08-21 19:26:15', NULL, '2024-08-16 14:47:15', 'admin', '2024-08-21 19:26:15', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824337490757468162, 'ActionNode', 1824336792565235714, NULL, 5, 7, **********252201473, 1824053226413379586, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824337508203405314\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824337508203405315\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"操作类型\",\"required\":true,\"name\":\"opType\",\"mutability\":\"readWrite\",\"value\":\"INSERT\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824337508203405316\",\"name\":\"sql\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"Sql语句\",\"value\":\"INSERT INTO `acm`.`acm_dashboard` (`tenant_id`, `dashboard`, `type`, `update_time`) VALUES ( :tenantId , :content , \'flowApp\', now());\",\"required\":true,\"page_control\":\"TEXTAREA\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824337508203405317\",\"name\":\"limit\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"查询结果最大返回行数\",\"value\":\"2\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824337508203405318\",\"name\":\"input\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"输入参数\",\"value\":{\"tenantId\":\"{{N1824374467900915713.current}}\",\"content\":\"{{N1824337106202705921.value}}\"},\"required\":false,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"自定义Sql操作数据库\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"自定义Sql\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"account\":{\"data_base_url\":\"***************************************************************************************\",\"dbname\":\"acm\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"**********252201473\",\"input\":{\"tenantId\":\"admin\",\"content\":\"{\\\"system\\\":0,\\\"expection\\\":0,\\\"tenantId\\\":\\\"admin\\\",\\\"active\\\":0,\\\"flow\\\":0}\"},\"limit\":2,\"op_type\":\"INSERT\",\"sql\":\"INSERT INTO `acm`.`acm_dashboard` (`tenant_id`, `dashboard`, `type`, `update_time`) VALUES ( :tenantId , :content , \'flowApp\', now());\"}', '{\"result\":\"Duplicate entry \'admin-flowApp\' for key \'acm_dashboard.PRIMARY\'\"}', 'FAILED', 'Duplicate entry \'admin-flowApp\' for key \'acm_dashboard.PRIMA...', '2024-08-21 11:20:49', '2024-08-21 11:20:49', NULL, '2024-08-16 14:48:47', 'admin', '2024-08-21 11:20:49', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824338674343919618, 'ActionNode', 1824375245701038081, 1824338951369310209, 5, 7, **********252201473, 1824053226413379586, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824338687914319873\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824338687914319874\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"操作类型\",\"required\":true,\"name\":\"opType\",\"mutability\":\"readWrite\",\"value\":\"DELETE\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824338687914319875\",\"name\":\"sql\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"Sql语句\",\"value\":\"DELETE \\nFROM\\n\\tacm_dashboard \\nWHERE\\n\\ttype = \'flowApp\' and tenant_id = :tenantId\",\"required\":true,\"page_control\":\"TEXTAREA\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824338687914319876\",\"name\":\"limit\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"查询结果最大返回行数\",\"value\":\"12\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824338687914319877\",\"name\":\"input\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"输入参数\",\"value\":{\"tenantId\":\"{{N1824374467900915713.current}}\"},\"required\":false,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"自定义Sql操作数据库\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"自定义Sql\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"account\":{\"data_base_url\":\"***************************************************************************************\",\"dbname\":\"acm\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"**********252201473\",\"input\":{\"tenantId\":\"admin\"},\"limit\":12,\"op_type\":\"DELETE\",\"sql\":\"DELETE \\nFROM\\n\\tacm_dashboard \\nWHERE\\n\\ttype = \'flowApp\' and tenant_id = :tenantId\"}', '{\"result\":true,\"total\":1,\"data\":null}', 'SUCCESS', 'OK', '2024-08-21 19:26:15', '2024-08-21 19:26:15', NULL, '2024-08-16 14:53:29', 'admin', '2024-08-21 19:26:15', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824338951369310209, 'ActionNode', 1824375245701038081, NULL, 5, 7, 1822874457954340865, 1824053226413379586, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824338964260233218\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824338964260233219\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"操作类型\",\"required\":true,\"name\":\"opType\",\"mutability\":\"readWrite\",\"value\":\"INSERT\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824338964260233220\",\"name\":\"sql\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"Sql语句\",\"value\":\"INSERT INTO `acm`.`acm_dashboard` (`tenant_id`, `dashboard`, `type`, `update_time`) VALUES ( :tenantId , :content , \'flowApp\', now());\",\"required\":true,\"page_control\":\"TEXTAREA\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824338964260233221\",\"name\":\"limit\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"查询结果最大返回行数\",\"value\":\"45\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824338964260233222\",\"name\":\"input\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"输入参数\",\"value\":{\"tenantId\":\"{{N1824374467900915713.current}}\",\"content\":\"{{N1824337106202705921.value}}\"},\"required\":false,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"自定义Sql操作数据库\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"自定义Sql\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"account\":{\"data_base_url\":\"***************************************************************************************\",\"dbname\":\"iam\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"1822874457954340865\",\"input\":{\"tenantId\":\"admin\",\"content\":\"{\\\"system\\\":0,\\\"expection\\\":0,\\\"tenantId\\\":\\\"admin\\\",\\\"active\\\":0,\\\"flow\\\":0}\"},\"limit\":45,\"op_type\":\"INSERT\",\"sql\":\"INSERT INTO `acm`.`acm_dashboard` (`tenant_id`, `dashboard`, `type`, `update_time`) VALUES ( :tenantId , :content , \'flowApp\', now());\"}', '{\"result\":true,\"total\":1,\"data\":null}', 'SUCCESS', 'OK', '2024-08-21 19:26:15', '2024-08-21 19:26:15', NULL, '2024-08-16 14:54:35', 'admin', '2024-08-21 19:26:15', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824374467900915713, 'LoopNode', 0, 1824053226656649217, NULL, NULL, NULL, 1824053226413379586, NULL, '{\"type\":\"ARRAY\",\"asc\":true,\"errorExec\":\"CONTINUE\",\"expression\":\"{{N1824335947773689858.value}}\"}', '{\"asc\":true,\"end\":null,\"error_exec\":\"CONTINUE\",\"expression\":\"{{N1824335947773689858.value}}\",\"start\":null,\"type\":\"ARRAY\"}', '{\"current\":\"admin\",\"index\":0,\"type\":\"ARRAY\"}', 'SUCCESS', 'OK', '2024-08-21 19:26:15', '2024-08-21 19:26:15', NULL, '2024-08-16 17:15:43', 'admin', '2024-08-21 19:26:15', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824375245701038081, 'ConditionNode', 1824374467900915713, NULL, NULL, NULL, NULL, 1824053226413379586, NULL, '{\"or_list\":[{\"and_list\":[{\"query\":\"{{N1824336670171250690.data}}[0].num\",\"operator\":\"ne\",\"value\":\"0\"}]}]}', '{\"or_list\":[{\"and_list\":[{\"operator\":\"ne\",\"query\":\"{{N1824336670171250690.data}}[0].num\",\"value\":\"0\"}]}]}', '{\"result\":true}', 'SUCCESS', 'OK', '2024-08-21 19:26:15', '2024-08-21 19:26:15', NULL, '2024-08-16 17:18:48', 'admin', '2024-08-21 19:26:15', 'admin', 'iam', '0', NULL);



-- Dashboard用户状态统计图
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824265087188647937, 'StartEventNode', 0, 1824265087343837186, 2, 2, NULL, NULL, 1824265087104761857, NULL, '{\"ui_id\":\"1824407386675388418\",\"name\":\"root\",\"type\":\"OBJECT\",\"description\":\"配置crontab，自定义触发时间\",\"display_name\":\"自定义执行\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1824407386675388419\",\"name\":\"startDate\",\"type\":\"STRING\",\"description\":\"定时执行生效的开始日期\",\"display_name\":\"生效开始日期\",\"multi_valued\":false,\"value\":\"2024-08-01\",\"update\":true,\"required\":true,\"page_control\":\"DATE\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824407386675388420\",\"name\":\"endDate\",\"type\":\"STRING\",\"description\":\"定时执行生效的结束日期\",\"display_name\":\"生效结束日期\",\"multi_valued\":false,\"value\":\"2029-08-31\",\"update\":true,\"required\":true,\"page_control\":\"DATE\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824407386675388421\",\"name\":\"cycle\",\"type\":\"STRING\",\"description\":\"cron表达式：秒 分 时 天 月 周\",\"display_name\":\"crontab表达式\",\"multi_valued\":false,\"value\":\"* */30 * * * *\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":true,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', '{\"cycle\":\"* */30 * * * *\",\"end_date\":\"2029-08-31\",\"start_date\":\"2024-08-01\"}', '{\"cycle\":\"* */30 * * * *\",\"end_date\":\"2029-08-31\",\"start_date\":\"2024-08-01\"}', 'SUCCESS', 'OK', '2024-08-29 21:23:45', '2024-08-29 21:23:45', NULL, '2024-08-16 10:01:05', 'admin', '2024-08-29 21:23:45', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824265087343837186, 'ActionNode', 0, 1824265778800017409, 5, 7, 'sql', 1822874457954340865, 1824265087104761857, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824265292099002370\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824265292099002371\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"操作类型\",\"required\":true,\"name\":\"opType\",\"mutability\":\"readWrite\",\"value\":\"SELECT\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824265292099002372\",\"name\":\"sql\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"Sql语句\",\"value\":\"SELECT\\n\\tcount(*) AS suspendUsers,\\n\\ttenant_id\\nFROM\\n\\tiam_user \\nWHERE\\n\\tSTATUS = 0\\n         AND username != \'admin\'\\nGROUP BY\\n\\ttenant_id\",\"required\":true,\"page_control\":\"TEXTAREA\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824265292099002373\",\"name\":\"limit\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"查询结果最大返回行数\",\"value\":\"2000\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824265292099002374\",\"name\":\"input\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"输入参数\",\"value\":{},\"required\":false,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"自定义Sql操作数据库\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"自定义Sql\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"account\":{\"data_base_url\":\"jdbc:mysql://*************:3306/iam?useUnicode=true&useSSL=false&characterEncoding=utf8\",\"dbname\":\"iam\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"1822874457954340865\",\"input\":{},\"limit\":2000,\"op_type\":\"SELECT\",\"sql\":\"SELECT\\n\\tcount(*) AS suspendUsers,\\n\\ttenant_id\\nFROM\\n\\tiam_user \\nWHERE\\n\\tSTATUS = 0\\n         AND username != \'admin\'\\nGROUP BY\\n\\ttenant_id\"}', '{\"result\":true,\"total\":0,\"data\":[]}', 'SUCCESS', 'OK', '2024-08-29 21:23:45', '2024-08-29 21:23:45', NULL, '2024-08-16 10:01:05', 'admin', '2024-08-29 21:23:45', 'admin', 'iam', '0', '获取各租户禁用用户数');
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824265087536775169, 'EndNode', 0, NULL, NULL, NULL, NULL, NULL, 1824265087104761857, NULL, NULL, NULL, NULL, 'IDLE', NULL, NULL, NULL, NULL, '2024-08-16 10:01:05', 'admin', '2024-08-16 10:01:05', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824265778800017409, 'ActionNode', 0, 1824266047780732930, 5, 7, 'sql', 1822874457954340865, 1824265087104761857, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824265791896461313\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824265791896461314\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"操作类型\",\"required\":true,\"name\":\"opType\",\"mutability\":\"readWrite\",\"value\":\"SELECT\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824265791896461315\",\"name\":\"sql\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"Sql语句\",\"value\":\"SELECT\\n\\tcount(*) AS noActiveUsers ,\\n\\tu.tenant_id\\nFROM\\n\\tiam_user u\\n\\tLEFT JOIN iam_user_status us ON u.id = us.id \\nWHERE\\n\\t(\\n\\t\\tus.login IS NULL \\n\\t\\tAND u.STATUS != 2 \\n                AND u.username != \'admin\'\\n\\t\\tAND (\\n\\t\\t\\tu.end_date = \'\' \\n\\t\\t\\tOR u.end_date IS NULL \\n\\t\\t OR u.end_date >= DATE_FORMAT( NOW(), \'%Y-%m-%d\' ))\\n\\t)\\n\\tGROUP BY\\n\\t\\tu.tenant_id\",\"required\":true,\"page_control\":\"TEXTAREA\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824265791896461316\",\"name\":\"limit\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"查询结果最大返回行数\",\"value\":\"2000\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824265791896461317\",\"name\":\"input\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"输入参数\",\"value\":{},\"required\":false,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"自定义Sql操作数据库\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"自定义Sql\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"account\":{\"data_base_url\":\"jdbc:mysql://*************:3306/iam?useUnicode=true&useSSL=false&characterEncoding=utf8\",\"dbname\":\"iam\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"1822874457954340865\",\"input\":{},\"limit\":2000,\"op_type\":\"SELECT\",\"sql\":\"SELECT\\n\\tcount(*) AS noActiveUsers ,\\n\\tu.tenant_id\\nFROM\\n\\tiam_user u\\n\\tLEFT JOIN iam_user_status us ON u.id = us.id \\nWHERE\\n\\t(\\n\\t\\tus.login IS NULL \\n\\t\\tAND u.STATUS != 2 \\n                AND u.username != \'admin\'\\n\\t\\tAND (\\n\\t\\t\\tu.end_date = \'\' \\n\\t\\t\\tOR u.end_date IS NULL \\n\\t\\t OR u.end_date >= DATE_FORMAT( NOW(), \'%Y-%m-%d\' ))\\n\\t)\\n\\tGROUP BY\\n\\t\\tu.tenant_id\"}', '{\"result\":true,\"total\":2,\"data\":[{\"noActiveUsers\":374,\"tenant_id\":\"bit\"},{\"noActiveUsers\":296,\"tenant_id\":\"hao\"}]}', 'SUCCESS', 'OK', '2024-08-29 21:23:45', '2024-08-29 21:23:45', NULL, '2024-08-16 10:03:50', 'admin', '2024-08-29 21:23:45', 'admin', 'iam', '0', '获取各租户未激活用户数');
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824266047780732930, 'ActionNode', 0, 1824267167982862337, 5, 7, 'sql', 1822874457954340865, 1824265087104761857, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824266248077352962\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824266248077352963\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"操作类型\",\"required\":true,\"name\":\"opType\",\"mutability\":\"readWrite\",\"value\":\"SELECT\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824266248077352964\",\"name\":\"sql\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"Sql语句\",\"value\":\"SELECT\\n\\tcount(*) AS passwdExpireUsers,\\n\\ttenant_id \\nFROM\\n\\tiam_user \\nWHERE\\n\\tusername != \'admin\' \\n\\tAND TIMESTAMPDIFF( DAY, now(), DATE ( pwd_expiration_time )) <= 7 \\nGROUP BY\\n\\ttenant_id\",\"required\":true,\"page_control\":\"TEXTAREA\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824266248077352965\",\"name\":\"limit\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"查询结果最大返回行数\",\"value\":\"2000\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824266248077352966\",\"name\":\"input\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"输入参数\",\"value\":{},\"required\":false,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"自定义Sql操作数据库\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"自定义Sql\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"account\":{\"data_base_url\":\"jdbc:mysql://*************:3306/iam?useUnicode=true&useSSL=false&characterEncoding=utf8\",\"dbname\":\"iam\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"1822874457954340865\",\"input\":{},\"limit\":2000,\"op_type\":\"SELECT\",\"sql\":\"SELECT\\n\\tcount(*) AS passwdExpireUsers,\\n\\ttenant_id \\nFROM\\n\\tiam_user \\nWHERE\\n\\tusername != \'admin\' \\n\\tAND TIMESTAMPDIFF( DAY, now(), DATE ( pwd_expiration_time )) <= 7 \\nGROUP BY\\n\\ttenant_id\"}', '{\"result\":true,\"total\":0,\"data\":[]}', 'SUCCESS', 'OK', '2024-08-29 21:23:45', '2024-08-29 21:23:45', NULL, '2024-08-16 10:04:54', 'admin', '2024-08-29 21:23:45', 'admin', 'iam', '0', '获取各密码即将过期数');
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824267167982862337, 'ActionNode', 0, 1824267808646021122, 5, 7, 'sql', 1822874457954340865, 1824265087104761857, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824267179414167553\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824267179414167554\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"操作类型\",\"required\":true,\"name\":\"opType\",\"mutability\":\"readWrite\",\"value\":\"SELECT\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824267179414167555\",\"name\":\"sql\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"Sql语句\",\"value\":\"SELECT\\n\\tSUM(JSON_EXTRACT( cs.stats, \'$.to_delete_limit\' )) AS toDeleteUsers,\\n\\tc.tenant_id\\nFROM\\n\\tiam_push_connector c\\n\\tLEFT JOIN iam_connector_push_history cs ON c.push_batch_no = cs.batch_no \\n\\tAND c.id = cs.connector_id \\n\\tAND c.`status` = 1 \\nGROUP BY\\n\\tc.tenant_id\",\"required\":true,\"page_control\":\"TEXTAREA\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824267179414167556\",\"name\":\"limit\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"查询结果最大返回行数\",\"value\":\"2000\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824267179414167557\",\"name\":\"input\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"输入参数\",\"value\":{},\"required\":false,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"自定义Sql操作数据库\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"自定义Sql\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"account\":{\"data_base_url\":\"jdbc:mysql://*************:3306/iam?useUnicode=true&useSSL=false&characterEncoding=utf8\",\"dbname\":\"iam\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"1822874457954340865\",\"input\":{},\"limit\":2000,\"op_type\":\"SELECT\",\"sql\":\"SELECT\\n\\tSUM(JSON_EXTRACT( cs.stats, \'$.to_delete_limit\' )) AS toDeleteUsers,\\n\\tc.tenant_id\\nFROM\\n\\tiam_push_connector c\\n\\tLEFT JOIN iam_connector_push_history cs ON c.push_batch_no = cs.batch_no \\n\\tAND c.id = cs.connector_id \\n\\tAND c.`status` = 1 \\nGROUP BY\\n\\tc.tenant_id\"}', '{\"result\":true,\"total\":1,\"data\":[{\"tenant_id\":\"bit\",\"toDeleteUsers\":null}]}', 'SUCCESS', 'OK', '2024-08-29 21:23:46', '2024-08-29 21:23:46', NULL, '2024-08-16 10:09:21', 'admin', '2024-08-29 21:23:46', 'admin', 'iam', '0', '获取各租户待删除数');
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824267808646021122, 'ActionNode', 0, 1824268444246654977, 5, 7, 'sql', 1822874457954340865, 1824265087104761857, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824267844349763586\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824267844349763587\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"操作类型\",\"required\":true,\"name\":\"opType\",\"mutability\":\"readWrite\",\"value\":\"SELECT\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824267844349763588\",\"name\":\"sql\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"Sql语句\",\"value\":\"select count(operator) AS lockUsers,tenant_id from (\\nSELECT\\n\\tDISTINCT operator,\\n\\ttenant_id\\nFROM\\n\\t(\\n\\t\\tSELECT\\n\\t\\t\\toperator,\\n\\t\\t\\terror_code,\\n\\t\\t\\toccur_time,\\n\\t\\t\\t@row_number := IF(@prev_operator = operator AND @prev_value = error_code, @row_number + 1, 1) AS row_num,\\n\\t\\t\\t@prev_operator := operator,\\n\\t\\t\\t@prev_value := error_code,\\n\\t\\t\\ttenant_id\\n\\t\\tFROM\\n\\t\\t\\tiam_auditlog\\n\\t\\tWHERE\\n\\t\\t error_code IN (\'1010200\', \'0\')\\n                  AND operator not in (\'admin\', \'linkadmin\')\\n\\t\\tORDER BY\\n\\t\\t\\toperator, occur_time,tenant_id\\n\\t) sub\\nWHERE\\n\\trow_num >= 5 AND error_code = \'1010200\'\\n\\n) res group BY res.tenant_id\",\"required\":true,\"page_control\":\"TEXTAREA\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824267844349763589\",\"name\":\"limit\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"查询结果最大返回行数\",\"value\":\"2000\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824267844349763590\",\"name\":\"input\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"输入参数\",\"value\":{},\"required\":false,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"自定义Sql操作数据库\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"自定义Sql\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"account\":{\"data_base_url\":\"jdbc:mysql://*************:3306/iam?useUnicode=true&useSSL=false&characterEncoding=utf8\",\"dbname\":\"iam\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"1822874457954340865\",\"input\":{},\"limit\":2000,\"op_type\":\"SELECT\",\"sql\":\"select count(operator) AS lockUsers,tenant_id from (\\nSELECT\\n\\tDISTINCT operator,\\n\\ttenant_id\\nFROM\\n\\t(\\n\\t\\tSELECT\\n\\t\\t\\toperator,\\n\\t\\t\\terror_code,\\n\\t\\t\\toccur_time,\\n\\t\\t\\t@row_number := IF(@prev_operator = operator AND @prev_value = error_code, @row_number + 1, 1) AS row_num,\\n\\t\\t\\t@prev_operator := operator,\\n\\t\\t\\t@prev_value := error_code,\\n\\t\\t\\ttenant_id\\n\\t\\tFROM\\n\\t\\t\\tiam_auditlog\\n\\t\\tWHERE\\n\\t\\t error_code IN (\'1010200\', \'0\')\\n                  AND operator not in (\'admin\', \'linkadmin\')\\n\\t\\tORDER BY\\n\\t\\t\\toperator, occur_time,tenant_id\\n\\t) sub\\nWHERE\\n\\trow_num >= 5 AND error_code = \'1010200\'\\n\\n) res group BY res.tenant_id\"}', '{\"result\":true,\"total\":1,\"data\":[{\"tenant_id\":\"bit\",\"lockUsers\":1}]}', 'SUCCESS', 'OK', '2024-08-29 21:23:46', '2024-08-29 21:23:46', NULL, '2024-08-16 10:11:53', 'admin', '2024-08-29 21:23:46', 'admin', 'iam', '0', '获取各租户锁定用户数');
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824268444246654977, 'ActionNode', 0, 1824397014101049346, 5, 7, 'sql', 1822874457954340865, 1824265087104761857, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824268454054764545\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824268454054764546\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"操作类型\",\"required\":true,\"name\":\"opType\",\"mutability\":\"readWrite\",\"value\":\"SELECT\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824268454054764547\",\"name\":\"sql\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"Sql语句\",\"value\":\"SELECT\\n\\tcount(DISTINCT operator ) AS passwdErrorUsers,\\n\\ttenant_id\\nFROM\\n\\tiam_auditlog \\nWHERE\\n\\terror_code = 1010200\\n        AND operator not in (\'admin\', \'linkadmin\')\\nGROUP BY\\n\\ttenant_id\",\"required\":true,\"page_control\":\"TEXTAREA\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824268454054764548\",\"name\":\"limit\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"查询结果最大返回行数\",\"value\":\"2000\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824268454054764549\",\"name\":\"input\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"输入参数\",\"value\":{},\"required\":false,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"自定义Sql操作数据库\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"自定义Sql\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"account\":{\"data_base_url\":\"jdbc:mysql://*************:3306/iam?useUnicode=true&useSSL=false&characterEncoding=utf8\",\"dbname\":\"iam\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"1822874457954340865\",\"input\":{},\"limit\":2000,\"op_type\":\"SELECT\",\"sql\":\"SELECT\\n\\tcount(DISTINCT operator ) AS passwdErrorUsers,\\n\\ttenant_id\\nFROM\\n\\tiam_auditlog \\nWHERE\\n\\terror_code = 1010200\\n        AND operator not in (\'admin\', \'linkadmin\')\\nGROUP BY\\n\\ttenant_id\"}', '{\"result\":true,\"total\":2,\"data\":[{\"passwdErrorUsers\":3,\"tenant_id\":\"bit\"},{\"passwdErrorUsers\":1,\"tenant_id\":\"hao\"}]}', 'SUCCESS', 'OK', '2024-08-29 21:23:46', '2024-08-29 21:23:46', NULL, '2024-08-16 10:14:25', 'admin', '2024-08-29 21:23:46', 'admin', 'iam', '0', '获取各租户密码连续错误');
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824339697947033602, 'ActionNode', 0, 1824340099471949826, 5, 7, 'sql', 1822874457954340865, 1824265087104761857, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824339710305280002\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824339710305280003\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"操作类型\",\"required\":true,\"name\":\"opType\",\"mutability\":\"readWrite\",\"value\":\"SELECT\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824339710305280004\",\"name\":\"sql\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"Sql语句\",\"value\":\"select tenant_id from iam_sys_tenant;\",\"required\":true,\"page_control\":\"TEXTAREA\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824339710305280005\",\"name\":\"limit\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"查询结果最大返回行数\",\"value\":\"1000\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824339710305280006\",\"name\":\"input\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"输入参数\",\"value\":{},\"required\":false,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"自定义Sql操作数据库\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"自定义Sql\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"account\":{\"data_base_url\":\"jdbc:mysql://*************:3306/iam?useUnicode=true&useSSL=false&characterEncoding=utf8\",\"dbname\":\"iam\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"1822874457954340865\",\"input\":{},\"limit\":1000,\"op_type\":\"SELECT\",\"sql\":\"select tenant_id from iam_sys_tenant;\"}', '{\"result\":true,\"total\":4,\"data\":[{\"tenant_id\":\"bit\"},{\"tenant_id\":\"hao\"},{\"tenant_id\":\"iam\"},{\"tenant_id\":\"vic\"}]}', 'SUCCESS', 'OK', '2024-08-29 21:23:46', '2024-08-29 21:23:46', NULL, '2024-08-16 14:57:33', 'admin', '2024-08-29 21:23:46', 'admin', 'iam', '0', '获取所有租户信息');
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824340099471949826, 'ActionNode', 0, 1824340152869634050, 3, 3, NULL, NULL, 1824265087104761857, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824340109498163202\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824340109498163203\",\"name\":\"vars\",\"description\":\"添加变量名及其初始值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"添加变量\",\"value\":{\"allTenant\":\"\"},\"required\":true,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"创建一个变量，并设置初始值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"创建变量\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"vars\":{\"allTenant\":\"\"}}', '{\"vars\":{\"allTenant\":\"\"}}', 'SUCCESS', 'OK', '2024-08-29 21:23:46', '2024-08-29 21:23:46', NULL, '2024-08-16 14:59:09', 'admin', '2024-08-29 21:23:46', 'admin', 'iam', '0', '接收所有租户信息');
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824340152869634050, 'ActionNode', 0, 1824340328166375425, 3, 4, NULL, NULL, 1824265087104761857, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824340161868242947\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824340161868242948\",\"name\":\"varPath\",\"description\":\"选择前面步骤已经创建的变量\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"选择要更新的变量\",\"value\":\"{{N1824340099471949826.vars.allTenant}}\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824340161868242949\",\"name\":\"value\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"设置新的值\",\"value\":\"var allTenant = {{N1824339697947033602.data}};\\n\\nconst tenantIds   = Array.from(new Set(allTenant.map(item => item.tenant_id)));\\n\\nreturn tenantIds;\",\"required\":true,\"page_control\":\"JSON\"}],\"name\":\"root\",\"description\":\"更新变量的值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"更新变量\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"input\":{\"value\":[\"bit\",\"hao\",\"iam\",\"vic\"],\"var_path\":\"N1824340099471949826.vars.allTenant\"}}', '{\"code\":200,\"msg\":\"OK\",\"value\":[\"bit\",\"hao\",\"iam\",\"vic\"]}', 'SUCCESS', 'OK', '2024-08-29 21:23:46', '2024-08-29 21:23:46', NULL, '2024-08-16 14:59:22', 'admin', '2024-08-29 21:23:46', 'admin', 'iam', '0', '计算租户列表');
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824340328166375425, 'ActionNode', 0, 1824340450451308545, 3, 3, NULL, NULL, 1824265087104761857, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824340355846414338\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824340355846414339\",\"name\":\"vars\",\"description\":\"添加变量名及其初始值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"添加变量\",\"value\":{\"userStatis\":\"\"},\"required\":true,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"创建一个变量，并设置初始值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"创建变量\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"vars\":{\"userStatis\":\"\"}}', '{\"vars\":{\"userStatis\":\"\"}}', 'SUCCESS', 'OK', '2024-08-29 21:23:46', '2024-08-29 21:23:46', NULL, '2024-08-16 15:00:03', 'admin', '2024-08-29 21:23:46', 'admin', 'iam', '0', '接收统计信息');
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824340450451308545, 'ActionNode', 0, 1824347965729718274, 3, 4, 'set_var', NULL, 1824265087104761857, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824340462004248579\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824340462004248580\",\"name\":\"varPath\",\"description\":\"选择前面步骤已经创建的变量\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"选择要更新的变量\",\"value\":\"{{N1824340328166375425.vars.userStatis}}\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824340462004248581\",\"name\":\"value\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"设置新的值\",\"value\":\"var suspendUser = {{N1824265087343837186.data}};\\nvar noActiveUser = {{N1824265778800017409.data}};\\nvar passwdExpire = {{N1824266047780732930.data}};\\nvar toDeleteUser = {{N1824267167982862337.data}};\\nvar lockUser = {{N1824267808646021122.data}};\\nvar passwdError = {{N1824268444246654977.data}};\\nvar loginUser = {{N1824397014101049346.data}};\\nvar allUser = {{N1824403362062712834.data}};\\n\\nconst suspendUserData = {};\\nsuspendUser.forEach(item => {\\n  const tenantId = item.tenant_id;\\n  if (!suspendUserData[tenantId]) {\\n    suspendUserData[tenantId] = [];\\n  }\\n  suspendUserData[tenantId].push(item);\\n});\\n\\nconst noactiveTData = {};\\nnoActiveUser.forEach(item => {\\n  const tenantId = item.tenant_id;\\n  if (!noactiveTData[tenantId]) {\\n    noactiveTData[tenantId] = [];\\n  }\\n  noactiveTData[tenantId].push(item);\\n});\\n\\nconst passwdExpireData = {};\\npasswdExpire.forEach(item => {\\n  const tenantId = item.tenant_id;\\n  if (!passwdExpireData[tenantId]) {\\n    passwdExpireData[tenantId] = [];\\n  }\\n  passwdExpireData[tenantId].push(item);\\n});\\n\\nconst toDeleteData = {};\\ntoDeleteUser.forEach(item => {\\n  const tenantId = item.tenant_id;\\n  if (!toDeleteData[tenantId]) {\\n    toDeleteData[tenantId] = [];\\n  }\\n  toDeleteData[tenantId].push(item);\\n});\\n\\nconst lockData = {};\\nlockUser.forEach(item => {\\n  const tenantId = item.tenant_id;\\n  if (!lockData[tenantId]) {\\n    lockData[tenantId] = [];\\n  }\\n  lockData[tenantId].push(item);\\n});\\n\\nconst passwdErrorData = {};\\npasswdError.forEach(item => {\\n  const tenantId = item.tenant_id;\\n  if (!passwdErrorData[tenantId]) {\\n    passwdErrorData[tenantId] = [];\\n  }\\n  passwdErrorData[tenantId].push(item);\\n});\\n\\nconst loginUserData = {};\\nloginUser.forEach(item => {\\n  const tenantId = item.tenant_id;\\n  if (!loginUserData[tenantId]) {\\n    loginUserData[tenantId] = [];\\n  }\\n  loginUserData[tenantId].push(item);\\n});\\n\\nconst allUserData = {};\\nallUser.forEach(item => {\\n  const tenantId = item.tenant_id;\\n  if (!allUserData[tenantId]) {\\n    allUserData[tenantId] = [];\\n  }\\n  allUserData[tenantId].push(item);\\n});\\n\\nvar resultArr = [];\\nfor (let tenantId of {{N1824340152869634050.value}}) {\\n\\n    const userStatusStatic = [\\n    {\\n      name: \\\"禁用用户数\\\",\\n      value: Number(suspendUserData[tenantId]?.[0]?.suspendUsers) || 0\\n    },\\n    {\\n      name: \\\"锁定用户数\\\",\\n      value: Number(lockData[tenantId]?.[0]?.lockUsers) || 0\\n    },\\n    {\\n      name: \\\"未激活用户数\\\",\\n      value: Number(noactiveTData[tenantId]?.[0]?.noActiveUsers) || 0\\n    },\\n    {\\n      name: \\\"待删除用户数\\\",\\n      value: Number(toDeleteData[tenantId]?.[0]?.toDeleteUsers) || 0\\n    },\\n    {\\n      name: \\\"密码即将过期数\\\",\\n      value: Number(passwdExpireData[tenantId]?.[0]?.passwdExpireUsers) || 0\\n    },\\n    {\\n      name: \\\"连续密码错误用户数\\\",\\n      value: Number(passwdErrorData[tenantId]?.[0]?.passwdErrorUsers) || 0\\n    },\\n    {\\n      name: \\\"不活跃用户数\\\",\\n      value: Number(loginUserData[tenantId]?.[0]?.loginUsers) || 0\\n    }\\n  ];\\n\\n  const res = {\\n    tenantId,\\n    data: userStatusStatic.filter(item => item.value !== 0)\\n  };\\n\\n  resultArr.push(res);\\n}\\n\\nreturn resultArr;\\n\\n\\n\\n\\n\\n\\n\\n\\n\\n\",\"required\":true,\"page_control\":\"JSON\"}],\"name\":\"root\",\"description\":\"更新变量的值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"更新变量\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"input\":{\"value\":[{\"data\":[{\"name\":\"锁定用户数\",\"value\":1},{\"name\":\"未激活用户数\",\"value\":374},{\"name\":\"连续密码错误用户数\",\"value\":3},{\"name\":\"不活跃用户数\",\"value\":13}],\"tenantId\":\"bit\"},{\"data\":[{\"name\":\"未激活用户数\",\"value\":296},{\"name\":\"连续密码错误用户数\",\"value\":1},{\"name\":\"不活跃用户数\",\"value\":2}],\"tenantId\":\"hao\"},{\"data\":[{\"name\":\"不活跃用户数\",\"value\":2}],\"tenantId\":\"iam\"},{\"data\":[{\"name\":\"不活跃用户数\",\"value\":1}],\"tenantId\":\"vic\"}],\"var_path\":\"N1824340328166375425.vars.userStatis\"}}', '{\"code\":200,\"msg\":\"OK\",\"value\":[{\"data\":[{\"name\":\"锁定用户数\",\"value\":1},{\"name\":\"未激活用户数\",\"value\":374},{\"name\":\"连续密码错误用户数\",\"value\":3},{\"name\":\"不活跃用户数\",\"value\":13}],\"tenantId\":\"bit\"},{\"data\":[{\"name\":\"未激活用户数\",\"value\":296},{\"name\":\"连续密码错误用户数\",\"value\":1},{\"name\":\"不活跃用户数\",\"value\":2}],\"tenantId\":\"hao\"},{\"data\":[{\"name\":\"不活跃用户数\",\"value\":2}],\"tenantId\":\"iam\"},{\"data\":[{\"name\":\"不活跃用户数\",\"value\":1}],\"tenantId\":\"vic\"}]}', 'SUCCESS', 'OK', '2024-08-29 21:23:46', '2024-08-29 21:23:46', NULL, '2024-08-16 15:00:33', 'admin', '2024-08-29 21:23:46', 'admin', 'iam', '0', '计算统计信息');
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824347965729718274, 'LoopNode', 0, 1824265087536775169, NULL, NULL, NULL, NULL, 1824265087104761857, NULL, '{\"type\":\"ARRAY\",\"asc\":true,\"errorExec\":\"CONTINUE\",\"expression\":\"{{N1824340152869634050.value}}\"}', '{\"asc\":true,\"end\":null,\"error_exec\":\"CONTINUE\",\"expression\":\"{{N1824340152869634050.value}}\",\"start\":null,\"type\":\"ARRAY\"}', '{\"current\":\"bit\",\"index\":0,\"type\":\"ARRAY\"}', 'SUCCESS', 'OK', '2024-08-29 21:23:46', '2024-08-29 21:23:46', NULL, '2024-08-16 15:30:24', 'admin', '2024-08-29 21:23:46', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824348093203005441, 'ActionNode', 1824347965729718274, 1824348661195653121, 5, 7, NULL, **********252201473, 1824265087104761857, NULL, '{\"ui_id\":\"1824348179901095938\",\"name\":\"root\",\"type\":\"OBJECT\",\"description\":\"自定义Sql操作数据库\",\"display_name\":\"自定义Sql\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1824348179901095939\",\"name\":\"opType\",\"type\":\"STRING\",\"description\":\"\",\"display_name\":\"操作类型\",\"multi_valued\":false,\"value\":\"SELECT\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824348179901095940\",\"name\":\"sql\",\"type\":\"STRING\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"display_name\":\"Sql语句\",\"multi_valued\":false,\"value\":\"SELECT\\n\\tcount(*) AS num \\nFROM\\n\\tacm_dashboard \\nWHERE\\n\\ttype = \'userStatis\' and tenant_id = :tenantId;\",\"update\":true,\"required\":true,\"page_control\":\"TEXTAREA\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824348179901095941\",\"name\":\"limit\",\"type\":\"NUMBER\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"display_name\":\"查询结果最大返回行数\",\"multi_valued\":false,\"value\":\"1\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824348179901095942\",\"name\":\"input\",\"type\":\"OBJECT\",\"description\":\"\",\"display_name\":\"输入参数\",\"multi_valued\":false,\"value\":{\"tenantId\":\"{{N1824347965729718274.current}}\"},\"update\":true,\"required\":false,\"page_control\":\"EXTEND_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":true,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', '{\"account\":{\"data_base_url\":\"***************************************************************************************\",\"dbname\":\"acm\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"**********252201473\",\"input\":{\"tenantId\":\"bit\"},\"limit\":1,\"op_type\":\"SELECT\",\"sql\":\"SELECT\\n\\tcount(*) AS num \\nFROM\\n\\tacm_dashboard \\nWHERE\\n\\ttype = \'userStatis\' and tenant_id = :tenantId;\"}', '{\"result\":true,\"total\":1,\"data\":[{\"num\":1}]}', 'SUCCESS', 'OK', '2024-08-29 21:23:46', '2024-08-29 21:23:46', NULL, '2024-08-16 15:30:55', 'admin', '2024-08-29 21:23:46', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824348454257082370, 'ConditionNode', 1824347965729718274, 1824349498592641026, NULL, NULL, NULL, NULL, 1824265087104761857, NULL, '{\"or_list\":[{\"and_list\":[{\"query\":\"{{N1824348093203005441.data}}[0].num\",\"operator\":\"eq\",\"value\":\"0\"}]}]}', '{\"or_list\":[{\"and_list\":[{\"operator\":\"eq\",\"query\":\"{{N1824348093203005441.data}}[0].num\",\"value\":\"0\"}]}]}', '{\"result\":false}', 'SUCCESS', 'OK', '2024-08-29 21:23:46', '2024-08-29 21:23:46', NULL, '2024-08-16 15:32:21', 'admin', '2024-08-29 21:23:46', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824348661195653121, 'ActionNode', 1824347965729718274, 1824348454257082370, 3, 4, NULL, NULL, 1824265087104761857, NULL, '{\"ui_id\":\"1824348672215916548\",\"name\":\"root\",\"type\":\"OBJECT\",\"description\":\"更新变量的值\",\"display_name\":\"更新变量\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1824348672215916549\",\"name\":\"varPath\",\"type\":\"STRING\",\"description\":\"选择前面步骤已经创建的变量\",\"display_name\":\"选择要更新的变量\",\"multi_valued\":false,\"value\":\"{{N1824340328166375425.vars.userStatis}}\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824348672215916550\",\"name\":\"value\",\"type\":\"OBJECT\",\"description\":\"\",\"display_name\":\"设置新的值\",\"multi_valued\":false,\"value\":\"var userStatis = {{N1824340450451308545.value}};\\nvar tenantId = {{N1824347965729718274.current}};\\n\\nconst allData = {};\\nuserStatis.forEach(item => {\\n  const tenantId = item.tenantId;\\n  if (!allData[tenantId]) {\\n    allData[tenantId] = [];\\n  }\\n  allData[tenantId].push(item);\\n});\\n\\nreturn JSON.stringify(allData[tenantId][0]);\\n\\n\",\"update\":true,\"required\":true,\"page_control\":\"JSON\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":true,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', '{\"input\":{\"value\":\"{\\\"data\\\":[{\\\"name\\\":\\\"锁定用户数\\\",\\\"value\\\":1},{\\\"name\\\":\\\"未激活用户数\\\",\\\"value\\\":374},{\\\"name\\\":\\\"连续密码错误用户数\\\",\\\"value\\\":3},{\\\"name\\\":\\\"不活跃用户数\\\",\\\"value\\\":13}],\\\"tenantId\\\":\\\"bit\\\"}\",\"var_path\":\"N1824340328166375425.vars.userStatis\"}}', '{\"code\":200,\"msg\":\"OK\",\"value\":\"{\\\"data\\\":[{\\\"name\\\":\\\"锁定用户数\\\",\\\"value\\\":1},{\\\"name\\\":\\\"未激活用户数\\\",\\\"value\\\":374},{\\\"name\\\":\\\"连续密码错误用户数\\\",\\\"value\\\":3},{\\\"name\\\":\\\"不活跃用户数\\\",\\\"value\\\":13}],\\\"tenantId\\\":\\\"bit\\\"}\"}', 'SUCCESS', 'OK', '2024-08-29 21:23:46', '2024-08-29 21:23:46', NULL, '2024-08-16 15:33:10', 'admin', '2024-08-29 21:23:46', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824349105569579009, 'ActionNode', 1824348454257082370, NULL, 5, 7, NULL, **********252201473, 1824265087104761857, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824349116145246209\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824349116145246210\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"操作类型\",\"required\":true,\"name\":\"opType\",\"mutability\":\"readWrite\",\"value\":\"INSERT\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824349116145246211\",\"name\":\"sql\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"Sql语句\",\"value\":\"INSERT INTO `acm`.`acm_dashboard` (`tenant_id`, `dashboard`, `type`, `update_time`) VALUES ( :tenantId , :content , \'userStatis\', now());\",\"required\":true,\"page_control\":\"TEXTAREA\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824349116145246212\",\"name\":\"limit\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"查询结果最大返回行数\",\"value\":\"100\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824349116145246213\",\"name\":\"input\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"输入参数\",\"value\":{\"tenantId\":\"{{N1824347965729718274.current}}\",\"content\":\"{{N1824348661195653121.value}}\"},\"required\":false,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"自定义Sql操作数据库\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"自定义Sql\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"account\":{\"data_base_url\":\"***************************************************************************************\",\"dbname\":\"acm\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"**********252201473\",\"input\":{\"tenantId\":\"admin\",\"content\":\"{\\\"data\\\":[{\\\"name\\\":\\\"禁用用户数\\\",\\\"value\\\":0},{\\\"name\\\":\\\"锁定用户数\\\",\\\"valueactive\\\":0},{\\\"name\\\":\\\"未激活用户数\\\",\\\"value\\\":1},{\\\"name\\\":\\\"待删除用户数\\\",\\\"value\\\":0},{\\\"name\\\":\\\"密码即将过期数\\\",\\\"value\\\":0},{\\\"name\\\":\\\"连续密码错误用户数\\\",\\\"value\\\":0},{\\\"name\\\":\\\"不活跃用户数\\\",\\\"value\\\":0}],\\\"tenantId\\\":\\\"admin\\\"}\"},\"limit\":100,\"op_type\":\"INSERT\",\"sql\":\"INSERT INTO `acm`.`acm_dashboard` (`tenant_id`, `dashboard`, `type`, `update_time`) VALUES ( :tenantId , :content , \'userStatis\', now());\"}', '{\"result\":true,\"total\":1,\"data\":null}', 'SUCCESS', 'OK', '2024-08-16 17:28:17', '2024-08-16 17:28:17', NULL, '2024-08-16 15:34:56', 'admin', '2024-08-16 17:28:17', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824349498592641026, 'ConditionNode', 1824347965729718274, NULL, NULL, NULL, NULL, NULL, 1824265087104761857, NULL, '{\"or_list\":[{\"and_list\":[{\"query\":\"{{N1824348093203005441.data}}[0].num\",\"operator\":\"ne\",\"value\":\"0\"}]}]}', '{\"or_list\":[{\"and_list\":[{\"operator\":\"ne\",\"query\":\"{{N1824348093203005441.data}}[0].num\",\"value\":\"0\"}]}]}', '{\"result\":true}', 'SUCCESS', 'OK', '2024-08-29 21:23:46', '2024-08-29 21:23:46', NULL, '2024-08-16 15:36:30', 'admin', '2024-08-29 21:23:46', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824349638959218689, 'ActionNode', 1824349498592641026, 1824350152597880834, 5, 7, NULL, **********252201473, 1824265087104761857, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824349660339412994\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824349660339412995\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"操作类型\",\"required\":true,\"name\":\"opType\",\"mutability\":\"readWrite\",\"value\":\"DELETE\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824349660339412996\",\"name\":\"sql\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"Sql语句\",\"value\":\"DELETE \\nFROM\\n\\tacm_dashboard \\nWHERE\\n\\ttype = \'userStatis\' and tenant_id = :tenantId\",\"required\":true,\"page_control\":\"TEXTAREA\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824349660339412997\",\"name\":\"limit\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"查询结果最大返回行数\",\"value\":\"100\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824349660339412998\",\"name\":\"input\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"输入参数\",\"value\":{\"tenantId\":\"{{N1824347965729718274.current}}\"},\"required\":false,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"自定义Sql操作数据库\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"自定义Sql\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"account\":{\"data_base_url\":\"***************************************************************************************\",\"dbname\":\"acm\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"**********252201473\",\"input\":{\"tenantId\":\"bit\"},\"limit\":100,\"op_type\":\"DELETE\",\"sql\":\"DELETE \\nFROM\\n\\tacm_dashboard \\nWHERE\\n\\ttype = \'userStatis\' and tenant_id = :tenantId\"}', '{\"result\":true,\"total\":1,\"data\":null}', 'SUCCESS', 'OK', '2024-08-29 21:23:46', '2024-08-29 21:23:46', NULL, '2024-08-16 15:37:03', 'admin', '2024-08-29 21:23:46', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824350152597880834, 'ActionNode', 1824349498592641026, NULL, 5, 7, NULL, **********252201473, 1824265087104761857, NULL, '{\"ui_id\":\"1824350186531627009\",\"name\":\"root\",\"type\":\"OBJECT\",\"description\":\"自定义Sql操作数据库\",\"display_name\":\"自定义Sql\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1824350186531627010\",\"name\":\"opType\",\"type\":\"STRING\",\"description\":\"\",\"display_name\":\"操作类型\",\"multi_valued\":false,\"value\":\"INSERT\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824350186531627011\",\"name\":\"sql\",\"type\":\"STRING\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"display_name\":\"Sql语句\",\"multi_valued\":false,\"value\":\"INSERT INTO `acm`.`acm_dashboard` (`tenant_id`, `dashboard`, `type`, `update_time`) VALUES ( :tenantId , :content , \'userStatis\', now());\",\"update\":true,\"required\":true,\"page_control\":\"TEXTAREA\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824350186531627012\",\"name\":\"limit\",\"type\":\"NUMBER\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"display_name\":\"查询结果最大返回行数\",\"multi_valued\":false,\"value\":\"120\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824350186531627013\",\"name\":\"input\",\"type\":\"OBJECT\",\"description\":\"\",\"display_name\":\"输入参数\",\"multi_valued\":false,\"value\":{\"tenantId\":\"{{N1824347965729718274.current}}\",\"content\":\"{{N1824348661195653121.value}}\"},\"update\":true,\"required\":false,\"page_control\":\"EXTEND_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":true,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', '{\"account\":{\"data_base_url\":\"***************************************************************************************\",\"dbname\":\"acm\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"**********252201473\",\"input\":{\"tenantId\":\"bit\",\"content\":\"{\\\"data\\\":[{\\\"name\\\":\\\"锁定用户数\\\",\\\"value\\\":1},{\\\"name\\\":\\\"未激活用户数\\\",\\\"value\\\":374},{\\\"name\\\":\\\"连续密码错误用户数\\\",\\\"value\\\":3},{\\\"name\\\":\\\"不活跃用户数\\\",\\\"value\\\":13}],\\\"tenantId\\\":\\\"bit\\\"}\"},\"limit\":120,\"op_type\":\"INSERT\",\"sql\":\"INSERT INTO `acm`.`acm_dashboard` (`tenant_id`, `dashboard`, `type`, `update_time`) VALUES ( :tenantId , :content , \'userStatis\', now());\"}', '{\"result\":true,\"total\":1,\"data\":null}', 'SUCCESS', 'OK', '2024-08-29 21:23:46', '2024-08-29 21:23:46', NULL, '2024-08-16 15:39:06', 'admin', '2024-08-29 21:23:46', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824397014101049346, 'ActionNode', 0, 1824403362062712834, 5, 7, 'sql', 1822874457954340865, 1824265087104761857, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824397023556837378\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824397023556837379\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"操作类型\",\"required\":true,\"name\":\"opType\",\"mutability\":\"readWrite\",\"value\":\"SELECT\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824397023556837380\",\"name\":\"sql\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"Sql语句\",\"value\":\"SELECT\\n\\tcount( DISTINCT operator ) AS loginUsers,\\n\\ttenant_id\\nFROM\\n\\tiam_auditlog \\nWHERE\\n\\tTIMESTAMPDIFF( DAY, now(), DATE ( occur_time )) <= 30 \\n\\tAND event_subtype in (101,104)\\nGROUP BY\\n\\ttenant_id\",\"required\":true,\"page_control\":\"TEXTAREA\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824397023556837381\",\"name\":\"limit\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"查询结果最大返回行数\",\"value\":\"2000\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824397023556837382\",\"name\":\"input\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"输入参数\",\"value\":{},\"required\":false,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"自定义Sql操作数据库\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"自定义Sql\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"account\":{\"data_base_url\":\"jdbc:mysql://*************:3306/iam?useUnicode=true&useSSL=false&characterEncoding=utf8\",\"dbname\":\"iam\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"1822874457954340865\",\"input\":{},\"limit\":2000,\"op_type\":\"SELECT\",\"sql\":\"SELECT\\n\\tcount( DISTINCT operator ) AS loginUsers,\\n\\ttenant_id\\nFROM\\n\\tiam_auditlog \\nWHERE\\n\\tTIMESTAMPDIFF( DAY, now(), DATE ( occur_time )) <= 30 \\n\\tAND event_subtype in (101,104)\\nGROUP BY\\n\\ttenant_id\"}', '{\"result\":true,\"total\":4,\"data\":[{\"loginUsers\":13,\"tenant_id\":\"bit\"},{\"loginUsers\":2,\"tenant_id\":\"hao\"},{\"loginUsers\":2,\"tenant_id\":\"iam\"},{\"loginUsers\":1,\"tenant_id\":\"vic\"}]}', 'SUCCESS', 'OK', '2024-08-29 21:23:46', '2024-08-29 21:23:46', NULL, '2024-08-16 18:45:18', 'admin', '2024-08-29 21:23:46', 'admin', 'iam', '0', '获取登录用户数');
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824403362062712834, 'ActionNode', 0, 1824339697947033602, 5, 7, 'sql', 1822874457954340865, 1824265087104761857, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824403377524744193\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824403377524744194\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"操作类型\",\"required\":true,\"name\":\"opType\",\"mutability\":\"readWrite\",\"value\":\"SELECT\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824403377524744195\",\"name\":\"sql\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"Sql语句\",\"value\":\"select count(*) as users,tenant_id from iam_user where username !=\'admin\' group by tenant_id; \",\"required\":true,\"page_control\":\"TEXTAREA\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824403377524744196\",\"name\":\"limit\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"查询结果最大返回行数\",\"value\":\"2000\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824403377524744197\",\"name\":\"input\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"输入参数\",\"value\":{},\"required\":false,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"自定义Sql操作数据库\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"自定义Sql\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"account\":{\"data_base_url\":\"jdbc:mysql://*************:3306/iam?useUnicode=true&useSSL=false&characterEncoding=utf8\",\"dbname\":\"iam\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"1822874457954340865\",\"input\":{},\"limit\":2000,\"op_type\":\"SELECT\",\"sql\":\"select count(*) as users,tenant_id from iam_user where username !=\'admin\' group by tenant_id; \"}', '{\"result\":true,\"total\":4,\"data\":[{\"tenant_id\":\"bit\",\"users\":380},{\"tenant_id\":\"hao\",\"users\":298},{\"tenant_id\":\"iam\",\"users\":1},{\"tenant_id\":\"vic\",\"users\":1}]}', 'SUCCESS', 'OK', '2024-08-29 21:23:46', '2024-08-29 21:23:46', NULL, '2024-08-16 19:10:32', 'admin', '2024-08-29 21:23:46', 'admin', 'iam', '0', '获取用户数');


-- 用户登录趋势
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824272013385449474, 'StartEventNode', 0, 1824272014308196353, 2, 2, NULL, 1824272013301563394, NULL, '{\"ui_id\":\"1826099006485303297\",\"name\":\"root\",\"type\":\"OBJECT\",\"description\":\"配置crontab，自定义触发时间\",\"display_name\":\"自定义执行\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1826099006485303298\",\"name\":\"startDate\",\"type\":\"STRING\",\"description\":\"定时执行生效的开始日期\",\"display_name\":\"生效开始日期\",\"multi_valued\":false,\"value\":\"2024-08-01\",\"update\":true,\"required\":true,\"page_control\":\"DATE\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1826099006485303299\",\"name\":\"endDate\",\"type\":\"STRING\",\"description\":\"定时执行生效的结束日期\",\"display_name\":\"生效结束日期\",\"multi_valued\":false,\"value\":\"2029-08-31\",\"update\":true,\"required\":true,\"page_control\":\"DATE\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1826099006485303300\",\"name\":\"cycle\",\"type\":\"STRING\",\"description\":\"cron表达式：秒 分 时 天 月 周\",\"display_name\":\"crontab表达式\",\"multi_valued\":false,\"value\":\"* */30 * * * * \",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":true,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', '{\"cycle\":\"* */30 * * * * \",\"end_date\":\"2029-08-31\",\"start_date\":\"2024-08-01\"}', '{\"cycle\":\"* */30 * * * * \",\"end_date\":\"2029-08-31\",\"start_date\":\"2024-08-01\"}', 'SUCCESS', 'OK', '2024-08-21 19:50:43', '2024-08-21 19:50:43', NULL, '2024-08-16 10:28:36', 'admin', '2024-08-21 19:50:43', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824272014308196353, 'ActionNode', 0, 1824277090368143362, 5, 7, 1822874457954340865, 1824272013301563394, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824272825735880706\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824272825735880707\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"操作类型\",\"required\":true,\"name\":\"opType\",\"mutability\":\"readWrite\",\"value\":\"SELECT\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824272825735880708\",\"name\":\"sql\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"Sql语句\",\"value\":\"SELECT\\n    COUNT(DISTINCT operator) AS loginUsers,\\n    COUNT(*) AS loginCount,\\n    DATE(occur_time) AS loginTime,\\n    tenant_id\\nFROM\\n    iam_auditlog\\nWHERE\\n    event_type = 1\\n    and operator != \'admin\'\\n    and event_subtype in (101,104)\\n    and TIMESTAMPDIFF( DAY, now(), DATE ( occur_time )) <= 7\\nGROUP BY\\n    DATE(occur_time),\\n    tenant_id\",\"required\":true,\"page_control\":\"TEXTAREA\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824272825735880709\",\"name\":\"limit\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"查询结果最大返回行数\",\"value\":\"14000\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824272825735880710\",\"name\":\"input\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"输入参数\",\"value\":{},\"required\":false,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"自定义Sql操作数据库\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"自定义Sql\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"account\":{\"data_base_url\":\"***************************************************************************************\",\"dbname\":\"iam\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"1822874457954340865\",\"input\":{},\"limit\":14000,\"op_type\":\"SELECT\",\"sql\":\"SELECT\\n    COUNT(DISTINCT operator) AS loginUsers,\\n    COUNT(*) AS loginCount,\\n    DATE(occur_time) AS loginTime,\\n    tenant_id\\nFROM\\n    iam_auditlog\\nWHERE\\n    event_type = 1\\n    and operator != \'admin\'\\n    and event_subtype in (101,104)\\n    and TIMESTAMPDIFF( DAY, now(), DATE ( occur_time )) <= 7\\nGROUP BY\\n    DATE(occur_time),\\n    tenant_id\"}', '{\"result\":true,\"total\":2,\"data\":[{\"loginUsers\":2,\"tenant_id\":\"bit\",\"loginTime\":\"2024-08-12\",\"loginCount\":2},{\"loginUsers\":2,\"tenant_id\":\"bit\",\"loginTime\":\"2024-08-13\",\"loginCount\":13}]}', 'SUCCESS', 'OK', '2024-08-21 19:50:43', '2024-08-21 19:50:43', NULL, '2024-08-16 10:28:36', 'admin', '2024-08-21 19:50:43', 'admin', 'iam', '0', '获取近7天各租户的登录人数和次数');
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824272014522105857, 'EndNode', 0, NULL, NULL, NULL, NULL, 1824272013301563394, NULL, NULL, NULL, NULL, 'IDLE', NULL, NULL, NULL, NULL, '2024-08-16 10:28:36', 'admin', '2024-08-16 10:28:36', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824277090368143362, 'ActionNode', 0, 1824351371257102338, 5, 7, 1822874457954340865, 1824272013301563394, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824277108292231169\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824277108292231170\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"操作类型\",\"required\":true,\"name\":\"opType\",\"mutability\":\"readWrite\",\"value\":\"SELECT\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824277108292231171\",\"name\":\"sql\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"Sql语句\",\"value\":\"SELECT\\n    COUNT(DISTINCT operator) AS authUsers,\\n    COUNT(*) AS authCount,\\n     DATE(occur_time) AS authTime,\\n     tenant_id\\nFROM\\n    iam_auditlog\\nWHERE\\n    event_type = 1\\n    and operator != \'admin\'\\n    and event_subtype in (103,104)\\n    and TIMESTAMPDIFF( DAY, now(), DATE ( occur_time )) <= 7 \\nGROUP BY\\n\\tDATE(occur_time),\\n    tenant_id\",\"required\":true,\"page_control\":\"TEXTAREA\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824277108292231172\",\"name\":\"limit\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"查询结果最大返回行数\",\"value\":\"14000\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824277108292231173\",\"name\":\"input\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"输入参数\",\"value\":{},\"required\":false,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"自定义Sql操作数据库\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"自定义Sql\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"account\":{\"data_base_url\":\"***************************************************************************************\",\"dbname\":\"iam\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"1822874457954340865\",\"input\":{},\"limit\":14000,\"op_type\":\"SELECT\",\"sql\":\"SELECT\\n    COUNT(DISTINCT operator) AS authUsers,\\n    COUNT(*) AS authCount,\\n     DATE(occur_time) AS authTime,\\n     tenant_id\\nFROM\\n    iam_auditlog\\nWHERE\\n    event_type = 1\\n    and operator != \'admin\'\\n    and event_subtype in (103,104)\\n    and TIMESTAMPDIFF( DAY, now(), DATE ( occur_time )) <= 7 \\nGROUP BY\\n\\tDATE(occur_time),\\n    tenant_id\"}', '{\"result\":true,\"total\":2,\"data\":[{\"tenant_id\":\"bit\",\"authCount\":4,\"authTime\":\"2024-08-12\",\"authUsers\":2},{\"tenant_id\":\"bit\",\"authCount\":9,\"authTime\":\"2024-08-13\",\"authUsers\":1}]}', 'SUCCESS', 'OK', '2024-08-21 19:50:43', '2024-08-21 19:50:43', NULL, '2024-08-16 10:48:46', 'admin', '2024-08-21 19:50:43', 'admin', 'iam', '0', '获取近7天各租户的授权人数和次数');
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824351371257102338, 'ActionNode', 0, 1824351610730889217, 5, 7, 1822874457954340865, 1824272013301563394, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824351386207428609\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824351386207428610\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"操作类型\",\"required\":true,\"name\":\"opType\",\"mutability\":\"readWrite\",\"value\":\"SELECT\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824351386207428611\",\"name\":\"sql\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"Sql语句\",\"value\":\"select tenant_id from iam_sys_tenant;\",\"required\":true,\"page_control\":\"TEXTAREA\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824351386207428612\",\"name\":\"limit\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"查询结果最大返回行数\",\"value\":\"1000\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824351386207428613\",\"name\":\"input\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"输入参数\",\"value\":{},\"required\":false,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"自定义Sql操作数据库\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"自定义Sql\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"account\":{\"data_base_url\":\"***************************************************************************************\",\"dbname\":\"iam\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"1822874457954340865\",\"input\":{},\"limit\":1000,\"op_type\":\"SELECT\",\"sql\":\"select tenant_id from iam_sys_tenant;\"}', '{\"result\":true,\"total\":3,\"data\":[{\"tenant_id\":\"admin\"},{\"tenant_id\":\"bit\"},{\"tenant_id\":\"iam\"}]}', 'SUCCESS', 'OK', '2024-08-21 19:50:43', '2024-08-21 19:50:43', NULL, '2024-08-16 15:43:56', 'admin', '2024-08-21 19:50:43', 'admin', 'iam', '0', '获取所有的租户');
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824351610730889217, 'ActionNode', 0, 1824351677856530433, 3, 3, NULL, 1824272013301563394, NULL, '{\"ui_id\":\"1824351623596646401\",\"name\":\"root\",\"type\":\"OBJECT\",\"description\":\"创建一个变量，并设置初始值\",\"display_name\":\"创建变量\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1824351623596646402\",\"name\":\"vars\",\"type\":\"OBJECT\",\"description\":\"添加变量名及其初始值\",\"display_name\":\"添加变量\",\"multi_valued\":false,\"value\":{\"allTenant\":\"\"},\"update\":true,\"required\":true,\"page_control\":\"EXTEND_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":true,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', '{\"vars\":{\"allTenant\":\"\"}}', '{\"vars\":{\"allTenant\":\"\"}}', 'SUCCESS', 'OK', '2024-08-21 19:50:43', '2024-08-21 19:50:43', NULL, '2024-08-16 15:44:53', 'admin', '2024-08-21 19:50:43', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824351677856530433, 'ActionNode', 0, 1824351835755298817, 3, 4, NULL, 1824272013301563394, NULL, '{\"ui_id\":\"1824351688637718531\",\"name\":\"root\",\"type\":\"OBJECT\",\"description\":\"更新变量的值\",\"display_name\":\"更新变量\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1824351688637718532\",\"name\":\"varPath\",\"type\":\"STRING\",\"description\":\"选择前面步骤已经创建的变量\",\"display_name\":\"选择要更新的变量\",\"multi_valued\":false,\"value\":\"{{N1824351610730889217.vars.allTenant}}\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824351688637718533\",\"name\":\"value\",\"type\":\"OBJECT\",\"description\":\"\",\"display_name\":\"设置新的值\",\"multi_valued\":false,\"value\":\"var allTenant = {{N1824351371257102338.data}};\\n\\nconst tenantIds   = Array.from(new Set(allTenant.map(item => item.tenant_id)));\\n\\nreturn tenantIds;\",\"update\":true,\"required\":true,\"page_control\":\"JSON\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":true,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', '{\"input\":{\"value\":[\"admin\",\"bit\",\"iam\"],\"var_path\":\"N1824351610730889217.vars.allTenant\"}}', '{\"code\":200,\"msg\":\"OK\",\"value\":[\"admin\",\"bit\",\"iam\"]}', 'SUCCESS', 'OK', '2024-08-21 19:50:44', '2024-08-21 19:50:44', NULL, '2024-08-16 15:45:09', 'admin', '2024-08-21 19:50:44', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824351835755298817, 'ActionNode', 0, 1824352095340773378, 3, 3, NULL, 1824272013301563394, NULL, '{\"ui_id\":\"1824351846012198914\",\"name\":\"root\",\"type\":\"OBJECT\",\"description\":\"创建一个变量，并设置初始值\",\"display_name\":\"创建变量\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1824351846012198915\",\"name\":\"vars\",\"type\":\"OBJECT\",\"description\":\"添加变量名及其初始值\",\"display_name\":\"添加变量\",\"multi_valued\":false,\"value\":{\"authLogin\":\"\"},\"update\":true,\"required\":true,\"page_control\":\"EXTEND_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":true,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', '{\"vars\":{\"authLogin\":\"\"}}', '{\"vars\":{\"authLogin\":\"\"}}', 'SUCCESS', 'OK', '2024-08-21 19:50:44', '2024-08-21 19:50:44', NULL, '2024-08-16 15:45:47', 'admin', '2024-08-21 19:50:44', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824352095340773378, 'ActionNode', 0, 1824356916781301762, 3, 4, NULL, 1824272013301563394, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824352104775589891\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824352104775589892\",\"name\":\"varPath\",\"description\":\"选择前面步骤已经创建的变量\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"选择要更新的变量\",\"value\":\"{{N1824351835755298817.vars.authLogin}}\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824352104775589893\",\"name\":\"value\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"设置新的值\",\"value\":\"var login = {{N1824272014308196353.data}};\\nvar auth = {{N1824277090368143362.data}};\\n\\nconst loginData = {};\\nlogin.forEach(item => {\\n  const tenantId = item.tenant_id;\\n  if (!loginData[tenantId]) {\\n    loginData[tenantId] = [];\\n  }\\n  loginData[tenantId].push(item);\\n});\\n\\nconst authData = {};\\nauth.forEach(item => {\\n  const tenantId = item.tenant_id;\\n  if (!authData[tenantId]) {\\n    authData[tenantId] = [];\\n  }\\n  authData[tenantId].push(item);\\n});\\n\\nlet dates = [];\\nfor (let i = 6; i >= 0; i--) {\\n  let date = new Date();\\n  date.setDate(date.getDate() - i);\\n  dates.push(date.toISOString().split(\'T\')[0]);\\n}\\n\\nvar resultArr = [];\\nfor (let tenantId of {{N1824351677856530433.value}}) {\\n  const loginDataByTime = {};\\n  const authDataByTime = {};\\n\\n  if (loginData[tenantId]) {\\n    loginData[tenantId].forEach(item => {\\n      const { loginTime, loginUsers, loginCount } = item;\\n      loginDataByTime[loginTime] = { loginUsers, loginCount };\\n    });\\n  }\\n\\n  if (authData[tenantId]) {\\n    authData[tenantId].forEach(item => {\\n      const { authTime, authUsers, authCount } = item;\\n      authDataByTime[authTime] = { authUsers, authCount };\\n    });\\n  }\\n\\n  const loginUsers = [];\\n  const loginCounts = [];\\n  const authUsers = [];\\n  const authCounts = [];\\n\\n  dates.forEach(date => {\\n    loginUsers.push(Number(loginDataByTime[date]?.loginUsers) || 0);\\n    loginCounts.push(Number(loginDataByTime[date]?.loginCount) || 0);\\n    authUsers.push(Number(authDataByTime[date]?.authUsers) || 0);\\n    authCounts.push(Number(authDataByTime[date]?.authCount) || 0);\\n  });\\n\\n  const res = {\\n    tenantId,\\n    data: [\\n      { name: \'登录人数\', data: loginUsers },\\n      { name: \'登录次数\', data: loginCounts },\\n      { name: \'授权人数\', data: authUsers },\\n      { name: \'授权次数\', data: authCounts }\\n    ]\\n  };\\n\\n  resultArr.push(res);\\n}\\n\\nreturn resultArr;\\n\",\"required\":true,\"page_control\":\"JSON\"}],\"name\":\"root\",\"description\":\"更新变量的值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"更新变量\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"input\":{\"value\":[{\"data\":[{\"data\":[0,0,0,0,0,0,0],\"name\":\"登录人数\"},{\"data\":[0,0,0,0,0,0,0],\"name\":\"登录次数\"},{\"data\":[0,0,0,0,0,0,0],\"name\":\"授权人数\"},{\"data\":[0,0,0,0,0,0,0],\"name\":\"授权次数\"}],\"tenantId\":\"admin\"},{\"data\":[{\"data\":[0,0,0,0,0,0,0],\"name\":\"登录人数\"},{\"data\":[0,0,0,0,0,0,0],\"name\":\"登录次数\"},{\"data\":[0,0,0,0,0,0,0],\"name\":\"授权人数\"},{\"data\":[0,0,0,0,0,0,0],\"name\":\"授权次数\"}],\"tenantId\":\"bit\"},{\"data\":[{\"data\":[0,0,0,0,0,0,0],\"name\":\"登录人数\"},{\"data\":[0,0,0,0,0,0,0],\"name\":\"登录次数\"},{\"data\":[0,0,0,0,0,0,0],\"name\":\"授权人数\"},{\"data\":[0,0,0,0,0,0,0],\"name\":\"授权次数\"}],\"tenantId\":\"iam\"}],\"var_path\":\"N1824351835755298817.vars.authLogin\"}}', '{\"code\":200,\"msg\":\"OK\",\"value\":[{\"data\":[{\"data\":[0,0,0,0,0,0,0],\"name\":\"登录人数\"},{\"data\":[0,0,0,0,0,0,0],\"name\":\"登录次数\"},{\"data\":[0,0,0,0,0,0,0],\"name\":\"授权人数\"},{\"data\":[0,0,0,0,0,0,0],\"name\":\"授权次数\"}],\"tenantId\":\"admin\"},{\"data\":[{\"data\":[0,0,0,0,0,0,0],\"name\":\"登录人数\"},{\"data\":[0,0,0,0,0,0,0],\"name\":\"登录次数\"},{\"data\":[0,0,0,0,0,0,0],\"name\":\"授权人数\"},{\"data\":[0,0,0,0,0,0,0],\"name\":\"授权次数\"}],\"tenantId\":\"bit\"},{\"data\":[{\"data\":[0,0,0,0,0,0,0],\"name\":\"登录人数\"},{\"data\":[0,0,0,0,0,0,0],\"name\":\"登录次数\"},{\"data\":[0,0,0,0,0,0,0],\"name\":\"授权人数\"},{\"data\":[0,0,0,0,0,0,0],\"name\":\"授权次数\"}],\"tenantId\":\"iam\"}]}', 'SUCCESS', 'OK', '2024-08-21 19:50:44', '2024-08-21 19:50:44', NULL, '2024-08-16 15:46:49', 'admin', '2024-08-21 19:50:44', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824356916781301762, 'LoopNode', 0, 1824272014522105857, NULL, NULL, NULL, 1824272013301563394, NULL, '{\"type\":\"ARRAY\",\"asc\":true,\"errorExec\":\"CONTINUE\",\"expression\":\"{{N1824351677856530433.value}}\"}', '{\"asc\":true,\"end\":null,\"error_exec\":\"CONTINUE\",\"expression\":\"{{N1824351677856530433.value}}\",\"start\":null,\"type\":\"ARRAY\"}', '{\"current\":\"admin\",\"index\":0,\"type\":\"ARRAY\"}', 'SUCCESS', 'OK', '2024-08-21 19:50:44', '2024-08-21 19:50:44', NULL, '2024-08-16 16:05:59', 'admin', '2024-08-21 19:50:44', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824356994224930817, 'ActionNode', 1824356916781301762, 1824357480122466306, 5, 7, **********252201473, 1824272013301563394, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824357298439626754\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824357298439626755\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"操作类型\",\"required\":true,\"name\":\"opType\",\"mutability\":\"readWrite\",\"value\":\"SELECT\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824357298439626756\",\"name\":\"sql\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"Sql语句\",\"value\":\"SELECT\\n\\tcount(*) AS num \\nFROM\\n\\tacm_dashboard \\nWHERE\\n\\ttype = \'authLogin\' and tenant_id = :tenantId;\",\"required\":true,\"page_control\":\"TEXTAREA\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824357298439626757\",\"name\":\"limit\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"查询结果最大返回行数\",\"value\":\"10\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824357298439626758\",\"name\":\"input\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"输入参数\",\"value\":{\"tenantId\":\"{{N1824356916781301762.current}}\"},\"required\":false,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"自定义Sql操作数据库\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"自定义Sql\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"account\":{\"data_base_url\":\"***************************************************************************************\",\"dbname\":\"acm\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"**********252201473\",\"input\":{\"tenantId\":\"admin\"},\"limit\":10,\"op_type\":\"SELECT\",\"sql\":\"SELECT\\n\\tcount(*) AS num \\nFROM\\n\\tacm_dashboard \\nWHERE\\n\\ttype = \'authLogin\' and tenant_id = :tenantId;\"}', '{\"result\":true,\"total\":1,\"data\":[{\"num\":1}]}', 'SUCCESS', 'OK', '2024-08-21 19:50:44', '2024-08-21 19:50:44', NULL, '2024-08-16 16:06:17', 'admin', '2024-08-21 19:50:44', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824357480122466306, 'ConditionNode', 1824356916781301762, 1824358255024324610, NULL, NULL, NULL, 1824272013301563394, NULL, '{\"or_list\":[{\"and_list\":[{\"query\":\"{{N1824356994224930817.data}}[0].num\",\"operator\":\"eq\",\"value\":\"0\"}]}]}', '{\"or_list\":[{\"and_list\":[{\"operator\":\"eq\",\"query\":\"{{N1824356994224930817.data}}[0].num\",\"value\":\"0\"}]}]}', '{\"result\":false}', 'SUCCESS', 'OK', '2024-08-21 19:50:44', '2024-08-21 19:50:44', NULL, '2024-08-16 16:08:13', 'admin', '2024-08-21 19:50:44', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824357752404099074, 'ActionNode', 1824357480122466306, NULL, 5, 7, **********252201473, 1824272013301563394, NULL, '{\"ui_id\":\"1824358623671918594\",\"name\":\"root\",\"type\":\"OBJECT\",\"description\":\"自定义Sql操作数据库\",\"display_name\":\"自定义Sql\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1824358623671918595\",\"name\":\"opType\",\"type\":\"STRING\",\"description\":\"\",\"display_name\":\"操作类型\",\"multi_valued\":false,\"value\":\"INSERT\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824358623671918596\",\"name\":\"sql\",\"type\":\"STRING\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"display_name\":\"Sql语句\",\"multi_valued\":false,\"value\":\"INSERT INTO `acm`.`acm_dashboard` (`tenant_id`, `dashboard`, `type`, `update_time`) VALUES ( :tenantId , :content , \'authLogin\', now());\",\"update\":true,\"required\":true,\"page_control\":\"TEXTAREA\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824358623671918597\",\"name\":\"limit\",\"type\":\"NUMBER\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"display_name\":\"查询结果最大返回行数\",\"multi_valued\":false,\"value\":\"1000\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824358623671918598\",\"name\":\"input\",\"type\":\"OBJECT\",\"description\":\"\",\"display_name\":\"输入参数\",\"multi_valued\":false,\"value\":{\"tenantId\":\"{{N1824356916781301762.current}}\",\"content\":\"{{N1824357820809003009.value}}\"},\"update\":true,\"required\":false,\"page_control\":\"EXTEND_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":true,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', '{\"account\":{\"data_base_url\":\"***************************************************************************************\",\"dbname\":\"acm\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"**********252201473\",\"input\":{\"tenantId\":\"admin\",\"content\":\"[{\\\"data\\\":[],\\\"name\\\":\\\"登录人数\\\"},{\\\"data\\\":[],\\\"name\\\":\\\"登录次数\\\"},{\\\"data\\\":[],\\\"name\\\":\\\"授权人数\\\"},{\\\"data\\\":[],\\\"name\\\":\\\"授权次数\\\"}]\"},\"limit\":1000,\"op_type\":\"INSERT\",\"sql\":\"INSERT INTO `acm`.`acm_dashboard` (`tenant_id`, `dashboard`, `type`, `update_time`) VALUES ( :tenantId , :content , \'authLogin\', now());\"}', '{\"result\":true,\"total\":1,\"data\":null}', 'SUCCESS', 'OK', '2024-08-16 17:30:01', '2024-08-16 17:30:01', NULL, '2024-08-16 16:09:18', 'admin', '2024-08-16 17:30:01', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824357820809003009, 'ActionNode', 1824356916781301762, 1824356994224930817, 3, 4, NULL, 1824272013301563394, NULL, '{\"ui_id\":\"1824357831049125892\",\"name\":\"root\",\"type\":\"OBJECT\",\"description\":\"更新变量的值\",\"display_name\":\"更新变量\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1824357831049125893\",\"name\":\"varPath\",\"type\":\"STRING\",\"description\":\"选择前面步骤已经创建的变量\",\"display_name\":\"选择要更新的变量\",\"multi_valued\":false,\"value\":\"{{N1824352095340773378.value}}\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824357831049125894\",\"name\":\"value\",\"type\":\"OBJECT\",\"description\":\"\",\"display_name\":\"设置新的值\",\"multi_valued\":false,\"value\":\"var authLogin = {{N1824352095340773378.value}};\\nvar tenantId = {{N1824356916781301762.current}};\\n\\nconst allData = {};\\nauthLogin.forEach(item => {\\n  const tenantId = item.tenantId;\\n  if (!allData[tenantId]) {\\n    allData[tenantId] = [];\\n  }\\n  allData[tenantId].push(item);\\n});\\n\\nreturn JSON.stringify(allData[tenantId][0].data);\\n\",\"update\":true,\"required\":true,\"page_control\":\"JSON\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":true,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', '{\"input\":{\"value\":\"[{\\\"data\\\":[0,0,0,0,0,0,0],\\\"name\\\":\\\"登录人数\\\"},{\\\"data\\\":[0,0,0,0,0,0,0],\\\"name\\\":\\\"登录次数\\\"},{\\\"data\\\":[0,0,0,0,0,0,0],\\\"name\\\":\\\"授权人数\\\"},{\\\"data\\\":[0,0,0,0,0,0,0],\\\"name\\\":\\\"授权次数\\\"}]\",\"var_path\":\"N1824352095340773378.value\"}}', '{\"code\":200,\"msg\":\"OK\",\"value\":\"[{\\\"data\\\":[0,0,0,0,0,0,0],\\\"name\\\":\\\"登录人数\\\"},{\\\"data\\\":[0,0,0,0,0,0,0],\\\"name\\\":\\\"登录次数\\\"},{\\\"data\\\":[0,0,0,0,0,0,0],\\\"name\\\":\\\"授权人数\\\"},{\\\"data\\\":[0,0,0,0,0,0,0],\\\"name\\\":\\\"授权次数\\\"}]\"}', 'SUCCESS', 'OK', '2024-08-21 19:50:44', '2024-08-21 19:50:44', NULL, '2024-08-16 16:09:34', 'admin', '2024-08-21 19:50:44', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824358255024324610, 'ConditionNode', 1824356916781301762, NULL, NULL, NULL, NULL, 1824272013301563394, NULL, '{\"or_list\":[{\"and_list\":[{\"query\":\"{{N1824356994224930817.data}}[0].num\",\"operator\":\"ne\",\"value\":\"0\"}]}]}', '{\"or_list\":[{\"and_list\":[{\"operator\":\"ne\",\"query\":\"{{N1824356994224930817.data}}[0].num\",\"value\":\"0\"}]}]}', '{\"result\":true}', 'SUCCESS', 'OK', '2024-08-21 19:50:44', '2024-08-21 19:50:44', NULL, '2024-08-16 16:11:18', 'admin', '2024-08-21 19:50:44', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824358393046286337, 'ActionNode', 1824358255024324610, 1824359562942529537, 5, 7, **********252201473, 1824272013301563394, NULL, '{\"ui_id\":\"1824358404129464321\",\"name\":\"root\",\"type\":\"OBJECT\",\"description\":\"自定义Sql操作数据库\",\"display_name\":\"自定义Sql\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1824358404129464322\",\"name\":\"opType\",\"type\":\"STRING\",\"description\":\"\",\"display_name\":\"操作类型\",\"multi_valued\":false,\"value\":\"DELETE\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824358404129464323\",\"name\":\"sql\",\"type\":\"STRING\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"display_name\":\"Sql语句\",\"multi_valued\":false,\"value\":\"DELETE \\nFROM\\n\\tacm_dashboard \\nWHERE\\n\\ttype = \'authLogin\' and tenant_id = :tenantId\",\"update\":true,\"required\":true,\"page_control\":\"TEXTAREA\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824358404129464324\",\"name\":\"limit\",\"type\":\"NUMBER\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"display_name\":\"查询结果最大返回行数\",\"multi_valued\":false,\"value\":\"1000\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824358404129464325\",\"name\":\"input\",\"type\":\"OBJECT\",\"description\":\"\",\"display_name\":\"输入参数\",\"multi_valued\":false,\"value\":{\"tenantId\":\"{{N1824356916781301762.current}}\"},\"update\":true,\"required\":false,\"page_control\":\"EXTEND_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":true,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', '{\"account\":{\"data_base_url\":\"***************************************************************************************\",\"dbname\":\"acm\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"**********252201473\",\"input\":{\"tenantId\":\"admin\"},\"limit\":1000,\"op_type\":\"DELETE\",\"sql\":\"DELETE \\nFROM\\n\\tacm_dashboard \\nWHERE\\n\\ttype = \'authLogin\' and tenant_id = :tenantId\"}', '{\"result\":true,\"total\":1,\"data\":null}', 'SUCCESS', 'OK', '2024-08-21 19:50:44', '2024-08-21 19:50:44', NULL, '2024-08-16 16:11:51', 'admin', '2024-08-21 19:50:44', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824359562942529537, 'ActionNode', 1824358255024324610, NULL, 5, 7, **********252201473, 1824272013301563394, NULL, '{\"ui_id\":\"1824359572998103042\",\"name\":\"root\",\"type\":\"OBJECT\",\"description\":\"自定义Sql操作数据库\",\"display_name\":\"自定义Sql\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1824359572998103043\",\"name\":\"opType\",\"type\":\"STRING\",\"description\":\"\",\"display_name\":\"操作类型\",\"multi_valued\":false,\"value\":\"INSERT\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824359572998103044\",\"name\":\"sql\",\"type\":\"STRING\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"display_name\":\"Sql语句\",\"multi_valued\":false,\"value\":\"INSERT INTO `acm`.`acm_dashboard` (`tenant_id`, `dashboard`, `type`, `update_time`) VALUES ( :tenantId , :content , \'authLogin\', now());\",\"update\":true,\"required\":true,\"page_control\":\"TEXTAREA\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824359572998103045\",\"name\":\"limit\",\"type\":\"NUMBER\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"display_name\":\"查询结果最大返回行数\",\"multi_valued\":false,\"value\":\"1000\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824359572998103046\",\"name\":\"input\",\"type\":\"OBJECT\",\"description\":\"\",\"display_name\":\"输入参数\",\"multi_valued\":false,\"value\":{\"tenantId\":\"{{N1824356916781301762.current}}\",\"content\":\"{{N1824357820809003009.value}}\"},\"update\":true,\"required\":false,\"page_control\":\"EXTEND_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":true,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', '{\"account\":{\"data_base_url\":\"***************************************************************************************\",\"dbname\":\"acm\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"**********252201473\",\"input\":{\"tenantId\":\"admin\",\"content\":\"[{\\\"data\\\":[0,0,0,0,0,0,0],\\\"name\\\":\\\"登录人数\\\"},{\\\"data\\\":[0,0,0,0,0,0,0],\\\"name\\\":\\\"登录次数\\\"},{\\\"data\\\":[0,0,0,0,0,0,0],\\\"name\\\":\\\"授权人数\\\"},{\\\"data\\\":[0,0,0,0,0,0,0],\\\"name\\\":\\\"授权次数\\\"}]\"},\"limit\":1000,\"op_type\":\"INSERT\",\"sql\":\"INSERT INTO `acm`.`acm_dashboard` (`tenant_id`, `dashboard`, `type`, `update_time`) VALUES ( :tenantId , :content , \'authLogin\', now());\"}', '{\"result\":true,\"total\":1,\"data\":null}', 'SUCCESS', 'OK', '2024-08-21 19:50:44', '2024-08-21 19:50:44', NULL, '2024-08-16 16:16:29', 'admin', '2024-08-21 19:50:44', 'admin', 'iam', '0', NULL);


-- Top 3
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824272226464481281, 'StartEventNode', 0, 1824272226556755969, 2, 2, NULL, NULL, 1824272226372206593, NULL, '{\"ui_id\":\"1824407039357657090\",\"name\":\"root\",\"type\":\"OBJECT\",\"description\":\"配置crontab，自定义触发时间\",\"display_name\":\"自定义执行\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1824407039357657091\",\"name\":\"startDate\",\"type\":\"STRING\",\"description\":\"定时执行生效的开始日期\",\"display_name\":\"生效开始日期\",\"multi_valued\":false,\"value\":\"2024-08-01\",\"update\":true,\"required\":true,\"page_control\":\"DATE\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824407039357657092\",\"name\":\"endDate\",\"type\":\"STRING\",\"description\":\"定时执行生效的结束日期\",\"display_name\":\"生效结束日期\",\"multi_valued\":false,\"value\":\"2028-08-31\",\"update\":true,\"required\":true,\"page_control\":\"DATE\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824407039357657093\",\"name\":\"cycle\",\"type\":\"STRING\",\"description\":\"cron表达式：秒 分 时 天 月 周\",\"display_name\":\"crontab表达式\",\"multi_valued\":false,\"value\":\"* */30 * * * *\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":true,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', '{\"cycle\":\"* */30 * * * *\",\"end_date\":\"2028-08-31\",\"start_date\":\"2024-08-01\"}', '{\"cycle\":\"* */30 * * * *\",\"end_date\":\"2028-08-31\",\"start_date\":\"2024-08-01\"}', 'SUCCESS', 'OK', '2024-08-28 15:56:50', '2024-08-28 15:56:50', NULL, '2024-08-16 10:29:27', 'admin', '2024-08-28 15:56:50', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824272226556755969, 'ActionNode', 0, 1824359981592788993, 5, 7, 'sql', 1822874457954340865, 1824272226372206593, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824277631439380481\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824277631439380482\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"操作类型\",\"required\":true,\"name\":\"opType\",\"mutability\":\"readWrite\",\"value\":\"SELECT\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824277631439380483\",\"name\":\"sql\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"Sql语句\",\"value\":\"WITH daily_login_summary AS (\\n  SELECT\\n    DATE(occur_time) AS loginDate,\\n    client_id,\\n    COUNT(DISTINCT operator) AS users,\\n    COUNT(*) AS loginCount,\\n    tenant_id\\n  FROM\\n    iam_auditlog\\n  WHERE\\n    event_type = 1\\n     AND client_id not in (\'usercenter\',\'tc\',\'adm\',\'portal\')\\n    AND event_subtype in (\'101\',\'104\')\\n    AND DATEDIFF(CURDATE(), DATE(occur_time)) <= 7\\n  GROUP BY\\n    DATE(occur_time),\\n    client_id,\\n\\ttenant_id\\n),\\ndaily_ranked AS (\\n  SELECT\\n    loginDate,\\n    client_id,\\n    users,\\n    loginCount,\\n\\ttenant_id,\\n    ROW_NUMBER() OVER (PARTITION BY loginDate ORDER BY users DESC) AS row_num\\n  FROM\\n    daily_login_summary\\n)\\nSELECT\\n  d.loginDate,\\n  d.client_id,\\n  d.users,\\n  d.loginCount,\\n  d.row_num,\\n  a.client_name,\\n  tenant_id\\nFROM\\n  daily_ranked d\\nJOIN\\n  iam_app a ON d.client_id = a.client_id\\nWHERE\\n  d.row_num <= 3\\nORDER BY\\n  d.loginDate,\\n  d.row_num;\",\"required\":true,\"page_control\":\"TEXTAREA\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824277631439380484\",\"name\":\"limit\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"查询结果最大返回行数\",\"value\":\"14000\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824277631439380485\",\"name\":\"input\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"输入参数\",\"value\":{},\"required\":false,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"自定义Sql操作数据库\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"自定义Sql\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"account\":{\"data_base_url\":\"jdbc:mysql://*************:3306/iam?useUnicode=true&useSSL=false&characterEncoding=utf8\",\"dbname\":\"iam\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"1822874457954340865\",\"input\":{},\"limit\":14000,\"op_type\":\"SELECT\",\"sql\":\"WITH daily_login_summary AS (\\n  SELECT\\n    DATE(occur_time) AS loginDate,\\n    client_id,\\n    COUNT(DISTINCT operator) AS users,\\n    COUNT(*) AS loginCount,\\n    tenant_id\\n  FROM\\n    iam_auditlog\\n  WHERE\\n    event_type = 1\\n     AND client_id not in (\'usercenter\',\'tc\',\'adm\',\'portal\')\\n    AND event_subtype in (\'101\',\'104\')\\n    AND DATEDIFF(CURDATE(), DATE(occur_time)) <= 7\\n  GROUP BY\\n    DATE(occur_time),\\n    client_id,\\n\\ttenant_id\\n),\\ndaily_ranked AS (\\n  SELECT\\n    loginDate,\\n    client_id,\\n    users,\\n    loginCount,\\n\\ttenant_id,\\n    ROW_NUMBER() OVER (PARTITION BY loginDate ORDER BY users DESC) AS row_num\\n  FROM\\n    daily_login_summary\\n)\\nSELECT\\n  d.loginDate,\\n  d.client_id,\\n  d.users,\\n  d.loginCount,\\n  d.row_num,\\n  a.client_name,\\n  tenant_id\\nFROM\\n  daily_ranked d\\nJOIN\\n  iam_app a ON d.client_id = a.client_id\\nWHERE\\n  d.row_num <= 3\\nORDER BY\\n  d.loginDate,\\n  d.row_num;\"}', '{\"result\":true,\"total\":0,\"data\":[]}', 'SUCCESS', 'OK', '2024-08-28 15:56:50', '2024-08-28 15:56:50', NULL, '2024-08-16 10:29:27', 'admin', '2024-08-28 15:56:50', 'linkadmin', 'iam', '0', '获取所有应用Top3信息');
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824272226678390785, 'EndNode', 0, NULL, NULL, NULL, NULL, NULL, 1824272226372206593, NULL, NULL, NULL, NULL, 'IDLE', NULL, NULL, NULL, NULL, '2024-08-16 10:29:27', 'admin', '2024-08-16 10:29:27', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824359981592788993, 'ActionNode', 0, 1824360132851974145, 5, 7, NULL, 1822874457954340865, 1824272226372206593, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824359992021655554\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824359992021655555\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"操作类型\",\"required\":true,\"name\":\"opType\",\"mutability\":\"readWrite\",\"value\":\"SELECT\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824359992021655556\",\"name\":\"sql\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"Sql语句\",\"value\":\"select tenant_id from iam_sys_tenant;\",\"required\":true,\"page_control\":\"TEXTAREA\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824359992021655557\",\"name\":\"limit\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"查询结果最大返回行数\",\"value\":\"1000\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824359992021655558\",\"name\":\"input\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"输入参数\",\"value\":{},\"required\":false,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"自定义Sql操作数据库\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"自定义Sql\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"account\":{\"data_base_url\":\"jdbc:mysql://*************:3306/iam?useUnicode=true&useSSL=false&characterEncoding=utf8\",\"dbname\":\"iam\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"1822874457954340865\",\"input\":{},\"limit\":1000,\"op_type\":\"SELECT\",\"sql\":\"select tenant_id from iam_sys_tenant;\"}', '{\"result\":true,\"total\":3,\"data\":[{\"tenant_id\":\"bit\"},{\"tenant_id\":\"iam\"},{\"tenant_id\":\"vic\"}]}', 'SUCCESS', 'OK', '2024-08-28 15:56:50', '2024-08-28 15:56:50', NULL, '2024-08-16 16:18:09', 'admin', '2024-08-28 15:56:50', 'admin', 'iam', '0', '获取所有租户信息');
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824360132851974145, 'ActionNode', 0, 1824360215681089538, 3, 3, NULL, NULL, 1824272226372206593, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824360140739092481\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824360140739092482\",\"name\":\"vars\",\"description\":\"添加变量名及其初始值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"添加变量\",\"value\":{\"allTenant\":\"\"},\"required\":true,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"创建一个变量，并设置初始值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"创建变量\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"vars\":{\"allTenant\":\"\"}}', '{\"vars\":{\"allTenant\":\"\"}}', 'SUCCESS', 'OK', '2024-08-28 15:56:50', '2024-08-28 15:56:50', NULL, '2024-08-16 16:18:45', 'admin', '2024-08-28 15:56:50', 'admin', 'iam', '0', '接收所有租户信息');
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824360215681089538, 'ActionNode', 0, 1824360350238556161, 3, 4, NULL, NULL, 1824272226372206593, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824360228303577092\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824360228303577093\",\"name\":\"varPath\",\"description\":\"选择前面步骤已经创建的变量\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"选择要更新的变量\",\"value\":\"{{N1824360132851974145.vars.allTenant}}\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824360228303577094\",\"name\":\"value\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"设置新的值\",\"value\":\"var allTenant = {{N1824359981592788993.data}};\\n\\nconst tenantIds   = Array.from(new Set(allTenant.map(item => item.tenant_id)));\\n\\nreturn tenantIds;\",\"required\":true,\"page_control\":\"JSON\"}],\"name\":\"root\",\"description\":\"更新变量的值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"更新变量\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"input\":{\"value\":[\"bit\",\"iam\",\"vic\"],\"var_path\":\"N1824360132851974145.vars.allTenant\"}}', '{\"code\":200,\"msg\":\"OK\",\"value\":[\"bit\",\"iam\",\"vic\"]}', 'SUCCESS', 'OK', '2024-08-28 15:56:50', '2024-08-28 15:56:50', NULL, '2024-08-16 16:19:05', 'admin', '2024-08-28 15:56:50', 'admin', 'iam', '0', '计算租户列表');
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824360350238556161, 'ActionNode', 0, 1824360437727543297, 3, 3, NULL, NULL, 1824272226372206593, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824360359551737857\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824360359551737858\",\"name\":\"vars\",\"description\":\"添加变量名及其初始值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"添加变量\",\"value\":{\"appTop3\":\"\"},\"required\":true,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"创建一个变量，并设置初始值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"创建变量\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"vars\":{\"appTop3\":\"\"}}', '{\"vars\":{\"appTop3\":\"\"}}', 'SUCCESS', 'OK', '2024-08-28 15:56:50', '2024-08-28 15:56:50', NULL, '2024-08-16 16:19:37', 'admin', '2024-08-28 15:56:50', 'admin', 'iam', '0', '接收Top3统计应用');
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824360437727543297, 'ActionNode', 0, 1824364424434466818, 3, 4, 'set_var', NULL, 1824272226372206593, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824360448525508611\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824360448525508612\",\"name\":\"varPath\",\"description\":\"选择前面步骤已经创建的变量\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"选择要更新的变量\",\"value\":\"{{N1824360350238556161.vars.appTop3}}\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824360448525508613\",\"name\":\"value\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"设置新的值\",\"value\":\"var appTop3 = {{N1824272226556755969.data}}; \\n\\nconst top3Data = {};\\nappTop3.forEach(item => {\\n  const tenantId = item.tenant_id;\\n  if (!top3Data[tenantId]) {\\n    top3Data[tenantId] = [];\\n  }\\n  top3Data[tenantId].push(item);\\n});\\n\\nlet dates = [];\\nfor (let i = 6; i >= 0; i--) {\\n  let date = new Date();\\n  date.setDate(date.getDate() - i);\\n  dates.push(date.toISOString().split(\'T\')[0]);\\n}\\n\\nvar resultArr = [];\\nfor (let tenantId of {{N1824360215681089538.value}}) {\\n  if (top3Data[tenantId]) {\\n    const curr = top3Data[tenantId];\\n    const groupedByClient = curr.reduce((acc, record) => {\\n      const { client_id, loginDate, users, loginCount } = record;\\n      if (!acc[client_id]) {\\n        acc[client_id] = {};\\n      }\\n      if (!acc[client_id][loginDate]) {\\n        acc[client_id][loginDate] = { users: 0, loginCount: 0 };\\n      }\\n      acc[client_id][loginDate].users += users;\\n      acc[client_id][loginDate].loginCount += loginCount;\\n      return acc;\\n    }, {});\\n\\n    const clientIdNameMap = new Map(curr.map(obj => [obj.client_id, obj.client_name]));\\n\\n    const results = Object.keys(groupedByClient).map(clientId => {\\n      const clientName = clientIdNameMap.get(clientId);\\n      const countsUsers = dates.map(date => Number(groupedByClient[clientId][date]?.users) || 0);\\n      const countsLoginCount = dates.map(date => Number(groupedByClient[clientId][date]?.loginCount) || 0);\\n\\n      return [\\n        { name: clientName, type: \\\"total\\\", data: countsUsers },\\n        { name: clientName, type: \\\"times\\\", data: countsLoginCount }\\n      ];\\n    }).flat();\\n\\n    const res = { tenantId, data: results };\\n    resultArr.push(res);\\n  }\\n}\\n\\nreturn resultArr;\\n\\n\\n\",\"required\":true,\"page_control\":\"JSON\"}],\"name\":\"root\",\"description\":\"更新变量的值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"更新变量\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"input\":{\"value\":[],\"var_path\":\"N1824360350238556161.vars.appTop3\"}}', '{\"code\":200,\"msg\":\"OK\",\"value\":[]}', 'SUCCESS', 'OK', '2024-08-28 15:56:50', '2024-08-28 15:56:50', NULL, '2024-08-16 16:19:58', 'admin', '2024-08-28 15:56:50', 'linkadmin', 'iam', '0', '计算Top3信息');
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824364424434466818, 'LoopNode', 0, 1824272226678390785, NULL, NULL, NULL, NULL, 1824272226372206593, NULL, '{\"type\":\"ARRAY\",\"asc\":true,\"errorExec\":\"CONTINUE\",\"expression\":\"{{N1824360215681089538.value}}\"}', '{\"asc\":true,\"end\":null,\"error_exec\":\"CONTINUE\",\"expression\":\"{{N1824360215681089538.value}}\",\"start\":null,\"type\":\"ARRAY\"}', '{\"current\":\"bit\",\"index\":0,\"type\":\"ARRAY\"}', 'SUCCESS', 'OK', '2024-08-28 15:56:50', '2024-08-28 15:56:50', NULL, '2024-08-16 16:35:48', 'admin', '2024-08-28 15:56:50', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824364531150143490, 'ActionNode', 1824364424434466818, 1824364792404951041, 3, 4, 'set_var', NULL, 1824272226372206593, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824364541201522691\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824364541201522692\",\"name\":\"varPath\",\"description\":\"选择前面步骤已经创建的变量\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"选择要更新的变量\",\"value\":\"{{N1824360350238556161.vars.appTop3}}\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824364541201522693\",\"name\":\"value\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"设置新的值\",\"value\":\"var authLogin = {{N1824360437727543297.value}};\\nvar tenantId = {{N1824364424434466818.current}};\\n\\nconst allData = {};\\nauthLogin.forEach(item => {\\n  const tenantId = item.tenantId;\\n  if (!allData[tenantId]) {\\n    allData[tenantId] = [];\\n  }\\n  allData[tenantId].push(item);\\n});\\n\\nreturn allData[tenantId]?JSON.stringify(allData[tenantId][0].data):\'[]\';\\n\\n\\n\",\"required\":true,\"page_control\":\"JSON\"}],\"name\":\"root\",\"description\":\"更新变量的值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"更新变量\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"input\":{\"value\":\"[]\",\"var_path\":\"N1824360350238556161.vars.appTop3\"}}', '{\"code\":200,\"msg\":\"OK\",\"value\":\"[]\"}', 'SUCCESS', 'OK', '2024-08-28 15:56:50', '2024-08-28 15:56:50', NULL, '2024-08-16 16:36:14', 'admin', '2024-08-28 15:56:50', 'linkadmin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824364792404951041, 'ActionNode', 1824364424434466818, 1824365017462915073, 5, 7, NULL, **********252201473, 1824272226372206593, NULL, '{\"ui_id\":\"1824364813730619394\",\"name\":\"root\",\"type\":\"OBJECT\",\"description\":\"自定义Sql操作数据库\",\"display_name\":\"自定义Sql\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1824364813730619395\",\"name\":\"opType\",\"type\":\"STRING\",\"description\":\"\",\"display_name\":\"操作类型\",\"multi_valued\":false,\"value\":\"SELECT\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824364813730619396\",\"name\":\"sql\",\"type\":\"STRING\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"display_name\":\"Sql语句\",\"multi_valued\":false,\"value\":\"\\nSELECT\\n\\n\\tcount(*) AS num \\n\\nFROM\\n\\n\\tacm_dashboard \\n\\nWHERE\\n\\n\\ttype = \'appTop3\' and tenant_id = :tenantId;\",\"update\":true,\"required\":true,\"page_control\":\"TEXTAREA\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824364813730619397\",\"name\":\"limit\",\"type\":\"NUMBER\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"display_name\":\"查询结果最大返回行数\",\"multi_valued\":false,\"value\":\"1000\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824364813730619398\",\"name\":\"input\",\"type\":\"OBJECT\",\"description\":\"\",\"display_name\":\"输入参数\",\"multi_valued\":false,\"value\":{\"tenantId\":\"{{N1824364424434466818.current}}\"},\"update\":true,\"required\":false,\"page_control\":\"EXTEND_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":true,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', '{\"account\":{\"data_base_url\":\"***************************************************************************************\",\"dbname\":\"acm\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"**********252201473\",\"input\":{\"tenantId\":\"bit\"},\"limit\":1000,\"op_type\":\"SELECT\",\"sql\":\"\\nSELECT\\n\\n\\tcount(*) AS num \\n\\nFROM\\n\\n\\tacm_dashboard \\n\\nWHERE\\n\\n\\ttype = \'appTop3\' and tenant_id = :tenantId;\"}', '{\"result\":true,\"total\":1,\"data\":[{\"num\":1}]}', 'SUCCESS', 'OK', '2024-08-28 15:56:50', '2024-08-28 15:56:50', NULL, '2024-08-16 16:37:16', 'admin', '2024-08-28 15:56:50', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824365017462915073, 'ConditionNode', 1824364424434466818, 1824365109423030274, NULL, NULL, NULL, NULL, 1824272226372206593, NULL, '{\"or_list\":[{\"and_list\":[{\"query\":\"{{N1824364792404951041.data}}[0].num\",\"operator\":\"eq\",\"value\":\"0\"}]}]}', '{\"or_list\":[{\"and_list\":[{\"operator\":\"eq\",\"query\":\"{{N1824364792404951041.data}}[0].num\",\"value\":\"0\"}]}]}', '{\"result\":false}', 'SUCCESS', 'OK', '2024-08-28 15:56:50', '2024-08-28 15:56:50', NULL, '2024-08-16 16:38:10', 'admin', '2024-08-28 15:56:50', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824365109423030274, 'ConditionNode', 1824364424434466818, NULL, NULL, NULL, NULL, NULL, 1824272226372206593, NULL, '{\"or_list\":[{\"and_list\":[{\"query\":\"{{N1824364792404951041.data}}[0].num\",\"operator\":\"ne\",\"value\":\"0\"}]}]}', '{\"or_list\":[{\"and_list\":[{\"operator\":\"ne\",\"query\":\"{{N1824364792404951041.data}}[0].num\",\"value\":\"0\"}]}]}', '{\"result\":true}', 'SUCCESS', 'OK', '2024-08-28 15:56:50', '2024-08-28 15:56:50', NULL, '2024-08-16 16:38:32', 'admin', '2024-08-28 15:56:50', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824365193015508994, 'ActionNode', 1824365017462915073, NULL, 5, 7, NULL, **********252201473, 1824272226372206593, NULL, '{\"ui_id\":\"1824365203951886338\",\"name\":\"root\",\"type\":\"OBJECT\",\"description\":\"自定义Sql操作数据库\",\"display_name\":\"自定义Sql\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1824365203951886339\",\"name\":\"opType\",\"type\":\"STRING\",\"description\":\"\",\"display_name\":\"操作类型\",\"multi_valued\":false,\"value\":\"INSERT\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824365203956080642\",\"name\":\"sql\",\"type\":\"STRING\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"display_name\":\"Sql语句\",\"multi_valued\":false,\"value\":\"INSERT INTO `acm`.`acm_dashboard` (`tenant_id`, `dashboard`, `type`, `update_time`) VALUES ( :tenantId , :content , \'appTop3\', now());\",\"update\":true,\"required\":true,\"page_control\":\"TEXTAREA\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824365203956080643\",\"name\":\"limit\",\"type\":\"NUMBER\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"display_name\":\"查询结果最大返回行数\",\"multi_valued\":false,\"value\":\"10\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824365203956080644\",\"name\":\"input\",\"type\":\"OBJECT\",\"description\":\"\",\"display_name\":\"输入参数\",\"multi_valued\":false,\"value\":{\"tenantId\":\"{{N1824364424434466818.current}}\",\"content\":\"{{N1824364531150143490.value}}\"},\"update\":true,\"required\":false,\"page_control\":\"EXTEND_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":true,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', '{\"account\":{\"data_base_url\":\"***************************************************************************************\",\"dbname\":\"acm\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"**********252201473\",\"input\":{\"tenantId\":\"bit\",\"content\":\"[]\"},\"limit\":10,\"op_type\":\"INSERT\",\"sql\":\"INSERT INTO `acm`.`acm_dashboard` (`tenant_id`, `dashboard`, `type`, `update_time`) VALUES ( :tenantId , :content , \'appTop3\', now());\"}', '{\"result\":true,\"total\":1,\"data\":null}', 'SUCCESS', 'OK', '2024-08-28 15:25:03', '2024-08-28 15:25:03', NULL, '2024-08-16 16:38:52', 'admin', '2024-08-28 15:25:03', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824365438793334786, 'ActionNode', 1824365109423030274, NULL, 5, 7, NULL, **********252201473, 1824272226372206593, NULL, '{\"ui_id\":\"1824365447917772802\",\"name\":\"root\",\"type\":\"OBJECT\",\"description\":\"自定义Sql操作数据库\",\"display_name\":\"自定义Sql\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1824365447917772803\",\"name\":\"opType\",\"type\":\"STRING\",\"description\":\"\",\"display_name\":\"操作类型\",\"multi_valued\":false,\"value\":\"INSERT\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824365447917772804\",\"name\":\"sql\",\"type\":\"STRING\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"display_name\":\"Sql语句\",\"multi_valued\":false,\"value\":\"INSERT INTO `acm`.`acm_dashboard` (`tenant_id`, `dashboard`, `type`, `update_time`) VALUES ( :tenantId , :content , \'appTop3\', now());\",\"update\":true,\"required\":true,\"page_control\":\"TEXTAREA\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824365447917772805\",\"name\":\"limit\",\"type\":\"NUMBER\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"display_name\":\"查询结果最大返回行数\",\"multi_valued\":false,\"value\":\"2\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824365447917772806\",\"name\":\"input\",\"type\":\"OBJECT\",\"description\":\"\",\"display_name\":\"输入参数\",\"multi_valued\":false,\"value\":{\"tenantId\":\"{{N1824364424434466818.current}}\",\"content\":\"{{N1824364531150143490.value}}\"},\"update\":true,\"required\":false,\"page_control\":\"EXTEND_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":true,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', '{\"account\":{\"data_base_url\":\"***************************************************************************************\",\"dbname\":\"acm\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"**********252201473\",\"input\":{\"tenantId\":\"bit\",\"content\":\"[]\"},\"limit\":2,\"op_type\":\"INSERT\",\"sql\":\"INSERT INTO `acm`.`acm_dashboard` (`tenant_id`, `dashboard`, `type`, `update_time`) VALUES ( :tenantId , :content , \'appTop3\', now());\"}', '{\"result\":true,\"total\":1,\"data\":null}', 'SUCCESS', 'OK', '2024-08-28 15:56:50', '2024-08-28 15:56:50', NULL, '2024-08-16 16:39:50', 'admin', '2024-08-28 15:56:50', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824365806117896193, 'ActionNode', 1824365109423030274, 1824365438793334786, 5, 7, NULL, **********252201473, 1824272226372206593, NULL, '{\"ui_id\":\"1824365815485603841\",\"name\":\"root\",\"type\":\"OBJECT\",\"description\":\"自定义Sql操作数据库\",\"display_name\":\"自定义Sql\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1824365815485603842\",\"name\":\"opType\",\"type\":\"STRING\",\"description\":\"\",\"display_name\":\"操作类型\",\"multi_valued\":false,\"value\":\"DELETE\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824365815485603843\",\"name\":\"sql\",\"type\":\"STRING\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"display_name\":\"Sql语句\",\"multi_valued\":false,\"value\":\"DELETE \\nFROM\\n\\tacm_dashboard \\nWHERE\\n\\ttype = \'appTop3\' and tenant_id = :tenantId\",\"update\":true,\"required\":true,\"page_control\":\"TEXTAREA\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824365815485603844\",\"name\":\"limit\",\"type\":\"NUMBER\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"display_name\":\"查询结果最大返回行数\",\"multi_valued\":false,\"value\":\"0\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824365815485603845\",\"name\":\"input\",\"type\":\"OBJECT\",\"description\":\"\",\"display_name\":\"输入参数\",\"multi_valued\":false,\"value\":{\"tenantId\":\"{{N1824364424434466818.current}}\"},\"update\":true,\"required\":false,\"page_control\":\"EXTEND_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":true,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', '{\"account\":{\"data_base_url\":\"***************************************************************************************\",\"dbname\":\"acm\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"**********252201473\",\"input\":{\"tenantId\":\"bit\"},\"limit\":0,\"op_type\":\"DELETE\",\"sql\":\"DELETE \\nFROM\\n\\tacm_dashboard \\nWHERE\\n\\ttype = \'appTop3\' and tenant_id = :tenantId\"}', '{\"result\":true,\"total\":1,\"data\":null}', 'SUCCESS', 'OK', '2024-08-28 15:56:50', '2024-08-28 15:56:50', NULL, '2024-08-16 16:41:18', 'admin', '2024-08-28 15:56:50', 'admin', 'iam', '0', NULL);



-- 应用授权信息统计
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824272437479915522, 'StartEventNode', 0, 1824272437593161730, 2, 2, NULL, NULL, 1824272437412806658, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824406695760273409\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824406695760273410\",\"name\":\"startDate\",\"description\":\"定时执行生效的开始日期\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"生效开始日期\",\"value\":\"2024-08-01\",\"required\":true,\"page_control\":\"DATE\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824406695760273411\",\"name\":\"endDate\",\"description\":\"定时执行生效的结束日期\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"生效结束日期\",\"value\":\"2029-08-31\",\"required\":true,\"page_control\":\"DATE\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824406695760273412\",\"name\":\"cycle\",\"description\":\"cron表达式：秒 分 时 天 月 周\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"crontab表达式\",\"value\":\"* */30 * * * *\",\"required\":true,\"page_control\":\"TEXT\"}],\"name\":\"root\",\"description\":\"配置crontab，自定义触发时间\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"自定义执行\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"cycle\":\"* */30 * * * *\",\"end_date\":\"2029-08-31\",\"start_date\":\"2024-08-01\"}', '{\"cycle\":\"* */30 * * * *\",\"end_date\":\"2029-08-31\",\"start_date\":\"2024-08-01\"}', 'SUCCESS', 'OK', '2024-08-28 15:57:51', '2024-08-28 15:57:51', NULL, '2024-08-16 10:30:17', 'admin', '2024-08-28 15:57:51', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824272437593161730, 'ActionNode', 0, 1824362420366655489, 5, 7, 'sql', 1822874457954340865, 1824272437412806658, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824278444345827330\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824278444345827331\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"操作类型\",\"required\":true,\"name\":\"opType\",\"mutability\":\"readWrite\",\"value\":\"SELECT\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824278444345827332\",\"name\":\"sql\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"Sql语句\",\"value\":\"SELECT\\n\\ti.idp_type,\\n    a.client_name,\\n\\tCOUNT(a.client_id) AS appCount,\\n\\tl.tenant_id\\nFROM\\n\\tiam.iam_auditlog l\\n\\tLEFT JOIN iam.iam_third_idp i ON l.auth_id = i.id\\n\\tRIGHT JOIN  iam.iam_app a ON l.client_id = a.client_id\\nWHERE\\n\\tl.client_id not in (\'usercenter\',\'tc\',\'adm\',\'portal\')\\nGROUP BY\\n\\tl.client_id,\\n\\ti.idp_type,\\n\\tl.tenant_id\\nORDER BY\\n\\ti.idp_type,\\n\\tappCount DESC;\\n\",\"required\":true,\"page_control\":\"TEXTAREA\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824278444345827333\",\"name\":\"limit\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"查询结果最大返回行数\",\"value\":\"14000\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824278444345827334\",\"name\":\"input\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"输入参数\",\"value\":{},\"required\":false,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"自定义Sql操作数据库\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"自定义Sql\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"account\":{\"data_base_url\":\"jdbc:mysql://*************:3306/iam?useUnicode=true&useSSL=false&characterEncoding=utf8\",\"dbname\":\"iam\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"1822874457954340865\",\"input\":{},\"limit\":14000,\"op_type\":\"SELECT\",\"sql\":\"SELECT\\n\\ti.idp_type,\\n    a.client_name,\\n\\tCOUNT(a.client_id) AS appCount,\\n\\tl.tenant_id\\nFROM\\n\\tiam.iam_auditlog l\\n\\tLEFT JOIN iam.iam_third_idp i ON l.auth_id = i.id\\n\\tRIGHT JOIN  iam.iam_app a ON l.client_id = a.client_id\\nWHERE\\n\\tl.client_id not in (\'usercenter\',\'tc\',\'adm\',\'portal\')\\nGROUP BY\\n\\tl.client_id,\\n\\ti.idp_type,\\n\\tl.tenant_id\\nORDER BY\\n\\ti.idp_type,\\n\\tappCount DESC;\\n\"}', '{\"result\":true,\"total\":0,\"data\":[]}', 'SUCCESS', 'OK', '2024-08-28 15:57:51', '2024-08-28 15:57:51', NULL, '2024-08-16 10:30:17', 'admin', '2024-08-28 15:57:51', 'linkadmin', 'iam', '0', '获取所有认证源的统计信息');
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824272437760933889, 'EndNode', 0, NULL, NULL, NULL, NULL, NULL, 1824272437412806658, NULL, NULL, NULL, NULL, 'IDLE', NULL, NULL, NULL, NULL, '2024-08-16 10:30:17', 'admin', '2024-08-16 10:30:17', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824362420366655489, 'ActionNode', 0, 1824362630580977665, 5, 7, NULL, 1822874457954340865, 1824272437412806658, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824362569656344578\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824362569656344579\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"操作类型\",\"required\":true,\"name\":\"opType\",\"mutability\":\"readWrite\",\"value\":\"SELECT\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824362569656344580\",\"name\":\"sql\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"Sql语句\",\"value\":\"select tenant_id from iam_sys_tenant;\",\"required\":true,\"page_control\":\"TEXTAREA\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824362569656344581\",\"name\":\"limit\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"查询结果最大返回行数\",\"value\":\"1000\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824362569656344582\",\"name\":\"input\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"输入参数\",\"value\":{},\"required\":false,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"自定义Sql操作数据库\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"自定义Sql\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"account\":{\"data_base_url\":\"jdbc:mysql://*************:3306/iam?useUnicode=true&useSSL=false&characterEncoding=utf8\",\"dbname\":\"iam\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"1822874457954340865\",\"input\":{},\"limit\":1000,\"op_type\":\"SELECT\",\"sql\":\"select tenant_id from iam_sys_tenant;\"}', '{\"result\":true,\"total\":3,\"data\":[{\"tenant_id\":\"bit\"},{\"tenant_id\":\"iam\"},{\"tenant_id\":\"vic\"}]}', 'SUCCESS', 'OK', '2024-08-28 15:57:51', '2024-08-28 15:57:51', NULL, '2024-08-16 16:27:51', 'admin', '2024-08-28 15:57:51', 'admin', 'iam', '0', '获取所有的租户');
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824362630580977665, 'ActionNode', 0, 1824362710956425218, 3, 3, NULL, NULL, 1824272437412806658, NULL, '{\"ui_id\":\"1824362640649134081\",\"name\":\"root\",\"type\":\"OBJECT\",\"description\":\"创建一个变量，并设置初始值\",\"display_name\":\"创建变量\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1824362640649134082\",\"name\":\"vars\",\"type\":\"OBJECT\",\"description\":\"添加变量名及其初始值\",\"display_name\":\"添加变量\",\"multi_valued\":false,\"value\":{\"allTenant\":\"\"},\"update\":true,\"required\":true,\"page_control\":\"EXTEND_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":true,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', '{\"vars\":{\"allTenant\":\"\"}}', '{\"vars\":{\"allTenant\":\"\"}}', 'SUCCESS', 'OK', '2024-08-28 15:57:51', '2024-08-28 15:57:51', NULL, '2024-08-16 16:28:41', 'admin', '2024-08-28 15:57:51', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824362710956425218, 'ActionNode', 0, 1824362879902990337, 3, 4, NULL, NULL, 1824272437412806658, NULL, '{\"ui_id\":\"1824362721704058884\",\"name\":\"root\",\"type\":\"OBJECT\",\"description\":\"更新变量的值\",\"display_name\":\"更新变量\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1824362721704058885\",\"name\":\"varPath\",\"type\":\"STRING\",\"description\":\"选择前面步骤已经创建的变量\",\"display_name\":\"选择要更新的变量\",\"multi_valued\":false,\"value\":\"{{N1824362630580977665.vars.allTenant}}\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824362721704058886\",\"name\":\"value\",\"type\":\"OBJECT\",\"description\":\"\",\"display_name\":\"设置新的值\",\"multi_valued\":false,\"value\":\"var allTenant = {{N1824362420366655489.data}};\\n\\nconst tenantIds   = Array.from(new Set(allTenant.map(item => item.tenant_id)));\\n\\nreturn tenantIds;\",\"update\":true,\"required\":true,\"page_control\":\"JSON\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":true,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', '{\"input\":{\"value\":[\"bit\",\"iam\",\"vic\"],\"var_path\":\"N1824362630580977665.vars.allTenant\"}}', '{\"code\":200,\"msg\":\"OK\",\"value\":[\"bit\",\"iam\",\"vic\"]}', 'SUCCESS', 'OK', '2024-08-28 15:57:51', '2024-08-28 15:57:51', NULL, '2024-08-16 16:29:00', 'admin', '2024-08-28 15:57:51', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824362879902990337, 'ActionNode', 0, 1824362946491760641, 3, 3, NULL, NULL, 1824272437412806658, NULL, '{\"ui_id\":\"1824362888020795394\",\"name\":\"root\",\"type\":\"OBJECT\",\"description\":\"创建一个变量，并设置初始值\",\"display_name\":\"创建变量\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1824362888020795395\",\"name\":\"vars\",\"type\":\"OBJECT\",\"description\":\"添加变量名及其初始值\",\"display_name\":\"添加变量\",\"multi_valued\":false,\"value\":{\"appIdp\":\"\"},\"update\":true,\"required\":true,\"page_control\":\"EXTEND_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":true,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', '{\"vars\":{\"appIdp\":\"\"}}', '{\"vars\":{\"appIdp\":\"\"}}', 'SUCCESS', 'OK', '2024-08-28 15:57:51', '2024-08-28 15:57:51', NULL, '2024-08-16 16:29:40', 'admin', '2024-08-28 15:57:51', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824362946491760641, 'ActionNode', 0, 1824366573528727554, 3, 4, NULL, NULL, 1824272437412806658, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824362954508902403\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824362954508902404\",\"name\":\"varPath\",\"description\":\"选择前面步骤已经创建的变量\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"选择要更新的变量\",\"value\":\"{{N1824362879902990337.vars.appIdp}}\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824362954508902405\",\"name\":\"value\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"设置新的值\",\"value\":\"var appIdp = {{N1824272437593161730.data}};\\n\\nconst appIdpData = {};\\nappIdp.forEach(item => {\\n  const tenantId = item.tenant_id;\\n  if (!appIdpData[tenantId]) {\\n    appIdpData[tenantId] = [];\\n  }\\n  appIdpData[tenantId].push(item);\\n});\\n\\nvar resultArr = [];\\nfor (let tenantId of {{N1824362710956425218.value}}) {\\n    if (appIdpData[tenantId]) {\\n    const curr = appIdpData[tenantId];\\n\\n    // 提取所有存在的 idp_type 并确保 \'default\' 总是在最后\\n    const allIdpTypes = Array.from(new Set(curr.map(item => item.idp_type))).concat([\'default\']);\\n    allIdpTypes.pop(); // 移除最后一个 \'default\'，因为它已经在前面添加过了\\n\\n    // 创建一个空对象用于存储分组后的数据\\n    const groupedData = {};\\n\\n    // 遍历数据并填充结果对象\\n    curr.forEach(item => {\\n      const { appCount, client_name, idp_type } = item;\\n      const type = idp_type || \'default\';\\n\\n      if (!groupedData[client_name]) {\\n        groupedData[client_name] = {\\n          name: client_name,\\n          data: allIdpTypes.reduce((acc, type) => {\\n            acc[type] = 0;\\n            return acc;\\n          }, {})\\n        };\\n      }\\n\\n      groupedData[client_name].data[type] += Number(appCount);\\n    });\\n\\n    // 将结果对象转换为数组\\n    const result = Object.values(groupedData).map(item => ({\\n      name: item.name,\\n      data: allIdpTypes.map(type => item.data[type])\\n    }));\\n\\n    const appAuth = {\\n      types: allIdpTypes,\\n      series: result\\n    };\\n\\n    const res = { tenantId, data: appAuth };\\n    resultArr.push(res);\\n  }\\n}\\n\\nreturn resultArr;\\n\\n\",\"required\":true,\"page_control\":\"JSON\"}],\"name\":\"root\",\"description\":\"更新变量的值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"更新变量\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"input\":{\"value\":[],\"var_path\":\"N1824362879902990337.vars.appIdp\"}}', '{\"code\":200,\"msg\":\"OK\",\"value\":[]}', 'SUCCESS', 'OK', '2024-08-28 15:57:51', '2024-08-28 15:57:51', NULL, '2024-08-16 16:29:56', 'admin', '2024-08-28 15:57:51', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824366573528727554, 'LoopNode', 0, 1824272437760933889, NULL, NULL, NULL, NULL, 1824272437412806658, NULL, '{\"type\":\"ARRAY\",\"asc\":true,\"errorExec\":\"CONTINUE\",\"expression\":\"{{N1824362710956425218.value}}\"}', '{\"asc\":true,\"end\":null,\"error_exec\":\"CONTINUE\",\"expression\":\"{{N1824362710956425218.value}}\",\"start\":null,\"type\":\"ARRAY\"}', '{\"current\":\"bit\",\"index\":0,\"type\":\"ARRAY\"}', 'SUCCESS', 'OK', '2024-08-28 15:57:51', '2024-08-28 15:57:51', NULL, '2024-08-16 16:44:21', 'admin', '2024-08-28 15:57:51', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824366692483383298, 'ActionNode', 1824366573528727554, 1824367045593448449, 3, 4, 'set_var', NULL, 1824272437412806658, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824366702396350467\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824366702396350468\",\"name\":\"varPath\",\"description\":\"选择前面步骤已经创建的变量\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"选择要更新的变量\",\"value\":\"{{N1824362879902990337.vars.appIdp}}\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824366702396350469\",\"name\":\"value\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"设置新的值\",\"value\":\"var authLogin = {{N1824362946491760641.value}};\\nvar tenantId = {{N1824366573528727554.current}};\\n\\nconst allData = {};\\nauthLogin.forEach(item => {\\n  const tenantId = item.tenantId;\\n  if (!allData[tenantId]) {\\n    allData[tenantId] = [];\\n  }\\n  allData[tenantId].push(item);\\n});\\n\\nreturn allData[tenantId]?JSON.stringify(allData[tenantId][0].data):\'[]\';\\n\\n\",\"required\":true,\"page_control\":\"JSON\"}],\"name\":\"root\",\"description\":\"更新变量的值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"更新变量\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"input\":{\"value\":\"[]\",\"var_path\":\"N1824362879902990337.vars.appIdp\"}}', '{\"code\":200,\"msg\":\"OK\",\"value\":\"[]\"}', 'SUCCESS', 'OK', '2024-08-28 15:57:51', '2024-08-28 15:57:51', NULL, '2024-08-16 16:44:49', 'admin', '2024-08-28 15:57:51', 'linkadmin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824367045593448449, 'ActionNode', 1824366573528727554, 1824367264276070401, 5, 7, NULL, **********252201473, 1824272437412806658, NULL, '{\"ui_id\":\"1824367054889852930\",\"name\":\"root\",\"type\":\"OBJECT\",\"description\":\"自定义Sql操作数据库\",\"display_name\":\"自定义Sql\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1824367054889852931\",\"name\":\"opType\",\"type\":\"STRING\",\"description\":\"\",\"display_name\":\"操作类型\",\"multi_valued\":false,\"value\":\"SELECT\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824367054889852932\",\"name\":\"sql\",\"type\":\"STRING\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"display_name\":\"Sql语句\",\"multi_valued\":false,\"value\":\"SELECT\\n\\tcount(*) AS num \\nFROM\\n\\tacm_dashboard \\nWHERE\\n\\ttype = \'appIdp\' and tenant_id = :tenantId;\",\"update\":true,\"required\":true,\"page_control\":\"TEXTAREA\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824367054889852933\",\"name\":\"limit\",\"type\":\"NUMBER\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"display_name\":\"查询结果最大返回行数\",\"multi_valued\":false,\"value\":\"10\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824367054889852934\",\"name\":\"input\",\"type\":\"OBJECT\",\"description\":\"\",\"display_name\":\"输入参数\",\"multi_valued\":false,\"value\":{\"tenantId\":\"{{N1824366573528727554.current}}\"},\"update\":true,\"required\":false,\"page_control\":\"EXTEND_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":true,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', '{\"account\":{\"data_base_url\":\"***************************************************************************************\",\"dbname\":\"acm\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"**********252201473\",\"input\":{\"tenantId\":\"bit\"},\"limit\":10,\"op_type\":\"SELECT\",\"sql\":\"SELECT\\n\\tcount(*) AS num \\nFROM\\n\\tacm_dashboard \\nWHERE\\n\\ttype = \'appIdp\' and tenant_id = :tenantId;\"}', '{\"result\":true,\"total\":1,\"data\":[{\"num\":0}]}', 'SUCCESS', 'OK', '2024-08-28 15:57:51', '2024-08-28 15:57:51', NULL, '2024-08-16 16:46:13', 'admin', '2024-08-28 15:57:51', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824367264276070401, 'ConditionNode', 1824366573528727554, 1824367387353726977, NULL, NULL, NULL, NULL, 1824272437412806658, NULL, '{\"or_list\":[{\"and_list\":[{\"query\":\"{{N1824367045593448449.data}}[0].num\",\"operator\":\"eq\",\"value\":\"0\"}]}]}', '{\"or_list\":[{\"and_list\":[{\"operator\":\"eq\",\"query\":\"{{N1824367045593448449.data}}[0].num\",\"value\":\"0\"}]}]}', '{\"result\":true}', 'SUCCESS', 'OK', '2024-08-28 15:57:51', '2024-08-28 15:57:51', NULL, '2024-08-16 16:47:06', 'admin', '2024-08-28 15:57:51', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824367387353726977, 'ConditionNode', 1824366573528727554, NULL, NULL, NULL, NULL, NULL, 1824272437412806658, NULL, '{\"or_list\":[{\"and_list\":[{\"query\":\"{{N1824367045593448449.data}}[0].num\",\"operator\":\"ne\",\"value\":\"0\"}]}]}', '{\"or_list\":[{\"and_list\":[{\"operator\":\"ne\",\"query\":\"{{N1824367045593448449.data}}[0].num\",\"value\":\"0\"}]}]}', '{\"result\":false}', 'SUCCESS', 'OK', '2024-08-28 15:57:51', '2024-08-28 15:57:51', NULL, '2024-08-16 16:47:35', 'admin', '2024-08-28 15:57:51', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824367510473326593, 'ActionNode', 1824367264276070401, NULL, 5, 7, NULL, **********252201473, 1824272437412806658, NULL, '{\"ui_id\":\"1824367521606836226\",\"name\":\"root\",\"type\":\"OBJECT\",\"description\":\"自定义Sql操作数据库\",\"display_name\":\"自定义Sql\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1824367521606836227\",\"name\":\"opType\",\"type\":\"STRING\",\"description\":\"\",\"display_name\":\"操作类型\",\"multi_valued\":false,\"value\":\"INSERT\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824367521606836228\",\"name\":\"sql\",\"type\":\"STRING\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"display_name\":\"Sql语句\",\"multi_valued\":false,\"value\":\"INSERT INTO `acm`.`acm_dashboard` (`tenant_id`, `dashboard`, `type`, `update_time`) VALUES ( :tenantId , :content , \'appIdp\', now());\",\"update\":true,\"required\":true,\"page_control\":\"TEXTAREA\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824367521606836229\",\"name\":\"limit\",\"type\":\"NUMBER\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"display_name\":\"查询结果最大返回行数\",\"multi_valued\":false,\"value\":\"10\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824367521606836230\",\"name\":\"input\",\"type\":\"OBJECT\",\"description\":\"\",\"display_name\":\"输入参数\",\"multi_valued\":false,\"value\":{\"tenantId\":\"{{N1824366573528727554.current}}\",\"content\":\"{{N1824366692483383298.value}}\"},\"update\":true,\"required\":false,\"page_control\":\"EXTEND_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":true,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', '{\"account\":{\"data_base_url\":\"***************************************************************************************\",\"dbname\":\"acm\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"**********252201473\",\"input\":{\"tenantId\":\"bit\",\"content\":\"[]\"},\"limit\":10,\"op_type\":\"INSERT\",\"sql\":\"INSERT INTO `acm`.`acm_dashboard` (`tenant_id`, `dashboard`, `type`, `update_time`) VALUES ( :tenantId , :content , \'appIdp\', now());\"}', '{\"result\":true,\"total\":1,\"data\":null}', 'SUCCESS', 'OK', '2024-08-28 15:57:51', '2024-08-28 15:57:51', NULL, '2024-08-16 16:48:04', 'admin', '2024-08-28 15:57:51', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824367781198872578, 'ActionNode', 1824367387353726977, NULL, 5, 7, NULL, **********252201473, 1824272437412806658, NULL, '{\"ui_id\":\"1824367791023759362\",\"name\":\"root\",\"type\":\"OBJECT\",\"description\":\"自定义Sql操作数据库\",\"display_name\":\"自定义Sql\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1824367791023759363\",\"name\":\"opType\",\"type\":\"STRING\",\"description\":\"\",\"display_name\":\"操作类型\",\"multi_valued\":false,\"value\":\"INSERT\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824367791023759364\",\"name\":\"sql\",\"type\":\"STRING\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"display_name\":\"Sql语句\",\"multi_valued\":false,\"value\":\"INSERT INTO `acm`.`acm_dashboard` (`tenant_id`, `dashboard`, `type`, `update_time`) VALUES ( :tenantId , :content , \'appIdp\', now());\",\"update\":true,\"required\":true,\"page_control\":\"TEXTAREA\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824367791023759365\",\"name\":\"limit\",\"type\":\"NUMBER\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"display_name\":\"查询结果最大返回行数\",\"multi_valued\":false,\"value\":\"10\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824367791023759366\",\"name\":\"input\",\"type\":\"OBJECT\",\"description\":\"\",\"display_name\":\"输入参数\",\"multi_valued\":false,\"value\":{\"tenantId\":\"{{N1824366573528727554.current}}\",\"content\":\"{{N1824366692483383298.value}}\"},\"update\":true,\"required\":false,\"page_control\":\"EXTEND_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":true,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', '{\"account\":{\"data_base_url\":\"***************************************************************************************\",\"dbname\":\"acm\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"**********252201473\",\"input\":{\"tenantId\":\"admin\",\"content\":\"{}\"},\"limit\":10,\"op_type\":\"INSERT\",\"sql\":\"INSERT INTO `acm`.`acm_dashboard` (`tenant_id`, `dashboard`, `type`, `update_time`) VALUES ( :tenantId , :content , \'appIdp\', now());\"}', '{\"result\":true,\"total\":1,\"data\":null}', 'SUCCESS', 'OK', '2024-08-20 12:12:51', '2024-08-20 12:12:51', NULL, '2024-08-16 16:49:09', 'admin', '2024-08-20 12:12:51', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824367992012980225, 'ActionNode', 1824367387353726977, 1824367781198872578, 5, 7, NULL, **********252201473, 1824272437412806658, NULL, '{\"ui_id\":\"1824368001716232193\",\"name\":\"root\",\"type\":\"OBJECT\",\"description\":\"自定义Sql操作数据库\",\"display_name\":\"自定义Sql\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1824368001716232194\",\"name\":\"opType\",\"type\":\"STRING\",\"description\":\"\",\"display_name\":\"操作类型\",\"multi_valued\":false,\"value\":\"DELETE\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824368001716232195\",\"name\":\"sql\",\"type\":\"STRING\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"display_name\":\"Sql语句\",\"multi_valued\":false,\"value\":\"DELETE \\nFROM\\n\\tacm_dashboard \\nWHERE\\n\\ttype = \'appIdp\' and tenant_id = :tenantId\",\"update\":true,\"required\":true,\"page_control\":\"TEXTAREA\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824368001716232196\",\"name\":\"limit\",\"type\":\"NUMBER\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"display_name\":\"查询结果最大返回行数\",\"multi_valued\":false,\"value\":\"10\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824368001716232197\",\"name\":\"input\",\"type\":\"OBJECT\",\"description\":\"\",\"display_name\":\"输入参数\",\"multi_valued\":false,\"value\":{\"tenantId\":\"{{N1824366573528727554.current}}\"},\"update\":true,\"required\":false,\"page_control\":\"EXTEND_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":true,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', '{\"account\":{\"data_base_url\":\"***************************************************************************************\",\"dbname\":\"acm\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"**********252201473\",\"input\":{\"tenantId\":\"admin\"},\"limit\":10,\"op_type\":\"DELETE\",\"sql\":\"DELETE \\nFROM\\n\\tacm_dashboard \\nWHERE\\n\\ttype = \'appIdp\' and tenant_id = :tenantId\"}', '{\"result\":true,\"total\":1,\"data\":null}', 'SUCCESS', 'OK', '2024-08-20 12:12:51', '2024-08-20 12:12:51', NULL, '2024-08-16 16:49:59', 'admin', '2024-08-20 12:12:51', 'admin', 'iam', '0', NULL);



INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824369959108001794, 'StartEventNode', 0, 1824369959191887874, 1, 1, NULL, 1824369959049281537, NULL, '{\"ui_id\":\"1824385377568464898\",\"name\":\"root\",\"type\":\"OBJECT\",\"description\":\"实时触发流程\",\"display_name\":\"接收数据\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1824385377568464899\",\"name\":\"hookUrl\",\"description\":\"请复制此Webhook地址，作为接口调用目标地址；目前仅支持POST方法且content-type为json的调用方式。\",\"display_name\":\"webhook地址\",\"multi_valued\":false,\"value\":\"https://192-168-50-20-7whpfwmaza4g.ztna-dingtalk.com/acm/flows/start/1/1824369959049281537/e28e5a28cdf3408eb80a9bd65d359d20\",\"update\":true,\"required\":true,\"page_control\":\"LABEL\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readonly\"},{\"ui_id\":\"1824385377568464900\",\"name\":\"type\",\"type\":\"STRING\",\"description\":\"异步：webhook接收到数据后，将直接返回接收成功的结果以及流的信息。\\n同步：Webhook接收到数据后，将等待连接流执行完成并将最后的执行结果返回给调用方（最长等待时间为10s，超时将返回为空）。请查看接口文档获得更多详细信息\",\"display_name\":\"连接流执行方式\",\"multi_valued\":false,\"value\":\"同步\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"异步\",\"同步\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824385377568464901\",\"name\":\"value\",\"type\":\"OBJECT\",\"description\":\"请按实际情况输入json格式的测试数据\",\"display_name\":\"测试数据\",\"multi_valued\":false,\"update\":true,\"required\":false,\"page_control\":\"JSON\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":true,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', '{\"hook_url\":\"https://192-168-50-20-7whpfwmaza4g.ztna-dingtalk.com/acm/flows/start/1/1824369959049281537/e28e5a28cdf3408eb80a9bd65d359d20\",\"sync\":true,\"type\":\"同步\",\"value\":null}', '{\"hook_url\":\"https://192-168-50-20-7whpfwmaza4g.ztna-dingtalk.com/acm/flows/start/1/1824369959049281537/e28e5a28cdf3408eb80a9bd65d359d20\",\"sync\":true,\"type\":\"同步\",\"value\":null}', 'SUCCESS', 'OK', '2024-08-16 19:05:38', '2024-08-16 19:05:38', NULL, '2024-08-16 16:57:48', 'admin', '2024-08-16 19:05:38', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824369959191887874, 'ActionNode', 0, 1824379756083593217, 5, 7, **********252201473, 1824369959049281537, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824379450186440706\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824379450186440707\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"操作类型\",\"required\":true,\"name\":\"opType\",\"mutability\":\"readWrite\",\"value\":\"SELECT\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824379450186440708\",\"name\":\"sql\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"Sql语句\",\"value\":\"select * from acm_dashboard where type= \'userOrg\' and tenant_id =:tenantId;\",\"required\":true,\"page_control\":\"TEXTAREA\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824379450186440709\",\"name\":\"limit\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"查询结果最大返回行数\",\"value\":\"10\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824***************\",\"name\":\"input\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"输入参数\",\"value\":{\"tenantId\":\"{{sys_tenant_id}}\"},\"required\":false,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"自定义Sql操作数据库\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"自定义Sql\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"account\":{\"data_base_url\":\"***************************************************************************************\",\"dbname\":\"acm\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"**********252201473\",\"input\":{\"tenantId\":\"iam\"},\"limit\":10,\"op_type\":\"SELECT\",\"sql\":\"select * from acm_dashboard where type= \'userOrg\' and tenant_id =:tenantId;\"}', '{\"result\":true,\"total\":1,\"data\":[{\"tenant_id\":\"iam\",\"update_time\":*************,\"type\":\"userOrg\",\"dashboard\":\"{\\\"org\\\":{\\\"total\\\":0},\\\"tenantId\\\":\\\"iam\\\",\\\"user\\\":{\\\"total\\\":0,\\\"active\\\":{\\\"week\\\":0,\\\"month\\\":0,\\\"day\\\":1},\\\"create\\\":{\\\"week\\\":0,\\\"month\\\":0,\\\"day\\\":0},\\\"login\\\":{\\\"week\\\":0,\\\"month\\\":0,\\\"day\\\":0}}}\"}]}', 'SUCCESS', 'OK', '2024-08-16 19:05:38', '2024-08-16 19:05:38', NULL, '2024-08-16 16:57:48', 'admin', '2024-08-16 19:05:38', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824369959321911297, 'EndNode', 0, NULL, NULL, NULL, NULL, 1824369959049281537, NULL, NULL, NULL, NULL, 'IDLE', NULL, NULL, NULL, NULL, '2024-08-16 16:57:48', 'admin', '2024-08-16 16:57:48', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824379756083593217, 'ActionNode', 0, 1824379807388319745, 3, 3, NULL, 1824369959049281537, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824379764796989442\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824379764796989443\",\"name\":\"vars\",\"description\":\"添加变量名及其初始值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"添加变量\",\"value\":{\"userOrg\":\"\"},\"required\":true,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"创建一个变量，并设置初始值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"创建变量\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"vars\":{\"userOrg\":\"\"}}', '{\"vars\":{\"userOrg\":\"\"}}', 'SUCCESS', 'OK', '2024-08-16 19:05:38', '2024-08-16 19:05:38', NULL, '2024-08-16 17:36:44', 'admin', '2024-08-16 19:05:38', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824379807388319745, 'ActionNode', 0, 1824369959321911297, 3, 4, NULL, 1824369959049281537, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824379817091571715\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824379817091571716\",\"name\":\"varPath\",\"description\":\"选择前面步骤已经创建的变量\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"选择要更新的变量\",\"value\":\"{{N1824379756083593217.vars.userOrg}}\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824379817091571717\",\"name\":\"value\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"设置新的值\",\"value\":\"var dashboard = {{N1824369959191887874.data}};\\nvar str = dashboard[0].dashboard;\\nlet res = JSON.parse(str);\\ndelete res.tenantId;\\nreturn res;\\n\\n\\n\",\"required\":true,\"page_control\":\"JSON\"}],\"name\":\"root\",\"description\":\"更新变量的值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"更新变量\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"input\":{\"value\":{\"org\":{\"total\":0},\"user\":{\"total\":0,\"active\":{\"week\":0,\"month\":0,\"day\":1},\"create\":{\"week\":0,\"month\":0,\"day\":0},\"login\":{\"week\":0,\"month\":0,\"day\":0}}},\"var_path\":\"N1824379756083593217.vars.userOrg\"}}', '{\"code\":200,\"msg\":\"OK\",\"value\":{\"org\":{\"total\":0},\"user\":{\"total\":0,\"active\":{\"week\":0,\"month\":0,\"day\":1},\"create\":{\"week\":0,\"month\":0,\"day\":0},\"login\":{\"week\":0,\"month\":0,\"day\":0}}}}', 'SUCCESS', 'OK', '2024-08-16 19:05:38', '2024-08-16 19:05:38', NULL, '2024-08-16 17:36:56', 'admin', '2024-08-16 19:05:38', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824380134084485121, 'StartEventNode', 0, 1824380134084485122, 1, 1, NULL, 1824380134007160833, NULL, '{\"ui_id\":\"1824385673975734273\",\"name\":\"root\",\"type\":\"OBJECT\",\"description\":\"实时触发流程\",\"display_name\":\"接收数据\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1824385673975734274\",\"name\":\"hookUrl\",\"description\":\"请复制此Webhook地址，作为接口调用目标地址；目前仅支持POST方法且content-type为json的调用方式。\",\"display_name\":\"webhook地址\",\"multi_valued\":false,\"value\":\"https://192-168-50-20-7whpfwmaza4g.ztna-dingtalk.com/acm/flows/start/1/1824380134007160833/7637e00658f34d838e8da6cc99086966\",\"update\":true,\"required\":true,\"page_control\":\"LABEL\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readonly\"},{\"ui_id\":\"1824385673975734275\",\"name\":\"type\",\"type\":\"STRING\",\"description\":\"异步：webhook接收到数据后，将直接返回接收成功的结果以及流的信息。\\n同步：Webhook接收到数据后，将等待连接流执行完成并将最后的执行结果返回给调用方（最长等待时间为10s，超时将返回为空）。请查看接口文档获得更多详细信息\",\"display_name\":\"连接流执行方式\",\"multi_valued\":false,\"value\":\"同步\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"异步\",\"同步\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824385673975734276\",\"name\":\"value\",\"type\":\"OBJECT\",\"description\":\"请按实际情况输入json格式的测试数据\",\"display_name\":\"测试数据\",\"multi_valued\":false,\"update\":true,\"required\":false,\"page_control\":\"JSON\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":true,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', '{\"hook_url\":\"https://192-168-50-20-7whpfwmaza4g.ztna-dingtalk.com/acm/flows/start/1/1824380134007160833/7637e00658f34d838e8da6cc99086966\",\"sync\":true,\"type\":\"同步\",\"value\":null}', '{\"hook_url\":\"https://192-168-50-20-7whpfwmaza4g.ztna-dingtalk.com/acm/flows/start/1/1824380134007160833/7637e00658f34d838e8da6cc99086966\",\"sync\":true,\"type\":\"同步\",\"value\":null}', 'SUCCESS', 'OK', '2024-08-16 18:00:25', '2024-08-16 18:00:25', NULL, '2024-08-16 17:38:14', 'admin', '2024-08-16 18:00:25', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824380134084485122, 'ActionNode', 0, 1824380134084485124, 5, 7, **********252201473, 1824380134007160833, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824379450186440706\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824379450186440707\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"操作类型\",\"required\":true,\"name\":\"opType\",\"mutability\":\"readWrite\",\"value\":\"SELECT\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824379450186440708\",\"name\":\"sql\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"Sql语句\",\"value\":\"select * from acm_dashboard where type= \'flowApp\' and tenant_id =:tenantId;\",\"required\":true,\"page_control\":\"TEXTAREA\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824379450186440709\",\"name\":\"limit\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"查询结果最大返回行数\",\"value\":\"10\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824***************\",\"name\":\"input\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"输入参数\",\"value\":{\"tenantId\":\"{{sys_tenant_id}}\"},\"required\":false,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"自定义Sql操作数据库\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"自定义Sql\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"account\":{\"data_base_url\":\"***************************************************************************************\",\"dbname\":\"acm\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"**********252201473\",\"input\":{\"tenantId\":\"iam\"},\"limit\":10,\"op_type\":\"SELECT\",\"sql\":\"select * from acm_dashboard where type= \'flowApp\' and tenant_id =:tenantId;\"}', '{\"result\":true,\"total\":1,\"data\":[{\"tenant_id\":\"iam\",\"update_time\":*************,\"type\":\"flowApp\",\"dashboard\":\"{\\\"system\\\":0,\\\"expection\\\":0,\\\"tenantId\\\":\\\"iam\\\",\\\"active\\\":0,\\\"flow\\\":0}\"}]}', 'SUCCESS', 'OK', '2024-08-16 18:00:25', '2024-08-16 18:00:25', NULL, '2024-08-16 17:38:14', 'admin', '2024-08-16 18:00:25', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824380134084485123, 'EndNode', 0, NULL, NULL, NULL, NULL, 1824380134007160833, NULL, NULL, NULL, NULL, 'IDLE', NULL, NULL, NULL, NULL, '2024-08-16 17:38:14', 'admin', '2024-08-16 17:38:14', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824380134084485124, 'ActionNode', 0, 1824380134084485125, 3, 3, NULL, 1824380134007160833, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824379764796989442\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824379764796989443\",\"name\":\"vars\",\"description\":\"添加变量名及其初始值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"添加变量\",\"value\":{\"flowApp\":\"\"},\"required\":true,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"创建一个变量，并设置初始值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"创建变量\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"vars\":{\"flowApp\":\"\"}}', '{\"vars\":{\"flowApp\":\"\"}}', 'SUCCESS', 'OK', '2024-08-16 18:00:25', '2024-08-16 18:00:25', NULL, '2024-08-16 17:38:14', 'admin', '2024-08-16 18:00:26', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824380134084485125, 'ActionNode', 0, 1824380134084485123, 3, 4, NULL, 1824380134007160833, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824379817091571715\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824379817091571716\",\"name\":\"varPath\",\"description\":\"选择前面步骤已经创建的变量\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"选择要更新的变量\",\"value\":\"{{N1824380134084485124.vars.flowApp}}\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824379817091571717\",\"name\":\"value\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"设置新的值\",\"value\":\"var dashboard = {{N1824380134084485122.data}};\\nvar str = dashboard[0].dashboard;\\nlet res = JSON.parse(str);\\ndelete res.tenantId;\\nvar result = {};\\nresult.link = res;\\nreturn result;\\n\\n\\n\\n\\n\",\"required\":true,\"page_control\":\"JSON\"}],\"name\":\"root\",\"description\":\"更新变量的值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"更新变量\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"input\":{\"value\":{\"link\":{\"system\":0,\"expection\":0,\"active\":0,\"flow\":0}},\"var_path\":\"N1824380134084485124.vars.flowApp\"}}', '{\"code\":200,\"msg\":\"OK\",\"value\":{\"link\":{\"system\":0,\"expection\":0,\"active\":0,\"flow\":0}}}', 'SUCCESS', 'OK', '2024-08-16 18:00:26', '2024-08-16 18:00:26', NULL, '2024-08-16 17:38:14', 'admin', '2024-08-16 18:00:26', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824381134425989121, 'StartEventNode', 0, 1824381134425989122, 1, 1, NULL, 1824381134206058498, NULL, '{\"ui_id\":\"1824385943371685890\",\"name\":\"root\",\"type\":\"OBJECT\",\"description\":\"实时触发流程\",\"display_name\":\"接收数据\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1824385943371685891\",\"name\":\"hookUrl\",\"description\":\"请复制此Webhook地址，作为接口调用目标地址；目前仅支持POST方法且content-type为json的调用方式。\",\"display_name\":\"webhook地址\",\"multi_valued\":false,\"value\":\"https://192-168-50-20-7whpfwmaza4g.ztna-dingtalk.com/acm/flows/start/1/1824381134206058498/2131733a6615451099e9966d6fffcd5d\",\"update\":true,\"required\":true,\"page_control\":\"LABEL\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readonly\"},{\"ui_id\":\"1824385943371685892\",\"name\":\"type\",\"type\":\"STRING\",\"description\":\"异步：webhook接收到数据后，将直接返回接收成功的结果以及流的信息。\\n同步：Webhook接收到数据后，将等待连接流执行完成并将最后的执行结果返回给调用方（最长等待时间为10s，超时将返回为空）。请查看接口文档获得更多详细信息\",\"display_name\":\"连接流执行方式\",\"multi_valued\":false,\"value\":\"同步\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"异步\",\"同步\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824385943371685893\",\"name\":\"value\",\"type\":\"OBJECT\",\"description\":\"请按实际情况输入json格式的测试数据\",\"display_name\":\"测试数据\",\"multi_valued\":false,\"update\":true,\"required\":false,\"page_control\":\"JSON\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":true,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', '{\"hook_url\":\"https://192-168-50-20-7whpfwmaza4g.ztna-dingtalk.com/acm/flows/start/1/1824381134206058498/2131733a6615451099e9966d6fffcd5d\",\"sync\":true,\"type\":\"同步\",\"value\":null}', '{\"hook_url\":\"https://192-168-50-20-7whpfwmaza4g.ztna-dingtalk.com/acm/flows/start/1/1824381134206058498/2131733a6615451099e9966d6fffcd5d\",\"sync\":true,\"type\":\"同步\",\"value\":null}', 'SUCCESS', 'OK', '2024-08-16 18:01:43', '2024-08-16 18:01:43', NULL, '2024-08-16 17:42:12', 'admin', '2024-08-16 18:01:43', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824381134425989122, 'ActionNode', 0, 1824381134425989124, 5, 7, **********252201473, 1824381134206058498, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824379450186440706\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824379450186440707\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"操作类型\",\"required\":true,\"name\":\"opType\",\"mutability\":\"readWrite\",\"value\":\"SELECT\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824379450186440708\",\"name\":\"sql\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"Sql语句\",\"value\":\"select * from acm_dashboard where type= \'userStatis\' and tenant_id =:tenantId;\",\"required\":true,\"page_control\":\"TEXTAREA\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824379450186440709\",\"name\":\"limit\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"查询结果最大返回行数\",\"value\":\"10\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824***************\",\"name\":\"input\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"输入参数\",\"value\":{\"tenantId\":\"{{sys_tenant_id}}\"},\"required\":false,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"自定义Sql操作数据库\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"自定义Sql\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"account\":{\"data_base_url\":\"***************************************************************************************\",\"dbname\":\"acm\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"**********252201473\",\"input\":{\"tenantId\":\"iam\"},\"limit\":10,\"op_type\":\"SELECT\",\"sql\":\"select * from acm_dashboard where type= \'userStatis\' and tenant_id =:tenantId;\"}', '{\"result\":true,\"total\":1,\"data\":[{\"tenant_id\":\"iam\",\"update_time\":*************,\"type\":\"userStatis\",\"dashboard\":\"{\\\"data\\\":[{\\\"name\\\":\\\"禁用用户数\\\",\\\"value\\\":0},{\\\"name\\\":\\\"锁定用户数\\\",\\\"valueactive\\\":0},{\\\"name\\\":\\\"未激活用户数\\\",\\\"value\\\":1},{\\\"name\\\":\\\"待删除用户数\\\",\\\"value\\\":0},{\\\"name\\\":\\\"密码即将过期数\\\",\\\"value\\\":0},{\\\"name\\\":\\\"连续密码错误用户数\\\",\\\"value\\\":1},{\\\"name\\\":\\\"不活跃用户数\\\",\\\"value\\\":1}],\\\"tenantId\\\":\\\"iam\\\"}\"}]}', 'SUCCESS', 'OK', '2024-08-16 18:01:43', '2024-08-16 18:01:43', NULL, '2024-08-16 17:42:12', 'admin', '2024-08-16 18:01:43', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824381134425989123, 'EndNode', 0, NULL, NULL, NULL, NULL, 1824381134206058498, NULL, NULL, NULL, NULL, 'IDLE', NULL, NULL, NULL, NULL, '2024-08-16 17:42:13', 'admin', '2024-08-16 17:42:13', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824381134425989124, 'ActionNode', 0, 1824381134425989125, 3, 3, NULL, 1824381134206058498, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824379764796989442\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824379764796989443\",\"name\":\"vars\",\"description\":\"添加变量名及其初始值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"添加变量\",\"value\":{\"userStatis\":\"\"},\"required\":true,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"创建一个变量，并设置初始值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"创建变量\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"vars\":{\"userStatis\":\"\"}}', '{\"vars\":{\"userStatis\":\"\"}}', 'SUCCESS', 'OK', '2024-08-16 18:01:43', '2024-08-16 18:01:43', NULL, '2024-08-16 17:42:13', 'admin', '2024-08-16 18:01:43', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824381134425989125, 'ActionNode', 0, 1824381134425989123, 3, 4, NULL, 1824381134206058498, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824379817091571715\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824379817091571716\",\"name\":\"varPath\",\"description\":\"选择前面步骤已经创建的变量\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"选择要更新的变量\",\"value\":\"{{N1824381134425989124.vars.userStatis}}\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824379817091571717\",\"name\":\"value\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"设置新的值\",\"value\":\"var dashboard = {{N1824381134425989122.data}};\\nvar str = dashboard[0].dashboard;\\nlet res = JSON.parse(str);\\ndelete res.tenantId;\\nvar result = {};\\nresult.user_status = res.data;\\nreturn result;\\n\\n\\n\\n\\n\",\"required\":true,\"page_control\":\"JSON\"}],\"name\":\"root\",\"description\":\"更新变量的值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"更新变量\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"input\":{\"value\":{\"user_status\":[{\"name\":\"禁用用户数\",\"value\":0},{\"name\":\"锁定用户数\",\"valueactive\":0},{\"name\":\"未激活用户数\",\"value\":1},{\"name\":\"待删除用户数\",\"value\":0},{\"name\":\"密码即将过期数\",\"value\":0},{\"name\":\"连续密码错误用户数\",\"value\":1},{\"name\":\"不活跃用户数\",\"value\":1}]},\"var_path\":\"N1824381134425989124.vars.userStatis\"}}', '{\"code\":200,\"msg\":\"OK\",\"value\":{\"user_status\":[{\"name\":\"禁用用户数\",\"value\":0},{\"name\":\"锁定用户数\",\"valueactive\":0},{\"name\":\"未激活用户数\",\"value\":1},{\"name\":\"待删除用户数\",\"value\":0},{\"name\":\"密码即将过期数\",\"value\":0},{\"name\":\"连续密码错误用户数\",\"value\":1},{\"name\":\"不活跃用户数\",\"value\":1}]}}', 'SUCCESS', 'OK', '2024-08-16 18:01:43', '2024-08-16 18:01:43', NULL, '2024-08-16 17:42:13', 'admin', '2024-08-16 18:01:43', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824381737948585985, 'StartEventNode', 0, 1824381737948585986, 1, 1, NULL, 1824381737745432577, NULL, '{\"ui_id\":\"1824386128541818882\",\"name\":\"root\",\"type\":\"OBJECT\",\"description\":\"实时触发流程\",\"display_name\":\"接收数据\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1824386128541818883\",\"name\":\"hookUrl\",\"description\":\"请复制此Webhook地址，作为接口调用目标地址；目前仅支持POST方法且content-type为json的调用方式。\",\"display_name\":\"webhook地址\",\"multi_valued\":false,\"value\":\"https://192-168-50-20-7whpfwmaza4g.ztna-dingtalk.com/acm/flows/start/1/1824381737745432577/6a435009486140b2801f18f10bd7aaea\",\"update\":true,\"required\":true,\"page_control\":\"LABEL\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readonly\"},{\"ui_id\":\"1824386128541818884\",\"name\":\"type\",\"type\":\"STRING\",\"description\":\"异步：webhook接收到数据后，将直接返回接收成功的结果以及流的信息。\\n同步：Webhook接收到数据后，将等待连接流执行完成并将最后的执行结果返回给调用方（最长等待时间为10s，超时将返回为空）。请查看接口文档获得更多详细信息\",\"display_name\":\"连接流执行方式\",\"multi_valued\":false,\"value\":\"同步\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"异步\",\"同步\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824386128541818885\",\"name\":\"value\",\"type\":\"OBJECT\",\"description\":\"请按实际情况输入json格式的测试数据\",\"display_name\":\"测试数据\",\"multi_valued\":false,\"update\":true,\"required\":false,\"page_control\":\"JSON\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":true,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', '{\"hook_url\":\"https://192-168-50-20-7whpfwmaza4g.ztna-dingtalk.com/acm/flows/start/1/1824381737745432577/6a435009486140b2801f18f10bd7aaea\",\"sync\":true,\"type\":\"同步\",\"value\":null}', '{\"hook_url\":\"https://192-168-50-20-7whpfwmaza4g.ztna-dingtalk.com/acm/flows/start/1/1824381737745432577/6a435009486140b2801f18f10bd7aaea\",\"sync\":true,\"type\":\"同步\",\"value\":null}', 'SUCCESS', 'OK', '2024-08-16 18:02:43', '2024-08-16 18:02:43', NULL, '2024-08-16 17:44:36', 'admin', '2024-08-16 18:02:43', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824381737948585986, 'ActionNode', 0, 1824381737948585988, 5, 7, **********252201473, 1824381737745432577, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824379450186440706\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824379450186440707\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"操作类型\",\"required\":true,\"name\":\"opType\",\"mutability\":\"readWrite\",\"value\":\"SELECT\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824379450186440708\",\"name\":\"sql\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"Sql语句\",\"value\":\"select * from acm_dashboard where type= \'authLogin\' and tenant_id =:tenantId;\",\"required\":true,\"page_control\":\"TEXTAREA\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824379450186440709\",\"name\":\"limit\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"查询结果最大返回行数\",\"value\":\"10\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824***************\",\"name\":\"input\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"输入参数\",\"value\":{\"tenantId\":\"{{sys_tenant_id}}\"},\"required\":false,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"自定义Sql操作数据库\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"自定义Sql\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"account\":{\"data_base_url\":\"***************************************************************************************\",\"dbname\":\"acm\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"**********252201473\",\"input\":{\"tenantId\":\"iam\"},\"limit\":10,\"op_type\":\"SELECT\",\"sql\":\"select * from acm_dashboard where type= \'authLogin\' and tenant_id =:tenantId;\"}', '{\"result\":true,\"total\":1,\"data\":[{\"tenant_id\":\"iam\",\"update_time\":*************,\"type\":\"authLogin\",\"dashboard\":\"[{\\\"data\\\":[1,1,1,0,1,0,0],\\\"name\\\":\\\"登录人数\\\"},{\\\"data\\\":[10,13,4,0,2,0,0],\\\"name\\\":\\\"登录次数\\\"},{\\\"data\\\":[1,1,1,0,1,0,0],\\\"name\\\":\\\"授权人数\\\"},{\\\"data\\\":[10,10,4,0,2,0,0],\\\"name\\\":\\\"授权次数\\\"}]\"}]}', 'SUCCESS', 'OK', '2024-08-16 18:02:44', '2024-08-16 18:02:44', NULL, '2024-08-16 17:44:36', 'admin', '2024-08-16 18:02:44', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824381737948585987, 'EndNode', 0, NULL, NULL, NULL, NULL, 1824381737745432577, NULL, NULL, NULL, NULL, 'IDLE', NULL, NULL, NULL, NULL, '2024-08-16 17:44:36', 'admin', '2024-08-16 17:44:36', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824381737948585988, 'ActionNode', 0, 1824381737948585989, 3, 3, NULL, 1824381737745432577, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824379764796989442\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824379764796989443\",\"name\":\"vars\",\"description\":\"添加变量名及其初始值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"添加变量\",\"value\":{\"authLogin\":\"\"},\"required\":true,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"创建一个变量，并设置初始值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"创建变量\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"vars\":{\"authLogin\":\"\"}}', '{\"vars\":{\"authLogin\":\"\"}}', 'SUCCESS', 'OK', '2024-08-16 18:02:44', '2024-08-16 18:02:44', NULL, '2024-08-16 17:44:36', 'admin', '2024-08-16 18:02:44', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824381737948585989, 'ActionNode', 0, 1824381737948585987, 3, 4, NULL, 1824381737745432577, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824379817091571715\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824379817091571716\",\"name\":\"varPath\",\"description\":\"选择前面步骤已经创建的变量\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"选择要更新的变量\",\"value\":\"{{N1824381737948585988.vars.authLogin}}\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824379817091571717\",\"name\":\"value\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"设置新的值\",\"value\":\"var dashboard = {{N1824381737948585986.data}};\\nvar str = dashboard[0].dashboard;\\nlet res = JSON.parse(str);\\ndelete res.tenantId;\\nvar result = {};\\nresult.login_trends = res;\\nreturn result;\\n\\n\\n\\n\\n\",\"required\":true,\"page_control\":\"JSON\"}],\"name\":\"root\",\"description\":\"更新变量的值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"更新变量\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"input\":{\"value\":{\"login_trends\":[{\"data\":[1,1,1,0,1,0,0],\"name\":\"登录人数\"},{\"data\":[10,13,4,0,2,0,0],\"name\":\"登录次数\"},{\"data\":[1,1,1,0,1,0,0],\"name\":\"授权人数\"},{\"data\":[10,10,4,0,2,0,0],\"name\":\"授权次数\"}]},\"var_path\":\"N1824381737948585988.vars.authLogin\"}}', '{\"code\":200,\"msg\":\"OK\",\"value\":{\"login_trends\":[{\"data\":[1,1,1,0,1,0,0],\"name\":\"登录人数\"},{\"data\":[10,13,4,0,2,0,0],\"name\":\"登录次数\"},{\"data\":[1,1,1,0,1,0,0],\"name\":\"授权人数\"},{\"data\":[10,10,4,0,2,0,0],\"name\":\"授权次数\"}]}}', 'SUCCESS', 'OK', '2024-08-16 18:02:44', '2024-08-16 18:02:44', NULL, '2024-08-16 17:44:36', 'admin', '2024-08-16 18:02:44', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824382297040920578, 'StartEventNode', 0, 1824382297040920579, 1, 1, NULL, 1824382296946819073, NULL, '{\"ui_id\":\"1824386372272824322\",\"name\":\"root\",\"type\":\"OBJECT\",\"description\":\"实时触发流程\",\"display_name\":\"接收数据\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1824386372272824323\",\"name\":\"hookUrl\",\"description\":\"请复制此Webhook地址，作为接口调用目标地址；目前仅支持POST方法且content-type为json的调用方式。\",\"display_name\":\"webhook地址\",\"multi_valued\":false,\"value\":\"https://192-168-50-20-7whpfwmaza4g.ztna-dingtalk.com/acm/flows/start/1/1824382296946819073/d74e3218769945cba71876459c439819\",\"update\":true,\"required\":true,\"page_control\":\"LABEL\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readonly\"},{\"ui_id\":\"1824386372272824324\",\"name\":\"type\",\"type\":\"STRING\",\"description\":\"异步：webhook接收到数据后，将直接返回接收成功的结果以及流的信息。\\n同步：Webhook接收到数据后，将等待连接流执行完成并将最后的执行结果返回给调用方（最长等待时间为10s，超时将返回为空）。请查看接口文档获得更多详细信息\",\"display_name\":\"连接流执行方式\",\"multi_valued\":false,\"value\":\"同步\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"异步\",\"同步\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824386372272824325\",\"name\":\"value\",\"type\":\"OBJECT\",\"description\":\"请按实际情况输入json格式的测试数据\",\"display_name\":\"测试数据\",\"multi_valued\":false,\"update\":true,\"required\":false,\"page_control\":\"JSON\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":true,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', '{\"hook_url\":\"https://192-168-50-20-7whpfwmaza4g.ztna-dingtalk.com/acm/flows/start/1/1824382296946819073/d74e3218769945cba71876459c439819\",\"sync\":true,\"type\":\"同步\",\"value\":null}', '{\"hook_url\":\"https://192-168-50-20-7whpfwmaza4g.ztna-dingtalk.com/acm/flows/start/1/1824382296946819073/d74e3218769945cba71876459c439819\",\"sync\":true,\"type\":\"同步\",\"value\":null}', 'SUCCESS', 'OK', '2024-08-16 18:03:12', '2024-08-16 18:03:12', NULL, '2024-08-16 17:46:50', 'admin', '2024-08-16 18:03:12', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824382297040920579, 'ActionNode', 0, 1824382297040920581, 5, 7, **********252201473, 1824382296946819073, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824379450186440706\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824379450186440707\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"操作类型\",\"required\":true,\"name\":\"opType\",\"mutability\":\"readWrite\",\"value\":\"SELECT\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824379450186440708\",\"name\":\"sql\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"Sql语句\",\"value\":\"select * from acm_dashboard where type= \'appTop3\' and tenant_id =:tenantId;\",\"required\":true,\"page_control\":\"TEXTAREA\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824379450186440709\",\"name\":\"limit\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"查询结果最大返回行数\",\"value\":\"10\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824***************\",\"name\":\"input\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"输入参数\",\"value\":{\"tenantId\":\"{{sys_tenant_id}}\"},\"required\":false,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"自定义Sql操作数据库\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"自定义Sql\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"account\":{\"data_base_url\":\"***************************************************************************************\",\"dbname\":\"acm\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"**********252201473\",\"input\":{\"tenantId\":\"iam\"},\"limit\":10,\"op_type\":\"SELECT\",\"sql\":\"select * from acm_dashboard where type= \'appTop3\' and tenant_id =:tenantId;\"}', '{\"result\":true,\"total\":1,\"data\":[{\"tenant_id\":\"iam\",\"update_time\":*************,\"type\":\"appTop3\",\"dashboard\":\"[{\\\"data\\\":[1,1,1,0,0,0,0],\\\"name\\\":\\\"tc\\\",\\\"type\\\":\\\"total\\\"},{\\\"data\\\":[10,13,4,0,0,0,0],\\\"name\\\":\\\"tc\\\",\\\"type\\\":\\\"times\\\"}]\"}]}', 'SUCCESS', 'OK', '2024-08-16 18:03:12', '2024-08-16 18:03:12', NULL, '2024-08-16 17:46:50', 'admin', '2024-08-16 18:03:12', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824382297040920580, 'EndNode', 0, NULL, NULL, NULL, NULL, 1824382296946819073, NULL, NULL, NULL, NULL, 'IDLE', NULL, NULL, NULL, NULL, '2024-08-16 17:46:50', 'admin', '2024-08-16 17:46:50', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824382297040920581, 'ActionNode', 0, 1824382297040920582, 3, 3, NULL, 1824382296946819073, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824379764796989442\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824379764796989443\",\"name\":\"vars\",\"description\":\"添加变量名及其初始值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"添加变量\",\"value\":{\"appTop3\":\"\"},\"required\":true,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"创建一个变量，并设置初始值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"创建变量\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"vars\":{\"appTop3\":\"\"}}', '{\"vars\":{\"appTop3\":\"\"}}', 'SUCCESS', 'OK', '2024-08-16 18:03:13', '2024-08-16 18:03:13', NULL, '2024-08-16 17:46:50', 'admin', '2024-08-16 18:03:13', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824382297040920582, 'ActionNode', 0, 1824382297040920580, 3, 4, NULL, 1824382296946819073, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824379817091571715\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824379817091571716\",\"name\":\"varPath\",\"description\":\"选择前面步骤已经创建的变量\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"选择要更新的变量\",\"value\":\"{{N1824382297040920581.vars.appTop3}}\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824379817091571717\",\"name\":\"value\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"设置新的值\",\"value\":\"var dashboard = {{N1824382297040920579.data}};\\nvar str = dashboard[0].dashboard;\\nlet res = JSON.parse(str);\\ndelete res.tenantId;\\nvar result = {};\\nresult.login_top_3 = res;\\nreturn result;\\n\\n\\n\\n\\n\",\"required\":true,\"page_control\":\"JSON\"}],\"name\":\"root\",\"description\":\"更新变量的值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"更新变量\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"input\":{\"value\":{\"login_top_3\":[{\"data\":[1,1,1,0,0,0,0],\"name\":\"tc\",\"type\":\"total\"},{\"data\":[10,13,4,0,0,0,0],\"name\":\"tc\",\"type\":\"times\"}]},\"var_path\":\"N1824382297040920581.vars.appTop3\"}}', '{\"code\":200,\"msg\":\"OK\",\"value\":{\"login_top_3\":[{\"data\":[1,1,1,0,0,0,0],\"name\":\"tc\",\"type\":\"total\"},{\"data\":[10,13,4,0,0,0,0],\"name\":\"tc\",\"type\":\"times\"}]}}', 'SUCCESS', 'OK', '2024-08-16 18:03:13', '2024-08-16 18:03:13', NULL, '2024-08-16 17:46:50', 'admin', '2024-08-16 18:03:13', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824382978686623746, 'StartEventNode', 0, 1824382978686623747, 1, 1, NULL, 1824382978630270977, NULL, '{\"ui_id\":\"1824386515944513537\",\"name\":\"root\",\"type\":\"OBJECT\",\"description\":\"实时触发流程\",\"display_name\":\"接收数据\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1824386515944513538\",\"name\":\"hookUrl\",\"description\":\"请复制此Webhook地址，作为接口调用目标地址；目前仅支持POST方法且content-type为json的调用方式。\",\"display_name\":\"webhook地址\",\"multi_valued\":false,\"value\":\"https://192-168-50-20-7whpfwmaza4g.ztna-dingtalk.com/acm/flows/start/1/1824382978630270977/bf0665f9997d42e7a5b7b6757e073197\",\"update\":true,\"required\":true,\"page_control\":\"LABEL\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readonly\"},{\"ui_id\":\"1824386515944513539\",\"name\":\"type\",\"type\":\"STRING\",\"description\":\"异步：webhook接收到数据后，将直接返回接收成功的结果以及流的信息。\\n同步：Webhook接收到数据后，将等待连接流执行完成并将最后的执行结果返回给调用方（最长等待时间为10s，超时将返回为空）。请查看接口文档获得更多详细信息\",\"display_name\":\"连接流执行方式\",\"multi_valued\":false,\"value\":\"同步\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"异步\",\"同步\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1824386515944513540\",\"name\":\"value\",\"type\":\"OBJECT\",\"description\":\"请按实际情况输入json格式的测试数据\",\"display_name\":\"测试数据\",\"multi_valued\":false,\"update\":true,\"required\":false,\"page_control\":\"JSON\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":true,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', '{\"hook_url\":\"https://192-168-50-20-7whpfwmaza4g.ztna-dingtalk.com/acm/flows/start/1/1824382978630270977/bf0665f9997d42e7a5b7b6757e073197\",\"sync\":true,\"type\":\"同步\",\"value\":null}', '{\"hook_url\":\"https://192-168-50-20-7whpfwmaza4g.ztna-dingtalk.com/acm/flows/start/1/1824382978630270977/bf0665f9997d42e7a5b7b6757e073197\",\"sync\":true,\"type\":\"同步\",\"value\":null}', 'SUCCESS', 'OK', '2024-08-16 18:04:21', '2024-08-16 18:04:21', NULL, '2024-08-16 17:49:32', 'admin', '2024-08-16 18:04:21', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824382978686623747, 'ActionNode', 0, 1824382978686623749, 5, 7, **********252201473, 1824382978630270977, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824379450186440706\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824379450186440707\",\"option_values\":[\"INSERT\",\"SELECT\",\"UPDATE\",\"DELETE\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"操作类型\",\"required\":true,\"name\":\"opType\",\"mutability\":\"readWrite\",\"value\":\"SELECT\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824379450186440708\",\"name\":\"sql\",\"description\":\"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"Sql语句\",\"value\":\"select * from acm_dashboard where type= \'appIdp\' and tenant_id =:tenantId;\",\"required\":true,\"page_control\":\"TEXTAREA\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824379450186440709\",\"name\":\"limit\",\"description\":\"查询结果最大返回行数，最大返回100条\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"查询结果最大返回行数\",\"value\":\"10\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824***************\",\"name\":\"input\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"输入参数\",\"value\":{\"tenantId\":\"{{sys_tenant_id}}\"},\"required\":false,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"自定义Sql操作数据库\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"自定义Sql\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"account\":{\"data_base_url\":\"***************************************************************************************\",\"dbname\":\"acm\",\"encode\":\"utf8\",\"file_name\":null,\"host\":\"*************\",\"instance\":null,\"password\":\"Cyb2021@dgsee\",\"port\":\"3306\",\"proxy_ip\":null,\"real_ip\":\"*************\",\"type\":\"MYSQL\",\"username\":\"uep\"},\"account_id\":\"**********252201473\",\"input\":{\"tenantId\":\"iam\"},\"limit\":10,\"op_type\":\"SELECT\",\"sql\":\"select * from acm_dashboard where type= \'appIdp\' and tenant_id =:tenantId;\"}', '{\"result\":true,\"total\":1,\"data\":[{\"tenant_id\":\"iam\",\"update_time\":*************,\"type\":\"appIdp\",\"dashboard\":\"{\\\"types\\\":[\\\"default\\\"],\\\"series\\\":[{\\\"data\\\":[36],\\\"name\\\":\\\"tc\\\"},{\\\"data\\\":[12],\\\"name\\\":\\\"usercenter\\\"}]}\"}]}', 'SUCCESS', 'OK', '2024-08-16 18:04:21', '2024-08-16 18:04:21', NULL, '2024-08-16 17:49:32', 'admin', '2024-08-16 18:04:21', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824382978686623748, 'EndNode', 0, NULL, NULL, NULL, NULL, 1824382978630270977, NULL, NULL, NULL, NULL, 'IDLE', NULL, NULL, NULL, NULL, '2024-08-16 17:49:32', 'admin', '2024-08-16 17:49:32', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824382978686623749, 'ActionNode', 0, 1824382978686623750, 3, 3, NULL, 1824382978630270977, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824379764796989442\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824379764796989443\",\"name\":\"vars\",\"description\":\"添加变量名及其初始值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"添加变量\",\"value\":{\"appIdp\":\"\"},\"required\":true,\"page_control\":\"EXTEND_OBJ\"}],\"name\":\"root\",\"description\":\"创建一个变量，并设置初始值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"创建变量\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"vars\":{\"appIdp\":\"\"}}', '{\"vars\":{\"appIdp\":\"\"}}', 'SUCCESS', 'OK', '2024-08-16 18:04:21', '2024-08-16 18:04:21', NULL, '2024-08-16 17:49:32', 'admin', '2024-08-16 18:04:21', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1824382978686623750, 'ActionNode', 0, 1824382978686623748, 3, 4, NULL, 1824382978630270977, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824379817091571715\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1824379817091571716\",\"name\":\"varPath\",\"description\":\"选择前面步骤已经创建的变量\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"选择要更新的变量\",\"value\":\"{{N1824382978686623749.vars.appIdp}}\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1824379817091571717\",\"name\":\"value\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"设置新的值\",\"value\":\"var dashboard = {{N1824382978686623747.data}};\\nvar str = dashboard[0].dashboard;\\nlet res = JSON.parse(str);\\ndelete res.tenantId;\\nvar result = {};\\nresult.app_auth = res;\\nreturn result;\\n\\n\\n\\n\\n\",\"required\":true,\"page_control\":\"JSON\"}],\"name\":\"root\",\"description\":\"更新变量的值\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"更新变量\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"input\":{\"value\":{\"app_auth\":{\"types\":[\"default\"],\"series\":[{\"data\":[36],\"name\":\"tc\"},{\"data\":[12],\"name\":\"usercenter\"}]}},\"var_path\":\"N1824382978686623749.vars.appIdp\"}}', '{\"code\":200,\"msg\":\"OK\",\"value\":{\"app_auth\":{\"types\":[\"default\"],\"series\":[{\"data\":[36],\"name\":\"tc\"},{\"data\":[12],\"name\":\"usercenter\"}]}}}', 'SUCCESS', 'OK', '2024-08-16 18:04:21', '2024-08-16 18:04:21', NULL, '2024-08-16 17:49:32', 'admin', '2024-08-16 18:04:21', 'admin', 'iam', '0', NULL);


INSERT INTO `acm`.`acm_account_instance` (`id`, `connector_id`, `account_schema_id`, `auth_type`, `name`, `status`, `config`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`) VALUES (1822874457954340865, 5, 5, 'RDBMS_ACCOUNT', '查询的账号', 1, '{\"description\":\"MySQL数据库配置\",\"display_name\":\"MySQL配置\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"MYSQL数据库\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"type\",\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1822874233017995266\",\"update\":true,\"value\":\"MYSQL\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"数据库地址：IP或域名。\",\"display_name\":\"数据库地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"host\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1822874233017995267\",\"update\":true,\"value\":\"*************\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"示例：3306，请填写数据库端口。\",\"display_name\":\"端口\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"port\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"NUMBER\",\"ui_id\":\"1822874233017995268\",\"update\":true,\"value\":\"3306\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"填写你想要连接的数据库名称。\",\"display_name\":\"数据库名称\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"dbname\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1822874233017995269\",\"update\":true,\"value\":\"iam\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"示例：utf8，数据库编码。\",\"display_name\":\"数据库编码\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"encode\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1822874233017995270\",\"update\":true,\"value\":\"utf8\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写用户名，建议创建一个具有有限权限的数据库用户。\",\"display_name\":\"用户名\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"username\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1822874233022189570\",\"update\":true,\"value\":\"uep\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写密码，建议使用复杂密码。\",\"display_name\":\"密码\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"password\",\"page_control\":\"PASSWORD\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1822874233026383874\",\"update\":true,\"value\":\"Cyb2021@dgsee\",\"value_type\":\"FIX_VALUE\"}],\"type\":\"OBJECT\",\"ui_id\":\"1822874233017995265\",\"update\":true,\"value_type\":\"JS_EXP\"}', NULL, '2024-08-12 13:55:13', 'admin', '2024-08-12 13:55:47', 'admin', 'iam');
INSERT INTO `acm`.`acm_account_instance` (`id`, `connector_id`, `account_schema_id`, `auth_type`, `name`, `status`, `config`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`) VALUES (**********252201473, 5, 5, 'RDBMS_ACCOUNT', 'acm库查询账号', 1, '{\"description\":\"MySQL数据库配置\",\"display_name\":\"MySQL配置\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"MYSQL数据库\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"type\",\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1823669499860262914\",\"update\":true,\"value\":\"MYSQL\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"数据库地址：IP或域名。\",\"display_name\":\"数据库地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"host\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1823669499860262915\",\"update\":true,\"value\":\"*************\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"示例：3306，请填写数据库端口。\",\"display_name\":\"端口\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"port\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"NUMBER\",\"ui_id\":\"1823669499860262916\",\"update\":true,\"value\":\"3306\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"填写你想要连接的数据库名称。\",\"display_name\":\"数据库名称\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"dbname\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1823669499860262917\",\"update\":true,\"value\":\"acm\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"示例：utf8，数据库编码。\",\"display_name\":\"数据库编码\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"encode\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1823669499860262918\",\"update\":true,\"value\":\"utf8\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写用户名，建议创建一个具有有限权限的数据库用户。\",\"display_name\":\"用户名\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"username\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1823669499860262919\",\"update\":true,\"value\":\"uep\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写密码，建议使用复杂密码。\",\"display_name\":\"密码\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"password\",\"page_control\":\"PASSWORD\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1823669499860262920\",\"update\":true,\"value\":\"Cyb2021@dgsee\",\"value_type\":\"FIX_VALUE\"}],\"type\":\"OBJECT\",\"ui_id\":\"1823669499860262913\",\"update\":true,\"value_type\":\"JS_EXP\"}', NULL, '2024-08-14 18:34:53', 'admin', '2024-08-14 18:35:10', 'admin', 'iam');


DROP TABLE IF EXISTS `acm_dashboard`;
create table acm_dashboard (
  `tenant_id` varchar(64) NOT NULL COMMENT '租户ID',
  `dashboard` mediumtext  NULL COMMENT '仪表盘配置',
  `type` varchar(128) NOT NULL COMMENT '类型',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  primary key (tenant_id,type)
);