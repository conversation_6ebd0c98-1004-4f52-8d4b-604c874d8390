INSERT INTO acm_app_account(id,name,auth_type,auth_schema,create_time,update_time,tenant_id) values (19, 'DB2', 'RDBMS_ACCOUNT', '{"description":"DB2数据库配置","displayName":"DB2配置","multiValued":false,"mutability":"readWrite","name":"root","subParams":[{"description":"","displayName":"DB2数据库","multiValued":false,"name":"type","type":"STRING","value":"DB2","required":true,"page_control":"HIDDEN","valueType":"FIX_VALUE"},{"description":"数据库地址：IP或域名。","displayName":"数据库地址","multiValued":false,"name":"host","type":"STRING","required":true,"page_control":"TEXT","valueType":"FIX_VALUE"},{"description":"示例：50000，请填写数据库端口。","displayName":"端口","multiValued":false,"name":"port","type":"NUMBER","required":true,"page_control":"TEXT","valueType":"FIX_VALUE"},{"description":"填写你想要连接的数据库名称。","displayName":"数据库名称","multiValued":false,"name":"dbname","type":"STRING","required":true,"page_control":"TEXT","valueType":"FIX_VALUE"},{"description":"示例：utf8，数据库编码。","displayName":"数据库编码","multiValued":false,"name":"encode","type":"STRING","value":"utf8","required":true,"page_control":"TEXT","valueType":"FIX_VALUE"},{"description":"请填写用户名，建议创建一个具有有限权限的数据库用户。","displayName":"用户名","multiValued":false,"name":"username","type":"STRING","required":true,"page_control":"TEXT","valueType":"FIX_VALUE"},{"description":"请填写密码，建议使用复杂密码。","displayName":"密码","multiValued":false,"name":"password","type":"STRING","required":true,"page_control":"PASSWORD","valueType":"FIX_VALUE"}],"type":"OBJECT","valueType":"JS_EXP"}', now(), now(), 'iam');
INSERT INTO acm_app_account(id,name,auth_type,auth_schema,create_time,update_time,tenant_id) VALUES (20, '酷学院', 'COOL_COLLEGE', '{"description":"酷学院认证方式","displayName":"酷学院认证方式","multiValued":false,"mutability":"readWrite","name":"root","subParams":[{"description":"","displayName":"Access Token URL","multiValued":false,"name":"endpoint","type":"STRING","required":true,"page_control":"TEXT","valueType":"FIX_VALUE"},{"description":"","displayName":"ApiKey","multiValued":false,"name":"apiKey","type":"STRING","required":true,"page_control":"TEXT","valueType":"FIX_VALUE"},{"description":"","displayName":"ApiSecret","multiValued":false,"name":"apiSecret","type":"STRING","required":true,"page_control":"TEXT","valueType":"FIX_VALUE"}],"type":"OBJECT","valueType":"JS_EXP"}', sysdate(), sysdate(), 'iam');
INSERT INTO acm_app_account(id,name,auth_type,auth_schema,create_time,update_time,tenant_id) values (21, '汇联易账号', 'HLY', '{"description":"汇联易账号配置","displayName":"汇联易账号","multiValued":false,"mutability":"readWrite","name":"root","subParams":[{"description":"打开汇联易开放平台-【调用说明】获取相应的域名","displayName":"请求域名","multiValued":false,"name":"host","type":"STRING","required":true,"page_control":"TEXT","valueType":"FIX_VALUE"},{"description":"登录汇联易平台-【系统管理】-【安全与审计】-【安全设置】中获取","displayName":"企业id","multiValued":false,"name":"appId","type":"STRING","required":true,"page_control":"TEXT","valueType":"FIX_VALUE"},{"description":"登录汇联易平台-【系统管理】-【安全与审计】-【安全设置】中获取","displayName":"企业密钥","multiValued":false,"name":"appSecret","type":"STRING","required":true,"page_control":"TEXT","valueType":"FIX_VALUE"},{"description":"使用webhook进行消息回调时必填，租户模式填租户id；公司模式填写companyOID","displayName":"公司id","multiValued":false,"name":"companyId","type":"STRING","required":false,"page_control":"TEXT","valueType":"FIX_VALUE"},{"description":"使用webhook进行消息回调时必填，登录汇联易平台-【系统管理】-【系统对接】-【外部系统接口回调】新建API回调设置时获取","displayName":"加解密key","multiValued":false,"name":"encodingAesKey","type":"STRING","required":false,"page_control":"TEXT","valueType":"FIX_VALUE"},{"description":"使用webhook进行消息回调时必填，登录汇联易平台-【系统管理】-【系统对接】-【外部系统接口回调】新建API回调设置时获取","displayName":"加签token","multiValued":false,"name":"encodingToken","type":"STRING","required":false,"page_control":"TEXT","valueType":"FIX_VALUE"}],"type":"OBJECT","valueType":"JS_EXP"}', now(), now(), 'iam');
INSERT INTO acm_app_account(id,name,auth_type,auth_schema,create_time,update_time,tenant_id) values (22, '企企通账号', 'QQT', '{"description":"企企通账号配置","displayName":"企企通账号","multiValued":false,"mutability":"readWrite","name":"root","subParams":[{"description":"登录企企通接口平台-【应用管理】中获取","displayName":"应用key","multiValued":false,"name":"appKey","type":"STRING","required":true,"page_control":"TEXT","valueType":"FIX_VALUE"},{"description":"登录企企通接口平台-【应用管理】中获取","displayName":"应用密钥","multiValued":false,"name":"appSecret","type":"STRING","required":true,"page_control":"TEXT","valueType":"FIX_VALUE"}],"type":"OBJECT","valueType":"JS_EXP"}', now(), now(), 'iam');

insert into acm_app_connector(id, name, description, build_type, app_package, ver_name, ver_number, create_time, update_time, tenant_id) values (13,'DB2数据库','DB2应用','BuiltIn','db2','1.0',1,now(),now(),'iam');
insert into acm_app_action(id,action_key,is_trigger,name,description,connector_id,account_schema_id,protocol,config_schema,create_time,update_time,tenant_id) values(16,'sql_db2',0,'自定义Sql','自定义Sql操作数据库',13,19,'RDBMS','{"description":"自定义Sql操作数据库","display_name":"自定义Sql","multi_valued":false,"mutability":"readWrite","name":"root","page_control":"FIXED_OBJ","required":true,"sub_params":[{"description":"","display_name":"操作类型","multi_valued":false,"mutability":"readWrite","name":"opType","option_values":["INSERT","SELECT","UPDATE","DELETE"],"page_control":"SELECT","required":true,"type":"STRING","value_type":"FIX_VALUE"},{"description":"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入","display_name":"Sql语句","multi_valued":false,"mutability":"readWrite","name":"sql","page_control":"TEXTAREA","required":true,"type":"STRING","value_type":"FIX_VALUE"},{"description":"查询结果最大返回行数，最大返回100条","display_name":"查询结果最大返回行数","multi_valued":false,"mutability":"readWrite","name":"limit","page_control":"TEXT","required":true,"type":"NUMBER","value_type":"FIX_VALUE"},{"description":"","display_name":"输入参数","multi_valued":false,"mutability":"readWrite","name":"input","page_control":"EXTEND_OBJ","required":false,"type":"OBJECT","value_type":"JS_EXP"}],"type":"OBJECT","value_type":"JS_EXP"}',now(),now(),'iam');

INSERT INTO acm_app_connector (id, name, description, icon, build_type, app_package, ver_name, ver_number, create_time, create_by, update_time, update_by, tenant_id) VALUES (14, '酷学院', '酷学院', NULL, 'BuiltIn', 'cool_college', '1.0', 1, sysdate(), NULL, sysdate(), NULL, 'iam');

-- 连接中心加入汇联易
INSERT INTO acm_app_connector (id, name, description, icon, build_type, app_package, ver_name, ver_number, create_time, create_by, update_time, update_by, tenant_id) VALUES (15, '汇联易', '汇联易', NULL, 'BuiltIn', 'hly', '1.0', 1, '2023-12-04 16:23:10', NULL, '2023-12-04 16:23:10', NULL, 'iam');
INSERT INTO acm_app_action (id, action_key, is_trigger, name, description, connector_id, account_schema_id, protocol, status, config_schema, create_time, create_by, update_time, update_by, tenant_id) VALUES (17, 'hly_event', 1, '汇联易事件通知', '当发生在汇联易订阅的事件时，实时触发流程', 15, 21, 'WEBHOOK', 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"回调地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"hookUrl\",\"page_control\":\"LABEL\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619267\",\"update\":true,\"value\":\"{{server}}/acm/flows/start/13/{{flowId}}/{{apiKey}}\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"接收数据\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"value\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619271\",\"update\":true,\"value_type\":\"JS_EXP\"}],\"type\":\"OBJECT\",\"ui_id\":\"1739531082927247362\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2023-12-26 10:37:29', 'admin', '2023-12-26 14:18:28', 'admin', 'iam');

-- 连接中心加入企企通
INSERT INTO acm_app_connector (id, name, description, icon, build_type, app_package, ver_name, ver_number, create_time, create_by, update_time, update_by, tenant_id) VALUES (16, '企企通', '企企通', NULL, 'BuiltIn', 'qqt', '1.0', 1, '2023-12-04 16:23:10', NULL, '2023-12-04 16:23:10', NULL, 'iam');

