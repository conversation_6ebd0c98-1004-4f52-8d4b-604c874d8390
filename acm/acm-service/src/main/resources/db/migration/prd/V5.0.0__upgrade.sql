alter table acm_flow_instance add column is_deleted tinyint(1) DEFAULT '0' COMMENT '逻辑删除状态: 0-未删除 1-已删除';

UPDATE `acm`.`acm_app_action` SET  `config_schema` = '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"type\",\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1777885711452794801\",\"update\":true,\"value\":\"ENCRYPT\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"alg\",\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1777885711452794802\",\"update\":true,\"value\":\"AES\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"选择用于数据加密的算法，例如 ECB、CBC\",\"display_name\":\"选择加密模式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"mode\",\"option_values\":[\"CBC\",\"ECB\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1777885711452794803\",\"update\":true,\"value\":\"\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"输入用于加密和解密的密钥字符串\",\"display_name\":\"输入密钥\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"secret\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1777885711452794805\",\"update\":true,\"value\":\"\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"选择密钥的编码方式，如 Base64、Hex、HEX、PlainText\",\"display_name\":\"选择密钥编码格式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"keyEncode\",\"option_values\":[\"Base64\",\"Hex\",\"HEX\",\"PlainText\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1735267166630\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"AES算法的密钥长度可选16位、24位、32位，超过所选位数截取，不足则在末尾填充\'\\u0000\'补足\",\"display_name\":\"指定密钥长度\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"secretLength\",\"option_values\":[\"16\",\"24\",\"32\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"NUMBER\",\"ui_id\":\"1777885711452794806\",\"update\":true,\"value\":\"\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"选择用于块补齐的填充模式，如 PKCS5Padding\",\"display_name\":\"选择填充方式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"fillMode\",\"option_values\":[\"PKCS5_PADDING\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1777885711452794807\",\"update\":true,\"value\":\"\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"输入或粘贴需要加密的文字或数据\",\"display_name\":\"输入待加密内容\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"content\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1777885711452794808\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"选择输入内容的编码方式，如 Base64、Hex、HEX、PlainText\",\"display_name\":\"选择待加密内容编码\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"contentEncode\",\"option_values\":[\"Base64\",\"Hex\",\"HEX\",\"PlainText\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1735007451680\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"AES算法的随机向量长度为16字符，超过16位截取前16位，不足则在末尾填充\'\\u0000\'补足。ECB不需要\\n用于某些加密模式（例如 CBC）的初始值，确保安全性。\",\"display_name\":\"输入随机向量（IV）\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"randomVector\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1777885711452794809\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"选择输出编码格式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"encode\",\"option_values\":[\"Base64\",\"Hex\",\"HEX\",\"PlainText\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1712917828726\",\"update\":true,\"value\":\"Base64\",\"value_type\":\"FIX_VALUE\"}],\"type\":\"OBJECT\",\"ui_id\":\"1872477490784698369\",\"update\":true,\"value_type\":\"JS_EXP\"}' WHERE `id` = 50;
UPDATE `acm`.`acm_app_action` SET  `config_schema` = '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"type\",\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1777885711452794801\",\"update\":true,\"value\":\"DECRYPT\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"alg\",\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1777885711452794802\",\"update\":true,\"value\":\"AES\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"选择用于数据加密的算法，例如 ECB、CBC\",\"display_name\":\"选择解密模式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"mode\",\"option_values\":[\"CBC\",\"ECB\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1777885711452794803\",\"update\":true,\"value\":\"\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"输入用于解密的密钥字符串\",\"display_name\":\"输入密钥\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"secret\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1777885711452794805\",\"update\":true,\"value\":\"\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"选择密钥的编码方式，如 Base64、Hex、HEX、PlainText\",\"display_name\":\"选择密钥编码格式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"keyEncode\",\"option_values\":[\"Base64\",\"Hex\",\"HEX\",\"PlainText\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1735269683043\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"AES算法的密钥长度可选16位、24位、32位，超过所选位数截取，不足则在末尾填充\'\\u0000\'补足\",\"display_name\":\"指定密钥长度\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"secretLength\",\"option_values\":[\"16\",\"24\",\"32\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"NUMBER\",\"ui_id\":\"1777885711452794806\",\"update\":true,\"value\":\"\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"选择用于块补齐的填充模式，如 PKCS5Padding\",\"display_name\":\"选择填充方式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"fillMode\",\"option_values\":[\"PKCS5_PADDING\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1777885711452794807\",\"update\":true,\"value\":\"\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"输入或粘贴需要解密的文字或数据\",\"display_name\":\"输入待解密内容\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"content\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1777885711452794808\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"选择输入内容的编码方式，如 Base64、Hex、HEX、PlainText\",\"display_name\":\"选择待加密内容编码\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"contentEncode\",\"option_values\":[\"Base64\",\"Hex\",\"HEX\",\"PlainText\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1735269821844\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"AES算法的随机向量长度为16字符，超过16位截取前16位，不足则在末尾填充\'\\u0000\'补足。ECB不需要\\n用于某些解密模式（例如 CBC）的初始值，确保安全性。\",\"display_name\":\"输入随机向量（IV）\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"randomVector\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1777885711452794809\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"选择输出编码格式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"encode\",\"option_values\":[\"Base64\",\"Hex\",\"HEX\",\"PlainText\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1712917956013\",\"update\":true,\"value_type\":\"FIX_VALUE\"}],\"type\":\"OBJECT\",\"ui_id\":\"1872484018342264833\",\"update\":true,\"value_type\":\"JS_EXP\"}' WHERE `id` = 51;
UPDATE `acm`.`acm_app_action` SET  `config_schema` = '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"type\",\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1777885711452794811\",\"update\":true,\"value\":\"ENCRYPT\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"alg\",\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1777885711452794812\",\"update\":true,\"value\":\"DES\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"选择用于数据加密的算法，例如 ECB、CBC\",\"display_name\":\"选择加密模式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"mode\",\"option_values\":[\"CBC\",\"ECB\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1777885711452794813\",\"update\":true,\"value\":\"\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"输入用于加密的密钥字符串\",\"display_name\":\"输入密钥\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"secret\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1777885711452794814\",\"update\":true,\"value\":\"\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"选择密钥的编码方式，如 Base64、Hex、HEX、PlainText\",\"display_name\":\"选择密钥编码格式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"keyEncode\",\"option_values\":[\"Base64\",\"Hex\",\"HEX\",\"PlainText\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1735270057713\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"选择用于块补齐的填充模式，如 PKCS5Padding\",\"display_name\":\"选择填充方式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"fillMode\",\"option_values\":[\"PKCS5_PADDING\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1777885711452794815\",\"update\":true,\"value\":\"\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"输入或粘贴需要加密的文字或数据\",\"display_name\":\"输入待加密内容\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"content\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1777885711452794816\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"选择输入内容的编码方式，如 Base64、Hex、HEX、PlainText\",\"display_name\":\"选择待加密内容编码\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"contentEncode\",\"option_values\":[\"Base64\",\"Hex\",\"HEX\",\"PlainText\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1735270173017\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"AES算法的随机向量长度为8字符，超过8位截取，不足则在末尾填充\'\\u0000\'补足。ECB不需要\\n用于某些加密模式（例如 CBC）的初始值，确保安全性。\",\"display_name\":\"输入随机向量（IV）\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"randomVector\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1712913135326\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"选择输出编码格式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"encode\",\"option_values\":[\"Base64\",\"Hex\",\"HEX\",\"PlainText\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1712918027029\",\"update\":true,\"value_type\":\"FIX_VALUE\"}],\"type\":\"OBJECT\",\"ui_id\":\"1872485431604609026\",\"update\":true,\"value_type\":\"JS_EXP\"}' WHERE `id` = 52;
UPDATE `acm`.`acm_app_action` SET  `config_schema` = '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"type\",\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1777885711452794811\",\"update\":true,\"value\":\"DECRYPT\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"alg\",\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1777885711452794812\",\"update\":true,\"value\":\"DES\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"选择用于数据解密的算法，例如 ECB、CBC\",\"display_name\":\"选择解密模式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"mode\",\"option_values\":[\"CBC\",\"ECB\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1777885711452794813\",\"update\":true,\"value\":\"\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"输入用于解密的密钥字符串\",\"display_name\":\"输入密钥\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"secret\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1777885711452794814\",\"update\":true,\"value\":\"\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"选择密钥的编码方式，如 Base64、Hex、HEX、PlainText\",\"display_name\":\"选择密钥编码格式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"keyEncode\",\"option_values\":[\"Base64\",\"Hex\",\"HEX\",\"PlainText\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1735270893093\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"选择用于块补齐的填充模式，如 PKCS5Padding\",\"display_name\":\"选择填充方式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"fillMode\",\"option_values\":[\"PKCS5_PADDING\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1777885711452794815\",\"update\":true,\"value\":\"\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"输入或粘贴需要解密的文字或数据\",\"display_name\":\"输入待解密内容\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"content\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1777885711452794816\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"选择输入内容的编码方式，如 Base64、Hex、HEX、PlainText\",\"display_name\":\"选择待加密内容编码\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"contentEncode\",\"option_values\":[\"Base64\",\"Hex\",\"HEX\",\"PlainText\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1735271030061\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"AES算法的随机向量长度为8字符，超过8位截取，不足则在末尾填充\'\\u0000\'补足。ECB不需要\\n用于某些加密模式（例如 CBC）的初始值，确保安全性。\",\"display_name\":\"输入随机向量（IV）\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"randomVector\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1712913217553\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"选择输出编码格式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"encode\",\"option_values\":[\"Base64\",\"Hex\",\"HEX\",\"PlainText\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1712918117671\",\"update\":true,\"value_type\":\"FIX_VALUE\"}],\"type\":\"OBJECT\",\"ui_id\":\"1872489057307787266\",\"update\":true,\"value_type\":\"JS_EXP\"}' WHERE `id` = 53;
UPDATE `acm`.`acm_app_action` SET  `config_schema` = '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"type\",\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1777885711452794801\",\"update\":true,\"value\":\"ENCRYPT\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"算法\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"alg\",\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1777885711452794802\",\"update\":true,\"value\":\"DESede\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"选择用于数据加密的算法，例如 ECB、CBC\",\"display_name\":\"选择加密模式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"mode\",\"option_values\":[\"CBC\",\"ECB\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1777885711452794803\",\"update\":true,\"value\":\"\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"输入用于加密和解密的密钥字符串\",\"display_name\":\"输入密钥\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"secret\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1777885711452794805\",\"update\":true,\"value\":\"\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"选择密钥的编码方式，如 Base64、Hex、HEX、PlainText\",\"display_name\":\"选择密钥编码格式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"keyEncode\",\"option_values\":[\"Base64\",\"Hex\",\"HEX\",\"PlainText\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1735272759900\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"选择用于块补齐的填充模式，如 PKCS5Padding\",\"display_name\":\"选择填充方式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"fillMode\",\"option_values\":[\"PKCS5_PADDING\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1777885711452794807\",\"update\":true,\"value\":\"\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"输入或粘贴需要加密的文字或数据\",\"display_name\":\"输入待加密内容\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"content\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1777885711452794808\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"选择输入内容的编码方式，如 Base64、Hex、HEX、PlainText\",\"display_name\":\"选择待加密内容编码\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"contentEncode\",\"option_values\":[\"Base64\",\"Hex\",\"HEX\",\"PlainText\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1735272893157\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"AES算法的随机向量长度为8字符，超过8位截取前8位，不足则在末尾填充\'\\u0000\'补足。ECB不需要\",\"display_name\":\"输入随机向量（IV）\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"randomVector\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1777885711452794809\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"选择输出编码格式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"encode\",\"option_values\":[\"Base64\",\"Hex\",\"HEX\",\"PlainText\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1712917828726\",\"update\":true,\"value\":\"Base64\",\"value_type\":\"FIX_VALUE\"}],\"type\":\"OBJECT\",\"ui_id\":\"1872496809304326145\",\"update\":true,\"value_type\":\"JS_EXP\"}' WHERE `id` = 54;
UPDATE `acm`.`acm_app_action` SET  `config_schema` = '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"type\",\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1777885711452794801\",\"update\":true,\"value\":\"DECRYPT\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"算法\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"alg\",\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1777885711452794802\",\"update\":true,\"value\":\"DESede\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"选择用于数据加密的算法，例如 ECB、CBC\",\"display_name\":\"选择解密模式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"mode\",\"option_values\":[\"CBC\",\"ECB\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1777885711452794803\",\"update\":true,\"value\":\"\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"输入用于解密的密钥字符串\",\"display_name\":\"输入密钥\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"secret\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1777885711452794805\",\"update\":true,\"value\":\"\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"选择密钥的编码方式，如 Base64、Hex、HEX、PlainText\",\"display_name\":\"选择密钥编码格式\\t\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"keyEncode\",\"option_values\":[\"Base64\",\"Hex\",\"HEX\",\"PlainText\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1735278403671\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"选择用于块补齐的填充模式，如 PKCS5Padding\",\"display_name\":\"选择填充方式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"fillMode\",\"option_values\":[\"PKCS5_PADDING\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1777885711452794807\",\"update\":true,\"value\":\"\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"输入或粘贴需要加密的文字或数据\",\"display_name\":\"输入待解密内容\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"content\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1777885711452794808\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"选择输入内容的编码方式，如 Base64、Hex、HEX、PlainText\",\"display_name\":\"选择待解密内容编码\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"contentEncode\",\"option_values\":[\"Base64\",\"Hex\",\"HEX\",\"PlainText\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1735278518224\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"AES算法的随机向量长度为16字符，超过16位截取前16位，不足则在末尾填充\'\\u0000\'补足。ECB不需要\",\"display_name\":\"输入随机向量（IV）\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"randomVector\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1777885711452794809\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"选择输出编码格式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"encode\",\"option_values\":[\"Base64\",\"Hex\",\"HEX\",\"PlainText\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1712917956013\",\"update\":true,\"value_type\":\"FIX_VALUE\"}],\"type\":\"OBJECT\",\"ui_id\":\"1872520497277894657\",\"update\":true,\"value_type\":\"JS_EXP\"}' WHERE `id` = 55;
UPDATE `acm`.`acm_app_action` SET  `config_schema` = '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"type\",\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1777885711452794811\",\"update\":true,\"value\":\"ENCRYPT\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"算法\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"alg\",\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1777885711452794812\",\"update\":true,\"value\":\"BASE64\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"输入或粘贴需要加密的文字或数据\",\"display_name\":\"输入待加密内容\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"content\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1777885711452794816\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"选择加密结果的编码格式，例如 Base64、Hex、HEX、PlainText\",\"display_name\":\"选择输出编码格式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"encode\",\"option_values\":[\"Base64\",\"Hex\",\"HEX\",\"PlainText\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1735269362993\",\"update\":true,\"value_type\":\"FIX_VALUE\"}],\"type\":\"OBJECT\",\"ui_id\":\"1872482100911996929\",\"update\":true,\"value_type\":\"JS_EXP\"}' WHERE `id` = 56;
UPDATE `acm`.`acm_app_action` SET  `config_schema` = '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"type\",\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1777885711452794811\",\"update\":true,\"value\":\"DECRYPT\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"算法\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"alg\",\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1777885711452794812\",\"update\":true,\"value\":\"BASE64\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"输入待解密内容\",\"display_name\":\"输入待解密内容\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"content\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1777885711452794816\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"选择加密结果的编码格式，例如 Base64、Hex、HEX、PlainText\",\"display_name\":\"选择输出编码格式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"encode\",\"option_values\":[\"Base64\",\"Hex\",\"HEX\",\"PlainText\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1735269251774\",\"update\":true,\"value\":\"Base64\",\"value_type\":\"FIX_VALUE\"}],\"type\":\"OBJECT\",\"ui_id\":\"1872481511511621634\",\"update\":true,\"value_type\":\"JS_EXP\"}' WHERE `id` = 57;
UPDATE `acm`.`acm_app_action` SET  `config_schema` = '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"签名算法\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"alg\",\"option_values\":[\"HmacMD5\",\"HmacSHA1\",\"HmacSHA224\",\"HmacSHA256\",\"HmacSHA384\",\"HmacSHA512\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1712918307343\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"输入用于签名的密钥字符串\",\"display_name\":\"输入签名密钥\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"secret\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1712918382782\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"选择密钥的编码方式，如 Base64、Hex、HEX、PlainText\",\"display_name\":\"选择密钥编码格式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"keyEncode\",\"option_values\":[\"Base64\",\"Hex\",\"HEX\",\"PlainText\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1735271215591\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"输入或粘贴需要签名的文字或数据\",\"display_name\":\"输入待签名内容\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"content\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1712918403854\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"选择输入内容的编码方式，如 Base64、Hex、HEX、PlainText\",\"display_name\":\"选择待签名内容编码\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"contentEncode\",\"option_values\":[\"Base64\",\"Hex\",\"HEX\",\"PlainText\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1735271319624\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"选择输出编码格式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"encode\",\"option_values\":[\"Base64\",\"Hex\",\"HEX\",\"PlainText\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1712918444379\",\"update\":true,\"value_type\":\"FIX_VALUE\"}],\"type\":\"OBJECT\",\"ui_id\":\"1872490121406906369\",\"update\":true,\"value_type\":\"JS_EXP\"}' WHERE `id` = 58;
UPDATE `acm`.`acm_app_action` SET  `config_schema` = '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"display_name\":\"加密算法\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"alg\",\"option_values\":[\"SHA-1\",\"SHA-224\",\"SHA-256\",\"SHA-384\",\"SHA-512\",\"MD5\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1712918634989\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"输入或粘贴需要加密的文字或数据\",\"display_name\":\"输入待加密内容\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"content\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1712918652117\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"选择输入内容的编码方式，如 Base64、Hex、HEX、PlainText\",\"display_name\":\"选择待解密内容编码\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"contentEncode\",\"option_values\":[\"Base64\",\"Hex\",\"HEX\",\"PlainText\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1735272391773\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"输出编码格式\",\"display_name\":\"选择输出编码格式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"encode\",\"option_values\":[\"Base64\",\"Hex\",\"HEX\",\"PlainText\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"FIX_VALUE\"}],\"type\":\"OBJECT\",\"ui_id\":\"1872494833556455425\",\"update\":true,\"value_type\":\"JS_EXP\"}' WHERE `id` = 59;

-- 飞书增加动作
INSERT IGNORE INTO `acm`.`acm_app_action` (`id`, `action_key`, `is_trigger`, `name`, `description`, `connector_id`, `account_schema_id`, `protocol`, `action_type`, `status`, `config_schema`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `sample_output`, `node_struct`) VALUES (1874642006161027073, '1874642006122033153', 0, '通过手机号或邮箱获取用户 ID', '', 1698956136478265345, 13, 'HTTP', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619266\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"LABEL\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619267\",\"update\":true,\"value\":\"https://open.feishu.cn/open-apis/contact/v3/users/batch_get_id\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619268\",\"update\":true,\"value\":\"application/json; charset=utf-8\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"user_id_type\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query.user_id_type\",\"option_values\":[\"open_id\",\"union_id\",\"user_id\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1699309157332619269\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\",\"display_name\":\"用户邮箱\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.emails\",\"page_control\":\"TEXTAREA\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619270\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\",\"display_name\":\"用户手机号\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.mobiles\",\"page_control\":\"TEXTAREA\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619271\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"查询结果是否包含离职员工的用户信息\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.include_resigned\",\"option_values\":[\"true\",\"false\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"BOOLEAN\",\"ui_id\":\"1735784970675\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1874644630980390914\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2025-01-02 10:20:58', 'admin', '2025-01-02 10:31:24', 'admin', 'iam', NULL, 'SINGLE');

INSERT IGNORE INTO `acm`.`acm_app_connector` (`id`, `name`, `description`, `icon`, `build_type`, `app_domain_category`, `app_action_category`, `app_package`, `deployment_type`, `ver_name`, `ver_number`, `ver_type`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`) VALUES (1864585974697222146, '钉钉专属待办', NULL, 'data:image/png;base64,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', 'BuiltIn', 'COLLABORATION', '[{\"label\":\"应⽤类⽬信息\",\"value\":\"Application_Category_Information\"},{\"label\":\"待办任务\",\"value\":\"todo_Tasks\"}]', '1864585974700175361', 'SaasApp', '1', 1, 'RELEASE', '2024-12-05 16:21:53', 'admin', '2025-01-06 10:53:02', 'admin', 'iam');
INSERT IGNORE INTO `acm`.`acm_app_action` (`id`, `action_key`, `is_trigger`, `name`, `description`, `connector_id`, `account_schema_id`, `protocol`, `action_type`, `status`, `config_schema`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `sample_output`, `node_struct`) VALUES (1864589953304944641, '1864589953307897859', 0, '注册应⽤类⽬信息', '调⽤本接⼝，应⽤可将基础信息以及需要推送到专属待办应⽤的类⽬信息注册到专属待\n办业务中，应⽤可多次注册', 1864585974697222146, 6, 'HTTP', 'Application_Category_Information', 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619266\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619267\",\"update\":true,\"value\":\"https://api.dingtalk.com/v1.0/todoEE/apps/categories/sourceConfigs/register\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"应⽤clientId +\\nbizCategoryId做唯\\n⼀性约束,⻓度限制：100\",\"display_name\":\"业务分类标识ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.bizCategoryId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1733388238416\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"⻓度限制：100\",\"display_name\":\"业务分类标识名称\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.bizCategoryName\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1864597136179326978\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2024-12-05 16:37:42', 'admin', '2024-12-06 14:58:22', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO `acm`.`acm_app_action` (`id`, `action_key`, `is_trigger`, `name`, `description`, `connector_id`, `account_schema_id`, `protocol`, `action_type`, `status`, `config_schema`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `sample_output`, `node_struct`) VALUES (1864597385703907330, '1864597385702666243', 0, '查询应⽤注册类⽬信息列表', '调⽤本接⼝，应⽤可以查询到已注册的分类信息列表。', 1864585974697222146, 6, 'HTTP', 'Application_Category_Information', 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619266\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619267\",\"update\":true,\"value\":\"https://api.dingtalk.com/v1.0/todoEE/apps/categories/sourceConfigs/lists/query\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"⾸⻚不填，其它⻚必\\n填,单次拉取数量为100\\n条,⽤于连续翻页，默\\n认只能向后翻页。\\n由于在⼀次查询的\\n翻页过程中token\\n长期有效，您可以\\n通过缓存并使⽤之\\n前的token实现向\\n前翻页。\\n\",\"display_name\":\"分⻚游标\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.nextToken\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1871400752554831873\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2024-12-05 17:07:14', 'admin', '2024-12-24 19:48:53', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO `acm`.`acm_app_action` (`id`, `action_key`, `is_trigger`, `name`, `description`, `connector_id`, `account_schema_id`, `protocol`, `action_type`, `status`, `config_schema`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `sample_output`, `node_struct`) VALUES (1864598421340479490, '1864598421339238402', 0, '修改应⽤类⽬注册信息', '调⽤本接⼝，应⽤可修改推送到专属待办应⽤的类⽬信息', 1864585974697222146, 6, 'HTTP', 'Application_Category_Information', 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619266\",\"update\":true,\"value\":\"PUT\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619267\",\"update\":true,\"value\":\"https://api.dingtalk.com/v1.0/todoEE/apps/categories/sourceConfigs\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"说明：\\n应⽤agentId +\\nbizCategoryId做唯\\n⼀性约束\",\"display_name\":\"业务分类标识ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.bizCategoryId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1733389890177\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"业务分类标识名称\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.bizCategoryName\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1864598933040132098\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2024-12-05 17:11:21', 'admin', '2024-12-05 17:13:23', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO `acm`.`acm_app_action` (`id`, `action_key`, `is_trigger`, `name`, `description`, `connector_id`, `account_schema_id`, `protocol`, `action_type`, `status`, `config_schema`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `sample_output`, `node_struct`) VALUES (1864599032681259009, '1864599032684212225', 0, '删除应⽤类⽬信息', '调⽤本接⼝，应⽤可将注册到专属待办业务中的类型信息删除。', 1864585974697222146, 6, 'HTTP', 'Application_Category_Information', 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619266\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619267\",\"update\":true,\"value\":\"https://api.dingtalk.com/v1.0/todoEE/apps/categories/sourceConfigs/remove\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"说明：\\n应⽤agentId +\\nbizCategoryId做唯\\n⼀性约束\",\"display_name\":\"业务分类标识ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.bizCategoryId\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1733390036925\",\"update\":true,\"value\":\"\",\"value_type\":\"ADAPTOR\"},{\"display_name\":\"业务分类标识名称\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.bizCategoryName\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1864599760656003073\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2024-12-05 17:13:47', 'admin', '2024-12-06 18:08:28', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO `acm`.`acm_app_action` (`id`, `action_key`, `is_trigger`, `name`, `description`, `connector_id`, `account_schema_id`, `protocol`, `action_type`, `status`, `config_schema`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `sample_output`, `node_struct`) VALUES (1864603270568271874, '1864603270554447874', 0, '专属-创建待办', '调⽤本接⼝，应⽤可创建⼀个待办任务', 1864585974697222146, 6, 'HTTP', 'todo_Tasks', 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619266\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619267\",\"update\":true,\"value\":\"https://api.dingtalk.com/v1.0/todoEE/apps/users/tasks\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"三⽅业务系统的唯⼀标识ID，即业务\\nID，⽤户⾃定义，⽤于审计和展示\",\"display_name\":\"业务 ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.sourceId\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1733391977928\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"三⽅业务系统的名称，⽤户⾃定义，\\n⽤于展示在待办列表\",\"display_name\":\"三⽅业务系统的名称\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.sourceTitle\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1733396261113\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"待办标题，最⼤⻓度1000。\",\"display_name\":\"待办标题\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.subject\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1733392007040\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\",\"display_name\":\"待办所属类⽬信息\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.bizCategoryId\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1733392007770\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"待办备注描述，最⼤⻓度4000。\",\"display_name\":\"待办备注描述\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.description\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1733392008416\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"截⽌时间，Unix时间戳，单位毫秒。\",\"display_name\":\"截⽌时间\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.dueTime\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"NUMBER\",\"ui_id\":\"1733392009033\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"执⾏者的unionId，最⼤数量1000。\",\"display_name\":\"执⾏者的unionId\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.executorIds\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1733392010133\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"创建第三⽅待办时，需传⼊⾃身\\n应⽤详情⻚链接。如果创建第三⽅待办时配置了DING\\n通知能⼒，appUrl需要⽀持以\\ndingtalk协议打开。\",\"display_name\":\"APP端详情⻚url跳转地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.detailUrl.appUrl\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1733392010826\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"创建第三⽅待办时，需传⼊⾃身\\n应⽤详情⻚链接。\",\"display_name\":\"PC端详情⻚url跳转地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.detailUrl.webUrl\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1733392011537\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"默认20，取值\\n10：较低\\n20：普通\\n30：紧急\\n40：⾮常紧急\",\"display_name\":\"优先级\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.priority\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"NUMBER\",\"ui_id\":\"1733392032226\",\"update\":true,\"value\":\"20\",\"value_type\":\"ADAPTOR\"},{\"description\":\"DING通知配置，⽬前仅⽀持取值为\\n1，表示应⽤内DING\",\"display_name\":\"DING通知配置\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.notifyConfigs.dingNotify\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1733392033184\",\"update\":true,\"value\":\"1\",\"value_type\":\"ADAPTOR\"},{\"display_name\":\"三⽅系统待办的⾃定义字段\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.customFields\",\"option_values\":[],\"page_control\":\"TEXTAREA\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1733392034650\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"todo：待办\\nread：待阅\",\"display_name\":\"类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.type\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1733392039070\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"操作者用户的unionId\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.operatorId\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1869706103964430338\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2024-12-05 17:30:37', 'admin', '2024-12-19 19:27:27', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO `acm`.`acm_app_action` (`id`, `action_key`, `is_trigger`, `name`, `description`, `connector_id`, `account_schema_id`, `protocol`, `action_type`, `status`, `config_schema`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `sample_output`, `node_struct`) VALUES (1864625582512332801, '1864625582498508803', 0, '专属-修改待办', '调⽤本接⼝，应⽤可修改⼀个待办任务', 1864585974697222146, 6, 'HTTP', 'todo_Tasks', 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619266\",\"update\":true,\"value\":\"PUT\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619267\",\"update\":true,\"value\":\"https://api.dingtalk.com/v1.0/todoEE/apps/users/tasks/infos\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"待办ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.taskId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1733396366107\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"最⼤⻓度1000\",\"display_name\":\"待办标题\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.subject\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1733396405343\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"最⼤⻓度4000\",\"display_name\":\"待办描述\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.description\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1733396405937\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"Unix时间戳，单位毫秒\",\"display_name\":\"截⽌时间\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.dueTime\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"NUMBER\",\"ui_id\":\"1733396406627\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"最⼤数量1000\",\"display_name\":\"执⾏者的unionId列表\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.executorIds\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1869697854984810497\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2024-12-05 18:59:17', 'admin', '2024-12-19 19:09:14', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO `acm`.`acm_app_action` (`id`, `action_key`, `is_trigger`, `name`, `description`, `connector_id`, `account_schema_id`, `protocol`, `action_type`, `status`, `config_schema`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `sample_output`, `node_struct`) VALUES (1864626509357047810, '1864626509360001026', 0, '专属-删除待办', '调⽤本接⼝，应⽤可删除⼀个待办任务', 1864585974697222146, 6, 'HTTP', 'todo_Tasks', 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619266\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619267\",\"update\":true,\"value\":\"https://api.dingtalk.com/v1.0/todoEE/apps/users/tasks/remove\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"待办ID List\",\"display_name\":\"待办ID\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.taskIds\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1733396587151\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"⼀般是操作⽤户ID，标识三⽅系统中的操\\n作⼈等，业务⾃定义传⼊，可以是钉钉的\\nUnionId，仅⽤于展示。\",\"display_name\":\"操作⽤户ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.operatorId\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1869706838756159490\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2024-12-05 19:02:58', 'admin', '2024-12-19 19:30:22', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO `acm`.`acm_app_action` (`id`, `action_key`, `is_trigger`, `name`, `description`, `connector_id`, `account_schema_id`, `protocol`, `action_type`, `status`, `config_schema`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `sample_output`, `node_struct`) VALUES (1864627168714219521, '1864627168717172737', 0, '专属-修改待办状态', '调⽤本接⼝，应⽤完成⼀个待办任务', 1864585974697222146, 6, 'HTTP', 'todo_Tasks', 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619266\",\"update\":true,\"value\":\"PUT\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619267\",\"update\":true,\"value\":\"https://api.dingtalk.com/v1.0/todoEE/apps/users/tasks/statuses\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"操作者的UnionId\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.operatorId\",\"option_values\":[],\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1734606260593\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"任务Id\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.userTaskStatuses\",\"option_values\":[\"执⾏者完成状态\"],\"page_control\":\"TEXTAREA\",\"required\":true,\"type\":\"OBJECT\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"[{taskId:\\\"123123\\\",done:false}]\",\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1869717557060956161\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2024-12-05 19:05:35', 'admin', '2024-12-19 20:12:58', 'admin', 'iam', NULL, 'SINGLE');
INSERT IGNORE INTO `acm`.`acm_app_action` (`id`, `action_key`, `is_trigger`, `name`, `description`, `connector_id`, `account_schema_id`, `protocol`, `action_type`, `status`, `config_schema`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `sample_output`, `node_struct`) VALUES (1864628008900419585, '1864628008890789890', 0, '专属-查询创建待办列表', '调⽤本接⼝，查询⽤户待办列表', 1864585974697222146, 6, 'HTTP', 'todo_Tasks', 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619266\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619267\",\"update\":true,\"value\":\"https://api.dingtalk.com//v1.0/todoEE/apps/users/tasks/list\",\"value_type\":\"FIX_VALUE\"},{\"display_name\":\"任务是否完成状态\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.done\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"BOOLEAN\",\"ui_id\":\"1733396952252\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"todo/read\",\"display_name\":\"任务类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.type\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1733396968861\",\"update\":true,\"value\":\"\",\"value_type\":\"ADAPTOR\"},{\"display_name\":\"当前分⻚⻚码\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.pageNumber\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"NUMBER\",\"ui_id\":\"1733396969477\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"display_name\":\"当前分⻚每⻚数量\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.pageSize\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"NUMBER\",\"ui_id\":\"1733396970731\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1869696990836551681\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2024-12-05 19:08:55', 'admin', '2024-12-19 19:12:11', 'admin', 'iam', NULL, 'SINGLE');