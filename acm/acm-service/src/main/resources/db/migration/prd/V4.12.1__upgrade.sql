update acm_app_action set name='指定根部门',description='指定一个唯一的根部门ID',config_schema='{"display_name":"root","multi_valued":false,"mutability":"readWrite","name":"root","page_control":"FIXED_OBJ","required":false,"type":"OBJECT","value_type":"JS_EXP","sub_params":[
           {"description":"外部部门的根部门ID","display_name":"外部根部门ID","multi_valued":false,"mutability":"readWrite","name":"root_id","page_control":"TEXT","required":true,"type":"STRING","update":true,"value_type":"ADAPTOR"}]}' where id=82;

update acm_app_action set description='批量集成部门',config_schema='{"display_name":"root","multi_valued":false,"mutability":"readWrite","name":"root","page_control":"FIXED_OBJ","required":false,"type":"OBJECT","value_type":"JS_EXP","sub_params":[
           {"description":"支持一次集成单个或多个外部部门","display_name":"外部部门","multi_valued":true,"mutability":"readWrite","name":"ext_org","page_control":"TEXT","required":true,"type":"OBJECT","update":true,"value_type":"ADAPTOR"},
           {"description":"并行：子流程按多线程并发执行；串行：子流程按单线程顺序执行","display_name":"执行方式","multi_valued":false,"mutability":"readWrite","name":"run_type","option_values":["并行","串行"],"page_control":"SELECT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"}]}' where id=84;

update acm_app_action set description='批量集成用户',config_schema='{"display_name":"root","multi_valued":false,"mutability":"readWrite","name":"root","page_control":"FIXED_OBJ","required":false,"type":"OBJECT","value_type":"JS_EXP","sub_params":[
           {"description":"支持一次读入单个或多个外部用户（json）对象","display_name":"外部用户","multi_valued":true,"mutability":"readWrite","name":"ext_user","page_control":"TEXT","required":true,"type":"OBJECT","update":true,"value_type":"ADAPTOR"},
           {"description":"并行：子流程按多线程并发执行；串行：子流程按单线程顺序执行","display_name":"执行方式","multi_valued":false,"mutability":"readWrite","name":"run_type","option_values":["并行","串行"],"page_control":"SELECT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"}]}' where id=87;

insert into acm_app_action(id,action_key,is_trigger,name,description,connector_id,account_schema_id,protocol,node_struct,config_schema,create_time,update_time,tenant_id) values
(111,'loop_ding_all',0,'遍历钉钉部门和用户','根据部门ID遍历该部门下的全部子部门和用户',6,6,'LOOP_DING_ALL','LOOP','{"display_name":"root","multi_valued":false,"mutability":"readWrite","name":"root","page_control":"FIXED_OBJ","required":false,"type":"OBJECT","value_type":"JS_EXP","sub_params":[
           {"description":"部门ID，钉钉根部门ID为1","display_name":"部门ID","multi_valued":false,"mutability":"readWrite","name":"dept_id","page_control":"TEXT","required":true,"type":"STRING","update":true,"value_type":"ADAPTOR"},
           {"description":"遍历时是否输出部门信息","display_name":"输出部门","multi_valued":false,"mutability":"readWrite","name":"output_org","option_values":["是","否"],"page_control":"SELECT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"},
           {"description":"遍历时是否输出用户信息","display_name":"输出用户","multi_valued":false,"mutability":"readWrite","name":"output_user","option_values":["是","否"],"page_control":"SELECT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"}
           ]}',now(),now(),'iam');

delete from acm_app_action where id in(83,86);
insert into acm_app_action(id,action_key,is_trigger,name,description,connector_id,account_schema_id,protocol,node_struct,config_schema,create_time,update_time,tenant_id) values
(83,'loop_seeyon_all',0,'遍历致远部门和用户','根据单位ID遍历该单位下的全部子部门和用户',1698969592782770178,11,'LOOP_SEEYON_ALL','LOOP','{"display_name":"root","multi_valued":false,"mutability":"readWrite","name":"root","page_control":"FIXED_OBJ","required":false,"type":"OBJECT","value_type":"JS_EXP","sub_params":[
           {"description":"单位ID，-1表示全部单位","display_name":"单位ID","multi_valued":false,"mutability":"readWrite","name":"org_id","page_control":"TEXT","required":true,"type":"STRING","update":true,"value_type":"ADAPTOR"},
           {"description":"遍历时是否输出部门信息，不包含停用部门","display_name":"输出部门","multi_valued":false,"mutability":"readWrite","name":"output_org","option_values":["是","否"],"page_control":"SELECT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"},
           {"description":"遍历时是否输出用户信息，不包含停用人员","display_name":"输出用户","multi_valued":false,"mutability":"readWrite","name":"output_user","option_values":["是","否"],"page_control":"SELECT","required":true,"type":"STRING","update":true,"value_type":"FIX_VALUE"}
           ]}',now(),now(),'iam');

-- 北森优化之后的部门和用户集成
INSERT INTO `acm_app_action` (`id`, `action_key`, `is_trigger`, `name`, `description`, `connector_id`, `account_schema_id`, `protocol`, `action_type`, `status`, `config_schema`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `sample_output`, `node_struct`) VALUES (1866036438978170882, '1866036438963929090', 0, '遍历北森部门和用户', '根据部门ID遍历该部门下的全部子部门和用户', 1727250285287473153, 2, 'LOOP_BEISEN_ALL', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"部门ID。默认组织OId为0，根组织OId为900+租户ID，如：*********\",\"display_name\":\"部门ID\\t\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"dept_id\",\"option_values\":[],\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619268\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1866037159599882241\",\"update\":true,\"value_type\":\"JS_EXP\"}', now(), 'admin', now(), 'admin', 'iam', NULL, 'LOOP');

delete from acm_flow_node where flow_id = 1853345178337701889;

INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1866037901756809218, 'StartEventNode', 0, 1866038232080879617, 31, 81, 'user_sync', NULL, 1853345178337701889, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1851605414348050433\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1851605414348050434\",\"option_values\":[\"异步\",\"同步\"],\"description\":\"异步：webhook接收到数据后，将直接返回接收成功的结果以及流的信息。\\n同步：Webhook接收到数据后，将等待连接流执行完成并将最后的执行结果返回给调用方（最长等待时间为10s，超时将返回为空）。请查看接口文档获得更多详细信息\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"连接流执行方式\",\"required\":true,\"name\":\"type\",\"mutability\":\"readWrite\",\"value\":\"异步\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1851605414348050435\",\"name\":\"value.org_total\",\"description\":\"预估总的外部部门个数，用于估算集成进度\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"预估部门个数\",\"value\":\"1000\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1851605414348050436\",\"name\":\"value.user_total\",\"description\":\"预估总的外部用户个数，用于估算集成进度\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"预估用户个数\",\"value\":\"10000\",\"required\":true,\"page_control\":\"TEXT\"}],\"name\":\"root\",\"description\":\"启动通讯录集成\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"启动通讯录集成\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', NULL, '{\"hook_url\":null,\"sync\":false,\"type\":\"异步\",\"value\":{\"org_total\":1000,\"user_total\":10000}}', 'SUCCESS', 'OK', '2024-12-09 17:30:05', '2024-12-09 17:30:05', NULL, '2024-12-09 16:31:20', 'anonymousUser', '2024-12-09 17:30:07', 'admin', 'bit', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1866037901756809219, 'EndNode', 0, NULL, NULL, NULL, NULL, NULL, 1853345178337701889, NULL, NULL, NULL, NULL, 'IDLE', NULL, NULL, NULL, NULL, '2024-12-09 16:31:20', 'anonymousUser', '2024-12-09 16:31:20', 'anonymousUser', 'bit', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1866037901756809222, 'ActionNode', 1866056696859066369, NULL, 31, 85, 'sync_update_org', NULL, 1853345178337701889, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1851607287675211788\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"ADAPTOR\",\"ui_id\":\"1851607287675211789\",\"name\":\"ext_org\",\"description\":\"待集成的外部部门对象（json）信息\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"外部部门对象\",\"value\":\"{{N1866056696859066369.current}}\",\"required\":true,\"page_control\":\"TEXT\"}],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', NULL, '{\"code\":\"0\",\"data\":[{\"external_id\":\"1567918\",\"flag\":\"CREATE\",\"local\":{\"manager\":\"\",\"name\":\"财务部下的子机构数据\",\"connector_parent_org_id\":\"1559466\",\"id\":\"1866057307280560129\",\"connector_org_id\":\"1567918\"},\"message\":\"\"}],\"message\":\"SUCCESS\"}', 'SUCCESS', 'OK', '2024-12-09 17:48:24', '2024-12-09 17:48:26', NULL, '2024-12-09 16:31:20', 'anonymousUser', '2024-12-09 17:48:41', 'admin', 'bit', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1866037901756809224, 'ActionNode', 1866056957698637825, NULL, 31, 88, 'sync_update_user', NULL, 1853345178337701889, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1851607497600126990\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"ADAPTOR\",\"ui_id\":\"1851607497600126991\",\"name\":\"ext_user\",\"description\":\"待集成的外部用户对象（json）信息\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"外部用户对象\",\"value\":\"{{N1866056957698637825.current}}\",\"required\":true,\"page_control\":\"TEXT\"}],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', NULL, '{\"code\":\"0\",\"data\":[],\"message\":\"SUCCESS\"}', 'SUCCESS', 'OK', '2024-12-09 17:41:33', '2024-12-09 17:41:33', NULL, '2024-12-09 16:31:20', 'anonymousUser', '2024-12-09 17:48:36', 'admin', 'bit', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1866037901756809225, 'ActionNode', 0, 1866037901756809219, 31, 89, 'complete_user_sync', NULL, 1853345178337701889, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1851607623072731137\",\"sub_params\":[],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', NULL, '{\"code\":\"0\",\"data\":null,\"message\":\"SUCCESS\"}', 'INVALID', 'OK', '2024-11-05 18:12:34', '2024-11-05 18:12:34', NULL, '2024-12-09 16:31:20', 'anonymousUser', '2024-12-09 16:31:20', 'anonymousUser', 'bit', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1866038232080879617, 'ActionNode', 0, 1866041227782819842, 31, 82, 'record_ext_root_org', NULL, 1853345178337701889, NULL, '{\"ui_id\":\"1866040686875328522\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1866040686875328523\",\"name\":\"root_id\",\"type\":\"STRING\",\"description\":\"外部部门的根部门ID\",\"display_name\":\"外部根部门ID\",\"multi_valued\":false,\"value\":\"1559466\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, '{\"result\":\"输入的外部部门为空\"}', 'SUCCESS', 'OK', '2024-12-09 16:43:13', '2024-12-09 16:43:13', NULL, '2024-12-09 16:32:38', 'admin', '2024-12-09 16:44:28', 'admin', 'bit', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1866041227782819842, 'ActionNode', 0, 1866037901756809225, 1727250285287473153, 1866036438978170882, '1866036438963929090', 1853362985356447745, 1853345178337701889, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1866037159599882241\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"ADAPTOR\",\"ui_id\":\"1699309157332619268\",\"option_values\":[],\"description\":\"部门ID。默认组织OId为0，根组织OId为900+租户ID，如：*********\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"部门ID\\t\",\"required\":true,\"name\":\"dept_id\",\"mutability\":\"readonly\",\"value\":\"1559466\",\"page_control\":\"TEXT\"}],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', NULL, '{\"current\":{\"dept\":[{\"modifiedTime\":\"2023-08-15T16:28:01\",\"oIdOrganizationType\":null,\"sysMartionProperties\":null,\"hRBP\":null,\"fifthLevelOrganization\":1567921,\"number\":null,\"changeDate\":\"2023-07-17T00:00:00\",\"createdTime\":\"2023-07-17T11:18:33\",\"fax\":null,\"personInCharge\":null,\"shortName_zh_TW\":null,\"level\":\"d8530c04-d961-489c-834f-a8da3b1bf73c\",\"leaderWithSpecificDuty\":null,\"broadType\":\"2\",\"postcode\":null,\"firstLevelOrganization\":1559466,\"establishDate\":\"2023-07-17T00:00:00\",\"seventhLevelOrganization\":null,\"pOIdOrgReserve2_TreePath\":null,\"customProperties\":null,\"phone\":null,\"name_zh_TW\":null,\"name\":\"财务部下的子机构05\",\"pOIdOrgReserve3\":-2,\"sixthLevelOrganization\":1567922,\"pOIdOrgReserve2\":-2,\"shortName\":\"财务部下的子机构05\",\"isVirtualOrg\":false,\"pOIdOrgReserve2_TreeLevel\":null,\"startDate\":\"2023-07-17T00:00:00\",\"status\":1,\"eighthLevelOrganization\":null,\"pOIdOrgAdminNameTreePath\":\"*********/1559466/1567918/1567919/1567920/1567921/1567922\",\"stdIsDeleted\":false,\"legalMan\":null,\"code\":\"BC916\",\"pOIdOrgAdmin\":1567921,\"shortName_en_US\":null,\"isCurrentRecord\":true,\"orderAdmin\":18,\"secondLevelOrganization\":1567918,\"orderReserve2\":null,\"description\":null,\"orderReserve3\":null,\"industry\":null,\"pOIdOrgAdmin_TreePath\":\"*********/1559466/1567918/1567919/1567920/1567921/1567922\",\"name_en_US\":null,\"businessModifiedTime\":\"2023-08-15T16:28:01\",\"businessModifiedBy\":404026121,\"administrativeAssistant\":null,\"personInChargeDeputy\":null,\"modifiedBy\":404026121,\"place\":null,\"thirdLevelOrganization\":1567919,\"objectId\":\"d796fe6b-8de3-402a-807d-1ac7c4ae122e\",\"address\":null,\"shopOwner\":null,\"oId\":1567922,\"url\":null,\"tenthLevelOrganization\":null,\"translateProperties\":null,\"createdBy\":*********,\"economicType\":null,\"fourthLevelOrganization\":1567920,\"comment\":null,\"ninthLevelOrganization\":null,\"orderCode\":null,\"pOIdOrgAdmin_TreeLevel\":7,\"stopDate\":\"9999-12-31T00:00:00\"}],\"user\":[{\"qQ\":null,\"lastSchool\":null,\"nation\":null,\"sysMartionProperties\":null,\"oIdJobSequence\":null,\"activationState\":null,\"isConfirmRetireDate\":null,\"userID\":*********,\"tutorNew\":null,\"probation\":1,\"entryStatus\":null,\"constellation\":null,\"employType\":0,\"actualRetireDate\":null,\"applicantIdV6\":null,\"createdTime\":\"2024-09-24T11:13:22\",\"regularizationDate\":null,\"isDisabled\":null,\"businessAddress\":null,\"applicantId\":null,\"staffID\":\"55135a5d-bb69-4ab7-a291-600253bd1dc9\",\"order\":null,\"entryDate\":\"2024-07-11T00:00:00\",\"pOIdEmpReserve2\":null,\"workYearGroupTotal\":null,\"timeZone\":null,\"workFlowProcessId\":null,\"probationResult\":null,\"lastname\":null,\"joinPartyDate\":null,\"emergencyContactPhone\":null,\"graduateDate\":null,\"phoneticOfMing\":null,\"customProperties\":null,\"mobilePhone\":null,\"nationality\":1,\"birthplace\":null,\"sourceType\":0,\"dimension1\":null,\"probationStopDate\":\"2024-08-10T00:00:00\",\"dimension4\":null,\"dimension5\":null,\"residenceAddress\":null,\"startDate\":\"2024-07-11T00:00:00\",\"dimension2\":null,\"dimension3\":null,\"probationActualStopDate\":null,\"oidJobGrade\":null,\"gender\":null,\"workYearTotal\":null,\"isCurrentRecord\":true,\"changedStatus\":null,\"oIdProfessionalLine\":null,\"bloodType\":null,\"iDCountryEmblemSide\":null,\"employmentForm\":null,\"iDPortraitSide\":null,\"officeTel\":null,\"certificateStartDate\":\"0001-01-01T00:00:00\",\"workDate\":null,\"businessModifiedTime\":\"2024-09-24T20:16:11\",\"businessModifiedBy\":*********,\"recordInfo\":{\"serviceType\":0,\"modifiedTime\":\"2024-12-04T02:45:10\",\"blackListAddReason\":null,\"sysMartionProperties\":null,\"oIdJobSequence\":null,\"oIdJobLevel\":\"4e882603-8c11-4507-887d-91c23c21fda2\",\"handoverPerson\":null,\"userID\":*********,\"probation\":1,\"employmentSource\":null,\"entryStatus\":null,\"employType\":0,\"lastWorkDate\":null,\"regularizationDate\":null,\"createdTime\":\"2024-09-24T11:13:22\",\"oIdOrganization\":*********,\"staffID\":\"55135a5d-bb69-4ab7-a291-600253bd1dc9\",\"order\":null,\"entryType\":\"1\",\"entryDate\":\"2024-07-11T00:00:00\",\"pOIdEmpReserve2\":null,\"workYearGroupTotal\":null,\"workFlowProcessId\":null,\"changeTypeOID\":\"1\",\"probationResult\":null,\"blackStaffDesc\":null,\"customProperties\":null,\"transitionTypeOID\":null,\"dimension1\":null,\"probationStopDate\":\"2024-08-10T00:00:00\",\"dimension4\":null,\"dimension5\":null,\"startDate\":\"2024-07-11T00:00:00\",\"jobNumber\":null,\"dimension2\":null,\"dimension3\":null,\"stdIsDeleted\":false,\"probationActualStopDate\":null,\"oIdJobPosition\":null,\"oidJobGrade\":null,\"workYearTotal\":null,\"lUOffer\":null,\"isCurrentRecord\":true,\"businessTypeOID\":\"1\",\"changedStatus\":null,\"oIdProfessionalLine\":null,\"employmentForm\":null,\"employeeStatus\":\"2\",\"employmentChangeID\":\"ef127229-6789-48f1-9324-6077ce508c20\",\"businessModifiedTime\":\"2024-09-24T20:16:11\",\"businessModifiedBy\":*********,\"serviceStatus\":0,\"workYearBefore\":null,\"workYearCompanyBefore\":0,\"modifiedBy\":10000,\"place\":null,\"isHaveProbation\":\"1\",\"objectId\":\"288031a2-88a4-42ea-9dab-0445e5fc657d\",\"approvalStatus\":4,\"oIdJobPost\":null,\"pObjectDataID\":\"033911aa-5248-4912-a100-35c53166326e\",\"probationStartDate\":\"2024-07-11T00:00:00\",\"changeReason\":null,\"employmentType\":null,\"addOrNotBlackList\":null,\"oIdDepartment\":1567919,\"whereabouts\":null,\"pOIdEmpAdmin\":null,\"workYearCompanyTotal\":0.4,\"translateProperties\":null,\"createdBy\":*********,\"traineeStartDate\":null,\"isCharge\":\"0\",\"workYearGroupBefore\":null,\"stopDate\":\"9999-12-31T00:00:00\",\"remarks\":null},\"workYearBefore\":null,\"modifiedBy\":10000,\"workYearCompanyBefore\":0,\"place\":null,\"isHaveProbation\":\"1\",\"email\":\"<EMAIL>\",\"oIdJobPost\":null,\"firstEntryDate\":\"2024-07-11T00:00:00\",\"probationStartDate\":\"2024-07-11T00:00:00\",\"iDNumber\":null,\"politicalStatus\":null,\"iDFront\":null,\"allowToLoginIn\":null,\"addOrNotBlackList\":null,\"issuingAuthority\":null,\"whereabouts\":null,\"pOIdEmpAdmin\":null,\"translateProperties\":null,\"isCharge\":\"0\",\"originalId\":null,\"workYearGroupBefore\":null,\"serviceType\":0,\"modifiedTime\":\"2024-12-04T02:45:10\",\"blackListAddReason\":null,\"postalCode\":null,\"employeeInfo\":{\"qQ\":null,\"lastSchool\":null,\"modifiedTime\":\"2024-09-24T11:13:23\",\"nation\":null,\"sysMartionProperties\":null,\"postalCode\":null,\"activationState\":null,\"isConfirmRetireDate\":null,\"userID\":*********,\"tutorNew\":null,\"weiXin\":null,\"latestEntryDate\":\"2024-07-11T00:00:00\",\"major\":null,\"constellation\":null,\"actualRetireDate\":null,\"marryCategory\":null,\"applicantIdV6\":null,\"createdTime\":\"2024-09-24T11:13:22\",\"isDisabled\":null,\"businessAddress\":null,\"applicantId\":null,\"homeAddress\":null,\"passportNumber\":null,\"certificateValidityTerm\":\"0001-01-01T00:00:00\",\"personalHomepage\":null,\"emergencyContact\":null,\"homePhone\":null,\"timeZone\":null,\"lastname\":null,\"joinPartyDate\":null,\"disabledNumber\":null,\"emergencyContactPhone\":null,\"graduateDate\":null,\"preRetireDate\":null,\"phoneticOfMing\":null,\"customProperties\":null,\"mobilePhone\":null,\"nationality\":1,\"birthplace\":null,\"sourceType\":0,\"registAddress\":null,\"name\":\"测试职位职级\",\"residenceAddress\":null,\"birthday\":null,\"stdIsDeleted\":false,\"firstname\":null,\"gender\":null,\"iDType\":\"6\",\"emergencyContactRelationship\":null,\"backupMail\":null,\"domicileType\":null,\"applyIdV6\":null,\"bloodType\":null,\"iDCountryEmblemSide\":null,\"iDPortraitSide\":null,\"aboutMe\":null,\"speciality\":null,\"officeTel\":null,\"certificateStartDate\":\"0001-01-01T00:00:00\",\"workDate\":null,\"engName\":null,\"businessModifiedTime\":\"2024-09-24T11:13:22\",\"businessModifiedBy\":*********,\"_Name\":\"ceshizhiweizhiji\",\"educationLevel\":null,\"smallIDPhoto\":null,\"modifiedBy\":10000,\"isLongTermCertificate\":null,\"email\":\"<EMAIL>\",\"objectId\":\"033911aa-5248-4912-a100-35c53166326e\",\"firstEntryDate\":\"2024-07-11T00:00:00\",\"iDNumber\":null,\"phoneticOfXing\":null,\"politicalStatus\":null,\"iDPhoto\":null,\"iDFront\":null,\"allowToLoginIn\":null,\"issuingAuthority\":null,\"translateProperties\":null,\"createdBy\":*********,\"iDBehind\":null,\"orderCode\":null,\"age\":null},\"oIdJobLevel\":\"4e882603-8c11-4507-887d-91c23c21fda2\",\"handoverPerson\":null,\"employmentSource\":null,\"weiXin\":null,\"latestEntryDate\":\"2024-07-11T00:00:00\",\"major\":null,\"lastWorkDate\":null,\"marryCategory\":null,\"oIdOrganization\":*********,\"homeAddress\":null,\"passportNumber\":null,\"entryType\":\"1\",\"certificateValidityTerm\":\"0001-01-01T00:00:00\",\"personalHomepage\":null,\"emergencyContact\":null,\"homePhone\":null,\"changeTypeOID\":\"1\",\"disabledNumber\":null,\"preRetireDate\":null,\"blackStaffDesc\":null,\"registAddress\":null,\"name\":\"测试职位职级\",\"transitionTypeOID\":null,\"jobNumber\":null,\"birthday\":null,\"stdIsDeleted\":false,\"firstname\":null,\"oIdJobPosition\":null,\"lUOffer\":null,\"iDType\":\"6\",\"businessTypeOID\":\"1\",\"emergencyContactRelationship\":null,\"backupMail\":null,\"domicileType\":null,\"applyIdV6\":null,\"employeeStatus\":\"2\",\"aboutMe\":null,\"speciality\":null,\"employmentChangeID\":\"ef127229-6789-48f1-9324-6077ce508c20\",\"engName\":null,\"_Name\":\"ceshizhiweizhiji\",\"educationLevel\":null,\"serviceStatus\":0,\"smallIDPhoto\":null,\"isLongTermCertificate\":null,\"objectId\":\"288031a2-88a4-42ea-9dab-0445e5fc657d\",\"approvalStatus\":4,\"pObjectDataID\":\"033911aa-5248-4912-a100-35c53166326e\",\"changeReason\":null,\"phoneticOfXing\":null,\"employmentType\":null,\"iDPhoto\":null,\"oIdDepartment\":1567919,\"workYearCompanyTotal\":0.4,\"createdBy\":*********,\"iDBehind\":null,\"traineeStartDate\":null,\"orderCode\":null,\"age\":null,\"stopDate\":\"9999-12-31T00:00:00\",\"remarks\":null}]},\"index\":0,\"type\":\"INNER\"}', 'SUCCESS', 'OK', '2024-12-09 17:29:13', '2024-12-09 17:29:14', NULL, '2024-12-09 16:44:33', 'admin', '2024-12-09 17:29:17', 'admin', 'bit', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1866056696859066369, 'ActionNode', 1866041227782819842, 1866056957698637825, 31, 84, 'loop_ext_org', NULL, 1853345178337701889, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1866056705169858562\",\"sub_params\":[{\"multi_valued\":true,\"value_type\":\"ADAPTOR\",\"ui_id\":\"1866056705169858563\",\"name\":\"ext_org\",\"description\":\"支持一次集成单个或多个外部部门\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"外部部门\",\"value\":\"{{N1866041227782819842.current.dept}}\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"ADAPTOR\",\"ui_id\":\"1866056705169858564\",\"option_values\":[\"并行\",\"串行\"],\"description\":\"并行：子流程按多线程并发执行；串行：子流程按单线程顺序执行\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"执行方式\",\"required\":true,\"name\":\"run_type\",\"mutability\":\"readWrite\",\"value\":\"串行\",\"page_control\":\"SELECT\"}],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', NULL, '{\"current\":{\"pOIdOrgAdminNameTreePath\":\"*********/1559466/1567918\",\"stdIsDeleted\":false,\"modifiedTime\":\"2023-07-17T11:11:19\",\"code\":\"BC910\",\"pOIdOrgAdmin\":1559466,\"isCurrentRecord\":true,\"orderAdmin\":14,\"secondLevelOrganization\":1567918,\"pOIdOrgAdmin_TreePath\":\"*********/1559466/1567918\",\"businessModifiedTime\":\"2023-07-17T11:11:17\",\"businessModifiedBy\":*********,\"changeDate\":\"2023-01-01T00:00:00\",\"createdTime\":\"2023-07-17T11:11:17\",\"modifiedBy\":10000,\"objectId\":\"69cee7c0-8177-4b8f-85a1-c65ddb897660\",\"level\":\"fbaea398-8066-48e9-a58e-4ca7ad0d3d13\",\"broadType\":\"2\",\"firstLevelOrganization\":1559466,\"oId\":1567918,\"establishDate\":\"2023-01-01T00:00:00\",\"createdBy\":*********,\"name\":\"财务部下的子机构数据\",\"shortName\":\"财务部下的子机构数据\",\"isVirtualOrg\":false,\"pOIdOrgAdmin_TreeLevel\":3,\"startDate\":\"2023-01-01T00:00:00\",\"stopDate\":\"9999-12-31T00:00:00\",\"status\":1},\"index\":0,\"type\":\"INNER\"}', 'SUCCESS', 'OK', '2024-12-09 17:46:42', '2024-12-09 17:46:42', NULL, '2024-12-09 17:46:01', 'admin', '2024-12-09 18:14:31', 'admin', 'bit', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1866056957698637825, 'ActionNode', 1866041227782819842, NULL, 31, 87, 'loop_ext_user', NULL, 1853345178337701889, NULL, '{\"ui_id\":\"1866056971675934726\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1866056971675934727\",\"name\":\"ext_user\",\"type\":\"OBJECT\",\"description\":\"支持一次读入单个或多个外部用户（json）对象\",\"display_name\":\"外部用户\",\"multi_valued\":true,\"value\":\"{{N1866041227782819842.current.user}}\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1866056971675934728\",\"name\":\"run_type\",\"type\":\"STRING\",\"description\":\"并行：子流程按多线程并发执行；串行：子流程按单线程顺序执行\",\"display_name\":\"执行方式\",\"multi_valued\":false,\"value\":\"并行\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"并行\",\"串行\"],\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, '{\"current\":{\"serviceType\":0,\"stdIsDeleted\":false,\"modifiedTime\":\"2024-11-06T02:47:47\",\"gender\":0,\"nation\":\"1\",\"isCurrentRecord\":true,\"iDType\":\"1\",\"businessTypeOID\":\"1\",\"oIdJobSequence\":\"29963\",\"employeeInfo\":{\"stdIsDeleted\":false,\"modifiedTime\":\"2023-07-17T10:59:50\",\"certificateValidityTerm\":\"0001-01-01T00:00:00\",\"firstEntryDate\":\"2023-04-01T00:00:00\",\"gender\":0,\"nation\":\"1\",\"iDType\":\"1\",\"userID\":*********,\"certificateStartDate\":\"0001-01-01T00:00:00\",\"latestEntryDate\":\"2023-04-01T00:00:00\",\"businessModifiedTime\":\"2023-07-17T10:59:50\",\"nationality\":1,\"businessModifiedBy\":*********,\"sourceType\":0,\"_Name\":\"zhufengfeng\",\"createdBy\":*********,\"name\":\"朱峰峰\",\"createdTime\":\"2023-07-06T19:58:57\",\"modifiedBy\":*********,\"email\":\"<EMAIL>\",\"objectId\":\"b2ef0c2a-2bb6-46e5-a545-39f93cdd267c\"},\"oIdJobLevel\":\"560e64f3-e47a-4452-b5c0-025b6d6a0ac8\",\"userID\":*********,\"employeeStatus\":\"3\",\"employmentChangeID\":\"f46f05bc-d290-4471-bf1c-c55a48594838\",\"certificateStartDate\":\"0001-01-01T00:00:00\",\"businessModifiedTime\":\"2024-09-24T19:27:55\",\"latestEntryDate\":\"2023-04-01T00:00:00\",\"employType\":0,\"businessModifiedBy\":*********,\"_Name\":\"zhufengfeng\",\"serviceStatus\":0,\"recordInfo\":{\"serviceType\":0,\"stdIsDeleted\":false,\"modifiedTime\":\"2024-11-06T02:47:47\",\"isCurrentRecord\":true,\"businessTypeOID\":\"1\",\"oIdJobSequence\":\"29963\",\"oIdJobLevel\":\"560e64f3-e47a-4452-b5c0-025b6d6a0ac8\",\"userID\":*********,\"employeeStatus\":\"3\",\"employmentChangeID\":\"f46f05bc-d290-4471-bf1c-c55a48594838\",\"businessModifiedTime\":\"2024-09-24T19:27:55\",\"employType\":0,\"businessModifiedBy\":*********,\"serviceStatus\":0,\"createdTime\":\"2023-07-06T19:58:57\",\"workYearCompanyBefore\":0,\"modifiedBy\":10000,\"isHaveProbation\":\"0\",\"oIdOrganization\":*********,\"staffID\":\"154257d7-4265-46ad-894a-1bfe6bfe1730\",\"objectId\":\"9e52081e-e07b-4ca5-bdf6-d54b71ddcbf5\",\"approvalStatus\":4,\"oIdJobPost\":\"250211\",\"entryType\":\"1\",\"pObjectDataID\":\"b2ef0c2a-2bb6-46e5-a545-39f93cdd267c\",\"entryDate\":\"2023-04-01T00:00:00\",\"changeTypeOID\":\"1\",\"oIdDepartment\":1559466,\"workYearCompanyTotal\":1.6,\"createdBy\":*********,\"isCharge\":\"0\",\"startDate\":\"2023-04-01T00:00:00\",\"stopDate\":\"9999-12-31T00:00:00\"},\"createdTime\":\"2023-07-06T19:58:57\",\"modifiedBy\":10000,\"workYearCompanyBefore\":0,\"isHaveProbation\":\"0\",\"oIdOrganization\":*********,\"staffID\":\"154257d7-4265-46ad-894a-1bfe6bfe1730\",\"email\":\"<EMAIL>\",\"objectId\":\"9e52081e-e07b-4ca5-bdf6-d54b71ddcbf5\",\"oIdJobPost\":\"250211\",\"approvalStatus\":4,\"entryType\":\"1\",\"certificateValidityTerm\":\"0001-01-01T00:00:00\",\"firstEntryDate\":\"2023-04-01T00:00:00\",\"pObjectDataID\":\"b2ef0c2a-2bb6-46e5-a545-39f93cdd267c\",\"entryDate\":\"2023-04-01T00:00:00\",\"changeTypeOID\":\"1\",\"oIdDepartment\":1559466,\"workYearCompanyTotal\":1.6,\"nationality\":1,\"sourceType\":0,\"createdBy\":*********,\"name\":\"朱峰峰\",\"isCharge\":\"0\",\"startDate\":\"2023-04-01T00:00:00\",\"stopDate\":\"9999-12-31T00:00:00\"},\"index\":0,\"type\":\"INNER\"}', 'SUCCESS', 'OK', '2024-12-09 17:47:16', '2024-12-09 17:47:16', NULL, '2024-12-09 17:47:03', 'admin', '2024-12-09 17:47:29', 'admin', 'bit', '0', NULL);

delete from `acm`.`acm_flow_node` where flow_id=1851627863757434881;
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1851975849086095362, 'StartEventNode', 1863512048990351361, 81, 'user_sync', NULL, 1851627863757434881, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1848568437562032130\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1848568437562032131\",\"option_values\":[\"异步\",\"同步\"],\"description\":\"异步：webhook接收到数据后，将直接返回接收成功的结果以及流的信息。\\n同步：Webhook接收到数据后，将等待连接流执行完成并将最后的执行结果返回给调用方（最长等待时间为10s，超时将返回为空）。请查看接口文档获得更多详细信息\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"连接流执行方式\",\"required\":true,\"name\":\"type\",\"mutability\":\"readWrite\",\"value\":\"异步\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1848568437562032132\",\"name\":\"value.org_total\",\"description\":\"预估总的外部部门个数，用于估算集成进度\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"预估部门个数\",\"value\":\"500\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1848568437562032133\",\"name\":\"value.user_total\",\"description\":\"预估总的外部用户个数，用于估算集成进度\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"预估用户个数\",\"value\":\"1000\",\"required\":true,\"page_control\":\"TEXT\"}],\"name\":\"root\",\"description\":\"启动通讯录集成\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"启动通讯录集成\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"hook_url\":null,\"sync\":false,\"type\":\"异步\",\"value\":{\"org_total\":500,\"user_total\":1000}}', '{\"hook_url\":null,\"sync\":false,\"type\":\"异步\",\"value\":{\"org_total\":500,\"user_total\":1000}}', 'SUCCESS', 'OK', '2024-12-06 19:08:45', '2024-12-06 19:08:45', '2024-10-31 21:13:45', 'anonymousUser', '2024-12-06 19:08:47', 'admin', 'iam', 31, 0, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1851975849086095364, 'EndNode', NULL, NULL, NULL, NULL, 1851627863757434881, NULL, NULL, NULL, NULL, 'IDLE', NULL, NULL, NULL, '2024-10-31 21:13:45', 'anonymousUser', '2024-10-31 21:13:45', 'anonymousUser', 'iam', NULL, 0, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1851975849086095373, 'ActionNode', 1851975849086095364, 89, 'complete_user_sync', NULL, 1851627863757434881, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1848611150936215553\",\"sub_params\":[],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', '{}', '{\"code\":\"0\",\"data\":null,\"message\":\"SUCCESS\"}', 'SUCCESS', 'OK', '2024-12-15 16:59:36', '2024-12-15 16:59:36', '2024-10-31 21:13:45', 'anonymousUser', '2024-12-15 16:59:38', 'admin', 'iam', 31, 0, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1863512048990351361, 'ActionNode', 1863525687784845314, 82, 'record_ext_root_org', NULL, 1851627863757434881, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1863512569669668874\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"ADAPTOR\",\"ui_id\":\"1863512569669668875\",\"name\":\"root_id\",\"description\":\"外部部门的根部门ID\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"外部根部门ID\",\"value\":\"1\",\"required\":true,\"page_control\":\"TEXT\"}],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', '{\"root_id\":\"1\"}', '{\"code\":\"0\",\"data\":{\"external_id\":\"1\",\"flag\":\"UPDATE\",\"local\":\"1854128961626357762\",\"message\":\"\"},\"message\":\"SUCCESS\"}', 'SUCCESS', 'OK', '2024-12-09 13:48:10', '2024-12-09 13:48:10', '2024-12-02 17:14:30', 'admin', '2024-12-09 13:48:10', 'admin', 'iam', 31, 0, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1863525687784845314, 'ActionNode', 1851975849086095373, 111, 'loop_ding_all', 1817800299591602177, 1851627863757434881, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1863525800349978654\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"ADAPTOR\",\"ui_id\":\"1863525800349978655\",\"name\":\"dept_id\",\"description\":\"部门ID，钉钉根部门ID为1\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"部门ID\",\"value\":\"{{N1863512048990351361.data.external_id}}\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1869276071277432865\",\"option_values\":[\"是\",\"否\"],\"description\":\"遍历时是否输出部门信息\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"输出部门\",\"required\":true,\"name\":\"output_org\",\"mutability\":\"readWrite\",\"value\":\"是\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1869276071277432866\",\"option_values\":[\"是\",\"否\"],\"description\":\"遍历时是否输出用户信息\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"输出用户\",\"required\":true,\"name\":\"output_user\",\"mutability\":\"readWrite\",\"value\":\"是\",\"page_control\":\"SELECT\"}],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', '{\"output_org\":\"是\",\"output_user\":\"是\",\"dept_id\":\"*********\"}', '{\"current\":{\"dept\":[{\"dept_permits\":[],\"outer_permit_users\":[],\"emp_apply_join_dept\":false,\"outer_dept\":false,\"auto_approve_apply\":false,\"group_contain_sub_dept\":false,\"auto_add_user\":false,\"parent_id\":*********,\"hide_dept\":false,\"name\":\"数犀样板间\",\"outer_permit_depts\":[],\"user_permits\":[],\"dept_id\":*********,\"create_dept_group\":false,\"order\":0},{\"dept_permits\":[],\"outer_permit_users\":[],\"emp_apply_join_dept\":false,\"outer_dept\":false,\"auto_approve_apply\":false,\"group_contain_sub_dept\":false,\"auto_add_user\":false,\"parent_id\":*********,\"hide_dept\":false,\"name\":\"钉钉测试test\",\"outer_permit_depts\":[],\"user_permits\":[],\"dept_id\":965361807,\"create_dept_group\":false,\"order\":0},{\"dept_permits\":[],\"outer_permit_users\":[],\"emp_apply_join_dept\":false,\"outer_dept\":false,\"auto_approve_apply\":false,\"group_contain_sub_dept\":false,\"auto_add_user\":false,\"parent_id\":*********,\"hide_dept\":false,\"name\":\"微信\",\"outer_permit_depts\":[],\"user_permits\":[],\"dept_id\":*********,\"create_dept_group\":false,\"order\":100}],\"user\":[]},\"index\":0,\"type\":\"INNER\"}', 'SUCCESS', 'OK', '2024-12-18 15:27:10', '2024-12-18 15:27:11', '2024-12-18 14:49:32', 'admin', '2024-12-18 15:27:18', 'admin', 'iam', 6, 0, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1863527132558995458, 'ActionNode', 1863529048353484802, 84, 'loop_ext_org', NULL, 1851627863757434881, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1863527155496046595\",\"sub_params\":[{\"multi_valued\":true,\"value_type\":\"JS_EXP\",\"ui_id\":\"1863527155496046596\",\"name\":\"ext_org\",\"description\":\"支持一>次集成单个或多个外部部门\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"外部部门\",\"value\":\"{{N1863525687784845314.current.dept}}\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"ADAPTOR\",\"ui_id\":\"1863527155496046597\",\"option_values\":[\"并行\",\"串行\"],\"description\":\"并行：子流程按多线程并发执行；串行：子流程按单线程顺序执行\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"执行方式\",\"required\":true,\"name\":\"run_type\",\"mutability\":\"readWrite\",\"value\":\"并行\",\"page_control\":\"SELECT\"}],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', NULL, '{\"current\":{\"dept_permits\":[],\"outer_permit_users\":[],\"emp_apply_join_dept\":false,\"outer_dept\":false,\"auto_approve_apply\":false,\"group_contain_sub_dept\":false,\"auto_add_user\":false,\"parent_id\":*********,\"hide_dept\":false,\"name\":\"数犀样板间\",\"outer_permit_depts\":[],\"user_permits\":[],\"dept_id\":*********,\"create_dept_group\":false,\"order\":0},\"index\":0,\"type\":\"INNER\"}', 'SUCCESS', 'OK', '2024-12-02 18:45:47', '2024-12-02 18:45:47', '2024-12-02 18:14:26', 'admin', '2024-12-02 18:46:00', 'admin', 'iam', 31, 1863525687784845314, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1863527946312691713, 'ActionNode', NULL, 85, 'sync_update_org', NULL, 1851627863757434881, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1863528944022769678\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"ADAPTOR\",\"ui_id\":\"1863528944022769679\",\"name\":\"ext_org\",\"description\":\"待集成的外部部门>对象（json）信息\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"外部部门对象\",\"value\":\"{{N1863527132558995458.current}}\",\"required\":true,\"page_control\":\"TEXT\"}],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', NULL, '{\"code\":\"0\",\"data\":[{\"external_id\":\"*********\",\"flag\":\"CREATE\",\"local\":{\"manager\":\"\",\"name\":\"数犀样板间\",\"connector_parent_org_id\":\"*********\",\"description\":\"数犀样板间\",\"id\":\"1863535296654643201\",\"connector_org_id\":\"*********\",\"seq\":\"0\"},\"message\":\"\"}],\"message\":\"SUCCESS\"}', 'SUCCESS', 'OK', '2024-12-02 18:46:49', '2024-12-02 18:46:52', '2024-12-02 18:17:40', 'admin', '2024-12-02 18:47:05', 'admin', 'iam', 31, 1863527132558995458, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1863529048353484802, 'ActionNode', NULL, 87, 'loop_ext_user', NULL, 1851627863757434881, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1863529061098377222\",\"sub_params\":[{\"multi_valued\":true,\"value_type\":\"ADAPTOR\",\"ui_id\":\"1863529061098377223\",\"name\":\"ext_user\",\"description\":\"支持一次读入单个或>多个外部用户（json）对象\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"外部用户\",\"value\":\"{{N1863525687784845314.current.user}}\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"ADAPTOR\",\"ui_id\":\"1863529061098377224\",\"option_values\":[\"并行\",\"串行\"],\"description\":\"并行：子流程按多线程并发执行；串行：子流程按单线程顺序执行\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"执行方式\",\"required\":true,\"name\":\"run_type\",\"mutability\":\"readWrite\",\"value\":\"并行\",\"page_control\":\"SELECT\"}],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', '{\"run_type\":\"并行\",\"ext_user\":[]}', '{\"current\":null,\"index\":0,\"type\":\"INNER\"}', 'SUCCESS', 'OK', '2024-12-02 18:54:01', '2024-12-02 18:54:01', '2024-12-02 18:22:02', 'admin', '2024-12-02 18:54:11', 'admin', 'iam', 31, 1863525687784845314, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1863529186572578818, 'ActionNode', NULL, 88, 'sync_update_user', NULL, 1851627863757434881, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1863529197509726224\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"ADAPTOR\",\"ui_id\":\"1863529197509726225\",\"name\":\"ext_user\",\"description\":\"待集成的外部用>户对象（json）信息\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"外部用户对象\",\"value\":\"{{N1863529048353484802.current}}\",\"required\":true,\"page_control\":\"TEXT\"}],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', '{}', '{\"code\":\"0\",\"data\":[],\"message\":\"SUCCESS\"}', 'SUCCESS', 'OK', '2024-12-02 20:22:44', '2024-12-02 20:22:44', '2024-12-02 18:22:35', 'admin', '2024-12-02 20:22:52', 'admin', 'iam', 31, 1863529048353484802, NULL, '0', NULL);

delete from `acm`.`acm_flow_node` where flow_id=1851839530835951618;
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1851902355656740865, 'StartEventNode', 1868219756976984065, 81, 'user_sync', NULL, 1851839530835951618, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1851605414348050433\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1851605414348050434\",\"option_values\":[\"异步\",\"同步\"],\"description\":\"异步：webhook接收到数据后，将直接返回接收成功的结果以及流的信息。\\n同步：Webhook接收到数据后，将等待连接流执行完成并将最后的执行结果返回给调用方（最长等待时间为10s，超时将返回为空）。请查看接口文档获得更多详细信息\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"连接流执行方式\",\"required\":true,\"name\":\"type\",\"mutability\":\"readWrite\",\"value\":\"异步\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1851605414348050435\",\"name\":\"value.org_total\",\"description\":\"预估总的外部部门个数，用于估算集成进度\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"预估部门个数\",\"value\":\"1000\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1851605414348050436\",\"name\":\"value.user_total\",\"description\":\"预估总的外部用户个数，用于估算集成进度\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"预估用户个数\",\"value\":\"10000\",\"required\":true,\"page_control\":\"TEXT\"}],\"name\":\"root\",\"description\":\"启动通讯录集成\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"启动通讯录集成\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"hook_url\":null,\"sync\":false,\"type\":\"异步\",\"value\":{\"org_total\":1000,\"user_total\":10000}}', '{\"hook_url\":null,\"sync\":false,\"type\":\"异步\",\"value\":{\"org_total\":1000,\"user_total\":10000}}', 'SUCCESS', 'OK', '2024-12-15 18:05:13', '2024-12-15 18:05:13', '2024-10-31 16:21:43', 'anonymousUser', '2024-12-15 18:05:15', 'admin', 'iam', 31, 0, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1851902355656740866, 'EndNode', NULL, NULL, NULL, NULL, 1851839530835951618, NULL, NULL, NULL, NULL, 'IDLE', NULL, NULL, NULL, '2024-10-31 16:21:43', 'anonymousUser', '2024-10-31 16:21:43', 'anonymousUser', 'iam', NULL, 0, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1868219756976984065, 'ActionNode', 1868236565369634818, 82, 'record_ext_root_org', NULL, 1851839530835951618, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1868219771848376329\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"ADAPTOR\",\"ui_id\":\"1868219771848376330\",\"name\":\"root_id\",\"description\":\"外部
部门的根部门ID\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"外部根部门ID\",\"value\":\"7313901301646381000\",\"required\":true,\"page_control\":\"TEXT\"}],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', '{\"root_id\":\"7313901301646381000\"}', '{\"code\":\"0\",\"data\":{\"external_id\":\"7313901301646381000\",\"flag\":\"UPDATE\",\"local\":\"1851902354324590593\",\"message\":\"\"},\"message\":\"SUCCESS\"}', 'SUCCESS', 'OK', '2024-12-15 18:23:07', '2024-12-15 18:23:08', '2024-12-15 17:01:15', 'admin', '2024-12-15 18:23:14', 'admin', 'iam', 31, 0, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1868236565369634818, 'ActionNode', 1868239538099372033, 83, 'loop_seeyon_all', 1848694140615843841, 1851839530835951618, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1868236612838690829\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"ADAPTOR\",\"ui_id\":\"1868236612838690830\",\"name\":\"org_id\",\"description\":\"单位ID，-1表示全部单位\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"单位ID\",\"value\":\"{{N1868219756976984065.data.external_id}}\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1868236612838690831\",\"option_values\":[\"是\",\"否\"],\"description\":\"遍历时是否输出部门信息，不包含停用部门\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"输出部门\",\"required\":true,\"name\":\"output_org\",\"mutability\":\"readWrite\",\"value\":\"是\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1868236612838690832\",\"option_values\":[\"是\",\"否\"],\"description\":\"遍历时是否输出用户信息，不包含停用人员\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"输出用户\",\"required\":true,\"name\":\"output_user\",\"mutability\":\"readWrite\",\"value\":\"是\",\"page_control\":\"SELECT\"}],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', NULL, '{\n  \"current\": {\n    \"dept\": [\n      {\n        \"orgAccountId\": 967547186016371000,\n        \"id\": 7313901301646382000,\n        \"name\": \"部门名称\",\n        \"enabled\": true,\n        \"sortId\": 2,\n        \"isGroup\": false,\n        \"superior\": 7313901301646381000,\n        \"path\": \"************\",\n        \"code\": \"\",\n        \"createTime\": *************,\n        \"updateTime\": *************,\n        \"isDeleted\": false,\n        \"status\": 1,\n        \"description\": \"\",\n        \"shortName\": null,\n        \"secondName\": null,\n        \"isInternal\": true,\n        \"levelScope\": -1,\n        \"type\": \"Department\",\n        \"superiorName\": \"\",\n        \"sortIdType\": \"1\",\n        \"entityType\": \"Department\",\n        \"depManager\": null,\n        \"depAdmin\": null,\n        \"posts\": [],\n        \"valid\": true,\n        \"group\": false,\n        \"customLogin\": false,\n        \"parentPath\": \"********\",\n        \"customLoginUrl\": null\n      }\n    ],\n    \"user\": [\n{\n      \"id\": 710000480641633900,\n      \"name\": \"s1\",\n      \"code\": \"001\",\n      \"createTime\": *************,\n      \"updateTime\": *************,\n      \"sortId\": 1,\n      \"isDeleted\": false,\n      \"enabled\": true,\n      \"status\": 1,\n      \"description\": \"说明\",\n      \"orgAccountId\": -2332227039700957700,\n      \"orgLevelId\": 3176188816548514000,\n      \"orgPostId\": -2779802668474544000,\n      \"orgDepartmentId\": 842270*************,\n      \"isInternal\": true,\n      \"isLoginable\": true,\n      \"isAssigned\": true,\n      \"isAdmin\": false,\n      \"isValid\": true,\n      \"state\": 1,\n      \"second_post\": [\n        {\n          \"memberId\": 710000480641633900,\n          \"depId\": 842270*************,\n          \"postId\": -6172367662268331000\n        }\n      ],\n      \"concurrent_post\": [],\n      \"telNumber\": \"***********\",\n      \"birthday\": *************,\n      \"officeNum\": \"010-********\",\n      \"emailAddress\": \"<EMAIL>\",\n      \"gender\": 1,\n      \"loginName\": \"s1\",\n      \"location\": \"工作地\",\n      \"reporter\": \"汇报人\",\n      \"hiredate\": \"入职时间\"\n    }\n]\n  },\n  \"index\": 0,\n  \"type\": \"INNER\"\n}', 'IDLE', NULL, NULL, NULL, '2024-12-15 18:08:02', 'admin', '2024-12-15 18:19:34', 'admin', 'iam', 1698969592782770178, 0, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1868238756939612161, 'ActionNode', 1868238923432509441, 84, 'loop_ext_org', NULL, 1851839530835951618, NULL, '{\"ui_id\":\"1868238768417345538\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1868238768417345539\",\"name\":\"ext_org\",\"type\":\"OBJECT\",\"description\":\"支>持一次集成单个或多个外部部门\",\"display_name\":\"外部部门\",\"multi_valued\":true,\"value\":\"{{N1868236565369634818.current.dept}}\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1868238768417345540\",\"name\":\"run_type\",\"type\":\"STRING\",\"description\":\"并行：子流程按多线程并发执行；串行：子流程按单线程顺序执行\",\"display_name\":\"执行方式\",\"multi_valued\":false,\"value\":\"并行\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"并行\",\"串行\"],\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, '{\"current\":{\"code\":\"\",\"customLogin\":false,\"parentPath\":\"********\",\"description\":\"\",\"type\":\"Department\",\"posts\":[],\"enabled\":true,\"sortIdType\":\"1\",\"valid\":true,\"path\":\"************\",\"orgAccountId\":\"967547186016371000\",\"isDeleted\":false,\"sortId\":2,\"levelScope\":-1,\"id\":\"7313901301646382000\",\"group\":false,\"superiorName\":\"\",\"entityType\":\"Department\",\"updateTime\":\"*************\",\"isInternal\":true,\"superior\":\"7313901301646381000\",\"createTime\":\"*************\",\"name\":\"部门名称\",\"isGroup\":false,\"status\":1},\"index\":0,\"type\":\"INNER\"}', 'SUCCESS', 'OK', '2024-12-15 18:17:16', '2024-12-15 18:17:16', '2024-12-15 18:16:45', 'admin', '2024-12-15 18:17:18', 'admin', 'iam', 31, 1868236565369634818, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1868238923432509441, 'ActionNode', NULL, 87, 'loop_ext_user', NULL, 1851839530835951618, NULL, '{\"ui_id\":\"1868238935291924485\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1868238935291924486\",\"name\":\"ext_user\",\"type\":\"OBJECT\",\"description\":\"支持一次读入单个
或多个外部用户（json）对象\",\"display_name\":\"外部用户\",\"multi_valued\":true,\"value\":\"{{N1868236565369634818.current.user}}\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1868238935291924487\",\"name\":\"run_type\",\"type\":\"STRING\",\"description\":\">并行：子流程按多线程并发执行；串行：子流程按单线程顺序执行\",\"display_name\":\"执行方式\",\"multi_valued\":false,\"value\":\"并行\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"并行\",\"串行\"],\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, '{\"current\":{\"birthday\":\"*************\",\"code\":\"001\",\"officeNum\":\"010-********\",\"gender\":1,\"description\":\"说明\",\"hiredate\":\"入职时间\",\"enabled\":true,\"orgAccountId\":\"-2332227039700957700\",\"emailAddress\":\"<EMAIL>\",\"isDeleted\":false,\"sortId\":1,\"loginName\":\"s1\",\"id\":\"710000480641633900\",\"state\":1,\"second_post\":[{\"depId\":\"842270*************\",\"postId\":\"-6172367662268331000\",\"memberId\":\"710000480641633900\"}],\"orgDepartmentId\":\"842270*************\",\"isValid\":true,\"updateTime\":\"*************\",\"reporter\":\"汇报人\",\"isAdmin\":false,\"orgLevelId\":\"3176188816548514000\",\"isInternal\":true,\"isLoginable\":true,\"isAssigned\":true,\"telNumber\":\"***********\",\"createTime\":\"*************\",\"concurrent_post\":[],\"orgPostId\":\"-2779802668474544000\",\"name\":\"s1\",\"location\":\"工作地\",\"status\":1},\"index\":0,\"type\":\"INNER\"}', 'SUCCESS', 'OK', '2024-12-15 18:17:48', '2024-12-15 18:17:48', '2024-12-15 18:17:24', 'admin', '2024-12-15 18:17:50', 'admin', 'iam', 31, 1868236565369634818, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1868239047340638209, 'ActionNode', NULL, 85, 'sync_update_org', NULL, 1851839530835951618, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1868239057362948107\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"ADAPTOR\",\"ui_id\":\"1868239057362948108\",\"name\":\"ext_org\",\"description\":\"待集成的外部部门对象（json）信息\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"外部部门对象\",\"value\":\"{{N1868238756939612161.current}}\",\"required\":true,\"page_control\":\"TEXT\"}],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', NULL, '{\"code\":\"0\",\"data\":[{\"external_id\":\"7313901301646382000\",\"flag\":\"CREATE\",\"local\":{\"manager\":\"\",\"name\":\"部门名称\",\"connector_parent_org_id\":\"7313901301646381000\",\"description\":\"\",\"id\":\"1868240622851223553\",\"connector_org_id\":\"7313901301646382000\",\"seq\":\"2\"},\"message\":\"\"}],\"message\":\"SUCCESS\"}', 'SUCCESS', 'OK', '2024-12-15 18:24:09', '2024-12-15 18:24:10', '2024-12-15 18:17:54', 'admin', '2024-12-15 18:24:11', 'admin', 'iam', 31, 1868238756939612161, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1868239312835887105, 'ActionNode', NULL, 88, 'sync_update_user', NULL, 1851839530835951618, NULL, '{\"ui_id\":\"1868239323344736269\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1868239323344736270\",\"name\":\"ext_user\",\"type\":\"OBJECT\",\"description\":\"待集成的外部>用户对象（json）信息\",\"display_name\":\"外部用户对象\",\"multi_valued\":false,\"value\":\"{{N1868238923432509441.current}}\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, '{\"code\":\"0\",\"data\":[{\"external_id\":\"710000480641633900\",\"flag\":\"DELETE\",\"local\":{\"sub\":\"1868239388289339393\",\"connector_org_ids\":[\"842270*************\",\"842270*************\"],\"manager\":\"\",\"name\":\"s1\",\"telephone_number\":\"010-********\",\"phone_number\":\"+86-***********\",\"user_job_number\":\"001\",\"email\":\"<EMAIL>\",\"username\":\"s1\"},\"message\":\"\"}],\"message\":\"SUCCESS\"}', 'SUCCESS', 'OK', '2024-12-15 18:19:12', '2024-12-15 18:19:15', '2024-12-15 18:18:57', 'admin', '2024-12-15 18:19:18', 'admin', 'iam', 31, 1868238923432509441, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1868239538099372033, 'ActionNode', 1851902355656740866, 89, 'complete_user_sync', NULL, 1851839530835951618, NULL, '{\"ui_id\":\"1868239556048916482\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, '{\"code\":\"0\",\"data\":null,\"message\":\"SUCCESS\"}', 'SUCCESS', 'OK', '2024-12-15 18:19:59', '2024-12-15 18:19:59', '2024-12-15 18:19:51', 'admin', '2024-12-15 18:20:08', 'admin', 'iam', 31, 0, NULL, '0', NULL);

delete from `acm`.`acm_flow_node` where flow_id=1851621369544585218;
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1865971766260731906, 'StartEventNode', 1865972720081100801, 81, 'user_sync', NULL, 1851621369544585218, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1851605414348050433\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1851605414348050434\",\"option_values\":[\"异步\",\"同步\"],\"description\":\"异步：webhook接收到数据后，将直接返回接收成功的结果以及流的信息。\\n同步：Webhook接收到数据后，将等待连接流执行完成并将最后的执行结果返回给调用方（最长等待时间为10s，超时将返回为空）。请查看接口文档获得更多详细信息\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"连接流执行方式\",\"required\":true,\"name\":\"type\",\"mutability\":\"readWrite\",\"value\":\"异步\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1851605414348050435\",\"name\":\"value.org_total\",\"description\":\"预估总的外部部门个数，用于估算集成进度\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"预估部门个数\",\"value\":\"1000\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1851605414348050436\",\"name\":\"value.user_total\",\"description\":\"预估总的外部用户个数，用于估算集成进度\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"预估用户个数\",\"value\":\"10000\",\"required\":true,\"page_control\":\"TEXT\"}],\"name\":\"root\",\"description\":\"启动通讯录集成\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"启动通讯录集成\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', '{\"hook_url\":null,\"sync\":false,\"type\":\"异步\",\"value\":{\"org_total\":1000,\"user_total\":10000}}', '{\"hook_url\":null,\"sync\":false,\"type\":\"异步\",\"value\":{\"org_total\":1000,\"user_total\":10000}}', 'SUCCESS', 'OK', '2024-12-09 12:12:49', '2024-12-09 12:12:49', '2024-12-09 12:08:32', 'anonymousUser', '2024-12-09 12:12:50', 'admin', 'iam', 31, 0, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1865971766260731907, 'ActionNode', 1865971766260731917, 5, 'http-json', NULL, 1851621369544585218, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1847173075261693954\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1802915904329203714\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"请求类型\",\"required\":true,\"name\":\"httpMethod\",\"mutability\":\"readWrite\",\"value\":\"GET\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"ADAPTOR\",\"ui_id\":\"1802915904329203715\",\"name\":\"endpoint\",\"description\":\"请填写完整的接口地址\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"请求地址\",\"value\":\"https://poc.digitalsee.cn/iam/api/dept\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1802915904329203716\",\"name\":\"contentType\",\"description\":\"\",\"update\":true,\"mutability\":\"readonly\",\"type\":\"STRING\",\"display_name\":\"请求体类型\",\"value\":\"application/json; charset=UTF-8\",\"required\":true,\"page_control\":\"LABEL\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1802915904329203717\",\"name\":\"header\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"Header参数\",\"value\":{},\"required\":false,\"page_control\":\"EXTEND_OBJ\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1718683627745\",\"name\":\"path\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"Path参数\",\"value\":{},\"required\":false,\"page_control\":\"EXTEND_OBJ\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1802915904329203718\",\"name\":\"query\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"Query参数\",\"value\":{},\"required\":false,\"page_control\":\"EXTEND_OBJ\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1802915904329203719\",\"name\":\"body\",\"description\":\"请输入json格式的请求参数body\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"Body参数\",\"required\":false,\"page_control\":\"JSON\"}],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', NULL, '{\n  \"status_code\": 200,\n  \"header\": {\n    \"Content-Type\": \"application/json\"\n  },\n  \"body\": {\n    \"dept_id\": \"1\",\n    \"parent_id\": \"0\",\n    \"name\": \"部门名称\"\n  }\n}', 'IDLE', 'OK', '2024-10-30 20:44:30', '2024-10-30 20:44:30', '2024-12-09 12:08:32', 'anonymousUser', '2024-12-09 12:13:46', 'admin', 'iam', 4, 0, 0, '0', '调用获取外部部门接口');
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1865971766260731908, 'EndNode', NULL, NULL, NULL, NULL, 1851621369544585218, NULL, NULL, NULL, NULL, 'IDLE', NULL, NULL, NULL, '2024-12-09 12:08:32', 'anonymousUser', '2024-12-09 12:08:32', 'anonymousUser', 'iam', NULL, 0, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1865971766260731916, 'ActionNode', 1865971766260731908, 89, 'complete_user_sync', NULL, 1851621369544585218, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1851607623072731137\",\"sub_params\":[],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', '{}', '{\"code\":\"0\",\"data\":null,\"message\":\"SUCCESS\"}', 'SUCCESS', 'OK', '2024-12-09 13:52:38', '2024-12-09 13:52:38', '2024-12-09 12:08:32', 'anonymousUser', '2024-12-15 19:59:40', 'admin', 'iam', 31, 0, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1865971766260731917, 'ActionNode', 1865974167501856769, 5, 'http-json', NULL, 1851621369544585218, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1847173075261693954\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1802915904329203714\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"description\":\"\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"请求类型\",\"required\":true,\"name\":\"httpMethod\",\"mutability\":\"readWrite\",\"value\":\"GET\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"ADAPTOR\",\"ui_id\":\"1802915904329203715\",\"name\":\"endpoint\",\"description\":\"请填写完整的接口地址\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"请求地址\",\"value\":\"https://poc.digitalsee.cn/iam/api/user\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1802915904329203716\",\"name\":\"contentType\",\"description\":\"\",\"update\":true,\"mutability\":\"readonly\",\"type\":\"STRING\",\"display_name\":\"请求体类型\",\"value\":\"application/json; charset=UTF-8\",\"required\":true,\"page_control\":\"LABEL\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1802915904329203717\",\"name\":\"header\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"Header参数\",\"value\":{},\"required\":false,\"page_control\":\"EXTEND_OBJ\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1718683627745\",\"name\":\"path\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"Path参数\",\"value\":{},\"required\":false,\"page_control\":\"EXTEND_OBJ\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1802915904329203718\",\"name\":\"query\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"Query参数\",\"value\":{},\"required\":false,\"page_control\":\"EXTEND_OBJ\"},{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1802915904329203719\",\"name\":\"body\",\"description\":\"请输入json格式的请求参数body\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"Body参数\",\"required\":false,\"page_control\":\"JSON\"}],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', NULL, '{\n  \"status_code\": 200,\n  \"header\": {\n    \"Content-Type\": \"application/json\"\n  },\n  \"body\": {\n    \"userid\": \"1\",\n    \"dept_id\": \"1\",\n    \"name\": \"姓名\"\n  }\n}', 'IDLE', 'OK', '2024-10-30 20:44:30', '2024-10-30 20:44:30', '2024-12-09 12:08:32', 'anonymousUser', '2024-12-09 12:17:56', 'admin', 'iam', 4, 0, 0, '0', '调用获取外部用户接口');
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1865972720081100801, 'ActionNode', 1865971766260731907, 82, 'record_ext_root_org', NULL, 1851621369544585218, NULL, '{\"ui_id\":\"1865972729377787914\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1865972729377787915\",\"name\":\"root_id\",\"type\":\"STRING\",\"description\":\"外部部门的根部门ID\",\"display_name\":\"外部根部门ID\",\"multi_valued\":false,\"value\":\"1\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', '{\"root_id\":\"1\"}', '{\"code\":\"0\",\"data\":{\"external_id\":\"1\",\"flag\":\"UPDATE\",\"local\":\"1851831152030040065\",\"message\":\"\"},\"message\":\"SUCCESS\"}', 'SUCCESS', 'OK', '2024-12-09 12:12:28', '2024-12-09 12:12:28', '2024-12-09 12:12:19', 'admin', '2024-12-09 12:13:05', 'admin', 'iam', 31, 0, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1865974167501856769, 'ActionNode', 1865974268735578114, 84, 'loop_ext_org', NULL, 1851621369544585218, NULL, '{\"ui_id\":\"1865974177721290754\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1865974177721290755\",\"name\":\"ext_org\",\"type\":\"OBJECT\",\"description\":\"支>持一次集成单个或多个外部部门\",\"display_name\":\"外部部门\",\"multi_valued\":true,\"value\":\"{{N1865971766260731907.body}}\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1865974177721290756\",\"name\":\"run_type\",\"type\":\"STRING\",\"description\":\"并行：>子流程按多线程并发执行；串行：子流程按单线程顺序执行\",\"display_name\":\"执行方式\",\"multi_valued\":false,\"value\":\"并行\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"并行\",\"串行\"],\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', '{\"run_type\":\"并行\",\"ext_org\":[{\"parent_id\":\"0\",\"name\":\"部门名称\",\"dept_id\":\"1\"}]}', '{\"current\":{\"parent_id\":\"0\",\"name\":\"部门名称\",\"dept_id\":\"1\"},\"index\":0,\"type\":\"INNER\"}', 'SUCCESS', 'OK', '2024-12-09 12:18:22', '2024-12-09 12:18:22', '2024-12-09 12:18:04', 'admin', '2024-12-09 12:18:24', 'admin', 'iam', 31, 0, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1865974268735578114, 'ActionNode', 1865971766260731916, 87, 'loop_ext_user', NULL, 1851621369544585218, NULL, '{\"ui_id\":\"1865974277797384198\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1865974277797384199\",\"name\":\"ext_user\",\"type\":\"OBJECT\",\"description\":\">支持一次读入单个或多个外部用户（json）对象\",\"display_name\":\"外部用户\",\"multi_valued\":true,\"value\":\"{{N1865971766260731917.body}}\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1865974277797384200\",\"name\":\"run_type\",\"type\":\"STRING\",\"description\":\"并行：子流程按多线程并发执行；串行：子流程按单线程顺序执行\",\"display_name\":\"执行方式\",\"multi_valued\":false,\"value\":\"并行\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"并行\",\"串行\"],\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', '{\"run_type\":\"并行\",\"ext_user\":[{\"name\":\"姓名\",\"dept_id\":\"1\",\"userid\":\"1\"}]}', '{\"current\":{\"name\":\"姓名\",\"dept_id\":\"1\",\"userid\":\"1\"},\"index\":0,\"type\":\"INNER\"}', 'SUCCESS', 'OK', '2024-12-09 12:18:44', '2024-12-09 12:18:44', '2024-12-09 12:18:28', 'admin', '2024-12-09 12:18:45', 'admin', 'iam', 31, 0, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1865974362180476929, 'ActionNode', NULL, 85, 'sync_update_org', NULL, 1851621369544585218, NULL, '{\"ui_id\":\"1865974372529934351\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1865974372529934352\",\"name\":\"ext_org\",\"type\":\"OBJECT\",\"description\":\"待集成的外部部>门对象（json）信息\",\"display_name\":\"外部部门对象\",\"multi_valued\":false,\"value\":\"{{N1865974167501856769.current}}\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', '{\"ext_org\":{\"parent_id\":\"0\",\"name\":\"部门名称\",\"dept_id\":\"1\"}}', '{\"code\":\"0\",\"data\":[{\"external_id\":\"1\",\"flag\":\"UPDATE\",\"local\":{\"name\":\"部门名称\",\"connector_parent_org_id\":\"0\",\"id\":\"1851831152030040065\",\"connector_org_id\":\"1\"},\"message\":\"\"}],\"message\":\"SUCCESS\"}', 'SUCCESS', 'OK', '2024-12-09 12:19:08', '2024-12-09 12:19:08', '2024-12-09 12:18:51', 'admin', '2024-12-09 12:19:10', 'admin', 'iam', 31, 1865974167501856769, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1865974461224771586, 'ActionNode', NULL, 88, 'sync_update_user', NULL, 1851621369544585218, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1865974469854564368\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"ADAPTOR\",\"ui_id\":\"1865974469854564369\",\"name\":\"ext_user\",\"description\":\"待集成的外部用户对象>（json）信息\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"外部用户对象\",\"value\":\"{{N1865974268735578114.current}}\",\"required\":true,\"page_control\":\"TEXT\"}],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', '{\"ext_user\":{\"name\":\"姓名\",\"dept_id\":\"1\",\"userid\":\"1\"}}', '{\"code\":\"0\",\"data\":[{\"external_id\":\"1\",\"flag\":\"UPDATE\",\"local\":{\"sub\":\"1851627276729831425\",\"connector_org_ids\":[\"1\"],\"manager\":\"\",\"username\":\"1\"},\"message\":\"\"}],\"message\":\"SUCCESS\"}', 'SUCCESS', 'OK', '2024-12-09 13:52:20', '2024-12-09 13:52:22', '2024-12-09 12:19:14', 'admin', '2024-12-09 13:52:32', 'admin', 'iam', 31, 1865974268735578114, NULL, '0', NULL);


-- 数据库 身份自动化 集成
INSERT INTO `acm`.`acm_app_connector` (`id`, `name`, `description`, `icon`, `build_type`, `app_domain_category`, `app_action_category`, `app_package`, `deployment_type`, `ver_name`, `ver_number`, `ver_type`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`) VALUES (1868125376064716801, '数据库通讯录集成', NULL, NULL, 'BuiltIn', 'DATA_PROTOCOL', NULL, '1868125376029474817', 'MixApp', 'init', 1, 'RELEASE', '2024-12-15 10:46:12', 'admin', '2024-12-15 10:46:12', NULL, 'iam');
INSERT INTO `acm`.`acm_app_action` (`id`, `action_key`, `is_trigger`, `name`, `description`, `connector_id`, `account_schema_id`, `protocol`, `action_type`, `status`, `config_schema`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `sample_output`, `node_struct`) VALUES (1868125792450052097, '1868125792435781633', 0, '遍历MYSQL数据库部门', '遍历MYSQL数据库部门', 1868125376064716801, 5, 'LOOP_DATABASE_ORG', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"1、SQL脚本用以获取数据库人员信息并建立和系统用户属性的对应关系，请根据实际需求，在select的查询结果部分定义需要获取的属性\\n2、SELECT查询的字段和字段别名都将作为用户的属性，“as”别名不支持中文，如果主键列使用了 “表名.列名” 格式 必须使用别名\\n3、用户和部门在不同表存储的场景，请切换到“获取部门”编写查询获取部门的SQL脚本\\n4、查询的结果需要跟本地用户的属性映射，执行成功确认查询结果后，请保存并下一步完成属性映射\\n5、推荐查询结果排序\",\"display_name\":\"查询所有部门的SQL\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"org_sql\",\"page_control\":\"TEXTAREA\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"查询出的部门id对应的字段名 用于排序\",\"display_name\":\"部门ID字段名\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"dept_id\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1734230897248\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"查询出的父部门id对应的字段名 用于排序\",\"display_name\":\"父部门ID字段名\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"parent_dept_id\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"FIX_VALUE\"}],\"type\":\"OBJECT\",\"ui_id\":\"1868133201451499521\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2024-12-15 10:47:52', 'admin', '2024-12-15 11:20:10', 'admin', 'iam', NULL, 'LOOP');
INSERT INTO `acm`.`acm_app_action` (`id`, `action_key`, `is_trigger`, `name`, `description`, `connector_id`, `account_schema_id`, `protocol`, `action_type`, `status`, `config_schema`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `sample_output`, `node_struct`) VALUES (1868128810037870594, '1868128810027794433', 0, '遍历MYSQL数据库用户', '遍历MYSQL数据库用户', 1868125376064716801, 5, 'LOOP_DATABASE_USER', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"1、SQL脚本用以获取数据库人员信息并建立和系统用户属性的对应关系，请根据实际需求，在select的查询结果部分定义需要获取的属性\\n2、SELECT查询的字段和字段别名都将作为用户的属性，“as”别名不支持中文，如果主键列使用了 “表名.列名” 格式 必须使用别名\\n3、用户和部门在不同表存储的场景，请切换到“获取部门”编写查询获取部门的SQL脚本\\n4、查询的结果需要跟本地用户的属性映射，执行成功确认查询结果后，请保存并下一步完成属性映射\\n5、执行时会根据提供的sql分页查询\",\"display_name\":\"查询用户的SQL\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"user_sql\",\"page_control\":\"TEXTAREA\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"FIX_VALUE\"}],\"type\":\"OBJECT\",\"ui_id\":\"1868132024978587650\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2024-12-15 10:59:51', 'admin', '2024-12-15 11:12:38', 'admin', 'iam', NULL, 'LOOP');
INSERT INTO `acm`.`acm_app_action` (`id`, `action_key`, `is_trigger`, `name`, `description`, `connector_id`, `account_schema_id`, `protocol`, `action_type`, `status`, `config_schema`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `sample_output`, `node_struct`) VALUES (1868133590831398913, '1868133590825517057', 0, '遍历DB2数据库部门', '遍历DB2数据库部门', 1868125376064716801, 19, 'LOOP_DATABASE_ORG', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"1、SQL脚本用以获取数据库人员信息并建立和系统用户属性的对应关系，请根据实际需求，在select的查询结果部分定义需要获取的属性\\n2、SELECT查询的字段和字段别名都将作为用户的属性，“as”别名不支持中文，如果主键列使用了 “表名.列名” 格式 必须使用别名\\n3、用户和部门在不同表存储的场景，请切换到“获取部门”编写查询获取部门的SQL脚本\\n4、查询的结果需要跟本地用户的属性映射，执行成功确认查询结果后，请保存并下一步完成属性映射\\n5、推荐查询结果排序\",\"display_name\":\"查询所有部门的SQL\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"org_sql\",\"page_control\":\"TEXTAREA\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"查询出的部门id对应的字段名 用于排序\",\"display_name\":\"部门ID字段名\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"dept_id\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1734230897248\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"查询出的父部门id对应的字段名 用于排序\",\"display_name\":\"父部门ID字段名\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"parent_dept_id\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"FIX_VALUE\"}],\"type\":\"OBJECT\",\"ui_id\":\"1868133201451499521\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2024-12-15 11:18:51', 'admin', NULL, NULL, 'iam', NULL, 'LOOP');
INSERT INTO `acm`.`acm_app_action` (`id`, `action_key`, `is_trigger`, `name`, `description`, `connector_id`, `account_schema_id`, `protocol`, `action_type`, `status`, `config_schema`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `sample_output`, `node_struct`) VALUES (1868135477764882434, '1868135477754806273', 0, '遍历DB2数据库用户', '遍历DB2数据库用户', 1868125376064716801, 19, 'LOOP_DATABASE_USER', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"1、SQL脚本用以获取数据库人员信息并建立和系统用户属性的对应关系，请根据实际需求，在select的查询结果部分定义需要获取的属性\\n2、SELECT查询的字段和字段别名都将作为用户的属性，“as”别名不支持中文，如果主键列使用了 “表名.列名” 格式 必须使用别名\\n3、用户和部门在不同表存储的场景，请切换到“获取部门”编写查询获取部门的SQL脚本\\n4、查询的结果需要跟本地用户的属性映射，执行成功确认查询结果后，请保存并下一步完成属性映射\\n5、执行时会根据提供的sql分页查询\",\"display_name\":\"查询用户的SQL\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"user_sql\",\"page_control\":\"TEXTAREA\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"FIX_VALUE\"}],\"type\":\"OBJECT\",\"ui_id\":\"1868132024978587650\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2024-12-15 11:26:21', 'admin', '2024-12-15 11:28:19', 'admin', 'iam', NULL, 'LOOP');
INSERT INTO `acm`.`acm_app_action` (`id`, `action_key`, `is_trigger`, `name`, `description`, `connector_id`, `account_schema_id`, `protocol`, `action_type`, `status`, `config_schema`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `sample_output`, `node_struct`) VALUES (1868136331091836930, '1868136331085955073', 0, '遍历SQL Server数据库部门', '遍历SQL Server数据库部门', 1868125376064716801, 25, 'LOOP_DATABASE_ORG', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"1、SQL脚本用以获取数据库人员信息并建立和系统用户属性的对应关系，请根据实际需求，在select的查询结果部分定义需要获取的属性\\n2、SELECT查询的字段和字段别名都将作为用户的属性，“as”别名不支持中文，如果主键列使用了 “表名.列名” 格式 必须使用别名\\n3、用户和部门在不同表存储的场景，请切换到“获取部门”编写查询获取部门的SQL脚本\\n4、查询的结果需要跟本地用户的属性映射，执行成功确认查询结果后，请保存并下一步完成属性映射\\n5、推荐查询结果排序\",\"display_name\":\"查询所有部门的SQL\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"org_sql\",\"page_control\":\"TEXTAREA\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"查询出的部门id对应的字段名 用于排序\",\"display_name\":\"部门ID字段名\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"dept_id\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1734230897248\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"查询出的父部门id对应的字段名 用于排序\",\"display_name\":\"父部门ID字段名\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"parent_dept_id\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"FIX_VALUE\"}],\"type\":\"OBJECT\",\"ui_id\":\"1868133201451499521\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2024-12-15 11:29:44', 'admin', '2024-12-15 11:32:09', 'admin', 'iam', NULL, 'LOOP');
INSERT INTO `acm`.`acm_app_action` (`id`, `action_key`, `is_trigger`, `name`, `description`, `connector_id`, `account_schema_id`, `protocol`, `action_type`, `status`, `config_schema`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `sample_output`, `node_struct`) VALUES (1868136491884675073, '1868136491882987522', 0, '遍历SQL Server数据库用户', '遍历SQL Server数据库用户', 1868125376064716801, 25, 'LOOP_DATABASE_USER', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"1、SQL脚本用以获取数据库人员信息并建立和系统用户属性的对应关系，请根据实际需求，在select的查询结果部分定义需要获取的属性\\n2、SELECT查询的字段和字段别名都将作为用户的属性，“as”别名不支持中文，如果主键列使用了 “表名.列名” 格式 必须使用别名\\n3、用户和部门在不同表存储的场景，请切换到“获取部门”编写查询获取部门的SQL脚本\\n4、查询的结果需要跟本地用户的属性映射，执行成功确认查询结果后，请保存并下一步完成属性映射\\n5、执行时会根据提供的sql分页查询\",\"display_name\":\"查询用户的SQL\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"user_sql\",\"page_control\":\"TEXTAREA\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"FIX_VALUE\"}],\"type\":\"OBJECT\",\"ui_id\":\"1868132024978587650\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2024-12-15 11:30:23', 'admin', '2024-12-15 11:32:41', 'admin', 'iam', NULL, 'LOOP');
INSERT INTO `acm`.`acm_app_action` (`id`, `action_key`, `is_trigger`, `name`, `description`, `connector_id`, `account_schema_id`, `protocol`, `action_type`, `status`, `config_schema`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `sample_output`, `node_struct`) VALUES (1868137205507751937, '1868137205501870081', 0, '遍历Oracle数据库用户', '遍历Oracle数据库用户', 1868125376064716801, 8, 'LOOP_DATABASE_USER', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"1、SQL脚本用以获取数据库人员信息并建立和系统用户属性的对应关系，请根据实际需求，在select的查询结果部分定义需要获取的属性\\n2、SELECT查询的字段和字段别名都将作为用户的属性，“as”别名不支持中文，如果主键列使用了 “表名.列名” 格式 必须使用别名\\n3、用户和部门在不同表存储的场景，请切换到“获取部门”编写查询获取部门的SQL脚本\\n4、查询的结果需要跟本地用户的属性映射，执行成功确认查询结果后，请保存并下一步完成属性映射\\n5、执行时会根据提供的sql分页查询\",\"display_name\":\"查询用户的SQL\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"user_sql\",\"page_control\":\"TEXTAREA\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"FIX_VALUE\"}],\"type\":\"OBJECT\",\"ui_id\":\"1868132024978587650\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2024-12-15 11:33:13', 'admin', '2024-12-15 11:33:15', 'admin', 'iam', NULL, 'LOOP');
INSERT INTO `acm`.`acm_app_action` (`id`, `action_key`, `is_trigger`, `name`, `description`, `connector_id`, `account_schema_id`, `protocol`, `action_type`, `status`, `config_schema`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `sample_output`, `node_struct`) VALUES (1868137408658866177, '1868137408652984321', 0, '遍历Oracle数据库部门', '遍历Oracle数据库部门', 1868125376064716801, 8, 'LOOP_DATABASE_ORG', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"1、SQL脚本用以获取数据库人员信息并建立和系统用户属性的对应关系，请根据实际需求，在select的查询结果部分定义需要获取的属性\\n2、SELECT查询的字段和字段别名都将作为用户的属性，“as”别名不支持中文，如果主键列使用了 “表名.列名” 格式 必须使用别名\\n3、用户和部门在不同表存储的场景，请切换到“获取部门”编写查询获取部门的SQL脚本\\n4、查询的结果需要跟本地用户的属性映射，执行成功确认查询结果后，请保存并下一步完成属性映射\\n5、推荐查询结果排序\",\"display_name\":\"查询所有部门的SQL\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"org_sql\",\"page_control\":\"TEXTAREA\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"查询出的部门id对应的字段名 用于排序\",\"display_name\":\"部门ID字段名\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"dept_id\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1734230897248\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"查询出的父部门id对应的字段名 用于排序\",\"display_name\":\"父部门ID字段名\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"parent_dept_id\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"FIX_VALUE\"}],\"type\":\"OBJECT\",\"ui_id\":\"1868133201451499521\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2024-12-15 11:34:01', 'admin', '2024-12-15 11:34:03', 'admin', 'iam', NULL, 'LOOP');

delete from `acm`.`acm_flow_node` where flow_id = 1852225766790139905;
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1868484789584084993, 'StartEventNode', 0, 1868572260429139970, 31, 81, 'user_sync', NULL, 1852225766790139905, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1851605414348050433\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1851605414348050434\",\"option_values\":[\"异步\",\"同步\"],\"description\":\"异步：webhook接收到数据后，将直接返回接收成功的结果以及流的信息。\\n同步：Webhook接收到数据后，将等待连接流执行完成并将最后的执行结果返回给调用方（最长等待时间为10s，超时将返回为空）。请查看接口文档获得更多详细信息\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"连接流执行方式\",\"required\":true,\"name\":\"type\",\"mutability\":\"readWrite\",\"value\":\"异步\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1851605414348050435\",\"name\":\"value.org_total\",\"description\":\"预估总的外部部门个数，用于估算集成进度\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"预估部门个数\",\"value\":\"1000\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1851605414348050436\",\"name\":\"value.user_total\",\"description\":\"预估总的外部用户个数，用于估算集成进度\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"预估用户个数\",\"value\":\"10000\",\"required\":true,\"page_control\":\"TEXT\"}],\"name\":\"root\",\"description\":\"启动通讯录集成\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"启动通讯录集成\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', NULL, '{\"hook_url\":null,\"sync\":false,\"type\":\"异步\",\"value\":{\"org_total\":1000,\"user_total\":10000}}', 'INVALID', 'OK', '2024-11-04 11:07:24', '2024-11-04 11:07:24', NULL, '2024-12-16 10:34:23', 'anonymousUser', '2024-12-16 16:22:33', 'anonymousUser', 'bit', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1868484789584084995, 'EndNode', 0, NULL, NULL, NULL, NULL, NULL, 1852225766790139905, NULL, NULL, NULL, NULL, 'IDLE', NULL, NULL, NULL, NULL, '2024-12-16 10:34:23', 'anonymousUser', '2024-12-16 10:34:23', 'anonymousUser', 'bit', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1868484789584085002, 'ActionNode', 1868552733599449089, NULL, 31, 88, 'sync_update_user', NULL, 1852225766790139905, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1851607497600126990\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"ADAPTOR\",\"ui_id\":\"1851607497600126991\",\"name\":\"ext_user\",\"description\":\"待集成的外部用户对象（json）信息\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"外部用户对象\",\"value\":\"{{N1868552733599449089.current}}\",\"required\":true,\"page_control\":\"TEXT\"}],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', NULL, '{\"code\":\"0\",\"data\":[{\"external_id\":\"user1\",\"flag\":\"CREATE\",\"local\":{\"sub\":\"1868552926019112961\",\"manager\":\"\",\"username\":\"user1\"},\"message\":\"\"}],\"message\":\"SUCCESS\"}', 'SUCCESS', 'OK', '2024-12-16 15:05:04', '2024-12-16 15:05:09', NULL, '2024-12-16 10:34:23', 'anonymousUser', '2024-12-16 15:05:10', 'admin', 'bit', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1868484789584085003, 'ActionNode', 0, 1868484789584084995, 31, 89, 'complete_user_sync', NULL, 1852225766790139905, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1851607623072731137\",\"sub_params\":[],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', NULL, '{\"code\":\"0\",\"data\":null,\"message\":\"SUCCESS\"}', 'INVALID', 'OK', '2024-11-04 12:14:50', '2024-11-04 12:14:50', NULL, '2024-12-16 10:34:23', 'anonymousUser', '2024-12-16 10:34:23', 'anonymousUser', 'bit', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1868485185247010817, 'ActionNode', 0, 1868545711160066050, 1868125376064716801, 1868125792450052097, '1868125792435781633', 1868131797015658497, 1852225766790139905, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1868133201451499521\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"*************\",\"name\":\"org_sql\",\"description\":\"1、SQL脚本用以获取数据库人员信息并建立和系统用户属性的对应关系，请根据实际需求，在select的查询结果部分定义需要获取的属性\\n2、SELECT查询的字段和字段别名都将作为用户的属性，“as”别名不支持中文，如果主键列使用了 “表名.列名” 格式 必须使用别名\\n3、用户和部门在不同表存储的场景，请切换到“获取部门”编写查询获取部门的SQL脚本\\n4、查询的结果需要跟本地用户的属性映射，执行成功确认查询结果后，请保存并下一步完成属性映射\\n5、推荐查询结果排序\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"查询所有部门的SQL\",\"value\":\"select id,parent_ref_id as parentId,name from iam_org where org_path  like \'%1851476324430635009%\';\",\"required\":true,\"page_control\":\"TEXTAREA\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1734230897248\",\"name\":\"dept_id\",\"description\":\"查询出的部门id对应的字段名 用于排序\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"部门ID字段名\",\"value\":\"id\",\"required\":false,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"*************\",\"name\":\"parent_dept_id\",\"description\":\"查询出的父部门id对应的字段名 用于排序\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"父部门ID字段名\",\"value\":\"parentId\",\"required\":false,\"page_control\":\"TEXT\"}],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', NULL, '{\"current\":{\"dept\":[{\"name\":\"dg-ldap\",\"id\":\"1851476324430635009\",\"${level}$\":1,\"parentId\":\"-1\"}]},\"index\":0,\"type\":\"INNER\"}', 'SUCCESS', 'OK', '2024-12-16 14:57:10', '2024-12-16 14:57:10', NULL, '2024-12-16 10:35:58', 'admin', '2024-12-16 14:57:13', 'admin', 'bit', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1868545711160066050, 'ActionNode', 0, 1868484789584085003, 1868125376064716801, 1868128810037870594, '1868128810027794433', 1868131797015658497, 1852225766790139905, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1868132024978587650\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"*************\",\"name\":\"user_sql\",\"description\":\"1、SQL脚本用以获取数据库人员信息并建立和系统用户属性的对应关系，请根据实际需求，在select的查询结果部分定义需要获取的属性\\n2、SELECT查询的字段和字段别名都将作为用户的属性，“as”别名不支持中文，如果主键列使用了 “表名.列名” 格式 必须使用别名\\n3、用户和部门在不同表存储的场景，请切换到“获取部门”编写查询获取部门的SQL脚本\\n4、查询的结果需要跟本地用户的属性映射，执行成功确认查询结果后，请保存并下一步完成属性映射\\n5、执行时会根据提供的sql分页查询\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"查询用户的SQL\",\"value\":\"select u.id,username,name,phone_number,uo.org_ref_id as deptId from iam_user u left join iam_user_org uo on u.id = uo.uid where uo.org_ref_id in (select id from iam_org where org_path  like \'%1851476324430635009%\')\",\"required\":true,\"page_control\":\"TEXTAREA\"}],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', NULL, '{\"current\":{\"user\":[{\"u_id\":\"1851475218552053761\",\"name\":\"用户\",\"deptId\":\"18515209***********\",\"phone_number\":\"\",\"username\":\"user1\"},{\"u_id\":\"1851475389696434178\",\"name\":\"子用户2\",\"deptId\":\"1851520954119372801\",\"phone_number\":\"\",\"username\":\"child1\"}]},\"index\":0,\"type\":\"INNER\"}', 'SUCCESS', 'OK', '2024-12-16 15:04:14', '2024-12-16 15:04:14', NULL, '2024-12-16 14:36:28', 'admin', '2024-12-16 15:04:26', 'admin', 'bit', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1868549683342110722, 'ActionNode', 1868551845140918274, NULL, 31, 85, 'sync_update_org', NULL, 1852225766790139905, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1868549900537614351\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"ADAPTOR\",\"ui_id\":\"1868549900537614352\",\"name\":\"ext_org\",\"description\":\"待集成的外部部门对象（json）信息\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"外部部门对象\",\"value\":\"{{N1868551845140918274.current}}\",\"required\":true,\"page_control\":\"TEXT\"}],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', NULL, '{\"code\":\"0\",\"data\":[],\"message\":\"SUCCESS\"}', 'SUCCESS', 'OK', '2024-12-16 14:57:25', '2024-12-16 14:57:25', NULL, '2024-12-16 14:52:15', 'admin', '2024-12-16 15:01:23', 'admin', 'bit', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1868551845140918274, 'ActionNode', 1868485185247010817, NULL, 31, 84, 'loop_ext_org', NULL, 1852225766790139905, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1868551852331814914\",\"sub_params\":[{\"multi_valued\":true,\"value_type\":\"ADAPTOR\",\"ui_id\":\"1868551852331814915\",\"name\":\"ext_org\",\"description\":\"支持一次集成单个或多个外部部门\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"外部部门\",\"value\":\"{{N1868485185247010817.current.dept}}\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"ADAPTOR\",\"ui_id\":\"1868551852331814916\",\"option_values\":[\"并行\",\"串行\"],\"description\":\"并行：子流程按多线程并发执行；串行：子流程按单线程顺序执行\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"执行方式\",\"required\":true,\"name\":\"run_type\",\"mutability\":\"readWrite\",\"value\":\"并行\",\"page_control\":\"SELECT\"}],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', NULL, '{\"current\":{\"dept\":[{\"name\":\"dg-ldap\",\"id\":\"1851476324430635009\",\"${level}$\":1,\"parentId\":\"-1\"}]},\"index\":0,\"type\":\"INNER\"}', 'SUCCESS', 'OK', '2024-12-16 15:01:07', '2024-12-16 15:01:07', NULL, '2024-12-16 15:00:51', 'admin', '2024-12-16 15:05:25', 'admin', 'bit', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1868552733599449089, 'ActionNode', 1868545711160066050, NULL, 31, 87, 'loop_ext_user', NULL, 1852225766790139905, NULL, '{\"ui_id\":\"1868552771639365638\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1868552771639365639\",\"name\":\"ext_user\",\"type\":\"OBJECT\",\"description\":\"支持一次读入单个或多个外部用户（json）对象\",\"display_name\":\"外部用户\",\"multi_valued\":true,\"value\":\"{{N1868545711160066050.current.user}}\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1868552771639365640\",\"name\":\"run_type\",\"type\":\"STRING\",\"description\":\"并行：子流程按多线程并发执行；串行：子流程按单线程顺序执行\",\"display_name\":\"执行方式\",\"multi_valued\":false,\"value\":\"并行\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"并行\",\"串行\"],\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, '{\"current\":{\"u_id\":\"1851475218552053761\",\"name\":\"用户\",\"deptId\":\"18515209***********\",\"phone_number\":\"\",\"username\":\"user1\"},\"index\":0,\"type\":\"INNER\"}', 'SUCCESS', 'OK', '2024-12-16 15:04:44', '2024-12-16 15:04:44', NULL, '2024-12-16 15:04:22', 'admin', '2024-12-16 15:05:14', 'admin', 'bit', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1868572260429139970, 'ActionNode', 0, 1868485185247010817, 31, 82, 'record_ext_root_org', NULL, 1852225766790139905, NULL, '{\"ui_id\":\"1868572279036891146\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1868572279036891147\",\"name\":\"root_id\",\"type\":\"STRING\",\"description\":\"外部部门的根部门ID\",\"display_name\":\"外部根部门ID\",\"multi_valued\":false,\"value\":\"1851476324430635009\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, '{\"code\":\"0\",\"data\":{\"external_id\":\"1851476324430635009\",\"flag\":\"UPDATE\",\"local\":\"1868484789262790658\",\"message\":\"\"},\"message\":\"SUCCESS\"}', 'SUCCESS', 'OK', '2024-12-16 16:22:19', '2024-12-16 16:22:24', NULL, '2024-12-16 16:21:58', 'admin', '2024-12-16 16:22:29', 'admin', 'bit', '0', NULL);


-- 优化text格式请求
UPDATE `acm`.`acm_app_action` SET config_schema = '{"display_name":"root","multi_valued":false,"mutability":"readWrite","name":"root","page_control":"FIXED_OBJ","required":false,"sub_params":[{"description":"","display_name":"请求类型","multi_valued":false,"mutability":"readWrite","name":"httpMethod","option_values":["GET","POST","PUT","PATCH","DELETE"],"page_control":"SELECT","required":true,"type":"STRING","ui_id":"1802916934316052483","update":true,"value_type":"FIX_VALUE"},{"description":"请填写完整的接口地址","display_name":"请求地址","multi_valued":false,"mutability":"readWrite","name":"endpoint","page_control":"TEXT","required":true,"type":"STRING","ui_id":"1802916934316052484","update":true,"value_type":"ADAPTOR"},{"description":"","display_name":"应答编码","multi_valued":false,"mutability":"readWrite","name":"encoding","option_values":["UTF8","GBK"],"page_control":"SELECT","required":true,"type":"STRING","ui_id":"1802916934316052485","update":true,"value_type":"FIX_VALUE"},{"description":"","display_name":"应答类型","multi_valued":false,"mutability":"readonly","name":"responseType","page_control":"HIDDEN","required":true,"type":"STRING","ui_id":"1802916934316052486","update":true,"value":"STRING","value_type":"FIX_VALUE"},{"description":"","display_name":"Header参数","multi_valued":false,"mutability":"readWrite","name":"header","page_control":"EXTEND_OBJ","required":false,"type":"OBJECT","ui_id":"1802916934316052487","update":true,"value_type":"JS_EXP"},{"display_name":"Path参数","multi_valued":false,"mutability":"readWrite","name":"path","page_control":"EXTEND_OBJ","required":false,"type":"OBJECT","ui_id":"1718683877009","update":true,"value_type":"JS_EXP"},{"description":"","display_name":"Query参数","multi_valued":false,"mutability":"readWrite","name":"query","page_control":"EXTEND_OBJ","required":false,"type":"OBJECT","ui_id":"1802916934316052488","update":true,"value_type":"JS_EXP"},{"description":"","display_name":"Body参数","multi_valued":false,"mutability":"readWrite","name":"body","page_control":"TEXTAREA","required":false,"type":"OBJECT","ui_id":"1802916934316052489","update":true,"value_type":"REPLACE_VAR"}],"type":"OBJECT","ui_id":"1868177239215153153","update":true,"value_type":"JS_EXP"}' WHERE id = 41;


-- 优化飞书、睿人事、企业微信集成
INSERT INTO `acm`.`acm_app_action` (`id`, `action_key`, `is_trigger`, `name`, `description`, `connector_id`, `account_schema_id`, `protocol`, `action_type`, `status`, `config_schema`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `sample_output`, `node_struct`) VALUES (113, 'loop_feishu_all', 0, '遍历飞书部门和用户', '根据部门ID遍历该部门下的全部子部门和用户', 1698956136478265345, 13, 'LOOP_FEISHU_ALL', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"部门ID，飞书根部门ID为1\",\"display_name\":\"部门ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"dept_id\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619271\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1866054481177808897\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2024-12-09 17:26:49', 'admin', '2024-12-09 17:37:13', 'admin', 'iam', NULL, 'LOOP');
INSERT INTO `acm`.`acm_app_action` (`id`, `action_key`, `is_trigger`, `name`, `description`, `connector_id`, `account_schema_id`, `protocol`, `action_type`, `status`, `config_schema`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `sample_output`, `node_struct`) VALUES (114, 'loop_rrs_all', 0, '遍历睿人事部门和用户', '根据部门ID遍历该部门下的全部子部门和用户', 29, 2, 'LOOP_RRS_ALL', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"待集成的根部门ID\",\"display_name\":\"部门ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"dept_id\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619271\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\",\"display_name\":\"CorpID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"corp_id\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1866329385341095938\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2024-12-09 17:26:49', 'admin', '2024-12-10 11:49:35', 'admin', 'iam', NULL, 'LOOP');
INSERT INTO `acm`.`acm_app_action` (`id`, `action_key`, `is_trigger`, `name`, `description`, `connector_id`, `account_schema_id`, `protocol`, `action_type`, `status`, `config_schema`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `sample_output`, `node_struct`) VALUES (115, 'loop_wework_all', 0, '遍历企业微信部门和用户', '根据部门ID遍历该部门下的全部子部门和用户', 1699228910102827009, 12, 'LOOP_WEWORK_ALL', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"部门ID，企业微信根部门ID为1\",\"display_name\":\"部门ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"dept_id\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619271\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1866787841349062658\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2024-12-09 17:26:49', 'admin', '2024-12-11 18:11:19', 'admin', 'iam', NULL, 'LOOP');
-- 飞书
delete from `acm`.`acm_flow_node` where flow_id = 1851932244147441665;
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1851932244267044866, 'StartEventNode', 1866001678969397249, 81, 'user_sync', NULL, 1851932244147441665, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1851605414348050433\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1851605414348050434\",\"option_values\":[\"异步\",\"同步\"],\"description\":\"异步：webhook接收到数据后，将直接返回接收成功的结果以及流的信息。\\n同步：Webhook接收到数据后，将等待连接流执行完成并将最后的执行结果返回给调用方（最长等待时间为10s，超时将返回为空）。请查看接口文档获得更多详细信息\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"连接流执行方式\",\"required\":true,\"name\":\"type\",\"mutability\":\"readWrite\",\"value\":\"异步\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1851605414348050435\",\"name\":\"value.org_total\",\"description\":\"预估总的外部部门个数，用于估算集成进度\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"预估部门个数\",\"value\":\"1000\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1851605414348050436\",\"name\":\"value.user_total\",\"description\":\"预估总的外部用户个数，用于估算集成进度\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"预估用户个数\",\"value\":\"10000\",\"required\":true,\"page_control\":\"TEXT\"}],\"name\":\"root\",\"description\":\"启动通讯录集成\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"启动通讯录集成\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', NULL, '{\"hook_url\":null,\"sync\":false,\"type\":\"异步\",\"value\":{\"org_total\":1000,\"user_total\":10000}}', 'SUCCESS', 'OK', '2024-11-03 10:34:36', '2024-11-03 10:34:36', '2024-10-31 18:20:29', 'anonymousUser', '2024-12-09 14:07:22', 'admin', 'iam', 31, 0, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1851932244267044868, 'EndNode', NULL, NULL, NULL, NULL, 1851932244147441665, NULL, NULL, NULL, NULL, 'IDLE', NULL, NULL, NULL, '2024-10-31 18:20:29', 'anonymousUser', '2024-10-31 18:20:29', 'anonymousUser', 'iam', NULL, 0, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1866001678969397249, 'ActionNode', 1866055718159781890, 82, 'record_ext_root_org', NULL, 1851932244147441665, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1866001685577535498\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"ADAPTOR\",\"ui_id\":\"1866001685577535499\",\"name\":\"root_id\",\"description\":\"外部部门的根部门ID\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"外部根部门ID\",\"value\":\"9c33bd2f6e92f231\",\"required\":true,\"page_control\":\"TEXT\"}],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', NULL, '', 'RUNNING', 'OK', '2024-12-09 19:42:18', '2024-12-09 19:42:16', '2024-12-09 14:07:24', 'admin', '2024-12-09 19:42:18', 'admin', 'iam', 31, 0, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1866055718159781890, 'ActionNode', 1866084550549585921, 113, 'loop_feishu_all', 1852904677734191106, 1851932244147441665, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1866054481177808897\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"ADAPTOR\",\"ui_id\":\"1699309157332619271\",\"name\":\"dept_id\",\"description\":\"部门ID，飞书根部门ID为1\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"部门ID\",\"value\":\"{{N1866001678969397249.data.external_id}}\",\"required\":true,\"page_control\":\"TEXT\"}],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', NULL, '{\"current\":{\"dept\":[{\"open_department_id\":\"od-6ff921b0561b23a29904e2c23151c7ae\",\"department_id\":\"123\",\"i18n_name\":{\"en_us\":\"\",\"zh_cn\":\"\",\"ja_jp\":\"\"},\"name\":\"同步\",\"member_count\":2,\"primary_member_count\":2,\"order\":\"2000\",\"parent_department_id\":\"9c33bd2f6e92f231\",\"status\":{\"is_deleted\":false}},{\"open_department_id\":\"od-321\",\"123\":\"7346592b2f6b885d\",\"i18n_name\":{\"en_us\":\"\",\"zh_cn\":\"\",\"ja_jp\":\"\"},\"name\":\"材质\",\"member_count\":0,\"primary_member_count\":0,\"order\":\"4000\",\"parent_department_id\":\"g1b62e6bccf42e3b\",\"status\":{\"is_deleted\":false}},{\"open_department_id\":\"od-40931afe02fd794779f7867e51aed8d0\",\"department_id\":\"e3acb126bc6ac3fb\",\"i18n_name\":{\"en_us\":\"\",\"zh_cn\":\"\",\"ja_jp\":\"\"},\"name\":\"动画\",\"member_count\":2,\"primary_member_count\":2,\"order\":\"2000\",\"parent_department_id\":\"3c3bf7ab34ab654b\",\"status\":{\"is_deleted\":false}},{\"open_department_id\":\"od-538753fc4f425cffa266e9a19a01ca10\",\"department_id\":\"d8e833bd11cgg1gc\",\"i18n_name\":{\"en_us\":\"\",\"zh_cn\":\"\",\"ja_jp\":\"\"},\"name\":\"UV\",\"member_count\":0,\"primary_member_count\":0,\"order\":\"2000\",\"parent_department_id\":\"7346592b2f6b885d\",\"status\":{\"is_deleted\":false}},{\"open_department_id\":\"od-05a8f733e0fb1091468118d5a19b24fb\",\"department_id\":\"ff992b6dc8e284cg\",\"i18n_name\":{\"en_us\":\"\",\"zh_cn\":\"\",\"ja_jp\":\"\"},\"name\":\"野区\",\"member_count\":1,\"primary_member_count\":1,\"order\":\"4000\",\"parent_department_id\":\"9c33bd2f6e92f231\",\"status\":{\"is_deleted\":false}},{\"open_department_id\":\"od-1dd970254393576b23e32beba9b34642\",\"department_id\":\"3c3bf7ab34ab654b\",\"i18n_name\":{\"en_us\":\"\",\"zh_cn\":\"\",\"ja_jp\":\"\"},\"name\":\"建模\",\"member_count\":2,\"primary_member_count\":2,\"order\":\"2000\",\"parent_department_id\":\"g1b62e6bccf42e3b\",\"status\":{\"is_deleted\":false}},{\"open_department_id\":\"od-73d0a8e11b642905e48a855fa2fd8d07\",\"department_id\":\"a926d4326g6c29ea\",\"i18n_name\":{\"en_us\":\"\",\"zh_cn\":\"\",\"ja_jp\":\"\"},\"name\":\"蓝Buff\",\"member_count\":1,\"primary_member_count\":1,\"order\":\"2000\",\"parent_department_id\":\"ff992b6dc8e284cg\",\"status\":{\"is_deleted\":false}},{\"open_department_id\":\"od-5958ac64fb86b818914b9fa233c641a7\",\"department_id\":\"78ab8e1ff1be588b\",\"i18n_name\":{\"en_us\":\"\",\"zh_cn\":\"\",\"ja_jp\":\"\"},\"name\":\"轻舞成双\",\"member_count\":0,\"primary_member_count\":0,\"order\":\"4000\",\"parent_department_id\":\"ff992b6dc8e284cg\",\"status\":{\"is_deleted\":false}}],\"user\":[{\"country\":\"\",\"work_station\":\"\",\"gender\":0,\"city\":\"\",\"open_id\":\"ou_8c6fbda8c6bba235324ba2d5e9562dd3\",\"mobile\":\"+8619958678578\",\"description\":\"\",\"employee_no\":\"\",\"avatar\":{\"avatar_640\":\"https://s1-imfile.feishucdn.com/static-resource/v1/v3_00bq_2a7387d4-d67d-4b05-a57c-ceaca212f84g~?image_size=640x640&cut_type=&quality=&format=png&sticker_format=.webp\",\"avatar_origin\":\"https://s1-imfile.feishucdn.com/static-resource/v1/v3_00bq_2a7387d4-d67d-4b05-a57c-ceaca212f84g~?image_size=noop&cut_type=&quality=&format=png&sticker_format=.webp\",\"avatar_72\":\"https://s1-imfile.feishucdn.com/static-resource/v1/v3_00bq_2a7387d4-d67d-4b05-a57c-ceaca212f84g~?image_size=72x72&cut_type=&quality=&format=png&sticker_format=.webp\",\"avatar_240\":\"https://s1-imfile.feishucdn.com/static-resource/v1/v3_00bq_2a7387d4-d67d-4b05-a57c-ceaca212f84g~?image_size=240x240&cut_type=&quality=&format=png&sticker_format=.webp\"},\"department_ids\":[\"e3acb126bc6ac3fb\"],\"join_time\":1718208000,\"employee_type\":1,\"user_id\":\"auser2\",\"name\":\"建模2个\",\"union_id\":\"on_01dc58928d83fe0bf9d4f85ef7e07c88\",\"en_name\":\"\",\"orders\":[{\"user_order\":0,\"department_id\":\"e3acb126bc6ac3fb\",\"is_primary_dept\":true,\"department_order\":100}],\"is_tenant_manager\":false,\"job_title\":\"\",\"email\":\"\",\"mobile_visible\":true,\"status\":{\"is_activated\":false,\"is_frozen\":false,\"is_resigned\":false,\"is_unjoin\":true,\"is_exited\":false}},{\"country\":\"\",\"work_station\":\"\",\"gender\":0,\"city\":\"\",\"open_id\":\"ou_40392a2eb366621f563db3178d8877b8\",\"mobile\":\"+8613385635412\",\"description\":\"\",\"employee_no\":\"49171592\",\"avatar\":{\"avatar_640\":\"https://s1-imfile.feishucdn.com/static-resource/v1/v3_00bv_6be9f186-4a30-4dbc-b2b5-4409a689a39g~?image_size=640x640&cut_type=&quality=&format=png&sticker_format=.webp\",\"avatar_origin\":\"https://s1-imfile.feishucdn.com/static-resource/v1/v3_00bv_6be9f186-4a30-4dbc-b2b5-4409a689a39g~?image_size=noop&cut_type=&quality=&format=png&sticker_format=.webp\",\"avatar_72\":\"https://s1-imfile.feishucdn.com/static-resource/v1/v3_00bv_6be9f186-4a30-4dbc-b2b5-4409a689a39g~?image_size=72x72&cut_type=&quality=&format=png&sticker_format=.webp\",\"avatar_240\":\"https://s1-imfile.feishucdn.com/static-resource/v1/v3_00bv_6be9f186-4a30-4dbc-b2b5-4409a689a39g~?image_size=240x240&cut_type=&quality=&format=png&sticker_format=.webp\"},\"department_ids\":[\"e3acb126bc6ac3fb\"],\"join_time\":1718640000,\"employee_type\":1,\"user_id\":\"XV72WPH87X5GEZQYV3SV\",\"name\":\"zhang022651716258\",\"union_id\":\"on_83fc993c063024fdae27fabe558148d9\",\"en_name\":\"\",\"orders\":[{\"user_order\":0,\"department_id\":\"e3acb126bc6ac3fb\",\"is_primary_dept\":true,\"department_order\":1}],\"is_tenant_manager\":false,\"job_title\":\"\",\"email\":\"\",\"mobile_visible\":true,\"status\":{\"is_activated\":false,\"is_frozen\":false,\"is_resigned\":false,\"is_unjoin\":true,\"is_exited\":false}},{\"country\":\"\",\"work_station\":\"\",\"gender\":0,\"city\":\"\",\"open_id\":\"ou_d5205f7f2f24bc8a9849508160c1fc62\",\"mobile\":\"+8618837456787\",\"description\":\"\",\"employee_no\":\"\",\"avatar\":{\"avatar_640\":\"https://s1-imfile.feishucdn.com/static-resource/v1/v3_00bq_b37e28a2-2f88-4191-8c25-32b3c0c08eeg~?image_size=640x640&cut_type=&quality=&format=png&sticker_format=.webp\",\"avatar_origin\":\"https://s1-imfile.feishucdn.com/static-resource/v1/v3_00bq_b37e28a2-2f88-4191-8c25-32b3c0c08eeg~?image_size=noop&cut_type=&quality=&format=png&sticker_format=.webp\",\"avatar_72\":\"https://s1-imfile.feishucdn.com/static-resource/v1/v3_00bq_b37e28a2-2f88-4191-8c25-32b3c0c08eeg~?image_size=72x72&cut_type=&quality=&format=png&sticker_format=.webp\",\"avatar_240\":\"https://s1-imfile.feishucdn.com/static-resource/v1/v3_00bq_b37e28a2-2f88-4191-8c25-32b3c0c08eeg~?image_size=240x240&cut_type=&quality=&format=png&sticker_format=.webp\"},\"department_ids\":[\"a926d4326g6c29ea\"],\"join_time\":1718208000,\"employee_type\":1,\"user_id\":\"auser01\",\"name\":\"建模一哥\",\"union_id\":\"on_4c2d8a7f0d4a4ef1eb42e1efb5c87767\",\"en_name\":\"\",\"orders\":[{\"user_order\":0,\"department_id\":\"a926d4326g6c29ea\",\"is_primary_dept\":true,\"department_order\":1}],\"is_tenant_manager\":false,\"job_title\":\"\",\"email\":\"\",\"mobile_visible\":true,\"status\":{\"is_activated\":false,\"is_frozen\":false,\"is_resigned\":false,\"is_unjoin\":true,\"is_exited\":false}}]},\"index\":0,\"type\":\"INNER\"}', 'SUCCESS', 'OK', '2024-12-09 19:24:43', '2024-12-09 19:24:58', '2024-12-09 17:42:07', 'admin', '2024-12-09 19:36:40', 'admin', 'iam', 1698956136478265345, 0, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1866082886790823937, 'ActionNode', 1866084151285399553, 84, 'loop_ext_org', NULL, 1851932244147441665, NULL, '{\"ui_id\":\"1866082898388553730\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1866082898388553731\",\"name\":\"ext_org\",\"type\":\"OBJECT\",\"description\":\"支持一次集成单个或多个外部部门\",\"display_name\":\"外部部门\",\"multi_valued\":true,\"value\":\"{{N1866055718159781890.current.dept}}\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1866082898388553732\",\"name\":\"run_type\",\"type\":\"STRING\",\"description\":\"并行：子流程按多线程并发执行；串行：子流程按单线程顺序执行\",\"display_name\":\"执行方式\",\"multi_valued\":false,\"value\":\"并行\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"并行\",\"串行\"],\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, '{\"current\":{\"open_department_id\":\"od-6ff921b0561b23a29904e2c23151c7ae\",\"department_id\":\"g1b62e6bccf42e3b\",\"i18n_name\":{\"en_us\":\"\",\"zh_cn\":\"\",\"ja_jp\":\"\"},\"name\":\"同步2\",\"member_count\":2,\"primary_member_count\":2,\"order\":\"2000\",\"parent_department_id\":\"9c33bd2f6e92f231\",\"status\":{\"is_deleted\":false}},\"index\":0,\"type\":\"INNER\"}', 'SUCCESS', 'OK', '2024-12-09 19:30:29', '2024-12-09 19:30:29', '2024-12-09 19:30:05', 'admin', '2024-12-09 19:34:48', 'admin', 'iam', 31, 1866055718159781890, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1866083140953063425, 'ActionNode', NULL, 85, 'sync_update_org', NULL, 1851932244147441665, NULL, '{\"ui_id\":\"1866083150579470350\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1866083150579470351\",\"name\":\"ext_org\",\"type\":\"OBJECT\",\"description\":\"待集成的外部部门对象（json）信息\",\"display_name\":\"外部部门对象\",\"multi_valued\":false,\"value\":\"{{N1866082886790823937.current}}\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, NULL, 'INVALID', NULL, NULL, NULL, '2024-12-09 19:31:06', 'admin', '2024-12-09 19:31:16', 'admin', 'iam', 31, 1866082886790823937, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1866084151285399553, 'ActionNode', NULL, 87, 'loop_ext_user', NULL, 1851932244147441665, NULL, '{\"ui_id\":\"1866084160597233669\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1866084160597233670\",\"name\":\"ext_user\",\"type\":\"OBJECT\",\"description\":\"支持一次读入单个或多个外部用户（json）对象\",\"display_name\":\"外部用户\",\"multi_valued\":true,\"value\":\"{{N1866055718159781890.current.user}}\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1866084160597233671\",\"name\":\"run_type\",\"type\":\"STRING\",\"description\":\"并行：子流程按多线程并发执行；串行：子流程按单线程顺序执行\",\"display_name\":\"执行方式\",\"multi_valued\":false,\"value\":\"并行\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"并行\",\"串行\"],\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, '{\"current\":{\"country\":\"\",\"work_station\":\"\",\"gender\":0,\"city\":\"\",\"open_id\":\"ou_8c6fbda8c6bba235324ba2d5e9562dd3\",\"mobile\":\"+8619958678578\",\"description\":\"\",\"employee_no\":\"\",\"avatar\":{\"avatar_640\":\"https://s1-imfile.feishucdn.com/static-resource/v1/v3_00bq_2a7387d4-d67d-4b05-a57c-ceaca212f84g~?image_size=640x640&cut_type=&quality=&format=png&sticker_format=.webp\",\"avatar_origin\":\"https://s1-imfile.feishucdn.com/static-resource/v1/v3_00bq_2a7387d4-d67d-4b05-a57c-ceaca212f84g~?image_size=noop&cut_type=&quality=&format=png&sticker_format=.webp\",\"avatar_72\":\"https://s1-imfile.feishucdn.com/static-resource/v1/v3_00bq_2a7387d4-d67d-4b05-a57c-ceaca212f84g~?image_size=72x72&cut_type=&quality=&format=png&sticker_format=.webp\",\"avatar_240\":\"https://s1-imfile.feishucdn.com/static-resource/v1/v3_00bq_2a7387d4-d67d-4b05-a57c-ceaca212f84g~?image_size=240x240&cut_type=&quality=&format=png&sticker_format=.webp\"},\"department_ids\":[\"e3acb126bc6ac3fb\"],\"join_time\":1718208000,\"employee_type\":1,\"user_id\":\"auser2\",\"name\":\"建模2个\",\"union_id\":\"on_01dc58928d83fe0bf9d4f85ef7e07c88\",\"en_name\":\"\",\"orders\":[{\"user_order\":0,\"department_id\":\"e3acb126bc6ac3fb\",\"is_primary_dept\":true,\"department_order\":100}],\"is_tenant_manager\":false,\"job_title\":\"\",\"email\":\"\",\"mobile_visible\":true,\"status\":{\"is_activated\":false,\"is_frozen\":false,\"is_resigned\":false,\"is_unjoin\":true,\"is_exited\":false}},\"index\":0,\"type\":\"INNER\"}', 'SUCCESS', 'OK', '2024-12-09 19:35:37', '2024-12-09 19:35:37', '2024-12-09 19:35:06', 'admin', '2024-12-09 19:35:48', 'admin', 'iam', 31, 1866055718159781890, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1866084345007718402, 'ActionNode', NULL, 88, 'sync_update_user', NULL, 1851932244147441665, NULL, '{\"ui_id\":\"1866084440533471248\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1866084440533471249\",\"name\":\"ext_user\",\"type\":\"OBJECT\",\"description\":\"待集成的外部用户对象（json）信息\",\"display_name\":\"外部用户对象\",\"multi_valued\":false,\"value\":\"{{N1866084151285399553.current}}\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, NULL, 'INVALID', NULL, NULL, NULL, '2024-12-09 19:35:53', 'admin', '2024-12-09 19:36:34', 'admin', 'iam', 31, 1866084151285399553, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1866084550549585921, 'ActionNode', 1851932244267044868, 89, 'complete_user_sync', NULL, 1851932244147441665, NULL, '{\"ui_id\":\"1866084569155997697\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', '{}', '{\"code\":\"0\",\"data\":null,\"message\":\"SUCCESS\"}', 'SUCCESS', 'OK', '2024-12-09 19:37:08', '2024-12-09 19:37:08', '2024-12-09 19:36:42', 'admin', '2024-12-09 19:37:08', 'admin', 'iam', 31, 0, NULL, '0', NULL);

-- 睿人事
delete from `acm`.`acm_flow_node` where flow_id = 1851905069528649729;
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1866680056951148545, 'StartEventNode', 1866680056951148547, 81, 'user_sync', NULL, 1851905069528649729, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1851605414348050433\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1851605414348050434\",\"option_values\":[\"异步\",\"同步\"],\"description\":\"异步：webhook接收到数据后，将直接返回接收成功的结果以及流的信息。\\n同步：Webhook接收到数据后，将等待连接流执行完成并将最后的执行结果返回给调用方（最长等待时间为10s，超时将返回为空）。请查看接口文档获得更多详细信息\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"连接流执行方式\",\"required\":true,\"name\":\"type\",\"mutability\":\"readWrite\",\"value\":\"异步\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1851605414348050435\",\"name\":\"value.org_total\",\"description\":\"预估总的外部部门个数，用于估算集成进度\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"预估部门个数\",\"value\":\"1000\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1851605414348050436\",\"name\":\"value.user_total\",\"description\":\"预估总的外部用户个数，用于估算集成进度\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"预估用户个数\",\"value\":\"10000\",\"required\":true,\"page_control\":\"TEXT\"}],\"name\":\"root\",\"description\":\"启动通讯录集成\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"启动通讯录集成\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', NULL, '{\"hook_url\":null,\"sync\":false,\"type\":\"异步\",\"value\":{\"org_total\":1000,\"user_total\":10000}}', 'INVALID', 'OK', '2024-11-13 17:22:41', '2024-11-13 17:22:41', '2024-12-11 11:03:01', 'admin', '2024-12-11 11:03:01', 'admin', 'iam', 31, 0, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1866680056951148547, 'ActionNode', 1866680056951148548, 82, 'record_ext_root_org', NULL, 1851905069528649729, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1866328416033247243\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"ADAPTOR\",\"ui_id\":\"1866328416033247244\",\"name\":\"root_id\",\"description\":\"外部部门的根部门ID\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"外部根部门ID\",\"value\":\"org_a7a3d455c3f240e4865c6478bdb16216\",\"required\":true,\"page_control\":\"TEXT\"}],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', NULL, '{\"code\":\"0\",\"data\":{\"external_id\":\"org_a7a3d455c3f240e4865c6478bdb16216\",\"flag\":\"UPDATE\",\"local\":\"1866673429265047554\",\"message\":\"\"},\"message\":\"SUCCESS\"}', 'SUCCESS', 'OK', '2024-12-11 11:04:16', '2024-12-11 11:04:16', '2024-12-11 11:03:01', 'admin', '2024-12-11 11:04:16', 'admin', 'iam', 31, 0, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1866680056951148546, 'EndNode', NULL, NULL, NULL, NULL, 1851905069528649729, NULL, NULL, NULL, NULL, 'IDLE', NULL, NULL, NULL, '2024-12-11 11:03:01', 'admin', '2024-12-11 11:03:01', 'admin', 'iam', NULL, 0, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1866680056951148548, 'ActionNode', 1866680056951148553, 114, 'loop_rrs_all', 1851913236094488577, 1851905069528649729, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1866329385341095938\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"ADAPTOR\",\"ui_id\":\"1699309157332619271\",\"name\":\"dept_id\",\"description\":\"待集成的根部门ID\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"部门ID\",\"value\":\"{{N1866680056951148547.data.external_id}}\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"ADAPTOR\",\"ui_id\":\"*************\",\"name\":\"corp_id\",\"description\":\"\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"CorpID\",\"value\":\"ding123\",\"required\":true,\"page_control\":\"TEXT\"}],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', NULL, '{\"current\":{\"dept\":[{\"effectDate\":\"2023-07-30 00:00:00\",\"gmtModified\":\"2024-09-11 17:42:59\",\"code\":\"99\",\"corpId\":\"ding123\",\"managerEmpId\":[],\"type\":\"department\",\"gmtCreate\":\"2023-07-26 16:16:58\",\"invalidDate\":\"2099-12-31 00:00:00\",\"delete\":\"0\",\"orgId\":\"org_9c4d937a26474fdca8611e43adf95294\",\"parentOrgId\":\"org_a7a3d455c3f240e4865c6478bdb16216\",\"children\":[],\"disable\":false,\"name\":\"cb走查测试\",\"id\":\"64c0d69c0c4ae92d643f0445\",\"syncStatus\":\"true\",\"status\":\"disable\",\"order\":9},{\"effectDate\":\"2024-03-27 00:00:00\",\"gmtModified\":\"2024-03-27 10:10:46\",\"corpId\":\"ding123\",\"managerEmpId\":[],\"type\":\"department\",\"gmtCreate\":\"2024-03-27 10:10:46\",\"invalidDate\":\"2099-12-31 00:00:00\",\"delete\":\"0\",\"orgId\":\"922700395\",\"parentOrgId\":\"org_a7a3d455c3f240e4865c6478bdb16216\",\"children\":[],\"disable\":false,\"name\":\"从晚上\",\"id\":\"66038026abfd9a1435167fed\",\"syncStatus\":\"true\",\"status\":\"enable\",\"order\":99999999},{\"effectDate\":\"2024-04-03 00:00:00\",\"gmtModified\":\"2024-04-03 16:52:55\",\"corpId\":\"ding123\",\"managerEmpId\":[],\"type\":\"department\",\"gmtCreate\":\"2024-04-03 16:52:55\",\"invalidDate\":\"2099-12-31 00:00:00\",\"delete\":\"0\",\"orgId\":\"923948286\",\"parentOrgId\":\"org_a7a3d455c3f240e4865c6478bdb16216\",\"children\":[],\"disable\":false,\"name\":\"aaa\",\"id\":\"660d18e70ea9b21e450a35c0\",\"syncStatus\":\"true\",\"status\":\"enable\",\"order\":*********},{\"effectDate\":\"2024-09-11 00:00:00\",\"gmtModified\":\"2024-09-11 17:05:08\",\"corpId\":\"ding123\",\"managerEmpId\":[\"emp_37950cb0bc564174baedfffaf2c22bc7\",\"emp_49d919cd470e46bda745cf633107bc8d\"],\"type\":\"department\",\"gmtCreate\":\"2024-09-11 17:05:08\",\"invalidDate\":\"2099-12-31 00:00:00\",\"delete\":\"0\",\"orgId\":\"885206696\",\"parentOrgId\":\"org_a7a3d455c3f240e4865c6478bdb16216\",\"children\":[],\"disable\":false,\"name\":\"小四叶草1111\",\"id\":\"66e15d4417cdb341fe6b4e33\",\"syncStatus\":\"true\",\"status\":\"enable\",\"order\":10},{\"effectDate\":\"2024-09-11 00:00:00\",\"gmtModified\":\"2024-09-11 17:05:48\",\"corpId\":\"ding123\",\"type\":\"department\",\"gmtCreate\":\"2023-11-02 17:23:43\",\"invalidDate\":\"2099-12-31 00:00:00\",\"delete\":\"0\",\"orgId\":\"903872392\",\"parentOrgId\":\"org_a7a3d455c3f240e4865c6478bdb16216\",\"children\":[],\"disable\":false,\"name\":\"1231\",\"id\":\"66e15d6c17cdb341fe6b4e34\",\"syncStatus\":\"true\",\"status\":\"disable\",\"order\":11},{\"effectDate\":\"2024-09-11 00:00:00\",\"gmtModified\":\"2024-09-11 18:21:12\",\"corpId\":\"ding123\",\"type\":\"department\",\"gmtCreate\":\"2024-09-11 17:44:01\",\"invalidDate\":\"2099-12-31 00:00:00\",\"delete\":\"0\",\"orgId\":\"org_25188b51008649f78cd1680cc0dded70\",\"parentOrgId\":\"org_a7a3d455c3f240e4865c6478bdb16216\",\"children\":[],\"disable\":false,\"name\":\"测试停用\",\"id\":\"66e1666117cdb341fe6b4e8f\",\"syncStatus\":\"true\",\"status\":\"enable\",\"order\":10},{\"effectDate\":\"2023-08-07 00:00:00\",\"gmtModified\":\"2024-02-01 14:43:16\",\"corpId\":\"ding123\",\"type\":\"department\",\"gmtCreate\":\"2023-08-07 16:13:16\",\"invalidDate\":\"2099-12-31 00:00:00\",\"delete\":\"0\",\"orgId\":\"885351337\",\"parentOrgId\":\"885206696\",\"children\":[],\"disable\":false,\"name\":\"嘻嘻\",\"id\":\"64d0a79ca6d21a297c56efd2\",\"syncStatus\":\"true\",\"status\":\"enable\",\"order\":1},{\"effectDate\":\"2024-06-13 00:00:00\",\"gmtModified\":\"2024-06-13 17:55:31\",\"code\":\"1231\",\"corpId\":\"ding123\",\"managerEmpId\":[\"emp_123\"],\"type\":\"subCorporation\",\"gmtCreate\":\"2024-06-13 17:55:30\",\"invalidDate\":\"2099-12-31 00:00:00\",\"delete\":\"0\",\"orgId\":\"org_eb44373e48bb43caa0f3201f4d999295\",\"parentOrgId\":\"885351337\",\"children\":[],\"disable\":false,\"name\":\"hq1212111\",\"id\":\"666ac2132b3358780688957c\",\"syncStatus\":\"true\",\"status\":\"enable\",\"order\":-1111111},{\"effectDate\":\"2023-07-27 00:00:00\",\"gmtModified\":\"2024-02-01 14:43:27\",\"corpId\":\"ding123\",\"type\":\"department\",\"gmtCreate\":\"2023-07-26 16:23:20\",\"invalidDate\":\"2099-12-31 00:00:00\",\"delete\":\"0\",\"orgId\":\"org_5dc9fba4b8dd4cc5a8cdf28c4f074fc2\",\"parentOrgId\":\"org_eb44373e48bb43caa0f3201f4d999295\",\"children\":[],\"disable\":false,\"name\":\"hq2\",\"id\":\"64c0d8fb0c4ae92d643f04ef\",\"syncStatus\":\"true\",\"status\":\"disable\",\"order\":0},{\"effectDate\":\"2023-07-27 00:00:00\",\"gmtModified\":\"2024-02-01 14:43:33\",\"corpId\":\"ding123\",\"type\":\"department\",\"gmtCreate\":\"2023-07-26 16:23:20\",\"invalidDate\":\"2099-12-31 00:00:00\",\"delete\":\"0\",\"orgId\":\"org_746622fe5cbb425a94d30d76f23a4f74\",\"parentOrgId\":\"org_5dc9fba4b8dd4cc5a8cdf28c4f074fc2\",\"children\":[],\"disable\":false,\"name\":\"hq3\",\"id\":\"64c0d8f20c4ae92d643f04ec\",\"syncStatus\":\"true\",\"status\":\"disable\",\"order\":0}],\"user\":[]},\"index\":0,\"type\":\"INNER\"}', 'SUCCESS', 'OK', '2024-12-11 11:04:23', '2024-12-11 11:04:24', '2024-12-11 11:03:01', 'admin', '2024-12-11 11:05:06', 'admin', 'iam', 29, 0, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1866680056951148549, 'ActionNode', 1866680056951148551, 84, 'loop_ext_org', NULL, 1851905069528649729, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1866360356648062979\",\"sub_params\":[{\"multi_valued\":true,\"value_type\":\"ADAPTOR\",\"ui_id\":\"1866360356648062980\",\"name\":\"ext_org\",\"description\":\"支持一次集成单个或多个外部部门\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"外部部门\",\"value\":\"{{N1866680056951148548.current.dept}}\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"ADAPTOR\",\"ui_id\":\"1866360356648062981\",\"option_values\":[\"并行\",\"串行\"],\"description\":\"并行：子流程按多线程并发执行；串行：子流程按单线程顺序执行\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"执行方式\",\"required\":true,\"name\":\"run_type\",\"mutability\":\"readWrite\",\"value\":\"串行\",\"page_control\":\"SELECT\"}],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', '{\"run_type\":\"串行\",\"ext_org\":[{\"effectDate\":\"2023-07-30 00:00:00\",\"gmtModified\":\"2024-09-11 17:42:59\",\"code\":\"99\",\"corpId\":\"ding123\",\"managerEmpId\":[],\"type\":\"department\",\"gmtCreate\":\"2023-07-26 16:16:58\",\"invalidDate\":\"2099-12-31 00:00:00\",\"delete\":\"0\",\"orgId\":\"org_9c4d937a26474fdca8611e43adf95294\",\"parentOrgId\":\"org_a7a3d455c3f240e4865c6478bdb16216\",\"children\":[],\"disable\":false,\"name\":\"cb走查测试\",\"id\":\"64c0d69c0c4ae92d643f0445\",\"syncStatus\":\"true\",\"status\":\"disable\",\"order\":9},{\"effectDate\":\"2024-03-27 00:00:00\",\"gmtModified\":\"2024-03-27 10:10:46\",\"corpId\":\"ding123\",\"managerEmpId\":[],\"type\":\"department\",\"gmtCreate\":\"2024-03-27 10:10:46\",\"invalidDate\":\"2099-12-31 00:00:00\",\"delete\":\"0\",\"orgId\":\"922700395\",\"parentOrgId\":\"org_a7a3d455c3f240e4865c6478bdb16216\",\"children\":[],\"disable\":false,\"name\":\"从晚上\",\"id\":\"66038026abfd9a1435167fed\",\"syncStatus\":\"true\",\"status\":\"enable\",\"order\":99999999},{\"effectDate\":\"2024-04-03 00:00:00\",\"gmtModified\":\"2024-04-03 16:52:55\",\"corpId\":\"ding123\",\"managerEmpId\":[],\"type\":\"department\",\"gmtCreate\":\"2024-04-03 16:52:55\",\"invalidDate\":\"2099-12-31 00:00:00\",\"delete\":\"0\",\"orgId\":\"923948286\",\"parentOrgId\":\"org_a7a3d455c3f240e4865c6478bdb16216\",\"children\":[],\"disable\":false,\"name\":\"aaa\",\"id\":\"660d18e70ea9b21e450a35c0\",\"syncStatus\":\"true\",\"status\":\"enable\",\"order\":*********},{\"effectDate\":\"2024-09-11 00:00:00\",\"gmtModified\":\"2024-09-11 17:05:08\",\"corpId\":\"ding123\",\"managerEmpId\":[\"emp_37950cb0bc564174baedfffaf2c22bc7\",\"emp_49d919cd470e46bda745cf633107bc8d\"],\"type\":\"department\",\"gmtCreate\":\"2024-09-11 17:05:08\",\"invalidDate\":\"2099-12-31 00:00:00\",\"delete\":\"0\",\"orgId\":\"885206696\",\"parentOrgId\":\"org_a7a3d455c3f240e4865c6478bdb16216\",\"children\":[],\"disable\":false,\"name\":\"小四叶草1111\",\"id\":\"66e15d4417cdb341fe6b4e33\",\"syncStatus\":\"true\",\"status\":\"enable\",\"order\":10},{\"effectDate\":\"2024-09-11 00:00:00\",\"gmtModified\":\"2024-09-11 17:05:48\",\"corpId\":\"ding123\",\"type\":\"department\",\"gmtCreate\":\"2023-11-02 17:23:43\",\"invalidDate\":\"2099-12-31 00:00:00\",\"delete\":\"0\",\"orgId\":\"903872392\",\"parentOrgId\":\"org_a7a3d455c3f240e4865c6478bdb16216\",\"children\":[],\"disable\":false,\"name\":\"1231\",\"id\":\"66e15d6c17cdb341fe6b4e34\",\"syncStatus\":\"true\",\"status\":\"disable\",\"order\":11},{\"effectDate\":\"2024-09-11 00:00:00\",\"gmtModified\":\"2024-09-11 18:21:12\",\"corpId\":\"ding123\",\"type\":\"department\",\"gmtCreate\":\"2024-09-11 17:44:01\",\"invalidDate\":\"2099-12-31 00:00:00\",\"delete\":\"0\",\"orgId\":\"org_25188b51008649f78cd1680cc0dded70\",\"parentOrgId\":\"org_a7a3d455c3f240e4865c6478bdb16216\",\"children\":[],\"disable\":false,\"name\":\"测试停用\",\"id\":\"66e1666117cdb341fe6b4e8f\",\"syncStatus\":\"true\",\"status\":\"enable\",\"order\":10},{\"effectDate\":\"2023-08-07 00:00:00\",\"gmtModified\":\"2024-02-01 14:43:16\",\"corpId\":\"ding123\",\"type\":\"department\",\"gmtCreate\":\"2023-08-07 16:13:16\",\"invalidDate\":\"2099-12-31 00:00:00\",\"delete\":\"0\",\"orgId\":\"885351337\",\"parentOrgId\":\"885206696\",\"children\":[],\"disable\":false,\"name\":\"嘻嘻\",\"id\":\"64d0a79ca6d21a297c56efd2\",\"syncStatus\":\"true\",\"status\":\"enable\",\"order\":1},{\"effectDate\":\"2024-06-13 00:00:00\",\"gmtModified\":\"2024-06-13 17:55:31\",\"code\":\"1231\",\"corpId\":\"ding123\",\"managerEmpId\":[\"emp_123\"],\"type\":\"subCorporation\",\"gmtCreate\":\"2024-06-13 17:55:30\",\"invalidDate\":\"2099-12-31 00:00:00\",\"delete\":\"0\",\"orgId\":\"org_eb44373e48bb43caa0f3201f4d999295\",\"parentOrgId\":\"885351337\",\"children\":[],\"disable\":false,\"name\":\"hq1212111\",\"id\":\"666ac2132b3358780688957c\",\"syncStatus\":\"true\",\"status\":\"enable\",\"order\":-1111111},{\"effectDate\":\"2023-07-27 00:00:00\",\"gmtModified\":\"2024-02-01 14:43:27\",\"corpId\":\"ding123\",\"type\":\"department\",\"gmtCreate\":\"2023-07-26 16:23:20\",\"invalidDate\":\"2099-12-31 00:00:00\",\"delete\":\"0\",\"orgId\":\"org_5dc9fba4b8dd4cc5a8cdf28c4f074fc2\",\"parentOrgId\":\"org_eb44373e48bb43caa0f3201f4d999295\",\"children\":[],\"disable\":false,\"name\":\"hq2\",\"id\":\"64c0d8fb0c4ae92d643f04ef\",\"syncStatus\":\"true\",\"status\":\"disable\",\"order\":0},{\"effectDate\":\"2023-07-27 00:00:00\",\"gmtModified\":\"2024-02-01 14:43:33\",\"corpId\":\"ding123\",\"type\":\"department\",\"gmtCreate\":\"2023-07-26 16:23:20\",\"invalidDate\":\"2099-12-31 00:00:00\",\"delete\":\"0\",\"orgId\":\"org_746622fe5cbb425a94d30d76f23a4f74\",\"parentOrgId\":\"org_5dc9fba4b8dd4cc5a8cdf28c4f074fc2\",\"children\":[],\"disable\":false,\"name\":\"hq3\",\"id\":\"64c0d8f20c4ae92d643f04ec\",\"syncStatus\":\"true\",\"status\":\"disable\",\"order\":0}]}', '{\"current\":{\"effectDate\":\"2023-07-30 00:00:00\",\"gmtModified\":\"2024-09-11 17:42:59\",\"code\":\"99\",\"corpId\":\"ding123\",\"managerEmpId\":[],\"type\":\"department\",\"gmtCreate\":\"2023-07-26 16:16:58\",\"invalidDate\":\"2099-12-31 00:00:00\",\"delete\":\"0\",\"orgId\":\"org_9c4d937a26474fdca8611e43adf95294\",\"parentOrgId\":\"org_a7a3d455c3f240e4865c6478bdb16216\",\"children\":[],\"disable\":false,\"name\":\"cb走查测试\",\"id\":\"64c0d69c0c4ae92d643f0445\",\"syncStatus\":\"true\",\"status\":\"disable\",\"order\":9},\"index\":0,\"type\":\"INNER\"}', 'SUCCESS', 'OK', '2024-12-11 11:05:27', '2024-12-11 11:05:28', '2024-12-11 11:03:01', 'admin', '2024-12-11 11:05:29', 'admin', 'iam', 31, 1866680056951148548, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1866680056951148550, 'ActionNode', NULL, 85, 'sync_update_org', NULL, 1851905069528649729, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1866360440899047439\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"ADAPTOR\",\"ui_id\":\"1866360440899047440\",\"name\":\"ext_org\",\"description\":\"待集成的外部部门对象（json）信息\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"外部部门对象\",\"value\":\"{{N1866680056951148549.current}}\",\"required\":true,\"page_control\":\"TEXT\"}],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', NULL, '{\"code\":\"0\",\"data\":[{\"external_id\":\"org_9c4d937a26474fdca8611e43adf95294\",\"flag\":\"NOUPDATE\",\"local\":{\"parentRefId\":\"1866673429265047554\",\"manager\":\"\",\"name\":\"cb走查测试\",\"connector_parent_org_id\":\"org_a7a3d455c3f240e4865c6478bdb16216\",\"org_path\":\"-1,1866673429265047554,1866680400701239298\",\"id\":\"1866680400701239298\",\"connector_org_id\":\"org_9c4d937a26474fdca8611e43adf95294\",\"seq\":\"9\",\"connector_manager\":[]},\"message\":\"\"}],\"message\":\"SUCCESS\"}', 'SUCCESS', 'OK', '2024-12-11 11:05:33', '2024-12-11 11:05:33', '2024-12-11 11:03:01', 'admin', '2024-12-11 11:05:35', 'admin', 'iam', 31, 1866680056951148549, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1866680056951148551, 'ActionNode', NULL, 87, 'loop_ext_user', NULL, 1851905069528649729, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1866360507689144326\",\"sub_params\":[{\"multi_valued\":true,\"value_type\":\"ADAPTOR\",\"ui_id\":\"1866360507689144327\",\"name\":\"ext_user\",\"description\":\"支持一次读入单个或多个外部用户（json）对象\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"外部用户\",\"value\":\"{{N1866680056951148548.current.user}}\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"ADAPTOR\",\"ui_id\":\"1866360507689144328\",\"option_values\":[\"并行\",\"串行\"],\"description\":\"并行：子流程按多线程并发执行；串行：子流程按单线程顺序执行\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"执行方式\",\"required\":true,\"name\":\"run_type\",\"mutability\":\"readWrite\",\"value\":\"并行\",\"page_control\":\"SELECT\"}],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', '{\"run_type\":\"并行\",\"ext_user\":[]}', '{\"current\":null,\"index\":0,\"type\":\"INNER\"}', 'SUCCESS', 'OK', '2024-12-11 11:05:38', '2024-12-11 11:05:38', '2024-12-11 11:03:02', 'admin', '2024-12-11 11:05:40', 'admin', 'iam', 31, 1866680056951148548, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1866680056951148552, 'ActionNode', NULL, 88, 'sync_update_user', NULL, 1851905069528649729, NULL, '{\"ui_id\":\"1866360586458173458\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1866360586458173459\",\"name\":\"ext_user\",\"type\":\"OBJECT\",\"description\":\"待集成的外部用户对象（json）信息\",\"display_name\":\"外部用户对象\",\"multi_valued\":false,\"value\":\"{{N1866680056951148551.current}}\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, '', 'IDLE', NULL, NULL, '2024-12-11 11:05:38', '2024-12-11 11:03:02', 'admin', '2024-12-11 11:05:38', 'admin', 'iam', 31, 1866680056951148551, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1866680056951148553, 'ActionNode', 1866680056951148546, 89, 'complete_user_sync', NULL, 1851905069528649729, NULL, '{\"ui_id\":\"1866361308734099458\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, '{\"code\":\"0\",\"data\":null,\"message\":\"SUCCESS\"}', 'INVALID', 'OK', '2024-12-10 13:56:31', '2024-12-10 13:56:31', '2024-12-11 11:03:02', 'admin', '2024-12-11 11:03:02', 'admin', 'iam', 31, 0, NULL, '0', NULL);

-- 企业微信
delete from `acm`.`acm_flow_node` where flow_id = 1851884701728423938;
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1866787483507822594, 'StartEventNode', 1866788874466885633, 81, 'user_sync', NULL, 1851884701728423938, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1851605414348050433\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1851605414348050434\",\"option_values\":[\"异步\",\"同步\"],\"description\":\"异步：webhook接收到数据后，将直接返回接收成功的结果以及流的信息。\\n同步：Webhook接收到数据后，将等待连接流执行完成并将最后的执行结果返回给调用方（最长等待时间为10s，超时将返回为空）。请查看接口文档获得更多详细信息\",\"update\":true,\"type\":\"STRING\",\"display_name\":\"连接流执行方式\",\"required\":true,\"name\":\"type\",\"mutability\":\"readWrite\",\"value\":\"异步\",\"page_control\":\"SELECT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1851605414348050435\",\"name\":\"value.org_total\",\"description\":\"预估总的外部部门个数，用于估算集成进度\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"预估部门个数\",\"value\":\"1000\",\"required\":true,\"page_control\":\"TEXT\"},{\"multi_valued\":false,\"value_type\":\"FIX_VALUE\",\"ui_id\":\"1851605414348050436\",\"name\":\"value.user_total\",\"description\":\"预估总的外部用户个数，用于估算集成进度\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"NUMBER\",\"display_name\":\"预估用户个数\",\"value\":\"10000\",\"required\":true,\"page_control\":\"TEXT\"}],\"name\":\"root\",\"description\":\"启动通讯录集成\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"启动通讯录集成\",\"required\":true,\"page_control\":\"FIXED_OBJ\"}', NULL, '{\"hook_url\":null,\"sync\":false,\"type\":\"异步\",\"value\":{\"org_total\":1000,\"user_total\":10000}}', 'INVALID', 'OK', '2024-10-31 18:51:00', '2024-10-31 18:51:00', '2024-12-11 18:09:54', 'admin', '2024-12-11 18:15:24', 'admin', 'iam', 31, 0, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1866787483507822595, 'EndNode', NULL, NULL, NULL, NULL, 1851884701728423938, NULL, NULL, NULL, NULL, 'IDLE', NULL, NULL, NULL, '2024-12-11 18:09:54', 'admin', '2024-12-11 18:09:54', 'admin', 'iam', NULL, 0, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1866788874466885633, 'ActionNode', 1866788917345255426, 82, 'record_ext_root_org', NULL, 1851884701728423938, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1866788883587469323\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"ADAPTOR\",\"ui_id\":\"1866788883587469324\",\"name\":\"root_id\",\"description\":\"外部部门的根部门ID\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"外部根部门ID\",\"value\":\"13\",\"required\":true,\"page_control\":\"TEXT\"}],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', NULL, '{\"code\":\"0\",\"data\":{\"external_id\":\"13\",\"flag\":\"UPDATE\",\"local\":\"1866787482561994754\",\"message\":\"\"},\"message\":\"SUCCESS\"}', 'SUCCESS', 'OK', '2024-12-11 19:03:36', '2024-12-11 19:03:36', '2024-12-11 18:15:26', 'admin', '2024-12-11 19:03:38', 'admin', 'iam', 31, 0, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1866788917345255426, 'ActionNode', 1866797486066835458, 115, 'loop_wework_all', 1851886512954773505, 1851884701728423938, NULL, '{\"multi_valued\":false,\"value_type\":\"JS_EXP\",\"ui_id\":\"1866787841349062658\",\"sub_params\":[{\"multi_valued\":false,\"value_type\":\"ADAPTOR\",\"ui_id\":\"1699309157332619271\",\"name\":\"dept_id\",\"description\":\"部门ID，企业微信根部门ID为1\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"STRING\",\"display_name\":\"部门ID\",\"value\":\"{{N1866788874466885633.data.external_id}}\",\"required\":true,\"page_control\":\"TEXT\"}],\"name\":\"root\",\"update\":true,\"mutability\":\"readWrite\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"required\":false,\"page_control\":\"FIXED_OBJ\"}', NULL, '{\"current\":{\"dept\":[{\"name\":\"微信\",\"id\":13,\"parentid\":1,\"department_leader\":[],\"order\":********,\"name_en\":\"微信\"},{\"name\":\"微信-子组织1号\",\"id\":14,\"parentid\":13,\"department_leader\":[],\"order\":*********},{\"name\":\"角色同步\",\"id\":15,\"parentid\":14,\"department_leader\":[],\"order\":*********},{\"name\":\"wqx部门群测试\",\"id\":17,\"parentid\":16,\"department_leader\":[\"284712604129489365\"],\"order\":*********},{\"name\":\"钉钉登录\",\"id\":16,\"parentid\":13,\"department_leader\":[],\"order\":********}],\"user\":[]},\"index\":0,\"type\":\"INNER\"}', 'SUCCESS', 'OK', '2024-12-11 19:03:40', '2024-12-11 19:03:42', '2024-12-11 18:15:36', 'admin', '2024-12-11 19:03:52', 'admin', 'iam', 1699228910102827009, 0, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1866797141710282754, 'ActionNode', 1866797274749411329, 84, 'loop_ext_org', NULL, 1851884701728423938, NULL, '{\"ui_id\":\"1866797154314588163\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1866797154314588164\",\"name\":\"ext_org\",\"type\":\"OBJECT\",\"description\":\"支持一次集成单个或多个外部部门\",\"display_name\":\"外部部门\",\"multi_valued\":true,\"value\":\"{{N1866788917345255426.current.dept}}\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1866797154314588165\",\"name\":\"run_type\",\"type\":\"STRING\",\"description\":\"并行：子流程按多线程并发执行；串行：子流程按单线程顺序执行\",\"display_name\":\"执行方式\",\"multi_valued\":false,\"value\":\"串行\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"并行\",\"串行\"],\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, '{\"current\":{\"name\":\"微信\",\"id\":13,\"parentid\":1,\"department_leader\":[],\"order\":********,\"name_en\":\"微信\"},\"index\":0,\"type\":\"INNER\"}', 'SUCCESS', 'OK', '2024-12-11 19:03:42', '2024-12-11 19:03:42', '2024-12-11 18:48:17', 'admin', '2024-12-11 19:03:42', 'admin', 'iam', 31, 1866788917345255426, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1866797274749411329, 'ActionNode', NULL, 87, 'loop_ext_user', NULL, 1851884701728423938, NULL, '{\"ui_id\":\"1866797288733642757\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1866797288733642758\",\"name\":\"ext_user\",\"type\":\"OBJECT\",\"description\":\"支持一次读入单个或多个外部用户（json）对象\",\"display_name\":\"外部用户\",\"multi_valued\":true,\"value\":\"{{N1866788917345255426.current.user}}\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1866797288733642759\",\"name\":\"run_type\",\"type\":\"STRING\",\"description\":\"并行：子流程按多线程并发执行；串行：子流程按单线程顺序执行\",\"display_name\":\"执行方式\",\"multi_valued\":false,\"value\":\"并行\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"并行\",\"串行\"],\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', '{\"run_type\":\"并行\",\"ext_user\":[]}', '{\"current\":null,\"index\":0,\"type\":\"INNER\"}', 'SUCCESS', 'OK', '2024-12-11 19:03:42', '2024-12-11 19:03:42', '2024-12-11 18:48:48', 'admin', '2024-12-11 19:03:42', 'admin', 'iam', 31, 1866788917345255426, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1866797394802974722, 'ActionNode', NULL, 88, 'sync_update_user', NULL, 1851884701728423938, NULL, '{\"ui_id\":\"1866797405041692688\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1866797405041692689\",\"name\":\"ext_user\",\"type\":\"OBJECT\",\"description\":\"待集成的外部用户对象（json）信息\",\"display_name\":\"外部用户对象\",\"multi_valued\":false,\"value\":\"{{N1866797274749411329.current}}\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, '', 'IDLE', NULL, NULL, '2024-12-11 19:03:42', '2024-12-11 18:49:17', 'admin', '2024-12-11 19:03:42', 'admin', 'iam', 31, 1866797274749411329, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1866797486066835458, 'ActionNode', 1866787483507822595, 89, 'complete_user_sync', NULL, 1851884701728423938, NULL, '{\"ui_id\":\"1866797607655936002\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, NULL, 'INVALID', NULL, NULL, NULL, '2024-12-11 18:49:39', 'admin', '2024-12-11 18:50:12', 'admin', 'iam', 31, 0, NULL, '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `next_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `connector_id`, `parent_id`, `proxy_id`, `closed`, `note`) VALUES (1866801097386004481, 'ActionNode', NULL, 85, 'sync_update_org', NULL, 1851884701728423938, NULL, '{\"ui_id\":\"1866801108008112142\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1866801108008112143\",\"name\":\"ext_org\",\"type\":\"OBJECT\",\"description\":\"待集成的外部部门对象（json）信息\",\"display_name\":\"外部部门对象\",\"multi_valued\":false,\"value\":\"{{N1866797141710282754.current}}\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, '', 'IDLE', NULL, NULL, NULL, '2024-12-11 19:04:00', 'admin', '2024-12-11 19:04:13', 'admin', 'iam', 31, 1866797141710282754, NULL, '0', NULL);

-- 增加动作分类
UPDATE acm.acm_app_connector SET app_action_category = '[{"label":"通讯录","value":"contacts"},{"label":"待办","value":"todo"},{"label":"日程","value":"calendar"},{"label":"会议","value":"meeting"},{"label":"消息通知","value":"message"},{"label":"AI","value":"ai"},{"label":"钉钉连接器","value":"connector"},{"label":"数据统计","value":"statistics"}]' WHERE id = 6;
UPDATE `acm`.`acm_app_action` SET `action_type` = 'message' WHERE `id` = 1698543817348730882;
UPDATE `acm`.`acm_app_action` SET `action_type` = 'todo' WHERE `id` = 1698576654068207618;
UPDATE `acm`.`acm_app_action` SET `action_type` = 'todo' WHERE `id` = 1698596926364176386;
UPDATE `acm`.`acm_app_action` SET `action_type` = 'todo' WHERE `id` = 1698602224617377794;
UPDATE `acm`.`acm_app_action` SET `action_type` = 'calendar' WHERE `id` = 1704769344111718402;
UPDATE `acm`.`acm_app_action` SET `action_type` = 'calendar' WHERE `id` = 1704797693420843010;
UPDATE `acm`.`acm_app_action` SET `action_type` = 'calendar' WHERE `id` = 1704805192941252610;
UPDATE `acm`.`acm_app_action` SET `action_type` = 'calendar' WHERE `id` = 1704806400913387521;
UPDATE `acm`.`acm_app_action` SET `action_type` = 'meeting' WHERE `id` = 1704807332266979329;
UPDATE `acm`.`acm_app_action` SET `action_type` = 'meeting' WHERE `id` = 1704808671726350337;
UPDATE `acm`.`acm_app_action` SET `action_type` = 'meeting' WHERE `id` = 1704810982997569537;
UPDATE `acm`.`acm_app_action` SET `action_type` = 'contacts' WHERE `id` = 1704811962581463041;
UPDATE `acm`.`acm_app_action` SET `action_type` = 'statistics' WHERE `id` = 1704812351808679937;
UPDATE `acm`.`acm_app_action` SET `action_type` = 'contacts' WHERE `id` = 1719531500884525057;
UPDATE `acm`.`acm_app_action` SET `action_type` = 'contacts' WHERE `id` = 1721825017014902786;
UPDATE `acm`.`acm_app_action` SET `action_type` = 'todo' WHERE `id` = 63;
UPDATE `acm`.`acm_app_action` SET `action_type` = 'todo' WHERE `id` = 71;
UPDATE `acm`.`acm_app_action` SET `action_type` = 'message' WHERE `id` = 1838116399015870465;
UPDATE `acm`.`acm_app_action` SET `action_type` = 'calendar' WHERE `id` = 1851803386065149954;
UPDATE `acm`.`acm_app_action` SET `action_type` = 'calendar' WHERE `id` = 1851805852127158274;
UPDATE `acm`.`acm_app_action` SET `action_type` = 'contacts' WHERE `id` = 1858697762636509186;
UPDATE `acm`.`acm_app_action` SET `action_type` = 'calendar' WHERE `id` = 69;
UPDATE `acm`.`acm_app_action` SET `action_type` = 'calendar' WHERE `id` = 70;
UPDATE `acm`.`acm_app_action` SET `action_type` = 'ai' WHERE `id` = 60;
UPDATE `acm`.`acm_app_action` SET `action_type` = 'contacts' WHERE `id` = 90;
UPDATE `acm`.`acm_app_action` SET `action_type` = 'contacts' WHERE `id` = 91;
UPDATE `acm`.`acm_app_action` SET `action_type` = 'contacts' WHERE `id` = 111;
UPDATE `acm`.`acm_app_action` SET `action_type` = 'contacts' WHERE `id` = 112;
-- 钉钉连接器
INSERT INTO `acm`.`acm_app_action` (`id`, `action_key`, `is_trigger`, `name`, `description`, `connector_id`, `account_schema_id`, `protocol`, `action_type`, `status`, `config_schema`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `sample_output`, `node_struct`) VALUES (116, '1869224647227318274', 0, '发送连接器事件', '发送连接器事件', 6, 6, 'HTTP', 'connector', 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619266\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619267\",\"update\":true,\"value\":\"https://api.dingtalk.com/v1.0/connector/triggers/data/sync\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619268\",\"update\":true,\"value\":\"application/json; charset=UTF-8\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"同步数据的应用ID：\\n第三方企业应用传应用的appId，\\n企业自建应用传应用agentId。\",\"display_name\":\"应用ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body.appId\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1734493185745\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"示例：[ {\\n    \\\"triggerId\\\" : \\\"必填, String, 钉钉连接器触发器ID\\\",\\n    \\\"customTriggerId\\\" : \\\"非必填, String, 开发者自定义触发器ID\\\",\\n    \\\"jsonData\\\" : \\\"必填, String, 符合数据模型标准的json格式的数据\\\",\\n    \\\"dataGmtCreate\\\" : 必填, Long, 数据创建时间,\\n    \\\"dataGmtModified\\\" : 必填, Long, 数据最后被修改的时间,\\n    \\\"action\\\" : \\\"必填, String, 本次操作的行为,取值：add：增加, delete：删除, update：更新\\\",\\n    \\\"integrationObject\\\" : \\\"非必填, String, 集成元素的唯一标识\\\",\\n    \\\"triggerCondition\\\" : \\\"非必填, String, 触发条件\\\"\\n  } ]\",\"display_name\":\"同步数据\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"body.triggerDataList\",\"page_control\":\"TEXTAREA\",\"required\":true,\"type\":\"OBJECT\",\"ui_id\":\"1734493261346\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"断言该接口的响应结果。例：[{\\\"path\\\":\\\"响应参数名\\\",\\\"op\\\":\\\"运算符\\\",\\\"value\\\":\\\"节点响应结果\\\"},\\\"path\\\":\\\"响应参数名2\\\",\\\"op\\\":\\\"运算符2\\\",\\\"value\\\":\\\"节点响应结果2\\\"]。；运算符示例：\\\"eq\\\"表示等于\\\"neq\\\"表示不等于；\\\"ne\\\"表示不相等；\\\"contains\\\"表示包含\",\"display_name\":\"预期响应\",\"multi_valued\":true,\"mutability\":\"readWrite\",\"name\":\"successCondition\",\"page_control\":\"JSON\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619123\",\"update\":true,\"value\":\"[{\\\"path\\\":\\\"status_code\\\",\\\"op\\\":\\\"eq\\\",\\\"value\\\":\\\"200\\\"},{\\\"path\\\":\\\"body.errcode\\\",\\\"op\\\":\\\"eq\\\",\\\"value\\\":\\\"0\\\"}]\",\"value_type\":\"JS_EXP\"}],\"type\":\"OBJECT\",\"ui_id\":\"1869228693015207938\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2024-12-18 11:34:19', 'admin', '2024-12-18 11:55:10', 'admin', 'iam', NULL, 'SINGLE');
INSERT INTO `acm`.`acm_app_action` (`id`, `action_key`, `is_trigger`, `name`, `description`, `connector_id`, `account_schema_id`, `protocol`, `action_type`, `status`, `config_schema`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `sample_output`, `node_struct`) VALUES (117, '1869343693222420482', 0, '调用钉钉连接器', NULL, 6, 6, 'HTTP', 'connector', 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619266\",\"update\":true,\"value\":\"POST\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"Webhook URL\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619267\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[],\"page_control\":\"HIDDEN\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619268\",\"update\":true,\"value\":\"application/json; charset=UTF-8\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"钉钉连接器，webhook如果打开了消息体加签，需要配置密钥；否侧为空。\",\"display_name\":\"消息体加签密钥\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"header.secret\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1734590875333\",\"update\":true,\"value_type\":\"ADAPTOR\"},{\"description\":\"\",\"display_name\":\"Body参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body\",\"page_control\":\"TEXTAREA\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619271\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1869637335866511361\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2024-12-18 19:27:22', 'admin', '2024-12-19 14:54:12', 'admin', 'iam', NULL, 'SINGLE');