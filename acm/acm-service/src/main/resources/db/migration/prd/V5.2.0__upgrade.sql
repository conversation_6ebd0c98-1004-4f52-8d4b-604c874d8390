INSERT INTO `acm`.`acm_app_connector` (`id`, `name`, `description`, `icon`, `build_type`, `app_domain_category`, `app_action_category`, `app_package`, `deployment_type`, `ver_name`, `ver_number`, `ver_type`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`) VALUES (39, '通讯录同步', NULL, NULL, 'BuiltIn', 'DS_AREA', NULL, '1881898965186723842', 'SaasApp', '1', 1, 'RELEASE', '2025-01-22 10:57:32', 'admin', '2025-02-20 16:35:41', 'admin', 'iam');

-- 通讯录同步流化动作
INSERT INTO `acm`.`acm_app_action` (`id`, `action_key`, `is_trigger`, `name`, `description`, `connector_id`, `account_schema_id`, `protocol`, `action_type`, `status`, `config_schema`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `sample_output`, `node_struct`) VALUES (151, '1887778362112516090', 1, '启动通讯录同步', '根据接收到的参数，实时启动通讯录集成流程', 39, NULL, 'USER_PUSH', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"异步：webhook接收到数据后，将直接返回接收成功的结果以及流的信息。 同步：Webhook接收到数据后，将等待连接流执行完成并将最后的执行结果返回给调用方（最长等待时间为10s，超时将返回为空）。请查看接口文档获得更多详细信息\",\"display_name\":\"连接流执行方式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"type\",\"option_values\":[\"同步\",\"异步\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value\":\"异步\",\"value_type\":\"FIX_VALUE\"}],\"type\":\"OBJECT\",\"ui_id\":\"1887778854628663297\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2025-02-07 16:20:09', 'admin', '2025-02-07 17:50:32', 'admin', 'iam', NULL, 'SINGLE');
INSERT INTO `acm`.`acm_app_action` (`id`, `action_key`, `is_trigger`, `name`, `description`, `connector_id`, `account_schema_id`, `protocol`, `action_type`, `status`, `config_schema`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `sample_output`, `node_struct`) VALUES (152, '1882032927489998852', 0, '遍历部门数据', '依照部门层级结构，逐一遍历各层级部门', 39, NULL, 'LOOP_DIGITAL_ORG', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"填写需要同步到下游真实存在的部门ID，如果不填写则使用创建连接器的值\",\"display_name\":\"下游根部门ID\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"thirdRootId\",\"option_values\":[],\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1890231663756222466\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2025-01-22 19:49:51', 'admin', '2025-02-14 10:48:42', 'admin', 'iam', NULL, 'LOOP');
INSERT INTO `acm`.`acm_app_action` (`id`, `action_key`, `is_trigger`, `name`, `description`, `connector_id`, `account_schema_id`, `protocol`, `action_type`, `status`, `config_schema`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `sample_output`, `node_struct`) VALUES (155, '1881899198532632579', 0, '遍历用户数据', '依照部门层级结构，逐一遍历各层级部门的直属用户', 39, NULL, 'LOOP_DIGITAL_USER', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[],\"type\":\"OBJECT\",\"ui_id\":\"1882614466460438529\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2025-01-22 10:58:28', 'admin', '2025-02-07 17:50:45', 'admin', 'iam', NULL, 'LOOP');
INSERT INTO `acm`.`acm_app_action` (`id`, `action_key`, `is_trigger`, `name`, `description`, `connector_id`, `account_schema_id`, `protocol`, `action_type`, `status`, `config_schema`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `sample_output`, `node_struct`) VALUES (158, '1887801420277784579', 0, '遍历待删除用户数据', '遍历符合删除阈值控制的所有用户数据', 39, NULL, 'LOOP_DELETE_USER', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[],\"type\":\"OBJECT\",\"ui_id\":\"1887801487747358722\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2025-02-07 17:51:47', 'admin', '2025-02-07 17:52:03', 'admin', 'iam', NULL, 'LOOP');
INSERT INTO `acm`.`acm_app_action` (`id`, `action_key`, `is_trigger`, `name`, `description`, `connector_id`, `account_schema_id`, `protocol`, `action_type`, `status`, `config_schema`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `sample_output`, `node_struct`) VALUES (159, '1887799845358575619', 0, '删除第三方用户信息', '删除三方用户数据', 39, NULL, 'DELETE_USER', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"请选择创建的请求类型\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619266\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619267\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619268\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"路径传惨使用该选项。使用 {} 标识地址参数\",\"display_name\":\"Path参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1738921709264\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"header\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619269\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Query参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619270\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"按照三方接口文档提交即可\",\"display_name\":\"Body参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body\",\"page_control\":\"TEXTAREA\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619271\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"请求返回的类型。默认是JSON 如果返回字符串需要修改\",\"display_name\":\"返回类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"responseType\",\"option_values\":[\"JSON\",\"STRING\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1739868613926\",\"update\":true,\"value\":\"JSON\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"用于判断请求是否成功。一般情况为 return response.body.code == 0; 根据实际情况编写\",\"display_name\":\"请求成功判断\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"successCondition\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619123\",\"update\":true,\"value\":\"\",\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1892493229843193858\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2025-02-07 17:45:31', 'admin', '2025-02-20 16:35:21', 'admin', 'iam', NULL, 'SINGLE');
INSERT INTO `acm`.`acm_app_action` (`id`, `action_key`, `is_trigger`, `name`, `description`, `connector_id`, `account_schema_id`, `protocol`, `action_type`, `status`, `config_schema`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `sample_output`, `node_struct`) VALUES (160, '1887799845358575620', 0, '删除第三方用户信息（内置鉴权）', '删除三方用户数据', 39, 35, 'DELETE_USER', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"请选择创建的请求类型\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619266\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619267\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619268\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"路径传惨使用该选项。使用 {} 标识地址参数\",\"display_name\":\"Path参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1738921709264\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"header\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619269\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Query参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619270\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"按照三方接口文档提交即可\",\"display_name\":\"Body参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body\",\"page_control\":\"TEXTAREA\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619271\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"请求返回的类型。默认是JSON 如果返回字符串需要修改\",\"display_name\":\"返回类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"responseType\",\"option_values\":[\"JSON\",\"STRING\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1739868576176\",\"update\":true,\"value\":\"JSON\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"用于判断请求是否成功。一般情况为 return response.body.code == 0; 根据实际情况编写\",\"display_name\":\"请求成功判断\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"successCondition\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619123\",\"update\":true,\"value\":\"\",\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1892493306687037441\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2025-02-07 17:45:31', 'admin', '2025-02-20 16:35:40', 'admin', 'iam', NULL, 'SINGLE');
INSERT INTO `acm`.`acm_app_action` (`id`, `action_key`, `is_trigger`, `name`, `description`, `connector_id`, `account_schema_id`, `protocol`, `action_type`, `status`, `config_schema`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `sample_output`, `node_struct`) VALUES (161, '1887801760695885827', 0, '遍历待删除部门数据', '遍历符合删除阈值控制的所有部门数据', 39, NULL, 'LOOP_DELETE_ORG', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[],\"type\":\"OBJECT\",\"ui_id\":\"1887802026677673986\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2025-02-07 17:53:08', 'admin', '2025-02-07 17:54:11', 'admin', 'iam', NULL, 'LOOP');
INSERT INTO `acm`.`acm_app_action` (`id`, `action_key`, `is_trigger`, `name`, `description`, `connector_id`, `account_schema_id`, `protocol`, `action_type`, `status`, `config_schema`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `sample_output`, `node_struct`) VALUES (162, '1887799285637095430', 0, '删除第三方部门信息', '删除三方部门数据', 39, NULL, 'DELETE_ORG', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"请选择请求类型\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619266\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619267\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619268\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"路径传惨使用该选项。使用 {} 标识地址参数\",\"display_name\":\"Path参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1738921774703\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"header\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619269\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Query参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619270\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"按照三方接口文档需要格式提交即可\",\"display_name\":\"Body参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body\",\"page_control\":\"TEXTAREA\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619271\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"请求返回的类型。默认是JSON 如果返回字符串需要修改\",\"display_name\":\"返回类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"responseType\",\"option_values\":[\"JSON\",\"STRING\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1739868515827\",\"update\":true,\"value\":\"JSON\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"用于判断请求是否成功。一般情况为 return response.body.code == 0; 根据实际情况编写\",\"display_name\":\"请求成功判断\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"successCondition\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619123\",\"update\":true,\"value\":\"\",\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1892492654116249601\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2025-02-07 17:43:18', 'admin', '2025-02-20 16:34:34', 'admin', 'iam', NULL, 'SINGLE');
INSERT INTO `acm`.`acm_app_action` (`id`, `action_key`, `is_trigger`, `name`, `description`, `connector_id`, `account_schema_id`, `protocol`, `action_type`, `status`, `config_schema`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `sample_output`, `node_struct`) VALUES (163, '1887799285637095431', 0, '删除第三方部门信息（内置鉴权）', '删除三方部门数据', 39, 35, 'DELETE_ORG', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"请选择创建的请求类型\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619266\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"endpoint\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619267\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619268\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"路径传惨使用该选项。使用 {} 标识地址参数\",\"display_name\":\"Path参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1738921774703\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"header\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619269\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Query参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619270\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"按照三方接口文档提交即可\",\"display_name\":\"Body参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body\",\"page_control\":\"TEXTAREA\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619271\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"请求返回的类型。默认是JSON 如果返回字符串需要修改\",\"display_name\":\"返回类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"responseType\",\"option_values\":[\"JSON\",\"STRING\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1739868390755\",\"update\":true,\"value\":\"JSON\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"用于判断请求是否成功。一般情况为 return response.body.code == 0; 根据实际情况编写\",\"display_name\":\"请求成功判断\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"successCondition\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619123\",\"update\":true,\"value\":\"\",\"value_type\":\"ADAPTOR\"}],\"type\":\"OBJECT\",\"ui_id\":\"1892493144006762498\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2025-02-07 17:43:18', 'admin', '2025-02-20 16:35:01', 'admin', 'iam', NULL, 'SINGLE');
INSERT INTO `acm`.`acm_app_action` (`id`, `action_key`, `is_trigger`, `name`, `description`, `connector_id`, `account_schema_id`, `protocol`, `action_type`, `status`, `config_schema`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `sample_output`, `node_struct`) VALUES (164, '1888176546500485123', 0, '完成通讯录同步', '完成通讯录同步，删除缓存、锁等内容', 39, NULL, 'COMPLETE_USER_PUSH', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[],\"type\":\"OBJECT\",\"ui_id\":\"1888176624174800897\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2025-02-08 18:42:24', 'admin', '2025-02-08 18:42:42', 'admin', 'iam', NULL, 'SINGLE');

INSERT INTO `acm`.`acm_app_action` (`id`, `action_key`, `is_trigger`, `name`, `description`, `connector_id`, `account_schema_id`, `protocol`, `action_type`, `status`, `config_schema`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `sample_output`, `node_struct`) VALUES (153, '1882311767479209989', 0, '创建或更新第三方部门信息', '用于三方系统创建和更新部门接口不相同的情况', 39, NULL, 'PUSH_ORG', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"请选择创建的请求类型\",\"display_name\":\"创建请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"createMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619266\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"创建的地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"createPath\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619267\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"更新的请求有类型和创建类型不一致时必填\",\"display_name\":\"更新请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"updateMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1737612672096\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"更新地址和创建地址不一致时必填\",\"display_name\":\"更新的地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"updatePath\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1737546450938\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619268\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"路径传惨使用该选项。使用 {} 标识地址参数\",\"display_name\":\"Path参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1739864096037\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"header\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619269\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Query参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619270\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"发起请求时的参数数据结构如下：\\n{\\n    \\\"k1\\\": \\\"v1\\\",\\n    \\\"k2\\\": \\\"v2\\\"\\n}\\n根据实际情况修改\\n提交时的数据结构：确保提交的数据结构与上述示例完全一致。\",\"display_name\":\"请求体\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body\",\"page_control\":\"TEXTAREA\",\"required\":true,\"type\":\"OBJECT\",\"ui_id\":\"1737546057702\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"与请求体不一致时配置\\n发起请求时的参数数据结构如下：\\n{\\n    \\\"k1\\\": \\\"v1\\\",\\n    \\\"k2\\\": \\\"v2\\\"\\n}\\n根据实际情况修改\\n提交时的数据结构：确保提交的数据结构与上述示例完全一致。\",\"display_name\":\"更新请求体\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"updateBody\",\"page_control\":\"TEXTAREA\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1741261577807\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"请求返回的类型。默认是JSON 如果返回字符串需要修改\",\"display_name\":\"返回类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"responseType\",\"option_values\":[\"JSON\",\"STRING\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1739868315362\",\"update\":true,\"value\":\"JSON\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"此输入框用于填写解析ID的表达式，帮助集成平台与第三方系统之间建立数据关联\\n 1、选择前置节点：如果前置节点已获取，请直接选择。\\n 2、编写表达式：根据返回结果编写解析ID的表达式。例如，若需从响应体中提取id字段，则应填写 return response.body.data.id;\",\"display_name\":\"解析Id的表达式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"idExpress\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1737545743752\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"用于在更新时判断部门是否存在，并决定是否创建新部门 \\n编写表达式：根据部门不存在的结果编写表达式，以判断是否需要创建新部门。 \\n示例：如果部门不存在则创建新部门，可以编写表达式 return response.body.code == 1002;\",\"display_name\":\"用户不存在检查\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"createExpress\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1737545820983\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"用于在创建时如果部门已经存在是否更新部门 \\n编写表达式：根据部门存在的结果编写表达式，以判断是否需要更新新部门。 \\n示例：如果部门存在则更新部门，可以编写表达式 return response.body.code == 1002;\",\"display_name\":\"部门存在检查\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"updateExpress\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1737545909620\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"用于判断请求是否成功。一般情况为 return response.body.code == 0; 根据实际情况编写\",\"display_name\":\"判断请求成功表达式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"successCondition\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"FIX_VALUE\"}],\"type\":\"OBJECT\",\"ui_id\":\"1897615211895013378\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2025-01-23 14:17:52', 'admin', '2025-03-06 19:48:17', 'admin', 'iam', NULL, 'SINGLE');
INSERT INTO `acm`.`acm_app_action` (`id`, `action_key`, `is_trigger`, `name`, `description`, `connector_id`, `account_schema_id`, `protocol`, `action_type`, `status`, `config_schema`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `sample_output`, `node_struct`) VALUES (154, '1882311767479209990', 0, '创建或更新第三方部门信息（内置鉴权）', '用于三方系统创建和更新部门接口不相同的情况', 39, 35, 'PUSH_ORG', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"请选择创建的请求类型\",\"display_name\":\"创建请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"createMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619266\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"创建的地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"createPath\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619267\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"更新的请求有类型和创建类型不一致时必填\",\"display_name\":\"更新请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"updateMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1737612672096\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"更新地址和创建地址不一致时必填\",\"display_name\":\"更新的地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"updatePath\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1737546450938\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619268\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"路径传惨使用该选项。使用 {} 标识地址参数\",\"display_name\":\"Path参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1739864070929\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"header\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619269\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Query参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619270\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"发起请求时的参数数据结构如下：\\n{\\n    \\\"k1\\\": \\\"v1\\\",\\n    \\\"k2\\\": \\\"v2\\\"\\n}\\n根据实际情况修改\\n提交时的数据结构：确保提交的数据结构与上述示例完全一致。\",\"display_name\":\"请求体\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body\",\"page_control\":\"TEXTAREA\",\"required\":true,\"type\":\"OBJECT\",\"ui_id\":\"1737546057702\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"与请求体不一致时配置\\n发起请求时的参数数据结构如下：\\n{\\n    \\\"k1\\\": \\\"v1\\\",\\n    \\\"k2\\\": \\\"v2\\\"\\n}\\n根据实际情况修改\\n提交时的数据结构：确保提交的数据结构与上述示例完全一致。\",\"display_name\":\"更新请求体\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"updateBody\",\"page_control\":\"TEXTAREA\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1741261717093\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"请求返回的类型。默认是JSON 如果返回字符串需要修改\",\"display_name\":\"返回类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"responseType\",\"option_values\":[\"JSON\",\"STRING\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1739868211408\",\"update\":true,\"value\":\"JSON\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"此输入框用于填写解析Id的表达式，帮助集成平台与第三方系统之间建立数据关联\\n1、选择前置节点：如果前置节点已获取，请直接选择。\\n2、编写表达式：根据返回结果编写解析ID的表达式。例如，若需从响应体中提取id字段，则应填写 return response.body.data.id;\",\"display_name\":\"解析Id的表达式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"idExpress\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1737545743752\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"用于在更新时判断部门是否存在，并决定是否创建新部门 \\n编写表达式：根据部门不存在的结果编写表达式，以判断是否需要创建新部门。 \\n示例：如果部门不存在则创建新部门，可以编写表达式 return response.body.code == 1002;\",\"display_name\":\"部门不存在检查\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"createExpress\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1737545820983\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"用于在创建时如果部门已经存在是否更新部门 \\n编写表达式：根据部门存在的结果编写表达式，以判断是否需要更新新部门。 \\n示例：如果部门存在则更新部门，可以编写表达式 return response.body.code == 1002;\",\"display_name\":\"部门存在检查\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"updateExpress\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1737545909620\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"用于判断请求是否成功。一般情况为 return response.body.code == 0; 根据实际情况编写\",\"display_name\":\"判断请求成功表达式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"successCondition\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"FIX_VALUE\"}],\"type\":\"OBJECT\",\"ui_id\":\"1897615436512575489\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2025-01-23 14:17:52', 'admin', '2025-03-06 19:49:20', 'admin', 'iam', NULL, 'SINGLE');

INSERT INTO `acm`.`acm_app_action` (`id`, `action_key`, `is_trigger`, `name`, `description`, `connector_id`, `account_schema_id`, `protocol`, `action_type`, `status`, `config_schema`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `sample_output`, `node_struct`) VALUES (156, '1882031726140669955', 0, '创建或更新第三方用户信息', '用于三方系统创建和更新用户接口不相同的情况', 39, NULL, 'PUSH_USER', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"请选择创建的请求类型\",\"display_name\":\"创建请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"createMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619266\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"创建的地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"createPath\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619267\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"更新的请求有类型和创建类型不一致时必填\",\"display_name\":\"更新请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"updateMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1737612672096\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"更新地址和创建地址不一致时必填\",\"display_name\":\"更新的地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"updatePath\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1737546450938\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619268\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"路径传惨使用该选项。使用 {} 标识地址参数\",\"display_name\":\"Path参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1739864036452\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"header\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619269\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Query参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619270\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"发起请求时的参数数据结构如下：\\n{\\n    \\\"k1\\\": \\\"v1\\\",\\n    \\\"k2\\\": \\\"v2\\\"\\n}\\n根据实际情况修改\\n提交时的数据结构：确保提交的数据结构与上述示例完全一致。\",\"display_name\":\"请求体\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body\",\"page_control\":\"TEXTAREA\",\"required\":true,\"type\":\"OBJECT\",\"ui_id\":\"1737546057702\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"与更新体不一致时配置\\n发起请求时的参数数据结构如下：\\n{\\n    \\\"k1\\\": \\\"v1\\\",\\n    \\\"k2\\\": \\\"v2\\\"\\n}\\n根据实际情况修改\\n提交时的数据结构：确保提交的数据结构与上述示例完全一致。\",\"display_name\":\"更新请求体\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"updateBody\",\"page_control\":\"TEXTAREA\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1741261444544\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"请求返回的类型。默认是JSON 如果返回字符串需要修改\",\"display_name\":\"请求返回类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"responseType\",\"option_values\":[\"JSON\",\"STRING\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1739868147024\",\"update\":true,\"value\":\"JSON\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"此输入框用于填写解析ID的表达式，帮助集成平台与第三方系统之间建立数据关联 \\n1、选择前置节点：如果前置节点已获取，请直接选择。\\n2、编写表达式：根据返回结果编写解析ID的表达式。例如，若需从响应体中提取id字段，则应填写 return response.body.data.id;\",\"display_name\":\"解析Id的表达式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"idExpress\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1737545743752\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"此输入框用于填写解析unionid的表达式，帮助集成平台与第三方系统之间建立数据关联 \\n1、选择前置节点：如果前置节点已获取，请直接选择。 \\n2、编写表达式：根据返回结果编写解析openid的表达式。例如，若需从响应体中提取id字段，则应填写 return response.body.data.unionid;\",\"display_name\":\"解析UnionId的表达式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"unionIdExpress\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1738840918861\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"此输入框用于填写解析openid的表达式，帮助集成平台与第三方系统之间建立数据关联 \\n1、选择前置节点：如果前置节点已获取，请直接选择。 \\n2、编写表达式：根据返回结果编写解析openid的表达式。例如，若需从响应体中提取id字段，则应填写 return response.body.data.openid;\",\"display_name\":\"解析OpenId的表达式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"openIdExpress\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1738840909114\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"用于在更新时判断用户是否存在，并决定是否创建新用户 \\n编写表达式：根据用户不存在的结果编写表达式，以判断是否需要创建新用户。 \\n示例：如果用户不存在则创建新用户，可以编写表达式 return response.body.code == 1002;\",\"display_name\":\"用户不存在检查\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"createExpress\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1737545820983\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"用于在创建时如果用户已经存在是否更新用户 \\n编写表达式：根据用户存在的结果编写表达式，以判断是否需要更新新用户。 示例：如果用户存在则更新用户，可以编写表达式 return response.body.code == 1002;\",\"display_name\":\"用户存在检查\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"updateExpress\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1737545909620\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"用于判断请求是否成功。一般情况为 return response.body.code == 0; 根据实际情况编写\",\"display_name\":\"判断请求成功表达式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"successCondition\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"FIX_VALUE\"}],\"type\":\"OBJECT\",\"ui_id\":\"1897614541414547457\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2025-01-22 19:45:05', 'admin', '2025-03-06 19:45:53', 'admin', 'iam', NULL, 'SINGLE');
INSERT INTO `acm`.`acm_app_action` (`id`, `action_key`, `is_trigger`, `name`, `description`, `connector_id`, `account_schema_id`, `protocol`, `action_type`, `status`, `config_schema`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `sample_output`, `node_struct`) VALUES (157, '1882031726140669950', 0, '创建或更新第三方用户信息（内置鉴权）', '用于三方系统创建和更新用户接口不相同的情况', 39, 35, 'PUSH_USER', NULL, 1, '{\"display_name\":\"root\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"page_control\":\"FIXED_OBJ\",\"required\":false,\"sub_params\":[{\"description\":\"请选择创建的请求类型\",\"display_name\":\"创建请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"createMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619266\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"请填写完整的接口地址\",\"display_name\":\"创建的地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"createPath\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619267\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"更新的请求有类型和创建类型不一致时必填\",\"display_name\":\"更新请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"updateMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1737612672096\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"更新地址和创建地址不一致时必填\",\"display_name\":\"更新的地址\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"updatePath\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1737546450938\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"mutability\":\"readonly\",\"name\":\"contentType\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1699309157332619268\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"路径传惨使用该选项。使用 {} 标识地址参数\",\"display_name\":\"Path参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"path\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1739863993469\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"header\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619269\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"\",\"display_name\":\"Query参数\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"query\",\"page_control\":\"EXTEND_OBJ\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1699309157332619270\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"发起请求时的参数数据结构如下：\\n{\\n    \\\"k1\\\": \\\"v1\\\",\\n    \\\"k2\\\": \\\"v2\\\"\\n}\\n根据实际情况修改。\\n提交时的数据结构：确保提交的数据结构与上述示例完全一致。\",\"display_name\":\"请求体\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"body\",\"page_control\":\"TEXTAREA\",\"required\":true,\"type\":\"OBJECT\",\"ui_id\":\"1737546057702\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"与请求体数据不一致时使用\\n发起请求时的参数数据结构如下：\\n{\\n    \\\"k1\\\": \\\"v1\\\",\\n    \\\"k2\\\": \\\"v2\\\"\\n}\\n根据实际情况修改。\\n提交时的数据结构：确保提交的数据结构与上述示例完全一致。\",\"display_name\":\"更新请求体\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"updateBody\",\"page_control\":\"TEXTAREA\",\"required\":false,\"type\":\"OBJECT\",\"ui_id\":\"1741261237792\",\"update\":true,\"value_type\":\"JS_EXP\"},{\"description\":\"请求返回的类型。默认是JSON 如果返回字符串需要修改\",\"display_name\":\"返回类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"responseType\",\"option_values\":[\"JSON\",\"STRING\"],\"page_control\":\"SELECT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1739868095014\",\"update\":true,\"value\":\"JSON\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"此输入框用于填写解析ID的表达式，帮助集成平台与第三方系统之间建立数据关联\\n1、选择前置节点：如果前置节点已获取，请直接选择。\\n2、编写表达式：根据返回结果编写解析ID的表达式。例如，若需从响应体中提取id字段，则应填写 return response.body.data.id;\",\"display_name\":\"解析Id的表达式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"idExpress\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1737545743752\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"此输入框用于填写解析unionid的表达式，帮助集成平台与第三方系统之间建立数据关联\\n1、选择前置节点：如果前置节点已获取，请直接选择。\\n2、编写表达式：根据返回结果编写解析openid的表达式。例如，若需从响应体中提取id字段，则应填写 return response.body.data.unionid;\",\"display_name\":\"解析UnionId的表达式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"unionIdExpress\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1738840918861\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"此输入框用于填写解析openid的表达式，帮助集成平台与第三方系统之间建立数据关联\\n1、选择前置节点：如果前置节点已获取，请直接选择。\\n2、编写表达式：根据返回结果编写解析openid的表达式。例如，若需从响应体中提取id字段，则应填写 return response.body.data.openid;\",\"display_name\":\"解析OpenId的表达式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"openIdExpress\",\"page_control\":\"TEXT\",\"required\":false,\"type\":\"STRING\",\"ui_id\":\"1738840909114\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"用于在更新时判断用户是否存在，并决定是否创建新用户\\n编写表达式：根据用户不存在的结果编写表达式，以判断是否需要创建新用户。\\n示例：例如，如果用户不存在则创建新用户，可以编写表达式 return response.body.code == 1002;\\n\",\"display_name\":\"用户不存在检查\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"createExpress\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1737545820983\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"用于在创建时如果用户已经存在是否更新用户\\n编写表达式：根据用户存在的结果编写表达式，以判断是否需要更新新用户。\\n示例：例如，如果用户存在则更新用户，可以编写表达式 return response.body.code == 1002;\\n\",\"display_name\":\"用户存在检查\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"updateExpress\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"1737545909620\",\"update\":true,\"value_type\":\"FIX_VALUE\"},{\"description\":\"用于判断请求是否成功。一般情况为 return response.body.code == 0;\\n根据实际情况编写\",\"display_name\":\"判断请求成功表达式\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"successCondition\",\"page_control\":\"TEXT\",\"required\":true,\"type\":\"STRING\",\"ui_id\":\"*************\",\"update\":true,\"value_type\":\"FIX_VALUE\"}],\"type\":\"OBJECT\",\"ui_id\":\"1897614072701075458\",\"update\":true,\"value_type\":\"JS_EXP\"}', '2025-01-22 19:45:05', 'admin', '2025-03-06 19:45:44', 'admin', 'iam', NULL, 'SINGLE');

-- 通讯录同步流化模板
INSERT INTO `acm`.`acm_flow_instance` (`id`, `name`, `description`, `flow_type`, `status`, `op_status`, `error_msg`, `run_start_time`, `run_end_time`, `api_key`, `category`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `template`, `is_deleted`) VALUES (1892486974506704898, 'Http通讯录同步', NULL, NULL, 'IDLE', 'ONLINE', NULL, NULL, NULL, 'adf236cb203345088478543ac4e04087', '通讯录同步', '2025-02-20 16:10:30', 'admin', '2025-02-20 16:41:22', 'admin', 'iam', 1, 0);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1892486974661894145, 'StartEventNode', 0, 1892486974787723266, 39, 151, '1887778362112516090', NULL, 1892486974506704898, NULL, '{\"ui_id\":\"1887778854628663297\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"*************\",\"name\":\"type\",\"type\":\"STRING\",\"description\":\"异步：webhook接收到数据后，将直接返回接收成功的结果以及流的信息。 同步：Webhook接收到数据后，将等待连接流执行完成并将最后的执行结果返回给调用方（最长等待时间为10s，超时将返回为空）。请查看接口文档获得更多详细信息\",\"display_name\":\"连接流执行方式\",\"multi_valued\":false,\"value\":\"异步\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"同步\",\"异步\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, NULL, 'SUCCESS', NULL, NULL, NULL, NULL, '2025-02-20 16:10:30', 'admin', '2025-02-20 16:10:41', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1892486974787723266, 'ActionNode', 0, 1892487796724506625, 39, 152, '1882032927489998852', NULL, 1892486974506704898, NULL, '{\"ui_id\":\"1890231663756222466\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"*************\",\"name\":\"thirdRootId\",\"type\":\"STRING\",\"description\":\"填写需要同步到下游真实存在的部门ID，如果不填写则使用创建连接器的值\",\"display_name\":\"下游根部门ID\",\"multi_valued\":false,\"value\":\"\",\"update\":true,\"required\":false,\"page_control\":\"TEXT\",\"option_values\":[],\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, NULL, 'SUCCESS', NULL, NULL, NULL, NULL, '2025-02-20 16:10:30', 'admin', '2025-02-20 16:10:57', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1892486974984855554, 'EndNode', 0, NULL, NULL, NULL, NULL, NULL, 1892486974506704898, NULL, NULL, NULL, NULL, 'SUCCESS', NULL, NULL, NULL, NULL, '2025-02-20 16:10:30', 'admin', '2025-02-20 16:10:30', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1892487796724506625, 'ActionNode', 0, 1892490769701707778, 39, 155, '1881899198532632579', NULL, 1892486974506704898, NULL, '{\"ui_id\":\"1882614466460438529\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, NULL, 'SUCCESS', NULL, NULL, NULL, NULL, '2025-02-20 16:13:46', 'admin', '2025-02-20 16:13:54', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1892490769701707778, 'ActionNode', 0, 1892493486511792130, 39, 158, '1887801420277784579', NULL, 1892486974506704898, NULL, '{\"ui_id\":\"1887801487747358722\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, NULL, 'SUCCESS', NULL, NULL, NULL, NULL, '2025-02-20 16:25:35', 'admin', '2025-02-20 16:25:50', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1892490849338957826, 'ActionNode', 1892490769701707778, NULL, 39, 159, '1887799845358575619', NULL, 1892486974506704898, NULL, '{\"ui_id\":\"1892493229843193858\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1699309157332619266\",\"name\":\"httpMethod\",\"type\":\"STRING\",\"description\":\"请选择创建的请求类型\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"value\":\"POST\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1699309157332619267\",\"name\":\"endpoint\",\"type\":\"STRING\",\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"value\":\"https://ip:port/path\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1699309157332619268\",\"name\":\"contentType\",\"type\":\"STRING\",\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"value\":\"application/json; charset=UTF-8\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readonly\"},{\"ui_id\":\"1738921709264\",\"name\":\"path\",\"type\":\"OBJECT\",\"description\":\"路径传惨使用该选项。使用 {} 标识地址参数\",\"display_name\":\"Path参数\",\"multi_valued\":false,\"value\":{},\"update\":true,\"required\":false,\"page_control\":\"EXTEND_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1699309157332619269\",\"name\":\"header\",\"type\":\"OBJECT\",\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"value\":{},\"update\":true,\"required\":false,\"page_control\":\"EXTEND_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1699309157332619270\",\"name\":\"query\",\"type\":\"OBJECT\",\"description\":\"\",\"display_name\":\"Query参数\",\"multi_valued\":false,\"value\":{},\"update\":true,\"required\":false,\"page_control\":\"EXTEND_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1699309157332619271\",\"name\":\"body\",\"type\":\"OBJECT\",\"description\":\"按照三方接口文档提交即可\",\"display_name\":\"Body参数\",\"multi_valued\":false,\"value\":\"\",\"update\":true,\"required\":false,\"page_control\":\"TEXTAREA\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1739868613926\",\"name\":\"responseType\",\"type\":\"STRING\",\"description\":\"请求返回的类型。默认是JSON 如果返回字符串需要修改\",\"display_name\":\"返回类型\",\"multi_valued\":false,\"value\":\"JSON\",\"update\":true,\"required\":false,\"page_control\":\"SELECT\",\"option_values\":[\"JSON\",\"STRING\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1699309157332619123\",\"name\":\"successCondition\",\"type\":\"STRING\",\"description\":\"用于判断请求是否成功。一般情况为 return response.body.code == 0; 根据实际情况编写\",\"display_name\":\"删除成功判断\",\"multi_valued\":false,\"value\":\"return response.body.code == 0;\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, NULL, 'SUCCESS', NULL, NULL, NULL, NULL, '2025-02-20 16:25:54', 'admin', '2025-02-20 16:36:19', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1892493486511792130, 'ActionNode', 0, 1892493750388039681, 39, 161, '1887801760695885827', NULL, 1892486974506704898, NULL, '{\"ui_id\":\"1887802026677673986\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, NULL, 'SUCCESS', NULL, NULL, NULL, NULL, '2025-02-20 16:36:23', 'admin', '2025-02-20 16:36:38', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1892493562009264129, 'ActionNode', 1892493486511792130, NULL, 39, 162, '1887799285637095430', NULL, 1892486974506704898, NULL, '{\"ui_id\":\"1892492654116249601\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1699309157332619266\",\"name\":\"httpMethod\",\"type\":\"STRING\",\"description\":\"请选择请求类型\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"value\":\"POST\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1699309157332619267\",\"name\":\"endpoint\",\"type\":\"STRING\",\"description\":\"请填写完整的接口地址\",\"display_name\":\"请求地址\",\"multi_valued\":false,\"value\":\"https://ip:port/path\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1699309157332619268\",\"name\":\"contentType\",\"type\":\"STRING\",\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"value\":\"application/json; charset=UTF-8\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readonly\"},{\"ui_id\":\"1738921774703\",\"name\":\"path\",\"type\":\"OBJECT\",\"description\":\"路径传惨使用该选项。使用 {} 标识地址参数\",\"display_name\":\"Path参数\",\"multi_valued\":false,\"value\":{},\"update\":true,\"required\":false,\"page_control\":\"EXTEND_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1699309157332619269\",\"name\":\"header\",\"type\":\"OBJECT\",\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"value\":{},\"update\":true,\"required\":false,\"page_control\":\"EXTEND_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1699309157332619270\",\"name\":\"query\",\"type\":\"OBJECT\",\"description\":\"\",\"display_name\":\"Query参数\",\"multi_valued\":false,\"value\":{},\"update\":true,\"required\":false,\"page_control\":\"EXTEND_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1699309157332619271\",\"name\":\"body\",\"type\":\"OBJECT\",\"description\":\"按照三方接口文档需要格式提交即可\",\"display_name\":\"Body参数\",\"multi_valued\":false,\"value\":\"\",\"update\":true,\"required\":false,\"page_control\":\"TEXTAREA\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1739868515827\",\"name\":\"responseType\",\"type\":\"STRING\",\"description\":\"请求返回的类型。默认是JSON 如果返回字符串需要修改\",\"display_name\":\"返回类型\",\"multi_valued\":false,\"value\":\"JSON\",\"update\":true,\"required\":false,\"page_control\":\"SELECT\",\"option_values\":[\"JSON\",\"STRING\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1699309157332619123\",\"name\":\"successCondition\",\"type\":\"STRING\",\"description\":\"用于判断请求是否成功。一般情况为 return response.body.code == 0; 根据实际情况编写\",\"display_name\":\"删除成功判断\",\"multi_valued\":false,\"value\":\"return response.body.code == 0;\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"ADAPTOR\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, NULL, 'SUCCESS', NULL, NULL, NULL, NULL, '2025-02-20 16:36:41', 'admin', '2025-02-20 16:37:23', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1892493750388039681, 'ActionNode', 0, 1892486974984855554, 39, 164, '1888176546500485123', NULL, 1892486974506704898, NULL, '{\"ui_id\":\"1888176624174800897\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, NULL, 'SUCCESS', NULL, NULL, NULL, NULL, '2025-02-20 16:37:26', 'admin', '2025-02-20 16:37:32', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1897502635532378114, 'ActionNode', 1892486974787723266, NULL, 39, 153, '1882311767479209989', NULL, 1892486974506704898, NULL, '{\"ui_id\":\"1897279361290248194\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1699309157332619266\",\"name\":\"createMethod\",\"type\":\"STRING\",\"description\":\"请选择创建的请求类型\",\"display_name\":\"创建请求类型\",\"multi_valued\":false,\"value\":\"POST\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1699309157332619267\",\"name\":\"createPath\",\"type\":\"STRING\",\"description\":\"请填写完整的接口地址\",\"display_name\":\"创建的地址\",\"multi_valued\":false,\"value\":\"https://ip:port/path\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1737612672096\",\"name\":\"updateMethod\",\"type\":\"STRING\",\"description\":\"更新的请求有类型和创建类型不一致时必填\",\"display_name\":\"更新请求类型\",\"multi_valued\":false,\"update\":true,\"required\":false,\"page_control\":\"SELECT\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1737546450938\",\"name\":\"updatePath\",\"type\":\"STRING\",\"description\":\"更新地址和创建地址不一致时必填\",\"display_name\":\"更新的地址\",\"multi_valued\":false,\"update\":true,\"required\":false,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1699309157332619268\",\"name\":\"contentType\",\"type\":\"STRING\",\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"value\":\"application/json; charset=UTF-8\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readonly\"},{\"ui_id\":\"1739864096037\",\"name\":\"path\",\"type\":\"OBJECT\",\"description\":\"路径传惨使用该选项。使用 {} 标识地址参数\",\"display_name\":\"Path参数\",\"multi_valued\":false,\"value\":{},\"update\":true,\"required\":false,\"page_control\":\"EXTEND_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1699309157332619269\",\"name\":\"header\",\"type\":\"OBJECT\",\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"value\":{},\"update\":true,\"required\":false,\"page_control\":\"EXTEND_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1699309157332619270\",\"name\":\"query\",\"type\":\"OBJECT\",\"description\":\"\",\"display_name\":\"Query参数\",\"multi_valued\":false,\"value\":{},\"update\":true,\"required\":false,\"page_control\":\"EXTEND_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1737546057702\",\"name\":\"body\",\"type\":\"OBJECT\",\"description\":\"发起请求时的参数数据结构如下：\\n       {\\n        \\\"k1\\\": \\\"v1\\\",\\n        \\\"k2\\\": \\\"v2\\\"\\n      }\\n 可根据实际情况修改。 \\n提交时的数据结构：确保提交的数据结构与上述示例完全一致。\",\"display_name\":\"请求体\",\"multi_valued\":false,\"value\":\"{}\",\"update\":true,\"required\":true,\"page_control\":\"TEXTAREA\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1741181591680\",\"name\":\"updateBody\",\"type\":\"OBJECT\",\"description\":\"更新参数和创建参数不一致的情况\\n发起请求时的参数数据结构如下：\\n       {\\n        \\\"k1\\\": \\\"v1\\\",\\n        \\\"k2\\\": \\\"v2\\\"\\n      }\\n可根据实际情况修改。\\n提交时的数据结构：确保提交的数据结构与上述示例完全一致。\",\"display_name\":\"更新请求体\",\"multi_valued\":false,\"update\":true,\"required\":false,\"page_control\":\"TEXTAREA\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1739868315362\",\"name\":\"responseType\",\"type\":\"STRING\",\"description\":\"请求返回的类型。默认是JSON 如果返回字符串需要修改\",\"display_name\":\"返回类型\",\"multi_valued\":false,\"value\":\"JSON\",\"update\":true,\"required\":false,\"page_control\":\"SELECT\",\"option_values\":[\"JSON\",\"STRING\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1737545743752\",\"name\":\"idExpress\",\"type\":\"STRING\",\"description\":\"此输入框用于填写解析ID的表达式，帮助集成平台与第三方系统之间建立数据关联\\n 1、选择前置节点：如果前置节点已获取，请直接选择。\\n 2、编写表达式：根据返回结果编写解析ID的表达式。例如，若需从响应体中提取id字段，则应填写 return response.body.data.id;\",\"display_name\":\"解析Id的表达式\",\"multi_valued\":false,\"value\":\"return response.body.data.id;\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1737545820983\",\"name\":\"createExpress\",\"type\":\"STRING\",\"description\":\"用于在更新时判断部门是否存在，并决定是否创建新部门 \\n编写表达式：根据部门不存在的结果编写表达式，以判断是否需要创建新部门。 \\n示例：如果部门不存在则创建新部门，可以编写表达式 return response.body.code == 1002;\",\"display_name\":\"部门不存在检查\",\"multi_valued\":false,\"value\":\"return response.body.code == 0;\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1737545909620\",\"name\":\"updateExpress\",\"type\":\"STRING\",\"description\":\"用于在创建时如果部门已经存在是否更新部门 \\n编写表达式：根据部门存在的结果编写表达式，以判断是否需要更新新部门。 \\n示例：如果部门存在则更新部门，可以编写表达式 return response.body.code == 1002;\",\"display_name\":\"部门存在检查\",\"multi_valued\":false,\"value\":\"return response.body.code == 0;\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"*************\",\"name\":\"successCondition\",\"type\":\"STRING\",\"description\":\"用于判断请求是否成功。一般情况为 return response.body.code == 0; 根据实际情况编写\",\"display_name\":\"判断请求成功表达式\",\"multi_valued\":false,\"value\":\"return response.body.code == 0;\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, NULL, 'SUCCESS', NULL, NULL, NULL, NULL, '2025-03-06 12:20:57', 'admin', '2025-03-06 12:21:49', 'admin', 'iam', '0', NULL);
INSERT INTO `acm`.`acm_flow_node` (`id`, `node_type`, `parent_id`, `next_id`, `connector_id`, `action_id`, `action_key`, `account_id`, `flow_id`, `description`, `config`, `input`, `output`, `status`, `error_msg`, `run_start_time`, `run_end_time`, `proxy_id`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`, `closed`, `note`) VALUES (1897502900025188353, 'ActionNode', 1892487796724506625, NULL, 39, 156, '1882031726140669955', NULL, 1892486974506704898, NULL, '{\"ui_id\":\"1897279141248671745\",\"name\":\"root\",\"type\":\"OBJECT\",\"display_name\":\"root\",\"multi_valued\":false,\"sub_params\":[{\"ui_id\":\"1699309157332619266\",\"name\":\"createMethod\",\"type\":\"STRING\",\"description\":\"请选择创建的请求类型\",\"display_name\":\"创建请求类型\",\"multi_valued\":false,\"value\":\"POST\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1699309157332619267\",\"name\":\"createPath\",\"type\":\"STRING\",\"description\":\"请填写完整的接口地址\",\"display_name\":\"创建的地址\",\"multi_valued\":false,\"value\":\"https://ip:port/path\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1737612672096\",\"name\":\"updateMethod\",\"type\":\"STRING\",\"description\":\"更新的请求有类型和创建类型不一致时必填\",\"display_name\":\"更新请求类型\",\"multi_valued\":false,\"update\":true,\"required\":false,\"page_control\":\"SELECT\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1737546450938\",\"name\":\"updatePath\",\"type\":\"STRING\",\"description\":\"更新地址和创建地址不一致时必填\",\"display_name\":\"更新的地址\",\"multi_valued\":false,\"update\":true,\"required\":false,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1699309157332619268\",\"name\":\"contentType\",\"type\":\"STRING\",\"description\":\"\",\"display_name\":\"请求体类型\",\"multi_valued\":false,\"value\":\"application/json; charset=UTF-8\",\"update\":true,\"required\":true,\"page_control\":\"SELECT\",\"option_values\":[\"application/x-www-form-urlencoded\",\"application/json; charset=UTF-8\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readonly\"},{\"ui_id\":\"1739863993469\",\"name\":\"path\",\"type\":\"OBJECT\",\"description\":\"路径传惨使用该选项。使用 {} 标识地址参数\",\"display_name\":\"Path参数\",\"multi_valued\":false,\"value\":{},\"update\":true,\"required\":false,\"page_control\":\"EXTEND_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1699309157332619269\",\"name\":\"header\",\"type\":\"OBJECT\",\"description\":\"\",\"display_name\":\"Header参数\",\"multi_valued\":false,\"value\":{},\"update\":true,\"required\":false,\"page_control\":\"EXTEND_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1699309157332619270\",\"name\":\"query\",\"type\":\"OBJECT\",\"description\":\"\",\"display_name\":\"Query参数\",\"multi_valued\":false,\"value\":{},\"update\":true,\"required\":false,\"page_control\":\"EXTEND_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1737546057702\",\"name\":\"body\",\"type\":\"OBJECT\",\"description\":\"发起请求时的参数数据结构如下：\\n       {\\n        \\\"k1\\\": \\\"v1\\\",\\n        \\\"k2\\\": \\\"v2\\\"\\n      }\\n 可根据实际情况修改。 \\n提交时的数据结构：确保提交的数据结构与上述示例完全一致。\",\"display_name\":\"请求体\",\"multi_valued\":false,\"value\":\"{}\",\"update\":true,\"required\":true,\"page_control\":\"TEXTAREA\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1741181517181\",\"name\":\"updateBody\",\"type\":\"OBJECT\",\"description\":\"更新参数和创建参数不一致的情况\\n发起请求时的参数数据结构如下：\\n       {\\n        \\\"k1\\\": \\\"v1\\\",\\n        \\\"k2\\\": \\\"v2\\\"\\n      }\\n可根据实际情况修改。\\n提交时的数据结构：确保提交的数据结构与上述示例完全一致。\",\"display_name\":\"更新请求体\",\"multi_valued\":false,\"update\":true,\"required\":false,\"page_control\":\"TEXTAREA\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1739868095014\",\"name\":\"responseType\",\"type\":\"STRING\",\"description\":\"请求返回的类型。默认是JSON 如果返回字符串需要修改\",\"display_name\":\"返回类型\",\"multi_valued\":false,\"value\":\"JSON\",\"update\":true,\"required\":false,\"page_control\":\"SELECT\",\"option_values\":[\"JSON\",\"STRING\"],\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1737545743752\",\"name\":\"idExpress\",\"type\":\"STRING\",\"description\":\"此输入框用于填写解析ID的表达式，帮助集成平台与第三方系统之间建立数据关联\\n1、选择前置节点：如果前置节点已获取，请直接选择。\\n2、编写表达式：根据返回结果编写解析ID的表达式。例如，若需从响应体中提取id字段，则应填写 return response.body.data.id;\",\"display_name\":\"解析Id的表达式\",\"multi_valued\":false,\"value\":\"return response.body.data.id;\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1738840918861\",\"name\":\"unionIdExpress\",\"type\":\"STRING\",\"description\":\"此输入框用于填写解析unionid的表达式，帮助集成平台与第三方系统之间建立数据关联\\n1、选择前置节点：如果前置节点已获取，请直接选择。\\n2、编写表达式：根据返回结果编写解析openid的表达式。例如，若需从响应体中提取id字段，则应填写 return response.body.data.unionid;\",\"display_name\":\"解析UnionId的表达式\",\"multi_valued\":false,\"update\":true,\"required\":false,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1738840909114\",\"name\":\"openIdExpress\",\"type\":\"STRING\",\"description\":\"此输入框用于填写解析openid的表达式，帮助集成平台与第三方系统之间建立数据关联\\n1、选择前置节点：如果前置节点已获取，请直接选择。\\n2、编写表达式：根据返回结果编写解析openid的表达式。例如，若需从响应体中提取id字段，则应填写 return response.body.data.openid;\",\"display_name\":\"解析OpenId的表达式\",\"multi_valued\":false,\"update\":true,\"required\":false,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1737545820983\",\"name\":\"createExpress\",\"type\":\"STRING\",\"description\":\"用于在更新时判断用户是否存在，并决定是否创建新用户\\n编写表达式：根据用户不存在的结果编写表达式，以判断是否需要创建新用户。\\n示例：例如，如果用户不存在则创建新用户，可以编写表达式 return response.body.code == 1002;\\n\",\"display_name\":\"用户不存在检查\",\"multi_valued\":false,\"value\":\"return response.body.code == 20;\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"1737545909620\",\"name\":\"updateExpress\",\"type\":\"STRING\",\"description\":\"用于在创建时如果用户已经存在是否更新用户\\n编写表达式：根据用户存在的结果编写表达式，以判断是否需要更新新用户。\\n示例：例如，如果用户存在则更新用户，可以编写表达式 return response.body.code == 1002;\\n\",\"display_name\":\"用户存在检查\",\"multi_valued\":false,\"value\":\"return response.body.code == 10;\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"},{\"ui_id\":\"*************\",\"name\":\"successCondition\",\"type\":\"STRING\",\"description\":\"用于判断请求是否成功。一般情况为 return response.body.code == 0;\\n根据实际情况编写\",\"display_name\":\"判断请求成功表达式\",\"multi_valued\":false,\"value\":\"return response.body.code == 0;\",\"update\":true,\"required\":true,\"page_control\":\"TEXT\",\"value_type\":\"FIX_VALUE\",\"mutability\":\"readWrite\"}],\"update\":true,\"required\":false,\"page_control\":\"FIXED_OBJ\",\"value_type\":\"JS_EXP\",\"mutability\":\"readWrite\"}', NULL, NULL, 'SUCCESS', NULL, NULL, NULL, NULL, '2025-03-06 12:22:00', 'admin', '2025-03-06 12:23:06', 'admin', 'iam', '0', NULL);


INSERT INTO `acm`.`acm_app_account` (`id`, `name`, `description`, `auth_type`, `auth_schema`, `create_time`, `create_by`, `update_time`, `update_by`, `tenant_id`) VALUES (35, ' HTTP 账号', NULL, 'ADAPT', '{\"description\":\"HTTP 账号\",\"displayName\":\"HTTP 账号\",\"multiValued\":false,\"mutability\":\"readWrite\",\"name\":\"root\",\"subParams\":[{\"description\":\"输入获取Token的地址\",\"displayName\":\"Access Token URL\",\"multiValued\":false,\"name\":\"endpoint\",\"type\":\"STRING\",\"required\":true,\"page_control\":\"TEXT\",\"valueType\":\"FIX_VALUE\"},{\"description\":\"\",\"display_name\":\"请求类型\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"httpMethod\",\"option_values\":[\"GET\",\"POST\",\"PUT\",\"PATCH\",\"DELETE\"],\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"请求的query参数：\\n{\\n        \\\"k1\\\":\\\"v1\\\",\\n        \\\"k2\\\": \\\"v2\\\"\\n    }\",\"displayName\":\"Query参数\",\"multiValued\":false,\"name\":\"query\",\"type\":\"OBJECT\",\"required\":false,\"page_control\":\"FIXED_OBJ\",\"valueType\":\"JS_EXP\"},{\"description\":\"请求的Header参数：\\n{\\n        \\\"k1\\\":\\\"v1\\\",\\n        \\\"k2\\\": \\\"v2\\\"\\n    }\",\"displayName\":\"Header参数\",\"multiValued\":false,\"name\":\"header\",\"type\":\"OBJECT\",\"required\":false,\"page_control\":\"FIXED_OBJ\",\"valueType\":\"JS_EXP\"},{\"description\":\"请求的Body参数：\\n{\\n        \\\"k1\\\":\\\"v1\\\",\\n        \\\"k2\\\": \\\"v2\\\"\\n    }\",\"displayName\":\"Body参数\",\"multiValued\":false,\"name\":\"body\",\"type\":\"OBJECT\",\"required\":false,\"page_control\":\"FIXED_OBJ\",\"valueType\":\"JS_EXP\"},{\"description\":\"将获取到的token方法到header还是token\",\"displayName\":\"Token放入header\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"putHeader\",\"option_values\":[\"TRUE\",\"FALSE\"],\"value\":\"TRUE\",\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"token的key\",\"displayName\":\"Token放入Header的Key\",\"multiValued\":false,\"name\":\"key\",\"type\":\"STRING\",\"value\":\"access-token\",\"page_control\":\"TEXT\",\"required\":true,\"valueType\":\"FIX_VALUE\"},{\"description\":\"将获取到的Token通过Query传参\",\"displayName\":\"Token放入query\",\"multi_valued\":false,\"mutability\":\"readWrite\",\"name\":\"putQuery\",\"option_values\":[\"TRUE\",\"FALSE\"],\"value\":\"TRUE\",\"page_control\":\"SELECT\",\"required\":true,\"type\":\"STRING\",\"value_type\":\"FIX_VALUE\"},{\"description\":\"token的key\",\"displayName\":\"Token放入Query的Key\",\"multiValued\":false,\"name\":\"queryKey\",\"type\":\"STRING\",\"value\":\"access-token\",\"page_control\":\"TEXT\",\"required\":true,\"valueType\":\"FIX_VALUE\"},{\"description\":\"从请求的返回信息中获取token的表达式。以return response 开头，后面根据接口文档补充\",\"displayName\":\"Token表达式\",\"multiValued\":false,\"name\":\"tokenExpression\",\"type\":\"STRING\",\"value\":\"return response.data;\",\"required\":true,\"page_control\":\"TEXT\",\"valueType\":\"FIX_VALUE\"},{\"description\":\"从请求的返回信息中获取token的表达式，以return response 开头，后面根据接口文档补充\",\"displayName\":\"Token过期表达式\",\"multiValued\":false,\"name\":\"tokenExpireExpression\",\"type\":\"STRING\",\"value\":\"return response.data;\",\"required\":true,\"page_control\":\"TEXT\",\"valueType\":\"FIX_VALUE\"}],\"type\":\"OBJECT\",\"valueType\":\"JS_EXP\"}', '2024-08-29 20:05:52', NULL, '2024-08-29 20:05:52', NULL, 'iam');

delete from acm_app_connector  where id = 30;
delete from acm_app_action  where connector_id = 30;
