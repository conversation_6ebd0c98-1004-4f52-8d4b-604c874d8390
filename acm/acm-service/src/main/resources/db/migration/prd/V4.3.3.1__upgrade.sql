INSERT INTO acm_app_account(id,name,auth_type,auth_schema,create_time,update_time,tenant_id) values
    (25, 'SQLServer数据库', 'RDBMS_ACCOUNT', '{"description":"SQLServer数据库配置","displayName":"SQLServer数据库配置","multiValued":false,"mutability":"readWrite","name":"root",
"subParams":[
{"description":"","displayName":"SQLServer数据库","multiValued":false,"name":"type","type":"STRING","value":"SQLSERVER","required":true,"page_control":"HIDDEN","valueType":"FIX_VALUE"},
{"description":"数据库地址：IP或域名。","displayName":"数据库地址","multiValued":false,"name":"host","type":"STRING","required":true,"page_control":"TEXT","valueType":"FIX_VALUE"},
{"description":"示例：1433，请填写数据库端口。","displayName":"端口","multiValued":false,"name":"port","type":"NUMBER","required":true,"page_control":"TEXT","valueType":"FIX_VALUE"},
{"description":"填写你想要连接的数据库实例。","displayName":"数据库实例","multiValued":false,"name":"instance","type":"STRING","required":true,"page_control":"TEXT","valueType":"FIX_VALUE"},
{"description":"填写你想要连接的数据库名称。","displayName":"数据库名称","multiValued":false,"name":"dbname","type":"STRING","required":true,"page_control":"TEXT","valueType":"FIX_VALUE"},
{"description":"示例：utf8，数据库编码。","displayName":"数据库编码","multiValued":false,"name":"encode","type":"STRING","value":"utf8","required":true,"page_control":"TEXT","valueType":"FIX_VALUE"},
{"description":"请填写用户名，建议创建一个具有有限权限的数据库用户。","displayName":"用户名","multiValued":false,"name":"username","type":"STRING","required":true,"page_control":"TEXT","valueType":"FIX_VALUE"},
{"description":"请填写密码，建议使用复杂密码。","displayName":"密码","multiValued":false,"name":"password","type":"STRING","required":true,"page_control":"PASSWORD","valueType":"FIX_VALUE"}
],
"type":"OBJECT","valueType":"JS_EXP"}', now(), now(), 'iam');


insert into acm_app_connector(id, name, description, build_type, app_package, ver_name, ver_number, create_time, update_time, tenant_id,icon) values (19,'SQLServer数据库','SQLServer数据库','BuiltIn','sqlserver','1.0',1,now(),now(),'iam',NULL);

insert into acm_app_action(id,action_key,is_trigger,name,description,connector_id,account_schema_id,protocol,config_schema,create_time,update_time,tenant_id) values
    (38,'sql_sqlserver',0,'自定义Sql','自定义Sql操作数据库',19,25,'RDBMS','{"description":"自定义Sql操作数据库","display_name":"自定义Sql","multi_valued":false,"mutability":"readWrite","name":"root","page_control":"FIXED_OBJ","required":true,
"sub_params":[
{"description":"","display_name":"操作类型","multi_valued":false,"mutability":"readWrite","name":"opType","option_values":["INSERT","SELECT","UPDATE","DELETE"],
"page_control":"SELECT","required":true,"type":"STRING","value_type":"FIX_VALUE"},
{"description":"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入","display_name":"Sql语句","multi_valued":false,"mutability":"readWrite","name":"sql","page_control":"TEXTAREA","required":true,"type":"STRING","value_type":"FIX_VALUE"},
{"description":"查询结果最大返回行数，最大返回100条","display_name":"查询结果最大返回行数","multi_valued":false,"mutability":"readWrite","name":"limit","page_control":"TEXT","required":true,"type":"NUMBER","value_type":"FIX_VALUE"},
{"description":"","display_name":"输入参数","multi_valued":false,"mutability":"readWrite","name":"input","page_control":"EXTEND_OBJ","required":false,"type":"OBJECT","value_type":"JS_EXP"}],"type":"OBJECT","value_type":"JS_EXP"}',now(),now(),'iam');


 -- access数据库
INSERT INTO acm_app_account(id,name,auth_type,auth_schema,create_time,update_time,tenant_id) values
    (26, 'Access数据库', 'RDBMS_ACCOUNT', '{"description":"Access数据库配置","displayName":"Access数据库配置","multiValued":false,"mutability":"readWrite","name":"root",
"subParams":[{"description":"","displayName":"Access数据库","multiValued":false,"name":"type","type":"STRING","value":"ACCESS","required":true,"page_control":"HIDDEN","valueType":"FIX_VALUE"},
{"description":"填写你想要连接的数据库文件名。","displayName":"数据库名（包含后缀）","multiValued":false,"name":"fileName","type":"STRING","required":true,"page_control":"TEXT","valueType":"FIX_VALUE"}],
"type":"OBJECT","valueType":"JS_EXP"}', now(), now(), 'iam');

insert into acm_app_connector(id, name, description, build_type, app_package, ver_name, ver_number, create_time, update_time, tenant_id,icon) values (20,'Access数据库','Access数据库','BuiltIn','access','1.0',1,now(),now(),'iam',NULL);

insert into acm_app_action(id,action_key,is_trigger,name,description,connector_id,account_schema_id,protocol,config_schema,create_time,update_time,tenant_id) values
    (39,'sql_access',0,'自定义Sql','自定义Sql操作数据库',20,26,'RDBMS','{"description":"自定义Sql操作数据库","display_name":"自定义Sql","multi_valued":false,"mutability":"readWrite","name":"root","page_control":"FIXED_OBJ","required":true,
"sub_params":[
{"description":"","display_name":"操作类型","multi_valued":false,"mutability":"readWrite","name":"opType","option_values":["INSERT","SELECT","UPDATE","DELETE"],
"page_control":"SELECT","required":true,"type":"STRING","value_type":"FIX_VALUE"},
{"description":"同时支持原生SQL及参数化输入。使用参数化(冒号+参数名)输入，可防止SQL注入","display_name":"Sql语句","multi_valued":false,"mutability":"readWrite","name":"sql","page_control":"TEXTAREA","required":true,"type":"STRING","value_type":"FIX_VALUE"},
{"description":"查询结果最大返回行数，最大返回100条","display_name":"查询结果最大返回行数","multi_valued":false,"mutability":"readWrite","name":"limit","page_control":"TEXT","required":true,"type":"NUMBER","value_type":"FIX_VALUE"},
{"description":"","display_name":"输入参数","multi_valued":false,"mutability":"readWrite","name":"input","page_control":"EXTEND_OBJ","required":false,"type":"OBJECT","value_type":"JS_EXP"}],"type":"OBJECT","value_type":"JS_EXP"}',now(),now(),'iam');

update acm_app_account set auth_schema = '{"description":"Access数据库配置","displayName":"Access数据库配置","multiValued":false,"mutability":"readWrite","name":"root",
"subParams":[{"description":"","displayName":"Access数据库","multiValued":false,"name":"type","type":"STRING","value":"ACCESS","required":true,"page_control":"HIDDEN","valueType":"FIX_VALUE"},
{"description":"填写你想要连接的数据库文件名。","displayName":"数据库名（包含后缀）","multiValued":false,"name":"fileName","type":"STRING","required":true,"page_control":"TEXT","valueType":"FIX_VALUE"},
{"description":"没有密码可不填","displayName":"数据库密码","multiValued":false,"name":"password","type":"STRING","required":false,"page_control":"TEXT","valueType":"FIX_VALUE"}],
"type":"OBJECT","valueType":"JS_EXP"}' where id = 26;

-- ftp应用
insert into acm_app_connector(id, name, description, build_type, app_package, ver_name, ver_number, create_time, update_time, tenant_id,icon) values (21,'FTP应用','FTP应用','BuiltIn','ftp','1.0',1,now(),now(),'iam',NULL);

INSERT INTO acm_app_account(id,name,auth_type,auth_schema,create_time,update_time,tenant_id) values
    (27, 'FTP应用', 'FTP', '{"description":"FTP应用配置","displayName":"FTP应用配置","multiValued":false,"mutability":"readWrite","name":"root",
"subParams":[{"description":"","displayName":"FTP应用","multiValued":false,"name":"type","type":"STRING","value":"FTP","required":true,"page_control":"HIDDEN","valueType":"FIX_VALUE"},
{"description":"服务器地址：IP或域名。","displayName":"服务器地址","multiValued":false,"name":"host","type":"STRING","required":true,"page_control":"TEXT","valueType":"FIX_VALUE"},
{"description":"示例：21，请填写端口。","displayName":"端口","multiValued":false,"name":"port","type":"NUMBER","required":true,"page_control":"TEXT","valueType":"FIX_VALUE"},
{"description":"传输模式,binary(字节码)/ASCII","display_name":"传输模式","multi_valued":false,"mutability":"readWrite","name":"transferMode","option_values":["binary","ASCII"],"value":"binary","page_control":"SELECT","required":true,"type":"STRING","value_type":"FIX_VALUE"},
{"description":"请填写用户名。","displayName":"用户名","multiValued":false,"name":"username","type":"STRING","required":true,"page_control":"TEXT","valueType":"FIX_VALUE"},
{"description":"请填写密码","displayName":"密码","multiValued":false,"name":"password","type":"STRING","required":true,"page_control":"PASSWORD","valueType":"FIX_VALUE"}],
"type":"OBJECT","valueType":"JS_EXP"}', now(), now(), 'iam');


insert into acm_app_action(id,action_key,is_trigger,name,description,connector_id,account_schema_id,protocol,config_schema,create_time,update_time,tenant_id) values
    (40,'file_ftp',0,'FTP应用','FTP应用',21,27,'FTP','{"description":"","display_name":"FTP应用","multi_valued":false,"mutability":"readWrite","name":"root","page_control":"FIXED_OBJ","required":true,
"sub_params":[{"description":"","display_name":"操作类型","multi_valued":false,"mutability":"readWrite","name":"opType","option_values":["DOWNLOAD"],"value":"DOWNLOAD","page_control":"SELECT","required":true,"type":"STRING","value_type":"FIX_VALUE"},
{"description":"下载文件路径","displayName":"下载文件路径","multiValued":false,"name":"remoteFilePath","type":"STRING","required":true,"page_control":"TEXT","valueType":"FIX_VALUE"}],
"type":"OBJECT","value_type":"JS_EXP"}',now(),now(),'iam');




























