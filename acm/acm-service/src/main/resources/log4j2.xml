<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN" monitorInterval="1800">
    <properties>
        <!--日志文件位置-->
        <property name="LOG_HOME">../logs</property>
        <!--日志文件名称-->
        <property name="FILE_NAME">acm-service</property>
    </properties>
    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout pattern="%d %highlight{%-5level %tcode [%X{X-B3-TraceId},%X{X-B3-SpanId},%X{X-B3-ParentSpanId},%X{X-Span-Export}]}{Yellow}  %style{[%-21t]}{Magenta} %style{%-30.-80c{1.}:%-3L}{Cyan} - %m%n" />
        </Console>
        <RollingRandomAccessFile
                name="running-log"
                fileName="${LOG_HOME}/${FILE_NAME}.log"
                filePattern="${LOG_HOME}/${FILE_NAME}-%d{yyyy-MM-dd}-%i.log.gz">
            <PatternLayout pattern="%d %highlight{%-5level %tcode [%X{X-B3-TraceId},%X{X-B3-SpanId},%X{X-B3-ParentSpanId},%X{X-Span-Export}]}{Yellow}  %style{[%-21t]}{Magenta} %style{%-30.-80c{1.}:%-3L}{Cyan} - %m%n" />
            <Policies>
                <TimeBasedTriggeringPolicy />
                <SizeBasedTriggeringPolicy size="100 MB" />
            </Policies>
            <DefaultRolloverStrategy max="50">
                <Delete basePath = "${LOG_HOME}" maxDepth="1">
                    <IfFileName glob="*/${FILE_NAME}-*.log.gz" />
                    <IfLastModified age="7d" />
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>
    </Appenders>
    <Loggers>
        <Root level="info">
            <AppenderRef ref="Console" />
            <AppenderRef ref="running-log" />
        </Root>
        <logger name="org.springframework.security" level="INFO" />
        <logger name="com.cyberscraft.uep" level="INFO" />
        <logger name="RocketmqRemoting" level="ERROR" />
        <logger name="RocketmqClient" level="ERROR" />
        <logger name="RocketmqCommon" level="ERROR" />
    </Loggers>
</Configuration>