# 平台设置配置项更新说明表格

## 密码策略配置

| 配置项名称 | 功能说明 | 主要更新内容 | 使用建议 |
|-----------|----------|-------------|----------|
| **首次登录修改密码** | 管理员重置密码后，用户首次登录必须修改密码 | • 明确AD/LDAP同步用户仍受此限制<br>• 补充SCIM/JIT用户例外情况<br>• 详细说明执行流程和Redis机制 | 建议启用，提高密码安全性。启用后需提前告知用户 |
| **重置密码关联账号属性** | 指定重置密码时用于识别用户身份的字段 | • 新增完整功能说明<br>• 详细说明各字段类型处理逻辑<br>• 补充同步用户处理方式 | 建议配置多个字段，优先选择已验证的联系方式 |
| **密码有效期** | 设置密码的使用期限，到期后强制修改 | • 补充同步用户处理逻辑<br>• 明确依赖条件和技术实现<br>• 详细说明过期处理机制 | 建议设置90-180天，需配合到期提醒使用 |
| **密码到期提醒** | 密码即将到期时提前通知用户 | • 新增完整实现机制说明<br>• 详细描述多渠道通知逻辑<br>• 补充定时任务和防重复机制 | 建议设置7-14天，确保邮件短信服务正常 |
| **密码历史记录** | 防止用户重复使用最近用过的密码 | • 补充验证时机详细说明<br>• 明确同步用户特殊处理<br>• 详细说明MD5存储机制 | 建议设置3-5个历史密码，平衡安全性和体验 |

## 登录策略配置

| 配置项名称 | 功能说明 | 主要更新内容 | 使用建议 |
|-----------|----------|-------------|----------|
| **忘记密码关联账号属性** | 配置忘记密码功能支持的用户属性字段 | • 新增完整功能说明<br>• 详细描述验证流程分支<br>• 补充安全机制和频率限制 | 建议包含主要联系方式，确保字段已验证 |

## 访问控制配置

| 配置项名称 | 功能说明 | 主要更新内容 | 使用建议 |
|-----------|----------|-------------|----------|
| **身份验证属性** | 配置登录时需要验证的用户属性字段 | • 新增完整功能说明<br>• 详细描述验证逻辑和流程<br>• 补充特殊用户处理规则 | 根据安全需求选择，避免过多验证影响体验 |

## 重要提醒

### 🔧 技术改进
- **执行机制优化**：所有配置项都补充了详细的技术实现逻辑
- **同步用户处理**：明确了AD/LDAP、SCIM、JIT等不同类型用户的处理方式
- **安全机制增强**：补充了各项配置的安全考量和风险控制措施

### ⚠️ 注意事项
- **AD/LDAP用户**：部分功能可能需要在源系统进行操作
- **配置依赖**：某些功能需要其他配置项配合使用
- **用户体验**：过多的安全验证可能影响用户登录体验

### 📋 配置建议
1. **优先级**：密码安全 > 用户体验 > 管理便利
2. **测试验证**：配置变更前建议在测试环境验证
3. **用户通知**：重要配置变更需提前通知用户
4. **监控观察**：配置生效后需观察用户反馈和系统日志

---
*更新时间：2024年12月*  
*适用版本：当前IAM系统版本* 