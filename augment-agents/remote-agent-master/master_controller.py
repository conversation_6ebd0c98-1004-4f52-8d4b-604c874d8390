#!/usr/bin/env python3
"""
Remote Agent Master Controller
管理分布式 Remote Agent 节点的主控制器
"""

import json
import time
import uuid
import logging
import requests
import threading
from datetime import datetime, timedelta
from flask import Flask, request, jsonify, render_template_string
from flask_cors import CORS
from collections import defaultdict

class RemoteAgentMaster:
    def __init__(self):
        self.nodes = {}  # 注册的节点
        self.tasks = {}  # 任务队列
        self.task_history = []  # 任务历史
        self.setup_logging()
        self.setup_flask()
        
    def setup_logging(self):
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('master-controller.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('master-controller')
        
    def setup_flask(self):
        """设置 Flask 应用"""
        self.app = Flask(__name__)
        CORS(self.app)
        
        # 节点管理 API
        @self.app.route('/api/nodes/register', methods=['POST'])
        def register_node():
            return jsonify(self.register_node(request.json))
            
        @self.app.route('/api/nodes/heartbeat', methods=['POST'])
        def node_heartbeat():
            return jsonify(self.update_node_heartbeat(request.json))
            
        @self.app.route('/api/nodes', methods=['GET'])
        def list_nodes():
            return jsonify(self.get_nodes_status())
            
        # 任务管理 API
        @self.app.route('/api/tasks/submit', methods=['POST'])
        def submit_task():
            return jsonify(self.submit_task(request.json))
            
        @self.app.route('/api/tasks/<task_id>', methods=['GET'])
        def get_task_status(task_id):
            return jsonify(self.get_task_status(task_id))
            
        @self.app.route('/api/tasks', methods=['GET'])
        def list_tasks():
            return jsonify(self.get_all_tasks())
            
        # 批量任务 API
        @self.app.route('/api/tasks/batch', methods=['POST'])
        def submit_batch_tasks():
            return jsonify(self.submit_batch_tasks(request.json))
            
        # Web 界面
        @self.app.route('/')
        def dashboard():
            return render_template_string(DASHBOARD_TEMPLATE, 
                                        nodes=self.nodes,
                                        tasks=self.tasks)
            
    def register_node(self, node_data):
        """注册新节点"""
        node_id = node_data['node_id']
        
        self.nodes[node_id] = {
            'node_id': node_id,
            'capabilities': node_data['capabilities'],
            'endpoint': node_data['endpoint'],
            'status': node_data['status'],
            'last_heartbeat': datetime.now(),
            'registered_at': datetime.now(),
            'tasks_completed': 0,
            'tasks_failed': 0
        }
        
        self.logger.info(f"节点注册成功: {node_id} - {node_data['endpoint']}")
        self.logger.info(f"节点能力: {[cap['type'] for cap in node_data['capabilities']]}")
        
        return {
            'success': True,
            'message': '节点注册成功',
            'node_id': node_id
        }
        
    def update_node_heartbeat(self, heartbeat_data):
        """更新节点心跳"""
        node_id = heartbeat_data['node_id']
        
        if node_id in self.nodes:
            self.nodes[node_id]['last_heartbeat'] = datetime.now()
            self.nodes[node_id]['status'] = heartbeat_data['status']
            return {'success': True}
        else:
            return {'success': False, 'error': '节点未注册'}
            
    def get_nodes_status(self):
        """获取所有节点状态"""
        current_time = datetime.now()
        nodes_status = []
        
        for node_id, node_info in self.nodes.items():
            # 检查节点是否在线
            time_since_heartbeat = current_time - node_info['last_heartbeat']
            is_online = time_since_heartbeat < timedelta(minutes=2)
            
            status = {
                'node_id': node_id,
                'endpoint': node_info['endpoint'],
                'capabilities': [cap['type'] for cap in node_info['capabilities']],
                'status': node_info['status'],
                'online': is_online,
                'last_heartbeat': node_info['last_heartbeat'].isoformat(),
                'tasks_completed': node_info['tasks_completed'],
                'tasks_failed': node_info['tasks_failed']
            }
            nodes_status.append(status)
            
        return {
            'total_nodes': len(nodes_status),
            'online_nodes': sum(1 for n in nodes_status if n['online']),
            'nodes': nodes_status
        }
        
    def find_capable_node(self, agent_type):
        """查找能够执行指定类型任务的节点"""
        current_time = datetime.now()
        
        for node_id, node_info in self.nodes.items():
            # 检查节点是否在线
            time_since_heartbeat = current_time - node_info['last_heartbeat']
            if time_since_heartbeat > timedelta(minutes=2):
                continue
                
            # 检查节点是否空闲
            if node_info['status'] != 'idle':
                continue
                
            # 检查节点是否支持该类型的 Agent
            supported_types = [cap['type'] for cap in node_info['capabilities']]
            if agent_type in supported_types:
                return node_id, node_info
                
        return None, None
        
    def submit_task(self, task_data):
        """提交单个任务"""
        task_id = str(uuid.uuid4())
        agent_type = task_data['agent_type']
        parameters = task_data.get('parameters', {})
        
        # 查找可用节点
        node_id, node_info = self.find_capable_node(agent_type)
        
        if not node_id:
            return {
                'success': False,
                'error': f'没有可用的节点执行 {agent_type} 任务',
                'task_id': task_id
            }
            
        # 创建任务
        task = {
            'task_id': task_id,
            'agent_type': agent_type,
            'parameters': parameters,
            'assigned_node': node_id,
            'status': 'submitted',
            'created_at': datetime.now(),
            'started_at': None,
            'completed_at': None,
            'result': None,
            'error': None
        }
        
        self.tasks[task_id] = task
        
        # 异步执行任务
        threading.Thread(
            target=self.execute_task_on_node,
            args=(task_id, node_id, node_info),
            daemon=True
        ).start()
        
        self.logger.info(f"任务已提交: {task_id} -> 节点 {node_id}")
        
        return {
            'success': True,
            'task_id': task_id,
            'assigned_node': node_id,
            'estimated_start_time': datetime.now().isoformat()
        }
        
    def execute_task_on_node(self, task_id, node_id, node_info):
        """在指定节点上执行任务"""
        task = self.tasks[task_id]
        task['status'] = 'running'
        task['started_at'] = datetime.now()
        
        try:
            # 向节点发送执行请求
            response = requests.post(
                f"{node_info['endpoint']}/execute",
                json={
                    'task_id': task_id,
                    'agent_type': task['agent_type'],
                    'parameters': task['parameters']
                },
                timeout=300  # 5分钟超时
            )
            
            if response.status_code == 200:
                result = response.json()
                
                if result['success']:
                    task['status'] = 'completed'
                    task['result'] = result['result']
                    self.nodes[node_id]['tasks_completed'] += 1
                    self.logger.info(f"任务完成: {task_id}")
                else:
                    task['status'] = 'failed'
                    task['error'] = result.get('error', '未知错误')
                    self.nodes[node_id]['tasks_failed'] += 1
                    self.logger.error(f"任务失败: {task_id} - {task['error']}")
            else:
                task['status'] = 'failed'
                task['error'] = f'HTTP {response.status_code}: {response.text}'
                self.nodes[node_id]['tasks_failed'] += 1
                
        except Exception as e:
            task['status'] = 'failed'
            task['error'] = str(e)
            self.nodes[node_id]['tasks_failed'] += 1
            self.logger.error(f"任务执行异常: {task_id} - {e}")
            
        finally:
            task['completed_at'] = datetime.now()
            self.task_history.append(task.copy())
            
    def submit_batch_tasks(self, batch_data):
        """提交批量任务"""
        tasks = batch_data['tasks']
        submitted_tasks = []
        
        for task_data in tasks:
            result = self.submit_task(task_data)
            submitted_tasks.append(result)
            
        return {
            'success': True,
            'total_tasks': len(tasks),
            'submitted_tasks': submitted_tasks
        }
        
    def get_task_status(self, task_id):
        """获取任务状态"""
        if task_id in self.tasks:
            task = self.tasks[task_id].copy()
            # 转换时间为字符串
            for time_field in ['created_at', 'started_at', 'completed_at']:
                if task[time_field]:
                    task[time_field] = task[time_field].isoformat()
            return task
        else:
            return {'error': '任务不存在'}
            
    def get_all_tasks(self):
        """获取所有任务状态"""
        all_tasks = []
        
        for task_id, task in self.tasks.items():
            task_copy = task.copy()
            # 转换时间为字符串
            for time_field in ['created_at', 'started_at', 'completed_at']:
                if task_copy[time_field]:
                    task_copy[time_field] = task_copy[time_field].isoformat()
            all_tasks.append(task_copy)
            
        return {
            'total_tasks': len(all_tasks),
            'tasks': sorted(all_tasks, key=lambda x: x['created_at'], reverse=True)
        }
        
    def start(self, port=8889):
        """启动主控制器"""
        self.logger.info("启动 Remote Agent Master Controller")
        self.logger.info(f"监听端口: {port}")
        self.logger.info(f"Web 界面: http://localhost:{port}")
        
        self.app.run(host='0.0.0.0', port=port, debug=False)

# Web 界面模板
DASHBOARD_TEMPLATE = '''
<!DOCTYPE html>
<html>
<head>
    <title>Remote Agent Master Controller</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #2c3e50; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .node { margin: 10px 0; padding: 15px; border-radius: 5px; border-left: 5px solid; }
        .online { background: #d4edda; border-color: #28a745; }
        .offline { background: #f8d7da; border-color: #dc3545; }
        .task { margin: 5px 0; padding: 10px; background: #f8f9fa; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🤖 Remote Agent Master Controller</h1>
        <p>分布式 Agent 节点管理控制台</p>
    </div>
    
    <div class="grid">
        <div class="card">
            <h2>📡 注册节点</h2>
            {% for node_id, node in nodes.items() %}
            <div class="node online">
                <strong>节点: {{ node_id[:8] }}</strong><br>
                <small>端点: {{ node.endpoint }}</small><br>
                <small>能力: {{ node.capabilities|map(attribute='type')|join(', ') }}</small><br>
                <small>状态: {{ node.status }}</small>
            </div>
            {% endfor %}
        </div>
        
        <div class="card">
            <h2>📋 运行任务</h2>
            {% for task_id, task in tasks.items() %}
            <div class="task">
                <strong>{{ task.agent_type }}</strong> - {{ task.status }}<br>
                <small>任务ID: {{ task_id[:8] }}</small>
            </div>
            {% endfor %}
        </div>
    </div>
</body>
</html>
'''

if __name__ == '__main__':
    import argparse
    
    parser = argparse.ArgumentParser(description='Remote Agent Master Controller')
    parser.add_argument('--port', type=int, default=8889, help='监听端口')
    
    args = parser.parse_args()
    
    master = RemoteAgentMaster()
    
    try:
        master.start(port=args.port)
    except KeyboardInterrupt:
        print("\n主控制器已停止")
