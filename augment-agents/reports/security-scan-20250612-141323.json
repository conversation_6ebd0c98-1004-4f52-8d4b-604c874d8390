{"timestamp": "2025-06-12T14:13:20.327620", "scan_summary": {"files_scanned": 362, "vulnerabilities_found": 19, "critical_issues": 0, "high_issues": 12, "medium_issues": 7, "low_issues": 0}, "vulnerabilities": [{"type": "hardcoded_activation_code", "severity": "high", "file": "iam/iam-service/src/main/resources/application.properties", "line": 98, "description": "发现硬编码的敏感信息: hardcoded_activation_code", "evidence": "activeCode=555555", "recommendation": "使用环境变量或配置中心管理敏感信息"}, {"type": "csrf_disabled", "severity": "high", "file": "ncm/ncm-service/src/main/java/com/cyberscraft/uep/ncm/config/WebSecurityConfiguration.java", "line": 40, "description": "安全配置问题: csrf_disabled", "evidence": ".csrf().disable()", "recommendation": "检查并加强安全配置"}, {"type": "csrf_disabled", "severity": "high", "file": "ncm/ncm-service/src/main/java/com/cyberscraft/uep/ncm/config/WebSecurityConfiguration.java", "line": 50, "description": "安全配置问题: csrf_disabled", "evidence": ".csrf().disable()", "recommendation": "检查并加强安全配置"}, {"type": "csrf_disabled", "severity": "high", "file": "iam/iam-notify-service/src/main/java/com/cyberscraft/uep/iam/notify/config/web/WebSecurityConfiguration.java", "line": 22, "description": "安全配置问题: csrf_disabled", "evidence": ".csrf().disable()", "recommendation": "检查并加强安全配置"}, {"type": "csrf_disabled", "severity": "high", "file": "iam/iam-service/src/main/java/com/cyberscraft/uep/iam/configuration/kerberos/KerberosWebSecurityConfig.java", "line": 49, "description": "安全配置问题: csrf_disabled", "evidence": ".csrf().disable()", "recommendation": "检查并加强安全配置"}, {"type": "csrf_disabled", "severity": "high", "file": "iam/iam-service/src/main/java/com/cyberscraft/uep/iam/configuration/server/SecurityConfig.java", "line": 137, "description": "安全配置问题: csrf_disabled", "evidence": ".csrf().disable()", "recommendation": "检查并加强安全配置"}, {"type": "csrf_disabled", "severity": "high", "file": "iam/iam-service/src/main/java/com/cyberscraft/uep/iam/configuration/server/SecurityConfig.java", "line": 143, "description": "安全配置问题: csrf_disabled", "evidence": ".csrf().disable()", "recommendation": "检查并加强安全配置"}, {"type": "permissive_access", "severity": "medium", "file": "iam/iam-service/src/main/java/com/cyberscraft/uep/iam/configuration/server/SecurityConfig.java", "line": 126, "description": "安全配置问题: permissive_access", "evidence": ".permitAll()", "recommendation": "检查并加强安全配置"}, {"type": "permissive_access", "severity": "medium", "file": "iam/iam-service/src/main/java/com/cyberscraft/uep/iam/configuration/server/SecurityConfig.java", "line": 131, "description": "安全配置问题: permissive_access", "evidence": ".permitAll()", "recommendation": "检查并加强安全配置"}, {"type": "permissive_access", "severity": "medium", "file": "iam/iam-service/src/main/java/com/cyberscraft/uep/iam/configuration/server/SecurityConfig.java", "line": 145, "description": "安全配置问题: permissive_access", "evidence": ".permitAll()", "recommendation": "检查并加强安全配置"}, {"type": "permissive_access", "severity": "medium", "file": "iam/iam-service/src/main/java/com/cyberscraft/uep/iam/configuration/server/SecurityConfig.java", "line": 152, "description": "安全配置问题: permissive_access", "evidence": ".permitAll()", "recommendation": "检查并加强安全配置"}, {"type": "permissive_access", "severity": "medium", "file": "iam/iam-service/src/main/java/com/cyberscraft/uep/iam/configuration/server/SecurityConfig.java", "line": 171, "description": "安全配置问题: permissive_access", "evidence": ".permitAll()", "recommendation": "检查并加强安全配置"}, {"type": "allow_all_requests", "severity": "medium", "file": "iam/iam-service/src/main/java/com/cyberscraft/uep/iam/configuration/server/SecurityConfig.java", "line": 152, "description": "安全配置问题: allow_all_requests", "evidence": ".anyRequest().permitAll()", "recommendation": "检查并加强安全配置"}, {"type": "csrf_disabled", "severity": "high", "file": "base/oauth2-client/src/main/java/com/cyberscraft/uep/base/oauth2/client/config/WebSecurityConfiguration.java", "line": 73, "description": "安全配置问题: csrf_disabled", "evidence": ".csrf().disable()", "recommendation": "检查并加强安全配置"}, {"type": "permissive_access", "severity": "medium", "file": "base/oauth2-client/src/main/java/com/cyberscraft/uep/base/oauth2/client/config/WebSecurityConfiguration.java", "line": 83, "description": "安全配置问题: permissive_access", "evidence": ".permitAll()", "recommendation": "检查并加强安全配置"}, {"type": "exposed_actuator_endpoints", "severity": "high", "file": "mdm/mdm-service/src/main/resources/application-product.properties", "line": 74, "description": "安全配置问题: exposed_actuator_endpoints", "evidence": "management.endpoints.web.exposure.include=*", "recommendation": "检查并加强安全配置"}, {"type": "exposed_actuator_endpoints", "severity": "high", "file": "mdm/mdm-service/src/main/resources/application.properties", "line": 19, "description": "安全配置问题: exposed_actuator_endpoints", "evidence": "management.endpoints.web.exposure.include=*", "recommendation": "检查并加强安全配置"}, {"type": "exposed_actuator_endpoints", "severity": "high", "file": "adm/adm-service/src/main/resources/application.properties", "line": 25, "description": "安全配置问题: exposed_actuator_endpoints", "evidence": "management.endpoints.web.exposure.include=*", "recommendation": "检查并加强安全配置"}, {"type": "jwt_no_expiration", "severity": "high", "file": "iam/iam-service/src/main/java/com/cyberscraft/uep/iam/controller/api/IdTokenEnhancer.java", "line": 132, "description": "JWT 安全问题: jwt_no_expiration", "evidence": ".setExpiration(null)", "recommendation": "使用强密钥和安全的 JWT 配置"}]}