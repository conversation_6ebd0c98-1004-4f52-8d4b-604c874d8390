
<!DOCTYPE html>
<html>
<head>
    <title>文档分析报告</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f8f9fa; padding: 20px; border-radius: 5px; border-left: 5px solid #6f42c1; }
        .summary { background: #e9ecef; padding: 15px; margin: 20px 0; border-radius: 5px; }
        .api { margin: 10px 0; padding: 15px; border-radius: 5px; border-left: 5px solid; }
        .documented { background: #d4edda; border-color: #28a745; }
        .undocumented { background: #f8d7da; border-color: #dc3545; }
        .missing { margin: 10px 0; padding: 15px; border-radius: 5px; border-left: 5px solid #ffc107; background: #fff3cd; }
        .suggestion { margin: 10px 0; padding: 15px; border-radius: 5px; border-left: 5px solid #17a2b8; background: #d1ecf1; }
        .file-path { font-family: monospace; background: #f8f9fa; padding: 2px 4px; border-radius: 3px; }
        .progress { width: 100%; background: #e9ecef; border-radius: 4px; }
        .progress-bar { height: 20px; background: #6f42c1; border-radius: 4px; text-align: center; line-height: 20px; color: white; }
    </style>
</head>
<body>
    <div class="header">
        <h1>📚 文档分析报告</h1>
        <p>生成时间: 2025-06-12T15:52:52.464555</p>
    </div>
    
    <div class="summary">
        <h2>📋 文档统计</h2>
        <ul>
            <li>API 端点总数: 10</li>
            <li>已文档化端点: 0</li>
            <li>缺少 JavaDoc: 115</li>
            <li>README 文件: 5</li>
            <li>文档覆盖率: 0.0%</li>
        </ul>
        
        <div class="progress">
            <div class="progress-bar" style="width: 0.0%">
                0.0%
            </div>
        </div>
    </div>
    
    <div class="api-analysis">
        <h2>🌐 API 文档分析</h2>
        
            <div class="api undocumented">
                <h3>Post ❌ 缺少文档</h3>
                <p><strong>文件:</strong> <span class="file-path">mdm/mdm-service/src/main/java/com/cyberscraft/uep/mdm/app/controller/app/basic/FileUploadController.java</span></p>
                <p><strong>操作文档:</strong> ❌ | 
                   <strong>响应文档:</strong> ❌</p>
            </div>
            
            <div class="api undocumented">
                <h3>Get ❌ 缺少文档</h3>
                <p><strong>文件:</strong> <span class="file-path">iam/iam-service/src/main/java/com/cyberscraft/uep/iam/controller/api/HealthCheckController.java</span></p>
                <p><strong>操作文档:</strong> ❌ | 
                   <strong>响应文档:</strong> ❌</p>
            </div>
            
            <div class="api undocumented">
                <h3>Get ❌ 缺少文档</h3>
                <p><strong>文件:</strong> <span class="file-path">iam/iam-service/src/main/java/com/cyberscraft/uep/iam/controller/api/LoginController.java</span></p>
                <p><strong>操作文档:</strong> ❌ | 
                   <strong>响应文档:</strong> ❌</p>
            </div>
            
            <div class="api undocumented">
                <h3>Post ❌ 缺少文档</h3>
                <p><strong>文件:</strong> <span class="file-path">iam/iam-service/src/main/java/com/cyberscraft/uep/iam/controller/api/connector/controller/ConnectorEventController.java</span></p>
                <p><strong>操作文档:</strong> ❌ | 
                   <strong>响应文档:</strong> ❌</p>
            </div>
            
            <div class="api undocumented">
                <h3>Get ❌ 缺少文档</h3>
                <p><strong>文件:</strong> <span class="file-path">iam/iam-service/src/main/java/com/cyberscraft/uep/iam/controller/api/connector/wework/controller/WeWorkController.java</span></p>
                <p><strong>操作文档:</strong> ❌ | 
                   <strong>响应文档:</strong> ❌</p>
            </div>
            
            <div class="api undocumented">
                <h3>Post ❌ 缺少文档</h3>
                <p><strong>文件:</strong> <span class="file-path">iam/iam-service/src/main/java/com/cyberscraft/uep/iam/controller/api/connector/wework/controller/WeWorkController.java</span></p>
                <p><strong>操作文档:</strong> ❌ | 
                   <strong>响应文档:</strong> ❌</p>
            </div>
            
            <div class="api undocumented">
                <h3>Post ❌ 缺少文档</h3>
                <p><strong>文件:</strong> <span class="file-path">iam/iam-service/src/main/java/com/cyberscraft/uep/iam/controller/api/connector/etl/controller/EtlController.java</span></p>
                <p><strong>操作文档:</strong> ❌ | 
                   <strong>响应文档:</strong> ❌</p>
            </div>
            
            <div class="api undocumented">
                <h3>Post ❌ 缺少文档</h3>
                <p><strong>文件:</strong> <span class="file-path">iam/iam-service/src/main/java/com/cyberscraft/uep/iam/controller/api/connector/etl/controller/EtlController.java</span></p>
                <p><strong>操作文档:</strong> ❌ | 
                   <strong>响应文档:</strong> ❌</p>
            </div>
            
            <div class="api undocumented">
                <h3>Get ❌ 缺少文档</h3>
                <p><strong>文件:</strong> <span class="file-path">uam/uam-service/src/main/java/com/cyberscraft/uep/uam/controller/TestController.java</span></p>
                <p><strong>操作文档:</strong> ❌ | 
                   <strong>响应文档:</strong> ❌</p>
            </div>
            
            <div class="api undocumented">
                <h3>Get ❌ 缺少文档</h3>
                <p><strong>文件:</strong> <span class="file-path">uam/uam-service/src/main/java/com/cyberscraft/uep/uam/controller/TestController.java</span></p>
                <p><strong>操作文档:</strong> ❌ | 
                   <strong>响应文档:</strong> ❌</p>
            </div>
            
    </div>
    
    <div class="missing-docs">
        <h2>❌ 缺失的文档</h2>
        
            <div class="missing">
                <h3>方法 getValue 缺少 JavaDoc 注释</h3>
                <p><strong>文件:</strong> <span class="file-path">mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/PolicyCategory.java</span></p>
                <p><strong>类型:</strong> method | <strong>名称:</strong> getValue</p>
            </div>
            
            <div class="missing">
                <h3>方法 valueOf 缺少 JavaDoc 注释</h3>
                <p><strong>文件:</strong> <span class="file-path">mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/PolicyCategory.java</span></p>
                <p><strong>类型:</strong> method | <strong>名称:</strong> valueOf</p>
            </div>
            
            <div class="missing">
                <h3>方法 findNameByValue 缺少 JavaDoc 注释</h3>
                <p><strong>文件:</strong> <span class="file-path">mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/PolicyCategory.java</span></p>
                <p><strong>类型:</strong> method | <strong>名称:</strong> findNameByValue</p>
            </div>
            
            <div class="missing">
                <h3>方法 getValue 缺少 JavaDoc 注释</h3>
                <p><strong>文件:</strong> <span class="file-path">mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/PolicySubCategory.java</span></p>
                <p><strong>类型:</strong> method | <strong>名称:</strong> getValue</p>
            </div>
            
            <div class="missing">
                <h3>方法 getClientCmd 缺少 JavaDoc 注释</h3>
                <p><strong>文件:</strong> <span class="file-path">mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/PolicySubCategory.java</span></p>
                <p><strong>类型:</strong> method | <strong>名称:</strong> getClientCmd</p>
            </div>
            
            <div class="missing">
                <h3>方法 valueOf 缺少 JavaDoc 注释</h3>
                <p><strong>文件:</strong> <span class="file-path">mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/PolicySubCategory.java</span></p>
                <p><strong>类型:</strong> method | <strong>名称:</strong> valueOf</p>
            </div>
            
            <div class="missing">
                <h3>方法 findNameByValue 缺少 JavaDoc 注释</h3>
                <p><strong>文件:</strong> <span class="file-path">mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/PolicySubCategory.java</span></p>
                <p><strong>类型:</strong> method | <strong>名称:</strong> findNameByValue</p>
            </div>
            
            <div class="missing">
                <h3>方法 isDDCustomizedPolicy 缺少 JavaDoc 注释</h3>
                <p><strong>文件:</strong> <span class="file-path">mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/PolicySubCategory.java</span></p>
                <p><strong>类型:</strong> method | <strong>名称:</strong> isDDCustomizedPolicy</p>
            </div>
            
            <div class="missing">
                <h3>方法 getValue 缺少 JavaDoc 注释</h3>
                <p><strong>文件:</strong> <span class="file-path">mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/PolicyStatus.java</span></p>
                <p><strong>类型:</strong> method | <strong>名称:</strong> getValue</p>
            </div>
            
            <div class="missing">
                <h3>方法 getCode 缺少 JavaDoc 注释</h3>
                <p><strong>文件:</strong> <span class="file-path">mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/ClientCommandType.java</span></p>
                <p><strong>类型:</strong> method | <strong>名称:</strong> getCode</p>
            </div>
            
            <div class="missing">
                <h3>方法 getCode 缺少 JavaDoc 注释</h3>
                <p><strong>文件:</strong> <span class="file-path">mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/TenantFromType.java</span></p>
                <p><strong>类型:</strong> method | <strong>名称:</strong> getCode</p>
            </div>
            
            <div class="missing">
                <h3>方法 isCallBack 缺少 JavaDoc 注释</h3>
                <p><strong>文件:</strong> <span class="file-path">mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/TenantFromType.java</span></p>
                <p><strong>类型:</strong> method | <strong>名称:</strong> isCallBack</p>
            </div>
            
            <div class="missing">
                <h3>方法 getCode 缺少 JavaDoc 注释</h3>
                <p><strong>文件:</strong> <span class="file-path">mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/DfsTypeEnum.java</span></p>
                <p><strong>类型:</strong> method | <strong>名称:</strong> getCode</p>
            </div>
            
            <div class="missing">
                <h3>方法 getName 缺少 JavaDoc 注释</h3>
                <p><strong>文件:</strong> <span class="file-path">mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/DfsTypeEnum.java</span></p>
                <p><strong>类型:</strong> method | <strong>名称:</strong> getName</p>
            </div>
            
            <div class="missing">
                <h3>方法 getValue 缺少 JavaDoc 注释</h3>
                <p><strong>文件:</strong> <span class="file-path">mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/DfsTypeEnum.java</span></p>
                <p><strong>类型:</strong> method | <strong>名称:</strong> getValue</p>
            </div>
            
    </div>
    
    <div class="suggestions">
        <h2>💡 文档改进建议</h2>
        
            <div class="suggestion">
                <h3>完善 API 文档 (优先级: high)</h3>
                <p><strong>类别:</strong> api_documentation</p>
                <p>只有 0.0% 的 API 端点有文档，建议添加 Swagger 注解</p>
            </div>
            
            <div class="suggestion">
                <h3>添加 JavaDoc 注释 (优先级: medium)</h3>
                <p><strong>类别:</strong> javadoc</p>
                <p>发现 115 个缺少 JavaDoc 的类或方法</p>
            </div>
            
    </div>
</body>
</html>
        