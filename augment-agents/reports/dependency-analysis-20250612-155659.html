
<!DOCTYPE html>
<html>
<head>
    <title>依赖管理报告</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f8f9fa; padding: 20px; border-radius: 5px; border-left: 5px solid #6f42c1; }
        .summary { background: #e9ecef; padding: 15px; margin: 20px 0; border-radius: 5px; }
        .issue { margin: 10px 0; padding: 15px; border-radius: 5px; border-left: 5px solid; }
        .critical { background: #f8d7da; border-color: #dc3545; }
        .high { background: #fff3cd; border-color: #ffc107; }
        .medium { background: #d1ecf1; border-color: #17a2b8; }
        .warning { background: #fff3cd; border-color: #ffc107; }
        .recommendation { margin: 10px 0; padding: 15px; border-radius: 5px; border-left: 5px solid #28a745; background: #d4edda; }
        .dependency { font-family: monospace; background: #f8f9fa; padding: 2px 4px; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>📦 依赖管理报告</h1>
        <p>生成时间: 2025-06-12T15:56:58.751989</p>
    </div>
    
    <div class="summary">
        <h2>📋 依赖统计</h2>
        <ul>
            <li>分析 POM 文件: 58</li>
            <li>发现依赖: 760</li>
            <li>过时依赖: 0</li>
            <li>安全问题: 0</li>
            <li>版本冲突: 11</li>
        </ul>
    </div>
    
    <div class="issues">
        <h2>🚨 发现的问题</h2>
        
            <div class="issue warning">
                <h3>依赖版本冲突: com.cyberscraft.uep:common</h3>
                <p><strong>依赖:</strong> <span class="dependency">com.cyberscraft.uep:common</span></p>
            </div>
            <div class="issue warning">
                <h3>依赖版本冲突: org.apache.poi:poi</h3>
                <p><strong>依赖:</strong> <span class="dependency">org.apache.poi:poi</span></p>
            </div>
            <div class="issue warning">
                <h3>依赖版本冲突: com.cyberscraft.uep:message-client</h3>
                <p><strong>依赖:</strong> <span class="dependency">com.cyberscraft.uep:message-client</span></p>
            </div>
            <div class="issue warning">
                <h3>依赖版本冲突: com.cyberscraft.uep:iam-api</h3>
                <p><strong>依赖:</strong> <span class="dependency">com.cyberscraft.uep:iam-api</span></p>
            </div>
            <div class="issue warning">
                <h3>依赖版本冲突: com.cyberscraft.uep:uam-api</h3>
                <p><strong>依赖:</strong> <span class="dependency">com.cyberscraft.uep:uam-api</span></p>
            </div>
            <div class="issue warning">
                <h3>依赖版本冲突: com.cyberscraft.uep:account-client</h3>
                <p><strong>依赖:</strong> <span class="dependency">com.cyberscraft.uep:account-client</span></p>
            </div>
            <div class="issue warning">
                <h3>依赖版本冲突: com.cyberscraft.uep:account-dingding</h3>
                <p><strong>依赖:</strong> <span class="dependency">com.cyberscraft.uep:account-dingding</span></p>
            </div>
            <div class="issue warning">
                <h3>依赖版本冲突: com.cyberscraft.uep:ncm-api</h3>
                <p><strong>依赖:</strong> <span class="dependency">com.cyberscraft.uep:ncm-api</span></p>
            </div>
            <div class="issue warning">
                <h3>依赖版本冲突: com.cyberscraft.uep:ncm-client</h3>
                <p><strong>依赖:</strong> <span class="dependency">com.cyberscraft.uep:ncm-client</span></p>
            </div>
            <div class="issue warning">
                <h3>依赖版本冲突: com.cyberscraft.uep:account-feishu</h3>
                <p><strong>依赖:</strong> <span class="dependency">com.cyberscraft.uep:account-feishu</span></p>
            </div>
            <div class="issue warning">
                <h3>依赖版本冲突: com.cyberscraft.uep:aksk-server</h3>
                <p><strong>依赖:</strong> <span class="dependency">com.cyberscraft.uep:aksk-server</span></p>
            </div>
    </div>
    
    <div class="recommendations">
        <h2>💡 改进建议</h2>
        
            <div class="recommendation">
                <h3>解决依赖冲突 (优先级: medium)</h3>
                <p>发现 11 个依赖冲突，建议统一版本</p>
            </div>
            
    </div>
</body>
</html>
        