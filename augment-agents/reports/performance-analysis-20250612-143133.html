
<!DOCTYPE html>
<html>
<head>
    <title>性能分析报告</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f8f9fa; padding: 20px; border-radius: 5px; border-left: 5px solid #28a745; }
        .summary { background: #e9ecef; padding: 15px; margin: 20px 0; border-radius: 5px; }
        .issue { margin: 10px 0; padding: 15px; border-radius: 5px; border-left: 5px solid #dc3545; background: #f8d7da; }
        .optimization { margin: 10px 0; padding: 15px; border-radius: 5px; border-left: 5px solid #007bff; background: #d1ecf1; }
        .priority { font-weight: bold; text-transform: uppercase; }
        .file-path { font-family: monospace; background: #f8f9fa; padding: 2px 4px; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>⚡ 性能分析报告</h1>
        <p>生成时间: 2025-06-12T14:31:30.996075</p>
    </div>
    
    <div class="summary">
        <h2>📊 分析统计</h2>
        <ul>
            <li>分析文件数: 50</li>
            <li>发现问题数: 0</li>
            <li>优化建议数: 1</li>
        </ul>
    </div>
    
    <div class="issues">
        <h2>🚨 性能问题</h2>
        <p style='color: green;'>✅ 未发现性能问题</p>
    </div>
    
    <div class="optimizations">
        <h2>💡 优化建议</h2>
        
            <div class="optimization">
                <h3>建议使用异步处理 <span class="priority">(low)</span></h3>
                <p><strong>文件:</strong> <span class="file-path">mdm/mdm-core/src/main/java/com/cyberscraft/uep/mdm/core/ios/impl/IOSMDMServiceImpl.java</span></p>
                <p><strong>收益:</strong> 提高系统响应性，避免阻塞</p>
                <p><strong>实施方案:</strong> 添加 @Async 注解或使用 CompletableFuture</p>
            </div>
            
    </div>
</body>
</html>
        