{"timestamp": "2025-06-12T14:23:26.943134", "dependency_summary": {"pom_files_analyzed": 58, "dependencies_found": 760, "outdated_dependencies": 0, "security_issues": 0, "conflicts_detected": 11}, "dependencies": [{"groupId": "org.springframework.cloud", "artifactId": "spring-cloud-dependencies", "version": "${spring-cloud.version}", "scope": "import", "file": "service-parent/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-parent", "version": "${spring-boot.version}", "scope": "import", "file": "service-parent/pom.xml"}, {"groupId": "org.springframework", "artifactId": "spring-web", "version": "${spring-framework.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.springframework", "artifactId": "spring-webmvc", "version": "${spring-framework.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.springframework.security", "artifactId": "spring-security-core", "version": "${spring-security.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.apache.tomcat.embed", "artifactId": "tomcat-embed-core", "version": "${tomcat.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.apache.tomcat.embed", "artifactId": "tomcat-embed-el", "version": "${tomcat.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.apache.tomcat.embed", "artifactId": "tomcat-embed-websocket", "version": "${tomcat.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.lmax", "artifactId": "disruptor", "version": "${disruptor.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.slf4j", "artifactId": "slf4j-api", "version": "${slf4j.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.slf4j", "artifactId": "log4j-over-slf4j", "version": "${slf4j.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.apache.logging.log4j", "artifactId": "log4j-to-slf4j", "version": "${log4j.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.apache.logging.log4j", "artifactId": "log4j-api", "version": "${log4j.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.apache.logging.log4j", "artifactId": "log4j-core", "version": "${log4j.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.apache.logging.log4j", "artifactId": "log4j-jul", "version": "${log4j.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.apache.logging.log4j", "artifactId": "log4j-slf4j-impl", "version": "${log4j.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "javax.transaction", "artifactId": "javax.transaction-api", "version": "${javax-transaction.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.mybatis.spring.boot", "artifactId": "mybatis-spring-boot-starter", "version": "${mybatis-spring.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.mybatis", "artifactId": "mybatis", "version": "${mybatis.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "de.codecentric", "artifactId": "spring-boot-admin-starter-client", "version": "${springboot-admin.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "joda-time", "artifactId": "joda-time", "version": "${joda-time.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.alibaba.platform.shared", "artifactId": "xxpt.gateway.shared.client", "version": "${xxpt-gateway-shared-client.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.alibaba", "artifactId": "<PERSON><PERSON><PERSON>", "version": "${fastjson.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.google.code.gson", "artifactId": "gson", "version": "${gson.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.fasterxml.jackson.core", "artifactId": "jackson-core", "version": "${jackson.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.fasterxml.jackson.core", "artifactId": "jackson-databind", "version": "${jackson.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.fasterxml.jackson.core", "artifactId": "jackson-annotations", "version": "${jackson.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.fasterxml.jackson.dataformat", "artifactId": "jackson-dataformat-yaml", "version": "${jackson.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.fasterxml.jackson.dataformat", "artifactId": "jackson-dataformat-xml", "version": "${jackson.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.javassist", "artifactId": "javassist", "version": "${javassist.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "cglib", "artifactId": "cglib", "version": "${cglib.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "net.sf.ezmorph", "artifactId": "ezmorph", "version": "${ezmorph.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "net.sf.ehcache", "artifactId": "ehcache", "version": "${ehcache.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.ehcache", "artifactId": "ehcache", "version": "${ehcache3.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.github.ben-manes.caffeine", "artifactId": "caffeine", "version": "${caffeine.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "ognl", "artifactId": "ognl", "version": "${ognl.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.thoughtworks.xstream", "artifactId": "xstream", "version": "${xstream.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.aspectj", "artifactId": "<PERSON><PERSON><PERSON><PERSON>", "version": "${aspectj.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.aspectj", "artifactId": "aspectjrt", "version": "${aspectj.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.dom4j", "artifactId": "dom4j", "version": "${dom4j2.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "jaxen", "artifactId": "jaxen", "version": "${jaxen.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "commons-beanutils", "artifactId": "commons-beanutils", "version": "${commons-beanutils.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.apache.commons", "artifactId": "commons-collections4", "version": "${commons-collections4.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "commons-io", "artifactId": "commons-io", "version": "${commons-io.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.apache.commons", "artifactId": "commons-text", "version": "${commons-text.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.apache.commons", "artifactId": "commons-lang3", "version": "${commons-lang3.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "commons-codec", "artifactId": "commons-codec", "version": "${commons-codec.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.apache.commons", "artifactId": "commons-compress", "version": "${commons-compress.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.apache.commons", "artifactId": "commons-exec", "version": "${commons-exec.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.apache.commons", "artifactId": "commons-pool2", "version": "${commons-pool2.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.apache.httpcomponents", "artifactId": "httpclient", "version": "${httpclient.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.apache.httpcomponents", "artifactId": "httpcore", "version": "${httpcore.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.apache.httpcomponents", "artifactId": "httpmime", "version": "${httpclient.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.aliyun", "artifactId": "ocr_api20210707", "version": "${aliyun_ocr_api.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "jakarta.mail", "artifactId": "jakarta.mail-api", "version": "${mail.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.sun.mail", "artifactId": "jakarta.mail", "version": "${mail.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.quartz-scheduler", "artifactId": "quartz", "version": "${quartz.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.turo", "artifactId": "pushy", "version": "${pushy.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.mapstruct", "artifactId": "mapstruct-jdk8", "version": "${mapstruct.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.mapstruct", "artifactId": "mapstruct", "version": "${mapstruct.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.mapstruct", "artifactId": "mapstruct-processor", "version": "${mapstruct.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.projectlombok", "artifactId": "lombok", "version": "${lombok.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.google.zxing", "artifactId": "core", "version": "${zxing.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.google.zxing", "artifactId": "javase", "version": "${zxing.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "common", "version": "${project.parent.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "mysql", "artifactId": "mysql-connector-java", "version": "${mysql.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.oracle.database.jdbc", "artifactId": "ojdbc8", "version": "${oracle.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.ibm.db2", "artifactId": "jcc", "version": "${db2.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.microsoft.sqlserver", "artifactId": "mssql-jdbc", "version": "${sqlserver.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.dameng", "artifactId": "DmJdbcDriver18", "version": "${dm-java-driver.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.kingbase8", "artifactId": "kingbase8", "version": "${kingbase-java-driver.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "net.sf.ucanaccess", "artifactId": "ucanaccess", "version": "${access.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.healthmarketscience.jackcess", "artifactId": "jack<PERSON>", "version": "${jackcess.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.healthmarketscience.jackcess", "artifactId": "jackcess-encrypt", "version": "${jackcess-encrypt.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "io.springfox", "artifactId": "springfox-swagger2", "version": "${springfox.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "io.swagger", "artifactId": "swagger-models", "version": "1.5.22", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.google.guava", "artifactId": "guava", "version": "${guava.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.google.apis", "artifactId": "google-api-services-androidenterprise", "version": "${google-api-services.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.google.http-client", "artifactId": "google-http-client", "version": "${google-http-client.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.google.api-client", "artifactId": "google-api-client", "version": "${google-api-client.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "commons-fileupload", "artifactId": "commons-fileupload", "version": "${commons-fileupload.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "redis.clients", "artifactId": "jedis", "version": "${jedis.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.redisson", "artifactId": "redisson", "version": "${redisson.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.redisson", "artifactId": "redisson-spring-boot-starter", "version": "${redisson.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "<PERSON><PERSON>", "artifactId": "fst", "version": "${fst.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.esotericsoftware", "artifactId": "kryo", "version": "${kryo.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.apache.poi", "artifactId": "poi", "version": "${poi.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.apache.poi", "artifactId": "poi-ooxml", "version": "${ooxml.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "net.dongliu", "artifactId": "apk-parser", "version": "${apk-parser.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.springframework.security.oauth", "artifactId": "spring-security-oauth2", "version": "${spring-security-oauth2.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.springframework.security.kerberos", "artifactId": "spring-security-kerberos-web", "version": "${spring-security-kerberos-web.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.springframework.security", "artifactId": "spring-security-jwt", "version": "${spring-security-jwt.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.unboundid", "artifactId": "unboundid-ldapsdk", "version": "${unboundid.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.ldaptive", "artifactId": "ldaptive", "version": "${ldaptive.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.ldaptive", "artifactId": "ldaptive-unboundid", "version": "${ldaptive.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.apache.thrift", "artifactId": "libthrift", "version": "${thrift.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-test", "version": "${spring-boot.version}", "scope": "test", "file": "service-parent/pom.xml"}, {"groupId": "junit", "artifactId": "junit", "version": "${junit.version}", "scope": "test", "file": "service-parent/pom.xml"}, {"groupId": "org.junit.jupiter", "artifactId": "junit-jupiter-api", "version": "${junit-jupiter.version}", "scope": "test", "file": "service-parent/pom.xml"}, {"groupId": "org.junit.jupiter", "artifactId": "junit-jupiter-engine", "version": "${junit-jupiter.version}", "scope": "test", "file": "service-parent/pom.xml"}, {"groupId": "org.openjdk.jmh", "artifactId": "jmh-core", "version": "${jmh.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.openjdk.jmh", "artifactId": "jmh-generator-annprocess", "version": "${jmh.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "dds-springboot-starter", "version": "${project.parent.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "dds-common", "version": "${project.parent.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.baomidou", "artifactId": "mybatis-plus-boot-starter", "version": "${mybatis-plus.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.baomidou", "artifactId": "mybatis-plus-generator", "version": "${mybatis-plus.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.baomidou", "artifactId": "mybatis-plus-extension", "version": "${mybatis-plus.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "p6spy", "artifactId": "p6spy", "version": "${p6spy.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.messaginghub", "artifactId": "pooled-jms", "version": "${pooled-jms.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.aliyun.openservices", "artifactId": "ons-client", "version": "${ons-client.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.googlecode.plist", "artifactId": "dd-plist", "version": "${dd-plist.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.dd", "artifactId": "dd-plist", "version": "${dd.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "io.jsonwebtoken", "artifactId": "jjwt", "version": "${jjwt.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "io.jsonwebtoken", "artifactId": "jjwt-api", "version": "${jjwt-api.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "io.jsonwebtoken", "artifactId": "jjwt-impl", "version": "${jjwt-api.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "io.jsonwebtoken", "artifactId": "jjwt-jackson", "version": "${jjwt-api.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.bitbucket.b_c", "artifactId": "jose4j", "version": "${jose4j.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.nimbusds", "artifactId": "nimbus-jose-jwt", "version": "${nimbus-jose-jwt.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "net.minidev", "artifactId": "json-smart", "version": "${json-smart.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.apache.velocity", "artifactId": "velocity-engine-core", "version": "${velocity.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.aliyun.oss", "artifactId": "aliyun-sdk-oss", "version": "${aliyun-sdk-oss.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.alibaba", "artifactId": "taobao-sdk", "version": "${taobao-sdk.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.aliyun", "artifactId": "<PERSON><PERSON><PERSON>", "version": "${dingtalk-sdk.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.dingtalk.open", "artifactId": "app-stream-client", "version": "${app-stream-sdk.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.microsoft.ews-java-api", "artifactId": "ews-java-api", "version": "${ews-java-sdk.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.aliyun", "artifactId": "alibabacloud-dysmsapi20170525", "version": "${alibabacloud-dysmsapi.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.aliyun.api.gateway", "artifactId": "sdk-core-java", "version": "${aliyun.api.gateway.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.flywaydm8", "artifactId": "flyway-core-dm8", "version": "${flywaydm8.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.opensaml", "artifactId": "opensaml-core", "version": "${opensaml.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.opensaml", "artifactId": "opensaml-saml-impl", "version": "${opensaml.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "nl.basjes.parse.useragent", "artifactId": "yauaa", "version": "${useragent-parse.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.yubico", "artifactId": "webauthn-server-core", "version": "${webauthn-server-core.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.jayway.jsonpath", "artifactId": "json-path", "version": "${jsonpath.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.graalvm.js", "artifactId": "js", "version": "${graaljs.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.graalvm.js", "artifactId": "js-scriptengine", "version": "${graaljs.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.yaml", "artifactId": "snake<PERSON>l", "version": "${snakeyaml.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "commons-net", "artifactId": "commons-net", "version": "${commons-net.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.json", "artifactId": "json", "version": "${org.json.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.mongodb", "artifactId": "mongo-java-driver", "version": "${mongo-java-driver.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "org.bouncycastle", "artifactId": "bcpkix-jdk15on", "version": "1.69", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.belerweb", "artifactId": "pinyin4j", "version": "${com.pinyin4j.version}", "scope": "compile", "file": "service-parent/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "common", "version": "${project.parent.version}", "scope": "compile", "file": "proxy-meta/pom.xml"}, {"groupId": "org.slf4j", "artifactId": "slf4j-api", "version": null, "scope": "compile", "file": "proxy-meta/pom.xml"}, {"groupId": "org.redisson", "artifactId": "redisson-spring-boot-starter", "version": null, "scope": "compile", "file": "proxy-meta/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "common", "version": "${project.parent.version}", "scope": "compile", "file": "mdm/mdm-api/pom.xml"}, {"groupId": "org.mapstruct", "artifactId": "mapstruct", "version": null, "scope": "compile", "file": "mdm/mdm-api/pom.xml"}, {"groupId": "io.springfox", "artifactId": "springfox-swagger2", "version": null, "scope": "compile", "file": "mdm/mdm-api/pom.xml"}, {"groupId": "com.googlecode.plist", "artifactId": "dd-plist", "version": null, "scope": "compile", "file": "mdm/mdm-api/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter", "version": null, "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-web", "version": null, "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-log4j2", "version": null, "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-data-redis", "version": null, "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "redis.clients", "artifactId": "jedis", "version": null, "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-validation", "version": null, "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "org.springframework.cloud", "artifactId": "spring-cloud-starter-kubernetes-client-config", "version": null, "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "org.springframework.cloud", "artifactId": "spring-cloud-starter-consul-config", "version": null, "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "org.springframework.cloud", "artifactId": "spring-cloud-starter-consul-discovery", "version": null, "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "de.codecentric", "artifactId": "spring-boot-admin-starter-client", "version": null, "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-actuator", "version": null, "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "org.springframework.cloud", "artifactId": "spring-cloud-starter-openfeign", "version": null, "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "dds-springboot-starter", "version": null, "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "common", "version": "${project.parent.version}", "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "common-task", "version": "${project.parent.version}", "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "org.apache.commons", "artifactId": "commons-lang3", "version": null, "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "org.mapstruct", "artifactId": "mapstruct", "version": null, "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "io.springfox", "artifactId": "springfox-swagger2", "version": null, "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "com.baomidou", "artifactId": "mybatis-plus-boot-starter", "version": null, "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "com.baomidou", "artifactId": "mybatis-plus-generator", "version": null, "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "com.baomidou", "artifactId": "mybatis-plus-extension", "version": null, "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "p6spy", "artifactId": "p6spy", "version": null, "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "org.mybatis", "artifactId": "mybatis", "version": null, "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "org.apache.velocity", "artifactId": "velocity-engine-core", "version": null, "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-<PERSON><PERSON><PERSON><PERSON>", "version": null, "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "org.dom4j", "artifactId": "dom4j", "version": null, "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "com.thoughtworks.xstream", "artifactId": "xstream", "version": null, "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "com.fasterxml.jackson.dataformat", "artifactId": "jackson-dataformat-xml", "version": null, "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "com.github.ben-manes.caffeine", "artifactId": "caffeine", "version": null, "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "net.dongliu", "artifactId": "apk-parser", "version": null, "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "io.jsonwebtoken", "artifactId": "jjwt-api", "version": null, "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "io.jsonwebtoken", "artifactId": "jjwt-impl", "version": null, "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "io.jsonwebtoken", "artifactId": "jjwt-jackson", "version": null, "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "org.bitbucket.b_c", "artifactId": "jose4j", "version": null, "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "com.googlecode.plist", "artifactId": "dd-plist", "version": null, "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "mdm-api", "version": "${project.parent.version}", "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "config-client", "version": "${project.parent.version}", "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "message-client", "version": "${project.parent.version}", "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "jakarta.mail", "artifactId": "jakarta.mail-api", "version": null, "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "iam-api", "version": "${project.parent.version}", "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "uam-api", "version": "${project.parent.version}", "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "account-client", "version": "${project.parent.version}", "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "account-govdd", "version": "${project.parent.version}", "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "account-dingding", "version": "${project.parent.version}", "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "push-client", "version": "${project.parent.version}", "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "file-client", "version": "${project.parent.version}", "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "com.cyberscraft.emm", "artifactId": "sms", "version": "1.0", "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "com.google.code.jscep", "artifactId": "jscep", "version": "${jscep.version}", "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "com.aliyun.oss", "artifactId": "aliyun-sdk-oss", "version": null, "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "org.apache.poi", "artifactId": "poi", "version": null, "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "oauth2-client", "version": "${project.parent.version}", "scope": "compile", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-data-mongodb", "version": null, "scope": "provided", "file": "mdm/mdm-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "mdm-core", "version": "${project.parent.version}", "scope": "compile", "file": "mdm/mdm-service/pom.xml"}, {"groupId": "com.fasterxml.jackson.dataformat", "artifactId": "jackson-dataformat-xml", "version": null, "scope": "compile", "file": "mdm/mdm-service/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "push-client", "version": "${project.parent.version}", "scope": "compile", "file": "mdm/mdm-service/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "command-client", "version": "${project.parent.version}", "scope": "compile", "file": "mdm/mdm-service/pom.xml"}, {"groupId": "mysql", "artifactId": "mysql-connector-java", "version": null, "scope": "compile", "file": "mdm/mdm-service/pom.xml"}, {"groupId": "io.springfox", "artifactId": "springfox-swagger2", "version": null, "scope": "compile", "file": "mdm/mdm-service/pom.xml"}, {"groupId": "junit", "artifactId": "junit", "version": null, "scope": "test", "file": "mdm/mdm-service/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-test", "version": null, "scope": "test", "file": "mdm/mdm-service/pom.xml"}, {"groupId": "com.lmax", "artifactId": "disruptor", "version": null, "scope": "compile", "file": "mdm/mdm-service/pom.xml"}, {"groupId": "org.junit.jupiter", "artifactId": "junit-jupiter-api", "version": null, "scope": "test", "file": "mdm/mdm-service/pom.xml"}, {"groupId": "org.junit.jupiter", "artifactId": "junit-jupiter-engine", "version": null, "scope": "test", "file": "mdm/mdm-service/pom.xml"}, {"groupId": "io.netty", "artifactId": "netty-all", "version": null, "scope": "test", "file": "mdm/mdm-service/pom.xml"}, {"groupId": "io.netty", "artifactId": "netty-codec-dns", "version": null, "scope": "compile", "file": "mdm/mdm-service/pom.xml"}, {"groupId": "com.flywaydm8", "artifactId": "flyway-core-dm8", "version": null, "scope": "compile", "file": "mdm/mdm-service/pom.xml"}, {"groupId": "com.dameng", "artifactId": "DmJdbcDriver18", "version": null, "scope": "compile", "file": "mdm/mdm-service/pom.xml"}, {"groupId": "com.kingbase8", "artifactId": "kingbase8", "version": null, "scope": "compile", "file": "mdm/mdm-service/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "mdm-service", "version": "${project.parent.version}", "scope": "compile", "file": "mdm/mdm-workbench/pom.xml"}, {"groupId": "org.openjdk.jmh", "artifactId": "jmh-core", "version": null, "scope": "compile", "file": "mdm/mdm-workbench/pom.xml"}, {"groupId": "org.openjdk.jmh", "artifactId": "jmh-generator-annprocess", "version": null, "scope": "provided", "file": "mdm/mdm-workbench/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "ncm-core", "version": "${project.version}", "scope": "compile", "file": "ncm/ncm-service/pom.xml"}, {"groupId": "mysql", "artifactId": "mysql-connector-java", "version": null, "scope": "compile", "file": "ncm/ncm-service/pom.xml"}, {"groupId": "io.springfox", "artifactId": "springfox-swagger2", "version": null, "scope": "compile", "file": "ncm/ncm-service/pom.xml"}, {"groupId": "com.flywaydm8", "artifactId": "flyway-core-dm8", "version": null, "scope": "compile", "file": "ncm/ncm-service/pom.xml"}, {"groupId": "com.dameng", "artifactId": "DmJdbcDriver18", "version": null, "scope": "compile", "file": "ncm/ncm-service/pom.xml"}, {"groupId": "com.kingbase8", "artifactId": "kingbase8", "version": null, "scope": "compile", "file": "ncm/ncm-service/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-test", "version": null, "scope": "test", "file": "ncm/ncm-service/pom.xml"}, {"groupId": "io.netty", "artifactId": "netty-all", "version": null, "scope": "test", "file": "ncm/ncm-service/pom.xml"}, {"groupId": "io.netty", "artifactId": "netty-codec-dns", "version": null, "scope": "compile", "file": "ncm/ncm-service/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "common", "version": "${project.parent.version}", "scope": "compile", "file": "ncm/ncm-api/pom.xml"}, {"groupId": "org.mapstruct", "artifactId": "mapstruct", "version": null, "scope": "compile", "file": "ncm/ncm-api/pom.xml"}, {"groupId": "io.springfox", "artifactId": "springfox-swagger2", "version": null, "scope": "compile", "file": "ncm/ncm-api/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "dds-springboot-starter", "version": null, "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "message-client", "version": "${project.version}", "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "common", "version": "${project.version}", "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "iam-api", "version": "${project.version}", "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "ncm-api", "version": "${project.version}", "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "ncm-client", "version": "${project.version}", "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "account-client", "version": "${project.version}", "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "account-dingding", "version": "${project.version}", "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "account-feishu", "version": "${project.version}", "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "aksk-server", "version": "${project.version}", "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "common-task", "version": "${project.parent.version}", "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter", "version": null, "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-web", "version": null, "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-log4j2", "version": null, "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-data-redis", "version": null, "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "redis.clients", "artifactId": "jedis", "version": null, "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "org.redisson", "artifactId": "redisson", "version": null, "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "org.redisson", "artifactId": "redisson-spring-boot-starter", "version": null, "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-validation", "version": null, "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "org.springframework.cloud", "artifactId": "spring-cloud-starter-kubernetes-client-config", "version": null, "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "org.springframework.cloud", "artifactId": "spring-cloud-starter-consul-config", "version": null, "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "org.springframework.cloud", "artifactId": "spring-cloud-starter-consul-discovery", "version": null, "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "de.codecentric", "artifactId": "spring-boot-admin-starter-client", "version": null, "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-actuator", "version": null, "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "org.springframework.cloud", "artifactId": "spring-cloud-starter-openfeign", "version": null, "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-test", "version": null, "scope": "test", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "org.springframework.security.oauth", "artifactId": "spring-security-oauth2", "version": "${spring-security-oauth2.version}", "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "org.springframework.security", "artifactId": "spring-security-oauth2-client", "version": null, "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "org.springframework.security", "artifactId": "spring-security-oauth2-jose", "version": null, "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "org.springframework.security", "artifactId": "spring-security-oauth2-resource-server", "version": null, "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "com.lmax", "artifactId": "disruptor", "version": null, "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "org.apache.commons", "artifactId": "commons-lang3", "version": null, "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "org.mapstruct", "artifactId": "mapstruct", "version": null, "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "io.springfox", "artifactId": "springfox-swagger2", "version": null, "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "com.baomidou", "artifactId": "mybatis-plus-boot-starter", "version": null, "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "com.baomidou", "artifactId": "mybatis-plus-generator", "version": null, "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "com.baomidou", "artifactId": "mybatis-plus-extension", "version": null, "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "p6spy", "artifactId": "p6spy", "version": null, "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "org.mybatis", "artifactId": "mybatis", "version": null, "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "org.apache.velocity", "artifactId": "velocity-engine-core", "version": null, "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-<PERSON><PERSON><PERSON><PERSON>", "version": null, "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "io.jsonwebtoken", "artifactId": "jjwt", "version": null, "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "io.jsonwebtoken", "artifactId": "jjwt-api", "version": null, "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "io.jsonwebtoken", "artifactId": "jjwt-impl", "version": null, "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "io.jsonwebtoken", "artifactId": "jjwt-jackson", "version": null, "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "org.bitbucket.b_c", "artifactId": "jose4j", "version": null, "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "io.github.openfeign", "artifactId": "feign-httpclient", "version": null, "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "jakarta.mail", "artifactId": "jakarta.mail-api", "version": null, "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "com.sun.mail", "artifactId": "jakarta.mail", "version": null, "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "com.aliyun", "artifactId": "alibabacloud-dysmsapi20170525", "version": null, "scope": "compile", "file": "ncm/ncm-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "common", "version": "${project.parent.version}", "scope": "compile", "file": "ncm/ncm-client/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "ncm-api", "version": "${project.parent.version}", "scope": "compile", "file": "ncm/ncm-client/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "message-client", "version": "${project.parent.version}", "scope": "compile", "file": "ncm/ncm-client/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-test", "version": null, "scope": "test", "file": "iam/iam-notify-service/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-web", "version": null, "scope": "compile", "file": "iam/iam-notify-service/pom.xml"}, {"groupId": "org.springframework.cloud", "artifactId": "spring-cloud-starter-kubernetes-client-config", "version": null, "scope": "compile", "file": "iam/iam-notify-service/pom.xml"}, {"groupId": "org.springframework.cloud", "artifactId": "spring-cloud-starter-consul-config", "version": null, "scope": "compile", "file": "iam/iam-notify-service/pom.xml"}, {"groupId": "org.springframework.cloud", "artifactId": "spring-cloud-starter-consul-discovery", "version": null, "scope": "compile", "file": "iam/iam-notify-service/pom.xml"}, {"groupId": "de.codecentric", "artifactId": "spring-boot-admin-starter-client", "version": null, "scope": "compile", "file": "iam/iam-notify-service/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-actuator", "version": null, "scope": "compile", "file": "iam/iam-notify-service/pom.xml"}, {"groupId": "io.springfox", "artifactId": "springfox-swagger2", "version": null, "scope": "compile", "file": "iam/iam-notify-service/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-autoconfigure", "version": null, "scope": "compile", "file": "iam/iam-notify-service/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-actuator-autoconfigure", "version": null, "scope": "compile", "file": "iam/iam-notify-service/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "iam-core", "version": "${project.parent.version}", "scope": "compile", "file": "iam/iam-notify-service/pom.xml"}, {"groupId": "org.graalvm.js", "artifactId": "js", "version": null, "scope": "compile", "file": "iam/iam-notify-service/pom.xml"}, {"groupId": "org.graalvm.js", "artifactId": "js-scriptengine", "version": null, "scope": "compile", "file": "iam/iam-notify-service/pom.xml"}, {"groupId": "com.ibm.db2", "artifactId": "jcc", "version": null, "scope": "compile", "file": "iam/iam-notify-service/pom.xml"}, {"groupId": "com.oracle.database.jdbc", "artifactId": "ojdbc8", "version": null, "scope": "compile", "file": "iam/iam-notify-service/pom.xml"}, {"groupId": "mysql", "artifactId": "mysql-connector-java", "version": null, "scope": "compile", "file": "iam/iam-notify-service/pom.xml"}, {"groupId": "com.microsoft.sqlserver", "artifactId": "mssql-jdbc", "version": null, "scope": "compile", "file": "iam/iam-notify-service/pom.xml"}, {"groupId": "com.dameng", "artifactId": "DmJdbcDriver18", "version": null, "scope": "compile", "file": "iam/iam-notify-service/pom.xml"}, {"groupId": "com.kingbase8", "artifactId": "kingbase8", "version": null, "scope": "compile", "file": "iam/iam-notify-service/pom.xml"}, {"groupId": "com.flywaydm8", "artifactId": "flyway-core-dm8", "version": null, "scope": "compile", "file": "iam/iam-notify-service/pom.xml"}, {"groupId": "org.springframework.cloud", "artifactId": "spring-cloud-starter-openfeign", "version": null, "scope": "compile", "file": "iam/iam-api/pom.xml"}, {"groupId": "org.springframework.cloud", "artifactId": "spring-cloud-openfeign-core", "version": null, "scope": "compile", "file": "iam/iam-api/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "common", "version": null, "scope": "compile", "file": "iam/iam-api/pom.xml"}, {"groupId": "org.springframework.data", "artifactId": "spring-data-commons", "version": null, "scope": "compile", "file": "iam/iam-api/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-test", "version": null, "scope": "test", "file": "iam/iam-core/pom.xml"}, {"groupId": "com.github.ben-manes.caffeine", "artifactId": "caffeine", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "org.apache.poi", "artifactId": "poi", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "org.apache.poi", "artifactId": "poi-ooxml", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "org.mybatis.spring.boot", "artifactId": "mybatis-spring-boot-starter", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "p6spy", "artifactId": "p6spy", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "com.yubico", "artifactId": "webauthn-server-core", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-log4j2", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-autoconfigure", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-actuator-autoconfigure", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-<PERSON><PERSON><PERSON><PERSON>", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "org.aspectj", "artifactId": "<PERSON><PERSON><PERSON><PERSON>", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "org.springframework", "artifactId": "spring-web", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "org.springframework.data", "artifactId": "spring-data-commons", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "org.springframework", "artifactId": "spring-context-support", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "org.apache.httpcomponents", "artifactId": "httpcore", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "com.google.code.gson", "artifactId": "gson", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "org.apache.commons", "artifactId": "commons-lang3", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "org.apache.commons", "artifactId": "commons-pool2", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "com.lmax", "artifactId": "disruptor", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "org.mapstruct", "artifactId": "mapstruct", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "org.assertj", "artifactId": "assertj-core", "version": "3.12.2", "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "javax.validation", "artifactId": "validation-api", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "jakarta.mail", "artifactId": "jakarta.mail-api", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "com.sun.mail", "artifactId": "jakarta.mail", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "org.zeromq", "artifactId": "j<PERSON><PERSON><PERSON>", "version": "0.5.1", "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "io.jsonwebtoken", "artifactId": "jjwt", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "org.springframework.security", "artifactId": "spring-security-web", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "org.springframework.security", "artifactId": "spring-security-jwt", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "com.nimbusds", "artifactId": "nimbus-jose-jwt", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "org.bitbucket.b_c", "artifactId": "jose4j", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "org.springframework.session", "artifactId": "spring-session-data-redis", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "org.springframework.security.oauth", "artifactId": "spring-security-oauth2", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "org.springframework.security.kerberos", "artifactId": "spring-security-kerberos-web", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "com.fasterxml.jackson.core", "artifactId": "jackson-databind", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "io.springfox", "artifactId": "springfox-swagger2", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "com.fasterxml.jackson.datatype", "artifactId": "jackson-datatype-jdk8", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "com.fasterxml.jackson.datatype", "artifactId": "jackson-datatype-jsr310", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "com.fasterxml.jackson.module", "artifactId": "jackson-module-parameter-names", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "com.baomidou", "artifactId": "mybatis-plus-generator", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "com.baomidou", "artifactId": "mybatis-plus-boot-starter", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "org.apache.velocity", "artifactId": "velocity-engine-core", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "org.redisson", "artifactId": "redisson", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "org.redisson", "artifactId": "redisson-spring-boot-starter", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "com.querydsl", "artifactId": "querydsl-core", "version": "4.2.1", "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "org.antlr", "artifactId": "antlr4-runtime", "version": "4.7.2", "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "com.maxmind.geoip2", "artifactId": "geoip2", "version": "2.10.0", "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "net.sourceforge.jexcelapi", "artifactId": "jxl", "version": "2.6.12", "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "net.sourceforge.jchardet", "artifactId": "j<PERSON>et", "version": "1.0", "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "com.aliyun", "artifactId": "<PERSON><PERSON><PERSON>", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "dds-springboot-starter", "version": "${project.parent.version}", "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "message-client", "version": "${project.parent.version}", "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "config-client", "version": "${project.parent.version}", "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "account-client", "version": "${project.parent.version}", "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "account-wework", "version": "${project.parent.version}", "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "account-dingding", "version": "${project.parent.version}", "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "account-feishu", "version": "${project.parent.version}", "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "account-ad", "version": "${project.parent.version}", "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "common-task", "version": "${project.parent.version}", "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "account-etl", "version": "${project.parent.version}", "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "iam-api", "version": "${project.parent.version}", "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "acm-api", "version": "${project.parent.version}", "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "ncm-client", "version": "${project.parent.version}", "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "proxy-meta", "version": "${project.parent.version}", "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "org.springframework.ldap", "artifactId": "spring-ldap-core", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "org.opensaml", "artifactId": "opensaml-core", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "org.opensaml", "artifactId": "opensaml-saml-impl", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "com.thoughtworks.xstream", "artifactId": "xstream", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "com.aliyun.api.gateway", "artifactId": "sdk-core-java", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "com.kingbase8", "artifactId": "kingbase8", "version": null, "scope": "compile", "file": "iam/iam-core/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-test", "version": null, "scope": "test", "file": "iam/iam-service/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-web", "version": null, "scope": "compile", "file": "iam/iam-service/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-<PERSON><PERSON><PERSON><PERSON>", "version": null, "scope": "compile", "file": "iam/iam-service/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-mail", "version": null, "scope": "compile", "file": "iam/iam-service/pom.xml"}, {"groupId": "org.springframework.cloud", "artifactId": "spring-cloud-starter-kubernetes-client-config", "version": null, "scope": "compile", "file": "iam/iam-service/pom.xml"}, {"groupId": "org.springframework.cloud", "artifactId": "spring-cloud-starter-consul-config", "version": null, "scope": "compile", "file": "iam/iam-service/pom.xml"}, {"groupId": "org.springframework.cloud", "artifactId": "spring-cloud-starter-consul-discovery", "version": null, "scope": "compile", "file": "iam/iam-service/pom.xml"}, {"groupId": "de.codecentric", "artifactId": "spring-boot-admin-starter-client", "version": null, "scope": "compile", "file": "iam/iam-service/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-actuator", "version": null, "scope": "compile", "file": "iam/iam-service/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "iam-core", "version": "${project.parent.version}", "scope": "compile", "file": "iam/iam-service/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "aksk-server", "version": "${project.parent.version}", "scope": "compile", "file": "iam/iam-service/pom.xml"}, {"groupId": "org.graalvm.js", "artifactId": "js", "version": null, "scope": "compile", "file": "iam/iam-service/pom.xml"}, {"groupId": "org.graalvm.js", "artifactId": "js-scriptengine", "version": null, "scope": "compile", "file": "iam/iam-service/pom.xml"}, {"groupId": "com.ibm.db2", "artifactId": "jcc", "version": null, "scope": "compile", "file": "iam/iam-service/pom.xml"}, {"groupId": "com.oracle.database.jdbc", "artifactId": "ojdbc8", "version": null, "scope": "compile", "file": "iam/iam-service/pom.xml"}, {"groupId": "mysql", "artifactId": "mysql-connector-java", "version": null, "scope": "compile", "file": "iam/iam-service/pom.xml"}, {"groupId": "com.microsoft.sqlserver", "artifactId": "mssql-jdbc", "version": null, "scope": "compile", "file": "iam/iam-service/pom.xml"}, {"groupId": "com.flywaydm8", "artifactId": "flyway-core-dm8", "version": null, "scope": "compile", "file": "iam/iam-service/pom.xml"}, {"groupId": "com.dameng", "artifactId": "DmJdbcDriver18", "version": null, "scope": "compile", "file": "iam/iam-service/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter", "version": null, "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-web", "version": null, "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-log4j2", "version": null, "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "ncm-client", "version": "${project.parent.version}", "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "shorturl-api", "version": "${project.parent.version}", "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "uam-api", "version": "${project.parent.version}", "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "common-task", "version": "${project.parent.version}", "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-data-redis", "version": null, "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "org.redisson", "artifactId": "redisson", "version": null, "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "org.redisson", "artifactId": "redisson-spring-boot-starter", "version": null, "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-validation", "version": null, "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "org.springframework.cloud", "artifactId": "spring-cloud-starter-kubernetes-client-config", "version": null, "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "org.springframework.cloud", "artifactId": "spring-cloud-starter-consul-config", "version": null, "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "org.springframework.cloud", "artifactId": "spring-cloud-starter-consul-discovery", "version": null, "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "de.codecentric", "artifactId": "spring-boot-admin-starter-client", "version": null, "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-actuator", "version": null, "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "org.springframework.cloud", "artifactId": "spring-cloud-starter-openfeign", "version": null, "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "io.github.openfeign", "artifactId": "feign-httpclient", "version": null, "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "dds-springboot-starter", "version": null, "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "org.apache.commons", "artifactId": "commons-lang3", "version": null, "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "org.mapstruct", "artifactId": "mapstruct", "version": null, "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "io.springfox", "artifactId": "springfox-swagger2", "version": null, "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "com.baomidou", "artifactId": "mybatis-plus-boot-starter", "version": null, "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "com.baomidou", "artifactId": "mybatis-plus-generator", "version": null, "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "com.baomidou", "artifactId": "mybatis-plus-extension", "version": null, "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "p6spy", "artifactId": "p6spy", "version": null, "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "org.mybatis", "artifactId": "mybatis", "version": null, "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "org.apache.velocity", "artifactId": "velocity-engine-core", "version": null, "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-<PERSON><PERSON><PERSON><PERSON>", "version": null, "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "org.dom4j", "artifactId": "dom4j", "version": null, "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "com.thoughtworks.xstream", "artifactId": "xstream", "version": null, "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "com.fasterxml.jackson.dataformat", "artifactId": "jackson-dataformat-xml", "version": null, "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "com.github.ben-manes.caffeine", "artifactId": "caffeine", "version": null, "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "net.dongliu", "artifactId": "apk-parser", "version": null, "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "io.jsonwebtoken", "artifactId": "jjwt-api", "version": null, "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "io.jsonwebtoken", "artifactId": "jjwt-impl", "version": null, "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "io.jsonwebtoken", "artifactId": "jjwt-jackson", "version": null, "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "org.bitbucket.b_c", "artifactId": "jose4j", "version": null, "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "com.googlecode.plist", "artifactId": "dd-plist", "version": null, "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "adm-api", "version": "${project.parent.version}", "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "jakarta.mail", "artifactId": "jakarta.mail-api", "version": null, "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "com.aliyun.oss", "artifactId": "aliyun-sdk-oss", "version": null, "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "org.apache.poi", "artifactId": "poi", "version": null, "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "file-client", "version": "${project.parent.version}", "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "com.cyberscraft.emm", "artifactId": "sms", "version": "1.0", "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "com.alibaba", "artifactId": "taobao-sdk", "version": null, "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "org.springframework.ldap", "artifactId": "spring-ldap-core", "version": null, "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "com.cyberscraft.mdm.app", "artifactId": "parseipa", "version": "1.0", "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "jaxen", "artifactId": "jaxen", "version": "${jaxen.version}", "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "account-client", "version": "${project.parent.version}", "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "oauth2-client", "version": "${project.parent.version}", "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "iam-api", "version": "${project.parent.version}", "scope": "compile", "file": "adm/adm-core/pom.xml"}, {"groupId": "org.mapstruct", "artifactId": "mapstruct", "version": null, "scope": "compile", "file": "adm/adm-api/pom.xml"}, {"groupId": "io.springfox", "artifactId": "springfox-swagger2", "version": null, "scope": "compile", "file": "adm/adm-api/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "common", "version": "${project.parent.version}", "scope": "compile", "file": "adm/adm-api/pom.xml"}, {"groupId": "org.springframework.cloud", "artifactId": "spring-cloud-starter-openfeign", "version": null, "scope": "compile", "file": "adm/adm-api/pom.xml"}, {"groupId": "org.springframework.cloud", "artifactId": "spring-cloud-openfeign-core", "version": null, "scope": "compile", "file": "adm/adm-api/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "adm-core", "version": "${project.parent.version}", "scope": "compile", "file": "adm/adm-service/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "shorturl-core", "version": "${project.parent.version}", "scope": "compile", "file": "adm/adm-service/pom.xml"}, {"groupId": "com.fasterxml.jackson.dataformat", "artifactId": "jackson-dataformat-xml", "version": null, "scope": "compile", "file": "adm/adm-service/pom.xml"}, {"groupId": "mysql", "artifactId": "mysql-connector-java", "version": null, "scope": "compile", "file": "adm/adm-service/pom.xml"}, {"groupId": "com.dameng", "artifactId": "DmJdbcDriver18", "version": null, "scope": "compile", "file": "adm/adm-service/pom.xml"}, {"groupId": "com.kingbase8", "artifactId": "kingbase8", "version": null, "scope": "compile", "file": "adm/adm-service/pom.xml"}, {"groupId": "io.springfox", "artifactId": "springfox-swagger2", "version": null, "scope": "compile", "file": "adm/adm-service/pom.xml"}, {"groupId": "junit", "artifactId": "junit", "version": null, "scope": "test", "file": "adm/adm-service/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-test", "version": null, "scope": "test", "file": "adm/adm-service/pom.xml"}, {"groupId": "com.lmax", "artifactId": "disruptor", "version": null, "scope": "compile", "file": "adm/adm-service/pom.xml"}, {"groupId": "org.junit.jupiter", "artifactId": "junit-jupiter-api", "version": null, "scope": "test", "file": "adm/adm-service/pom.xml"}, {"groupId": "org.junit.jupiter", "artifactId": "junit-jupiter-engine", "version": null, "scope": "test", "file": "adm/adm-service/pom.xml"}, {"groupId": "io.netty", "artifactId": "netty-all", "version": null, "scope": "test", "file": "adm/adm-service/pom.xml"}, {"groupId": "io.netty", "artifactId": "netty-codec-dns", "version": null, "scope": "compile", "file": "adm/adm-service/pom.xml"}, {"groupId": "com.google.code.gson", "artifactId": "gson", "version": null, "scope": "compile", "file": "adm/adm-service/pom.xml"}, {"groupId": "com.flywaydm8", "artifactId": "flyway-core-dm8", "version": null, "scope": "compile", "file": "adm/adm-service/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-test", "version": null, "scope": "test", "file": "uam/uam-service/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-web", "version": null, "scope": "compile", "file": "uam/uam-service/pom.xml"}, {"groupId": "org.springframework.cloud", "artifactId": "spring-cloud-starter-kubernetes-client-config", "version": null, "scope": "compile", "file": "uam/uam-service/pom.xml"}, {"groupId": "org.springframework.cloud", "artifactId": "spring-cloud-starter-consul-config", "version": null, "scope": "compile", "file": "uam/uam-service/pom.xml"}, {"groupId": "org.springframework.cloud", "artifactId": "spring-cloud-starter-consul-discovery", "version": null, "scope": "compile", "file": "uam/uam-service/pom.xml"}, {"groupId": "de.codecentric", "artifactId": "spring-boot-admin-starter-client", "version": null, "scope": "compile", "file": "uam/uam-service/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-actuator", "version": null, "scope": "compile", "file": "uam/uam-service/pom.xml"}, {"groupId": "io.springfox", "artifactId": "springfox-swagger2", "version": null, "scope": "compile", "file": "uam/uam-service/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-autoconfigure", "version": null, "scope": "compile", "file": "uam/uam-service/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-actuator-autoconfigure", "version": null, "scope": "compile", "file": "uam/uam-service/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "uam-api", "version": "${project.version}", "scope": "compile", "file": "uam/uam-service/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "uam-core", "version": "${project.version}", "scope": "compile", "file": "uam/uam-service/pom.xml"}, {"groupId": "mysql", "artifactId": "mysql-connector-java", "version": null, "scope": "compile", "file": "uam/uam-service/pom.xml"}, {"groupId": "com.dameng", "artifactId": "DmJdbcDriver18", "version": null, "scope": "compile", "file": "uam/uam-service/pom.xml"}, {"groupId": "com.kingbase8", "artifactId": "kingbase8", "version": null, "scope": "compile", "file": "uam/uam-service/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "uam-api", "version": "${project.version}", "scope": "compile", "file": "uam/uam-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "iam-core", "version": "${project.parent.version}", "scope": "compile", "file": "uam/uam-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "common-task", "version": "${project.parent.version}", "scope": "compile", "file": "uam/uam-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "common", "version": "${project.parent.version}", "scope": "compile", "file": "uam/uam-api/pom.xml"}, {"groupId": "org.mapstruct", "artifactId": "mapstruct", "version": null, "scope": "compile", "file": "uam/uam-api/pom.xml"}, {"groupId": "io.springfox", "artifactId": "springfox-swagger2", "version": null, "scope": "compile", "file": "uam/uam-api/pom.xml"}, {"groupId": "org.apache.commons", "artifactId": "commons-lang3", "version": null, "scope": "compile", "file": "base/push-client/pom.xml"}, {"groupId": "com.alibaba.platform.shared", "artifactId": "xxpt.gateway.shared.client", "version": null, "scope": "compile", "file": "base/push-client/pom.xml"}, {"groupId": "org.apache.httpcomponents", "artifactId": "httpclient", "version": null, "scope": "compile", "file": "base/push-client/pom.xml"}, {"groupId": "joda-time", "artifactId": "joda-time", "version": null, "scope": "compile", "file": "base/push-client/pom.xml"}, {"groupId": "org.mapstruct", "artifactId": "mapstruct", "version": null, "scope": "compile", "file": "base/push-client/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "config-client", "version": "${project.parent.version}", "scope": "compile", "file": "base/push-client/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "message-client", "version": "${project.parent.version}", "scope": "compile", "file": "base/push-client/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "account-client", "version": "${project.parent.version}", "scope": "compile", "file": "base/push-client/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "account-govdd", "version": "${project.parent.version}", "scope": "compile", "file": "base/push-client/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "common", "version": "${project.parent.version}", "scope": "compile", "file": "base/push-client/pom.xml"}, {"groupId": "io.netty", "artifactId": "netty-tcnative-boringssl-static", "version": null, "scope": "compile", "file": "base/push-client/pom.xml"}, {"groupId": "com.turo", "artifactId": "pushy", "version": null, "scope": "compile", "file": "base/push-client/pom.xml"}, {"groupId": "com.baomidou", "artifactId": "mybatis-plus-boot-starter", "version": null, "scope": "compile", "file": "base/push-client/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-test", "version": null, "scope": "test", "file": "base/push-client/pom.xml"}, {"groupId": "org.springframework", "artifactId": "spring-test", "version": null, "scope": "test", "file": "base/push-client/pom.xml"}, {"groupId": "org.junit.jupiter", "artifactId": "junit-jupiter-api", "version": null, "scope": "test", "file": "base/push-client/pom.xml"}, {"groupId": "junit", "artifactId": "junit", "version": null, "scope": "test", "file": "base/push-client/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "common", "version": "${project.parent.version}", "scope": "compile", "file": "base/account-dingding/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "account-client", "version": "${project.parent.version}", "scope": "compile", "file": "base/account-dingding/pom.xml"}, {"groupId": "org.redisson", "artifactId": "redisson-spring-boot-starter", "version": null, "scope": "compile", "file": "base/account-dingding/pom.xml"}, {"groupId": "com.alibaba", "artifactId": "taobao-sdk", "version": null, "scope": "compile", "file": "base/account-dingding/pom.xml"}, {"groupId": "com.aliyun", "artifactId": "<PERSON><PERSON><PERSON>", "version": null, "scope": "compile", "file": "base/account-dingding/pom.xml"}, {"groupId": "org.mapstruct", "artifactId": "mapstruct", "version": null, "scope": "compile", "file": "base/account-dingding/pom.xml"}, {"groupId": "com.alibaba.platform.shared", "artifactId": "xxpt.gateway.shared.client", "version": null, "scope": "compile", "file": "base/account-govdd/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "common", "version": "${project.parent.version}", "scope": "compile", "file": "base/account-govdd/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "account-client", "version": "${project.parent.version}", "scope": "compile", "file": "base/account-govdd/pom.xml"}, {"groupId": "org.apache.commons", "artifactId": "commons-lang3", "version": null, "scope": "compile", "file": "base/account-client/pom.xml"}, {"groupId": "org.apache.httpcomponents", "artifactId": "httpclient", "version": null, "scope": "compile", "file": "base/account-client/pom.xml"}, {"groupId": "joda-time", "artifactId": "joda-time", "version": null, "scope": "compile", "file": "base/account-client/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter", "version": null, "scope": "compile", "file": "base/account-client/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-autoconfigure", "version": null, "scope": "compile", "file": "base/account-client/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-data-redis", "version": null, "scope": "compile", "file": "base/account-client/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-actuator", "version": null, "scope": "compile", "file": "base/account-client/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-configuration-processor", "version": null, "scope": "compile", "file": "base/account-client/pom.xml"}, {"groupId": "org.springframework", "artifactId": "spring-web", "version": null, "scope": "compile", "file": "base/account-client/pom.xml"}, {"groupId": "org.springframework", "artifactId": "spring-context", "version": null, "scope": "compile", "file": "base/account-client/pom.xml"}, {"groupId": "org.springframework.cloud", "artifactId": "spring-cloud-context", "version": null, "scope": "compile", "file": "base/account-client/pom.xml"}, {"groupId": "org.slf4j", "artifactId": "slf4j-api", "version": null, "scope": "compile", "file": "base/account-client/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "common", "version": "${project.parent.version}", "scope": "compile", "file": "base/account-client/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "proxy-meta", "version": "${project.parent.version}", "scope": "compile", "file": "base/account-client/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "dds-springboot-starter", "version": "${project.parent.version}", "scope": "compile", "file": "base/account-client/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "config-client", "version": "${project.parent.version}", "scope": "compile", "file": "base/account-client/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "dds-common", "version": null, "scope": "compile", "file": "base/account-client/pom.xml"}, {"groupId": "com.nimbusds", "artifactId": "nimbus-jose-jwt", "version": null, "scope": "compile", "file": "base/account-client/pom.xml"}, {"groupId": "org.apache.commons", "artifactId": "commons-lang3", "version": null, "scope": "compile", "file": "base/config-client/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter", "version": null, "scope": "compile", "file": "base/config-client/pom.xml"}, {"groupId": "org.springframework", "artifactId": "spring-web", "version": null, "scope": "compile", "file": "base/config-client/pom.xml"}, {"groupId": "org.springframework.cloud", "artifactId": "spring-cloud-context", "version": null, "scope": "compile", "file": "base/config-client/pom.xml"}, {"groupId": "org.slf4j", "artifactId": "slf4j-api", "version": null, "scope": "compile", "file": "base/config-client/pom.xml"}, {"groupId": "org.slf4j", "artifactId": "slf4j-api", "version": null, "scope": "compile", "file": "base/common-task/pom.xml"}, {"groupId": "org.redisson", "artifactId": "redisson", "version": null, "scope": "compile", "file": "base/common-task/pom.xml"}, {"groupId": "org.apache.commons", "artifactId": "commons-lang3", "version": null, "scope": "compile", "file": "base/common-task/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "common", "version": "${project.parent.version}", "scope": "compile", "file": "base/account-ad/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "account-client", "version": "${project.parent.version}", "scope": "compile", "file": "base/account-ad/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "proxy-meta", "version": "${project.parent.version}", "scope": "compile", "file": "base/account-ad/pom.xml"}, {"groupId": "com.unboundid", "artifactId": "unboundid-ldapsdk", "version": null, "scope": "compile", "file": "base/account-ad/pom.xml"}, {"groupId": "org.ldaptive", "artifactId": "ldaptive", "version": null, "scope": "compile", "file": "base/account-ad/pom.xml"}, {"groupId": "org.ldaptive", "artifactId": "ldaptive-unboundid", "version": null, "scope": "compile", "file": "base/account-ad/pom.xml"}, {"groupId": "org.redisson", "artifactId": "redisson-spring-boot-starter", "version": null, "scope": "compile", "file": "base/account-ad/pom.xml"}, {"groupId": "org.apache.commons", "artifactId": "commons-lang3", "version": null, "scope": "compile", "file": "base/common/pom.xml"}, {"groupId": "org.apache.httpcomponents", "artifactId": "httpclient", "version": null, "scope": "compile", "file": "base/common/pom.xml"}, {"groupId": "org.apache.httpcomponents", "artifactId": "httpcore", "version": null, "scope": "compile", "file": "base/common/pom.xml"}, {"groupId": "org.apache.httpcomponents", "artifactId": "httpmime", "version": null, "scope": "compile", "file": "base/common/pom.xml"}, {"groupId": "org.jsoup", "artifactId": "jsoup", "version": "1.14.3", "scope": "compile", "file": "base/common/pom.xml"}, {"groupId": "com.aliyun", "artifactId": "ocr_api20210707", "version": null, "scope": "compile", "file": "base/common/pom.xml"}, {"groupId": "com.alibaba", "artifactId": "<PERSON><PERSON><PERSON>", "version": null, "scope": "compile", "file": "base/common/pom.xml"}, {"groupId": "com.fasterxml.jackson.core", "artifactId": "jackson-core", "version": null, "scope": "provided", "file": "base/common/pom.xml"}, {"groupId": "com.fasterxml.jackson.core", "artifactId": "jackson-databind", "version": null, "scope": "provided", "file": "base/common/pom.xml"}, {"groupId": "com.fasterxml.jackson.core", "artifactId": "jackson-annotations", "version": null, "scope": "provided", "file": "base/common/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-web", "version": null, "scope": "compile", "file": "base/common/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-validation", "version": null, "scope": "compile", "file": "base/common/pom.xml"}, {"groupId": "com.baomidou", "artifactId": "mybatis-plus-boot-starter", "version": null, "scope": "provided", "file": "base/common/pom.xml"}, {"groupId": "io.springfox", "artifactId": "springfox-swagger2", "version": null, "scope": "compile", "file": "base/common/pom.xml"}, {"groupId": "org.apache.commons", "artifactId": "commons-compress", "version": null, "scope": "compile", "file": "base/common/pom.xml"}, {"groupId": "org.springframework.cloud", "artifactId": "spring-cloud-commons", "version": null, "scope": "compile", "file": "base/common/pom.xml"}, {"groupId": "com.google.zxing", "artifactId": "core", "version": null, "scope": "compile", "file": "base/common/pom.xml"}, {"groupId": "com.google.zxing", "artifactId": "javase", "version": null, "scope": "compile", "file": "base/common/pom.xml"}, {"groupId": "io.github.openfeign", "artifactId": "feign-okhttp", "version": null, "scope": "compile", "file": "base/common/pom.xml"}, {"groupId": "org.springframework.cloud", "artifactId": "spring-cloud-context", "version": null, "scope": "compile", "file": "base/common/pom.xml"}, {"groupId": "commons-io", "artifactId": "commons-io", "version": null, "scope": "compile", "file": "base/common/pom.xml"}, {"groupId": "org.springframework.cloud", "artifactId": "spring-cloud-openfeign-core", "version": null, "scope": "compile", "file": "base/common/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-data-redis", "version": null, "scope": "compile", "file": "base/common/pom.xml"}, {"groupId": "<PERSON><PERSON>", "artifactId": "fst", "version": null, "scope": "compile", "file": "base/common/pom.xml"}, {"groupId": "com.esotericsoftware", "artifactId": "kryo", "version": null, "scope": "compile", "file": "base/common/pom.xml"}, {"groupId": "com.thoughtworks.xstream", "artifactId": "xstream", "version": null, "scope": "compile", "file": "base/common/pom.xml"}, {"groupId": "nl.basjes.parse.useragent", "artifactId": "yauaa", "version": null, "scope": "compile", "file": "base/common/pom.xml"}, {"groupId": "com.jayway.jsonpath", "artifactId": "json-path", "version": null, "scope": "compile", "file": "base/common/pom.xml"}, {"groupId": "org.graalvm.js", "artifactId": "js", "version": null, "scope": "provided", "file": "base/common/pom.xml"}, {"groupId": "org.graalvm.js", "artifactId": "js-scriptengine", "version": null, "scope": "provided", "file": "base/common/pom.xml"}, {"groupId": "com.github.jsqlparser", "artifactId": "jsqlparser", "version": "4.0", "scope": "compile", "file": "base/common/pom.xml"}, {"groupId": "org.json", "artifactId": "json", "version": null, "scope": "compile", "file": "base/common/pom.xml"}, {"groupId": "com.healthmarketscience.jackcess", "artifactId": "jack<PERSON>", "version": null, "scope": "compile", "file": "base/common/pom.xml"}, {"groupId": "com.healthmarketscience.jackcess", "artifactId": "jackcess-encrypt", "version": null, "scope": "compile", "file": "base/common/pom.xml"}, {"groupId": "com.fasterxml.jackson.dataformat", "artifactId": "jackson-dataformat-yaml", "version": null, "scope": "compile", "file": "base/common/pom.xml"}, {"groupId": "com.belerweb", "artifactId": "pinyin4j", "version": null, "scope": "compile", "file": "base/common/pom.xml"}, {"groupId": "org.apache.commons", "artifactId": "commons-lang3", "version": null, "scope": "compile", "file": "base/aksk-server/pom.xml"}, {"groupId": "org.apache.httpcomponents", "artifactId": "httpclient", "version": null, "scope": "compile", "file": "base/aksk-server/pom.xml"}, {"groupId": "joda-time", "artifactId": "joda-time", "version": null, "scope": "compile", "file": "base/aksk-server/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter", "version": null, "scope": "compile", "file": "base/aksk-server/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-autoconfigure", "version": null, "scope": "compile", "file": "base/aksk-server/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-data-redis", "version": null, "scope": "compile", "file": "base/aksk-server/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-actuator", "version": null, "scope": "compile", "file": "base/aksk-server/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-configuration-processor", "version": null, "scope": "compile", "file": "base/aksk-server/pom.xml"}, {"groupId": "org.springframework.cloud", "artifactId": "spring-cloud-context", "version": null, "scope": "compile", "file": "base/aksk-server/pom.xml"}, {"groupId": "org.springframework", "artifactId": "spring-web", "version": null, "scope": "compile", "file": "base/aksk-server/pom.xml"}, {"groupId": "org.springframework", "artifactId": "spring-context", "version": null, "scope": "compile", "file": "base/aksk-server/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "common", "version": "${project.parent.version}", "scope": "compile", "file": "base/aksk-server/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "config-client", "version": "${project.parent.version}", "scope": "compile", "file": "base/aksk-server/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-test", "version": null, "scope": "test", "file": "base/aksk-server/pom.xml"}, {"groupId": "org.junit.jupiter", "artifactId": "junit-jupiter-api", "version": null, "scope": "test", "file": "base/aksk-server/pom.xml"}, {"groupId": "org.junit.jupiter", "artifactId": "junit-jupiter-engine", "version": null, "scope": "test", "file": "base/aksk-server/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "common", "version": "${project.parent.version}", "scope": "compile", "file": "base/account-feishu/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "account-client", "version": "${project.parent.version}", "scope": "compile", "file": "base/account-feishu/pom.xml"}, {"groupId": "org.redisson", "artifactId": "redisson-spring-boot-starter", "version": null, "scope": "compile", "file": "base/account-feishu/pom.xml"}, {"groupId": "org.mapstruct", "artifactId": "mapstruct", "version": null, "scope": "compile", "file": "base/account-feishu/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "message-client", "version": "${project.parent.version}", "scope": "compile", "file": "base/command-client/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "common", "version": "${project.parent.version}", "scope": "compile", "file": "base/command-client/pom.xml"}, {"groupId": "org.springframework", "artifactId": "spring-jms", "version": null, "scope": "compile", "file": "base/command-client/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-activemq", "version": null, "scope": "compile", "file": "base/command-client/pom.xml"}, {"groupId": "org.messaginghub", "artifactId": "pooled-jms", "version": null, "scope": "compile", "file": "base/command-client/pom.xml"}, {"groupId": "com.alibaba", "artifactId": "<PERSON><PERSON><PERSON>", "version": null, "scope": "compile", "file": "base/command-client/pom.xml"}, {"groupId": "com.aliyun.openservices", "artifactId": "ons-client", "version": null, "scope": "compile", "file": "base/command-client/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter", "version": null, "scope": "compile", "file": "base/command-client/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-autoconfigure", "version": null, "scope": "compile", "file": "base/command-client/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-configuration-processor", "version": null, "scope": "compile", "file": "base/command-client/pom.xml"}, {"groupId": "org.springframework.cloud", "artifactId": "spring-cloud-context", "version": null, "scope": "compile", "file": "base/command-client/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "common", "version": "${project.parent.version}", "scope": "compile", "file": "base/account-wework/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "account-client", "version": "${project.parent.version}", "scope": "compile", "file": "base/account-wework/pom.xml"}, {"groupId": "org.redisson", "artifactId": "redisson-spring-boot-starter", "version": null, "scope": "compile", "file": "base/account-wework/pom.xml"}, {"groupId": "org.dom4j", "artifactId": "dom4j", "version": null, "scope": "compile", "file": "base/account-wework/pom.xml"}, {"groupId": "org.springframework", "artifactId": "spring-test", "version": null, "scope": "test", "file": "base/account-wework/pom.xml"}, {"groupId": "org.junit.jupiter", "artifactId": "junit-jupiter-api", "version": null, "scope": "test", "file": "base/account-wework/pom.xml"}, {"groupId": "junit", "artifactId": "junit", "version": null, "scope": "test", "file": "base/account-wework/pom.xml"}, {"groupId": "org.springframework", "artifactId": "spring-context", "version": null, "scope": "compile", "file": "base/file-client/pom.xml"}, {"groupId": "org.slf4j", "artifactId": "slf4j-api", "version": null, "scope": "compile", "file": "base/file-client/pom.xml"}, {"groupId": "com.aliyun.oss", "artifactId": "aliyun-sdk-oss", "version": null, "scope": "compile", "file": "base/file-client/pom.xml"}, {"groupId": "org.apache.poi", "artifactId": "poi", "version": null, "scope": "compile", "file": "base/file-client/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter", "version": null, "scope": "compile", "file": "base/file-client/pom.xml"}, {"groupId": "org.springframework.cloud", "artifactId": "spring-cloud-context", "version": null, "scope": "compile", "file": "base/file-client/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "account-client", "version": "${project.parent.version}", "scope": "compile", "file": "base/file-client/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "common", "version": "${project.parent.version}", "scope": "compile", "file": "base/account-etl/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "account-client", "version": "${project.parent.version}", "scope": "compile", "file": "base/account-etl/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "dds-springboot-starter", "version": null, "scope": "compile", "file": "base/account-etl/pom.xml"}, {"groupId": "org.apache.commons", "artifactId": "commons-lang3", "version": null, "scope": "compile", "file": "base/account-etl/pom.xml"}, {"groupId": "org.mapstruct", "artifactId": "mapstruct", "version": null, "scope": "compile", "file": "base/account-etl/pom.xml"}, {"groupId": "com.baomidou", "artifactId": "mybatis-plus-boot-starter", "version": null, "scope": "compile", "file": "base/account-etl/pom.xml"}, {"groupId": "p6spy", "artifactId": "p6spy", "version": null, "scope": "compile", "file": "base/account-etl/pom.xml"}, {"groupId": "org.apache.commons", "artifactId": "commons-lang3", "version": null, "scope": "compile", "file": "base/data-connect/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "common", "version": null, "scope": "compile", "file": "base/data-connect/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "account-client", "version": "${project.version}", "scope": "compile", "file": "base/data-connect/pom.xml"}, {"groupId": "org.redisson", "artifactId": "redisson", "version": null, "scope": "compile", "file": "base/data-connect/pom.xml"}, {"groupId": "org.redisson", "artifactId": "redisson-spring-boot-starter", "version": null, "scope": "compile", "file": "base/data-connect/pom.xml"}, {"groupId": "junit", "artifactId": "junit", "version": null, "scope": "test", "file": "base/data-connect/pom.xml"}, {"groupId": "jakarta.mail", "artifactId": "jakarta.mail-api", "version": null, "scope": "compile", "file": "base/data-connect/pom.xml"}, {"groupId": "com.sun.mail", "artifactId": "jakarta.mail", "version": null, "scope": "compile", "file": "base/data-connect/pom.xml"}, {"groupId": "commons-net", "artifactId": "commons-net", "version": null, "scope": "compile", "file": "base/data-connect/pom.xml"}, {"groupId": "io.socket", "artifactId": "socket.io-client", "version": "2.1.0", "scope": "compile", "file": "base/data-connect/pom.xml"}, {"groupId": "org.ldaptive", "artifactId": "ldaptive", "version": null, "scope": "compile", "file": "base/data-connect/pom.xml"}, {"groupId": "org.ldaptive", "artifactId": "ldaptive-unboundid", "version": null, "scope": "compile", "file": "base/data-connect/pom.xml"}, {"groupId": "com.alibaba", "artifactId": "<PERSON><PERSON><PERSON>", "version": null, "scope": "compile", "file": "base/common-policy/pom.xml"}, {"groupId": "org.apache.commons", "artifactId": "commons-collections4", "version": null, "scope": "compile", "file": "base/common-policy/pom.xml"}, {"groupId": "org.springframework", "artifactId": "spring-jms", "version": null, "scope": "compile", "file": "base/message-client/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-activemq", "version": null, "scope": "compile", "file": "base/message-client/pom.xml"}, {"groupId": "org.messaginghub", "artifactId": "pooled-jms", "version": null, "scope": "compile", "file": "base/message-client/pom.xml"}, {"groupId": "com.alibaba", "artifactId": "<PERSON><PERSON><PERSON>", "version": null, "scope": "compile", "file": "base/message-client/pom.xml"}, {"groupId": "com.aliyun.openservices", "artifactId": "ons-client", "version": null, "scope": "compile", "file": "base/message-client/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter", "version": null, "scope": "compile", "file": "base/message-client/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-autoconfigure", "version": null, "scope": "compile", "file": "base/message-client/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-actuator", "version": null, "scope": "compile", "file": "base/message-client/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-configuration-processor", "version": null, "scope": "compile", "file": "base/message-client/pom.xml"}, {"groupId": "org.springframework.cloud", "artifactId": "spring-cloud-context", "version": null, "scope": "compile", "file": "base/message-client/pom.xml"}, {"groupId": "org.apache.commons", "artifactId": "commons-lang3", "version": null, "scope": "compile", "file": "base/message-client/pom.xml"}, {"groupId": "<PERSON><PERSON>", "artifactId": "fst", "version": null, "scope": "compile", "file": "base/message-client/pom.xml"}, {"groupId": "com.esotericsoftware", "artifactId": "kryo", "version": null, "scope": "compile", "file": "base/message-client/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "common", "version": "${project.parent.version}", "scope": "compile", "file": "base/message-client/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-test", "version": null, "scope": "test", "file": "base/message-client/pom.xml"}, {"groupId": "org.junit.jupiter", "artifactId": "junit-jupiter-api", "version": null, "scope": "test", "file": "base/message-client/pom.xml"}, {"groupId": "org.junit.jupiter", "artifactId": "junit-jupiter-engine", "version": null, "scope": "test", "file": "base/message-client/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "common", "version": "${project.parent.version}", "scope": "compile", "file": "base/oauth2-client/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "dds-springboot-starter", "version": "${project.parent.version}", "scope": "compile", "file": "base/oauth2-client/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "iam-api", "version": "${project.parent.version}", "scope": "compile", "file": "base/oauth2-client/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-security", "version": null, "scope": "compile", "file": "base/oauth2-client/pom.xml"}, {"groupId": "org.springframework.security.oauth", "artifactId": "spring-security-oauth2", "version": null, "scope": "compile", "file": "base/oauth2-client/pom.xml"}, {"groupId": "org.springframework.security", "artifactId": "spring-security-oauth2-client", "version": null, "scope": "compile", "file": "base/oauth2-client/pom.xml"}, {"groupId": "org.springframework.security", "artifactId": "spring-security-oauth2-jose", "version": null, "scope": "compile", "file": "base/oauth2-client/pom.xml"}, {"groupId": "org.springframework.security", "artifactId": "spring-security-oauth2-resource-server", "version": null, "scope": "compile", "file": "base/oauth2-client/pom.xml"}, {"groupId": "org.springframework.cloud", "artifactId": "spring-cloud-context", "version": null, "scope": "compile", "file": "base/oauth2-client/pom.xml"}, {"groupId": "org.springframework.data", "artifactId": "spring-data-redis", "version": null, "scope": "compile", "file": "base/oauth2-client/pom.xml"}, {"groupId": "org.redisson", "artifactId": "redisson", "version": null, "scope": "compile", "file": "base/oauth2-client/pom.xml"}, {"groupId": "org.apache.commons", "artifactId": "commons-lang3", "version": null, "scope": "compile", "file": "base/shorturl/shorturl-core/pom.xml"}, {"groupId": "org.apache.httpcomponents", "artifactId": "httpclient", "version": null, "scope": "compile", "file": "base/shorturl/shorturl-core/pom.xml"}, {"groupId": "joda-time", "artifactId": "joda-time", "version": null, "scope": "compile", "file": "base/shorturl/shorturl-core/pom.xml"}, {"groupId": "org.mapstruct", "artifactId": "mapstruct", "version": null, "scope": "compile", "file": "base/shorturl/shorturl-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "shorturl-api", "version": "${project.parent.version}", "scope": "compile", "file": "base/shorturl/shorturl-core/pom.xml"}, {"groupId": "com.baomidou", "artifactId": "mybatis-plus-boot-starter", "version": null, "scope": "compile", "file": "base/shorturl/shorturl-core/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-test", "version": null, "scope": "test", "file": "base/shorturl/shorturl-core/pom.xml"}, {"groupId": "org.springframework", "artifactId": "spring-test", "version": null, "scope": "test", "file": "base/shorturl/shorturl-core/pom.xml"}, {"groupId": "org.junit.jupiter", "artifactId": "junit-jupiter-api", "version": null, "scope": "test", "file": "base/shorturl/shorturl-core/pom.xml"}, {"groupId": "junit", "artifactId": "junit", "version": null, "scope": "test", "file": "base/shorturl/shorturl-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "common", "version": "${project.parent.version}", "scope": "compile", "file": "base/shorturl/shorturl-api/pom.xml"}, {"groupId": "org.mapstruct", "artifactId": "mapstruct", "version": null, "scope": "compile", "file": "base/shorturl/shorturl-api/pom.xml"}, {"groupId": "io.springfox", "artifactId": "springfox-swagger2", "version": null, "scope": "compile", "file": "base/shorturl/shorturl-api/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-web", "version": null, "scope": "compile", "file": "base/dds/dds-springboot-starter/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter", "version": null, "scope": "compile", "file": "base/dds/dds-springboot-starter/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-test", "version": null, "scope": "test", "file": "base/dds/dds-springboot-starter/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-autoconfigure", "version": null, "scope": "compile", "file": "base/dds/dds-springboot-starter/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-configuration-processor", "version": null, "scope": "compile", "file": "base/dds/dds-springboot-starter/pom.xml"}, {"groupId": "com.zaxxer", "artifactId": "HikariCP", "version": null, "scope": "compile", "file": "base/dds/dds-springboot-starter/pom.xml"}, {"groupId": "org.springframework", "artifactId": "spring-jdbc", "version": null, "scope": "compile", "file": "base/dds/dds-springboot-starter/pom.xml"}, {"groupId": "org.springframework.cloud", "artifactId": "spring-cloud-context", "version": null, "scope": "compile", "file": "base/dds/dds-springboot-starter/pom.xml"}, {"groupId": "org.apache.commons", "artifactId": "commons-lang3", "version": null, "scope": "compile", "file": "base/dds/dds-springboot-starter/pom.xml"}, {"groupId": "commons-beanutils", "artifactId": "commons-beanutils", "version": null, "scope": "compile", "file": "base/dds/dds-springboot-starter/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "common", "version": null, "scope": "compile", "file": "base/dds/dds-springboot-starter/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-web", "version": null, "scope": "compile", "file": "base/dds/dds-common/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter", "version": null, "scope": "compile", "file": "base/dds/dds-common/pom.xml"}, {"groupId": "com.zaxxer", "artifactId": "HikariCP", "version": null, "scope": "compile", "file": "base/dds/dds-common/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "common", "version": null, "scope": "compile", "file": "base/dds/dds-common/pom.xml"}, {"groupId": "net.sf.ucanaccess", "artifactId": "ucanaccess", "version": null, "scope": "compile", "file": "base/dds/dds-common/pom.xml"}, {"groupId": "org.springframework", "artifactId": "spring-web", "version": null, "scope": "compile", "file": "gateway/gateway-core/pom.xml"}, {"groupId": "org.springframework", "artifactId": "spring-context", "version": null, "scope": "compile", "file": "gateway/gateway-core/pom.xml"}, {"groupId": "org.bitbucket.b_c", "artifactId": "jose4j", "version": null, "scope": "compile", "file": "gateway/gateway-core/pom.xml"}, {"groupId": "com.nimbusds", "artifactId": "nimbus-jose-jwt", "version": null, "scope": "compile", "file": "gateway/gateway-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "proxy-meta", "version": "${project.parent.version}", "scope": "compile", "file": "gateway/gateway-core/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-web", "version": null, "scope": "compile", "file": "gateway/gateway-service/pom.xml"}, {"groupId": "org.slf4j", "artifactId": "slf4j-api", "version": null, "scope": "compile", "file": "gateway/gateway-service/pom.xml"}, {"groupId": "org.apache.logging.log4j", "artifactId": "log4j-slf4j-impl", "version": null, "scope": "compile", "file": "gateway/gateway-service/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "common", "version": null, "scope": "compile", "file": "gateway/gateway-service/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "gateway-core", "version": "${project.parent.version}", "scope": "compile", "file": "gateway/gateway-service/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "common", "version": null, "scope": "compile", "file": "acm/acm-api/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-log4j2", "version": null, "scope": "compile", "file": "acm/acm-service/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-test", "version": null, "scope": "test", "file": "acm/acm-service/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-web", "version": null, "scope": "compile", "file": "acm/acm-service/pom.xml"}, {"groupId": "org.springframework.cloud", "artifactId": "spring-cloud-starter-kubernetes-client-config", "version": null, "scope": "compile", "file": "acm/acm-service/pom.xml"}, {"groupId": "org.springframework.cloud", "artifactId": "spring-cloud-starter-consul-config", "version": null, "scope": "compile", "file": "acm/acm-service/pom.xml"}, {"groupId": "org.springframework.cloud", "artifactId": "spring-cloud-starter-consul-discovery", "version": null, "scope": "compile", "file": "acm/acm-service/pom.xml"}, {"groupId": "de.codecentric", "artifactId": "spring-boot-admin-starter-client", "version": null, "scope": "compile", "file": "acm/acm-service/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-actuator", "version": null, "scope": "compile", "file": "acm/acm-service/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "acm-core", "version": "${project.parent.version}", "scope": "compile", "file": "acm/acm-service/pom.xml"}, {"groupId": "org.graalvm.js", "artifactId": "js", "version": null, "scope": "compile", "file": "acm/acm-service/pom.xml"}, {"groupId": "org.graalvm.js", "artifactId": "js-scriptengine", "version": null, "scope": "compile", "file": "acm/acm-service/pom.xml"}, {"groupId": "com.oracle.database.jdbc", "artifactId": "ojdbc8", "version": null, "scope": "compile", "file": "acm/acm-service/pom.xml"}, {"groupId": "com.ibm.db2", "artifactId": "jcc", "version": null, "scope": "compile", "file": "acm/acm-service/pom.xml"}, {"groupId": "mysql", "artifactId": "mysql-connector-java", "version": null, "scope": "compile", "file": "acm/acm-service/pom.xml"}, {"groupId": "com.microsoft.sqlserver", "artifactId": "mssql-jdbc", "version": null, "scope": "compile", "file": "acm/acm-service/pom.xml"}, {"groupId": "com.dameng", "artifactId": "DmJdbcDriver18", "version": null, "scope": "compile", "file": "acm/acm-service/pom.xml"}, {"groupId": "com.kingbase8", "artifactId": "kingbase8", "version": null, "scope": "compile", "file": "acm/acm-service/pom.xml"}, {"groupId": "net.sf.ucanaccess", "artifactId": "ucanaccess", "version": null, "scope": "compile", "file": "acm/acm-service/pom.xml"}, {"groupId": "com.flywaydm8", "artifactId": "flyway-core-dm8", "version": null, "scope": "compile", "file": "acm/acm-service/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "acm-api", "version": "${project.parent.version}", "scope": "compile", "file": "acm/acm-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "iam-api", "version": "${project.parent.version}", "scope": "compile", "file": "acm/acm-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "data-connect", "version": "${project.parent.version}", "scope": "compile", "file": "acm/acm-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "proxy-meta", "version": "${project.parent.version}", "scope": "compile", "file": "acm/acm-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "dds-springboot-starter", "version": "${project.parent.version}", "scope": "compile", "file": "acm/acm-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "oauth2-client", "version": "${project.parent.version}", "scope": "compile", "file": "acm/acm-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "message-client", "version": "${project.parent.version}", "scope": "compile", "file": "acm/acm-core/pom.xml"}, {"groupId": "com.cyberscraft.uep", "artifactId": "common-task", "version": "${project.parent.version}", "scope": "compile", "file": "acm/acm-core/pom.xml"}, {"groupId": "org.mapstruct", "artifactId": "mapstruct", "version": null, "scope": "compile", "file": "acm/acm-core/pom.xml"}, {"groupId": "org.assertj", "artifactId": "assertj-core", "version": "3.12.2", "scope": "compile", "file": "acm/acm-core/pom.xml"}, {"groupId": "org.apache.poi", "artifactId": "poi", "version": "5.2.3", "scope": "compile", "file": "acm/acm-core/pom.xml"}, {"groupId": "org.jsoup", "artifactId": "jsoup", "version": "1.14.3", "scope": "compile", "file": "acm/acm-core/pom.xml"}, {"groupId": "com.baomidou", "artifactId": "mybatis-plus-generator", "version": null, "scope": "compile", "file": "acm/acm-core/pom.xml"}, {"groupId": "com.microsoft.ews-java-api", "artifactId": "ews-java-api", "version": null, "scope": "compile", "file": "acm/acm-core/pom.xml"}, {"groupId": "com.microsoft.graph", "artifactId": "microsoft-graph", "version": "5.0.0", "scope": "compile", "file": "acm/acm-core/pom.xml"}, {"groupId": "com.azure", "artifactId": "azure-identity", "version": "1.13.1", "scope": "compile", "file": "acm/acm-core/pom.xml"}, {"groupId": "com.dingtalk.open", "artifactId": "app-stream-client", "version": null, "scope": "compile", "file": "acm/acm-core/pom.xml"}, {"groupId": "com.baomidou", "artifactId": "mybatis-plus-boot-starter", "version": null, "scope": "compile", "file": "acm/acm-core/pom.xml"}, {"groupId": "org.springframework.boot", "artifactId": "spring-boot-starter-data-mongodb", "version": null, "scope": "compile", "file": "acm/acm-core/pom.xml"}, {"groupId": "org.mongodb", "artifactId": "mongo-java-driver", "version": null, "scope": "compile", "file": "acm/acm-core/pom.xml"}], "issues": [{"type": "version_conflict", "severity": "warning", "dependency": "com.cyberscraft.uep:common", "versions": ["${project.parent.version}", "${project.version}"], "files": ["service-parent/pom.xml", "proxy-meta/pom.xml", "mdm/mdm-api/pom.xml", "mdm/mdm-core/pom.xml", "ncm/ncm-api/pom.xml", "ncm/ncm-core/pom.xml", "ncm/ncm-client/pom.xml", "iam/iam-api/pom.xml", "adm/adm-api/pom.xml", "uam/uam-api/pom.xml", "base/push-client/pom.xml", "base/account-dingding/pom.xml", "base/account-govdd/pom.xml", "base/account-client/pom.xml", "base/account-ad/pom.xml", "base/aksk-server/pom.xml", "base/account-feishu/pom.xml", "base/command-client/pom.xml", "base/account-wework/pom.xml", "base/account-etl/pom.xml", "base/data-connect/pom.xml", "base/message-client/pom.xml", "base/oauth2-client/pom.xml", "base/shorturl/shorturl-api/pom.xml", "base/dds/dds-springboot-starter/pom.xml", "base/dds/dds-common/pom.xml", "gateway/gateway-service/pom.xml", "acm/acm-api/pom.xml"], "description": "依赖版本冲突: com.cyberscraft.uep:common"}, {"type": "version_conflict", "severity": "warning", "dependency": "org.apache.poi:poi", "versions": ["5.2.3", "${poi.version}"], "files": ["service-parent/pom.xml", "mdm/mdm-core/pom.xml", "iam/iam-core/pom.xml", "adm/adm-core/pom.xml", "base/file-client/pom.xml", "acm/acm-core/pom.xml"], "description": "依赖版本冲突: org.apache.poi:poi"}, {"type": "version_conflict", "severity": "warning", "dependency": "com.cyberscraft.uep:message-client", "versions": ["${project.parent.version}", "${project.version}"], "files": ["mdm/mdm-core/pom.xml", "ncm/ncm-core/pom.xml", "ncm/ncm-client/pom.xml", "iam/iam-core/pom.xml", "base/push-client/pom.xml", "base/command-client/pom.xml", "acm/acm-core/pom.xml"], "description": "依赖版本冲突: com.cyberscraft.uep:message-client"}, {"type": "version_conflict", "severity": "warning", "dependency": "com.cyberscraft.uep:iam-api", "versions": ["${project.parent.version}", "${project.version}"], "files": ["mdm/mdm-core/pom.xml", "ncm/ncm-core/pom.xml", "iam/iam-core/pom.xml", "adm/adm-core/pom.xml", "base/oauth2-client/pom.xml", "acm/acm-core/pom.xml"], "description": "依赖版本冲突: com.cyberscraft.uep:iam-api"}, {"type": "version_conflict", "severity": "warning", "dependency": "com.cyberscraft.uep:uam-api", "versions": ["${project.parent.version}", "${project.version}"], "files": ["mdm/mdm-core/pom.xml", "adm/adm-core/pom.xml", "uam/uam-service/pom.xml", "uam/uam-core/pom.xml"], "description": "依赖版本冲突: com.cyberscraft.uep:uam-api"}, {"type": "version_conflict", "severity": "warning", "dependency": "com.cyberscraft.uep:account-client", "versions": ["${project.parent.version}", "${project.version}"], "files": ["mdm/mdm-core/pom.xml", "ncm/ncm-core/pom.xml", "iam/iam-core/pom.xml", "adm/adm-core/pom.xml", "base/push-client/pom.xml", "base/account-dingding/pom.xml", "base/account-govdd/pom.xml", "base/account-ad/pom.xml", "base/account-feishu/pom.xml", "base/account-wework/pom.xml", "base/file-client/pom.xml", "base/account-etl/pom.xml", "base/data-connect/pom.xml"], "description": "依赖版本冲突: com.cyberscraft.uep:account-client"}, {"type": "version_conflict", "severity": "warning", "dependency": "com.cyberscraft.uep:account-dingding", "versions": ["${project.parent.version}", "${project.version}"], "files": ["mdm/mdm-core/pom.xml", "ncm/ncm-core/pom.xml", "iam/iam-core/pom.xml"], "description": "依赖版本冲突: com.cyberscraft.uep:account-dingding"}, {"type": "version_conflict", "severity": "warning", "dependency": "com.cyberscraft.uep:ncm-api", "versions": ["${project.parent.version}", "${project.version}"], "files": ["ncm/ncm-core/pom.xml", "ncm/ncm-client/pom.xml"], "description": "依赖版本冲突: com.cyberscraft.uep:ncm-api"}, {"type": "version_conflict", "severity": "warning", "dependency": "com.cyberscraft.uep:ncm-client", "versions": ["${project.parent.version}", "${project.version}"], "files": ["ncm/ncm-core/pom.xml", "iam/iam-core/pom.xml", "adm/adm-core/pom.xml"], "description": "依赖版本冲突: com.cyberscraft.uep:ncm-client"}, {"type": "version_conflict", "severity": "warning", "dependency": "com.cyberscraft.uep:account-feishu", "versions": ["${project.parent.version}", "${project.version}"], "files": ["ncm/ncm-core/pom.xml", "iam/iam-core/pom.xml"], "description": "依赖版本冲突: com.cyberscraft.uep:account-feishu"}, {"type": "version_conflict", "severity": "warning", "dependency": "com.cyberscraft.uep:aksk-server", "versions": ["${project.parent.version}", "${project.version}"], "files": ["ncm/ncm-core/pom.xml", "iam/iam-service/pom.xml"], "description": "依赖版本冲突: com.cyberscraft.uep:aksk-server"}], "recommendations": [{"priority": "medium", "title": "解决依赖冲突", "description": "发现 11 个依赖冲突，建议统一版本"}]}