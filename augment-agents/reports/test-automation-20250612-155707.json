{"timestamp": "2025-06-12T15:57:05.323381", "test_summary": {"source_files": 4755, "test_files": 169, "coverage_estimate": 3.6, "missing_tests": 75, "test_suggestions": 8}, "coverage_analysis": [{"module": "iam", "source_files": 1830, "test_files": 67, "coverage_estimate": 3.7, "status": "needs_improvement"}, {"module": "mdm", "source_files": 932, "test_files": 71, "coverage_estimate": 7.6, "status": "needs_improvement"}, {"module": "adm", "source_files": 371, "test_files": 7, "coverage_estimate": 1.9, "status": "needs_improvement"}, {"module": "ncm", "source_files": 226, "test_files": 5, "coverage_estimate": 2.2, "status": "needs_improvement"}, {"module": "uam", "source_files": 44, "test_files": 1, "coverage_estimate": 2.3, "status": "needs_improvement"}, {"module": "acm", "source_files": 194, "test_files": 1, "coverage_estimate": 0.5, "status": "needs_improvement"}, {"module": "gateway", "source_files": 13, "test_files": 0, "coverage_estimate": 0.0, "status": "needs_improvement"}], "missing_tests": [{"source_file": "mdm/mdm-core/src/main/java/com/cyberscraft/uep/mdm/core/ios/IOSUdidDeviceCacheService.java", "category": "service", "description": "业务逻辑服务", "suggested_test_path": "mdm/mdm-core/src/test/java/com/cyberscraft/uep/mdm/core/ios/IOSUdidDeviceCacheServiceTest.java", "priority": "high"}, {"source_file": "mdm/mdm-core/src/main/java/com/cyberscraft/uep/mdm/core/ios/IOSMDMService.java", "category": "service", "description": "业务逻辑服务", "suggested_test_path": "mdm/mdm-core/src/test/java/com/cyberscraft/uep/mdm/core/ios/IOSMDMServiceTest.java", "priority": "high"}, {"source_file": "mdm/mdm-core/src/main/java/com/cyberscraft/uep/mdm/core/ios/impl/IOSMDMServiceImpl.java", "category": "service", "description": "业务逻辑服务", "suggested_test_path": "mdm/mdm-core/src/test/java/com/cyberscraft/uep/mdm/core/ios/impl/IOSMDMServiceImplTest.java", "priority": "high"}, {"source_file": "mdm/mdm-core/src/main/java/com/cyberscraft/uep/mdm/core/ios/impl/IOSUdidDeviceCacheServiceImpl.java", "category": "service", "description": "业务逻辑服务", "suggested_test_path": "mdm/mdm-core/src/test/java/com/cyberscraft/uep/mdm/core/ios/impl/IOSUdidDeviceCacheServiceImplTest.java", "priority": "high"}, {"source_file": "mdm/mdm-core/src/main/java/com/cyberscraft/uep/mdm/core/service/cmd/ICommandBaseService.java", "category": "service", "description": "业务逻辑服务", "suggested_test_path": "mdm/mdm-core/src/test/java/com/cyberscraft/uep/mdm/core/service/cmd/ICommandBaseServiceTest.java", "priority": "high"}, {"source_file": "mdm/mdm-core/src/main/java/com/cyberscraft/uep/mdm/core/service/cmd/ICommandProfileService.java", "category": "service", "description": "业务逻辑服务", "suggested_test_path": "mdm/mdm-core/src/test/java/com/cyberscraft/uep/mdm/core/service/cmd/ICommandProfileServiceTest.java", "priority": "high"}, {"source_file": "mdm/mdm-core/src/main/java/com/cyberscraft/uep/mdm/core/service/cmd/IProfileService.java", "category": "service", "description": "业务逻辑服务", "suggested_test_path": "mdm/mdm-core/src/test/java/com/cyberscraft/uep/mdm/core/service/cmd/IProfileServiceTest.java", "priority": "high"}, {"source_file": "mdm/mdm-core/src/main/java/com/cyberscraft/uep/mdm/core/service/cmd/ICmdService.java", "category": "service", "description": "业务逻辑服务", "suggested_test_path": "mdm/mdm-core/src/test/java/com/cyberscraft/uep/mdm/core/service/cmd/ICmdServiceTest.java", "priority": "high"}, {"source_file": "mdm/mdm-core/src/main/java/com/cyberscraft/uep/mdm/core/service/cmd/ICommandProcessService.java", "category": "service", "description": "业务逻辑服务", "suggested_test_path": "mdm/mdm-core/src/test/java/com/cyberscraft/uep/mdm/core/service/cmd/ICommandProcessServiceTest.java", "priority": "high"}, {"source_file": "mdm/mdm-core/src/main/java/com/cyberscraft/uep/mdm/core/service/settings/ICertificateService.java", "category": "service", "description": "业务逻辑服务", "suggested_test_path": "mdm/mdm-core/src/test/java/com/cyberscraft/uep/mdm/core/service/settings/ICertificateServiceTest.java", "priority": "high"}, {"source_file": "mdm/mdm-core/src/main/java/com/cyberscraft/uep/mdm/core/service/app/IAppStoreDeviceService.java", "category": "service", "description": "业务逻辑服务", "suggested_test_path": "mdm/mdm-core/src/test/java/com/cyberscraft/uep/mdm/core/service/app/IAppStoreDeviceServiceTest.java", "priority": "high"}, {"source_file": "mdm/mdm-core/src/main/java/com/cyberscraft/uep/mdm/core/service/clientcmd/ILegacyClientCommandService.java", "category": "service", "description": "业务逻辑服务", "suggested_test_path": "mdm/mdm-core/src/test/java/com/cyberscraft/uep/mdm/core/service/clientcmd/ILegacyClientCommandServiceTest.java", "priority": "high"}, {"source_file": "mdm/mdm-core/src/main/java/com/cyberscraft/uep/mdm/core/service/clientcmd/PullCommandService.java", "category": "service", "description": "业务逻辑服务", "suggested_test_path": "mdm/mdm-core/src/test/java/com/cyberscraft/uep/mdm/core/service/clientcmd/PullCommandServiceTest.java", "priority": "high"}, {"source_file": "mdm/mdm-core/src/main/java/com/cyberscraft/uep/mdm/core/service/clientcmd/IClientRestRequestService.java", "category": "service", "description": "业务逻辑服务", "suggested_test_path": "mdm/mdm-core/src/test/java/com/cyberscraft/uep/mdm/core/service/clientcmd/IClientRestRequestServiceTest.java", "priority": "high"}, {"source_file": "mdm/mdm-core/src/main/java/com/cyberscraft/uep/mdm/core/service/clientcmd/IClientCommandService.java", "category": "service", "description": "业务逻辑服务", "suggested_test_path": "mdm/mdm-core/src/test/java/com/cyberscraft/uep/mdm/core/service/clientcmd/IClientCommandServiceTest.java", "priority": "high"}, {"source_file": "mdm/mdm-core/src/main/java/com/cyberscraft/uep/mdm/core/service/config/ITenantConfigService.java", "category": "service", "description": "业务逻辑服务", "suggested_test_path": "mdm/mdm-core/src/test/java/com/cyberscraft/uep/mdm/core/service/config/ITenantConfigServiceTest.java", "priority": "high"}, {"source_file": "mdm/mdm-core/src/main/java/com/cyberscraft/uep/mdm/core/service/oauth2/IAccessTokenService.java", "category": "service", "description": "业务逻辑服务", "suggested_test_path": "mdm/mdm-core/src/test/java/com/cyberscraft/uep/mdm/core/service/oauth2/IAccessTokenServiceTest.java", "priority": "high"}, {"source_file": "mdm/mdm-core/src/main/java/com/cyberscraft/uep/mdm/core/service/oauth2/IAuthorizationService.java", "category": "service", "description": "业务逻辑服务", "suggested_test_path": "mdm/mdm-core/src/test/java/com/cyberscraft/uep/mdm/core/service/oauth2/IAuthorizationServiceTest.java", "priority": "high"}, {"source_file": "mdm/mdm-core/src/main/java/com/cyberscraft/uep/mdm/core/service/oauth2/IRefreshTokenService.java", "category": "service", "description": "业务逻辑服务", "suggested_test_path": "mdm/mdm-core/src/test/java/com/cyberscraft/uep/mdm/core/service/oauth2/IRefreshTokenServiceTest.java", "priority": "high"}, {"source_file": "mdm/mdm-core/src/main/java/com/cyberscraft/uep/mdm/core/service/admin/ISysUserPermissionService.java", "category": "service", "description": "业务逻辑服务", "suggested_test_path": "mdm/mdm-core/src/test/java/com/cyberscraft/uep/mdm/core/service/admin/ISysUserPermissionServiceTest.java", "priority": "high"}, {"source_file": "mdm/mdm-service/src/main/java/com/cyberscraft/uep/mdm/app/controller/BaseController.java", "category": "controller", "description": "REST API 控制器", "suggested_test_path": "mdm/mdm-service/src/test/java/com/cyberscraft/uep/mdm/app/controller/BaseControllerTest.java", "priority": "high"}, {"source_file": "mdm/mdm-service/src/main/java/com/cyberscraft/uep/mdm/app/controller/app/BasicController.java", "category": "controller", "description": "REST API 控制器", "suggested_test_path": "mdm/mdm-service/src/test/java/com/cyberscraft/uep/mdm/app/controller/app/BasicControllerTest.java", "priority": "high"}, {"source_file": "mdm/mdm-service/src/main/java/com/cyberscraft/uep/mdm/app/controller/app/testController.java", "category": "controller", "description": "REST API 控制器", "suggested_test_path": "mdm/mdm-service/src/test/java/com/cyberscraft/uep/mdm/app/controller/app/testControllerTest.java", "priority": "high"}, {"source_file": "mdm/mdm-service/src/main/java/com/cyberscraft/uep/mdm/app/controller/admin/AdminAuthorizationController.java", "category": "controller", "description": "REST API 控制器", "suggested_test_path": "mdm/mdm-service/src/test/java/com/cyberscraft/uep/mdm/app/controller/admin/AdminAuthorizationControllerTest.java", "priority": "high"}, {"source_file": "mdm/mdm-service/src/main/java/com/cyberscraft/uep/mdm/app/controller/basic/PushController.java", "category": "controller", "description": "REST API 控制器", "suggested_test_path": "mdm/mdm-service/src/test/java/com/cyberscraft/uep/mdm/app/controller/basic/PushControllerTest.java", "priority": "high"}, {"source_file": "mdm/mdm-service/src/main/java/com/cyberscraft/uep/mdm/app/controller/basic/ConfigController.java", "category": "controller", "description": "REST API 控制器", "suggested_test_path": "mdm/mdm-service/src/test/java/com/cyberscraft/uep/mdm/app/controller/basic/ConfigControllerTest.java", "priority": "high"}, {"source_file": "mdm/mdm-service/src/main/java/com/cyberscraft/uep/mdm/app/controller/basic/MessageController.java", "category": "controller", "description": "REST API 控制器", "suggested_test_path": "mdm/mdm-service/src/test/java/com/cyberscraft/uep/mdm/app/controller/basic/MessageControllerTest.java", "priority": "high"}, {"source_file": "mdm/mdm-service/src/main/java/com/cyberscraft/uep/mdm/app/controller/basic/SysController.java", "category": "controller", "description": "REST API 控制器", "suggested_test_path": "mdm/mdm-service/src/test/java/com/cyberscraft/uep/mdm/app/controller/basic/SysControllerTest.java", "priority": "high"}, {"source_file": "mdm/mdm-service/src/main/java/com/cyberscraft/uep/mdm/app/controller/basic/TenantController.java", "category": "controller", "description": "REST API 控制器", "suggested_test_path": "mdm/mdm-service/src/test/java/com/cyberscraft/uep/mdm/app/controller/basic/TenantControllerTest.java", "priority": "high"}, {"source_file": "mdm/mdm-service/src/main/java/com/cyberscraft/uep/mdm/app/controller/app/oauth2/AuthorizationController.java", "category": "controller", "description": "REST API 控制器", "suggested_test_path": "mdm/mdm-service/src/test/java/com/cyberscraft/uep/mdm/app/controller/app/oauth2/AuthorizationControllerTest.java", "priority": "high"}, {"source_file": "mdm/mdm-service/src/main/java/com/cyberscraft/uep/mdm/app/controller/app/basic/FileUploadController.java", "category": "controller", "description": "REST API 控制器", "suggested_test_path": "mdm/mdm-service/src/test/java/com/cyberscraft/uep/mdm/app/controller/app/basic/FileUploadControllerTest.java", "priority": "high"}, {"source_file": "mdm/mdm-service/src/main/java/com/cyberscraft/uep/mdm/app/controller/app/ios/IOSLegacyController.java", "category": "controller", "description": "REST API 控制器", "suggested_test_path": "mdm/mdm-service/src/test/java/com/cyberscraft/uep/mdm/app/controller/app/ios/IOSLegacyControllerTest.java", "priority": "high"}, {"source_file": "mdm/mdm-service/src/main/java/com/cyberscraft/uep/mdm/app/controller/app/ios/IOSController.java", "category": "controller", "description": "REST API 控制器", "suggested_test_path": "mdm/mdm-service/src/test/java/com/cyberscraft/uep/mdm/app/controller/app/ios/IOSControllerTest.java", "priority": "high"}, {"source_file": "mdm/mdm-service/src/main/java/com/cyberscraft/uep/mdm/app/controller/app/android/AndroidLegacyController.java", "category": "controller", "description": "REST API 控制器", "suggested_test_path": "mdm/mdm-service/src/test/java/com/cyberscraft/uep/mdm/app/controller/app/android/AndroidLegacyControllerTest.java", "priority": "high"}, {"source_file": "mdm/mdm-service/src/main/java/com/cyberscraft/uep/mdm/app/controller/app/android/AndroidController.java", "category": "controller", "description": "REST API 控制器", "suggested_test_path": "mdm/mdm-service/src/test/java/com/cyberscraft/uep/mdm/app/controller/app/android/AndroidControllerTest.java", "priority": "high"}, {"source_file": "mdm/mdm-service/src/main/java/com/cyberscraft/uep/mdm/app/controller/app/command/PullCommandController.java", "category": "controller", "description": "REST API 控制器", "suggested_test_path": "mdm/mdm-service/src/test/java/com/cyberscraft/uep/mdm/app/controller/app/command/PullCommandControllerTest.java", "priority": "high"}, {"source_file": "mdm/mdm-service/src/main/java/com/cyberscraft/uep/mdm/app/controller/app/windows/WindowsLegacyController.java", "category": "controller", "description": "REST API 控制器", "suggested_test_path": "mdm/mdm-service/src/test/java/com/cyberscraft/uep/mdm/app/controller/app/windows/WindowsLegacyControllerTest.java", "priority": "high"}, {"source_file": "mdm/mdm-service/src/main/java/com/cyberscraft/uep/mdm/app/controller/admin/cmd/CmdController.java", "category": "controller", "description": "REST API 控制器", "suggested_test_path": "mdm/mdm-service/src/test/java/com/cyberscraft/uep/mdm/app/controller/admin/cmd/CmdControllerTest.java", "priority": "high"}, {"source_file": "mdm/mdm-service/src/main/java/com/cyberscraft/uep/mdm/app/controller/admin/settings/CertController.java", "category": "controller", "description": "REST API 控制器", "suggested_test_path": "mdm/mdm-service/src/test/java/com/cyberscraft/uep/mdm/app/controller/admin/settings/CertControllerTest.java", "priority": "high"}, {"source_file": "mdm/mdm-service/src/main/java/com/cyberscraft/uep/mdm/app/controller/admin/upgrade/SysMigrationController.java", "category": "controller", "description": "REST API 控制器", "suggested_test_path": "mdm/mdm-service/src/test/java/com/cyberscraft/uep/mdm/app/controller/admin/upgrade/SysMigrationControllerTest.java", "priority": "high"}, {"source_file": "iam/iam-core/src/main/java/com/cyberscraft/uep/iam/service/fido2/CredentialRepositoryImpl.java", "category": "repository", "description": "数据访问层", "suggested_test_path": "iam/iam-core/src/test/java/com/cyberscraft/uep/iam/service/fido2/CredentialRepositoryImplTest.java", "priority": "medium"}, {"source_file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/policy/ios/IOSMDMPayloadUtil.java", "category": "utility", "description": "工具类", "suggested_test_path": "mdm/mdm-api/src/test/java/com/cyberscraft/uep/mdm/api/dto/policy/ios/IOSMDMPayloadUtilTest.java", "priority": "medium"}, {"source_file": "mdm/mdm-core/src/main/java/com/cyberscraft/uep/mdm/core/util/ResponseUtil.java", "category": "utility", "description": "工具类", "suggested_test_path": "mdm/mdm-core/src/test/java/com/cyberscraft/uep/mdm/core/util/ResponseUtilTest.java", "priority": "medium"}, {"source_file": "mdm/mdm-core/src/main/java/com/cyberscraft/uep/mdm/core/util/PolicyUtil.java", "category": "utility", "description": "工具类", "suggested_test_path": "mdm/mdm-core/src/test/java/com/cyberscraft/uep/mdm/core/util/PolicyUtilTest.java", "priority": "medium"}, {"source_file": "mdm/mdm-core/src/main/java/com/cyberscraft/uep/mdm/core/util/SignUtil.java", "category": "utility", "description": "工具类", "suggested_test_path": "mdm/mdm-core/src/test/java/com/cyberscraft/uep/mdm/core/util/SignUtilTest.java", "priority": "medium"}, {"source_file": "mdm/mdm-core/src/main/java/com/cyberscraft/uep/mdm/core/util/MultiLangUtil.java", "category": "utility", "description": "工具类", "suggested_test_path": "mdm/mdm-core/src/test/java/com/cyberscraft/uep/mdm/core/util/MultiLangUtilTest.java", "priority": "medium"}, {"source_file": "mdm/mdm-core/src/main/java/com/cyberscraft/uep/mdm/core/util/ParamUtil.java", "category": "utility", "description": "工具类", "suggested_test_path": "mdm/mdm-core/src/test/java/com/cyberscraft/uep/mdm/core/util/ParamUtilTest.java", "priority": "medium"}, {"source_file": "mdm/mdm-core/src/main/java/com/cyberscraft/uep/mdm/core/util/SmsCodeTranslatorUtil.java", "category": "utility", "description": "工具类", "suggested_test_path": "mdm/mdm-core/src/test/java/com/cyberscraft/uep/mdm/core/util/SmsCodeTranslatorUtilTest.java", "priority": "medium"}, {"source_file": "mdm/mdm-core/src/main/java/com/cyberscraft/uep/mdm/core/util/StringUtil.java", "category": "utility", "description": "工具类", "suggested_test_path": "mdm/mdm-core/src/test/java/com/cyberscraft/uep/mdm/core/util/StringUtilTest.java", "priority": "medium"}, {"source_file": "mdm/mdm-core/src/main/java/com/cyberscraft/uep/mdm/core/util/WebContextUtil.java", "category": "utility", "description": "工具类", "suggested_test_path": "mdm/mdm-core/src/test/java/com/cyberscraft/uep/mdm/core/util/WebContextUtilTest.java", "priority": "medium"}, {"source_file": "mdm/mdm-core/src/main/java/com/cyberscraft/uep/mdm/core/util/SmsUtil.java", "category": "utility", "description": "工具类", "suggested_test_path": "mdm/mdm-core/src/test/java/com/cyberscraft/uep/mdm/core/util/SmsUtilTest.java", "priority": "medium"}, {"source_file": "mdm/mdm-core/src/main/java/com/cyberscraft/uep/mdm/core/util/MyStringUtils.java", "category": "utility", "description": "工具类", "suggested_test_path": "mdm/mdm-core/src/test/java/com/cyberscraft/uep/mdm/core/util/MyStringUtilsTest.java", "priority": "medium"}, {"source_file": "mdm/mdm-core/src/main/java/com/cyberscraft/uep/mdm/core/util/SpringUtil.java", "category": "utility", "description": "工具类", "suggested_test_path": "mdm/mdm-core/src/test/java/com/cyberscraft/uep/mdm/core/util/SpringUtilTest.java", "priority": "medium"}, {"source_file": "mdm/mdm-core/src/main/java/com/cyberscraft/uep/mdm/core/util/VersionCodeUtil.java", "category": "utility", "description": "工具类", "suggested_test_path": "mdm/mdm-core/src/test/java/com/cyberscraft/uep/mdm/core/util/VersionCodeUtilTest.java", "priority": "medium"}, {"source_file": "mdm/mdm-core/src/main/java/com/cyberscraft/uep/mdm/core/util/Oauth2ThreadLocalUtil.java", "category": "utility", "description": "工具类", "suggested_test_path": "mdm/mdm-core/src/test/java/com/cyberscraft/uep/mdm/core/util/Oauth2ThreadLocalUtilTest.java", "priority": "medium"}, {"source_file": "mdm/mdm-core/src/main/java/com/cyberscraft/uep/mdm/core/util/RegexValidateUtil.java", "category": "utility", "description": "工具类", "suggested_test_path": "mdm/mdm-core/src/test/java/com/cyberscraft/uep/mdm/core/util/RegexValidateUtilTest.java", "priority": "medium"}, {"source_file": "mdm/mdm-core/src/main/java/com/cyberscraft/uep/mdm/core/util/UserAgentUtil.java", "category": "utility", "description": "工具类", "suggested_test_path": "mdm/mdm-core/src/test/java/com/cyberscraft/uep/mdm/core/util/UserAgentUtilTest.java", "priority": "medium"}, {"source_file": "mdm/mdm-core/src/main/java/com/cyberscraft/uep/mdm/core/util/PropertyUtil.java", "category": "utility", "description": "工具类", "suggested_test_path": "mdm/mdm-core/src/test/java/com/cyberscraft/uep/mdm/core/util/PropertyUtilTest.java", "priority": "medium"}, {"source_file": "mdm/mdm-service/src/main/java/com/cyberscraft/uep/mdm/app/config/UtilConfiguration.java", "category": "utility", "description": "工具类", "suggested_test_path": "mdm/mdm-service/src/test/java/com/cyberscraft/uep/mdm/app/config/UtilConfigurationTest.java", "priority": "medium"}, {"source_file": "ncm/ncm-core/src/main/java/com/cyberscraft/uep/ncm/core/common/util/LoginUserUtil.java", "category": "utility", "description": "工具类", "suggested_test_path": "ncm/ncm-core/src/test/java/com/cyberscraft/uep/ncm/core/common/util/LoginUserUtilTest.java", "priority": "medium"}, {"source_file": "iam/iam-notify-service/src/main/java/com/cyberscraft/uep/iam/notify/configuartion/CacheManagerConfiguration.java", "category": "manager", "description": "管理器类", "suggested_test_path": "iam/iam-notify-service/src/test/java/com/cyberscraft/uep/iam/notify/configuartion/CacheManagerConfigurationTest.java", "priority": "medium"}, {"source_file": "iam/iam-core/src/main/java/com/cyberscraft/uep/iam/common/util/DataSourceOperManager.java", "category": "manager", "description": "管理器类", "suggested_test_path": "iam/iam-core/src/test/java/com/cyberscraft/uep/iam/common/util/DataSourceOperManagerTest.java", "priority": "medium"}, {"source_file": "iam/iam-core/src/main/java/com/cyberscraft/uep/iam/service/oidc/util/IamOAuth2AuthenticationManager.java", "category": "manager", "description": "管理器类", "suggested_test_path": "iam/iam-core/src/test/java/com/cyberscraft/uep/iam/service/oidc/util/IamOAuth2AuthenticationManagerTest.java", "priority": "medium"}, {"source_file": "iam/iam-core/src/main/java/com/cyberscraft/uep/iam/service/connector/ldap/util/DummyTrustManager.java", "category": "manager", "description": "管理器类", "suggested_test_path": "iam/iam-core/src/test/java/com/cyberscraft/uep/iam/service/connector/ldap/util/DummyTrustManagerTest.java", "priority": "medium"}, {"source_file": "iam/iam-service/src/main/java/com/cyberscraft/uep/iam/configuration/cache/CacheManagerConfiguration.java", "category": "manager", "description": "管理器类", "suggested_test_path": "iam/iam-service/src/test/java/com/cyberscraft/uep/iam/configuration/cache/CacheManagerConfigurationTest.java", "priority": "medium"}, {"source_file": "adm/adm-core/src/main/java/com/cyberscraft/uep/adm/core/service/user/util/DummyTrustManager.java", "category": "manager", "description": "管理器类", "suggested_test_path": "adm/adm-core/src/test/java/com/cyberscraft/uep/adm/core/service/user/util/DummyTrustManagerTest.java", "priority": "medium"}, {"source_file": "adm/adm-service/src/main/java/com/cyberscraft/uep/adm/config/CacheManagerConfiguration.java", "category": "manager", "description": "管理器类", "suggested_test_path": "adm/adm-service/src/test/java/com/cyberscraft/uep/adm/config/CacheManagerConfigurationTest.java", "priority": "medium"}, {"source_file": "uam/uam-service/src/main/java/com/cyberscraft/uep/uam/config/db/CacheManagerConfiguration.java", "category": "manager", "description": "管理器类", "suggested_test_path": "uam/uam-service/src/test/java/com/cyberscraft/uep/uam/config/db/CacheManagerConfigurationTest.java", "priority": "medium"}, {"source_file": "base/common/src/main/java/com/cyberscraft/uep/common/service/OcrManager.java", "category": "manager", "description": "管理器类", "suggested_test_path": "base/common/src/test/java/com/cyberscraft/uep/common/service/OcrManagerTest.java", "priority": "medium"}, {"source_file": "base/file-client/src/main/java/com/cyberscraft/uep/file/FileManager.java", "category": "manager", "description": "管理器类", "suggested_test_path": "base/file-client/src/test/java/com/cyberscraft/uep/file/FileManagerTest.java", "priority": "medium"}, {"source_file": "base/file-client/src/main/java/com/cyberscraft/uep/file/impl/GovDDFileManager.java", "category": "manager", "description": "管理器类", "suggested_test_path": "base/file-client/src/test/java/com/cyberscraft/uep/file/impl/GovDDFileManagerTest.java", "priority": "medium"}, {"source_file": "base/file-client/src/main/java/com/cyberscraft/uep/file/impl/OssFileManager.java", "category": "manager", "description": "管理器类", "suggested_test_path": "base/file-client/src/test/java/com/cyberscraft/uep/file/impl/OssFileManagerTest.java", "priority": "medium"}, {"source_file": "base/file-client/src/main/java/com/cyberscraft/uep/file/impl/LocalFileManager.java", "category": "manager", "description": "管理器类", "suggested_test_path": "base/file-client/src/test/java/com/cyberscraft/uep/file/impl/LocalFileManagerTest.java", "priority": "medium"}, {"source_file": "base/dds/dds-springboot-starter/src/main/java/com/cyberscraft/uep/starter/dds/DDSDataSourceManager.java", "category": "manager", "description": "管理器类", "suggested_test_path": "base/dds/dds-springboot-starter/src/test/java/com/cyberscraft/uep/starter/dds/DDSDataSourceManagerTest.java", "priority": "medium"}, {"source_file": "acm/acm-core/src/main/java/com/cyberscraft/uep/acm/listener/EventSourceManager.java", "category": "manager", "description": "管理器类", "suggested_test_path": "acm/acm-core/src/test/java/com/cyberscraft/uep/acm/listener/EventSourceManagerTest.java", "priority": "medium"}], "test_suggestions": [{"test_file": "mdm/mdm-service/src/test/java/dfs/OSSTest.java", "suggestions": ["建议使用 Mock 对象进行单元测试", "建议添加更多断言来验证测试结果", "测试方法较少(2个)，建议增加更多测试场景"], "test_methods_count": 2}, {"test_file": "mdm/mdm-service/src/test/java/legacyCmd/BaseCmdRequestTest.java", "suggestions": ["建议使用 JUnit 5 注解", "建议使用 Mock 对象进行单元测试", "建议添加更多断言来验证测试结果", "测试方法较少(0个)，建议增加更多测试场景"], "test_methods_count": 0}, {"test_file": "mdm/mdm-service/src/test/java/lock/RedisLockTest.java", "suggestions": ["建议使用 Mock 对象进行单元测试", "建议添加更多断言来验证测试结果", "考虑使用 @WebMvcTest 进行更轻量的控制器测试", "测试方法较少(1个)，建议增加更多测试场景"], "test_methods_count": 1}, {"test_file": "mdm/mdm-service/src/test/java/com/cyberscraft/uep/mdm/core/service/device/impl/SysUserFileServiceImplTest.java", "suggestions": ["建议使用 Mock 对象进行单元测试", "建议添加更多断言来验证测试结果", "考虑使用 @WebMvcTest 进行更轻量的控制器测试", "测试方法较少(0个)，建议增加更多测试场景"], "test_methods_count": 0}, {"test_file": "iam/iam-notify-service/src/test/java/com/cyberscraft/uep/iam/notify/push/DataPushCustomerTest.java", "suggestions": ["建议使用 Mock 对象进行单元测试", "建议添加更多断言来验证测试结果", "考虑使用 @WebMvcTest 进行更轻量的控制器测试", "测试方法较少(1个)，建议增加更多测试场景"], "test_methods_count": 1}, {"test_file": "iam/iam-notify-service/src/test/java/com/cyberscraft/uep/iam/notify/job/DataPushJobTest.java", "suggestions": ["建议使用 Mock 对象进行单元测试", "建议添加更多断言来验证测试结果", "考虑使用 @WebMvcTest 进行更轻量的控制器测试", "测试方法较少(1个)，建议增加更多测试场景"], "test_methods_count": 1}, {"test_file": "iam/iam-notify-service/src/test/java/com/cyberscraft/uep/iam/notify/service/impl/DataPushAllServiceImplTest.java", "suggestions": ["建议使用 Mock 对象进行单元测试", "建议添加更多断言来验证测试结果", "考虑使用 @WebMvcTest 进行更轻量的控制器测试", "测试方法较少(1个)，建议增加更多测试场景"], "test_methods_count": 1}, {"test_file": "iam/iam-notify-service/src/test/java/com/cyberscraft/uep/iam/notify/service/impl/DataPullServiceImplTest.java", "suggestions": ["建议使用 Mock 对象进行单元测试", "建议添加更多断言来验证测试结果", "考虑使用 @WebMvcTest 进行更轻量的控制器测试", "测试方法较少(2个)，建议增加更多测试场景"], "test_methods_count": 2}, {"test_file": "overall", "suggestions": ["整体测试覆盖率较低(3.6%)，建议优先为核心业务逻辑添加测试", "建议设置测试覆盖率目标为70%以上"]}, {"test_file": "overall", "suggestions": ["发现75个文件缺少测试，建议优先为Service和Controller类添加测试"]}]}