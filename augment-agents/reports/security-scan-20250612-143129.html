
<!DOCTYPE html>
<html>
<head>
    <title>安全扫描报告</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f8f9fa; padding: 20px; border-radius: 5px; border-left: 5px solid #007bff; }
        .summary { background: #e9ecef; padding: 15px; margin: 20px 0; border-radius: 5px; }
        .vulnerability { margin: 10px 0; padding: 15px; border-radius: 5px; border-left: 5px solid; }
        .critical { background: #f8d7da; border-color: #dc3545; }
        .high { background: #fff3cd; border-color: #ffc107; }
        .medium { background: #d1ecf1; border-color: #17a2b8; }
        .low { background: #d4edda; border-color: #28a745; }
        .severity { font-weight: bold; text-transform: uppercase; }
        .file-path { font-family: monospace; background: #f8f9fa; padding: 2px 4px; border-radius: 3px; }
        .evidence { font-family: monospace; background: #f8f9fa; padding: 5px; border-radius: 3px; margin: 5px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🛡️ 安全扫描报告</h1>
        <p>生成时间: 2025-06-12T14:31:26.425129</p>
    </div>
    
    <div class="summary">
        <h2>📊 扫描统计</h2>
        <ul>
            <li>扫描文件数: 362</li>
            <li>发现漏洞数: 19</li>
            <li>🔴 严重: 0</li>
            <li>🟠 高危: 12</li>
            <li>🟡 中危: 7</li>
            <li>🟢 低危: 0</li>
        </ul>
    </div>
    
    <div class="vulnerabilities">
        <h2>🚨 发现的漏洞</h2>
        
            <div class="vulnerability high">
                <div class="severity">high</div>
                <h3>发现硬编码的敏感信息: hardcoded_activation_code</h3>
                <p><strong>文件:</strong> <span class="file-path">iam/iam-service/src/main/resources/application.properties</span> (行 98)</p>
                <p><strong>类型:</strong> hardcoded_activation_code</p>
                <div class="evidence"><strong>证据:</strong><br>activeCode=555555</div>
                <p><strong>建议:</strong> 使用环境变量或配置中心管理敏感信息</p>
            </div>
            
            <div class="vulnerability high">
                <div class="severity">high</div>
                <h3>安全配置问题: csrf_disabled</h3>
                <p><strong>文件:</strong> <span class="file-path">ncm/ncm-service/src/main/java/com/cyberscraft/uep/ncm/config/WebSecurityConfiguration.java</span> (行 40)</p>
                <p><strong>类型:</strong> csrf_disabled</p>
                <div class="evidence"><strong>证据:</strong><br>.csrf().disable()</div>
                <p><strong>建议:</strong> 检查并加强安全配置</p>
            </div>
            
            <div class="vulnerability high">
                <div class="severity">high</div>
                <h3>安全配置问题: csrf_disabled</h3>
                <p><strong>文件:</strong> <span class="file-path">ncm/ncm-service/src/main/java/com/cyberscraft/uep/ncm/config/WebSecurityConfiguration.java</span> (行 50)</p>
                <p><strong>类型:</strong> csrf_disabled</p>
                <div class="evidence"><strong>证据:</strong><br>.csrf().disable()</div>
                <p><strong>建议:</strong> 检查并加强安全配置</p>
            </div>
            
            <div class="vulnerability high">
                <div class="severity">high</div>
                <h3>安全配置问题: csrf_disabled</h3>
                <p><strong>文件:</strong> <span class="file-path">iam/iam-notify-service/src/main/java/com/cyberscraft/uep/iam/notify/config/web/WebSecurityConfiguration.java</span> (行 22)</p>
                <p><strong>类型:</strong> csrf_disabled</p>
                <div class="evidence"><strong>证据:</strong><br>.csrf().disable()</div>
                <p><strong>建议:</strong> 检查并加强安全配置</p>
            </div>
            
            <div class="vulnerability high">
                <div class="severity">high</div>
                <h3>安全配置问题: csrf_disabled</h3>
                <p><strong>文件:</strong> <span class="file-path">iam/iam-service/src/main/java/com/cyberscraft/uep/iam/configuration/kerberos/KerberosWebSecurityConfig.java</span> (行 49)</p>
                <p><strong>类型:</strong> csrf_disabled</p>
                <div class="evidence"><strong>证据:</strong><br>.csrf().disable()</div>
                <p><strong>建议:</strong> 检查并加强安全配置</p>
            </div>
            
            <div class="vulnerability high">
                <div class="severity">high</div>
                <h3>安全配置问题: csrf_disabled</h3>
                <p><strong>文件:</strong> <span class="file-path">iam/iam-service/src/main/java/com/cyberscraft/uep/iam/configuration/server/SecurityConfig.java</span> (行 137)</p>
                <p><strong>类型:</strong> csrf_disabled</p>
                <div class="evidence"><strong>证据:</strong><br>.csrf().disable()</div>
                <p><strong>建议:</strong> 检查并加强安全配置</p>
            </div>
            
            <div class="vulnerability high">
                <div class="severity">high</div>
                <h3>安全配置问题: csrf_disabled</h3>
                <p><strong>文件:</strong> <span class="file-path">iam/iam-service/src/main/java/com/cyberscraft/uep/iam/configuration/server/SecurityConfig.java</span> (行 143)</p>
                <p><strong>类型:</strong> csrf_disabled</p>
                <div class="evidence"><strong>证据:</strong><br>.csrf().disable()</div>
                <p><strong>建议:</strong> 检查并加强安全配置</p>
            </div>
            
            <div class="vulnerability medium">
                <div class="severity">medium</div>
                <h3>安全配置问题: permissive_access</h3>
                <p><strong>文件:</strong> <span class="file-path">iam/iam-service/src/main/java/com/cyberscraft/uep/iam/configuration/server/SecurityConfig.java</span> (行 126)</p>
                <p><strong>类型:</strong> permissive_access</p>
                <div class="evidence"><strong>证据:</strong><br>.permitAll()</div>
                <p><strong>建议:</strong> 检查并加强安全配置</p>
            </div>
            
            <div class="vulnerability medium">
                <div class="severity">medium</div>
                <h3>安全配置问题: permissive_access</h3>
                <p><strong>文件:</strong> <span class="file-path">iam/iam-service/src/main/java/com/cyberscraft/uep/iam/configuration/server/SecurityConfig.java</span> (行 131)</p>
                <p><strong>类型:</strong> permissive_access</p>
                <div class="evidence"><strong>证据:</strong><br>.permitAll()</div>
                <p><strong>建议:</strong> 检查并加强安全配置</p>
            </div>
            
            <div class="vulnerability medium">
                <div class="severity">medium</div>
                <h3>安全配置问题: permissive_access</h3>
                <p><strong>文件:</strong> <span class="file-path">iam/iam-service/src/main/java/com/cyberscraft/uep/iam/configuration/server/SecurityConfig.java</span> (行 145)</p>
                <p><strong>类型:</strong> permissive_access</p>
                <div class="evidence"><strong>证据:</strong><br>.permitAll()</div>
                <p><strong>建议:</strong> 检查并加强安全配置</p>
            </div>
            
            <div class="vulnerability medium">
                <div class="severity">medium</div>
                <h3>安全配置问题: permissive_access</h3>
                <p><strong>文件:</strong> <span class="file-path">iam/iam-service/src/main/java/com/cyberscraft/uep/iam/configuration/server/SecurityConfig.java</span> (行 152)</p>
                <p><strong>类型:</strong> permissive_access</p>
                <div class="evidence"><strong>证据:</strong><br>.permitAll()</div>
                <p><strong>建议:</strong> 检查并加强安全配置</p>
            </div>
            
            <div class="vulnerability medium">
                <div class="severity">medium</div>
                <h3>安全配置问题: permissive_access</h3>
                <p><strong>文件:</strong> <span class="file-path">iam/iam-service/src/main/java/com/cyberscraft/uep/iam/configuration/server/SecurityConfig.java</span> (行 171)</p>
                <p><strong>类型:</strong> permissive_access</p>
                <div class="evidence"><strong>证据:</strong><br>.permitAll()</div>
                <p><strong>建议:</strong> 检查并加强安全配置</p>
            </div>
            
            <div class="vulnerability medium">
                <div class="severity">medium</div>
                <h3>安全配置问题: allow_all_requests</h3>
                <p><strong>文件:</strong> <span class="file-path">iam/iam-service/src/main/java/com/cyberscraft/uep/iam/configuration/server/SecurityConfig.java</span> (行 152)</p>
                <p><strong>类型:</strong> allow_all_requests</p>
                <div class="evidence"><strong>证据:</strong><br>.anyRequest().permitAll()</div>
                <p><strong>建议:</strong> 检查并加强安全配置</p>
            </div>
            
            <div class="vulnerability high">
                <div class="severity">high</div>
                <h3>安全配置问题: csrf_disabled</h3>
                <p><strong>文件:</strong> <span class="file-path">base/oauth2-client/src/main/java/com/cyberscraft/uep/base/oauth2/client/config/WebSecurityConfiguration.java</span> (行 73)</p>
                <p><strong>类型:</strong> csrf_disabled</p>
                <div class="evidence"><strong>证据:</strong><br>.csrf().disable()</div>
                <p><strong>建议:</strong> 检查并加强安全配置</p>
            </div>
            
            <div class="vulnerability medium">
                <div class="severity">medium</div>
                <h3>安全配置问题: permissive_access</h3>
                <p><strong>文件:</strong> <span class="file-path">base/oauth2-client/src/main/java/com/cyberscraft/uep/base/oauth2/client/config/WebSecurityConfiguration.java</span> (行 83)</p>
                <p><strong>类型:</strong> permissive_access</p>
                <div class="evidence"><strong>证据:</strong><br>.permitAll()</div>
                <p><strong>建议:</strong> 检查并加强安全配置</p>
            </div>
            
            <div class="vulnerability high">
                <div class="severity">high</div>
                <h3>安全配置问题: exposed_actuator_endpoints</h3>
                <p><strong>文件:</strong> <span class="file-path">mdm/mdm-service/src/main/resources/application-product.properties</span> (行 74)</p>
                <p><strong>类型:</strong> exposed_actuator_endpoints</p>
                <div class="evidence"><strong>证据:</strong><br>management.endpoints.web.exposure.include=*</div>
                <p><strong>建议:</strong> 检查并加强安全配置</p>
            </div>
            
            <div class="vulnerability high">
                <div class="severity">high</div>
                <h3>安全配置问题: exposed_actuator_endpoints</h3>
                <p><strong>文件:</strong> <span class="file-path">mdm/mdm-service/src/main/resources/application.properties</span> (行 19)</p>
                <p><strong>类型:</strong> exposed_actuator_endpoints</p>
                <div class="evidence"><strong>证据:</strong><br>management.endpoints.web.exposure.include=*</div>
                <p><strong>建议:</strong> 检查并加强安全配置</p>
            </div>
            
            <div class="vulnerability high">
                <div class="severity">high</div>
                <h3>安全配置问题: exposed_actuator_endpoints</h3>
                <p><strong>文件:</strong> <span class="file-path">adm/adm-service/src/main/resources/application.properties</span> (行 25)</p>
                <p><strong>类型:</strong> exposed_actuator_endpoints</p>
                <div class="evidence"><strong>证据:</strong><br>management.endpoints.web.exposure.include=*</div>
                <p><strong>建议:</strong> 检查并加强安全配置</p>
            </div>
            
            <div class="vulnerability high">
                <div class="severity">high</div>
                <h3>JWT 安全问题: jwt_no_expiration</h3>
                <p><strong>文件:</strong> <span class="file-path">iam/iam-service/src/main/java/com/cyberscraft/uep/iam/controller/api/IdTokenEnhancer.java</span> (行 132)</p>
                <p><strong>类型:</strong> jwt_no_expiration</p>
                <div class="evidence"><strong>证据:</strong><br>.setExpiration(null)</div>
                <p><strong>建议:</strong> 使用强密钥和安全的 JWT 配置</p>
            </div>
            
    </div>
</body>
</html>
        