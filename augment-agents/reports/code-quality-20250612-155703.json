{"timestamp": "2025-06-12T15:57:01.378830", "quality_summary": {"files_checked": 100, "issues_found": 1415, "auto_fixable": 21, "critical_issues": 0, "warnings": 1413}, "quality_issues": [{"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/BasicApi.java", "line": 25, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/BasicApi.java", "line": 38, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/BasicApi.java", "line": 53, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/BasicApi.java", "line": 82, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/BasicApi.java", "line": 99, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/BasicApi.java", "line": 116, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/BasicApi.java", "line": 132, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/BasicApi.java", "line": 146, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/BasicApi.java", "line": 162, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/BasicApi.java", "line": 175, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/BasicApi.java", "line": 193, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/BasicApi.java", "line": 209, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/AndroidApi.java", "line": 23, "description": "建议使用统一的大括号风格", "evidence": "{\n\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/AndroidApi.java", "line": 36, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/AndroidApi.java", "line": 53, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/AuthorizationApi.java", "line": 25, "description": "建议使用统一的大括号风格", "evidence": "{\n\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/AuthorizationApi.java", "line": 38, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/AuthorizationApi.java", "line": 53, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/AuthorizationApi.java", "line": 69, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/IOSApi.java", "line": 22, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/admin/AdminAuthorizationApi.java", "line": 20, "description": "建议使用统一的大括号风格", "evidence": "{\n\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/admin/AdminAuthorizationApi.java", "line": 48, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/basic/MessageApi.java", "line": 20, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/basic/MessageApi.java", "line": 32, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/basic/MessageApi.java", "line": 55, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/basic/PushApi.java", "line": 20, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/basic/ConfigApi.java", "line": 21, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/basic/SysApi.java", "line": 22, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/basic/TenantApi.java", "line": 29, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/basic/TenantApi.java", "line": 65, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/basic/TenantApi.java", "line": 78, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/basic/TenantApi.java", "line": 109, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/basic/TenantApi.java", "line": 126, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/basic/TenantApi.java", "line": 142, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/CircleType.java", "line": 8, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/CircleType.java", "line": 16, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/CircleType.java", "line": 20, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/IMdmBaseEnum.java", "line": 3, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/PolicyCategory.java", "line": 3, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/PolicyCategory.java", "line": 11, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/PolicyCategory.java", "line": 18, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/PolicyCategory.java", "line": 21, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/PolicyCategory.java", "line": 22, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/PolicyCategory.java", "line": 23, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/PolicyCategory.java", "line": 30, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/PolicyCategory.java", "line": 31, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/PolicyCategory.java", "line": 32, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "missing_braces", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/PolicyCategory.java", "line": 23, "description": "建议为 if 语句添加大括号", "evidence": "if (category.getValue() =", "auto_fixable": true}, {"type": "missing_braces", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/PolicyCategory.java", "line": 32, "description": "建议为 if 语句添加大括号", "evidence": "if (category.getValue() =", "auto_fixable": true}, {"type": "missing_braces", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/PolicyCategory.java", "line": 22, "description": "建议为 for 语句添加大括号", "evidence": "for (PolicyCategory category : values())", "auto_fixable": true}, {"type": "missing_braces", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/PolicyCategory.java", "line": 31, "description": "建议为 for 语句添加大括号", "evidence": "for (PolicyCategory category : values())", "auto_fixable": true}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/LicenseIndustryTypeConstant.java", "line": 8, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/ClientCmdType.java", "line": 8, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/ClientCmdType.java", "line": 112, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/ClientCmdType.java", "line": 116, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/Constant.java", "line": 8, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/PolicySubCategory.java", "line": 6, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/PolicySubCategory.java", "line": 20, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/PolicySubCategory.java", "line": 29, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/PolicySubCategory.java", "line": 33, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/PolicySubCategory.java", "line": 37, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/PolicySubCategory.java", "line": 38, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/PolicySubCategory.java", "line": 39, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/PolicySubCategory.java", "line": 46, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/PolicySubCategory.java", "line": 47, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/PolicySubCategory.java", "line": 48, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/PolicySubCategory.java", "line": 56, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/PolicySubCategory.java", "line": 65, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "missing_braces", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/PolicySubCategory.java", "line": 39, "description": "建议为 if 语句添加大括号", "evidence": "if (category.getValue() =", "auto_fixable": true}, {"type": "missing_braces", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/PolicySubCategory.java", "line": 48, "description": "建议为 if 语句添加大括号", "evidence": "if (category.getValue() =", "auto_fixable": true}, {"type": "missing_braces", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/PolicySubCategory.java", "line": 38, "description": "建议为 for 语句添加大括号", "evidence": "for (PolicySubCategory category : values())", "auto_fixable": true}, {"type": "missing_braces", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/PolicySubCategory.java", "line": 47, "description": "建议为 for 语句添加大括号", "evidence": "for (PolicySubCategory category : values())", "auto_fixable": true}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/ClientCmdResult.java", "line": 8, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/ClientCmdResult.java", "line": 20, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/ClientCmdResult.java", "line": 27, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/ProgressStatus.java", "line": 8, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/ProgressStatus.java", "line": 28, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/ProgressStatus.java", "line": 32, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/OsTypeConstant.java", "line": 12, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/OsTypeConstant.java", "line": 55, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/OsTypeConstant.java", "line": 67, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/PolicyStatus.java", "line": 3, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/PolicyStatus.java", "line": 5, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/PolicyStatus.java", "line": 12, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/UrlConstant.java", "line": 8, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/ClientCommandType.java", "line": 8, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/ClientCommandType.java", "line": 14, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/ClientCommandType.java", "line": 18, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/TenantFromType.java", "line": 12, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/TenantFromType.java", "line": 29, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/TenantFromType.java", "line": 34, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/TenantFromType.java", "line": 38, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/FileBusinessTypeEnum.java", "line": 13, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/FileBusinessTypeEnum.java", "line": 25, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/FileBusinessTypeEnum.java", "line": 29, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/DfsTypeEnum.java", "line": 12, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/DfsTypeEnum.java", "line": 31, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/DfsTypeEnum.java", "line": 36, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/DfsTypeEnum.java", "line": 40, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/DfsTypeEnum.java", "line": 44, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/DfsTypeEnum.java", "line": 45, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/DfsTypeEnum.java", "line": 46, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/DfsTypeEnum.java", "line": 53, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/DfsTypeEnum.java", "line": 54, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/DfsTypeEnum.java", "line": 55, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "missing_braces", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/DfsTypeEnum.java", "line": 46, "description": "建议为 if 语句添加大括号", "evidence": "if(ele.getName().", "auto_fixable": true}, {"type": "missing_braces", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/DfsTypeEnum.java", "line": 55, "description": "建议为 if 语句添加大括号", "evidence": "if(ele.name.equalsIgnoreCase(name))", "auto_fixable": true}, {"type": "missing_braces", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/DfsTypeEnum.java", "line": 45, "description": "建议为 for 语句添加大括号", "evidence": "for (DfsTypeEnum ele : values())", "auto_fixable": true}, {"type": "missing_braces", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/DfsTypeEnum.java", "line": 54, "description": "建议为 for 语句添加大括号", "evidence": "for (DfsTypeEnum ele : values())", "auto_fixable": true}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/errors/MdmErrorType.java", "line": 15, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/errors/MdmErrorType.java", "line": 197, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/errors/MdmErrorType.java", "line": 202, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/errors/MdmErrorType.java", "line": 209, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/event/Event.java", "line": 6, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/event/Event.java", "line": 45, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/event/Event.java", "line": 49, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/event/Event.java", "line": 54, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/event/Event.java", "line": 58, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/event/Event.java", "line": 63, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/event/Event.java", "line": 67, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/event/Event.java", "line": 72, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/event/Event.java", "line": 76, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/event/Event.java", "line": 81, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/event/Event.java", "line": 85, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/event/Event.java", "line": 90, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/event/Event.java", "line": 94, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/event/Event.java", "line": 99, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/event/Event.java", "line": 103, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/event/Event.java", "line": 108, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/event/Event.java", "line": 112, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/event/Event.java", "line": 117, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/event/Event.java", "line": 121, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/event/Event.java", "line": 127, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/TenantCreateRequestDto.java", "line": 16, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/TenantCreateRequestDto.java", "line": 68, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/TenantCreateRequestDto.java", "line": 72, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/TenantCreateRequestDto.java", "line": 76, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/TenantCreateRequestDto.java", "line": 80, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/TenantCreateRequestDto.java", "line": 84, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/TenantCreateRequestDto.java", "line": 88, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/TenantCreateRequestDto.java", "line": 92, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/TenantCreateRequestDto.java", "line": 96, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/TenantCreateRequestDto.java", "line": 100, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/TenantCreateRequestDto.java", "line": 104, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/TenantCreateRequestDto.java", "line": 108, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/TenantCreateRequestDto.java", "line": 112, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/TenantCreateRequestDto.java", "line": 116, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/TenantCreateRequestDto.java", "line": 120, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/TenantCreateRequestDto.java", "line": 124, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/TenantCreateRequestDto.java", "line": 128, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/TenantCreateRequestDto.java", "line": 133, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientSignatureSecretRotationVO.java", "line": 13, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientSignatureSecretRotationVO.java", "line": 15, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientSignatureSecretRotationVO.java", "line": 19, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientSignatureSecretRotationVO.java", "line": 30, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientSignatureSecretRotationVO.java", "line": 34, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientSignatureSecretRotationVO.java", "line": 38, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientSignatureSecretRotationVO.java", "line": 42, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientSignatureSecretRotationVO.java", "line": 47, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationVO.java", "line": 6, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationVO.java", "line": 105, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationVO.java", "line": 109, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationVO.java", "line": 113, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationVO.java", "line": 117, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationVO.java", "line": 121, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationVO.java", "line": 125, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationVO.java", "line": 129, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationVO.java", "line": 133, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationVO.java", "line": 137, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationVO.java", "line": 141, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationVO.java", "line": 145, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationVO.java", "line": 149, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationVO.java", "line": 153, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationVO.java", "line": 157, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationVO.java", "line": 161, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationVO.java", "line": 165, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationVO.java", "line": 169, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationVO.java", "line": 173, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationVO.java", "line": 177, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationVO.java", "line": 181, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationVO.java", "line": 185, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationVO.java", "line": 189, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationVO.java", "line": 193, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationVO.java", "line": 197, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationVO.java", "line": 201, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationVO.java", "line": 205, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationVO.java", "line": 209, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationVO.java", "line": 213, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationVO.java", "line": 217, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationVO.java", "line": 221, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationVO.java", "line": 225, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationVO.java", "line": 229, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationVO.java", "line": 233, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationVO.java", "line": 237, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationVO.java", "line": 241, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationVO.java", "line": 245, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationVO.java", "line": 249, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationVO.java", "line": 253, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationVO.java", "line": 258, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationDto.java", "line": 6, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationDto.java", "line": 30, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationDto.java", "line": 34, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationDto.java", "line": 38, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationDto.java", "line": 42, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationDto.java", "line": 46, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationDto.java", "line": 50, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationDto.java", "line": 54, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationDto.java", "line": 58, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationDto.java", "line": 63, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/cmd/CmdBodyPolicyDto.java", "line": 9, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/cmd/CmdBodyPolicyDto.java", "line": 13, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/cmd/CmdBodyPolicyDto.java", "line": 17, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/cmd/CmdBodyPolicyDto.java", "line": 21, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/cmd/CmdBodyEraseDeviceDto.java", "line": 11, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/cmd/CmdBodyEraseDeviceDto.java", "line": 19, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/cmd/CmdBodyEraseDeviceDto.java", "line": 23, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/cmd/CmdDto.java", "line": 9, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/cmd/CmdDto.java", "line": 17, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/cmd/CmdDto.java", "line": 21, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/cmd/CmdDto.java", "line": 25, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/cmd/CmdDto.java", "line": 29, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/cmd/CmdBodyLockScreenDto.java", "line": 9, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/cmd/CmdBodyLockScreenDto.java", "line": 13, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/cmd/CmdBodyLockScreenDto.java", "line": 17, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/cmd/CmdTargetDto.java", "line": 10, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/cmd/CmdTargetDto.java", "line": 25, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/cmd/CmdTargetDto.java", "line": 29, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/cmd/CmdTargetDto.java", "line": 33, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/cmd/CmdTargetDto.java", "line": 37, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/cmd/CmdTargetDto.java", "line": 41, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/cmd/CmdTargetDto.java", "line": 45, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/cmd/CmdTargetDto.java", "line": 49, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/cmd/CmdTargetDto.java", "line": 53, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/cmd/CmdTargetDto.java", "line": 57, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/cmd/CmdTargetDto.java", "line": 61, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/cmd/CmdTargetDto.java", "line": 65, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/cmd/CmdTargetDto.java", "line": 69, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/cmd/CmdTargetDto.java", "line": 74, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/settings/UpdateCertificateDto.java", "line": 9, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/settings/UpdateCertificateDto.java", "line": 28, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/settings/UpdateCertificateDto.java", "line": 32, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/settings/UpdateCertificateDto.java", "line": 36, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/settings/UpdateCertificateDto.java", "line": 40, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/settings/UpdateCertificateDto.java", "line": 44, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/settings/UpdateCertificateDto.java", "line": 48, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/settings/UpdateCertificateDto.java", "line": 52, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/settings/UpdateCertificateDto.java", "line": 56, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/settings/UpdateCertificateDto.java", "line": 60, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/settings/UpdateCertificateDto.java", "line": 64, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/settings/CertificateInfoVo.java", "line": 6, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/settings/CertificateInfoVo.java", "line": 44, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/settings/CertificateInfoVo.java", "line": 48, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/settings/CertificateInfoVo.java", "line": 52, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/settings/CertificateInfoVo.java", "line": 56, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/settings/CertificateInfoVo.java", "line": 60, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/settings/CertificateInfoVo.java", "line": 64, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/settings/CertificateInfoVo.java", "line": 68, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/settings/CertificateInfoVo.java", "line": 72, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/settings/CertificateInfoVo.java", "line": 76, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/settings/CertificateInfoVo.java", "line": 80, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/settings/CertificateInfoVo.java", "line": 84, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/settings/CertificateInfoVo.java", "line": 88, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/settings/CertificateInfoVo.java", "line": 92, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/settings/CertificateInfoVo.java", "line": 96, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/settings/CertificateInfoVo.java", "line": 100, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/settings/CertificateInfoVo.java", "line": 104, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 13, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 40, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 44, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 48, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 52, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 56, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 60, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 64, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 68, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 72, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 76, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 80, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 84, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 88, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 92, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 96, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 100, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 104, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 108, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 112, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 116, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 120, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 124, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 128, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 132, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 136, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 140, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 144, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 148, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 152, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 156, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 160, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 164, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 168, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 172, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 176, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 180, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 184, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 188, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 192, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 196, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 200, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 204, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 208, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 212, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 216, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 220, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 224, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/app/UserApplicationDto.java", "line": 228, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/PullCommandRequestBody.java", "line": 7, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/PullCommandRequestBody.java", "line": 20, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/PullCommandRequestBody.java", "line": 24, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/PullCommandRequestBody.java", "line": 28, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/PullCommandRequestBody.java", "line": 32, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/PullCommandRequestBody.java", "line": 36, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/PullCommandRequestBody.java", "line": 40, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/PullCommandRequestBody.java", "line": 44, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/PullCommandRequestBody.java", "line": 48, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/PullCommandRequestBody.java", "line": 52, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/PullCommandRequestBody.java", "line": 56, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/DeviceActivatedCmdBody.java", "line": 14, "description": "建议使用统一的大括号风格", "evidence": "{\n\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/DeviceActivatedCmdBody.java", "line": 35, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/DeviceActivatedCmdBody.java", "line": 39, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/DeviceActivatedCmdBody.java", "line": 43, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/DeviceActivatedCmdBody.java", "line": 47, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/PullCommandRequestHeader.java", "line": 10, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/PullCommandRequestHeader.java", "line": 23, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/PullCommandRequestHeader.java", "line": 27, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/PullCommandRequestHeader.java", "line": 31, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/PullCommandRequestHeader.java", "line": 35, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/PullCommandRequestHeader.java", "line": 39, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/PullCommandRequestHeader.java", "line": 43, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/PullCommandRequestHeader.java", "line": 47, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/PullCommandRequestHeader.java", "line": 51, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/PullCommandRequestHeader.java", "line": 55, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/PullCommandRequestHeader.java", "line": 59, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/PullCommandRequestHeader.java", "line": 63, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/PullCommandRequestHeader.java", "line": 67, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/PullCommandRequestHeader.java", "line": 71, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/PullCommandRequestHeader.java", "line": 75, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/PullCommandRequestHeader.java", "line": 79, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/PullCommandRequestHeader.java", "line": 83, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 14, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 470, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 474, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 478, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 482, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 486, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 490, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 494, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 498, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 502, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 506, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 510, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 514, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 518, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 522, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 526, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 530, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 534, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 538, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 542, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 546, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 550, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 554, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 558, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 562, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 566, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 570, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 574, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 578, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 582, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 586, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 590, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 594, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 598, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 602, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 606, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 610, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 614, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 618, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 622, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 626, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 630, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 634, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 638, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 642, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 646, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 650, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 654, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 658, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 662, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 666, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 670, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 674, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 678, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 682, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 686, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 690, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 694, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 698, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 702, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 706, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 710, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 714, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 718, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 722, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 726, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 730, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 734, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 738, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 742, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 746, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 750, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 754, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 758, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 762, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 766, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 770, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 774, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 778, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 782, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 786, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 790, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 794, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 798, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 802, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 806, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 810, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 814, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 818, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 822, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 826, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 830, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 834, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 838, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 842, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 846, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 850, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 854, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 858, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 862, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 866, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 870, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 874, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 878, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 882, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 886, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 890, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 894, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 898, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 902, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 906, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 910, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 914, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 918, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 922, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 926, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 930, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 934, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 938, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 942, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 946, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 950, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 954, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 958, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 962, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 966, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 970, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 974, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 978, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 982, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 986, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 990, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 994, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 998, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 1002, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 1006, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 1010, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 1014, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 1018, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 1022, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 1026, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 1030, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 1034, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 1038, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 1042, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateDeviceCmdBody.java", "line": 1046, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/ClientCmdBody.java", "line": 10, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/ClientCmdBody.java", "line": 13, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/ClientCmdBody.java", "line": 17, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/DeviceStatusQueryCmdBody.java", "line": 3, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/ClientCmdRequestDto.java", "line": 17, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/ClientCmdRequestDto.java", "line": 60, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/ClientCmdRequestDto.java", "line": 64, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/ClientCmdRequestDto.java", "line": 68, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/ClientCmdRequestDto.java", "line": 72, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/ClientCmdRequestDto.java", "line": 76, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/ClientCmdRequestDto.java", "line": 80, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/ClientCmdRequestDto.java", "line": 84, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/ClientCmdRequestDto.java", "line": 88, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/ClientCmdRequestDto.java", "line": 92, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/ClientCmdRequestDto.java", "line": 96, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/ClientCmdRequestDto.java", "line": 100, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/ClientCmdRequestDto.java", "line": 104, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/NICInfo.java", "line": 14, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/NICInfo.java", "line": 26, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/NICInfo.java", "line": 30, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/NICInfo.java", "line": 34, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/NICInfo.java", "line": 38, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/NICInfo.java", "line": 42, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/NICInfo.java", "line": 46, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 16, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 158, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 162, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 166, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 170, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 174, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 178, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 182, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 186, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 190, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 194, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 198, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 202, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 206, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 210, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 214, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 218, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 222, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 226, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 230, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 234, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 238, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 242, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 246, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 250, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 254, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 258, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 262, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 266, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 270, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 274, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 278, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 282, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 286, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 290, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 294, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 298, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 302, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 306, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 310, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 314, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 318, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 322, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 326, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 330, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 334, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 338, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 342, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 346, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 350, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 354, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 358, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 362, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 366, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 370, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 374, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 378, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 382, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 386, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 390, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 394, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 398, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 402, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 406, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 410, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 414, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 418, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 422, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 426, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 430, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 434, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 438, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 442, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 446, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 450, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 454, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 458, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 462, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 466, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 470, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 474, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 478, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 482, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 486, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 490, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 494, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 498, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 502, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 506, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 510, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 514, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 518, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadRisksCmdBody.java", "line": 522, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadViolationCmdBody.java", "line": 3, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadViolationCmdBody.java", "line": 9, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadViolationCmdBody.java", "line": 13, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadViolationCmdBody.java", "line": 17, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadViolationCmdBody.java", "line": 21, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadViolationCmdBody.java", "line": 25, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadViolationCmdBody.java", "line": 29, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadViolationCmdBody.java", "line": 33, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadViolationCmdBody.java", "line": 37, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/PullCommandRequest.java", "line": 9, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/PullCommandRequest.java", "line": 19, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/PullCommandRequest.java", "line": 23, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/PullCommandRequest.java", "line": 27, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/PullCommandRequest.java", "line": 31, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/PullCommandRequest.java", "line": 35, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateNetTrafficCmdBody.java", "line": 17, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateNetTrafficCmdBody.java", "line": 28, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateNetTrafficCmdBody.java", "line": 32, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadAllAppCmdBody.java", "line": 15, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadAllAppCmdBody.java", "line": 23, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadAllAppCmdBody.java", "line": 27, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/ProcessInfo.java", "line": 14, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/ProcessInfo.java", "line": 23, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/ProcessInfo.java", "line": 27, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/ProcessInfo.java", "line": 31, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/ProcessInfo.java", "line": 35, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/RequestNetCmdBody.java", "line": 8, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/RequestNetCmdBody.java", "line": 13, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/RequestNetCmdBody.java", "line": 17, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/MdmChallengeVO.java", "line": 8, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/MdmChallengeVO.java", "line": 11, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/MdmChallengeVO.java", "line": 15, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/MdmChallengeVO.java", "line": 19, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateVersionCmdBody.java", "line": 12, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateVersionCmdBody.java", "line": 41, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateVersionCmdBody.java", "line": 45, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateVersionCmdBody.java", "line": 49, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateVersionCmdBody.java", "line": 53, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateVersionCmdBody.java", "line": 57, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateVersionCmdBody.java", "line": 61, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateVersionCmdBody.java", "line": 65, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateVersionCmdBody.java", "line": 69, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateVersionCmdBody.java", "line": 73, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateVersionCmdBody.java", "line": 77, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/EmptyClientCmdBody.java", "line": 13, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/EmptyClientCmdBody.java", "line": 56, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/EmptyClientCmdBody.java", "line": 60, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/EmptyClientCmdBody.java", "line": 64, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/EmptyClientCmdBody.java", "line": 68, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/EmptyClientCmdBody.java", "line": 72, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/EmptyClientCmdBody.java", "line": 73, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/EmptyClientCmdBody.java", "line": 79, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/EmptyClientCmdBody.java", "line": 83, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/EmptyClientCmdBody.java", "line": 84, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/EmptyClientCmdBody.java", "line": 90, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/EmptyClientCmdBody.java", "line": 94, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/EmptyClientCmdBody.java", "line": 98, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/EmptyClientCmdBody.java", "line": 102, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/EmptyClientCmdBody.java", "line": 106, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/EmptyClientCmdBody.java", "line": 110, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/EmptyClientCmdBody.java", "line": 114, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "missing_braces", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/EmptyClientCmdBody.java", "line": 73, "description": "建议为 if 语句添加大括号", "evidence": "if(StringUtils.isNotBlank(uemVersion))", "auto_fixable": true}, {"type": "missing_braces", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/EmptyClientCmdBody.java", "line": 84, "description": "建议为 if 语句添加大括号", "evidence": "if(StringUtils.isNotBlank(zsbVersion))", "auto_fixable": true}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateBatteryCmdBody.java", "line": 11, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateBatteryCmdBody.java", "line": 20, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateBatteryCmdBody.java", "line": 24, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateBatteryCmdBody.java", "line": 28, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateBatteryCmdBody.java", "line": 32, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateBatteryCmdBody.java", "line": 36, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateBatteryCmdBody.java", "line": 40, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateBatteryCmdBody.java", "line": 44, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateBatteryCmdBody.java", "line": 48, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateBatteryCmdBody.java", "line": 52, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateBatteryCmdBody.java", "line": 56, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/DeviceMdmDeActivateCmdBody.java", "line": 8, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/PushOperOkCmdBody.java", "line": 12, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/PushOperOkCmdBody.java", "line": 37, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/PushOperOkCmdBody.java", "line": 41, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/PushOperOkCmdBody.java", "line": 46, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/PushOperOkCmdBody.java", "line": 51, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/PushOperOkCmdBody.java", "line": 55, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/PushOperOkCmdBody.java", "line": 59, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/PushOperOkCmdBody.java", "line": 63, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/PushOperOkCmdBody.java", "line": 67, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/FullSyncPolicyDTO.java", "line": 17, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/FullSyncPolicyDTO.java", "line": 33, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/FullSyncPolicyDTO.java", "line": 37, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/FullSyncPolicyDTO.java", "line": 41, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/FullSyncPolicyDTO.java", "line": 45, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/FullSyncPolicyDTO.java", "line": 49, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/FullSyncPolicyDTO.java", "line": 53, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/FullSyncPolicyDTO.java", "line": 57, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/FullSyncPolicyDTO.java", "line": 61, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateAppPushTokenCmdBody.java", "line": 12, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateAppPushTokenCmdBody.java", "line": 24, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateAppPushTokenCmdBody.java", "line": 28, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateAppPushTokenCmdBody.java", "line": 32, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateAppPushTokenCmdBody.java", "line": 36, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateAppPushTokenCmdBody.java", "line": 40, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UpdateAppPushTokenCmdBody.java", "line": 44, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadDualStatusCmdBody.java", "line": 14, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadDualStatusCmdBody.java", "line": 23, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadDualStatusCmdBody.java", "line": 27, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadJailbrokenCmdBody.java", "line": 13, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadJailbrokenCmdBody.java", "line": 29, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadJailbrokenCmdBody.java", "line": 33, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadJailbrokenCmdBody.java", "line": 37, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/UploadJailbrokenCmdBody.java", "line": 41, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/VirusInfo.java", "line": 13, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/VirusInfo.java", "line": 23, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/VirusInfo.java", "line": 27, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/VirusInfo.java", "line": 31, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/VirusInfo.java", "line": 35, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/QueryUserProfileCommandRequest.java", "line": 7, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/QueryUserProfileCommandRequest.java", "line": 17, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/QueryUserProfileCommandRequest.java", "line": 21, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/QueryUserProfileCommandRequest.java", "line": 25, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/QueryUserProfileCommandRequest.java", "line": 29, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/WifiInfo.java", "line": 14, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/WifiInfo.java", "line": 24, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/WifiInfo.java", "line": 28, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/WifiInfo.java", "line": 32, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/WifiInfo.java", "line": 36, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/group/GroupQueryDto.java", "line": 11, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/group/GroupQueryDto.java", "line": 23, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/group/GroupQueryDto.java", "line": 27, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/group/GroupQueryDto.java", "line": 31, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/group/GroupQueryDto.java", "line": 35, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/group/GroupQueryDto.java", "line": 39, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/group/GroupQueryDto.java", "line": 43, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/group/GroupQueryDto.java", "line": 47, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/group/GroupQueryDto.java", "line": 51, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/group/GroupQueryDto.java", "line": 55, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/group/GroupQueryDto.java", "line": 59, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/group/GroupQueryDto.java", "line": 63, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/group/GroupQueryDto.java", "line": 67, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/group/GroupQueryDto.java", "line": 71, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/group/GroupQueryDto.java", "line": 75, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/group/GroupQueryDto.java", "line": 79, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/group/GroupQueryDto.java", "line": 83, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/group/GroupNodeVO.java", "line": 8, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/group/GroupNodeVO.java", "line": 20, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/group/GroupNodeVO.java", "line": 24, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/group/GroupNodeVO.java", "line": 28, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/group/GroupNodeVO.java", "line": 32, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/group/GroupNodeVO.java", "line": 36, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/group/GroupNodeVO.java", "line": 40, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/group/GroupNodeVO.java", "line": 44, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/group/GroupNodeVO.java", "line": 48, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/group/GroupNodeVO.java", "line": 52, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/group/GroupNodeVO.java", "line": 56, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/group/GroupNodeVO.java", "line": 60, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/group/GroupNodeVO.java", "line": 64, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/group/GroupNodeVO.java", "line": 68, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/group/GroupNodeVO.java", "line": 72, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/group/GroupNodeVO.java", "line": 76, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/group/GroupNodeVO.java", "line": 80, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/group/GroupNodeVO.java", "line": 84, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/group/GroupNodeVO.java", "line": 88, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/group/GroupNodeVO.java", "line": 92, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/group/GroupNodeVO.java", "line": 96, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/group/GroupNodeVO.java", "line": 100, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/group/GroupNodeVO.java", "line": 101, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/group/GroupNodeVO.java", "line": 105, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "missing_braces", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/group/GroupNodeVO.java", "line": 101, "description": "建议为 if 语句添加大括号", "evidence": "if (obj == null) ", "auto_fixable": true}, {"type": "missing_braces", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/group/GroupNodeVO.java", "line": 105, "description": "建议为 if 语句添加大括号", "evidence": "if (!(obj instanceof GroupNodeVO))", "auto_fixable": true}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenRequestDto.java", "line": 20, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenRequestDto.java", "line": 138, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenRequestDto.java", "line": 142, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenRequestDto.java", "line": 146, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenRequestDto.java", "line": 150, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenRequestDto.java", "line": 154, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenRequestDto.java", "line": 158, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenRequestDto.java", "line": 162, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenRequestDto.java", "line": 166, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenRequestDto.java", "line": 170, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenRequestDto.java", "line": 174, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenRequestDto.java", "line": 178, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenRequestDto.java", "line": 182, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenRequestDto.java", "line": 186, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenRequestDto.java", "line": 190, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenRequestDto.java", "line": 194, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenRequestDto.java", "line": 198, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenRequestDto.java", "line": 202, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenRequestDto.java", "line": 206, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenRequestDto.java", "line": 210, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenRequestDto.java", "line": 214, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenRequestDto.java", "line": 218, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenRequestDto.java", "line": 222, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenRequestDto.java", "line": 226, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenRequestDto.java", "line": 230, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenRequestDto.java", "line": 234, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenRequestDto.java", "line": 238, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenRequestDto.java", "line": 242, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenRequestDto.java", "line": 246, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenRequestDto.java", "line": 250, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenRequestDto.java", "line": 254, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenRequestDto.java", "line": 258, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenRequestDto.java", "line": 265, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenVO.java", "line": 14, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenVO.java", "line": 110, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenVO.java", "line": 114, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenVO.java", "line": 118, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenVO.java", "line": 122, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenVO.java", "line": 126, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenVO.java", "line": 130, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenVO.java", "line": 134, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenVO.java", "line": 138, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenVO.java", "line": 142, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenVO.java", "line": 146, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenVO.java", "line": 150, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenVO.java", "line": 154, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenVO.java", "line": 158, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenVO.java", "line": 162, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenVO.java", "line": 166, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenVO.java", "line": 170, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenVO.java", "line": 174, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenVO.java", "line": 178, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenVO.java", "line": 182, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenVO.java", "line": 186, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenVO.java", "line": 190, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenVO.java", "line": 194, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenVO.java", "line": 198, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenVO.java", "line": 202, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenVO.java", "line": 206, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenVO.java", "line": 210, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenVO.java", "line": 214, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenVO.java", "line": 218, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenVO.java", "line": 222, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenVO.java", "line": 226, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenVO.java", "line": 230, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenVO.java", "line": 234, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenVOV2.java", "line": 14, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenVOV2.java", "line": 33, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenVOV2.java", "line": 37, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenVOV2.java", "line": 41, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenVOV2.java", "line": 45, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenVOV2.java", "line": 49, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/oauth2/AccessTokenVOV2.java", "line": 53, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/admin/AdminLoginDto.java", "line": 13, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/admin/AdminLoginDto.java", "line": 65, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/admin/AdminLoginDto.java", "line": 69, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/admin/AdminLoginDto.java", "line": 73, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/admin/AdminLoginDto.java", "line": 77, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/admin/AdminLoginDto.java", "line": 81, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/admin/AdminLoginDto.java", "line": 85, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/admin/AdminLoginDto.java", "line": 89, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/admin/AdminLoginDto.java", "line": 93, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/admin/AdminLoginDto.java", "line": 97, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/admin/AdminLoginDto.java", "line": 101, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/admin/AdminLoginDto.java", "line": 105, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/admin/AdminLoginDto.java", "line": 109, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/admin/AdminLoginDto.java", "line": 113, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/admin/AdminLoginDto.java", "line": 117, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/admin/AdminLoginDto.java", "line": 121, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/admin/AdminLoginDto.java", "line": 125, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/admin/AdminTokenVO.java", "line": 12, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/admin/AdminTokenVO.java", "line": 24, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/admin/AdminTokenVO.java", "line": 28, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/admin/AdminTokenVO.java", "line": 32, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/admin/AdminTokenVO.java", "line": 36, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/admin/SwaggerLoginDto.java", "line": 13, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/admin/SwaggerLoginDto.java", "line": 37, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/admin/SwaggerLoginDto.java", "line": 41, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/admin/SwaggerLoginDto.java", "line": 45, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/admin/SwaggerLoginDto.java", "line": 49, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/admin/SwaggerLoginDto.java", "line": 53, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/admin/SwaggerLoginDto.java", "line": 57, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/admin/AdminLoginPageInfoVO.java", "line": 11, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/admin/AdminLoginPageInfoVO.java", "line": 40, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/admin/AdminLoginPageInfoVO.java", "line": 44, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/admin/AdminLoginPageInfoVO.java", "line": 48, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/admin/AdminLoginPageInfoVO.java", "line": 52, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/admin/AdminLoginPageInfoVO.java", "line": 56, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/admin/AdminLoginPageInfoVO.java", "line": 60, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/admin/AdminLoginPageInfoVO.java", "line": 64, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/admin/AdminLoginPageInfoVO.java", "line": 68, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/admin/AdminLoginPageInfoVO.java", "line": 72, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/admin/AdminLoginPageInfoVO.java", "line": 76, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/admin/AdminLoginPageInfoVO.java", "line": 80, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/admin/AdminLoginPageInfoVO.java", "line": 84, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/basic/PushServerInfoVO.java", "line": 14, "description": "建议使用统一的大括号风格", "evidence": "{\n\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/basic/PushServerInfoVO.java", "line": 54, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/basic/PushServerInfoVO.java", "line": 58, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/basic/PushServerInfoVO.java", "line": 62, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/basic/PushServerInfoVO.java", "line": 66, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/basic/PushServerInfoVO.java", "line": 70, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/basic/PushServerInfoVO.java", "line": 74, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/basic/PushServerInfoVO.java", "line": 78, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/basic/PushServerInfoVO.java", "line": 82, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/basic/PushServerInfoVO.java", "line": 86, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/basic/PushServerInfoVO.java", "line": 90, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/basic/PushServerInfoVO.java", "line": 94, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/basic/PushServerInfoVO.java", "line": 98, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/basic/PushServerInfoVO.java", "line": 102, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/basic/PushServerInfoVO.java", "line": 106, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/basic/LocationInfoVO.java", "line": 14, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/basic/LocationInfoVO.java", "line": 41, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/basic/LocationInfoVO.java", "line": 45, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/basic/LocationInfoVO.java", "line": 49, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/basic/LocationInfoVO.java", "line": 53, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/basic/LocationInfoVO.java", "line": 57, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/basic/LocationInfoVO.java", "line": 61, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/basic/LocationInfoVO.java", "line": 65, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/basic/LocationInfoVO.java", "line": 69, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/basic/ServerInfoVO.java", "line": 14, "description": "建议使用统一的大括号风格", "evidence": "{\n\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/basic/ServerInfoVO.java", "line": 32, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/basic/ServerInfoVO.java", "line": 36, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/basic/ServerInfoVO.java", "line": 40, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/basic/ServerInfoVO.java", "line": 44, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/basic/ServerInfoVO.java", "line": 48, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/basic/ServerInfoVO.java", "line": 52, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/basic/ServerInfoVO.java", "line": 56, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/basic/ServerInfoVO.java", "line": 60, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/basic/ServerInfoVO.java", "line": 64, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/basic/ServerInfoVO.java", "line": 68, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/basic/ServerInfoVO.java", "line": 72, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/basic/ServerInfoVO.java", "line": 76, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/DynaLabelUserGroup.java", "line": 10, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/DynaLabelUserGroup.java", "line": 20, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/DynaLabelUserGroup.java", "line": 24, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/DynaLabelUserGroup.java", "line": 28, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/DynaLabelUserGroup.java", "line": 32, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/DynaLabelUserGroup.java", "line": 36, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/DynaLabelUserGroup.java", "line": 40, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/DynaLabelUserGroup.java", "line": 44, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/DynaLabelUserGroup.java", "line": 48, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/DynaLabelUserGroup.java", "line": 52, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/DynaLabelUserGroup.java", "line": 56, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/DynaLabelUserGroup.java", "line": 60, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/DynaLabelUserGroup.java", "line": 64, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/DynaLabelUserGroup.java", "line": 68, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/DynaLabelUserGroup.java", "line": 72, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/LabelDetailsVO.java", "line": 14, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/LabelDetailsVO.java", "line": 50, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/LabelDetailsVO.java", "line": 54, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/LabelDetailsVO.java", "line": 63, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/LabelDetailsVO.java", "line": 67, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/LabelDetailsVO.java", "line": 71, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/LabelDetailsVO.java", "line": 75, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/LabelDetailsVO.java", "line": 79, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/LabelDetailsVO.java", "line": 83, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/LabelDetailsVO.java", "line": 87, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/LabelDetailsVO.java", "line": 91, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/LabelDetailsVO.java", "line": 95, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/LabelDetailsVO.java", "line": 99, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/LabelDetailsVO.java", "line": 104, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/LabelDetailsVO.java", "line": 108, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/LabelDetailsVO.java", "line": 112, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/LabelDetailsVO.java", "line": 116, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/LabelDetailsVO.java", "line": 120, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/LabelDetailsVO.java", "line": 124, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/StaticLabelVO.java", "line": 18, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/StaticLabelVO.java", "line": 59, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/StaticLabelVO.java", "line": 63, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/StaticLabelVO.java", "line": 67, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/StaticLabelVO.java", "line": 71, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/StaticLabelVO.java", "line": 75, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/StaticLabelVO.java", "line": 79, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/StaticLabelVO.java", "line": 83, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/StaticLabelVO.java", "line": 87, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/StaticLabelVO.java", "line": 91, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/StaticLabelVO.java", "line": 95, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/StaticLabelVO.java", "line": 99, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/StaticLabelVO.java", "line": 103, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/StaticLabelVO.java", "line": 107, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/label/StaticLabelVO.java", "line": 111, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 13, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 94, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 99, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 116, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 139, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 165, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 169, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 173, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 177, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 181, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 185, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 189, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 193, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 197, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 201, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 205, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 209, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 213, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 217, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 221, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 225, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 229, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 233, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 237, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 241, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 245, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 249, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 253, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 257, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 261, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 265, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 269, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 273, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 277, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 281, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 285, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 289, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 293, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 297, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 301, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 305, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 309, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 313, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 317, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 321, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 325, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 329, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 333, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 337, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 341, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 345, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 349, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 353, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 357, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 361, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 365, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 369, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 373, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 377, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 381, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 385, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 389, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 393, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 397, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 401, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 405, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 409, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 413, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 417, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 421, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 425, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 429, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 433, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 437, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 441, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 445, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 449, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 453, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 457, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 461, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 465, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 469, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 473, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 477, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 481, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 485, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 489, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 493, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 497, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 501, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 505, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 509, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 513, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 517, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserDetailVO.java", "line": 521, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserInfoVO.java", "line": 12, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserInfoVO.java", "line": 115, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserInfoVO.java", "line": 119, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserInfoVO.java", "line": 123, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserInfoVO.java", "line": 127, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserInfoVO.java", "line": 131, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserInfoVO.java", "line": 135, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserInfoVO.java", "line": 139, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserInfoVO.java", "line": 143, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserInfoVO.java", "line": 147, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserInfoVO.java", "line": 151, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserInfoVO.java", "line": 155, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserInfoVO.java", "line": 159, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserInfoVO.java", "line": 163, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserInfoVO.java", "line": 167, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserInfoVO.java", "line": 171, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserInfoVO.java", "line": 175, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserInfoVO.java", "line": 179, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserInfoVO.java", "line": 183, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserInfoVO.java", "line": 187, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserInfoVO.java", "line": 191, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserInfoVO.java", "line": 195, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserInfoVO.java", "line": 199, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserInfoVO.java", "line": 203, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserInfoVO.java", "line": 207, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserInfoVO.java", "line": 211, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserInfoVO.java", "line": 215, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserInfoVO.java", "line": 219, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserInfoVO.java", "line": 223, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserInfoVO.java", "line": 227, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserInfoVO.java", "line": 231, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserInfoVO.java", "line": 235, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserInfoVO.java", "line": 239, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserInfoVO.java", "line": 243, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserInfoVO.java", "line": 247, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserInfoVO.java", "line": 251, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserInfoVO.java", "line": 255, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserPolicyDeviceNumVO.java", "line": 7, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserPolicyDeviceNumVO.java", "line": 25, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserPolicyDeviceNumVO.java", "line": 28, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserPolicyDeviceNumVO.java", "line": 31, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserPolicyDeviceNumVO.java", "line": 34, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserPolicyDeviceNumVO.java", "line": 37, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserPolicyDeviceNumVO.java", "line": 40, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/LabelQueryDto.java", "line": 13, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/LabelQueryDto.java", "line": 19, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/LabelQueryDto.java", "line": 23, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/LabelQueryDto.java", "line": 27, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/LabelQueryDto.java", "line": 31, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/LabelQueryDto.java", "line": 35, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/LabelQueryDto.java", "line": 39, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserGroupVO.java", "line": 10, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserGroupVO.java", "line": 33, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserGroupVO.java", "line": 37, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserGroupVO.java", "line": 41, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserGroupVO.java", "line": 45, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserGroupVO.java", "line": 49, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserGroupVO.java", "line": 53, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserGroupVO.java", "line": 57, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserGroupVO.java", "line": 61, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserGroupVO.java", "line": 65, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserGroupVO.java", "line": 69, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/DynamicLabelUserGroupQueryDto.java", "line": 12, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/DynamicLabelUserGroupQueryDto.java", "line": 16, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/DynamicLabelUserGroupQueryDto.java", "line": 20, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/DynamicLabelUserGroupQueryDto.java", "line": 24, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/DynamicLabelUserGroupQueryDto.java", "line": 28, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserListVO.java", "line": 11, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserListVO.java", "line": 28, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserListVO.java", "line": 32, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserListVO.java", "line": 36, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserListVO.java", "line": 40, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserListVO.java", "line": 44, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserListVO.java", "line": 48, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserListVO.java", "line": 52, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserListVO.java", "line": 56, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserListVO.java", "line": 60, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserListVO.java", "line": 64, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserListVO.java", "line": 68, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserListVO.java", "line": 72, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserListVO.java", "line": 76, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserListVO.java", "line": 80, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserListVO.java", "line": 84, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserListVO.java", "line": 88, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserListVO.java", "line": 92, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserListVO.java", "line": 96, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserListVO.java", "line": 100, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserListVO.java", "line": 104, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserListVO.java", "line": 108, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserListVO.java", "line": 112, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserListVO.java", "line": 116, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserListVO.java", "line": 120, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserListVO.java", "line": 124, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserListVO.java", "line": 128, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserListVO.java", "line": 132, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserListVO.java", "line": 136, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserFamilyPhoneVO.java", "line": 13, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserFamilyPhoneVO.java", "line": 21, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserFamilyPhoneVO.java", "line": 25, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserFamilyPhoneVO.java", "line": 29, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserFamilyPhoneVO.java", "line": 33, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserFamilyPhoneVO.java", "line": 37, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserFamilyPhoneVO.java", "line": 41, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserFamilyPhoneVO.java", "line": 45, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserFamilyPhoneVO.java", "line": 49, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserFamilyPhoneVO.java", "line": 53, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserFamilyPhoneVO.java", "line": 57, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserFamilyPhoneVO.java", "line": 63, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserFamilyPhoneVO.java", "line": 64, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserFamilyPhoneVO.java", "line": 67, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserFamilyPhoneVO.java", "line": 73, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserFamilyPhoneVO.java", "line": 80, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "missing_braces", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserFamilyPhoneVO.java", "line": 64, "description": "建议为 if 语句添加大括号", "evidence": "if (this == o) ", "auto_fixable": true}, {"type": "missing_braces", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserFamilyPhoneVO.java", "line": 67, "description": "建议为 if 语句添加大括号", "evidence": "if (o == null || getClass() !", "auto_fixable": true}, {"type": "missing_braces", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserFamilyPhoneVO.java", "line": 73, "description": "建议为 if 语句添加大括号", "evidence": "if (null != userId && !userId.equals(vo.userId))", "auto_fixable": true}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/StaticLabelUserGroupQueryDto.java", "line": 12, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/StaticLabelUserGroupQueryDto.java", "line": 17, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/StaticLabelUserGroupQueryDto.java", "line": 21, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/StaticLabelUserGroupQueryDto.java", "line": 25, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/StaticLabelUserGroupQueryDto.java", "line": 29, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/StaticLabelUserGroupQueryDto.java", "line": 33, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/StaticLabelUserGroupQueryDto.java", "line": 37, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserListLoadDetailDTO.java", "line": 10, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserListLoadDetailDTO.java", "line": 44, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserListLoadDetailDTO.java", "line": 48, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserListLoadDetailDTO.java", "line": 52, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserListLoadDetailDTO.java", "line": 56, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserListLoadDetailDTO.java", "line": 60, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserListLoadDetailDTO.java", "line": 64, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserListLoadDetailDTO.java", "line": 68, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserListLoadDetailDTO.java", "line": 72, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserListLoadDetailDTO.java", "line": 76, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserListLoadDetailDTO.java", "line": 80, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserListLoadDetailDTO.java", "line": 84, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserListLoadDetailDTO.java", "line": 88, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserBatchInviteDto.java", "line": 15, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserBatchInviteDto.java", "line": 34, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserBatchInviteDto.java", "line": 38, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserBatchInviteDto.java", "line": 42, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserBatchInviteDto.java", "line": 46, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserBatchInviteDto.java", "line": 50, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserBatchInviteDto.java", "line": 54, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserBatchInviteDto.java", "line": 58, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserBatchInviteDto.java", "line": 62, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserBatchInviteDto.java", "line": 66, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserBatchInviteDto.java", "line": 70, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserBatchInviteDto.java", "line": 74, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/InviteResultVO.java", "line": 10, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/InviteResultVO.java", "line": 17, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/InviteResultVO.java", "line": 21, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/InviteResultVO.java", "line": 25, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/InviteResultVO.java", "line": 29, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/InviteResultVO.java", "line": 33, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/InviteResultVO.java", "line": 37, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/InviteResultVO.java", "line": 41, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/InviteResultVO.java", "line": 45, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/InviteResultVO.java", "line": 49, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/InviteResultVO.java", "line": 53, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserQueryDTO.java", "line": 10, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserQueryDTO.java", "line": 20, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserQueryDTO.java", "line": 24, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserQueryDTO.java", "line": 28, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserQueryDTO.java", "line": 32, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserQueryDTO.java", "line": 37, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserQueryDTO.java", "line": 41, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserQueryDTO.java", "line": 45, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserQueryDTO.java", "line": 49, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserQueryDTO.java", "line": 53, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserQueryDTO.java", "line": 57, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserQueryDTO.java", "line": 61, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserQueryDTO.java", "line": 65, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserQueryDTO.java", "line": 69, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserQueryDTO.java", "line": 73, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserQueryDTO.java", "line": 77, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/user/UserQueryDTO.java", "line": 81, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/push/ApnsCredentialInitRequestDto.java", "line": 9, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/push/ApnsCredentialInitRequestDto.java", "line": 36, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/push/ApnsCredentialInitRequestDto.java", "line": 40, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/push/ApnsCredentialInitRequestDto.java", "line": 44, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/push/ApnsCredentialInitRequestDto.java", "line": 48, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/push/ApnsCredentialInitRequestDto.java", "line": 52, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/push/ApnsCredentialInitRequestDto.java", "line": 56, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/push/ApnsCredentialInitRequestDto.java", "line": 60, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/push/ApnsCredentialInitRequestDto.java", "line": 64, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/push/ApnsCredentialInitRequestDto.java", "line": 68, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/push/ApnsCredentialInitRequestDto.java", "line": 72, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/push/ApnsCredentialInfoVO.java", "line": 7, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/push/ApnsCredentialInfoVO.java", "line": 28, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/push/ApnsCredentialInfoVO.java", "line": 32, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/push/ApnsCredentialInfoVO.java", "line": 36, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/push/ApnsCredentialInfoVO.java", "line": 40, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/push/ApnsCredentialInfoVO.java", "line": 44, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/push/ApnsCredentialInfoVO.java", "line": 48, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/push/ApnsCredentialInfoVO.java", "line": 52, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/push/ApnsCredentialInfoVO.java", "line": 56, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/common/LicenseInfoVO.java", "line": 9, "description": "建议使用统一的大括号风格", "evidence": "{\n", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/common/LicenseInfoVO.java", "line": 56, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/common/LicenseInfoVO.java", "line": 60, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/common/LicenseInfoVO.java", "line": 64, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/common/LicenseInfoVO.java", "line": 68, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/common/LicenseInfoVO.java", "line": 72, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/common/LicenseInfoVO.java", "line": 76, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/common/LicenseInfoVO.java", "line": 80, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/common/LicenseInfoVO.java", "line": 84, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/common/LicenseInfoVO.java", "line": 88, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/common/LicenseInfoVO.java", "line": 92, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/common/LicenseInfoVO.java", "line": 96, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/common/LicenseInfoVO.java", "line": 100, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/common/LicenseInfoVO.java", "line": 104, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/common/LicenseInfoVO.java", "line": 108, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/common/LicenseInfoVO.java", "line": 112, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/common/LicenseInfoVO.java", "line": 116, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/common/LicenseInfoVO.java", "line": 120, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/common/LicenseInfoVO.java", "line": 124, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/common/LicenseInfoVO.java", "line": 128, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/common/LicenseInfoVO.java", "line": 132, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/common/LicenseInfoVO.java", "line": 136, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/common/LicenseInfoVO.java", "line": 140, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/common/LicenseInfoVO.java", "line": 144, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/common/LicenseInfoVO.java", "line": 148, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/common/LicenseInfoVO.java", "line": 152, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/common/LicenseInfoVO.java", "line": 156, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/common/LicenseInfoVO.java", "line": 160, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/common/LicenseInfoVO.java", "line": 164, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/common/LicenseInfoVO.java", "line": 168, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/common/LicenseInfoVO.java", "line": 172, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/common/LicenseInfoVO.java", "line": 176, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/common/LicenseInfoVO.java", "line": 180, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/common/LicenseInfoVO.java", "line": 184, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/common/LicenseInfoVO.java", "line": 188, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/common/LicenseInfoVO.java", "line": 192, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/common/LicenseInfoVO.java", "line": 196, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/common/LicenseInfoVO.java", "line": 200, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/common/LicenseInfoVO.java", "line": 204, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/common/LicenseInfoVO.java", "line": 208, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/common/LicenseInfoVO.java", "line": 212, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/common/LicenseInfoVO.java", "line": 216, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/common/LicenseInfoVO.java", "line": 220, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/log/DeviceViolationLogVO.java", "line": 5, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/log/DeviceViolationLogVO.java", "line": 32, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/log/DeviceViolationLogVO.java", "line": 36, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/log/DeviceViolationLogVO.java", "line": 40, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/log/DeviceViolationLogVO.java", "line": 44, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/log/DeviceViolationLogVO.java", "line": 48, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/log/DeviceViolationLogVO.java", "line": 52, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/log/DeviceViolationLogVO.java", "line": 56, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/log/DeviceViolationLogVO.java", "line": 60, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/log/DeviceViolationLogVO.java", "line": 64, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/log/DeviceViolationLogVO.java", "line": 68, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/log/DeviceViolationLogVO.java", "line": 72, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/log/DeviceViolationLogVO.java", "line": 76, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/log/DeviceViolationLogVO.java", "line": 80, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/log/DeviceViolationLogVO.java", "line": 84, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/log/DeviceViolationLogVO.java", "line": 88, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/log/DeviceViolationLogVO.java", "line": 92, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/log/DeviceViolationLogVO.java", "line": 96, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/log/DeviceViolationLogVO.java", "line": 100, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/log/DeviceViolationLogVO.java", "line": 104, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/log/DeviceViolationLogVO.java", "line": 108, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/log/DeviceViolationLogVO.java", "line": 112, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/log/DeviceViolationLogVO.java", "line": 116, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/log/DeviceViolationLogVO.java", "line": 120, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/log/DeviceViolationLogVO.java", "line": 124, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/log/DeviceViolationLogVO.java", "line": 128, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "brace_style", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/log/DeviceViolationLogVO.java", "line": 132, "description": "建议使用统一的大括号风格", "evidence": "{", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/AndroidApi.java", "line": 1, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/BasicApi.java:1", "evidence": "package com.cyberscraft.uep.mdm.api.app;\n\nimport com.cyberscraft.uep.common.bean.Result;\nimport com....", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/AndroidApi.java", "line": 8, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/BasicApi.java:10", "evidence": "import io.swagger.annotations.Api;\nimport io.swagger.annotations.ApiImplicitParam;\nimport io.swagger...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/AndroidApi.java", "line": 9, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/BasicApi.java:11", "evidence": "import io.swagger.annotations.ApiImplicitParam;\nimport io.swagger.annotations.ApiImplicitParams;\nimp...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/AndroidApi.java", "line": 10, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/BasicApi.java:12", "evidence": "import io.swagger.annotations.ApiImplicitParams;\nimport io.swagger.annotations.ApiOperation;\nimport ...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/AndroidApi.java", "line": 11, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/BasicApi.java:13", "evidence": "import io.swagger.annotations.ApiOperation;\nimport org.springframework.http.MediaType;\nimport org.sp...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/AuthorizationApi.java", "line": 1, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/BasicApi.java:1", "evidence": "package com.cyberscraft.uep.mdm.api.app;\n\nimport com.cyberscraft.uep.common.bean.Result;\nimport com....", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/AuthorizationApi.java", "line": 10, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/BasicApi.java:10", "evidence": "import io.swagger.annotations.Api;\nimport io.swagger.annotations.ApiImplicitParam;\nimport io.swagger...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/AuthorizationApi.java", "line": 11, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/BasicApi.java:11", "evidence": "import io.swagger.annotations.ApiImplicitParam;\nimport io.swagger.annotations.ApiImplicitParams;\nimp...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/AuthorizationApi.java", "line": 12, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/BasicApi.java:12", "evidence": "import io.swagger.annotations.ApiImplicitParams;\nimport io.swagger.annotations.ApiOperation;\nimport ...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/AuthorizationApi.java", "line": 13, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/BasicApi.java:13", "evidence": "import io.swagger.annotations.ApiOperation;\nimport org.springframework.http.MediaType;\nimport org.sp...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/AuthorizationApi.java", "line": 14, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/BasicApi.java:14", "evidence": "import org.springframework.http.MediaType;\nimport org.springframework.web.bind.annotation.*;\n\n/***\n ...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/AuthorizationApi.java", "line": 43, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/AuthorizationApi.java:28", "evidence": "/****\n     * 用于获取设备访问对应的Access_token,如果发生错误，则返回统一的错误信息\n     * @return\n     * @throws Exception\n     ...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/AuthorizationApi.java", "line": 51, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/AuthorizationApi.java:36", "evidence": "value = \"获取设备访问token\",\n            notes = \"获取设备访问token\")\n//    @ApiImplicitParams({\n//            @...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/IOSApi.java", "line": 1, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/BasicApi.java:1", "evidence": "package com.cyberscraft.uep.mdm.api.app;\n\nimport com.cyberscraft.uep.common.bean.Result;\nimport com....", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/IOSApi.java", "line": 2, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/AndroidApi.java:2", "evidence": "import com.cyberscraft.uep.common.bean.Result;\nimport com.cyberscraft.uep.mdm.api.constant.Constant;...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/admin/AdminAuthorizationApi.java", "line": 9, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/BasicApi.java:14", "evidence": "import org.springframework.http.MediaType;\nimport org.springframework.web.bind.annotation.*;\n\n/***\n ...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/basic/MessageApi.java", "line": 5, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/BasicApi.java:10", "evidence": "import io.swagger.annotations.Api;\nimport io.swagger.annotations.ApiImplicitParam;\nimport io.swagger...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/basic/MessageApi.java", "line": 6, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/BasicApi.java:11", "evidence": "import io.swagger.annotations.ApiImplicitParam;\nimport io.swagger.annotations.ApiImplicitParams;\nimp...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/basic/MessageApi.java", "line": 7, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/BasicApi.java:12", "evidence": "import io.swagger.annotations.ApiImplicitParams;\nimport io.swagger.annotations.ApiOperation;\nimport ...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/basic/MessageApi.java", "line": 8, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/BasicApi.java:13", "evidence": "import io.swagger.annotations.ApiOperation;\nimport org.springframework.http.MediaType;\nimport org.sp...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/basic/MessageApi.java", "line": 9, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/app/BasicApi.java:14", "evidence": "import org.springframework.http.MediaType;\nimport org.springframework.web.bind.annotation.*;\n\n/***\n ...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/basic/ConfigApi.java", "line": 1, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/basic/MessageApi.java:1", "evidence": "package com.cyberscraft.uep.mdm.api.basic;\n\nimport com.cyberscraft.uep.common.bean.Result;\nimport co...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/basic/ConfigApi.java", "line": 5, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/basic/PushApi.java:7", "evidence": "import io.swagger.annotations.Api;\nimport io.swagger.annotations.ApiOperation;\nimport io.swagger.ann...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/basic/ConfigApi.java", "line": 6, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/basic/PushApi.java:8", "evidence": "import io.swagger.annotations.ApiOperation;\nimport io.swagger.annotations.ApiParam;\nimport org.sprin...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/basic/SysApi.java", "line": 6, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/basic/ConfigApi.java:8", "evidence": "import org.springframework.http.MediaType;\nimport org.springframework.web.bind.annotation.*;\n\nimport...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/basic/TenantApi.java", "line": 77, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/basic/TenantApi.java:64", "evidence": "@ResponseBody\n    @ApiImplicitParams({\n            @ApiImplicitParam(paramType = \"header\", name = \"C...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/Constant.java", "line": 20, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/Constant.java:19", "evidence": "/***\n     * 租户类型配置项名称\n     */\n    public final static String TENANT_TYPE_CONFI_NAME = \"sys.conf.tena...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/Constant.java", "line": 26, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/Constant.java:25", "evidence": "/***\n     * 是否使用多租户配置项名称\n     */\n    public final static String TENANT_USE_CONFI_NAME = \"sys.mtenant...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/Constant.java", "line": 31, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/Constant.java:30", "evidence": "/***\n     * SESSION过期时间配置项名称\n     */\n    public final static String SESSION_TIME_OUT_CONF_NAME = \"sy...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/Constant.java", "line": 45, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/Constant.java:44", "evidence": "/***\n     * 默认的请求参数tenantid的参数名称\n     */\n    public final static String TENANT_ID_PARAM_NAME = \"tena...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/Constant.java", "line": 50, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/Constant.java:49", "evidence": "/***\n     *默认的请Header参数tenantid的参数名称\n     */\n    public final static String DEFAULT_TENANT_HEADEARNA...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/Constant.java", "line": 55, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/Constant.java:54", "evidence": "/***\n     * 默认请求Header中idtoken的参数名称\n     */\n    public final static String DEFAULT_IDTOKEN_HEADERNAM...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/Constant.java", "line": 61, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/Constant.java:60", "evidence": "/***\n     * 默认请求Header中access_token的参数名称\n     */\n    public final static String DEFAULT_ACCESSTOKEN_...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/Constant.java", "line": 66, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/Constant.java:65", "evidence": "/***\n     * 默认请求Header中access_token的模式名称\n     */\n    public static final String DEFAULT_ACCESSTOKEN_...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/Constant.java", "line": 71, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/Constant.java:70", "evidence": "/***\n     * 默认请求Header中access_token的模式名称\n     */\n    public static final String DEFAULT_APP_ACCESSTO...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/Constant.java", "line": 76, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/Constant.java:75", "evidence": "/***\n     * WEB Token对应的请求参数\n     */\n    public final static String WEB_TOKEN_PARAMNAME = \"webToken\"...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/Constant.java", "line": 82, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/Constant.java:81", "evidence": "/***\n     * Access Token对应的请求参数\n     */\n    public final static String ACCESS_TOKEN_PARAMNAME = \"acc...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/Constant.java", "line": 88, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/Constant.java:87", "evidence": "/***\n     * 能否登录自服务平台，使用父对像的值，比如说组，或者父级组的配置\n     */\n    public final static Integer CAN_LOGIN_SELF_U...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/Constant.java", "line": 103, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/Constant.java:102", "evidence": "/***\n     * MDM服务配置名称\n     */\n    public final static String MDM_SERVICE_CONFIG_NAME = \"mdm_service_...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/Constant.java", "line": 140, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/Constant.java:139", "evidence": "public static final String SYSCONF_PNSERVER_APPID = \"sys.conf.PNServer.appid\";\n    public static fin...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/Constant.java", "line": 141, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/Constant.java:139", "evidence": "public static final String SYSCONF_PNSERVER_APPID = \"sys.conf.PNServer.appid\";\n    public static fin...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/PolicySubCategory.java", "line": 20, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/PolicySubCategory.java:19", "evidence": "PolicySubCategory(int value, String clientCmd) {\n        this.value = value;\n        this.clientCmd ...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/PolicySubCategory.java", "line": 39, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/PolicyCategory.java:23", "evidence": "if (category.getValue() == value) {\n                return category;\n            }\n        }\n       ...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/PolicySubCategory.java", "line": 48, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/PolicyCategory.java:32", "evidence": "if (category.getValue() == value) {\n                return category.name();\n            }\n        }\n...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/UrlConstant.java", "line": 37, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/UrlConstant.java:36", "evidence": "/***\n     * 推送对应的API\n     */\n    public final static String BASIC_PUSH_URL_PREFIX = \"/mdm/basic/push...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/UrlConstant.java", "line": 43, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/UrlConstant.java:42", "evidence": "/**********************************\n     * 管理员对应的请求\n     *********************************/\n    publ...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/UrlConstant.java", "line": 48, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/UrlConstant.java:47", "evidence": "/***\n     * 用户对应的URL前缀\n     */\n    public final static String ADMIN_USER_URL_PREFIX = ADMIN_URL_PREF...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/UrlConstant.java", "line": 53, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/UrlConstant.java:52", "evidence": "/***\n     * 设备对应的URL前缀\n     */\n    public final static String ADMIN_DEVICE_URL_PREFIX = ADMIN_URL_PR...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/UrlConstant.java", "line": 58, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/UrlConstant.java:57", "evidence": "/***\n     * 策略对应的URL前缀\n     */\n    public final static String ADMIN_POLICY_URL_PREFIX = ADMIN_URL_PR...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/UrlConstant.java", "line": 63, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/UrlConstant.java:62", "evidence": "/***\n     * 策略对应的URL前缀\n     */\n    public final static String ADMIN_LOG_URL_PREFIX = ADMIN_URL_PREFI...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/UrlConstant.java", "line": 68, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/UrlConstant.java:67", "evidence": "/***\n     * 组管理对应的URL前缀\n     */\n    public final static String ADMIN_GROUP_URL_PREFIX = ADMIN_URL_PR...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/UrlConstant.java", "line": 73, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/UrlConstant.java:72", "evidence": "/***\n     * 向客户端发命令对应的URL前缀\n     */\n    public final static String ADMIN_COMMAND_URL_PREFIX = ADMIN_...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/UrlConstant.java", "line": 78, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/UrlConstant.java:77", "evidence": "/***\n     * 标签管理对应的URL前缀\n     */\n    public final static String ADMIN_LABEL_URL_PREFIX = ADMIN_URL_P...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/UrlConstant.java", "line": 83, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/UrlConstant.java:82", "evidence": "/***\n     * 标签管理对应的URL前缀\n     */\n    public final static String ADMIN_CLIENTAPP_URL_PREFIX = ADMIN_U...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/UrlConstant.java", "line": 88, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/UrlConstant.java:87", "evidence": "/***\n     * 系统配置相关的URL前缀\n     */\n    public final static String ADMIN_SETTINGS_URL_PREFIX = ADMIN_UR...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/UrlConstant.java", "line": 93, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/UrlConstant.java:92", "evidence": "/***\n     * 证书管理相关的URL前缀\n     */\n    public final static String ADMIN_CERT_URL_PREFIX = ADMIN_SETTIN...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/UrlConstant.java", "line": 99, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/UrlConstant.java:98", "evidence": "/**********************************\n     * 设备及应用客户端对应的请求\n     *********************************/\n   ...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/UrlConstant.java", "line": 104, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/UrlConstant.java:103", "evidence": "/***\n     * 设备及应用客户端对应的指令相关请求\n     */\n    public final static String APP_COMMAND_URL_PREFIX = APP_UR...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/UrlConstant.java", "line": 109, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/UrlConstant.java:108", "evidence": "/***\n     * 设备及应用客户端对应的OAUTH2请求\n     */\n    public final static String APP_OAUTH2_URL_PREFIX = APP_U...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/UrlConstant.java", "line": 114, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/UrlConstant.java:113", "evidence": "/***\n     * 基础租户服务文件对应的请求\n     */\n    public final static String BASIC_FILE_URL_PREFIX = APP_URL_PRE...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/UrlConstant.java", "line": 119, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/UrlConstant.java:118", "evidence": "/***\n     * 设备及应用客户端对应的IOS请求\n     */\n    public final static String APP_IOS_URL_PREFIX = APP_URL_PRE...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/UrlConstant.java", "line": 124, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/UrlConstant.java:123", "evidence": "/***\n     * 设备及应用客户端对应的ANDRIOD请求\n     */\n    public final static String APP_ANDROID_URL_PREFIX = APP...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/UrlConstant.java", "line": 129, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/UrlConstant.java:128", "evidence": "/***\n     * 设备及应用客户端对应的ANDRIOD请求\n     */\n    public final static String APP_TDOS_URL_PREFIX = APP_UR...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/UrlConstant.java", "line": 134, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/UrlConstant.java:133", "evidence": "/***\n     * 设备及应用客户端对应的WINDOWS请求\n     */\n    public final static String APP_WINDOWS_URL_PREFIX = APP...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/UrlConstant.java", "line": 140, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/UrlConstant.java:139", "evidence": "/***\n     * 老接口\n     */\n    public final static String APP_LEGACY_URL_PREFIX = APP_URL_PREFIX + \"/le...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/TenantFromType.java", "line": 29, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/constant/TenantFromType.java:28", "evidence": "TenantFromType(int code,boolean callBack) {\n        this.code = code;\n        this.callBack = callBa...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/errors/MdmErrorType.java", "line": 197, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/errors/MdmErrorType.java:196", "evidence": "MdmErrorType(String errorCode, String desc) {\n        this.code = errorCode;\n        this.desc = des...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/event/Event.java", "line": 49, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/event/Event.java:48", "evidence": "public Event setTriggerBy(String triggerBy) {\n        this.triggerBy = triggerBy;\n        return thi...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/event/Event.java", "line": 58, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/event/Event.java:57", "evidence": "public Event setBusinessDataId(Object businessDataId) {\n        this.businessDataId = businessDataId...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/event/Event.java", "line": 67, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/event/Event.java:66", "evidence": "public Event setEventNumber(String eventNumber) {\n        this.eventNumber = eventNumber;\n        re...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/event/Event.java", "line": 76, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/event/Event.java:75", "evidence": "public Event setTenantId(String tenantId) {\n        this.tenantId = tenantId;\n        return this;\n ...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/event/Event.java", "line": 85, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/event/Event.java:84", "evidence": "public Event setBusinessType(Integer businessType) {\n        this.businessType = businessType;\n     ...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/event/Event.java", "line": 94, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/event/Event.java:93", "evidence": "public Event setChangeType(Integer changeType) {\n        this.changeType = changeType;\n        retur...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/event/Event.java", "line": 121, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/event/Event.java:120", "evidence": "public Event setEventTime(LocalDateTime eventTime) {\n        this.eventTime = eventTime;\n        ret...", "auto_fixable": false}, {"type": "duplicate_code", "severity": "warning", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientSignatureSecretRotationVO.java", "line": 19, "description": "重复代码块，原始位置: mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientSignatureSecretRotationVO.java:18", "evidence": "public ClientSignatureSecretRotationVO(String clientId, String signatureSecret) {\n        setClientI...", "auto_fixable": false}, {"type": "unused_import", "severity": "info", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/tenant/ClientIntegrationDto.java", "line": 4, "description": "未使用的导入: java.time.LocalDateTime", "evidence": "import java.time.LocalDateTime;", "auto_fixable": true}, {"type": "unused_import", "severity": "info", "file": "mdm/mdm-api/src/main/java/com/cyberscraft/uep/mdm/api/dto/clientcmd/ClientCmdRequestDto.java", "line": 4, "description": "未使用的导入: com.cyberscraft.uep.mdm.api.dto.device.DeviceDto", "evidence": "import com.cyberscraft.uep.mdm.api.dto.device.DeviceDto;", "auto_fixable": true}], "suggestions": [{"priority": "medium", "title": "自动修复简单问题", "description": "有 21 个问题可以自动修复"}]}