
<!DOCTYPE html>
<html>
<head>
    <title>架构分析报告</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f0f0f0; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; }
        .issue { background: #ffe6e6; padding: 10px; margin: 5px 0; border-radius: 3px; }
        .recommendation { background: #e6f3ff; padding: 10px; margin: 5px 0; border-radius: 3px; }
        .success { color: green; }
        .warning { color: orange; }
        .error { color: red; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🏗️ 架构分析报告</h1>
        <p>生成时间: 2025-06-12T14:37:31.137701</p>
    </div>
    
    <div class="section">
        <h2>📊 项目概览</h2>
        <ul>
            <li>项目名称: 数犀集成平台</li>
            <li>项目类型: spring-boot-microservices</li>
            <li>模块数量: 7</li>
            <li>Java 文件数量: 5012</li>
        </ul>
    </div>
    
    <div class="section">
        <h2>🔍 发现的问题</h2>
        
            <div class="issue">
                <strong class="warning">HIGH</strong>: 可能包含硬编码的敏感信息<br>
                <small>文件: mdm/mdm-service/target/classes/application-product.properties</small>
            </div>
            
            <div class="issue">
                <strong class="warning">HIGH</strong>: 可能包含硬编码的敏感信息<br>
                <small>文件: mdm/mdm-service/src/main/resources/application-product.properties</small>
            </div>
            
            <div class="issue">
                <strong class="warning">HIGH</strong>: 可能包含硬编码的敏感信息<br>
                <small>文件: ncm/ncm-service/target/classes/application.properties</small>
            </div>
            
            <div class="issue">
                <strong class="warning">HIGH</strong>: 可能包含硬编码的敏感信息<br>
                <small>文件: ncm/ncm-service/src/main/resources/application.properties</small>
            </div>
            
            <div class="issue">
                <strong class="warning">HIGH</strong>: 可能包含硬编码的敏感信息<br>
                <small>文件: gateway/gateway-service/target/classes/application.properties</small>
            </div>
            
            <div class="issue">
                <strong class="warning">HIGH</strong>: 可能包含硬编码的敏感信息<br>
                <small>文件: gateway/gateway-service/src/main/resources/application.properties</small>
            </div>
            
    </div>
    
    <div class="section">
        <h2>💡 优化建议</h2>
        
            <div class="recommendation">
                <strong>使用配置中心管理敏感信息</strong> (优先级: high)<br>
                <small>建议使用 Spring Cloud Config 或环境变量管理密码等敏感信息</small>
            </div>
            
            <div class="recommendation">
                <strong>启用数据库连接池</strong> (优先级: medium)<br>
                <small>确保所有数据库连接都使用连接池以提高性能</small>
            </div>
            
            <div class="recommendation">
                <strong>实施服务网格</strong> (优先级: medium)<br>
                <small>考虑使用 Istio 或类似的服务网格来管理微服务间通信</small>
            </div>
            
    </div>
</body>
</html>
        