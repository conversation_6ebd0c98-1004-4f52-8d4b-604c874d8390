# 架构分析 Agent 配置
name: "Architecture Analyzer Agent"
version: "1.0.0"
description: "分析微服务架构，识别潜在问题和优化机会"

# 目标项目配置
target_project:
  root_path: "/Users/<USER>/Documents/shuxi/source-code"
  project_type: "spring-boot-microservices"
  build_tool: "maven"
  
# 分析模块
analysis_modules:
  - name: "dependency_analysis"
    enabled: true
    config:
      check_circular_dependencies: true
      check_version_conflicts: true
      check_security_vulnerabilities: true
      
  - name: "service_coupling_analysis"
    enabled: true
    config:
      max_coupling_threshold: 0.7
      check_feign_clients: true
      analyze_api_dependencies: true
      
  - name: "performance_analysis"
    enabled: true
    config:
      check_database_queries: true
      analyze_caching_strategy: true
      check_async_patterns: true

# 输出配置
output:
  format: ["json", "html", "markdown"]
  include_recommendations: true
  include_code_examples: true
  
# 集成配置
integrations:
  sonarqube:
    enabled: false
    url: "http://localhost:9000"
  
  jenkins:
    enabled: false
    url: "http://localhost:8080"
    
  git:
    enabled: true
    auto_commit_reports: false

# 调度配置
schedule:
  enabled: true
  cron: "0 2 * * 1"  # 每周一凌晨2点执行
  
# 通知配置
notifications:
  email:
    enabled: false
    recipients: ["<EMAIL>"]
  
  slack:
    enabled: false
    webhook_url: ""
    channel: "#dev-alerts"
