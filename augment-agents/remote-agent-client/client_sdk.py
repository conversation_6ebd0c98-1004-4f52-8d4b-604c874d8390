#!/usr/bin/env python3
"""
Remote Agent Client SDK
用于与 Remote Agent 系统交互的客户端 SDK
"""

import json
import time
import requests
from datetime import datetime
from typing import Dict, List, Optional, Any

class RemoteAgentClient:
    """Remote Agent 客户端"""
    
    def __init__(self, master_url: str = "http://localhost:8889"):
        self.master_url = master_url.rstrip('/')
        self.session = requests.Session()
        
    def get_nodes_status(self) -> Dict[str, Any]:
        """获取所有节点状态"""
        response = self.session.get(f"{self.master_url}/api/nodes")
        response.raise_for_status()
        return response.json()
        
    def submit_task(self, agent_type: str, parameters: Dict[str, Any] = None) -> Dict[str, Any]:
        """提交单个任务"""
        task_data = {
            'agent_type': agent_type,
            'parameters': parameters or {}
        }
        
        response = self.session.post(
            f"{self.master_url}/api/tasks/submit",
            json=task_data
        )
        response.raise_for_status()
        return response.json()
        
    def submit_batch_tasks(self, tasks: List[Dict[str, Any]]) -> Dict[str, Any]:
        """提交批量任务"""
        batch_data = {'tasks': tasks}
        
        response = self.session.post(
            f"{self.master_url}/api/tasks/batch",
            json=batch_data
        )
        response.raise_for_status()
        return response.json()
        
    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """获取任务状态"""
        response = self.session.get(f"{self.master_url}/api/tasks/{task_id}")
        response.raise_for_status()
        return response.json()
        
    def wait_for_task(self, task_id: str, timeout: int = 300, poll_interval: int = 2) -> Dict[str, Any]:
        """等待任务完成"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            status = self.get_task_status(task_id)
            
            if status.get('status') in ['completed', 'failed']:
                return status
                
            time.sleep(poll_interval)
            
        raise TimeoutError(f"任务 {task_id} 在 {timeout} 秒内未完成")
        
    def run_security_scan(self, target_path: str = None, scan_type: str = "full") -> Dict[str, Any]:
        """运行安全扫描"""
        parameters = {
            'target_path': target_path,
            'scan_type': scan_type
        }
        
        result = self.submit_task('security_scanner', parameters)
        if result['success']:
            return self.wait_for_task(result['task_id'])
        else:
            return result
            
    def run_performance_analysis(self, target_modules: List[str] = None) -> Dict[str, Any]:
        """运行性能分析"""
        parameters = {
            'target_modules': target_modules or []
        }
        
        result = self.submit_task('performance_optimizer', parameters)
        if result['success']:
            return self.wait_for_task(result['task_id'])
        else:
            return result
            
    def run_code_quality_check(self, check_types: List[str] = None) -> Dict[str, Any]:
        """运行代码质量检查"""
        parameters = {
            'check_types': check_types or ['style', 'complexity', 'duplicates']
        }
        
        result = self.submit_task('code_quality_monitor', parameters)
        if result['success']:
            return self.wait_for_task(result['task_id'])
        else:
            return result
            
    def run_full_analysis(self) -> Dict[str, Any]:
        """运行完整分析（所有类型的 Agent）"""
        tasks = [
            {'agent_type': 'security_scanner', 'parameters': {'scan_type': 'full'}},
            {'agent_type': 'performance_optimizer', 'parameters': {}},
            {'agent_type': 'code_quality_monitor', 'parameters': {}}
        ]
        
        batch_result = self.submit_batch_tasks(tasks)
        
        if batch_result['success']:
            # 等待所有任务完成
            results = {}
            for task_result in batch_result['submitted_tasks']:
                if task_result['success']:
                    task_id = task_result['task_id']
                    agent_type = None
                    
                    # 从任务中找到对应的 agent_type
                    for task in tasks:
                        if task_result.get('agent_type') == task['agent_type']:
                            agent_type = task['agent_type']
                            break
                    
                    if agent_type:
                        results[agent_type] = self.wait_for_task(task_id)
                        
            return {
                'success': True,
                'results': results,
                'summary': self._generate_analysis_summary(results)
            }
        else:
            return batch_result
            
    def _generate_analysis_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """生成分析摘要"""
        summary = {
            'total_agents': len(results),
            'successful_agents': 0,
            'failed_agents': 0,
            'key_findings': []
        }
        
        for agent_type, result in results.items():
            if result.get('status') == 'completed':
                summary['successful_agents'] += 1
                
                # 提取关键发现
                if agent_type == 'security_scanner':
                    if result.get('result', {}).get('vulnerabilities_found', 0) > 0:
                        summary['key_findings'].append(
                            f"🛡️ 发现 {result['result']['vulnerabilities_found']} 个安全漏洞"
                        )
                        
                elif agent_type == 'performance_optimizer':
                    if result.get('result', {}).get('optimizations_found', 0) > 0:
                        summary['key_findings'].append(
                            f"⚡ 发现 {result['result']['optimizations_found']} 个性能优化点"
                        )
                        
                elif agent_type == 'code_quality_monitor':
                    if result.get('result', {}).get('issues_found', 0) > 0:
                        summary['key_findings'].append(
                            f"📊 发现 {result['result']['issues_found']} 个代码质量问题"
                        )
            else:
                summary['failed_agents'] += 1
                
        return summary

# 命令行工具
def main():
    import argparse
    
    parser = argparse.ArgumentParser(description='Remote Agent Client')
    parser.add_argument('--master-url', default='http://localhost:8889', help='主控制器地址')
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 节点状态命令
    subparsers.add_parser('nodes', help='查看节点状态')
    
    # 安全扫描命令
    security_parser = subparsers.add_parser('security', help='运行安全扫描')
    security_parser.add_argument('--target', help='扫描目标路径')
    security_parser.add_argument('--type', default='full', help='扫描类型')
    
    # 性能分析命令
    performance_parser = subparsers.add_parser('performance', help='运行性能分析')
    performance_parser.add_argument('--modules', nargs='*', help='目标模块')
    
    # 代码质量命令
    quality_parser = subparsers.add_parser('quality', help='运行代码质量检查')
    quality_parser.add_argument('--types', nargs='*', help='检查类型')
    
    # 完整分析命令
    subparsers.add_parser('full', help='运行完整分析')
    
    args = parser.parse_args()
    
    client = RemoteAgentClient(args.master_url)
    
    try:
        if args.command == 'nodes':
            status = client.get_nodes_status()
            print(f"📡 总节点数: {status['total_nodes']}")
            print(f"🟢 在线节点: {status['online_nodes']}")
            
            for node in status['nodes']:
                status_icon = "🟢" if node['online'] else "🔴"
                print(f"{status_icon} {node['node_id'][:8]} - {', '.join(node['capabilities'])}")
                
        elif args.command == 'security':
            print("🛡️ 启动安全扫描...")
            result = client.run_security_scan(args.target, args.type)
            
            if result.get('status') == 'completed':
                scan_result = result['result']
                print(f"✅ 扫描完成")
                print(f"🔍 发现漏洞: {scan_result.get('vulnerabilities_found', 0)}")
                print(f"🚨 严重问题: {scan_result.get('critical_issues', 0)}")
            else:
                print(f"❌ 扫描失败: {result.get('error', '未知错误')}")
                
        elif args.command == 'performance':
            print("⚡ 启动性能分析...")
            result = client.run_performance_analysis(args.modules)
            
            if result.get('status') == 'completed':
                perf_result = result['result']
                print(f"✅ 分析完成")
                print(f"🔧 优化建议: {perf_result.get('optimizations_found', 0)}")
                print(f"📈 潜在提升: {perf_result.get('potential_improvement', 'N/A')}")
            else:
                print(f"❌ 分析失败: {result.get('error', '未知错误')}")
                
        elif args.command == 'quality':
            print("📊 启动代码质量检查...")
            result = client.run_code_quality_check(args.types)
            
            if result.get('status') == 'completed':
                quality_result = result['result']
                print(f"✅ 检查完成")
                print(f"🔍 发现问题: {quality_result.get('issues_found', 0)}")
                print(f"📊 质量评分: {quality_result.get('quality_score', 'N/A')}")
            else:
                print(f"❌ 检查失败: {result.get('error', '未知错误')}")
                
        elif args.command == 'full':
            print("🚀 启动完整分析...")
            result = client.run_full_analysis()
            
            if result.get('success'):
                summary = result['summary']
                print(f"✅ 分析完成")
                print(f"📊 成功执行: {summary['successful_agents']}/{summary['total_agents']} 个 Agent")
                
                if summary['key_findings']:
                    print("\n🔍 关键发现:")
                    for finding in summary['key_findings']:
                        print(f"  {finding}")
            else:
                print(f"❌ 分析失败: {result.get('error', '未知错误')}")
                
        else:
            parser.print_help()
            
    except Exception as e:
        print(f"❌ 执行失败: {e}")

if __name__ == '__main__':
    main()
