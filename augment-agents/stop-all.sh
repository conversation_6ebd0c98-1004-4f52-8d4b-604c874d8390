#!/bin/bash

# Augment Agents 停止脚本
# 用于停止所有运行中的 Agent

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🛑 停止所有 Augment Agents...${NC}"

# 停止主控制台
echo -e "${YELLOW}停止主控制台...${NC}"
pkill -f "master_control.py" || echo "  主控制台未运行"

# 停止所有 Agent 进程
agents=(
    "architecture_analyzer.py"
    "code_quality_monitor.py"
    "security_scanner.py"
    "performance_optimizer.py"
    "test_automation.py"
    "dependency_manager.py"
    "documentation_generator.py"
)

for agent in "${agents[@]}"; do
    echo -e "${YELLOW}停止 $agent...${NC}"
    pkill -f "$agent" || echo "  $agent 未运行"
done

# 停止 Web 服务
echo -e "${YELLOW}停止 Web 服务...${NC}"
pkill -f "flask" || echo "  Flask 服务未运行"
pkill -f "gunicorn" || echo "  Gunicorn 服务未运行"

# 清理临时文件
echo -e "${YELLOW}清理临时文件...${NC}"
rm -rf temp/*
rm -rf *.pid

echo -e "${GREEN}✅ 所有 Augment Agents 已停止${NC}"
