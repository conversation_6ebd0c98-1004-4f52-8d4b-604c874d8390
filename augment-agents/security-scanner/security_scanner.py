#!/usr/bin/env python3
"""
Security Scanner Agent
全面扫描项目安全漏洞
"""

import os
import sys
import yaml
import logging
import argparse
import json
import re
from datetime import datetime
from pathlib import Path

class SecurityScannerAgent:
    def __init__(self, config_path):
        self.config_path = config_path
        self.config = self.load_config()
        self.setup_logging()
        self.project_root = Path("/Users/<USER>/Documents/shuxi/source-code")
        
    def load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            print(f"❌ 配置文件加载失败: {e}")
            sys.exit(1)
            
    def setup_logging(self):
        """设置日志"""
        log_dir = Path('../logs')
        log_dir.mkdir(exist_ok=True)
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_dir / 'security-scanner.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('security-scanner')
        
    def run(self):
        """运行 Agent"""
        self.logger.info("🛡️ 启动安全扫描 Agent")
        
        try:
            # 执行安全扫描
            results = self.execute_security_scan()
            
            # 生成报告
            self.generate_report(results)
            
            self.logger.info("✅ 安全扫描完成")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 安全扫描失败: {e}")
            return False
            
    def execute_security_scan(self):
        """执行安全扫描"""
        self.logger.info("🔍 开始安全扫描...")
        
        results = {
            'timestamp': datetime.now().isoformat(),
            'scan_summary': {
                'files_scanned': 0,
                'vulnerabilities_found': 0,
                'critical_issues': 0,
                'high_issues': 0,
                'medium_issues': 0,
                'low_issues': 0
            },
            'vulnerabilities': []
        }
        
        # 扫描硬编码凭据
        self.scan_hardcoded_credentials(results)
        
        # 扫描 SQL 注入风险
        self.scan_sql_injection(results)
        
        # 扫描配置安全问题
        self.scan_configuration_issues(results)
        
        # 扫描 JWT 安全问题
        self.scan_jwt_security(results)
        
        # 更新统计信息
        self.update_scan_summary(results)
        
        return results
        
    def scan_hardcoded_credentials(self, results):
        """扫描硬编码凭据"""
        self.logger.info("🔐 扫描硬编码凭据...")
        
        patterns = [
            (r'password\s*=\s*["\'][^"\']{8,}["\']', 'hardcoded_password'),
            (r'secret\s*=\s*["\'][^"\']{8,}["\']', 'hardcoded_secret'),
            (r'token\s*=\s*["\'][^"\']{20,}["\']', 'hardcoded_token'),
            (r'key\s*=\s*["\'][^"\']{16,}["\']', 'hardcoded_key'),
            (r'activeCode\s*=\s*["\']?\d{6}["\']?', 'hardcoded_activation_code')
        ]
        
        # 扫描配置文件
        config_files = list(self.project_root.rglob('*.properties')) + \
                      list(self.project_root.rglob('*.yml')) + \
                      list(self.project_root.rglob('*.yaml'))
        
        for file_path in config_files:
            if 'target' in str(file_path):  # 跳过编译输出
                continue
                
            try:
                content = file_path.read_text(encoding='utf-8')
                results['scan_summary']['files_scanned'] += 1
                
                for pattern, vuln_type in patterns:
                    matches = re.finditer(pattern, content, re.IGNORECASE)
                    for match in matches:
                        line_num = content[:match.start()].count('\n') + 1
                        
                        vulnerability = {
                            'type': vuln_type,
                            'severity': 'critical' if 'password' in vuln_type else 'high',
                            'file': str(file_path.relative_to(self.project_root)),
                            'line': line_num,
                            'description': f'发现硬编码的敏感信息: {vuln_type}',
                            'evidence': match.group()[:50] + '...' if len(match.group()) > 50 else match.group(),
                            'recommendation': '使用环境变量或配置中心管理敏感信息'
                        }
                        
                        results['vulnerabilities'].append(vulnerability)
                        
            except Exception as e:
                self.logger.warning(f"读取文件失败 {file_path}: {e}")
                
    def scan_sql_injection(self, results):
        """扫描 SQL 注入风险"""
        self.logger.info("💉 扫描 SQL 注入风险...")
        
        patterns = [
            (r'Statement.*execute.*\+', 'sql_injection_statement'),
            (r'createStatement\(\).*executeQuery\(', 'sql_injection_create_statement'),
            (r'PreparedStatement.*setString.*\+', 'sql_injection_prepared_statement'),
            (r'Query.*createQuery\(.*\+', 'sql_injection_jpa_query')
        ]
        
        java_files = list(self.project_root.rglob('*.java'))
        
        for file_path in java_files[:100]:  # 限制扫描数量以提高性能
            if 'target' in str(file_path):
                continue
                
            try:
                content = file_path.read_text(encoding='utf-8')
                results['scan_summary']['files_scanned'] += 1
                
                for pattern, vuln_type in patterns:
                    matches = re.finditer(pattern, content, re.IGNORECASE | re.DOTALL)
                    for match in matches:
                        line_num = content[:match.start()].count('\n') + 1
                        
                        vulnerability = {
                            'type': vuln_type,
                            'severity': 'critical',
                            'file': str(file_path.relative_to(self.project_root)),
                            'line': line_num,
                            'description': 'SQL 注入风险：直接拼接 SQL 语句',
                            'evidence': match.group()[:100] + '...' if len(match.group()) > 100 else match.group(),
                            'recommendation': '使用参数化查询或 PreparedStatement'
                        }
                        
                        results['vulnerabilities'].append(vulnerability)
                        
            except Exception as e:
                self.logger.warning(f"读取 Java 文件失败 {file_path}: {e}")
                
    def scan_configuration_issues(self, results):
        """扫描配置安全问题"""
        self.logger.info("⚙️ 扫描配置安全问题...")
        
        # 检查 Spring Security 配置
        security_patterns = [
            (r'\.csrf\(\)\.disable\(\)', 'csrf_disabled'),
            (r'\.permitAll\(\)', 'permissive_access'),
            (r'\.anyRequest\(\)\.permitAll\(\)', 'allow_all_requests'),
            (r'management\.endpoints\.web\.exposure\.include=\*', 'exposed_actuator_endpoints')
        ]
        
        config_files = list(self.project_root.rglob('*SecurityConfig*.java')) + \
                      list(self.project_root.rglob('application*.properties')) + \
                      list(self.project_root.rglob('application*.yml'))
        
        for file_path in config_files:
            if 'target' in str(file_path):
                continue
                
            try:
                content = file_path.read_text(encoding='utf-8')
                
                for pattern, vuln_type in security_patterns:
                    matches = re.finditer(pattern, content, re.IGNORECASE)
                    for match in matches:
                        line_num = content[:match.start()].count('\n') + 1
                        
                        severity = 'high' if 'csrf' in vuln_type or 'exposed' in vuln_type else 'medium'
                        
                        vulnerability = {
                            'type': vuln_type,
                            'severity': severity,
                            'file': str(file_path.relative_to(self.project_root)),
                            'line': line_num,
                            'description': f'安全配置问题: {vuln_type}',
                            'evidence': match.group(),
                            'recommendation': '检查并加强安全配置'
                        }
                        
                        results['vulnerabilities'].append(vulnerability)
                        
            except Exception as e:
                self.logger.warning(f"读取配置文件失败 {file_path}: {e}")
                
    def scan_jwt_security(self, results):
        """扫描 JWT 安全问题"""
        self.logger.info("🔑 扫描 JWT 安全问题...")
        
        jwt_patterns = [
            (r'Algorithm\.HMAC256\(["\'][^"\']{1,16}["\']', 'weak_jwt_secret'),
            (r'setSigningKey\(["\'][^"\']{1,16}["\']', 'weak_jwt_signing_key'),
            (r'Algorithm\.none\(\)', 'jwt_none_algorithm'),
            (r'\.setExpiration\(null\)', 'jwt_no_expiration')
        ]
        
        java_files = list(self.project_root.rglob('*Token*.java')) + \
                    list(self.project_root.rglob('*JWT*.java')) + \
                    list(self.project_root.rglob('*Auth*.java'))
        
        for file_path in java_files:
            if 'target' in str(file_path):
                continue
                
            try:
                content = file_path.read_text(encoding='utf-8')
                
                for pattern, vuln_type in jwt_patterns:
                    matches = re.finditer(pattern, content, re.IGNORECASE)
                    for match in matches:
                        line_num = content[:match.start()].count('\n') + 1
                        
                        vulnerability = {
                            'type': vuln_type,
                            'severity': 'high',
                            'file': str(file_path.relative_to(self.project_root)),
                            'line': line_num,
                            'description': f'JWT 安全问题: {vuln_type}',
                            'evidence': match.group(),
                            'recommendation': '使用强密钥和安全的 JWT 配置'
                        }
                        
                        results['vulnerabilities'].append(vulnerability)
                        
            except Exception as e:
                self.logger.warning(f"读取 JWT 文件失败 {file_path}: {e}")
                
    def update_scan_summary(self, results):
        """更新扫描统计信息"""
        summary = results['scan_summary']
        summary['vulnerabilities_found'] = len(results['vulnerabilities'])
        
        for vuln in results['vulnerabilities']:
            severity = vuln['severity']
            if severity == 'critical':
                summary['critical_issues'] += 1
            elif severity == 'high':
                summary['high_issues'] += 1
            elif severity == 'medium':
                summary['medium_issues'] += 1
            elif severity == 'low':
                summary['low_issues'] += 1
                
    def generate_report(self, results):
        """生成安全报告"""
        self.logger.info("📄 生成安全报告...")
        
        report_dir = Path('../reports')
        report_dir.mkdir(exist_ok=True)
        
        # 生成 JSON 报告
        json_report = report_dir / f'security-scan-{datetime.now().strftime("%Y%m%d-%H%M%S")}.json'
        with open(json_report, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
            
        # 生成 HTML 报告
        html_report = report_dir / f'security-scan-{datetime.now().strftime("%Y%m%d-%H%M%S")}.html'
        self.generate_html_report(results, html_report)
        
        self.logger.info(f"🛡️ 安全报告已生成: {json_report}")
        self.logger.info(f"🌐 HTML 报告: {html_report}")
        
        # 输出关键统计信息
        summary = results['scan_summary']
        self.logger.info(f"📊 扫描统计: 文件 {summary['files_scanned']}, 漏洞 {summary['vulnerabilities_found']}")
        self.logger.info(f"🚨 严重程度: 严重 {summary['critical_issues']}, 高 {summary['high_issues']}, 中 {summary['medium_issues']}, 低 {summary['low_issues']}")
        
    def generate_html_report(self, results, output_path):
        """生成 HTML 安全报告"""
        summary = results['scan_summary']
        
        html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>安全扫描报告</title>
    <meta charset="utf-8">
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background: #f8f9fa; padding: 20px; border-radius: 5px; border-left: 5px solid #007bff; }}
        .summary {{ background: #e9ecef; padding: 15px; margin: 20px 0; border-radius: 5px; }}
        .vulnerability {{ margin: 10px 0; padding: 15px; border-radius: 5px; border-left: 5px solid; }}
        .critical {{ background: #f8d7da; border-color: #dc3545; }}
        .high {{ background: #fff3cd; border-color: #ffc107; }}
        .medium {{ background: #d1ecf1; border-color: #17a2b8; }}
        .low {{ background: #d4edda; border-color: #28a745; }}
        .severity {{ font-weight: bold; text-transform: uppercase; }}
        .file-path {{ font-family: monospace; background: #f8f9fa; padding: 2px 4px; border-radius: 3px; }}
        .evidence {{ font-family: monospace; background: #f8f9fa; padding: 5px; border-radius: 3px; margin: 5px 0; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🛡️ 安全扫描报告</h1>
        <p>生成时间: {results['timestamp']}</p>
    </div>
    
    <div class="summary">
        <h2>📊 扫描统计</h2>
        <ul>
            <li>扫描文件数: {summary['files_scanned']}</li>
            <li>发现漏洞数: {summary['vulnerabilities_found']}</li>
            <li>🔴 严重: {summary['critical_issues']}</li>
            <li>🟠 高危: {summary['high_issues']}</li>
            <li>🟡 中危: {summary['medium_issues']}</li>
            <li>🟢 低危: {summary['low_issues']}</li>
        </ul>
    </div>
    
    <div class="vulnerabilities">
        <h2>🚨 发现的漏洞</h2>
        {self._format_vulnerabilities_html(results['vulnerabilities'])}
    </div>
</body>
</html>
        """
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
            
    def _format_vulnerabilities_html(self, vulnerabilities):
        if not vulnerabilities:
            return "<p style='color: green;'>✅ 未发现安全漏洞</p>"
            
        html = ""
        for vuln in vulnerabilities:
            severity_class = vuln['severity']
            html += f"""
            <div class="vulnerability {severity_class}">
                <div class="severity">{vuln['severity']}</div>
                <h3>{vuln['description']}</h3>
                <p><strong>文件:</strong> <span class="file-path">{vuln['file']}</span> (行 {vuln['line']})</p>
                <p><strong>类型:</strong> {vuln['type']}</p>
                <div class="evidence"><strong>证据:</strong><br>{vuln['evidence']}</div>
                <p><strong>建议:</strong> {vuln['recommendation']}</p>
            </div>
            """
        return html

def main():
    parser = argparse.ArgumentParser(description='安全扫描 Agent')
    parser.add_argument('--config', default='config.yaml', help='配置文件路径')
    
    args = parser.parse_args()
    
    agent = SecurityScannerAgent(args.config)
    success = agent.run()
    
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()
