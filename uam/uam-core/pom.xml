<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>uam</artifactId>
        <groupId>com.cyberscraft.uep</groupId>
        <version>5.4.1</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>uam-core</artifactId>

    <properties>
        <spring-security-oauth2.version>2.4.1.RELEASE</spring-security-oauth2.version>
    </properties>

    <dependencies>
        <!-- digitalsee dependency begin-->
        <dependency>
            <groupId>com.cyberscraft.uep</groupId>
            <artifactId>uam-api</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.cyberscraft.uep</groupId>
            <artifactId>iam-core</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>com.cyberscraft.uep</groupId>
            <artifactId>common-task</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <!-- digitalsee dependency end-->
        <!-- ！！！！！！！！！！！！！！！！！！！！！！！！！！！！分界线 ！！！！！！！！！！！！！！！！！！！！！！！！！！！！！ -->

    </dependencies>

    <build>
        <finalName>uam-core</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>