package com.cyberscraft.uep.uam.core.transfer;

import com.cyberscraft.uep.iam.dto.response.SysTenantVo;
import com.cyberscraft.uep.iam.dto.response.tenant.TenantLicenseVO;
import com.cyberscraft.uep.uam.common.vo.UamTenantLicenseVO;
import com.cyberscraft.uep.uam.common.vo.UamTenantVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public abstract class UamTenantTransfer {
    public abstract UamTenantVO iamTenant2UamTenant(SysTenantVo tenantVo);

    public abstract List<UamTenantVO> iamTenant2UamTenant(List<SysTenantVo> tenantVo);

    public abstract UamTenantLicenseVO  iamTenantLicenseToUamTenantLicense(TenantLicenseVO tenantLicenseVO);

    public abstract List<UamTenantLicenseVO>  iamTenantLicenseToUamTenantLicense(List<SysTenantVo> tenantLicenseVO);

    @Mapping(target = "status", expression = "java(com.cyberscraft.uep.iam.dto.enums.TenantStatusEnum.getValue(tenantVo.getStatus().toString()))")
    public abstract UamTenantLicenseVO iamTenantToUamTenant(SysTenantVo tenantVo);
}
