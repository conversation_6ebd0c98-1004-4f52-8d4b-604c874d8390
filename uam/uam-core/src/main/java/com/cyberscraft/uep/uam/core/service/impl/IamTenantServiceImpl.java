package com.cyberscraft.uep.uam.core.service.impl;

import com.cyberscraft.uep.iam.dto.response.SysTenantVo;
import com.cyberscraft.uep.iam.dto.response.tenant.TenantLicenseVO;
import com.cyberscraft.uep.iam.service.ITenantService;
import com.cyberscraft.uep.starter.dds.TenantHolder;
import com.cyberscraft.uep.uam.common.vo.UamTenantLicenseVO;
import com.cyberscraft.uep.uam.common.vo.UamTenantVO;
import com.cyberscraft.uep.uam.core.service.IUamTenantService;
import com.cyberscraft.uep.uam.core.transfer.UamTenantTransfer;

import javax.annotation.Resource;
import java.util.List;

public class IamTenantServiceImpl implements IUamTenantService {
    @Resource
    private ITenantService tenantService;

    @Resource
    private UamTenantTransfer uamTenantTransfer;

    @Override
    public List<UamTenantVO> getAllTenants() {
        List<SysTenantVo> tenantList = tenantService.getTenantList();
        return uamTenantTransfer.iamTenant2UamTenant(tenantList);
    }

    @Override
    public UamTenantLicenseVO getTenantLicense() {
        String tenantCode = TenantHolder.getTenantCode();
        TenantLicenseVO tenantLicense = tenantService.getTenantLicense(tenantCode);
        return uamTenantTransfer.iamTenantLicenseToUamTenantLicense(tenantLicense);
    }

    @Override
    public List<UamTenantLicenseVO> getAllTenantLicense() {
        List<SysTenantVo> tenantList = tenantService.getTenantList();
        return uamTenantTransfer.iamTenantLicenseToUamTenantLicense(tenantList);
    }
}
