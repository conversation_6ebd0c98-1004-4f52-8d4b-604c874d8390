package com.cyberscraft.uep.uam.common.vo;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.text.SimpleDateFormat;
import java.util.Date;

public class UamTenantLicenseVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "授权用户数")
    private Integer licenseNum;

    @ApiModelProperty(value = "租户状态")
    private Integer status;

    @ApiModelProperty(value = "租户ID")
    private String tenantId;

    @ApiModelProperty(value = "系统当前时间")
    private String currentDate = new SimpleDateFormat("yyyy-MM-dd").format(new Date());

    @ApiModelProperty(value = "授权模块")
    private LicenseConfigVO licenseConfig;

    public Integer getLicenseNum() {
        return licenseNum;
    }

    public void setLicenseNum(Integer licenseNum) {
        this.licenseNum = licenseNum;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getCurrentDate() {
        return currentDate;
    }

    public LicenseConfigVO getLicenseConfig() {
        return licenseConfig;
    }

    public void setLicenseConfig(LicenseConfigVO licenseConfig) {
        this.licenseConfig = licenseConfig;
    }
}
