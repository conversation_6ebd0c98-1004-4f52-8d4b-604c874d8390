package com.cyberscraft.uep.uam.common.vo;

import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

public class DlpModuleVO extends NormalModuleVO implements Serializable {

    private static final long serialVersionUID = -3323583518508635166L;

    private final static Logger LOG = LoggerFactory.getLogger(DlpModuleVO.class);

    @ApiModelProperty(value = "SDk授权个数")
    private Integer sdkNum;

    public Integer getSdkNum() {
        return sdkNum;
    }

    public void setSdkNum(Integer sdkNum) {
        this.sdkNum = sdkNum;
    }
}
