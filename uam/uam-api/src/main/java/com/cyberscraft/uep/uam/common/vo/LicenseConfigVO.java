package com.cyberscraft.uep.uam.common.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@ApiModel(value = "LicenseConfigVO", description = "用户授权模块信息")
public class LicenseConfigVO implements Serializable {

    private static final long serialVersionUID = -3323583518508635166L;

    @ApiModelProperty(value = "应用SSO")
    private NormalModuleVO appSso;

    @ApiModelProperty(value = "用户链接")
    private NormalModuleVO userJoin;

    @ApiModelProperty(value = "安全基线监测/准入")
    private NormalModuleVO riskMonitor;

    @ApiModelProperty(value = "文件审计")
    private NormalModuleVO fileAudit;

    @ApiModelProperty(value = "应用分发")
    private AdmModuleVO adm;

    @ApiModelProperty(value = "安全接入网关")
    private NormalModuleVO gateway;

    @ApiModelProperty(value = "数据防泄漏")
    private DlpModuleVO dlp;

    @ApiModelProperty(value = "数据连接器模块")
    private DataConnectModuleVO dataConnect;

    public NormalModuleVO getAppSso() {
        return appSso;
    }

    public void setAppSso(NormalModuleVO appSso) {
        this.appSso = appSso;
    }

    public NormalModuleVO getUserJoin() {
        return userJoin;
    }

    public void setUserJoin(NormalModuleVO userJoin) {
        this.userJoin = userJoin;
    }

    public NormalModuleVO getRiskMonitor() {
        return riskMonitor;
    }

    public void setRiskMonitor(NormalModuleVO riskMonitor) {
        this.riskMonitor = riskMonitor;
    }

    public NormalModuleVO getFileAudit() {
        return fileAudit;
    }

    public void setFileAudit(NormalModuleVO fileAudit) {
        this.fileAudit = fileAudit;
    }

    public AdmModuleVO getAdm() {
        return adm;
    }

    public void setAdm(AdmModuleVO adm) {
        this.adm = adm;
    }

    public NormalModuleVO getGateway() {
        return gateway;
    }

    public void setGateway(NormalModuleVO gateway) {
        this.gateway = gateway;
    }

    public DlpModuleVO getDlp() {
        return dlp;
    }

    public void setDlp(DlpModuleVO dlp) {
        this.dlp = dlp;
    }

    public DataConnectModuleVO getDataConnect() {
        return dataConnect;
    }

    public void setDataConnect(DataConnectModuleVO dataConnect) {
        this.dataConnect = dataConnect;
    }
}
