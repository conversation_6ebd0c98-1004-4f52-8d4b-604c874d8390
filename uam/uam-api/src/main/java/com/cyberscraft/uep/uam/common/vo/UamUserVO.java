package com.cyberscraft.uep.uam.common.vo;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2020-09-17 14:48
 */
public class UamUserVO implements Serializable {
    private static final long serialVersionUID=1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 用户的电子邮件
     */
    private String email;

    /**
     * 用户的全名，即包括姓和名
     */
    private String name;

    /**
     * 用户的移动电话号码
     */
    private String phoneNumber;

    /**
     * 用户登录名
     */
    private String username;


    /**
     * 数据来源ID，同步或导入时使用
     */
    private Long connectorId;

    /**
     * 同步源的用户登录名
     */
    private String connectorUid;

    /**
     * 同步源的用户实际sub id，通常唯一，且不可变，而connnectorUid是可变的
     */
    private String connectorSubId;

    /**
     * 同步源的连接器类型
     */
    private Integer connectorType;

    /***
     * 当前用户对应的租户Id
     */
    private String tenantId;
    /**
     * 用户的状态， ['ACTIVE', 'INACTIVE']stringEnum:"ACTIVE", "INACTIVE"
     */
    private Integer status;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 用户所在的组织
     */
    private List<UamOrgVO> orgList;

    /**
     * 用户所在组织名称，多个以逗号分隔
     */
    private String orgNames;

    public String getOrgNames() {
        return orgNames;
    }

    public void setOrgNames(String orgNames) {
        this.orgNames = orgNames;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public Long getConnectorId() {
        return connectorId;
    }

    public void setConnectorId(Long connectorId) {
        this.connectorId = connectorId;
    }

    public String getConnectorUid() {
        return connectorUid;
    }

    public void setConnectorUid(String connectorUid) {
        this.connectorUid = connectorUid;
    }

    public String getConnectorSubId() {
        return connectorSubId;
    }

    public void setConnectorSubId(String connectorSubId) {
        this.connectorSubId = connectorSubId;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public Integer getConnectorType() {
        return connectorType;
    }

    public void setConnectorType(Integer connectorType) {
        this.connectorType = connectorType;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public List<UamOrgVO> getOrgList() {
        return orgList;
    }

    public void setOrgList(List<UamOrgVO> orgList) {
        this.orgList = orgList;
    }
}
