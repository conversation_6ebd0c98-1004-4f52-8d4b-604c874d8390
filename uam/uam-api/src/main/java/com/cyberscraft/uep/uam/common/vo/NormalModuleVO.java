package com.cyberscraft.uep.uam.common.vo;

import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

public class NormalModuleVO implements Serializable {

    private static final long serialVersionUID = -3323583518508635166L;

    private final static Logger LOG = LoggerFactory.getLogger(NormalModuleVO.class);

    @ApiModelProperty(value = "是否可用")
    private Boolean canUse;

    @ApiModelProperty(value = "选中状态")
    private Boolean checkedValue;

    // 开始-结束时间
    @ApiModelProperty(value = "开始-结束时间")
    private String[] dates;

    public Boolean getCanUse() {
        if (dates == null || StringUtils.isBlank(dates[0])) {
            canUse = false;
        } else {
            // 设置是否过期
            LocalDate startDate = LocalDate.parse(dates[0], DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            LocalDate stopDate = LocalDate.parse(dates[1], DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            if (stopDate != null && startDate != null) {
                canUse = LocalDate.now().compareTo(stopDate) <= 0 && LocalDate.now().compareTo(startDate) >= 0;
            } else {
                canUse = false;
            }
        }
        return canUse;
    }

    public String[] getDates() {
        return dates;
    }

    public void setDates(String[] dates) {
        this.dates = dates;
    }

    public Boolean getCheckedValue() {
        return checkedValue;
    }

    public void setCheckedValue(Boolean checkedValue) {
        this.checkedValue = checkedValue;
    }
}
