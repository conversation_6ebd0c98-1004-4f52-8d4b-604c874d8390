package com.cyberscraft.uep.uam.api;

import com.cyberscraft.uep.common.bean.Result;
import com.cyberscraft.uep.common.dto.PageView;
import com.cyberscraft.uep.common.dto.Pagination;
import com.cyberscraft.uep.uam.common.constants.UrlConstants;
import com.cyberscraft.uep.uam.common.dto.UamUserQueryDTO;
import com.cyberscraft.uep.uam.common.vo.UamOrgVO;
import com.cyberscraft.uep.uam.common.vo.UamUserVO;
import io.swagger.annotations.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

import static com.cyberscraft.uep.common.constant.UEPSwaggerConstants.HEADER_BEARER_XXX;
import static com.cyberscraft.uep.common.constant.UEPSwaggerConstants.HEADER_UC_ACCESS_TOKEN;
import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;

/**
 * <p>
 * 用户接口
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2020-09-17 14:31
 */
@RestController
@Api(description = "用户管理", tags = "User")
@FeignClient(value = "uam-service", contextId = "uam-serviceUserApi")
public interface UserApi {

    @ApiOperation(response = UamUserVO.class, value = "根据用户名查询用户信息", notes = "根据用户名查询用户信息", produces = "application/json")
    @RequestMapping(value = UrlConstants.URL_PREFIX_API_VERSION + "/users/getByUsername", produces = MediaType.APPLICATION_JSON_VALUE, method = GET)
    @ResponseBody
    Result<UamUserVO> getUserByUsername(
            @NotEmpty @RequestParam(value = "username")
            @ApiParam(value = "用户名", example = "John") String username);


    @ApiOperation(response = UamUserVO.class, value = "根据用户名和密码查询用户信息", notes = "根据用户名和密码查询用户信息", produces = "application/json")
    @RequestMapping(value = UrlConstants.URL_PREFIX_API_VERSION + "/users/getByUsernameAndPwd", produces = MediaType.APPLICATION_JSON_VALUE, method = GET)
    @ResponseBody
    Result<UamUserVO> getUserByUsername(
            @NotEmpty @RequestParam(value = "username")
            @ApiParam(value = "用户名", example = "John") String username,
            @NotEmpty @RequestParam(value = "password")
            @ApiParam(value = "用户登录密码", example = "password") String password);

    @ApiOperation(response = UamUserVO.class, value = "根据第三方平台账户类型，第三方平台用户ID获取IAM用户对像", notes = "根据第三方平台账户类型，第三方平台用户ID获取IAM用户对像", produces = "application/json")
    @RequestMapping(value = UrlConstants.URL_PREFIX_API_VERSION + "/users/getUserByThirdPartyUserId", produces = MediaType.APPLICATION_JSON_VALUE, method = GET)
    @ResponseBody
    Result<UamUserVO> getUserByThirdPartyUserId(
            @NotEmpty @RequestParam(value = "uid")
            @ApiParam(value = "用户名", example = "1") String uid,
            @NotEmpty @RequestParam(value = "type")
            @ApiParam(value = "第三方平台账户类型,参考账户类型定义3代表DD,51代表政务钉钉", example = "3") Integer accountType);

    @ApiOperation(response = UamUserVO.class, value = "根据sns平台账户类型，sns平台授权码获取IAM用户对象", notes = "根据sns平台账户类型，sns平台授权码获取IAM用户对象", produces = "application/json")
    @RequestMapping(value = UrlConstants.URL_PREFIX_API_VERSION + "/users/getUserBySnsAuthCode", produces = MediaType.APPLICATION_JSON_VALUE, method = GET)
    @ResponseBody
    Result<UamUserVO> getUserBySnsAuthCode(
            @NotEmpty @RequestParam(value = "code")
            @ApiParam(value = "授权码", example = "********") String code,
            @NotEmpty @RequestParam(value = "type")
            @ApiParam(value = "第三方平台账户类型,参考账户类型定义3代表DD,51代表政务钉钉", example = "3") Integer accountType);

    @ApiOperation(response = UamUserVO.class, value = "根据用户ID查询用户信息", notes = "根据用户ID查询用户信息", produces = "application/json")
    @RequestMapping(value = UrlConstants.URL_PREFIX_API_VERSION + "/users/{id}", produces = MediaType.APPLICATION_JSON_VALUE, method = GET)
    @ResponseBody
    Result<UamUserVO> getUser(
            @NotEmpty @PathVariable(value = "id")
            @ApiParam(value = "用户ID", example = "1") Long id);


    @ApiOperation(response = UamUserVO.class, value = "根据多个用户名，查询多个用户信息", notes = "根据多个用户名，查询多个用户信息", produces = "application/json")
    @RequestMapping(value = UrlConstants.URL_PREFIX_API_VERSION + "/users/getListByUsernames", produces = MediaType.APPLICATION_JSON_VALUE, method = GET)
    @ResponseBody
    Result<List<UamUserVO>> getUsersByUsername(
            @NotEmpty @RequestParam(value = "username")
            @ApiParam(value = "用户名", example = "John") List<String> usernames);


    @ApiOperation(response = UamUserVO.class, value = "根据多个用户ID，查询多个用户信息", notes = "根据多个用户ID，查询多个用户信息", produces = "application/json")
    @RequestMapping(value = UrlConstants.URL_PREFIX_API_VERSION + "/users/getListByIds", produces = MediaType.APPLICATION_JSON_VALUE, method = GET)
    @ResponseBody
    Result<List<UamUserVO>> getUsersByIds(
            @NotEmpty @RequestParam(value = "id")
            @ApiParam(value = "用户ID", example = "1") List<Long> ids,
            @NotEmpty @RequestParam(value = "isIncludeOrg")
            @ApiParam(value = "是否查询组织", example = "true") boolean isIncludeOrg);


    @ApiOperation(response = UamUserVO.class, value = "根据组织ID，查询组下的用户列表", notes = "根据组织ID，查询组下的用户列表", produces = "application/json")
    @RequestMapping(value = UrlConstants.URL_PREFIX_API_VERSION + "/users/getListByOrgId", produces = MediaType.APPLICATION_JSON_VALUE, method = GET)
    @ResponseBody
    Result<List<UamUserVO>> getUsersByOrgId(
            @NotEmpty @RequestParam(value = "orgId")
            @ApiParam(value = "组ID", example = "1") List<Long> orgId,
            @NotEmpty @RequestParam(value = "isIncludeSubOrg")
            @ApiParam(value = "是否包含对应的子组", example = "false") Boolean isIncludeSubOrg);


    @ApiOperation(response = UamOrgVO.class, value = "根据组织ID，查询组织信息", notes = "根据组织ID，查询组织信息", produces = "application/json")
    @RequestMapping(value = UrlConstants.URL_PREFIX_API_VERSION + "/orgs/{id}", produces = MediaType.APPLICATION_JSON_VALUE, method = GET)
    @ResponseBody
    Result<UamOrgVO> getOrgById(
            @NotEmpty @PathVariable(value = "id")
            @ApiParam(value = "组ID", example = "1") Long id);


    @ApiOperation(response = UamOrgVO.class, value = "根据第三方平台组代码查询组信息", notes = "根据第三方平台组代码查询组信息", produces = "application/json")
    @RequestMapping(value = UrlConstants.URL_PREFIX_API_VERSION + "/orgs/getByThirdOrgId", produces = MediaType.APPLICATION_JSON_VALUE, method = GET)
    @ResponseBody
    Result<UamOrgVO> getOrgByThirdOrgId(
            @NotEmpty @RequestParam(value = "thridOrgId")
            @ApiParam(value = "第三方平台组代码", example = "1") String thridOrgId,
            @NotEmpty @RequestParam(value = "accountType")
            @ApiParam(value = "第三方平台账户类型", example = "1") String accountType,
            @NotEmpty @RequestParam(value = "isCreateOnNotExist")
            @ApiParam(value = "如果不存在，则尝试进行用户组的创建", example = "false") Boolean isCreateOnNotExist);


    @ApiOperation(response = UamOrgVO.class, value = "根据多个第三方平台组代码和账户类型，查询组信息", notes = "根据多个第三方平台组代码和账户类型，查询组信息", produces = "application/json")
    @RequestMapping(value = UrlConstants.URL_PREFIX_API_VERSION + "/orgs/getListByThirdOrgIdsAndAccountType", produces = MediaType.APPLICATION_JSON_VALUE, method = GET)
    @ResponseBody
    Result<List<UamOrgVO>> getOrgsByThirdOrgId(
            @NotEmpty @RequestParam(value = "thridOrgIds")
            @ApiParam(value = "第三方平台组代码", example = "1") List<String> thridOrgIds,
            @NotEmpty @RequestParam(value = "accountType")
            @ApiParam(value = "第三方平台账户类型", example = "1") String accountType);


    @ApiOperation(response = UamOrgVO.class, value = "根据多个组织ID，查询多个组织信息", notes = "根据多个组织ID，查询多个组织信息", produces = "application/json")
    @RequestMapping(value = UrlConstants.URL_PREFIX_API_VERSION + "/orgs/getListByIds", produces = MediaType.APPLICATION_JSON_VALUE, method = GET)
    @ResponseBody
    Result<List<UamOrgVO>> getOrgsById(
            @NotEmpty @RequestParam(value = "id")
            @ApiParam(value = "组ID", example = "1") List<Long> ids,
            @NotEmpty @RequestParam(value = "isIncludeSubOrg")
            @ApiParam(value = "是否包含对应的子组", example = "false") Boolean isIncludeSubOrg);

    @ApiOperation(response = UamOrgVO.class, value = "根据用户ID查询用户所在的组织信息", notes = "根据用户ID查询用户所在的组织信息", produces = "application/json")
    @RequestMapping(value = UrlConstants.URL_PREFIX_API_VERSION + "/orgs/getListByUserId", produces = MediaType.APPLICATION_JSON_VALUE, method = GET)
    @ResponseBody
    Result<List<UamOrgVO>> getOrgsByUser(
            @NotEmpty @RequestParam(value = "userId")
            @ApiParam(value = "用户ID", example = "1") Long userId,
            @NotEmpty @RequestParam(value = "isIncludeSubOrg")
            @ApiParam(value = "是否包含对应的子组", example = "false") Boolean isIncludeSubOrg);

    @ApiOperation(response = UamOrgVO.class, value = "根据用户ID查询用户所在的组织及上层组织信息", notes = "根据用户ID查询用户所在的组织及上层组织信息", produces = "application/json")
    @RequestMapping(value = UrlConstants.URL_PREFIX_API_VERSION + "/orgs/getParentListByUserId", produces = MediaType.APPLICATION_JSON_VALUE, method = GET)
    @ResponseBody
    Result<List<UamOrgVO>> getParentOrgsByUser(
            @NotEmpty @RequestParam(value = "userId")
            @ApiParam(value = "用户ID", example = "1") Long userId);

    @ApiOperation(response = UamOrgVO.class, value = "分页查询用户信息", notes = "分页查询用户信息", produces = "application/json")
    @RequestMapping(value = UrlConstants.URL_PREFIX_API_VERSION + "/users/search", produces = MediaType.APPLICATION_JSON_VALUE, method = POST)
    @ResponseBody
    Result<PageView<UamUserVO>> searchUsers(

            @NotEmpty @RequestParam(value = "isLoadGroup")
            @ApiParam(value = "是否加载用户组信息", example = "false") Boolean isLoadGroup,

            @NotNull @RequestBody Pagination<UamUserQueryDTO> page);


    @RequestMapping(value = UrlConstants.URL_PREFIX_API_VERSION, produces = MediaType.APPLICATION_JSON_VALUE, method = RequestMethod.GET)
    @ResponseBody
    @ApiOperation(response = UamOrgVO.class, nickname = "searchUserWithFuzzyWord",
            value = "模糊查询用户",
            notes = "针对用户登录名，全名，电子邮件，移动电话号码和状态属性，模糊查询用户。"
                    + "\n\r查询匹配的标准是字符串的子串匹配方式。"
                    + "\n\r**如果查询参数'q'没有指定，即使指定了返回用户列表的排序方式，API也会使用缺省的'username asc, name asc, email asc'来排序。** "
                    + "\n\r**注意: 即使在查询参数'attrs'中指定了敏感信息（如密码），API也不会返回。**",
            authorizations = {@Authorization(value = "uc_auth", scopes = {
                    @AuthorizationScope(scope = "search:users", description = "")})})
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String",
                    value = HEADER_UC_ACCESS_TOKEN, defaultValue = HEADER_BEARER_XXX,
                    required = true)})
    Result<List<UamUserVO>> searchUserWithInfo(
            @RequestParam(value = "q", required = false)
            @ApiParam(value = "作为过滤条件，在子串匹配时过滤用户登录名，全名，电子邮件和移动电话的属性的字符串", example = "name1") String q,
            @RequestParam(value = "deptIds", required = false)
            @ApiParam(value = "部门ID作为查询条件", example = "deptIds") List<String> deptIds);
}
