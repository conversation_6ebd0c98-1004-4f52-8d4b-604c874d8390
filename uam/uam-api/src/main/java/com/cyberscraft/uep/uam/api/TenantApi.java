package com.cyberscraft.uep.uam.api;

import com.cyberscraft.uep.common.bean.Result;
import com.cyberscraft.uep.uam.common.constants.UrlConstants;
import com.cyberscraft.uep.uam.common.vo.UamTenantLicenseVO;
import com.cyberscraft.uep.uam.common.vo.UamTenantVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static com.cyberscraft.uep.common.constant.UEPSwaggerConstants.*;
import static org.springframework.web.bind.annotation.RequestMethod.GET;
@RestController
@Api(description = "租户管理", tags = "Tenant")
@FeignClient(value = "uam-service", contextId = "uam-serviceTenantApi")
public interface TenantApi {

    @ApiOperation(response = UamTenantVO.class, value = "获取所有租户信息列表", notes = "获取所有租户信息列表", produces = "application/json")
    @RequestMapping(value = UrlConstants.URL_PREFIX_API_VERSION + "/tenants", produces = MediaType.APPLICATION_JSON_VALUE, method = GET)
    @ResponseBody
    Result<List<UamTenantVO>> getAllTenants();

    @RequestMapping(value = UrlConstants.URL_PREFIX_API_VERSION + "/tenantLicense", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    @ApiOperation(response = String.class, nickname = "获取租户的授权信息列表",
            value = "获取租户的授权信息列表", notes = "获取租户的授权信息列表")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HttpHeaders.AUTHORIZATION, defaultValue = HEADER_BEARER_XXX, required = true),
    })
    Result<UamTenantLicenseVO> getTenantLicense();

    @RequestMapping(value = UrlConstants.URL_PREFIX_API_VERSION + "/tenantsLicense", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    @ApiOperation(response = String.class, nickname = "获取所有租户的具体信息",
            value = "获取所有租户的具体信息", notes = "获取所有租户的具体信息")
    @ApiImplicitParams({
            @ApiImplicitParam(paramType = "header", name = "Content-Type", dataType = "String", value = HEADER_REQUEST_CONTENT_TYPE, defaultValue = HEADER_APPLICATION_JSON_CHARSET_UTF_8, required = true),
            @ApiImplicitParam(paramType = "header", name = "Authorization", dataType = "String", value = HttpHeaders.AUTHORIZATION, defaultValue = HEADER_BEARER_XXX, required = true),
    })
    Result<List<UamTenantLicenseVO>> getAllTenantLicense();
}
