package com.cyberscraft.uep.uam.common.dto;

import com.cyberscraft.uep.common.dto.IQueryDto;

import java.util.List;

/***
 *
 * @date 2021/9/27
 * <AUTHOR>
 ***/
public class UamUserQueryDTO implements IQueryDto {

    /***
     * 支持的用户名，邮箱，手机号等搜索，模糊搜索处理
     */
    private String q;

    /***
     * 用户所在的组列表，不需要再次处理子组，如果要查询某一组及子组的用户列表，会在查询条件中，自动指定对应的组及子组列表
     */
    private List<Long> orgIds;

    /***
     * 指定的用户ID列表
     */
    private List<Long> ids;

    /***
     * 用户状态
     */
    private Integer status;

    public String getQ() {
        return q;
    }

    public void setQ(String q) {
        this.q = q;
    }

    public List<Long> getOrgIds() {
        return orgIds;
    }

    public void setOrgIds(List<Long> orgIds) {
        this.orgIds = orgIds;
    }

    public List<Long> getIds() {
        return ids;
    }

    public void setIds(List<Long> ids) {
        this.ids = ids;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
