package com.cyberscraft.uep.uam.controller;

import com.cyberscraft.uep.common.util.ServerContext;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/10/14 3:16 下午
 */
@RestController
public class SysController {

    /**
     * 获取server端build版本
     *
     * @return
     */
    @RequestMapping(method = RequestMethod.GET, value = "/uam/version", produces = MediaType.APPLICATION_JSON_VALUE)
    @ResponseBody
    public Map version() {
        return ServerContext.getVersionInfo();
    }
}
