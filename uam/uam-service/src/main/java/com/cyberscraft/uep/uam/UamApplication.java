package com.cyberscraft.uep.uam;

import com.cyberscraft.uep.mq.EnableMQ;
import com.cyberscraft.uep.ncm.client.EnableNcmClient;
import com.cyberscraft.uep.starter.dds.EnableDDS;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.security.servlet.ManagementWebSecurityAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.ldap.LdapAutoConfiguration;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @version uep
 * @since 2020-09-16 14:00
 */
@EnableAsync
@SpringBootApplication(scanBasePackages = {
        "com.cyberscraft.uep.iam",
        "com.cyberscraft.uep.common.config",
        "com.cyberscraft.uep.account.client",
        "com.cyberscraft.uep.uam",
        "com.cyberscraft.uep.dds.common",
        "com.cyberscraft.uep.proxy.meta",
        "com.cyberscraft.uep.common.exception"
},exclude = {SecurityAutoConfiguration.class, ManagementWebSecurityAutoConfiguration.class, LdapAutoConfiguration.class,
})
@EnableTransactionManagement
@EnableDiscoveryClient
@EnableDDS
@EnableMQ
@EnableNcmClient
public class UamApplication {
    private static final Logger LOG = LoggerFactory.getLogger(UamApplication.class);

    public static void main(String[] args) {

        SpringApplication.run(UamApplication.class, args);

        LOG.info("============================================================");
        LOG.info("===========uam service is success started===============");
        LOG.info("============================================================");

    }
}
